<template>
	<div class="PaymentCategory content_body" v-loading="loading">
		<!-- 搜索 -->
		<div class="nav_header">
			<el-row>
				<el-col :span="20">
					<el-form :inline="true" size="small" @keyup.enter.native="handleSearchEvents">
						<el-form-item label="类别名称">
							<el-input v-model="searchName" size="small" @clear="handleSearchEvents" placeholder="输入类别名称" clearable></el-input>
						</el-form-item>
						<el-form-item label="有效性">
							<el-select size="small" @change="handleSearchEvents" @clear="handleSearchEvents" v-model="searchActive" placeholder="请选择" clearable>
								<el-option label="有效" :value="true"></el-option>
								<el-option label="无效" :value="false"></el-option>
							</el-select>
						</el-form-item>
						<el-form-item>
							<el-button size="small" type="primary" @click="handleSearchEvents" v-prevent-click>搜索</el-button>
						</el-form-item>
					</el-form>
				</el-col>
				<el-col :span="4" class="text_right">
					<el-button type="primary" @click="addPrepayCategoryClick" size="small" v-prevent-click>新增</el-button>
				</el-col>
			</el-row>
		</div>

		<!-- 表格 -->
		<div>
			<el-table size="small" :data="tableData" tooltip-effect="light">
				<el-table-column prop="Name" label="类别名称"></el-table-column>
				<el-table-column prop="Active" label="有效性">
					<template slot-scope="scope">
						{{ activeFormat(scope.row) }}
					</template>
				</el-table-column>
				<el-table-column label="操作" width="90px">
					<template slot-scope="scope">
						<el-button type="primary" size="small" @click="editPrepayCategory(scope.row)">编辑</el-button>
					</template>
				</el-table-column>
			</el-table>
		</div>
		<!-- 分页 -->
		<div class="pad_10 dis_flex flex_x_end" v-if="paginations.total > 0">
			<el-pagination background :current-page.sync="paginations.page" :layout="paginations.layout" :total="paginations.total" @current-change="currentChange"></el-pagination>
		</div>

		<!--分类弹窗  新增一级分类 -->
		<el-dialog title="收付款类别" :visible.sync="dialogVisible" width="550px">
			<el-form ref="ruleForm" :model="ruleForm" :rules="rules" label-width="auto" size="small" @submit.native.prevent>
				<el-form-item label="类别名称" prop="Name">
					<el-input v-model="ruleForm.Name" autocomplete="off" placeholder="请输入类别名称"></el-input>
				</el-form-item>
				<el-form-item label="是否有效" v-if="!isAdd" prop="Active">
					<el-radio-group v-model="ruleForm.Active">
						<el-radio :label="true">是</el-radio>
						<el-radio :label="false">否</el-radio>
					</el-radio-group>
				</el-form-item>
			</el-form>
			<div slot="footer" class="dialog-footer">
				<el-button @click="dialogVisible = false" size="small" v-prevent-click>取 消</el-button>
				<el-button type="primary" @click="addSubmitPrepayCategory" :loading="modalLoading" size="small" v-prevent-click>保 存</el-button>
			</div>
		</el-dialog>
	</div>
</template>

<script>
	import API from "@/api/PSI/Payment/paymentCategory.js";
	export default {
		name: "PaymentCategory",
		props: {},
		/**  引入的组件  */
		components: {},
		/**  Vue 实例的数据对象**/
		data() {
			return {
				loading: false,
				dialogVisible: false,
				modalLoading: false,
				isAdd: true,
				searchName: "",
				searchActive: true,
				tableData: [],
				paginations: {
					page: 1, // 当前位于哪页
					total: 0, // 总数
					page_size: 10, // 1页显示多少条
					layout: "total, prev, pager, next, jumper", // 翻页属性
				}, //需要给分页组件传的信息
				ruleForm: {
					ID: "",
					Name: "",
					Active: "",
				},
				//新增
				rules: {
					Name: [
						{
							required: true,
							message: "请输入类别名称",
							trigger: ["blur", "change"],
						},
					],
					Active: [{ required: true, message: "请选择是否有效", trigger: "change" }],
				},
			};
		},
		/**计算属性  */
		computed: {},
		/**  方法集合  */
		methods: {
			/**    */
			activeFormat(row) {
				switch (row.Active) {
					case true:
						return "有效";
					case false:
						return "无效";
					default:
						return "";
				}
			},
			/**  搜索  */
			handleSearchEvents() {
				let that = this;
				that.paginations.page = 1;
				that.prepayCategory_list();
			},
			/**    */
			currentChange(page) {
				let that = this;
				that.paginations.page = page;
				that.prepayCategory_list();
			},
			/**   添加 */
			addPrepayCategoryClick() {
				let that = this;
				that.ruleForm = {
					ID: "",
					Name: "",
					Active: true,
				};
				that.isAdd = true;
				that.dialogVisible = true;
			},
			/**   编辑 */
			editPrepayCategory(row) {
				let that = this;
				that.ruleForm = row;
				that.isAdd = false;
				that.dialogVisible = true;
			},
			/**   保存 */
			addSubmitPrepayCategory() {
				let that = this;
				that.$refs.ruleForm.validate((valid) => {
					if (valid) {
						that.modalLoading = true;
						if (that.isAdd) {
							that.prepayCategory_create();
						} else {
							that.prepayCategory_update();
						}
					}
				});
			},
			/************** *************************/
			/**  列表请求  */
			async prepayCategory_list() {
				let that = this;
				let params = {
					Name: that.searchName, //类别名称
					Active: that.searchActive, //有效性
					PageNum: that.paginations.page,
				};
				that.loading = true;
				let res = await API.prepayCategory_list(params);
				if (res.StateCode == 200) {
					that.tableData = res.List;
					that.paginations.total = res.Total;
				} else {
					that.$message.error(res.Message);
				}
				that.loading = false;
			},

			/** 创建  */
			async prepayCategory_create() {
				let that = this;
				let params = { Name: that.ruleForm.Name };
				let res = await API.prepayCategory_create(params);
				if (res.StateCode == 200) {
					that.$message.success("添加成功");
					that.handleSearchEvents();
					that.dialogVisible = false;
				} else {
					that.$message.error(res.Message);
				}
				that.modalLoading = false;
			},

			/**   更新 */
			async prepayCategory_update() {
				let that = this;
				let params = Object.assign({}, that.ruleForm);
				let res = await API.prepayCategory_update(params);
				if (res.StateCode == 200) {
					that.$message.success("更新成功");
					that.handleSearchEvents();
					that.dialogVisible = false;
				} else {
					that.$message.error(res.Message);
				}
				that.modalLoading = false;
			},
		},
		/** 监听数据变化   */
		watch: {},
		/** 创建实例之前执行函数   */
		beforeCreate() {},
		/**  实例创建完成之后  */
		created() {
			let that = this;
			that.prepayCategory_list();
		},
		/**  在挂载开始之前被调用  */
		beforeMount() {},
		/**  实例被挂载后调用  */
		mounted() {},
		/**  数据更新 之前 调用  */
		beforeUpdate() {},
		/** 数据更新 完成 调用   */
		updated() {},
		/**  实例销毁之前调用  */
		beforeDestroy() {},
		/**  实例销毁后调用  */
		destroyed() {},
	};
</script>

<style lang="scss">
	.PaymentCategory {
	}
</style>
