/**
 * Created by preference on 2021/02/01
 *  zmx 
 */

import * as API from '@/api/index'
export default {
  /**  小程序立即授权 */
  miniprogramComponent:params =>{
    return API.POST('api/micromall/miniprogram/componentloginpage', params)
  },
  /**  公众号立即授权 */
  offiaccountComponent:params=>{
    return API.POST('api/open/offiaccount/componentloginpage', params)
  },
  /** 公众号绑定成功查询信息  */
  getOffiaccountAuthorizerInfo: params => {
    return API.POST('api/open/offiaccount/authorizerInfo', params)
  },
  /** 公众号绑定成功查询信息  */
  getMiniprogramAuthorizerInfo: params => {
    return API.POST('api/micromall/miniprogram/authinfo', params)
  },
  /** 公众号绑定成功查询信息  */
  setOffiaccountRemoveBind: params => {
    return API.POST('api/open/offiaccount/removeBind', params)
  },

  /** 提交审核的接口 */
  miniprogram_submitAudit: params => {
    return API.POST('api/micromall/miniprogram/submitAudit', params)
  },

  /** 取消授权接口 */
  miniprogram_cancelauth: params => {
    return API.POST('api/micromall/miniprogram/cancelauth', params)
  },

  /** 获取最后一次审核 状态 */
  miniprogram_getLatestAuditStatus: params => {
    return API.POST('api/micromall/miniprogram/getLatestAuditStatus', params)
  },

  /** 发布的接口 */
  miniprogram_release: params => {
    return API.POST('api/micromall/miniprogram/release', params)
  },
  
  /** 添加微信支付方式 */
  miniprogram_PayMethod: params => {
    return API.importFile('api/open/miniprogramPayMethod', params)
  },

  /** 添加微信支付方式 */
  getMiniprogramPayMethod: params => {
    return API.POST('api/open/getMiniprogramPayMethod', params)
  },

  


  // 获取支付方式
  getSalePayMethod: params => {
    return API.POST('api/paymethod/all', params)
  },

}