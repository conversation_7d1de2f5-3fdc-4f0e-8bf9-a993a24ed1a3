<template>
  <div class="content_body" v-loading="loading">

    <el-tabs value="first" @tab-click="handleClick">
      <el-tab-pane label="DataBase-Schema-All" name="first">
        <el-form label-width="140px" size="small">
          <el-form-item label="Schema-All">
            <el-input v-model="statement" type="textarea" :rows="16" placeholder="请输入执行的SQL语句，更新全部商户"></el-input>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="executeSchema">提交</el-button>
          </el-form-item>
        </el-form>
      </el-tab-pane>
      <el-tab-pane label="DataBase-Schema-Medicine" name="first-Medicine">
        <el-form label-width="140px" size="small">
          <el-form-item label="Schema-Medicine">
            <el-input v-model="statementMedicine" type="textarea" :rows="16" placeholder="请输入执行的SQL语句，更新医美商户">
            </el-input>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="executeMedicineSchema">提交</el-button>
          </el-form-item>
        </el-form>
      </el-tab-pane>
      <el-tab-pane label="Redis-Enterprise-Delete" name="second">
        <el-form label-width="120px" size="small">
          <el-form-item label="EnterpriseCode">
            <el-input v-model="enterpriseCode" type="input" placeholder="请输入商户号"></el-input>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="executeRedisEnterpriseDelete">提交</el-button>
          </el-form-item>
        </el-form>
      </el-tab-pane>
      <el-tab-pane label="Customer-MiniProgram" name="third">
        <el-form label-width="0px" size="small" inline="true">
          <el-form-item>
            <el-button type="primary" @click="executeMiniProgramDomain">修改小程序域名</el-button>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="executeMiniProgramSubmitAuditAll">小程序新版本提交审核</el-button>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="executeMiniProgramSubmitAuditWithoutPublish">提交未审核通过小程序</el-button>
          </el-form-item>
        </el-form>
      </el-tab-pane>
      <el-tab-pane label="Customer-OfficialAccount" name="fourth">
        <el-form label-width="0px" size="small" inline="true">
          <el-form-item>
            <el-button type="primary" @click="executeOfficialAccountRemoveMessageTemplate">删除公众号消息模板</el-button>
          </el-form-item>
        </el-form>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<script>
import Schema from "@/api/System/Schema/schema";
import Redis from "@/api/System/Redis/redis";
import MiniProgram from "@/api/System/MiniProgram/miniprogram";
import OfficialAccount from "@/api/System/OfficialAccount/officialaccount";
export default {
  data() {
    return {
      loading: false,
      statement: "",
      statementMedicine: "",
      enterpriseCode: "",
    };
  },
  methods: {
    executeSchema() {
      var that = this;
      that.loading = true;
      Schema.executeSchema({ Statement: that.statement })
        .then((res) => {
          if (res.StateCode == 200) {
            that.$message.success({
              message: "执行成功！！！",
              duration: 5000,
            });
          } else {
            that.$message.error({
              message: res.Message,
              duration: 5000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },
    executeMedicineSchema() {
      var that = this;
      that.loading = true;
      Schema.executeMedicineSchema({ Statement: that.statementMedicine })
        .then((res) => {
          if (res.StateCode == 200) {
            that.$message.success({
              message: "执行成功！！！",
              duration: 5000,
            });
          } else {
            that.$message.error({
              message: res.Message,
              duration: 5000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },
    executeRedisEnterpriseDelete() {
      var that = this;
      that.loading = true;
      Redis.executeRedisEnterpriseDelete({ EnterpriseCode: that.enterpriseCode })
        .then((res) => {
          if (res.StateCode == 200) {
            that.$message.success({
              message: "执行成功！！！",
              duration: 5000,
            });
          } else {
            that.$message.error({
              message: res.Message,
              duration: 5000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },
    executeMiniProgramDomain() {
      var that = this;
      that.loading = true;
      MiniProgram.executeMiniProgramDomin()
        .then((res) => {
          if (res.StateCode == 200) {
            that.$message.success({
              message: "执行成功！！！",
              duration: 5000,
            });
          } else {
            that.$message.error({
              message: res.Message,
              duration: 5000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },
    executeMiniProgramSubmitAuditAll() {
      var that = this;
      that.loading = true;
      MiniProgram.executeMiniProgramSubmitAuditAll()
        .then((res) => {
          if (res.StateCode == 200) {
            that.$message.success({
              message: "执行成功！！！",
              duration: 5000,
            });
          } else {
            that.$message.error({
              message: res.Message,
              duration: 5000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },
    executeMiniProgramSubmitAuditWithoutPublish() {
      var that = this;
      that.loading = true;
      MiniProgram.executeMiniProgramSubmitAuditWithoutPublish()
        .then((res) => {
          if (res.StateCode == 200) {
            that.$message.success({
              message: "执行成功！！！",
              duration: 5000,
            });
          } else {
            that.$message.error({
              message: res.Message,
              duration: 5000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },

    executeOfficialAccountRemoveMessageTemplate() {
      var that = this;
      that.loading = true;
      OfficialAccount.executeOfficialAccountRemoveMessageTemplate()
        .then((res) => {
          if (res.StateCode == 200) {
            that.$message.success({
              message: "执行成功！！！",
              duration: 5000,
            });
          } else {
            that.$message.error({
              message: res.Message,
              duration: 5000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },
  },
};
</script>