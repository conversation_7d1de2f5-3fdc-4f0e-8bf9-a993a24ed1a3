import * as API from '@/api/index'
export default {
  /* 价格设置列表 */
  getDispatchPriceGoodst_list: params => {
    return API.POST('api/dispatchPriceGoods/list', params)
  },
  /** 9.1.产品品牌列表（不分页）  */
  getProductBrandList: params => {
    return API.POST('api/productBrand/all', params)
  },

  // 获取产品分类列表(可以，去除没有二级的类别)
  getValidProductCategory() {
    return API.POST('api/productCategory/valid')
  },

}