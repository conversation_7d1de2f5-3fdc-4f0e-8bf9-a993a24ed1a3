<template>
  <div class="arrearModifyEntityPerformance">
    <el-dialog :visible.sync="visible_" title="补欠款订单修改门店业绩" width="1380px" @close="closeModifyEntityPerformance">
      <div v-if="orderDetail" class="font_13" style="height: 60vh">
        <div class="pad_10 border">
          <el-button type="text" @click="batchSettingSaleEntityPerformanceClick">批量设置门店业绩</el-button>
        </div>
        <el-scrollbar class="el-scrollbar_height">
          <!-- 项目 -->
          <div v-if="orderDetail.Project.length > 0">
            <div v-for="(item, index) in orderDetail.Project" :key="index">
              <el-row class="row_header border_right border_left">
                <el-col :span="8">项目</el-col>
                <el-col :span="4">数量</el-col>
                <el-col :span="6">购买金额</el-col>
                <el-col :span="6">补欠款金额</el-col>
              </el-row>
              <el-row>
                <el-col :span="24" class="pad_10 border_right border_left border_bottom">
                  <el-col :span="24">
                    <el-col :span="8">
                      <div>
                        {{ item.ProjectName }}
                        <span v-if="item.Alias">({{ item.Alias }})</span>
                        <el-tag v-if="item.IsLargess" size="mini" type="danger" class="marlt_10">赠</el-tag>
                      </div>
                    </el-col>
                    <el-col :span="4">x {{ item.Quantity }}</el-col>
                    <el-col :span="6">¥ {{ item.IsLargess ? 0 : item.AccountTotalAmount | toFixed | NumFormat }}</el-col>
                    <el-col :span="6">¥ {{ (parseFloat(item.PayAmount) + parseFloat(item.CardDeductionTotalAmount)) | toFixed | NumFormat }}</el-col>
                  </el-col>
                  <el-col :span="24" class="martp_5">
                    <el-col :span="6" :offset="18">
                      <span class="color_gray font_12" v-if="item.PayAmount > 0">现金金额：¥ {{ item.PayAmount | toFixed | NumFormat }}</span>
                      <span class="color_gray font_12" :class="item.PayAmount > 0 ? 'marlt_15' : ''" v-if="item.CardDeductionAmount > 0"
                        >卡抵扣：¥ {{ item.CardDeductionAmount | toFixed | NumFormat }}</span
                      >
                      <span
                        class="color_gray font_12"
                        :class="item.PricePreferentialAmount > 0 || item.PayAmount > 0 ? 'marlt_15' : ''"
                        v-if="item.CardDeductionLargessAmount > 0"
                        >赠送卡抵扣：¥ {{ item.CardDeductionLargessAmount | toFixed | NumFormat }}</span
                      >
                    </el-col>
                  </el-col>
                </el-col>
              </el-row>
              <el-row  v-if="!item.IsLargess" class="padlt_10 border_left border_bottom font_12">
                <el-col :span="2" class="padtp_10 padbm_10">
                  <el-link class="font_12 line_26" type="primary" :underline="false" @click="entityHandleClick(item, 2)">门店</el-link>
                </el-col>
                <el-col :span="22" class="border_left">
                  <el-row v-for="(entity, entityIndex) in item.Performance" :key="entity.EntityID + 'EntityID'">
                    <el-col :span="6" class="padtp_10 padrt_10" :class="entityIndex != 0 ? 'border_top' : ''">
                      <el-form class="sale-ModifyPerformanceCommission-Handler" label-position="right" size="mini" label-width="150px">
                        <el-form-item :label="`${entity.EntityName}`">
                          <el-input
                            type="number"
                            v-input-fixed
                            class="input_type"
                            style="width: 100px"
                            v-model="entity.Scale"
                            v-enter-number2
                            @input="changeSaleEntityRate(item, entity, 2)"
                          >
                            <template slot="append">%</template>
                          </el-input>
                          <i
                            v-if="billEntityID && billEntityID != entity.EntityID"
                            class="el-icon-error marlt_10"
                            style="font-size: 18px"
                            @click="removeEntityClick(item, entity, entityIndex)"
                          ></i>
                        </el-form-item>
                      </el-form>
                    </el-col>
                    <el-col :span="18" class="border_left border_right">
                      <el-row class="padtp_10 padrt_10" :class="index != 0 ? 'border_top' : ''">
                        <el-col v-if="item.PayAmount && item.PayAmount > 0" :span="8">
                          <el-form class="sale-ModifyPerformanceCommission-Handler" label-position="right" size="mini" label-width="120px">
                            <el-form-item label="现金业绩">
                              <span slot="label">
                                现金业绩
                                <el-popover placement="top-start" width="280" trigger="hover">
                                  <p>现金业绩 = 现金付款金额 x 门店现金业绩占比 x 业绩占比</p>
                                  <p>
                                    门店现金业绩占比参考值：
                                    <span v-if="item.PerformancePayRate != null">{{ (item.PerformancePayRate * 100) | toFixed | NumFormat }}%</span>
                                    <span v-else>无</span>
                                  </p>

                                  <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                </el-popover>
                              </span>
                              <el-input type="number" v-input-fixed class="input_type" style="width: 120px" v-model="entity.PayPerformance" v-enter-number2>
                                <template slot="append">元</template>
                              </el-input>
                            </el-form-item>
                          </el-form>
                        </el-col>
                        <el-col v-if="item.CardDeductionAmount && item.CardDeductionAmount > 0" :span="8">
                          <el-form class="sale-ModifyPerformanceCommission-Handler" label-position="right" size="mini" label-width="150px">
                            <el-form-item label="卡抵扣业绩">
                              <span slot="label">
                                卡抵扣业绩
                                <el-popover placement="top-start" width="280" trigger="hover">
                                  <p>卡抵扣业绩 = 卡抵扣金额 x 门店卡抵扣业绩占比 x 业绩占比</p>
                                  <p v-if="item.PerformanceSavingCardRate != null">
                                    门店卡抵扣业绩占比参考值：{{ (item.PerformanceSavingCardRate * 100) | toFixed | NumFormat }}%
                                  </p>
                                  <p v-else>门店卡抵扣业绩占比参考值：无</p>
                                  <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                </el-popover>
                              </span>
                              <el-input
                                type="number"
                                v-input-fixed
                                class="input_type"
                                style="width: 100px"
                                v-model="entity.SavingCardPerformance"
                                v-enter-number2
                              >
                                <template slot="append">元</template>
                              </el-input>
                            </el-form-item>
                          </el-form>
                        </el-col>
                        <el-col v-if="item.CardDeductionLargessAmount && item.CardDeductionLargessAmount > 0" :span="8">
                          <el-form class="sale-ModifyPerformanceCommission-Handler" label-position="right" size="mini" label-width="150px">
                            <el-form-item label="赠送卡抵扣业绩">
                              <span slot="label">
                                赠送卡抵扣业绩
                                <el-popover placement="top-start" width="280" trigger="hover">
                                  <p>赠送卡抵扣业绩 = 赠送卡抵扣金额 x 门店赠送卡抵扣业绩占比 x 业绩占比</p>
                                  <p v-if="entity.PerformanceSavingCardLargessRate != null">
                                    门店赠送卡抵扣业绩占比参考值：¥ {{ entity.PerformanceSavingCardLargessRate | toFixed | NumFormat }}
                                  </p>
                                  <p v-else>门店赠送卡抵扣业绩占比参考值：无</p>
                                  <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                </el-popover>
                              </span>
                              <el-input
                                type="number"
                                v-input-fixed
                                class="input_type"
                                style="width: 100px"
                                v-model="entity.SavingCardLargessPerformance"
                                v-enter-number2
                              >
                                <template slot="append">元</template>
                              </el-input>
                            </el-form-item>
                          </el-form>
                        </el-col>
                      </el-row>
                    </el-col>
                  </el-row>
                </el-col>
              </el-row>
            </div>
          </div>
          <!-- 储值卡 -->
          <div v-if="orderDetail.SavingCard.length > 0">
            <div v-for="(item, index) in orderDetail.SavingCard" :key="index">
              <el-row class="row_header border_right border_left">
                <el-col :span="8">储值卡</el-col>
                <!-- <el-col :span="2">购买数量</el-col> -->
                <el-col :span="8">充值金额</el-col>
                <!-- <el-col :span="4">赠送金额</el-col> -->
                <!-- <el-col :span="5">支付金额</el-col> -->
                <el-col :span="8">补欠款金额</el-col>
              </el-row>
              <el-row>
                <el-col :span="24" class="pad_10 border_left border_bottom">
                  <el-col :span="24">
                    <el-col :span="8">
                      {{ item.SavingCardName }}
                      <span v-if="item.Alias">({{ item.Alias }})</span>
                    </el-col>
                    <el-col :span="8">¥ {{ item.AccountTotalAmount | toFixed | NumFormat }}</el-col>
                    <el-col :span="8">¥ {{ item.TotalAmount | toFixed | NumFormat }}</el-col>
                  </el-col>
                  <el-col :span="24" class="martp_5">
                    <el-col :span="9" :offset="16">
                      <span class="color_gray font_12" v-if="item.PayAmount > 0">现金金额：¥ {{ item.PayAmount | toFixed | NumFormat }}</span>
                    </el-col>
                  </el-col>
                </el-col>
              </el-row>
              <el-row  v-if="!item.IsLargess" class="padlt_10 border_left border_bottom font_12">
                <el-col :span="2" class="padtp_10 padbm_10">
                  <el-link class="font_12 line_26" type="primary" :underline="false" @click="entityHandleClick(item, 5)">门店</el-link>
                </el-col>
                <el-col :span="22" class="border_left">
                  <el-row v-for="(entity, entityIndex) in item.Performance" :key="entity.EntityID + 'EntityID'">
                    <el-col :span="6" class="padtp_10 padrt_10" :class="entityIndex != 0 ? 'border_top' : ''">
                      <el-form class="sale-ModifyPerformanceCommission-Handler" label-position="right" size="mini" label-width="150px">
                        <el-form-item :label="`${entity.EntityName}`">
                          <el-input
                            type="number"
                            v-input-fixed
                            class="input_type"
                            style="width: 100px"
                            v-model="entity.Scale"
                            v-enter-number2
                            @input="changeSaleEntityRate(item, entity, 5)"
                          >
                            <template slot="append">%</template>
                          </el-input>
                          <i
                            v-if="billEntityID && billEntityID != entity.EntityID"
                            class="el-icon-error marlt_10"
                            style="font-size: 18px"
                            @click="removeEntityClick(item, entity, entityIndex)"
                          ></i>
                        </el-form-item>
                      </el-form>
                    </el-col>
                    <el-col v-if="item.PayAmount && item.PayAmount > 0" :span="18" class="border_left border_right">
                      <el-row class="padtp_10 padrt_10" :class="index != 0 ? 'border_top' : ''">
                        <el-col :span="8">
                          <el-form class="sale-ModifyPerformanceCommission-Handler" label-position="right" size="mini" label-width="120px">
                            <el-form-item label="现金业绩">
                              <span slot="label">
                                现金业绩
                                <el-popover placement="top-start" width="280" trigger="hover">
                                  <p>现金业绩 = 现金付款金额 x 门店现金业绩占比 x 业绩占比</p>
                                  <p>
                                    门店现金业绩占比参考值：
                                    <span v-if="item.PerformancePayRate != null">{{ (item.PerformancePayRate * 100) | toFixed | NumFormat }}%</span>
                                    <span v-else>无</span>
                                  </p>

                                  <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                </el-popover>
                              </span>
                              <el-input type="number" v-input-fixed class="input_type" style="width: 120px" v-model="entity.PayPerformance" v-enter-number2>
                                <template slot="append">元</template>
                              </el-input>
                            </el-form-item>
                          </el-form>
                        </el-col>
                      </el-row>
                    </el-col>
                  </el-row>
                </el-col>
              </el-row>
            </div>
          </div>
          <!-- 时效卡 -->
          <div v-if="orderDetail.TimeCard.length > 0">
            <div v-for="(item, index) in orderDetail.TimeCard" :key="index">
              <el-row class="row_header border_right border_left">
                <el-col :span="8">时效卡</el-col>
                <!-- <el-col :span="2">数量</el-col> -->
                <!-- <el-col :span="4">优惠金额</el-col> -->
                <el-col :span="8">购买金额</el-col>
                <!-- <el-col :span="5">支付金额</el-col> -->
                <el-col :span="8">补欠款金额</el-col>
              </el-row>
              <el-row>
                <el-col :span="24" class="pad_10 border_right border_left border_bottom">
                  <el-col :span="24">
                    <el-col :span="8">
                      <div>
                        {{ item.TimeCardName }}
                        <span v-if="item.Alias">({{ item.Alias }})</span>
                        <el-tag v-if="item.IsLargess" size="mini" type="danger" class="marlt_10">赠</el-tag>
                      </div>
                    </el-col>
                    <el-col :span="8">¥ {{ item.AccountTotalAmount | toFixed | NumFormat }}</el-col>
                    <el-col :span="8">¥ {{ item.TotalAmount | toFixed | NumFormat }}</el-col>
                  </el-col>
                  <el-col :span="24" class="martp_5">
                    <el-col :span="8" :offset="16">
                      <span class="color_gray font_12" v-if="item.PayAmount > 0">现金金额：¥ {{ item.PayAmount | toFixed | NumFormat }}</span>
                      <span class="color_gray font_12" :class="item.PayAmount > 0 ? 'marlt_15' : ''" v-if="item.CardDeductionAmount > 0"
                        >卡抵扣：¥ {{ item.CardDeductionAmount | toFixed | NumFormat }}</span
                      >
                      <span
                        class="color_gray font_12"
                        :class="item.PricePreferentialAmount > 0 || item.PayAmount > 0 ? 'marlt_15' : ''"
                        v-if="item.CardDeductionLargessAmount > 0"
                        >赠送卡抵扣：¥ {{ item.CardDeductionLargessAmount | toFixed | NumFormat }}</span
                      >
                    </el-col>
                  </el-col>
                </el-col>
              </el-row>

              <el-row  v-if="!item.IsLargess" class="padlt_10 border_left border_bottom font_12">
                <el-col :span="2" class="padtp_10 padbm_10">
                  <el-link class="font_12 line_26" type="primary" :underline="false" @click="entityHandleClick(item, 4)">门店</el-link>
                </el-col>
                <el-col :span="22" class="border_left">
                  <el-row v-for="(entity, entityIndex) in item.Performance" :key="entity.EntityID + 'EntityID'">
                    <el-col :span="6" class="padtp_10 padrt_10" :class="entityIndex != 0 ? 'border_top' : ''">
                      <el-form class="sale-ModifyPerformanceCommission-Handler" label-position="right" size="mini" label-width="150px">
                        <el-form-item :label="`${entity.EntityName}`">
                          <el-input
                            type="number"
                            v-input-fixed
                            class="input_type"
                            style="width: 100px"
                            v-model="entity.Scale"
                            v-enter-number2
                            @input="changeSaleEntityRate(item, entity, 4)"
                          >
                            <template slot="append">%</template>
                          </el-input>
                          <i
                            v-if="billEntityID && billEntityID != entity.EntityID"
                            class="el-icon-error marlt_10"
                            style="font-size: 18px"
                            @click="removeEntityClick(item, entity, entityIndex)"
                          ></i>
                        </el-form-item>
                      </el-form>
                    </el-col>
                    <el-col :span="18" class="border_left border_right">
                      <el-row class="padtp_10 padrt_10" :class="index != 0 ? 'border_top' : ''">
                        <el-col v-if="item.PayAmount && item.PayAmount > 0" :span="8">
                          <el-form class="sale-ModifyPerformanceCommission-Handler" label-position="right" size="mini" label-width="120px">
                            <el-form-item label="现金业绩">
                              <span slot="label">
                                现金业绩
                                <el-popover placement="top-start" width="280" trigger="hover">
                                  <p>现金业绩 = 现金付款金额 x 门店现金业绩占比 x 业绩占比</p>
                                  <p>
                                    门店现金业绩占比参考值：
                                    <span v-if="item.PerformancePayRate != null">{{ (item.PerformancePayRate * 100) | toFixed | NumFormat }}%</span>
                                    <span v-else>无</span>
                                  </p>

                                  <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                </el-popover>
                              </span>
                              <el-input type="number" v-input-fixed class="input_type" style="width: 120px" v-model="entity.PayPerformance" v-enter-number2>
                                <template slot="append">元</template>
                              </el-input>
                            </el-form-item>
                          </el-form>
                        </el-col>
                        <el-col v-if="item.CardDeductionAmount && item.CardDeductionAmount > 0" :span="8">
                          <el-form class="sale-ModifyPerformanceCommission-Handler" label-position="right" size="mini" label-width="150px">
                            <el-form-item label="卡抵扣业绩">
                              <span slot="label">
                                卡抵扣业绩
                                <el-popover placement="top-start" width="280" trigger="hover">
                                  <p>卡抵扣业绩 = 卡抵扣金额 x 门店卡抵扣业绩占比 x 业绩占比</p>
                                  <p v-if="item.PerformanceSavingCardRate != null">
                                    门店卡抵扣业绩占比参考值：{{ (item.PerformanceSavingCardRate * 100) | toFixed | NumFormat }}%
                                  </p>
                                  <p v-else>门店卡抵扣业绩占比参考值：无</p>
                                  <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                </el-popover>
                              </span>
                              <el-input
                                type="number"
                                v-input-fixed
                                class="input_type"
                                style="width: 100px"
                                v-model="entity.SavingCardPerformance"
                                v-enter-number2
                              >
                                <template slot="append">元</template>
                              </el-input>
                            </el-form-item>
                          </el-form>
                        </el-col>
                        <el-col v-if="item.CardDeductionLargessAmount && item.CardDeductionLargessAmount > 0" :span="8">
                          <el-form class="sale-ModifyPerformanceCommission-Handler" label-position="right" size="mini" label-width="150px">
                            <el-form-item label="赠送卡抵扣业绩">
                              <span slot="label">
                                赠送卡抵扣业绩
                                <el-popover placement="top-start" width="280" trigger="hover">
                                  <p>赠送卡抵扣业绩 = 赠送卡抵扣金额 x 门店赠送卡抵扣业绩占比 x 业绩占比</p>
                                  <p v-if="entity.PerformanceSavingCardLargessRate != null">
                                    门店赠送卡抵扣业绩占比参考值：¥ {{ entity.PerformanceSavingCardLargessRate | toFixed | NumFormat }}
                                  </p>
                                  <p v-else>门店赠送卡抵扣业绩占比参考值：无</p>
                                  <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                </el-popover>
                              </span>
                              <el-input
                                type="number"
                                v-input-fixed
                                class="input_type"
                                style="width: 100px"
                                v-model="entity.SavingCardLargessPerformance"
                                v-enter-number2
                              >
                                <template slot="append">元</template>
                              </el-input>
                            </el-form-item>
                          </el-form>
                        </el-col>
                      </el-row>
                    </el-col>
                  </el-row>
                </el-col>
              </el-row>
            </div>
          </div>
          <!-- 通用次卡 -->
          <div v-if="orderDetail.GeneralCard.length > 0">
            <div v-for="(item, index) in orderDetail.GeneralCard" :key="index">
              <el-row class="row_header border_right border_left">
                <el-col :span="8">通用次卡</el-col>
                <!-- <el-col :span="2">数量</el-col> -->
                <!-- <el-col :span="4">优惠金额</el-col> -->
                <el-col :span="8">购买金额</el-col>
                <!-- <el-col :span="5">支付金额</el-col> -->
                <el-col :span="8">补欠款金额</el-col>
              </el-row>
              <el-row>
                <el-col :span="24" class="pad_10 border_right border_left border_bottom">
                  <el-col :span="24">
                    <el-col :span="8">
                      <div>
                        {{ item.GeneralCardName }}
                        <span v-if="item.Alias">({{ item.Alias }})</span>
                        <el-tag v-if="item.IsLargess" size="mini" type="danger" class="marlt_10">赠</el-tag>
                      </div>
                    </el-col>
                    <el-col :span="8">¥ {{ item.AccountTotalAmount | toFixed | NumFormat }}</el-col>

                    <el-col :span="8">¥ {{ item.TotalAmount | toFixed | NumFormat }}</el-col>
                  </el-col>
                  <el-col :span="24" class="martp_5">
                    <el-col :span="8" :offset="16">
                      <span class="color_gray font_12" v-if="item.PayAmount > 0">现金金额：¥ {{ item.PayAmount | toFixed | NumFormat }}</span>
                      <span class="color_gray font_12" :class="item.PayAmount > 0 ? 'marlt_15' : ''" v-if="item.CardDeductionAmount > 0"
                        >卡抵扣：¥ {{ item.CardDeductionAmount | toFixed | NumFormat }}</span
                      >
                      <span
                        class="color_gray font_12"
                        :class="item.PricePreferentialAmount > 0 || item.PayAmount > 0 ? 'marlt_15' : ''"
                        v-if="item.CardDeductionLargessAmount > 0"
                        >赠送卡抵扣：¥ {{ item.CardDeductionLargessAmount | toFixed | NumFormat }}</span
                      >
                    </el-col>
                  </el-col>
                </el-col>
              </el-row>
              <el-row  v-if="!item.IsLargess" class="padlt_10 border_left border_bottom font_12">
                <el-col :span="2" class="padtp_10 padbm_10">
                  <el-link class="font_12 line_26" type="primary" :underline="false" @click="entityHandleClick(item, 3)">门店</el-link>
                </el-col>
                <el-col :span="22" class="border_left">
                  <el-row v-for="(entity, entityIndex) in item.Performance" :key="entity.EntityID + 'EntityID'">
                    <el-col :span="6" class="padtp_10 padrt_10" :class="entityIndex != 0 ? 'border_top' : ''">
                      <el-form class="sale-ModifyPerformanceCommission-Handler" label-position="right" size="mini" label-width="150px">
                        <el-form-item :label="`${entity.EntityName}`">
                          <el-input
                            type="number"
                            v-input-fixed
                            class="input_type"
                            style="width: 100px"
                            v-model="entity.Scale"
                            v-enter-number2
                            @input="changeSaleEntityRate(item, entity, 3)"
                          >
                            <template slot="append">%</template>
                          </el-input>
                          <i
                            v-if="billEntityID && billEntityID != entity.EntityID"
                            class="el-icon-error marlt_10"
                            style="font-size: 18px"
                            @click="removeEntityClick(item, entity, entityIndex)"
                          ></i>
                        </el-form-item>
                      </el-form>
                    </el-col>
                    <el-col :span="18" class="border_left border_right">
                      <el-row class="padtp_10 padrt_10" :class="index != 0 ? 'border_top' : ''">
                        <el-col v-if="item.PayAmount && item.PayAmount > 0" :span="8">
                          <el-form class="sale-ModifyPerformanceCommission-Handler" label-position="right" size="mini" label-width="120px">
                            <el-form-item label="现金业绩">
                              <span slot="label">
                                现金业绩
                                <el-popover placement="top-start" width="280" trigger="hover">
                                  <p>现金业绩 = 现金付款金额 x 门店现金业绩占比 x 业绩占比</p>
                                  <p>
                                    门店现金业绩占比参考值：
                                    <span v-if="item.PerformancePayRate != null">{{ (item.PerformancePayRate * 100) | toFixed | NumFormat }}%</span>
                                    <span v-else>无</span>
                                  </p>

                                  <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                </el-popover>
                              </span>
                              <el-input type="number" v-input-fixed class="input_type" style="width: 120px" v-model="entity.PayPerformance" v-enter-number2>
                                <template slot="append">元</template>
                              </el-input>
                            </el-form-item>
                          </el-form>
                        </el-col>
                        <el-col v-if="item.CardDeductionAmount && item.CardDeductionAmount > 0" :span="8">
                          <el-form class="sale-ModifyPerformanceCommission-Handler" label-position="right" size="mini" label-width="150px">
                            <el-form-item label="卡抵扣业绩">
                              <span slot="label">
                                卡抵扣业绩
                                <el-popover placement="top-start" width="280" trigger="hover">
                                  <p>卡抵扣业绩 = 卡抵扣金额 x 门店卡抵扣业绩占比 x 业绩占比</p>
                                  <p v-if="item.PerformanceSavingCardRate != null">
                                    门店卡抵扣业绩占比参考值：{{ (item.PerformanceSavingCardRate * 100) | toFixed | NumFormat }}%
                                  </p>
                                  <p v-else>门店卡抵扣业绩占比参考值：无</p>
                                  <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                </el-popover>
                              </span>
                              <el-input
                                type="number"
                                v-input-fixed
                                class="input_type"
                                style="width: 100px"
                                v-model="entity.SavingCardPerformance"
                                v-enter-number2
                              >
                                <template slot="append">元</template>
                              </el-input>
                            </el-form-item>
                          </el-form>
                        </el-col>
                        <el-col v-if="item.CardDeductionLargessAmount && item.CardDeductionLargessAmount > 0" :span="8">
                          <el-form class="sale-ModifyPerformanceCommission-Handler" label-position="right" size="mini" label-width="150px">
                            <el-form-item label="赠送卡抵扣业绩">
                              <span slot="label">
                                赠送卡抵扣业绩
                                <el-popover placement="top-start" width="280" trigger="hover">
                                  <p>赠送卡抵扣业绩 = 赠送卡抵扣金额 x 门店赠送卡抵扣业绩占比 x 业绩占比</p>
                                  <p v-if="entity.PerformanceSavingCardLargessRate != null">
                                    门店赠送卡抵扣业绩占比参考值：¥ {{ entity.PerformanceSavingCardLargessRate | toFixed | NumFormat }}
                                  </p>
                                  <p v-else>门店赠送卡抵扣业绩占比参考值：无</p>
                                  <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                </el-popover>
                              </span>
                              <el-input
                                type="number"
                                v-input-fixed
                                class="input_type"
                                style="width: 100px"
                                v-model="entity.SavingCardLargessPerformance"
                                v-enter-number2
                              >
                                <template slot="append">元</template>
                              </el-input>
                            </el-form-item>
                          </el-form>
                        </el-col>
                      </el-row>
                    </el-col>
                  </el-row>
                </el-col>
              </el-row>
            </div>
          </div>
          <!-- 产品 -->
          <div v-if="orderDetail.Product.length > 0">
            <div v-for="(item, index) in orderDetail.Product" :key="index">
              <el-row class="row_header border_right border_left">
                <el-col :span="8">产品</el-col>
                <el-col :span="4">数量</el-col>
                <el-col :span="6">购买金额</el-col>
                <el-col :span="6">补欠款金额</el-col>
              </el-row>
              <el-row>
                <el-col :span="24" class="pad_10 border_right border_left border_bottom">
                  <el-col :span="24">
                    <el-col :span="8">
                      <div>
                        {{ item.ProductName }}
                        <span v-if="item.Alias">({{ item.Alias }})</span>
                        <el-tag v-if="item.IsLargess" size="mini" type="danger" class="marlt_10">赠</el-tag>
                      </div>
                    </el-col>
                    <el-col :span="4">x {{ item.Quantity }}</el-col>
                    <el-col :span="6">¥ {{ item.IsLargess ? 0 : item.AccountTotalAmount | toFixed | NumFormat }}</el-col>
                    <el-col :span="6">¥ {{ (parseFloat(item.PayAmount) + parseFloat(item.CardDeductionTotalAmount)) | toFixed | NumFormat }}</el-col>
                  </el-col>
                  <el-col :span="24" class="martp_5">
                    <el-col :span="6" :offset="18">
                      <span class="color_gray font_12" v-if="item.PayAmount > 0">现金金额：¥ {{ item.PayAmount | toFixed | NumFormat }}</span>
                      <span class="color_gray font_12" :class="item.PayAmount > 0 ? 'marlt_15' : ''" v-if="item.CardDeductionAmount > 0"
                        >卡抵扣：¥ {{ item.CardDeductionAmount | toFixed | NumFormat }}</span
                      >
                      <span
                        class="color_gray font_12"
                        :class="item.PricePreferentialAmount > 0 || item.PayAmount > 0 ? 'marlt_15' : ''"
                        v-if="item.CardDeductionLargessAmount > 0"
                        >赠送卡抵扣：¥ {{ item.CardDeductionLargessAmount | toFixed | NumFormat }}</span
                      >
                    </el-col>
                  </el-col>
                </el-col>
              </el-row>
              <el-row  v-if="!item.IsLargess" class="padlt_10 border_left border_bottom font_12">
                <el-col :span="2" class="padtp_10 padbm_10">
                  <el-link class="font_12 line_26" type="primary" :underline="false" @click="entityHandleClick(item, 1)">门店</el-link>
                </el-col>
                <el-col :span="22" class="border_left">
                  <el-row v-for="(entity, entityIndex) in item.Performance" :key="entity.EntityID + 'EntityID'">
                    <el-col :span="6" class="padtp_10 padrt_10" :class="entityIndex != 0 ? 'border_top' : ''">
                      <el-form class="sale-ModifyPerformanceCommission-Handler" label-position="right" size="mini" label-width="150px">
                        <el-form-item :label="`${entity.EntityName}`">
                          <el-input
                            type="number"
                            v-input-fixed
                            class="input_type"
                            style="width: 100px"
                            v-model="entity.Scale"
                            v-enter-number2
                            @input="changeSaleEntityRate(item, entity, 1)"
                          >
                            <template slot="append">%</template>
                          </el-input>
                          <i
                            v-if="billEntityID && billEntityID != entity.EntityID"
                            class="el-icon-error marlt_10"
                            style="font-size: 18px"
                            @click="removeEntityClick(item, entity, entityIndex)"
                          ></i>
                        </el-form-item>
                      </el-form>
                    </el-col>
                    <el-col :span="18" class="border_left border_right">
                      <el-row class="padtp_10 padrt_10" :class="index != 0 ? 'border_top' : ''">
                        <el-col v-if="item.PayAmount && item.PayAmount > 0" :span="8">
                          <el-form class="sale-ModifyPerformanceCommission-Handler" label-position="right" size="mini" label-width="120px">
                            <el-form-item label="现金业绩">
                              <span slot="label">
                                现金业绩
                                <el-popover placement="top-start" width="280" trigger="hover">
                                  <p>现金业绩 = 现金付款金额 x 门店现金业绩占比 x 业绩占比</p>
                                  <p>
                                    门店现金业绩占比参考值：
                                    <span v-if="item.PerformancePayRate != null">{{ (item.PerformancePayRate * 100) | toFixed | NumFormat }}%</span>
                                    <span v-else>无</span>
                                  </p>

                                  <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                </el-popover>
                              </span>
                              <el-input type="number" v-input-fixed class="input_type" style="width: 120px" v-model="entity.PayPerformance" v-enter-number2>
                                <template slot="append">元</template>
                              </el-input>
                            </el-form-item>
                          </el-form>
                        </el-col>
                        <el-col v-if="item.CardDeductionAmount && item.CardDeductionAmount > 0" :span="8">
                          <el-form class="sale-ModifyPerformanceCommission-Handler" label-position="right" size="mini" label-width="150px">
                            <el-form-item label="卡抵扣业绩">
                              <span slot="label">
                                卡抵扣业绩
                                <el-popover placement="top-start" width="280" trigger="hover">
                                  <p>卡抵扣业绩 = 卡抵扣金额 x 门店卡抵扣业绩占比 x 业绩占比</p>
                                  <p v-if="item.PerformanceSavingCardRate != null">
                                    门店卡抵扣业绩占比参考值：{{ (item.PerformanceSavingCardRate * 100) | toFixed | NumFormat }}%
                                  </p>
                                  <p v-else>门店卡抵扣业绩占比参考值：无</p>
                                  <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                </el-popover>
                              </span>
                              <el-input
                                type="number"
                                v-input-fixed
                                class="input_type"
                                style="width: 100px"
                                v-model="entity.SavingCardPerformance"
                                v-enter-number2
                              >
                                <template slot="append">元</template>
                              </el-input>
                            </el-form-item>
                          </el-form>
                        </el-col>
                        <el-col v-if="item.CardDeductionLargessAmount && item.CardDeductionLargessAmount > 0" :span="8">
                          <el-form class="sale-ModifyPerformanceCommission-Handler" label-position="right" size="mini" label-width="150px">
                            <el-form-item label="赠送卡抵扣业绩">
                              <span slot="label">
                                赠送卡抵扣业绩
                                <el-popover placement="top-start" width="280" trigger="hover">
                                  <p>赠送卡抵扣业绩 = 赠送卡抵扣金额 x 门店赠送卡抵扣业绩占比 x 业绩占比</p>
                                  <p v-if="entity.PerformanceSavingCardLargessRate != null">
                                    门店赠送卡抵扣业绩占比参考值：¥ {{ entity.PerformanceSavingCardLargessRate | toFixed | NumFormat }}
                                  </p>
                                  <p v-else>门店赠送卡抵扣业绩占比参考值：无</p>
                                  <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                </el-popover>
                              </span>
                              <el-input
                                type="number"
                                v-input-fixed
                                class="input_type"
                                style="width: 100px"
                                v-model="entity.SavingCardLargessPerformance"
                                v-enter-number2
                              >
                                <template slot="append">元</template>
                              </el-input>
                            </el-form-item>
                          </el-form>
                        </el-col>
                      </el-row>
                    </el-col>
                  </el-row>
                </el-col>
              </el-row>
            </div>
          </div>
          <!-- 套餐卡 -->
          <div v-if="orderDetail.PackageCard.length > 0">
            <div v-for="(packageCard, index) in orderDetail.PackageCard" :key="index">
              <el-row class="row_header border_right border_left">
                <el-col :span="9">套餐卡</el-col>
                <el-col :span="5">数量</el-col>
                <el-col :span="5">购买金额</el-col>
                <el-col :span="5">补欠款金额</el-col>
              </el-row>
              <el-row>
                <el-col :span="24" class="pad_10 border_right border_left border_bottom">
                  <el-col :span="24">
                    <el-col :span="9">
                      <div>
                        {{ packageCard.PackageCardName }}
                        <span v-if="packageCard.Alias">({{ packageCard.Alias }})</span>
                        <el-tag v-if="packageCard.IsLargess" size="mini" type="danger" class="marlt_10">赠</el-tag>
                      </div>
                    </el-col>
                    <el-col :span="5">x {{ packageCard.Quantity }}</el-col>
                    <el-col :span="5">¥ {{ packageCard.AccountTotalAmount | toFixed | NumFormat }}</el-col>
                    <el-col :span="5">¥ {{ packageCard.TotalAmount | toFixed | NumFormat }}</el-col>
                  </el-col>
                  <el-col :span="24" class="martp_5">
                    <el-col :span="5" :offset="19">
                      <span class="color_gray font_12" v-if="packageCard.PayAmount > 0">现金金额：¥ {{ packageCard.PayAmount | toFixed | NumFormat }}</span>
                      <span class="color_gray font_12" :class="packageCard.PayAmount > 0 ? 'marlt_15' : ''" v-if="packageCard.CardDeductionAmount > 0"
                        >卡抵扣：¥ {{ packageCard.CardDeductionAmount | toFixed | NumFormat }}</span
                      >
                      <span
                        class="color_gray font_12"
                        :class="packageCard.PricePreferentialAmount > 0 || packageCard.PayAmount > 0 ? 'marlt_15' : ''"
                        v-if="packageCard.CardDeductionLargessAmount > 0"
                        >赠送卡抵扣：¥ {{ packageCard.CardDeductionLargessAmount | toFixed | NumFormat }}</span
                      >
                    </el-col>
                  </el-col>
                </el-col>
              </el-row>
              <el-row class="padlt_10 border_left border_bottom font_12">
                <el-col :span="2" class="padtp_10 padbm_10">
                  <el-link class="font_12 line_26" type="primary" :underline="false" @click="entityHandleClick(packageCard, 6)">门店</el-link>
                </el-col>
                <el-col :span="22" class="border_left">
                  <el-row>
                    <el-col
                      v-for="(entity, entityIndex) in packageCard.Performance"
                      :key="entity.EntityID + 'EntityID'"
                      :span="6"
                      class="padtp_10 padrt_10"
                      :class="entityIndex != 0 ? 'border_top' : ''"
                    >
                      <el-form class="sale-ModifyPerformanceCommission-Handler" label-position="right" size="mini" label-width="150px">
                        <el-form-item :label="`${entity.EntityName}`">
                          <el-input
                            type="number"
                            v-input-fixed
                            class="input_type"
                            style="width: 100px"
                            v-model="entity.Scale"
                            v-enter-number2
                            @input="changeSaleEntityRate(packageCard, entity, 6)"
                          >
                            <template slot="append">%</template>
                          </el-input>
                          <i
                            v-if="billEntityID && billEntityID != entity.EntityID"
                            class="el-icon-error marlt_10"
                            style="font-size: 18px"
                            @click="removeEntityClick(item, entity, entityIndex)"
                          ></i>
                        </el-form-item>
                      </el-form>
                    </el-col>
                  </el-row>
                </el-col>
              </el-row>
              <!-- 套餐卡-产品 -->
              <div v-if="packageCard.Product.length > 0">
                <div v-for="(item, index) in packageCard.Product" :key="index">
                  <el-row class="row_header_package_detail border_right border_left">
                    <el-col :span="9">套餐卡产品</el-col>
                    <el-col :span="5">数量</el-col>
                    <el-col :span="5">购买金额</el-col>
                    <el-col :span="5">补欠款金额</el-col>
                  </el-row>
                  <el-row>
                    <el-col :span="24" class="pad_10 border_right border_left border_bottom">
                      <el-col :span="24">
                        <el-col :span="9">
                          <div>
                            {{ item.ProductName }}
                            <span v-if="item.Alias">({{ item.Alias }})</span>
                            <el-tag v-if="item.IsLargess" size="mini" type="danger" class="marlt_10">赠</el-tag>
                          </div>
                        </el-col>
                        <el-col :span="5">x {{ item.Quantity * packageCard.Quantity }}</el-col>
                        <el-col :span="5">¥ {{ item.AccountTotalAmount | toFixed | NumFormat }}</el-col>
                        <el-col :span="5">¥ {{ item.TotalAmount | toFixed | NumFormat }}</el-col>
                      </el-col>
                      <el-col :span="24" class="martp_5">
                        <el-col :span="5" :offset="19">
                          <span class="color_gray font_12" v-if="item.PayAmount > 0">现金金额：¥ {{ item.PayAmount | toFixed | NumFormat }}</span>
                          <span class="color_gray font_12" :class="item.PayAmount > 0 ? 'marlt_15' : ''" v-if="item.CardDeductionAmount > 0"
                            >卡抵扣：¥ {{ item.CardDeductionAmount | toFixed | NumFormat }}</span
                          >
                          <span
                            class="color_gray font_12"
                            :class="item.PricePreferentialAmount > 0 || item.PayAmount > 0 ? 'marlt_15' : ''"
                            v-if="item.CardDeductionLargessAmount > 0"
                            >赠送卡抵扣：¥ {{ item.CardDeductionLargessAmount | toFixed | NumFormat }}</span
                          >
                        </el-col>
                      </el-col>
                    </el-col>
                  </el-row>

                  <el-row  v-if="!item.IsLargess" class="padlt_10 border_left border_bottom font_12">
                    <el-col :span="2" class="padtp_10 padbm_10">门店</el-col>
                    <el-col :span="22" class="border_left">
                      <el-row v-for="(entity, index) in item.Performance" :key="entity.EntityID + 'EntityID'">
                        <el-col :span="6" class="padtp_10 padrt_10" :class="index != 0 ? 'border_top' : ''">
                          <el-form class="sale-ModifyPerformanceCommission-Handler" label-position="right" size="mini" label-width="150px">

                            <el-form-item :label="`${entity.EntityName}：`">{{ entity.Scale }}%</el-form-item>
                          </el-form>
                        </el-col>
                        <el-col :span="18" class="border_left border_right">
                          <el-row class="padtp_10 padrt_10" :class="index != 0 ? 'border_top' : ''">
                            <el-col v-if="item.PayAmount && item.PayAmount > 0" :span="8">
                              <el-form class="sale-ModifyPerformanceCommission-Handler" label-position="right" size="mini" label-width="120px">
                                <el-form-item label="现金业绩">
                                  <span slot="label">
                                    现金业绩
                                    <el-popover placement="top-start" width="280" trigger="hover">
                                      <p>现金业绩 = 现金付款金额 x 门店现金业绩占比 x 业绩占比</p>
                                      <p>
                                        门店现金业绩占比参考值：
                                        <span v-if="item.PerformancePayRate != null">{{ (item.PerformancePayRate * 100) | toFixed | NumFormat }}%</span>
                                        <span v-else>无</span>
                                      </p>

                                      <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                    </el-popover>
                                  </span>
                                  <el-input type="number" v-input-fixed class="input_type" style="width: 120px" v-model="entity.PayPerformance" v-enter-number2>
                                    <template slot="append">元</template>
                                  </el-input>
                                </el-form-item>
                              </el-form>
                            </el-col>
                            <el-col v-if="item.CardDeductionAmount && item.CardDeductionAmount > 0" :span="8">
                              <el-form class="sale-ModifyPerformanceCommission-Handler" label-position="right" size="mini" label-width="150px">
                                <el-form-item label="卡抵扣业绩">
                                  <span slot="label">
                                    卡抵扣业绩
                                    <el-popover placement="top-start" width="280" trigger="hover">
                                      <p>卡抵扣业绩 = 卡抵扣金额 x 门店卡抵扣业绩占比 x 业绩占比</p>
                                      <p v-if="item.PerformanceSavingCardRate != null">
                                        门店卡抵扣业绩占比参考值：{{ (item.PerformanceSavingCardRate * 100) | toFixed | NumFormat }}%
                                      </p>
                                      <p v-else>门店卡抵扣业绩占比参考值：无</p>
                                      <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                    </el-popover>
                                  </span>
                                  <el-input
                                    type="number"
                                    v-input-fixed
                                    class="input_type"
                                    style="width: 100px"
                                    v-model="entity.SavingCardPerformance"
                                    v-enter-number2
                                  >
                                    <template slot="append">元</template>
                                  </el-input>
                                </el-form-item>
                              </el-form>
                            </el-col>
                            <el-col v-if="item.CardDeductionLargessAmount && item.CardDeductionLargessAmount > 0" :span="8">
                              <el-form class="sale-ModifyPerformanceCommission-Handler" label-position="right" size="mini" label-width="150px">
                                <el-form-item label="赠送卡抵扣业绩">
                                  <span slot="label">
                                    赠送卡抵扣业绩
                                    <el-popover placement="top-start" width="280" trigger="hover">
                                      <p>赠送卡抵扣业绩 = 赠送卡抵扣金额 x 门店赠送卡抵扣业绩占比 x 业绩占比</p>
                                      <p v-if="entity.PerformanceSavingCardLargessRate != null">
                                        门店赠送卡抵扣业绩占比参考值：¥ {{ entity.PerformanceSavingCardLargessRate | toFixed | NumFormat }}
                                      </p>
                                      <p v-else>门店赠送卡抵扣业绩占比参考值：无</p>
                                      <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                    </el-popover>
                                  </span>
                                  <el-input
                                    type="number"
                                    v-input-fixed
                                    class="input_type"
                                    style="width: 100px"
                                    v-model="entity.SavingCardLargessPerformance"
                                    v-enter-number2
                                  >
                                    <template slot="append">元</template>
                                  </el-input>
                                </el-form-item>
                              </el-form>
                            </el-col>
                          </el-row>
                        </el-col>
                      </el-row>
                    </el-col>
                  </el-row>
                </div>
              </div>
              <!-- 套餐卡-项目 -->
              <div v-if="packageCard.Project.length > 0">
                <div v-for="(item, index) in packageCard.Project" :key="index">
                  <el-row class="row_header_package_detail border_right border_left">
                    <el-col :span="9">套餐卡项目</el-col>
                    <el-col :span="5">数量</el-col>
                    <el-col :span="5">购买金额</el-col>
                    <el-col :span="5">补欠款金额</el-col>
                  </el-row>
                  <el-row>
                    <el-col :span="24" class="pad_10 border_right border_left border_bottom">
                      <el-col :span="24">
                        <el-col :span="9">
                          <div>
                            {{ item.ProjectName }}
                            <span v-if="item.Alias">({{ item.Alias }})</span>
                            <el-tag v-if="item.IsLargess" size="mini" type="danger" class="marlt_10">赠</el-tag>
                          </div>
                        </el-col>
                        <el-col :span="5">x {{ item.Quantity * packageCard.Quantity }}</el-col>
                        <el-col :span="5">¥ {{ item.AccountTotalAmount | toFixed | NumFormat }}</el-col>

                        <el-col :span="5">¥ {{ item.TotalAmount | toFixed | NumFormat }}</el-col>
                      </el-col>
                      <el-col :span="24" class="martp_5">
                        <el-col :span="5" :offset="19">
                          <span class="color_gray font_12" v-if="item.PayAmount > 0">现金金额：¥ {{ item.PayAmount | toFixed | NumFormat }}</span>
                          <span class="color_gray font_12" :class="item.PayAmount > 0 ? 'marlt_15' : ''" v-if="item.CardDeductionAmount > 0"
                            >卡抵扣：¥ {{ item.CardDeductionAmount | toFixed | NumFormat }}</span
                          >
                          <span
                            class="color_gray font_12"
                            :class="item.PricePreferentialAmount > 0 || item.PayAmount > 0 ? 'marlt_15' : ''"
                            v-if="item.CardDeductionLargessAmount > 0"
                            >赠送卡抵扣：¥ {{ item.CardDeductionLargessAmount | toFixed | NumFormat }}</span
                          >
                        </el-col>
                      </el-col>
                    </el-col>
                  </el-row>
                  <el-row  v-if="!item.IsLargess" class="padlt_10 border_left border_bottom font_12">
                    <el-col :span="2" class="padtp_10 padbm_10">门店</el-col>
                    <el-col :span="22" class="border_left">
                      <el-row v-for="(entity, index) in item.Performance" :key="entity.EntityID + 'EntityID'">
                        <el-col :span="6" class="padtp_10 padrt_10" :class="index != 0 ? 'border_top' : ''">
                          <el-form class="sale-ModifyPerformanceCommission-Handler" label-position="right" size="mini" label-width="150px">
                            <el-form-item :label="`${entity.EntityName}：`">{{ entity.Scale }}%</el-form-item>
                          </el-form>
                        </el-col>
                        <el-col :span="18" class="border_left border_right">
                          <el-row class="padtp_10 padrt_10" :class="index != 0 ? 'border_top' : ''">
                            <el-col v-if="item.PayAmount && item.PayAmount > 0" :span="8">
                              <el-form class="sale-ModifyPerformanceCommission-Handler" label-position="right" size="mini" label-width="120px">
                                <el-form-item label="现金业绩">
                                  <span slot="label">
                                    现金业绩
                                    <el-popover placement="top-start" width="280" trigger="hover">
                                      <p>现金业绩 = 现金付款金额 x 门店现金业绩占比 x 业绩占比</p>
                                      <p>
                                        门店现金业绩占比参考值：
                                        <span v-if="item.PerformancePayRate != null">{{ (item.PerformancePayRate * 100) | toFixed | NumFormat }}%</span>
                                        <span v-else>无</span>
                                      </p>

                                      <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                    </el-popover>
                                  </span>
                                  <el-input type="number" v-input-fixed class="input_type" style="width: 120px" v-model="entity.PayPerformance" v-enter-number2>
                                    <template slot="append">元</template>
                                  </el-input>
                                </el-form-item>
                              </el-form>
                            </el-col>
                            <el-col v-if="item.CardDeductionAmount && item.CardDeductionAmount > 0" :span="8">
                              <el-form class="sale-ModifyPerformanceCommission-Handler" label-position="right" size="mini" label-width="150px">
                                <el-form-item label="卡抵扣业绩">
                                  <span slot="label">
                                    卡抵扣业绩
                                    <el-popover placement="top-start" width="280" trigger="hover">
                                      <p>卡抵扣业绩 = 卡抵扣金额 x 门店卡抵扣业绩占比 x 业绩占比</p>
                                      <p v-if="item.PerformanceSavingCardRate != null">
                                        门店卡抵扣业绩占比参考值：{{ (item.PerformanceSavingCardRate * 100) | toFixed | NumFormat }}%
                                      </p>
                                      <p v-else>门店卡抵扣业绩占比参考值：无</p>
                                      <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                    </el-popover>
                                  </span>
                                  <el-input
                                    type="number"
                                    v-input-fixed
                                    class="input_type"
                                    style="width: 100px"
                                    v-model="entity.SavingCardPerformance"
                                    v-enter-number2
                                  >
                                    <template slot="append">元</template>
                                  </el-input>
                                </el-form-item>
                              </el-form>
                            </el-col>
                            <el-col v-if="item.CardDeductionLargessAmount && item.CardDeductionLargessAmount > 0" :span="8">
                              <el-form class="sale-ModifyPerformanceCommission-Handler" label-position="right" size="mini" label-width="150px">
                                <el-form-item label="赠送卡抵扣业绩">
                                  <span slot="label">
                                    赠送卡抵扣业绩
                                    <el-popover placement="top-start" width="280" trigger="hover">
                                      <p>赠送卡抵扣业绩 = 赠送卡抵扣金额 x 门店赠送卡抵扣业绩占比 x 业绩占比</p>
                                      <p v-if="entity.PerformanceSavingCardLargessRate != null">
                                        门店赠送卡抵扣业绩占比参考值：¥ {{ entity.PerformanceSavingCardLargessRate | toFixed | NumFormat }}
                                      </p>
                                      <p v-else>门店赠送卡抵扣业绩占比参考值：无</p>
                                      <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                    </el-popover>
                                  </span>
                                  <el-input
                                    type="number"
                                    v-input-fixed
                                    class="input_type"
                                    style="width: 100px"
                                    v-model="entity.SavingCardLargessPerformance"
                                    v-enter-number2
                                  >
                                    <template slot="append">元</template>
                                  </el-input>
                                </el-form-item>
                              </el-form>
                            </el-col>
                          </el-row>
                        </el-col>
                      </el-row>
                    </el-col>
                  </el-row>
                </div>
              </div>
              <!-- 套餐卡-通用次卡 -->
              <div v-if="packageCard.GeneralCard.length > 0">
                <div v-for="(item, index) in packageCard.GeneralCard" :key="index">
                  <el-row class="row_header_package_detail border_right border_left">
                    <el-col :span="9">套餐卡通用次卡</el-col>
                    <el-col :span="5">数量</el-col>
                    <el-col :span="5">购买金额</el-col>
                    <el-col :span="5">补欠款金额</el-col>
                  </el-row>
                  <el-row>
                    <el-col :span="24" class="pad_10 border_right border_left border_bottom">
                      <el-col :span="24">
                        <el-col :span="9">
                          <div>
                            {{ item.GeneralCardName }}
                            <span v-if="item.Alias">({{ item.Alias }})</span>
                            <el-tag v-if="item.IsLargess" size="mini" type="danger" class="marlt_10">赠</el-tag>
                          </div>
                        </el-col>
                        <el-col :span="5">x {{ item.Quantity * packageCard.Quantity }}</el-col>
                        <el-col :span="5">¥ {{ item.AccountTotalAmount | toFixed | NumFormat }}</el-col>
                        <el-col :span="5">¥ {{ item.TotalAmount | toFixed | NumFormat }}</el-col>
                      </el-col>
                      <el-col :span="24" class="martp_5">
                        <el-col :span="5" :offset="19">
                          <span class="color_gray font_12" v-if="item.PayAmount > 0">现金金额：¥ {{ item.PayAmount | toFixed | NumFormat }}</span>
                          <span class="color_gray font_12" :class="item.PayAmount > 0 ? 'marlt_15' : ''" v-if="item.CardDeductionAmount > 0"
                            >卡抵扣：¥ {{ item.CardDeductionAmount | toFixed | NumFormat }}</span
                          >
                          <span
                            class="color_gray font_12"
                            :class="item.PricePreferentialAmount > 0 || item.PayAmount > 0 ? 'marlt_15' : ''"
                            v-if="item.CardDeductionLargessAmount > 0"
                            >赠送卡抵扣：¥ {{ item.CardDeductionLargessAmount | toFixed | NumFormat }}</span
                          >
                        </el-col>
                      </el-col>
                    </el-col>
                  </el-row>

                  <el-row  v-if="!item.IsLargess" class="padlt_10 border_left border_bottom font_12">
                    <el-col :span="2" class="padtp_10 padbm_10">门店</el-col>
                    <el-col :span="22" class="border_left">
                      <el-row v-for="(entity, index) in item.Performance" :key="entity.EntityID + 'EntityID'">
                        <el-col :span="6" class="padtp_10 padrt_10" :class="index != 0 ? 'border_top' : ''">
                          <el-form class="sale-ModifyPerformanceCommission-Handler" label-position="right" size="mini" label-width="150px">

                            <el-form-item :label="`${entity.EntityName}：`">{{ entity.Scale }}%</el-form-item>
                          </el-form>
                        </el-col>
                        <el-col :span="18" class="border_left border_right">
                          <el-row class="padtp_10 padrt_10" :class="index != 0 ? 'border_top' : ''">
                            <el-col v-if="item.PayAmount && item.PayAmount > 0" :span="8">
                              <el-form class="sale-ModifyPerformanceCommission-Handler" label-position="right" size="mini" label-width="120px">
                                <el-form-item label="现金业绩">
                                  <span slot="label">
                                    现金业绩
                                    <el-popover placement="top-start" width="280" trigger="hover">
                                      <p>现金业绩 = 现金付款金额 x 门店现金业绩占比 x 业绩占比</p>
                                      <p>
                                        门店现金业绩占比参考值：
                                        <span v-if="item.PerformancePayRate != null">{{ (item.PerformancePayRate * 100) | toFixed | NumFormat }}%</span>
                                        <span v-else>无</span>
                                      </p>

                                      <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                    </el-popover>
                                  </span>
                                  <el-input type="number" v-input-fixed class="input_type" style="width: 120px" v-model="entity.PayPerformance" v-enter-number2>
                                    <template slot="append">元</template>
                                  </el-input>
                                </el-form-item>
                              </el-form>
                            </el-col>
                            <el-col v-if="item.CardDeductionAmount && item.CardDeductionAmount > 0" :span="8">
                              <el-form class="sale-ModifyPerformanceCommission-Handler" label-position="right" size="mini" label-width="150px">
                                <el-form-item label="卡抵扣业绩">
                                  <span slot="label">
                                    卡抵扣业绩
                                    <el-popover placement="top-start" width="280" trigger="hover">
                                      <p>卡抵扣业绩 = 卡抵扣金额 x 门店卡抵扣业绩占比 x 业绩占比</p>
                                      <p v-if="item.PerformanceSavingCardRate != null">
                                        门店卡抵扣业绩占比参考值：{{ (item.PerformanceSavingCardRate * 100) | toFixed | NumFormat }}%
                                      </p>
                                      <p v-else>门店卡抵扣业绩占比参考值：无</p>
                                      <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                    </el-popover>
                                  </span>
                                  <el-input
                                    type="number"
                                    v-input-fixed
                                    class="input_type"
                                    style="width: 100px"
                                    v-model="entity.SavingCardPerformance"
                                    v-enter-number2
                                  >
                                    <template slot="append">元</template>
                                  </el-input>
                                </el-form-item>
                              </el-form>
                            </el-col>
                            <el-col v-if="item.CardDeductionLargessAmount && item.CardDeductionLargessAmount > 0" :span="8">
                              <el-form class="sale-ModifyPerformanceCommission-Handler" label-position="right" size="mini" label-width="150px">
                                <el-form-item label="赠送卡抵扣业绩">
                                  <span slot="label">
                                    赠送卡抵扣业绩
                                    <el-popover placement="top-start" width="280" trigger="hover">
                                      <p>赠送卡抵扣业绩 = 赠送卡抵扣金额 x 门店赠送卡抵扣业绩占比 x 业绩占比</p>
                                      <p v-if="entity.PerformanceSavingCardLargessRate != null">
                                        门店赠送卡抵扣业绩占比参考值：¥ {{ entity.PerformanceSavingCardLargessRate | toFixed | NumFormat }}
                                      </p>
                                      <p v-else>门店赠送卡抵扣业绩占比参考值：无</p>
                                      <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                    </el-popover>
                                  </span>
                                  <el-input
                                    type="number"
                                    v-input-fixed
                                    class="input_type"
                                    style="width: 100px"
                                    v-model="entity.SavingCardLargessPerformance"
                                    v-enter-number2
                                  >
                                    <template slot="append">元</template>
                                  </el-input>
                                </el-form-item>
                              </el-form>
                            </el-col>
                          </el-row>
                        </el-col>
                      </el-row>
                    </el-col>
                  </el-row>
                </div>
              </div>
              <!-- 套餐卡-时效卡 -->
              <div v-if="packageCard.TimeCard.length > 0">
                <div v-for="(item, index) in packageCard.TimeCard" :key="index">
                  <el-row class="row_header_package_detail border_right border_left">
                    <el-col :span="9">套餐卡时效卡</el-col>
                    <el-col :span="5">数量</el-col>
                    <el-col :span="5">购买金额</el-col>
                    <el-col :span="5">补欠款金额</el-col>
                  </el-row>
                  <el-row>
                    <el-col :span="24" class="pad_10 border_right border_left border_bottom">
                      <el-col :span="24">
                        <el-col :span="9">
                          <div>
                            {{ item.TimeCardName }}
                            <span v-if="item.Alias">({{ item.Alias }})</span>
                            <el-tag v-if="item.IsLargess" size="mini" type="danger" class="marlt_10">赠</el-tag>
                          </div>
                        </el-col>
                        <el-col :span="5">x {{ item.Quantity * packageCard.Quantity }}</el-col>

                        <el-col :span="5">¥ {{ item.AccountTotalAmount | toFixed | NumFormat }}</el-col>

                        <el-col :span="5">¥ {{ item.TotalAmount | toFixed | NumFormat }}</el-col>
                      </el-col>
                      <el-col :span="24" class="martp_5">
                        <el-col :span="5" :offset="19">
                          <span class="color_gray font_12" v-if="item.PayAmount > 0">现金金额：¥ {{ item.PayAmount | toFixed | NumFormat }}</span>
                          <span class="color_gray font_12" :class="item.PayAmount > 0 ? 'marlt_15' : ''" v-if="item.CardDeductionAmount > 0"
                            >卡抵扣：¥ {{ item.CardDeductionAmount | toFixed | NumFormat }}</span
                          >
                          <span
                            class="color_gray font_12"
                            :class="item.PricePreferentialAmount > 0 || item.PayAmount > 0 ? 'marlt_15' : ''"
                            v-if="item.CardDeductionLargessAmount > 0"
                            >赠送卡抵扣：¥ {{ item.CardDeductionLargessAmount | toFixed | NumFormat }}</span
                          >
                        </el-col>
                      </el-col>
                    </el-col>
                  </el-row>

                  <el-row  v-if="!item.IsLargess" class="padlt_10 border_left border_bottom font_12">
                    <el-col :span="2" class="padtp_10 padbm_10">门店</el-col>
                    <el-col :span="22" class="border_left">
                      <el-row v-for="(entity, index) in item.Performance" :key="entity.EntityID + 'EntityID'">
                        <el-col :span="6" class="padtp_10 padrt_10" :class="index != 0 ? 'border_top' : ''">
                          <el-form class="sale-ModifyPerformanceCommission-Handler" label-position="right" size="mini" label-width="150px">

                            <el-form-item :label="`${entity.EntityName}：`">{{ entity.Scale }}%</el-form-item>
                          </el-form>
                        </el-col>
                        <el-col :span="18" class="border_left border_right">
                          <el-row class="padtp_10 padrt_10" :class="index != 0 ? 'border_top' : ''">
                            <el-col v-if="item.PayAmount && item.PayAmount > 0" :span="8">
                              <el-form class="sale-ModifyPerformanceCommission-Handler" label-position="right" size="mini" label-width="120px">
                                <el-form-item label="现金业绩">
                                  <span slot="label">
                                    现金业绩
                                    <el-popover placement="top-start" width="280" trigger="hover">
                                      <p>现金业绩 = 现金付款金额 x 门店现金业绩占比 x 业绩占比</p>
                                      <p>
                                        门店现金业绩占比参考值：
                                        <span v-if="item.PerformancePayRate != null">{{ (item.PerformancePayRate * 100) | toFixed | NumFormat }}%</span>
                                        <span v-else>无</span>
                                      </p>

                                      <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                    </el-popover>
                                  </span>
                                  <el-input type="number" v-input-fixed class="input_type" style="width: 120px" v-model="entity.PayPerformance" v-enter-number2>
                                    <template slot="append">元</template>
                                  </el-input>
                                </el-form-item>
                              </el-form>
                            </el-col>
                            <el-col v-if="item.CardDeductionAmount && item.CardDeductionAmount > 0" :span="8">
                              <el-form class="sale-ModifyPerformanceCommission-Handler" label-position="right" size="mini" label-width="150px">
                                <el-form-item label="卡抵扣业绩">
                                  <span slot="label">
                                    卡抵扣业绩
                                    <el-popover placement="top-start" width="280" trigger="hover">
                                      <p>卡抵扣业绩 = 卡抵扣金额 x 门店卡抵扣业绩占比 x 业绩占比</p>
                                      <p v-if="item.PerformanceSavingCardRate != null">
                                        门店卡抵扣业绩占比参考值：{{ (item.PerformanceSavingCardRate * 100) | toFixed | NumFormat }}%
                                      </p>
                                      <p v-else>门店卡抵扣业绩占比参考值：无</p>
                                      <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                    </el-popover>
                                  </span>
                                  <el-input
                                    type="number"
                                    v-input-fixed
                                    class="input_type"
                                    style="width: 100px"
                                    v-model="entity.SavingCardPerformance"
                                    v-enter-number2
                                  >
                                    <template slot="append">元</template>
                                  </el-input>
                                </el-form-item>
                              </el-form>
                            </el-col>
                            <el-col v-if="item.CardDeductionLargessAmount && item.CardDeductionLargessAmount > 0" :span="8">
                              <el-form class="sale-ModifyPerformanceCommission-Handler" label-position="right" size="mini" label-width="150px">
                                <el-form-item label="赠送卡抵扣业绩">
                                  <span slot="label">
                                    赠送卡抵扣业绩
                                    <el-popover placement="top-start" width="280" trigger="hover">
                                      <p>赠送卡抵扣业绩 = 赠送卡抵扣金额 x 门店赠送卡抵扣业绩占比 x 业绩占比</p>
                                      <p v-if="entity.PerformanceSavingCardLargessRate != null">
                                        门店赠送卡抵扣业绩占比参考值：¥ {{ entity.PerformanceSavingCardLargessRate | toFixed | NumFormat }}
                                      </p>
                                      <p v-else>门店赠送卡抵扣业绩占比参考值：无</p>
                                      <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                    </el-popover>
                                  </span>
                                  <el-input
                                    type="number"
                                    v-input-fixed
                                    class="input_type"
                                    style="width: 100px"
                                    v-model="entity.SavingCardLargessPerformance"
                                    v-enter-number2
                                  >
                                    <template slot="append">元</template>
                                  </el-input>
                                </el-form-item>
                              </el-form>
                            </el-col>
                          </el-row>
                        </el-col>
                      </el-row>
                    </el-col>
                  </el-row>
                </div>
              </div>
              <!-- 套餐卡-储值卡 -->
              <div v-if="packageCard.SavingCard.length > 0">
                <div v-for="(item, index) in packageCard.SavingCard" :key="index">
                  <el-row class="row_header_package_detail border_right border_left">
                    <el-col :span="8">套餐卡储值卡</el-col>
                    <el-col :span="8">充值金额</el-col>
                    <el-col :span="8">补欠款金额</el-col>
                  </el-row>
                  <el-row>
                    <el-col :span="24" class="pad_10 border_right border_left border_bottom">
                      <el-col :span="24">
                        <el-col :span="8">
                          <div>
                            {{ item.SavingCardName }}
                            <span v-if="item.Alias">({{ item.Alias }})</span>
                            <el-tag v-if="item.IsLargess" size="mini" type="danger" class="marlt_10">赠</el-tag>
                          </div>
                        </el-col>
                        <el-col :span="8">¥ {{ item.AccountTotalAmount | toFixed | NumFormat }}</el-col>
                        <el-col :span="8">¥ {{ item.TotalAmount | toFixed | NumFormat }}</el-col>
                      </el-col>
                      <el-col :span="24" class="martp_5">
                        <el-col :span="8" :offset="16">
                          <span class="color_gray font_12" v-if="item.PayAmount > 0">现金金额：¥ {{ item.PayAmount | toFixed | NumFormat }}</span>
                        </el-col>
                      </el-col>
                    </el-col>
                  </el-row>

                  <el-row  v-if="!item.IsLargess" class="padlt_10 border_left border_bottom font_12">
                    <el-col :span="2" class="padtp_10 padbm_10">门店</el-col>
                    <el-col :span="22" class="border_left">
                      <el-row v-for="(entity, index) in item.Performance" :key="entity.EntityID + 'EntityID'">
                        <el-col :span="6" class="padtp_10 padrt_10" :class="index != 0 ? 'border_top' : ''">
                          <el-form class="sale-ModifyPerformanceCommission-Handler" label-position="right" size="mini" label-width="150px">

                            <el-form-item :label="`${entity.EntityName}：`">{{ entity.Scale }}%</el-form-item>
                          </el-form>
                        </el-col>
                        <el-col :span="18" class="border_left border_right">
                          <el-row class="padtp_10 padrt_10" :class="index != 0 ? 'border_top' : ''">
                            <el-col v-if="item.PayAmount && item.PayAmount > 0" :span="8">
                              <el-form class="sale-ModifyPerformanceCommission-Handler" label-position="right" size="mini" label-width="120px">
                                <el-form-item label="现金业绩">
                                  <span slot="label">
                                    现金业绩
                                    <el-popover placement="top-start" width="280" trigger="hover">
                                      <p>现金业绩 = 现金付款金额 x 门店现金业绩占比 x 业绩占比</p>
                                      <p>
                                        门店现金业绩占比参考值：
                                        <span v-if="item.PerformancePayRate != null">{{ (item.PerformancePayRate * 100) | toFixed | NumFormat }}%</span>
                                        <span v-else>无</span>
                                      </p>

                                      <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                    </el-popover>
                                  </span>
                                  <el-input type="number" v-input-fixed class="input_type" style="width: 120px" v-model="entity.PayPerformance" v-enter-number2>
                                    <template slot="append">元</template>
                                  </el-input>
                                </el-form-item>
                              </el-form>
                            </el-col>
                          </el-row>
                        </el-col>
                      </el-row>
                    </el-col>
                  </el-row>
                </div>
              </div>
            </div>
          </div>
        </el-scrollbar>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="visible_ = false" size="small">取消</el-button>
        <el-button type="primary" @click="saveModifyEntityPerformance" size="small" v-prevent-click :loading="loading">保存</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: "arrearModifyEntityPerformance_components",

  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    orderDetail: {
      type: Object,
    },
    loading: {
      type: Boolean,
      default: false,
    },
    billEntityID: {
      type: [String, Number],
      default: null,
    },
  },
  /** 监听数据变化   */
  watch: {
    visible: {
      immediate: true,
      handler(val) {
        this.visible_ = val;
      },
    },
  },
  /**  引入的组件  */
  components: {},
  /**  Vue 实例的数据对象**/
  data() {
    return {
      visible_: false,
      changeLoading: false,
    };
  },
  /**计算属性  */
  computed: {},
  /**  方法集合  */
  methods: {
    /**  保存业绩修改  */
    saveModifyEntityPerformance() {
      let that = this;
      that.$emit("saveModifyEntityPerformance");
    },
    /**  关闭弹窗  */
    closeModifyEntityPerformance() {
      let that = this;
      that.$emit("update:visible", false);
    },
    /**   保存门店 */
    saveEntityPerformanceClick() {
      // let that = this;
    },
    /**  添加 门店业绩  */
    entityHandleClick(item, type) {
      let that = this;
      that.$emit("entityHandleClick", item, type);
    },
    /**  修改 门店业绩占比  */
    changeSaleEntityRate(item, entity, type) {
      let that = this;
      that.$emit("changeSaleEntityRate", item, entity, type);
    },
    /**  删除门店  */
    removeEntityClick(item, entity, entityIndex) {
      let that = this;
      that.$emit("removeEntityClick", item, entity, entityIndex);
    },
    /**   批量设置门店业绩 */
    batchSettingSaleEntityPerformanceClick() {
      let that = this;
      that.$emit("batchSettingSaleEntityPerformanceClick");
    },
  },
  /** 创建实例之前执行函数   */
  beforeCreate() {},
  /**  实例创建完成之后  */
  created() {},
  /**  在挂载开始之前被调用  */
  beforeMount() {},
  /**  实例被挂载后调用  */
  mounted() {},
  /**  数据更新 之前 调用  */
  beforeUpdate() {},
  /** 数据更新 完成 调用   */
  updated() {},
  /**  实例销毁之前调用  */
  beforeDestroy() {},
  /**  实例销毁后调用  */
  destroyed() {},
};
</script>

<style lang="scss">
.arrearModifyEntityPerformance {
  .sale-ModifyPerformanceCommission-Handler {
    .el-form-item__label {
      font-size: 12px !important;
      line-height: 26px;
    }
    .el-form-item__content {
      font-size: 12px !important;
      line-height: 26px;
      display: flex;
      align-items: center;
    }
    .el-form-item {
      margin-bottom: 10px;
    }
  }

  .el-scrollbar_height {
    height: calc(100% - 60px) !important ;
    .el-scrollbar__wrap {
      overflow-x: hidden;
    }
  }
}
</style>
