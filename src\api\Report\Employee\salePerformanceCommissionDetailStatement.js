/**
 * 员工销售业绩api
 */
 import * as API from '@/api/index'

 export default {
     // 营业报表-实收统计
     getEmployeeSalePerformanceCommissionDetail: params => {
         return API.POST('api/employeeSalePerformanceCommissionDetailStatement/list', params)
     },
    // 54.9.员工销售业绩提成明细导出
    exportEmployeeSalePerformanceCommissionDetailStatement: params => {
      return API.exportExcel('api/employeeSalePerformanceCommissionDetailStatement/excel', params)
    },
  /* 查询客户等级 */
  customerLevel_all: (params) => {
    return API.POST("api/customerLevel/all", params);
  },
 }