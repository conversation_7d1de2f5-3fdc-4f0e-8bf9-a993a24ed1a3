(function (t) {
  function e(e) {
    for (var s, a, r = e[0], d = e[1], l = e[2], u = 0, p = []; u < r.length; u++) (a = r[u]), Object.prototype.hasOwnProperty.call(o, a) && o[a] && p.push(o[a][0]), (o[a] = 0);
    for (s in d) Object.prototype.hasOwnProperty.call(d, s) && (t[s] = d[s]);
    c && c(e);
    while (p.length) p.shift()();
    return n.push.apply(n, l || []), i();
  }
  function i() {
    for (var t, e = 0; e < n.length; e++) {
      for (var i = n[e], s = !0, r = 1; r < i.length; r++) {
        var d = i[r];
        0 !== o[d] && (s = !1);
      }
      s && (n.splice(e--, 1), (t = a((a.s = i[0]))));
    }
    return t;
  }
  var s = {},
    o = { HongMaiEditor: 0 },
    n = [];
  function a(e) {
    if (s[e]) return s[e].exports;
    var i = (s[e] = { i: e, l: !1, exports: {} });
    return t[e].call(i.exports, i, i.exports, a), (i.l = !0), i.exports;
  }
  (a.m = t),
    (a.c = s),
    (a.d = function (t, e, i) {
      a.o(t, e) || Object.defineProperty(t, e, { enumerable: !0, get: i });
    }),
    (a.r = function (t) {
      "undefined" !== typeof Symbol && Symbol.toStringTag && Object.defineProperty(t, Symbol.toStringTag, { value: "Module" }), Object.defineProperty(t, "__esModule", { value: !0 });
    }),
    (a.t = function (t, e) {
      if ((1 & e && (t = a(t)), 8 & e)) return t;
      if (4 & e && "object" === typeof t && t && t.__esModule) return t;
      var i = Object.create(null);
      if ((a.r(i), Object.defineProperty(i, "default", { enumerable: !0, value: t }), 2 & e && "string" != typeof t))
        for (var s in t)
          a.d(
            i,
            s,
            function (e) {
              return t[e];
            }.bind(null, s)
          );
      return i;
    }),
    (a.n = function (t) {
      var e =
        t && t.__esModule
          ? function () {
              return t["default"];
            }
          : function () {
              return t;
            };
      return a.d(e, "a", e), e;
    }),
    (a.o = function (t, e) {
      return Object.prototype.hasOwnProperty.call(t, e);
    }),
    (a.p = "");
  var r = (window["webpackJsonp"] = window["webpackJsonp"] || []),
    d = r.push.bind(r);
  (r.push = e), (r = r.slice());
  for (var l = 0; l < r.length; l++) e(r[l]);
  var c = d;
  n.push([1, "chunk-vendors-editor", "chunk-editor"]), i();
})({
  "0080": function (t, e, i) {
    "use strict";
    i("7f21");
  },
  "01bb": function (t, e, i) {},
  "0551": function (t, e, i) {},
  "0a87": function (t, e, i) {
    "use strict";
    i("1ade");
  },
  1: function (t, e, i) {
    t.exports = i("4e12");
  },
  "1ade": function (t, e, i) {},
  "1c05": function (t, e, i) {
    "use strict";
    i("0551");
  },
  "1cbb": function (t, e, i) {},
  "1d4f": function (t, e, i) {},
  3171: function (t, e, i) {},
  3750: function (t, e, i) {
    "use strict";
    i("9d1f");
  },
  "47e1": function (t, e, i) {
    "use strict";
    i("ca68");
  },
  "4e12": function (t, e, i) {
    "use strict";
    i.r(e);
    var s = i("a026"),
      o = function () {
        var t = this,
          e = t._self._c;
        t._self._setupProxy;
        return e("div", { staticClass: "hms-editor-wrapper" }, [
          t.hideTool
            ? t._e()
            : e("div", { staticClass: "hms-editor-header hms-editor-tool" }, [
                e("div", { staticClass: "hms-btn-group" }, [
                  t.freeEdit
                    ? t._e()
                    : e(
                        "button",
                        {
                          attrs: { disabled: t.editorCtx.headerEditing },
                          on: {
                            click: function (e) {
                              return t.setEditable(!t.editable);
                            },
                          },
                        },
                        [e("div", { class: t.editable ? "hm-icon-edit-data" : "hm-icon-design" }), e("span", [t._v(t._s(t.editable ? "编辑模式" : "设计模式"))])]
                      ),
                  t.freeEdit ? t._e() : e("div", { staticClass: "hms-editor-tool-btn-split" }),
                  t.freeEdit
                    ? t._e()
                    : e(
                        "button",
                        {
                          attrs: { disabled: !t.editable || t.editorCtx.headerEditing, title: "选项" },
                          on: {
                            click: function (e) {
                              return t.inserCheck();
                            },
                          },
                        },
                        [e("div", { staticClass: "hm-icon-checkbox" }), e("span", [t._v("选项")])]
                      ),
                  t.freeEdit
                    ? t._e()
                    : e(
                        "button",
                        {
                          attrs: { disabled: !t.editable || t.editorCtx.headerEditing, title: "下拉选择" },
                          on: {
                            click: function (e) {
                              return t.inserSelect();
                            },
                          },
                        },
                        [e("div", { staticClass: "hm-icon-dropdown" }), e("span", [t._v("下拉")])]
                      ),
                  t.freeEdit
                    ? t._e()
                    : e(
                        "button",
                        {
                          attrs: { disabled: !t.editable || t.editorCtx.headerEditing, title: "词条" },
                          on: {
                            click: function (e) {
                              return t.cmds.wordentry();
                            },
                          },
                        },
                        [e("div", { staticClass: "hm-icon-input" }), e("span", [t._v("词条")])]
                      ),
                  t.freeEdit
                    ? t._e()
                    : e(
                        "button",
                        {
                          attrs: { disabled: !t.editable || t.editorCtx.headerEditing, title: "创建联动组" },
                          on: {
                            click: function (e) {
                              return t.showgroup();
                            },
                          },
                        },
                        [e("div", { staticClass: "hm-icon-group" }), e("span", [t._v("联动")])]
                      ),
                  t.freeEdit ? t._e() : e("button", { attrs: { disabled: !t.editable || t.editorCtx.headerEditing, title: "字段" }, on: { click: t.insertField } }, [e("div", { staticClass: "hm-icon-field" }), e("span", [t._v("字段")])]),
                  e(
                    "button",
                    {
                      attrs: { disabled: !t.editable || t.editorCtx.headerEditing, title: "时间" },
                      on: {
                        click: function (e) {
                          return t.cmds.datetime();
                        },
                      },
                    },
                    [e("div", { staticClass: "hm-icon-datetime" }), e("span", [t._v("时间")])]
                  ),
                  e("div", { staticClass: "hms-editor-tool-btn-split" }),
                  e(
                    "button",
                    {
                      attrs: { disabled: !t.editable || t.editorCtx.headerEditing, title: "图片" },
                      on: {
                        click: function (e) {
                          return t.insertImg();
                        },
                      },
                    },
                    [e("div", { staticClass: "hm-icon-img" }), e("span", [t._v("图片")])]
                  ),
                  e(
                    "button",
                    {
                      attrs: { disabled: !t.editable || t.editorCtx.headerEditing, title: "表格" },
                      on: {
                        click: function (e) {
                          return t.cmds.createTable({ rowsCount: 3, colsCount: 4, withHeaderRow: !1 });
                        },
                      },
                    },
                    [e("div", { staticClass: "hm-icon-table" }), e("span", [t._v("表格")])]
                  ),
                  t.freeEdit
                    ? t._e()
                    : e(
                        "button",
                        {
                          attrs: { disabled: !t.editable || t.editorCtx.headerEditing, title: "电子签名" },
                          on: {
                            click: function (e) {
                              return t.cmds.sign();
                            },
                          },
                        },
                        [e("div", { staticClass: "hm-icon-sign2" }), e("span", [t._v("签名")])]
                      ),
                  t.freeEdit
                    ? t._e()
                    : e(
                        "button",
                        {
                          attrs: { disabled: !t.editable || t.editorCtx.headerEditing, title: "电子签名" },
                          on: {
                            click: function (e) {
                              return t.cmds.chart({ chart: "jiancha" });
                            },
                          },
                        },
                        [e("div", { staticClass: "hm-icon-field" }), e("span", [t._v("检查")])]
                      ),
                  e("div", { staticClass: "hms-editor-tool-btn-split" }),
                  t.freeEdit
                    ? t._e()
                    : e(
                        "button",
                        {
                          attrs: { disabled: !t.editable || t.editorCtx.headerEditing, title: "脉搏/体温表" },
                          on: {
                            click: function (e) {
                              return t.cmds.chart({ chart: "tiwenchart" });
                            },
                          },
                        },
                        [e("div", { staticClass: "hm-icon-chart" }), e("span", [t._v("脉搏图")])]
                      ),
                  t.freeEdit
                    ? t._e()
                    : e(
                        "button",
                        {
                          attrs: { disabled: !t.editable || t.editorCtx.headerEditing, title: "植发图" },
                          on: {
                            click: function (e) {
                              return t.insertSvg();
                            },
                          },
                        },
                        [e("div", { staticClass: "hm-icon-mark-range" }), e("span", [t._v("植发图")])]
                      ),
                  t.freeEdit
                    ? t._e()
                    : e(
                        "button",
                        {
                          attrs: { disabled: !t.editable || t.editorCtx.headerEditing, title: "手术示意图(SVG 区位选择)" },
                          on: {
                            click: function (e) {
                              return t.imgMark();
                            },
                          },
                        },
                        [e("div", { staticClass: "hm-icon-human-body" }), e("span", [t._v("标注图")])]
                      ),
                  e("div", { staticClass: "hms-editor-tool-btn-split" }),
                  e(
                    "button",
                    {
                      attrs: { disabled: !t.editable || t.editorCtx.headerEditing, title: "标题1" },
                      on: {
                        click: function (e) {
                          return t.cmds.heading({ level: 1 });
                        },
                      },
                    },
                    [e("div", { staticClass: "hm-icon-heading1" }), e("span", [t._v("标题1")])]
                  ),
                  e(
                    "button",
                    {
                      attrs: { disabled: !t.editable || t.editorCtx.headerEditing, title: "标题2" },
                      on: {
                        click: function (e) {
                          return t.cmds.heading({ level: 2 });
                        },
                      },
                    },
                    [e("div", { staticClass: "hm-icon-heading2" }), e("span", [t._v("标题2")])]
                  ),
                  e(
                    "button",
                    {
                      attrs: { disabled: !t.editable || t.editorCtx.headerEditing, title: "标题3" },
                      on: {
                        click: function (e) {
                          return t.cmds.heading({ level: 3 });
                        },
                      },
                    },
                    [e("div", { staticClass: "hm-icon-heading3" }), e("span", [t._v("标题3")])]
                  ),
                  e("div", { staticClass: "hms-editor-tool-btn-split" }),
                  e("div", { staticStyle: { "align-items": "normal" } }, [
                    e("div", [
                      e("button", {
                        staticClass: "hm-icon-align-left",
                        attrs: { disabled: !t.editable || t.editorCtx.headerEditing, title: "左对齐" },
                        on: {
                          click: function (e) {
                            return t.cmds.setTextAlign("left");
                          },
                        },
                      }),
                      e("button", {
                        staticClass: "hm-icon-align-center",
                        attrs: { disabled: !t.editable || t.editorCtx.headerEditing, title: "居中对齐" },
                        on: {
                          click: function (e) {
                            return t.cmds.setTextAlign("center");
                          },
                        },
                      }),
                      e("button", {
                        staticClass: "hm-icon-align-right",
                        attrs: { disabled: !t.editable || t.editorCtx.headerEditing, title: "右对齐" },
                        on: {
                          click: function (e) {
                            return t.cmds.setTextAlign("right");
                          },
                        },
                      }),
                      e("button", {
                        staticClass: "hm-icon-align-justify",
                        attrs: { disabled: !t.editable || t.editorCtx.headerEditing, title: "两端对齐" },
                        on: {
                          click: function (e) {
                            return t.cmds.setTextAlign("justify");
                          },
                        },
                      }),
                    ]),
                    e("div", [
                      e("button", {
                        staticClass: "hm-icon-division",
                        attrs: { disabled: !t.editable || t.editorCtx.headerEditing, title: "除法符号" },
                        on: {
                          click: function (e) {
                            return t.cmds.CommonInline({ type: "division" });
                          },
                        },
                      }),
                      e("button", {
                        staticClass: "hm-icon-symbol",
                        attrs: { disabled: !t.editable || t.editorCtx.headerEditing, title: "特殊符号(Alt+/)" },
                        on: {
                          click: function (e) {
                            return t.symbol();
                          },
                        },
                      }),
                      e("button", {
                        staticClass: "hm-icon-hr",
                        attrs: { disabled: !t.editable || t.editorCtx.headerEditing, title: "横线" },
                        on: {
                          click: function (e) {
                            return t.cmds.hr();
                          },
                        },
                      }),
                      e("button", {
                        staticClass: "hm-icon-sup",
                        attrs: { disabled: !t.editable || t.editorCtx.headerEditing, title: "上标" },
                        on: {
                          click: function (e) {
                            return t.cmds.sup();
                          },
                        },
                      }),
                    ]),
                  ]),
                  e("div", { staticClass: "hms-editor-tool-btn-split" }),
                  e(
                    "button",
                    {
                      attrs: { disabled: !t.editable || t.editorCtx.headerEditing, title: "分页" },
                      on: {
                        click: function (e) {
                          return t.cmds.pagebreak();
                        },
                      },
                    },
                    [e("div", { staticClass: "hm-icon-pagebreak1" }), e("span", [t._v("分页")])]
                  ),
                  e(
                    "button",
                    {
                      attrs: { disabled: !t.editable || t.editorCtx.headerEditing, title: "纸张预览" },
                      on: {
                        click: function (e) {
                          return t.paperSetting();
                        },
                      },
                    },
                    [e("div", { staticClass: "hm-icon-paper" }), e("span", [t._v("纸张")])]
                  ),
                ]),
              ]),
          t.groupshow
            ? e("div", { staticClass: "group" }, [
                e(
                  "div",
                  { staticClass: "group-content", class: { itemdrag: t.dragstartItem }, on: { drop: t.drop, dragover: t.ondragover } },
                  t._l(t.tempgroup, function (i, s) {
                    return e(
                      "span",
                      {
                        key: s,
                        staticClass: "group-item",
                        on: {
                          mouseleave: function (e) {
                            return t.outGroupItem(i.el);
                          },
                          mouseenter: function (e) {
                            return t.hoverGroupItem(i.el);
                          },
                        },
                      },
                      [
                        e("span", [t._v(t._s(i.id))]),
                        e("button", {
                          staticClass: "hm-icon-delete",
                          attrs: { title: "从组中删除" },
                          on: {
                            click: function (e) {
                              return t.delItem(i, s);
                            },
                          },
                        }),
                      ]
                    );
                  }),
                  0
                ),
                e("div", { staticClass: "group-btn" }, [
                  e(
                    "button",
                    {
                      staticClass: "cancalgroup",
                      on: {
                        click: function (e) {
                          return t.showgroup();
                        },
                      },
                    },
                    [t._v("取消")]
                  ),
                  e("span", { staticClass: "group-name" }, [
                    e("div", [t._v("选择联动类型：")]),
                    e(
                      "select",
                      {
                        directives: [{ name: "model", rawName: "v-model", value: t.groupname, expression: "groupname" }],
                        on: {
                          change: function (e) {
                            var i = Array.prototype.filter
                              .call(e.target.options, function (t) {
                                return t.selected;
                              })
                              .map(function (t) {
                                var e = "_value" in t ? t._value : t.value;
                                return e;
                              });
                            t.groupname = e.target.multiple ? i : i[0];
                          },
                        },
                      },
                      t._l(t.grouptipName, function (i, s) {
                        return e("option", { key: s, domProps: { value: i.val } }, [t._v(t._s(i.label))]);
                      }),
                      0
                    ),
                    e(
                      "button",
                      {
                        staticClass: "addgroup",
                        attrs: { disabled: !t.tempgroup.length || !t.groupname },
                        on: {
                          click: function (e) {
                            return t.addGroup();
                          },
                        },
                      },
                      [t._v("确定")]
                    ),
                  ]),
                ]),
              ])
            : t._e(),
          e("iframe", { ref: "editor", staticClass: "hms-editor-core", class: t.setting.size, attrs: { src: t.jsBase + "/editorCore.html", frameborder: "0" } }),
        ]);
      },
      n = [],
      a = i("ade3"),
      r = (i("14d9"), i("9ab4")),
      d = i("1b40"),
      l = i("2d2b"),
      c = function () {
        var t = this,
          e = t._self._c;
        t._self._setupProxy;
        return e(
          "div",
          { staticClass: "form" },
          [
            t.isEdit
              ? t._e()
              : e("div", { staticStyle: { "margin-bottom": "15px" } }, [
                  e("span", [t._v("类型：")]),
                  e("label", { staticStyle: { "margin-right": "30px" } }, [
                    e("input", {
                      directives: [{ name: "model", rawName: "v-model", value: t.type, expression: "type" }],
                      attrs: { type: "radio", name: "type", value: "checkbox" },
                      domProps: { checked: t._q(t.type, "checkbox") },
                      on: {
                        change: function (e) {
                          t.type = "checkbox";
                        },
                      },
                    }),
                    e("span", [t._v("多选")]),
                  ]),
                  e("label", [
                    e("input", {
                      directives: [{ name: "model", rawName: "v-model", value: t.type, expression: "type" }],
                      attrs: { type: "radio", name: "type", value: "radio" },
                      domProps: { checked: t._q(t.type, "radio") },
                      on: {
                        change: function (e) {
                          t.type = "radio";
                        },
                      },
                    }),
                    e("span", [t._v("单选")]),
                  ]),
                ]),
            e("div", { staticStyle: { "margin-bottom": "15px" } }, [
              e("span", [t._v("必填：")]),
              e("label", { staticStyle: { "margin-right": "30px" } }, [
                e("input", {
                  directives: [{ name: "model", rawName: "v-model", value: t.required, expression: "required" }],
                  attrs: { type: "radio", name: "required" },
                  domProps: { value: !1, checked: t._q(t.required, !1) },
                  on: {
                    change: function (e) {
                      t.required = !1;
                    },
                  },
                }),
                e("span", [t._v("否")]),
              ]),
              e("label", [
                e("input", {
                  directives: [{ name: "model", rawName: "v-model", value: t.required, expression: "required" }],
                  attrs: { type: "radio", name: "required" },
                  domProps: { value: !0, checked: t._q(t.required, !0) },
                  on: {
                    change: function (e) {
                      t.required = !0;
                    },
                  },
                }),
                e("span", [t._v("是")]),
              ]),
            ]),
            e("optioneditor", { attrs: { tags: t.options, label: "选项" } }),
            e("button", { attrs: { disabled: (!t.type && !t.isEdit) || !t.options.length }, on: { click: t.ok } }, [t._v("确定")]),
          ],
          1
        );
      },
      u = [],
      p = function () {
        var t = this,
          e = t._self._c;
        t._self._setupProxy;
        return e("div", { staticClass: "mktageditor" }, [
          e("div", { staticStyle: { display: "flex", "align-items": "center" } }, [t.label ? e("div", { staticClass: "tags-title" }, [t._v(t._s(t.label) + "：")]) : t._e()]),
          e(
            "div",
            { staticClass: "tags" },
            t._l(t.tags, function (i, s) {
              return e(
                "div",
                { key: s, staticClass: "mkb-tag" },
                [
                  e("plaintext", {
                    staticClass: "tag-name",
                    attrs: { placeholder: "请输入内容" },
                    nativeOn: {
                      keydown: function (e) {
                        if (!e.type.indexOf("key") && t._k(e.keyCode, "enter", 13, e.key, "Enter")) return null;
                        e.preventDefault();
                      },
                    },
                    model: {
                      value: i.value,
                      callback: function (e) {
                        t.$set(i, "value", e);
                      },
                      expression: "t.value",
                    },
                  }),
                  e("div", { staticClass: "tag-config" }, [
                    e("label", [
                      e("input", {
                        directives: [{ name: "model", rawName: "v-model", value: i.remarks, expression: "t.remarks" }],
                        attrs: { type: "checkbox" },
                        domProps: { checked: Array.isArray(i.remarks) ? t._i(i.remarks, null) > -1 : i.remarks },
                        on: {
                          change: function (e) {
                            var s = i.remarks,
                              o = e.target,
                              n = !!o.checked;
                            if (Array.isArray(s)) {
                              var a = null,
                                r = t._i(s, a);
                              o.checked ? r < 0 && t.$set(i, "remarks", s.concat([a])) : r > -1 && t.$set(i, "remarks", s.slice(0, r).concat(s.slice(r + 1)));
                            } else t.$set(i, "remarks", n);
                          },
                        },
                      }),
                      e("span", [t._v("补充项")]),
                    ]),
                    e("label", [
                      e("input", {
                        directives: [{ name: "model", rawName: "v-model", value: i.checked, expression: "t.checked" }],
                        attrs: { type: "checkbox" },
                        domProps: { checked: Array.isArray(i.checked) ? t._i(i.checked, null) > -1 : i.checked },
                        on: {
                          change: function (e) {
                            var s = i.checked,
                              o = e.target,
                              n = !!o.checked;
                            if (Array.isArray(s)) {
                              var a = null,
                                r = t._i(s, a);
                              o.checked ? r < 0 && t.$set(i, "checked", s.concat([a])) : r > -1 && t.$set(i, "checked", s.slice(0, r).concat(s.slice(r + 1)));
                            } else t.$set(i, "checked", n);
                          },
                        },
                      }),
                      e("span", [t._v("默认选中")]),
                    ]),
                    e("div", {
                      staticClass: "tag-btn hm-icon-close",
                      on: {
                        click: function (e) {
                          return t.deleteTag(s);
                        },
                      },
                    }),
                  ]),
                ],
                1
              );
            }),
            0
          ),
          e(
            "form",
            {
              staticClass: "tag-form",
              on: {
                submit: function (t) {
                  t.preventDefault();
                },
              },
            },
            [
              e("input", {
                directives: [{ name: "model", rawName: "v-model", value: t.tag.value, expression: "tag.value" }],
                attrs: { type: "text", name: "tag", required: "", placeholder: "输入选项，回车" },
                domProps: { value: t.tag.value },
                on: {
                  keydown: function (e) {
                    return t.keydownDel(e);
                  },
                  input: function (e) {
                    e.target.composing || t.$set(t.tag, "value", e.target.value);
                  },
                },
              }),
              e(
                "button",
                {
                  staticClass: "icon-add button",
                  staticStyle: { display: "none" },
                  attrs: { disabled: !t.tag.value },
                  on: {
                    click: function (e) {
                      return t.addTag();
                    },
                  },
                },
                [t._v("添加")]
              ),
            ]
          ),
        ]);
      },
      h = [],
      g = function () {
        var t = this,
          e = t._self._c;
        t._self._setupProxy;
        return e("div", { ref: "input", staticClass: "plaintext", class: { editable: !t.disabled }, attrs: { tabindex: "0", contenteditable: !t.disabled, spellcheck: "false", placeholder: t.placeholder }, on: { input: t.change, compositionend: t.end, compositionstart: t.start } });
      },
      m = [];
    let b = class extends d["d"] {
      constructor(...t) {
        super(...t), Object(a["a"])(this, "tip", null), Object(a["a"])(this, "value", void 0), Object(a["a"])(this, "disabled", void 0), Object(a["a"])(this, "placeholder", void 0), Object(a["a"])(this, "composing", 0);
      }
      vChange(t) {
        let e = this.$refs.input;
        e.innerText != t && (e.innerText = t);
      }
      change(t) {
        this.composing || this.$emit("change", t.target.innerText);
      }
      start() {
        this.composing++;
      }
      end(t) {
        this.composing && ((this.composing -= 1), this.composing || this.$emit("change", t.target.innerText));
      }
      mounted() {
        let t = this.$refs.input;
        t.innerText = this.value;
      }
    };
    Object(r["a"])([Object(d["c"])(), Object(d["b"])("change")], b.prototype, "value", void 0),
      Object(r["a"])([Object(d["c"])({ default: !1 })], b.prototype, "disabled", void 0),
      Object(r["a"])([Object(d["c"])({ default: "" })], b.prototype, "placeholder", void 0),
      Object(r["a"])([Object(d["e"])("value")], b.prototype, "vChange", null),
      (b = Object(r["a"])([Object(d["a"])({})], b));
    var v = b,
      f = v,
      y = (i("5bb2"), i("2877")),
      O = Object(y["a"])(f, g, m, !1, null, "698b7bac", null),
      j = O.exports;
    let C = class extends d["d"] {
      constructor(...t) {
        super(...t), Object(a["a"])(this, "tags", void 0), Object(a["a"])(this, "label", void 0), Object(a["a"])(this, "tag", { value: "", checked: !1, remarks: !1 });
      }
      addTag() {
        let t = this.tag.value.trim();
        t && this.tags.findIndex((t) => t.value == this.tag.value) < 0 && this.tags.push(this.tag), (this.tag = { value: "", checked: !1, remarks: !1 });
      }
      keydownDel(t) {
        let e = t.keyCode ? t.keyCode : t.which;
        8 != e || this.tag || this.tags.pop();
      }
      deleteTag(t) {
        this.tags.splice(t, 1);
      }
    };
    Object(r["a"])([Object(d["c"])({ required: !0 })], C.prototype, "tags", void 0), Object(r["a"])([Object(d["c"])({})], C.prototype, "label", void 0), (C = Object(r["a"])([Object(d["a"])({ name: "mktageditor", components: { plaintext: j } })], C));
    var _ = C,
      x = _,
      k = (i("0080"), Object(y["a"])(x, p, h, !1, null, null, null)),
      w = k.exports;
    let E = class extends d["d"] {
      constructor(...t) {
        super(...t), Object(a["a"])(this, "type", ""), Object(a["a"])(this, "required", !1), Object(a["a"])(this, "rq", void 0), Object(a["a"])(this, "ops", void 0), Object(a["a"])(this, "isEdit", !1), Object(a["a"])(this, "options", []);
      }
      beforeMount() {
        this.ops && ((this.isEdit = !0), (this.required = this.rq), (this.options = this.ops));
      }
      ok() {
        this.isEdit ? this.$emit("edit", { required: this.required, options: this.options }) : this.$emit("insert", { type: this.type, required: this.required, options: this.options }), this.$parent.$destroy();
      }
    };
    Object(r["a"])([Object(d["c"])()], E.prototype, "rq", void 0), Object(r["a"])([Object(d["c"])()], E.prototype, "ops", void 0), (E = Object(r["a"])([Object(d["a"])({ components: { optioneditor: w } })], E));
    var $ = E,
      D = $,
      S = (i("1c05"), Object(y["a"])(D, c, u, !1, null, "9f9c2948", null)),
      T = S.exports,
      P = function () {
        var t = this,
          e = t._self._c;
        t._self._setupProxy;
        return e(
          "div",
          { staticClass: "form" },
          [
            e("mktag", {
              attrs: { label: "选项" },
              model: {
                value: t.options,
                callback: function (e) {
                  t.options = e;
                },
                expression: "options",
              },
            }),
            e("button", { attrs: { disabled: !t.options || !t.options.length }, on: { click: t.ok } }, [t._v("确定")]),
          ],
          1
        );
      },
      q = [],
      W = function () {
        var t = this,
          e = t._self._c;
        t._self._setupProxy;
        return e("div", { staticClass: "mktageditor" }, [
          e(
            "div",
            { staticClass: "tags" },
            [
              t.label ? e("div", { staticClass: "tags-title" }, [t._v(t._s(t.label) + "：")]) : t._e(),
              t._l(t.value, function (i, s) {
                return e("div", { key: s, staticClass: "mkb-tag" }, [
                  e("span", [t._v(t._s(i.label))]),
                  e("div", {
                    staticClass: "tag-btn hm-icon-close",
                    on: {
                      click: function (e) {
                        return t.deleteTag(s);
                      },
                    },
                  }),
                ]);
              }),
              e(
                "form",
                {
                  staticClass: "tag-form",
                  on: {
                    submit: function (t) {
                      t.preventDefault();
                    },
                  },
                },
                [
                  e("input", {
                    directives: [{ name: "model", rawName: "v-model", value: t.tag.value, expression: "tag.value" }],
                    attrs: { type: "text", name: "tag", required: "", placeholder: "输入选项，回车" },
                    domProps: { value: t.tag.value },
                    on: {
                      keydown: function (e) {
                        return !e.type.indexOf("key") && t._k(e.keyCode, "delete", [8, 46], e.key, ["Backspace", "Delete", "Del"]) ? null : t.keydownDel.apply(null, arguments);
                      },
                      input: function (e) {
                        e.target.composing || t.$set(t.tag, "value", e.target.value);
                      },
                    },
                  }),
                  e(
                    "button",
                    {
                      staticClass: "icon-add button",
                      staticStyle: { display: "none" },
                      attrs: { disabled: !t.tag.value },
                      on: {
                        click: function (e) {
                          return t.addTag();
                        },
                      },
                    },
                    [t._v("添加")]
                  ),
                ]
              ),
            ],
            2
          ),
        ]);
      },
      A = [];
    let N = class extends d["d"] {
      constructor(...t) {
        super(...t), Object(a["a"])(this, "value", void 0), Object(a["a"])(this, "label", void 0), Object(a["a"])(this, "tag", { value: "", label: "" });
      }
      addTag() {
        let t = this.tag.value.trim();
        if (t && this.value.findIndex((t) => t.value == this.tag.value) < 0) {
          let e = [...this.value];
          e.push({ value: t, label: t }), this.$emit("change", e);
        }
        this.tag = { value: "", label: "" };
      }
      keydownDel() {
        if (!this.tag.value) {
          let t = [...this.value];
          t.pop(), this.$emit("change", t);
        }
      }
      deleteTag(t) {
        let e = [...this.value];
        e.splice(t, 1), this.$emit("change", e);
      }
    };
    Object(r["a"])([Object(d["c"])(), Object(d["b"])("change")], N.prototype, "value", void 0), Object(r["a"])([Object(d["c"])({})], N.prototype, "label", void 0), (N = Object(r["a"])([Object(d["a"])({ name: "mktageditor", components: { plaintext: j } })], N));
    var M = N,
      I = M,
      H = (i("3750"), Object(y["a"])(I, W, A, !1, null, null, null)),
      B = H.exports;
    let L = class extends d["d"] {
      constructor(...t) {
        super(...t), Object(a["a"])(this, "type", ""), Object(a["a"])(this, "ops", void 0), Object(a["a"])(this, "isEdit", !1), Object(a["a"])(this, "options", []);
      }
      beforeMount() {
        this.ops && ((this.isEdit = !0), (this.options = this.ops));
      }
      ok() {
        this.isEdit ? this.$emit("edit", this.options) : this.$emit("insert", this.options), this.$parent.$destroy();
      }
    };
    Object(r["a"])([Object(d["c"])()], L.prototype, "ops", void 0), (L = Object(r["a"])([Object(d["a"])({ components: { mktag: B } })], L));
    var z = L,
      G = z,
      R = (i("a5b7"), Object(y["a"])(G, P, q, !1, null, "0e86f8b8", null)),
      F = R.exports,
      J = function () {
        var t = this,
          e = t._self._c;
        t._self._setupProxy;
        return e("div", { staticClass: "form" }, [
          e(
            "div",
            { staticStyle: { "margin-bottom": "15px" } },
            [
              e("span", [t._v("类型：")]),
              t._l(Object.keys(t.size), function (i, s) {
                return e("label", { key: s }, [
                  e("input", {
                    directives: [{ name: "model", rawName: "v-model", value: t.setting.size, expression: "setting.size" }],
                    attrs: { type: "radio", name: "type" },
                    domProps: { value: i, checked: t._q(t.setting.size, i) },
                    on: {
                      change: function (e) {
                        return t.$set(t.setting, "size", i);
                      },
                    },
                  }),
                  e("span", [t._v(t._s(t.size[i].name))]),
                ]);
              }),
            ],
            2
          ),
          e("div", { staticStyle: { "margin-bottom": "15px" } }, [
            e("span", [t._v("边距：")]),
            e("label", { staticClass: "margin", attrs: { title: "上边距" } }, [
              e("span", [t._v("上")]),
              e("input", {
                directives: [{ name: "model", rawName: "v-model", value: t.setting.marginTop, expression: "setting.marginTop" }],
                attrs: { type: "number", max: "50", min: "0" },
                domProps: { value: t.setting.marginTop },
                on: {
                  input: [
                    function (e) {
                      e.target.composing || t.$set(t.setting, "marginTop", e.target.value);
                    },
                    function (e) {
                      return t.num(e, "marginTop");
                    },
                  ],
                },
              }),
              e("span", [t._v("毫米")]),
            ]),
            e("label", { staticClass: "margin", attrs: { title: "下边距" } }, [
              e("span", [t._v("下")]),
              e("input", {
                directives: [{ name: "model", rawName: "v-model", value: t.setting.marginBottom, expression: "setting.marginBottom" }],
                attrs: { type: "number", max: "50", min: "0" },
                domProps: { value: t.setting.marginBottom },
                on: {
                  input: [
                    function (e) {
                      e.target.composing || t.$set(t.setting, "marginBottom", e.target.value);
                    },
                    function (e) {
                      return t.num(e, "marginBottom");
                    },
                  ],
                },
              }),
              e("span", [t._v("毫米")]),
            ]),
            e("label", { staticClass: "margin", attrs: { title: "左右边距" } }, [
              e("span", [t._v("左右")]),
              e("input", {
                directives: [{ name: "model", rawName: "v-model", value: t.setting.marginX, expression: "setting.marginX" }],
                attrs: { type: "number", max: "50", min: "0" },
                domProps: { value: t.setting.marginX },
                on: {
                  input: [
                    function (e) {
                      e.target.composing || t.$set(t.setting, "marginX", e.target.value);
                    },
                    function (e) {
                      return t.num(e, "marginX");
                    },
                  ],
                },
              }),
              e("span", [t._v("毫米")]),
            ]),
          ]),
        ]);
      },
      X = [];
    let U = class extends d["d"] {
      constructor(...t) {
        super(...t), Object(a["a"])(this, "setting", void 0), Object(a["a"])(this, "size", { A4: { name: "A4", width: 210, height: 297 }, A5: { name: "A5", width: 148, height: 210 }, A4T: { name: "A4横向", width: 297, height: 210 }, A5T: { name: "A5横向", width: 210, height: 148 } });
      }
      num(t, e) {
        let i = t.target.value;
        (i = Number(i)), (this.setting[e] = !i || i < 0 ? 0 : i);
      }
    };
    Object(r["a"])([Object(d["c"])({ required: !0 })], U.prototype, "setting", void 0), (U = Object(r["a"])([Object(d["a"])({})], U));
    var V = U,
      K = V,
      Q = (i("ffbd"), Object(y["a"])(K, J, X, !1, null, "112c29ed", null)),
      Y = Q.exports,
      Z = i("5db5"),
      tt = i("4627"),
      et = i("fb02"),
      it = i("28bf");
    let st = document.getElementsByTagName("script"),
      ot = st[st.length - 1],
      nt = ot.src.split("/");
    nt.pop(), (nt = nt.join("/"));
    let at = class extends d["d"] {
      constructor(...t) {
        super(...t),
          Object(a["a"])(
            this,
            "grouptipName",
            Object.keys(et["a"]).map((t) => ({ val: t, label: et["a"][t].name || t }))
          ),
          Object(a["a"])(this, "docCtx", void 0),
          Object(a["a"])(this, "docData", void 0),
          Object(a["a"])(this, "fieldData", void 0),
          Object(a["a"])(this, "fieldDict", void 0),
          Object(a["a"])(this, "wordDict", void 0),
          Object(a["a"])(this, "jsBase", nt),
          Object(a["a"])(this, "editable", !0),
          Object(a["a"])(this, "freeEdit", !1),
          Object(a["a"])(this, "hideTool", !1),
          Object(a["a"])(this, "isShowHeader", !0),
          Object(a["a"])(this, "editorCtx", { headerEditing: !1 }),
          Object(a["a"])(this, "cmds", null),
          Object(a["a"])(this, "editorWin", null),
          Object(a["a"])(this, "editingGroup", null),
          Object(a["a"])(this, "dragstartItem", null),
          Object(a["a"])(this, "tempgroup", []),
          Object(a["a"])(this, "groupshow", !1),
          Object(a["a"])(this, "groupname", ""),
          Object(a["a"])(this, "setting", { size: "A4", marginX: 10, marginTop: 20, marginBottom: 10, headerContent: "" });
      }
      saveDocData() {
        localStorage.setItem("docData", JSON.stringify(this.docData));
      }
      insertField() {
        this.cmds.textfield({ k: "user.name" });
      }
      insertSvg() {
        Object(Z["a"])("image/svg+xml", !1, (t) => {
          let e = t[0];
          if (!e) return;
          if ("image/svg+xml" != e.type) return void this.tip({ type: "warning", msg: "请选择 SVG 模型文件" });
          e.size > 102400 && this.tip({ type: "warning", msg: "不支持超过 100KB 的模型文件" });
          const i = new FileReader();
          i.readAsText(e),
            (i.onload = () => {
              this.cmds.svg({ model: i.result });
            });
        });
      }
      insertImg() {
        Object(Z["a"])(".png, .jpg", !1, (t) => {
          let e = t[0];
          e &&
            (e.size > 10485760 && this.tip({ type: "warning", msg: "不支持超过 10 M 的图片文件" }),
            Object(tt["a"])(e, { maxWidthOrHeight: 1e3, useWebWorker: !1 }).then((t) => {
              let e = new FileReader();
              (e.onload = (t) => {
                this.cmds.image({ src: e.result });
              }),
                e.readAsDataURL(t);
            }));
        });
      }
      imgMark() {
        Object(Z["a"])(".png, .jpg", !1, (t) => {
          let e = t[0];
          e &&
            (e.size > 1048576 && this.tip({ type: "warning", msg: "不支持超过 1 M 的图片文件" }),
            Object(tt["a"])(e, { maxWidthOrHeight: 1e3, useWebWorker: !1 }).then((t) => {
              let e = new FileReader();
              (e.onload = (t) => {
                this.cmds.imgmark({ src: e.result });
              }),
                e.readAsDataURL(t);
            }));
        });
      }
      inserCheck() {
        this.dialog({
          title: "插入选项",
          component: T,
          data: {
            on: {
              insert: ({ type: t, required: e, options: i }) => {
                this.cmds.check({ type: t, required: e, options: JSON.stringify(i.map((t) => ((t.checked = !!t.checked), t))) });
              },
            },
          },
        });
      }
      inserSelect() {
        this.dialog({
          title: "插入下拉",
          component: F,
          data: {
            on: {
              insert: (t) => {
                this.cmds.CommonField({ type: "select", meta: JSON.stringify({ options: t }) });
              },
            },
          },
        });
      }
      getDocTextcontent() {
        return this.editorWin.editor.editorWrapper.querySelector(".ProseMirror").textContent;
      }
      getDocJsonContent() {
        return this.editorWin.editor.getJsonDoc();
      }
      getDocContent() {
        return this.editorWin.editor.getHTML();
      }
      setDocContent(t) {
        this.editorWin.editor.setContent(t);
      }
      resetDocContent(t) {
        this.editorWin.editor.newArticle(t);
      }
      getHeaderContent() {
        return this.editorWin.editor.pageHeader.getHTML();
      }
      setHeaderContent(t) {
        this.editorWin.editor.pageHeader.setContent(t);
      }
      resetHeaderContent(t) {
        this.editorWin.editor.pageHeader.resetContent(t);
      }
      setEditable(t) {
        this.editorWin.editor.setEditable(t), (this.editable = this.editorWin.editor.editable);
        let e = this.editorWin.document.body.classList;
        e.remove("mode0"), e.remove("mode1"), e.add("mode" + (this.editable ? 1 : 0)), t ? (this.editorWin.editor.config.readonly = !1) : this.setFreeEdit(!1);
      }
      setFreeEdit(t) {
        (this.editorWin.editor.freeEdit = t), (this.freeEdit = this.editorWin.editor.freeEdit), t && this.setEditable(!0);
      }
      setReadonly(t) {
        !t || this.setEditable(!1), (this.editorWin.editor.config.readonly = t);
        let e = this.editorWin.editor.ele.classList;
        t ? e.add("readonly") : e.remove("readonly");
      }
      hideToolbar(t) {
        this.hideTool = t;
      }
      clearData() {
        this.editorWin.editor.clearData();
      }
      destroyData() {
        Object.keys(this.docData).forEach((t) => {
          delete this.docData[t];
        });
      }
      print() {
        this.editorWin.editor.emit("canvasToImg"),
          setTimeout(async () => {
            await this.editorWin.print(), this.editorWin.editor.emit("imgToCanvas");
          }, 10);
      }
      validate() {
        let t = this.editorWin.editor.ele,
          e = t.classList;
        return (
          e.add("show-error"),
          setTimeout(() => {
            e.remove("show-error");
          }, 1100),
          !t.querySelector(".error")
        );
      }
      showHeader(t) {
        (this.isShowHeader = t), this.editorWin.editor.showHeader(t);
      }
      toggleHeader() {
        this.showHeader(!this.isShowHeader);
      }
      symbol() {
        this.cmds.suggestion({
          onOpen: (t, e, i, s) => ({
            component: l["a"],
            componentData: {
              on: {
                value: (t) => {
                  e.$emit("input", t);
                },
              },
            },
          }),
        });
      }
      mounted() {
        let t = {},
          e = this.$refs.editor;
        e.onload = () => {
          let i = e.contentWindow,
            s = new i.medicalEditorCore(this.editorCtx, this.docCtx, this.docData, this.fieldData, this.fieldDict, this.wordDict, { readonly: !1, xPadding: 30, yPadding: 100 }, t);
          s.paperSetting(this.setting),
            s.init(),
            s
              .on("transaction", (t) => {
                this.$emit("transaction", s.getHTML());
              })
              .on("group", (t) => {
                this.$emit("group", t);
              })
              .on("formChange", (t) => {
                this.$emit("formChange", t);
              })
              .on("sign", (t) => {
                this.$emit("sign", t);
              })
              .on("singleSign", (t) => {
                this.$emit("singleSign", t);
              })
              .on("groupitem", (t) => {
                t && this.groupshow && !this.tempgroup.some((e) => e.id == t.id) ? (this.dragstartItem = t) : (this.dragstartItem = null);
              })
              .on("editgroup", (e) => {
                (this.editingGroup = e), (this.groupshow = !0), (this.groupname = e.name), (this.tempgroup.length = 0);
                let i = e.id || "group" + new Date().getTime();
                e.ids.forEach((e) => {
                  let s = t[e];
                  s &&
                    this.tempgroup.push({
                      id: e,
                      el: s.$el,
                      group: (t, e) => {
                        t ? s.$emit("group", JSON.stringify({ ids: e, name: t, id: i })) : s.$emit("group", "");
                      },
                    });
                });
              })
              .on("delgroup", (e) => {
                e.ids.forEach((e) => {
                  let i = t[e];
                  i && i.$emit("group", "");
                }),
                  1 == this.groupshow && this.editingGroup && this.editingGroup.id == e.id && ((this.groupshow = !1), (this.groupname = ""), (this.editingGroup = null), (this.tempgroup.length = 0));
              })
              .on("editcheck", ({ options: t, required: e, done: i }) => {
                this.dialog({
                  title: "编辑选项",
                  component: T,
                  data: {
                    props: { rq: e, ops: t },
                    on: {
                      edit: ({ options: t, required: e }) => {
                        i(t, e);
                      },
                    },
                  },
                });
              })
              .on("editoption", ({ options: t, done: e }) => {
                this.dialog({
                  title: "编辑选项",
                  component: F,
                  data: {
                    props: { ops: t },
                    on: {
                      edit: (t) => {
                        e(t);
                      },
                    },
                  },
                });
              })
              .on("checkUp", (t) => {
                console.log(t), this.$emit("checkUp", t);
              }),
            (this.cmds = s.commands),
            (this.editorWin = i),
            (i.editor = s),
            this.$emit("initComplete", i);
        };
      }
      showgroup() {
        (this.dragstartItem = null), (this.groupshow = !this.groupshow), (this.editingGroup = null), this.groupshow || ((this.groupname = ""), (this.tempgroup.length = 0));
      }
      hoverGroupItem(t) {
        t.classList.add("group-item-hover");
      }
      outGroupItem(t) {
        t.classList.remove("group-item-hover");
      }
      addGroup() {
        let t = this.tempgroup.map((t) => t.id),
          e = "group" + new Date().getTime();
        this.tempgroup.forEach((i) => {
          i.group(this.groupname, t, e);
        }),
          (this.groupshow = !1),
          (this.tempgroup.length = 0);
      }
      beforeMount() {
        this.docCtx.paperSetting ? (this.setting = this.docCtx.paperSetting) : (this.docCtx.paperSetting = this.setting);
      }
      applySetting() {
        this.editorWin && (this.editorWin.editor.paperSetting(this.setting), (this.docCtx.paperSetting = this.setting), this.$emit("paperchange", this.setting));
      }
      paperSetting() {
        this.dialog({ title: "纸张设置", component: Y, data: { props: { setting: this.setting } } });
      }
      delItem(t, e) {
        this.tempgroup.splice(e, 1), t.group(null), t.el.classList.remove("group-item-hover");
      }
      setPageSize(t) {
        this.editorWin.editor.setPageSize(t);
      }
      drop(t) {
        t.preventDefault(), null != this.dragstartItem && this.tempgroup.push(this.dragstartItem);
      }
      ondragover(t) {
        this.tempgroup && t.preventDefault();
      }
      showTip(t) {
        this.editorWin.editor.tip(t);
      }
      getSignPlaceholder() {
        return this.editorWin.editor.getSignPlaceholder();
      }
      getSingleSignPlaceholder() {
        return this.editorWin.editor.getSingleSignPlaceholder();
      }
      getPdf() {
        return new Promise((t, e) => {
          let i = window.top.require;
          if (!i) return void e("静默生成 pdf 仅支持客户端");
          let { remote: s, ipcRenderer: o } = i("electron"),
            n = new s.BrowserWindow({ show: !1, parent: s.getCurrentWindow(), webPreferences: { contextIsolation: !1, nodeIntegration: !0, enableRemoteModule: !0 } });
          n.loadURL(nt + "/pdfPrinter.html");
          let a = n.webContents;
          a.on("did-finish-load", () => {
            a.send("editorData", JSON.stringify({ html: this.getDocContent(), docCtx: this.docCtx, docData: this.docData, fieldData: this.fieldData }));
          }),
            a.on("did-fail-load", (t) => {
              n.close(), e(t);
            }),
            o.once("editorRenderComplete", (i) => {
              setTimeout(() => {
                n.getContentView()
                  .webContents.printToPDF({ printBackground: !0 })
                  .then((e) => {
                    t(e);
                  })
                  .catch((t) => {
                    e(t);
                  })
                  .finally(() => {
                    n.close();
                  });
              }, 300);
            });
        });
      }
      setPaper(t) {
        (this.setting = t), this.editorWin.editor.paperSetting(t);
      }
      getPaper() {
        return this.setting;
      }
      signRect(t, e) {
        return Object(it["signRect"])(t, e);
      }
      IsScale(t = !1) {
        this.editorWin.editor.IsScale(t);
      }
    };
    Object(r["a"])([Object(d["c"])({ required: !0 })], at.prototype, "docCtx", void 0),
      Object(r["a"])([Object(d["c"])({ required: !0 })], at.prototype, "docData", void 0),
      Object(r["a"])([Object(d["c"])({ required: !0 })], at.prototype, "fieldData", void 0),
      Object(r["a"])([Object(d["c"])({ required: !0 })], at.prototype, "fieldDict", void 0),
      Object(r["a"])([Object(d["c"])({ required: !0 })], at.prototype, "wordDict", void 0),
      Object(r["a"])([Object(d["e"])("setting", { deep: !0 })], at.prototype, "applySetting", null),
      (at = Object(r["a"])([Object(d["a"])({})], at));
    var rt = at,
      dt = rt,
      lt = (i("8bf2"), Object(y["a"])(dt, o, n, !1, null, null, null)),
      ct = lt.exports,
      ut = function () {
        var t = this,
          e = t._self._c;
        t._self._setupProxy;
        return e(
          "div",
          {
            class: `app-dialog-wraper ${t.show && !t.closed ? "show" : ""} ${t.option.class}`,
            on: {
              click: function (e) {
                return t.cls(!0);
              },
            },
          },
          [
            e(
              "div",
              {
                staticClass: "dialog-body",
                class: { noframe: t.option.noFrame },
                on: {
                  click: function (t) {
                    t.stopPropagation();
                  },
                  transitionend: function (e) {
                    return t.$doClose();
                  },
                },
              },
              [
                t.option.title
                  ? e("div", { staticClass: "dialog-header" }, [
                      e("div", { staticClass: "dialog-title" }, [t._v(t._s(t.option.title))]),
                      t.option.hideClose
                        ? t._e()
                        : e("div", {
                            staticClass: "dialog-close dialog-icon-close",
                            on: {
                              click: function (e) {
                                return t.cls();
                              },
                            },
                          }),
                    ])
                  : t._e(),
                t._t("default"),
              ],
              2
            ),
          ]
        );
      },
      pt = [];
    let ht,
      gt = class extends d["d"] {
        constructor(...t) {
          super(...t), Object(a["a"])(this, "dref", null), Object(a["a"])(this, "ddata", null), Object(a["a"])(this, "closed", !1), Object(a["a"])(this, "vue", null), Object(a["a"])(this, "show", !1), Object(a["a"])(this, "option", void 0);
        }
        static init(t) {
          ht = t;
        }
        mounted() {
          setTimeout(() => {
            this.show = !0;
          });
        }
        close() {
          this.cls(!1);
        }
        cls(t) {
          this.closed || (t && !this.option.closeOnClick) || ((this.closed = !0), this.option.onClose && this.option.onClose());
        }
        $doClose() {
          this.closed && this.$destroy();
        }
        destroyed() {
          this.$el.parentElement && this.$el.remove();
        }
      };
    Object(r["a"])([Object(d["c"])({ required: !0 })], gt.prototype, "option", void 0), (gt = Object(r["a"])([Object(d["a"])({ name: "mint-dialog" })], gt));
    var mt = gt,
      bt = mt,
      vt = (i("cb05"), Object(y["a"])(bt, ut, pt, !1, null, null, null)),
      ft = vt.exports,
      yt = function () {
        var t = this,
          e = t._self._c;
        t._self._setupProxy;
        return e(
          "div",
          {
            staticClass: "mint-dialog-notify",
            class: { show: t.show && !t.closed },
            on: {
              transitionend: function (e) {
                return t.doClose();
              },
            },
          },
          [e("div", { staticClass: "notify-header" }, [t._v(t._s(t.option.title))]), e("div", { staticClass: "notify-body" }, [t._v(t._s(t.option.msg))])]
        );
      },
      Ot = [];
    let jt = class extends d["d"] {
      constructor(...t) {
        super(...t), Object(a["a"])(this, "option", void 0), Object(a["a"])(this, "closed", !1), Object(a["a"])(this, "show", !1);
      }
      mounted() {
        setTimeout(() => {
          this.show = !0;
        }, 10),
          setTimeout(() => {
            this.closed = !0;
          }, this.option.duration);
      }
      close() {
        this.closed = !0;
      }
      setMsg(t) {
        this.$set(this.option, "title", t);
      }
      setTitle(t) {
        this.$set(this.option, "title", t);
      }
      doClose() {
        this.closed && this.$destroy();
      }
      destroyed() {
        this.$el.parentElement && this.$el.remove();
      }
    };
    Object(r["a"])([Object(d["c"])({ required: !0 })], jt.prototype, "option", void 0), (jt = Object(r["a"])([Object(d["a"])({ name: "mint-dialog-notify" })], jt));
    var Ct = jt,
      _t = Ct,
      xt = (i("8411"), Object(y["a"])(_t, yt, Ot, !1, null, null, null)),
      kt = xt.exports,
      wt = function () {
        var t = this,
          e = t._self._c;
        t._self._setupProxy;
        return e(
          "div",
          {
            class: `mint-dialog-tip ${t.show && !t.closed ? "show" : ""} ${t.option.type}`,
            on: {
              transitionend: function (e) {
                return t.doClose();
              },
            },
          },
          [e("div", { staticClass: "tip-icon", class: "dialog-icon-" + t.option.type }), e("div", { staticClass: "tip-msg" }, [t._v(t._s(t.option.msg))])]
        );
      },
      Et = [];
    let $t = class extends d["d"] {
      constructor(...t) {
        super(...t), Object(a["a"])(this, "option", void 0), Object(a["a"])(this, "closed", !1), Object(a["a"])(this, "show", !1);
      }
      mounted() {
        setTimeout(() => {
          this.show = !0;
        }, 10),
          setTimeout(() => {
            this.closed = !0;
          }, this.option.duration);
      }
      close() {
        this.closed = !0;
      }
      setMsg(t) {
        this.$set(this.option, "title", t);
      }
      setType(t) {
        this.$set(this.option, "type", t);
      }
      doClose() {
        this.closed && this.$destroy();
      }
      destroyed() {
        this.$el.parentElement && this.$el.remove();
      }
    };
    Object(r["a"])([Object(d["c"])({ required: !0 })], $t.prototype, "option", void 0), ($t = Object(r["a"])([Object(d["a"])({ name: "mint-dialog-tip" })], $t));
    var Dt = $t,
      St = Dt,
      Tt = (i("5057"), Object(y["a"])(St, wt, Et, !1, null, null, null)),
      Pt = Tt.exports,
      qt = function () {
        var t = this,
          e = t._self._c;
        t._self._setupProxy;
        return e("div", { class: "mint-dialog-" + t.dialogdata._type }, [
          e("div", { staticClass: "mint-dialog-title" }, [t._v(t._s(t.dialogdata.title))]),
          e("div", { staticClass: "mint-dialog-msg" }, [t._v(t._s(t.dialogdata.msg))]),
          e("div", { staticClass: "mint-dialog-btns", style: { "flex-direction": t.rvbtn ? "row-reverse" : "", "justify-content": t.rvbtn ? "flex-start" : "" } }, [
            e(
              "button",
              {
                staticClass: "button mint-cancel-btn",
                on: {
                  click: function (e) {
                    return t.cb(!1);
                  },
                },
              },
              [t._v("取消")]
            ),
            e(
              "button",
              {
                staticClass: "button",
                class: { "mint-danger-btn": t.dialogdata.isDanger },
                on: {
                  click: function (e) {
                    return t.cb(!0);
                  },
                },
              },
              [t._v("确定")]
            ),
          ]),
        ]);
      },
      Wt = [];
    let At = class extends d["d"] {
      constructor(...t) {
        super(...t), Object(a["a"])(this, "dialogdata", void 0), Object(a["a"])(this, "dialogref", void 0), Object(a["a"])(this, "rvbtn", !1);
      }
      beforeMount() {
        this.dialogdata || (this.dialogdata = {}), (this.rvbtn = this.dialogdata.reverseBtn || !1);
      }
      cb(t) {
        let e = this.dialogdata.callBack;
        if (e)
          try {
            e(t);
          } catch (i) {
            console.error(i);
          }
        this.dialogref.close();
      }
    };
    Object(r["a"])([Object(d["c"])({ required: !0 })], At.prototype, "dialogdata", void 0), Object(r["a"])([Object(d["c"])({ required: !0 })], At.prototype, "dialogref", void 0), (At = Object(r["a"])([Object(d["a"])({ name: "mint-dialog-confirm" })], At));
    var Nt = At,
      Mt = Nt,
      It = (i("47e1"), Object(y["a"])(Mt, qt, Wt, !1, null, null, null)),
      Ht = It.exports,
      Bt = function () {
        var t = this,
          e = t._self._c;
        t._self._setupProxy;
        return e(
          "div",
          {
            staticClass: "mint-dialog-loading",
            class: { show: t.show && !t.closed, large: t.option.icon || t.option.progress >= 0 },
            on: {
              transitionend: function (e) {
                return t.doClose();
              },
            },
          },
          [
            t.option.icon || t.option.progress >= 0
              ? e("div", { staticClass: "mint-loading-icon", class: { "show-progress": t.option.progress >= 0 } }, [t.option.progress >= 0 ? e("div", { staticClass: "loading-progress" }, [e("span", [t._v(t._s(t.option.progress))]), e("span", { staticClass: "percent" }, [t._v("%")])]) : t._e()])
              : t._e(),
            e("div", { staticClass: "mint-loading-text" }, [t.option.msg ? e("span", [t._v(t._s(t.option.msg))]) : t._e(), t.option.tail ? e("div", { staticClass: "tail" }) : t._e()]),
          ]
        );
      },
      Lt = [];
    let zt = class extends d["d"] {
      constructor(...t) {
        super(...t), Object(a["a"])(this, "option", void 0), Object(a["a"])(this, "onShow", void 0), Object(a["a"])(this, "closed", !1), Object(a["a"])(this, "show", !1);
      }
      mounted() {
        setTimeout(() => {
          (this.show = !0), this.onShow();
        }, this.option.delay);
      }
      close() {
        (this.closed = !0),
          setTimeout(() => {
            (this.show && 0 != parseFloat(getComputedStyle(this.$el).opacity)) || this.$destroy();
          });
      }
      setMsg(t) {
        this.option.msg = t || "";
      }
      setProgress(t) {
        this.option.progress = t;
      }
      doClose() {
        this.closed && this.$destroy();
      }
      destroyed() {
        this.$el.parentElement && this.$el.remove();
      }
    };
    Object(r["a"])([Object(d["c"])({ required: !0 })], zt.prototype, "option", void 0), Object(r["a"])([Object(d["c"])({ required: !0 })], zt.prototype, "onShow", void 0), (zt = Object(r["a"])([Object(d["a"])({ name: "mint-dialog-loading" })], zt));
    var Gt = zt,
      Rt = Gt,
      Ft = (i("c210"), Object(y["a"])(Rt, Bt, Lt, !1, null, null, null)),
      Jt = Ft.exports;
    const Xt = document.createElement("div"),
      Ut = document.createElement("div"),
      Vt = document.createElement("div"),
      Kt = document.createElement("div");
    let Qt;
    (Xt.className = "mint-dialog-tip-container"), (Vt.className = "mint-dialog-notify-container"), (Kt.className = "mint-dialog-loading-container empty");
    let Yt = 0;
    class Zt {
      static init(t) {
        Qt = t;
        let e = ft;
        e.init(t), document.body.appendChild(Xt), document.body.appendChild(Vt), document.body.appendChild(Ut), document.body.appendChild(Kt);
      }
      static tip(t) {
        (t.msg = t.msg || ""), (t.duration = t.duration >= 0 ? t.duration : 3e3);
        let e = new Pt({ propsData: { option: t } });
        return e.$mount(), Xt.appendChild(e.$el), e;
      }
      static loading(t) {
        (t.msg = t.msg || ""), void 0 === t.icon && (t.icon = !0), (t.progress = t.progress || -1), (t.delay = t.delay >= 0 ? t.delay : 0);
        let e = new Jt({
          propsData: {
            option: t,
            onShow() {
              e.closed || (Yt++, Kt.classList.remove("empty"));
            },
          },
          destroyed() {
            Yt--, Yt <= 0 && (Kt.classList.add("empty"), (Yt = 0));
          },
          mounted() {},
        });
        return e.$mount(), Kt.appendChild(e.$el), e;
      }
      static notify(t) {
        (t.msg = t.msg || ""), (t.duration = t.duration >= 0 ? t.duration : 4e3);
        let e = new kt({ propsData: { option: t } });
        return e.$mount(), Vt.appendChild(e.$el), e;
      }
      static confirm(t) {
        return Zt._confirm(t, "confirm");
      }
      static alert(t) {
        return Zt._confirm(t, "alert");
      }
      static _confirm(t, e) {
        (t.msg = t.msg || ""), (t.title = t.title || ""), (t.isDanger = t.isDanger || !1), (t.reverseBtn = t.reverseBtn || !1);
        let i = Object.assign({}, t);
        i._type = e;
        let s = Zt.dialog({ component: Ht, data: i, closeOnClick: t.closeOnClick });
        return {
          setMsg(t) {
            i.msg = t;
          },
          setTitle(t) {
            i.title = t;
          },
          close() {
            s.close();
          },
        };
      }
      static dialog(t) {
        let e = new ft({ propsData: { option: { ...t, data: null, component: null } } }),
          i = e.$createElement(t.component, t.data);
        return (e.$slots.default = [i]), e.$mount(), Ut.appendChild(e.$el), e;
      }
    }
    var te = function (t) {
        Zt.init(t),
          (t.prototype.dialog = function (t) {
            return Zt.dialog(t);
          }),
          (t.prototype.confirm = function (t) {
            return Zt.confirm(t);
          }),
          (t.prototype.alert = function (t) {
            return Zt.alert(t);
          }),
          (t.prototype.loading = function (t) {
            return Zt.loading(Object.assign({ tail: !0 }, t));
          }),
          (t.prototype.notify = function (t) {
            return Zt.notify(t);
          }),
          (t.prototype.tip = function (t) {
            return Zt.tip(t);
          });
      },
      ee = i("596a"),
      ie = function () {
        var t = this,
          e = t._self._c;
        t._self._setupProxy;
        return e("iframe", { ref: "__printerEditor", staticClass: "__printerEditor-print-helper", class: t.setting.size, attrs: { src: t.jsBase + "/editorCore.html", frameborder: "0" } });
      },
      se = [];
    let oe = document.getElementsByTagName("script"),
      ne = oe[oe.length - 1],
      ae = ne.src.split("/");
    ae.pop(), (ae = ae.join("/"));
    let re = { size: "A4", marginX: 10, marginTop: 20, marginBottom: 10, headerContent: "" },
      de = null,
      le = null,
      ce = null,
      ue = null,
      pe = class extends d["d"] {
        constructor(...t) {
          super(...t), Object(a["a"])(this, "jsBase", ae), Object(a["a"])(this, "docCtx", { paperSetting: re }), Object(a["a"])(this, "docData", {}), Object(a["a"])(this, "fieldData", {}), Object(a["a"])(this, "setting", re);
        }
        applySetting() {
          ue && (ce.paperSetting(this.setting), (this.docCtx.paperSetting = this.setting));
        }
        print(t) {
          (le = t), (de = this.loading({ icon: !1, tail: !0, msg: t.length + "连打，第1次" })), (de.total = t.length), this.doPrint();
        }
        doPrint() {
          if (!le || !le.length) return ce && ce.destroy(), ue && (ue = null), de && (de.close(), (de = null)), this.$destroy(), void this.$el.parentElement.removeChild(this.$el);
          de.setMsg(`${de.total}连打，剩余${le.length}次`);
          let t = le.shift();
          Object.assign(this.docCtx, t.docCtx),
            Object.keys(this.docData).forEach((t) => {
              delete this.docData[t];
            }),
            Object.keys(this.fieldData).forEach((t) => {
              delete this.fieldData[t];
            }),
            ce.newArticle(t.html),
            Object.assign(this.docData, t.docData),
            Object.assign(this.fieldData, t.fieldData),
            setTimeout(() => {
              ue.print();
            }, 200);
        }
        mounted() {
          ce && ce.destroy(), ue && (ue = null), de && (de.close(), (de = null)), (le = null);
          let t = this.$refs.__printerEditor;
          t.onload = () => {
            (ue = t.contentWindow), (ce = new ue.medicalEditorCore({ headerEditing: !1 }, this.docCtx, this.docData, this.fieldData, [], [], { readonly: !1, xPadding: 30, yPadding: 100 })), ce.paperSetting(this.setting), ce.init(), ce.setEditable(!1), (ce.config.readonly = !0);
            let e = ce.ele.classList;
            e.add("readonly"),
              ue.addEventListener("beforeprint", (t) => {}),
              ue.addEventListener("afterprint", (t) => {
                this.doPrint();
              }),
              this.$emit("initComplete", ue);
          };
        }
        destroyed() {
          ce && ce.destroy(), ue && (ue = null), (le = null);
        }
      };
    Object(r["a"])([Object(d["e"])("setting", { deep: !0 })], pe.prototype, "applySetting", null), (pe = Object(r["a"])([Object(d["a"])({ components: {} })], pe));
    var he = pe,
      ge = he,
      me = (i("0a87"), Object(y["a"])(ge, ie, se, !1, null, "174f5f29", null)),
      be = me.exports;
    (s["default"].config.productionTip = !1), s["default"].use(te), (s["default"].prototype.axios = ee["a"]), s["default"].component("plaintext", j);
    let ve = window;
    (ve.createEditor = function (t, e, i, s, o, n) {
      return new ct({ propsData: { docCtx: e, docData: i, fieldData: s, fieldDict: o, wordDict: n } }).$mount(t);
    }),
      (ve.superPrinter = () => {
        let t = new be();
        return t.$mount(), document.body.appendChild(t.$el), t;
      });
  },
  5057: function (t, e, i) {
    "use strict";
    i("3171");
  },
  "5bb2": function (t, e, i) {
    "use strict";
    i("1cbb");
  },
  "6e96": function (t, e, i) {},
  "7f21": function (t, e, i) {},
  8242: function (t, e, i) {},
  8411: function (t, e, i) {
    "use strict";
    i("d071");
  },
  "8bf2": function (t, e, i) {
    "use strict";
    i("1d4f");
  },
  9153: function (t, e, i) {},
  "9d1f": function (t, e, i) {},
  a5b7: function (t, e, i) {
    "use strict";
    i("6e96");
  },
  c210: function (t, e, i) {
    "use strict";
    i("8242");
  },
  ca68: function (t, e, i) {},
  cb05: function (t, e, i) {
    "use strict";
    i("9153");
  },
  d071: function (t, e, i) {},
  ffbd: function (t, e, i) {
    "use strict";
    i("01bb");
  },
});
