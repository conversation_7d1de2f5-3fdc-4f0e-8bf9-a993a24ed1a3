<template>
  <div class="customerBasicFileSettting content_body" :loading="loading">
    <!-- 搜索 -->
    <div class="nav_header">
      <el-row>
        <el-col :span="20">
          <el-form :inline="true" size="small" @keyup.enter.native="handleSearch" @submit.native.prevent>
            <el-form-item label="档案项名称">
              <el-input v-model="searchName" placeholder="输入档案项名称搜索" clearable @clear="handleSearch"></el-input>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" size="small" @click="handleSearch" v-prevent-click>搜索</el-button>
            </el-form-item>
          </el-form>
        </el-col>
        <el-col :span="4" class="text_right">
          <el-button type="primary" size="small" @click="addBasicFileClick" v-prevent-click>新增</el-button>
        </el-col>
      </el-row>
    </div>
    <!-- 表格 -->
    <div>
      <el-table :data="tableData" size="small" tooltip-effect="light">
        <el-table-column prop="Name" label="档案项名称"></el-table-column>
        <el-table-column label="类型" prop="Type">
          <template slot-scope="scope">
            {{ formatTypeName(scope.row.Type) }}
          </template>
        </el-table-column>

        <el-table-column label="移动" min-width="180px">
          <template slot-scope="scope">
            <el-button :disabled="scope.$index == 0" size="small" type="primary" circle icon="el-icon-upload2" @click="upOneClick(scope.row, scope.$index)"></el-button>
            <el-button :disabled="scope.$index == 0" size="small" type="primary" circle icon="el-icon-top" @click="upClick(scope.row, scope.$index)"></el-button>
            <el-button :disabled="scope.$index == tableData.length - 1" size="small" type="primary" circle icon="el-icon-bottom" @click="downClick(scope.row, scope.$index)"></el-button>
            <el-button :disabled="scope.$index == tableData.length - 1" size="small" type="primary" circle icon="el-icon-download" @click="downOneClick(scope.row, scope.$index)"></el-button>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="145px">
          <template slot-scope="scope">
            <el-button type="primary" size="small" @click="showEditDialog(scope.row)" v-prevent-click>编辑</el-button>
            <el-button v-if="isDelete" type="danger" size="small" @click="removeItem(scope.row)" v-prevent-click>删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!--编辑、新增弹框-->
    <el-dialog :title="isAdd?'新增档案项':'编辑档案项'" :visible.sync="dialogVisible" width="30%">
      <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="120px" class="demo-ruleForm" size="small">
        <el-form-item label="档案项名称" prop="Name">
          <el-input v-model="ruleForm.Name" maxlength="8" placeholder="不超过8个字"></el-input>
        </el-form-item>
        <el-form-item label="字段类型" prop="Type">
          <el-select v-model="ruleForm.Type" placeholder="请选择">
            <el-option v-for="item in basicType" :key="item.value" :label="item.title" :value="item.value"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item v-if="ruleForm.Type == 30" label="字段类型" prop="RadioSelections">
          <draggable v-model="ruleForm.RadioSelections" chosenClass="chosen" group="people1" animation="1">
            <div v-for="(item, index) in ruleForm.RadioSelections" :key="index" class="marbm_10">
              <el-input @change="radioSelectionsChange(item)" v-model="item.key"> </el-input>
              <i class="el-icon-s-operation marlt_10 marrt_10"></i>
              <el-button @click="removeRadioSelectionsClick(index)" size="small" type="text">删除</el-button>
            </div>
          </draggable>
          <div @click="addRadioSelectionsClick" class="color_main pad_10">+ 添加选项</div>
        </el-form-item>

        <el-form-item v-if="ruleForm.Type == 40" label="字段类型" prop="MultipleSelections">
          <draggable v-model="ruleForm.MultipleSelections" chosenClass="chosen" group="people1" animation="1">
            <div v-for="(item, index) in ruleForm.MultipleSelections" :key="index" class="marbm_10">
              <el-input @change="radioSelectionsChange(item)" v-model="item.key"> </el-input>
              <i class="el-icon-s-operation marlt_10 marrt_10"></i>
              <el-button @click="removeMultipleSelectionsClick(index)" size="small" type="text">删除</el-button>
            </div>
          </draggable>
          <div @click="addMultipleSelectionsClick" class="color_main pad_10">+ 添加选项</div>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false" size="small" v-prevent-click>取 消</el-button>
        <el-button type="primary" size="small" :loading="modalLoading" @click="submitForm" v-prevent-click>保 存</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import permission from "@/components/js/permission.js";
import API from "@/api/CRM/Customer/customerBasicFileSettting.js";
import draggable from "vuedraggable";

export default {
  name: "CustomerBasicFileSettting",
  props: {},
  beforeRouteEnter(to, from, next) {
    next((vm) => {
      vm.isDelete = permission.routerPermission(
        to.meta.Permission,
        "CRM-Customer-CustomerBasicFileSettting-Delete"
      );
    });
  },
  /**  引入的组件  */
  components: {
    draggable,
  },
  /**  Vue 实例的数据对象**/
  data() {
    var validateSelections = (rule, value, callback) => {
      let result = [];
      let hash = {};
      value
        .filter((val) => val.key != "")
        .forEach((item) => {
          let key = item.key;
          if (!hash[key]) {
            hash[key] = true;
          } else {
            result.push(item);
          }
        });

      let isEmpty = value.some((val) => val.key == "");
      if (result.length > 0) {
        callback(new Error("选项内容不可以相同！"));
      }
      if (isEmpty) {
        callback(new Error("请填写选项内容！"));
      } else {
        callback();
      }
    };
    return {
      loading: false,
      isDelete: false,
      dialogVisible: false,
      modalLoading: false,
      isAdd: false,
      searchName: "",
      tableData: [],
      basicType: [
        { title: "文本", value: 10 },
        { title: "日期", value: 20 },
        { title: "单选项", value: 30 },
        { title: "多选项", value: 40 },
      ],
      ruleForm: {
        Name: "",
        Type: 10,
        RadioSelections: [
          { key: "", value: "" },
          { key: "", value: "" },
        ],
        MultipleSelections: [
          { key: "", value: "" },
          { key: "", value: "" },
        ],
      },
      rules: {
        Name: [
          {
            required: true,
            message: "请输入档案项名称",
            trigger: ["change", "blur"],
          },
        ],
        Type: [
          {
            required: true,
            message: "请输选择档案项类型+ ",
            trigger: ["change", "blur"],
          },
        ],
        RadioSelections: [
          {
            required: true,
            validator: validateSelections,
            trigger: ["change", "blur"],
          },
        ],
        MultipleSelections: [
          {
            required: true,
            validator: validateSelections,
            trigger: ["change", "blur"],
          },
        ],
      },
    };
  },
  /**计算属性  */
  computed: {},
  /**  方法集合  */
  methods: {
    /** 搜索   */
    handleSearch() {
      let that = this;
      that.customerBasicFile_all();
    },
    /**  新增  */
    addBasicFileClick() {
      let that = this;
      that.isAdd = true;
      that.ruleForm = {
        Name: "",
        Type: 10,
        RadioSelections: [
          { key: "", value: "" },
          { key: "", value: "" },
        ],
        MultipleSelections: [
          { key: "", value: "" },
          { key: "", value: "" },
        ],
      };
      that.dialogVisible = true;
    },
    /**  类型 格式化  */
    formatTypeName(type) {
      switch (type) {
        case 10:
          return "文本";
        case 20:
          return "日期";
        case 30:
          return "单选项";
        case 40:
          return "多选项";
        default:
          "";
      }
    },

    // 移动至顶部
    upOneClick(row) {
      var that = this;
      that.customerBasicFile_move(row.ID, "");
    },
    // 向上移动
    upClick(row, index) {
      var that = this;
      var beforeId = "";
      if (index > 1) {
        beforeId = that.tableData[index - 2].ID;
      }
      that.customerBasicFile_move(row.ID, beforeId);
    },
    // 向下移动
    downClick(row, index) {
      var that = this;
      var beforeId = "";
      var customerFileCategory = [];
      customerFileCategory = that.tableData;
      if (index + 1 != customerFileCategory.length) {
        beforeId = customerFileCategory[index + 1].ID;
      }
      that.customerBasicFile_move(row.ID, beforeId);
    },
    // 移动至底部
    downOneClick(row, index) {
      var that = this;
      var beforeId = "";
      var tableLength = 0;
      tableLength = that.tableData.length;
      if (index < tableLength - 1) {
        beforeId = that.tableData[tableLength - 1].ID;
      }
      that.customerBasicFile_move(row.ID, beforeId);
    },
    /**  单选修改选项内容  */
    radioSelectionsChange(item) {
      item.value = item.key;
    },
    /**   添加单选项 */
    addRadioSelectionsClick() {
      let that = this;
      that.ruleForm.RadioSelections.push({ key: "", value: "" });
    },
    /**    */
    removeRadioSelectionsClick(index) {
      let that = this;
      that.ruleForm.RadioSelections.splice(index, 1);
    },
    /**   多选项添加 */
    addMultipleSelectionsClick() {
      let that = this;
      that.ruleForm.MultipleSelections.push({ key: "", value: "" });
    },
    /**    */
    removeMultipleSelectionsClick(index) {
      let that = this;
      that.ruleForm.MultipleSelections.splice(index, 1);
    },

    /**  提交  */
    submitForm() {
      let that = this;
      that.$refs.ruleForm.validate((valid) => {
        if (valid) {
          if (that.isAdd) {
            that.customerBasicFile_create();
          } else {
            that.customerBasicFile_update();
          }
        } 
      });
    },

    /**  删除  */
    removeItem(row) {
      let that = this;
      that
        .$confirm("是否确定档案项信息？", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
        .then(() => {
          that.customerBasicFile_delete(row.ID);
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消挂单",
          });
        });
    },
    /**  编辑  */
    showEditDialog(row) {
      let that = this;
      that.isAdd = false;
      let ComponentsProperty = "";
      if (row.Type == 30 || row.Type == 40) {
        ComponentsProperty = JSON.parse(row.ComponentsProperty);
      }

      let temp = [
        { key: "", value: "" },
        { key: "", value: "" },
      ];

      that.ruleForm = {
        Name: row.Name,
        Type: row.Type,
        ID: row.ID,
        RadioSelections: row.Type == 30 ? ComponentsProperty : temp,
        MultipleSelections: row.Type == 40 ? ComponentsProperty : temp,
      };
      that.dialogVisible = true;
    },
    /**  ***************************  */
    /** 列表    */
    async customerBasicFile_all() {
      let that = this;
      let params = {
        Name: that.searchName,
        Type: "",
      };
      that.loading = true;
      let res = await API.customerBasicFile_all(params);
      if (res.StateCode == 200) {
        that.tableData = res.Data;
      } else {
        that.$message.error(res.Message);
      }
      that.loading = false;
    },
    /**  创建  */
    async customerBasicFile_create() {
      let that = this;
      let ComponentsProperty = "";
      if (that.ruleForm.Type == "30") {
        ComponentsProperty = JSON.stringify(that.ruleForm.RadioSelections);
      }

      if (that.ruleForm.Type == "40") {
        ComponentsProperty = JSON.stringify(that.ruleForm.MultipleSelections);
      }
      let params = {
        Name: that.ruleForm.Name,
        Type: that.ruleForm.Type, //字段类型（10：文本、20：日期、30：单选项、40：多选项）
        ComponentsProperty: ComponentsProperty, //json数据
      };
      that.modalLoading = true;
      let res = await API.customerBasicFile_create(params);
      if (res.StateCode == 200) {
        that.$message.success("添加成功");
        that.customerBasicFile_all();
        that.dialogVisible = false;
      } else {
        that.$message.error(res.Message);
      }
      that.modalLoading = false;
    },
    /** 更新   */
    async customerBasicFile_update() {
      let that = this;

      let ComponentsProperty = "";
      if (that.ruleForm.Type == "30") {
        ComponentsProperty = JSON.stringify(that.ruleForm.RadioSelections);
      }

      if (that.ruleForm.Type == "40") {
        ComponentsProperty = JSON.stringify(that.ruleForm.MultipleSelections);
      }
      let params = {
        ID: that.ruleForm.ID,
        Name: that.ruleForm.Name,
        Type: that.ruleForm.Type, //字段类型（10：文本、20：日期、30：单选项、40：多选项）
        ComponentsProperty: ComponentsProperty, //json数据
      };

      that.modalLoading = true;
      let res = await API.customerBasicFile_update(params);
      if (res.StateCode == 200) {
        that.$message.success("操作成功");
        that.customerBasicFile_all();
        that.dialogVisible = false;
      } else {
        that.$message.error(res.Message);
      }
      that.modalLoading = false;
    },
    /**  移动  */
    async customerBasicFile_move(MoveID, BeforeID) {
      let that = this;
      let params = {
        MoveID: MoveID,
        BeforeID: BeforeID,
      };
      let res = await API.customerBasicFile_move(params);
      if (res.StateCode == 200) {
        that.$message.success("操作成功");
        that.customerBasicFile_all();
      } else {
        that.$message.error(res.Message);
      }
    },
    /** 删除  */
    async customerBasicFile_delete(ID) {
      let that = this;
      let params = { ID: ID };
      let res = await API.customerBasicFile_delete(params);
      if (res.StateCode == 200) {
        that.$message.success("操作成功");
        that.customerBasicFile_all();
      } else {
        that.$message.error(res.Message);
      }
    },
  },
  /** 监听数据变化   */
  watch: {},
  /** 创建实例之前执行函数   */
  beforeCreate() {},
  /**  实例创建完成之后  */
  created() {
    let that = this;
    that.customerBasicFile_all();
  },
  /**  在挂载开始之前被调用  */
  beforeMount() {},
  /**  实例被挂载后调用  */
  mounted() {
    let that = this;
    that.isDelete = permission.permission(
      that.$route.meta.Permission,
      "CRM-Customer-CustomerBasicFileSettting-Delete"
    );
  },
  /**  数据更新 之前 调用  */
  beforeUpdate() {},
  /** 数据更新 完成 调用   */
  updated() {},
  /**  实例销毁之前调用  */
  beforeDestroy() {},
  /**  实例销毁后调用  */
  destroyed() {},
};
</script>

<style lang="scss">
.customerBasicFileSettting {
}
</style>
