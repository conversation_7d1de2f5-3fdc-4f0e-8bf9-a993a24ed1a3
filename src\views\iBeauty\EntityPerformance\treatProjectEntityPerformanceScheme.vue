<template>
  <div class="treatProjectEntityPerformanceScheme content_body">
    <!-- 搜索 -->
    <div class="nav_header">
      <el-row>
        <el-col :span="20">
          <el-form :inline="true" size="small" @submit.native.prevent>
            <el-form-item label="组织单位">
              <el-input
                @clear="handleSearch"
                v-model="Name"
                placeholder="输入组织单位名称搜索"
                clearable
                @keyup.enter.native="handleSearch"
              ></el-input>
            </el-form-item>
            <el-form-item>
              <el-button
                type="primary"
                size="small"
                @click="handleSearch"
                v-prevent-click
                >搜索</el-button
              >
            </el-form-item>
          </el-form>
        </el-col>
        <el-col :span="4" class="text_right">
          <el-button
            type="primary"
            size="small"
            @click="showAddDialog"
            v-prevent-click
            >新增</el-button
          >
        </el-col>
      </el-row>
    </div>
    <!-- 表格 -->
    <div>
      <el-table size="small" :data="treatProjectEntityTableData">
        <el-table-column  prop="EntityName"  label="组织单位"></el-table-column>
        <el-table-column label="操作" width="145px">
          <template slot-scope="scope">
            <el-button
              type="primary"
              size="small"
              @click="showEditDialog(scope.row)"
              v-prevent-click
              >编辑</el-button
            >
            <el-button
              type="danger"
              size="small"
              @click="removeEntityClick(scope.row)"
              v-prevent-click
              v-if="isDelete"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <div class="page pad_10 text_right">
        <div class="text_right" v-if="paginations.total > 0">
          <el-pagination
            background
            @current-change="handleCurrentChange"
            :current-page.sync="paginations.page"
            :page-size="paginations.page_size"
            :layout="paginations.layout"
            :total="paginations.total"
          ></el-pagination>
        </div>
      </div>
   </div>
   <!-- 新增弹窗 -->
   <el-dialog
      title="新增项目消耗门店业绩方案"
      :visible.sync="dialogVisible"
      width="30%"
      custom-class="custom-dialog-add"
    >
      <div>
        <el-form
          :model="ruleForm"
          :rules="rules"
          ref="ruleForm"
          label-width="120px"
          size="small"
        >
          <el-form-item label="组织单位" prop="EntityID">
            <span slot="label">
              适用组织
              <el-popover placement="top-start" width="200" trigger="hover">
                <p>适用于同级所有节点，则只需选择父节点。</p>
                <p>比如：适用于所有节点，只需选择“顶级/第一个”节点。</p>
                <el-button
                  type="text"
                  style="color: #dcdfe6"
                  icon="el-icon-info"
                  slot="reference"
                ></el-button>
              </el-popover>
            </span>
            <treeselect
              v-model="ruleForm.EntityID"
              :options="entityList"
              :normalizer="normalizer"
              clearValueText
              noResultsText="无匹配数据"
              placeholder="选择所属部门"
            />
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="dialogVisible = false" v-prevent-click
          >取 消</el-button
        >
        <el-button
          type="primary"
          size="small"
          :loading="modalLoading"
          v-prevent-click
          @click="submitProjectEntityPerformanceClick"
          >保存</el-button
        >
      </div>
   </el-dialog>
   <!-- 编辑弹窗 -->
   <el-dialog :visible.sync="dialogEdit" custom-class="custom-dialog-edit" width="60%">
      <div slot="title">{{ entityName }} - 项目分类消耗门店业绩方案</div>
      <el-table
        size="small"
        :data="treatProjectEntityCategoryTableData"
        row-key="id"
        :tree-props="{ children: 'Child', hasChildren: 'hasChild' }"
        :row-class-name="tableRowClassName"
        max-height="500px"
      >
       <el-table-column prop="CategoryName" label="分类名称" min-width="150px" fixed></el-table-column>

			<el-table-column  label="现金比例" min-width="105px">
						<template slot-scope="scope">
							<el-input type="number" v-model="scope.row.PayRate" size="mini" v-input-fixed="2" @input="royaltyRateChange(0, scope.row)" class="input_type">
								<template slot="append">%</template>
							</el-input>
						</template>
			</el-table-column>

			<el-table-column  label="卡抵扣比例" min-width="105px">
						<template slot-scope="scope">
							<el-input type="number" v-model="scope.row.CardRate" size="mini" v-input-fixed="2" @input="royaltyRateChange(1, scope.row)" class="input_type">
								<template slot="append">%</template>
							</el-input>
						</template>
			</el-table-column>

			<el-table-column  label="赠送卡抵扣比例" min-width="105px">
						<template slot-scope="scope">
							<el-input type="number" v-model="scope.row.CardLargessRate" size="mini" v-input-fixed="2" @input="royaltyRateChange(2, scope.row)" class="input_type">
								<template slot="append">%</template>
							</el-input>
						</template>
			</el-table-column>

			<el-table-column  label="赠送比例" min-width="105px">
						<template slot-scope="scope">
							<el-input type="number" v-model="scope.row.LargessRate" size="mini" v-input-fixed="2" class="input_type" @input="royaltyRateChange(3, scope.row)">
								<template slot="append">%</template>
							</el-input>
						</template>
			</el-table-column>

      <el-table-column label="操作" width="115px">
          <template slot-scope="scope">
            <el-button
              type="primary"
              size="mini"
              @click="projectEntityPerformance(scope.row)"
              v-if="scope.row.isChild == true"
              >项目业绩</el-button
            >
          </template>
      </el-table-column>
      </el-table>
      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="dialogEdit = false" v-prevent-click
          >取 消</el-button
        >
        <el-button
          type="primary"
          size="small"
          :loading="modalLoading"
          v-prevent-click
          @click="submitProjectEntityCategoryClick"
          >保存</el-button
        >
      </div>
   </el-dialog>
    <!--项目业绩弹窗-->
   <el-dialog :visible.sync="dialogProjectEntity" width="40%" custom-class="custom-dialog-edit_Project">
      <div slot="title">
        {{ entityName }} - {{ categoryName }} - 项目消耗门店业绩方案
      </div>
      <div>
        <el-form :inline="true" size="small" @submit.native.prevent>
          <el-form-item>
            <el-input
              v-model="SearchKey"
              placeholder="输入项目名称搜索"
              prefix-icon="el-icon-search"
              clearable
            ></el-input>
          </el-form-item>
        </el-form>
        <el-table
          :data="
            projectEntityRoyaltyList.filter(
              (data) =>
                !SearchKey ||
                data.GoodName.toLowerCase().includes(SearchKey.toLowerCase())
            )
          "
          row-key="GoodID"
          size="small"
          max-height="500px"
        >
         <el-table-column prop="GoodName" label="项目名称" fixed min-width="150px"></el-table-column>

					<el-table-column  label="现金比例" min-width="105px">
						<template slot-scope="scope">
							<el-input type="number" v-model="scope.row.PayRate" v-input-fixed="2" class="input_type" @input="royaltyRateChange(0, scope.row)" size="mini">
								<template slot="append">%</template>
							</el-input>
						</template>
					</el-table-column>

					<el-table-column  label="卡抵扣比例" min-width="105px">
						<template slot-scope="scope">
							<el-input type="number" v-model="scope.row.CardRate" size="mini" v-input-fixed="2" @input="royaltyRateChange(1, scope.row)" class="input_type">
								<template slot="append">%</template>
							</el-input>
						</template>
					</el-table-column>

					<el-table-column  label="赠送卡抵扣比例" min-width="105px">
						<template slot-scope="scope">
							<el-input type="number" v-model="scope.row.CardLargessRate" size="mini" v-input-fixed="2" @input="royaltyRateChange(2, scope.row)" class="input_type">
								<template slot="append">%</template>
							</el-input>
						</template>
					</el-table-column>

					<el-table-column  label="赠送比例" min-width="105px">
						<template slot-scope="scope">
							<el-input type="number" v-model="scope.row.LargessRate" size="mini" v-input-fixed="2" class="input_type" @input="royaltyRateChange(3, scope.row)">
								<template slot="append">%</template>
							</el-input>
						</template>
					</el-table-column>
        </el-table>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="dialogProjectEntity = false" v-prevent-click
          >取 消</el-button
        >
        <el-button
          type="primary"
          size="small"
          :loading="modalLoading"
          v-prevent-click
          @click="updateProjectEntityPerformance"
          >保存</el-button
        >
      </div>
   </el-dialog>

  </div>
</template>

<script>

import API from "@/api/iBeauty/EntityPerformance/treatProjectEntityPerformanceScheme"
import APIEntity from "@/api/KHS/Entity/entity";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
var Enumerable = require("linq");

export default {
 name: 'treatProjectEntityPerformanceScheme',

beforeRouteEnter(to, from, next) {
 next((vm) => {
     vm.isDelete = vm.$permission.permission(
      to.meta.Permission,
      "KHS-EntityPerformance-TreatProjectEntityPerformanceScheme-Delete"
    );
  });
},
  props:{},
  /**  引入的组件  */
  components: {Treeselect},
  /**  Vue 实例的数据对象**/
  data() {
    return {
     isDelete: false,
     modalLoading: false,
     loading: false,
     dialogVisible: false,
     dialogEdit: false,
     dialogProjectEntity: false,
     Name: '', // 搜索条件
     EntityID: "", //  当前的门店ID
     entityName: "",
     categoryName: "",
     projectCategoryID: "",
     SearchKey: "",  // 项目搜索
     treatProjectEntityTableData: [], //表格数据
     treatProjectEntityCategoryTableData: [], //编辑弹窗表格数据
     projectEntityRoyaltyList: [], //项目弹窗表格数据
     entityList: [], //门店数据
     ruleForm: {
        EntityID: null,
        },
        rules: {
        EntityID: [
          { required: true, message: "请选择组织", trigger: "change" },
         ],
        },
     //需要给分页组件传的信息
        paginations: {
        page: 1, // 当前位于哪页
        total: 0, // 总数
        page_size: 10, // 1页显示多少条
        layout: "total, prev, pager, next,jumper", // 翻页属性
        },
    }
  },
   /**计算属性  */
  computed: {
  },
  /**  方法集合  */
  methods: {
    /* 数据显示 */
    handleSearch(){
      let that = this;
      that.paginations.page = 1;
      that.getTreatProjectEntityPerformanceScheme()
    },
    /* 上下分页 */
    handleCurrentChange(page) {
      let that = this;
      that.paginations.page = page;
      that.getTreatProjectEntityPerformanceScheme()
    },
    /* 新增 */
    showAddDialog(){
      let that = this
      that.ruleForm = {
        entity: null,
      };
      that.dialogVisible = true;
    },
    /* 编辑 */
    showEditDialog(row){
      let that = this
      that.entityName = row.EntityName;
      that.EntityID = row.EntityID;
      that.getTreatProjectCategoryEntityPerformance()
    },
    /* 新增保存 */
    submitProjectEntityPerformanceClick(){
      let that = this;
				that.$refs.ruleForm.validate((valid) => {
					if (valid) {
						that.modalLoading = true;
						let para = Object.assign({}, that.ruleForm);
						API.createTreatProjectEntityPerformanceScheme(para)
							.then(function(res) {
								if (res.StateCode === 200) {
									that.$message.success({
										message: "新增成功",
										duration: 2000,
									});
									that.getTreatProjectEntityPerformanceScheme();
									that.$refs["ruleForm"].resetFields();
									that.dialogVisible = false;
								} else {
									that.$message.error({
										message: res.Message,
										duration: 2000,
									});
								}
							})
							.finally(function() {
								that.modalLoading = false;
							});
					}
				});
    },
    /* 获取门店项目消耗业绩方案列表 */
    getTreatProjectEntityPerformanceScheme: function() {
				let that = this;
				that.loading = true;
				var params = {
					Name: that.name,
					PageNum: that.paginations.page,
				};
				API.getTreatProjectEntityPerformanceScheme(params)
					.then((res) => {
						if (res.StateCode == 200) {
							that.treatProjectEntityTableData = res.List;
							that.paginations.total = res.Total;
							that.paginations.page_size = res.PageSize;
						} else {
							that.$message.error({
								message: res.Message,
								duration: 2000,
							});
						}
					})
					.finally(function() {
						that.loading = false;
					});
		},
    /* 获取分类项目消耗业绩 */
    getTreatProjectCategoryEntityPerformance: function() {
				var that = this;
				that.loading = true;
				var params = {
					EntityID: that.EntityID,
				};
				API.getTreatProjectCategoryEntityPerformance(params)
					.then((res) => {
						if (res.StateCode == 200) {
              that.dialogEdit = true;
             res.Data.Category.forEach((item) => {
								item.Child = Enumerable.from(item.Child)
									.select((val) => ({
										id: val.CategoryName + val.CategoryID + "project",
										CategoryID: val.CategoryID,
										CategoryName: val.CategoryName,
										ParentID: val.ParentID,
										LargessRate: val.LargessRate,
                    PayRate: val.PayRate, //现金比例
                    CardRate: val.CardRate, //卡本金比例
                    CardLargessRate: val.CardLargessRate, //赠卡比例
										isChild: true,
									}))
									.toArray();
							});
							var data = {
                id: res.Data.EntityID + "project" + res.Data.EntityName,
								CategoryID: res.Data.EntityID,
								CategoryName: "所有项目",
								LargessRate: res.Data.LargessRate,
								PayRate: res.Data.PayRate, //现金比例
								CardRate: res.Data.CardRate, //卡本金比例
								CardLargessRate: res.Data.CardLargessRate, //赠卡比例
								isEntity: true,
								isChild: false,
							};

							var Category = Enumerable.from(res.Data.Category)
								.select((val) => ({
                  id: val.CategoryID + "project" + val.CategoryName,
									CategoryID: val.CategoryID,
									CategoryName: val.CategoryName,
									ParentID: val.ParentID,
									LargessRate: val.LargessRate,
									PayRate: val.PayRate, //现金比例
									CardRate: val.CardRate, //卡本金比例
									CardLargessRate: val.CardLargessRate, //赠卡比例
									Child: val.Child,
									isEntity: false,
									isChild: false,
								}))
								.toArray();
							that.treatProjectEntityCategoryTableData = Object.assign([], Category);
							that.treatProjectEntityCategoryTableData.unshift(data);
						} else {
							that.$message.error({
								message: res.Message,
								duration: 2000,
							});
						}
					})
					.finally(function() {
						that.loading = false;
					});
		},
    /* 保存分类项目消耗业绩 */
    submitProjectEntityCategoryClick(){
     var that = this;
				var params = {
					EntityID: that.treatProjectEntityCategoryTableData[0].CategoryID,
					LargessRate: that.treatProjectEntityCategoryTableData[0].LargessRate,
					PayRate: that.treatProjectEntityCategoryTableData[0].PayRate, //现金比例
					CardRate: that.treatProjectEntityCategoryTableData[0].CardRate, //卡本金比例
					CardLargessRate: that.treatProjectEntityCategoryTableData[0].CardLargessRate, //赠卡比例
				};
      let  treatProjectEntityCategoryTableData = JSON.parse(JSON.stringify(that.treatProjectEntityCategoryTableData))
				treatProjectEntityCategoryTableData.forEach((item) => {
					item.Child = Enumerable.from(item.Child)
						.where(function(i) {
							return (
								(i.PayRate !== "" && i.PayRate !== null) ||
								(i.CardRate !== "" && i.CardRate !== null) ||
								(i.CardLargessRate !== "" && i.CardLargessRate !== null) ||
								(i.LargessRate !== "" && i.LargessRate !== null)
							);
						})
						.select((val) => ({
							CategoryID: val.CategoryID,
							LargessRate: val.LargessRate,
							PayRate: val.PayRate, //现金比例
							CardRate: val.CardRate, //卡本金比例
							CardLargessRate: val.CardLargessRate, //赠卡比例
						}))
						.toArray();
				});
				treatProjectEntityCategoryTableData = Enumerable.from(treatProjectEntityCategoryTableData)
					.where(function(i) {
						return (
							(i.PayRate !== "" && i.PayRate !== null) ||
							(i.CardRate !== "" && i.CardRate !== null) ||
							(i.CardLargessRate !== "" && i.CardLargessRate !== null) ||
							(i.LargessRate !== "" && i.LargessRate !== null) ||
							i.Child.length > 0
						);
					})
					.toArray();

				that.modalLoading = true;
				var Category = Enumerable.from(treatProjectEntityCategoryTableData)
					.where(function(i) {
						return !i.isEntity;
					})
					.select((val) => ({
						CategoryID: val.CategoryID, // 分类ID
						LargessRate: val.LargessRate, //赠送比例
						PayRate: val.PayRate, //现金比例
						CardRate: val.CardRate, //卡本金比例
						CardLargessRate: val.CardLargessRate, //赠卡比例
						Child: val.Child,
					}))
					.toArray();
			params.Category = Category;
				API.updateTreatProjectCategoryEntityPerformance(params)
					.then((res) => {
						if (res.StateCode == 200) {
							that.$message.success("业绩设置成功");
							that.dialogEdit = false;
						} else {
							that.$message.error({
								message: res.Message,
								duration: 2000,
							});
						}
					})
					.finally(function() {
						that.modalLoading = false;
					});
    },
    /* 项目业绩设置 */
    projectEntityPerformance: function (row) {
      var that = this;
      that.projectCategoryID = row.CategoryID;
      that.categoryName = row.CategoryName;
      that.getTreatProjectEntityPerformance()
    },
    /* 获取项目业绩 */
    getTreatProjectEntityPerformance() {
				var that = this;
				var params = {
					EntityID: that.EntityID,
					CategoryID: that.projectCategoryID,
				};
				API.getTreatProjectEntityPerformance(params).then((res) => {
					if (res.StateCode == 200) {
            that.dialogProjectEntity = true;
						that.projectEntityRoyaltyList = res.Data;
					} else {
						that.$message.error({
							message: res.Message,
							duration: 2000,
						});
					}
				});
		},
    /* 项目业绩保存 */
    updateProjectEntityPerformance: function (){
      let that = this;
				that.modalLoading = true;
				let projectList = [];
				projectList = Enumerable.from(that.projectEntityRoyaltyList)
					.where(function(i) {
						return (
							(i.PayRate !== "" && i.PayRate !== null) ||
							(i.CardRate !== "" && i.CardRate !== null) ||
							(i.CardLargessRate !== "" && i.CardLargessRate !== null) ||
							(i.LargessRate !== "" && i.LargessRate !== null)
						);
					})
					.select((val) => ({
						GoodID: val.GoodID,
						PayRate: val.PayRate, //现金比例
						CardRate: val.CardRate, //卡本金比例
						CardLargessRate: val.CardLargessRate, //赠卡比例
						LargessRate: val.LargessRate,
					}))
					.toArray();
				let params = {
					EntityID: that.EntityID,
					Good: projectList,
					CategoryID: that.projectCategoryID,
				};

				API.updateTreatProjectEntityPerformance(params)
					.then((res) => {
						if (res.StateCode == 200) {
							that.$message.success("业绩设置成功");
              this.dialogProjectEntity = false;
						} else {
							that.$message.error({
								message: res.Message,
								duration: 2000,
							});
						}
					})
					.finally(() => {
						that.modalLoading = false;
					});
    },
    /* 删除门店项目消耗业绩方案 */
    removeEntityClick: function (row) {
      var that = this;
      that
        .$confirm("此操作将永久删除该记录, 是否继续?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
        .then(() => {
          that.loading = true;
						var params = {
							EntityID: row.EntityID,
						};
						API.deleteTreatProjectEntityPerformanceScheme(params)
							.then((res) => {
								if (res.StateCode == 200) {
                    that.$message.success({
										message: "删除成功",
										duration: 2000,
									});
									that.getTreatProjectEntityPerformanceScheme();
								} else {
									that.$message.error({
										message: res.Message,
										duration: 2000,
									});
								}
							})
							.finally(function() {
								that.loading = false;
							});
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    /* 所属单位 */
    entityData: function () {
      var that = this;
      APIEntity.getEntityAll()
        .then((res) => {
          if (res.StateCode == 200) {
            that.entityList = res.Data;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },
    /* 业绩比例约束 */
		royaltyRateChange: function(index, row) {
				switch (index) {
					case 0:
						row.PayRate = row.PayRate > 100 ? 100 : row.PayRate;
						break;
					case 1:
						row.CardRate = row.CardRate > 100 ? 100 : row.CardRate;
						break;
					case 2:
						row.CardLargessRate = row.CardLargessRate > 100 ? 100 : row.CardLargessRate;
						break;
					case 3:
						row.LargessRate = row.LargessRate > 100 ? 100 : row.LargessRate;
						break;
				}
		},
    /* 高亮第一级表格 */
    tableRowClassName({ rowIndex }) {
      if (rowIndex === 0) {
        return "info-row";
      }
      return "";
    },
    /* 树形结构数据转换 */
    normalizer(node) {
      return {
        id: node.ID,
        label: node.EntityName,
        children: node.Child,
      };
    },
  },
  /** 监听数据变化   */
  watch: {},
  /** 创建实例之前执行函数   */
  beforeCreate() {},
  /**  实例创建完成之后  */
  created() {},
  /**  在挂载开始之前被调用  */
  beforeMount() {},
  /**  实例被挂载后调用  */
  mounted() {
    let that = this
    this.isDelete = this.$permission.permission(
      this.$route.meta.Permission,
       "KHS-EntityPerformance-TreatProjectEntityPerformanceScheme-Delete"
    );
    that.handleSearch();
		that.entityData();
  },
  /**  数据更新 之前 调用  */
  beforeUpdate() {},
  /** 数据更新 完成 调用   */
  updated() {},
  /**  实例销毁之前调用  */
  beforeDestroy() {},
  /**  实例销毁后调用  */
  destroyed() {},
}
</script>

<style lang="scss">

.treatProjectEntityPerformanceScheme{
 .input_type {
			.el-input-group__append {
				padding: 0 10px;
			}
		}
		.el-input__inner {
			padding-right: 0;
		}
  .el-table .info-row {
    background: #c0c4cc;
  }
  .custom-dialog-add{
    min-width: 500px;
  }
  .custom-dialog-edit{
    min-width: 950px;
  }
  .custom-dialog-edit_Project{
    min-width: 850px;
  }
 }
</style>
