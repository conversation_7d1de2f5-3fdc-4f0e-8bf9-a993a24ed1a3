/**
 * Created by jerry on 2020/03/24.
 * 顾客api
 */
import * as API from "@/api/index";

export default {
  //获取会员基本信息
  getCustomerInfo: (params) => {
    return API.POST("api/customer/info", params);
  },
  //获取会员详细信息
  getCustomerDetail: (params) => {
    return API.POST("api/customer/customerDetail", params);
  },
  /* //获取营销顾问
    getConsultant: params => {
        return API.POST('api/customer/consultant',params)
    }, */
  // 顾客列表
  getCustomer: (params) => {
    return API.POST("api/customer/list", params);
  },
  // 创建顾客
  createCustomer: (params) => {
    return API.POST("api/customer/create", params);
  },
  // 修改顾客信息
  updateCustomer: (params) => {
    return API.POST("api/customer/update", params);
  },
  // 获取顾客标签信息
  getCustomerTag: (params) => {
    return API.POST("api/customer/getCustomerTagLibrary", params);
  },
  //保存选中顾客标签
  updateCustomerTagLibrary: (params) => {
    return API.POST("api/customer/updateCustomerTagLibrary", params);
  },
  //修改顾客头像
  updateCustomerUploadImage: (params) => {
    return API.POST("api/customer/uploadImage", params);
  },
  // 顾客存量余额信息
  AccountInfo: (params) => {
    return API.POST("api/customer/accountInfo", params);
  },
  // 顾客介绍人列表
  customerAll: (params) => {
    return API.POST("api/customer/all", params);
  },
  // 新增获取服务人员信息
  getCustomerServicer: (params) => {
    return API.POST("api/servicer/getCustomerServicerRange", params);
  },
  // 编辑获取服务人员信息
  getAllCustomerServicer: (params) => {
    return API.POST("api/servicer/getAllCustomerServicerRange", params);
  },
  // 设置会员等级
  setCustomerLevel: (params) => {
    return API.POST("api/customer/saveLevel", params);
  },
  // 移除会员等级
  deleteCustomerLevel: (params) => {
    return API.POST("api/customer/deleteLevel", params);
  },
  // 获取成长值列表
  getGrowthData: (params) => {
    return API.POST("api/customer/growthValue", params);
  },
  // 增减成长值
  updateGrowth: (params) => {
    return API.POST("api/customer/addGrowthValue", params);
  },
  //获取营销云的标签
  getMarketingCloud: (params) => {
    return API.POST("api/customer/yingxiaoyunLabel", params);
  },
  // 导出 -显示手机号
  excelDisPlayPhone: (params) => {
    return API.exportExcel("api/customer/excelDisPlayPhone", params);
  },

  // 导出 -隐藏手机号
  excelNoDisPlayPhone: (params) => {
    return API.exportExcel("api/customer/excelNoDisPlayPhone", params);
  },
  //可修改顾客所属医院列表
  customer_customerBelongEntity: (params) => {
    return API.POST("api/customer/customerBelongEntity", params);
  },
  //修改所属医院
  customer_updateCustomerBelongEntity: (params) => {
    return API.POST("api/customer/updateCustomerBelongEntity", params);
  },

  //  查询门店编码以及机构编码 - 已弃用
  // customer_yjEntity: (params) => {
  //   return API.POST("api/customer/yjEntity", params);
  // },
  //  查询门店编码以及机构编码
  phoneCallBack_callBackLog: (params) => {
    return API.POST("api/phoneCallBack/callBackLog", params);
  },
};
