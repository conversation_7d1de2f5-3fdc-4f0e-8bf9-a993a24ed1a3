<template>
  <div class="Message content_body" :loading="loading">
    <div v-for="item in msgTemplate" :key="item.Category">
      <div class="tip">{{ item.Category }}</div>
      <div class="dis_flex flex_wrap" style="padding: 10px 10px 0px 10px">
        <el-card v-for="child in item.Detail" :key="child.Code" class="messageBox-card" shadow="never">
          <div slot="header" class="messageBox-card_header">
            <span>{{ child.Name }}</span>
            <el-button style="float: right; padding: 3px 0" size="small" type="text" @click="checkMsgTemplate(child)">查看模板</el-button>
          </div>
          <div>
            <span class="font_12 color_666 marrt_10">公众号消息推送</span>
            <el-switch v-model="child.IsSendWechat" @change="(enven) => changeIsSendWechat(enven, child)"> </el-switch>
          </div>
        </el-card>
      </div>
    </div>
    <el-dialog :title="msgInfo ? msgInfo.dialogTitle : ''" :visible.sync="dialogVisible" width="500px">
      <!-- <span>推送时间点：顾客消费成功后立即发送</span> -->
      <div class="dis_flex flex_y_center flex_dir_column" style="padding-bottom: 10px">
        <el-card v-if="msgInfo" class="msgBox-card" shadow="never">
          <div v-for="(val, key) in msgInfo" :key="key" class="marbm_5">
            <div v-if="key == 'title'" class="font_15">{{ val }}</div>
            <div v-else-if="key == 'first'" class="color_999 padbm_10 font_14">{{ val }}</div>
            <div v-else-if="key == 'remark'">
              <span class="color_999 font_13" :span="6">备注：</span><span class="font_13">{{ val }}</span>
            </div>
            <div v-else>
              <span v-if="key != 'dialogTitle'" class="color_999 padrt_20 font_13">{{ key }}：</span><span v-if="key != 'dialogTitle'" class="font_13">{{ val }}</span>
            </div>
          </div>
        </el-card>
        <!-- <span class="martp_10 color_999">注：需要顾客关注您店铺公众号才可以接受消息通知</span> -->
      </div>
    </el-dialog>
  </div>
</template>

<script>
import API from "@/api/Marketing/App/Message.js";
export default {
  name: "Message",
  props: {},
  /** 监听数据变化   */
  watch: {},
  /**  引入的组件  */
  components: {},
  /**  Vue 实例的数据对象**/
  data() {
    return {
      loading: false,
      saveLoading: false,
      dialogVisible: false,
      msgTemplate: [],
      msgInfo: null,
    };
  },
  /**计算属性  */
  computed: {},
  /**  方法集合  */
  methods: {
    /**    */
    changeIsSendWechat(val, item) {
      this.messageNotification_update(item.Code, item.IsSendWechat);
    },
    /**  查看模板  */
    checkMsgTemplate(item) {
      let that = this;
      that.dialogVisible = true;
      switch (item.Code) {
        case "SaleNotice":
        case "RechargeNotice":
        case "ArrearsNotice":
          that.msgInfo = {
            dialogTitle: item.Name,
            title: "交易成功通知",
            门店名称: "{门店名称}",
            交易金额: "{交易金额}",
            交易订单: "{交易订单}",
            交易时间: "{交易时间}",
          };
          break;
        case "RefundNotice":
          that.msgInfo = {
            dialogTitle: item.Name,
            title: "账户退款成功通知",
            门店名称: "{门店名称}",
            退款金额: "{退款金额}",
            订单号: "{订单号}",
            操作时间: "{操作时间}",
          };
          break;
        case "SaleCancelNotice":
        case "RechargeCancelNotice":
        case "ArrearsCancelNotice":
          that.msgInfo = {
            dialogTitle: item.Name,
            title: "交易撤销通知",
            门店名称: "{门店名称}",
            交易金额: "{交易金额}",
            交易时间: "{交易时间}",
            撤销原因: "{撤销原因}",
          };
          break;

        case "TreatNotice":
          that.msgInfo = {
            dialogTitle: item.Name,
            title: "项目消费成功通知",
            门店名称: "{门店名称}",
            服务项目: "{服务项目}",
            消费数量: "{消费数量}",
            服务时间: "{服务时间}",
          };
          break;
        case "TreatCancelNotice":
          that.msgInfo = {
            dialogTitle: item.Name,
            title: "项目消费撤销通知",
            门店名称: "{门店名称}",
            服务项目: "{服务项目}",
            撤销时间: "{撤销时间}",
          };
          break;
        case "RefundTreatNotice":
          that.msgInfo = {
            dialogTitle: item.Name,
            title: "退单成功通知",
            项目名称: "{项目名称}",
            数量: "{数量}",
            退单时间: "{退单时间}",
          };
          break;

        case "AppointmentNotice":
          that.msgInfo = {
            dialogTitle: item.Name,
            title: "预约成功通知",
            预约门店: "{预约门店}",
            预约时间: "{预约时间}",
            服务项目: "{服务项目}",
            订单号: "{订单号}",
            门店地址: "{门店地址}",
          };
          break;

        case "AppointmentModifyNotice":
          that.msgInfo = {
            dialogTitle: item.Name,
            title: "预约改期成功通知",
            预约门店: "{预约门店}",
            服务项目: "{服务项目}",
            原定时间: "{原定时间}",
            修改后时间: "{修改后时间}",
            门店地址: "{门店地址}",
          };
          break;

        case "AppointmentCancelNotice":
          that.msgInfo = {
            dialogTitle: item.Name,
            title: "预约取消通知",
            预约门店: "{预约门店}",
            预约时间: "{预约时间}",
            预约项目: "{预约项目}",
            订单号: "{订单号}",
          };
          break;
        case "AppointmentCancelRemind":
          that.msgInfo = {
            dialogTitle: item.Name,
            title: "客户到店提醒",
            预约时间: "{预约时间}",
            预约门店: "{预约门店}",
            预约项目: "{预约项目}",
            门店地址: "{门店地址}",
          };
          break;

        
        default:
          break;
      }
    },

    /**  请求  */
    async messageNotification_all() {
      let that = this;
      that.loading = true;
      let params = {};
      let res = await API.messageNotification_all(params);
      if (res.StateCode == 200) {
        that.msgTemplate = res.Data;
      } else {
        that.$message.error(res.Message);
      }
      that.loading = false;
    },
    /** 修改   */
    async messageNotification_update(Code, IsSendWechat) {
      let that = this;
      that.saveLoading = true;

      let params = {
        Code: Code, //编号
        IsSendWechat: IsSendWechat, //是否发送
      };
      let res = await API.messageNotification_update(params);
      if (res.StateCode == 200) {
        that.$message.success("保存成功");
        that.$messageNotification_all();
      } else {
        that.$message.error(res.Message);
      }
      that.saveLoading = false;
    },
  },
  /** 创建实例之前执行函数   */
  beforeCreate() {},
  /**  实例创建完成之后  */
  created() {},
  /**  在挂载开始之前被调用  */
  beforeMount() {},
  /**  实例被挂载后调用  */
  mounted() {
    this.messageNotification_all();
  },
  /**  数据更新 之前 调用  */
  beforeUpdate() {},
  /** 数据更新 完成 调用   */
  updated() {},
  /**  实例销毁之前调用  */
  beforeDestroy() {},
  /**  实例销毁后调用  */
  destroyed() {},
};
</script>

<style lang="scss">
.Message {
  font-size: 14px;
  color: #333333;
  .messageBox-card {
    width: 300px;
    background-color: #f7f8fa;
    margin-right: 20px;
    margin-bottom: 20px;
  }
  .msgBox-card {
    width: 100%;
    background-color: #f7f8fa;
  }
}
</style>
