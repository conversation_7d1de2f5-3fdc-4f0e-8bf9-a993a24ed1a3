<template>
  <div class="saleModifyHandledPerformance">
    <el-dialog v-if="changePerformance" :visible.sync="visible_" width="1380px" @close="closeModifyPerformance">
      <span slot="title">修改经手人业绩提成</span>
      <div v-if="PerformanceInfo" class="font_13" style="height: 60vh">
        <el-scrollbar class="el-scrollbar_height">
          <!-- 项目 -->
          <div v-if="PerformanceInfo.Project.length > 0">
            <div v-for="(item, index) in PerformanceInfo.Project" :key="index">
              <el-row class="row_header border_right border_left">
                <el-col :span="5">项目</el-col>
                <el-col :span="2">数量</el-col>
                <el-col :span="4">优惠金额</el-col>
                <el-col :span="3">购买金额</el-col>
                <el-col :span="5">支付金额</el-col>
                <el-col :span="5">欠款金额</el-col>
              </el-row>
              <el-row>
                <el-col :span="24" class="pad_10 border_right border_left border_bottom">
                  <el-col :span="24">
                    <el-col :span="5">
                      <div>
                        {{ item.ProjectName }}
                        <span v-if="item.Alias">({{ item.Alias }})</span>
                        <el-tag v-if="item.IsLargess" size="mini" type="danger" class="marlt_10">赠</el-tag>
                      </div>
                    </el-col>
                    <el-col :span="2">x {{ item.Quantity }}</el-col>
                    <el-col :span="4">
                      <span v-if="item.PreferentialTotalAmount < 0">+ ¥ {{ Math.abs(item.PreferentialTotalAmount) | toFixed | NumFormat }}</span>
                      <span v-else-if="item.PreferentialTotalAmount > 0">- ¥ {{ Math.abs(item.PreferentialTotalAmount) | toFixed | NumFormat }}</span>
                      <span v-else>¥ 0.00</span>
                    </el-col>
                    <el-col :span="3">¥ {{ item.IsLargess ? 0 : item.TotalAmount | toFixed | NumFormat }}</el-col>
                    <el-col :span="5">¥ {{ (parseFloat(item.PayAmount) + parseFloat(item.CardDeductionTotalAmount)) | toFixed | NumFormat }}</el-col>
                    <el-col :span="5">¥ {{ item.ArrearAmount | toFixed | NumFormat }}</el-col>
                  </el-col>
                  <el-col :span="24" class="martp_5">
                    <el-col :span="7">
                      <div class="color_red font_12">¥ {{ item.Price | toFixed | NumFormat }}</div>
                    </el-col>
                    <el-col :span="7">
                      <span class="color_gray font_12" v-if="item.PricePreferentialAmount != 0">
                        手动改价：
                        <span class="color_red" v-if="item.PricePreferentialAmount > 0">- ¥ {{ item.PricePreferentialAmount | toFixed | NumFormat }}</span>
                        <span class="color_green" v-else>+ ¥ {{ Math.abs(item.PricePreferentialAmount) | toFixed | NumFormat }}</span>
                      </span>
                      <span class="color_gray font_12" :class="item.PricePreferentialAmount != 0 ? 'marlt_15' : ''" v-if="item.CardPreferentialAmount > 0">
                        卡优惠：
                        <span class="color_red">- ¥ {{ parseFloat(item.CardPreferentialAmount) | toFixed | NumFormat }}</span>
                      </span>


                      <span class="color_gray font_12" :class="item.PricePreferentialAmount != 0 ? item.CardPreferentialAmount != 0 ? 'marlt_15':'' : ''" v-if="item.MemberPreferentialAmount > 0">
                        会员优惠：
                        <span class="color_red">- ¥ {{ parseFloat(item.MemberPreferentialAmount) | toFixed | NumFormat }}</span>
                      </span>

                    </el-col>
                    <el-col :span="10" :offset="item.PricePreferentialAmount == 0 && item.CardPreferentialAmount == 0 ? 7 : 0">
                      <span class="color_gray font_12" v-if="item.PayAmount > 0">现金金额：¥ {{ item.PayAmount | toFixed | NumFormat }}</span>
                      <span class="color_gray font_12" :class="item.PayAmount > 0 ? 'marlt_15' : ''" v-if="item.CardDeductionAmount > 0"
                        >卡抵扣：¥ {{ item.CardDeductionAmount | toFixed | NumFormat }}</span
                      >
                      <span
                        class="color_gray font_12"
                        :class="item.PricePreferentialAmount > 0 || item.PayAmount > 0 ? 'marlt_15' : ''"
                        v-if="item.CardDeductionLargessAmount > 0"
                        >赠送卡抵扣：¥ {{ item.CardDeductionLargessAmount | toFixed | NumFormat }}</span
                      >
                    </el-col>
                  </el-col>
                </el-col>
              </el-row>
              <el-row
                class="padlt_10 padrt_10 border_right border_left border_bottom font_12"
                v-for="handler in item.SaleBillHandler"
                :key="handler.SaleHandlerID"
              >
                <el-col :span="2" class="padtp_10 padbm_10">
                  <el-link class="font_12 line_26" type="primary" :underline="false" @click="employeeHandleClick(1, item)">{{
                    handler.SaleHandlerName
                  }}</el-link>
                </el-col>
                <el-col :span="22" class="border_left">
                  <el-row v-for="(employee, index) in handler.Employee" :key="employee.EmployeeID">
                    <el-col :span="5" class="padtp_10 padrt_10 " :class="index != 0 ? 'border_top' : ''">
                      <el-form class="sale-ModifyPerformanceCommission-Handler" label-position="right" :inline="true" size="mini" label-width="70px">
                        <el-form-item :label="`${employee.EmployeeName}`">
                          <el-input
                            type="number"
                            v-input-fixed
                            class="input_type"
                            style="width: 90px"
                            v-model="employee.Scale"
                            v-enter-number2
                            @input="changeSaleHandlerRate(item, employee, 10)"
                          >
                            <template slot="append">%</template>
                          </el-input>
                        </el-form-item>
                        <el-form-item class="cursorclass"><el-checkbox size="small" v-model="employee.IsCalculatePassengerFlow">客流</el-checkbox>
                        </el-form-item>
                      </el-form>
                    </el-col>
                    <el-col :span="15" class="border_right border_left">
                      <el-row v-if="item.PayAmount > 0" class="padtp_10 padrt_10" :class="index != 0 ? 'border_top' : ''">
                        <el-col :span="8">
                          <el-form class="sale-ModifyPerformanceCommission-Handler" label-position="right" size="mini" label-width="120px">
                            <el-form-item label="现金业绩">
                              <span slot="label">
                                现金业绩
                                <el-popover placement="top-start" width="200" trigger="hover">
                                  <p>现金业绩 = 现金付款金额 x 员工现金业绩占比 x 业绩占比</p>
                                  <p>
                                    员工现金业绩占比参考值：
                                    <span v-if="employee.PerformancePayRate != null">{{ (employee.PerformancePayRate * 100) | toFixed | NumFormat }}%</span>
                                    <span v-else>无</span>
                                  </p>

                                  <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                </el-popover>
                              </span>
                              <el-input type="number" v-input-fixed class="input_type" style="width: 120px" v-model="employee.PayPerformance" v-enter-number2>
                                <template slot="append">元</template>
                              </el-input>
                            </el-form-item>
                          </el-form>
                        </el-col>
                        <el-col :span="8">
                          <el-form class="sale-ModifyPerformanceCommission-Handler" label-position="right" size="mini" label-width="150px">
                            <el-form-item label="现金比例提成">
                              <span slot="label">
                                现金比例提成
                                <el-popover placement="top-start" width="200" trigger="hover">
                                  <p>现金比例提成 = 现金业绩 x 现金比例</p>
                                  <p v-if="employee.PayRate != null">现金比例参考值：{{ employee.PayRate | toFixed | NumFormat }}%</p>
                                  <p v-else>现金比例参考值：无</p>
                                  <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                </el-popover>
                              </span>
                              <el-input
                                type="number"
                                v-input-fixed
                                class="input_type"
                                style="width: 100px"
                                v-model="employee.PayRateCommission"
                                v-enter-number2
                              >
                                <template slot="append">元</template>
                              </el-input>
                            </el-form-item>
                          </el-form>
                        </el-col>
                        <el-col :span="8">
                          <el-form class="sale-ModifyPerformanceCommission-Handler" label-position="right" size="mini" label-width="150px">
                            <el-form-item label="现金固定提成">
                              <span slot="label">
                                现金固定提成
                                <el-popover placement="top-start" width="200" trigger="hover">
                                  <p>现金固定提成 = （现金业绩 ÷ 购买金额）x 现金固定 x 数量</p>
                                  <p v-if="employee.PayFixed != null">现金固定参考值：¥ {{ employee.PayFixed | toFixed | NumFormat }}</p>
                                  <p v-else>现金固定参考值：无</p>
                                  <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                </el-popover>
                              </span>
                              <el-input
                                type="number"
                                v-input-fixed
                                class="input_type"
                                style="width: 100px"
                                v-model="employee.PayFixedCommission"
                                v-enter-number2
                              >
                                <template slot="append">元</template>
                              </el-input>
                            </el-form-item>
                          </el-form>
                        </el-col>
                      </el-row>
                      <el-row v-if="item.CardDeductionAmount > 0" class="padtp_10 padrt_10 border_top">
                        <el-col :span="8">
                          <el-form class="sale-ModifyPerformanceCommission-Handler" label-position="right" size="mini" label-width="120px">
                            <el-form-item label="卡抵扣业绩">
                              <span slot="label">
                                卡抵扣业绩
                                <el-popover placement="top-start" width="200" trigger="hover">
                                  <p>卡抵扣业绩 = 卡抵扣金额 x 员工卡抵扣业绩占比 x 业绩占比</p>
                                  <p>
                                    员工卡抵扣业绩占比参考值：
                                    <span v-if="employee.PerformanceSavingCardRate != null"
                                      >{{ (employee.PerformanceSavingCardRate * 100) | toFixed | NumFormat }}%</span
                                    >
                                    <span v-else>无</span>
                                  </p>

                                  <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                </el-popover>
                              </span>
                              <el-input
                                type="number"
                                v-input-fixed
                                class="input_type"
                                style="width: 120px"
                                v-model="employee.SavingCardPerformance"
                                v-enter-number2
                              >
                                <template slot="append">元</template>
                              </el-input>
                            </el-form-item>
                          </el-form>
                        </el-col>
                        <el-col :span="8">
                          <el-form class="sale-ModifyPerformanceCommission-Handler" label-position="right" size="mini" label-width="150px">
                            <el-form-item label="卡抵扣比例提成">
                              <span slot="label">
                                卡抵扣比例提成
                                <el-popover placement="top-start" width="200" trigger="hover">
                                  <p>卡抵扣比例提成 = 卡抵扣业绩 x 卡抵扣比例</p>
                                  <p v-if="employee.SavingCardRate != null">卡抵扣比例参考值：{{ employee.SavingCardRate | toFixed | NumFormat }}%</p>
                                  <p v-else>卡抵扣比例参考值：无</p>
                                  <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                </el-popover>
                              </span>
                              <el-input
                                type="number"
                                v-input-fixed
                                class="input_type"
                                style="width: 100px"
                                v-model="employee.SavingCardRateCommission"
                                v-enter-number2
                              >
                                <template slot="append">元</template>
                              </el-input>
                            </el-form-item>
                          </el-form>
                        </el-col>
                        <el-col :span="8">
                          <el-form class="sale-ModifyPerformanceCommission-Handler" label-position="right" size="mini" label-width="150px">
                            <el-form-item label="卡抵扣固定提成">
                              <span slot="label">
                                卡抵扣固定提成
                                <el-popover placement="top-start" width="200" trigger="hover">
                                  <p>卡抵扣固定提成 = （卡抵扣业绩 ÷ 购买金额）x 卡抵扣固定 x 数量</p>
                                  <p v-if="employee.SavingCardFixed != null">卡抵扣固定参考值：¥ {{ employee.SavingCardFixed | toFixed | NumFormat }}</p>
                                  <p v-else>卡抵扣固定参考值：无</p>
                                  <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                </el-popover>
                              </span>
                              <el-input
                                type="number"
                                v-input-fixed
                                class="input_type"
                                style="width: 100px"
                                v-model="employee.SavingCardFixedCommission"
                                v-enter-number2
                              >
                                <template slot="append">元</template>
                              </el-input>
                            </el-form-item>
                          </el-form>
                        </el-col>
                      </el-row>
                      <el-row v-if="item.CardDeductionLargessAmount > 0" class="padtp_10 padrt_10 border_top">
                        <el-col :span="8">
                          <el-form class="sale-ModifyPerformanceCommission-Handler" label-position="right" size="mini" label-width="120px">
                            <el-form-item label="赠送卡抵扣业绩">
                              <span slot="label">
                                赠送卡抵扣业绩
                                <el-popover placement="top-start" width="200" trigger="hover">
                                  <p>赠送卡抵扣业绩 = 赠送卡抵扣金额 x 赠送卡抵扣业绩占比 x 业绩占比</p>
                                  <p>
                                    员工赠送卡抵扣业绩占比参考值：
                                    <span v-if="employee.PerformanceSavingCardLargessRate != null"
                                      >{{ (employee.PerformanceSavingCardLargessRate * 100) | toFixed | NumFormat }}%</span
                                    >
                                    <span v-else>无</span>
                                  </p>

                                  <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                </el-popover>
                              </span>
                              <el-input type="number" v-input-fixed class="input_type" style="width: 120px" v-model="employee.SavingCardLargessPerformance">
                                <template slot="append">元</template>
                              </el-input>
                            </el-form-item>
                          </el-form>
                        </el-col>
                        <el-col :span="8">
                          <el-form class="sale-ModifyPerformanceCommission-Handler" label-position="right" size="mini" label-width="150px">
                            <el-form-item label="赠送卡抵扣比例提成">
                              <span slot="label">
                                赠送卡抵扣比例提成
                                <el-popover placement="top-start" width="200" trigger="hover">
                                  <p>赠送卡抵扣比例提成 = 赠送卡抵扣业绩 x 赠送卡抵扣比例</p>
                                  <p v-if="employee.SavingCardLargessRate != null">
                                    赠送卡抵扣比例参考值：{{ employee.SavingCardLargessRate | toFixed | NumFormat }}%
                                  </p>
                                  <p v-else>赠送卡抵扣比例参考值：无</p>
                                  <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                </el-popover>
                              </span>
                              <el-input
                                type="number"
                                v-input-fixed
                                class="input_type"
                                style="width: 100px"
                                v-model="employee.SavingCardLargessRateCommission"
                                v-enter-number2
                              >
                                <template slot="append">元</template>
                              </el-input>
                            </el-form-item>
                          </el-form>
                        </el-col>
                        <el-col :span="8">
                          <el-form class="sale-ModifyPerformanceCommission-Handler" label-position="right" size="mini" label-width="150px">
                            <el-form-item label="赠送卡抵扣固定提成">
                              <span slot="label">
                                赠送卡抵扣固定提成
                                <el-popover placement="top-start" width="200" trigger="hover">
                                  <p>赠送卡抵扣固定提成 = （赠送卡抵扣业绩 ÷ 购买金额）x 赠送卡抵扣固定 x 数量</p>
                                  <p v-if="employee.SavingCardLargessFixed != null">
                                    赠送卡抵扣固定参考值：¥ {{ employee.SavingCardLargessFixed | toFixed | NumFormat }}
                                  </p>
                                  <p v-else>赠送卡抵扣固定参考值：无</p>
                                  <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                </el-popover>
                              </span>
                              <el-input
                                type="number"
                                v-input-fixed
                                class="input_type"
                                style="width: 100px"
                                v-model="employee.SavingCardLargessFixedCommission"
                                v-enter-number2
                              >
                                <template slot="append">元</template>
                              </el-input>
                            </el-form-item>
                          </el-form>
                        </el-col>
                      </el-row>
                    </el-col>
                    <el-col v-if="!item.IsLargess" :span="4" class="padtp_10" :class="index != 0 ? 'border_top' : ''">
                      <el-form class="sale-ModifyPerformanceCommission-Handler" label-position="right" size="mini" label-width="90px">
                        <el-form-item label="无业绩奖励">
                          <span slot="label">
                            无业绩奖励
                            <el-popover placement="top-start" width="200" trigger="hover">
                              <p>无业绩奖励 = 无业绩奖励参考值 x 数量 x 经手人比例</p>
                              <p v-if="employee.SpecialBenefit != null">无业绩奖励参考值：¥ {{ employee.SpecialBenefit | toFixed | NumFormat }}</p>
                              <p v-else>无业绩奖励参考值：无</p>
                              <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                            </el-popover>
                          </span>
                          <el-input
                            type="number"
                            v-input-fixed
                            class="input_type"
                            style="width: 100px"
                            v-model="employee.SpecialBenefitCommission"
                            v-enter-number2
                          >
                            <template slot="append">元</template>
                          </el-input>
                        </el-form-item>
                      </el-form>
                    </el-col>
                  </el-row>
                </el-col>
              </el-row>
            </div>
          </div>
          <!-- 储值卡 -->
          <div v-if="PerformanceInfo.SavingCard.length > 0">
            <div v-for="(item, index) in PerformanceInfo.SavingCard" :key="index">
              <el-row class="row_header border_right border_left">
                <el-col :span="5">储值卡</el-col>
                <el-col :span="2">购买数量</el-col>
                <el-col :span="4">充值金额</el-col>
                <el-col :span="4">赠送金额</el-col>
                <el-col :span="5">支付金额</el-col>
                <el-col :span="4">欠款金额</el-col>
              </el-row>
              <el-row>
                <el-col :span="24" class="pad_10 border_left border_bottom">
                  <el-col :span="24">
                    <el-col :span="5">
                      <div>
                        {{ item.SavingCardName }}
                        <span v-if="item.Alias">({{ item.Alias }})</span>
                      </div>
                    </el-col>
                    <el-col :span="2">x {{ item.Quantity }}</el-col>
                    <el-col :span="4">¥ {{ item.TotalAmount | toFixed | NumFormat }}</el-col>
                    <el-col :span="4">¥ {{ item.LargessAmount | toFixed | NumFormat }}</el-col>
                    <el-col :span="5">¥ {{ item.PayAmount | toFixed | NumFormat }}</el-col>
                    <el-col :span="4">¥ {{ item.ArrearAmount | toFixed | NumFormat }}</el-col>
                  </el-col>
                  <el-col :span="24" class="martp_5">
                    <el-col :span="15">
                      <div>
                        <span class="color_red font_12">¥ {{ item.Price | toFixed | NumFormat }}</span>
                        <span class="marlt_10 font_12 color_gray">赠送：¥ {{ item.LargessPrice | toFixed }}</span>
                      </div>
                    </el-col>
                    <el-col :span="9">
                      <span class="color_gray font_12" v-if="item.PayAmount > 0">现金金额：¥ {{ item.PayAmount | toFixed | NumFormat }}</span>
                    </el-col>
                  </el-col>
                </el-col>
              </el-row>
              <el-row
                class="padlt_10 padrt_10 border_right border_left border_bottom font_12"
                v-for="handler in item.SaleBillHandler"
                :key="handler.SaleHandlerID"
              >
                <el-col :span="2" class="padtp_10 padbm_10">
                  <el-link class="font_12 line_26" type="primary" :underline="false" @click="employeeHandleClick(5, item)">{{
                    handler.SaleHandlerName
                  }}</el-link>
                </el-col>
                <el-col :span="22" class="border_left">
                  <el-row v-for="(employee, index) in handler.Employee" :key="employee.EmployeeID">
                    <el-col :span="5" class="padtp_10 padrt_10 " :class="index != 0 ? 'border_top' : ''">
                      <el-form class="sale-ModifyPerformanceCommission-Handler" label-position="right" size="mini" label-width="70px" :inline="true">
                        <el-form-item :label="`${employee.EmployeeName}`">
                          <el-input
                            type="number"
                            v-input-fixed
                            class="input_type"
                            style="width: 90px"
                            v-model="employee.Scale"
                            v-enter-number2
                            @input="changeSaleHandlerRate(item, employee, 20)"
                          >
                            <template slot="append">%</template>
                          </el-input>
                        </el-form-item>
                        <el-form-item class="cursorclass">
                          <el-checkbox size="small" v-model="employee.IsCalculatePassengerFlow">客流</el-checkbox>
                        </el-form-item>
                      </el-form>
                    </el-col>
                    <el-col :span="15" class="border_left border_right">
                      <el-row v-if="item.PayAmount > 0" class="padtp_10 padrt_10" :class="index != 0 ? 'border_top' : ''">
                        <el-col :span="8">
                          <el-form class="sale-ModifyPerformanceCommission-Handler" label-position="right" size="mini" label-width="120px">
                            <el-form-item label="现金业绩">
                              <span slot="label">
                                现金业绩
                                <el-popover placement="top-start" width="200" trigger="hover">
                                  <p>现金业绩 = 现金付款金额 x 员工现金业绩占比 x 业绩占比</p>
                                  <p>
                                    员工现金业绩占比参考值：
                                    <span v-if="employee.PerformancePayRate != null">{{ (employee.PerformancePayRate * 100) | toFixed | NumFormat }}%</span>
                                    <span v-else>无</span>
                                  </p>

                                  <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                </el-popover>
                              </span>
                              <el-input type="number" v-input-fixed class="input_type" style="width: 120px" v-model="employee.PayPerformance" v-enter-number2>
                                <template slot="append">元</template>
                              </el-input>
                            </el-form-item>
                          </el-form>
                        </el-col>
                        <el-col :span="8">
                          <el-form class="sale-ModifyPerformanceCommission-Handler" label-position="right" size="mini" label-width="150px">
                            <el-form-item label="现金比例提成">
                              <span slot="label">
                                现金比例提成
                                <el-popover placement="top-start" width="200" trigger="hover">
                                  <p>现金比例提成 = 现金业绩 x 现金比例</p>
                                  <p v-if="employee.PayRate != null">现金比例参考值：{{ employee.PayRate | toFixed | NumFormat }}%</p>
                                  <p v-else>现金比例参考值：无</p>
                                  <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                </el-popover>
                              </span>
                              <el-input
                                type="number"
                                v-input-fixed
                                class="input_type"
                                style="width: 100px"
                                v-model="employee.PayRateCommission"
                                v-enter-number2
                              >
                                <template slot="append">元</template>
                              </el-input>
                            </el-form-item>
                          </el-form>
                        </el-col>
                        <el-col :span="8">
                          <el-form class="sale-ModifyPerformanceCommission-Handler" label-position="right" size="mini" label-width="150px">
                            <el-form-item label="现金固定提成">
                              <span slot="label">
                                现金固定提成
                                <el-popover placement="top-start" width="200" trigger="hover">
                                  <p>现金固定提成 = （现金业绩 ÷ 购买金额）x 现金固定 x 数量</p>
                                  <p v-if="employee.PayFixed != null">现金固定参考值：¥ {{ employee.PayFixed | toFixed | NumFormat }}</p>
                                  <p v-else>现金固定参考值：无</p>
                                  <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                </el-popover>
                              </span>
                              <el-input
                                type="number"
                                v-input-fixed
                                class="input_type"
                                style="width: 100px"
                                v-model="employee.PayFixedCommission"
                                v-enter-number2
                              >
                                <template slot="append">元</template>
                              </el-input>
                            </el-form-item>
                          </el-form>
                        </el-col>
                      </el-row>
                    </el-col>
                    <el-col :span="4" class="padtp_10" :class="index != 0 ? 'border_top' : ''">
                      <el-form class="sale-ModifyPerformanceCommission-Handler" label-position="right" size="mini" label-width="90px">
                        <el-form-item label="无业绩奖励">
                          <span slot="label">
                            无业绩奖励
                            <el-popover placement="top-start" width="200" trigger="hover">
                              <p>无业绩奖励 = 无业绩奖励参考值 x 数量 x 经手人比例</p>
                              <p v-if="employee.SpecialBenefit != null">无业绩奖励参考值：¥ {{ employee.SpecialBenefit | toFixed | NumFormat }}</p>
                              <p v-else>无业绩奖励参考值：无</p>
                              <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                            </el-popover>
                          </span>
                          <el-input
                            type="number"
                            v-input-fixed
                            class="input_type"
                            style="width: 100px"
                            v-model="employee.SpecialBenefitCommission"
                            v-enter-number2
                          >
                            <template slot="append">元</template>
                          </el-input>
                        </el-form-item>
                      </el-form>
                    </el-col>
                  </el-row>
                </el-col>
              </el-row>
            </div>
          </div>
          <!-- 时效卡 -->
          <div v-if="PerformanceInfo.TimeCard.length > 0">
            <div v-for="(item, index) in PerformanceInfo.TimeCard" :key="index">
              <el-row class="row_header border_right border_left">
                <el-col :span="5">时效卡</el-col>
                <el-col :span="2">数量</el-col>
                <el-col :span="4">优惠金额</el-col>
                <el-col :span="3">购买金额</el-col>
                <el-col :span="5">支付金额</el-col>
                <el-col :span="5">欠款金额</el-col>
              </el-row>
              <el-row>
                <el-col :span="24" class="pad_10 border_right border_left border_bottom">
                  <el-col :span="24">
                    <el-col :span="5">
                      <div>
                        {{ item.TimeCardName }}
                        <span v-if="item.Alias">({{ item.Alias }})</span>
                        <el-tag v-if="item.IsLargess" size="mini" type="danger" class="marlt_10">赠</el-tag>
                      </div>
                    </el-col>
                    <el-col :span="2">x {{ item.Quantity }}</el-col>
                    <el-col :span="4">
                      <span v-if="item.PreferentialTotalAmount < 0">+ ¥ {{ Math.abs(item.PreferentialTotalAmount) | toFixed | NumFormat }}</span>
                      <span v-else-if="item.PreferentialTotalAmount > 0">- ¥ {{ Math.abs(item.PreferentialTotalAmount) | toFixed | NumFormat }}</span>
                      <span v-else>¥ 0.00</span>
                    </el-col>
                    <el-col :span="3">¥ {{ item.IsLargess ? 0 : item.TotalAmount | toFixed | NumFormat }}</el-col>
                    <el-col :span="5">¥ {{ (parseFloat(item.PayAmount) + parseFloat(item.CardDeductionTotalAmount)) | toFixed | NumFormat }}</el-col>
                    <el-col :span="5">¥ {{ item.ArrearAmount | toFixed | NumFormat }}</el-col>
                  </el-col>
                  <el-col :span="24" class="martp_5">
                    <el-col :span="7">
                      <div class="color_red font_12">¥ {{ item.Price | toFixed | NumFormat }}</div>
                    </el-col>
                    <el-col :span="7">
                      <span class="color_gray font_12" v-if="item.PricePreferentialAmount != 0">
                        手动改价：
                        <span class="color_red" v-if="item.PricePreferentialAmount > 0">- ¥ {{ item.PricePreferentialAmount | toFixed | NumFormat }}</span>
                        <span class="color_green" v-else>+ ¥ {{ Math.abs(item.PricePreferentialAmount) | toFixed | NumFormat }}</span>
                      </span>
                      <span class="color_gray font_12" :class="item.PricePreferentialAmount != 0 ? 'marlt_15' : ''" v-if="item.CardPreferentialAmount > 0">
                        卡优惠：
                        <span class="color_red">- ¥ {{ parseFloat(item.CardPreferentialAmount) | toFixed | NumFormat }}</span>
                      </span>

                      <span class="color_gray font_12" :class="item.PricePreferentialAmount != 0 ? item.CardPreferentialAmount != 0 ? 'marlt_15':'' : ''" v-if="item.MemberPreferentialAmount > 0">
                        会员优惠：
                        <span class="color_red">- ¥ {{ parseFloat(item.MemberPreferentialAmount) | toFixed | NumFormat }}</span>
                      </span>
                    </el-col>
                    <el-col :span="10" :offset="item.PricePreferentialAmount == 0 && item.CardPreferentialAmount == 0 ? 7 : 0">
                      <span class="color_gray font_12" v-if="item.PayAmount > 0">现金金额：¥ {{ item.PayAmount | toFixed | NumFormat }}</span>
                      <span class="color_gray font_12" :class="item.PayAmount > 0 ? 'marlt_15' : ''" v-if="item.CardDeductionAmount > 0"
                        >卡抵扣：¥ {{ item.CardDeductionAmount | toFixed | NumFormat }}</span
                      >
                      <span
                        class="color_gray font_12"
                        :class="item.PricePreferentialAmount > 0 || item.PayAmount > 0 ? 'marlt_15' : ''"
                        v-if="item.CardDeductionLargessAmount > 0"
                        >赠送卡抵扣：¥ {{ item.CardDeductionLargessAmount | toFixed | NumFormat }}</span
                      >
                    </el-col>
                  </el-col>
                </el-col>
              </el-row>
              <el-row
                class="padlt_10 padrt_10 border_right border_left border_bottom font_12"
                v-for="handler in item.SaleBillHandler"
                :key="handler.SaleHandlerID"
              >
                <el-col :span="2" class="padtp_10 padbm_10">
                  <el-link class="font_12 line_26" type="primary" :underline="false" @click="employeeHandleClick(4, item)">{{
                    handler.SaleHandlerName
                  }}</el-link>
                </el-col>
                <el-col :span="22" class="border_left">
                  <el-row v-for="(employee, index) in handler.Employee" :key="employee.EmployeeID">
                    <el-col :span="5" class="padtp_10 padrt_10 " :class="index != 0 ? 'border_top' : ''">
                      <el-form class="sale-ModifyPerformanceCommission-Handler" label-position="right" size="mini" label-width="70px" :inline="true">
                        <el-form-item :label="`${employee.EmployeeName}`">
                          <el-input
                            type="number"
                            v-input-fixed
                            class="input_type"
                            style="width: 90px"
                            v-model="employee.Scale"
                            v-enter-number2
                            @input="changeSaleHandlerRate(item, employee, 30)"
                          >
                            <template slot="append">%</template>
                          </el-input>
                        </el-form-item>
                        <el-form-item class="cursorclass">
                          <el-checkbox size="small" v-model="employee.IsCalculatePassengerFlow">客流</el-checkbox>
                        </el-form-item>
                      </el-form>
                    </el-col>
                    <el-col :span="15" class="border_left border_right">
                      <el-row v-if="item.PayAmount > 0" class="padtp_10 padrt_10" :class="index != 0 ? 'border_top' : ''">
                        <el-col :span="8">
                          <el-form class="sale-ModifyPerformanceCommission-Handler" label-position="right" size="mini" label-width="120px">
                            <el-form-item label="现金业绩">
                              <span slot="label">
                                现金业绩
                                <el-popover placement="top-start" width="200" trigger="hover">
                                  <p>现金业绩 = 现金付款金额 x 员工现金业绩占比 x 业绩占比</p>
                                  <p>
                                    员工现金业绩占比参考值：
                                    <span v-if="employee.PerformancePayRate != null">{{ (employee.PerformancePayRate * 100) | toFixed | NumFormat }}%</span>
                                    <span v-else>无</span>
                                  </p>

                                  <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                </el-popover>
                              </span>
                              <el-input type="number" v-input-fixed class="input_type" style="width: 120px" v-model="employee.PayPerformance" v-enter-number2>
                                <template slot="append">元</template>
                              </el-input>
                            </el-form-item>
                          </el-form>
                        </el-col>
                        <el-col :span="8">
                          <el-form class="sale-ModifyPerformanceCommission-Handler" label-position="right" size="mini" label-width="150px">
                            <el-form-item label="现金比例提成">
                              <span slot="label">
                                现金比例提成
                                <el-popover placement="top-start" width="200" trigger="hover">
                                  <p>现金比例提成 = 现金业绩 x 现金比例</p>
                                  <p v-if="employee.PayRate != null">现金比例参考值：{{ employee.PayRate | toFixed | NumFormat }}%</p>
                                  <p v-else>现金比例参考值：无</p>
                                  <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                </el-popover>
                              </span>
                              <el-input
                                type="number"
                                v-input-fixed
                                class="input_type"
                                style="width: 100px"
                                v-model="employee.PayRateCommission"
                                v-enter-number2
                              >
                                <template slot="append">元</template>
                              </el-input>
                            </el-form-item>
                          </el-form>
                        </el-col>
                        <el-col :span="8">
                          <el-form class="sale-ModifyPerformanceCommission-Handler" label-position="right" size="mini" label-width="150px">
                            <el-form-item label="现金固定提成">
                              <span slot="label">
                                现金固定提成
                                <el-popover placement="top-start" width="200" trigger="hover">
                                  <p>现金固定提成 = （现金业绩 ÷ 购买金额）x 现金固定 x 数量</p>
                                  <p v-if="employee.PayFixed != null">现金固定参考值：¥ {{ employee.PayFixed | toFixed | NumFormat }}</p>
                                  <p v-else>现金固定参考值：无</p>
                                  <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                </el-popover>
                              </span>
                              <el-input
                                type="number"
                                v-input-fixed
                                class="input_type"
                                style="width: 100px"
                                v-model="employee.PayFixedCommission"
                                v-enter-number2
                              >
                                <template slot="append">元</template>
                              </el-input>
                            </el-form-item>
                          </el-form>
                        </el-col>
                      </el-row>
                      <el-row v-if="item.CardDeductionAmount > 0" class="padtp_10 padrt_10 border_top">
                        <el-col :span="8">
                          <el-form class="sale-ModifyPerformanceCommission-Handler" label-position="right" size="mini" label-width="120px">
                            <el-form-item label="卡抵扣业绩">
                              <span slot="label">
                                卡抵扣业绩
                                <el-popover placement="top-start" width="200" trigger="hover">
                                  <p>卡抵扣业绩 = 卡抵扣金额 x 员工卡抵扣业绩占比 x 业绩占比</p>
                                  <p>
                                    员工卡抵扣业绩占比参考值：
                                    <span v-if="employee.PerformanceSavingCardRate != null"
                                      >{{ (employee.PerformanceSavingCardRate * 100) | toFixed | NumFormat }}%</span
                                    >
                                    <span v-else>无</span>
                                  </p>

                                  <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                </el-popover>
                              </span>
                              <el-input
                                type="number"
                                v-input-fixed
                                class="input_type"
                                style="width: 120px"
                                v-model="employee.SavingCardPerformance"
                                v-enter-number2
                              >
                                <template slot="append">元</template>
                              </el-input>
                            </el-form-item>
                          </el-form>
                        </el-col>
                        <el-col :span="8">
                          <el-form class="sale-ModifyPerformanceCommission-Handler" label-position="right" size="mini" label-width="150px">
                            <el-form-item label="卡抵扣比例提成">
                              <span slot="label">
                                卡抵扣比例提成
                                <el-popover placement="top-start" width="200" trigger="hover">
                                  <p>卡抵扣比例提成 = 卡抵扣业绩 x 卡抵扣比例</p>
                                  <p v-if="employee.SavingCardRate != null">卡抵扣比例参考值：{{ employee.SavingCardRate | toFixed | NumFormat }}%</p>
                                  <p v-else>卡抵扣比例参考值：无</p>
                                  <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                </el-popover>
                              </span>
                              <el-input
                                type="number"
                                v-input-fixed
                                class="input_type"
                                style="width: 100px"
                                v-model="employee.SavingCardRateCommission"
                                v-enter-number2
                              >
                                <template slot="append">元</template>
                              </el-input>
                            </el-form-item>
                          </el-form>
                        </el-col>
                        <el-col :span="8">
                          <el-form class="sale-ModifyPerformanceCommission-Handler" label-position="right" size="mini" label-width="150px">
                            <el-form-item label="卡抵扣固定提成">
                              <span slot="label">
                                卡抵扣固定提成
                                <el-popover placement="top-start" width="200" trigger="hover">
                                  <p>卡抵扣固定提成 = （卡抵扣业绩 ÷ 购买金额）x 卡抵扣固定 x 数量</p>
                                  <p v-if="employee.SavingCardFixed != null">卡抵扣固定参考值：¥ {{ employee.SavingCardFixed | toFixed | NumFormat }}</p>
                                  <p v-else>卡抵扣固定参考值：无</p>
                                  <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                </el-popover>
                              </span>
                              <el-input
                                type="number"
                                v-input-fixed
                                class="input_type"
                                style="width: 100px"
                                v-model="employee.SavingCardFixedCommission"
                                v-enter-number2
                              >
                                <template slot="append">元</template>
                              </el-input>
                            </el-form-item>
                          </el-form>
                        </el-col>
                      </el-row>
                      <el-row v-if="item.CardDeductionLargessAmount > 0" class="padtp_10 padrt_10 border_top">
                        <el-col :span="8">
                          <el-form class="sale-ModifyPerformanceCommission-Handler" label-position="right" size="mini" label-width="120px">
                            <el-form-item label="赠送卡抵扣业绩">
                              <span slot="label">
                                赠送卡抵扣业绩
                                <el-popover placement="top-start" width="200" trigger="hover">
                                  <p>赠送卡抵扣业绩 = 赠送卡抵扣金额 x 赠送卡抵扣业绩占比 x 业绩占比</p>
                                  <p>
                                    员工赠送卡抵扣业绩占比参考值：
                                    <span v-if="employee.PerformanceSavingCardLargessRate != null"
                                      >{{ (employee.PerformanceSavingCardLargessRate * 100) | toFixed | NumFormat }}%</span
                                    >
                                    <span v-else>无</span>
                                  </p>

                                  <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                </el-popover>
                              </span>
                              <el-input type="number" v-input-fixed class="input_type" style="width: 120px" v-model="employee.SavingCardLargessPerformance">
                                <template slot="append">元</template>
                              </el-input>
                            </el-form-item>
                          </el-form>
                        </el-col>
                        <el-col :span="8">
                          <el-form class="sale-ModifyPerformanceCommission-Handler" label-position="right" size="mini" label-width="150px">
                            <el-form-item label="赠送卡抵扣比例提成">
                              <span slot="label">
                                赠送卡抵扣比例提成
                                <el-popover placement="top-start" width="200" trigger="hover">
                                  <p>赠送卡抵扣比例提成 = 赠送卡抵扣业绩 x 赠送卡抵扣比例</p>
                                  <p v-if="employee.SavingCardLargessRate != null">
                                    赠送卡抵扣比例参考值：{{ employee.SavingCardLargessRate | toFixed | NumFormat }}%
                                  </p>
                                  <p v-else>赠送卡抵扣比例参考值：无</p>
                                  <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                </el-popover>
                              </span>
                              <el-input
                                type="number"
                                v-input-fixed
                                class="input_type"
                                style="width: 100px"
                                v-model="employee.SavingCardLargessRateCommission"
                                v-enter-number2
                              >
                                <template slot="append">元</template>
                              </el-input>
                            </el-form-item>
                          </el-form>
                        </el-col>
                        <el-col :span="8">
                          <el-form class="sale-ModifyPerformanceCommission-Handler" label-position="right" size="mini" label-width="150px">
                            <el-form-item label="赠送卡抵扣固定提成">
                              <span slot="label">
                                赠送卡抵扣固定提成
                                <el-popover placement="top-start" width="200" trigger="hover">
                                  <p>赠送卡抵扣固定提成 = （赠送卡抵扣业绩 ÷ 购买金额）x 赠送卡抵扣固定 x 数量</p>
                                  <p v-if="employee.SavingCardLargessFixed != null">
                                    赠送卡抵扣固定参考值：¥ {{ employee.SavingCardLargessFixed | toFixed | NumFormat }}
                                  </p>
                                  <p v-else>赠送卡抵扣固定参考值：无</p>
                                  <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                </el-popover>
                              </span>
                              <el-input
                                type="number"
                                v-input-fixed
                                class="input_type"
                                style="width: 100px"
                                v-model="employee.SavingCardLargessFixedCommission"
                                v-enter-number2
                              >
                                <template slot="append">元</template>
                              </el-input>
                            </el-form-item>
                          </el-form>
                        </el-col>
                      </el-row>
                    </el-col>
                    <el-col v-if="!item.IsLargess" :span="4" class="padtp_10" :class="index != 0 ? 'border_top' : ''">
                      <el-form class="sale-ModifyPerformanceCommission-Handler" label-position="right" size="mini" label-width="90px">
                        <el-form-item label="无业绩奖励">
                          <span slot="label">
                            无业绩奖励
                            <el-popover placement="top-start" width="200" trigger="hover">
                              <p>无业绩奖励 = 无业绩奖励参考值 x 数量 x 经手人比例</p>
                              <p v-if="employee.SpecialBenefit != null">无业绩奖励参考值：¥ {{ employee.SpecialBenefit | toFixed | NumFormat }}</p>
                              <p v-else>无业绩奖励参考值：无</p>
                              <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                            </el-popover>
                          </span>
                          <el-input
                            type="number"
                            v-input-fixed
                            class="input_type"
                            style="width: 100px"
                            v-model="employee.SpecialBenefitCommission"
                            v-enter-number2
                          >
                            <template slot="append">元</template>
                          </el-input>
                        </el-form-item>
                      </el-form>
                    </el-col>
                  </el-row>
                </el-col>
              </el-row>
            </div>
          </div>
          <!-- 通用次卡 -->
          <div v-if="PerformanceInfo.GeneralCard.length > 0">
            <div v-for="(item, index) in PerformanceInfo.GeneralCard" :key="index">
              <el-row class="row_header border_right border_left">
                <el-col :span="5">通用次卡</el-col>
                <el-col :span="2">数量</el-col>
                <el-col :span="4">优惠金额</el-col>
                <el-col :span="3">购买金额</el-col>
                <el-col :span="5">支付金额</el-col>
                <el-col :span="5">欠款金额</el-col>
              </el-row>
              <el-row>
                <el-col :span="24" class="pad_10 border_right border_left border_bottom">
                  <el-col :span="24">
                    <el-col :span="5">
                      <div>
                        {{ item.GeneralCardName }}
                        <span v-if="item.Alias">({{ item.Alias }})</span>
                        <el-tag v-if="item.IsLargess" size="mini" type="danger" class="marlt_10">赠</el-tag>
                      </div>
                    </el-col>
                    <el-col :span="2">x {{ item.Quantity }}</el-col>
                    <el-col :span="4">
                      <span v-if="item.PreferentialTotalAmount < 0">+ ¥ {{ Math.abs(item.PreferentialTotalAmount) | toFixed | NumFormat }}</span>
                      <span v-else-if="item.PreferentialTotalAmount > 0">- ¥ {{ Math.abs(item.PreferentialTotalAmount) | toFixed | NumFormat }}</span>
                      <span v-else>¥ 0.00</span>
                    </el-col>
                    <el-col :span="3">¥ {{ item.IsLargess ? 0 : item.TotalAmount | toFixed | NumFormat }}</el-col>
                    <el-col :span="5">¥ {{ (parseFloat(item.PayAmount) + parseFloat(item.CardDeductionTotalAmount)) | toFixed | NumFormat }}</el-col>
                    <el-col :span="5">¥ {{ item.ArrearAmount | toFixed | NumFormat }}</el-col>
                  </el-col>
                  <el-col :span="24" class="martp_5">
                    <el-col :span="7">
                      <div class="color_red font_12">¥ {{ item.Price | toFixed | NumFormat }}</div>
                    </el-col>
                    <el-col :span="7">
                      <span class="color_gray font_12" v-if="item.PricePreferentialAmount != 0">
                        手动改价：
                        <span class="color_red" v-if="item.PricePreferentialAmount > 0">- ¥ {{ item.PricePreferentialAmount | toFixed | NumFormat }}</span>
                        <span class="color_green" v-else>+ ¥ {{ Math.abs(item.PricePreferentialAmount) | toFixed | NumFormat }}</span>
                      </span>
                      <span class="color_gray font_12" :class="item.PricePreferentialAmount != 0 ? 'marlt_15' : ''" v-if="item.CardPreferentialAmount > 0">
                        卡优惠：
                        <span class="color_red">- ¥ {{ parseFloat(item.CardPreferentialAmount) | toFixed | NumFormat }}</span>
                      </span>

                      <span class="color_gray font_12" :class="item.PricePreferentialAmount != 0 ? item.CardPreferentialAmount != 0 ? 'marlt_15':'' : ''" v-if="item.MemberPreferentialAmount > 0">
                        会员优惠：
                        <span class="color_red">- ¥ {{ parseFloat(item.MemberPreferentialAmount) | toFixed | NumFormat }}</span>
                      </span>
                    </el-col>
                    <el-col :span="10" :offset="item.PricePreferentialAmount == 0 && item.CardPreferentialAmount == 0 ? 7 : 0">
                      <span class="color_gray font_12" v-if="item.PayAmount > 0">现金金额：¥ {{ item.PayAmount | toFixed | NumFormat }}</span>
                      <span class="color_gray font_12" :class="item.PayAmount > 0 ? 'marlt_15' : ''" v-if="item.CardDeductionAmount > 0"
                        >卡抵扣：¥ {{ item.CardDeductionAmount | toFixed | NumFormat }}</span
                      >
                      <span
                        class="color_gray font_12"
                        :class="item.PricePreferentialAmount > 0 || item.PayAmount > 0 ? 'marlt_15' : ''"
                        v-if="item.CardDeductionLargessAmount > 0"
                        >赠送卡抵扣：¥ {{ item.CardDeductionLargessAmount | toFixed | NumFormat }}</span
                      >
                    </el-col>
                  </el-col>
                </el-col>
              </el-row>
              <el-row
                class="padlt_10 padrt_10 border_right border_left border_bottom font_12"
                v-for="handler in item.SaleBillHandler"
                :key="handler.SaleHandlerID"
              >
                <el-col :span="2" class="padtp_10 padbm_10">
                  <el-link class="font_12 line_26" type="primary" :underline="false" @click="employeeHandleClick(3, item)">{{
                    handler.SaleHandlerName
                  }}</el-link>
                </el-col>
                <el-col :span="22" class="border_left">
                  <el-row v-for="(employee, index) in handler.Employee" :key="employee.EmployeeID">
                    <el-col :span="5" class="padtp_10 padrt_10 " :class="index != 0 ? 'border_top' : ''">
                      <el-form class="sale-ModifyPerformanceCommission-Handler" label-position="right" size="mini" label-width="70px" :inline="true">
                        <el-form-item :label="`${employee.EmployeeName}`">
                          <el-input
                            type="number"
                            v-input-fixed
                            class="input_type"
                            style="width: 90px"
                            v-model="employee.Scale"
                            v-enter-number2
                            @input="changeSaleHandlerRate(item, employee, 40)"
                          >
                            <template slot="append">%</template>
                          </el-input>
                        </el-form-item>
                        <el-form-item class="cursorclass">
                          <el-checkbox size="small" v-model="employee.IsCalculatePassengerFlow">客流</el-checkbox>
                        </el-form-item>
                      </el-form>
                    </el-col>
                    <el-col :span="15" class="border_left border_right">
                      <el-row v-if="item.PayAmount > 0" class="padtp_10 padrt_10" :class="index != 0 ? 'border_top' : ''">
                        <el-col :span="8">
                          <el-form class="sale-ModifyPerformanceCommission-Handler" label-position="right" size="mini" label-width="120px">
                            <el-form-item label="现金业绩">
                              <span slot="label">
                                现金业绩
                                <el-popover placement="top-start" width="200" trigger="hover">
                                  <p>现金业绩 = 现金付款金额 x 员工现金业绩占比 x 业绩占比</p>
                                  <p>
                                    员工现金业绩占比参考值：
                                    <span v-if="employee.PerformancePayRate != null">{{ (employee.PerformancePayRate * 100) | toFixed | NumFormat }}%</span>
                                    <span v-else>无</span>
                                  </p>

                                  <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                </el-popover>
                              </span>
                              <el-input type="number" v-input-fixed class="input_type" style="width: 120px" v-model="employee.PayPerformance" v-enter-number2>
                                <template slot="append">元</template>
                              </el-input>
                            </el-form-item>
                          </el-form>
                        </el-col>
                        <el-col :span="8">
                          <el-form class="sale-ModifyPerformanceCommission-Handler" label-position="right" size="mini" label-width="150px">
                            <el-form-item label="现金比例提成">
                              <span slot="label">
                                现金比例提成
                                <el-popover placement="top-start" width="200" trigger="hover">
                                  <p>现金比例提成 = 现金业绩 x 现金比例</p>
                                  <p v-if="employee.PayRate != null">现金比例参考值：{{ employee.PayRate | toFixed | NumFormat }}%</p>
                                  <p v-else>现金比例参考值：无</p>
                                  <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                </el-popover>
                              </span>
                              <el-input
                                type="number"
                                v-input-fixed
                                class="input_type"
                                style="width: 100px"
                                v-model="employee.PayRateCommission"
                                v-enter-number2
                              >
                                <template slot="append">元</template>
                              </el-input>
                            </el-form-item>
                          </el-form>
                        </el-col>
                        <el-col :span="8">
                          <el-form class="sale-ModifyPerformanceCommission-Handler" label-position="right" size="mini" label-width="150px">
                            <el-form-item label="现金固定提成">
                              <span slot="label">
                                现金固定提成
                                <el-popover placement="top-start" width="200" trigger="hover">
                                  <p>现金固定提成 = （现金业绩 ÷ 购买金额）x 现金固定 x 数量</p>
                                  <p v-if="employee.PayFixed != null">现金固定参考值：¥ {{ employee.PayFixed | toFixed | NumFormat }}</p>
                                  <p v-else>现金固定参考值：无</p>
                                  <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                </el-popover>
                              </span>
                              <el-input
                                type="number"
                                v-input-fixed
                                class="input_type"
                                style="width: 100px"
                                v-model="employee.PayFixedCommission"
                                v-enter-number2
                              >
                                <template slot="append">元</template>
                              </el-input>
                            </el-form-item>
                          </el-form>
                        </el-col>
                      </el-row>
                      <el-row v-if="item.CardDeductionAmount > 0" class="padtp_10 padrt_10 border_top">
                        <el-col :span="8">
                          <el-form class="sale-ModifyPerformanceCommission-Handler" label-position="right" size="mini" label-width="120px">
                            <el-form-item label="卡抵扣业绩">
                              <span slot="label">
                                卡抵扣业绩
                                <el-popover placement="top-start" width="200" trigger="hover">
                                  <p>卡抵扣业绩 = 卡抵扣金额 x 员工卡抵扣业绩占比 x 业绩占比</p>
                                  <p>
                                    员工卡抵扣业绩占比参考值：
                                    <span v-if="employee.PerformanceSavingCardRate != null"
                                      >{{ (employee.PerformanceSavingCardRate * 100) | toFixed | NumFormat }}%</span
                                    >
                                    <span v-else>无</span>
                                  </p>

                                  <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                </el-popover>
                              </span>
                              <el-input
                                type="number"
                                v-input-fixed
                                class="input_type"
                                style="width: 120px"
                                v-model="employee.SavingCardPerformance"
                                v-enter-number2
                              >
                                <template slot="append">元</template>
                              </el-input>
                            </el-form-item>
                          </el-form>
                        </el-col>
                        <el-col :span="8">
                          <el-form class="sale-ModifyPerformanceCommission-Handler" label-position="right" size="mini" label-width="150px">
                            <el-form-item label="卡抵扣比例提成">
                              <span slot="label">
                                卡抵扣比例提成
                                <el-popover placement="top-start" width="200" trigger="hover">
                                  <p>卡抵扣比例提成 = 卡抵扣业绩 x 卡抵扣比例</p>
                                  <p v-if="employee.SavingCardRate != null">卡抵扣比例参考值：{{ employee.SavingCardRate | toFixed | NumFormat }}%</p>
                                  <p v-else>卡抵扣比例参考值：无</p>
                                  <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                </el-popover>
                              </span>
                              <el-input
                                type="number"
                                v-input-fixed
                                class="input_type"
                                style="width: 100px"
                                v-model="employee.SavingCardRateCommission"
                                v-enter-number2
                              >
                                <template slot="append">元</template>
                              </el-input>
                            </el-form-item>
                          </el-form>
                        </el-col>
                        <el-col :span="8">
                          <el-form class="sale-ModifyPerformanceCommission-Handler" label-position="right" size="mini" label-width="150px">
                            <el-form-item label="卡抵扣固定提成">
                              <span slot="label">
                                卡抵扣固定提成
                                <el-popover placement="top-start" width="200" trigger="hover">
                                  <p>卡抵扣固定提成 = （卡抵扣业绩 ÷ 购买金额）x 卡抵扣固定 x 数量</p>
                                  <p v-if="employee.SavingCardFixed != null">卡抵扣固定参考值：¥ {{ employee.SavingCardFixed | toFixed | NumFormat }}</p>
                                  <p v-else>卡抵扣固定参考值：无</p>
                                  <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                </el-popover>
                              </span>
                              <el-input
                                type="number"
                                v-input-fixed
                                class="input_type"
                                style="width: 100px"
                                v-model="employee.SavingCardFixedCommission"
                                v-enter-number2
                              >
                                <template slot="append">元</template>
                              </el-input>
                            </el-form-item>
                          </el-form>
                        </el-col>
                      </el-row>
                      <el-row v-if="item.CardDeductionLargessAmount > 0" class="padtp_10 padrt_10 border_top">
                        <el-col :span="8">
                          <el-form class="sale-ModifyPerformanceCommission-Handler" label-position="right" size="mini" label-width="120px">
                            <el-form-item label="赠送卡抵扣业绩">
                              <span slot="label">
                                赠送卡抵扣业绩
                                <el-popover placement="top-start" width="200" trigger="hover">
                                  <p>赠送卡抵扣业绩 = 赠送卡抵扣金额 x 赠送卡抵扣业绩占比 x 业绩占比</p>
                                  <p>
                                    员工赠送卡抵扣业绩占比参考值：
                                    <span v-if="employee.PerformanceSavingCardLargessRate != null"
                                      >{{ (employee.PerformanceSavingCardLargessRate * 100) | toFixed | NumFormat }}%</span
                                    >
                                    <span v-else>无</span>
                                  </p>

                                  <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                </el-popover>
                              </span>
                              <el-input type="number" v-input-fixed class="input_type" style="width: 120px" v-model="employee.SavingCardLargessPerformance">
                                <template slot="append">元</template>
                              </el-input>
                            </el-form-item>
                          </el-form>
                        </el-col>
                        <el-col :span="8">
                          <el-form class="sale-ModifyPerformanceCommission-Handler" label-position="right" size="mini" label-width="150px">
                            <el-form-item label="赠送卡抵扣比例提成">
                              <span slot="label">
                                赠送卡抵扣比例提成
                                <el-popover placement="top-start" width="200" trigger="hover">
                                  <p>赠送卡抵扣比例提成 = 赠送卡抵扣业绩 x 赠送卡抵扣比例</p>
                                  <p v-if="employee.SavingCardLargessRate != null">
                                    赠送卡抵扣比例参考值：{{ employee.SavingCardLargessRate | toFixed | NumFormat }}%
                                  </p>
                                  <p v-else>赠送卡抵扣比例参考值：无</p>
                                  <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                </el-popover>
                              </span>
                              <el-input
                                type="number"
                                v-input-fixed
                                class="input_type"
                                style="width: 100px"
                                v-model="employee.SavingCardLargessRateCommission"
                                v-enter-number2
                              >
                                <template slot="append">元</template>
                              </el-input>
                            </el-form-item>
                          </el-form>
                        </el-col>
                        <el-col :span="8">
                          <el-form class="sale-ModifyPerformanceCommission-Handler" label-position="right" size="mini" label-width="150px">
                            <el-form-item label="赠送卡抵扣固定提成">
                              <span slot="label">
                                赠送卡抵扣固定提成
                                <el-popover placement="top-start" width="200" trigger="hover">
                                  <p>赠送卡抵扣固定提成 = （赠送卡抵扣业绩 ÷ 购买金额）x 赠送卡抵扣固定 x 数量</p>
                                  <p v-if="employee.SavingCardLargessFixed != null">
                                    赠送卡抵扣固定参考值：¥ {{ employee.SavingCardLargessFixed | toFixed | NumFormat }}
                                  </p>
                                  <p v-else>赠送卡抵扣固定参考值：无</p>
                                  <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                </el-popover>
                              </span>
                              <el-input
                                type="number"
                                v-input-fixed
                                class="input_type"
                                style="width: 100px"
                                v-model="employee.SavingCardLargessFixedCommission"
                                v-enter-number2
                              >
                                <template slot="append">元</template>
                              </el-input>
                            </el-form-item>
                          </el-form>
                        </el-col>
                      </el-row>
                    </el-col>
                    <el-col v-if="!item.IsLargess" :span="4" class="padtp_10" :class="index != 0 ? 'border_top' : ''">
                      <el-form class="sale-ModifyPerformanceCommission-Handler" label-position="right" size="mini" label-width="90px">
                        <el-form-item label="无业绩奖励">
                          <span slot="label">
                            无业绩奖励
                            <el-popover placement="top-start" width="200" trigger="hover">
                              <p>无业绩奖励 = 无业绩奖励参考值 x 数量 x 经手人比例</p>
                              <p v-if="employee.SpecialBenefit != null">无业绩奖励参考值：¥ {{ employee.SpecialBenefit | toFixed | NumFormat }}</p>
                              <p v-else>无业绩奖励参考值：无</p>
                              <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                            </el-popover>
                          </span>
                          <el-input
                            type="number"
                            v-input-fixed
                            class="input_type"
                            style="width: 100px"
                            v-model="employee.SpecialBenefitCommission"
                            v-enter-number2
                          >
                            <template slot="append">元</template>
                          </el-input>
                        </el-form-item>
                      </el-form>
                    </el-col>
                  </el-row>
                </el-col>
              </el-row>
            </div>
          </div>
          <!-- 产品 -->
          <div v-if="PerformanceInfo.Product.length > 0">
            <div v-for="(item, index) in PerformanceInfo.Product" :key="index">
              <el-row class="row_header border_right border_left">
                <el-col :span="5">产品</el-col>
                <el-col :span="2">数量</el-col>
                <el-col :span="4">优惠金额</el-col>
                <el-col :span="3">购买金额</el-col>
                <el-col :span="5">支付金额</el-col>
                <el-col :span="5">欠款金额</el-col>
              </el-row>
              <el-row>
                <el-col :span="24" class="pad_10 border_right border_left border_bottom">
                  <el-col :span="24">
                    <el-col :span="5">
                      <div>
                        {{ item.ProductName }}
                        <span v-if="item.Alias">({{ item.Alias }})</span>
                        <el-tag v-if="item.IsLargess" size="mini" type="danger" class="marlt_10">赠</el-tag>
                      </div>
                    </el-col>
                    <el-col :span="2">x {{ item.Quantity }}</el-col>
                    <el-col :span="4">
                      <span v-if="item.PreferentialTotalAmount < 0">+ ¥ {{ Math.abs(item.PreferentialTotalAmount) | toFixed | NumFormat }}</span>
                      <span v-else-if="item.PreferentialTotalAmount > 0">- ¥ {{ Math.abs(item.PreferentialTotalAmount) | toFixed | NumFormat }}</span>
                      <span v-else>¥ 0.00</span>
                    </el-col>
                    <el-col :span="3">¥ {{ item.IsLargess ? 0 : item.TotalAmount | toFixed | NumFormat }}</el-col>
                    <el-col :span="5">¥ {{ (parseFloat(item.PayAmount) + parseFloat(item.CardDeductionTotalAmount)) | toFixed | NumFormat }}</el-col>
                    <el-col :span="5">¥ {{ item.ArrearAmount | toFixed | NumFormat }}</el-col>
                  </el-col>
                  <el-col :span="24" class="martp_5">
                    <el-col :span="7">
                      <div class="color_red font_12">¥ {{ item.Price | toFixed | NumFormat }}</div>
                    </el-col>
                    <el-col :span="7">
                      <span class="color_gray font_12" v-if="item.PricePreferentialAmount != 0">
                        手动改价：
                        <span class="color_red" v-if="item.PricePreferentialAmount > 0">- ¥ {{ item.PricePreferentialAmount | toFixed | NumFormat }}</span>
                        <span class="color_green" v-else>+ ¥ {{ Math.abs(item.PricePreferentialAmount) | toFixed | NumFormat }}</span>
                      </span>
                      <span class="color_gray font_12" :class="item.PricePreferentialAmount != 0 ? 'marlt_15' : ''" v-if="item.CardPreferentialAmount > 0">
                        卡优惠：
                        <span class="color_red">- ¥ {{ parseFloat(item.CardPreferentialAmount) | toFixed | NumFormat }}</span>
                      </span>

                      <span class="color_gray font_12" :class="item.PricePreferentialAmount != 0 ? item.CardPreferentialAmount != 0 ? 'marlt_15':'' : ''" v-if="item.MemberPreferentialAmount > 0">
                        会员优惠：
                        <span class="color_red">- ¥ {{ parseFloat(item.MemberPreferentialAmount) | toFixed | NumFormat }}</span>
                      </span>
                    </el-col>
                    <el-col :span="10" :offset="item.PricePreferentialAmount == 0 && item.CardPreferentialAmount == 0 ? 7 : 0">
                      <span class="color_gray font_12" v-if="item.PayAmount > 0">现金金额：¥ {{ item.PayAmount | toFixed | NumFormat }}</span>
                      <span class="color_gray font_12" :class="item.PayAmount > 0 ? 'marlt_15' : ''" v-if="item.CardDeductionAmount > 0"
                        >卡抵扣：¥ {{ item.CardDeductionAmount | toFixed | NumFormat }}</span
                      >
                      <span
                        class="color_gray font_12"
                        :class="item.PricePreferentialAmount > 0 || item.PayAmount > 0 ? 'marlt_15' : ''"
                        v-if="item.CardDeductionLargessAmount > 0"
                        >赠送卡抵扣：¥ {{ item.CardDeductionLargessAmount | toFixed | NumFormat }}</span
                      >
                    </el-col>
                  </el-col>
                </el-col>
              </el-row>
              <el-row
                class="padlt_10 padrt_10 border_right border_left border_bottom font_12"
                v-for="handler in item.SaleBillHandler"
                :key="handler.SaleHandlerID"
              >
                <el-col :span="2" class="padtp_10 padbm_10">
                  <el-link class="font_12 line_26" type="primary" :underline="false" @click="employeeHandleClick(2, item)">{{
                    handler.SaleHandlerName
                  }}</el-link>
                </el-col>
                <el-col :span="22" class="border_left">
                  <el-row v-for="(employee, index) in handler.Employee" :key="employee.EmployeeID">
                    <el-col :span="5" class="padtp_10 padrt_10 " :class="index != 0 ? 'border_top' : ''">
                      <el-form class="sale-ModifyPerformanceCommission-Handler" label-position="right" size="mini" label-width="70px" :inline="true">
                        <el-form-item :label="`${employee.EmployeeName}`">
                          <el-input
                            type="number"
                            v-input-fixed
                            class="input_type"
                            style="width: 90px"
                            v-model="employee.Scale"
                            v-enter-number2
                            @input="changeSaleHandlerRate(item, employee, 50)"
                          >
                            <template slot="append">%</template>
                          </el-input>
                        </el-form-item>
                        <el-form-item class="cursorclass">
                          <el-checkbox size="small" v-model="employee.IsCalculatePassengerFlow">客流</el-checkbox>
                        </el-form-item>
                      </el-form>
                    </el-col>
                    <el-col :span="15" class="border_left border_right">
                      <el-row v-if="item.PayAmount > 0" class="padtp_10 padrt_10" :class="index != 0 ? 'border_top' : ''">
                        <el-col :span="8">
                          <el-form class="sale-ModifyPerformanceCommission-Handler" label-position="right" size="mini" label-width="120px">
                            <el-form-item label="现金业绩">
                              <span slot="label">
                                现金业绩
                                <el-popover placement="top-start" width="200" trigger="hover">
                                  <p>现金业绩 = 现金付款金额 x 员工现金业绩占比 x 业绩占比</p>
                                  <p>
                                    员工现金业绩占比参考值：
                                    <span v-if="employee.PerformancePayRate != null">{{ (employee.PerformancePayRate * 100) | toFixed | NumFormat }}%</span>
                                    <span v-else>无</span>
                                  </p>

                                  <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                </el-popover>
                              </span>
                              <el-input type="number" v-input-fixed class="input_type" style="width: 120px" v-model="employee.PayPerformance" v-enter-number2>
                                <template slot="append">元</template>
                              </el-input>
                            </el-form-item>
                          </el-form>
                        </el-col>
                        <el-col :span="8">
                          <el-form class="sale-ModifyPerformanceCommission-Handler" label-position="right" size="mini" label-width="150px">
                            <el-form-item label="现金比例提成">
                              <span slot="label">
                                现金比例提成
                                <el-popover placement="top-start" width="200" trigger="hover">
                                  <p>现金比例提成 = 现金业绩 x 现金比例</p>
                                  <p v-if="employee.PayRate != null">现金比例参考值：{{ employee.PayRate | toFixed | NumFormat }}%</p>
                                  <p v-else>现金比例参考值：无</p>
                                  <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                </el-popover>
                              </span>
                              <el-input
                                type="number"
                                v-input-fixed
                                class="input_type"
                                style="width: 100px"
                                v-model="employee.PayRateCommission"
                                v-enter-number2
                              >
                                <template slot="append">元</template>
                              </el-input>
                            </el-form-item>
                          </el-form>
                        </el-col>
                        <el-col :span="8">
                          <el-form class="sale-ModifyPerformanceCommission-Handler" label-position="right" size="mini" label-width="150px">
                            <el-form-item label="现金固定提成">
                              <span slot="label">
                                现金固定提成
                                <el-popover placement="top-start" width="200" trigger="hover">
                                  <p>现金固定提成 = （现金业绩 ÷ 购买金额）x 现金固定 x 数量</p>
                                  <p v-if="employee.PayFixed != null">现金固定参考值：¥ {{ employee.PayFixed | toFixed | NumFormat }}</p>
                                  <p v-else>现金固定参考值：无</p>
                                  <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                </el-popover>
                              </span>
                              <el-input
                                type="number"
                                v-input-fixed
                                class="input_type"
                                style="width: 100px"
                                v-model="employee.PayFixedCommission"
                                v-enter-number2
                              >
                                <template slot="append">元</template>
                              </el-input>
                            </el-form-item>
                          </el-form>
                        </el-col>
                      </el-row>
                      <el-row v-if="item.CardDeductionAmount > 0" class="padtp_10 padrt_10 border_top">
                        <el-col :span="8">
                          <el-form class="sale-ModifyPerformanceCommission-Handler" label-position="right" size="mini" label-width="120px">
                            <el-form-item label="卡抵扣业绩">
                              <span slot="label">
                                卡抵扣业绩
                                <el-popover placement="top-start" width="200" trigger="hover">
                                  <p>卡抵扣业绩 = 卡抵扣金额 x 员工卡抵扣业绩占比 x 业绩占比</p>
                                  <p>
                                    员工卡抵扣业绩占比参考值：
                                    <span v-if="employee.PerformanceSavingCardRate != null"
                                      >{{ (employee.PerformanceSavingCardRate * 100) | toFixed | NumFormat }}%</span
                                    >
                                    <span v-else>无</span>
                                  </p>

                                  <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                </el-popover>
                              </span>
                              <el-input
                                type="number"
                                v-input-fixed
                                class="input_type"
                                style="width: 120px"
                                v-model="employee.SavingCardPerformance"
                                v-enter-number2
                              >
                                <template slot="append">元</template>
                              </el-input>
                            </el-form-item>
                          </el-form>
                        </el-col>
                        <el-col :span="8">
                          <el-form class="sale-ModifyPerformanceCommission-Handler" label-position="right" size="mini" label-width="150px">
                            <el-form-item label="卡抵扣比例提成">
                              <span slot="label">
                                卡抵扣比例提成
                                <el-popover placement="top-start" width="200" trigger="hover">
                                  <p>卡抵扣比例提成 = 卡抵扣业绩 x 卡抵扣比例</p>
                                  <p v-if="employee.SavingCardRate != null">卡抵扣比例参考值：{{ employee.SavingCardRate | toFixed | NumFormat }}%</p>
                                  <p v-else>卡抵扣比例参考值：无</p>
                                  <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                </el-popover>
                              </span>
                              <el-input
                                type="number"
                                v-input-fixed
                                class="input_type"
                                style="width: 100px"
                                v-model="employee.SavingCardRateCommission"
                                v-enter-number2
                              >
                                <template slot="append">元</template>
                              </el-input>
                            </el-form-item>
                          </el-form>
                        </el-col>
                        <el-col :span="8">
                          <el-form class="sale-ModifyPerformanceCommission-Handler" label-position="right" size="mini" label-width="150px">
                            <el-form-item label="卡抵扣固定提成">
                              <span slot="label">
                                卡抵扣固定提成
                                <el-popover placement="top-start" width="200" trigger="hover">
                                  <p>卡抵扣固定提成 = （卡抵扣业绩 ÷ 购买金额）x 卡抵扣固定 x 数量</p>
                                  <p v-if="employee.SavingCardFixed != null">卡抵扣固定参考值：¥ {{ employee.SavingCardFixed | toFixed | NumFormat }}</p>
                                  <p v-else>卡抵扣固定参考值：无</p>
                                  <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                </el-popover>
                              </span>
                              <el-input
                                type="number"
                                v-input-fixed
                                class="input_type"
                                style="width: 100px"
                                v-model="employee.SavingCardFixedCommission"
                                v-enter-number2
                              >
                                <template slot="append">元</template>
                              </el-input>
                            </el-form-item>
                          </el-form>
                        </el-col>
                      </el-row>
                      <el-row v-if="item.CardDeductionLargessAmount > 0" class="padtp_10 padrt_10 border_top">
                        <el-col :span="8">
                          <el-form class="sale-ModifyPerformanceCommission-Handler" label-position="right" size="mini" label-width="120px">
                            <el-form-item label="赠送卡抵扣业绩">
                              <span slot="label">
                                赠送卡抵扣业绩
                                <el-popover placement="top-start" width="200" trigger="hover">
                                  <p>赠送卡抵扣业绩 = 赠送卡抵扣金额 x 赠送卡抵扣业绩占比 x 业绩占比</p>
                                  <p>
                                    员工赠送卡抵扣业绩占比参考值：
                                    <span v-if="employee.PerformanceSavingCardLargessRate != null"
                                      >{{ (employee.PerformanceSavingCardLargessRate * 100) | toFixed | NumFormat }}%</span
                                    >
                                    <span v-else>无</span>
                                  </p>

                                  <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                </el-popover>
                              </span>
                              <el-input type="number" v-input-fixed class="input_type" style="width: 120px" v-model="employee.SavingCardLargessPerformance">
                                <template slot="append">元</template>
                              </el-input>
                            </el-form-item>
                          </el-form>
                        </el-col>
                        <el-col :span="8">
                          <el-form class="sale-ModifyPerformanceCommission-Handler" label-position="right" size="mini" label-width="150px">
                            <el-form-item label="赠送卡抵扣比例提成">
                              <span slot="label">
                                赠送卡抵扣比例提成
                                <el-popover placement="top-start" width="200" trigger="hover">
                                  <p>赠送卡抵扣比例提成 = 赠送卡抵扣业绩 x 赠送卡抵扣比例</p>
                                  <p v-if="employee.SavingCardLargessRate != null">
                                    赠送卡抵扣比例参考值：{{ employee.SavingCardLargessRate | toFixed | NumFormat }}%
                                  </p>
                                  <p v-else>赠送卡抵扣比例参考值：无</p>
                                  <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                </el-popover>
                              </span>
                              <el-input
                                type="number"
                                v-input-fixed
                                class="input_type"
                                style="width: 100px"
                                v-model="employee.SavingCardLargessRateCommission"
                                v-enter-number2
                              >
                                <template slot="append">元</template>
                              </el-input>
                            </el-form-item>
                          </el-form>
                        </el-col>
                        <el-col :span="8">
                          <el-form class="sale-ModifyPerformanceCommission-Handler" label-position="right" size="mini" label-width="150px">
                            <el-form-item label="赠送卡抵扣固定提成">
                              <span slot="label">
                                赠送卡抵扣固定提成
                                <el-popover placement="top-start" width="200" trigger="hover">
                                  <p>赠送卡抵扣固定提成 = （赠送卡抵扣业绩 ÷ 购买金额）x 赠送卡抵扣固定 x 数量</p>
                                  <p v-if="employee.SavingCardLargessFixed != null">
                                    赠送卡抵扣固定参考值：¥ {{ employee.SavingCardLargessFixed | toFixed | NumFormat }}
                                  </p>
                                  <p v-else>赠送卡抵扣固定参考值：无</p>
                                  <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                </el-popover>
                              </span>
                              <el-input
                                type="number"
                                v-input-fixed
                                class="input_type"
                                style="width: 100px"
                                v-model="employee.SavingCardLargessFixedCommission"
                                v-enter-number2
                              >
                                <template slot="append">元</template>
                              </el-input>
                            </el-form-item>
                          </el-form>
                        </el-col>
                      </el-row>
                    </el-col>
                    <el-col v-if="!item.IsLargess" :span="4" class="padtp_10" :class="index != 0 ? 'border_top' : ''">
                      <el-form class="sale-ModifyPerformanceCommission-Handler" label-position="right" size="mini" label-width="90px">
                        <el-form-item label="无业绩奖励">
                          <span slot="label">
                            无业绩奖励
                            <el-popover placement="top-start" width="200" trigger="hover">
                              <p>无业绩奖励 = 无业绩奖励参考值 x 数量 x 经手人比例</p>
                              <p v-if="employee.SpecialBenefit != null">无业绩奖励参考值：¥ {{ employee.SpecialBenefit | toFixed | NumFormat }}</p>
                              <p v-else>无业绩奖励参考值：无</p>
                              <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                            </el-popover>
                          </span>
                          <el-input
                            type="number"
                            v-input-fixed
                            class="input_type"
                            style="width: 100px"
                            v-model="employee.SpecialBenefitCommission"
                            v-enter-number2
                          >
                            <template slot="append">元</template>
                          </el-input>
                        </el-form-item>
                      </el-form>
                    </el-col>
                  </el-row>
                </el-col>
              </el-row>
            </div>
          </div>
          <!-- 套餐卡 -->
          <div v-if="PerformanceInfo.PackageCard.length > 0">
            <div v-for="(packageCard, index) in PerformanceInfo.PackageCard" :key="index">
              <el-row class="row_header border_right border_left">
                <el-col :span="5">套餐卡</el-col>
                <el-col :span="2">数量</el-col>
                <el-col :span="4">优惠金额</el-col>
                <el-col :span="3">购买金额</el-col>
                <el-col :span="5">支付金额</el-col>
                <el-col :span="5">欠款金额</el-col>
              </el-row>
              <el-row>
                <el-col :span="24" class="pad_10 border_right border_left border_bottom">
                  <el-col :span="24">
                    <el-col :span="5">
                      <div>
                        {{ packageCard.PackageCardName }}
                        <span v-if="packageCard.Alias">({{ packageCard.Alias }})</span>
                        <el-tag v-if="packageCard.IsLargess" size="mini" type="danger" class="marlt_10">赠</el-tag>
                      </div>
                    </el-col>
                    <el-col :span="2">x {{ packageCard.Quantity }}</el-col>
                    <el-col :span="4">
                      <span v-if="packageCard.PreferentialTotalAmount < 0">+ ¥ {{ Math.abs(packageCard.PreferentialTotalAmount) | toFixed | NumFormat }}</span>
                      <span v-else-if="packageCard.PreferentialTotalAmount > 0"
                        >- ¥ {{ Math.abs(packageCard.PreferentialTotalAmount) | toFixed | NumFormat }}</span
                      >
                      <span v-else>¥ 0.00</span>
                    </el-col>
                    <el-col :span="3">¥ {{ packageCard.IsLargess ? 0 : packageCard.TotalAmount | toFixed | NumFormat }}</el-col>
                    <el-col :span="5"
                      >¥ {{ (parseFloat(packageCard.PayAmount) + parseFloat(packageCard.CardDeductionTotalAmount)) | toFixed | NumFormat }}</el-col
                    >
                    <el-col :span="5">¥ {{ packageCard.ArrearAmount | toFixed | NumFormat }}</el-col>
                  </el-col>
                  <el-col :span="24" class="martp_5">
                    <el-col :span="7">
                      <div class="color_red font_12">¥ {{ packageCard.Price | toFixed | NumFormat }}</div>
                    </el-col>
                    <el-col :span="7">
                      <span class="color_gray font_12" v-if="packageCard.PricePreferentialAmount != 0">
                        手动改价：
                        <span class="color_red" v-if="packageCard.PricePreferentialAmount > 0"
                          >- ¥ {{ packageCard.PricePreferentialAmount | toFixed | NumFormat }}</span
                        >
                        <span class="color_green" v-else>+ ¥ {{ Math.abs(packageCard.PricePreferentialAmount) | toFixed | NumFormat }}</span>
                      </span>
                      <span
                        class="color_gray font_12"
                        :class="packageCard.PricePreferentialAmount != 0 ? 'marlt_15' : ''"
                        v-if="packageCard.CardPreferentialAmount > 0"
                      >
                        卡优惠：
                        <span class="color_red">- ¥ {{ parseFloat(packageCard.CardPreferentialAmount) | toFixed | NumFormat }}</span>
                      </span>

                      <span class="color_gray font_12" :class="packageCard.PricePreferentialAmount != 0 ? packageCard.CardPreferentialAmount != 0 ? 'marlt_15':'' : ''" v-if="packageCard.MemberPreferentialAmount > 0">
                        会员优惠：
                        <span class="color_red">- ¥ {{ parseFloat(packageCard.MemberPreferentialAmount) | toFixed | NumFormat }}</span>
                      </span>

                    </el-col>
                    <el-col :span="10" :offset="packageCard.PricePreferentialAmount == 0 && packageCard.CardPreferentialAmount == 0 ? 7 : 0">
                      <span class="color_gray font_12" v-if="packageCard.PayAmount > 0">现金金额：¥ {{ packageCard.PayAmount | toFixed | NumFormat }}</span>
                      <span class="color_gray font_12" :class="packageCard.PayAmount > 0 ? 'marlt_15' : ''" v-if="packageCard.CardDeductionAmount > 0"
                        >卡抵扣：¥ {{ packageCard.CardDeductionAmount | toFixed | NumFormat }}</span
                      >
                      <span
                        class="color_gray font_12"
                        :class="packageCard.PricePreferentialAmount > 0 || packageCard.PayAmount > 0 ? 'marlt_15' : ''"
                        v-if="packageCard.CardDeductionLargessAmount > 0"
                        >赠送卡抵扣：¥ {{ packageCard.CardDeductionLargessAmount | toFixed | NumFormat }}</span
                      >
                    </el-col>
                  </el-col>
                </el-col>
              </el-row>
              <el-row
                class="padlt_10 padrt_10 border_right border_left border_bottom font_12"
                v-for="handler in packageCard.SaleBillHandler"
                :key="handler.SaleHandlerID"
              >
                <el-col :span="2" class="padtp_10 padbm_10">
                  <el-link class="font_12 line_26" type="primary" :underline="false" @click="employeeHandleClick(6, packageCard)">{{
                    handler.SaleHandlerName
                  }}</el-link>
                </el-col>
                <el-col :span="22" class="border_left">
                  <el-row v-for="employee in handler.Employee" :key="employee.EmployeeID">
                    <el-form class="sale-ModifyPerformanceCommission-Handler" :inline="true" label-position="right" size="mini" label-width="70px" >
                      <el-form-item class="padtp_10" :label="`${employee.EmployeeName}`">
                        <el-input
                          type="number"
                          v-input-fixed
                          class="input_type"
                          style="width: 90px"
                          v-model="employee.Scale"
                          v-enter-number2
                          @input="changePackageSaleHandlerRate(packageCard, handler, employee)"
                        >
                          <template slot="append">%</template>
                        </el-input>
                      </el-form-item>
                      <el-form-item class="padtp_10 cursorclass">
                        <el-checkbox size="small" v-model="employee.IsCalculatePassengerFlow">客流</el-checkbox>
                      </el-form-item>
                    </el-form>
                  </el-row>
                </el-col>
              </el-row>
              <!-- 套餐卡-产品 -->
              <div v-if="packageCard.Product.length > 0">
                <div v-for="(item, index) in packageCard.Product" :key="index">
                  <el-row class="row_header_package_detail border_right border_left">
                    <el-col :span="5">套餐卡产品</el-col>
                    <el-col :span="2">数量</el-col>
                    <el-col :span="4">优惠金额</el-col>
                    <el-col :span="3">购买金额</el-col>
                    <el-col :span="5">支付金额</el-col>
                    <el-col :span="5">欠款金额</el-col>
                  </el-row>
                  <el-row>
                    <el-col :span="24" class="pad_10 border_right border_left border_bottom">
                      <el-col :span="24">
                        <el-col :span="5">
                          <div>
                            {{ item.ProductName }}
                            <span v-if="item.Alias">({{ item.Alias }})</span>
                            <el-tag v-if="item.IsLargess" size="mini" type="danger" class="marlt_10">赠</el-tag>
                          </div>
                        </el-col>
                        <el-col :span="2">x {{ item.Quantity * packageCard.Quantity }}</el-col>
                        <el-col :span="4">
                          <span v-if="item.PreferentialTotalAmount < 0">+ ¥ {{ Math.abs(item.PreferentialTotalAmount) | toFixed | NumFormat }}</span>
                          <span v-else-if="item.PreferentialTotalAmount > 0">- ¥ {{ Math.abs(item.PreferentialTotalAmount) | toFixed | NumFormat }}</span>
                          <span v-else>¥ 0.00</span>
                        </el-col>
                        <el-col :span="3">¥ {{ item.IsLargess ? 0 : item.TotalAmount | toFixed | NumFormat }}</el-col>
                        <el-col :span="5">¥ {{ (parseFloat(item.PayAmount) + parseFloat(item.CardDeductionTotalAmount)) | toFixed | NumFormat }}</el-col>
                        <el-col :span="5">¥ {{ item.ArrearAmount | toFixed | NumFormat }}</el-col>
                      </el-col>
                      <el-col :span="24" class="martp_5">
                        <el-col :span="7">
                          <div class="color_red font_12">¥ {{ item.Price | toFixed | NumFormat }}</div>
                        </el-col>
                        <el-col :span="7">
                          <span class="color_gray font_12" v-if="item.PricePreferentialAmount != 0">
                            手动改价：
                            <span class="color_red" v-if="item.PricePreferentialAmount > 0">- ¥ {{ item.PricePreferentialAmount | toFixed | NumFormat }}</span>
                            <span class="color_green" v-else>+ ¥ {{ Math.abs(item.PricePreferentialAmount) | toFixed | NumFormat }}</span>
                          </span>
                          <span class="color_gray font_12" :class="item.PricePreferentialAmount != 0 ? 'marlt_15' : ''" v-if="item.CardPreferentialAmount > 0">
                            卡优惠：
                            <span class="color_red">- ¥ {{ parseFloat(item.CardPreferentialAmount) | toFixed | NumFormat }}</span>
                          </span>

                          <span class="color_gray font_12" :class="item.PricePreferentialAmount != 0 ? item.CardPreferentialAmount != 0 ? 'marlt_15':'' : ''" v-if="item.MemberPreferentialAmount > 0">
                            会员优惠：
                            <span class="color_red">- ¥ {{ parseFloat(item.MemberPreferentialAmount) | toFixed | NumFormat }}</span>
                          </span>

                        </el-col>
                        <el-col :span="10" :offset="item.PricePreferentialAmount == 0 && item.CardPreferentialAmount == 0 ? 7 : 0">
                          <span class="color_gray font_12" v-if="item.PayAmount > 0">现金金额：¥ {{ item.PayAmount | toFixed | NumFormat }}</span>
                          <span class="color_gray font_12" :class="item.PayAmount > 0 ? 'marlt_15' : ''" v-if="item.CardDeductionAmount > 0"
                            >卡抵扣：¥ {{ item.CardDeductionAmount | toFixed | NumFormat }}</span
                          >
                          <span
                            class="color_gray font_12"
                            :class="item.PricePreferentialAmount > 0 || item.PayAmount > 0 ? 'marlt_15' : ''"
                            v-if="item.CardDeductionLargessAmount > 0"
                            >赠送卡抵扣：¥ {{ item.CardDeductionLargessAmount | toFixed | NumFormat }}</span
                          >
                        </el-col>
                      </el-col>
                    </el-col>
                  </el-row>
                  <div>
                    <el-row
                      class="padlt_10 padrt_10 border_right border_left border_bottom font_12"
                      v-for="handler in item.SaleBillHandler"
                      :key="handler.SaleHandlerID"
                    >
                      <el-col :span="2" class="padtp_10 padbm_10 font_12 line_26">{{ handler.SaleHandlerName }}</el-col>
                      <el-col :span="22" class="border_left">
                        <el-row v-for="(employee, index) in handler.Employee" :key="employee.EmployeeID">
                          <el-col :span="4" class="padtp_10 padrt_10" :class="index != 0 ? 'border_top' : ''">
                            <el-form class="sale-ModifyPerformanceCommission-Handler" label-position="right" size="mini" label-width="120px" label-suffix=":">
                              <el-form-item :label="`${employee.EmployeeName}`">{{ employee.Scale }}%</el-form-item>
                            </el-form>
                          </el-col>
                          <el-col :span="16" class="border_left border_right">
                            <el-row v-if="item.PayAmount > 0" class="padtp_10 padrt_10" :class="index != 0 ? 'border_top' : ''">
                              <el-col :span="8">
                                <el-form class="sale-ModifyPerformanceCommission-Handler" label-position="right" size="mini" label-width="120px">
                                  <el-form-item label="现金业绩">
                                    <span slot="label">
                                      现金业绩
                                      <el-popover placement="top-start" width="200" trigger="hover">
                                        <p>现金业绩 = 现金付款金额 x 员工现金业绩占比 x 业绩占比</p>
                                        <p>
                                          员工现金业绩占比参考值：
                                          <span v-if="employee.PerformancePayRate != null"
                                            >{{ (employee.PerformancePayRate * 100) | toFixed | NumFormat }}%</span
                                          >
                                          <span v-else>无</span>
                                        </p>

                                        <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                      </el-popover>
                                    </span>
                                    <el-input
                                      type="number"
                                      v-input-fixed
                                      class="input_type"
                                      style="width: 120px"
                                      v-model="employee.PayPerformance"
                                      v-enter-number2
                                    >
                                      <template slot="append">元</template>
                                    </el-input>
                                  </el-form-item>
                                </el-form>
                              </el-col>
                              <el-col :span="8">
                                <el-form class="sale-ModifyPerformanceCommission-Handler" label-position="right" size="mini" label-width="150px">
                                  <el-form-item label="现金比例提成">
                                    <span slot="label">
                                      现金比例提成
                                      <el-popover placement="top-start" width="200" trigger="hover">
                                        <p>现金比例提成 = 现金业绩 x 现金比例</p>
                                        <p v-if="employee.PayRate != null">现金比例参考值：{{ employee.PayRate | toFixed | NumFormat }}%</p>
                                        <p v-else>现金比例参考值：无</p>
                                        <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                      </el-popover>
                                    </span>
                                    <el-input
                                      type="number"
                                      v-input-fixed
                                      class="input_type"
                                      style="width: 100px"
                                      v-model="employee.PayRateCommission"
                                      v-enter-number2
                                    >
                                      <template slot="append">元</template>
                                    </el-input>
                                  </el-form-item>
                                </el-form>
                              </el-col>
                              <el-col :span="8">
                                <el-form class="sale-ModifyPerformanceCommission-Handler" label-position="right" size="mini" label-width="150px">
                                  <el-form-item label="现金固定提成">
                                    <span slot="label">
                                      现金固定提成
                                      <el-popover placement="top-start" width="200" trigger="hover">
                                        <p>现金固定提成 = （现金业绩 ÷ 购买金额）x 现金固定 x 数量</p>
                                        <p v-if="employee.PayFixed != null">现金固定参考值：¥ {{ employee.PayFixed | toFixed | NumFormat }}</p>
                                        <p v-else>现金固定参考值：无</p>
                                        <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                      </el-popover>
                                    </span>
                                    <el-input
                                      type="number"
                                      v-input-fixed
                                      class="input_type"
                                      style="width: 100px"
                                      v-model="employee.PayFixedCommission"
                                      v-enter-number2
                                    >
                                      <template slot="append">元</template>
                                    </el-input>
                                  </el-form-item>
                                </el-form>
                              </el-col>
                            </el-row>
                            <el-row v-if="item.CardDeductionAmount > 0" class="padtp_10 padrt_10 border_top">
                              <el-col :span="8">
                                <el-form class="sale-ModifyPerformanceCommission-Handler" label-position="right" size="mini" label-width="120px">
                                  <el-form-item label="卡抵扣业绩">
                                    <span slot="label">
                                      卡抵扣业绩
                                      <el-popover placement="top-start" width="200" trigger="hover">
                                        <p>卡抵扣业绩 = 卡抵扣金额 x 员工卡抵扣业绩占比 x 业绩占比</p>
                                        <p>
                                          员工卡抵扣业绩占比参考值：
                                          <span v-if="employee.PerformanceSavingCardRate != null"
                                            >{{ (employee.PerformanceSavingCardRate * 100) | toFixed | NumFormat }}%</span
                                          >
                                          <span v-else>无</span>
                                        </p>

                                        <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                      </el-popover>
                                    </span>
                                    <el-input
                                      type="number"
                                      v-input-fixed
                                      class="input_type"
                                      style="width: 120px"
                                      v-model="employee.SavingCardPerformance"
                                      v-enter-number2
                                    >
                                      <template slot="append">元</template>
                                    </el-input>
                                  </el-form-item>
                                </el-form>
                              </el-col>
                              <el-col :span="8">
                                <el-form class="sale-ModifyPerformanceCommission-Handler" label-position="right" size="mini" label-width="150px">
                                  <el-form-item label="卡抵扣比例提成">
                                    <span slot="label">
                                      卡抵扣比例提成
                                      <el-popover placement="top-start" width="200" trigger="hover">
                                        <p>卡抵扣比例提成 = 卡抵扣业绩 x 卡抵扣比例</p>
                                        <p v-if="employee.SavingCardRate != null">卡抵扣比例参考值：{{ employee.SavingCardRate | toFixed | NumFormat }}%</p>
                                        <p v-else>卡抵扣比例参考值：无</p>
                                        <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                      </el-popover>
                                    </span>
                                    <el-input
                                      type="number"
                                      v-input-fixed
                                      class="input_type"
                                      style="width: 100px"
                                      v-model="employee.SavingCardRateCommission"
                                      v-enter-number2
                                    >
                                      <template slot="append">元</template>
                                    </el-input>
                                  </el-form-item>
                                </el-form>
                              </el-col>
                              <el-col :span="8">
                                <el-form class="sale-ModifyPerformanceCommission-Handler" label-position="right" size="mini" label-width="150px">
                                  <el-form-item label="卡抵扣固定提成">
                                    <span slot="label">
                                      卡抵扣固定提成
                                      <el-popover placement="top-start" width="200" trigger="hover">
                                        <p>卡抵扣固定提成 = （卡抵扣业绩 ÷ 购买金额）x 卡抵扣固定 x 数量</p>
                                        <p v-if="employee.SavingCardFixed != null">卡抵扣固定参考值：¥ {{ employee.SavingCardFixed | toFixed | NumFormat }}</p>
                                        <p v-else>卡抵扣固定参考值：无</p>
                                        <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                      </el-popover>
                                    </span>
                                    <el-input
                                      type="number"
                                      v-input-fixed
                                      class="input_type"
                                      style="width: 100px"
                                      v-model="employee.SavingCardFixedCommission"
                                      v-enter-number2
                                    >
                                      <template slot="append">元</template>
                                    </el-input>
                                  </el-form-item>
                                </el-form>
                              </el-col>
                            </el-row>
                            <el-row v-if="item.CardDeductionLargessAmount > 0" class="padtp_10 padrt_10 border_top">
                              <el-col :span="8">
                                <el-form class="sale-ModifyPerformanceCommission-Handler" label-position="right" size="mini" label-width="120px">
                                  <el-form-item label="赠送卡抵扣业绩">
                                    <span slot="label">
                                      赠送卡抵扣业绩
                                      <el-popover placement="top-start" width="200" trigger="hover">
                                        <p>赠送卡抵扣业绩 = 赠送卡抵扣金额 x 赠送卡抵扣业绩占比 x 业绩占比</p>
                                        <p>
                                          员工赠送卡抵扣业绩占比参考值：
                                          <span v-if="employee.PerformanceSavingCardLargessRate != null"
                                            >{{ (employee.PerformanceSavingCardLargessRate * 100) | toFixed | NumFormat }}%</span
                                          >
                                          <span v-else>无</span>
                                        </p>

                                        <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                      </el-popover>
                                    </span>
                                    <el-input
                                      type="number"
                                      v-input-fixed
                                      class="input_type"
                                      style="width: 120px"
                                      v-model="employee.SavingCardLargessPerformance"
                                    >
                                      <template slot="append">元</template>
                                    </el-input>
                                  </el-form-item>
                                </el-form>
                              </el-col>
                              <el-col :span="8">
                                <el-form class="sale-ModifyPerformanceCommission-Handler" label-position="right" size="mini" label-width="150px">
                                  <el-form-item label="赠送卡抵扣比例提成">
                                    <span slot="label">
                                      赠送卡抵扣比例提成
                                      <el-popover placement="top-start" width="200" trigger="hover">
                                        <p>赠送卡抵扣比例提成 = 赠送卡抵扣业绩 x 赠送卡抵扣比例</p>
                                        <p v-if="employee.SavingCardLargessRate != null">
                                          赠送卡抵扣比例参考值：{{ employee.SavingCardLargessRate | toFixed | NumFormat }}%
                                        </p>
                                        <p v-else>赠送卡抵扣比例参考值：无</p>
                                        <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                      </el-popover>
                                    </span>
                                    <el-input
                                      type="number"
                                      v-input-fixed
                                      class="input_type"
                                      style="width: 100px"
                                      v-model="employee.SavingCardLargessRateCommission"
                                      v-enter-number2
                                    >
                                      <template slot="append">元</template>
                                    </el-input>
                                  </el-form-item>
                                </el-form>
                              </el-col>
                              <el-col :span="8">
                                <el-form class="sale-ModifyPerformanceCommission-Handler" label-position="right" size="mini" label-width="150px">
                                  <el-form-item label="赠送卡抵扣固定提成">
                                    <span slot="label">
                                      赠送卡抵扣固定提成
                                      <el-popover placement="top-start" width="200" trigger="hover">
                                        <p>赠送卡抵扣固定提成 = （赠送卡抵扣业绩 ÷ 购买金额）x 赠送卡抵扣固定 x 数量</p>
                                        <p v-if="employee.SavingCardLargessFixed != null">
                                          赠送卡抵扣固定参考值：¥ {{ employee.SavingCardLargessFixed | toFixed | NumFormat }}
                                        </p>
                                        <p v-else>赠送卡抵扣固定参考值：无</p>
                                        <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                      </el-popover>
                                    </span>
                                    <el-input
                                      type="number"
                                      v-input-fixed
                                      class="input_type"
                                      style="width: 100px"
                                      v-model="employee.SavingCardLargessFixedCommission"
                                      v-enter-number2
                                    >
                                      <template slot="append">元</template>
                                    </el-input>
                                  </el-form-item>
                                </el-form>
                              </el-col>
                            </el-row>
                          </el-col>
                          <el-col v-if="!item.IsLargess" :span="4" class="padtp_10" :class="index != 0 ? 'border_top' : ''">
                            <el-form class="sale-ModifyPerformanceCommission-Handler" label-position="right" size="mini" label-width="90px">
                              <el-form-item label="无业绩奖励">
                                <span slot="label">
                                  无业绩奖励
                                  <el-popover placement="top-start" width="200" trigger="hover">
                                    <p>无业绩奖励 = 无业绩奖励参考值 x 数量 x 经手人比例</p>
                                    <p v-if="employee.SpecialBenefit != null">无业绩奖励参考值：¥ {{ employee.SpecialBenefit | toFixed | NumFormat }}</p>
                                    <p v-else>无业绩奖励参考值：无</p>
                                    <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                  </el-popover>
                                </span>
                                <el-input
                                  type="number"
                                  v-input-fixed
                                  class="input_type"
                                  style="width: 100px"
                                  v-model="employee.SpecialBenefitCommission"
                                  v-enter-number2
                                >
                                  <template slot="append">元</template>
                                </el-input>
                              </el-form-item>
                            </el-form>
                          </el-col>
                        </el-row>
                      </el-col>
                    </el-row>
                  </div>
                </div>
              </div>
              <!-- 套餐卡-项目 -->
              <div v-if="packageCard.Project.length > 0">
                <div v-for="(item, index) in packageCard.Project" :key="index">
                  <el-row class="row_header_package_detail border_right border_left">
                    <el-col :span="5">套餐卡项目</el-col>
                    <el-col :span="2">数量</el-col>
                    <el-col :span="4">优惠金额</el-col>
                    <el-col :span="3">购买金额</el-col>
                    <el-col :span="5">支付金额</el-col>
                    <el-col :span="5">欠款金额</el-col>
                  </el-row>
                  <el-row>
                    <el-col :span="24" class="pad_10 border_right border_left border_bottom">
                      <el-col :span="24">
                        <el-col :span="5">
                          <div>
                            {{ item.ProjectName }}
                            <span v-if="item.Alias">({{ item.Alias }})</span>
                            <el-tag v-if="item.IsLargess" size="mini" type="danger" class="marlt_10">赠</el-tag>
                          </div>
                        </el-col>
                        <el-col :span="2">x {{ item.Quantity * packageCard.Quantity }}</el-col>
                        <el-col :span="4">
                          <span v-if="item.PreferentialTotalAmount < 0">+ ¥ {{ Math.abs(item.PreferentialTotalAmount) | toFixed | NumFormat }}</span>
                          <span v-else-if="item.PreferentialTotalAmount > 0">- ¥ {{ Math.abs(item.PreferentialTotalAmount) | toFixed | NumFormat }}</span>
                          <span v-else>¥ 0.00</span>
                        </el-col>
                        <el-col :span="3">¥ {{ item.IsLargess ? 0 : item.TotalAmount | toFixed | NumFormat }}</el-col>
                        <el-col :span="5">¥ {{ (parseFloat(item.PayAmount) + parseFloat(item.CardDeductionTotalAmount)) | toFixed | NumFormat }}</el-col>
                        <el-col :span="5">¥ {{ item.ArrearAmount | toFixed | NumFormat }}</el-col>
                      </el-col>
                      <el-col :span="24" class="martp_5">
                        <el-col :span="7">
                          <div class="color_red font_12">¥ {{ item.Price | toFixed | NumFormat }}</div>
                        </el-col>
                        <el-col :span="7">
                          <span class="color_gray font_12" v-if="item.PricePreferentialAmount != 0">
                            手动改价：
                            <span class="color_red" v-if="item.PricePreferentialAmount > 0">- ¥ {{ item.PricePreferentialAmount | toFixed | NumFormat }}</span>
                            <span class="color_green" v-else>+ ¥ {{ Math.abs(item.PricePreferentialAmount) | toFixed | NumFormat }}</span>
                          </span>
                          <span class="color_gray font_12" :class="item.PricePreferentialAmount != 0 ? 'marlt_15' : ''" v-if="item.CardPreferentialAmount > 0">
                            卡优惠：
                            <span class="color_red">- ¥ {{ parseFloat(item.CardPreferentialAmount) | toFixed | NumFormat }}</span>
                          </span>

                          <span class="color_gray font_12" :class="item.PricePreferentialAmount != 0 ? item.CardPreferentialAmount != 0 ? 'marlt_15':'' : ''" v-if="item.MemberPreferentialAmount > 0">
                            会员优惠：
                            <span class="color_red">- ¥ {{ parseFloat(item.MemberPreferentialAmount) | toFixed | NumFormat }}</span>
                          </span>

                        </el-col>
                        <el-col :span="10" :offset="item.PricePreferentialAmount == 0 && item.CardPreferentialAmount == 0 ? 7 : 0">
                          <span class="color_gray font_12" v-if="item.PayAmount > 0">现金金额：¥ {{ item.PayAmount | toFixed | NumFormat }}</span>
                          <span class="color_gray font_12" :class="item.PayAmount > 0 ? 'marlt_15' : ''" v-if="item.CardDeductionAmount > 0"
                            >卡抵扣：¥ {{ item.CardDeductionAmount | toFixed | NumFormat }}</span
                          >
                          <span
                            class="color_gray font_12"
                            :class="item.PricePreferentialAmount > 0 || item.PayAmount > 0 ? 'marlt_15' : ''"
                            v-if="item.CardDeductionLargessAmount > 0"
                            >赠送卡抵扣：¥ {{ item.CardDeductionLargessAmount | toFixed | NumFormat }}</span
                          >
                        </el-col>
                      </el-col>
                    </el-col>
                  </el-row>
                  <div>
                    <el-row
                      class="padlt_10 padrt_10 border_right border_left border_bottom font_12"
                      v-for="handler in item.SaleBillHandler"
                      :key="handler.SaleHandlerID"
                    >
                      <el-col :span="2" class="padtp_10 padbm_10 font_12 line_26">{{ handler.SaleHandlerName }}</el-col>
                      <el-col :span="22" class="border_left">
                        <el-row v-for="(employee, index) in handler.Employee" :key="employee.EmployeeID">
                          <el-col :span="4" class="padtp_10 padrt_10" :class="index != 0 ? 'border_top' : ''">
                            <el-form class="sale-ModifyPerformanceCommission-Handler" label-position="right" size="mini" label-width="120px" label-suffix=":">
                              <el-form-item :label="`${employee.EmployeeName}`">{{ employee.Scale }}%</el-form-item>
                            </el-form>
                          </el-col>
                          <el-col :span="16" class="border_left border_right">
                            <el-row v-if="item.PayAmount > 0" class="padtp_10 padrt_10" :class="index != 0 ? 'border_top' : ''">
                              <el-col :span="8">
                                <el-form class="sale-ModifyPerformanceCommission-Handler" label-position="right" size="mini" label-width="120px">
                                  <el-form-item label="现金业绩">
                                    <span slot="label">
                                      现金业绩
                                      <el-popover placement="top-start" width="200" trigger="hover">
                                        <p>现金业绩 = 现金付款金额 x 员工现金业绩占比 x 业绩占比</p>
                                        <p>
                                          员工现金业绩占比参考值：
                                          <span v-if="employee.PerformancePayRate != null"
                                            >{{ (employee.PerformancePayRate * 100) | toFixed | NumFormat }}%</span
                                          >
                                          <span v-else>无</span>
                                        </p>

                                        <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                      </el-popover>
                                    </span>
                                    <el-input
                                      type="number"
                                      v-input-fixed
                                      class="input_type"
                                      style="width: 120px"
                                      v-model="employee.PayPerformance"
                                      v-enter-number2
                                    >
                                      <template slot="append">元</template>
                                    </el-input>
                                  </el-form-item>
                                </el-form>
                              </el-col>
                              <el-col :span="8">
                                <el-form class="sale-ModifyPerformanceCommission-Handler" label-position="right" size="mini" label-width="150px">
                                  <el-form-item label="现金比例提成">
                                    <span slot="label">
                                      现金比例提成
                                      <el-popover placement="top-start" width="200" trigger="hover">
                                        <p>现金比例提成 = 现金业绩 x 现金比例</p>
                                        <p v-if="employee.PayRate != null">现金比例参考值：{{ employee.PayRate | toFixed | NumFormat }}%</p>
                                        <p v-else>现金比例参考值：无</p>
                                        <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                      </el-popover>
                                    </span>
                                    <el-input
                                      type="number"
                                      v-input-fixed
                                      class="input_type"
                                      style="width: 100px"
                                      v-model="employee.PayRateCommission"
                                      v-enter-number2
                                    >
                                      <template slot="append">元</template>
                                    </el-input>
                                  </el-form-item>
                                </el-form>
                              </el-col>
                              <el-col :span="8">
                                <el-form class="sale-ModifyPerformanceCommission-Handler" label-position="right" size="mini" label-width="150px">
                                  <el-form-item label="现金固定提成">
                                    <span slot="label">
                                      现金固定提成
                                      <el-popover placement="top-start" width="200" trigger="hover">
                                        <p>现金固定提成 = （现金业绩 ÷ 购买金额）x 现金固定 x 数量</p>
                                        <p v-if="employee.PayFixed != null">现金固定参考值：¥ {{ employee.PayFixed | toFixed | NumFormat }}</p>
                                        <p v-else>现金固定参考值：无</p>
                                        <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                      </el-popover>
                                    </span>
                                    <el-input
                                      type="number"
                                      v-input-fixed
                                      class="input_type"
                                      style="width: 100px"
                                      v-model="employee.PayFixedCommission"
                                      v-enter-number2
                                    >
                                      <template slot="append">元</template>
                                    </el-input>
                                  </el-form-item>
                                </el-form>
                              </el-col>
                            </el-row>
                            <el-row v-if="item.CardDeductionAmount > 0" class="padtp_10 padrt_10 border_top">
                              <el-col :span="8">
                                <el-form class="sale-ModifyPerformanceCommission-Handler" label-position="right" size="mini" label-width="120px">
                                  <el-form-item label="卡抵扣业绩">
                                    <span slot="label">
                                      卡抵扣业绩
                                      <el-popover placement="top-start" width="200" trigger="hover">
                                        <p>卡抵扣业绩 = 卡抵扣金额 x 员工卡抵扣业绩占比 x 业绩占比</p>
                                        <p>
                                          员工卡抵扣业绩占比参考值：
                                          <span v-if="employee.PerformanceSavingCardRate != null"
                                            >{{ (employee.PerformanceSavingCardRate * 100) | toFixed | NumFormat }}%</span
                                          >
                                          <span v-else>无</span>
                                        </p>

                                        <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                      </el-popover>
                                    </span>
                                    <el-input
                                      type="number"
                                      v-input-fixed
                                      class="input_type"
                                      style="width: 120px"
                                      v-model="employee.SavingCardPerformance"
                                      v-enter-number2
                                    >
                                      <template slot="append">元</template>
                                    </el-input>
                                  </el-form-item>
                                </el-form>
                              </el-col>
                              <el-col :span="8">
                                <el-form class="sale-ModifyPerformanceCommission-Handler" label-position="right" size="mini" label-width="150px">
                                  <el-form-item label="卡抵扣比例提成">
                                    <span slot="label">
                                      卡抵扣比例提成
                                      <el-popover placement="top-start" width="200" trigger="hover">
                                        <p>卡抵扣比例提成 = 卡抵扣业绩 x 卡抵扣比例</p>
                                        <p v-if="employee.SavingCardRate != null">卡抵扣比例参考值：{{ employee.SavingCardRate | toFixed | NumFormat }}%</p>
                                        <p v-else>卡抵扣比例参考值：无</p>
                                        <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                      </el-popover>
                                    </span>
                                    <el-input
                                      type="number"
                                      v-input-fixed
                                      class="input_type"
                                      style="width: 100px"
                                      v-model="employee.SavingCardRateCommission"
                                      v-enter-number2
                                    >
                                      <template slot="append">元</template>
                                    </el-input>
                                  </el-form-item>
                                </el-form>
                              </el-col>
                              <el-col :span="8">
                                <el-form class="sale-ModifyPerformanceCommission-Handler" label-position="right" size="mini" label-width="150px">
                                  <el-form-item label="卡抵扣固定提成">
                                    <span slot="label">
                                      卡抵扣固定提成
                                      <el-popover placement="top-start" width="200" trigger="hover">
                                        <p>卡抵扣固定提成 = （卡抵扣业绩 ÷ 购买金额）x 卡抵扣固定 x 数量</p>
                                        <p v-if="employee.SavingCardFixed != null">卡抵扣固定参考值：¥ {{ employee.SavingCardFixed | toFixed | NumFormat }}</p>
                                        <p v-else>卡抵扣固定参考值：无</p>
                                        <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                      </el-popover>
                                    </span>
                                    <el-input
                                      type="number"
                                      v-input-fixed
                                      class="input_type"
                                      style="width: 100px"
                                      v-model="employee.SavingCardFixedCommission"
                                      v-enter-number2
                                    >
                                      <template slot="append">元</template>
                                    </el-input>
                                  </el-form-item>
                                </el-form>
                              </el-col>
                            </el-row>
                            <el-row v-if="item.CardDeductionLargessAmount > 0" class="padtp_10 padrt_10 border_top">
                              <el-col :span="8">
                                <el-form class="sale-ModifyPerformanceCommission-Handler" label-position="right" size="mini" label-width="120px">
                                  <el-form-item label="赠送卡抵扣业绩">
                                    <span slot="label">
                                      赠送卡抵扣业绩
                                      <el-popover placement="top-start" width="200" trigger="hover">
                                        <p>赠送卡抵扣业绩 = 赠送卡抵扣金额 x 赠送卡抵扣业绩占比 x 业绩占比</p>
                                        <p>
                                          员工赠送卡抵扣业绩占比参考值：
                                          <span v-if="employee.PerformanceSavingCardLargessRate != null"
                                            >{{ (employee.PerformanceSavingCardLargessRate * 100) | toFixed | NumFormat }}%</span
                                          >
                                          <span v-else>无</span>
                                        </p>

                                        <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                      </el-popover>
                                    </span>
                                    <el-input
                                      type="number"
                                      v-input-fixed
                                      class="input_type"
                                      style="width: 120px"
                                      v-model="employee.SavingCardLargessPerformance"
                                    >
                                      <template slot="append">元</template>
                                    </el-input>
                                  </el-form-item>
                                </el-form>
                              </el-col>
                              <el-col :span="8">
                                <el-form class="sale-ModifyPerformanceCommission-Handler" label-position="right" size="mini" label-width="150px">
                                  <el-form-item label="赠送卡抵扣比例提成">
                                    <span slot="label">
                                      赠送卡抵扣比例提成
                                      <el-popover placement="top-start" width="200" trigger="hover">
                                        <p>赠送卡抵扣比例提成 = 赠送卡抵扣业绩 x 赠送卡抵扣比例</p>
                                        <p v-if="employee.SavingCardLargessRate != null">
                                          赠送卡抵扣比例参考值：{{ employee.SavingCardLargessRate | toFixed | NumFormat }}%
                                        </p>
                                        <p v-else>赠送卡抵扣比例参考值：无</p>
                                        <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                      </el-popover>
                                    </span>
                                    <el-input
                                      type="number"
                                      v-input-fixed
                                      class="input_type"
                                      style="width: 100px"
                                      v-model="employee.SavingCardLargessRateCommission"
                                      v-enter-number2
                                    >
                                      <template slot="append">元</template>
                                    </el-input>
                                  </el-form-item>
                                </el-form>
                              </el-col>
                              <el-col :span="8">
                                <el-form class="sale-ModifyPerformanceCommission-Handler" label-position="right" size="mini" label-width="150px">
                                  <el-form-item label="赠送卡抵扣固定提成">
                                    <span slot="label">
                                      赠送卡抵扣固定提成
                                      <el-popover placement="top-start" width="200" trigger="hover">
                                        <p>赠送卡抵扣固定提成 = （赠送卡抵扣业绩 ÷ 购买金额）x 赠送卡抵扣固定 x 数量</p>
                                        <p v-if="employee.SavingCardLargessFixed != null">
                                          赠送卡抵扣固定参考值：¥ {{ employee.SavingCardLargessFixed | toFixed | NumFormat }}
                                        </p>
                                        <p v-else>赠送卡抵扣固定参考值：无</p>
                                        <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                      </el-popover>
                                    </span>
                                    <el-input
                                      type="number"
                                      v-input-fixed
                                      class="input_type"
                                      style="width: 100px"
                                      v-model="employee.SavingCardLargessFixedCommission"
                                      v-enter-number2
                                    >
                                      <template slot="append">元</template>
                                    </el-input>
                                  </el-form-item>
                                </el-form>
                              </el-col>
                            </el-row>
                          </el-col>
                          <el-col v-if="!item.IsLargess" :span="4" class="padtp_10" :class="index != 0 ? 'border_top' : ''">
                            <el-form class="sale-ModifyPerformanceCommission-Handler" label-position="right" size="mini" label-width="90px">
                              <el-form-item label="无业绩奖励">
                                <span slot="label">
                                  无业绩奖励
                                  <el-popover placement="top-start" width="200" trigger="hover">
                                    <p>无业绩奖励 = 无业绩奖励参考值 x 数量 x 经手人比例</p>
                                    <p v-if="employee.SpecialBenefit != null">无业绩奖励参考值：¥ {{ employee.SpecialBenefit | toFixed | NumFormat }}</p>
                                    <p v-else>无业绩奖励参考值：无</p>
                                    <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                  </el-popover>
                                </span>
                                <el-input
                                  type="number"
                                  v-input-fixed
                                  class="input_type"
                                  style="width: 100px"
                                  v-model="employee.SpecialBenefitCommission"
                                  v-enter-number2
                                >
                                  <template slot="append">元</template>
                                </el-input>
                              </el-form-item>
                            </el-form>
                          </el-col>
                        </el-row>
                      </el-col>
                    </el-row>
                  </div>
                </div>
              </div>
              <!-- 套餐卡-通用次卡 -->
              <div v-if="packageCard.GeneralCard.length > 0">
                <div v-for="(item, index) in packageCard.GeneralCard" :key="index">
                  <el-row class="row_header_package_detail border_right border_left">
                    <el-col :span="5">套餐卡通用次卡</el-col>
                    <el-col :span="2">数量</el-col>
                    <el-col :span="4">优惠金额</el-col>
                    <el-col :span="3">购买金额</el-col>
                    <el-col :span="5">支付金额</el-col>
                    <el-col :span="5">欠款金额</el-col>
                  </el-row>
                  <el-row>
                    <el-col :span="24" class="pad_10 border_right border_left border_bottom">
                      <el-col :span="24">
                        <el-col :span="5">
                          <div>
                            {{ item.GeneralCardName }}
                            <span v-if="item.Alias">({{ item.Alias }})</span>
                            <el-tag v-if="item.IsLargess" size="mini" type="danger" class="marlt_10">赠</el-tag>
                          </div>
                        </el-col>
                        <el-col :span="2">x {{ item.Quantity * packageCard.Quantity }}</el-col>
                        <el-col :span="4">
                          <span v-if="item.PreferentialTotalAmount < 0">+ ¥ {{ Math.abs(item.PreferentialTotalAmount) | toFixed | NumFormat }}</span>
                          <span v-else-if="item.PreferentialTotalAmount > 0">- ¥ {{ Math.abs(item.PreferentialTotalAmount) | toFixed | NumFormat }}</span>
                          <span v-else>¥ 0.00</span>
                        </el-col>
                        <el-col :span="3">¥ {{ item.IsLargess ? 0 : item.TotalAmount | toFixed | NumFormat }}</el-col>
                        <el-col :span="5">¥ {{ (parseFloat(item.PayAmount) + parseFloat(item.CardDeductionTotalAmount)) | toFixed | NumFormat }}</el-col>
                        <el-col :span="5">¥ {{ item.ArrearAmount | toFixed | NumFormat }}</el-col>
                      </el-col>
                      <el-col :span="24" class="martp_5">
                        <el-col :span="7">
                          <div class="color_red font_12">¥ {{ item.Price | toFixed | NumFormat }}</div>
                        </el-col>
                        <el-col :span="7">
                          <span class="color_gray font_12" v-if="item.PricePreferentialAmount != 0">
                            手动改价：
                            <span class="color_red" v-if="item.PricePreferentialAmount > 0">- ¥ {{ item.PricePreferentialAmount | toFixed | NumFormat }}</span>
                            <span class="color_green" v-else>+ ¥ {{ Math.abs(item.PricePreferentialAmount) | toFixed | NumFormat }}</span>
                          </span>
                          <span class="color_gray font_12" :class="item.PricePreferentialAmount != 0 ? 'marlt_15' : ''" v-if="item.CardPreferentialAmount > 0">
                            卡优惠：
                            <span class="color_red">- ¥ {{ parseFloat(item.CardPreferentialAmount) | toFixed | NumFormat }}</span>
                          </span>

                          <span class="color_gray font_12" :class="item.PricePreferentialAmount != 0 ? item.CardPreferentialAmount != 0 ? 'marlt_15':'' : ''" v-if="item.MemberPreferentialAmount > 0">
                            会员优惠：
                            <span class="color_red">- ¥ {{ parseFloat(item.MemberPreferentialAmount) | toFixed | NumFormat }}</span>
                          </span>
                        </el-col>
                        <el-col :span="10" :offset="item.PricePreferentialAmount == 0 && item.CardPreferentialAmount == 0 ? 7 : 0">
                          <span class="color_gray font_12" v-if="item.PayAmount > 0">现金金额：¥ {{ item.PayAmount | toFixed | NumFormat }}</span>
                          <span class="color_gray font_12" :class="item.PayAmount > 0 ? 'marlt_15' : ''" v-if="item.CardDeductionAmount > 0"
                            >卡抵扣：¥ {{ item.CardDeductionAmount | toFixed | NumFormat }}</span
                          >
                          <span
                            class="color_gray font_12"
                            :class="item.PricePreferentialAmount > 0 || item.PayAmount > 0 ? 'marlt_15' : ''"
                            v-if="item.CardDeductionLargessAmount > 0"
                            >赠送卡抵扣：¥ {{ item.CardDeductionLargessAmount | toFixed | NumFormat }}</span
                          >
                        </el-col>
                      </el-col>
                    </el-col>
                  </el-row>

                  <div>
                    <el-row
                      class="padlt_10 padrt_10 border_right border_left border_bottom font_12"
                      v-for="handler in item.SaleBillHandler"
                      :key="handler.SaleHandlerID"
                    >
                      <el-col :span="2" class="padtp_10 padbm_10 font_12 line_26">{{ handler.SaleHandlerName }}</el-col>
                      <el-col :span="22" class="border_left">
                        <el-row v-for="(employee, index) in handler.Employee" :key="employee.EmployeeID">
                          <el-col :span="4" class="padtp_10 padrt_10" :class="index != 0 ? 'border_top' : ''">
                            <el-form class="sale-ModifyPerformanceCommission-Handler" label-position="right" size="mini" label-width="120px" label-suffix=":">
                              <el-form-item :label="`${employee.EmployeeName}`">{{ employee.Scale }}%</el-form-item>
                            </el-form>
                          </el-col>
                          <el-col :span="16" class="border_left border_right">
                            <el-row v-if="item.PayAmount > 0" class="padtp_10 padrt_10" :class="index != 0 ? 'border_top' : ''">
                              <el-col :span="8">
                                <el-form class="sale-ModifyPerformanceCommission-Handler" label-position="right" size="mini" label-width="120px">
                                  <el-form-item label="现金业绩">
                                    <span slot="label">
                                      现金业绩
                                      <el-popover placement="top-start" width="200" trigger="hover">
                                        <p>现金业绩 = 现金付款金额 x 员工现金业绩占比 x 业绩占比</p>
                                        <p>
                                          员工现金业绩占比参考值：
                                          <span v-if="employee.PerformancePayRate != null"
                                            >{{ (employee.PerformancePayRate * 100) | toFixed | NumFormat }}%</span
                                          >
                                          <span v-else>无</span>
                                        </p>

                                        <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                      </el-popover>
                                    </span>
                                    <el-input
                                      type="number"
                                      v-input-fixed
                                      class="input_type"
                                      style="width: 120px"
                                      v-model="employee.PayPerformance"
                                      v-enter-number2
                                    >
                                      <template slot="append">元</template>
                                    </el-input>
                                  </el-form-item>
                                </el-form>
                              </el-col>
                              <el-col :span="8">
                                <el-form class="sale-ModifyPerformanceCommission-Handler" label-position="right" size="mini" label-width="150px">
                                  <el-form-item label="现金比例提成">
                                    <span slot="label">
                                      现金比例提成
                                      <el-popover placement="top-start" width="200" trigger="hover">
                                        <p>现金比例提成 = 现金业绩 x 现金比例</p>
                                        <p v-if="employee.PayRate != null">现金比例参考值：{{ employee.PayRate | toFixed | NumFormat }}%</p>
                                        <p v-else>现金比例参考值：无</p>
                                        <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                      </el-popover>
                                    </span>
                                    <el-input
                                      type="number"
                                      v-input-fixed
                                      class="input_type"
                                      style="width: 100px"
                                      v-model="employee.PayRateCommission"
                                      v-enter-number2
                                    >
                                      <template slot="append">元</template>
                                    </el-input>
                                  </el-form-item>
                                </el-form>
                              </el-col>
                              <el-col :span="8">
                                <el-form class="sale-ModifyPerformanceCommission-Handler" label-position="right" size="mini" label-width="150px">
                                  <el-form-item label="现金固定提成">
                                    <span slot="label">
                                      现金固定提成
                                      <el-popover placement="top-start" width="200" trigger="hover">
                                        <p>现金固定提成 = （现金业绩 ÷ 购买金额）x 现金固定 x 数量</p>
                                        <p v-if="employee.PayFixed != null">现金固定参考值：¥ {{ employee.PayFixed | toFixed | NumFormat }}</p>
                                        <p v-else>现金固定参考值：无</p>
                                        <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                      </el-popover>
                                    </span>
                                    <el-input
                                      type="number"
                                      v-input-fixed
                                      class="input_type"
                                      style="width: 100px"
                                      v-model="employee.PayFixedCommission"
                                      v-enter-number2
                                    >
                                      <template slot="append">元</template>
                                    </el-input>
                                  </el-form-item>
                                </el-form>
                              </el-col>
                            </el-row>
                            <el-row v-if="item.CardDeductionAmount > 0" class="padtp_10 padrt_10 border_top">
                              <el-col :span="8">
                                <el-form class="sale-ModifyPerformanceCommission-Handler" label-position="right" size="mini" label-width="120px">
                                  <el-form-item label="卡抵扣业绩">
                                    <span slot="label">
                                      卡抵扣业绩
                                      <el-popover placement="top-start" width="200" trigger="hover">
                                        <p>卡抵扣业绩 = 卡抵扣金额 x 员工卡抵扣业绩占比 x 业绩占比</p>
                                        <p>
                                          员工卡抵扣业绩占比参考值：
                                          <span v-if="employee.PerformanceSavingCardRate != null"
                                            >{{ (employee.PerformanceSavingCardRate * 100) | toFixed | NumFormat }}%</span
                                          >
                                          <span v-else>无</span>
                                        </p>

                                        <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                      </el-popover>
                                    </span>
                                    <el-input
                                      type="number"
                                      v-input-fixed
                                      class="input_type"
                                      style="width: 120px"
                                      v-model="employee.SavingCardPerformance"
                                      v-enter-number2
                                    >
                                      <template slot="append">元</template>
                                    </el-input>
                                  </el-form-item>
                                </el-form>
                              </el-col>
                              <el-col :span="8">
                                <el-form class="sale-ModifyPerformanceCommission-Handler" label-position="right" size="mini" label-width="150px">
                                  <el-form-item label="卡抵扣比例提成">
                                    <span slot="label">
                                      卡抵扣比例提成
                                      <el-popover placement="top-start" width="200" trigger="hover">
                                        <p>卡抵扣比例提成 = 卡抵扣业绩 x 卡抵扣比例</p>
                                        <p v-if="employee.SavingCardRate != null">卡抵扣比例参考值：{{ employee.SavingCardRate | toFixed | NumFormat }}%</p>
                                        <p v-else>卡抵扣比例参考值：无</p>
                                        <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                      </el-popover>
                                    </span>
                                    <el-input
                                      type="number"
                                      v-input-fixed
                                      class="input_type"
                                      style="width: 100px"
                                      v-model="employee.SavingCardRateCommission"
                                      v-enter-number2
                                    >
                                      <template slot="append">元</template>
                                    </el-input>
                                  </el-form-item>
                                </el-form>
                              </el-col>
                              <el-col :span="8">
                                <el-form class="sale-ModifyPerformanceCommission-Handler" label-position="right" size="mini" label-width="150px">
                                  <el-form-item label="卡抵扣固定提成">
                                    <span slot="label">
                                      卡抵扣固定提成
                                      <el-popover placement="top-start" width="200" trigger="hover">
                                        <p>卡抵扣固定提成 = （卡抵扣业绩 ÷ 购买金额）x 卡抵扣固定 x 数量</p>
                                        <p v-if="employee.SavingCardFixed != null">卡抵扣固定参考值：¥ {{ employee.SavingCardFixed | toFixed | NumFormat }}</p>
                                        <p v-else>卡抵扣固定参考值：无</p>
                                        <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                      </el-popover>
                                    </span>
                                    <el-input
                                      type="number"
                                      v-input-fixed
                                      class="input_type"
                                      style="width: 100px"
                                      v-model="employee.SavingCardFixedCommission"
                                      v-enter-number2
                                    >
                                      <template slot="append">元</template>
                                    </el-input>
                                  </el-form-item>
                                </el-form>
                              </el-col>
                            </el-row>
                            <el-row v-if="item.CardDeductionLargessAmount > 0" class="padtp_10 padrt_10 border_top">
                              <el-col :span="8">
                                <el-form class="sale-ModifyPerformanceCommission-Handler" label-position="right" size="mini" label-width="120px">
                                  <el-form-item label="赠送卡抵扣业绩">
                                    <span slot="label">
                                      赠送卡抵扣业绩
                                      <el-popover placement="top-start" width="200" trigger="hover">
                                        <p>赠送卡抵扣业绩 = 赠送卡抵扣金额 x 赠送卡抵扣业绩占比 x 业绩占比</p>
                                        <p>
                                          员工赠送卡抵扣业绩占比参考值：
                                          <span v-if="employee.PerformanceSavingCardLargessRate != null"
                                            >{{ (employee.PerformanceSavingCardLargessRate * 100) | toFixed | NumFormat }}%</span
                                          >
                                          <span v-else>无</span>
                                        </p>

                                        <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                      </el-popover>
                                    </span>
                                    <el-input
                                      type="number"
                                      v-input-fixed
                                      class="input_type"
                                      style="width: 120px"
                                      v-model="employee.SavingCardLargessPerformance"
                                    >
                                      <template slot="append">元</template>
                                    </el-input>
                                  </el-form-item>
                                </el-form>
                              </el-col>
                              <el-col :span="8">
                                <el-form class="sale-ModifyPerformanceCommission-Handler" label-position="right" size="mini" label-width="150px">
                                  <el-form-item label="赠送卡抵扣比例提成">
                                    <span slot="label">
                                      赠送卡抵扣比例提成
                                      <el-popover placement="top-start" width="200" trigger="hover">
                                        <p>赠送卡抵扣比例提成 = 赠送卡抵扣业绩 x 赠送卡抵扣比例</p>
                                        <p v-if="employee.SavingCardLargessRate != null">
                                          赠送卡抵扣比例参考值：{{ employee.SavingCardLargessRate | toFixed | NumFormat }}%
                                        </p>
                                        <p v-else>赠送卡抵扣比例参考值：无</p>
                                        <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                      </el-popover>
                                    </span>
                                    <el-input
                                      type="number"
                                      v-input-fixed
                                      class="input_type"
                                      style="width: 100px"
                                      v-model="employee.SavingCardLargessRateCommission"
                                      v-enter-number2
                                    >
                                      <template slot="append">元</template>
                                    </el-input>
                                  </el-form-item>
                                </el-form>
                              </el-col>
                              <el-col :span="8">
                                <el-form class="sale-ModifyPerformanceCommission-Handler" label-position="right" size="mini" label-width="150px">
                                  <el-form-item label="赠送卡抵扣固定提成">
                                    <span slot="label">
                                      赠送卡抵扣固定提成
                                      <el-popover placement="top-start" width="200" trigger="hover">
                                        <p>赠送卡抵扣固定提成 = （赠送卡抵扣业绩 ÷ 购买金额）x 赠送卡抵扣固定 x 数量</p>
                                        <p v-if="employee.SavingCardLargessFixed != null">
                                          赠送卡抵扣固定参考值：¥ {{ employee.SavingCardLargessFixed | toFixed | NumFormat }}
                                        </p>
                                        <p v-else>赠送卡抵扣固定参考值：无</p>
                                        <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                      </el-popover>
                                    </span>
                                    <el-input
                                      type="number"
                                      v-input-fixed
                                      class="input_type"
                                      style="width: 100px"
                                      v-model="employee.SavingCardLargessFixedCommission"
                                      v-enter-number2
                                    >
                                      <template slot="append">元</template>
                                    </el-input>
                                  </el-form-item>
                                </el-form>
                              </el-col>
                            </el-row>
                          </el-col>
                          <el-col v-if="!item.IsLargess" :span="4" class="padtp_10" :class="index != 0 ? 'border_top' : ''">
                            <el-form class="sale-ModifyPerformanceCommission-Handler" label-position="right" size="mini" label-width="90px">
                              <el-form-item label="无业绩奖励">
                                <span slot="label">
                                  无业绩奖励
                                  <el-popover placement="top-start" width="200" trigger="hover">
                                    <p>无业绩奖励 = 无业绩奖励参考值 x 数量 x 经手人比例</p>
                                    <p v-if="employee.SpecialBenefit != null">无业绩奖励参考值：¥ {{ employee.SpecialBenefit | toFixed | NumFormat }}</p>
                                    <p v-else>无业绩奖励参考值：无</p>
                                    <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                  </el-popover>
                                </span>
                                <el-input
                                  type="number"
                                  v-input-fixed
                                  class="input_type"
                                  style="width: 100px"
                                  v-model="employee.SpecialBenefitCommission"
                                  v-enter-number2
                                >
                                  <template slot="append">元</template>
                                </el-input>
                              </el-form-item>
                            </el-form>
                          </el-col>
                        </el-row>
                      </el-col>
                    </el-row>
                  </div>
                </div>
              </div>
              <!-- 套餐卡-时效卡 -->
              <div v-if="packageCard.TimeCard.length > 0">
                <div v-for="(item, index) in packageCard.TimeCard" :key="index">
                  <el-row class="row_header_package_detail border_right border_left">
                    <el-col :span="5">套餐卡时效卡</el-col>
                    <el-col :span="2">数量</el-col>
                    <el-col :span="4">优惠金额</el-col>
                    <el-col :span="3">购买金额</el-col>
                    <el-col :span="5">支付金额</el-col>
                    <el-col :span="5">欠款金额</el-col>
                  </el-row>
                  <el-row>
                    <el-col :span="24" class="pad_10 border_right border_left border_bottom">
                      <el-col :span="24">
                        <el-col :span="5">
                          <div>
                            {{ item.TimeCardName }}
                            <span v-if="item.Alias">({{ item.Alias }})</span>
                            <el-tag v-if="item.IsLargess" size="mini" type="danger" class="marlt_10">赠</el-tag>
                          </div>
                        </el-col>
                        <el-col :span="2">x {{ item.Quantity * packageCard.Quantity }}</el-col>
                        <el-col :span="4">
                          <span v-if="item.PreferentialTotalAmount < 0">+ ¥ {{ Math.abs(item.PreferentialTotalAmount) | toFixed | NumFormat }}</span>
                          <span v-else-if="item.PreferentialTotalAmount > 0">- ¥ {{ Math.abs(item.PreferentialTotalAmount) | toFixed | NumFormat }}</span>
                          <span v-else>¥ 0.00</span>
                        </el-col>
                        <el-col :span="3">¥ {{ item.IsLargess ? 0 : item.TotalAmount | toFixed | NumFormat }}</el-col>
                        <el-col :span="5">¥ {{ (parseFloat(item.PayAmount) + parseFloat(item.CardDeductionTotalAmount)) | toFixed | NumFormat }}</el-col>
                        <el-col :span="5">¥ {{ item.ArrearAmount | toFixed | NumFormat }}</el-col>
                      </el-col>
                      <el-col :span="24" class="martp_5">
                        <el-col :span="7">
                          <div class="color_red font_12">¥ {{ item.Price | toFixed | NumFormat }}</div>
                        </el-col>
                        <el-col :span="7">
                          <span class="color_gray font_12" v-if="item.PricePreferentialAmount != 0">
                            手动改价：
                            <span class="color_red" v-if="item.PricePreferentialAmount > 0">- ¥ {{ item.PricePreferentialAmount | toFixed | NumFormat }}</span>
                            <span class="color_green" v-else>+ ¥ {{ Math.abs(item.PricePreferentialAmount) | toFixed | NumFormat }}</span>
                          </span>
                          <span class="color_gray font_12" :class="item.PricePreferentialAmount != 0 ? 'marlt_15' : ''" v-if="item.CardPreferentialAmount > 0">
                            卡优惠：
                            <span class="color_red">- ¥ {{ parseFloat(item.CardPreferentialAmount) | toFixed | NumFormat }}</span>
                          </span>
                          <span class="color_gray font_12" :class="item.PricePreferentialAmount != 0 ? item.CardPreferentialAmount != 0 ? 'marlt_15':'' : ''" v-if="item.MemberPreferentialAmount > 0">
                            会员优惠：
                            <span class="color_red">- ¥ {{ parseFloat(item.MemberPreferentialAmount) | toFixed | NumFormat }}</span>
                          </span>
                        </el-col>
                        <el-col :span="10" :offset="item.PricePreferentialAmount == 0 && item.CardPreferentialAmount == 0 ? 7 : 0">
                          <span class="color_gray font_12" v-if="item.PayAmount > 0">现金金额：¥ {{ item.PayAmount | toFixed | NumFormat }}</span>
                          <span class="color_gray font_12" :class="item.PayAmount > 0 ? 'marlt_15' : ''" v-if="item.CardDeductionAmount > 0"
                            >卡抵扣：¥ {{ item.CardDeductionAmount | toFixed | NumFormat }}</span
                          >
                          <span
                            class="color_gray font_12"
                            :class="item.PricePreferentialAmount > 0 || item.PayAmount > 0 ? 'marlt_15' : ''"
                            v-if="item.CardDeductionLargessAmount > 0"
                            >赠送卡抵扣：¥ {{ item.CardDeductionLargessAmount | toFixed | NumFormat }}</span
                          >
                        </el-col>
                      </el-col>
                    </el-col>
                  </el-row>
                  <!-- <div v-if="!item.IsLargess"> -->
                  <div>
                    <el-row
                      class="padlt_10 padrt_10 border_right border_left border_bottom font_12"
                      v-for="handler in item.SaleBillHandler"
                      :key="handler.SaleHandlerID"
                    >
                      <el-col :span="2" class="padtp_10 padbm_10 font_12 line_26">{{ handler.SaleHandlerName }}</el-col>
                      <el-col :span="22" class="border_left">
                        <el-row v-for="(employee, index) in handler.Employee" :key="employee.EmployeeID">
                          <el-col :span="4" class="padtp_10 padrt_10" :class="index != 0 ? 'border_top' : ''">
                            <el-form class="sale-ModifyPerformanceCommission-Handler" label-position="right" size="mini" label-width="120px" label-suffix=":">
                              <el-form-item :label="`${employee.EmployeeName}`">{{ employee.Scale }}%</el-form-item>
                            </el-form>
                          </el-col>
                          <el-col :span="16" class="border_left border_right">
                            <el-row v-if="item.PayAmount > 0" class="padtp_10 padrt_10" :class="index != 0 ? 'border_top' : ''">
                              <el-col :span="8">
                                <el-form class="sale-ModifyPerformanceCommission-Handler" label-position="right" size="mini" label-width="120px">
                                  <el-form-item label="现金业绩">
                                    <span slot="label">
                                      现金业绩
                                      <el-popover placement="top-start" width="200" trigger="hover">
                                        <p>现金业绩 = 现金付款金额 x 员工现金业绩占比 x 业绩占比</p>
                                        <p>
                                          员工现金业绩占比参考值：
                                          <span v-if="employee.PerformancePayRate != null"
                                            >{{ (employee.PerformancePayRate * 100) | toFixed | NumFormat }}%</span
                                          >
                                          <span v-else>无</span>
                                        </p>

                                        <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                      </el-popover>
                                    </span>
                                    <el-input
                                      type="number"
                                      v-input-fixed
                                      class="input_type"
                                      style="width: 120px"
                                      v-model="employee.PayPerformance"
                                      v-enter-number2
                                    >
                                      <template slot="append">元</template>
                                    </el-input>
                                  </el-form-item>
                                </el-form>
                              </el-col>
                              <el-col :span="8">
                                <el-form class="sale-ModifyPerformanceCommission-Handler" label-position="right" size="mini" label-width="150px">
                                  <el-form-item label="现金比例提成">
                                    <span slot="label">
                                      现金比例提成
                                      <el-popover placement="top-start" width="200" trigger="hover">
                                        <p>现金比例提成 = 现金业绩 x 现金比例</p>
                                        <p v-if="employee.PayRate != null">现金比例参考值：{{ employee.PayRate | toFixed | NumFormat }}%</p>
                                        <p v-else>现金比例参考值：无</p>
                                        <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                      </el-popover>
                                    </span>
                                    <el-input
                                      type="number"
                                      v-input-fixed
                                      class="input_type"
                                      style="width: 100px"
                                      v-model="employee.PayRateCommission"
                                      v-enter-number2
                                    >
                                      <template slot="append">元</template>
                                    </el-input>
                                  </el-form-item>
                                </el-form>
                              </el-col>
                              <el-col :span="8">
                                <el-form class="sale-ModifyPerformanceCommission-Handler" label-position="right" size="mini" label-width="150px">
                                  <el-form-item label="现金固定提成">
                                    <span slot="label">
                                      现金固定提成
                                      <el-popover placement="top-start" width="200" trigger="hover">
                                        <p>现金固定提成 = （现金业绩 ÷ 购买金额）x 现金固定 x 数量</p>
                                        <p v-if="employee.PayFixed != null">现金固定参考值：¥ {{ employee.PayFixed | toFixed | NumFormat }}</p>
                                        <p v-else>现金固定参考值：无</p>
                                        <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                      </el-popover>
                                    </span>
                                    <el-input
                                      type="number"
                                      v-input-fixed
                                      class="input_type"
                                      style="width: 100px"
                                      v-model="employee.PayFixedCommission"
                                      v-enter-number2
                                    >
                                      <template slot="append">元</template>
                                    </el-input>
                                  </el-form-item>
                                </el-form>
                              </el-col>
                            </el-row>
                            <el-row v-if="item.CardDeductionAmount > 0" class="padtp_10 padrt_10 border_top">
                              <el-col :span="8">
                                <el-form class="sale-ModifyPerformanceCommission-Handler" label-position="right" size="mini" label-width="120px">
                                  <el-form-item label="卡抵扣业绩">
                                    <span slot="label">
                                      卡抵扣业绩
                                      <el-popover placement="top-start" width="200" trigger="hover">
                                        <p>卡抵扣业绩 = 卡抵扣金额 x 员工卡抵扣业绩占比 x 业绩占比</p>
                                        <p>
                                          员工卡抵扣业绩占比参考值：
                                          <span v-if="employee.PerformanceSavingCardRate != null"
                                            >{{ (employee.PerformanceSavingCardRate * 100) | toFixed | NumFormat }}%</span
                                          >
                                          <span v-else>无</span>
                                        </p>

                                        <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                      </el-popover>
                                    </span>
                                    <el-input
                                      type="number"
                                      v-input-fixed
                                      class="input_type"
                                      style="width: 120px"
                                      v-model="employee.SavingCardPerformance"
                                      v-enter-number2
                                    >
                                      <template slot="append">元</template>
                                    </el-input>
                                  </el-form-item>
                                </el-form>
                              </el-col>
                              <el-col :span="8">
                                <el-form class="sale-ModifyPerformanceCommission-Handler" label-position="right" size="mini" label-width="150px">
                                  <el-form-item label="卡抵扣比例提成">
                                    <span slot="label">
                                      卡抵扣比例提成
                                      <el-popover placement="top-start" width="200" trigger="hover">
                                        <p>卡抵扣比例提成 = 卡抵扣业绩 x 卡抵扣比例</p>
                                        <p v-if="employee.SavingCardRate != null">卡抵扣比例参考值：{{ employee.SavingCardRate | toFixed | NumFormat }}%</p>
                                        <p v-else>卡抵扣比例参考值：无</p>
                                        <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                      </el-popover>
                                    </span>
                                    <el-input
                                      type="number"
                                      v-input-fixed
                                      class="input_type"
                                      style="width: 100px"
                                      v-model="employee.SavingCardRateCommission"
                                      v-enter-number2
                                    >
                                      <template slot="append">元</template>
                                    </el-input>
                                  </el-form-item>
                                </el-form>
                              </el-col>
                              <el-col :span="8">
                                <el-form class="sale-ModifyPerformanceCommission-Handler" label-position="right" size="mini" label-width="150px">
                                  <el-form-item label="卡抵扣固定提成">
                                    <span slot="label">
                                      卡抵扣固定提成
                                      <el-popover placement="top-start" width="200" trigger="hover">
                                        <p>卡抵扣固定提成 = （卡抵扣业绩 ÷ 购买金额）x 卡抵扣固定 x 数量</p>
                                        <p v-if="employee.SavingCardFixed != null">卡抵扣固定参考值：¥ {{ employee.SavingCardFixed | toFixed | NumFormat }}</p>
                                        <p v-else>卡抵扣固定参考值：无</p>
                                        <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                      </el-popover>
                                    </span>
                                    <el-input
                                      type="number"
                                      v-input-fixed
                                      class="input_type"
                                      style="width: 100px"
                                      v-model="employee.SavingCardFixedCommission"
                                      v-enter-number2
                                    >
                                      <template slot="append">元</template>
                                    </el-input>
                                  </el-form-item>
                                </el-form>
                              </el-col>
                            </el-row>
                            <el-row v-if="item.CardDeductionLargessAmount > 0" class="padtp_10 padrt_10 border_top">
                              <el-col :span="8">
                                <el-form class="sale-ModifyPerformanceCommission-Handler" label-position="right" size="mini" label-width="120px">
                                  <el-form-item label="赠送卡抵扣业绩">
                                    <span slot="label">
                                      赠送卡抵扣业绩
                                      <el-popover placement="top-start" width="200" trigger="hover">
                                        <p>赠送卡抵扣业绩 = 赠送卡抵扣金额 x 赠送卡抵扣业绩占比 x 业绩占比</p>
                                        <p>
                                          员工赠送卡抵扣业绩占比参考值：
                                          <span v-if="employee.PerformanceSavingCardLargessRate != null"
                                            >{{ (employee.PerformanceSavingCardLargessRate * 100) | toFixed | NumFormat }}%</span
                                          >
                                          <span v-else>无</span>
                                        </p>

                                        <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                      </el-popover>
                                    </span>
                                    <el-input
                                      type="number"
                                      v-input-fixed
                                      class="input_type"
                                      style="width: 120px"
                                      v-model="employee.SavingCardLargessPerformance"
                                    >
                                      <template slot="append">元</template>
                                    </el-input>
                                  </el-form-item>
                                </el-form>
                              </el-col>
                              <el-col :span="8">
                                <el-form class="sale-ModifyPerformanceCommission-Handler" label-position="right" size="mini" label-width="150px">
                                  <el-form-item label="赠送卡抵扣比例提成">
                                    <span slot="label">
                                      赠送卡抵扣比例提成
                                      <el-popover placement="top-start" width="200" trigger="hover">
                                        <p>赠送卡抵扣比例提成 = 赠送卡抵扣业绩 x 赠送卡抵扣比例</p>
                                        <p v-if="employee.SavingCardLargessRate != null">
                                          赠送卡抵扣比例参考值：{{ employee.SavingCardLargessRate | toFixed | NumFormat }}%
                                        </p>
                                        <p v-else>赠送卡抵扣比例参考值：无</p>
                                        <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                      </el-popover>
                                    </span>
                                    <el-input
                                      type="number"
                                      v-input-fixed
                                      class="input_type"
                                      style="width: 100px"
                                      v-model="employee.SavingCardLargessRateCommission"
                                      v-enter-number2
                                    >
                                      <template slot="append">元</template>
                                    </el-input>
                                  </el-form-item>
                                </el-form>
                              </el-col>
                              <el-col :span="8">
                                <el-form class="sale-ModifyPerformanceCommission-Handler" label-position="right" size="mini" label-width="150px">
                                  <el-form-item label="赠送卡抵扣固定提成">
                                    <span slot="label">
                                      赠送卡抵扣固定提成
                                      <el-popover placement="top-start" width="200" trigger="hover">
                                        <p>赠送卡抵扣固定提成 = （赠送卡抵扣业绩 ÷ 购买金额）x 赠送卡抵扣固定 x 数量</p>
                                        <p v-if="employee.SavingCardLargessFixed != null">
                                          赠送卡抵扣固定参考值：¥ {{ employee.SavingCardLargessFixed | toFixed | NumFormat }}
                                        </p>
                                        <p v-else>赠送卡抵扣固定参考值：无</p>
                                        <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                      </el-popover>
                                    </span>
                                    <el-input
                                      type="number"
                                      v-input-fixed
                                      class="input_type"
                                      style="width: 100px"
                                      v-model="employee.SavingCardLargessFixedCommission"
                                      v-enter-number2
                                    >
                                      <template slot="append">元</template>
                                    </el-input>
                                  </el-form-item>
                                </el-form>
                              </el-col>
                            </el-row>
                          </el-col>
                          <el-col v-if="!item.IsLargess" :span="4" class="padtp_10" :class="index != 0 ? 'border_top' : ''">
                            <el-form class="sale-ModifyPerformanceCommission-Handler" label-position="right" size="mini" label-width="90px">
                              <el-form-item label="无业绩奖励">
                                <span slot="label">
                                  无业绩奖励
                                  <el-popover placement="top-start" width="200" trigger="hover">
                                    <p>无业绩奖励 = 无业绩奖励参考值 x 数量 x 经手人比例</p>
                                    <p v-if="employee.SpecialBenefit != null">无业绩奖励参考值：¥ {{ employee.SpecialBenefit | toFixed | NumFormat }}</p>
                                    <p v-else>无业绩奖励参考值：无</p>
                                    <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                  </el-popover>
                                </span>
                                <el-input
                                  type="number"
                                  v-input-fixed
                                  class="input_type"
                                  style="width: 100px"
                                  v-model="employee.SpecialBenefitCommission"
                                  v-enter-number2
                                >
                                  <template slot="append">元</template>
                                </el-input>
                              </el-form-item>
                            </el-form>
                          </el-col>
                        </el-row>
                      </el-col>
                    </el-row>
                  </div>
                </div>
              </div>
              <!-- 套餐卡-储值卡 -->
              <div v-if="packageCard.SavingCard.length > 0">
                <div v-for="(item, index) in packageCard.SavingCard" :key="index">
                  <el-row class="row_header_package_detail border_right border_left">
                    <el-col :span="5">套餐卡储值卡</el-col>
                    <el-col :span="2">购买数量</el-col>
                    <el-col :span="4">充值金额</el-col>
                    <el-col :span="4">赠送金额</el-col>
                    <el-col :span="5">支付金额</el-col>
                    <el-col :span="4">欠款金额</el-col>
                  </el-row>
                  <el-row>
                    <el-col :span="24" class="pad_10 border_right border_left border_bottom">
                      <el-col :span="24">
                        <el-col :span="5">
                          <div>
                            {{ item.SavingCardName }}
                            <span v-if="item.Alias">({{ item.Alias }})</span>
                            <el-tag v-if="item.IsLargess" size="mini" type="danger" class="marlt_10">赠</el-tag>
                          </div>
                        </el-col>
                        <el-col :span="2">x {{ item.Quantity * packageCard.Quantity }}</el-col>
                        <el-col :span="4">¥ {{ item.TotalAmount | toFixed | NumFormat }}</el-col>
                        <el-col :span="4">¥ {{ item.LargessAmount | toFixed | NumFormat }}</el-col>
                        <el-col :span="5">¥ {{ item.PayAmount | toFixed | NumFormat }}</el-col>
                        <el-col :span="4">¥ {{ item.ArrearAmount | toFixed | NumFormat }}</el-col>
                      </el-col>
                      <el-col :span="24" class="martp_5">
                        <el-col :span="15">
                          <div>
                            <span class="color_red font_12">¥ {{ item.Price | toFixed | NumFormat }}</span>
                            <span class="marlt_10 font_12 color_gray">赠送：¥ {{ item.LargessPrice }}</span>
                          </div>
                        </el-col>
                        <el-col :span="9">
                          <span class="color_gray font_12" v-if="item.PayAmount > 0">现金金额：¥ {{ item.PayAmount | toFixed | NumFormat }}</span>
                        </el-col>
                      </el-col>
                    </el-col>
                  </el-row>

                  <div>
                    <el-row
                      class="padlt_10 padrt_10 border_right border_left border_bottom font_12"
                      v-for="handler in item.SaleBillHandler"
                      :key="handler.SaleHandlerID"
                    >
                      <el-col :span="2" class="padtp_10 padbm_10 font_12 line_26">{{ handler.SaleHandlerName }}</el-col>
                      <el-col :span="22" class="border_left">
                        <el-row v-for="(employee, index) in handler.Employee" :key="employee.EmployeeID">
                          <el-col :span="4" class="padtp_10 padrt_10" :class="index != 0 ? 'border_top' : ''">
                            <el-form class="sale-ModifyPerformanceCommission-Handler" label-position="right" size="mini" label-width="120px" label-suffix=":">
                              <el-form-item :label="`${employee.EmployeeName}`">{{ employee.Scale }}%</el-form-item>
                            </el-form>
                          </el-col>
                          <el-col :span="16" class="border_left border_right">
                            <el-row v-if="item.PayAmount > 0" class="padtp_10 padrt_10" :class="index != 0 ? 'border_top' : ''">
                              <el-col :span="8">
                                <el-form class="sale-ModifyPerformanceCommission-Handler" label-position="right" size="mini" label-width="120px">
                                  <el-form-item label="现金业绩">
                                    <span slot="label">
                                      现金业绩
                                      <el-popover placement="top-start" width="200" trigger="hover">
                                        <p>现金业绩 = 现金付款金额 x 员工现金业绩占比 x 业绩占比</p>
                                        <p>
                                          员工现金业绩占比参考值：
                                          <span v-if="employee.PerformancePayRate != null"
                                            >{{ (employee.PerformancePayRate * 100) | toFixed | NumFormat }}%</span
                                          >
                                          <span v-else>无</span>
                                        </p>

                                        <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                      </el-popover>
                                    </span>
                                    <el-input
                                      type="number"
                                      v-input-fixed
                                      class="input_type"
                                      style="width: 120px"
                                      v-model="employee.PayPerformance"
                                      v-enter-number2
                                    >
                                      <template slot="append">元</template>
                                    </el-input>
                                  </el-form-item>
                                </el-form>
                              </el-col>
                              <el-col :span="8">
                                <el-form class="sale-ModifyPerformanceCommission-Handler" label-position="right" size="mini" label-width="150px">
                                  <el-form-item label="现金比例提成">
                                    <span slot="label">
                                      现金比例提成
                                      <el-popover placement="top-start" width="200" trigger="hover">
                                        <p>现金比例提成 = 现金业绩 x 现金比例</p>
                                        <p v-if="employee.PayRate != null">现金比例参考值：{{ employee.PayRate | toFixed | NumFormat }}%</p>
                                        <p v-else>现金比例参考值：无</p>
                                        <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                      </el-popover>
                                    </span>
                                    <el-input
                                      type="number"
                                      v-input-fixed
                                      class="input_type"
                                      style="width: 100px"
                                      v-model="employee.PayRateCommission"
                                      v-enter-number2
                                    >
                                      <template slot="append">元</template>
                                    </el-input>
                                  </el-form-item>
                                </el-form>
                              </el-col>
                              <el-col :span="8">
                                <el-form class="sale-ModifyPerformanceCommission-Handler" label-position="right" size="mini" label-width="150px">
                                  <el-form-item label="现金固定提成">
                                    <span slot="label">
                                      现金固定提成
                                      <el-popover placement="top-start" width="200" trigger="hover">
                                        <p>现金固定提成 = （现金业绩 ÷ 购买金额）x 现金固定 x 数量</p>
                                        <p v-if="employee.PayFixed != null">现金固定参考值：¥ {{ employee.PayFixed | toFixed | NumFormat }}</p>
                                        <p v-else>现金固定参考值：无</p>
                                        <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                      </el-popover>
                                    </span>
                                    <el-input
                                      type="number"
                                      v-input-fixed
                                      class="input_type"
                                      style="width: 100px"
                                      v-model="employee.PayFixedCommission"
                                      v-enter-number2
                                    >
                                      <template slot="append">元</template>
                                    </el-input>
                                  </el-form-item>
                                </el-form>
                              </el-col>
                            </el-row>
                          </el-col>
                          <el-col v-if="!item.IsLargess" :span="4" class="padtp_10" :class="index != 0 ? 'border_top' : ''">
                            <el-form class="sale-ModifyPerformanceCommission-Handler" label-position="right" size="mini" label-width="90px">
                              <el-form-item label="无业绩奖励">
                                <span slot="label">
                                  无业绩奖励
                                  <el-popover placement="top-start" width="200" trigger="hover">
                                    <p>无业绩奖励 = 无业绩奖励参考值 x 数量 x 经手人比例</p>
                                    <p v-if="employee.SpecialBenefit != null">无业绩奖励参考值：¥ {{ employee.SpecialBenefit | toFixed | NumFormat }}</p>
                                    <p v-else>无业绩奖励参考值：无</p>
                                    <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                  </el-popover>
                                </span>
                                <el-input
                                  type="number"
                                  v-input-fixed
                                  class="input_type"
                                  style="width: 100px"
                                  v-model="employee.SpecialBenefitCommission"
                                  v-enter-number2
                                >
                                  <template slot="append">元</template>
                                </el-input>
                              </el-form-item>
                            </el-form>
                          </el-col>
                        </el-row>
                      </el-col>
                    </el-row>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-scrollbar>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="visible_ = false" size="small">取消</el-button>
        <el-button type="primary" @click="saveSaleBill" size="small" v-prevent-click :loading="saveLoading">保存</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: "saleModifyHandledPerformance",

  props: {
    changePerformance: {
      type: Boolean,
      default: false,
      require: true,
    },
    visible: {
      type: Boolean,
      default: false,
      require: true,
    },

    loading: {
      type: Boolean,
      default: false,
    },
    PerformanceInfo: {
      type: Object,
      default() {
        return null;
      },
    },

    saveLoading: {
      type: Boolean,
      default: false,
      require: true,
    },
  },
  /** 监听数据变化   */
  watch: {
    visible: {
      immediate: true,
      handler(val) {
        this.visible_ = val;
      },
    },
  },
  /**  引入的组件  */
  components: {},
  /**  Vue 实例的数据对象**/
  data() {
    return {
      visible_: false,
    };
  },
  /**计算属性  */
  computed: {},
  /**  方法集合  */
  methods: {
    /**    */
    changeIsCalculatePassengerFlowClick(emp) {
      emp.IsCalculatePassengerFlow = !emp.IsCalculatePassengerFlow;
    },
    /**    */
    closeModifyPerformance() {
      let that = this;
      that.$emit("update:visible", false);
    },
    /**    */
    employeeHandleClick(type, item) {
      let that = this;
      that.$emit("employeeHandleClick", type, item);
    },
    /**    */
    changeSaleHandlerRate(item, employee, itemType) {
      let that = this;
      that.$emit("changeSaleHandlerRate", item, employee, itemType);
    },
    /**    */
    changePackageSaleHandlerRate(packageCard, packageHandler, employee) {
      let that = this;
      that.$emit("changePackageSaleHandlerRate", packageCard, packageHandler, employee);
    },
    /**    */
    saveSaleBill() {
      let that = this;
      that.$emit("saveSaleBill");
    },
  },
  /** 创建实例之前执行函数   */
  beforeCreate() {},
  /**  实例创建完成之后  */
  created() {},
  /**  在挂载开始之前被调用  */
  beforeMount() {},
  /**  实例被挂载后调用  */
  mounted() {},
  /**  数据更新 之前 调用  */
  beforeUpdate() {},
  /** 数据更新 完成 调用   */
  updated() {},
  /**  实例销毁之前调用  */
  beforeDestroy() {},
  /**  实例销毁后调用  */
  destroyed() {},
};
</script>

<style lang="scss">
.saleModifyHandledPerformance {
  .sale-ModifyPerformanceCommission-Handler {
    .el-form-item__label {
      font-size: 12px !important;
      line-height: 26px;
    }
    .el-form-item__content {
      font-size: 12px !important;
      line-height: 26px;
    }
    .el-form-item {
      margin-bottom: 10px;
    }
  }

  .el-scrollbar_height {
    height: calc(100% - 60px) !important ;
    .el-scrollbar__wrap {
      overflow-x: hidden;
    }
  }
  .cursorclass{
    cursor:pointer;
  }
}
</style>
