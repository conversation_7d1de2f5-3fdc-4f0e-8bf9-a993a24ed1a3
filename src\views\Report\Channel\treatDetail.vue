<template>
  <div class="content_body_nopadding ChannelTreatDetail" v-loading="loading">
    <el-tabs type="border-card">
      <el-tab-pane label="消耗明细">
        <div class="nav_header" style="padding: 0px">
          <el-form :inline="true" size="small" :model="searchTreatData" @submit.native.prevent>
            <el-form-item v-if="storeEntityList.length > 1" label="开单门店">
              <el-select
                v-model="searchTreatData.EntityID"
                clearable
                filterable
                placeholder="请选择开单门店"
                :default-first-option="true"
                @change="handleTreatSearch"
              >
                <el-option v-for="item in storeEntityList" :key="item.ID" :label="item.EntityName" :value="item.ID"> </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="时间筛选">
              <el-date-picker
                v-model="searchTreatData.QueryDate"
                :picker-options="pickerOptions"
                unlink-panels
                type="daterange"
                range-separator="至"
                value-format="yyyy-MM-dd"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                @change="handleTreatSearch"
                :clearable="false"
              ></el-date-picker>
            </el-form-item>
            <el-form-item label="订单来源">
              <el-select
                v-model="searchTreatData.Channel"
                clearable
                filterable
                placeholder="请选择订单来源"
                :default-first-option="true"
                @change="handleTreatSearch"
              >
                <el-option label="PC" value="PC"> </el-option>
                <el-option label="小程序" value="Miniprogram"> </el-option>
                <!-- <el-option label="商城" value="MicroMall"> </el-option> -->
              </el-select>
            </el-form-item>
            <el-form-item label="卡项类型">
              <el-select
                v-model="searchTreatData.TreatCardTypeName"
                clearable
                filterable
                placeholder="请选择卡项类型"
                :default-first-option="true"
                @change="handleTreatSearch"
              >
                <el-option label="项目卡" value="项目卡"></el-option>
                <el-option label="通用次卡" value="通用次卡"></el-option>
                <el-option label="时效卡" value="时效卡"></el-option>
                <el-option label="储值卡" value="储值卡"></el-option>
                <el-option label="产品卡" value="产品卡"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item v-if="BelongEntityList.length > 1" label="销售门店">
              <el-select
                v-model="searchTreatData.BuyEntityID"
                clearable
                filterable
                placeholder="请选择卡销售门店"
                :default-first-option="true"
                @change="handleTreatSearch"
              >
                <el-option v-for="item in BelongEntityList" :key="item.ID" :label="item.Name" :value="item.ID"> </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="商品名称">
              <el-input
                v-model="searchTreatData.GoodName"
                clearable
                @keyup.enter.native="handleTreatSearch"
                @clear="handleTreatSearch"
                placeholder="请输入消耗项目、产品名称"
              ></el-input>
            </el-form-item>

            <el-form-item label="客户信息">
              <el-input
                v-model="searchTreatData.CustomerName"
                clearable
                @keyup.enter.native="handleTreatSearch"
                @clear="handleTreatSearch"
                placeholder="请输入客户姓名、手机号"
              ></el-input>
            </el-form-item>

            <el-form-item v-if="BelongEntityList.length > 1" label="所属组织">
              <el-select
                v-model="searchTreatData.BelongEntityID"
                clearable
                filterable
                placeholder="请选择客户所属组织"
                :default-first-option="true"
                @change="handleTreatSearch"
              >
                <el-option v-for="item in BelongEntityList" :key="item.ID" :label="item.Name" :value="item.ID"> </el-option>
              </el-select>
            </el-form-item>

            <el-form-item label="会员等级">
              <el-select v-model="searchTreatData.CustomerLevelID" placeholder="请选择会员等级" filterable size="small" clearable @change="handleTreatSearch">
                <el-option v-for="item in customerLevel" :key="item.ID" :label="item.Name" :value="item.ID"> </el-option>
              </el-select>
            </el-form-item>

            <el-form-item label="信息来源">
              <el-cascader
                v-model="searchTreatData.CustomerSourceID"
                placeholder="请选择客户信息来源"
                :options="customerSource"
                :props="{
                  checkStrictly: true,
                  children: 'Child',
                  value: 'ID',
                  label: 'Name',
                  emitPath: false,
                }"
                :show-all-levels="false"
                filterable
                clearable
                @change="handleTreatSearch"
              ></el-cascader>
            </el-form-item>
            <el-form-item label="渠道来源">
              <el-input
                v-model="searchTreatData.ChannelName"
                clearable
                @keyup.enter.native="handleTreatSearch"
                @clear="handleTreatSearch"
                placeholder="请输入渠道来源"
              ></el-input>
            </el-form-item>

            <el-form-item label="介绍人">
              <el-input
                v-model="searchTreatData.IntroducerName"
                clearable
                @keyup.enter.native="handleTreatSearch"
                @clear="handleTreatSearch"
                placeholder="请输入介绍人"
              ></el-input>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" v-prevent-click @click="handleTreatSearch">搜索</el-button>
            </el-form-item>
            <el-form-item>
              <el-button v-if="treatDetailExport" type="primary" size="small" :loading="downloadLoading" @click="downloadTreatExcel">导出</el-button>
            </el-form-item>
          </el-form>
        </div>
        <el-table size="small" show-summary :summary-method="getTreatDetailListSummaries" :data="treatDetailList">
          <el-table-column prop="TreatBillID" label="订单编号"></el-table-column>
          <el-table-column prop="BillDate" label="下单日期">
            <template slot-scope="scope">
              {{ scope.row.BillDate | dateFormat('YYYY-MM-DD HH:mm') }}
            </template>
          </el-table-column>
          <el-table-column prop="EntityName" label="下单门店"></el-table-column>
          <el-table-column prop="EmployeeName" label="录单人"></el-table-column>
          <el-table-column prop="Channel" label="订单来源"></el-table-column>
          <el-table-column label="客户信息">
            <el-table-column prop="CustomerName" width="150px" label="客户">
              <template slot-scope="scope">
                <div>{{ scope.row.CustomerName }}</div>
                <div v-if="scope.row.CustomerPhoneNumber">手机号：{{ scope.row.CustomerPhoneNumber | hidephone }}</div>
              </template>
            </el-table-column>
            <el-table-column prop="CustomerEntityName" label="所属组织"></el-table-column>
            <el-table-column prop="CustomerLevelName" label="会员等级"></el-table-column>
            <el-table-column prop="CustomerSourceName" label="信息来源"></el-table-column>
            <el-table-column prop="ChannelName" label="渠道来源"></el-table-column>
            <el-table-column prop="IntroducerName" label="介绍人"></el-table-column>
          </el-table-column>
          <el-table-column label="卡项信息">
            <el-table-column prop="CardName" label="卡项名称"></el-table-column>
            <el-table-column prop="TreatCardTypeName" label="卡项类型"></el-table-column>
            <el-table-column prop="BuyEntityName" label="销售门店"></el-table-column>
            <el-table-column prop="IsLargess" label="是否赠送">
              <template slot-scope="scope">
                <div v-if="scope.row.IsLargess">是</div>
                <div v-else>否</div>
              </template>
            </el-table-column>
            <el-table-column prop="AccountRemark" label="备注信息" show-overflow-tooltip></el-table-column>
          </el-table-column>
          <el-table-column label="消耗商品信息">
            <el-table-column prop="GoodName" label="商品名称"></el-table-column>
            <!-- <el-table-column prop="CategoryName" label="商品分类"></el-table-column> -->
            <el-table-column align="right" prop="Price" label="单价">
              <template slot-scope="scope">
                {{ scope.row.Price | toFixed | NumFormat }}
              </template>
            </el-table-column>
            <el-table-column align="right" prop="Quantity" label="消耗数量"></el-table-column>
            <el-table-column align="right" prop="PreferentialAmount" label="优惠金额">
              <template align="right" slot-scope="scope">
                <div v-if="scope.row.PreferentialAmount < 0" class="color_red">{{ scope.row.PreferentialAmount | toFixed | NumFormat }}</div>
                <div v-else-if="scope.row.PreferentialAmount > 0" class="color_green">+{{ scope.row.PreferentialAmount | toFixed | NumFormat }}</div>
                <div v-else>0.00</div>
              </template>
            </el-table-column>
            <el-table-column align="right" prop="TotalAmount" label="合计金额">
              <template slot-scope="scope">
                {{ scope.row.TotalAmount | toFixed | NumFormat }}
              </template>
            </el-table-column>
          </el-table-column>
          <el-table-column label="消耗金额">
            <el-table-column align="right" prop="TreatPayAmount" label="现金金额">
              <template slot-scope="scope">
                {{ scope.row.TreatPayAmount | toFixed | NumFormat }}
              </template>
            </el-table-column>

            <el-table-column align="right" prop="TreatCardAmount" label="卡扣金额">
              <template slot-scope="scope">
                {{ scope.row.TreatCardAmount | toFixed | NumFormat }}
              </template>
            </el-table-column>

            <el-table-column align="right" prop="TreatCardLargessAmount" label="赠卡扣金额">
              <template slot-scope="scope">
                {{ scope.row.TreatCardLargessAmount | toFixed | NumFormat }}
              </template>
            </el-table-column>

            <el-table-column align="right" prop="TreatLargessAmount" label="赠送金额">
              <template slot-scope="scope">
                {{ scope.row.TreatLargessAmount | toFixed | NumFormat }}
              </template>
            </el-table-column>
          </el-table-column>
        </el-table>
        <div class="pad_15 text_right">
          <el-pagination
            background
            v-if="treatPaginations.total > 0"
            @current-change="handleTreatDetailPageChange"
            :current-page.sync="treatPaginations.page"
            :page-size="treatPaginations.page_size"
            :layout="treatPaginations.layout"
            :total="treatPaginations.total"
          ></el-pagination>
        </div>
      </el-tab-pane>
      <el-tab-pane label="退消耗明细">
        <div class="nav_header" style="padding: 0px">
          <el-form :inline="true" size="small" :model="searchTreatRefundData" @submit.native.prevent>
            <el-form-item v-if="storeEntityList.length > 1" label="开单门店">
              <el-select
                v-model="searchTreatRefundData.EntityID"
                clearable
                filterable
                placeholder="请选择开单门店"
                :default-first-option="true"
                @change="handleTreatRefundSearch"
              >
                <el-option v-for="item in storeEntityList" :key="item.ID" :label="item.EntityName" :value="item.ID"> </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="时间筛选">
              <el-date-picker
                v-model="searchTreatRefundData.QueryDate"
                :picker-options="treatRefundPickerOptions"
                unlink-panels
                type="daterange"
                range-separator="至"
                value-format="yyyy-MM-dd"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                @change="handleTreatRefundSearch"
                :clearable="false"
              ></el-date-picker>
            </el-form-item>

            <el-form-item label="订单来源">
              <el-select
                v-model="searchTreatRefundData.Channel"
                clearable
                filterable
                placeholder="请选择订单来源"
                :default-first-option="true"
                @change="handleTreatRefundSearch"
              >
                <el-option label="PC" value="PC"> </el-option>
                <el-option label="小程序" value="Miniprogram"> </el-option>
                <!-- <el-option label="商城" value="MicroMall"> </el-option> -->
              </el-select>
            </el-form-item>
            <el-form-item label="卡项类型">
              <el-select
                v-model="searchTreatRefundData.TreatCardTypeName"
                clearable
                filterable
                placeholder="请选择卡项类型"
                :default-first-option="true"
                @change="handleTreatRefundSearch"
              >
                <el-option label="项目卡" value="项目卡"></el-option>
                <el-option label="通用次卡" value="通用次卡"></el-option>
                <el-option label="时效卡" value="时效卡"></el-option>
                <el-option label="储值卡" value="储值卡"></el-option>
                <el-option label="产品卡" value="产品卡"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item v-if="BelongEntityList.length > 1" label="销售门店">
              <el-select
                v-model="searchTreatRefundData.BuyEntityID"
                clearable
                filterable
                placeholder="请选择卡销售门店"
                :default-first-option="true"
                @change="handleTreatRefundSearch"
              >
                <el-option v-for="item in BelongEntityList" :key="item.ID" :label="item.Name" :value="item.ID"> </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="商品名称">
              <el-input
                v-model="searchTreatRefundData.GoodName"
                clearable
                @keyup.enter.native="handleTreatRefundSearch"
                @clear="handleTreatRefundSearch"
                placeholder="请输入退消耗项目、产品名称"
              ></el-input>
            </el-form-item>
            <el-form-item label="客户信息">
              <el-input
                v-model="searchTreatRefundData.CustomerName"
                clearable
                @keyup.enter.native="handleTreatRefundSearch"
                @clear="handleTreatRefundSearch"
                placeholder="请输入客户姓名、手机号"
              ></el-input>
            </el-form-item>
            <el-form-item v-if="BelongEntityList.length > 1" label="所属组织">
              <el-select
                v-model="searchTreatRefundData.BelongEntityID"
                clearable
                filterable
                placeholder="请选择客户所属组织"
                :default-first-option="true"
                @change="handleTreatRefundSearch"
              >
                <el-option v-for="item in BelongEntityList" :key="item.ID" :label="item.Name" :value="item.ID"> </el-option>
              </el-select>
            </el-form-item>

            <el-form-item label="会员等级">
              <el-select
                v-model="searchTreatRefundData.CustomerLevelID"
                placeholder="请选择会员等级"
                filterable
                size="small"
                clearable
                @change="handleTreatRefundSearch"
              >
                <el-option v-for="item in customerLevel" :key="item.ID" :label="item.Name" :value="item.ID"> </el-option>
              </el-select>
            </el-form-item>

            <el-form-item label="信息来源">
              <el-cascader
                v-model="searchTreatRefundData.CustomerSourceID"
                placeholder="请选择客户信息来源"
                :options="customerSource"
                :props="{
                  checkStrictly: true,
                  children: 'Child',
                  value: 'ID',
                  label: 'Name',
                  emitPath: false,
                }"
                :show-all-levels="false"
                filterable
                clearable
                @change="handleTreatRefundSearch"
              ></el-cascader>
            </el-form-item>
            <el-form-item label="渠道来源">
              <el-input
                v-model="searchTreatRefundData.ChannelName"
                clearable
                @keyup.enter.native="handleTreatRefundSearch"
                @clear="handleTreatRefundSearch"
                placeholder="请输入渠道来源"
              ></el-input>
            </el-form-item>

            <el-form-item label="介绍人">
              <el-input
                v-model="searchTreatRefundData.IntroducerName"
                clearable
                @keyup.enter.native="handleTreatRefundSearch"
                @clear="handleTreatRefundSearch"
                placeholder="请输入介绍人"
              ></el-input>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" v-prevent-click @click="handleTreatRefundSearch">搜索</el-button>
            </el-form-item>

            <el-form-item>
              <el-button v-if="refundTreatDetailExport" type="primary" size="small" :loading="downloadLoading" @click="downloadreatRefundExcel">导出</el-button>
            </el-form-item>
          </el-form>
        </div>
        <el-table size="small" show-summary :summary-method="getTreatRefundDetailListSummaries" :data="treatRefundDetailList">
          <el-table-column prop="TreatBillID" label="订单编号"></el-table-column>
          <el-table-column prop="BillDate" label="下单日期">
            <template slot-scope="scope">
              {{ scope.row.BillDate | dateFormat('YYYY-MM-DD HH:mm') }}
            </template>
          </el-table-column>
          <el-table-column prop="EntityName" label="下单门店"></el-table-column>
          <el-table-column prop="EmployeeName" label="录单人"></el-table-column>
          <el-table-column prop="Channel" label="订单来源"></el-table-column>
          <el-table-column label="客户信息">
            <el-table-column prop="CustomerName" width="150px" label="客户">
              <template slot-scope="scope">
                <div>
                  <span class="marrt_10">{{ scope.row.CustomerName }}</span>
                  <!-- <span v-if="scope.row.CustomerCode">({{ scope.row.CustomerCode }})</span> -->
                </div>
                <div v-if="scope.row.CustomerPhoneNumber">手机号：{{ scope.row.CustomerPhoneNumber | hidephone }}</div>
              </template>
            </el-table-column>
            <el-table-column prop="CustomerEntityName" label="所属组织"></el-table-column>
            <el-table-column prop="CustomerLevelName" label="会员等级"></el-table-column>
            <el-table-column prop="CustomerSourceName" label="信息来源"></el-table-column>
            <el-table-column prop="ChannelName" label="渠道来源"></el-table-column>
            <el-table-column prop="IntroducerName" label="介绍人"></el-table-column>
          </el-table-column>
          <el-table-column label="卡项信息">
            <el-table-column prop="CardName" label="卡名称"></el-table-column>
            <el-table-column prop="TreatCardTypeName" label="卡类型"></el-table-column>
            <el-table-column prop="BuyEntityName" label="销售门店"></el-table-column>
            <el-table-column prop="IsLargess" label="是否赠送">
              <template slot-scope="scope">
                <div v-if="scope.row.IsLargess">是</div>
                <div v-else>否</div>
              </template>
            </el-table-column>
          </el-table-column>
          <el-table-column label="退消耗商品信息">
            <el-table-column prop="GoodName" label="商品名称"></el-table-column>
            <el-table-column prop="CategoryName" label="商品类别"></el-table-column>
            <el-table-column align="right" prop="Quantity" label="数量"> </el-table-column>
            <el-table-column align="right" prop="TotalAmount" label="合计金额">
              <template slot-scope="scope">
                {{ scope.row.TotalAmount | toFixed | NumFormat }}
              </template>
            </el-table-column>
          </el-table-column>
          <el-table-column label="退消耗金额">
            <el-table-column align="right" prop="TreatPayAmount" label="现金金额">
              <template slot-scope="scope">
                {{ scope.row.TreatPayAmount | toFixed | NumFormat }}
              </template>
            </el-table-column>

            <el-table-column align="right" prop="TreatCardAmount" label="卡扣金额">
              <template slot-scope="scope">
                {{ scope.row.TreatCardAmount | toFixed | NumFormat }}
              </template>
            </el-table-column>

            <el-table-column align="right" prop="TreatCardLargessAmount" label="赠卡扣金额">
              <template slot-scope="scope">
                {{ scope.row.TreatCardLargessAmount | toFixed | NumFormat }}
              </template>
            </el-table-column>

            <el-table-column align="right" prop="TreatLargessAmount" label="赠送金额">
              <template slot-scope="scope">
                {{ scope.row.TreatLargessAmount | toFixed | NumFormat }}
              </template>
            </el-table-column>
          </el-table-column>
        </el-table>
        <div class="pad_15 text_right">
          <el-pagination
            background
            v-if="treatRefundPaginations.total > 0"
            @current-change="handleTreatRefundDetailPageChange"
            :current-page.sync="treatRefundPaginations.page"
            :page-size="treatRefundPaginations.page_size"
            :layout="treatRefundPaginations.layout"
            :total="treatRefundPaginations.total"
          ></el-pagination>
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import EntityAPI from '@/api/Report/Common/entity';
import APICustomerLevel from '@/api/CRM/Customer/customerLevel';
import APICustomerSource from '@/api/CRM/Customer/customerSource';
import API from '@/api/Report/Channel/consultantAndDeveloperTreatPerformanceDetail';
import permission from '@/components/js/permission.js';
const dayjs = require('dayjs');
const isoWeek = require('dayjs/plugin/isoWeek');
dayjs.extend(isoWeek);

export default {
  name: 'ChannelTreatDetail',
  data() {
    return {
      loading: false,
      downloadLoading: false,
      searchTreatData: {
        EntityID: null,
        QueryDate: [new Date(), new Date()],
        TreatCardTypeName: '',
        GoodName: '',
        CustomerName: null,
        BelongEntityID: '',
        CustomerLevelID: '',
        CustomerSourceID: '',
        Channel: '', //订单渠道：PC: PC，小程序：Miniprogram，商城：MicroMall
        IntroducerName: '', //介绍人
        ChannelName: '', //渠道
      },
      searchTreatRefundData: {
        EntityID: null,
        QueryDate: [new Date(), new Date()],
        TreatCardTypeName: '',
        GoodName: '',
        CustomerName: null,
        CustomerLevelID: '',
        CustomerSourceID: '',
        Channel: '', //订单渠道：PC: PC，小程序：Miniprogram，商城：MicroMall
        IntroducerName: '', //介绍人
        ChannelName: '', //渠道
      },
      storeEntityList: [], //门店列表
      treatDetailList: [], //消耗明细
      treatDetailSum: {},

      treatRefundDetailList: [], //消耗退款明细
      treatRefundDetailSum: {},

      //需要给分页组件传的信息
      treatPaginations: {
        page: 1, // 当前位于哪页
        total: 0, // 总数
        page_size: 10, // 1页显示多少条
        layout: 'total, prev, pager, next,jumper', // 翻页属性
      },

      //需要给分页组件传的信息
      treatRefundPaginations: {
        page: 1, // 当前位于哪页
        total: 0, // 总数
        page_size: 10, // 1页显示多少条
        layout: 'total, prev, pager, next,jumper', // 翻页属性
      },
      refundTreatDetailExport: false,
      treatDetailExport: false,
      BelongEntityList: [],
      pickerOptions: {
        shortcuts: [
          {
            text: '今天',
            onClick: (picker) => {
              let end = new Date();
              let start = new Date();
              picker.$emit('pick', [start, end]);
            },
          },
          {
            text: '本周',
            onClick: (picker) => {
              let end = new Date();
              let weekDay = dayjs(end).isoWeekday();
              let start = dayjs(end)
                .subtract(weekDay - 1, 'day')
                .toDate();
              picker.$emit('pick', [start, end]);
            },
          },
          {
            text: '本月',
            onClick: (picker) => {
              let end = new Date();
              let start = dayjs(dayjs(end).format('YYYY-MM') + '01').toDate();
              picker.$emit('pick', [start, end]);
            },
          },
        ],
      },
      treatRefundPickerOptions: {
        shortcuts: [
          {
            text: '今天',
            onClick: (picker) => {
              let end = new Date();
              let start = new Date();
              picker.$emit('pick', [start, end]);
            },
          },
          {
            text: '本周',
            onClick: (picker) => {
              let end = new Date();
              let weekDay = dayjs(end).isoWeekday();
              let start = dayjs(end)
                .subtract(weekDay - 1, 'day')
                .toDate();
              picker.$emit('pick', [start, end]);
            },
          },
          {
            text: '本月',
            onClick: (picker) => {
              let end = new Date();
              let start = dayjs(dayjs(end).format('YYYY-MM') + '01').toDate();
              picker.$emit('pick', [start, end]);
            },
          },
        ],
      },
      customerLevel: [],
      customerSource: [],
    };
  },
  methods: {
    beforeRouteEnter(to, from, next) {
      next((vm) => {
        vm.refundTreatDetailExport = permission.permission(to.meta.Permission, 'Report-Channel-TreatDetail-Export');
        vm.treatDetailExport = permission.permission(to.meta.Permission, 'Report-Channel-TreatDetail-Export');
      });
    },
    //获得当前用户下的权限门店
    getstoreEntityList() {
      var that = this;
      that.loading = true;
      EntityAPI.getStoreEntityList()
        .then((res) => {
          if (res.StateCode == 200) {
            that.storeEntityList = res.Data;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },
    handleTreatSearch() {
      var that = this;
      that.treatPaginations.page = 1;
      that.treatSearch();
    },
    handleTreatDetailPageChange(page) {
      this.treatPaginations.page = page;
      this.treatSearch();
    },
    // 消耗搜索
    treatSearch() {
      var that = this;
      if (that.searchTreatData.QueryDate != null) {
        if (dayjs(that.searchTreatData.QueryDate[0]).add(366, 'day').valueOf() < dayjs(that.searchTreatData.QueryDate[1]).valueOf()) {
          that.$message.error('时间筛选范围不能超366天');
          return;
        }
        var params = {
          EntityID: that.searchTreatData.EntityID,
          StartDate: that.searchTreatData.QueryDate[0],
          // StartDate: "2021-03-02",
          EndDate: that.searchTreatData.QueryDate[1],
          TreatCardTypeName: that.searchTreatData.TreatCardTypeName.trim(),
          GoodName: that.searchTreatData.GoodName.trim(),
          IsLargess: that.searchTreatData.IsLargess,
          PageNum: that.treatPaginations.page,
          CustomerName: that.searchTreatData.CustomerName,
          BelongEntityID: that.searchTreatData.BelongEntityID,
          BuyEntityID: that.searchTreatData.BuyEntityID,
          CustomerLevelID: that.searchTreatData.CustomerLevelID, //顾客等级
          CustomerSourceID: that.searchTreatData.CustomerSourceID, //顾客来源

          Channel: that.searchTreatData.Channel, //订单渠道：PC: PC，小程序：Miniprogram，商城：MicroMall
          IntroducerName: that.searchTreatData.IntroducerName, //介绍人
          ChannelName: that.searchTreatData.ChannelName, //渠道
        };
        that.loading = true;
        API.channelTreatDetailStatement_list(params)
          .then((res) => {
            if (res.StateCode == 200) {
              that.treatDetailSum = res.Data.channelTreatDetailSumStatementForm;
              that.treatDetailList = res.Data.channelTreatDetailStatementForms.List;
              that.treatPaginations.total = res.Data.channelTreatDetailStatementForms.Total;
              that.treatPaginations.page_size = res.Data.channelTreatDetailStatementForms.PageSize;
            } else {
              that.$message.error({
                message: res.Message,
                duration: 2000,
              });
            }
          })
          .finally(function () {
            that.loading = false;
          });
      }
    },
    getTreatDetailListSummaries({ columns }) {
      let that = this;
      const sums = [];
      let filter_NumFormat = this.$options.filters['NumFormat'];
      let toFixed = this.$options.filters['toFixed'];
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = <span class="font_weight_600">合计</span>;
          return;
        }
        let value = that.treatDetailSum ? that.treatDetailSum[column.property] : 0;
        switch (column.property) {
          case 'PreferentialAmount':
          case 'TotalAmount':
          case 'TreatPayAmount':
          case 'TreatCardAmount':
          case 'TreatCardLargessAmount':
          case 'TreatLargessAmount':
            if (that.treatDetailSum && that.treatDetailSum[column.property] < 0) {
              sums[index] = <span class="font_weight_600 color_red">{filter_NumFormat(toFixed(value))}</span>;
            } else {
              sums[index] = <span class="font_weight_600">{filter_NumFormat(value)}</span>;
            }
            break;
          default:
            sums[index] = <span class="font_weight_600"></span>;
        }
      });
      return sums;
    },

    handleTreatRefundSearch() {
      var that = this;
      that.treatRefundPaginations.page = 1;
      that.treatRefundSearch();
    },
    handleTreatRefundDetailPageChange(page) {
      this.treatRefundPaginations.page = page;
      this.treatRefundSearch();
    },
    // 退消耗搜索
    treatRefundSearch() {
      var that = this;
      if (that.searchTreatRefundData.QueryDate != null) {
        if (dayjs(that.searchTreatRefundData.QueryDate[0]).add(366, 'day').valueOf() < dayjs(that.searchTreatRefundData.QueryDate[1]).valueOf()) {
          that.$message.error('时间筛选范围不能超366天');
          return;
        }
        var params = {
          EntityID: that.searchTreatRefundData.EntityID,
          StartDate: that.searchTreatRefundData.QueryDate[0],
          EndDate: that.searchTreatRefundData.QueryDate[1],
          TreatCardTypeName: that.searchTreatRefundData.TreatCardTypeName.trim(),
          GoodName: that.searchTreatRefundData.GoodName.trim(),
          IsLargess: that.searchTreatRefundData.IsLargess,
          PageNum: that.treatRefundPaginations.page,
          CustomerName: that.searchTreatRefundData.CustomerName,
          BelongEntityID: that.searchTreatRefundData.BelongEntityID,
          BuyEntityID: that.searchTreatRefundData.BuyEntityID,
          CustomerLevelID: that.searchTreatRefundData.CustomerLevelID, //顾客等级
          CustomerSourceID: that.searchTreatRefundData.CustomerSourceID, //顾客来源

          Channel: that.searchTreatRefundData.Channel, //订单渠道：PC: PC，小程序：Miniprogram，商城：MicroMall
          IntroducerName: that.searchTreatRefundData.IntroducerName, //介绍人
          ChannelName: that.searchTreatRefundData.ChannelName, //渠道
        };
        that.loading = true;
        API.channelTreatRefundDetailStatement_list(params)
          .then((res) => {
            if (res.StateCode == 200) {
              that.treatRefundDetailSum = res.Data.channelTreatRefundDetailSumStatementForm;
              that.treatRefundDetailList = res.Data.channelTreatRefundDetailStatementForms.List;
              that.treatRefundPaginations.total = res.Data.channelTreatRefundDetailStatementForms.Total;
              that.treatRefundPaginations.page_size = res.Data.channelTreatRefundDetailStatementForms.PageSize;
            } else {
              that.$message.error({
                message: res.Message,
                duration: 2000,
              });
            }
          })
          .finally(function () {
            that.loading = false;
          });
      }
    },
    getTreatRefundDetailListSummaries({ columns }) {
      let that = this;
      const sums = [];
      let filter_NumFormat = this.$options.filters['NumFormat'];
      let toFixed = this.$options.filters['toFixed'];
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = <span class="font_weight_600">合计</span>;
          return;
        }
        let value = that.treatRefundDetailSum ? that.treatRefundDetailSum[column.property] : 0;
        switch (column.property) {
          case 'TotalAmount':
          case 'TreatPayAmount':
          case 'TreatCardAmount':
          case 'TreatCardLargessAmount':
          case 'TreatLargessAmount':
            sums[index] = <span class="font_weight_600">{filter_NumFormat(toFixed(value))}</span>;
            break;
          default:
            sums[index] = '';
        }
      });
      return sums;
    },
    /** 数据导出 消耗明细 */
    downloadTreatExcel() {
      var that = this;
      if (that.searchTreatData.QueryDate != null) {
        if (dayjs(that.searchTreatData.QueryDate[0]).add(366, 'day').valueOf() < dayjs(that.searchTreatData.QueryDate[1]).valueOf()) {
          that.$message.error('时间筛选范围不能超366天');
          return;
        }
        var params = {
          EntityID: that.searchTreatData.EntityID,
          StartDate: that.searchTreatData.QueryDate[0],
          EndDate: that.searchTreatData.QueryDate[1],
          TreatCardTypeName: that.searchTreatData.TreatCardTypeName.trim(),
          GoodName: that.searchTreatData.GoodName.trim(),
          IsLargess: that.searchTreatData.IsLargess,
          BelongEntityID: that.searchTreatData.BelongEntityID,
          BuyEntityID: that.searchTreatData.BuyEntityID,
          CustomerLevelID: that.searchTreatData.CustomerLevelID, //顾客等级
          CustomerSourceID: that.searchTreatData.CustomerSourceID, //顾客来源
          CustomerName: that.searchTreatData.CustomerName,

          Channel: that.searchTreatData.Channel, //订单渠道：PC: PC，小程序：Miniprogram，商城：MicroMall
          IntroducerName: that.searchTreatData.IntroducerName, //介绍人
          ChannelName: that.searchTreatData.ChannelName, //渠道
        };

        that.downloadLoading = true;
        API.channelTreatDetailStatement_excel(params)
          .then((res) => {
            this.$message.success({
              message: '正在导出',
              duration: '4000',
            });
            const link = document.createElement('a');
            let blob = new Blob([res], { type: 'application/octet-stream' });
            link.style.display = 'none';
            link.href = URL.createObjectURL(blob);
            link.download = '渠道消耗明细.xlsx'; //下载的文件名
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
          })
          .finally(function () {
            that.downloadLoading = false;
          });
      }
    },

    /** 数据导出 退消耗明细 */
    downloadreatRefundExcel() {
      var that = this;
      if (that.searchTreatRefundData.QueryDate != null) {
        if (dayjs(that.searchTreatRefundData.QueryDate[0]).add(366, 'day').valueOf() < dayjs(that.searchTreatRefundData.QueryDate[1]).valueOf()) {
          that.$message.error('时间筛选范围不能超366天');
          return;
        }
        var params = {
          EntityID: that.searchTreatRefundData.EntityID,
          StartDate: that.searchTreatRefundData.QueryDate[0],

          EndDate: that.searchTreatRefundData.QueryDate[1],
          TreatCardTypeName: that.searchTreatRefundData.TreatCardTypeName.trim(),
          GoodName: that.searchTreatRefundData.GoodName.trim(),
          IsLargess: that.searchTreatRefundData.IsLargess,
          BelongEntityID: that.searchTreatRefundData.BelongEntityID,
          BuyEntityID: that.searchTreatRefundData.BuyEntityID,
          CustomerLevelID: that.searchTreatRefundData.CustomerLevelID, //顾客等级
          CustomerSourceID: that.searchTreatRefundData.CustomerSourceID, //顾客来源
          CustomerName: that.searchTreatRefundData.CustomerName,

          Channel: that.searchTreatRefundData.Channel, //订单渠道：PC: PC，小程序：Miniprogram，商城：MicroMall
          IntroducerName: that.searchTreatRefundData.IntroducerName, //介绍人
          ChannelName: that.searchTreatRefundData.ChannelName, //渠道
        };

        that.downloadLoading = true;
        API.channelTreatRefundDetailStatement_excel(params)
          .then((res) => {
            this.$message.success({
              message: '正在导出',
              duration: '4000',
            });
            const link = document.createElement('a');
            let blob = new Blob([res], { type: 'application/octet-stream' });
            link.style.display = 'none';
            link.href = URL.createObjectURL(blob);
            link.download = '渠道退消耗明细.xlsx'; //下载的文件名
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
          })
          .finally(function () {
            that.downloadLoading = false;
          });
      }
    },

    /**    */
    async getAllEntity() {
      let that = this;
      let res = await API.allEntity();
      if (res.StateCode == 200) {
        that.BelongEntityList = res.Data;
      } else {
        that.$message.error(res.Message);
      }
    },

    // 顾客来源
    CustomerSourceData: function () {
      var that = this;
      var params = {
        Name: '',
        Active: true,
      };
      APICustomerSource.getCustomerSource(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.customerSource = res.Data;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },
    /* 顾客等级 */
    CustomerLevelData() {
      var that = this;
      var params = {
        Name: '',
        Active: true,
      };
      APICustomerLevel.getCustomerLevel(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.customerLevel = res.Data;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },
  },
  mounted() {
    var that = this;
    that.refundTreatDetailExport = permission.permission(that.$route.meta.Permission, 'Report-Channel-RefundTreatDetail-Export');
    that.treatDetailExport = permission.permission(that.$route.meta.Permission, 'Report-Channel-TreatDetail-Export');
    that.getstoreEntityList();
    that.handleTreatSearch();
    that.handleTreatRefundSearch();
    that.getAllEntity();
    that.CustomerSourceData();
    that.CustomerLevelData();
  },
};
</script>

<style lang="scss">
.ChannelTreatDetail {
  .el-tabs--border-card {
    border: 0px !important;
    box-shadow: 0 0px 0px 0 rgba(0, 0, 0, 0), 0 0 0px 0 rgba(0, 0, 0, 0);
  }

  .el-form-item--mini.el-form-item,
  .el-form-item--small.el-form-item {
    margin-bottom: 15px;
  }
}
</style>
