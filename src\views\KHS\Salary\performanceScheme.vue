<template>
  <div class="content_body SalaryPerformanceScheme">
    <!-- 头部 -->
    <div class="nav_header">
      <el-row>
        <el-col :span="20">
          <el-form :inline="true" size="small" @submit.native.prevent @keyup.enter.native="handleSearch">
            <el-form-item label="业绩取值方案">
              <el-input v-model="searchData.performanceName" size="small" placeholder="输入业绩取值方案搜索" clearable @clear="handleSearch"></el-input>
            </el-form-item>
            <el-form-item label="有效性">
              <el-select v-model="searchData.isValidity" placeholder="选择有效性" clearable size="small" @change="handleSearch">
                <el-option label="有效" :value="true"></el-option>
                <el-option label="无效" :value="false"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" size="small" @click="handleSearch" v-prevent-click>搜索</el-button>
            </el-form-item>
          </el-form>
        </el-col>
        <el-col :span="4" class="text_right">
          <el-button type="primary" size="small" @click="handleShow('newDialogVisible')" v-prevent-click>新增</el-button>
        </el-col>
      </el-row>
    </div>
    <!-- 表格 -->
    <div>
      <el-table size="small" :data="tableData" v-loading="loading">
        <el-table-column prop="Name" label="业绩取值方案"></el-table-column>
        <el-table-column prop="Active" :formatter="(row) => (row.Active ? '有效' : '无效')" label="有效性"> </el-table-column>
        <el-table-column label="操作" width="80">
          <template slot-scope="scope">
            <el-button type="primary" size="small" @click="handleShow('editDialogVisible', scope.row)" v-prevent-click>编辑 </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <!-- 分页 -->
    <div class="pad_15 text_right">
      <el-pagination
        background
        v-if="paginations.total > 0"
        @current-change="handleCurrentChange"
        :current-page.sync="paginations.page"
        :page-size="paginations.page_size"
        :layout="paginations.layout"
        :total="paginations.total"
      ></el-pagination>
    </div>
    <!-- 新增业绩取值方案弹出层 -->
    <el-dialog title="新增业绩取值方案" :visible.sync="newDialogVisible" width="500px" @close="handleClose('newDialogVisible', 'ruleForm')">
      <el-form size="small" :model="ruleForm" ref="ruleForm" label-width="110px" class="demo-ruleForm" @submit.native.prevent>
        <el-form-item
          label="业绩取值方案"
          prop="PerformancePlan"
          :rules="[
            {
              required: true,
              message: '请输入业绩取值方案名称',
              trigger: 'blur',
            },
          ]"
          label-width="110px"
        >
          <el-input v-model="ruleForm.PerformancePlan"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button size="small" @click="newDialogVisible = false" v-prevent-click>取 消</el-button>
        <el-button size="small" type="primary" @click="handleSave('newDialogVisible')" v-prevent-click :loading="faSaveLoading">保 存</el-button>
      </span>
    </el-dialog>
    <!--编辑业绩取值方案弹出层-->
    <el-dialog title="编辑业绩取值方案" :visible.sync="editDialogVisible" width="1350px" custom-class="editDialog" @close="handleClose('editDialogVisible')">
      <el-form size="small" :inline="true" :model="EditFormData" ref="EditFormData" label-width="120px">
        <el-form-item
          label="业绩取值方案"
          prop="Name"
          :rules="[
            {
              required: true,
              message: '请输入业绩取值方案名称',
              trigger: 'blur',
            },
          ]"
        >
          <el-input v-model="EditFormData.Name"></el-input>
        </el-form-item>
        <el-form-item label="有效性">
          <el-radio-group v-model="EditFormData.Active">
            <el-radio :label="true">有效</el-radio>
            <el-radio :label="false">无效</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <el-tabs v-model="activeName" @tab-click="handleTabClick">
        <el-tab-pane label="销售-产品" name="first">
          <el-tabs type="border-card" v-model="packageActiveName">
            <el-tab-pane label="产品业绩比例" name="noPackage">
              <el-table
                :data="GoodsCategoryCommission.ProductCategory"
                size="small"
                max-height="480px"
                row-key="CategoryID"
                :tree-props="{ children: 'Child' }"
                v-loading="PerformanceSchemeDetailLoading"
              >
                <el-table-column prop="CategoryName" label="产品分类"></el-table-column>
                <el-table-column label="现金业绩">
                  <template slot-scope="scope">
                    <el-input type="number" size="mini" v-input-fixed="2" v-model="scope.row.PayPerformanceRate" @input="royaltyRateChange(1, scope.row)">
                      <template slot="append">%</template>
                    </el-input>
                  </template>
                </el-table-column>
                <el-table-column label="卡抵扣业绩">
                  <template slot-scope="scope">
                    <el-input
                      type="number"
                      size="mini"
                      v-model="scope.row.SavingCardPerformanceRate"
                      v-input-fixed="2"
                      @input="royaltyRateChange(2, scope.row)"
                    >
                      <template slot="append">%</template>
                    </el-input>
                  </template>
                </el-table-column>
                <el-table-column label="赠送卡扣业绩">
                  <template slot-scope="scope">
                    <el-input
                      type="number"
                      size="mini"
                      v-model="scope.row.SavingCardPerformanceLargessRate"
                      v-input-fixed="2"
                      @input="royaltyRateChange(3, scope.row)"
                    >
                      <template slot="append">%</template>
                    </el-input>
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="100px">
                  <template slot-scope="scope">
                    <el-button v-if="scope.row.ParentID" type="primary" size="mini" @click="handleShow('dialogDetailVisible', scope.row, 1)">
                      产品业绩</el-button
                    >
                  </template>
                </el-table-column>
              </el-table>
            </el-tab-pane>
            <el-tab-pane label="是否包含套餐卡产品业绩" name="package">
              <el-scrollbar class="el-scrollbar_height pad_10">
                <el-tree
                  ref="PackagrProductsRef"
                  :expand-on-click-node="false"
                  :check-on-click-node="true"
                  :check-strictly="true"
                  :auto-expand-parent="true"
                  :data="GoodsCategoryCommission.ProductPackageCardCategory"
                  show-checkbox
                  node-key="ID"
                  :props="defaultProps"
                  :default-checked-keys="defaultCheckedKeys"
                  :default-expanded-keys="defaultExpandedKeys"
                  @node-click="(item, IsCheck) => packageCategoryCheckChange(item, IsCheck, 'ProductPackageCardCategory')"
                >
                  <div slot-scope="{ data }" >
                    <span>{{ data.Name }}</span>
                    <el-tag class="marlt_5" type="info" size="mini" v-if="data.IsCategory"> 分类 </el-tag>
                  </div>
                </el-tree>
              </el-scrollbar>
            </el-tab-pane>
          </el-tabs>
        </el-tab-pane>
        <el-tab-pane label="销售-项目" name="second">
          <el-tabs type="border-card" v-model="packageActiveName">
            <el-tab-pane label="项目业绩比例" name="noPackage">
              <el-table
                :data="GoodsCategoryCommission.ProjectCategory"
                size="small"
                max-height="450px"
                row-key="CategoryID"
                :tree-props="{ children: 'Child' }"
              >
                <el-table-column prop="CategoryName" label="项目分类"></el-table-column>
                <el-table-column label="现金业绩">
                  <template slot-scope="scope">
                    <el-input
                      type="number"
                      size="mini"
                      v-input-fixed="2"
                      v-model="scope.row.PayPerformanceRate"
                      class="input_type"
                      @input="royaltyRateChange(1, scope.row)"
                    >
                      <template slot="append">%</template>
                    </el-input>
                  </template>
                </el-table-column>
                <el-table-column label="卡抵扣业绩">
                  <template slot-scope="scope">
                    <el-input
                      type="number"
                      size="mini"
                      v-input-fixed="2"
                      v-model="scope.row.SavingCardPerformanceRate"
                      class="input_type"
                      @input="royaltyRateChange(2, scope.row)"
                    >
                      <template slot="append">%</template>
                    </el-input>
                  </template>
                </el-table-column>
                <el-table-column label="赠送卡扣业绩">
                  <template slot-scope="scope">
                    <el-input
                      type="number"
                      size="mini"
                      v-input-fixed="2"
                      v-model="scope.row.SavingCardPerformanceLargessRate"
                      class="input_type"
                      @input="royaltyRateChange(3, scope.row)"
                    >
                      <template slot="append">%</template>
                    </el-input>
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="100px">
                  <template slot-scope="scope">
                    <el-button v-if="scope.row.ParentID" type="primary" size="mini" @click="handleShow('dialogDetailVisible', scope.row, 2)">
                      项目业绩</el-button
                    >
                  </template>
                </el-table-column>
              </el-table>
            </el-tab-pane>
            <el-tab-pane label="是否包含套餐卡项目业绩" name="package">
              <el-scrollbar class="el-scrollbar_height pad_10">
                <el-tree
                  ref="PackagrProjectsRef"
                  :expand-on-click-node="false"
                  :check-on-click-node="true"
                  :check-strictly="true"
                  :auto-expand-parent="true"
                  :data="GoodsCategoryCommission.ProjectPackageCardCategory"
                  show-checkbox
                  node-key="ID"
                  :props="defaultProps"
                  :default-checked-keys="defaultProjectCheckedKeys"
                  :default-expanded-keys="defaultProjectExpandedKeys"
                  @check-change="(item, IsCheck) => packageCategoryCheckChange(item, IsCheck, 'ProjectPackageCardCategory')"
                >
                
                <div slot-scope="{ data }" >
                    <span>{{ data.Name }}</span>
                    <el-tag class="marlt_5" type="info" size="mini" v-if="data.IsCategory"> 分类 </el-tag>
                  </div>
              </el-tree>
              </el-scrollbar>
            </el-tab-pane>
          </el-tabs>
        </el-tab-pane>
        <el-tab-pane label="销售-储值卡" name="third">
          <el-tabs type="border-card" v-model="packageActiveName">
            <el-tab-pane label="储值卡业绩比例" name="noPackage">
              <el-table
                :data="GoodsCategoryCommission.SavingCardCategory"
                size="small"
                max-height="450px"
                row-key="CategoryID"
                :tree-props="{ children: 'Child' }"
              >
                <el-table-column prop="CategoryName" label="储值卡分类"></el-table-column>
                <el-table-column label="现金业绩">
                  <template slot-scope="scope">
                    <el-col :span="12">
                      <el-input
                        type="number"
                        size="mini"
                        v-input-fixed="2"
                        v-model="scope.row.PayPerformanceRate"
                        class="input_type"
                        @input="royaltyRateChange(1, scope.row)"
                      >
                        <template slot="append">%</template>
                      </el-input>
                    </el-col>
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="115px">
                  <template slot-scope="scope">
                    <el-button type="primary" size="mini" @click="handleShow('dialogDetailVisible', scope.row, 3)">储值卡业绩 </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-tab-pane>
            <el-tab-pane label="是否包含套餐卡储值卡业绩" name="package">
              <el-scrollbar class="el-scrollbar_height pad_10">
                <el-tree
                  ref="PackagrSavingCardsRef"
                  :expand-on-click-node="false"
                  :check-on-click-node="true"
                  :check-strictly="true"
                  :auto-expand-parent="true"
                  :data="GoodsCategoryCommission.SavingCardPackageCardCategory"
                  show-checkbox
                  node-key="ID"
                  :props="defaultProps"
                  :default-checked-keys="defaultSavingCardCheckedKeys"
                  :default-expanded-keys="defaultSavingCardExpandedKeys"
                  @check-change="(item, IsCheck) => packageCategoryCheckChange(item, IsCheck, 'SavingCardPackageCardCategory')"
                >
                
                <div slot-scope="{ data }" >
                    <span>{{ data.Name }}</span>
                    <el-tag class="marlt_5" type="info" size="mini" v-if="data.IsCategory"> 分类 </el-tag>
                  </div>
              </el-tree>
              </el-scrollbar>
            </el-tab-pane>
          </el-tabs>
        </el-tab-pane>
        <el-tab-pane label="销售-时效卡" name="fourth">
          <el-tabs type="border-card" v-model="packageActiveName">
            <el-tab-pane label="时效卡业绩比例" name="noPackage">
              <el-table
                :data="GoodsCategoryCommission.TimeCardCategory"
                size="small"
                max-height="450px"
                row-key="CategoryID"
                :tree-props="{ children: 'Child' }"
              >
                <el-table-column prop="CategoryName" label="商品分类" width="180px" fixed></el-table-column>
                <el-table-column prop="PayRate" label="现金业绩">
                  <template slot-scope="scope">
                    <el-input
                      type="number"
                      size="mini"
                      v-input-fixed="2"
                      v-model="scope.row.PayPerformanceRate"
                      class="input_type"
                      @input="royaltyRateChange(1, scope.row)"
                    >
                      <template slot="append">%</template>
                    </el-input>
                  </template>
                </el-table-column>
                <el-table-column prop="PayRate" label="卡抵扣业绩">
                  <template slot-scope="scope">
                    <el-input
                      type="number"
                      size="mini"
                      v-input-fixed="2"
                      v-model="scope.row.SavingCardPerformanceRate"
                      class="input_type"
                      @input="royaltyRateChange(2, scope.row)"
                    >
                      <template slot="append">%</template>
                    </el-input>
                  </template>
                </el-table-column>
                <el-table-column prop="PayRate" label="赠送卡扣业绩">
                  <template slot-scope="scope">
                    <el-input
                      type="number"
                      size="mini"
                      v-input-fixed="2"
                      v-model="scope.row.SavingCardPerformanceLargessRate"
                      class="input_type"
                      @input="royaltyRateChange(3, scope.row)"
                    >
                      <template slot="append">%</template>
                    </el-input>
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="115px">
                  <template slot-scope="scope">
                    <el-button type="primary" size="mini" @click="handleShow('dialogDetailVisible', scope.row, 4)">时效卡业绩 </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-tab-pane>
            <el-tab-pane label="是否包含套餐卡时效卡业绩" name="package">
              <el-scrollbar class="el-scrollbar_height pad_10">
                <el-tree
                  ref="PackagrTimeCardsRef"
                  :expand-on-click-node="false"
                  :check-on-click-node="true"
                  :check-strictly="true"
                  :auto-expand-parent="true"
                  :data="GoodsCategoryCommission.TimeCardPackageCardCategory"
                  show-checkbox
                  node-key="ID"
                  :props="defaultProps"
                  :default-checked-keys="defaultTimeCardCheckedKeys"
                  :default-expanded-keys="defaultTimeCardExpandedKeys"
                  @check-change="(item, IsCheck) => packageCategoryCheckChange(item, IsCheck, 'TimeCardPackageCardCategory')"
                >
                
                <div slot-scope="{ data }" >
                    <span>{{ data.Name }}</span>
                    <el-tag class="marlt_5" type="info" size="mini" v-if="data.IsCategory"> 分类 </el-tag>
                  </div>
              </el-tree>
              </el-scrollbar>
            </el-tab-pane>
          </el-tabs>
        </el-tab-pane>
        <el-tab-pane label="销售-通用次卡" name="five">
          <el-tabs type="border-card" v-model="packageActiveName">
            <el-tab-pane label="通用次卡业绩比例" name="noPackage">
              <el-table
                :data="GoodsCategoryCommission.GeneralCardCategory"
                size="small"
                max-height="450px"
                row-key="CategoryID"
                :tree-props="{ children: 'Child' }"
              >
                <el-table-column prop="CategoryName" label="商品分类"></el-table-column>
                <el-table-column prop="PayRate" label="现金业绩">
                  <template slot-scope="scope">
                    <el-input
                      type="number"
                      size="mini"
                      v-input-fixed="2"
                      v-model="scope.row.PayPerformanceRate"
                      class="input_type"
                      @input="royaltyRateChange(1, scope.row)"
                    >
                      <template slot="append">%</template>
                    </el-input>
                  </template>
                </el-table-column>
                <el-table-column prop="PayRate" label="卡抵扣业绩">
                  <template slot-scope="scope">
                    <el-input
                      type="number"
                      size="mini"
                      v-input-fixed="2"
                      v-model="scope.row.SavingCardPerformanceRate"
                      class="input_type"
                      @input="royaltyRateChange(2, scope.row)"
                    >
                      <template slot="append">%</template>
                    </el-input>
                  </template>
                </el-table-column>
                <el-table-column prop="PayRate" label="赠送卡扣业绩">
                  <template slot-scope="scope">
                    <el-input
                      type="number"
                      size="mini"
                      v-input-fixed="2"
                      v-model="scope.row.SavingCardPerformanceLargessRate"
                      class="input_type"
                      @input="royaltyRateChange(3, scope.row)"
                    >
                      <template slot="append">%</template>
                    </el-input>
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="125px">
                  <template slot-scope="scope">
                    <el-button type="primary" size="mini" @click="handleShow('dialogDetailVisible', scope.row, 5)">通用次卡业绩 </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-tab-pane>
            <el-tab-pane label="是否包含套餐卡通用次卡业绩" name="package">
              <el-scrollbar class="el-scrollbar_height pad_10">
                <el-tree
                  ref="PackagrGeneralCardsRef"
                  :expand-on-click-node="false"
                  :check-on-click-node="true"
                  :check-strictly="true"
                  :auto-expand-parent="true"
                  :data="GoodsCategoryCommission.GeneralCardPackageCardCategory"
                  show-checkbox
                  node-key="ID"
                  :props="defaultProps"
                  :default-checked-keys="defaultGeneralCardCheckedKeys"
                  :default-expanded-keys="defaultGeneralCardExpandedKeys"
                  @check-change="(item, IsCheck) => packageCategoryCheckChange(item, IsCheck, 'GeneralCardPackageCardCategory')"
                >
                
                <div slot-scope="{ data }" >
                    <span>{{ data.Name }}</span>
                    <el-tag class="marlt_5" type="info" size="mini" v-if="data.IsCategory"> 分类 </el-tag>
                  </div>
              </el-tree>
              </el-scrollbar>
            </el-tab-pane>
          </el-tabs>
        </el-tab-pane>
        <el-tab-pane label="销售-套餐卡" name="sale-packageCard">
          <el-table
            :data="GoodsCategoryCommission.PackageCardCategory"
            size="small"
            max-height="450px"
            row-key="CategoryID"
            :tree-props="{ children: 'Child' }"
          >
            <el-table-column prop="CategoryName" label="商品分类"></el-table-column>
            <el-table-column prop="PayRate" label="现金业绩">
              <template slot-scope="scope">
                <el-input
                  type="number"
                  size="mini"
                  v-input-fixed="2"
                  v-model="scope.row.PayPerformanceRate"
                  class="input_type"
                  @input="royaltyRateChange(1, scope.row)"
                >
                  <template slot="append">%</template>
                </el-input>
              </template>
            </el-table-column>
            <el-table-column prop="PayRate" label="卡抵扣业绩">
              <template slot-scope="scope">
                <el-input
                  type="number"
                  size="mini"
                  v-input-fixed="2"
                  v-model="scope.row.SavingCardPerformanceRate"
                  class="input_type"
                  @input="royaltyRateChange(2, scope.row)"
                >
                  <template slot="append">%</template>
                </el-input>
              </template>
            </el-table-column>
            <el-table-column prop="PayRate" label="赠送卡扣业绩">
              <template slot-scope="scope">
                <el-input
                  type="number"
                  size="mini"
                  v-input-fixed="2"
                  v-model="scope.row.SavingCardPerformanceLargessRate"
                  class="input_type"
                  @input="royaltyRateChange(3, scope.row)"
                >
                  <template slot="append">%</template>
                </el-input>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="125px">
              <template slot-scope="scope">
                <el-button type="primary" size="mini" @click="handleShow('dialogDetailVisible', scope.row, 11)">套餐卡业绩 </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
        <el-tab-pane label="消耗-产品" name="six">
          <el-table
            :data="GoodsCategoryCommission.TreatProductCategory"
            size="small"
            max-height="450px"
            row-key="CategoryID"
            :tree-props="{ children: 'Child' }"
          >
            <el-table-column prop="CategoryName" label="商品分类"></el-table-column>

            <el-table-column label="现金业绩">
              <template slot-scope="scope">
                <el-input
                  type="number"
                  size="mini"
                  v-input-fixed="2"
                  v-model="scope.row.PerformancePayRate"
                  class="input_type"
                  @input="royaltyRateChange(4, scope.row)"
                >
                  <template slot="append">%</template>
                </el-input>
              </template>
            </el-table-column>
            <el-table-column label="卡抵扣业绩">
              <template slot-scope="scope">
                <el-input
                  type="number"
                  size="mini"
                  v-input-fixed="2"
                  v-model="scope.row.PerformanceCardRate"
                  class="input_type"
                  @input="royaltyRateChange(5, scope.row)"
                >
                  <template slot="append">%</template>
                </el-input>
              </template>
            </el-table-column>
            <el-table-column label="赠送卡抵扣业绩">
              <template slot-scope="scope">
                <el-input
                  type="number"
                  size="mini"
                  v-input-fixed="2"
                  v-model="scope.row.PerformanceCardLargessRate"
                  class="input_type"
                  @input="royaltyRateChange(6, scope.row)"
                >
                  <template slot="append">%</template>
                </el-input>
              </template>
            </el-table-column>
            <el-table-column label="赠送业绩">
              <template slot-scope="scope">
                <el-input
                  type="number"
                  size="mini"
                  v-input-fixed="2"
                  v-model="scope.row.PerformanceLargessRate"
                  class="input_type"
                  @input="royaltyRateChange(7, scope.row)"
                >
                  <template slot="append">%</template>
                </el-input>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="100px">
              <template slot-scope="scope">
                <el-button v-if="scope.row.ParentID" type="primary" size="mini" @click="handleShow('dialogDetailVisible', scope.row, 6)"> 产品业绩</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
        <el-tab-pane label="消耗-项目" name="seven">
          <el-table
            :data="GoodsCategoryCommission.TreatProjectCategory"
            size="small"
            max-height="450px"
            row-key="CategoryID"
            :tree-props="{ children: 'Child' }"
          >
            <el-table-column prop="CategoryName" label="商品分类"></el-table-column>

            <el-table-column label="现金业绩">
              <template slot-scope="scope">
                <el-input
                  type="number"
                  size="mini"
                  v-input-fixed="2"
                  v-model="scope.row.PerformancePayRate"
                  class="input_type"
                  @input="royaltyRateChange(4, scope.row)"
                >
                  <template slot="append">%</template>
                </el-input>
              </template>
            </el-table-column>
            <el-table-column label="卡抵扣业绩">
              <template slot-scope="scope">
                <el-input
                  type="number"
                  size="mini"
                  v-input-fixed="2"
                  v-model="scope.row.PerformanceCardRate"
                  class="input_type"
                  @input="royaltyRateChange(5, scope.row)"
                >
                  <template slot="append">%</template>
                </el-input>
              </template>
            </el-table-column>
            <el-table-column label="赠送卡抵扣业绩">
              <template slot-scope="scope">
                <el-input
                  type="number"
                  size="mini"
                  v-input-fixed="2"
                  v-model="scope.row.PerformanceCardLargessRate"
                  class="input_type"
                  @input="royaltyRateChange(6, scope.row)"
                >
                  <template slot="append">%</template>
                </el-input>
              </template>
            </el-table-column>
            <el-table-column label="赠送业绩">
              <template slot-scope="scope">
                <el-input
                  type="number"
                  size="mini"
                  v-input-fixed="2"
                  v-model="scope.row.PerformanceLargessRate"
                  class="input_type"
                  @input="royaltyRateChange(7, scope.row)"
                >
                  <template slot="append">%</template>
                </el-input>
              </template>
            </el-table-column>

            <el-table-column label="操作" width="100px">
              <template slot-scope="scope">
                <el-button v-if="scope.row.ParentID" type="primary" size="mini" @click="handleShow('dialogDetailVisible', scope.row, 7)"> 项目业绩</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
        <el-tab-pane label="消耗-储值卡" name="eight">
          <el-table
            :data="GoodsCategoryCommission.TreatSavingCardCategory"
            size="small"
            max-height="450px"
            row-key="CategoryID"
            :tree-props="{ children: 'Child' }"
          >
            <el-table-column prop="CategoryName" label="商品分类"></el-table-column>
            <el-table-column label="卡抵扣业绩">
              <template slot-scope="scope">
                <el-input
                  type="number"
                  size="mini"
                  v-input-fixed="2"
                  v-model="scope.row.PerformanceCardRate"
                  class="input_type"
                  @input="royaltyRateChange(5, scope.row)"
                >
                  <template slot="append">%</template>
                </el-input>
              </template>
            </el-table-column>
            <el-table-column label="赠送卡抵扣业绩">
              <template slot-scope="scope">
                <el-input
                  type="number"
                  size="mini"
                  v-input-fixed="2"
                  v-model="scope.row.PerformanceCardLargessRate"
                  class="input_type"
                  @input="royaltyRateChange(6, scope.row)"
                >
                  <template slot="append">%</template>
                </el-input>
              </template>
            </el-table-column>

            <el-table-column label="操作" width="115px">
              <template slot-scope="scope">
                <el-button type="primary" size="mini" @click="handleShow('dialogDetailVisible', scope.row, 8)"> 储值卡业绩</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
        <el-tab-pane label="消耗-时效卡" name="nine">
          <el-table
            :data="GoodsCategoryCommission.TreatTimeCardCategory"
            size="small"
            max-height="450px"
            row-key="CategoryID"
            :tree-props="{ children: 'Child' }"
          >
            <el-table-column prop="CategoryName" label="商品分类"></el-table-column>

            <el-table-column label="现金业绩">
              <template slot-scope="scope">
                <el-input
                  type="number"
                  size="mini"
                  v-input-fixed="2"
                  v-model="scope.row.PerformancePayRate"
                  class="input_type"
                  @input="royaltyRateChange(4, scope.row)"
                >
                  <template slot="append">%</template>
                </el-input>
              </template>
            </el-table-column>
            <el-table-column label="卡抵扣业绩">
              <template slot-scope="scope">
                <el-input
                  type="number"
                  size="mini"
                  v-input-fixed="2"
                  v-model="scope.row.PerformanceCardRate"
                  class="input_type"
                  @input="royaltyRateChange(5, scope.row)"
                >
                  <template slot="append">%</template>
                </el-input>
              </template>
            </el-table-column>
            <el-table-column label="赠送卡抵扣业绩">
              <template slot-scope="scope">
                <el-input
                  type="number"
                  size="mini"
                  v-input-fixed="2"
                  v-model="scope.row.PerformanceCardLargessRate"
                  class="input_type"
                  @input="royaltyRateChange(6, scope.row)"
                >
                  <template slot="append">%</template>
                </el-input>
              </template>
            </el-table-column>
            <el-table-column label="赠送业绩">
              <template slot-scope="scope">
                <el-input
                  type="number"
                  size="mini"
                  v-input-fixed="2"
                  v-model="scope.row.PerformanceLargessRate"
                  class="input_type"
                  @input="royaltyRateChange(7, scope.row)"
                >
                  <template slot="append">%</template>
                </el-input>
              </template>
            </el-table-column>

            <el-table-column label="操作" width="115px">
              <template slot-scope="scope">
                <el-button type="primary" size="mini" @click="handleShow('dialogDetailVisible', scope.row, 9)"> 时效卡业绩</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
        <el-tab-pane label="消耗-通用次卡" name="ten">
          <el-table
            :data="GoodsCategoryCommission.TreatGeneralCardCategory"
            size="small"
            max-height="450px"
            row-key="CategoryID"
            :tree-props="{ children: 'Child' }"
          >
            <el-table-column prop="CategoryName" label="商品分类"></el-table-column>

            <el-table-column label="现金业绩">
              <template slot-scope="scope">
                <el-input
                  type="number"
                  size="mini"
                  v-input-fixed="2"
                  v-model="scope.row.PerformancePayRate"
                  class="input_type"
                  @input="royaltyRateChange(4, scope.row)"
                >
                  <template slot="append">%</template>
                </el-input>
              </template>
            </el-table-column>
            <el-table-column label="卡抵扣业绩">
              <template slot-scope="scope">
                <el-input
                  type="number"
                  size="mini"
                  v-input-fixed="2"
                  v-model="scope.row.PerformanceCardRate"
                  class="input_type"
                  @input="royaltyRateChange(5, scope.row)"
                >
                  <template slot="append">%</template>
                </el-input>
              </template>
            </el-table-column>
            <el-table-column label="赠送卡抵扣业绩">
              <template slot-scope="scope">
                <el-input
                  type="number"
                  size="mini"
                  v-input-fixed="2"
                  v-model="scope.row.PerformanceCardLargessRate"
                  class="input_type"
                  @input="royaltyRateChange(6, scope.row)"
                >
                  <template slot="append">%</template>
                </el-input>
              </template>
            </el-table-column>
            <el-table-column label="赠送业绩">
              <template slot-scope="scope">
                <el-input
                  type="number"
                  size="mini"
                  v-input-fixed="2"
                  v-model="scope.row.PerformanceLargessRate"
                  class="input_type"
                  @input="royaltyRateChange(7, scope.row)"
                >
                  <template slot="append">%</template>
                </el-input>
              </template>
            </el-table-column>

            <el-table-column label="操作" width="125px">
              <template slot-scope="scope">
                <el-button type="primary" size="mini" @click="handleShow('dialogDetailVisible', scope.row, 10)"> 通用次卡业绩</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
      </el-tabs>
      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="editDialogVisible = false" v-prevent-click>取 消</el-button>
        <el-button type="primary" size="small" :loading="saveLoading" @click="handleSave('editDialogVisible')" v-prevent-click>保 存 </el-button>
      </div>
    </el-dialog>
    <!-- 商品明细业绩弹出层 -->
    <el-dialog :title="GoodsTitle" :visible.sync="dialogDetailVisible" width="1000px" custom-class="editDialog" @close="handleClose('dialogDetailVisible')">
      <el-table :data="goodsPerformanceList" size="small" max-height="450px">
        <el-table-column prop="Name" :label="goodsCategory"></el-table-column>

        <template
          v-if="activeName != 'six' && activeName != 'seven' && activeName != 'third' && activeName != 'eight' && activeName != 'nine' && activeName != 'ten'"
        >
          <el-table-column prop="PayRate" label="现金业绩">
            <template slot-scope="scope">
              <el-input type="number" size="mini" v-input-fixed="2" v-model="scope.row.PayPerformanceRate" @input="royaltyRateChange(1, scope.row)">
                <template slot="append">%</template>
              </el-input>
            </template>
          </el-table-column>
          <el-table-column prop="PayRate" label="卡抵扣业绩">
            <template slot-scope="scope">
              <el-input type="number" size="mini" v-input-fixed="2" v-model="scope.row.SavingCardPerformanceRate" @input="royaltyRateChange(2, scope.row)">
                <template slot="append">%</template>
              </el-input>
            </template>
          </el-table-column>
          <el-table-column prop="PayRate" label="赠送卡扣业绩">
            <template slot-scope="scope">
              <el-input
                type="number"
                size="mini"
                v-input-fixed="2"
                v-model="scope.row.SavingCardPerformanceLargessRate"
                @input="royaltyRateChange(3, scope.row)"
              >
                <template slot="append">%</template>
              </el-input>
            </template>
          </el-table-column>
        </template>
        <template v-else-if="activeName == 'third'">
          <el-table-column prop="PayRate" label="现金业绩">
            <template slot-scope="scope">
              <el-col :span="12">
                <el-input
                  type="number"
                  size="mini"
                  v-input-fixed="2"
                  v-model="scope.row.PayPerformanceRate"
                  class="input_type"
                  @input="royaltyRateChange(1, scope.row)"
                >
                  <template slot="append">%</template>
                </el-input>
              </el-col>
            </template>
          </el-table-column>
        </template>
        <template v-else-if="activeName == 'eight'">
          <el-table-column prop="PayRate" label="卡抵扣业绩">
            <template slot-scope="scope">
              <el-input type="number" size="mini" v-input-fixed="2" v-model="scope.row.PerformanceCardRate" @input="royaltyRateChange(5, scope.row)">
                <template slot="append">%</template>
              </el-input>
            </template>
          </el-table-column>
          <el-table-column prop="PayRate" label="赠送卡扣业绩">
            <template slot-scope="scope">
              <el-input type="number" size="mini" v-input-fixed="2" v-model="scope.row.PerformanceCardLargessRate" @input="royaltyRateChange(6, scope.row)">
                <template slot="append">%</template>
              </el-input>
            </template>
          </el-table-column>
        </template>
        <template v-else-if="activeName == 'nine' || activeName == 'ten'">
          <el-table-column prop="PayRate" label="现金业绩">
            <template slot-scope="scope">
              <el-input
                type="number"
                size="mini"
                v-input-fixed="2"
                v-model="scope.row.PerformancePayRate"
                class="input_type"
                @input="royaltyRateChange(4, scope.row)"
              >
                <template slot="append">%</template>
              </el-input>
            </template>
          </el-table-column>
          <el-table-column prop="PayRate" label="卡抵扣业绩">
            <template slot-scope="scope">
              <el-input
                type="number"
                size="mini"
                v-input-fixed="2"
                v-model="scope.row.PerformanceCardRate"
                class="input_type"
                @input="royaltyRateChange(5, scope.row)"
              >
                <template slot="append">%</template>
              </el-input>
            </template>
          </el-table-column>
          <el-table-column prop="PayRate" label="赠送卡扣业绩">
            <template slot-scope="scope">
              <el-input
                type="number"
                size="mini"
                v-input-fixed="2"
                v-model="scope.row.PerformanceCardLargessRate"
                class="input_type"
                @input="royaltyRateChange(6, scope.row)"
              >
                <template slot="append">%</template>
              </el-input>
            </template>
          </el-table-column>
          <el-table-column label="赠送业绩">
            <template slot-scope="scope">
              <el-input
                type="number"
                size="mini"
                v-input-fixed="2"
                v-model="scope.row.PerformanceLargessRate"
                class="input_type"
                @input="royaltyRateChange(7, scope.row)"
              >
                <template slot="append">%</template>
              </el-input>
            </template>
          </el-table-column>
        </template>
        <template v-else>
          <el-table-column label="现金业绩">
            <template slot-scope="scope">
              <el-input
                type="number"
                size="mini"
                v-input-fixed="2"
                v-model="scope.row.PerformancePayRate"
                class="input_type"
                @input="royaltyRateChange(4, scope.row)"
              >
                <template slot="append">%</template>
              </el-input>
            </template>
          </el-table-column>
          <el-table-column label="卡本金业绩">
            <template slot-scope="scope">
              <el-input
                type="number"
                size="mini"
                v-input-fixed="2"
                v-model="scope.row.PerformanceCardRate"
                class="input_type"
                @input="royaltyRateChange(5, scope.row)"
              >
                <template slot="append">%</template>
              </el-input>
            </template>
          </el-table-column>
          <el-table-column label="卡赠金业绩">
            <template slot-scope="scope">
              <el-input
                type="number"
                size="mini"
                v-input-fixed="2"
                v-model="scope.row.PerformanceCardLargessRate"
                class="input_type"
                @input="royaltyRateChange(6, scope.row)"
              >
                <template slot="append">%</template>
              </el-input>
            </template>
          </el-table-column>
          <el-table-column label="赠送业绩">
            <template slot-scope="scope">
              <el-input
                type="number"
                size="mini"
                v-input-fixed="2"
                v-model="scope.row.PerformanceLargessRate"
                class="input_type"
                @input="royaltyRateChange(7, scope.row)"
              >
                <template slot="append">%</template>
              </el-input>
            </template>
          </el-table-column>
          <!-- <el-table-column prop="PayRate" label="非赠送业绩333">
            <template slot-scope="scope">
              <el-input type="number" size="mini" min="0" v-model="scope.row.PerformanceRate" v-enter-number3
                class="input_type" @input="royaltyRateChange(4, scope.row)">
                <template slot="append">%</template>
              </el-input>

            </template>
          </el-table-column>
          <el-table-column prop="PayRate" label="赠送业绩">
            <template slot-scope="scope">
              <el-input type="number" size="mini" min="0" v-model="scope.row.PerformanceLargessRate" class="input_type" v-enter-number3
                @input="royaltyRateChange(5, scope.row)">
                <template slot="append">%</template>
              </el-input>

            </template>
          </el-table-column> -->
        </template>
      </el-table>
      <span slot="footer" class="dialog-footer">
        <el-button size="small" @click="dialogDetailVisible = false" v-prevent-click>取 消</el-button>
        <el-button size="small" type="primary" @click="handleSave('dialogDetailVisible')" v-prevent-click>保 存</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import API from "@/api/KHS/Salary/performanceScheme";
export default {
  name: "SalaryPerformanceScheme",
  components: {},
  directives: {},
  data() {
    return {
      loading: false,
      faSaveLoading: false,
      saveLoading: false,
      PerformanceSchemeDetailLoading: false,
      packageActiveName: "noPackage",
      searchData: {
        performanceName: "",
        isValidity: true,
      },
      tableData: [],
      paginations: {
        page: 1, // 当前位于哪页
        total: 0, // 总数
        page_size: 10, // 1页显示多少条
        layout: "total, prev, pager, next,jumper", // 翻页属性
      },

      editDialogVisible: false,
      newDialogVisible: false,
      dialogDetailVisible: false,
      GoodsCategoryCommission: {},
      ruleForm: {
        PerformancePlan: "",
      },
      EditFormData: {
        ID: "",
        Name: "",
        Active: true,
        ProductCategory: [],
        ProjectCategory: [],
        GeneralCardCategory: [],
        TimeCardCategory: [],
        SavingCardCategory: [],
        PackageCardCategory: [],
        TreatProductCategory: [],
        TreatProjectCategory: [],
        TreatGeneralCardCategory: [],
        TreatTimeCardCategory: [],
        TreatSavingCardCategory: [],
        ProductPackageCardCategory: [], //产品套餐卡分类
        ProjectPackageCardCategory: [], //项目套餐卡分类
        GeneralCardPackageCardCategory: [], //通用次卡套餐卡分类
        TimeCardPackageCardCategory: [], //时效卡套餐卡分类
        SavingCardPackageCardCategory: [], //储值卡套餐卡分类
        ProductPackageCard: [], //产品套餐卡
        ProjectPackageCard: [], //项目套餐卡
        GeneralCardPackageCard: [], //通用次卡套餐卡
        TimeCardPackageCard: [], //时效卡套餐卡
        SavingCardPackageCard: [], //产品套餐卡
      },
      activeName: "first",
      GoodsTitle: "",
      goodsCategory: "",
      goodsPerformanceID: "",
      goodsPerformanceList: [],

      defaultProps: {
        children: "PackageCard",
        label: "Name",
      },
      defaultCheckedKeys: [],
      defaultExpandedKeys: [],

      defaultProjectCheckedKeys: [],
      defaultProjectExpandedKeys: [],

      defaultSavingCardCheckedKeys: [],
      defaultSavingCardExpandedKeys: [],

      defaultTimeCardCheckedKeys: [],
      defaultTimeCardExpandedKeys: [],

      defaultGeneralCardCheckedKeys: [],
      defaultGeneralCardExpandedKeys: [],
    };
  },

  mounted() {
    const that = this;
    that.handleSearch();
  },

  methods: {
    /**    */
    packageCategoryCheckChange(item, IsCheck, type) {
      const that = this;
      if (item.PackageCard && item.PackageCard.length) {
        item.IsCheck = !item.IsCheck;
        if (IsCheck) {
          that.EditFormData[type].push(item.ID);
        } else {
          let index = that.EditFormData[type].findIndex((i) => i.ID == item.ID);
          that.EditFormData[type].splice(index, 1);
        }
      }
    },
    // 业绩取值方案详情
    PerformanceSchemeDetail(id) {
      const that = this;
      const params = {
        ID: id,
      };
      that.PerformanceSchemeDetailLoading = true;
      API.PerformanceSchemeDetail(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.GoodsCategoryCommission = res.Data;
            that.defaultCheckedKeys = that.getCardCheckedKeys(res.Data.ProductPackageCardCategory, true);
            that.EditFormData.ProductPackageCardCategory = that.getCardCheckedKeys(res.Data.ProductPackageCardCategory, false);
            that.defaultExpandedKeys = [...that.defaultCheckedKeys ,...that.EditFormData.ProductPackageCardCategory];

            that.defaultProjectCheckedKeys = that.getCardCheckedKeys(that.GoodsCategoryCommission.ProjectPackageCardCategory, true);
            that.EditFormData.ProjectPackageCardCategory = that.getCardCheckedKeys(that.GoodsCategoryCommission.ProjectPackageCardCategory, false);
            that.defaultProjectExpandedKeys = [...that.defaultProjectCheckedKeys,...that.EditFormData.ProjectPackageCardCategory];

            that.defaultSavingCardCheckedKeys = that.getCardCheckedKeys(that.GoodsCategoryCommission.SavingCardPackageCardCategory, true);
            that.EditFormData.SavingCardPackageCardCategory = that.getCardCheckedKeys(that.GoodsCategoryCommission.SavingCardPackageCardCategory, false);
            that.defaultSavingCardExpandedKeys = [...that.defaultSavingCardCheckedKeys,...that.EditFormData.SavingCardPackageCardCategory];

            that.defaultTimeCardCheckedKeys = that.getCardCheckedKeys(that.GoodsCategoryCommission.TimeCardPackageCardCategory, true);
            that.EditFormData.TimeCardPackageCardCategory = that.getCardCheckedKeys(that.GoodsCategoryCommission.TimeCardPackageCardCategory, false);
            that.defaultTimeCardExpandedKeys = [...that.defaultTimeCardCheckedKeys,...that.EditFormData.TimeCardPackageCardCategory];

            that.defaultGeneralCardCheckedKeys = that.getCardCheckedKeys(that.GoodsCategoryCommission.GeneralCardPackageCardCategory, true);
            that.EditFormData.GeneralCardPackageCardCategory = that.getCardCheckedKeys(that.GoodsCategoryCommission.GeneralCardPackageCardCategory, false);
            that.defaultGeneralCardExpandedKeys = [...that.defaultGeneralCardCheckedKeys,...that.EditFormData.GeneralCardPackageCardCategory];
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(() => {
          that.PerformanceSchemeDetailLoading = false;
        });
    },
    /* 获取选中的值 */
    getCardCheckedKeys(arr, isChild) {
      const that = this;
      let keyIDs = [];
      arr.forEach((i) => {
        if (i.IsCheck) {
          keyIDs.push(i.ID);
        }
        if (i.PackageCard && i.PackageCard.length > 0 && isChild) {
          let childKeys = that.getCardCheckedKeys(i.PackageCard);
          keyIDs = [...keyIDs, ...childKeys];
        }
      });
      return keyIDs;
    },
    // 比例
    royaltyRateChange: function (index, row) {
      if (index == 1) {
        if (row.PayPerformanceRate > 100) {
          row.PayPerformanceRate = 100;
        }
      } else if (index == 2) {
        if (row.SavingCardPerformanceRate > 100) {
          row.SavingCardPerformanceRate = 100;
        }
      } else if (index == 3) {
        if (row.SavingCardPerformanceLargessRate > 100) {
          row.SavingCardPerformanceLargessRate = 100;
        }
      } else if (index == 4) {
        if (row.PerformancePayRate > 100) {
          row.PerformancePayRate = 100;
        }
      } else if (index == 5) {
        if (row.PerformanceCardRate > 100) {
          row.PerformanceCardRate = 100;
        }
      } else if (index == 6) {
        if (row.PerformanceCardLargessRate > 100) {
          row.PerformanceCardLargessRate = 100;
        }
      } else if (index == 7) {
        if (row.PerformanceLargessRate > 100) {
          row.PerformanceLargessRate = 100;
        }
      }
    },

    //   搜索
    handleSearch() {
      this.paginations.page = 1;
      this.PerformanceSchemeAll();
    },
    // 获取方案列表
    PerformanceSchemeAll() {
      const that = this;
      const params = {
        Name: that.searchData.performanceName,
        Active: that.searchData.isValidity,
        PageNum: that.paginations.page,
      };
      that.loading = true;
      API.PerformanceSchemeAll(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.tableData = res.List;
            that.paginations.total = res.Total;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },
    // 分页
    handleCurrentChange() {
      this.PerformanceSchemeAll();
    },
    /**    */
    changeIsSeparateSalePackageCardPerformance(val) {
      let that = this;
      if (!val) {
        that.activeName = "first";
      }
    },
    // 点击tab
    handleTabClick(e) {
      const that = this;
      that.activeName = e.name;
      that.packageActiveName = "noPackage";
    },
    // 新增保存
    handleNewAdd() {
      const that = this;
      that.$refs.ruleForm.validate((valid) => {
        if (valid) {
          that.faSaveLoading = true;
          API.PerformanceSchemeCreate({
            Name: that.ruleForm.PerformancePlan,
          })
            .then((res) => {
              if (res.StateCode == 200) {
                that.$message.success({
                  message: "添加成功",
                  duration: 2000,
                });
                that.handleSearch();
              } else {
                that.$message.success({
                  message: res.Message,
                  duration: 2000,
                });
              }
            })
            .finally(() => {
              that.faSaveLoading = false;
            });
        }
      });
    },

    // 编辑保存
    handleEditSave() {
      const that = this;
      that.EditFormData.ProductPackageCard = that.$refs.PackagrProductsRef.getCheckedKeys(true);
      that.EditFormData.ProjectPackageCard = that.$refs.PackagrProjectsRef.getCheckedKeys(true);
      that.EditFormData.SavingCardPackageCard = that.$refs.PackagrSavingCardsRef.getCheckedKeys(true);
      that.EditFormData.TimeCardPackageCard = that.$refs.PackagrTimeCardsRef.getCheckedKeys(true);
      that.EditFormData.GeneralCardPackageCard = that.$refs.PackagrGeneralCardsRef.getCheckedKeys(true);

      that.EditFormData.ProductPackageCardCategory = that.EditFormData.ProductPackageCardCategory.map(i => {
         return i.slice(2);
      })
      that.EditFormData.ProjectPackageCardCategory = that.EditFormData.ProjectPackageCardCategory.map(i => {
         return i.slice(2);
      })
      that.EditFormData.GeneralCardPackageCardCategory = that.EditFormData.GeneralCardPackageCardCategory.map(i => {
         return i.slice(2);
      })
      that.EditFormData.TimeCardPackageCardCategory = that.EditFormData.TimeCardPackageCardCategory.map(i => {
         return i.slice(2);
      })
      that.EditFormData.SavingCardPackageCardCategory = that.EditFormData.SavingCardPackageCardCategory.map(i => {
         return i.slice(2);
      })

      that.saveLoading = true;
      // 销售产品
      that.GoodsCategoryCommission.ProductCategory.forEach((item) => {
        let param = {
          CategoryID: item.CategoryID,
          PayPerformanceRate: item.PayPerformanceRate,
          SavingCardPerformanceRate: item.SavingCardPerformanceRate,
          SavingCardPerformanceLargessRate: item.SavingCardPerformanceLargessRate,
          Child: [],
        };
        if (item.PayPerformanceRate || item.SavingCardPerformanceLargessRate || item.SavingCardPerformanceRate) {
          if (item.Child && item.Child.length) {
            item.Child.forEach((item) => {
              if (item.PayPerformanceRate || item.SavingCardPerformanceLargessRate || item.SavingCardPerformanceRate) {
                param.Child.push({
                  CategoryID: item.CategoryID,
                  PayPerformanceRate: item.PayPerformanceRate,
                  SavingCardPerformanceRate: item.SavingCardPerformanceRate,
                  SavingCardPerformanceLargessRate: item.SavingCardPerformanceLargessRate,
                });
              }
            });
          }
          that.EditFormData.ProductCategory.push(param);
        } else if (item.Child && item.Child.length) {
          item.Child.forEach((item) => {
            if (item.PayPerformanceRate || item.SavingCardPerformanceLargessRate || item.SavingCardPerformanceRate) {
              param.Child.push({
                CategoryID: item.CategoryID,
                PayPerformanceRate: item.PayPerformanceRate,
                SavingCardPerformanceRate: item.SavingCardPerformanceRate,
                SavingCardPerformanceLargessRate: item.SavingCardPerformanceLargessRate,
              });
            }
          });
          if (param.Child.length) {
            that.EditFormData.ProductCategory.push(param);
          }
        }
      });
      // 销售项目
      that.GoodsCategoryCommission.ProjectCategory.forEach((item) => {
        let param = {
          CategoryID: item.CategoryID,
          PayPerformanceRate: item.PayPerformanceRate,
          SavingCardPerformanceRate: item.SavingCardPerformanceRate,
          SavingCardPerformanceLargessRate: item.SavingCardPerformanceLargessRate,
          Child: [],
        };
        if (item.PayPerformanceRate || item.SavingCardPerformanceLargessRate || item.SavingCardPerformanceRate) {
          if (item.Child && item.Child.length) {
            item.Child.forEach((item) => {
              if (item.PayPerformanceRate || item.SavingCardPerformanceLargessRate || item.SavingCardPerformanceRate) {
                param.Child.push({
                  CategoryID: item.CategoryID,
                  PayPerformanceRate: item.PayPerformanceRate,
                  SavingCardPerformanceRate: item.SavingCardPerformanceRate,
                  SavingCardPerformanceLargessRate: item.SavingCardPerformanceLargessRate,
                });
              }
            });
          }
          that.EditFormData.ProjectCategory.push(param);
        } else if (item.Child && item.Child.length) {
          item.Child.forEach((item) => {
            if (item.PayPerformanceRate || item.SavingCardPerformanceLargessRate || item.SavingCardPerformanceRate) {
              param.Child.push({
                CategoryID: item.CategoryID,
                PayPerformanceRate: item.PayPerformanceRate,
                SavingCardPerformanceRate: item.SavingCardPerformanceRate,
                SavingCardPerformanceLargessRate: item.SavingCardPerformanceLargessRate,
              });
            }
          });
          if (param.Child.length) {
            that.EditFormData.ProjectCategory.push(param);
          }
        }
      });
      // 销售通用次卡
      that.GoodsCategoryCommission.GeneralCardCategory.forEach((item) => {
        if (item.PayPerformanceRate || item.SavingCardPerformanceLargessRate || item.SavingCardPerformanceRate) {
          let param = {
            CategoryID: item.CategoryID,
            PayPerformanceRate: item.PayPerformanceRate,
            SavingCardPerformanceRate: item.SavingCardPerformanceRate,
            SavingCardPerformanceLargessRate: item.SavingCardPerformanceLargessRate,
          };
          that.EditFormData.GeneralCardCategory.push(param);
        }
      });
      // 销售套餐卡
      that.GoodsCategoryCommission.PackageCardCategory.forEach((item) => {
        if (item.PayPerformanceRate || item.SavingCardPerformanceLargessRate || item.SavingCardPerformanceRate) {
          let param = {
            CategoryID: item.CategoryID,
            PayPerformanceRate: item.PayPerformanceRate,
            SavingCardPerformanceRate: item.SavingCardPerformanceRate,
            SavingCardPerformanceLargessRate: item.SavingCardPerformanceLargessRate,
          };
          that.EditFormData.PackageCardCategory.push(param);
        }
      });
      // 销售时效卡
      that.GoodsCategoryCommission.TimeCardCategory.forEach((item) => {
        if (item.PayPerformanceRate || item.SavingCardPerformanceLargessRate || item.SavingCardPerformanceRate) {
          let param = {
            CategoryID: item.CategoryID,
            PayPerformanceRate: item.PayPerformanceRate,
            SavingCardPerformanceRate: item.SavingCardPerformanceRate,
            SavingCardPerformanceLargessRate: item.SavingCardPerformanceLargessRate,
          };
          that.EditFormData.TimeCardCategory.push(param);
        }
      });
      // 销售储值卡
      that.GoodsCategoryCommission.SavingCardCategory.forEach((item) => {
        if (item.PayPerformanceRate) {
          let param = {
            CategoryID: item.CategoryID,
            PayPerformanceRate: item.PayPerformanceRate,
          };
          that.EditFormData.SavingCardCategory.push(param);
        }
      });
      // 消耗产品
      that.GoodsCategoryCommission.TreatProductCategory.forEach((item) => {
        let param = {
          CategoryID: item.CategoryID,
          PerformanceCardLargessRate: item.PerformanceCardLargessRate,
          PerformanceCardRate: item.PerformanceCardRate,
          PerformancePayRate: item.PerformancePayRate,
          PerformanceLargessRate: item.PerformanceLargessRate,

          Child: [],
        };
        if (item.PerformanceCardLargessRate || item.PerformanceCardRate || item.PerformancePayRate || item.PerformanceLargessRate) {
          if (item.Child && item.Child.length) {
            item.Child.forEach((item) => {
              if (item.PerformanceCardLargessRate || item.PerformanceCardRate || item.PerformancePayRate || item.PerformanceLargessRate) {
                param.Child.push({
                  CategoryID: item.CategoryID,
                  PerformanceCardLargessRate: item.PerformanceCardLargessRate,
                  PerformanceCardRate: item.PerformanceCardRate,
                  PerformancePayRate: item.PerformancePayRate,
                  PerformanceLargessRate: item.PerformanceLargessRate,
                });
              }
            });
          }
          that.EditFormData.TreatProductCategory.push(param);
        } else if (item.Child && item.Child.length) {
          item.Child.forEach((item) => {
            if (item.PerformanceCardLargessRate || item.PerformanceCardRate || item.PerformancePayRate || item.PerformanceLargessRate) {
              param.Child.push({
                CategoryID: item.CategoryID,
                PerformanceCardLargessRate: item.PerformanceCardLargessRate,
                PerformanceCardRate: item.PerformanceCardRate,
                PerformancePayRate: item.PerformancePayRate,
                PerformanceLargessRate: item.PerformanceLargessRate,
              });
            }
          });
          if (param.Child.length) {
            that.EditFormData.TreatProductCategory.push(param);
          }
        }
      });
      // 消耗项目
      that.GoodsCategoryCommission.TreatProjectCategory.forEach((item) => {
        let param = {
          CategoryID: item.CategoryID,
          PerformanceCardLargessRate: item.PerformanceCardLargessRate,
          PerformanceCardRate: item.PerformanceCardRate,
          PerformancePayRate: item.PerformancePayRate,
          PerformanceLargessRate: item.PerformanceLargessRate,
          Child: [],
        };
        if (item.PerformanceCardLargessRate || item.PerformanceCardRate || item.PerformancePayRate || item.PerformanceLargessRate) {
          if (item.Child && item.Child.length) {
            item.Child.forEach((item) => {
              if (item.PerformanceCardLargessRate || item.PerformanceCardRate || item.PerformancePayRate || item.PerformanceLargessRate) {
                param.Child.push({
                  CategoryID: item.CategoryID,
                  PerformanceCardLargessRate: item.PerformanceCardLargessRate,
                  PerformanceCardRate: item.PerformanceCardRate,
                  PerformancePayRate: item.PerformancePayRate,
                  PerformanceLargessRate: item.PerformanceLargessRate,
                });
              }
            });
          }
          that.EditFormData.TreatProjectCategory.push(param);
        } else if (item.Child && item.Child.length) {
          item.Child.forEach((item) => {
            if (item.PerformanceCardLargessRate || item.PerformanceCardRate || item.PerformancePayRate || item.PerformanceLargessRate) {
              param.Child.push({
                CategoryID: item.CategoryID,
                PerformanceCardLargessRate: item.PerformanceCardLargessRate,
                PerformanceCardRate: item.PerformanceCardRate,
                PerformancePayRate: item.PerformancePayRate,
                PerformanceLargessRate: item.PerformanceLargessRate,
              });
            }
          });
          if (param.Child.length) {
            that.EditFormData.TreatProjectCategory.push(param);
          }
        }
      });
      // 消耗通用次卡
      that.GoodsCategoryCommission.TreatGeneralCardCategory.forEach((item) => {
        if (item.PerformancePayRate || item.PerformanceCardRate || item.PerformanceLargessRate || item.PerformanceCardLargessRate) {
          let param = {
            CategoryID: item.CategoryID,
            PerformancePayRate: item.PerformancePayRate,
            PerformanceCardRate: item.PerformanceCardRate,
            PerformanceLargessRate: item.PerformanceLargessRate,
            PerformanceCardLargessRate: item.PerformanceCardLargessRate,
          };
          that.EditFormData.TreatGeneralCardCategory.push(param);
        }
      });
      // 消耗时效卡
      that.GoodsCategoryCommission.TreatTimeCardCategory.forEach((item) => {
        if (item.PerformancePayRate || item.PerformanceCardRate || item.PerformanceCardLargessRate || item.PerformanceLargessRate) {
          let param = {
            CategoryID: item.CategoryID,
            PerformancePayRate: item.PerformancePayRate,
            PerformanceCardLargessRate: item.PerformanceCardLargessRate,
            PerformanceCardRate: item.PerformanceCardRate,
            PerformanceLargessRate: item.PerformanceLargessRate,
          };
          that.EditFormData.TreatTimeCardCategory.push(param);
        }
      });
      // 消耗储值卡
      that.GoodsCategoryCommission.TreatSavingCardCategory.forEach((item) => {
        if (item.PerformanceCardRate || item.PerformanceCardLargessRate) {
          let param = {
            CategoryID: item.CategoryID,
            PerformanceCardRate: item.PerformanceCardRate,
            PerformanceCardLargessRate: item.PerformanceCardLargessRate,
          };
          that.EditFormData.TreatSavingCardCategory.push(param);
        }
      });

      API.PerformanceSchemeUpdate(that.EditFormData)
        .then((res) => {
          if (res.StateCode == 200) {
            that.handleSearch();
            that.$message.success({
              message: "保存成功",
              duration: 2000,
            });
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(() => {
          that.saveLoading = false;
        });
    },

    // 商品明细业绩获取
    goodsPerformance(type, row) {
      const that = this;
      const params = {
        PerformanceSchemeID: that.EditFormData.ID,
        CategoryID: row.CategoryID,
      };
      let api;
      switch (type) {
        case 1:
          that.GoodsTitle = "产品销售业绩取值" + " - " + row.ParentName + " - " + row.CategoryName;
          that.goodsCategory = "产品名称";
          api = "PerformanceSchemeProductAll";
          break;
        case 2:
          that.GoodsTitle = "项目销售业绩取值" + " - " + row.ParentName + " - " + row.CategoryName;
          that.goodsCategory = "项目名称";
          api = "PerformanceSchemeProjectAll";
          break;
        case 3:
          that.GoodsTitle = "储值卡销售业绩取值" + " - " + row.CategoryName;
          that.goodsCategory = "储值卡名称";
          api = "PerformanceSchemeSavingCardAll";
          break;
        case 4:
          that.GoodsTitle = "时效卡销售业绩取值" + " - " + row.CategoryName;
          that.goodsCategory = "时效卡名称";
          api = "PerformanceSchemeTimeCardAll";
          break;
        case 5:
          that.GoodsTitle = "通用次卡销售业绩取值" + " - " + row.CategoryName;
          that.goodsCategory = "通用次卡名称";
          api = "PerformanceSchemeGeneralCardAll";
          break;
        case 6:
          that.GoodsTitle = "产品消耗业绩取值" + " - " + row.ParentName + " - " + row.CategoryName;
          that.goodsCategory = "产品名称";
          api = "PerformanceSchemeTreatProductAll";
          break;
        case 7:
          that.GoodsTitle = "项目消耗业绩取值" + " - " + row.ParentName + " - " + row.CategoryName;
          that.goodsCategory = "项目名称";
          api = "PerformanceSchemeTreatProjectAll";
          break;
        case 8:
          that.GoodsTitle = "储值卡消耗业绩取值" + " - " + row.CategoryName;
          that.goodsCategory = "储值卡名称";
          api = "PerformanceSchemeTreatSavingCardAll";
          break;
        case 9:
          that.GoodsTitle = "时效卡消耗业绩取值" + " - " + row.CategoryName;
          that.goodsCategory = "时效卡名称";
          api = "PerformanceSchemeTreatTimeCardAll";
          break;
        case 10:
          that.GoodsTitle = "通用次卡消耗业绩取值" + " - " + row.CategoryName;
          that.goodsCategory = "通用次卡名称";
          api = "PerformanceSchemeTreatGeneralCardAll";
          break;
        case 11:
          that.GoodsTitle = "套餐卡销售业绩取值" + " - " + row.CategoryName;
          that.goodsCategory = "套餐卡名称";
          api = "performanceSchemePackageCard_all";
          break;
      }
      API[api](params).then((res) => {
        if (res.StateCode == 200) {
          that.goodsPerformanceList = res.Data;
        } else {
          that.$message.error({
            message: res.Message,
            duration: 2000,
          });
        }
      });
    },
    // 商品明细业绩保存
    goodsDetailSave() {
      const that = this;
      const activeName = that.activeName;
      let api;
      let param = {
        PerformanceSchemeID: that.EditFormData.ID,
        CategoryID: that.goodsPerformanceID,
        Good: [],
      };
      if (!that.goodsPerformanceList.length) return;
      switch (activeName) {
        case "first":
          api = "PerformanceSchemeProductCreate";
          that.goodsPerformanceList.forEach((item) => {
            if (item.PayPerformanceRate || item.SavingCardPerformanceLargessRate || item.SavingCardPerformanceRate) {
              param.Good.push({
                GoodID: item.ID,
                PayPerformanceRate: item.PayPerformanceRate,
                SavingCardPerformanceRate: item.SavingCardPerformanceRate,
                SavingCardPerformanceLargessRate: item.SavingCardPerformanceLargessRate,
              });
            }
          });
          break;
        case "second":
          api = "PerformanceSchemeProjectCreate";
          that.goodsPerformanceList.forEach((item) => {
            if (item.PayPerformanceRate || item.SavingCardPerformanceLargessRate || item.SavingCardPerformanceRate) {
              param.Good.push({
                GoodID: item.ID,
                PayPerformanceRate: item.PayPerformanceRate,
                SavingCardPerformanceRate: item.SavingCardPerformanceRate,
                SavingCardPerformanceLargessRate: item.SavingCardPerformanceLargessRate,
              });
            }
          });
          break;
        case "third":
          api = "PerformanceSchemeGeneralSavingCardCreate";
          that.goodsPerformanceList.forEach((item) => {
            if (item.PayPerformanceRate) {
              param.Good.push({
                GoodID: item.ID,
                PayPerformanceRate: item.PayPerformanceRate,
              });
            }
          });
          break;
        case "fourth":
          api = "PerformanceSchemeGeneralTimeCardCreate";
          that.goodsPerformanceList.forEach((item) => {
            if (item.PayPerformanceRate || item.SavingCardPerformanceLargessRate || item.SavingCardPerformanceRate) {
              param.Good.push({
                GoodID: item.ID,
                PayPerformanceRate: item.PayPerformanceRate,
                SavingCardPerformanceRate: item.SavingCardPerformanceRate,
                SavingCardPerformanceLargessRate: item.SavingCardPerformanceLargessRate,
              });
            }
          });
          break;
        case "five":
          api = "PerformanceSchemeGeneralCardCreate";
          that.goodsPerformanceList.forEach((item) => {
            if (item.PayPerformanceRate || item.SavingCardPerformanceLargessRate || item.SavingCardPerformanceRate) {
              param.Good.push({
                GoodID: item.ID,
                PayPerformanceRate: item.PayPerformanceRate,
                SavingCardPerformanceRate: item.SavingCardPerformanceRate,
                SavingCardPerformanceLargessRate: item.SavingCardPerformanceLargessRate,
              });
            }
          });
          break;
        case "six":
          api = "PerformanceSchemeGeneralTreatProductCreate";
          that.goodsPerformanceList.forEach((item) => {
            if (item.PerformanceCardLargessRate || item.PerformanceCardRate || item.PerformancePayRate || item.PerformanceLargessRate) {
              param.Good.push({
                GoodID: item.ID,
                PerformanceCardLargessRate: item.PerformanceCardLargessRate,
                PerformanceCardRate: item.PerformanceCardRate,
                PerformancePayRate: item.PerformancePayRate,
                PerformanceLargessRate: item.PerformanceLargessRate,
              });
            }
          });
          break;
        case "seven":
          api = "PerformanceSchemeGeneralTreatProjectCreate";
          that.goodsPerformanceList.forEach((item) => {
            if (item.PerformanceCardLargessRate || item.PerformanceCardRate || item.PerformancePayRate || item.PerformanceLargessRate) {
              param.Good.push({
                GoodID: item.ID,
                PerformanceCardLargessRate: item.PerformanceCardLargessRate,
                PerformanceCardRate: item.PerformanceCardRate,
                PerformancePayRate: item.PerformancePayRate,
                PerformanceLargessRate: item.PerformanceLargessRate,
              });
            }
          });
          break;
        case "eight":
          api = "PerformanceSchemeTreatSavingCardCreate";
          that.goodsPerformanceList.forEach((item) => {
            if (item.PerformanceCardRate || item.PerformanceCardLargessRate) {
              param.Good.push({
                GoodID: item.ID,
                PerformanceCardRate: item.PerformanceCardRate,
                PerformanceCardLargessRate: item.PerformanceCardLargessRate,
              });
            }
          });
          break;
        case "nine":
          api = "PerformanceSchemeTreatTimeCardCreate";
          that.goodsPerformanceList.forEach((item) => {
            if (item.PerformanceCardLargessRate || item.PerformanceCardRate || item.PerformancePayRate || item.PerformanceLargessRate) {
              param.Good.push({
                GoodID: item.ID,
                PerformanceCardLargessRate: item.PerformanceCardLargessRate,
                PerformanceCardRate: item.PerformanceCardRate,
                PerformancePayRate: item.PerformancePayRate,
                PerformanceLargessRate: item.PerformanceLargessRate,
              });
            }
          });
          break;
        case "ten":
          api = "PerformanceSchemeTreatGeneralCardCreate";
          that.goodsPerformanceList.forEach((item) => {
            if (item.PerformanceCardLargessRate || item.PerformanceCardRate || item.PerformancePayRate || item.PerformanceLargessRate) {
              param.Good.push({
                GoodID: item.ID,
                PerformanceCardLargessRate: item.PerformanceCardLargessRate,
                PerformanceCardRate: item.PerformanceCardRate,
                PerformancePayRate: item.PerformancePayRate,
                PerformanceLargessRate: item.PerformanceLargessRate,
              });
            }
          });
          break;
        case "sale-packageCard":
          api = "performanceSchemePackageCard_create";
          that.goodsPerformanceList.forEach((item) => {
            if (item.PayPerformanceRate || item.SavingCardPerformanceLargessRate || item.SavingCardPerformanceRate) {
              param.Good.push({
                GoodID: item.ID,
                PayPerformanceRate: item.PayPerformanceRate,
                SavingCardPerformanceRate: item.SavingCardPerformanceRate,
                SavingCardPerformanceLargessRate: item.SavingCardPerformanceLargessRate,
              });
            }
          });
          break;
      }
      API[api](param).then((res) => {
        if (res.StateCode == 200) {
          that.$message.success({
            message: "保存成功",
            duration: 2000,
          });
        } else {
          that.$message.error({
            message: res.Message,
            duration: 2000,
          });
        }
      });
    },
    // 显示弹层
    handleShow(type, row, opt) {
      const that = this;
      that[type] = true;
      switch (type) {
        // 编辑业绩取值方案弹层
        case "editDialogVisible":
          that.EditFormData.ID = row.ID;
          that.EditFormData.Active = row.Active;
          that.EditFormData.Name = row.Name;
          that.activeName = "first";
          that.packageActiveName = "noPackage";
          that.PerformanceSchemeDetail(row.ID);
          break;
        // 商品明细业绩弹层
        case "dialogDetailVisible":
          that.goodsPerformanceID = row.CategoryID;
          that.goodsPerformance(opt, row);
          break;
      }
    },
    // 保存弹层
    async handleSave(type) {
      const that = this;
      switch (type) {
        case "newDialogVisible":
          that.handleNewAdd();
          break;
        case "editDialogVisible":
          that.handleEditSave();
          break;
        case "dialogDetailVisible":
          that.goodsDetailSave();
          break;
      }
      await that.$nextTick();
      that[type] = false;
    },
    // 关闭弹层
    handleClose(type, ref) {
      const that = this;
      let isRef = false; //控制是否执行Ref清空表单
      that[type] = false; //关闭弹层

      switch (type) {
        // 编辑业绩取值方案弹层
        case "editDialogVisible":
          isRef = true;
          that.activeName = "first";
          that.EditFormData = {
            ID: "",
            Name: "",
            Active: true,
            ProductCategory: [],
            ProjectCategory: [],
            GeneralCardCategory: [],
            TimeCardCategory: [],
            SavingCardCategory: [],
            PackageCardCategory: [],
            TreatProductCategory: [],
            TreatProjectCategory: [],
            TreatGeneralCardCategory: [],
            TreatTimeCardCategory: [],
            TreatSavingCardCategory: [],
            ProductPackageCardCategory: [], //产品套餐卡分类
            ProjectPackageCardCategory: [], //项目套餐卡分类
            GeneralCardPackageCardCategory: [], //通用次卡套餐卡分类
            TimeCardPackageCardCategory: [], //时效卡套餐卡分类
            SavingCardPackageCardCategory: [], //储值卡套餐卡分类
            ProductPackageCard: [], //产品套餐卡
            ProjectPackageCard: [], //项目套餐卡
            GeneralCardPackageCard: [], //通用次卡套餐卡
            TimeCardPackageCard: [], //时效卡套餐卡
            SavingCardPackageCard: [], //产品套餐卡
          };
            that.defaultCheckedKeys = [];
            that.defaultExpandedKeys = [];

            that.defaultCheckedKeys = [];
            that.defaultExpandedKeys = [];

            that.defaultProjectCheckedKeys = [];
            that.defaultProjectExpandedKeys = [];

            that.defaultSavingCardCheckedKeys = [];
            that.defaultSavingCardExpandedKeys = [];

            that.defaultTimeCardCheckedKeys = [];
            that.defaultTimeCardExpandedKeys = [];

            that.defaultGeneralCardCheckedKeys = [];
            that.defaultGeneralCardExpandedKeys = [];
 

          break;
        // 商品明细业绩弹层
        case "dialogDetailVisible":
          isRef = true;
          break;
      }
      if (isRef) return;
      that.$refs[ref].resetFields();
    },
  },
};
</script>

<style lang="scss">
.SalaryPerformanceScheme {
  .editDialog {
    .el-input__inner {
      padding: 0 0 0 15px;
    }
    .el-tabs__content {
      .el-tabs__content {
        padding: unset;
        // padding-left: unset;
        // padding-right: unset;
        // padding-bottom: unset;
        // padding-top: 5px;
      }
    }
    .el-scrollbar_height {
      height: 50vh;

      .el-scrollbar__wrap {
        overflow-x: hidden;
      }
    }
  }
}
</style>
