<template>
  <div style="height: calc(100% - 63px)">
    <el-row class="consume_content">
      <el-col :span="9" class="project_left">
        <div class="pad_15">
          <el-input
            prefix-icon="el-icon-search"
            placeholder="请输入商品名称、别名关键字，按回车搜索"
            v-model="searchName"
            size="small"
            clearable
            @keyup.enter.native="searchGoodsClick"
            @clear="clearClick"
          ></el-input>
        </div>

        <el-tabs class="consumeGoods" v-model="tabPane" @tab-click="tabClick">
          <el-tab-pane v-if="treatProjectAccountList.length > 0" label="项目" name="0">
            <el-scrollbar class="el-scrollbar_height">
              <el-card
                class="marbm_10 marlt_10 marrt_10 cursor_pointer"
                :class="index == 0 ? 'martp_10' : ''"
                :body-style="{ padding: '0px' }"
                shadow="hover"
                v-for="(item, index) in treatProjectAccountList"
                :key="index + 'cpr'"
                @click.native="consumeProjectClick(item)"
              >
                <div slot="header" class="dis_flex flex_x_between flex_y_center">
                  <div>
                    <span>{{ item.Name }}</span>
                    <span v-if="item.Alias">({{ item.Alias }})</span>
                    <el-tag v-if="item.IsLargess" size="mini" class="marlt_5" type="danger">赠</el-tag>
                  </div>
                  <el-button @click.stop="checkAccountOperationRecord(item, 'Project')" size="mini" type="text">操作记录</el-button>
                </div>
                <el-row class="border_bottom">
                  <el-col :span="8" class="border_right">
                    <div class="goods-item">
                      <span class="goods-lable">有效次数：</span>
                      <span>{{ item.ValidBalance }}</span>
                      <el-popover class="marlt_5" placement="top-start" width="240" trigger="hover">
                        <p>有效次数= 剩余数量-退款中数量-欠款占用的数量[欠款金额÷参考单价(向上取整)]</p>
                        <el-button type="text" style="color: #dcdfe6" class="font_12 el-popover-botton-tip" icon="el-icon-info" slot="reference"></el-button>
                      </el-popover>
                    </div>
                  </el-col>
                  <el-col :span="8">
                    <div class="goods-item">
                      <span class="goods-lable">剩余数量：</span>
                      <span>{{ item.Balance }}</span>
                    </div>
                  </el-col>
                  <el-col :span="8" class="border_left">
                    <div class="goods-item">
                      <span class="goods-lable">退款中数量：</span>
                      <span>{{ item.RefundBalance }}</span>
                    </div>
                  </el-col>
                </el-row>
                <el-row class="border_bottom">
                  <el-col :span="8" class="border_right">
                    <div class="goods-item">
                      <span class="goods-lable">购买价格：</span>
                      <span v-if="item.IsLargess">¥ 0.00</span>
                      <span v-else>¥ {{ item.TotalAmount | toFixed | NumFormat }}</span>
                    </div>
                  </el-col>
                  <el-col :span="8">
                    <div class="goods-item">
                      <span class="goods-lable">购买数量：</span>
                      <span>{{ item.Quantity }}</span>
                    </div>
                  </el-col>
                  <el-col :span="8" class="border_left">
                    <div class="goods-item">
                      <span class="goods-lable">参考单价：</span>
                      <span>¥ {{ item.Amount | toFixed | NumFormat }}</span>
                    </div>
                  </el-col>
                </el-row>
                <el-row :class="item.Remark ? 'border_bottom' : ''">
                  <el-col :span="8" class="border_right">
                    <div class="goods-item">
                      <span class="goods-lable">欠款金额：</span>
                      <span>¥ {{ item.ArrearAmount | toFixed | NumFormat }}</span>
                    </div>
                  </el-col>
                  <el-col :span="16">
                    <div class="goods-item">
                      <span class="goods-lable">购买日期：</span>
                      <span>{{ item.BuyDate }}</span>
                    </div>
                  </el-col>
                </el-row>
                <el-row v-if="item.Remark">
                  <el-col :span="24">
                    <div class="goods-item">
                      <span class="goods-lable color_red">备注信息：</span>
                      <span class="color_red">{{ item.Remark }}</span>
                    </div>
                  </el-col>
                </el-row>
              </el-card>
            </el-scrollbar>
          </el-tab-pane>
          <el-tab-pane v-if="treatSavingCardAccountList.length > 0" label="储值卡" name="1">
            <el-scrollbar class="el-scrollbar_height el_scrollbar_project">
              <el-card
                class="marbm_10 marlt_10 marrt_10 cursor_pointer"
                :class="index == 0 ? 'martp_10' : ''"
                :body-style="{ padding: '0px' }"
                shadow="hover"
                v-for="(item, index) in treatSavingCardAccountList"
                :key="index + 'cs'"
                @click.native="consumeSavingCardClick(item)"
              >
                <div slot="header" class="dis_flex flex_x_between flex_y_center">
                  <div>
                    <span>{{ item.Name }}</span>
                    <span v-if="item.Alias">({{ item.Alias }})</span>
                    <el-tag v-if="item.IsLargess" size="mini" class="marlt_5" type="danger">赠</el-tag>
                  </div>
                  <el-button @click.stop="checkAccountOperationRecord(item, 'SavingCard')" size="mini" type="text">操作记录</el-button>
                </div>
                <el-row class="border_bottom">
                  <el-col :span="8" class="border_right">
                    <div class="goods-item">
                      <span class="goods-lable">有效余额：</span>
                      <span>¥ {{ item.ValidBalance | toFixed | NumFormat }}</span>
                      <el-popover class="marlt_5" placement="top-start" width="240" trigger="hover">
                        <p>1.有效金额 = 剩余数量-退款中金额-欠款金额；</p>
                        <p>2.注意：如果有赠额的情况，赠额可用金额也跟退款金额和欠款金额有关；</p>
                        <p>3.比如：充值100送100，欠款50，则可用本金为50，可用赠额为50，可用余额为100。</p>
                        <el-button type="text" style="color: #dcdfe6" class="font_12 el-popover-botton-tip" icon="el-icon-info" slot="reference"></el-button>
                      </el-popover>
                    </div>
                  </el-col>
                  <el-col :span="8">
                    <div class="goods-item">
                      <span class="goods-lable">剩余金额：</span>
                      <span>¥ {{ item.TotalBalance | toFixed | NumFormat }}</span>
                      <el-popover class="marlt_5" placement="top-start" width="240" trigger="hover">
                        <p>剩余金额 = 剩余本金+剩余赠额</p>
                        <el-button type="text" style="color: #dcdfe6" class="font_12 el-popover-botton-tip" icon="el-icon-info" slot="reference"></el-button>
                      </el-popover>
                    </div>
                  </el-col>
                  <el-col :span="8" class="border_left">
                    <div class="goods-item">
                      <span class="goods-lable">退款中金额：</span>
                      <span>¥ {{ item.RefundAmount | toFixed | NumFormat }}</span>
                    </div>
                  </el-col>
                </el-row>
                <el-col :span="8" class="border_right">
                  <div class="goods-item">
                    <span class="goods-lable">购买金额：</span>
                    <span>¥ {{ item.TotalAmount | toFixed | NumFormat }}</span>
                  </div>
                </el-col>
                <el-row class="border_bottom">
                  <el-col :span="8">
                    <div class="goods-item">
                      <span class="goods-lable">剩余本金：</span>
                      <span>¥ {{ item.Balance | toFixed | NumFormat }}</span>
                    </div>
                  </el-col>
                  <el-col :span="8" class="border_left">
                    <div class="goods-item">
                      <span class="goods-lable">剩余赠额：</span>
                      <span>¥ {{ item.LargessBalance | toFixed | NumFormat }}</span>
                    </div>
                  </el-col>
                </el-row>
                <el-row :class="item.Remark ? 'border_bottom' : ''">
                  <el-col :span="8" class="border_right">
                    <div class="goods-item">
                      <span class="goods-lable">欠款金额：</span>
                      <span>¥ {{ item.ArrearAmount | toFixed | NumFormat }}</span>
                    </div>
                  </el-col>
                  <el-col :span="8">
                    <div class="goods-item">
                      <span class="goods-lable">购买日期：</span>
                      <span>{{ item.BuyDate }}</span>
                    </div>
                  </el-col>
                  <el-col :span="8" class="border_left">
                    <div class="goods-item">
                      <span class="goods-lable">有效期：</span>
                      <span>{{ item.ValidDayName }}</span>
                    </div>
                  </el-col>
                </el-row>
                <el-row v-if="item.Remark">
                  <el-col :span="24">
                    <div class="goods-item">
                      <span class="goods-lable color_red">备注信息：</span>
                      <span class="color_red">{{ item.Remark }}</span>
                    </div>
                  </el-col>
                </el-row>
              </el-card>
            </el-scrollbar>
          </el-tab-pane>
          <el-tab-pane v-if="treatTimeCardAccountList.length > 0" label="时效卡" name="2">
            <el-scrollbar class="el-scrollbar_height el_scrollbar_project">
              <el-card
                class="marbm_10 marlt_10 marrt_10 cursor_pointer"
                :class="index == 0 ? 'martp_10' : ''"
                :body-style="item.IsRefund > 0 ? { padding: '0px', 'background-color': '#F5F7FA' } : { padding: '0px' }"
                :shadow="item.IsRefund > 0 ? 'never' : 'hover'"
                v-for="(item, index) in treatTimeCardAccountList"
                :key="index + 'ct'"
                @click.native="consumeTimeCardClick(item)"
              >
                <div slot="header" class="dis_flex flex_x_between flex_y_center">
                  <div>
                    <span>{{ item.Name }}</span>
                    <span v-if="item.Alias">({{ item.Alias }})</span>
                    <el-tag v-if="item.IsLargess" size="mini" class="marlt_5" type="danger">赠</el-tag>
                    <el-tag v-if="item.IsRefund" size="mini" class="marlt_5" type="danger">退款处理中</el-tag>
                  </div>
                  <el-button @click.stop="checkAccountOperationRecord(item, 'TimeCard')" size="mini" type="text">操作记录</el-button>
                </div>
                <el-row class="border_bottom">
                  <el-col :span="8" class="border_right">
                    <div class="goods-item">
                      <span class="goods-lable">周期内消耗次数：</span>
                      <span>{{ item.ConsumeCycleAmount }}</span>
                      <el-popover class="marlt_5" placement="top-start" width="240" trigger="hover">
                        <p>当“周期内消耗次数”超过“消耗周期限制”，则不能使用。</p>
                        <el-button type="text" style="color: #dcdfe6" class="font_12 el-popover-botton-tip" icon="el-icon-info" slot="reference"></el-button>
                      </el-popover>
                    </div>
                  </el-col>
                  <el-col :span="8">
                    <div class="goods-item">
                      <span class="goods-lable">消耗周期限制：</span>
                      <span>{{
                        item.ConsumeCycle == 0 || item.CycleLimitAmount == 0 ? '不限制' : item.CycleLimitAmount + '(次)/' + item.ConsumeCycle + '(天)'
                      }}</span>
                    </div>
                  </el-col>
                  <el-col :span="8" class="border_left">
                    <div class="goods-item">
                      <span class="goods-lable">累计耗用次数：</span>
                      <span>{{ item.ConsumeAmount }}</span>
                    </div>
                  </el-col>
                </el-row>
                <el-row class="border_bottom">
                  <el-col :span="8" class="border_right">
                    <div class="goods-item">
                      <span class="goods-lable">购买金额：</span>
                      <span v-if="item.IsLargess">¥ 0.00</span>
                      <span v-else>¥ {{ item.TotalAmount | toFixed | NumFormat }}</span>
                    </div>
                  </el-col>
                  <el-col :span="8">
                    <div class="goods-item">
                      <span class="goods-lable">剩余业绩提成次数：</span>
                      <span>{{ item.PerformanceBalance }}</span>
                      <el-popover class="marlt_5" placement="top-start" width="240" trigger="hover">
                        <p>用于确认员工消耗业绩。</p>
                        <p>
                          比如：购买金额为1000(如果卡为赠送，则按照售卖价格)，业绩提成次数为10，则每次使用时效卡耗做项目，则1~10次时，员工业绩为100，11次以后（包含第11次），员工业绩为0。
                        </p>
                        <el-button type="text" style="color: #dcdfe6" class="font_12 el-popover-botton-tip" icon="el-icon-info" slot="reference"></el-button>
                      </el-popover>
                    </div>
                  </el-col>
                  <el-col :span="8" class="border_left">
                    <div class="goods-item">
                      <span class="goods-lable">参考单价：</span>
                      <span>¥ {{ item.Amount | toFixed | NumFormat }}</span>
                    </div>
                  </el-col>
                </el-row>
                <el-row :class="item.Remark ? 'border_bottom' : ''">
                  <el-col :span="8" class="border_right">
                    <div class="goods-item">
                      <span class="goods-lable">欠款金额：</span>
                      <span>¥ {{ item.ArrearAmount | toFixed | NumFormat }}</span>
                    </div>
                  </el-col>
                  <el-col :span="8">
                    <div class="goods-item">
                      <span class="goods-lable">购买日期：</span>
                      <span>{{ item.BuyDate }}</span>
                    </div>
                  </el-col>
                  <el-col :span="8" class="border_left">
                    <div class="goods-item">
                      <span class="goods-lable">有效期：</span>
                      <span>{{ item.ValidDayName }}</span>
                    </div>
                  </el-col>
                </el-row>
                <el-row v-if="item.Remark">
                  <el-col :span="24">
                    <div class="goods-item">
                      <span class="goods-lable color_red">备注信息：</span>
                      <span class="color_red">{{ item.Remark }}</span>
                    </div>
                  </el-col>
                </el-row>
              </el-card>
            </el-scrollbar>
          </el-tab-pane>
          <el-tab-pane v-if="treatGeneralCardAccountList.length > 0" label="通用次卡" name="3">
            <el-scrollbar class="el-scrollbar_height el_scrollbar_project">
              <el-card
                class="marbm_10 marlt_10 marrt_10 cursor_pointer"
                :class="index == 0 ? 'martp_10' : ''"
                :body-style="{ padding: '0px' }"
                shadow="hover"
                v-for="(item, index) in treatGeneralCardAccountList"
                :key="index + 'cg'"
                @click.native="consumeGeneralCardClick(item)"
              >
                <div slot="header" class="dis_flex flex_x_between flex_y_center">
                  <div>
                    <span>{{ item.Name }}</span>
                    <span v-if="item.Alias">({{ item.Alias }})</span>
                    <el-tag v-if="item.IsLargess" size="mini" class="marlt_5" type="danger">赠</el-tag>
                  </div>
                  <el-button @click.stop="checkAccountOperationRecord(item, 'GeneralCard')" size="mini" type="text">操作记录</el-button>
                </div>
                <el-row class="border_bottom">
                  <el-col :span="8" class="border_right">
                    <div class="goods-item">
                      <span class="goods-lable">有效次数：</span>
                      <span>{{ item.ValidBalance }}</span>
                      <el-popover class="marlt_5" placement="top-start" width="240" trigger="hover">
                        <p>有效次数= 剩余数量-退款中数量-欠款占用的数量[欠款金额÷参考单价(向上取整)]</p>
                        <el-button type="text" style="color: #dcdfe6" class="font_12 el-popover-botton-tip" icon="el-icon-info" slot="reference"></el-button>
                      </el-popover>
                    </div>
                  </el-col>
                  <el-col :span="8">
                    <div class="goods-item">
                      <span class="goods-lable">剩余次数：</span>
                      <span>{{ item.Balance }}</span>
                    </div>
                  </el-col>
                  <el-col :span="8" class="border_left">
                    <div class="goods-item">
                      <span class="goods-lable">退款中数量：</span>
                      <span>{{ item.RefundAmount }}</span>
                    </div>
                  </el-col>
                </el-row>
                <el-row class="border_bottom">
                  <el-col :span="8" class="border_right">
                    <div class="goods-item">
                      <span class="goods-lable">购买金额：</span>
                      <span v-if="item.IsLargess">¥ 0.00</span>
                      <span v-else>¥ {{ item.TotalAmount | toFixed | NumFormat }}</span>
                    </div>
                  </el-col>
                  <el-col :span="8">
                    <div class="goods-item">
                      <span class="goods-lable">卡总次数：</span>
                      <span>{{ item.CardTimes }}</span>
                    </div>
                  </el-col>
                  <el-col :span="8" class="border_left">
                    <div class="goods-item">
                      <span class="goods-lable">参考单价：</span>
                      <span>¥ {{ item.Amount | toFixed | NumFormat }}</span>
                    </div>
                  </el-col>
                </el-row>
                <el-row :class="item.Remark ? 'border_bottom' : ''">
                  <el-col :span="8" class="border_right">
                    <div class="goods-item">
                      <span class="goods-lable">欠款金额：</span>
                      <span>¥ {{ item.ArrearAmount | toFixed | NumFormat }}</span>
                    </div>
                  </el-col>
                  <el-col :span="8">
                    <div class="goods-item">
                      <span class="goods-lable">购买日期：</span>
                      <span>{{ item.BuyDate }}</span>
                    </div>
                  </el-col>
                  <el-col :span="8" class="border_left">
                    <div class="goods-item">
                      <span class="goods-lable">有效期：</span>
                      <span>{{ item.ValidDayName }}</span>
                    </div>
                  </el-col>
                </el-row>
                <el-row v-if="item.Remark">
                  <el-col :span="24">
                    <div class="goods-item">
                      <span class="goods-lable color_red">备注信息：</span>
                      <span class="color_red">{{ item.Remark }}</span>
                    </div>
                  </el-col>
                </el-row>
              </el-card>
            </el-scrollbar>
          </el-tab-pane>
          <el-tab-pane v-if="treatGoodsPackageCardAccountList.length > 0" label="套餐卡" name="4">
            <el-row class="category_project valueCard_project">
              <el-col :span="4" class="text_center category">
                <el-scrollbar class="el-scrollbar_height border_right">
                  <div
                    v-for="(item, index) in treatGoodsPackageCardAccountList"
                    :key="index + 'cpc'"
                    :class="[currentPackCategoryIndex == index ? 'category_select' : '', 'pad_10_15', 'border_bottom', 'cursor_pointer']"
                    @click="clickPackageCagegory(item, index)"
                  >
                    <span class="line_20">{{ item.Name }}</span>
                    <span class="line_20" v-if="item.Alias">({{ item.Alias }})</span>
                    <el-tag v-if="item.IsLargess" size="mini" class="marlt_5 line_20" type="danger">赠</el-tag>
                    <el-popover class="marlt_5" popper-class="popover-package" placement="top-start" width="400" trigger="hover">
                      <el-card :body-style="{ padding: '0px' }">
                        <div slot="header">
                          <span>{{ item.Name }}</span>
                          <span v-if="item.Alias">({{ item.Alias }})</span>
                          <el-tag v-if="item.IsLargess" size="mini" class="marlt_5" type="danger">赠</el-tag>
                        </div>
                        <el-row class="border_bottom">
                          <el-col :span="12" class="border_right">
                            <div class="goods-item">
                              <span class="goods-lable">购买价格：</span>
                              <span>¥ {{ item.TotalAmount | toFixed | NumFormat }}</span>
                            </div>
                          </el-col>
                          <el-col :span="12">
                            <div class="goods-item">
                              <span class="goods-lable">欠款金额：</span>
                              <span>¥ {{ item.ArrearAmount | toFixed | NumFormat }}</span>
                            </div>
                          </el-col>
                        </el-row>
                        <el-row class="border_bottom">
                          <el-col :span="12" class="border_right">
                            <div class="goods-item">
                              <span class="goods-lable">购买日期：</span>
                              <span>{{ item.BuyDate }}</span>
                            </div>
                          </el-col>
                          <el-col :span="12">
                            <div class="goods-item">
                              <span class="goods-lable">有效期：</span>
                              <span>{{ item.ValidDayName }}</span>
                            </div>
                          </el-col>
                        </el-row>
                        <el-row v-if="item.Remark">
                          <el-col :span="24">
                            <div class="goods-item">
                              <span class="goods-lable color_red">备注信息：</span>
                              <span class="color_red">{{ item.Remark }}</span>
                            </div>
                          </el-col>
                        </el-row>
                      </el-card>
                      <el-button type="text" style="color: #dcdfe6; padding: 0px" class="font_12" icon="el-icon-info" slot="reference"></el-button>
                    </el-popover>
                  </div>
                </el-scrollbar>
              </el-col>
              <el-col :span="20" class="project" v-loading="packageDetailLoading">
                <div class="producct_list">
                  <el-scrollbar class="el-scrollbar_height">
                    <!-- 套餐卡项目 -->
                    <el-card
                      class="marbm_10 marlt_10 marrt_10 cursor_pointer"
                      :class="index == 0 ? 'martp_10' : ''"
                      :body-style="{ padding: '0px' }"
                      shadow="hover"
                      v-for="(item, index) in treatGoodsPackageCardAccountDetailsList.Project"
                      :key="index + 'cppr'"
                      @click.native="consumePackageProjectClick(item)"
                    >
                      <div slot="header" class="dis_flex flex_x_between flex_y_center">
                        <div>
                          <el-tag size="mini">项目</el-tag>
                          <span>{{ item.Name }}</span>
                          <span v-if="item.Alias">({{ item.Alias }})</span>
                          <el-tag v-if="item.IsLargess" size="mini" class="marlt_5" type="danger">赠</el-tag>
                        </div>
                        <el-button @click.stop="checkAccountOperationRecord(item, 'Project')" size="mini" type="text">操作记录</el-button>
                      </div>
                      <el-row class="border_bottom">
                        <el-col :span="8" class="border_right">
                          <div class="goods-item">
                            <span class="goods-lable">有效次数：</span>
                            <span>{{ item.ValidBalance }}</span>
                            <el-popover class="marlt_5" placement="top-start" width="240" trigger="hover">
                              <p>有效次数= 剩余数量-退款中数量-欠款占用的数量[欠款金额÷参考单价(向上取整)]</p>
                              <el-button
                                type="text"
                                style="color: #dcdfe6"
                                class="font_12 el-popover-botton-tip"
                                icon="el-icon-info"
                                slot="reference"
                              ></el-button>
                            </el-popover>
                          </div>
                        </el-col>
                        <el-col :span="8">
                          <div class="goods-item">
                            <span class="goods-lable">剩余数量：</span>
                            <span>{{ item.Balance }}</span>
                          </div>
                        </el-col>
                        <el-col :span="8" class="border_left">
                          <div class="goods-item">
                            <span class="goods-lable">退款中数量：</span>
                            <span>{{ item.RefundBalance }}</span>
                          </div>
                        </el-col>
                      </el-row>
                      <el-row class="border_bottom">
                        <el-col :span="8" class="border_right">
                          <div class="goods-item">
                            <span class="goods-lable">购买价格：</span>
                            <span v-if="item.IsLargess">¥ 0.00</span>
                            <span v-else>¥ {{ item.TotalAmount | toFixed | NumFormat }}</span>
                          </div>
                        </el-col>
                        <el-col :span="8">
                          <div class="goods-item">
                            <span class="goods-lable">购买数量：</span>
                            <span>{{ item.Quantity }}</span>
                          </div>
                        </el-col>
                        <el-col :span="8" class="border_left">
                          <div class="goods-item">
                            <span class="goods-lable">参考单价：</span>
                            <span>¥ {{ item.Amount | toFixed | NumFormat }}</span>
                          </div>
                        </el-col>
                      </el-row>
                      <el-row :class="item.Remark ? 'border_bottom' : ''">
                        <el-col :span="8" class="border_right">
                          <div class="goods-item">
                            <span class="goods-lable">欠款金额：</span>
                            <span>¥ {{ item.ArrearAmount | toFixed | NumFormat }}</span>
                          </div>
                        </el-col>
                        <el-col :span="8">
                          <div class="goods-item">
                            <span class="goods-lable">购买日期：</span>
                            <span>{{ item.BuyDate }}</span>
                          </div>
                        </el-col>
                        <el-col :span="8" class="border_left">
                          <div class="goods-item">
                            <span class="goods-lable">有效期：</span>
                            <span>{{ item.ValidDayName }}</span>
                          </div>
                        </el-col>
                      </el-row>
                      <el-row v-if="item.Remark">
                        <el-col :span="24">
                          <div class="goods-item">
                            <span class="goods-lable color_red">备注信息：</span>
                            <span class="color_red">{{ item.Remark }}</span>
                          </div>
                        </el-col>
                      </el-row>
                    </el-card>
                    <!-- 套餐卡储值卡 -->
                    <el-card
                      class="marbm_10 marlt_10 marrt_10 cursor_pointer"
                      :class="index == 0 ? 'martp_10' : ''"
                      :body-style="{ padding: '0px' }"
                      shadow="hover"
                      v-for="(item, index) in treatGoodsPackageCardAccountDetailsList.SavingCard"
                      :key="index + 'cps'"
                      @click.native="consumePackageSavingCardClick(currentSelectPackageItem, item)"
                    >
                      <div slot="header" class="dis_flex flex_x_between flex_y_center">
                        <div>
                          <el-tag size="mini">储值卡</el-tag>
                          <span>{{ item.Name }}</span>
                          <span v-if="item.Alias">({{ item.Alias }})</span>
                          <el-tag v-if="item.IsLargess" size="mini" class="marlt_5" type="danger">赠</el-tag>
                        </div>
                        <el-button @click.stop="checkAccountOperationRecord(item, 'SavingCard')" size="mini" type="text">操作记录</el-button>
                      </div>
                      <el-row class="border_bottom">
                        <el-col :span="8" class="border_right">
                          <div class="goods-item">
                            <span class="goods-lable">可用余额：</span>
                            <span>¥ {{ item.ValidBalance | toFixed | NumFormat }}</span>
                            <el-popover class="marlt_5" placement="top-start" width="240" trigger="hover">
                              <p>1.有效金额= 剩余数量-退款中金额-欠款金额；</p>
                              <p>2.注意：如果有赠额的情况，赠额可用金额也跟退款金额和欠款金额有关；</p>
                              <p>3.比如：充值100送100，欠款50，则可用本金为50，可用赠额为50，可用余额为100。</p>
                              <el-button
                                type="text"
                                style="color: #dcdfe6"
                                class="font_12 el-popover-botton-tip"
                                icon="el-icon-info"
                                slot="reference"
                              ></el-button>
                            </el-popover>
                          </div>
                        </el-col>
                        <el-col :span="8">
                          <div class="goods-item">
                            <span class="goods-lable">剩余金额：</span>
                            <span>¥ {{ item.TotalBalance | toFixed | NumFormat }}</span>
                            <el-popover class="marlt_5" placement="top-start" width="240" trigger="hover">
                              <p>剩余金额= 剩余本金+剩余赠额</p>
                              <el-button
                                type="text"
                                style="color: #dcdfe6"
                                class="font_12 el-popover-botton-tip"
                                icon="el-icon-info"
                                slot="reference"
                              ></el-button>
                            </el-popover>
                          </div>
                        </el-col>
                        <el-col :span="8" class="border_left">
                          <div class="goods-item">
                            <span class="goods-lable">退款中金额：</span>
                            <span>¥ {{ item.RefundAmount | toFixed | NumFormat }}</span>
                          </div>
                        </el-col>
                      </el-row>
                      <el-row class="border_bottom">
                        <el-col :span="8" class="border_right">
                          <div class="goods-item">
                            <span class="goods-lable">购买金额：</span>
                            <span>¥ {{ item.TotalAmount | toFixed | NumFormat }}</span>
                          </div>
                        </el-col>
                        <el-col :span="8">
                          <div class="goods-item">
                            <span class="goods-lable">剩余本金：</span>
                            <span>¥ {{ item.Balance | toFixed | NumFormat }}</span>
                          </div>
                        </el-col>
                        <el-col :span="8" class="border_left">
                          <div class="goods-item">
                            <span class="goods-lable">剩余赠额：</span>
                            <span>¥ {{ item.LargessBalance | toFixed | NumFormat }}</span>
                          </div>
                        </el-col>
                      </el-row>
                      <el-row :class="item.Remark ? 'border_bottom' : ''">
                        <el-col :span="8" class="border_right">
                          <div class="goods-item">
                            <span class="goods-lable">欠款金额：</span>
                            <span>¥ {{ item.ArrearAmount | toFixed | NumFormat }}</span>
                          </div>
                        </el-col>
                        <el-col :span="8">
                          <div class="goods-item">
                            <span class="goods-lable">购买日期：</span>
                            <span>{{ item.BuyDate }}</span>
                          </div>
                        </el-col>
                        <el-col :span="8" class="border_left">
                          <div class="goods-item">
                            <span class="goods-lable">有效期：</span>
                            <span>{{ item.ValidDayName }}</span>
                          </div>
                        </el-col>
                      </el-row>
                      <el-row v-if="item.Remark">
                        <el-col :span="24">
                          <div class="goods-item">
                            <span class="goods-lable color_red">备注信息：</span>
                            <span class="color_red">{{ item.Remark }}</span>
                          </div>
                        </el-col>
                      </el-row>
                    </el-card>
                    <!-- 套餐卡时效卡 -->
                    <el-card
                      class="marbm_10 marlt_10 marrt_10 cursor_pointer"
                      :class="index == 0 ? 'martp_10' : ''"
                      :body-style="{ padding: '0px' }"
                      shadow="hover"
                      v-for="(item, index) in treatGoodsPackageCardAccountDetailsList.TimeCard"
                      :key="index + 'cpt'"
                      @click.native="consumePackageTimeCardClick(currentSelectPackageItem, item)"
                    >
                      <div slot="header" class="dis_flex flex_x_between flex_y_center">
                        <div>
                          <el-tag size="mini">时效卡</el-tag>
                          <span>{{ item.Name }}</span>
                          <span v-if="item.Alias">({{ item.Alias }})</span>
                          <el-tag v-if="item.IsLargess" size="mini" class="marlt_5" type="danger">赠</el-tag>
                        </div>
                        <el-button @click.stop="checkAccountOperationRecord(item, 'TimeCard')" size="mini" type="text">操作记录</el-button>
                      </div>
                      <el-row class="border_bottom">
                        <el-col :span="8" class="border_right">
                          <div class="goods-item">
                            <span class="goods-lable">周期内消耗次数：</span>
                            <span>{{ item.ConsumeCycleAmount }}</span>
                            <el-popover class="marlt_5" placement="top-start" width="240" trigger="hover">
                              <p>当“周期内消耗次数”超过“消耗周期限制”，则不能使用。</p>
                              <el-button
                                type="text"
                                style="color: #dcdfe6"
                                class="font_12 el-popover-botton-tip"
                                icon="el-icon-info"
                                slot="reference"
                              ></el-button>
                            </el-popover>
                          </div>
                        </el-col>
                        <el-col :span="8">
                          <div class="goods-item">
                            <span class="goods-lable">消耗周期限制：</span>
                            <span>{{
                              item.ConsumeCycle == 0 || item.CycleLimitAmount == 0 ? '不限制' : item.CycleLimitAmount + '(次)/' + item.ConsumeCycle + '(天)'
                            }}</span>
                          </div>
                        </el-col>
                        <el-col :span="8" class="border_left">
                          <div class="goods-item">
                            <span class="goods-lable">累计耗用次数：</span>
                            <span>{{ item.ConsumeAmount }}</span>
                          </div>
                        </el-col>
                      </el-row>
                      <el-row class="border_bottom">
                        <el-col :span="8" class="border_right">
                          <div class="goods-item">
                            <span class="goods-lable">购买金额：</span>
                            <span v-if="item.IsLargess">¥ 0.00</span>
                            <span v-else>¥ {{ item.TotalAmount | toFixed | NumFormat }}</span>
                          </div>
                        </el-col>
                        <el-col :span="8">
                          <div class="goods-item">
                            <span class="goods-lable">业绩提成次数：</span>
                            <span>{{ item.PerformanceBalance }}</span>
                            <el-popover class="marlt_5" placement="top-start" width="240" trigger="hover">
                              <p>用于确认员工消耗业绩。</p>
                              <p>
                                比如：购买金额为1000，业绩提成次数为10，则每次使用时效卡耗做项目，则1~10次时，员工业绩为100，11次以后（包含第11次），员工业绩为0。
                              </p>
                              <el-button
                                type="text"
                                style="color: #dcdfe6"
                                class="font_12 el-popover-botton-tip"
                                icon="el-icon-info"
                                slot="reference"
                              ></el-button>
                            </el-popover>
                          </div>
                        </el-col>
                        <el-col :span="8" class="border_left">
                          <div class="goods-item">
                            <span class="goods-lable">参考单价：</span>
                            <span>¥ {{ item.Amount | toFixed | NumFormat }}</span>
                          </div>
                        </el-col>
                      </el-row>
                      <el-row :class="item.Remark ? 'border_bottom' : ''">
                        <el-col :span="8" class="border_right">
                          <div class="goods-item">
                            <span class="goods-lable">欠款金额：</span>
                            <span>¥ {{ item.ArrearAmount | toFixed | NumFormat }}</span>
                          </div>
                        </el-col>
                        <el-col :span="8">
                          <div class="goods-item">
                            <span class="goods-lable">购买日期：</span>
                            <span>{{ item.BuyDate }}</span>
                          </div>
                        </el-col>
                        <el-col :span="8" class="border_left">
                          <div class="goods-item">
                            <span class="goods-lable">有效期：</span>
                            <span>{{ item.ValidDayName }}</span>
                          </div>
                        </el-col>
                      </el-row>
                      <el-row v-if="item.Remark">
                        <el-col :span="24">
                          <div class="goods-item">
                            <span class="goods-lable color_red">备注信息：</span>
                            <span class="color_red">{{ item.Remark }}</span>
                          </div>
                        </el-col>
                      </el-row>
                    </el-card>
                    <!-- 套餐卡通用次卡 -->
                    <el-card
                      class="marbm_10 marlt_10 marrt_10 cursor_pointer"
                      :class="index == 0 ? 'martp_10' : ''"
                      :body-style="{ padding: '0px' }"
                      shadow="hover"
                      v-for="(item, index) in treatGoodsPackageCardAccountDetailsList.GeneralCard"
                      :key="index + 'cpg'"
                      @click.native="consumePackageGeneralCardClick(item)"
                    >
                      <div slot="header" class="dis_flex flex_x_between flex_y_center">
                        <div>
                          <el-tag size="mini">通用次卡</el-tag>
                          <span>{{ item.Name }}</span>
                          <span v-if="item.Alias">({{ item.Alias }})</span>
                          <el-tag v-if="item.IsLargess" size="mini" class="marlt_5" type="danger">赠</el-tag>
                        </div>
                        <el-button @click.stop="checkAccountOperationRecord(item, 'GeneralCard')" size="mini" type="text">操作记录</el-button>
                      </div>
                      <el-row class="border_bottom">
                        <el-col :span="8" class="border_right">
                          <div class="goods-item">
                            <span class="goods-lable">有效次数：</span>
                            <span>{{ item.ValidBalance }}</span>
                            <el-popover class="marlt_5" placement="top-start" width="240" trigger="hover">
                              <p>有效次数= 剩余数量-退款中数量-欠款占用的数量[欠款金额÷参考单价(向上取整)]</p>
                              <el-button
                                type="text"
                                style="color: #dcdfe6"
                                class="font_12 el-popover-botton-tip"
                                icon="el-icon-info"
                                slot="reference"
                              ></el-button>
                            </el-popover>
                          </div>
                        </el-col>
                        <el-col :span="8">
                          <div class="goods-item">
                            <span class="goods-lable">剩余次数：</span>
                            <span>{{ item.Balance }}</span>
                          </div>
                        </el-col>
                        <el-col :span="8" class="border_left">
                          <div class="goods-item">
                            <span class="goods-lable">退款中数量：</span>
                            <span>{{ item.RefundAmount }}</span>
                          </div>
                        </el-col>
                      </el-row>
                      <el-row class="border_bottom">
                        <el-col :span="8" class="border_right">
                          <div class="goods-item">
                            <span class="goods-lable">购买金额：</span>
                            <span v-if="item.IsLargess">¥ 0.00</span>
                            <span v-else>¥ {{ item.TotalAmount | toFixed | NumFormat }}</span>
                          </div>
                        </el-col>
                        <el-col :span="8">
                          <div class="goods-item">
                            <span class="goods-lable">卡总次数：</span>
                            <span>{{ item.CardTimes }}</span>
                          </div>
                        </el-col>
                        <el-col :span="8" class="border_left">
                          <div class="goods-item">
                            <span class="goods-lable">参考单价：</span>
                            <span>¥ {{ item.Amount | toFixed | NumFormat }}</span>
                          </div>
                        </el-col>
                      </el-row>
                      <el-row :class="item.Remark ? 'border_bottom' : ''">
                        <el-col :span="8" class="border_right">
                          <div class="goods-item">
                            <span class="goods-lable">欠款金额：</span>
                            <span>¥ {{ item.ArrearAmount | toFixed | NumFormat }}</span>
                          </div>
                        </el-col>
                        <el-col :span="8">
                          <div class="goods-item">
                            <span class="goods-lable">购买日期：</span>
                            <span>{{ item.BuyDate }}</span>
                          </div>
                        </el-col>
                        <el-col :span="8" class="border_left">
                          <div class="goods-item">
                            <span class="goods-lable">有效期：</span>
                            <span>{{ item.ValidDayName }}</span>
                          </div>
                        </el-col>
                      </el-row>
                      <el-row v-if="item.Remark">
                        <el-col :span="24">
                          <div class="goods-item">
                            <span class="goods-lable color_red">备注信息：</span>
                            <span class="color_red">{{ item.Remark }}</span>
                          </div>
                        </el-col>
                      </el-row>
                    </el-card>
                    <!-- 套餐卡产品 -->
                    <el-card
                      class="marbm_10 marlt_10 marrt_10 cursor_pointer"
                      :class="index == 0 ? 'martp_10' : ''"
                      :body-style="{ padding: '0px' }"
                      shadow="hover"
                      v-for="(item, index) in treatGoodsPackageCardAccountDetailsList.Product"
                      :key="index + 'cppd'"
                      @click.native="consumePackageProductClick(item)"
                    >
                      <div slot="header" class="dis_flex flex_x_between flex_y_center">
                        <div>
                          <el-tag size="mini">产品</el-tag>
                          <span>{{ item.Name }}</span>
                          <span v-if="item.Alias">({{ item.Alias }})</span>
                          <el-tag v-if="item.IsLargess" size="mini" class="marlt_5" type="danger">赠</el-tag>
                        </div>
                        <el-button @click.stop="checkAccountOperationRecord(item, 'Product')" size="mini" type="text">操作记录</el-button>
                      </div>
                      <el-row class="border_bottom">
                        <el-col :span="8" class="border_right">
                          <div class="goods-item">
                            <span class="goods-lable">有效数量：</span>
                            <span>{{ item.ValidBalance }}</span>
                            <el-popover class="marlt_5" placement="top-start" width="240" trigger="hover">
                              <p>有效数量= 剩余数量-退款中数量-欠款占用的数量[欠款金额÷参考单价(向上取整)]</p>
                              <el-button
                                type="text"
                                style="color: #dcdfe6"
                                class="font_12 el-popover-botton-tip"
                                icon="el-icon-info"
                                slot="reference"
                              ></el-button>
                            </el-popover>
                          </div>
                        </el-col>
                        <el-col :span="8">
                          <div class="goods-item">
                            <span class="goods-lable">剩余数量：</span>
                            <span>{{ item.Balance }}</span>
                          </div>
                        </el-col>
                        <el-col :span="8" class="border_left">
                          <div class="goods-item">
                            <span class="goods-lable">退款中数量：</span>
                            <span>{{ item.RefundBalance }}</span>
                          </div>
                        </el-col>
                      </el-row>
                      <el-row class="border_bottom">
                        <el-col :span="8" class="border_right">
                          <div class="goods-item">
                            <span class="goods-lable">购买价格：</span>
                            <span v-if="item.IsLargess">¥ 0.00</span>
                            <span v-else>¥ {{ item.TotalAmount | toFixed | NumFormat }}</span>
                          </div>
                        </el-col>
                        <el-col :span="8">
                          <div class="goods-item">
                            <span class="goods-lable">购买数量：</span>
                            <span>{{ item.Quantity }}</span>
                          </div>
                        </el-col>
                        <el-col :span="8" class="border_left">
                          <div class="goods-item">
                            <span class="goods-lable">参考单价：</span>
                            <span>¥ {{ item.Amount | toFixed | NumFormat }}</span>
                          </div>
                        </el-col>
                      </el-row>
                      <el-row :class="item.Remark ? 'border_bottom' : ''">
                        <el-col :span="8" class="border_right">
                          <div class="goods-item">
                            <span class="goods-lable">欠款金额：</span>
                            <span>¥ {{ item.ArrearAmount | toFixed | NumFormat }}</span>
                          </div>
                        </el-col>
                        <el-col :span="8">
                          <div class="goods-item">
                            <span class="goods-lable">购买日期：</span>
                            <span>{{ item.BuyDate }}</span>
                          </div>
                        </el-col>
                        <el-col :span="8" class="border_left">
                          <div class="goods-item">
                            <span class="goods-lable">有效期：</span>
                            <span>{{ item.ValidDayName }}</span>
                          </div>
                        </el-col>
                      </el-row>
                      <el-row v-if="item.Remark">
                        <el-col :span="24">
                          <div class="goods-item">
                            <span class="goods-lable color_red">备注信息：</span>
                            <span class="color_red">{{ item.Remark }}</span>
                          </div>
                        </el-col>
                      </el-row>
                    </el-card>
                  </el-scrollbar>
                </div>
              </el-col>
            </el-row>
          </el-tab-pane>
          <el-tab-pane v-if="treatProductAccountList.length > 0" label="产品" name="5">
            <el-scrollbar class="el-scrollbar_height el_scrollbar_project">
              <el-card
                class="marbm_10 marlt_10 marrt_10 cursor_pointer"
                :class="index == 0 ? 'martp_10' : ''"
                :body-style="{ padding: '0px' }"
                shadow="hover"
                v-for="(item, index) in treatProductAccountList"
                :key="index + 'cpd'"
                @click.native="consumeProductClick(item)"
              >
                <div slot="header" class="dis_flex flex_x_between flex_y_center">
                  <div>
                    <span>{{ item.Name }}</span>
                    <span v-if="item.Alias">({{ item.Alias }})</span>
                    <el-tag v-if="item.IsLargess" size="mini" class="marlt_5" type="danger">赠</el-tag>
                  </div>
                  <el-button @click.stop="checkAccountOperationRecord(item, 'Product')" size="mini" type="text">操作记录</el-button>
                </div>
                <el-row class="border_bottom">
                  <el-col :span="8" class="border_right">
                    <div class="goods-item">
                      <span class="goods-lable">有效数量：</span>
                      <span>{{ item.ValidBalance }}</span>
                      <el-popover class="marlt_5" placement="top-start" width="240" trigger="hover">
                        <p>有效数量= 剩余数量-退款中数量-欠款占用的数量[欠款金额÷参考单价(向上取整)]</p>
                        <el-button type="text" style="color: #dcdfe6" class="font_12 el-popover-botton-tip" icon="el-icon-info" slot="reference"></el-button>
                      </el-popover>
                    </div>
                  </el-col>
                  <el-col :span="8">
                    <div class="goods-item">
                      <span class="goods-lable">剩余数量：</span>
                      <span>{{ item.Balance }}</span>
                    </div>
                  </el-col>
                  <el-col :span="8" class="border_left">
                    <div class="goods-item">
                      <span class="goods-lable">退款中数量：</span>
                      <span>{{ item.RefundBalance }}</span>
                    </div>
                  </el-col>
                </el-row>
                <el-row class="border_bottom">
                  <el-col :span="8" class="border_right">
                    <div class="goods-item">
                      <span class="goods-lable">购买价格：</span>
                      <span v-if="item.IsLargess">¥ 0.00</span>
                      <span v-else>¥ {{ item.TotalAmount | toFixed | NumFormat }}</span>
                    </div>
                  </el-col>
                  <el-col :span="8">
                    <div class="goods-item">
                      <span class="goods-lable">购买数量：</span>
                      <span>{{ item.Quantity }}</span>
                    </div>
                  </el-col>
                  <el-col :span="8" class="border_left">
                    <div class="goods-item">
                      <span class="goods-lable">参考单价：</span>
                      <span>¥ {{ item.Amount | toFixed | NumFormat }}</span>
                    </div>
                  </el-col>
                </el-row>
                <el-row :class="item.Remark ? 'border_bottom' : ''">
                  <el-col :span="8" class="border_right">
                    <div class="goods-item">
                      <span class="goods-lable">欠款金额：</span>
                      <span>¥ {{ item.ArrearAmount | toFixed | NumFormat }}</span>
                    </div>
                  </el-col>
                  <el-col :span="16">
                    <div class="goods-item">
                      <span class="goods-lable">购买日期：</span>
                      <span>{{ item.BuyDate }}</span>
                    </div>
                  </el-col>
                </el-row>
                <el-row v-if="item.Remark">
                  <el-col :span="24">
                    <div class="goods-item">
                      <span class="goods-lable color_red">备注信息：</span>
                      <span class="color_red">{{ item.Remark }}</span>
                    </div>
                  </el-col>
                </el-row>
              </el-card>
            </el-scrollbar>
          </el-tab-pane>
        </el-tabs>
      </el-col>
      <el-col :span="15" class="project_right position_relative">
        <el-container style="height: 100%">
          <el-main>
            <el-scrollbar class="el-scrollbar_height color_333">
              <!--项目-->
              <div v-for="(item, index) in currentSelectProjectList" :key="index + 'spr'">
                <el-row class="row_header border_bottom">
                  <el-col :span="24">
                    <el-col :span="8">项目</el-col>
                    <el-col :span="8">数量</el-col>
                    <el-col :span="5">金额</el-col>
                    <el-col :span="3"></el-col>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="24" class="pad_10 border_bottom">
                    <el-col :span="8">
                      <div>
                        {{ item.Name }}
                        <span v-if="item.Alias">({{ item.Alias }})</span>
                        <el-tag class="marlt_5" v-if="item.IsLargess" size="mini" type="danger">赠</el-tag>
                        <span class="marlt_5 color_primary cursor_pointer">
                          <el-button icon="el-icon-edit-outline" type="text" style="padding: unset" @click="editProjectRemarkClick(item)"></el-button>
                        </span>
                      </div>
                      <div class="color_red martp_5">
                        <span>¥ {{ item.Price | toFixed | NumFormat }}</span>
                      </div>
                    </el-col>
                    <el-col :span="8">
                      <el-input-number
                        v-model="item.Quantity"
                        :min="1"
                        :max="item.ValidBalance - getCurrentProjectQuantity(item.ID) + item.Quantity"
                        size="mini"
                        style="width: 100px"
                        @change="projectItemQuantityChangeClick(item)"
                      >
                      </el-input-number>
                    </el-col>
                    <el-col :span="5">
                      <div>
                        <span class="marrt_15">¥ {{ item.TotalAmount | toFixed | NumFormat }}</span>
                      </div>
                      <div>
                        <span class="color_gray font_12" v-if="parseFloat(item.TotalAmount) - parseFloat(item.Price) * parseFloat(item.Quantity) != 0">
                          卡优惠：
                          <span class="color_green" v-if="parseFloat(item.TotalAmount) - parseFloat(item.Price) * parseFloat(item.Quantity) > 0">
                            +{{ Math.abs(parseFloat(item.TotalAmount) - parseFloat(item.Price) * parseFloat(item.Quantity)).toFixed(2) | NumFormat }}
                          </span>
                          <span class="color_red" v-else-if="parseFloat(item.TotalAmount) - parseFloat(item.Price) * parseFloat(item.Quantity) < 0">
                            -{{ Math.abs(parseFloat(item.TotalAmount) - parseFloat(item.Price) * parseFloat(item.Quantity)).toFixed(2) | NumFormat }}
                          </span>
                        </span>
                      </div>
                    </el-col>
                    <el-col :span="3" class="text_right">
                      <el-button
                        v-if="TreatPermission.isTreatConsumable"
                        type="success"
                        icon="el-icon-plus"
                        circle
                        size="mini"
                        @click="addProjecConsumable(`project_${index}`)"
                      ></el-button>
                      <el-button type="danger" icon="el-icon-delete" circle size="mini" @click="removeProjectSelectItemClick(index)"></el-button>
                    </el-col>
                  </el-col>
                  <el-col v-if="item.Remark" :span="24" class="pad_10 border_bottom font_12 color_333">备注信息: {{ item.Remark }} </el-col>
                  <el-col v-if="item.HandleTypeList.length > 0" :span="24" class="pad_10 padbm_0 border_bottom">
                    <el-row
                      class="cursor_pointer"
                      @click.native="employeeHandleClick(0, item, index, handleIndex)"
                      v-for="(handleItem, handleIndex) in item.HandleTypeList"
                      :key="handleIndex"
                    >
                      <el-col :span="4">
                        <el-form :inline="true" size="mini" label-position="left">
                          <el-form-item style="margin-bottom: 10px" :label="`${handleItem.Name}：`"></el-form-item>
                        </el-form>
                      </el-col>
                      <el-col :span="20">
                        <el-form :inline="true" size="mini">
                          <el-form-item
                            style="margin-bottom: 10px"
                            v-for="(empItem, empIndex) in handleItem.Employee"
                            :key="empIndex"
                            :label="`${empItem.EmployeeName} `"
                          >
                            <el-input
                              class="employee_num custom-input-number"
                              v-model="empItem.Discount"
                              size="mini"
                              :min="0"
                              :max="100"
                              type="number"
                              v-on:click.native.stop
                              v-input-fixed
                              @input="handlerPercentChange(handleItem.Employee, empItem)"
                            >
                              <template slot="append">%</template>
                            </el-input>
                            <i
                              class="el-icon-error marlt_5 font_16"
                              style="color: #909399; vertical-align: middle"
                              v-on:click.stop="removeHandleClick(handleItem, empIndex)"
                            ></i>
                          </el-form-item>
                        </el-form>
                      </el-col>
                    </el-row>
                  </el-col>
                  <treat-consumable
                    :ref="`project_${index}`"
                    v-if="TreatPermission.isTreatConsumable"
                    @remove="(event) => removeTreatConsumableClick(event, item, index, 'Project')"
                    @removeAll="(event) => removeAllTreatConsumableClick(event, item, index, 'Project')"
                    @add="(e) => addTreatConsumableClick(e, item, index, 'Project')"
                    :consumableEntityList="consumableEntityList"
                    :projectID="item.ProjectID"
                    :consumableList="item.Consumable"
                  ></treat-consumable>
                </el-row>
              </div>
              <!--储值卡-->
              <div v-for="(item, index) in currentSelectSavingCardList" :key="index + 'ss'">
                <el-row>
                  <el-card shadow="never" style="border: 0px">
                    <div slot="header" class="font_13 cursor_pointer" @click="consumeSavingCardClick(item)">
                      <span>
                        <span>储值卡：</span>
                        <span>{{ item.Name }}</span>
                        <span v-if="item.Alias">({{ item.Alias }})</span>
                      </span>
                      <span class="color_gray" style="float: right">
                        <span>
                          <span>剩余金额：</span>
                          <span>¥ {{ parseFloat(item.ValidBalance - getCurrentSavingCardConsumeAmount(item, false)).toFixed(2) | NumFormat }}</span>
                        </span>
                        <span class="marlt_5">
                          <span>(卡余额：</span>
                          <span>¥ {{ parseFloat(item.ValidBalance).toFixed(2) | NumFormat }}</span>
                          <span class="marlt_10">卡抵扣：</span>
                          <span>-¥ {{ parseFloat(getCurrentSavingCardConsumeAmount(item, false)).toFixed(2) | NumFormat }})</span>
                        </span>
                      </span>
                    </div>
                    <el-row v-for="(project, pIndex) in item.Projects" :key="pIndex + 'sav_p_s'">
                      <el-col class="row_header border_bottom" :span="24">
                        <el-col :span="8">项目</el-col>
                        <el-col :span="8">数量</el-col>
                        <el-col :span="5">金额</el-col>
                        <el-col :span="3"></el-col>
                      </el-col>
                      <el-col :span="24" class="pad_10 border_bottom">
                        <el-col :span="24">
                          <el-col :span="8">
                            <div>
                              {{ project.Name }}
                              <span v-if="project.Alias">({{ project.Alias }})</span>
                              <span class="marlt_5 color_primary cursor_pointer">
                                <el-button icon="el-icon-edit-outline" type="text" style="padding: unset" @click="editProjectRemarkClick(project)"></el-button>
                              </span>
                            </div>
                          </el-col>
                          <el-col :span="8">
                            <el-input-number
                              v-model="project.Quantity"
                              :min="1"
                              size="mini"
                              style="width: 100px"
                              @change="(currentValue, oldValue) => changeSavingCardConsumeProject(currentValue, oldValue, item, project)"
                            ></el-input-number>
                          </el-col>
                          <el-col :span="5">
                            <span class="marrt_15">{{ parseFloat(project.TotalAmount).toFixed(2) | NumFormat }}</span>
                            <el-popover v-model="project.PopoveVisible">
                              <el-row v-if="project.MemberDiscountData">
                                <el-radio-group v-model="project.isMemberDiscount" @input="changeSavingCardIsMemberDiscount(project, item)">
                                  <el-radio :label="true">使用会员优惠</el-radio>
                                  <el-radio :label="false">不使用会员优惠</el-radio>
                                </el-radio-group>
                              </el-row>
                              <el-row v-if="!project.isMemberDiscount">
                                <el-col :span="11" class="pad_10_0">
                                  <el-input
                                    size="small"
                                    type="number"
                                    v-model="project.PopoveAmount"
                                    placeholder="请输入金额"
                                    v-input-fixed
                                    @input="savingCardPopInputChange(project, 0)"
                                  >
                                    <template slot="prepend">¥</template>
                                  </el-input>
                                </el-col>
                                <el-col :span="2" class="text_center pad_10_0 martp_10">
                                  <i class="el-icon-sort"></i>
                                </el-col>
                                <el-col :span="11" class="pad_10_0">
                                  <el-input
                                    size="small"
                                    placeholder="请输入折扣"
                                    v-model="project.PerformanceRatio"
                                    @input="savingCardPopInputChange(project, 1)"
                                  >
                                    <template slot="append">%</template>
                                  </el-input>
                                </el-col>
                              </el-row>
                              <el-row class="text_right martp_15">
                                <el-button size="small" @click="project.PopoveVisible = false">取消</el-button>
                                <el-button size="small" type="primary" @click="savingCardPopoverClickConfirm(project, item)">确定</el-button>
                              </el-row>

                              <el-button
                                v-show="TreatPermission.ModifyPrices_TreatSavingCard || TreatPermission.isTreatBillingModifyPrices_TreatSavingCard"
                                slot="reference"
                                type="primary"
                                icon="el-icon-edit"
                                circle
                                size="mini"
                                @click="showPopover(project)"
                              >
                              </el-button>
                            </el-popover>
                          </el-col>
                          <el-col :span="3" class="text_right">
                            <el-button
                              v-if="TreatPermission.isTreatConsumable"
                              type="success"
                              icon="el-icon-plus"
                              circle
                              size="mini"
                              @click="addProjecConsumable(`savingCardProject_${index}_${pIndex}`)"
                            ></el-button>
                            <el-button
                              type="danger"
                              icon="el-icon-delete"
                              circle
                              size="mini"
                              @click="removeSavingCardSelectItemClick(index, pIndex)"
                            ></el-button>
                          </el-col>
                        </el-col>
                        <el-col :span="24">
                          <el-col :span="16" class="color_red">¥ {{ parseFloat(project.Price) | toFixed | NumFormat }}</el-col>
                          <el-col :span="8">
                            <div>
                              <span class="color_gray font_12" v-if="project.PricePreferentialAmount != 0">
                                手动改价：
                                <span class="color_red" v-if="project.PricePreferentialAmount > 0"
                                  >-{{ parseFloat(project.PricePreferentialAmount).toFixed(2) | NumFormat }}</span
                                >
                                <span class="color_green" v-else>+{{ parseFloat(Math.abs(project.PricePreferentialAmount)).toFixed(2) | NumFormat }}</span>
                              </span>
                              <span
                                class="color_gray font_12"
                                :class="project.PricePreferentialAmount == 0 ? '' : 'marlt_15'"
                                v-if="project.CardPreferentialAmount > 0"
                              >
                                卡优惠：
                                <span class="color_red">-{{ parseFloat(project.CardPreferentialAmountTotal) | toFixed | NumFormat }}</span>
                              </span>

                              <span
                                class="color_gray font_12"
                                :class="project.MemberPreferentialAmountTotal == 0 ? '' : 'marlt_15'"
                                v-if="project.MemberPreferentialAmountTotal > 0"
                              >
                                会员优惠：
                                <span class="color_red">-{{ parseFloat(project.MemberPreferentialAmountTotal) | toFixed | NumFormat }}</span>
                              </span>
                            </div>
                          </el-col>
                        </el-col>
                      </el-col>
                      <el-col v-if="project.Remark" :span="24" class="pad_10 border_bottom font_12 color_333">备注信息: {{ project.Remark }} </el-col>
                      <el-col v-if="project.HandleTypeList.length > 0" :span="24" class="pad_10 padbm_0 border_bottom">
                        <el-row
                          class="cursor_pointer"
                          @click.native="employeeHandleClick(2, project, pIndex)"
                          v-for="(handleItem, handleIndex) in project.HandleTypeList"
                          :key="handleIndex"
                        >
                          <el-col :span="4">
                            <el-form :inline="true" size="mini" label-position="left">
                              <el-form-item style="margin-bottom: 10px" :label="`${handleItem.Name}：`"></el-form-item>
                            </el-form>
                          </el-col>
                          <el-col :span="20">
                            <el-form :inline="true" size="mini">
                              <el-form-item
                                style="margin-bottom: 10px"
                                v-for="(empItem, empIndex) in handleItem.Employee"
                                :key="empIndex"
                                :label="`${empItem.EmployeeName} `"
                              >
                                <el-input
                                  class="employee_num custom-input-number"
                                  v-model="empItem.Discount"
                                  size="mini"
                                  :min="0"
                                  :max="100"
                                  type="number"
                                  v-on:click.native.stop
                                  v-input-fixed
                                  @input="handlerPercentChange(handleItem.Employee, empItem)"
                                >
                                  <template slot="append">%</template>
                                </el-input>
                                <i
                                  class="el-icon-error marlt_5 font_16"
                                  style="color: #909399; vertical-align: middle"
                                  v-on:click.stop="removeHandleClick(handleItem, empIndex)"
                                ></i>
                              </el-form-item>
                            </el-form>
                          </el-col>
                        </el-row>
                      </el-col>

                      <treat-consumable
                        :ref="`savingCardProject_${index}_${pIndex}`"
                        v-if="TreatPermission.isTreatConsumable"
                        @remove="(event) => removeTreatConsumableClick(event, project, index, 'SavingCard')"
                        @removeAll="(event) => removeAllTreatConsumableClick(event, project, index, 'SavingCard')"
                        @add="(e) => addTreatConsumableClick(e, project, index, 'SavingCard')"
                        :consumableEntityList="consumableEntityList"
                        :projectID="project.ID"
                        :consumableList="project.Consumable"
                      ></treat-consumable>
                    </el-row>
                  </el-card>
                </el-row>
              </div>
              <!--时效卡-->
              <div v-for="(item, index) in currentSelectTimeCardList" :key="index + 'st'">
                <el-row>
                  <el-card shadow="never" style="border: 0px">
                    <div slot="header" class="font_13 cursor_pointer" @click="consumeTimeCardClick(item)">
                      <span>
                        <span>时效卡：</span>
                        <span>{{ item.Name }}</span>
                        <span v-if="item.Alias">({{ item.Alias }})</span>
                        <el-tag class="marlt_5" v-if="item.IsLargess" size="mini" type="danger">赠</el-tag>
                      </span>
                    </div>
                    <el-row v-for="(Project, ProjectIndex) in item.Projects" :key="ProjectIndex">
                      <el-col class="row_header border_bottom" :span="24">
                        <el-col :span="8">项目</el-col>
                        <el-col :span="8">数量</el-col>
                        <el-col :span="5">金额</el-col>
                        <el-col :span="3"></el-col>
                      </el-col>
                      <el-col :span="24" class="pad_10 border_bottom">
                        <el-col :span="24">
                          <el-col :span="8">
                            <div>
                              {{ Project.Name }}
                              <span v-if="Project.Alias">({{ Project.Alias }})</span>
                              <el-tag class="marlt_5" v-if="item.IsLargess" size="mini" type="danger">赠</el-tag>
                              <span class="marlt_5 color_primary cursor_pointer">
                                <el-button icon="el-icon-edit-outline" type="text" style="padding: unset" @click="editProjectRemarkClick(Project)"></el-button>
                              </span>
                            </div>
                          </el-col>
                          <el-col :span="8">
                            <el-input-number
                              v-model="Project.Quantity"
                              :min="1"
                              :max="getTimeCardMaxQuantity(item) - getTimeCardQuantity(item.ID) + Project.Quantity"
                              size="mini"
                              style="width: 100px"
                              @change="(oldval, newval) => timeItemQuantityChangeClick(oldval, newval, item, Project)"
                            ></el-input-number>
                          </el-col>
                          <el-col :span="5">
                            <div>
                              {{ Project.TotalAmount | toFixed | NumFormat }}
                            </div>
                          </el-col>
                          <el-col :span="3" class="text_right">
                            <el-button
                              v-if="TreatPermission.isTreatConsumable"
                              type="success"
                              icon="el-icon-plus"
                              circle
                              size="mini"
                              @click="addProjecConsumable(`timeCardProject_${index}_${ProjectIndex}`)"
                            ></el-button>
                            <el-button
                              type="danger"
                              icon="el-icon-delete"
                              circle
                              size="mini"
                              @click="timeRemoveSelectItemClick(index, ProjectIndex)"
                            ></el-button>
                          </el-col>
                        </el-col>
                        <el-col :span="24">
                          <el-col :span="16">
                            <div class="color_red font_12">¥ {{ Project.Price | toFixed | NumFormat }}</div>
                          </el-col>
                          <el-col :span="8">
                            <span
                              class="color_gray font_12"
                              v-if="parseFloat(Project.TotalAmount) - parseFloat(Project.Price) * parseFloat(Project.Quantity) != 0"
                            >
                              卡优惠：
                              <span class="color_green" v-if="parseFloat(Project.TotalAmount) - parseFloat(Project.Price) * parseFloat(Project.Quantity) > 0">
                                +{{
                                  Math.abs(parseFloat(Project.TotalAmount) - parseFloat(Project.Price) * parseFloat(Project.Quantity)).toFixed(2) | NumFormat
                                }}
                              </span>
                              <span
                                class="color_red"
                                v-else-if="parseFloat(Project.TotalAmount) - parseFloat(Project.Price) * parseFloat(Project.Quantity) < 0"
                              >
                                -{{
                                  Math.abs(parseFloat(Project.TotalAmount) - parseFloat(Project.Price) * parseFloat(Project.Quantity)).toFixed(2) | NumFormat
                                }}
                              </span>
                            </span>
                          </el-col>
                        </el-col>
                      </el-col>
                      <el-col v-if="Project.Remark" :span="24" class="pad_10 border_bottom font_12 color_333">备注信息: {{ Project.Remark }} </el-col>
                      <el-col v-if="Project.HandleTypeList.length" :span="24" class="pad_10 padbm_0 border_bottom">
                        <el-row
                          class="cursor_pointer"
                          @click.native="employeeHandleClick(4, Project, ProjectIndex)"
                          v-for="(handleItem, handleIndex) in Project.HandleTypeList"
                          :key="handleIndex"
                        >
                          <el-col :span="4">
                            <el-form :inline="true" size="mini" label-position="left">
                              <el-form-item style="margin-bottom: 10px" :label="`${handleItem.Name}：`"></el-form-item>
                            </el-form>
                          </el-col>
                          <el-col :span="20">
                            <el-form :inline="true" size="mini">
                              <el-form-item
                                style="margin-bottom: 10px"
                                v-for="(empItem, empIndex) in handleItem.Employee"
                                :key="empIndex"
                                :label="`${empItem.EmployeeName} `"
                              >
                                <el-input
                                  class="employee_num custom-input-number"
                                  v-model="empItem.Discount"
                                  size="mini"
                                  :min="0"
                                  :max="100"
                                  type="number"
                                  v-on:click.native.stop
                                  v-input-fixed
                                  @input="handlerPercentChange(handleItem.Employee, empItem)"
                                >
                                  <template slot="append">%</template>
                                </el-input>
                                <i
                                  class="el-icon-error marlt_5 font_16"
                                  style="color: #909399; vertical-align: middle"
                                  v-on:click.stop="removeHandleClick(handleItem, empIndex)"
                                ></i>
                              </el-form-item>
                            </el-form>
                          </el-col>
                        </el-row>
                      </el-col>
                      <treat-consumable
                        :ref="`timeCardProject_${index}_${ProjectIndex}`"
                        v-if="TreatPermission.isTreatConsumable"
                        @remove="(event) => removeTreatConsumableClick(event, Project, index, 'TimeCard')"
                        @removeAll="(event) => removeAllTreatConsumableClick(event, Project, index, 'TimeCard')"
                        @add="(e) => addTreatConsumableClick(e, Project, index, 'TimeCard')"
                        :consumableEntityList="consumableEntityList"
                        :projectID="Project.ID"
                        :consumableList="Project.Consumable"
                      ></treat-consumable>
                    </el-row>
                  </el-card>
                </el-row>
              </div>
              <!--通用次卡-->
              <div v-for="(item, index) in currentSelectGeneralCardList" :key="index + 'sg'">
                <el-row>
                  <el-card shadow="never" style="border: 0px">
                    <div slot="header" class="font_13" @click="consumeGeneralCardClick(item.GeneralCardItem)">
                      <span>
                        <span>通用次卡：</span>
                        <span>{{ item.Name }}</span>
                        <span v-if="item.Alias">({{ item.Alias }})</span>
                        <el-tag class="marlt_5" v-if="item.IsLargess" size="mini" type="danger">赠</el-tag>
                      </span>
                      <span class="color_gray" style="float: right">
                        <span>
                          <span>剩余次数：</span>
                          <span> {{ item.ValidBalance - getGeneralCardQuantity(item.ID) }}</span>
                        </span>
                        <span class="marlt_5">
                          <span>(有效次数：</span>
                          <span>{{ item.ValidBalance }}</span>
                          <span class="marlt_10">消耗次数：</span>
                          <span>{{ getGeneralCardQuantity(item.ID) }})</span>
                        </span>
                      </span>
                    </div>
                    <el-row v-for="(Project, ProjectIndex) in item.Projects" :key="ProjectIndex">
                      <el-col class="row_header border_bottom" :span="24">
                        <el-col :span="8">项目</el-col>
                        <el-col :span="8">数量</el-col>
                        <el-col :span="5">金额</el-col>
                        <el-col :span="3"></el-col>
                      </el-col>
                      <el-col :span="24" class="pad_10 border_bottom">
                        <el-col :span="24">
                          <el-col :span="8">
                            <div>
                              {{ Project.Name }}
                              <span v-if="Project.Alias">({{ Project.Alias }})</span>
                              <el-tag class="marlt_5" v-if="item.IsLargess" size="mini" type="danger">赠</el-tag>
                              <span class="marlt_5 color_primary cursor_pointer">
                                <el-button icon="el-icon-edit-outline" type="text" style="padding: unset" @click="editProjectRemarkClick(Project)"></el-button>
                              </span>
                            </div>
                          </el-col>
                          <el-col :span="8">
                            <el-input-number
                              v-model="Project.Quantity"
                              :min="1"
                              :max="getGeneralCardProjectMaxQuantity(item, Project)"
                              size="mini"
                              style="width: 100px"
                              @change="(oldval, newval) => generalItemQuantityChangeClick(oldval, newval, item, Project)"
                            ></el-input-number>
                          </el-col>
                          <el-col :span="5">
                            <div>
                              {{ Number(Project.TotalAmount).toFixed(2) }}
                            </div>
                          </el-col>
                          <el-col :span="3" class="text_right">
                            <el-button
                              v-if="TreatPermission.isTreatConsumable"
                              type="success"
                              icon="el-icon-plus"
                              circle
                              size="mini"
                              @click="addProjecConsumable(`generalCardProject_${index}_${ProjectIndex}`)"
                            ></el-button>
                            <el-button
                              type="danger"
                              icon="el-icon-delete"
                              circle
                              size="mini"
                              @click="removeSelectGeneralItemClick(index, ProjectIndex)"
                            ></el-button>
                          </el-col>
                        </el-col>
                        <el-col :span="24">
                          <el-col :span="16">
                            <div>
                              <span class="color_red">¥ {{ Project.Price | toFixed | NumFormat }}</span>
                              <span class="color_gray marlt_5 font_12">
                                (本次耗卡次数：{{ Project.ConsumeAmount * Project.Quantity }}
                                <span class="marlt_10">单次耗卡次数: {{ Project.ConsumeAmount }} 次</span>)
                              </span>
                            </div>
                          </el-col>
                          <el-col :span="8">
                            <span
                              class="color_gray font_12"
                              v-if="parseFloat(Project.TotalAmount) - parseFloat(Project.Price) * parseFloat(Project.Quantity) != 0"
                            >
                              卡优惠：
                              <span class="color_green" v-if="parseFloat(Project.TotalAmount) - parseFloat(Project.Price) * parseFloat(Project.Quantity) > 0">
                                +{{
                                  Math.abs(parseFloat(Project.TotalAmount) - parseFloat(Project.Price) * parseFloat(Project.Quantity)).toFixed(2) | NumFormat
                                }}
                              </span>
                              <span
                                class="color_red"
                                v-else-if="parseFloat(Project.TotalAmount) - parseFloat(Project.Price) * parseFloat(Project.Quantity) < 0"
                              >
                                -{{
                                  Math.abs(parseFloat(Project.TotalAmount) - parseFloat(Project.Price) * parseFloat(Project.Quantity)).toFixed(2) | NumFormat
                                }}
                              </span>
                            </span>
                          </el-col>
                        </el-col>
                      </el-col>
                      <el-col v-if="Project.Remark" :span="24" class="pad_10 border_bottom font_12 color_333">备注信息: {{ Project.Remark }} </el-col>
                      <el-col v-if="Project.HandleTypeList.length > 0" :span="24" class="pad_10 padbm_0 border_bottom">
                        <el-row
                          class="cursor_pointer"
                          @click.native="employeeHandleClick(5, Project, ProjectIndex)"
                          v-for="(handleItem, handleIndex) in Project.HandleTypeList"
                          :key="handleIndex"
                        >
                          <el-col :span="4">
                            <el-form :inline="true" size="mini" label-position="left">
                              <el-form-item style="margin-bottom: 10px" :label="`${handleItem.Name}：`"></el-form-item>
                            </el-form>
                          </el-col>
                          <el-col :span="20">
                            <el-form :inline="true" size="mini">
                              <el-form-item
                                style="margin-bottom: 10px"
                                v-for="(empItem, empIndex) in handleItem.Employee"
                                :key="empIndex"
                                :label="`${empItem.EmployeeName} `"
                              >
                                <el-input
                                  class="employee_num custom-input-number"
                                  v-model="empItem.Discount"
                                  size="mini"
                                  :min="0"
                                  :max="100"
                                  type="number"
                                  v-on:click.native.stop
                                  v-input-fixed
                                  @input="handlerPercentChange(handleItem.Employee, empItem)"
                                >
                                  <template slot="append">%</template>
                                </el-input>
                                <i
                                  class="el-icon-error marlt_5 font_16"
                                  style="color: #909399; vertical-align: middle"
                                  v-on:click.stop="removeHandleClick(handleItem, empIndex)"
                                ></i>
                              </el-form-item>
                            </el-form>
                          </el-col>
                        </el-row>
                      </el-col>

                      <treat-consumable
                        :ref="`generalCardProject_${index}_${ProjectIndex}`"
                        v-if="TreatPermission.isTreatConsumable"
                        @remove="(event) => removeTreatConsumableClick(event, Project, index, 'GeneralCard')"
                        @removeAll="(event) => removeAllTreatConsumableClick(event, Project, index, 'GeneralCard')"
                        @add="(e) => addTreatConsumableClick(e, Project, index, 'GeneralCard')"
                        :consumableEntityList="consumableEntityList"
                        :projectID="Project.ID"
                        :consumableList="Project.Consumable"
                      ></treat-consumable>
                    </el-row>
                  </el-card>
                </el-row>
              </div>
              <!--产品-->
              <div v-for="(item, index) in currentSelectProductList" :key="index + 'spd'">
                <el-row class="row_header border_bottom">
                  <el-col :span="24">
                    <el-col :span="8">产品</el-col>
                    <el-col :span="8">数量</el-col>
                    <el-col :span="5">金额</el-col>
                    <el-col :span="3"></el-col>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="24" class="pad_10 border_bottom">
                    <el-col :span="8">
                      <div>
                        {{ item.Name }}
                        <span v-if="item.Alias">({{ item.Alias }})</span>
                        <el-tag class="marlt_5" v-if="item.IsLargess" size="mini" type="danger">赠</el-tag>
                        <span class="marlt_5 color_primary cursor_pointer">
                          <el-button icon="el-icon-edit-outline" type="text" style="padding: unset" @click="editProjectRemarkClick(item)"></el-button>
                        </span>
                      </div>
                      <div class="color_red martp_5">
                        <span>¥ {{ item.Price | toFixed | NumFormat }}</span>
                      </div>
                    </el-col>
                    <el-col :span="8">
                      <el-input-number
                        v-model="item.Quantity"
                        :min="1"
                        :max="item.ValidBalance - getCurrentProductQuantity(item.ID) + item.Quantity"
                        size="mini"
                        style="width: 100px"
                        @change="productItemQuantityChangeClick(item)"
                      >
                      </el-input-number>
                    </el-col>
                    <el-col :span="5">
                      <div>
                        <span class="marrt_15">¥ {{ parseFloat(item.TotalAmount).toFixed(2) | NumFormat }}</span>
                      </div>
                      <div class="martp_5">
                        <span class="color_gray font_12" v-if="parseFloat(item.TotalAmount) - parseFloat(item.Price) * parseFloat(item.Quantity) != 0">
                          卡优惠：
                          <span class="color_green" v-if="parseFloat(item.TotalAmount) - parseFloat(item.Price) * parseFloat(item.Quantity) > 0">
                            +{{ Math.abs(parseFloat(item.TotalAmount) - parseFloat(item.Price) * parseFloat(item.Quantity)).toFixed(2) | NumFormat }}
                          </span>
                          <span class="color_red" v-else-if="parseFloat(item.TotalAmount) - parseFloat(item.Price) * parseFloat(item.Quantity) < 0">
                            -{{ Math.abs(parseFloat(item.TotalAmount) - parseFloat(item.Price) * parseFloat(item.Quantity)).toFixed(2) | NumFormat }}
                          </span>
                        </span>
                      </div>
                    </el-col>
                    <el-col :span="3" class="text_right">
                      <el-button type="danger" icon="el-icon-delete" circle size="mini" @click="removeSelectProductItemClick(index)"></el-button>
                    </el-col>
                  </el-col>
                  <el-col v-if="item.Remark" :span="24" class="pad_10 border_bottom font_12 color_333">备注信息: {{ item.Remark }} </el-col>
                  <el-col v-if="item.HandleTypeList.length > 0" :span="24" class="pad_10 padbm_0 border_bottom">
                    <el-row
                      class="cursor_pointer"
                      @click.native="employeeHandleClick(1, item, index)"
                      v-for="(handleItem, handleIndex) in item.HandleTypeList"
                      :key="handleIndex"
                    >
                      <el-col :span="4">
                        <el-form :inline="true" size="mini" label-position="left">
                          <el-form-item style="margin-bottom: 10px" :label="`${handleItem.Name}：`"></el-form-item>
                        </el-form>
                      </el-col>
                      <el-col :span="20">
                        <el-form :inline="true" size="mini">
                          <el-form-item
                            style="margin-bottom: 10px"
                            v-for="(empItem, empIndex) in handleItem.Employee"
                            :key="empIndex"
                            :label="`${empItem.EmployeeName} `"
                          >
                            <el-input
                              class="employee_num custom-input-number"
                              v-model="empItem.Discount"
                              size="mini"
                              :min="0"
                              :max="100"
                              type="number"
                              v-on:click.native.stop
                              v-input-fixed
                              @input="handlerPercentChange(handleItem.Employee, empItem)"
                            >
                              <template slot="append">%</template>
                            </el-input>
                            <i
                              class="el-icon-error marlt_5 font_16"
                              style="color: #909399; vertical-align: middle"
                              v-on:click.stop="removeHandleClick(handleItem, empIndex)"
                            ></i>
                          </el-form-item>
                        </el-form>
                      </el-col>
                    </el-row>
                  </el-col>
                </el-row>
              </div>
              <!--套餐卡-->
              <div v-for="(item, index) in currentselectPackageCardList" :key="index">
                <el-row class="row_header border_bottom">
                  <el-col :span="24">
                    <el-col :span="24">
                      <span>套餐卡-{{ item.Name }}</span>
                      <span v-if="item.Alias">({{ item.Alias }})</span>
                      <el-tag class="marlt_5" v-if="item.IsLargess" size="mini" type="danger">赠</el-tag>
                    </el-col>
                    <!-- <el-col :span="8">数量</el-col>
                    <el-col :span="6">金额</el-col>
                    <el-col :span="2"></el-col> -->
                  </el-col>
                </el-row>
                <!-- 套餐卡项目 -->
                <el-row v-for="(Project, ProjectIndex) in item.packageProjectList" :key="ProjectIndex + 'sppr'">
                  <el-col class="row_header border_bottom" :span="24">
                    <el-col :span="8">项目</el-col>
                    <el-col :span="8">数量</el-col>
                    <el-col :span="5">金额</el-col>
                    <el-col :span="3"></el-col>
                  </el-col>
                  <el-col :span="24" class="pad_10 border_bottom">
                    <el-col :span="8">
                      <div>
                        <el-tag size="mini">项目</el-tag>
                        <span>{{ Project.Name }}</span>
                        <span v-if="Project.Alias">({{ Project.Alias }})</span>
                        <el-tag class="marlt_5" v-if="Project.IsLargess" size="mini" type="danger">赠</el-tag>
                        <span class="marlt_5 color_primary cursor_pointer">
                          <el-button icon="el-icon-edit-outline" type="text" style="padding: unset" @click="editProjectRemarkClick(Project)"></el-button>
                        </span>
                      </div>
                      <div class="color_red martp_5">
                        <span>¥ {{ Project.Price | toFixed | NumFormat }}</span>
                      </div>
                    </el-col>
                    <el-col :span="8">
                      <el-input-number
                        v-model="Project.Quantity"
                        :min="1"
                        :max="Project.ValidBalance - getPackageProjectConsumeQuantity(item, Project.ID) + Project.Quantity"
                        size="mini"
                        style="width: 100px"
                        @change="(oldval, newval) => packageProjectQuantityChangeClick(Project)"
                      ></el-input-number>
                    </el-col>
                    <el-col :span="5">
                      <div>
                        <span class="marrt_15">¥ {{ Project.TotalAmount | toFixed | NumFormat }}</span>
                      </div>
                      <div>
                        <span class="color_gray font_12" v-if="parseFloat(Project.TotalAmount) - parseFloat(Project.Price) * parseFloat(Project.Quantity) != 0">
                          卡优惠：
                          <span class="color_green" v-if="parseFloat(Project.TotalAmount) - parseFloat(Project.Price) * parseFloat(Project.Quantity) > 0">
                            +{{ Math.abs(parseFloat(Project.TotalAmount) - parseFloat(Project.Price) * parseFloat(Project.Quantity)).toFixed(2) | NumFormat }}
                          </span>
                          <span class="color_red" v-else-if="parseFloat(Project.TotalAmount) - parseFloat(Project.Price) * parseFloat(Project.Quantity) < 0">
                            -{{ Math.abs(parseFloat(Project.TotalAmount) - parseFloat(Project.Price) * parseFloat(Project.Quantity)).toFixed(2) | NumFormat }}
                          </span>
                        </span>
                      </div>
                    </el-col>
                    <el-col :span="3" class="text_right">
                      <el-button
                        v-if="TreatPermission.isTreatConsumable"
                        type="success"
                        icon="el-icon-plus"
                        circle
                        size="mini"
                        @click="addProjecConsumable(`packageProject_${index}_${ProjectIndex}`)"
                      ></el-button>
                      <el-button
                        type="danger"
                        icon="el-icon-delete"
                        circle
                        size="mini"
                        @click="removePackageProjectItemClick(item, index, ProjectIndex)"
                      ></el-button>
                    </el-col>
                  </el-col>
                  <el-col v-if="Project.Remark" :span="24" class="pad_10 border_bottom font_12 color_333">备注信息: {{ Project.Remark }} </el-col>
                  <el-col v-if="Project.HandleTypeList.length > 0" :span="24" class="pad_10 padbm_0 border_bottom">
                    <el-row
                      class="cursor_pointer"
                      @click.native="employeeHandleClick(0, Project)"
                      v-for="(handleItem, handleIndex) in Project.HandleTypeList"
                      :key="handleIndex"
                    >
                      <el-col :span="4">
                        <el-form :inline="true" size="mini" label-position="left">
                          <el-form-item style="margin-bottom: 10px" :label="`${handleItem.Name}：`"></el-form-item>
                        </el-form>
                      </el-col>
                      <el-col :span="20">
                        <el-form :inline="true" size="mini">
                          <el-form-item
                            style="margin-bottom: 10px"
                            v-for="(empItem, empIndex) in handleItem.Employee"
                            :key="empIndex"
                            :label="`${empItem.EmployeeName} `"
                          >
                            <el-input
                              class="employee_num custom-input-number"
                              v-model="empItem.Discount"
                              size="mini"
                              :min="0"
                              :max="100"
                              type="number"
                              v-on:click.native.stop
                              v-input-fixed
                              @input="handlerPercentChange(handleItem.Employee, empItem)"
                            >
                              <template slot="append">%</template>
                            </el-input>
                            <i
                              class="el-icon-error marlt_5 font_16"
                              style="color: #909399; vertical-align: middle"
                              v-on:click.stop="removeHandleClick(handleItem, empIndex)"
                            ></i>
                          </el-form-item>
                        </el-form>
                      </el-col>
                    </el-row>
                  </el-col>
                  <treat-consumable
                    :ref="`packageProject_${index}_${ProjectIndex}`"
                    v-if="TreatPermission.isTreatConsumable"
                    @remove="(event) => removeTreatConsumableClick(event, Project, index, 'PackageProject')"
                    @removeAll="(event) => removeAllTreatConsumableClick(event, Project, index, 'PackageProject')"
                    @add="(e) => addTreatConsumableClick(e, Project, index, 'PackageProject')"
                    :consumableEntityList="consumableEntityList"
                    :projectID="Project.ProjectID"
                    :consumableList="Project.Consumable"
                  ></treat-consumable>
                </el-row>
                <!-- 套餐卡储值卡 -->
                <el-row v-for="(SavingCard, SavingCardIndex) in item.packageSavingCardList" :key="SavingCardIndex + 'sps'">
                  <el-card shadow="never" style="border: 0px">
                    <div slot="header" class="font_13 cursor_pointer" @click="consumePackageSavingCardSelectClick(item, SavingCard)">
                      <span>
                        <el-tag size="mini">储值卡</el-tag>
                        <span> {{ SavingCard.Name }}</span>
                        <span v-if="SavingCard.Alias">({{ SavingCard.Alias }})</span>
                      </span>
                      <span class="color_gray" style="float: right">
                        <span>
                          <span>剩余金额：</span>
                          <span
                            >¥ {{ parseFloat(SavingCard.ValidBalance - getPackageSavingCardConsumeAmount(item, SavingCard.ID)).toFixed(2) | NumFormat }}</span
                          >
                        </span>
                        <span class="marlt_5">
                          <span>(卡余额：</span>
                          <span>¥ {{ parseFloat(SavingCard.ValidBalance).toFixed(2) | NumFormat }}</span>
                          <span class="marlt_10">卡抵扣：</span>
                          <span>-¥ {{ parseFloat(getPackageSavingCardConsumeAmount(item, SavingCard.ID)).toFixed(2) | NumFormat }})</span>
                        </span>
                      </span>
                    </div>
                    <el-row v-for="(Project, ProjectIndex) in SavingCard.Projects" :key="ProjectIndex">
                      <el-col class="row_header border_bottom" :span="24">
                        <el-col :span="8">项目</el-col>
                        <el-col :span="8">数量</el-col>
                        <el-col :span="5">金额</el-col>
                        <el-col :span="3"></el-col>
                      </el-col>
                      <el-col :span="24" class="pad_10 border_bottom">
                        <el-col :span="24">
                          <el-col :span="8">
                            <div>
                              {{ Project.Name }}
                              <span v-if="Project.Alias">({{ Project.Alias }})</span>
                              <span class="marlt_5 color_primary cursor_pointer">
                                <el-button icon="el-icon-edit-outline" type="text" style="padding: unset" @click="editProjectRemarkClick(Project)"></el-button>
                              </span>
                            </div>
                          </el-col>
                          <el-col :span="8">
                            <el-input-number
                              v-model="Project.Quantity"
                              :min="1"
                              size="mini"
                              style="width: 100px"
                              @change="(oldval, newval) => packageSavingCartItemQuantityChangeClick(oldval, newval, item, SavingCard, Project)"
                            ></el-input-number>
                          </el-col>
                          <el-col :span="5">
                            <span class="marrt_15">{{ parseFloat(Project.TotalAmount).toFixed(2) | NumFormat }}</span>
                            <el-popover v-model="Project.PopoveVisible">
                              <el-row v-if="Project.MemberDiscountData">
                                <el-radio-group v-model="Project.isMemberDiscount" @input="changeSavingCardIsMemberDiscount(Project, SavingCard)">
                                  <el-radio :label="true">使用会员优惠</el-radio>
                                  <el-radio :label="false">不使用会员优惠</el-radio>
                                </el-radio-group>
                              </el-row>
                              <el-row v-if="!Project.isMemberDiscount">
                                <el-col :span="11" class="pad_10_0">
                                  <el-input
                                    size="small"
                                    type="number"
                                    v-input-fixed
                                    v-model="Project.PopoveAmount"
                                    placeholder="请输入金额"
                                    @input="savingCardPopInputChange(Project, 0)"
                                  >
                                    <template slot="prepend">¥</template>
                                  </el-input>
                                </el-col>
                                <el-col :span="2" class="text_center pad_10_0 martp_10">
                                  <i class="el-icon-sort"></i>
                                </el-col>
                                <el-col :span="11" class="pad_10_0">
                                  <el-input
                                    size="small"
                                    placeholder="请输入折扣"
                                    v-model="Project.PerformanceRatio"
                                    @input="savingCardPopInputChange(Project, 1)"
                                  >
                                    <template slot="append">%</template>
                                  </el-input>
                                </el-col>
                              </el-row>
                              <el-row class="text_right martp_10">
                                <el-button size="small" @click="Project.PopoveVisible = false">取消</el-button>
                                <el-button size="small" type="primary" @click="savingCardPopoverClickConfirm(Project, SavingCard)">确定</el-button>
                              </el-row>

                              <el-button
                                v-show="TreatPermission.ModifyPrices_TreatSavingCard || TreatPermission.isTreatBillingModifyPrices_TreatSavingCard"
                                slot="reference"
                                type="primary"
                                icon="el-icon-edit"
                                circle
                                size="mini"
                                @click="showPopover(Project)"
                              >
                              </el-button>
                            </el-popover>
                          </el-col>
                          <el-col :span="3" class="text_right">
                            <el-button
                              v-if="TreatPermission.isTreatConsumable"
                              type="success"
                              icon="el-icon-plus"
                              circle
                              size="mini"
                              @click="addProjecConsumable(`packageSavingCardProject_${index}_${SavingCardIndex}_${ProjectIndex}`)"
                            ></el-button>
                            <el-button
                              type="danger"
                              icon="el-icon-delete"
                              circle
                              size="mini"
                              @click="removePackageSavingCardItemClick(item, SavingCard, Project, index, SavingCardIndex, ProjectIndex)"
                            ></el-button>
                          </el-col>
                        </el-col>
                        <el-col :span="24">
                          <el-col :span="16" class="color_red">¥ {{ parseFloat(Project.Price).toFixed(2) | NumFormat }}</el-col>
                          <el-col :span="8">
                            <div>
                              <span class="color_gray font_12" v-if="Project.PricePreferentialAmount != 0">
                                手动改价：
                                <span class="color_red" v-if="Project.PricePreferentialAmount > 0"
                                  >-{{ parseFloat(Project.PricePreferentialAmount).toFixed(2) | NumFormat }}</span
                                >
                                <span class="color_green" v-else>+{{ parseFloat(Math.abs(Project.PricePreferentialAmount)).toFixed(2) | NumFormat }}</span>
                              </span>
                              <span
                                class="color_gray font_12"
                                :class="Project.PricePreferentialAmount == 0 ? '' : 'marlt_15'"
                                v-if="Project.CardPreferentialAmount > 0"
                              >
                                卡优惠：
                                <span class="color_red">-{{ parseFloat(Project.CardPreferentialAmountTotal) | toFixed | NumFormat }}</span>
                              </span>
                              <span
                                class="color_gray font_12"
                                :class="Project.CardPreferentialAmountTotal == 0 ? '' : 'marlt_15'"
                                v-if="Project.MemberPreferentialAmountTotal > 0"
                              >
                                会员优惠：
                                <span class="color_red">-{{ parseFloat(Project.MemberPreferentialAmountTotal) | toFixed | NumFormat }}</span>
                              </span>
                            </div>
                          </el-col>
                        </el-col>
                      </el-col>
                      <el-col v-if="Project.Remark" :span="24" class="pad_10 border_bottom font_12 color_333">备注信息: {{ Project.Remark }} </el-col>
                      <el-col v-if="Project.HandleTypeList.length > 0" :span="24" class="pad_10 padbm_0 border_bottom">
                        <el-row
                          class="cursor_pointer"
                          @click.native="employeeHandleClick(2, Project)"
                          v-for="(handleItem, handleIndex) in Project.HandleTypeList"
                          :key="handleIndex"
                        >
                          <el-col :span="4">
                            <el-form :inline="true" size="mini" label-position="left">
                              <el-form-item style="margin-bottom: 10px" :label="`${handleItem.Name}：`"></el-form-item>
                            </el-form>
                          </el-col>
                          <el-col :span="20">
                            <el-form :inline="true" size="mini">
                              <el-form-item
                                style="margin-bottom: 10px"
                                v-for="(empItem, empIndex) in handleItem.Employee"
                                :key="empIndex"
                                :label="`${empItem.EmployeeName} `"
                              >
                                <el-input
                                  class="employee_num custom-input-number"
                                  v-model="empItem.Discount"
                                  size="mini"
                                  :min="0"
                                  :max="100"
                                  type="number"
                                  v-on:click.native.stop
                                  v-input-fixed
                                  @input="handlerPercentChange(handleItem.Employee, empItem)"
                                >
                                  <template slot="append">%</template>
                                </el-input>
                                <i
                                  class="el-icon-error marlt_5 font_16"
                                  style="color: #909399; vertical-align: middle"
                                  v-on:click.stop="removeHandleClick(handleItem, empIndex)"
                                ></i>
                              </el-form-item>
                            </el-form>
                          </el-col>
                        </el-row>
                      </el-col>
                      <treat-consumable
                        :ref="`packageSavingCardProject_${index}_${SavingCardIndex}_${ProjectIndex}`"
                        v-if="TreatPermission.isTreatConsumable"
                        @remove="(event) => removeTreatConsumableClick(event, Project, index, 'PackageSavingCard')"
                        @removeAll="(event) => removeAllTreatConsumableClick(event, Project, index, 'PackageSavingCard')"
                        @add="(e) => addTreatConsumableClick(e, Project, index, 'PackageSavingCard')"
                        :consumableEntityList="consumableEntityList"
                        :projectID="Project.ID"
                        :consumableList="Project.Consumable"
                      ></treat-consumable>
                    </el-row>
                  </el-card>
                </el-row>
                <!-- 套餐卡时效卡 -->
                <el-row v-for="(timeCard, timeCardIndex) in item.packageTimeCardList" :key="timeCardIndex + 'spt'">
                  <el-card shadow="never" style="border: 0px">
                    <div slot="header" class="font_13 cursor_pointer" @click="consumePackageTimeCardSelectClick(item, timeCard)">
                      <span>
                        <el-tag size="mini">时效卡</el-tag>
                        <span>{{ timeCard.Name }}</span>
                        <span v-if="timeCard.Alias">({{ timeCard.Alias }})</span>
                        <el-tag class="marlt_5" v-if="timeCard.IsLargess" size="mini" type="danger">赠</el-tag>
                      </span>
                    </div>
                    <el-row v-for="(Project, ProjectIndex) in timeCard.Projects" :key="ProjectIndex">
                      <el-col class="row_header border_bottom" :span="24">
                        <el-col :span="8">项目</el-col>
                        <el-col :span="8">数量</el-col>
                        <el-col :span="5">金额</el-col>
                        <el-col :span="3"></el-col>
                      </el-col>
                      <el-col :span="24" class="pad_10 border_bottom">
                        <el-col :span="24">
                          <el-col :span="8">
                            <div>
                              {{ Project.Name }}
                              <span v-if="Project.Alias">({{ Project.Alias }})</span>
                              <el-tag class="marlt_5" v-if="item.IsLargess" size="mini" type="danger">赠</el-tag>
                              <span class="marlt_5 color_primary cursor_pointer">
                                <el-button icon="el-icon-edit-outline" type="text" style="padding: unset" @click="editProjectRemarkClick(Project)"></el-button>
                              </span>
                            </div>
                          </el-col>
                          <el-col :span="8">
                            <el-input-number
                              v-model="Project.Quantity"
                              :min="1"
                              :max="getTimeCardMaxQuantity(timeCard) - getPackageTimeCardConsumAmount(item.ID, timeCard.ID) + Project.Quantity"
                              size="mini"
                              style="width: 100px"
                              @change="(oldval, newval) => packageTimeCartItemQuantityChangeClick(oldval, newval, item, timeCard, Project)"
                            ></el-input-number>
                          </el-col>
                          <el-col :span="5">
                            <span>{{ Project.TotalAmount | toFixed | NumFormat }}</span>
                          </el-col>
                          <el-col :span="3" class="text_right">
                            <el-button
                              v-if="TreatPermission.isTreatConsumable"
                              type="success"
                              icon="el-icon-plus"
                              circle
                              size="mini"
                              @click="addProjecConsumable(`packageTimegCardProject_${index}_${timeCardIndex}_${ProjectIndex}`)"
                            ></el-button>
                            <el-button
                              type="danger"
                              icon="el-icon-delete"
                              circle
                              size="mini"
                              @click="removePackageTimeCardItemClick(item, timeCard, Project, index, timeCardIndex, ProjectIndex)"
                            ></el-button>
                          </el-col>
                        </el-col>
                        <el-col :span="24">
                          <el-col :span="16">
                            <div class="color_red">¥ {{ Project.Price | toFixed | NumFormat }}</div>
                          </el-col>
                          <el-col :span="8">
                            <span
                              class="color_gray font_12"
                              v-if="parseFloat(Project.TotalAmount) - parseFloat(Project.Price) * parseFloat(Project.Quantity) != 0"
                            >
                              卡优惠：
                              <span class="color_green" v-if="parseFloat(Project.TotalAmount) - parseFloat(Project.Price) * parseFloat(Project.Quantity) > 0">
                                +{{
                                  Math.abs(parseFloat(Project.TotalAmount) - parseFloat(Project.Price) * parseFloat(Project.Quantity)).toFixed(2) | NumFormat
                                }}
                              </span>
                              <span
                                class="color_red"
                                v-else-if="parseFloat(Project.TotalAmount) - parseFloat(Project.Price) * parseFloat(Project.Quantity) < 0"
                              >
                                -{{
                                  Math.abs(parseFloat(Project.TotalAmount) - parseFloat(Project.Price) * parseFloat(Project.Quantity)).toFixed(2) | NumFormat
                                }}
                              </span>
                            </span>
                          </el-col>
                        </el-col>
                      </el-col>
                      <el-col v-if="Project.Remark" :span="24" class="pad_10 border_bottom font_12 color_333">备注信息: {{ Project.Remark }} </el-col>
                      <el-col v-if="Project.HandleTypeList.length > 0" :span="24" class="pad_10 padbm_0 border_bottom">
                        <el-row
                          class="cursor_pointer"
                          @click.native="employeeHandleClick(4, Project)"
                          v-for="(handleItem, handleIndex) in Project.HandleTypeList"
                          :key="handleIndex"
                        >
                          <el-col :span="4">
                            <el-form :inline="true" size="mini" label-position="left">
                              <el-form-item style="margin-bottom: 10px" :label="`${handleItem.Name}：`"></el-form-item>
                            </el-form>
                          </el-col>
                          <el-col :span="20">
                            <el-form :inline="true" size="mini">
                              <el-form-item
                                style="margin-bottom: 10px"
                                v-for="(empItem, empIndex) in handleItem.Employee"
                                :key="empIndex"
                                :label="`${empItem.EmployeeName} `"
                              >
                                <el-input
                                  class="employee_num custom-input-number"
                                  v-model="empItem.Discount"
                                  size="mini"
                                  :min="0"
                                  :max="100"
                                  type="number"
                                  v-on:click.native.stop
                                  v-input-fixed
                                  @input="handlerPercentChange(handleItem.Employee, empItem)"
                                >
                                  <template slot="append">%</template>
                                </el-input>
                                <i
                                  class="el-icon-error marlt_5 font_16"
                                  style="color: #909399; vertical-align: middle"
                                  v-on:click.stop="removeHandleClick(handleItem, empIndex)"
                                ></i>
                              </el-form-item>
                            </el-form>
                          </el-col>
                        </el-row>
                      </el-col>
                      <treat-consumable
                        :ref="`packageTimegCardProject_${index}_${timeCardIndex}_${ProjectIndex}`"
                        v-if="TreatPermission.isTreatConsumable"
                        @remove="(event) => removeTreatConsumableClick(event, Project, index, 'PackageTimeCard')"
                        @removeAll="(event) => removeAllTreatConsumableClick(event, Project, index, 'PackageTimeCard')"
                        @add="(e) => addTreatConsumableClick(e, Project, index, 'PackageTimeCard')"
                        :consumableEntityList="consumableEntityList"
                        :projectID="Project.ID"
                        :consumableList="Project.Consumable"
                      ></treat-consumable>
                    </el-row>
                  </el-card>
                </el-row>
                <!-- 套餐卡通用次卡 -->
                <el-row v-for="(generalCard, generalCardIndex) in item.packageGeneralList" :key="generalCardIndex + 'spg'">
                  <el-card shadow="never" style="border: 0px">
                    <div slot="header" class="font_13 cursor_pointer" @click="consumePackageGeneralCardSelectClick(item, generalCard)">
                      <span>
                        <el-tag size="mini">通用次卡</el-tag>
                        <span>{{ generalCard.Name }}</span>
                        <span v-if="generalCard.Alias">({{ generalCard.Alias }})</span>
                        <el-tag class="marlt_5" v-if="generalCard.IsLargess" size="mini" type="danger">赠</el-tag>
                      </span>
                      <span class="color_gray" style="float: right">
                        <span>
                          <span>剩余次数：</span>
                          <span>¥ {{ generalCard.ValidBalance - getPackageGeneralCardConsumAmount(item.ID, generalCard.ID) }}</span>
                        </span>
                        <span class="marlt_5">
                          <span>(有效次数：</span>
                          <span>{{ generalCard.ValidBalance }}</span>
                          <span class="marlt_10">消耗次数：</span>
                          <span>{{ getPackageGeneralCardConsumAmount(item.ID, generalCard.ID) }})</span>
                        </span>
                      </span>
                    </div>
                    <el-row v-for="(Project, ProjectIndex) in generalCard.Projects" :key="ProjectIndex">
                      <el-col class="row_header border_bottom" :span="24">
                        <el-col :span="8">项目</el-col>
                        <el-col :span="8">数量</el-col>
                        <el-col :span="5">金额</el-col>
                        <el-col :span="3"></el-col>
                      </el-col>
                      <el-col :span="24" class="pad_10 border_bottom">
                        <el-col :span="24">
                          <el-col :span="8">
                            <div>
                              {{ Project.Name }}
                              <span v-if="Project.Alias">({{ Project.Alias }})</span>
                              <el-tag class="marlt_5" v-if="generalCard.IsLargess" size="mini" type="danger">赠</el-tag>
                              <span class="marlt_5 color_primary cursor_pointer">
                                <el-button icon="el-icon-edit-outline" type="text" style="padding: unset" @click="editProjectRemarkClick(Project)"></el-button>
                              </span>
                            </div>
                          </el-col>
                          <el-col :span="8">
                            <el-input-number
                              v-model="Project.Quantity"
                              :min="1"
                              :max="getPackageGeneralCardProjectMaxQuantity(item, generalCard, Project)"
                              size="mini"
                              style="width: 100px"
                              @change="(oldval, newval) => packageGeneralCartItemQuantityChangeClick(oldval, newval, item, generalCard, Project)"
                            ></el-input-number>
                          </el-col>
                          <el-col :span="5">
                            <span>{{ parseFloat(Project.TotalAmount).toFixed(2) | NumFormat }}</span>
                          </el-col>
                          <el-col :span="3" class="text_right">
                            <el-button
                              v-if="TreatPermission.isTreatConsumable"
                              type="success"
                              icon="el-icon-plus"
                              circle
                              size="mini"
                              @click="addProjecConsumable(`packageGeneralCardProject_${index}_${generalCardIndex}_${ProjectIndex}`)"
                            ></el-button>
                            <el-button
                              type="danger"
                              icon="el-icon-delete"
                              circle
                              size="mini"
                              @click="removePackageGeneralCardItemClick(item, generalCard, Project, index, generalCardIndex, ProjectIndex)"
                            ></el-button>
                          </el-col>
                        </el-col>
                        <el-col :span="24">
                          <el-col :span="24">
                            <el-col :span="16">
                              <div>
                                <span class="color_red">¥ {{ Project.Price | toFixed | NumFormat }}</span>
                                <span class="color_gray marlt_5 font_12">
                                  (本次耗卡次数：{{ Project.ConsumeAmount * Project.Quantity }}
                                  <span class="marlt_10">单次耗卡次数: {{ Project.ConsumeAmount }} 次</span>)
                                </span>
                              </div>
                            </el-col>
                            <el-col :span="8">
                              <span
                                class="color_gray font_12"
                                v-if="parseFloat(Project.TotalAmount) - parseFloat(Project.Price) * parseFloat(Project.Quantity) != 0"
                              >
                                卡优惠：
                                <span class="color_green" v-if="parseFloat(Project.TotalAmount) - parseFloat(Project.Price) * parseFloat(Project.Quantity) > 0">
                                  +{{
                                    Math.abs(parseFloat(Project.TotalAmount) - parseFloat(Project.Price) * parseFloat(Project.Quantity)).toFixed(2) | NumFormat
                                  }}
                                </span>
                                <span
                                  class="color_red"
                                  v-else-if="parseFloat(Project.TotalAmount) - parseFloat(Project.Price) * parseFloat(Project.Quantity) < 0"
                                >
                                  -{{
                                    Math.abs(parseFloat(Project.TotalAmount) - parseFloat(Project.Price) * parseFloat(Project.Quantity)).toFixed(2) | NumFormat
                                  }}
                                </span>
                              </span>
                            </el-col>
                          </el-col>
                        </el-col>
                      </el-col>
                      <el-col v-if="Project.Remark" :span="24" class="pad_10 border_bottom font_12 color_333">备注信息: {{ Project.Remark }} </el-col>
                      <el-col v-if="Project.HandleTypeList.length > 0" :span="24" class="pad_10 padbm_0 border_bottom">
                        <el-row
                          class="cursor_pointer"
                          @click.native="employeeHandleClick(5, Project)"
                          v-for="(handleItem, handleIndex) in Project.HandleTypeList"
                          :key="handleIndex"
                        >
                          <el-col :span="4">
                            <el-form :inline="true" size="mini" label-position="left">
                              <el-form-item style="margin-bottom: 10px" :label="`${handleItem.Name}：`"></el-form-item>
                            </el-form>
                          </el-col>
                          <el-col :span="20">
                            <el-form :inline="true" size="mini">
                              <el-form-item
                                style="margin-bottom: 10px"
                                v-for="(empItem, empIndex) in handleItem.Employee"
                                :key="empIndex"
                                :label="`${empItem.EmployeeName} `"
                              >
                                <el-input
                                  class="employee_num custom-input-number"
                                  v-model="empItem.Discount"
                                  size="mini"
                                  :min="0"
                                  :max="100"
                                  type="number"
                                  v-on:click.native.stop
                                  v-input-fixed
                                  @input="handlerPercentChange(handleItem.Employee, empItem)"
                                >
                                  <template slot="append">%</template>
                                </el-input>
                                <i
                                  class="el-icon-error marlt_5 font_16"
                                  style="color: #909399; vertical-align: middle"
                                  v-on:click.stop="removeHandleClick(handleItem, empIndex)"
                                ></i>
                              </el-form-item>
                            </el-form>
                          </el-col>
                        </el-row>
                      </el-col>
                      <treat-consumable
                        :ref="`packageGeneralCardProject_${index}_${generalCardIndex}_${ProjectIndex}`"
                        v-if="TreatPermission.isTreatConsumable"
                        @remove="(event) => removeTreatConsumableClick(event, Project, index, 'PackageGeneralCard')"
                        @removeAll="(event) => removeAllTreatConsumableClick(event, Project, index, 'PackageGeneralCard')"
                        @add="(e) => addTreatConsumableClick(e, Project, index, 'PackageGeneralCard')"
                        :consumableEntityList="consumableEntityList"
                        :projectID="Project.ID"
                        :consumableList="Project.Consumable"
                      ></treat-consumable>
                    </el-row>
                  </el-card>
                </el-row>
                <!-- 套餐卡产品 -->
                <el-row class="border_bottom" v-for="(Product, ProductIndex) in item.packageProductList" :key="ProductIndex + 'sppd'">
                  <el-col class="row_header border_bottom" :span="24">
                    <el-col :span="8">产品</el-col>
                    <el-col :span="8">数量</el-col>
                    <el-col :span="5">金额</el-col>
                    <el-col :span="3"></el-col>
                  </el-col>
                  <el-col :span="24" class="pad_10 border_bottom">
                    <el-col :span="8">
                      <div>
                        <el-tag size="mini">产品</el-tag>
                        <span>{{ Product.Name }}</span>
                        <span v-if="Product.Alias">({{ Product.Alias }})</span>
                        <el-tag class="marlt_5" v-if="Product.IsLargess" size="mini" type="danger">赠</el-tag>
                        <span class="marlt_5 color_primary cursor_pointer">
                          <el-button icon="el-icon-edit-outline" type="text" style="padding: unset" @click="editProjectRemarkClick(Product)"></el-button>
                        </span>
                      </div>
                      <div class="color_red martp_5">
                        <span>¥ {{ Product.Price | toFixed | NumFormat }}</span>
                      </div>
                    </el-col>
                    <el-col :span="8">
                      <el-input-number
                        v-model="Product.Quantity"
                        :min="1"
                        :max="Product.ValidBalance - getPackageProductConsumeQuantity(item, Product.ID) + Product.Quantity"
                        size="mini"
                        style="width: 100px"
                        @change="(oldval, newval) => packageProductQuantityChangeClick(Product)"
                      ></el-input-number>
                    </el-col>
                    <el-col :span="5">
                      <div>
                        <span class="marrt_15">¥ {{ Product.TotalAmount | toFixed | NumFormat }}</span>
                      </div>
                      <div>
                        <span class="color_gray font_12" v-if="parseFloat(Product.TotalAmount) - parseFloat(Product.Price) * parseFloat(Product.Quantity) != 0">
                          卡优惠：
                          <span class="color_green" v-if="parseFloat(Product.TotalAmount) - parseFloat(Product.Price) * parseFloat(Product.Quantity) > 0">
                            +{{ Math.abs(parseFloat(Product.TotalAmount) - parseFloat(Product.Price) * parseFloat(Product.Quantity)).toFixed(2) | NumFormat }}
                          </span>
                          <span class="color_red" v-else-if="parseFloat(Product.TotalAmount) - parseFloat(Product.Price) * parseFloat(Product.Quantity) < 0">
                            -{{ Math.abs(parseFloat(Product.TotalAmount) - parseFloat(Product.Price) * parseFloat(Product.Quantity)).toFixed(2) | NumFormat }}
                          </span>
                        </span>
                      </div>
                    </el-col>
                    <el-col :span="3" class="text_right">
                      <el-button
                        type="danger"
                        icon="el-icon-delete"
                        circle
                        size="mini"
                        @click="removePackageProductItemClick(item, index, ProductIndex)"
                      ></el-button>
                    </el-col>
                  </el-col>
                  <el-col v-if="Product.Remark" :span="24" class="pad_10 border_bottom font_12 color_333">备注信息: {{ Product.Remark }} </el-col>
                  <el-col v-if="Product.HandleTypeList.length > 0" :span="24" class="pad_10 padbm_0 border_bottom">
                    <el-row
                      class="cursor_pointer"
                      @click.native="employeeHandleClick(1, Product)"
                      v-for="(handleItem, handleIndex) in Product.HandleTypeList"
                      :key="handleIndex"
                    >
                      <el-col :span="4">
                        <el-form :inline="true" size="mini" label-position="left">
                          <el-form-item style="margin-bottom: 10px" :label="`${handleItem.Name}：`"></el-form-item>
                        </el-form>
                      </el-col>
                      <el-col :span="20">
                        <el-form :inline="true" size="mini">
                          <el-form-item
                            style="margin-bottom: 10px"
                            v-for="(empItem, empIndex) in handleItem.Employee"
                            :key="empIndex"
                            :label="`${empItem.EmployeeName} `"
                          >
                            <el-input
                              class="employee_num custom-input-number"
                              v-model="empItem.Discount"
                              size="mini"
                              :min="0"
                              :max="100"
                              type="number"
                              v-on:click.native.stop
                              v-input-fixed
                              @input="handlerPercentChange(handleItem.Employee, empItem)"
                            >
                              <template slot="append">%</template>
                            </el-input>
                            <i
                              class="el-icon-error marlt_5 font_16"
                              style="color: #909399; vertical-align: middle"
                              v-on:click.stop="removeHandleClick(handleItem, empIndex)"
                            ></i>
                          </el-form-item>
                        </el-form>
                      </el-col>
                    </el-row>
                  </el-col>
                </el-row>
              </div>
            </el-scrollbar>
          </el-main>
          <transition>
            <div v-show="showRemark" class="orderInfoRemark" @click="showRemark = false">
              <div @click.stop class="infoRemarContent">
                <el-row @click.native="hiddenInfoRemarkClick" style="height: 40px" class="dis_flex flex_y_center">
                  <el-col :span="12">订单信息</el-col>
                  <el-col :span="12" class="text_right">
                    <el-button @click="hiddenInfoRemarkClick" type="text">收起</el-button>
                    <i class="el-icon-arrow-down color_main font_16 text-bold"></i>
                  </el-col>
                </el-row>
                <div class="back_f7f8fa" style="padding: 20px 16px 2px 16px">
                  <el-form label-width="80px" size="small">
                    <el-form-item
                      v-show="
                        (currentSelectProjectList && currentSelectProjectList.length > 0) ||
                        (currentSelectSavingCardList && currentSelectSavingCardList.length > 0) ||
                        (currentSelectTimeCardList && currentSelectTimeCardList.length > 0) ||
                        (currentSelectGeneralCardList && currentSelectGeneralCardList.length > 0) ||
                        (currentSelectProductList && currentSelectProductList.length > 0) ||
                        (currentselectPackageCardList && currentselectPackageCardList.length > 0)
                      "
                      label="批量添加："
                    >
                      <el-button @click="showSelectAllHandlerClick" icon="el-icon-plus">经手人</el-button>
                    </el-form-item>
                    <el-form-item label="订单备注：">
                      <el-input type="textarea" :rows="3" placeholder="请输入备注信息" v-model="remark"></el-input>
                    </el-form-item>
                  </el-form>
                </div>
              </div>
            </div>
          </transition>
          <el-row @click.native="showRemarkClick" style="height: 40px" class="dis_flex flex_y_center pad_0_10 back_f8 font_13">
            <el-col :span="12" class="color_666">订单信息<span class="font_12 color_999">(已折叠)</span></el-col>
            <el-col :span="12" class="text_right">
              <el-button type="text">展开</el-button>
              <i class="el-icon-arrow-up color_main font_16 text-bold"></i>
            </el-col>
          </el-row>
          <el-footer class="border_top">
            <el-row class="color_333" type="flex" align="middle">
              <el-col :span="20">
                <span>
                  <span class="font_14 color_maroon">消耗金额(含赠送)：¥ {{ Number(totalAmount).toFixed(2) | NumFormat }}</span>
                </span>
              </el-col>
              <el-col :span="2" class="text_right">
                <el-button
                  v-if="TreatPermission.isTreatPending || TreatPermission.isTreatBillingTreatPending"
                  type="primary"
                  size="small"
                  @click="createPendingOrderClick"
                  >提交</el-button
                >
              </el-col>
              <el-col :span="2" class="text_right">
                <el-button
                  v-if="TreatPermission.isTreatSettle || TreatPermission.isTreatBillingTreatSettle"
                  type="primary"
                  size="small"
                  @click="consumeBillClick"
                  >结账</el-button
                >
              </el-col>
            </el-row>
          </el-footer>
        </el-container>
      </el-col>
    </el-row>

    <!-- 储值卡消耗项目 -->
    <el-dialog :visible.sync="dialogSavingCard" :title="selectSavingcardItem.Name" width="680px" append-to-body>
      <div slot="title">
        储值卡消耗 - {{ selectSavingcardItem.Name }}
        <span v-if="selectSavingcardItem.Alias">({{ selectSavingcardItem.Alias }})</span>
      </div>
      <el-input
        @clear="clearAndSearch"
        placeholder="请输入项目名称、别名关键字，按回车搜索"
        prefix-icon="el-icon-search"
        v-model="searchSavingcardProjectName"
        clearable
        size="small"
        @keyup.enter.native="searchSavingCardProjectClick"
      >
      </el-input>
      <el-row class="valueCard_project border martp_10">
        <el-col :span="5" class="border_right text_center category">
          <el-scrollbar class="el-scrollbar_height el-scrollbar_dialog_height">
            <div
              v-for="(cItem, index) in currentSavingcardProjectCategoty"
              :key="index + 'sav_c'"
              :class="[savingcardProjectCategotyIndex == index ? 'category_select' : '', 'pad_10_15', 'border_bottom', 'cursor_pointer']"
              @click="savingCardProjectCategoryClick(cItem, index)"
            >
              {{ cItem.ParentName }}
            </div>
          </el-scrollbar>
        </el-col>
        <el-col :span="5" class="border_right text_center category">
          <el-scrollbar class="el-scrollbar_height el-scrollbar_dialog_height">
            <div
              v-for="(cItem, index) in currentSavingcardProjectSecondCategoty"
              :key="index + 'sav_c'"
              :class="[savingcardProjectSecondCategotyIndex == index ? 'category_select' : '', 'pad_10_15', 'border_bottom', 'cursor_pointer']"
              @click="savingCardProjectSecondCategoryClick(cItem, index)"
            >
              {{ cItem.CategoryName }}
            </div>
          </el-scrollbar>
        </el-col>
        <el-col :span="14" class="category" v-loading="savingCardLoading">
          <el-scrollbar class="el-scrollbar_height">
            <!-- <el-collapse v-model="currentSavingActiveName">
              <el-collapse-item v-for="(item, index) in currentSavingcardProject" :key="index + 'sav_c_1'" :title="item.Name" :name="item.ID"> -->
            <div
              v-for="(project, index) in currentSavingcardProject"
              class="border_bottom pad_5_10 cursor_pointer"
              :key="project.ID + '_sva_p_' + index + project.Name"
              @click="addSavingCardApplyProjectItem(project)"
            >
              <div>
                {{ project.Name }}
                <span v-if="project.Alias">({{ project.Alias }})</span>
              </div>
              <div class="font_13">
                <span class="color_red font_13">¥ {{ project.PreferentialPrice | toFixed | NumFormat }}</span>
                <span class="color_gray font_10 marlt_5">
                  <span>(原价：</span>
                  <span class="t-through">¥ {{ project.Price | toFixed | NumFormat }}</span>
                  <span class="marlt_10" v-if="project.PriceType == 1">卡权益：{{ project.discountName }})</span>
                  <span class="marlt_10" v-if="project.PriceType == 2">卡权益：¥ {{ project.DiscountPrice | toFixed | NumFormat }} (元)</span>
                </span>
              </div>
            </div>
            <!-- </el-collapse-item>
            </el-collapse> -->
          </el-scrollbar>
        </el-col>
      </el-row>

      <el-row type="flex" align="middle">
        <el-col :span="24" class="text_right padtp_10">
          <el-pagination
            background
            v-if="savingCardPaginations.total > 0"
            @current-change="handleSavingProjectCurrentChange"
            :current-page.sync="savingCardPaginations.page"
            :page-size="savingCardPaginations.page_size"
            :layout="savingCardPaginations.layout"
            :total="savingCardPaginations.total"
            :pager-count="5"
          ></el-pagination>
        </el-col>
      </el-row>

      <span slot="footer" class="dialog-footer">
        <el-row type="flex" align="middle">
          <el-col :span="18" class="text_left font_14">
            <div class="font_14">
              <span>
                <span>剩余金额：</span>
                <span class="color_red"
                  >¥
                  {{
                    (selectSavingcardItem.ValidBalance - getCurrentSavingCardConsumeAmount(savingCardConsumeItem, isConsumePackage)) | toFixed | NumFormat
                  }}</span
                >
              </span>
              <span class="font_12 color_gray marlt_5">
                <span>(可用余额：</span>
                <span>¥ {{ selectSavingcardItem.ValidBalance | toFixed | NumFormat }}</span>
                <span class="marlt_10">卡抵扣：</span>
                <span>-¥ {{ getCurrentSavingCardConsumeAmount(savingCardConsumeItem, isConsumePackage) | toFixed | NumFormat }})</span>
              </span>
            </div>
          </el-col>
          <el-col :span="6">
            <el-button type="primary" plain @click="dialogSavingCard = false" size="small">取消</el-button>
          </el-col>
        </el-row>
      </span>
    </el-dialog>

    <!-- 通用次卡消耗项目 -->
    <el-dialog :visible.sync="dialogGeneralCard" width="680px" append-to-body>
      <div slot="title">
        通用次卡消耗 - {{ selectGeneralcardItem.Name }}
        <span v-if="selectGeneralcardItem.Alias">({{ selectGeneralcardItem.Alias }})</span>
      </div>
      <el-input
        placeholder="请输入项目名称、别名关键字，按回车搜索"
        prefix-icon="el-icon-search"
        v-model="searchGeneralcarPorjectName"
        clearable
        size="small"
        @clear="clearAndGenterSearch"
        @keyup.enter.native="searchGeneralCardProjectClick"
      >
      </el-input>
      <el-row class="valueCard_project border martp_10">
        <el-col :span="5" class="border_right text_center category">
          <el-scrollbar class="el-scrollbar_height">
            <div
              v-for="(cateItem, index) in currentGeneralcarProjectCategoty"
              :key="index"
              :class="[generalCardProjectCategotyIndex == index ? 'category_select' : '', 'pad_10_15', 'border_bottom', 'cursor_pointer']"
              @click="generalCardProjectCategoryClick(cateItem, index)"
            >
              {{ cateItem.ParentName }}
            </div>
          </el-scrollbar>
        </el-col>
        <el-col v-if="!(isShowHistoricalData && generalCardProjectCategotyIndex == 0)" :span="5" class="border_right text_center category">
          <el-scrollbar class="el-scrollbar_height">
            <div
              v-for="(cateItem, index) in currentGeneralcarProjectSecondCategoty"
              :key="index"
              :class="[generalCardProjectSecondCategotyIndex == index ? 'category_select' : '', 'pad_10_15', 'border_bottom', 'cursor_pointer']"
              @click="generalCardProjectSecondCategoryClick(cateItem, index)"
            >
              {{ cateItem.CategoryName }}
            </div>
          </el-scrollbar>
        </el-col>
        <el-col :span="!(isShowHistoricalData && generalCardProjectCategotyIndex == 0) ? 14 : 19" class="category" v-loading="generalCardPrjectLoading">
          <el-scrollbar class="el-scrollbar_height">
            <div
              v-for="project in !(isShowHistoricalData && generalCardProjectCategotyIndex == 0) ? currentGeneralcarProject : generalCardHistoricalData"
              class="border_bottom pad_5_10 cursor_pointer"
              :key="project.ID"
              @click="addGeneralclickCardApplyProjectItem(project)"
            >
              <div>
                {{ project.Name }}
                <span v-if="project.Alias">({{ project.Alias }})</span>
              </div>
              <div class="dis_flex flex_x_between">
                <span class="color_red font_13">¥ {{ project.Price | toFixed | NumFormat }}</span>
                <!-- <span class="color_gray t-through font_10"></span> -->
                <div class="color_gray font_10">单次消耗卡：{{ project.ConsumeAmount }}次</div>
              </div>
            </div>
          </el-scrollbar>
        </el-col>
      </el-row>

      <el-row type="flex" align="middle">
        <el-col :span="24" class="text_right padtp_10">
          <el-pagination
            background
            v-if="generalCardPaginations.total > 0"
            @current-change="handleGeeralCardProjectCurrentChange"
            :current-page.sync="generalCardPaginations.page"
            :page-size="generalCardPaginations.page_size"
            :layout="generalCardPaginations.layout"
            :total="generalCardPaginations.total"
            :pager-count="5"
          ></el-pagination>
        </el-col>
      </el-row>
      <span slot="footer">
        <el-row type="flex" align="middle">
          <el-col :span="18" class="text_left font_14">
            <div class="font_14">
              <span>
                <span>剩余次数：</span>
                <span v-if="isConsumePackage" class="color_red">{{
                  selectGeneralcardItem.ValidBalance - getPackageGeneralCardConsumAmount(tempCurrentSelectPackageItem.ID, selectGeneralcardItem.ID)
                }}</span>
                <span v-else class="color_red">{{ selectGeneralcardItem.ValidBalance - getGeneralCardQuantity(selectGeneralcardItem.ID) }}</span>
              </span>
              <span class="font_12 color_gray marlt_5">
                <span>(有效次数：</span>
                <span>{{ selectGeneralcardItem.ValidBalance }}</span>
                <span class="marlt_10">消耗次数：</span>
                <span>{{ getGeneralCardQuantity(selectGeneralcardItem.ID) }})</span>
              </span>
            </div>
          </el-col>
          <el-col :span="6">
            <el-button type="primary" plain @click="dialogGeneralCard = false" size="small">取消</el-button>
          </el-col>
        </el-row>
      </span>
    </el-dialog>

    <!-- 时效卡消耗项目 -->
    <el-dialog :visible.sync="dialogTimeCard" width="680px" append-to-body>
      <div slot="title">
        时效卡消耗 - {{ selectTimecardItem.Name }}
        <span v-if="selectTimecardItem.Alias">({{ selectTimecardItem.Alias }})</span>
      </div>
      <el-input
        placeholder="请输入项目名称、别名关键字，按回车搜索"
        prefix-icon="el-icon-search"
        v-model="searchTimeCardPorjectName"
        clearable
        size="small"
        asdf
        @clear="clearAndTimeSearch"
        @keyup.enter.native="searchTimeCardProjectClick"
      >
        <!-- <el-button slot="append" icon="el-icon-search" @click="searchTimeCardProjectClick"></el-button> -->
      </el-input>
      <el-row class="valueCard_project border martp_10">
        <el-col :span="5" class="border_right text_center category">
          <!-- <div class="row_header">分类</div> -->
          <el-scrollbar class="el-scrollbar_height">
            <div
              v-for="(cateItem, index) in currentTimeCardProjectCategoty"
              :key="index + 'time_p_'"
              :class="[timeCardProjectCategotyIndex == index ? 'category_select' : '', 'pad_10_15', 'border_bottom', 'cursor_pointer', 'font_13']"
              @click="timeCardProjectCategoryClick(cateItem, index)"
            >
              {{ cateItem.ParentName }}
            </div>
          </el-scrollbar>
        </el-col>
        <el-col :span="5" class="border_right text_center category">
          <el-scrollbar class="el-scrollbar_height">
            <div
              v-for="(cateItem, index) in currentTimecarProjectSecondCategoty"
              :key="index + 'time_p_'"
              :class="[timeCardProjectSecondCategotyIndex == index ? 'category_select' : '', 'pad_10_15', 'border_bottom', 'cursor_pointer', 'font_13']"
              @click="timeCardProjectSecndCategoryClick(cateItem, index)"
            >
              {{ cateItem.CategoryName }}
            </div>
          </el-scrollbar>
        </el-col>
        <el-col :span="14" class="category" v-loading="timeCardPrjectLoading">
          <el-scrollbar class="el-scrollbar_height">
            <div
              v-for="project in currentTimeCardProject"
              class="border_bottom pad_5_10 font_13 cursor_pointer"
              :key="project.ID"
              @click="addTimeCardApplyProjectItem(project)"
            >
              <div>
                {{ project.Name }}
                <span v-if="project.Alias">({{ project.Alias }})</span>
              </div>
              <div>
                <span class="color_red font_13">¥ {{ project.Price | toFixed | NumFormat }}</span>
                <!-- <span class="color_gray t-through"></span>
                <div class="color_gray"></div>-->
              </div>
            </div>
          </el-scrollbar>
        </el-col>
      </el-row>

      <el-row type="flex" align="middle">
        <el-col :span="24" class="text_right padtp_10">
          <el-pagination
            background
            v-if="timeCardPaginations.total > 0"
            @current-change="handleTimeCardProjectCurrentChange"
            :current-page.sync="timeCardPaginations.page"
            :page-size="timeCardPaginations.page_size"
            :layout="timeCardPaginations.layout"
            :total="timeCardPaginations.total"
            :pager-count="5"
          ></el-pagination>
        </el-col>
      </el-row>

      <span slot="footer">
        <el-button type="primary" plain @click="dialogTimeCard = false" size="small">取消</el-button>
      </span>
    </el-dialog>

    <!--经手人-->
    <el-dialog title="经手人" :visible.sync="dialogVisible" width="800px" append-to-body>
      <div>
        <el-row class="padbm_10">
          <el-col :span="8">
            <el-input placeholder="请输入员工编号、姓名" prefix-icon="el-icon-search" v-model="handlerName" size="small" clearable></el-input>
          </el-col>
        </el-row>
        <el-tabs v-model="tabHandle">
          <el-tab-pane :label="handler.Name" :name="`${index}`" v-for="(handler, index) in treatHandlerList" :key="index">
            <el-row style="max-height: 300px; overflow-y: auto">
              <el-col
                :span="12"
                v-for="item in handler.Employee.filter(
                  (item) =>
                    !handlerName ||
                    item.EmployeeName.toLowerCase().includes(handlerName.toLowerCase()) ||
                    item.EmployeeID.toLowerCase().includes(handlerName.toLowerCase())
                )"
                :key="item.EmployeeID"
                class="marbm_10 dis_flex flex_y_center"
              >
                <el-checkbox v-model="item.Checked" @change="handlerCheckedChange(handler.Employee, item)">
                  <span class="marrt_10">{{ item.EmployeeName }} [{{ item.EmployeeID }}]</span>
                </el-checkbox>
                <el-input
                  class="custom-input-number"
                  placeholder
                  v-model="item.Discount"
                  style="width: 120px"
                  type="number"
                  size="mini"
                  min="0"
                  max="100"
                  v-input-fixed
                  @input="handlerPercentChange(handler.Employee, item, 'dialog')"
                >
                  <template slot="append">%</template>
                </el-input>
              </el-col>
            </el-row>
          </el-tab-pane>
        </el-tabs>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false" size="small">取 消</el-button>
        <el-button type="primary" size="small" @click="submitHandleClick" v-prevent-click>确 定</el-button>
      </div>
    </el-dialog>

    <!--结账-->
    <el-dialog :visible.sync="dialogBill" width="350px" append-to-body>
      <div class="dis_flex flex_x_center flex_y_center flex_dir_column">
        <el-row>
          <i class="el-icon-document" style="font-size: 80px; color: #999"></i>
        </el-row>
        <el-row>
          <el-col class="color_red font_24 martp_15">是否确认结账</el-col>
        </el-row>
        <el-row>
          <el-col class="martp_15">是否确认单据无误，确认后系统将生成单据</el-col>
        </el-row>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogBill = false" size="small">取 消</el-button>
        <el-button type="primary" @click="submitConsumeOrderClick" :loading="submitLoading" v-prevent-click size="small">确定结账</el-button>
      </div>
    </el-dialog>

    <!--结账成功-->
    <el-dialog :visible.sync="dialoConsumeSucceed" width="450px" append-to-body @close="continueCreateConsumeOrder">
      <div class="text_center pad_15">
        <i class="el-icon-document" style="font-size: 80px; color: #999"></i>
        <div class="pad_15 color_primary font_weight_600 font_18">订单已结账成功</div>
      </div>
      <el-row class="pad_15 border_bottom">
        <el-col :span="12">订单号：</el-col>
        <el-col :span="12" class="text_right">{{ consumeBillNumber }}</el-col>
      </el-row>
      <el-row class="pad_15 border_bottom">
        <el-col :span="12">订单金额：</el-col>
        <el-col :span="12" class="color_red text_right">¥{{ consumeTotalAmount | toFixed | NumFormat }}</el-col>
      </el-row>
      <el-row class="pad_15 border_bottom">
        <el-col :span="5">订单备注：</el-col>
        <el-col :span="19">{{ remark }}</el-col>
      </el-row>

      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="continueCreateConsumeOrder" size="small">继续开单</el-button>
        <el-button type="primary" @click="printNoteOfSmallDenominationTreatInfo" :loading="printLoading" v-prevent-click size="small">打印小票</el-button>
        <el-button type="primary" @click="confrimTreatPrintClick" size="small">打印单据</el-button>
      </div>
    </el-dialog>

    <operation-record :RecordDialog.sync="RecordDialog" :ID="AccountID" :Type="Type"></operation-record>

    <!--经手人-->
    <el-dialog title="经手人" :visible.sync="dialogVisibleAllHandler" width="800px" append-to-body>
      <div>
        <el-row class="padbm_10">
          <el-col :span="8">
            <el-input placeholder="请输入员工编号、姓名" prefix-icon="el-icon-search" v-model="handlerAllName" size="small" clearable></el-input>
          </el-col>
        </el-row>
        <el-tabs v-model="tabAllHandle">
          <el-tab-pane :label="handler.Name" :name="`${index}`" v-for="(handler, index) in treatAllHandlerList" :key="index">
            <el-row style="max-height: 300px; overflow-y: auto">
              <el-col
                :span="12"
                v-for="item in handler.Employee.filter(
                  (item) =>
                    !handlerAllName ||
                    item.EmployeeName.toLowerCase().includes(handlerAllName.toLowerCase()) ||
                    item.EmployeeID.toLowerCase().includes(handlerAllName.toLowerCase())
                )"
                :key="item.EmployeeID"
                class="marbm_10 dis_flex flex_y_center"
              >
                <el-checkbox v-model="item.Checked" @change="handlerCheckedChange(handler.Employee, item)">
                  <span class="marrt_10">{{ item.EmployeeName }} [{{ item.EmployeeID }}]</span>
                </el-checkbox>
                <el-input
                  placeholder
                  v-model="item.Discount"
                  style="width: 120px"
                  type="number"
                  size="mini"
                  v-input-fixed
                  min="0"
                  max="100"
                  @input="handlerPercentChange(handler.Employee, item, 'dialog')"
                >
                  <template slot="append">%</template>
                </el-input>
              </el-col>
            </el-row>
          </el-tab-pane>
        </el-tabs>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisibleAllHandler = false" size="small">取 消</el-button>
        <el-button type="primary" size="small" @click="confirmAllHandlerClick" v-prevent-click>确 定</el-button>
      </div>
    </el-dialog>

    <el-button v-show="false" ref="printButtonTreat" v-print="'printContentTreat'">打印</el-button>
    <!-- 打印 -->
    <el-dialog title="选择打印模板" :visible.sync="printTreatTemplateVisible" width="400px" append-to-body>
      <el-select size="small" v-model="printTreatTemplateID">
        <el-option v-for="item in treatTemplateTypeList" :key="item.ID" :label="item.Name" :value="item.ID"></el-option>
      </el-select>
      <div slot="footer">
        <el-button @click="printTreatTemplateVisible = false" size="small" v-prevent-click>取消</el-button>
        <el-button type="primary" @click="confirmTreatSelectPrintTemplate" size="small" v-prevent-click>打印 </el-button>
      </div>
    </el-dialog>
    <div style="display: none">
      <div id="printContentTreat">
        <component :is="treatPrintComponentName"></component>
      </div>
    </div>

    <el-dialog :visible.sync="projectRemarkDialogVisible" title="备注信息" width="500px" append-to-body>
      <el-input type="textarea" :autosize="{ minRows: 2, maxRows: 4 }" maxlength="100" show-word-limit placeholder="请输入备注信息" v-model="projectRemark">
      </el-input>

      <div slot="footer" class="dialog-footer">
        <el-button @click="projectRemarkDialogVisible = false" size="small" :disabled="RemarkLoading">取 消</el-button>
        <el-button type="primary" @click="saveRemarkClick" :loading="RemarkLoading" v-prevent-click size="small">保 存</el-button>
      </div>
    </el-dialog>

    <treatCashierReceipt
      v-if="treatCashierReceiptDialogVisible"
      :visible.sync="treatCashierReceiptDialogVisible"
      :treatInfo="treatInfo"
      :entityName="userInfo.EntityName"
      :cashierReceipt="cashierReceipt"
    ></treatCashierReceipt>
  </div>
</template>

<script>
import API from '@/api/iBeauty/Order/consumeGoods';
import draftAPI from '@/api/iBeauty/Order/draftOrder';
import API_consumable from '@/api/iBeauty/Order/treatConsumable.js';

import date from '@/components/js/date';

import orderAPI from '@/api/iBeauty/Order/treatBill';
import cashierAPI from '@/api/iBeauty/Order/cashierReceipt';
import operationRecord from '@/views/CRM/Customer/Components/Customer/customerAccountOperationRecord.vue';
import treatConsumable from '@/components/iBeauty/Order/treatConsumable.vue';

import printComponent from '@/views/iBeauty/Order/components/zl-print.js';
import print from 'vue-print-nb';

var Enumerable = require('linq');
export default {
  directives: {
    print,
  },
  props: {
    billDate: String, //开单时间
    isSupplement: Boolean, // 是否补单
    customerID: [Number, String], // 顾客编号
    customerFullName: String, // 顾客完整姓名
    customerPhoneNumber: String, // 顾客手机号
    TreatPermission: Object,
  },
  components: {
    operationRecord,
    treatConsumable,
    treatCashierReceipt: () => import('@/components/iBeauty/Order/cashierReceipt/treatCashierReceipt.vue'),
  },
  data() {
    /** 下表   项目 0 储值卡1  时效卡 2 通用次卡 3 套餐卡4  产品 5  */
    return {
      employeeDiscount: '',
      discountData: {
        PriceType: '',
        DiscountPrice: '',
      },
      consumableEntityList: [],
      printTreatTemplateVisible: false,
      treatTemplateTypeList: [],
      treatPrintComponentName: '',
      printTreatTemplateID: null,

      dialogVisibleAllHandler: false,
      showRemark: false,
      RecordDialog: false,
      generalCardPrjectLoading: false,
      timeCardPrjectLoading: false,
      AccountID: '',
      Type: '',
      treatCashierReceiptDialogVisible: false,
      submitLoading: false,
      loading: false,
      modalLoading: false,
      printLoading: false,
      dialogSavingCard: false,
      dialogGeneralCard: false,
      dialogTimeCard: false,
      dialogVisible: false,
      dialogBill: false,
      dialogDeduction: false,
      dialoConsumeSucceed: false,
      tabPane: '', //搜索商品的下表
      searchName: '', //搜索品项
      currentConsumeIndex: 0,
      remark: '',
      consumeBillNumber: '',
      consumeTotalAmount: 0,
      activeName: 0, // 默认展开选择项目的第一项
      tabHandle: '0',
      handlerName: '',
      /**  请求数据  */
      treatProjectAccountList: [],
      treatProductAccountList: [],
      treatSavingCardAccountList: [],
      treatSavingCardAccountProjectList: [],
      treatSavingCardAccountProjectList_i: [],
      treatGoodsPackageCardAccountList: [],
      treatGoodsPackageCardAccountDetailsList: [],
      treatGeneralCardAccountList: [],
      treatTimeCardAccountList: [],
      /**  经手人数据  */
      projectTreatHandlers: [],
      productTreatHandlers: [],
      savingCardTreatHandlers: [],
      /**  选择时的经手人 中间数组  */
      treatHandlerList: [],
      /** 将要消耗的品项 品项   */
      currentSelectProjectList: [],
      currentSelectProductList: [],
      currentSelectSavingCardList: [],
      currentSelectTimeCardList: [],
      currentSelectGeneralCardList: [],
      currentselectPackageCardList: [],
      /**  选中的 储值卡 时效卡 通用次卡  */
      /**  将要消耗的储值卡  */
      selectSavingcardItem: {},
      selectSavingCardCategoryItem: {},
      savingcardProjectSecondCategotyIndex: 0,
      savingcardProjectCategotyIndex: 0,
      currentSavingcardProjectCategoty: [],
      currentSavingcardProjectSecondCategoty: [],
      currentSavingcardProject: [],
      currentSavingActiveName: [],
      searchSavingcardProjectName: '',
      savingCardConsumeItem: null, //
      savingCardLoading: false,
      /**  通用次卡  */
      selectGeneralcardItem: {},
      selectGenearlCardCategoryItem: {},
      currentGeneralcarProjectCategoty: [],
      currentGeneralcarProjectSecondCategoty: [],
      generalCardProjectSecondCategotyIndex: 0,
      generalCardProjectCategotyIndex: 0,
      currentGeneralcarProject: [],
      currentGeneralcarActiveName: [],
      searchGeneralcarPorjectName: '',
      GeneralcarConsumeItem: null, //
      /**  时效卡  */
      selectTimecardItem: {},
      selectTimeCardCategoryItem: {},
      currentTimeCardProjectCategoty: [],
      currentTimecarProjectSecondCategoty: [],
      timeCardProjectSecondCategotyIndex: 0,
      timeCardProjectCategotyIndex: 0,
      currentTimeCardProject: [],
      currentTimeCardActiveName: [],
      searchTimeCardPorjectName: '',
      timeCardConsumeItem: null, //

      currentSelectCardItem: {},
      currentCardApplyProject: [], // 当前卡项适用项目
      currentCardApplyProjectChild: [],

      currentCategoryIndex: 0,

      currentPackCategoryIndex: 0,
      currentApplyCardName: '',
      cardSearchName: '',
      currentSelectPackageItem: {}, // 点击分类的套餐卡信息
      tempCurrentSelectPackageItem: {}, // 临时数据 当前选中的套餐卡
      isConsumePackage: false,
      packageDetailLoading: false,
      cashierReceipt: {
        NameEncrypt: '',
      },
      userInfo: {
        EntityName: '',
      },
      treatInfo: null,
      TakeOrderNum: '',
      // treatTakeOrderData: "",

      savingCardPaginations: {
        page: 1, // 当前位于哪页
        total: 0, // 总数
        page_size: 10, // 1页显示多少条
        layout: 'total, prev, pager, next', // 翻页属性
      },
      generalCardPaginations: {
        page: 1, // 当前位于哪页
        total: 0, // 总数
        page_size: 10, // 1页显示多少条
        layout: 'total, prev, pager, next', // 翻页属性
      },
      timeCardPaginations: {
        page: 1, // 当前位于哪页
        total: 0, // 总数
        page_size: 10, // 1页显示多少条
        layout: 'total, prev, pager, next', // 翻页属性
      },

      generalCardHistoricalData: [],
      generalCardHistoricalPaginations: {
        page: 1, // 当前位于哪页
        total: 0, // 总数
        page_size: 10, // 1页显示多少条
        layout: 'total, prev, pager, next', // 翻页属性
      },
      isShowHistoricalData: false,
      tabAllHandlePosition: 'saleHandler',
      tabAllHandle: '0',
      saleAllHandlerList: [],
      treatAllHandlerList: [],
      handlerAllName: '',

      projectRemark: '',
      currentRemarkItem: '',
      projectRemarkDialogVisible: false,
      RemarkLoading: false,
    };
  },
  /**  计算属性  */
  computed: {
    /**  计算将要消耗的业绩金额  */
    totalAmount: function () {
      var that = this;
      var total = 0;
      total += Enumerable.from(that.currentSelectProjectList).sum((i) => Number(i.TotalAmount));
      total += Enumerable.from(that.currentSelectProductList).sum((i) => Number(i.TotalAmount));

      /**  储值卡  */
      Enumerable.from(that.currentSelectSavingCardList).forEach((item) => {
        total += Enumerable.from(item.Projects).sum((i) => Number(i.TotalAmount));
      });

      /**  时效卡  */
      Enumerable.from(that.currentSelectTimeCardList).forEach((item) => {
        total += Enumerable.from(item.Projects).sum((i) => Number(i.TotalAmount));
      });

      /**  通用次卡  */
      Enumerable.from(that.currentSelectGeneralCardList).forEach((item) => {
        total += Enumerable.from(item.Projects).sum((i) => Number(i.TotalAmount));
      });

      Enumerable.from(that.currentselectPackageCardList).forEach((item) => {
        total += Enumerable.from(item.packageProjectList).sum((i) => Number(i.TotalAmount));
        total += Enumerable.from(item.packageProductList).sum((i) => Number(i.TotalAmount));

        Enumerable.from(item.packageSavingCardList).forEach((SavingCard) => {
          total += Enumerable.from(SavingCard.Projects).sum((i) => Number(i.TotalAmount));
        });

        Enumerable.from(item.packageTimeCardList).forEach((TimeCard) => {
          total += Enumerable.from(TimeCard.Projects).sum((i) => Number(i.TotalAmount));
        });
        Enumerable.from(item.packageGeneralList).forEach((GeneralCard) => {
          total += GeneralCard.Projects.reduce((curVal, perVal) => {
            return curVal + parseFloat(perVal.TotalAmount);
          }, 0);
        });
      });

      return total;
    },
  },
  /**  生命周期函数   实例被挂载后调用 生命周期内只执行一次  */
  mounted() {
    var that = this;
    that.userInfo = JSON.parse(localStorage.getItem('access-user'));
    /**  经手人  */
    that.treatProjectHandlerNet();
    that.treatProductHandlerNet();
    that.treatSavingCardHandlerNet();
    that.treatBill_productEntity();
    that.saleBill_employeeDiscount();
  },
  /**  方法集合  */
  methods: {
    /**  保存备注  */
    saveRemarkClick() {
      let that = this;
      that.currentRemarkItem.Remark = that.projectRemark;
      that.projectRemarkDialogVisible = false;
      that.projectRemark = '';
      that.currentRemarkItem = '';
    },
    // /**  编辑项目 备注  */
    editProjectRemarkClick(item) {
      let that = this;
      that.projectRemarkDialogVisible = true;
      that.currentRemarkItem = item;
      that.projectRemark = that.currentRemarkItem.Remark;
    },
    // /**  确定打印-消耗 -多  */
    confirmTreatSelectPrintTemplate() {
      let that = this;
      that.printTreatTemplateVisible = false;
      that.$nextTick(() => {
        let temp = that.treatTemplateTypeList.find((i) => {
          return i.ID == that.printTreatTemplateID;
        });
        that.treatPrintComponentName = printComponent.getPrintComponent(that.treatInfo, temp.Template);
        let buttonElement = that.$refs.printButtonTreat.$el;
        let clickEvent = new MouseEvent('click');
        buttonElement.dispatchEvent(clickEvent);
      });
    },
    // /**  确认打印-消耗  */
    confrimTreatPrintClick() {
      let that = this;
      if (that.treatTemplateTypeList.length == 1) {
        let temp = that.treatTemplateTypeList[0].Template;
        that.$nextTick(() => {
          that.treatPrintComponentName = printComponent.getPrintComponent(that.treatInfo, temp);
          let buttonElement = that.$refs.printButtonTreat.$el;
          let clickEvent = new MouseEvent('click');
          buttonElement.dispatchEvent(clickEvent);
        });
      } else {
        if (!that.treatTemplateTypeList || !that.treatTemplateTypeList.length) {
          that.$message.error('暂无打印模板，请添加打印模板');
          return;
        }
        that.printTreatTemplateID = that.treatTemplateTypeList[0].ID;
        that.printTreatTemplateVisible = true;
      }
    },
    // 弹出小票打印窗口
    printNoteOfSmallDenominationTreatInfo() {
      let that = this;
      that.treatCashierReceiptDialogVisible = true;
    },
    /**  确认全部修改经手人  */
    confirmAllHandlerClick() {
      let that = this;
      /* 项目 */
      that.currentSelectProjectList.forEach((j) => {
        j.HandleTypeList.forEach((n) => {
          that.treatAllHandlerList
            .filter((i) => i.Name == n.Name)
            .forEach((e) => {
              n.Employee = e.Employee.filter((em) => em.Checked).map((emp) => {
                emp.ID = `${n.ID}-${emp.EmployeeID}`;
                emp.TreatHandlerID = n.ID;
                return Object.assign({}, emp);
              });
            });
        });
      });
      /* 产品 */
      that.currentSelectProductList.forEach((j) => {
        j.HandleTypeList.forEach((n) => {
          that.treatAllHandlerList
            .filter((i) => i.Name == n.Name)
            .forEach((e) => {
              n.Employee = e.Employee.filter((em) => em.Checked).map((emp) => {
                emp.ID = `${n.ID}-${emp.EmployeeID}`;
                emp.TreatHandlerID = n.ID;
                return Object.assign({}, emp);
              });
            });
        });
      });
      /* 储值卡 */
      that.currentSelectSavingCardList.forEach((j) => {
        j.Projects.forEach((p) => {
          p.HandleTypeList.forEach((n) => {
            that.treatAllHandlerList
              .filter((i) => i.Name == n.Name)
              .forEach((e) => {
                n.Employee = e.Employee.filter((em) => em.Checked).map((emp) => {
                  emp.ID = `${n.ID}-${emp.EmployeeID}`;
                  emp.TreatHandlerID = n.ID;
                  return Object.assign({}, emp);
                });
              });
          });
        });
      });
      /* 时效卡 */
      that.currentSelectTimeCardList.forEach((j) => {
        j.Projects.forEach((p) => {
          p.HandleTypeList.forEach((n) => {
            that.treatAllHandlerList
              .filter((i) => i.Name == n.Name)
              .forEach((e) => {
                n.Employee = e.Employee.filter((em) => em.Checked).map((emp) => {
                  emp.ID = `${n.ID}-${emp.EmployeeID}`;
                  emp.TreatHandlerID = n.ID;
                  return Object.assign({}, emp);
                });
              });
          });
        });
      });
      /* 通用次卡 */
      that.currentSelectGeneralCardList.forEach((j) => {
        j.Projects.forEach((p) => {
          p.HandleTypeList.forEach((n) => {
            that.treatAllHandlerList
              .filter((i) => i.Name == n.Name)
              .forEach((e) => {
                n.Employee = e.Employee.filter((em) => em.Checked).map((emp) => {
                  emp.ID = `${n.ID}-${emp.EmployeeID}`;
                  emp.TreatHandlerID = n.ID;
                  return Object.assign({}, emp);
                });
              });
          });
        });
      });
      /* 套餐卡 */
      that.currentselectPackageCardList.forEach((pack) => {
        pack.packageProductList.forEach((j) => {
          j.HandleTypeList.forEach((n) => {
            that.treatAllHandlerList
              .filter((i) => i.Name == n.Name)
              .forEach((e) => {
                n.Employee = e.Employee.filter((em) => em.Checked).map((emp) => {
                  emp.ID = `${n.ID}-${emp.EmployeeID}`;
                  emp.TreatHandlerID = n.ID;
                  return Object.assign({}, emp);
                });
              });
          });
        });
        pack.packageProjectList.forEach((j) => {
          j.HandleTypeList.forEach((n) => {
            that.treatAllHandlerList
              .filter((i) => i.Name == n.Name)
              .forEach((e) => {
                n.Employee = e.Employee.filter((em) => em.Checked).map((emp) => {
                  emp.ID = `${n.ID}-${emp.EmployeeID}`;
                  emp.TreatHandlerID = n.ID;
                  return Object.assign({}, emp);
                });
              });
          });
        });
        pack.packageGeneralList.forEach((j) => {
          j.Projects.forEach((p) => {
            p.HandleTypeList.forEach((n) => {
              that.treatAllHandlerList
                .filter((i) => i.Name == n.Name)
                .forEach((e) => {
                  n.Employee = e.Employee.filter((em) => em.Checked).map((emp) => {
                    emp.ID = `${n.ID}-${emp.EmployeeID}`;
                    emp.TreatHandlerID = n.ID;
                    return Object.assign({}, emp);
                  });
                });
            });
          });
        });
        pack.packageTimeCardList.forEach((j) => {
          j.Projects.forEach((p) => {
            p.HandleTypeList.forEach((n) => {
              that.treatAllHandlerList
                .filter((i) => i.Name == n.Name)
                .forEach((e) => {
                  n.Employee = e.Employee.filter((em) => em.Checked).map((emp) => {
                    emp.ID = `${n.ID}-${emp.EmployeeID}`;
                    emp.TreatHandlerID = n.ID;
                    return Object.assign({}, emp);
                  });
                });
            });
          });
        });
        pack.packageSavingCardList.forEach((j) => {
          j.Projects.forEach((p) => {
            p.HandleTypeList.forEach((n) => {
              that.treatAllHandlerList
                .filter((i) => i.Name == n.Name)
                .forEach((e) => {
                  n.Employee = e.Employee.filter((em) => em.Checked).map((emp) => {
                    emp.ID = `${n.ID}-${emp.EmployeeID}`;
                    emp.TreatHandlerID = n.ID;
                    return Object.assign({}, emp);
                  });
                });
            });
          });
        });
      });

      that.dialogVisibleAllHandler = false;
      that.showRemark = false;
      that.saleAllHandlerList = [];
      that.treatAllHandlerList = [];
    },

    /**  修改经手人类型 销售、消耗  */
    hangeAllHandleType() {
      var that = this;
      that.tabAllHandle = '0';
    },
    /**    */
    showSelectAllHandlerClick() {
      let that = this;

      let GoodTypes = [];
      if (that.currentSelectProductList && that.currentSelectProductList.length > 0) {
        GoodTypes.push('10');
      }
      if (
        (that.currentSelectProjectList && that.currentSelectProjectList.length > 0) ||
        (that.currentSelectGeneralCardList && that.currentSelectGeneralCardList.length > 0) ||
        (that.currentSelectTimeCardList && that.currentSelectTimeCardList.length > 0)
      ) {
        GoodTypes.push('20');
      }
      if (that.currentSelectSavingCardList && that.currentSelectSavingCardList.length > 0) {
        GoodTypes.push('50');
      }

      that.currentselectPackageCardList.forEach((i) => {
        if (i.packageProductList && i.packageProductList.length > 0 && GoodTypes.findIndex((j) => j == '10') == -1) {
          GoodTypes.push('10');
        }

        if (
          ((i.packageProjectList && i.packageProjectList.length > 0) ||
            (i.packageGeneralList && i.packageGeneralList.length > 0) ||
            (i.packageTimeCardList && i.packageTimeCardList.length > 0)) &&
          GoodTypes.findIndex((j) => j == '20') == -1
        ) {
          GoodTypes.push('20');
        }
        if (i.packageSavingCardList && i.packageSavingCardList.length > 0 && GoodTypes.findIndex((j) => j == '50') == -1) {
          GoodTypes.push('50');
        }
      });
      that.tabAllHandle = '0';
      that.treatHandler_allHandler(GoodTypes);
      that.dialogVisibleAllHandler = true;
    },
    /**    */
    hiddenInfoRemarkClick() {
      let that = this;
      that.showRemark = false;
    },
    /**    */
    showRemarkClick() {
      let that = this;
      that.showRemark = true;
    },
    /**  删除耗材  */
    removeTreatConsumableClick(conIndex, item) {
      item.Consumable.splice(conIndex, 1);
    },
    /**  删除所有耗材  */
    removeAllTreatConsumableClick(conIndex, item) {
      item.Consumable = [];
    },
    /**  添加耗材  */
    addTreatConsumableClick(event, item) {
      let that = this;
      let cur = item.Consumable ? item.Consumable : [];
      let temp = [...cur, ...event];

      that.$set(item, 'Consumable', temp);
    },
    addProjecConsumable(index) {
      this.$refs[`${index}`][0].addConsumableClick();
    },

    /**   修改门店 */
    changeEntity(event, item) {
      let that = this;
      let tmp = item.Consumable[event.index];
      tmp.StockQuantity = event.StockQuantity;
      that.$set(item.Consumable, event.index, tmp);
    },
    /**  查看账号操作记录  */
    checkAccountOperationRecord(item, type) {
      let that = this;
      that.RecordDialog = true;
      that.AccountID = type == 'SavingCard' ? item.AccountID : item.ID;
      that.Type = type;
    },
    /**    */
    treatTakeOrder(data) {
      let that = this;
      // that.treatTakeOrderData = data;
      that.remark = data.Remark;
      that.$nextTick(() => {
        that.TakeOrderNum = data.ID;
        let Content = JSON.parse(data.Content);
        /**  项目  */
        that.currentSelectProjectList = Content.currentSelectProjectList;
        /**  产品  */
        that.currentSelectProductList = Content.currentSelectProductList;
        /** 储值卡   */
        that.currentSelectSavingCardList = Content.currentSelectSavingCardList;
        /**  时效卡   */
        that.currentSelectTimeCardList = Content.currentSelectTimeCardList;
        /**  通用次卡  */
        that.currentSelectGeneralCardList = Content.currentSelectGeneralCardList;
        /**  套餐卡  */
        that.currentselectPackageCardList = Content.currentselectPackageCardList;
      });
    },
    /**    */
    getTreatTakeOrderHandle(TreatTypeHandlers, TreatBillHandler) {
      let tempHandle = TreatTypeHandlers.map((item) => {
        let selItem = TreatBillHandler.find((selItem) => selItem.TreatHandlerID == item.ID, {});
        let tempEmployees = [];
        if (selItem != undefined) {
          tempEmployees = selItem.Employee.map((emp) => {
            // 取出 经手人信息
            let tempEmp = item.Employee.find((selEmp) => selEmp.EmployeeID == emp.EmployeeID);
            // 有
            if (tempEmp != undefined) {
              return {
                Checked: true,
                Discount: emp.Scale,
                EmployeeID: tempEmp.EmployeeID,
                EmployeeName: tempEmp.EmployeeName,
                ID: tempEmp.ID,
                JobTypeName: tempEmp.JobTypeName,
                TreatHandlerID: tempEmp.TreatHandlerID,
              };
            } else {
              // 无
              return {
                EmployeeID: emp.EmployeeID,
                EmployeeName: emp.EmployeeName,
                Scale: emp.Scale,
              };
            }
          });
        }

        let temp = {
          Name: item.Name,
          ID: item.ID,
          Employee: tempEmployees,
        };
        return temp;
      });

      return tempHandle;
    },
    //清空按钮触发s
    clearAndSearch() {
      this.searchSavingCardProjectClick();
    },
    //时效卡
    clearAndTimeSearch() {
      this.searchTimeCardProjectClick();
    },
    clearAndGenterSearch() {
      this.searchGeneralCardProjectClick();
    },
    /**  调用 请求网络 数据 */
    changMemberOrType: function () {
      var that = this;
      if (that.customerID != null && that.getBillDate() != null) {
        that.tabPane = '';
        that.TakeOrderNum = '';
        /**  存量信息  */
        that.searchGoodsClick();
      } else {
        that.clearConsumeNetWorkData();
      }
    },
    /**  会员清除时 清除上一个顾客的存量  */
    clearConsumeNetWorkData() {
      var that = this;
      that.tabPane = '';
      that.treatProjectAccountList = [];
      that.treatProductAccountList = [];
      that.treatSavingCardAccountList = [];
      that.treatSavingCardAccountProjectList = [];
      that.treatSavingCardAccountProjectList_i = [];
      that.treatGoodsPackageCardAccountList = [];
      that.treatGoodsPackageCardAccountDetailsList = {};
      that.treatGeneralCardAccountList = [];
      that.treatTimeCardAccountList = [];

      that.currentSelectProjectList = [];
      that.currentSelectProductList = [];
      that.currentSelectSavingCardList = [];
      that.currentSelectTimeCardList = [];
      that.currentSelectGeneralCardList = [];
      that.currentselectPackageCardList = [];
    },
    /** 修改时间清除 已选项   */
    clearConsumeSelectItem() {
      let that = this;
      // that.currentSelectProjectList = [];
      // that.currentSelectProductList = [];
      that.currentSelectSavingCardList = [];
      that.currentSelectTimeCardList = [];
      that.currentSelectGeneralCardList = [];
      that.currentselectPackageCardList = [];
    },
    /**  点击switch切换  将搜索内容 置空 */
    tabClick: function () {
      var that = this;
      that.searchName = '';
    },

    /**======================================================================  */
    /**  项目消耗  */
    consumeProjectClick(item) {
      var that = this;
      if (item.ValidBalance <= that.getCurrentProjectQuantity(item.ID)) {
        that.$message.error({
          message: '有效次数不足',
          duration: 2000,
        });
        return;
      }

      var tempHandle = [];
      Enumerable.from(that.projectTreatHandlers).forEach((item) => {
        tempHandle.push({
          Name: item.Name,
          ID: item.ID,
          Employee: [],
        });
      });

      that.currentSelectProjectList.push({
        ID: item.ID,
        Name: item.Name,
        Alias: item.Alias,
        ValidBalance: item.ValidBalance,
        ProjectID: item.ProjectID,
        Quantity: 1,
        Price: item.Price,
        UnitAmount: item.Amount,
        TotalAmount: item.Amount,
        IsLargess: item.IsLargess,
        Consumable: [],
        ProjectTreatHandler: [],
        HandleTypeList: tempHandle,
      });
    },

    projectItemQuantityChangeClick(item) {
      item.TotalAmount = parseFloat(item.UnitAmount * item.Quantity).toFixed(2);
    },

    getCurrentProjectQuantity: function (itemID) {
      return Enumerable.from(this.currentSelectProjectList)
        .where((i) => i.ID == itemID)
        .sum('$.Quantity');
    },

    removeProjectSelectItemClick(index) {
      this.currentSelectProjectList.splice(index, 1);
    },

    /**======================================================================  */
    /**  产品  */
    consumeProductClick(item) {
      var that = this;
      if (item.ValidBalance <= that.getCurrentProductQuantity(item.ID)) {
        that.$message.error({
          message: '有效次数不足',
          duration: 2000,
        });
        return;
      }
      var tempHandle = [];
      that.productTreatHandlers.forEach((item) => {
        tempHandle.push({
          Name: item.Name,
          ID: item.ID,
          Employee: [],
        });
      });

      that.currentSelectProductList.push({
        Name: item.Name,
        Alias: item.Alias,
        ValidBalance: item.ValidBalance,
        ID: item.ID,
        ProductID: item.ProductID,
        Quantity: 1,
        Price: item.Price,
        UnitAmount: item.Amount,
        TotalAmount: item.Amount,
        IsLargess: item.IsLargess,
        ProductTreatHandler: [],
        HandleTypeList: tempHandle,
      });
    },

    productItemQuantityChangeClick(item) {
      item.TotalAmount = item.UnitAmount * item.Quantity;
    },

    getCurrentProductQuantity: function (itemID) {
      return Enumerable.from(this.currentSelectProductList)
        .where((i) => i.ID == itemID)
        .sum('$.Quantity');
    },

    removeSelectProductItemClick(index) {
      this.currentSelectProductList.splice(index, 1);
    },
    /**======================================================================  */
    /**  储值卡弹窗  */
    /**  搜索 储值卡使用项目  */
    searchSavingCardProjectClick: function () {
      var that = this;
      that.currentSavingcardProjectCategoty = [];
      that.currentSavingcardProjectSecondCategoty = [];
      that.currentSavingcardProject = [];
      that.savingcardProjectSecondCategotyIndex = 0;
      that.savingcardProjectCategotyIndex = 0;
      that.savingCardPaginations.page = 1;
      that.treatGoodsAccount_savingCardAccountProjectCategory();
    },
    /**  修改储值卡适用项目 修改分页  */
    handleSavingProjectCurrentChange(page) {
      let that = this;
      that.savingCardPaginations.page = page;
      that.treatGoodsAccount_savingCardAccountProjectByCategory();
    },
    /** 储值卡使用项目 点击分类   */
    savingCardProjectCategoryClick(item, index) {
      var that = this;
      that.savingcardProjectCategotyIndex = index;
      that.savingcardProjectSecondCategotyIndex = 0;
      that.savingCardPaginations.page = 1;
      that.currentSavingcardProjectSecondCategoty = item.Child;
      that.selectSavingCardCategoryItem = that.currentSavingcardProjectSecondCategoty[that.savingcardProjectSecondCategotyIndex];
      that.treatGoodsAccount_savingCardAccountProjectByCategory();
    },
    /**  储值卡 使用项目点击二级分类  */
    savingCardProjectSecondCategoryClick(item, index) {
      let that = this;
      that.savingcardProjectSecondCategotyIndex = index;
      that.savingCardPaginations.page = 1;
      that.selectSavingCardCategoryItem = item;
      that.treatGoodsAccount_savingCardAccountProjectByCategory();
    },
    /** 添加储值卡消耗项目   */
    addSavingCardApplyProjectItem(item) {
      this.treatGoodsAccount_savingCardAccountProjectCustomerDiscount(item.ID).then(() => {
        if (this.isConsumePackage) {
          this.addPackageItemSavingCardProject(item);
        } else {
          this.addSavingCardProject(item);
        }
      });
    },

    /**======================================================================  */
    /**  储值卡  */
    /**  点击储值卡消耗  */
    consumeSavingCardClick(item) {
      var that = this;
      that.isConsumePackage = false;
      if (item.ValidBalance == 0) {
        that.$message.error({
          message: '该储值卡有效余额不足，请选择其他储值卡',
          duration: 2000,
        });
        return;
      }
      that.selectSavingcardItem = item;
      that.currentSavingcardProjectCategoty = [];
      that.currentSavingcardProject = [];
      that.searchSavingcardProjectName = '';
      that.savingCardConsumeItem = null;
      that.savingCardConsumeItem = Enumerable.from(that.currentSelectSavingCardList).firstOrDefault((i) => i.ID == item.ID);
      that.treatGoodsAccount_savingCardAccountProjectCategory(true);
    },
    /* 计算会员优惠后的卡优惠 */
    getSavingCardPreferentialAmount() {},
    /** 储值卡添加项目   */
    addSavingCardProject(item) {
      var that = this;
      var consumeAmount = that.getCurrentSavingCardConsumeAmount(that.savingCardConsumeItem, that.isConsumePackage); //将要消耗的金额
      var ValidBalance = that.selectSavingcardItem.ValidBalance; // 当前储值卡的有效余额
      if (Number(ValidBalance) == Number(consumeAmount)) {
        that.$message.error({
          message: '储值卡余额不足。',
          duration: 2000,
        });
        return;
      }
      let isMemberDiscount = false;

      let memberAmount = item.Price;
      let MemberPreferentialAmount = 0;
      let tempMemberAmount = memberAmount;
      
      if (that.discountData) { // 存在会员优惠
        isMemberDiscount = true;
        if (that.discountData.PriceType == 1) {
          memberAmount = parseFloat(item.Price * that.discountData.DiscountPrice).toFixed(2);
          MemberPreferentialAmount = item.Price - memberAmount;
          tempMemberAmount = memberAmount;
        }

        if (this.discountData.PriceType == 2) {
          memberAmount = parseFloat(this.discountData.DiscountPrice).toFixed(2);
          MemberPreferentialAmount = item.Price - memberAmount;
          tempMemberAmount = memberAmount;
        }
      }
      // 卡优惠
      // 折扣
      if (item.PriceType == 1) {
        item.PreferentialPrice = parseFloat(memberAmount * item.DiscountPrice).toFixed(2);
        if(parseFloat(memberAmount) > parseFloat(item.PreferentialPrice)){
          item.CardPreferentialAmount = parseFloat(memberAmount - item.PreferentialPrice).toFixed(2);
        }
        else{
          item.CardPreferentialAmount =0; 
        }
        
        tempMemberAmount = parseFloat(item.PreferentialPrice) > parseFloat(tempMemberAmount) ? tempMemberAmount:item.PreferentialPrice;
      }
      // 金额
      if (item.PriceType == 2) {
        item.PreferentialPrice = parseFloat(item.DiscountPrice).toFixed(2);
        if(parseFloat(memberAmount) > parseFloat(item.PreferentialPrice)){
          item.CardPreferentialAmount = parseFloat(memberAmount - item.PreferentialPrice).toFixed(2);
        }
        else{
          item.CardPreferentialAmount =0; 
        }
        tempMemberAmount = parseFloat(item.PreferentialPrice) > parseFloat(tempMemberAmount) ? tempMemberAmount:item.PreferentialPrice;
      }

      var tempConsumeAmount = Number(consumeAmount) + Number(tempMemberAmount);
      /**  判断 将要消耗的金额是否大于储值卡的有效余额  */
      if (tempConsumeAmount > ValidBalance) {
        if (that.TreatPermission.ModifyPrices_TreatSavingCard || that.TreatPermission.isTreatBillingModifyPrices_TreatSavingCard) {
          var tempPrice = ValidBalance - consumeAmount;
          //  var tempPrice_1 = ValidBalance_1 - consumeAmount_1
          let changeDiscount = tempPrice / item.Price;
          if (that.employeeDiscount > changeDiscount) {
            that.$message.error({
              // message: '改价后折扣不能大于员工最低折扣',
              message: '储值卡余额不足。不可改价',
              duration: 2000,
            });
            return;
          }
          var tempStr =
            '卡剩余' +
            tempPrice.toFixed(2) +
            ' 元，该项目需要消耗' +
            Number(tempMemberAmount).toFixed(2) +
            '元，如果需要继续操作，此项目的耗卡金额将修改为 ' +
            tempPrice.toFixed(2) +
            ' 元';
          that
            .$confirm(tempStr, {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning',
            })
            .then(() => {
              let Performance = {
                PerformanceRatio: parseFloat((tempPrice / tempMemberAmount) * 100).toFixed(2),
                PricePreferentialAmount: parseFloat(tempMemberAmount - tempPrice).toFixed(2),
              };
              that.judgeSavingCardConsumeProject(item, Number(tempPrice).toFixed(2), Performance, MemberPreferentialAmount, isMemberDiscount);
              that.dialogSavingCard = false;
            })
            .catch(() => {
              this.$message({
                type: 'info',
                message: '已取消操作',
              });
            });
        } else {
          that.$message.error({
            message: '卡剩余金额不足，请更换其他卡',
            duration: 2000,
          });
        }
      } else {
        let Performance = {
          PerformanceRatio: 100,
          PricePreferentialAmount: 0,
        };
        that.judgeSavingCardConsumeProject(item, tempMemberAmount, Performance, MemberPreferentialAmount, isMemberDiscount);
        that.dialogSavingCard = false;
      }
        
    },
    /**  添加消耗项目   */
    judgeSavingCardConsumeProject(item, itemAmount, Performance, MemberPreferentialAmount, isMemberDiscount) {
      var that = this;
      /**  消耗项目  */
      var project = {
        Name: item.Name,
        Alias: item.Alias,
        ID: item.ID,
        Price: item.Price,
        Quantity: 1,
        UnitAmount: itemAmount,
        TotalAmount: itemAmount,
        PreferentialPrice: itemAmount,
        PopoveVisible: false,
        PopoveAmount: itemAmount,
        PerformanceRatio: Performance.PerformanceRatio,
        PricePreferentialAmount: Number(Performance.PricePreferentialAmount).toFixed(2), //手动改价优惠
        CardPreferentialAmount: Number(item.CardPreferentialAmount).toFixed(2), //卡优惠价格
        CardPreferentialAmountTotal: Number(item.CardPreferentialAmount).toFixed(2), //卡优惠价格
        SavingCardTreatHandler: [],
        HandleTypeList: [],
        Consumable: [],
        MemberPreferentialAmount: MemberPreferentialAmount,
        MemberPreferentialAmountTotal: MemberPreferentialAmount,
        MemberDiscountData: this.discountData,
        isMemberDiscount: isMemberDiscount,
        CardPriceType: item.PriceType,
        CardDiscountPrice: item.DiscountPrice,
      };
      /**  经手人  */
      that.savingCardTreatHandlers.forEach((item) => {
        project.HandleTypeList.push({
          Name: item.Name,
          ID: item.ID,
          Employee: [],
        });
      });
      /**  将要消耗的储值卡  */
      if (JSON.stringify(that.savingCardConsumeItem) == '{}' || that.savingCardConsumeItem == null || typeof that.savingCardConsumeItem == 'undefined') {
        that.savingCardConsumeItem = {
          ID: that.selectSavingcardItem.ID,
          AccountID: that.selectSavingcardItem.AccountID,
          SavingCardID: that.selectSavingcardItem.SavingCardID,
          Name: that.selectSavingcardItem.Name,
          Alias: that.selectSavingcardItem.Alias,
          Type: that.selectSavingcardItem.Type,
          ValidBalance: that.selectSavingcardItem.ValidBalance,
          SavingCardItem: that.selectSavingcardItem,
          Projects: [project],
        };
        that.currentSelectSavingCardList.push(that.savingCardConsumeItem);
      } else {
        that.savingCardConsumeItem.Projects.push(project);
      }
    },
    /**  计算 储值卡将要消耗金额  */
    getCurrentSavingCardConsumeAmount: function (savingCard) {
      if (!savingCard) return 0;
      return Enumerable.from(savingCard.Projects).sum((i) => Number(i.TotalAmount));
    },
    /**  修改储值卡消耗项目的数量  */
    changeSavingCardConsumeProject(currentValue, oldValue, item, project) {
      var that = this;
      var consumeAmount = that.getCurrentSavingCardConsumeAmount(item); //  当前 消耗金额
      if (Number(item.ValidBalance) < Number(consumeAmount) - Number(project.TotalAmount) + Number(project.PreferentialPrice * project.Quantity)) {
        that.$message.error({
          message: '当前储值卡余额不足',
          duration: 2000,
        });
        that.$nextTick(() => {
          project.Quantity = oldValue;
        });
        return;
      }

      var curAmount = project.Quantity * project.PreferentialPrice + consumeAmount - project.PreferentialPrice * oldValue;

      if (curAmount > item.ValidBalance) {
        var MaxQuantity = Math.floor((item.ValidBalance - consumeAmount + project.TotalAmount) / project.PreferentialPrice);
        that.$nextTick(() => {
          project.Quantity = MaxQuantity;
          project.TotalAmount = Number(parseFloat(project.PreferentialPrice * project.Quantity).toFixed(2));
          project.CardPreferentialAmountTotal = parseFloat(project.CardPreferentialAmount * project.Quantity).toFixed(2);
          project.MemberPreferentialAmountTotal = parseFloat(project.MemberPreferentialAmount * project.Quantity).toFixed(2);
          project.PopoveAmount = project.TotalAmount;
          project.PerformanceRatio = 100;
          project.PricePreferentialAmount = 0;
        });
        that.$message.error({
          message: '该储值卡有效余额不足。',
          duration: 2000,
        });
        return;
      } else {
        // isMemberDiscount   是否使用会员优惠
        project.TotalAmount = Number(parseFloat(Number(project.PreferentialPrice) * Number(project.Quantity)).toFixed(2));
        project.CardPreferentialAmountTotal = parseFloat(project.CardPreferentialAmount * project.Quantity).toFixed(2); //卡优惠
        if (project.isMemberDiscount) {
          project.MemberPreferentialAmountTotal = parseFloat(project.MemberPreferentialAmount * project.Quantity).toFixed(2); //会员优惠
        }
        else {
          project.MemberPreferentialAmountTotal = 0;
        }
        project.PopoveAmount = project.TotalAmount;
        project.PerformanceRatio = 100;
        project.PricePreferentialAmount = 0;
      }
    },
    /**  储值卡删除事件  */
    removeSavingCardSelectItemClick(index, pIndex) {
      var that = this;
      that.currentSelectSavingCardList[index].Projects.splice(pIndex, 1);
      if (that.currentSelectSavingCardList[index].Projects.length <= 0) {
        that.currentSelectSavingCardList.splice(index, 1);
      }
    },
    /**  储值卡手动改价 输入框事件  */
    savingCardPopInputChange(item, type) {
      switch (type) {
        case 0:
          var temp = (item.PopoveAmount / (item.PreferentialPrice * item.Quantity)) * 100;
          item.PerformanceRatio = Number(temp.toFixed(2));
          break;
        case 1:
          var temp_1 = (item.PreferentialPrice * item.Quantity * item.PerformanceRatio) / 100;
          item.PopoveAmount = Number(temp_1).toFixed(2);
          break;
        default:
          break;
      }
    },
    /**    */
    showPopover(item) {
      item.PopoveAmount = Number(item.TotalAmount).toFixed(2);
      let PreferentialAmount = parseFloat(item.Price * item.Quantity - item.CardPreferentialAmountTotal).toFixed(2);
      console.log("🚀 ~ showPopover ~ PreferentialAmount:", PreferentialAmount)
      let temp = (item.TotalAmount / PreferentialAmount) * 100;
      item.PerformanceRatio = Number(temp).toFixed(2);
    },
    /* 修改储值卡是否使用会员折扣 */
    changeSavingCardIsMemberDiscount(project, item) {
      let that = this;
      let PreferentialPrice = project.PreferentialPrice;
      if (project.isMemberDiscount) {
        let Amount = project.Price  * project.Quantity;
        project.MemberPreferentialAmountTotal = project.MemberPreferentialAmount * project.Quantity;
        Amount = Amount - project.MemberPreferentialAmountTotal;
        
        PreferentialPrice = project.Price - project.MemberPreferentialAmount;
        
        if (project.CardPriceType == 1) {
          Amount = Amount * project.CardDiscountPrice;
          PreferentialPrice = PreferentialPrice * project.CardDiscountPrice;
        }
        if (project.CardPriceType == 2) {
          let CardDiscountPrice = project.CardDiscountPrice * project.Quantity;
          Amount = CardDiscountPrice > Amount ? Amount : CardDiscountPrice;
          PreferentialPrice =  project.CardDiscountPrice >PreferentialPrice ? PreferentialPrice : project.CardDiscountPrice;
        }
        
        project.PreferentialPrice = PreferentialPrice;
        project.TotalAmount = Amount ;
        project.PopoveAmount = project.TotalAmount;
        project.UnitAmount = Amount;
        
        project.CardPreferentialAmount =  (project.Price * project.Quantity - Amount - project.MemberPreferentialAmountTotal) / project.Quantity;
        project.CardPreferentialAmountTotal = project.CardPreferentialAmount * project.Quantity;
        
        project.PricePreferentialAmount = 0;
      } else {
        let Amount = project.Price;
        if (project.CardPriceType == 1) {
          Amount = project.Price * project.CardDiscountPrice;
        }
        if (project.CardPriceType == 2) {
          Amount = project.CardDiscountPrice ;
        }
        let ValidBalance = item.SavingCardItem.ValidBalance;
        if ((Amount * project.Quantity) > ValidBalance) {
          let discount = (Number(ValidBalance) / Number(Amount) * 100).toFixed(2);
          if (that.employeeDiscount > discount) {
            that.$message.error({
              message: '储值卡余额不足。不可改价',
              duration: 2000,
            });
            project.isMemberDiscount = true;
            return;
          }
          else {

            project.PreferentialPrice = ValidBalance;
            project.TotalAmount = ValidBalance;
            project.PopoveAmount = project.TotalAmount;
            project.PerformanceRatio = discount;
            project.UnitAmount = Amount;
            project.MemberPreferentialAmountTotal = 0;
            project.CardPreferentialAmount = project.Price * project.Quantity - Amount;
          }
        }
        else {
          project.PreferentialPrice = Amount;
          project.TotalAmount = Amount * project.Quantity ;
          project.PopoveAmount = project.TotalAmount;
          project.PerformanceRatio = 100;
          project.UnitAmount = Amount;
          project.MemberPreferentialAmountTotal = 0;
          project.CardPreferentialAmount = (project.Price - Amount) * project.Quantity ;
          project.CardPreferentialAmountTotal = project.CardPreferentialAmount ;
        }
      }
    },
    /**    */
    savingCardPopoverClickConfirm(item, savingItem) {
      console.log("🚀 ~ savingCardPopoverClickConfirm ~ item:", item)
      var that = this;
      if (!item.isMemberDiscount) {
        let CardAmount = item.Price;
        if (item.CardPriceType == 1) {
          CardAmount = item.Price * item.Quantity * item.CardDiscountPrice;
        }
        if (item.CardPriceType == 2) {
          CardAmount = item.CardDiscountPrice * item.Quantity;
        }
        let discountPrice = parseFloat(CardAmount * that.employeeDiscount).toFixed(2);
        let changeDiscount = parseFloat(item.PopoveAmount / (CardAmount)).toFixed(2);
        console.log("🚀 ~ savingCardPopoverClickConfirm ~ changeDiscount:", changeDiscount)
        if (that.employeeDiscount > changeDiscount) {
          that.$message.error({
            message: `当前员工最大折扣为${that.employeeDiscount * 100}%`,
            duration: 2000,
          });

          item.PerformanceRatio = parseFloat(that.employeeDiscount * 100).toFixed(2);
          item.PopoveAmount = discountPrice;
          return;
        }

        item.PerformanceRatio = parseFloat(changeDiscount * 100).toFixed(2);
        // item.PreferentialPrice = discountPrice * item.Quantity;

        var currentAmount = Number(that.getCurrentSavingCardConsumeAmount(savingItem)) - Number(item.TotalAmount) + Number(item.PopoveAmount);
        if (currentAmount > savingItem.ValidBalance) {
          that.$message.error({
            message: '储值卡有效余额不足,请修改消耗金额',
            duration: 2000,
          });
          return;
        }
        var temp = Number(CardAmount) - Number(item.PopoveAmount);
        item.PricePreferentialAmount = Number(temp).toFixed(2);
        item.PopoveVisible = false;
        item.TotalAmount = Number(item.PopoveAmount).toFixed(2);
        // item.PreferentialPrice = item.TotalAmount;
      } else {
        item.PopoveVisible = false;
      }
    },

    /**======================================================================  */
    /**  搜索 时效卡使用项目  */
    searchTimeCardProjectClick: function () {
      var that = this;
      that.currentTimeCardProject = [];
      that.currentTimeCardProjectCategoty = [];
      that.currentTimecarProjectSecondCategoty = [];
      that.timeCardPaginations.page = 1;
      that.treatGoodsAccount_timeCardAccountProjectCategory();
    },
    /**    */
    handleTimeCardProjectCurrentChange(page) {
      let that = this;
      that.timeCardPaginations.page = page;
      that.treatGoodsAccount_timeCardAccountProjectByCategory();
    },
    /**  时效卡 适用项目分类点击  */
    timeCardProjectCategoryClick(item, index) {
      var that = this;
      that.timeCardProjectCategotyIndex = index;
      that.timeCardProjectSecondCategotyIndex = 0;
      that.generalCardPaginations.page = 1;
      that.currentTimecarProjectSecondCategoty = item.Child;
      that.selectTimeCardCategoryItem = that.currentTimecarProjectSecondCategoty[that.timeCardProjectSecondCategotyIndex];
      that.treatGoodsAccount_timeCardAccountProjectByCategory();
    },
    /**    */
    timeCardProjectSecndCategoryClick(item, index) {
      let that = this;
      that.timeCardProjectSecondCategotyIndex = index;
      that.generalCardPaginations.page = 1;
      that.selectTimeCardCategoryItem = item;
      that.treatGoodsAccount_timeCardAccountProjectByCategory();
    },
    /** 添加时效卡 适用项目   */
    addTimeCardApplyProjectItem(item) {
      var that = this;

      if (that.isConsumePackage) {
        that.addPackageTimeCardProjectClick(item);
      } else {
        that.addTimeCardProjectCliak(item);
      }
    },

    /**======================================================================  */
    /**  时效卡消耗  */
    /** 时效卡存量点击   */
    consumeTimeCardClick(item) {
      var that = this;
      if (item.IsRefund) return;
      that.isConsumePackage = false;
      if (item.ValidBalance == 0 && item.ArrearAmount > 0) {
        that.$message.error({
          message: '此时效卡有欠款，有效次数不足',
          duration: 2000,
        });
        return;
      }
      if (item.ConsumeCycle == 0 || item.CycleLimitAmount == 0) {
        /**  如果 消耗周期 或者 周期次数 为零 则消耗不做限制  */
        that.selectTimecardItem = item;
        that.currentTimeCardProject = [];
        that.currentTimeCardProjectCategoty = [];
        that.currentTimecarProjectSecondCategoty = [];
        that.searchTimeCardPorjectName = '';
        that.generalCardPaginations.page = 1;
        that.treatGoodsAccount_timeCardAccountProjectCategory(true);
      } else {
        var ValidBalance = item.CycleLimitAmount - item.ConsumeCycleAmount; //周期次数 减去周期内已耗用次数 等于有效次数
        if (ValidBalance <= 0) {
          that.$message.error({
            message: '周期内消耗次数不足',
            duration: 2000,
          });
          return;
        }
        that.selectTimecardItem = item;
        that.currentTimeCardProject = [];
        that.currentTimeCardProjectCategoty = [];
        that.currentTimecarProjectSecondCategoty = [];
        that.searchTimeCardPorjectName = '';
        that.generalCardPaginations.page = 1;
        that.treatGoodsAccount_timeCardAccountProjectCategory(true);
      }
    },

    /** 时效卡   */
    addTimeCardProjectCliak(item) {
      var that = this;
      /**  周期内有效的耗用次数  */
      var CycleValidBalance = that.selectTimecardItem.CycleLimitAmount - that.selectTimecardItem.ConsumeCycleAmount;
      /**  将要耗用的次数  */
      var curAmount = that.getTimeCardQuantity(that.selectTimecardItem.ID);

      /**  如果 消耗周期 与周期次数任意为 0 切在不欠款的情况下不限制消耗次数  */
      if (that.selectTimecardItem.CycleLimitAmount != 0 && that.selectTimecardItem.ConsumeCycle != 0) {
        if (curAmount >= CycleValidBalance) {
          /**  判断周期次数  */
          that.$message.error({
            message: '周期内有效次数不足',
            duration: 2000,
          });
          return;
        }
      }

      /**   如果时效卡有欠款 则根据有效次数限制消耗次数 */
      if (that.selectTimecardItem.ArrearAmount > 0) {
        //有欠款
        if (curAmount >= that.selectTimecardItem.ValidBalance) {
          /**  当前消耗次数是否大于 有效次数  */

          that.$message.error({
            message: '当前时效卡存在欠款，累计消耗次数不能超过' + that.selectTimecardItem.ValidBalance + '次',
            duration: 2000,
          });
          return;
        }
      }
      /**  当前业绩金额  */
      var currentTimeAmount = that.selectTimecardItem.Amount;
      /**  如果 消耗次数大于 等于 剩余业绩次数 则消耗金额为零  */
      if (curAmount >= that.selectTimecardItem.PerformanceBalance) {
        currentTimeAmount = 0;
      }

      var tempHandle = [];
      Enumerable.from(that.projectTreatHandlers).forEach((item) => {
        tempHandle.push({
          Name: item.Name,
          ID: item.ID,
          Employee: [],
        });
      });

      var Project = {
        Name: item.Name,
        Alias: item.Alias,
        ID: item.ID,
        Quantity: 1, //数量
        IsLargess: that.selectTimecardItem.IsLargess,
        Price: item.Price, // 项目单价
        UnitAmount: that.selectTimecardItem.Amount, //  消耗参考单价
        TotalAmount: currentTimeAmount, // 消耗 业绩金额
        TimeCardTreatHandler: [],
        HandleTypeList: tempHandle,
        Consumable: [],
      };

      /**  取出时效卡  */
      var timeCardItem = Enumerable.from(that.currentSelectTimeCardList).firstOrDefault((i) => i.ID == that.selectTimecardItem.ID);
      /**  判断时效卡是否存在  */
      if (typeof timeCardItem == 'undefined') {
        that.currentSelectTimeCardList.push({
          ID: that.selectTimecardItem.ID,
          TimeCardID: that.selectTimecardItem.TimeCardID,
          Name: that.selectTimecardItem.Name,
          Alias: that.selectTimecardItem.Alias,
          IsLargess: that.selectTimecardItem.IsLargess,
          ArrearAmount: that.selectTimecardItem.ArrearAmount,
          Amount: that.selectTimecardItem.Amount,
          UnitAmount: that.selectTimecardItem.Amount,
          CycleLimitAmount: that.selectTimecardItem.CycleLimitAmount, // 周期内次数
          ConsumeCycle: that.selectTimecardItem.ConsumeCycle,
          ConsumeCycleAmount: that.selectTimecardItem.ConsumeCycleAmount, // 周期内已消耗次数
          ValidBalance: that.selectTimecardItem.ValidBalance, //周期内有效次数
          PerformanceTimes: that.selectTimecardItem.PerformanceTimes, // 总业绩次数
          PerformanceBalance: that.selectTimecardItem.PerformanceBalance, // 剩余业绩次数
          TimeCardItem: that.selectTimecardItem,
          Projects: [Project],
        });
      } else {
        timeCardItem.Projects.push(Project);
      }
      that.dialogTimeCard = false;
    },
    /** 获取 时效卡的最大值   */
    getTimeCardMaxQuantity(timerCard) {
      if (timerCard.ArrearAmount > 0) {
        //有欠款的情况
        if (timerCard.CycleLimitAmount != 0 && timerCard.ConsumeCycle != 0) {
          // 周期次数 有限制
          if (timerCard.ValidBalance > Number(timerCard.CycleLimitAmount) - Number(timerCard.ConsumeCycleAmount)) {
            // 判断有效周期次数 是否小于 欠款时的有效次数
            return Number(timerCard.CycleLimitAmount) - Number(timerCard.ConsumeCycleAmount);
          }
        }

        return timerCard.ValidBalance;
      } else {
        if (timerCard.CycleLimitAmount != 0 && timerCard.ConsumeCycle != 0) {
          /**  周期次数 减去 周期内已消耗次数  */
          return Number(timerCard.CycleLimitAmount) - Number(timerCard.ConsumeCycleAmount);
        } else {
          /**  如果没有欠款 且 周期 和周期次数均不为 0 则返回最大值  */
          return Number.MAX_VALUE;
        }
      }
    },
    /**  计算时效卡周期内已选的消耗次数  */
    getTimeCardQuantity: function (timerCardID) {
      if (!timerCardID) return 0;
      var timerCard = Enumerable.from(this.currentSelectTimeCardList).firstOrDefault((i) => i.ID == timerCardID);
      if (typeof timerCard == 'undefined') return 0;
      return Enumerable.from(timerCard.Projects).sum((i) => Number(i.Quantity));
    },
    /**  获取有效业绩次数 排除当前项目 */
    getTimeCardConsumePerformanceAmount(timeCard, Project) {
      if (!timeCard) return 0;
      var ValidBalance =
        Enumerable.from(
          Enumerable.from(timeCard.Projects)
            .where((i) => i != Project)
            .toArray()
        ).sum((i) => Number(i.TotalAmount)) / timeCard.UnitAmount;
      return timeCard.ValidBalance - ValidBalance;
    },
    /**  时效卡修改数量   */
    timeItemQuantityChangeClick(currentValue, oldValue, timeCard, Project) {
      var that = this;
      var curVailAmount = that.getTimeCardConsumePerformanceAmount(timeCard, Project);
      if (curVailAmount > 0) {
        if (Project.Quantity >= curVailAmount) {
          Project.TotalAmount = parseFloat(Project.UnitAmount * curVailAmount).toFixed(2);
        } else {
          Project.TotalAmount = parseFloat(Project.UnitAmount * Project.Quantity).toFixed(2);
          that.timeCardAllocationValidQuantity(timeCard, Project, curVailAmount);
        }
      }
    },

    /**  分配有效余额  */
    timeCardAllocationValidQuantity(timeCard, Project, curVailAmount) {
      var surplusAmount = curVailAmount - Project.TotalAmount / Project.UnitAmount;
      if (timeCard.Projects.length > 1 && surplusAmount > 0) {
        for (let index = 0; index < timeCard.Projects.length; index++) {
          var ocation = timeCard.Projects[index];
          if (ocation != Project) {
            var iPerAmount = ocation.TotalAmount / ocation.UnitAmount; //业绩次数
            if (ocation.Quantity > iPerAmount) {
              var diff = ocation.Quantity - iPerAmount > surplusAmount ? surplusAmount : ocation.Quantity - iPerAmount;
              ocation.TotalAmount = parseFloat(parseFloat(diff * ocation.UnitAmount) + parseFloat(ocation.TotalAmount)).toFixed(2);
              surplusAmount -= diff;
              if (surplusAmount <= 0) {
                return;
              }
            }
          }
        }
      }
    },

    /** 删除 重新分配   */
    timeCardRemoveAllOcationValidQuantity(timeCard) {
      var timeCardTotalAmount = Enumerable.from(timeCard.Projects).sum((i) => Number(i.TotalAmount)) / timeCard.UnitAmount; // 消耗的总金额
      // 剩余的业绩次数
      var diffAmount = timeCard.ValidBalance - timeCardTotalAmount;
      if (diffAmount > 0) {
        for (let index = 0; index < timeCard.Projects.length; index++) {
          var Project = timeCard.Projects[index];
          // 当前项目业绩次数
          var ProjectPerQuan = Project.TotalAmount / Project.UnitAmount;
          // 判断 是否 有非业绩次数
          if (Project.Quantity > ProjectPerQuan) {
            let tempDiff = Project.Quantity - ProjectPerQuan;
            var different = tempDiff > diffAmount ? diffAmount : tempDiff;
            Project.TotalAmount = parseFloat(parseFloat(different * Project.UnitAmount) + parseFloat(Project.TotalAmount)).toFixed(2);
            diffAmount -= different;
            if (diffAmount <= 0) {
              return;
            }
          }
        }
      }
    },

    /**   删除 */
    timeRemoveSelectItemClick(index, ProjectIndex) {
      var that = this;
      that.currentSelectTimeCardList[index].Projects.splice(ProjectIndex, 1);

      if (that.currentSelectTimeCardList[index].Projects.length <= 0) {
        that.currentSelectTimeCardList.splice(index, 1);
      } else {
        var timeCard = that.currentSelectTimeCardList[index];
        that.timeCardRemoveAllOcationValidQuantity(timeCard);
      }
    },

    /**======================================================================  */
    /**  搜索 通用次卡使用项目  */
    searchGeneralCardProjectClick: function () {
      var that = this;
      that.currentGeneralcarProject = [];
      that.currentGeneralcarProjectCategoty = [];
      that.currentGeneralcarProjectSecondCategoty = [];
      that.currentGeneralcarActiveName = [];
      that.generalCardPaginations.page = 1;
      that.treatGoodsAccount_generalCardAccountProjectCategory(false).then(() => {
        that.isShowHistoricalData = false;
        that.generalCardHistoricalData = [];
        that.treatGoodsAccount_generalCardHistoricalData(true);
      });
    },
    /**    */
    handleGeeralCardProjectCurrentChange(page) {
      let that = this;
      that.generalCardPaginations.page = page;
      if (that.isShowHistoricalData && that.generalCardProjectCategotyIndex == 0) {
        that.treatGoodsAccount_generalCardHistoricalData(false);
      } else {
        that.treatGoodsAccount_generalCardAccountProjectByCategory();
      }
    },
    /**  通用次卡 适用项目分类点击  */
    generalCardProjectCategoryClick(item, index) {
      var that = this;
      that.generalCardProjectCategotyIndex = index;
      that.generalCardProjectSecondCategotyIndex = 0;
      that.generalCardPaginations.page = 1;
      that.currentGeneralcarProjectSecondCategoty = item.Child;
      if (!that.isShowHistoricalData) {
        that.selectGenearlCardCategoryItem = that.currentGeneralcarProjectSecondCategoty[that.generalCardProjectSecondCategotyIndex];
      } else {
        if (that.generalCardProjectCategotyIndex != 0) {
          that.selectGenearlCardCategoryItem = that.currentGeneralcarProjectSecondCategoty[that.generalCardProjectSecondCategotyIndex];
        }
      }

      that.treatGoodsAccount_generalCardAccountProjectByCategory();
    },
    /**   二级分类 */
    generalCardProjectSecondCategoryClick(item, index) {
      let that = this;
      that.generalCardProjectSecondCategotyIndex = index;
      that.selectGenearlCardCategoryItem = item;
      that.generalCardPaginations.page = 1;
      that.treatGoodsAccount_generalCardAccountProjectByCategory();
    },
    /**  添加通用次卡使用项目  */
    addGeneralclickCardApplyProjectItem(project) {
      var that = this;
      if (that.isConsumePackage) {
        that.addPackageGeneralCardProjectClick(project);
      } else {
        that.addGeneralProjectClick(project);
      }
    },

    /**  ======================================================================  */
    /**  通用次卡  */
    /**    */
    consumeGeneralCardClick(item) {
      var that = this;
      that.isConsumePackage = false;
      that.isConsumePackage = null;
      that.selectGeneralcardItem = item;
      that.currentGeneralcarProject = [];
      that.currentGeneralcarProjectCategoty = [];
      that.currentGeneralcarProjectSecondCategoty = [];
      that.generalCardProjectSecondCategotyIndex = 0;
      that.generalCardProjectCategotyIndex = 0;
      that.generalCardPaginations.page = 1;
      that.isShowHistoricalData = false;
      that.searchGeneralcarPorjectName = '';
      that.treatGoodsAccount_generalCardAccountProjectCategory(true).then(() => {
        that.isShowHistoricalData = false;
        that.generalCardHistoricalData = [];
        that.treatGoodsAccount_generalCardHistoricalData(true);
      });
    },

    /**  通用次卡添加 项目  */
    addGeneralProjectClick(item) {
      var that = this;
      if (that.selectGeneralcardItem.ValidBalance < that.getGeneralCardQuantity(that.selectGeneralcardItem.ID) + item.ConsumeAmount) {
        that.$message.error({ message: '通用次卡次数不足', duration: 2000 });
        return;
      }
      if (
        item.LimitConsumeTimes != 0 &&
        item.LimitConsumeTimes - item.Quantity <= that.getGeneralCardProQua(that.selectGeneralcardItem.ID, item.ID) / item.ConsumeAmount
      ) {
        that.$message.error({
          message: '该项目以达到最大可消耗次数',
          duration: 2000,
        });
        return;
      }

      var generalCard = Enumerable.from(that.currentSelectGeneralCardList).firstOrDefault((i) => i.ID == that.selectGeneralcardItem.ID);

      /* 需要限制项目数 */
      if (that.selectGeneralcardItem.TreatProjectNumber != 0 && !that.generalCardHistoricalData.some((i) => i.ID == item.ID)) {
        /* 历史数量和限制数量相同则不可以添加新项目  判断当前项目是否在历史消耗项目中 */
        if (that.generalCardHistoricalData.length == that.selectGeneralcardItem.TreatProjectNumber) {
          let isConsume = that.generalCardHistoricalData.some((i) => i.ID == item.ID);
          if (!isConsume) {
            that.$message.error('当前通用次卡项目数已经选择完成，请选择已消耗的项目进行操作！');
            return;
          }
        } else {
          /* 当前 选择通用次卡已有选择消耗项目 */
          if (typeof generalCard != 'undefined') {
            /* 判断 项目不在历史消耗项目数组中的项目 */
            let tmp = generalCard.Projects.reduce((per, cur) => {
              let isConsume = that.generalCardHistoricalData.some((i) => i.ID == cur.ID);
              if (!isConsume) {
                return [...per, cur];
              } else {
                return per;
              }
            }, []);
            if (tmp.length + that.generalCardHistoricalData.length >= that.selectGeneralcardItem.TreatProjectNumber && !tmp.some((i) => i.ID == item.ID)) {
              that.$message.error('当前通用次卡项目数已经选择完成，请选择已消耗的项目进行操作！');
              return;
            }
          }
        }
      }

      /**  通用次卡使用项目信息  */
      var Project = {
        Name: item.Name,
        Alias: item.Alias,
        Price: item.Price,
        ID: item.ID,
        Quantity: 1,
        LimitConsumeTimes: item.LimitConsumeTimes,
        quantity: item.Quantity,
        UnitAmount: that.selectGeneralcardItem.Amount,
        TotalAmount: parseFloat(that.selectGeneralcardItem.Amount * item.ConsumeAmount).toFixed(2),
        ConsumeAmount: item.ConsumeAmount,
        ProjectTreatHandler: [],
        HandleTypeList: [],
        Consumable: [],
      };

      Enumerable.from(that.projectTreatHandlers).forEach((item) => {
        Project.HandleTypeList.push({
          Name: item.Name,
          ID: item.ID,
          Employee: [],
        });
      });

      if (typeof generalCard == 'undefined') {
        that.currentSelectGeneralCardList.push({
          ID: that.selectGeneralcardItem.ID,
          GeneralCardID: that.selectGeneralcardItem.GeneralCardID,
          Name: that.selectGeneralcardItem.Name,
          Alias: that.selectGeneralcardItem.Alias,
          ValidBalance: that.selectGeneralcardItem.ValidBalance,
          Price: that.selectGeneralcardItem.Price,
          Amount: that.selectGeneralcardItem.Amount,
          IsLargess: that.selectGeneralcardItem.IsLargess,
          ArrearAmount: that.selectGeneralcardItem.ArrearAmount,
          GeneralCardItem: that.selectGeneralcardItem,
          Projects: [Project],
        });
      } else {
        generalCard.Projects.push(Project);
      }
      that.dialogGeneralCard = false;
    },
    /** 通用次卡修改数量   */
    generalItemQuantityChangeClick(currentValue, oldValue, item, Project) {
      Project.TotalAmount = Number(parseFloat(Project.UnitAmount * Project.Quantity * Project.ConsumeAmount).toFixed(2));
    },
    /**  通用次卡将要消耗次数  */
    getGeneralCardQuantity: function (generalID) {
      var that = this;
      if (!generalID) return 0;
      var generalItem = Enumerable.from(that.currentSelectGeneralCardList).firstOrDefault((i) => i.ID == generalID);
      if (typeof generalItem == 'undefined') return 0;
      return Enumerable.from(generalItem.Projects).sum((i) => Number(Number(i.Quantity) * Number(i.ConsumeAmount)));
    },
    // 当前项目消耗次数
    getGeneralCardProQua(generalID, ProID) {
      var that = this;
      if (!generalID) return 0;
      var generalItem = Enumerable.from(that.currentSelectGeneralCardList).firstOrDefault((i) => i.ID == generalID);
      if (typeof generalItem == 'undefined') return 0;
      return Enumerable.from(generalItem.Projects).sum((i) => {
        if (i.ID == ProID) {
          return Number(Number(i.Quantity) * Number(i.ConsumeAmount));
        } else {
          return 0;
        }
      });
    },
    /**  获取 通用次卡的最大值  */
    getGeneralCardProjectMaxQuantity(generalCard, Project) {
      var that = this;
      const num1 = Math.floor((generalCard.ValidBalance - that.getGeneralCardQuantity(generalCard.ID)) / Project.ConsumeAmount) + Project.Quantity;
      const num2 =
        Math.floor((Project.LimitConsumeTimes - Project.quantity - that.getGeneralCardProQua(generalCard.ID, Project.ID)) / Project.ConsumeAmount) +
        Project.Quantity;
      if (Project.LimitConsumeTimes == 0) {
        return num1;
      } else {
        return num2 <= num1 ? num2 : num1;
      }
    },
    /**  通用次卡 删除   */
    removeSelectGeneralItemClick(index, ProjectIndex) {
      var that = this;
      that.currentSelectGeneralCardList[index].Projects.splice(ProjectIndex, 1);
      if (that.currentSelectGeneralCardList[index].Projects.length <= 0) {
        that.currentSelectGeneralCardList.splice(index, 1);
      }
    },

    /** 套餐卡 ======================================================================  */
    // 套餐卡项目消耗
    consumePackageProjectClick(item) {
      var that = this;
      if (item.ValidBalance == 0) {
        that.$message.error({
          message: '有效次数不足',
          duration: 2000,
        });
        return;
      }
      var selectPack = Enumerable.from(that.currentselectPackageCardList)
        .where(function (item) {
          return item.ID == that.currentSelectPackageItem.ID;
        })
        .toArray()[0];
      var Project = {
        Name: item.Name,
        Alias: item.Alias,
        ValidBalance: item.ValidBalance,
        ID: item.ID,
        ProjectID: item.ProjectID,
        Quantity: 1,
        Price: item.Price,
        UnitAmount: item.Amount,
        TotalAmount: item.Amount,
        IsLargess: item.IsLargess,
        max: item.ValidBalance,
        ProjectTreatHandler: [],
        HandleTypeList: [],
        Consumable: [],
      };
      that.projectTreatHandlers.forEach((item) => {
        Project.HandleTypeList.push({
          Name: item.Name,
          ID: item.ID,
          Employee: [],
        });
      });
      if (typeof selectPack == 'undefined') {
        that.currentselectPackageCardList.push({
          ID: that.currentSelectPackageItem.ID,
          PackageCardID: that.currentSelectPackageItem.PackageCardID,
          Name: that.currentSelectPackageItem.Name,
          Alias: that.currentSelectPackageItem.Alias,
          IsLargess: that.currentSelectPackageItem.IsLargess,
          packageProjectList: [Project],
          packageProductList: [],
          packageSavingCardList: [],
          packageTimeCardList: [],
          packageGeneralList: [],
        });
      } else {
        if (item.ValidBalance <= that.getPackageProjectConsumeQuantity(selectPack, item.ID)) {
          that.$message.error({
            message: '有效次数不足',
            duration: 2000,
          });
          return;
        }
        selectPack.packageProjectList.push(Project);
      }
    },

    //套餐卡储值卡消耗 c存量列表的点击
    consumePackageSavingCardClick(packageItem, item) {
      var that = this;
      that.isConsumePackage = true;
      that.tempCurrentSelectPackageItem = that.currentSelectPackageItem;
      if (item.ValidBalance == 0) {
        that.$message.error({
          message: '储值卡有效余额为0，暂不可消耗。请选择其他储值卡',
          duration: 2000,
        });
        return;
      }
      that.isConsumePackage = true;
      that.selectSavingcardItem = item;
      that.currentSavingcardProject = [];
      that.currentSavingcardProjectCategoty = [];
      that.currentSavingcardProjectSecondCategoty = [];
      that.savingcardProjectCategotyIndex = 0;
      that.savingcardProjectSecondCategotyIndex = 0;
      that.savingCardPaginations.page = 1;
      that.searchSavingcardProjectName = '';
      that.savingCardConsumeItem = null;

      var packItem = Enumerable.from(that.currentselectPackageCardList).firstOrDefault((i) => i.ID == packageItem.ID);
      if (typeof packItem != 'undefined') {
        that.savingCardConsumeItem = Enumerable.from(packItem.packageSavingCardList).firstOrDefault((i) => i.ID == item.ID);
      }
      that.treatGoodsAccount_savingCardAccountProjectCategory(true);
    },
    /**  已选列表的 点击  */
    consumePackageSavingCardSelectClick(packageItem, item) {
      var that = this;
      that.isConsumePackage = true;
      that.tempCurrentSelectPackageItem = item;
      if (item.ValidBalance == 0) {
        that.$message.error({
          message: '储值卡有效余额为0，暂不可消耗。请选择其他储值卡',
          duration: 2000,
        });
        return;
      }
      that.isConsumePackage = true;
      that.selectSavingcardItem = item;
      that.currentSavingcardProjectCategoty = [];
      that.currentSavingcardProject = [];
      that.searchSavingcardProjectName = '';
      that.savingCardConsumeItem = null;

      var packItem = Enumerable.from(that.currentselectPackageCardList).firstOrDefault((i) => i.ID == packageItem.ID);
      if (typeof packItem != 'undefined') {
        that.savingCardConsumeItem = Enumerable.from(packItem.packageSavingCardList).firstOrDefault((i) => i.ID == item.ID);
      }
      that.treatSavingCardAccountProjectNet(true);
    },

    /**  套餐卡 储值卡 弹窗 点击项目  */
    addPackageItemSavingCardProject(item) {
      var that = this;
      var consumeAmount_1 = that.getCurrentSavingCardConsumeAmount(that.savingCardConsumeItem, that.isConsumePackage); //将要消耗的金额
      var ValidBalance_1 = that.selectSavingcardItem.ValidBalance; // 当前储值卡的有效余额
      if (Number(ValidBalance_1) == Number(consumeAmount_1)) {
        that.$message.error({
          message: '储值卡余额不足。请选择其他储值卡',
          duration: 2000,
        });
        return;
      }
      var tempConsumeAmount_1 = Number(consumeAmount_1) + Number(item.PreferentialPrice);
      let isMemberDiscount = false;
      let memberAmount = item.Price;
      let MemberPreferentialAmount = 0;
      let tempMemberAmount = memberAmount;
      if (this.discountData) {
        isMemberDiscount = true;
        if (this.discountData.PriceType == 1) {
          memberAmount = parseFloat(item.Price * this.discountData.DiscountPrice).toFixed(2);
          MemberPreferentialAmount = item.Price - memberAmount;
          tempMemberAmount = memberAmount;
        }

        if (this.discountData.PriceType == 2) {
          memberAmount = parseFloat(this.discountData.DiscountPrice).toFixed(2);
          MemberPreferentialAmount = item.Price - memberAmount;
          tempMemberAmount = memberAmount;
        }
      }
      // 卡优惠
      // 折扣
      if (item.PriceType == 1) {
        item.PreferentialPrice = parseFloat(memberAmount * item.DiscountPrice).toFixed(2);
        item.CardPreferentialAmount = parseFloat(memberAmount - item.PreferentialPrice).toFixed(2);
        tempMemberAmount = item.PreferentialPrice;
      }
      // 金额
      if (item.PriceType == 2) {
        item.PreferentialPrice = parseFloat(item.DiscountPrice).toFixed(2);
        item.CardPreferentialAmount = parseFloat(memberAmount - item.PreferentialPrice).toFixed(2);
        tempMemberAmount = item.PreferentialPrice;
      }

      /**  判断 将要消耗的金额是否大于储值卡的有效余额  */
      if (tempConsumeAmount_1 > ValidBalance_1) {
        if (that.TreatPermission.ModifyPrices_TreatSavingCard || that.TreatPermission.isTreatBillingModifyPrices_TreatSavingCard) {
          var tempPrice_1 = ValidBalance_1 - consumeAmount_1;
          let changeDiscount = tempPrice_1 / item.Price;
          if (that.employeeDiscount > changeDiscount) {
            that.$message.error({
              // message: '改价后折扣不能大于员工最低折扣',
              message: '储值卡余额不足。不可改价',
              duration: 2000,
            });
            return;
          }

          var tempStr_1 =
            '当前可用余额为 ' +
            tempPrice_1.toFixed(2) +
            ' 元，此项目需要消耗' +
            Number(memberAmount).toFixed(2) +
            '，如果需要继续操作，此项目的耗卡金额将修改为 ' +
            tempPrice_1.toFixed(2) +
            ' 元';
          that
            .$confirm(tempStr_1, {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning',
            })
            .then(() => {
              let Performance = {
                PerformanceRatio: parseFloat((tempPrice_1 / tempMemberAmount) * 100).toFixed(2),
                PricePreferentialAmount: parseFloat(tempMemberAmount - tempPrice_1).toFixed(2),
              };
              that.judgePackageSavingCardConsumeProject(item, tempPrice_1, Performance, MemberPreferentialAmount, isMemberDiscount);
              that.dialogSavingCard = false;
            })
            .catch(() => {
              this.$message({
                type: 'info',
                message: '已取消操作',
              });
            });
        } else {
          that.$message.error({
            message: '卡剩余金额不足，请更换其他卡',
            duration: 2000,
          });
        }
      } else {
        let Performance = {
          PerformanceRatio: 100,
          PricePreferentialAmount: 0,
        };
        that.judgePackageSavingCardConsumeProject(item, tempMemberAmount, Performance, MemberPreferentialAmount, isMemberDiscount);
        that.dialogSavingCard = false;
      }
    },

    /**  套餐卡中的储值卡  */
    judgePackageSavingCardConsumeProject(project, savingAmount, Performance, MemberPreferentialAmount, isMemberDiscount) {
      /**  消耗的项目  */
      var that = this;
      // var PerformanceRatio = ((savingAmount / project.PreferentialPrice) * 100).toFixed(2);
      var packSavingChildItem = {
        Name: project.Name,
        Alias: project.Alias,
        ID: project.ID,
        Quantity: 1,
        Price: project.Price,
        UnitAmount: savingAmount,
        TotalAmount: savingAmount,
        PreferentialPrice: savingAmount,
        CardPreferentialAmount: project.CardPreferentialAmount, //优惠价格
        CardPreferentialAmountTotal: project.CardPreferentialAmount, //优惠价格
        PopoveVisible: false,
        PopoveAmount: savingAmount,
        PerformanceRatio: Performance.PerformanceRatio,
        PricePreferentialAmount: Number(Performance.PricePreferentialAmount).toFixed(2), //手动改价优惠
        SavingCardTreatHandler: [],
        HandleTypeList: [],
        Consumable: [],
        MemberPreferentialAmount: MemberPreferentialAmount,
        MemberPreferentialAmountTotal: MemberPreferentialAmount,
        MemberDiscountData: this.discountData,
        CardPriceType: project.PriceType,
        CardDiscountPrice: project.DiscountPrice,
        isMemberDiscount: isMemberDiscount,
      };

      that.savingCardTreatHandlers.forEach((item) => {
        packSavingChildItem.HandleTypeList.push({
          Name: item.Name,
          ID: item.ID,
          Employee: [],
        });
      });

      var packSavingItem = null;
      var packItem = null;
      that.currentselectPackageCardList.forEach((item) => {
        if (item.ID == that.currentSelectPackageItem.ID) {
          packItem = item;
          item.packageSavingCardList.forEach((childItem) => {
            if (childItem.ID == that.selectSavingcardItem.ID) {
              packSavingItem = childItem;
              that.savingCardConsumeItem = packSavingItem;
            }
          });
        }
      });

      if (packItem == null) {
        packSavingItem = {
          ID: that.selectSavingcardItem.ID,
          SavingCardID: that.selectSavingcardItem.SavingCardID,
          AccountID: that.selectSavingcardItem.AccountID,
          Type: that.selectSavingcardItem.Type,
          Name: that.selectSavingcardItem.Name,
          Alias: that.selectSavingcardItem.Alias,
          IsLargess: that.selectSavingcardItem.IsLargess,
          ValidBalance: that.selectSavingcardItem.ValidBalance,
          SavingCardItem: that.selectSavingcardItem,
          Projects: [packSavingChildItem],
        };
        that.savingCardConsumeItem = packSavingItem;
        packItem = {
          ID: that.currentSelectPackageItem.ID,
          PackageCardID: that.currentSelectPackageItem.PackageCardID,
          Name: that.currentSelectPackageItem.Name,
          Alias: that.currentSelectPackageItem.Alias,
          IsLargess: that.currentSelectPackageItem.IsLargess,
          packageProjectList: [],
          packageProductList: [],
          packageSavingCardList: [packSavingItem],
          packageTimeCardList: [],
          packageGeneralList: [],
        };
        that.currentselectPackageCardList.push(packItem);
      } else {
        if (packSavingItem == null) {
          packSavingItem = {
            ID: that.selectSavingcardItem.ID,
            SavingCardID: that.selectSavingcardItem.SavingCardID,
            AccountID: that.selectSavingcardItem.AccountID,
            Type: that.selectSavingcardItem.Type,
            Name: that.selectSavingcardItem.Name,
            Alias: that.currentSelectPackageItem.Alias,
            ValidBalance: that.selectSavingcardItem.ValidBalance,
            Projects: [packSavingChildItem],
          };
          that.savingCardConsumeItem = packSavingItem;
          packItem.packageSavingCardList.push(packSavingItem);
        } else {
          packSavingItem.Projects.push(packSavingChildItem);
        }
      }
    },

    // 套餐卡消耗时效卡
    consumePackageTimeCardClick(packItem, item) {
      var that = this;
      that.isConsumePackage = true;
      that.tempCurrentSelectPackageItem = that.currentSelectPackageItem;
      if (item.ConsumeCycle == 0 || item.CycleLimitAmount == 0) {
        /**  如果 消耗周期 或者 周期次数 为零 则消耗不做限制  */
        that.selectTimecardItem = item;
        that.currentTimeCardProject = [];
        that.currentTimeCardProjectCategoty = [];
        that.currentTimecarProjectSecondCategoty = [];
        that.timeCardProjectCategotyIndex = 0;
        that.timeCardProjectSecondCategotyIndex = 0;
        that.searchTimeCardPorjectName = '';
        that.timeCardPaginations.page = 1;
        that.treatGoodsAccount_timeCardAccountProjectCategory(true);
      } else {
        var ValidBalance = item.CycleLimitAmount - item.ConsumeCycleAmount; //周期次数 减去周期内已耗用次数 等于有效次数
        if (ValidBalance <= 0) {
          that.$message.error({
            message: '周期内消耗次数不足',
            duration: 2000,
          });
          return;
        }
        that.selectTimecardItem = item;

        that.currentTimeCardProject = [];
        that.currentTimeCardProjectCategoty = [];
        that.currentTimecarProjectSecondCategoty = [];
        that.timeCardProjectCategotyIndex = 0;
        that.timeCardProjectSecondCategotyIndex = 0;
        that.searchTimeCardPorjectName = '';
        that.timeCardPaginations.page = 1;
        that.treatGoodsAccount_timeCardAccountProjectCategory(true);
      }
    },

    // 套餐卡消耗时效 已选列表卡
    consumePackageTimeCardSelectClick(packItem, item) {
      var that = this;
      that.isConsumePackage = true;
      that.tempCurrentSelectPackageItem = packItem;

      if (item.ConsumeCycle == 0 || item.CycleLimitAmount == 0) {
        /**  如果 消耗周期 或者 周期次数 为零 则消耗不做限制  */
        that.selectTimecardItem = item;
        that.currentTimeCardProjectCategoty = [];
        that.currentTimeCardProject = [];
        that.searchTimeCardPorjectName = '';
        that.treatTimeCardAccountProjectNet(true);
      } else {
        var ValidBalance = item.CycleLimitAmount - item.ConsumeCycleAmount; //周期次数 减去周期内已耗用次数 等于有效次数
        if (ValidBalance <= 0) {
          that.$message.error({
            message: '周期内消耗次数不足',
            duration: 2000,
          });
          return;
        }
        that.selectTimecardItem = item;
        that.currentTimeCardProjectCategoty = [];
        that.currentTimeCardProject = [];
        that.searchTimeCardPorjectName = '';
        that.treatTimeCardAccountProjectNet(true);
      }
    },

    /** 套餐卡 中时效卡点击项目   */
    addPackageTimeCardProjectClick(item) {
      var that = this;
      /**  周期内有效的耗用次数  */
      var CycleValidBalance = that.selectTimecardItem.CycleLimitAmount - that.selectTimecardItem.ConsumeCycleAmount;
      /**  已耗用的次数  */
      var curAmount = that.getPackageTimeCardConsumAmount(that.tempCurrentSelectPackageItem.ID, that.selectTimecardItem.ID);

      /**  如果 消耗周期 与周期次数任意为 0 切在不欠款的情况下不限制消耗次数  */
      if (that.selectTimecardItem.CycleLimitAmount != 0 && that.selectTimecardItem.ConsumeCycle != 0) {
        if (curAmount >= CycleValidBalance) {
          /**  判断周期次数  */
          that.$message.error({
            message: '周期内有效次数不足',
            duration: 2000,
          });
          return;
        }
      }

      /**   如果时效卡有欠款 则根据有效次数限制消耗次数 */
      if (that.selectTimecardItem.ArrearAmount > 0) {
        //有欠款
        if (curAmount > that.selectTimecardItem.ValidBalance) {
          /**  当前消耗次数是否大于 有效次数  */

          that.$message.error({
            message: '当前时效卡存在欠款，累计消耗次数不能超过' + that.selectTimecardItem.ValidBalance + '次',
            duration: 2000,
          });
          return;
        }
      }
      /**  当前业绩金额  */
      var currentTimeAmount = that.selectTimecardItem.Amount;
      /**  如果 消耗次数大于 等于 剩余业绩次数 则消耗金额为零  */
      if (curAmount >= that.selectTimecardItem.PerformanceBalance) {
        currentTimeAmount = 0;
      }

      var tempHandle = [];
      Enumerable.from(that.projectTreatHandlers).forEach((item) => {
        tempHandle.push({
          Name: item.Name,
          ID: item.ID,
          Employee: [],
        });
      });

      var Project = {
        Name: item.Name,
        Alias: item.Alias,
        ID: item.ID,
        Quantity: 1, //数量
        IsLargess: that.selectTimecardItem.IsLargess,
        Price: item.Price, // 项目单价
        UnitAmount: that.selectTimecardItem.Amount, //  消耗参考单价
        TotalAmount: currentTimeAmount, // 消耗 业绩金额
        TimeCardTreatHandler: [],
        HandleTypeList: tempHandle,
        Consumable: [],
      };

      var timeCardItem = {
        ID: that.selectTimecardItem.ID,
        TimeCardID: that.selectTimecardItem.TimeCardID,
        Name: that.selectTimecardItem.Name,
        Alias: that.selectTimecardItem.Alias,
        IsLargess: that.selectTimecardItem.IsLargess,
        ArrearAmount: that.selectTimecardItem.ArrearAmount,
        Amount: that.selectTimecardItem.Amount,
        UnitAmount: that.selectTimecardItem.Amount,
        CycleLimitAmount: that.selectTimecardItem.CycleLimitAmount, // 周期内次数
        ConsumeCycle: that.selectTimecardItem.ConsumeCycle,
        ConsumeCycleAmount: that.selectTimecardItem.ConsumeCycleAmount, // 周期内已消耗次数
        ValidBalance: that.selectTimecardItem.ValidBalance, //周期内有效次数
        PerformanceTimes: that.selectTimecardItem.PerformanceTimes, // 总业绩次数
        PerformanceBalance: that.selectTimecardItem.PerformanceBalance, // 剩余业绩次数
        TimeCardItem: that.selectTimecardItem,
        Projects: [Project],
      };

      var PackageItem = Enumerable.from(that.currentselectPackageCardList).firstOrDefault((i) => i.ID == that.tempCurrentSelectPackageItem.ID);
      if (typeof PackageItem == 'undefined') {
        that.currentselectPackageCardList.push({
          ID: that.tempCurrentSelectPackageItem.ID,
          PackageCardID: that.tempCurrentSelectPackageItem.PackageCardID,
          Name: that.tempCurrentSelectPackageItem.Name,
          Alias: that.tempCurrentSelectPackageItem.Alias,
          IsLargess: that.tempCurrentSelectPackageItem.IsLargess,
          PerformanceBalance: that.selectTimecardItem.PerformanceBalance,
          packageProjectList: [],
          packageProductList: [],
          packageSavingCardList: [],
          packageTimeCardList: [timeCardItem],
          packageGeneralList: [],
        });
      } else {
        var timeCard = Enumerable.from(PackageItem.packageTimeCardList).firstOrDefault((i) => i.ID == that.selectTimecardItem.ID);
        if (typeof timeCard == 'undefined') {
          PackageItem.packageTimeCardList.push(timeCardItem);
        } else {
          timeCard.Projects.push(Project);
        }
      }
      that.dialogTimeCard = false;
    },

    /**======================================================================  */
    // 套餐卡消耗通用次卡
    consumePackageGeneralCardClick(item) {
      var that = this;
      that.isConsumePackage = true;
      that.tempCurrentSelectPackageItem = that.currentSelectPackageItem;
      that.selectGeneralcardItem = item;
      that.currentGeneralcarProject = [];
      that.currentGeneralcarProjectCategoty = [];
      that.currentGeneralcarProjectSecondCategoty = [];
      that.generalCardProjectCategotyIndex = 0;
      that.generalCardProjectSecondCategotyIndex = 0;
      that.generalCardPaginations.page = 1;
      that.searchGeneralcarPorjectName = '';
      that.isShowHistoricalData = false;
      that.treatGoodsAccount_generalCardAccountProjectCategory(true).then(() => {
        that.isShowHistoricalData = false;
        that.generalCardHistoricalData = [];
        that.treatGoodsAccount_generalCardHistoricalData(true);
      });
    },
    /**  已选 套餐卡中 通用次卡点击事件  */
    consumePackageGeneralCardSelectClick(packItem, item) {
      var that = this;
      that.isConsumePackage = true;
      that.tempCurrentSelectPackageItem = packItem;
      that.selectGeneralcardItem = item;
      that.currentGeneralcarProjectCategoty = [];
      that.currentGeneralcarProject = [];
      that.currentGeneralcarActiveName = [];
      that.searchGeneralcarPorjectName = '';
      that.treatGoodsAccount_generalCardAccountProjectCategory(true).then(() => {
        that.isShowHistoricalData = false;
        that.generalCardHistoricalData = [];
        that.treatGoodsAccount_generalCardHistoricalData(true);
      });
    },

    /** 套餐卡中通用次卡点击 项目   */
    addPackageGeneralCardProjectClick(item) {
      var that = this;
      if (that.selectGeneralcardItem.ValidBalance < that.getGeneralCardQuantity(that.selectGeneralcardItem.ID) + item.ConsumeAmount) {
        that.$message.error({
          message: '通用次卡次数不足',
          duration: 2000,
        });
        return;
      }
      let beforeConsumeTimes = that.getPackageGeneralCardProQua(that.tempCurrentSelectPackageItem.ID, that.selectGeneralcardItem.ID, item.ID);

      if (item.LimitConsumeTimes != 0 && item.LimitConsumeTimes - item.Quantity <= beforeConsumeTimes / item.ConsumeAmount) {
        that.$message.error({
          message: '该项目以达到最大可消耗次数',
          duration: 2000,
        });
        return;
      }

      /**  通用次卡使用项目信息  */
      var Project = {
        Name: item.Name,
        Alias: item.Alias,
        Price: item.Price,
        ID: item.ID,
        Quantity: 1,
        UnitAmount: that.selectGeneralcardItem.Amount,
        TotalAmount: parseFloat(that.selectGeneralcardItem.Amount * item.ConsumeAmount).toFixed(2),
        ConsumeAmount: item.ConsumeAmount,
        ProjectTreatHandler: [],
        HandleTypeList: [],
        LimitConsumeTimes: item.LimitConsumeTimes,
        beforeQuantity: item.Quantity,
        Consumable: [],
      };

      Enumerable.from(that.projectTreatHandlers).forEach((item) => {
        Project.HandleTypeList.push({
          Name: item.Name,
          ID: item.ID,
          Employee: [],
        });
      });

      var generalCard = {
        ID: that.selectGeneralcardItem.ID,
        GeneralCardID: that.selectGeneralcardItem.GeneralCardID,
        Name: that.selectGeneralcardItem.Name,
        Alias: that.selectGeneralcardItem.Alias,
        ValidBalance: that.selectGeneralcardItem.ValidBalance,
        Price: that.selectGeneralcardItem.Price,
        Amount: that.selectGeneralcardItem.Amount,
        IsLargess: that.selectGeneralcardItem.IsLargess,
        ArrearAmount: that.selectGeneralcardItem.ArrearAmount,
        GeneralCardItem: that.selectGeneralcardItem,
        TreatProjectNumber: that.selectGeneralcardItem.TreatProjectNumber,
        Projects: [Project],
      };

      var packageItem = Enumerable.from(that.currentselectPackageCardList).firstOrDefault((i) => i.ID == that.tempCurrentSelectPackageItem.ID);
      if (typeof packageItem == 'undefined') {
        that.currentselectPackageCardList.push({
          ID: that.tempCurrentSelectPackageItem.ID,
          PackageCardID: that.tempCurrentSelectPackageItem.PackageCardID,
          Name: that.tempCurrentSelectPackageItem.Name,
          Alias: that.tempCurrentSelectPackageItem.Alias,
          IsLargess: that.tempCurrentSelectPackageItem.IsLargess,
          PerformanceBalance: that.selectTimecardItem.PerformanceBalance,
          packageProjectList: [],
          packageProductList: [],
          packageSavingCardList: [],
          packageTimeCardList: [],
          packageGeneralList: [generalCard],
        });
      } else {
        var generalCardSelect = Enumerable.from(packageItem.packageGeneralList).firstOrDefault((i) => i.ID == that.selectGeneralcardItem.ID);

        /* 需要限制项目数 */
        if (that.selectGeneralcardItem.TreatProjectNumber != 0 && !that.generalCardHistoricalData.some((i) => i.ID == item.ID)) {
          /* 历史数量和限制数量相同则不可以添加新项目  判断当前项目是否在历史消耗项目中 */
          if (that.generalCardHistoricalData.length == that.selectGeneralcardItem.TreatProjectNumber) {
            let isConsume = that.generalCardHistoricalData.some((i) => i.ID == item.ID);
            if (!isConsume) {
              that.$message.error('当前通用次卡项目数已经选择完成，请选择已消耗的项目进行操作！');
              return;
            }
          } else {
            /* 当前 选择通用次卡已有选择消耗项目 */
            if (typeof generalCardSelect != 'undefined') {
              /* 判断 项目不在历史消耗项目数组中的项目 */
              let tmp = generalCardSelect.Projects.reduce((per, cur) => {
                let isConsume = that.generalCardHistoricalData.some((i) => i.ID == cur.ID);
                if (!isConsume) {
                  return [...per, cur];
                } else {
                  return per;
                }
              }, []);
              console.log(Number(tmp.length + that.generalCardHistoricalData.length) >= Number(that.selectGeneralcardItem.TreatProjectNumber));
              if (
                Number(tmp.length + that.generalCardHistoricalData.length) >= Number(that.selectGeneralcardItem.TreatProjectNumber) &&
                !tmp.some((i) => i.ID == item.ID)
              ) {
                that.$message.error('当前通用次卡项目数已经选择完成，请选择已消耗的项目进行操作！');
                return;
              }
            }
          }
        }

        if (typeof generalCardSelect == 'undefined') {
          packageItem.packageGeneralList.push(generalCard);
        } else {
          generalCardSelect.Projects.push(Project);
        }
      }
      that.dialogGeneralCard = false;
    },
    // 当前套餐看通用次卡 项目消耗次数
    getPackageGeneralCardProQua(packageCardID, generalID, ProID) {
      var that = this;
      if (!generalID || !packageCardID) return 0;
      let packageCardItem = Enumerable.from(that.currentselectPackageCardList).firstOrDefault((i) => {
        return i.ID == packageCardID;
      }, -1);
      if (packageCardItem === -1) return 0;
      var generalItem = Enumerable.from(packageCardItem.packageGeneralList).firstOrDefault((i) => {
        return i.ID == generalID;
      }, -1);
      if (generalItem === -1) return 0;
      return Enumerable.from(generalItem.Projects).sum((i) => {
        if (i.ID == ProID) {
          return Number(Number(i.Quantity) * Number(i.ConsumeAmount));
        } else {
          return 0;
        }
      });
    },

    /**======================================================================  */
    // 套餐卡消耗产品
    consumePackageProductClick(item) {
      var that = this;
      if (item.ValidBalance == 0) {
        that.$message.error({
          message: '有效次数不足',
          duration: 2000,
        });
        return;
      }
      var selectPack = Enumerable.from(that.currentselectPackageCardList)
        .where(function (item) {
          return item.ID == that.currentSelectPackageItem.ID;
        })
        .toArray()[0];
      var Product = {
        Name: item.Name,
        Alias: item.Alias,
        ValidBalance: item.ValidBalance,
        ID: item.ID,
        ProductID: item.ProductID,
        Quantity: 1,
        Price: item.Price,
        UnitAmount: item.Amount,
        TotalAmount: item.Amount,
        IsLargess: item.IsLargess,
        ProjectTreatHandler: [],
        HandleTypeList: [],
      };

      that.productTreatHandlers.forEach((item) => {
        Product.HandleTypeList.push({
          Name: item.Name,
          ID: item.ID,
          Employee: [],
        });
      });

      if (typeof selectPack == 'undefined') {
        that.currentselectPackageCardList.push({
          ID: that.currentSelectPackageItem.ID,
          PackageCardID: that.currentSelectPackageItem.PackageCardID,
          Name: that.currentSelectPackageItem.Name,
          Alias: that.currentSelectPackageItem.Alias,
          IsLargess: that.currentSelectPackageItem.IsLargess,
          packageProjectList: [],
          packageProductList: [Product],
          packageSavingCardList: [],
          packageTimeCardList: [],
          packageGeneralList: [],
        });
      } else {
        if (item.ValidBalance <= that.getPackageProductConsumeQuantity(selectPack, item.ID)) {
          that.$message.error({
            message: '有效次数不足',
            duration: 2000,
          });
          return;
        }
        selectPack.packageProductList.push(Product);
      }
    },

    /**  点击套餐卡分类  */
    clickPackageCagegory: function (item, index) {
      var that = this;
      if (index != that.currentPackCategoryIndex) {
        that.currentSelectPackageItem = item;
        that.currentPackCategoryIndex = index;
        that.treatPackageCardAccountDetailsNet();
      }
    },

    /**  套餐卡中项目的消耗次数  */
    getPackageProjectConsumeQuantity(packageItem, ProjectID) {
      if (!packageItem) return 0;
      var Amount = 0;
      packageItem.packageProjectList.forEach((Project) => {
        if (Project.ID == ProjectID) {
          Amount += Number(Project.Quantity);
        }
      });
      return Amount;
    },
    /**  套餐卡中项目的消耗次数  */
    getPackageProductConsumeQuantity(packageItem, ProductID) {
      if (!packageItem) return 0;
      var Amount = 0;
      packageItem.packageProductList.forEach((Product) => {
        if (Product.ID == ProductID) {
          Amount += Number(Product.Quantity);
        }
      });
      return Amount;
    },

    /**  套餐卡储值卡金额  */
    getPackageSavingCardConsumeAmount(packageItem, SavingCardID) {
      if (!packageItem) return 0;
      var savingCard = Enumerable.from(packageItem.packageSavingCardList).firstOrDefault((i) => i.ID == SavingCardID);
      if (typeof savingCard == 'undefined') return 0;
      var amount = Enumerable.from(savingCard.Projects).sum((p) => Number(p.TotalAmount));
      return amount;
    },

    /**  套餐卡时效卡次数  */
    getPackageTimeCardConsumAmount(packageItemID, timeCardID) {
      if (!packageItemID) return 0;
      var Amount = 0;
      var that = this;
      that.currentselectPackageCardList.forEach((packageItem) => {
        if (packageItemID == packageItem.ID) {
          packageItem.packageTimeCardList.forEach((timeCard) => {
            if (timeCardID == timeCard.ID) {
              timeCard.Projects.forEach((Project) => {
                Amount += Number(Project.Quantity);
              });
            }
          });
        }
      });
      return Amount;
    },
    /**  套餐卡通用次卡次数  */
    getPackageGeneralCardConsumAmount(packageID, generalCardID) {
      if (!packageID) return 0;
      var packageItem = Enumerable.from(this.currentselectPackageCardList).firstOrDefault((i) => i.ID == packageID);

      if (typeof packageItem == 'undefined') return 0;
      var generalCard = Enumerable.from(packageItem.packageGeneralList).firstOrDefault((i) => i.ID == generalCardID);
      if (typeof generalCard == 'undefined') return 0;
      return Enumerable.from(generalCard.Projects).sum((i) => Number(Number(i.Quantity) * Number(i.ConsumeAmount)));
    },

    /**  套餐卡中 通用次卡的最大数量  */
    getPackageGeneralCardProjectMaxQuantity(packageItem, generalCard, Project) {
      var that = this;
      let times =
        Math.floor((generalCard.ValidBalance - that.getPackageGeneralCardConsumAmount(packageItem.ID, generalCard.ID)) / Project.ConsumeAmount) +
        Project.Quantity;
      if (Project.LimitConsumeTimes == 0) {
        return times;
      }
      return Project.LimitConsumeTimes - Project.beforeQuantity > times ? times : Project.LimitConsumeTimes - Project.beforeQuantity;
    },

    /**  套餐卡中 项目 数量更改  */
    packageProjectQuantityChangeClick(item) {
      item.TotalAmount = Number(parseFloat(item.UnitAmount * item.Quantity).toFixed(2));
    },
    /**  套餐卡中的产品  数量更改  */
    packageProductQuantityChangeClick(item) {
      item.TotalAmount = parseFloat(item.UnitAmount * item.Quantity).toFixed(2);
    },

    /** 删除套餐卡中的 储值卡 */
    removePackageSavingCardItemClick(packItem, SavingCard, Project, index, SavingCardIndex, ProjectIndex) {
      var that = this;
      SavingCard.Projects.splice(ProjectIndex, 1);
      if (SavingCard.Projects.length <= 0) {
        packItem.packageSavingCardList.splice(SavingCardIndex, 1);
      }

      if (
        packItem.packageProjectList.length == 0 &&
        packItem.packageProductList.length == 0 &&
        packItem.packageSavingCardList.length == 0 &&
        packItem.packageTimeCardList.length == 0 &&
        packItem.packageGeneralList.length == 0
      ) {
        that.currentselectPackageCardList.splice(index, 1);
      }
    },
    /** 删除套餐卡中的 时效卡 */
    removePackageTimeCardItemClick(packItem, timeCard, Project, index, timeCardIndex, ProjectIndex) {
      var that = this;
      timeCard.Projects.splice(ProjectIndex, 1);
      if (timeCard.Projects.length == 0) {
        packItem.packageTimeCardList.splice(timeCardIndex, 1);
      } else {
        that.timeCardRemoveAllOcationValidQuantity(timeCard);
      }

      if (
        packItem.packageProjectList.length == 0 &&
        packItem.packageProductList.length == 0 &&
        packItem.packageSavingCardList.length == 0 &&
        packItem.packageTimeCardList.length == 0 &&
        packItem.packageGeneralList.length == 0
      ) {
        that.currentselectPackageCardList.splice(index, 1);
      }
    },

    /**  删除套餐卡中的通用次卡  */
    removePackageGeneralCardItemClick(packItem, generalCard, Project, index, generalCardIndex, ProjectIndex) {
      var that = this;
      generalCard.Projects.splice(ProjectIndex, 1);
      if (generalCard.Projects.length <= 0) {
        packItem.packageGeneralList.splice(generalCardIndex, 1);
      }

      if (
        packItem.packageProjectList.length == 0 &&
        packItem.packageProductList.length == 0 &&
        packItem.packageSavingCardList.length == 0 &&
        packItem.packageTimeCardList.length == 0 &&
        packItem.packageGeneralList.length == 0
      ) {
        that.currentselectPackageCardList.splice(index, 1);
      }
    },

    /** 删除套餐卡中的 储值卡 时效卡 通用次卡   */
    removeSelectPackageCardItemClick(index, childIndex, childIndex_1) {
      var that = this;
      that.currentselectPackageCardList[index].ChildItems[childIndex].ChildItems.splice(childIndex_1, 1);
      if (that.currentselectPackageCardList[index].ChildItems[childIndex].ChildItems.length <= 0) {
        that.currentselectPackageCardList[index].ChildItems.splice(childIndex, 1);
      }
      if (that.currentselectPackageCardList[index].ChildItems.length <= 0) {
        that.currentselectPackageCardList.splice(index, 1);
      }
    },
    /**  删除套餐卡中的项目  */
    removePackageProjectItemClick(packItem, index, childIndex) {
      var that = this;
      packItem.packageProjectList.splice(childIndex, 1);
      if (
        packItem.packageProjectList.length == 0 &&
        packItem.packageProductList.length == 0 &&
        packItem.packageSavingCardList.length == 0 &&
        packItem.packageTimeCardList.length == 0 &&
        packItem.packageGeneralList.length == 0
      ) {
        that.currentselectPackageCardList.splice(index, 1);
      }
    },
    /**  删除套餐卡中的 产品  */
    removePackageProductItemClick(packItem, index, childIndex) {
      var that = this;
      packItem.packageProductList.splice(childIndex, 1);
      if (
        packItem.packageProjectList.length == 0 &&
        packItem.packageProductList.length == 0 &&
        packItem.packageSavingCardList.length == 0 &&
        packItem.packageTimeCardList.length == 0 &&
        packItem.packageGeneralList.length == 0
      ) {
        that.currentselectPackageCardList.splice(index, 1);
      }
    },
    /**  套餐卡 中 储值卡修改数量   */
    packageSavingCartItemQuantityChangeClick(currentValue, oldValue, packageItem, savingCard, Project) {
      var that = this;

      var savingCardAmount = that.getPackageSavingCardConsumeAmount(packageItem, savingCard.ID); //  当前 消耗金额
      var curAmount = Project.Quantity * Project.PreferentialPrice + savingCardAmount - Project.PreferentialPrice * oldValue;
      if (Number(savingCard.ValidBalance) < Number(savingCardAmount) - Project.TotalAmount + Number(Project.PreferentialPrice * Project.Quantity)) {
        that.$message.error({
          message: '有效余额不足',
          duration: 2000,
        });
        that.$nextTick(() => {
          Project.Quantity = oldValue;
        });

        return;
      }

      if (savingCard.ValidBalance < curAmount) {
        that.$message.error({
          message: '有效余额不足',
          duration: 2000,
        });

        var MaxQuantity = Math.floor((savingCard.ValidBalance - savingCardAmount + Number(Project.TotalAmount)) / Project.PreferentialPrice);
        this.$nextTick(() => {
          Project.Quantity = MaxQuantity;
          Project.TotalAmount = parseFloat(Project.Quantity * Project.PreferentialPrice).toFixed(2);

          Project.CardPreferentialAmountTotal = parseFloat(Project.CardPreferentialAmount * Project.Quantity).toFixed(2);
          Project.MemberPreferentialAmountTotal = parseFloat(Project.MemberPreferentialAmount * Project.Quantity).toFixed(2);
          Project.PopoveAmount = Project.TotalAmount;
        });
      } else {
        Project.PerformanceRatio = 100;
        Project.PricePreferentialAmount = 0;
        Project.TotalAmount = parseFloat(Project.Quantity * Project.PreferentialPrice).toFixed(2);
        Project.PopoveAmount = Project.TotalAmount;        
        Project.CardPreferentialAmountTotal = parseFloat(Project.CardPreferentialAmount * Project.Quantity).toFixed(2);
        if(Project.isMemberDiscount){
          Project.MemberPreferentialAmountTotal = parseFloat(Project.MemberPreferentialAmount * Project.Quantity).toFixed(2);
        }
      }
    },
    /**  套餐卡修改通用次卡数量  */
    packageGeneralCartItemQuantityChangeClick(currentValue, oldValue, item, generalCard, Project) {
      Project.TotalAmount = parseFloat((Project.UnitAmount * Project.Quantity * Project.ConsumeAmount).toFixed(2));

      // Project.Amount = (generalCard.Amount * Project.Quantity * Project.ConsumeAmount).toFixed(2);
    },
    /**  套餐卡 时效卡修改数量  */
    packageTimeCartItemQuantityChangeClick(currentValue, oldValue, item, timeCard, Project) {
      var that = this;
      var curVailAmount = that.getTimeCardConsumePerformanceAmount(timeCard, Project);
      if (curVailAmount > 0) {
        if (Project.Quantity >= curVailAmount) {
          Project.TotalAmount = parseFloat(Project.UnitAmount * curVailAmount).toFixed(2);
        } else {
          Project.TotalAmount = parseFloat(Project.UnitAmount * Project.Quantity).toFixed(2);
          that.timeCardAllocationValidQuantity(timeCard, Project, curVailAmount);
        }
      }
    },

    /**   其他  */
    // 锚点
    navChange: function (index, selector) {
      var that = this;
      that.navIndex = index;
      var anchor = this.$el.querySelector(selector);
      that.$el.querySelector('.el_scrollbar_project').scrollTop = anchor.offsetTop - 50;
    },
    /*  */
    reviewHandledScale() {
      const that = this;
      const isProjectHandleScale = that.currentSelectProjectList.some((i) => {
        return (
          i.HandleTypeList &&
          i.HandleTypeList.some((h) => {
            return h.Employee.some((e) => {
              return !e.Discount && e.Discount !== 0;
            });
          })
        );
      });
      const isProductHandleScale = that.currentSelectProductList.some((i) => {
        return (
          i.HandleTypeList &&
          i.HandleTypeList.some((h) => {
            return h.Employee.some((e) => {
              return !e.Discount && e.Discount !== 0;
            });
          })
        );
      });

      const isTimeCardHandleScale = that.currentSelectTimeCardList.some((i) => {
        return (
          i.Projects &&
          i.Projects.some((p) => {
            return (
              p.HandleTypeList &&
              p.HandleTypeList.some((h) => {
                return h.Employee.some((e) => {
                  return !e.Discount && e.Discount !== 0;
                });
              })
            );
          })
        );
      });

      const isGeneralCarddHandleScale = that.currentSelectGeneralCardList.some((i) => {
        return (
          i.Projects &&
          i.Projects.some((p) => {
            return (
              p.HandleTypeList &&
              p.HandleTypeList.some((h) => {
                return h.Employee.some((e) => {
                  return !e.Discount && e.Discount !== 0;
                });
              })
            );
          })
        );
      });

      const isSavingCardHandleScale = that.currentSelectSavingCardList.some((i) => {
        return (
          i.Projects &&
          i.Projects.some((p) => {
            return (
              p.HandleTypeList &&
              p.HandleTypeList.some((h) => {
                return h.Employee.some((e) => {
                  return !e.Discount && e.Discount !== 0;
                });
              })
            );
          })
        );
      });

      const isPackageCardHandleScale = that.currentselectPackageCardList.some((pack) => {
        const isPackProjectHandleScale = pack.packageProjectList.some((i) => {
          return (
            i.HandleTypeList &&
            i.HandleTypeList.some((h) => {
              return h.Employee.some((e) => {
                return !e.Discount && e.Discount !== 0;
              });
            })
          );
        });
        const isPackProductHandleScale = pack.packageProductList.some((i) => {
          return (
            i.HandleTypeList &&
            i.HandleTypeList.some((h) => {
              return h.Employee.some((e) => {
                return !e.Discount && e.Discount !== 0;
              });
            })
          );
        });

        const isPackTimeCardHandleScale = pack.packageTimeCardList.some((i) => {
          return (
            i.Projects &&
            i.Projects.some((p) => {
              return (
                p.HandleTypeList &&
                p.HandleTypeList.some((h) => {
                  return h.Employee.some((e) => {
                    return !e.Discount && e.Discount !== 0;
                  });
                })
              );
            })
          );
        });

        const isPackGeneralCarddHandleScale = pack.packageGeneralList.some((i) => {
          return (
            i.Projects &&
            i.Projects.some((p) => {
              return (
                p.HandleTypeList &&
                p.HandleTypeList.some((h) => {
                  return h.Employee.some((e) => {
                    return !e.Discount && e.Discount !== 0;
                  });
                })
              );
            })
          );
        });

        const isPackSavingCardHandleScale = pack.packageSavingCardList.some((i) => {
          return (
            i.Projects &&
            i.Projects.some((p) => {
              return (
                p.HandleTypeList &&
                p.HandleTypeList.some((h) => {
                  return h.Employee.some((e) => {
                    return !e.Discount && e.Discount !== 0;
                  });
                })
              );
            })
          );
        });

        return (
          isPackProjectHandleScale || isPackProductHandleScale || isPackTimeCardHandleScale || isPackGeneralCarddHandleScale || isPackSavingCardHandleScale
        );
      });

      return (
        isProjectHandleScale ||
        isProductHandleScale ||
        isTimeCardHandleScale ||
        isGeneralCarddHandleScale ||
        isSavingCardHandleScale ||
        isPackageCardHandleScale
      );
    },
    // 结账
    consumeBillClick: function () {
      var that = this;
      if (!that.customerID) {
        that.$message.error('请填写客户信息');
        return;
      }
      if (
        (that.currentSelectProjectList.length <= 0) &
        (that.currentSelectProductList.length <= 0) &
        (that.currentSelectTimeCardList.length <= 0) &
        (that.currentselectPackageCardList.length <= 0) &
        (that.currentSelectGeneralCardList.length <= 0) &
        (that.currentSelectSavingCardList.length <= 0)
      ) {
        that.$message.error('请选择商品！');
        return;
      }

      if (that.reviewHandledScale()) {
        that.$message.error('请输入经手人比例');
        return;
      }

      let isChenck = that.checkEntityOrQuantity();
      if (!isChenck) {
        return;
      }
      if (that.getBillDate() == null) {
        that.$message.error('请输入补录日期');
        return;
      }
      const isProjectHanlder = that.currentSelectProjectList.some((i) => {
        return !i.HandleTypeList || !i.HandleTypeList.some((j) => j.Employee && j.Employee.length);
      });

      const isProductHanlder = that.currentSelectProductList.some((i) => {
        return !i.HandleTypeList || !i.HandleTypeList.some((j) => j.Employee && j.Employee.length);
      });

      const isSavingCardHanlder = that.currentSelectSavingCardList.some((i) => {
        return (
          !i.Projects ||
          i.Projects.some((n) => {
            return !n.HandleTypeList || !n.HandleTypeList.some((j) => j.Employee && j.Employee.length);
          })
        );
      });
      const isTimeCardHanlder = that.currentSelectTimeCardList.some((i) => {
        return (
          !i.Projects ||
          i.Projects.some((n) => {
            return !n.HandleTypeList || !n.HandleTypeList.some((j) => j.Employee && j.Employee.length);
          })
        );
      });

      const isGeneralCardHanlder = that.currentSelectGeneralCardList.some((i) => {
        return (
          !i.Projects ||
          i.Projects.some((n) => {
            return !n.HandleTypeList || !n.HandleTypeList.some((j) => j.Employee && j.Employee.length);
          })
        );
      });

      const isPackageCardHanlder = that.currentselectPackageCardList.some((m) => {
        const isPackProjectHanlder = m.packageProjectList.some((i) => {
          return !i.HandleTypeList || !i.HandleTypeList.some((j) => j.Employee && j.Employee.length);
        });

        const isPackProductHanlder = m.packageProductList.some((i) => {
          return !i.HandleTypeList || !i.HandleTypeList.some((j) => j.Employee && j.Employee.length);
        });

        const isPackageSavingCardHanlder = m.packageSavingCardList.some((i) => {
          return (
            !i.Projects ||
            i.Projects.some((n) => {
              return !n.HandleTypeList || !n.HandleTypeList.some((j) => j.Employee && j.Employee.length);
            })
          );
        });

        const isPackageGeneralCardHanlder = m.packageGeneralList.some((i) => {
          return (
            !i.Projects ||
            i.Projects.some((n) => {
              return !n.HandleTypeList || !n.HandleTypeList.some((j) => j.Employee && j.Employee.length);
            })
          );
        });

        const isPackageTimeCardHanlder = m.packageTimeCardList.some((i) => {
          return (
            !i.Projects ||
            i.Projects.some((n) => {
              return !n.HandleTypeList || !n.HandleTypeList.some((j) => j.Employee && j.Employee.length);
            })
          );
        });

        return isPackProjectHanlder || isPackProductHanlder || isPackageSavingCardHanlder || isPackageGeneralCardHanlder || isPackageTimeCardHanlder;
      });

      if (isProjectHanlder || isProductHanlder || isTimeCardHanlder || isGeneralCardHanlder || isSavingCardHanlder || isPackageCardHanlder) {
        that
          .$confirm('存在未添加经手人的商品，是否继续结账', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          })
          .then(() => {
            that.dialogBill = true;
          })
          .catch(() => {
            that.$message({
              type: 'info',
              message: '已取消操作',
            });
          });
      } else {
        that.dialogBill = true;
      }
    },

    checkEntityOrQuantity() {
      let that = this;
      /**  •• 项目•••••••••••••••• •••••••••••••••••• •••••••••••••••••• ••••••••••••••••••  */
      let isEntit = that.currentSelectProjectList.some((i) => {
        return (i.Consumable || []).some((j) => !j.EntityID);
      });
      if (isEntit) {
        that.$message.error('请选择项目耗材出库仓库');
        return false;
      }
      let isProject = that.currentSelectProjectList.some((i) => {
        return (i.Consumable || []).some((j) => !j.Quantity || !j.EntityID);
      });

      if (isProject) {
        that.$message.error('请填写项目耗材出库数量');
        return false;
      }
      /**  •• 储值卡  •••••••••••••••• •••••••••••••••••• •••••••••••••••••• ••••••••••••••••••  */
      let isEntitySavingCard = that.currentSelectSavingCardList.some((i) => {
        return i.Projects.some((n) => {
          return (n.Consumable || []).some((j) => !j.EntityID);
        });
      });
      if (isEntitySavingCard) {
        that.$message.error('请选择储值卡项目耗材出库仓库');
        return false;
      }

      let isQuantitySavingCard = that.currentSelectSavingCardList.some((i) => {
        return i.Projects.some((n) => {
          return (n.Consumable || []).some((j) => !j.Quantity);
        });
      });
      if (isQuantitySavingCard) {
        that.$message.error('请选择储值卡项目耗材出库数量');
        return false;
      }

      /**  •• 时效卡  •••••••••••••••• •••••••••••••••••• •••••••••••••••••• ••••••••••••••••••  */
      let isEntityTimeCard = that.currentSelectTimeCardList.some((i) => {
        return i.Projects.some((n) => {
          return (n.Consumable || []).some((j) => !j.EntityID);
        });
      });
      if (isEntityTimeCard) {
        that.$message.error('请选择时效卡项目耗材出库仓库');
        return false;
      }

      let isQuantityTimeCard = that.currentSelectTimeCardList.some((i) => {
        return i.Projects.some((n) => {
          return (n.Consumable || []).some((j) => !j.Quantity);
        });
      });
      if (isQuantityTimeCard) {
        that.$message.error('请选择时效卡项目耗材出库数量');
        return false;
      }
      /**  •• 通用次卡  •••••••••••••••• •••••••••••••••••• •••••••••••••••••• ••••••••••••••••••  */
      let isEntityGeneralCard = that.currentSelectGeneralCardList.some((i) => {
        return i.Projects.some((n) => {
          return (n.Consumable || []).some((j) => !j.EntityID);
        });
      });
      if (isEntityGeneralCard) {
        that.$message.error('请选择通用次卡项目耗材出库仓库');
        return false;
      }

      let isQuantityGeneralCard = that.currentSelectGeneralCardList.some((i) => {
        return i.Projects.some((n) => {
          return (n.Consumable || []).some((j) => !j.Quantity);
        });
      });
      if (isQuantityGeneralCard) {
        that.$message.error('请选择通用次卡项目耗材出库数量');
        return false;
      }

      /**  •• 套餐卡项目  •••••••••••••••• •••••••••••••••••• •••••••••••••••••• ••••••••••••••••••  */
      let isQuantityPackageCardProject = that.currentselectPackageCardList.some((i) => {
        return i.packageProjectList.some((n) => {
          return (n.Consumable || []).some((j) => !j.Quantity);
        });
      });
      if (isQuantityPackageCardProject) {
        that.$message.error('请选择套餐卡项目耗材出库数量');
        return false;
      }

      let isEntityPackageCardProject = that.currentselectPackageCardList.some((i) => {
        return i.packageProjectList.some((n) => {
          return (n.Consumable || []).some((j) => !j.EntityID);
        });
      });
      if (isEntityPackageCardProject) {
        that.$message.error('请选择套餐卡项目耗材出库仓库');
        return false;
      }
      /**  •• 套餐卡储值卡  •••••••••••••••• •••••••••••••••••• •••••••••••••••••• ••••••••••••••••••  */
      let isQuantityPackageCardSavingCard = that.currentselectPackageCardList.some((i) => {
        return i.packageSavingCardList.some((m) => {
          return m.Projects.some((n) => {
            return (n.Consumable || []).some((j) => !j.Quantity);
          });
        });
      });
      if (isQuantityPackageCardSavingCard) {
        that.$message.error('请选择套餐卡储值卡项目耗材出库数量');
        return false;
      }

      let isEntityPackageCardSavingCard = that.currentselectPackageCardList.some((i) => {
        return i.packageSavingCardList.some((m) => {
          return m.Projects.some((n) => {
            return (n.Consumable || []).some((j) => !j.EntityID);
          });
        });
      });
      if (isEntityPackageCardSavingCard) {
        that.$message.error('请选择套餐卡储值卡项目耗材出库仓库');
        return false;
      }

      /**  •• 套餐卡通用次卡  •••••••••••••••• •••••••••••••••••• •••••••••••••••••• ••••••••••••••••••  */
      let isQuantityPackageCardGeneralCard = that.currentselectPackageCardList.some((i) => {
        return i.packageGeneralList.some((m) => {
          return m.Projects.some((n) => {
            return (n.Consumable || []).some((j) => !j.Quantity);
          });
        });
      });
      if (isQuantityPackageCardGeneralCard) {
        that.$message.error('请选择套餐卡通用次卡项目耗材出库数量');
        return false;
      }

      let isEntityPackageCardGeneralCard = that.currentselectPackageCardList.some((i) => {
        return i.packageGeneralList.some((m) => {
          return m.Projects.some((n) => {
            return (n.Consumable || []).some((j) => !j.EntityID);
          });
        });
      });
      if (isEntityPackageCardGeneralCard) {
        that.$message.error('请选择套餐卡通用次卡项目耗材出库仓库');
        return false;
      }

      /**  •• 套餐卡时效卡  •••••••••••••••• •••••••••••••••••• •••••••••••••••••• ••••••••••••••••••  */
      let isQuantityPackageCardTimeCard = that.currentselectPackageCardList.some((i) => {
        return i.packageTimeCardList.some((m) => {
          return m.Projects.some((n) => {
            return (n.Consumable || []).some((j) => !j.Quantity);
          });
        });
      });
      if (isQuantityPackageCardTimeCard) {
        that.$message.error('请选择套餐卡时效卡项目耗材出库数量');
        return false;
      }

      let isEntityPackageCardTimeCard = that.currentselectPackageCardList.some((i) => {
        return i.packageTimeCardList.some((m) => {
          return m.Projects.some((n) => {
            return (n.Consumable || []).some((j) => !j.EntityID);
          });
        });
      });
      if (isEntityPackageCardTimeCard) {
        that.$message.error('请选择套餐卡时效卡项目耗材出库仓库');
        return false;
      }

      return true;
    },

    /**  提交 消耗订单  */
    submitConsumeOrderClick: function () {
      var that = this;
      that.treatBillCreateNet(that.getTreatParams());
    },

    getTreatParams() {
      let that = this;
      let treatParams = {};
      treatParams.BillDate = that.getBillDate();
      treatParams.CustomerID = that.customerID;
      treatParams.Remark = that.remark;
      treatParams.Amount = parseFloat(that.totalAmount).toFixed(2);
      treatParams.Project = [];
      treatParams.Product = [];
      treatParams.SavingCard = [];
      treatParams.TimeCard = [];
      treatParams.GeneralCard = [];
      treatParams.BillID = that.TakeOrderNum ? that.TakeOrderNum : '';

      Enumerable.from(that.currentSelectProjectList).forEach((item) => {
        var handle = [];
        item.HandleTypeList.forEach((handleType) => {
          handleType.Employee.forEach((hand) => {
            handle.push({
              TreatHandlerID: handleType.ID,
              EmployeeID: hand.EmployeeID,
              Scale: hand.Discount,
            });
          });
        });
        treatParams.Project.push({
          ProjectAccountID: item.ID,
          ProjectID: item.ProjectID,
          Quantity: item.Quantity,
          Price: item.Price,
          Amount: item.UnitAmount,
          TotalAmount: parseFloat(item.TotalAmount).toFixed(2),
          IsLargess: item.IsLargess,
          ProjectTreatHandler: handle,
          Remark: item.Remark,
          Consumable: (item.Consumable || []).map((i) => {
            return {
              EntityID: i.EntityID, //门店ID
              ProductID: i.ProductID, //产品编号
              MinimumUnitID: i.MinimumUnitID, //最小包装单位id
              Quantity: i.Quantity, //库存数量
            };
          }),
        });
      });

      /**  产品  */
      that.currentSelectProductList.forEach((item) => {
        var handle = [];
        item.HandleTypeList.forEach((handleType) => {
          handleType.Employee.forEach((hand) => {
            handle.push({
              TreatHandlerID: handleType.ID,
              EmployeeID: hand.EmployeeID,
              Scale: hand.Discount,
            });
          });
        });
        treatParams.Product.push({
          ProductAccountID: item.ID,
          ProductID: item.ProductID,
          Quantity: item.Quantity,
          Price: item.Price,
          Amount: item.UnitAmount,
          TotalAmount: parseFloat(item.TotalAmount).toFixed(2),
          IsLargess: item.IsLargess,
          Remark: item.Remark,
          ProductTreatHandler: handle,
        });
      });
      /**  储值卡  */
      that.currentSelectSavingCardList.forEach((item) => {
        item.Projects.forEach((Project) => {
          var handle = [];
          Project.HandleTypeList.forEach((handleType) => {
            handleType.Employee.forEach((hand) => {
              handle.push({
                TreatHandlerID: handleType.ID,
                EmployeeID: hand.EmployeeID,
                Scale: hand.Discount,
              });
            });
          });
          treatParams.SavingCard.push({
            ID: item.ID,
            Type: item.Type,
            SavingCardAccountID: item.AccountID,
            ProjectID: Project.ID,
            Quantity: Project.Quantity,
            Price: Project.Price,
            TotalAmount: parseFloat(Project.TotalAmount || 0).toFixed(2),
            CardPreferentialAmount: parseFloat(Project.CardPreferentialAmountTotal || 0).toFixed(2),
            PricePreferentialAmount: parseFloat(Project.PricePreferentialAmount || 0).toFixed(2),
            SavingCardTreatHandler: handle,
            Remark: Project.Remark,
            MemberPreferentialAmount: Number(Project.MemberPreferentialAmountTotal || 0).toFixed(2),
            Consumable: (Project.Consumable || []).map((i) => {
              return {
                EntityID: i.EntityID, //门店ID
                ProductID: i.ProductID, //产品编号
                MinimumUnitID: i.MinimumUnitID, //最小包装单位id
                Quantity: i.Quantity, //库存数量
              };
            }),
          });
        });
      });
      /**  时效卡  */
      that.currentSelectTimeCardList.forEach((item) => {
        item.Projects.forEach((Project) => {
          var handle = [];
          Project.HandleTypeList.forEach((handleType) => {
            handleType.Employee.forEach((hand) => {
              handle.push({
                TreatHandlerID: handleType.ID,
                EmployeeID: hand.EmployeeID,
                Scale: hand.Discount,
              });
            });
          });

          treatParams.TimeCard.push({
            TimeCardAccountID: item.ID,
            ProjectID: Project.ID,
            Quantity: Project.Quantity,
            Price: Project.Price,
            Amount: Project.UnitAmount,
            TotalAmount: parseFloat(Project.TotalAmount).toFixed(2),
            IsLargess: Project.IsLargess,
            TimeCardTreatHandler: handle,
            Remark: Project.Remark,
            Consumable: (Project.Consumable || []).map((i) => {
              return {
                EntityID: i.EntityID, //门店ID
                ProductID: i.ProductID, //产品编号
                MinimumUnitID: i.MinimumUnitID, //最小包装单位id
                Quantity: i.Quantity, //库存数量
              };
            }),
          });
        });
      });
      /**  通用次卡  */
      that.currentSelectGeneralCardList.forEach((item) => {
        item.Projects.forEach((Project) => {
          var handle = [];
          Project.HandleTypeList.forEach((handleType) => {
            handleType.Employee.forEach((hand) => {
              handle.push({
                TreatHandlerID: handleType.ID,
                EmployeeID: hand.EmployeeID,
                Scale: hand.Discount,
              });
            });
          });
          /**  这里的优惠金额明天确认 之后修改  */
          treatParams.GeneralCard.push({
            GeneralCardAccountID: item.ID,
            ProjectID: Project.ID,
            Quantity: Project.Quantity,
            Price: Project.Price,
            CardTreatTimes: Project.ConsumeAmount * Project.Quantity,
            Amount: Project.UnitAmount,
            TotalAmount: parseFloat(Project.TotalAmount).toFixed(2),
            IsLargess: item.IsLargess,
            GeneralCardTreatHandler: handle,
            Remark: Project.Remark,
            Consumable: (Project.Consumable || []).map((i) => {
              return {
                EntityID: i.EntityID, //门店ID
                ProductID: i.ProductID, //产品编号
                MinimumUnitID: i.MinimumUnitID, //最小包装单位id
                Quantity: i.Quantity, //库存数量
              };
            }),
          });
        });
      });

      /**  套餐卡  */
      that.currentselectPackageCardList.forEach((item) => {
        item.packageProjectList.forEach((element) => {
          var handle = [];
          element.HandleTypeList.forEach((handleType) => {
            handleType.Employee.forEach((hand) => {
              handle.push({
                TreatHandlerID: handleType.ID,
                EmployeeID: hand.EmployeeID,
                Scale: hand.Discount,
              });
            });
          });

          treatParams.Project.push({
            ProjectAccountID: element.ID,
            ProjectID: element.ProjectID,
            Quantity: element.Quantity,
            Price: element.Price,
            Amount: element.UnitAmount,
            TotalAmount: parseFloat(element.TotalAmount).toFixed(2),
            IsLargess: element.IsLargess,
            ProjectTreatHandler: handle,
            Remark: element.Remark,
            Consumable: (element.Consumable || []).map((i) => {
              return {
                EntityID: i.EntityID, //门店ID
                ProductID: i.ProductID, //产品编号
                MinimumUnitID: i.MinimumUnitID, //最小包装单位id
                Quantity: i.Quantity, //库存数量
              };
            }),
          });
        });

        item.packageProductList.forEach((element) => {
          var productHandle = [];
          element.HandleTypeList.forEach((handleType) => {
            handleType.Employee.forEach((hand) => {
              productHandle.push({
                TreatHandlerID: handleType.ID,
                EmployeeID: hand.EmployeeID,
                Scale: hand.Discount,
              });
            });
          });

          treatParams.Product.push({
            ProductAccountID: element.ID,
            ProductID: element.ProductID,
            Quantity: element.Quantity,
            Price: element.Price,
            Amount: element.UnitAmount,
            TotalAmount: parseFloat(element.TotalAmount).toFixed(2),
            IsLargess: element.IsLargess,
            Remark: element.Remark,
            ProductTreatHandler: productHandle,
          });
        });

        item.packageSavingCardList.forEach((savingCard) => {
          savingCard.Projects.forEach((project) => {
            var handle = [];
            project.HandleTypeList.forEach((handleType) => {
              handleType.Employee.forEach((hand) => {
                handle.push({
                  TreatHandlerID: handleType.ID,
                  EmployeeID: hand.EmployeeID,
                  Scale: hand.Discount,
                });
              });
            });

            treatParams.SavingCard.push({
              ID: savingCard.ID,
              Type: savingCard.Type,
              SavingCardAccountID: savingCard.AccountID,
              ProjectID: project.ID,
              Quantity: project.Quantity,
              Price: project.Price,
              TotalAmount: parseFloat(project.TotalAmount).toFixed(2),
              CardPreferentialAmount: parseFloat(project.CardPreferentialAmountTotal || 0).toFixed(2),
              CardPreferentialAmountTotal: parseFloat(project.CardPreferentialAmountTotal || 0).toFixed(2),
              PricePreferentialAmount: parseFloat(project.PricePreferentialAmount || 0).toFixed(2),
              SavingCardTreatHandler: handle,
              Remark: project.Remark,
              MemberPreferentialAmount: Number(project.MemberPreferentialAmountTotal || 0).toFixed(2),
              Consumable: (project.Consumable || []).map((i) => {
                return {
                  EntityID: i.EntityID, //门店ID
                  ProductID: i.ProductID, //产品编号
                  MinimumUnitID: i.MinimumUnitID, //最小包装单位id
                  Quantity: i.Quantity, //库存数量
                };
              }),
            });
          });
        });

        item.packageTimeCardList.forEach((timeCard) => {
          timeCard.Projects.forEach((project) => {
            var handle = [];
            project.HandleTypeList.forEach((handleType) => {
              handleType.Employee.forEach((hand) => {
                handle.push({
                  TreatHandlerID: handleType.ID,
                  EmployeeID: hand.EmployeeID,
                  Scale: hand.Discount,
                });
              });
            });

            treatParams.TimeCard.push({
              TimeCardAccountID: timeCard.ID,
              ProjectID: project.ID,
              Quantity: project.Quantity,
              Price: project.Price,
              Amount: project.UnitAmount,
              TotalAmount: parseFloat(project.TotalAmount).toFixed(2),
              IsLargess: timeCard.IsLargess,
              TimeCardTreatHandler: handle,
              Remark: project.Remark,
              Consumable: (project.Consumable || []).map((i) => {
                return {
                  EntityID: i.EntityID, //门店ID
                  ProductID: i.ProductID, //产品编号
                  MinimumUnitID: i.MinimumUnitID, //最小包装单位id
                  Quantity: i.Quantity, //库存数量
                };
              }),
            });
          });
        });

        item.packageGeneralList.forEach((generalCard) => {
          generalCard.Projects.forEach((project) => {
            var handle = [];
            project.HandleTypeList.forEach((handleType) => {
              handleType.Employee.forEach((hand) => {
                handle.push({
                  TreatHandlerID: handleType.ID,
                  EmployeeID: hand.EmployeeID,
                  Scale: hand.Discount,
                });
              });
            });

            treatParams.GeneralCard.push({
              GeneralCardAccountID: generalCard.ID,
              ProjectID: project.ID,
              Quantity: project.Quantity,
              Price: project.Price,
              CardTreatTimes: Number(project.ConsumeAmount) * Number(project.Quantity),
              Amount: project.UnitAmount,
              TotalAmount: parseFloat(project.TotalAmount).toFixed(2),
              IsLargess: generalCard.IsLargess,
              GeneralCardTreatHandler: handle,
              Remark: project.Remark,
              Consumable: (project.Consumable || []).map((i) => {
                return {
                  EntityID: i.EntityID, //门店ID
                  ProductID: i.ProductID, //产品编号
                  MinimumUnitID: i.MinimumUnitID, //最小包装单位id
                  Quantity: i.Quantity, //库存数量
                };
              }),
            });
          });
        });
      });

      return treatParams;
    },

    /**  继续开单  */
    continueCreateConsumeOrder() {
      var that = this;
      that.remark = '';
      that.dialoConsumeSucceed = false;
      that.$emit('continueCreateConsume');
    },

    /**  搜索消耗商品  */
    searchGoodsClick() {
      var that = this;
      if (that.customerID == null) {
        that.$message.error('请填写客户信息');
        return;
      }
      that.tabPane = '';
      that.treatProjectAccount();
      that.treatSavingCardAccountNet();
      that.treatPackageCardAccounttNet();
      that.treatTimeCardAccountNet();
      that.treatGeneralCardAccountNet();
      that.treatProductAccount();
      /**  经手人  */
      that.treatProjectHandlerNet();
      that.treatProductHandlerNet();
      that.treatSavingCardHandlerNet();
    },

    /**  清空搜索内容  */
    clearClick: function () {
      var that = this;
      that.searchGoodsClick();
    },

    /**  开单时间  */
    getBillDate: function () {
      var that = this;
      return that.isSupplement ? that.billDate : date.formatDate.format(new Date(), 'YYYY-MM-DD hh:mm:ss');
    },

    /**  经手人选择事件  */
    // 点击选择经手人呢 0 项目 1、产品 2、储值卡 3、 套餐卡 4、时效卡 5、通用次卡
    employeeHandleClick: function (type, item) {
      var that = this;
      that.tabHandle = '0';
      switch (type) {
        case 1:
          that.treatHandlerList = that.productTreatHandlers;
          break;
        case 2:
          that.treatHandlerList = that.savingCardTreatHandlers;
          break;

        case 0:
        case 4:
        case 5:
          that.treatHandlerList = that.projectTreatHandlers;
          break;
      }
      var emplayee = [];
      item.HandleTypeList.forEach(function (hand) {
        hand.Employee.forEach(function (emp) {
          emplayee.push({ ID: emp.ID, Discount: emp.Discount });
        });
      });
      that.treatHandlerList.forEach(function (handler) {
        handler.Employee.forEach(function (emp) {
          emp.Checked = false;
          emp.Discount = '';
          emplayee.forEach(function (i) {
            if (emp.ID == i.ID) {
              emp.Checked = true;
              emp.Discount = i.Discount;
            }
          });
        });
      });
      that.dialogVisible = true;
      that.cardSelectType = type;
      that.cardSelectItem = item;
    },
    /**  经手人确认事件  */
    submitHandleClick: function () {
      var that = this;
      var goodsHandlerList = JSON.parse(JSON.stringify(that.treatHandlerList));
      if (
        goodsHandlerList.some((item) => {
          return (
            item.Employee.reduce((pre, pri) => {
              return pre + Number(pri.Discount);
            }, 0) > 100
          );
        })
      ) {
        that.$message.error('比例总和不能超过100%');
        return;
      }
      goodsHandlerList.forEach(function (item) {
        item.Employee = Enumerable.from(item.Employee)
          .where(function (i) {
            return i.Checked;
          })
          .toArray();
      });
      switch (that.cardSelectType) {
        case 0:
          that.cardSelectItem.HandleTypeList = [];
          that.cardSelectItem.HandleTypeList = goodsHandlerList;
          break;

        case 1:
          that.cardSelectItem.HandleTypeList = [];
          that.cardSelectItem.HandleTypeList = goodsHandlerList;
          break;
        case 2:
          that.cardSelectItem.HandleTypeList = [];
          that.cardSelectItem.HandleTypeList = goodsHandlerList;

          break;

        case 3:
          that.cardSelectItem.HandleTypeList = [];
          that.cardSelectItem.HandleTypeList = goodsHandlerList;

          break;

        case 4:
          that.cardSelectItem.HandleTypeList = [];
          that.cardSelectItem.HandleTypeList = goodsHandlerList;
          break;
        case 5:
          that.cardSelectItem.HandleTypeList = [];
          that.cardSelectItem.HandleTypeList = goodsHandlerList;
          break;

        default:
          break;
      }

      that.dialogVisible = false;
    },

    // 经手人选择
    handlerCheckedChange: function (row, item) {
      let checkedArr = row.filter((i) => {
        return i.Checked;
      });
      row.forEach((val) => {
        if (val.Checked) {
          val.Discount = Math.floor(100 / checkedArr.length);
          if (val.EmployeeID == checkedArr[checkedArr.length - 1].EmployeeID) {
            val.Discount = Math.ceil(100 / checkedArr.length);
          }
        }
      });
      var discount = 0;
      var employee = Enumerable.from(row)
        .where(function (i) {
          return i.Checked;
        })
        .toArray();
      employee.forEach(function (emp) {
        var Discount = emp.Discount;
        if (Discount == '') {
          Discount = 0;
        }
        discount = parseFloat(discount) + parseFloat(Discount);
      });
      if (!item.Checked) {
        item.Discount = '';
      } else {
        if (item.Discount == '') {
          if (discount > 100) {
            item.Discount = 0;
          } else {
            item.Discount = 100 - discount;
          }
        }
      }
    },
    // 百分比
    handlerPercentChange: function (row, item, type) {
      var that = this;
      var discount = 0;
      if (item.Discount != '') {
        item.Discount = parseFloat(item.Discount);
      }
      if (type !== 'dialog') {
        if (item.Discount > 100) {
          item.Discount = 100;
        }
      }
      var employee = Enumerable.from(row)
        .where(function (i) {
          return i.Checked;
        })
        .toArray();
      employee.forEach(function (emp) {
        var Discount = emp.Discount;
        if (Discount == '') {
          Discount = 0;
        }
        discount = parseFloat(discount) + parseFloat(Discount);
      });
      if (type !== 'dialog') {
        if (parseFloat(discount) > 100) {
          item.Discount = 100 - (discount - item.Discount);
          that.$message.error('比例总和不能超过100%');
        }
      }
    },
    // 删除经手人
    removeHandleClick: function (item, index) {
      item.Employee.splice(index, 1);
    },
    /** 数据网络请求  */
    /**  52.1.项目存量列表  */
    treatProjectAccount: function () {
      var that = this;
      that.loading = true;
      var params = {
        CustomerID: that.customerID,
        Name: that.searchName,
      };
      API.treatGoodsProjectAccount(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.treatProjectAccountList = res.Data;
            if (that.tabPane == '' && that.treatProjectAccountList.length > 0) {
              that.tabPane = '0';
            }
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },

    /**  52.2.产品存量列表  */
    treatProductAccount: function () {
      var that = this;
      that.loading = true;
      var params = {
        CustomerID: that.customerID,
        Name: that.searchName,
      };
      API.treatGoodsProductAccount(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.treatProductAccountList = res.Data;
            if (that.tabPane == '' && that.treatProductAccountList.length > 0) {
              that.tabPane = '5';
            }
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },
    /**  52.3.储值卡存量列表  */
    treatSavingCardAccountNet: function () {
      var that = this;
      that.loading = true;
      var params = {
        CustomerID: that.customerID,
        BillDate: that.getBillDate(),
        Name: that.searchName,
      };
      API.treatGoodsSavingCardAccount(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.treatSavingCardAccountList = res.Data;
            if (that.tabPane == '' && that.treatSavingCardAccountList.length > 0) {
              that.tabPane = '1';
            }
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },
    /**  52.4.储值卡消耗适用项目  */
    /**  isInit：是否初始化，点击储值卡传true，搜索传false  */
    treatSavingCardAccountProjectNet: function (isInit) {
      var that = this;
      that.loading = true;
      var params = {
        Type: that.selectSavingcardItem.Type,
        SavingCardID: that.selectSavingcardItem.SavingCardID,
        Name: that.searchSavingcardProjectName,
      };
      API.treatGoodsSavingCardAccountProject(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.currentSavingcardProjectCategoty = res.Data;
            if (isInit) {
              if (that.currentSavingcardProjectCategoty.length > 0) {
                that.dialogSavingCard = true;
              } else {
                that.$message.error({
                  message: '当前储值卡未配置项目，请配置项目再消耗。',
                  duration: 2000,
                });
                return;
              }
            }
            that.currentSavingcardProjectCategoty.forEach((cItem) => {
              cItem.Child.forEach((item) => {
                if (item.IsGoods == '0') {
                  item.Child.forEach((element) => {
                    if (element.PriceType == '1') {
                      element.PreferentialPrice = Number(element.Price * element.DiscountPrice).toFixed(2);

                      element.discount = element.DiscountPrice * 10; //计算几折
                      if (element.discount == 10) {
                        element.discountName = '无折扣';
                      } else {
                        element.discountName = parseFloat(element.discount || 0).toFixed(2) + '折';
                      }
                      element.CardPreferentialAmount = element.Price - element.PreferentialPrice;
                    }

                    if (element.PriceType == '2') {
                      element.PreferentialPrice = element.DiscountPrice;
                      element.discount = (element.DiscountPrice / element.Price) * 10;
                      if (element.discount == 10) {
                        element.discountName = '无折扣';
                      } else {
                        element.discountName = parseFloat(element.discount || 0).toFixed(2) + '折';
                      }
                      element.CardPreferentialAmount = element.Price - element.PreferentialPrice;
                    }
                  });
                }
              });
            });

            if (that.currentSavingcardProjectCategoty.length > 0) {
              that.currentCategoryIndex = 0;
              that.currentSavingcardProject = that.currentSavingcardProjectCategoty[that.currentCategoryIndex].Child;
              that.currentSavingActiveName = Enumerable.from(that.currentSavingcardProject)
                .select((val) => val.ID)
                .toArray();
            }
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },
    /**  isInit：是否初始化，点击储值卡传true，搜索传false    */
    async treatGoodsAccount_savingCardAccountProjectCategory(isInit) {
      let that = this;
      try {
        let params = {
          Type: that.selectSavingcardItem.Type,
          SavingCardID: that.selectSavingcardItem.SavingCardID,
          Name: that.searchSavingcardProjectName, //
        };
        let res = await API.treatGoodsAccount_savingCardAccountProjectCategory(params);
        if (res.StateCode == 200) {
          that.currentSavingcardProjectCategoty = res.Data;
          if (isInit) {
            if (that.currentSavingcardProjectCategoty.length <= 0) {
              that.$message.error({
                message: '当前储值卡未配置项目，请配置项目再消耗。',
                duration: 2000,
              });
              return;
            }
          }

          if (that.currentSavingcardProjectCategoty && that.currentSavingcardProjectCategoty.length > 0) {
            that.savingcardProjectCategotyIndex = 0;
            let firstItem = that.currentSavingcardProjectCategoty[that.savingcardProjectCategotyIndex];

            if (firstItem.Child && firstItem.Child.length) {
              that.currentSavingcardProjectSecondCategoty = firstItem.Child;
              that.savingcardProjectSecondCategotyIndex = 0;
              that.selectSavingCardCategoryItem = firstItem.Child[that.savingcardProjectSecondCategotyIndex];
              that.treatGoodsAccount_savingCardAccountProjectByCategory();
            }
          } else {
            that.projectSecondCategory = [];
            that.projectList = [];
          }

          that.dialogSavingCard = true;
        } else {
          that.$message.error(res.Message);
        }
      } catch (error) {
        that.$message.error(error);
      }
    },
    /** 请求项目   */
    async treatGoodsAccount_savingCardAccountProjectByCategory() {
      let that = this;
      try {
        that.savingCardLoading = true;
        let params = {
          PageNum: that.savingCardPaginations.page,
          SavingCardID: that.selectSavingcardItem.SavingCardID,
          Type: that.selectSavingcardItem.Type,
          Name: that.searchSavingcardProjectName, //
          CategoryID: that.selectSavingCardCategoryItem.CategoryID,
        };
        let res = await API.treatGoodsAccount_savingCardAccountProjectByCategory(params);
        if (res.StateCode == 200) {
          that.currentSavingcardProject = res.List.map((i) => {
            if (i.PriceType == '1') {
              i.PreferentialPrice = Number(i.Price * i.DiscountPrice).toFixed(2);

              i.discount = i.DiscountPrice * 10; //计算几折
              if (i.discount == 10) {
                i.discountName = '无折扣';
              } else {
                i.discountName = parseFloat(i.discount || 0).toFixed(2) + '折';
              }
              i.CardPreferentialAmount = i.Price - i.PreferentialPrice;
            }

            if (i.PriceType == '2') {
              i.PreferentialPrice = i.DiscountPrice;
              i.discount = (i.DiscountPrice / i.Price) * 10;
              if (i.discount == 10) {
                i.discountName = '无折扣';
              } else {
                i.discountName = parseFloat(i.discount || 0).toFixed(2) + '折';
              }
              i.CardPreferentialAmount = i.Price - i.PreferentialPrice;
            }
            return i;
          });
          that.savingCardPaginations.total = res.Total;
        } else {
          that.$message.error(res.Message);
        }
        that.savingCardLoading = false;
      } catch (error) {
        that.savingCardLoading = false;
        that.$message.error(error);
      }
    },

    /**  52.5.通用次卡存量列表  */
    treatGeneralCardAccountNet: function () {
      var that = this;
      that.loading = true;
      var params = {
        CustomerID: that.customerID,
        BillDate: that.getBillDate(),
        Name: that.searchName,
      };
      API.treatGoodsGeneralCardAccount(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.treatGeneralCardAccountList = res.Data;
            if (that.tabPane == '' && that.treatGeneralCardAccountList.length > 0) {
              that.tabPane = '3';
            }
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },
    /*  */
    treatGoodsAccount_generalCardHistoricalData(init) {
      let that = this;
      let params = {
        PageNum: that.generalCardPaginations.page,
        Name: that.searchGeneralcarPorjectName,
        GeneralCardAccountID: that.selectGeneralcardItem.ID, //通用此卡账户ID
        GeneralCardID: that.selectGeneralcardItem.GeneralCardID, //通用此卡ID
      };
      API.treatGoodsAccount_generalCardHistoricalData(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.generalCardHistoricalData = res.List;

            if (init && that.generalCardHistoricalData.length > 0) {
              that.currentGeneralcarProjectCategoty.unshift({
                ParentName: '历史消耗',
                ParentID: '',
              });
              that.isShowHistoricalData = true;
            }
            that.generalCardPaginations.total = res.Total;
          } else {
            that.$message.error(res.Message);
          }
        })
        .catch(() => {});
    },
    /**  52.6.通用次卡消耗适用项目  */
    treatGeneralCardAccountProjectNet: function (isInit) {
      var that = this;
      that.loading = true;
      var params = {
        GeneralCardID: that.selectGeneralcardItem.GeneralCardID,
        Name: that.searchGeneralcarPorjectName,
        GeneralCardAccountID: that.selectGeneralcardItem.ID,
      };
      API.treatGoodsGeneralCardAccountProject(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.currentGeneralcarProjectCategoty = res.Data;
            if (isInit) {
              if (that.currentGeneralcarProjectCategoty.length > 0) {
                that.dialogGeneralCard = true;
              } else {
                that.$message.error({
                  message: '当前通用次卡未配置项目，请配置项目再消耗。',
                  duration: 2000,
                });
                return;
              }
            }

            if (that.currentGeneralcarProjectCategoty.length > 0) {
              that.currentCategoryIndex = 0;
              that.currentGeneralcarProject = that.currentGeneralcarProjectCategoty[that.currentCategoryIndex].Child;
              that.currentGeneralcarActiveName = Enumerable.from(that.currentGeneralcarProject)
                .select((val) => val.ID)
                .toArray();
            }
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },
    /**    */
    async treatGoodsAccount_generalCardAccountProjectCategory(isInit) {
      let that = this;
      try {
        let params = {
          GeneralCardID: that.selectGeneralcardItem.GeneralCardID,
          Name: that.searchGeneralcarPorjectName,
        };
        let res = await API.treatGoodsAccount_generalCardAccountProjectCategory(params);
        if (res.StateCode == 200) {
          that.currentGeneralcarProjectCategoty = res.Data;
          // that.currentGeneralcarProjectCategoty.unshift({
          //   ParentName:"历史消耗",
          //   ParentID:""
          // })
          that.generalCardProjectCategotyIndex = 0;
          if (isInit) {
            if (that.currentGeneralcarProjectCategoty.length <= 0) {
              that.$message.error({
                message: '当前通用次卡未配置项目，请配置项目再消耗。',
                duration: 2000,
              });
              return;
            }
          }
          if (that.currentGeneralcarProjectCategoty && that.currentGeneralcarProjectCategoty.length > 0) {
            that.generalCardProjectCategotyIndex = 0;
            let firstItem = that.currentGeneralcarProjectCategoty[that.generalCardProjectCategotyIndex];

            if (firstItem.Child && firstItem.Child.length) {
              that.currentGeneralcarProjectSecondCategoty = firstItem.Child;
              that.generalCardProjectSecondCategotyIndex = 0;
              that.selectGenearlCardCategoryItem = firstItem.Child[that.generalCardProjectSecondCategotyIndex];
              that.treatGoodsAccount_generalCardAccountProjectByCategory();
            }
          } else {
            that.currentGeneralcarProjectCategoty = [];
            that.currentGeneralcarProjectSecondCategoty = [];
            that.currentGeneralcarProject = [];
          }
          that.dialogGeneralCard = true;
        } else {
          that.$message.error(res.Message);
        }
      } catch (error) {
        that.$message.error(error);
      }
    },
    /**    */
    async treatGoodsAccount_generalCardAccountProjectByCategory() {
      let that = this;
      try {
        let params = {
          PageNum: that.generalCardPaginations.page,
          GeneralCardAccountID: that.selectGeneralcardItem.ID,
          GeneralCardID: that.selectGeneralcardItem.GeneralCardID,
          Name: that.searchGeneralcarPorjectName, //
          CategoryID: that.selectGenearlCardCategoryItem.CategoryID,
        };
        that.generalCardPrjectLoading = true;
        let res = await API.treatGoodsAccount_generalCardAccountProjectByCategory(params);
        if (res.StateCode == 200) {
          that.currentGeneralcarProject = res.List;
          that.generalCardPaginations.total = res.Total;
        } else {
          that.$message.error(res.Message);
        }
        that.generalCardPrjectLoading = false;
      } catch (error) {
        that.generalCardPrjectLoading = false;
        that.$message.error(error);
      }
    },

    /**  52.7.时效卡存量列表 */
    treatTimeCardAccountNet: function () {
      var that = this;
      that.loading = true;
      var params = {
        CustomerID: that.customerID,
        BillDate: that.getBillDate(),
        Name: that.searchName,
      };
      API.treatGoodsTimeCardAccount(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.treatTimeCardAccountList = res.Data;
            if (that.tabPane == '' && that.treatTimeCardAccountList.length > 0) {
              that.tabPane = '2';
            }
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },
    /**  52.8.时效卡消耗适用项目 */
    treatTimeCardAccountProjectNet: function (isInit) {
      var that = this;
      that.loading = true;
      var params = {
        TimeCardID: that.selectTimecardItem.TimeCardID,
        Name: that.searchTimeCardPorjectName,
      };
      API.treatGoodsTimeCardAccountProject(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.currentTimeCardProjectCategoty = res.Data;
            if (isInit) {
              if (that.currentTimeCardProjectCategoty.length > 0) {
                that.dialogTimeCard = true;
              } else {
                that.$message.error({
                  message: '当前时效卡未配置项目，请配置项目再消耗。',
                  duration: 2000,
                });
                return;
              }
            }
            if (that.currentTimeCardProjectCategoty.length > 0) {
              that.currentCategoryIndex = 0;
              that.currentTimeCardProject = that.currentTimeCardProjectCategoty[that.currentCategoryIndex].Child;
              // that.currentTimeCardActiveName =
              //   that.currentTimeCardProject[0].ID;
              that.currentTimeCardActiveName = Enumerable.from(that.currentTimeCardProject)
                .select((val) => val.ID)
                .toArray();
            }
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },
    /**    */
    async treatGoodsAccount_timeCardAccountProjectCategory(isInit) {
      let that = this;
      try {
        let params = {
          TimeCardID: that.selectTimecardItem.TimeCardID,
          Name: that.searchTimeCardPorjectName,
        };
        let res = await API.treatGoodsAccount_timeCardAccountProjectCategory(params);
        if (res.StateCode == 200) {
          that.currentTimeCardProjectCategoty = res.Data;
          if (isInit) {
            if (that.currentTimeCardProjectCategoty.length <= 0) {
              that.$message.error({
                message: '当前时效卡未配置项目，请配置项目再消耗。',
                duration: 2000,
              });
              return;
            }
          }
          if (that.currentTimeCardProjectCategoty && that.currentTimeCardProjectCategoty.length > 0) {
            that.timeCardProjectCategotyIndex = 0;
            let firstItem = that.currentTimeCardProjectCategoty[that.timeCardProjectCategotyIndex];

            if (firstItem.Child && firstItem.Child.length) {
              that.currentTimecarProjectSecondCategoty = firstItem.Child;
              that.timeCardProjectSecondCategotyIndex = 0;
              that.selectTimeCardCategoryItem = firstItem.Child[that.timeCardProjectSecondCategotyIndex];
              that.treatGoodsAccount_timeCardAccountProjectByCategory();
            }
          } else {
            that.currentTimeCardProjectCategoty = [];
            that.currentTimecarProjectSecondCategoty = [];
            that.currentTimeCardProject = [];
          }
          that.dialogTimeCard = true;
        } else {
          that.$message.error(res.Message);
        }
      } catch (error) {
        that.$message.error(error);
      }
    },
    /**    */
    async treatGoodsAccount_timeCardAccountProjectByCategory() {
      let that = this;
      try {
        let params = {
          PageNum: that.timeCardPaginations.page,
          TimeCardID: that.selectTimecardItem.TimeCardID,
          Name: that.searchTimeCardPorjectName, //
          CategoryID: that.selectTimeCardCategoryItem.CategoryID,
        };
        that.timeCardPrjectLoading = true;
        let res = await API.treatGoodsAccount_timeCardAccountProjectByCategory(params);
        if (res.StateCode == 200) {
          that.currentTimeCardProject = res.List;
          that.timeCardPaginations.total = res.Total;
        } else {
          that.$message.error(res.Message);
        }
        that.timeCardPrjectLoading = false;
      } catch (error) {
        that.timeCardPrjectLoading = false;
        that.$message.error(error);
      }
    },
    /**  52.9.套餐卡存量列表 */
    treatPackageCardAccounttNet: function () {
      var that = this;
      that.loading = true;
      var params = {
        CustomerID: that.customerID,
        BillDate: that.getBillDate(),
        Name: that.searchName,
      };
      API.treatGoodsPackageCardAccount(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.treatGoodsPackageCardAccountList = res.Data;
            if (that.treatGoodsPackageCardAccountList.length > 0) {
              that.currentSelectPackageItem = that.treatGoodsPackageCardAccountList[0];
              (that.currentPackCategoryIndex = 0), that.treatPackageCardAccountDetailsNet();
            }
            if (that.tabPane == '' && that.treatGoodsPackageCardAccountList.length > 0) {
              that.tabPane = '4';
            }
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },
    /**  52.10.套餐卡存量明细 */
    treatPackageCardAccountDetailsNet: function () {
      var that = this;
      that.packageDetailLoading = true;
      var params = {
        PackageCardAccountID: that.currentSelectPackageItem.ID,
        BillDate: that.getBillDate(),
      };
      that.treatGoodsPackageCardAccountDetailsList = {};
      API.treatGoodsPackageCardAccountDetails(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.treatGoodsPackageCardAccountDetailsList = res.Data;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.packageDetailLoading = false;
        });
    },
    /**  52.11.获取项目消耗经手人 */
    treatProjectHandlerNet: function () {
      var that = this;
      that.loading = true;
      var params = {};
      API.treatGoodsProjectHandler(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.projectTreatHandlers = res.Data;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },
    /**  52.12.获取产品消耗经手人 */
    treatProductHandlerNet: function () {
      var that = this;
      that.loading = true;
      var params = {};
      API.treatGoodsProductHandler(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.productTreatHandlers = res.Data;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },
    /**  52.13.获取储值卡消耗经手人 */
    treatSavingCardHandlerNet: function () {
      var that = this;
      that.loading = true;
      var params = {
        // GeneralCardID: "",
      };
      API.treatGoodsSavingCardHandler(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.savingCardTreatHandlers = res.Data;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },
    /**  52.14.消耗结账 */
    treatBillCreateNet: function (treatParams) {
      var that = this;
      that.submitLoading = true;

      API.treatBillCreate(treatParams)
        .then((res) => {
          if (res.StateCode == 200) {
            that.consumeTotalAmount = treatParams.Amount;
            that.currentSelectProjectList = [];
            that.currentSelectProductList = [];
            that.currentSelectSavingCardList = [];
            that.currentSelectTimeCardList = [];
            that.currentSelectGeneralCardList = [];
            that.currentselectPackageCardList = [];
            // that.remark = "";
            that.changMemberOrType();
            that.consumeBillNumber = res.Message;
            that.dialoConsumeSucceed = true;
            that.TakeOrderNum = '';
            that.showTreatInfo();
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.dialogBill = false;
          that.submitLoading = false;
        });
    },

    /**消耗 详情 数据    */
    showTreatInfo: function () {
      var that = this;
      let params = {
        ID: that.consumeBillNumber,
      };
      that.printLoading = true;
      orderAPI
        .treatBillinfo(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.treatInfo = res.Data;
            that.getPrintTemplate_list();
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.printLoading = false;
          that.getReceiptConfig();
        });
    },
    /**  获取小票配置信息  */
    getReceiptConfig() {
      var that = this;
      cashierAPI
        .getReceiptConfigBill()
        .then((res) => {
          if (res.StateCode == 200) {
            that.cashierReceipt = res.Data;
          }
        })
        .finally(() => {});
    },

    mathAbsData: function (item) {
      return Math.abs(item);
    },

    /**    */
    createPendingOrderClick() {
      let that = this;
      if (
        (that.currentSelectProjectList.length <= 0) &
        (that.currentSelectProductList.length <= 0) &
        (that.currentSelectTimeCardList.length <= 0) &
        (that.currentselectPackageCardList.length <= 0) &
        (that.currentSelectGeneralCardList.length <= 0) &
        (that.currentSelectSavingCardList.length <= 0)
      ) {
        that.$message.error('请选择商品！');
        return;
      }

      let isChenck = that.checkEntityOrQuantity();
      if (!isChenck) {
        return;
      }

      if (that.getBillDate() == null) {
        that.$message.error('请输入补录日期');
        return;
      }

      that
        .$confirm('是否确定挂单信息？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })
        .then(() => {
          that.treatBillPendingOrder_create();
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消挂单',
          });
        });
    },

    /**  挂单  */
    async treatBillPendingOrder_create() {
      let that = this;
      let Content = {
        currentSelectProjectList: that.currentSelectProjectList,
        currentSelectProductList: that.currentSelectProductList,
        currentSelectSavingCardList: that.currentSelectSavingCardList,
        currentSelectTimeCardList: that.currentSelectTimeCardList,
        currentSelectGeneralCardList: that.currentSelectGeneralCardList,
        currentselectPackageCardList: that.currentselectPackageCardList,
      };
      let params = Object.assign({ Content: JSON.stringify(Content) }, that.getTreatParams());
      let res = await draftAPI.treatBillPendingOrder_create(params);
      if (res.StateCode == 200) {
        that.currentSelectProjectList = [];
        that.currentSelectProductList = [];
        that.currentSelectSavingCardList = [];
        that.currentSelectTimeCardList = [];
        that.currentSelectGeneralCardList = [];
        that.currentselectPackageCardList = [];
        that.remark = '';
        that.changMemberOrType();
        that.consumeBillNumber = res.Message;
        that.TakeOrderNum = '';
        that.$message.success('挂单成功');
      } else {
        that.$message.error(res.Message);
      }
    },
    /**  消耗经手人  */
    async treatHandler_allHandler(GoodTypes) {
      let that = this;
      try {
        let params = {
          GoodTypes: GoodTypes,
        };
        let res = await API.treatHandler_allHandler(params);
        if (res.StateCode == 200) {
          that.treatAllHandlerList = res.Data.map((i) => {
            return {
              Name: i.Name,
              Employee: i.Employee.map((j) => {
                j.Checked = false;
                return Object.assign({}, j);
              }),
            };
          });
        } else {
          that.$message.error(res.Message);
        }
      } catch (error) {
        //that.$message.error(error);
      }
    },

    async getPrintTemplate_list() {
      let that = this;
      let params = { TemplateType: 'treatbill' };
      let res = await orderAPI.getPrintTemplate_list(params);
      if (res.StateCode == 200) {
        that.treatTemplateTypeList = res.Data;
      } else {
        that.$message.error(res.Message);
      }
      return res;
    },
    /**  门店列表  */
    async treatBill_productEntity() {
      let that = this;
      let params = {};
      let res = await API_consumable.treatBill_productEntity(params);
      if (res.StateCode == 200) {
        that.consumableEntityList = res.Data;
      } else {
        that.$message.error(res.Message);
      }
    },
    /**  获取项目会员优惠  */
    async treatGoodsAccount_savingCardAccountProjectCustomerDiscount(ProjectID) {
      let params = {
        ProjectID: ProjectID, //项目编号
        CustomerID: this.customerID, //会员编号
      };
      let res = await API.treatGoodsAccount_savingCardAccountProjectCustomerDiscount(params);
      //  .then((res) => {
      if (res.StateCode == 200) {
        this.discountData = res.Data;
      } else {
        this.$message.error(res.Message);
      }
      //  })
      //  .catch((fail) => {
      //   this.$message.error(fail);
      //  });
    },
    /**  获取员工最低折扣权限  */
    saleBill_employeeDiscount() {
      let that = this;
      let params = {};
      orderAPI
        .saleBill_employeeDiscount(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.employeeDiscount = res.Data ? res.Data.Discount : null;
          } else {
            that.$message.error(res.Message);
          }
        })
        .catch((fail) => {
          that.$message.error(fail);
        });
    },
  },
  beforeDestroy() {
    // let that = this;
    // that.$bus.$off(that.$bus.treatTakeOrder);
  },
};
</script>

<style lang="scss">
.consume_content {
  font-size: 13px;
  height: 100%;

  .project_left {
    border-right: 1px solid #eee;
    height: 100%;
    color: #303133;

    .el-tabs {
      height: calc(100% - 62px);

      .el-tabs__header {
        margin: 0;

        .el-tabs__nav-scroll {
          // padding-left: 15px;
          margin-left: 15px;
        }
      }

      .el-tabs__content {
        height: calc(100% - 40px);

        .el-tab-pane {
          height: 100%;

          .col_border {
            // border-right: 1px solid #eee;
            padding: 5px;
            color: #666;
            font-size: 13px;
          }

          .category_project {
            height: 100%;

            .category {
              height: 100%;

              .el-scrollbar_height {
                height: 100%;
              }
            }

            .project {
              height: 100%;
              overflow: auto;

              .producct_list {
                height: 100%;

                .el-scrollbar_height {
                  height: 100%;
                }
              }
            }
          }

          .el-popover-botton-tip {
            padding: 0px;
          }
        }
      }
    }
  }

  .project_right {
    height: 100%;

    .el-icon-sort {
      color: #666;
      font-size: 20px;
      transform: rotate(90deg);
    }

    .el-main {
      padding: 0;

      .row_header {
        background-color: #fff7f3;
        padding: 10px;
      }

      .employee_num {
        width: 90px;

        .el-input-group__append {
          padding: 0 10px;
        }
      }

      .el-form-item__label {
        font-size: 13px !important;
      }

      .back_project_col {
        background-color: #fafafa;
      }
    }

    .el-footer {
      height: initial !important;
      padding: 10px;
    }

    .el-card__header {
      padding: 10px 10px;
      background-color: #f5f7fa;
    }

    .el-card {
      border-radius: 0px;
    }

    .el-card__body {
      padding: 0px;
    }
  }
}

.orderInfoRemark {
  position: absolute;
  background-color: rgba($color: #333, $alpha: 0.3);
  top: 0;
  right: 0;
  width: 100%;
  height: calc(100% - 54px);
  z-index: 1;

  .infoRemarContent {
    padding: 0px 10px 10px;
    background-color: #fff;
    position: absolute;
    bottom: 0;
    right: 0;
    left: 0;
    // width: 100%;
    font-size: 13px;
    color: #666;

    .el-form {
      .el-form-item {
        .el-form-item__label {
          font-size: 13px;
        }
      }
    }
  }

  .v-enter-active,
  .v-leave-active {
    transition: all 0.2s ease;
  }

  .v-enter,
  .v-leave-to {
    transform: opacity 0.5s;
    opacity: 0;
  }
}

.consumeGoods {
  .goods-item {
    line-height: 36px;
    font-size: 12px;
    margin-left: 20px;
    margin-right: 5px;
  }

  .goods-lable {
    color: #606266;
  }

  .el-card__header {
    padding: 10px 20px;
    background-color: #f5f7fa;
  }
}

.dialog_bill_detail {
  background-color: #fff7f3;
  padding: 15px;
  border-radius: 5px;
}

.el-scrollbar_height {
  height: 100%;

  .el-scrollbar__wrap {
    overflow-x: hidden;
  }
}

.el-scrollbar_x_width {
  .el-scrollbar__wrap {
    .el-scrollbar__view {
      white-space: nowrap;

      .el-menu-item {
        display: inline-block;
      }

      .is-active {
        a {
          color: #ff8646;
        }
      }
    }
  }
}

.valueCard_project {
  height: 430px;

  .category {
    height: 100%;

    .row_header {
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 13px;
      font-weight: 600;
      color: #333333;
      height: 45px;
      width: 100%;
      // border-bottom: 1px solid #eeeeee;
      // border-right: 1px solid #eeeeee;
      background-color: #fff7f3;
    }

    .el-scrollbar_height {
      height: 100%;
    }

    .category_select {
      color: var(--zl-color-orange-primary);
    }
  }
}

.el-collapse {
  .el-collapse-item__header {
    background-color: #f5f7fa !important;
    padding: 0 10px;
  }

  .el-collapse-item__content {
    padding: 0;
  }
}

.popover-package {
  padding: 0px;

  .el-card__header {
    padding: 10px 20px;
    background-color: #f5f7fa;
  }

  .goods-item {
    line-height: 36px;
    font-size: 12px;
    margin-left: 20px;
    margin-right: 5px;
  }

  .goods-lable {
    color: #606266;
  }
}

.t-through {
  text-decoration: line-through;
}
</style>
