<template>
  <div class="HomeDecoration" v-loading="loading">
    <div class="custom-page_edit">
      <el-scrollbar class="custom-page-left">
        <el-collapse v-model="componentActiveNames">
          <el-collapse-item title="基本组件" name="1">
            <template slot="title"><i class="el-icon-s-grid mar_0_10"></i>基本组件
            </template>
            <div class="custom-page-left-dl">
              <div v-for="(item, index) in ComponentData.BaseComponent" :key="index" class="custom-page-left-dl-item"
                @click="addComponent(item)">
                <el-image style="height: 28px; width: 28px" :src="
                  require('../../../assets/img/homeConf/' +
                    item.Icon +
                    '.png')
                "></el-image>
                <p class="font_14 color_666 martp_5">{{ item.Name }}</p>
              </div>
            </div>
          </el-collapse-item>
          <el-collapse-item title="营销组件" name="2">
            <template slot="title"><i class="el-icon-s-grid mar_0_10"></i>营销组件
            </template>
            <div class="custom-page-left-dl">
              <div v-for="(item, index) in ComponentData.MarketingComponent" :key="index" class="custom-page-left-dl-item"
                @click="addComponent(item)">
                <el-image style="height: 28px; width: 28px" :src="
                  require('../../../assets/img/homeConf/' +
                    item.Icon +
                    '.png')
                "></el-image>
                <p class="font_14 color_666 martp_5">{{ item.Name }}</p>
              </div>
            </div>
          </el-collapse-item>
          <el-collapse-item title="效率 Efficiency" name="3">
            <template slot="title"><i class="el-icon-s-grid mar_0_10"></i>其他组件
            </template>
            <div class="custom-page-left-dl">
              <div v-for="(item, index) in ComponentData.OtherComponent" :key="index" class="custom-page-left-dl-item"
                @click="addComponent(item)">
                <el-image style="height: 28px; width: 28px" :src="
                  require('../../../assets/img/homeConf/' +
                    item.Icon +
                    '.png')
                "></el-image>
                <p class="font_14 color_666 martp_5">{{ item.Name }}</p>
              </div>
            </div>
          </el-collapse-item>
        </el-collapse>
      </el-scrollbar>
      <div class="custom-page-view">
        <div class="custom-phone">
          <div class="custom-phone-nav-bar">
            <el-image :src="
              require('../../../assets/img/homeConf/PhoneStatus Bar2.png')
            "></el-image>
            <!-- 门店 -->
            <div class="backfff font_16 color_333 padlt_10 dis_flex flex_y_center" style="height: 42px">
              <span class="font_14 color_333">{{ EntityName }}</span>
              <div class="dis_flex flex_y_center flex_x_center marlt_5">
                <el-image style="width: 15px; height: 13px"
                  :src="require('@/assets/img/store/toggleClick.png')"></el-image>
              </div>
            </div>
          </div>
          <!-- 搜索框 -->
          <div class="custom-phone-search">
            <div class="search-content">
              <i class="el-icon-search marlt_5 font_14"></i>
              <div class="font_12 color_999 marlt_5">输入商品名称进行搜索</div>
            </div>
          </div>
          <el-scrollbar style="height: 535px">
            <!-- <div class=""> -->
            <draggable v-model="comps" chosenClass="chosen" group="people1" animation="1" class="custom-phone-content">
              <div class="drag-box" :class="comp.isEdit ? 'active' : ''" style="min-height: 20px"
                v-for="(comp, index) in comps" :key="comp.Code + index" @mouseenter="compontenMouse(comp)"
                @mouseleave="compontenMouseLeave(comp)" @click="selectComponentClick(comp, index)">
                <div class="operate-info">
                  <el-button v-show="comp.isEdit" @click="deleteComponent(index)" type="primary"
                    icon="el-icon-delete-solid " size="small" class="custom-button"></el-button>
                </div>
                <component :is="miniProgramHomeComponents[comp.Code]" :ContentProperty="comp.ContentProperty"
                  :ConfigProperty="comp.ConfigProperty"></component>
                <!-- <component
                    :is="miniProgramHomeComponents[comp.Code]"
                    :bannerList="comp.Conf.Carousel_file_list"
                    :HotGoodsList="comp.Conf.HotGoodsConf.GoodsList"
                    :GoodsConf="comp.Conf.HotGoodsConf.Conf"
                    :StoreShowInfo="comp.Conf.EntityInfoConf"
                    :RichTextContent="comp.Conf.RichText"
                    :AuxiliaryLineConf="comp.Conf.AuxiliaryLine"
                    :GrouponGoodsList="comp.Conf.GrouponGoodsConf.GoodsList"
                    :GrouponGoodsConf="comp.Conf.GrouponGoodsConf.Conf"
                    :SeckillGoodsList="comp.Conf.SeckillConf.GoodsList"
                    :SeckillGoodsConf="comp.Conf.SeckillConf.Conf"
                  ></component> -->
              </div>
            </draggable>
            <div style="height: 10px"></div>
            <!-- </div> -->
          </el-scrollbar>
          <div class="custom-phone-tabbar">
            <div class="dis_flex flex_dir_column flex_y_center">
              <i class="el-icon-s-home font_20" style="color: #1e9efa"></i>
              <span class="font_12" style="color: #1e9efa">首页</span>
            </div>
            <div class="dis_flex flex_dir_column flex_y_center">
              <i class="el-icon-user-solid font_20 color_999"></i>
              <span class="font_12 color_666">我的</span>
            </div>
          </div>
        </div>
      </div>
      <div class="custom-page-right">
        <div style="height: 100%; width: 100%">
          <div v-if="SelectComponent.Code === 'Carousel'" class="Carousel">
            <div class="pad_10 font_14 color_333 backfff">
              <p class="padbm_10">轮播海报</p>
              <p class="font_12 color_999">
                最多上传五张，建议尺寸750 * 375(宽度:高度)
              </p>
              <p class="font_12 color_999">图片大小不能超过3M</p>
            </div>
            <div class="flex_wrap pad_10">
              <draggable v-model="SelectComponent.ContentProperty" animation="1000">
                <div v-for="(item, index) in SelectComponent.ContentProperty" :key="index" class="carousel-item">
                  <el-image class="swiperImage" :src="item.url" fit="cover"></el-image>
                  <div class="dis_flex flex_dir_column flex_x_center">
                    <el-popover placement="left-start" width="360" trigger="hover"
                      popper-class="custom-component-popover-class" :ref="`popoerRef-${index}`"
                      @hide="popoverAfterLeave('carousel', index)">
                      <span slot="reference">
                        <span v-if="item.linkTitle && item.linkTitle != ''">
                          <el-tag @close="deleteSwiperGoods(index)" closable size="small">
                            <span v-if="item.linkTypeName">{{ item.linkTypeName }} | </span>
                            <span>{{ item.linkTitle }} </span>
                          </el-tag>
                          <el-button type="text" icon="el-icon--right">修改</el-button>
                        </span>
                        <el-button v-else type="text" icon="el-icon--right">请选择跳转链接</el-button>
                      </span>
                      <el-cascader-panel :options="cascaderOptions" @change="(envent) => addNavigateLink(envent, index)"
                        :ref="`cascader-panel-${index}`"></el-cascader-panel>
                    </el-popover>
                  </div>
                  <i class="el-icon-error color_666 font_20 position_absolute" style="right: 0; top: 0"
                    @click="deleteCarouseClick(index)"></i>
                </div>
              </draggable>

              <el-upload :limit="5" action="#" list-type="picture-card"
                :before-upload="($event) => beforeSwiperUpload($event)" class="carousel-upload" multiple>
                <div slot="default">
                  <i class="el-icon-camera-solid" style="font-size: 40px; color: #999"></i>
                </div>
                <div slot="file" slot-scope="{}"></div>
              </el-upload>
            </div>
          </div>
          <!-- 门店信息 -->
          <div v-if="SelectComponent.Code === 'EntityInfo'">
            <div class="switchBox pad_0_10 dis_flex flex_y_center">
              <span class="font_14 color_333">门店信息</span>
            </div>
            <el-form class="pad_10">
              <el-form-item label="显示信息:">
                <el-checkbox-group v-model="SelectComponent.ConfigProperty" class="dis_flex flex_dir_column">
                  <el-checkbox label="Name">店铺名称</el-checkbox>
                  <el-checkbox label="Address">地址</el-checkbox>
                  <el-checkbox label="Phone">服务电话</el-checkbox>
                  <el-checkbox label="Describe">门店简介</el-checkbox>
                </el-checkbox-group>
              </el-form-item>
            </el-form>
          </div>
          <!-- 热门推荐 -->
          <el-scrollbar style="height: 100%" v-if="SelectComponent.Code === 'GoodsList'">
            <div class="switchBox pad_0_10 dis_flex flex_y_center flex_x_between">
              <div class="font_14 color_333">热门推荐</div>

              <el-dropdown szie="small" class="martp_10 marlt_10" style="right: 10px">
                <el-button size="small" type="primary">选择商品</el-button>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item @click.native="addGoods('project', '', true)">项目</el-dropdown-item>
                  <el-dropdown-item @click.native="addGoods('product', '', true)">产品</el-dropdown-item>
                  <el-dropdown-item @click.native="addGoods('generalCard', '', true)">通用次卡</el-dropdown-item>
                  <el-dropdown-item @click.native="addGoods('timeCard', '', true)">时效卡</el-dropdown-item>
                  <el-dropdown-item @click.native="addGoods('saveCard', '', true)">储值卡</el-dropdown-item>
                  <el-dropdown-item @click.native="addGoods('packageCard', '', true)">套餐卡</el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </div>

            <el-table :data="SelectComponent.ContentProperty" size="small">
              <el-table-column prop="Name" label="商品"> </el-table-column>
              <el-table-column prop="GoodsTypeName" label="商品类型">
              </el-table-column>
              <el-table-column prop="Price" label="售价（元）">
              </el-table-column>
              <el-table-column label="操作">
                <template slot-scope="scope">
                  <el-button size="small" type="danger" @click="deleteHotGoodsItemClick(scope.$index)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>

            <el-form class="pad_10">
              <el-form-item label="显示信息:">
                <el-checkbox-group v-model="SelectComponent.ConfigProperty" class="dis_flex flex_dir_column">
                  <el-checkbox label="Name">服务名称</el-checkbox>
                  <el-checkbox label="Price">服务价格</el-checkbox>
                  <el-checkbox label="OriginalText">划线价格</el-checkbox>
                </el-checkbox-group>
              </el-form-item>
            </el-form>
          </el-scrollbar>
          <!-- 空白元素 -->
          <div v-if="SelectComponent.Code === 'AuxiliaryBlank'">
            <div class="switchBox pad_0_10 dis_flex flex_y_center">
              <span class="font_14 color_333">辅助空白</span>
            </div>
            <div class="dis_flex flex_dir_row flex_y_center">
              <el-slider v-model="SelectComponent.ConfigProperty.Height" :min="blank_mini" style="width: 70%"
                class="marlt_15" size="small"></el-slider>
              <span class="font_12 color_666 marlt_15">{{ SelectComponent.ConfigProperty.Height }} 像素</span>
            </div>
            <el-form class="pad_10">
              <el-form-item label="背景颜色:">
                <div class="dis_flex flex_dir_column customerColor">
                  <el-radio-group class="martp_15" v-model="blank_colorRadio">
                    <el-radio label="1">默认</el-radio>
                    <el-radio label="2">自定义</el-radio>
                  </el-radio-group>
                  <div v-if="blank_colorRadio == '2'" class="dis_flex flex_y_center martp_15">
                    <el-color-picker size="medium" style="width: 50px" v-model="SelectComponent.ConfigProperty.Color"
                      :predefine="predefineColors"></el-color-picker>
                    <el-button style="margin-left: 40px" size="mini" type="primary" @click="resetColor">重 置</el-button>
                  </div>
                </div>
              </el-form-item>
            </el-form>
          </div>
          <!-- 分割线 -->
          <div v-if="SelectComponent.Code === 'AuxiliaryLine'">
            <div class="switchBox pad_0_10 dis_flex flex_y_center">
              <span class="font_14 color_333">辅助线段</span>
            </div>

            <el-form class="pad_10">
              <el-form-item label="颜色:">
                <div class="dis_flex flex_y_center martp_10">
                  <el-color-picker size="medium" style="width: 50px" v-model="SelectComponent.ConfigProperty.Color"
                    :predefine="predefineColors"></el-color-picker>
                  <el-button style="margin-left: 40px" size="small" type="primary" @click="resetColor">重 置</el-button>
                </div>
              </el-form-item>

              <el-form-item label="边距:">
                <el-radio-group v-model="SelectComponent.ConfigProperty.isPadding">
                  <el-radio :label="false">无边距</el-radio>
                  <el-radio :label="true">左右留边</el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="样式:">
                <el-radio-group v-model="SelectComponent.ConfigProperty.borderStyle">
                  <el-radio label="solid">实线</el-radio>
                  <el-radio label="dashed">虚线</el-radio>
                  <el-radio label="dotted">点线</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-form>
          </div>
          <!-- 富文本 -->
          <div v-if="SelectComponent.Code === 'RichText'" style="height: 100%">
            <div class="switchBox pad_0_10 dis_flex flex_y_center">
              <span class="font_14 color_333">富文本</span>
            </div>
            <quill-editor ref="myQuillEditor" v-model="SelectComponent.ContentProperty" :options="editorOption"
              style="width: 100%; height: calc(100% - 50px)" class="richtextCustomClass">
            </quill-editor>
          </div>

          <!-- 拼团 -->
          <div v-if="SelectComponent.Code === 'Groupon'">
            <div class="mar_10 dis_flex flex_y_center pad_0_10">
              <span class="font_14 color_333">多人拼团</span>
            </div>

            <el-collapse v-model="collapseActiveNames">
              <el-collapse-item class="back_f8 mar_10 pad_0_10" name="1">
                <div slot="title" class="back_f8">
                  <div>
                    添加活动
                    <span class="color_999 font_12">
                      最多可选择3个拼团活动</span>
                  </div>
                </div>

                <div v-for="(grouponItem, index) in SelectComponent.ContentProperty" :key="index"
                  class="backfff pad_10 martp_5 marbm_5 position_relative">
                  <span>{{ grouponItem.Name }}</span>
                  <i @click="removeGrouponGoodsItemClick(index)" class="el-icon-error color_999 position_absolute font_18"
                    style="right: 2px; top: 2px"></i>
                </div>

                <el-button @click="addGrouponClick" type="primary" plain style="width: 100%; height: 45px"
                  class="martp_5">添加活动</el-button>
              </el-collapse-item>

              <el-collapse-item class="back_f8 mar_10 pad_0_10" name="3">
                <div slot="title" class="back_f8">
                  <span class="font_14">显示设置</span>
                </div>

                <div>
                  <el-checkbox-group v-model="SelectComponent.ConfigProperty" class="dis_flex flex_dir_column">
                    <el-checkbox label="Time">活动时间</el-checkbox>
                    <el-checkbox label="Name" class="martp_10">活动名称</el-checkbox>
                    <el-checkbox label="Price" class="martp_10">活动价格</el-checkbox>
                  </el-checkbox-group>
                </div>
              </el-collapse-item>
            </el-collapse>
          </div>

          <!-- 秒杀 -->
          <div v-if="SelectComponent.Code === 'Seckill'">
            <div class="mar_10 dis_flex flex_y_center pad_0_10">
              <span class="font_14 color_333">秒杀活动</span>
            </div>
            <el-collapse v-model="collapseActiveNames">
              <el-collapse-item class="back_f8 mar_10 pad_0_10" name="1">
                <div slot="title" class="back_f8">
                  <div>
                    添加活动
                    <span class="color_999 font_12">
                      最多可选择3个秒杀活动</span>
                  </div>
                </div>

                <div
                  v-for="(
                                                                                                                                                          seckillItem, index
                                                                                                                                                        ) in SelectComponent.ContentProperty"
                  :key="index" class="backfff pad_10 martp_5 marbm_5 position_relative">
                  <span>{{ seckillItem.Name }}</span>
                  <i @click="removeSeckillGoodsItemClick(index)" class="el-icon-error color_999 position_absolute font_18"
                    style="right: 2px; top: 2px"></i>
                </div>

                <el-button @click="addSeckillClick" type="primary" plain style="width: 100%; height: 45px"
                  class="martp_5">添加活动</el-button>
              </el-collapse-item>

              <el-collapse-item class="back_f8 mar_10 pad_0_10" name="3">
                <div slot="title" class="back_f8">
                  <span class="font_14">显示设置</span>
                </div>

                <div>
                  <el-checkbox-group v-model="SelectComponent.ConfigProperty" class="dis_flex flex_dir_column">
                    <el-checkbox label="Time">活动时间</el-checkbox>
                    <el-checkbox label="Name" class="martp_10">活动名称</el-checkbox>
                    <el-checkbox label="Price" class="martp_10">活动价格</el-checkbox>
                  </el-checkbox-group>
                </div>
              </el-collapse-item>
            </el-collapse>
          </div>
          <!-- 拼图 -->
          <div v-if="SelectComponent.Code === 'Puzzle'" class="puzzle-container">
            <div class="header-title">拼图</div>
            <el-collapse v-model="puzzleActive">
              <el-collapse-item title="内容设置" name="add">
                <!-- 布局选择 -->
                <div class="layout-header">
                  <div class="title">图片布局</div>
                  <div class="layout-item-content">
                    <ul class="layout-box">
                      <li v-for="item in 8" :key="item" @click="selectLayoutItemClick(item)">
                        <div class="layout-item" :class="
                          SelectComponent.ContentProperty.key == item ? 'selectedState' : ''
                        ">
                          <el-image class="img" :src="setLayItemImage(item)"></el-image>
                        </div>
                      </li>
                    </ul>
                  </div>
                </div>
                <!-- 内容回显 -->
                <div class="layout-content" :style="setLayoutStyle()">
                  <div v-for="(item, index) in SelectComponent.ContentProperty
                    .imgs" :key="index" class="layout-item" :class="index == selectLayoutIndex ? 'selected' : ''"
                    :style="getPuzzleItemStyle(item)" @click="selectLayoutContentItemClick(item, index)">
                    <div class="img_wrap" v-if="item.url">
                      <el-image :src="item.url" class="img"></el-image>
                    </div>
                    <span v-else class="placeholderText">{{ setLayoutItemPlaceholderText(item) }}</span>
                  </div>
                </div>
                <!-- 商品链接 -->
                <div class="add-img">
                  <div @click="addPictureComponent" class="custom-upload">
                    <el-image v-if="selectLayoutItem.url" :src="selectLayoutItem.url" class="img"></el-image>
                    <i v-else class="el-icon-plus"></i>
                  </div>
                  <div class="link-address">
                    <span>链接</span>
                    <!-- <el-dropdown class="marlt_5"> -->
                    <el-popover placement="left-start" width="360" trigger="hover"
                      popper-class="custom-component-popover-class" ref="Puzzle-popoer-ref"
                      @hide="popoverAfterLeave('puzzle')">
                      <span slot="reference">
                        <el-button v-if="!selectLayoutItem.linkTitle" type="text"
                          icon="el-icon--right">请选择跳转链接</el-button>
                        <span v-else>
                          <el-tag @close="closePuzzleLink" closable size="small">
                            <span v-if="selectLayoutItem.linkTypeName">{{ selectLayoutItem.linkTypeName }} | </span>
                            <span>{{ selectLayoutItem.linkTitle }} </span>
                          </el-tag>
                          <el-button type="text" icon="el-icon--right">修改</el-button>
                        </span>
                      </span>
                      <el-cascader-panel :options="cascaderOptions" @change="(envent) => addNavigateLink(envent)"
                        ref="Puzzle-cascader-ref"></el-cascader-panel>
                    </el-popover>
                  </div>
                </div>
              </el-collapse-item>
              <el-collapse-item title="样式设置" name="style">
                <div class="puzzle-style-box">
                  <label class="title">图片间隙</label>
                  <div class="flex_box pad_0_15">
                    <el-slider v-model="SelectComponent.ConfigProperty.margin" size="small" :max="30"></el-slider>
                  </div>
                  <label style="width: 40px;text-align: center;">{{ SelectComponent.ConfigProperty.margin }}</label>
                </div>
              </el-collapse-item>
            </el-collapse>
          </div>
          <!-- 图片 -->
          <div v-if="SelectComponent.Code === 'Picture'" class="picture-container">
            <div class="header-title">图片</div>
            <el-collapse v-model="pictureActive">
              <el-collapse-item title="添加图片" name="add">
                <div class="add-img">
                  <div @click="addPictureComponent" class="custom-upload">
                    <el-image v-if="SelectComponent.ContentProperty.url" :src="SelectComponent.ContentProperty.url"
                      class="img"></el-image>
                    <i v-else class="el-icon-plus"></i>
                  </div>
                  <div class="link-address">
                    <span>链接</span>
                    <el-popover placement="left-start" width="360" trigger="hover"
                      popper-class="custom-component-popover-class" ref="Picture-popoer-ref"
                      @hide="popoverAfterLeave('puzzle')">
                      <span slot="reference">
                        <el-button v-if="!SelectComponent.ContentProperty.linkTitle" type="text"
                          icon="el-icon--right">请选择跳转链接</el-button>
                        <span v-else>
                          <el-tag @close="closePictureLink" closable size="small">
                            <span v-if="SelectComponent.ContentProperty.linkTypeName">{{ SelectComponent.ContentProperty.linkTypeName }} | </span>
                            <span>{{ SelectComponent.ContentProperty.linkTitle }} </span>
                          </el-tag>
                          <el-button type="text" icon="el-icon--right">修改</el-button>
                        </span>
                      </span>
                      <el-cascader-panel :options="cascaderOptions" @change="(envent) => addNavigateLink(envent)"
                        ref="Picture-cascader-ref"></el-cascader-panel>
                    </el-popover>
                  </div>
                </div>
              </el-collapse-item>
              <el-collapse-item title="样式设置" name="style">
                <div class="picture-styl-container">
                  <div class="picture-style-box">
                    <label class="title">图片宽度</label>
                    <el-input v-model="SelectComponent.ConfigProperty.width" size="mini" class="custom-picture-input"
                      v-input-fixed="0"></el-input>
                  </div>
                  <div class="picture-style-box">
                    <label class="title">图片高度</label>
                    <el-input v-model="SelectComponent.ConfigProperty.height" size="mini" class="custom-picture-input"
                      v-input-fixed="0"></el-input>
                  </div>
                  <div class="picture-style-box">
                    <label class="title">图片位置</label>
                    <div>
                      <el-button @click="changePosition('start')" size="mini"
                        :type="activePosition == 'start' ? 'primary' : ''">居左</el-button>
                      <el-button @click="changePosition('center')" size="mini"
                        :type="activePosition == 'center' ? 'primary' : ''">居中</el-button>
                      <el-button @click="changePosition('end')" size="mini"
                        :type="activePosition == 'end' ? 'primary' : ''">居右</el-button>
                    </div>
                  </div>

                  <div class="picture-style-box">
                    <label class="title">背景颜色</label>
                    <div class="picture-color">
                      <span @click="resettingPictureBackgroundColor" class="marrt_10 color_main">重置</span>
                      <el-color-picker v-model="SelectComponent.ConfigProperty.backgroundColor"
                        size="small"></el-color-picker>
                    </div>
                  </div>

                  <div class="picture-style-box">
                    <label class="title">圆角效果</label>
                    <div class="flex_box pad_0_15">
                      <el-slider v-model="SelectComponent.ConfigProperty.radius" size="small" :max="200"></el-slider>
                    </div>
                    <el-input @input="changeInputRadius" v-model="SelectComponent.ConfigProperty.radius" size="mini"
                      style="width: 60px" v-input-fixed="0"></el-input>
                  </div>
                </div>
              </el-collapse-item>
            </el-collapse>
          </div>
        </div>
      </div>
    </div>

    <!-- 活动商品、图片链接商品、魔方块链接商品  添加商品  -->
    <el-dialog :title="goodsClassifyTitle" :visible.sync="goodsClassifyBool" @close="goodsDialogClose" width="60%">
      <!-- 搜索框 -->
      <el-row>
        <el-col :span="22">
          <el-form :inline="true" size="small" class="demo-form-inline" label-width="70px" @submit.native.prevent>
            <el-form-item label="名称">
              <el-input v-model="goodsSearchName" @clear="SearchGoods" clearable placeholder="输入商品线上名称搜索"></el-input>
            </el-form-item>
            <el-form-item label="是否上架">
              <el-select v-model="IsAllowSell" placeholder="请选择是否上架" @change="SearchGoods">
                <el-option label="是" :value="true"></el-option>
                <el-option label="否" :value="false"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button size="small" type="primary" @click="SearchGoods">搜索</el-button>
            </el-form-item>
          </el-form>
        </el-col>
      </el-row>
      <!-- 表格 -->

      <el-table ref="HotGoodsRef" :row-key="(row) => row.ID" :data="GoodList" tooltip-effect="dark" height="55vh"
        style="width: 100%" @current-change="handleCurrentChange" @selection-change="hotGoodsSelectionChange"
        :highlight-current-row="!isAddHotGoods">
        <el-table-column v-if="isAddHotGoods" :reserve-selection="true" width="55" type="selection"></el-table-column>
        <el-table-column label="主图缩略">
          <template slot-scope="scope">
            <el-image style="width: 35px; height: 35px" :src="scope.row.ImageURL" fit="cover">
            </el-image>
          </template>
        </el-table-column>
        <el-table-column prop="Name" label="商品名称"></el-table-column>
        <el-table-column prop="Price" label="售价（元）"></el-table-column>
        <el-table-column v-if="
          goodsClassifyTitle == '项目列表' || goodsClassifyTitle == '产品列表'
        " prop="OriginalText" label="划线价（元）"></el-table-column>
        <el-table-column v-if="goodsClassifyTitle == '通用次卡列表'" prop="Amount" label="次数"></el-table-column>
        <el-table-column v-if="goodsClassifyTitle == '时效卡列表'" prop="ConsumeCycle" :formatter="formatterTimeCardDay"
          label="消耗周期(天)"></el-table-column>
        <el-table-column v-if="goodsClassifyTitle == '时效卡列表'" prop="CycleLimitAmount" :formatter="formatterTimeCardNum"
          label="周期次数"></el-table-column>
        <el-table-column v-if="goodsClassifyTitle == '时效卡列表'" prop="PerformanceAmount" label="消耗提成次数"></el-table-column>
        <el-table-column v-if="goodsClassifyTitle == '储值卡列表'" prop="LargessPrice" label="赠送金额"></el-table-column>
        <el-table-column v-if="
          goodsClassifyTitle != '项目列表' && goodsClassifyTitle != '产品列表'
        " prop="ValidDayName" label="有效期"></el-table-column>
        <el-table-column prop="GoodsTypeName" label="商品类型"></el-table-column>
      </el-table>

      <!-- 页码 -->
      <div class="pad_10 dis_flex flex_x_end">
        <el-pagination background :current-page.sync="paginations.page" :layout="paginations.layout"
          :total="paginations.total" @current-change="pageChange"></el-pagination>
      </div>
      <!-- 底部 -->
      <span v-if="isAddHotGoods" slot="footer" class="dialog-footer">
        <el-button size="small" @click="goodsClassifyBool = false">取 消</el-button>
        <el-button size="small" type="primary" @click="saveHotGoods">确 定</el-button>
      </span>
    </el-dialog>

    <!-- 商品 拼团 -->
    <el-dialog title="拼团活动" :visible.sync="grouponDialog" width="1000px">
      <el-table ref="GrouponGoodsRef" :row-key="(row) => row.ID" :data="groupon_list" tooltip-effect="dark" height="50vh"
        style="width: 100%" @current-change="handleGrouponCurrentChange" @selection-change="handleGrouponSelectionChange"
        @cell-click="grouponGoodsCellClick">
        <el-table-column v-if="!isLink" :reserve-selection="true" width="55" type="selection"></el-table-column>
        <el-table-column prop="Name" label="活动名称"></el-table-column>
        <el-table-column prop="GoodsType" label="商品类型" :formatter="grouponGoodsTypeFormatter"></el-table-column>
        <el-table-column label="成团人数/价格">
          <template slot-scope="scope">
            <div style="white-space: pre-line">
              {{ grouponGrouponPriceFormatter(scope.row) }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="活动时间" width="400">
          <template slot-scope="scope">
            <div style="white-space: pre-line">
              {{ scope.row.BeginDateTime }} 至 {{ scope.row.EndDateTime }}
            </div>
          </template>
        </el-table-column>
      </el-table>
      <!-- 页码 -->
      <div class="pad_10 dis_flex flex_x_end">
        <el-pagination background :current-page.sync="grouponPaginations.page" :layout="grouponPaginations.layout"
          :total="grouponPaginations.total" @current-change="grouponPageChange"></el-pagination>
      </div>
      <!-- 底部 -->
      <span v-if="!isLink" slot="footer" class="dialog-footer">
        <el-button size="small" @click="grouponDialog = false">取 消</el-button>
        <el-button size="small" type="primary" @click="saveGroupinGoodsClick" v-prevent-click>确 定</el-button>
      </span>
    </el-dialog>

    <!-- 商品 秒杀 -->
    <el-dialog title="秒杀活动" :visible.sync="seckilllDialog" width="1000px">
      <!-- 搜索框 -->
      <el-table ref="SeckillGoodsRef" :row-key="
        (row) => {
          return 'seckill' + row.ID;
        }
      " :data="seckillList" tooltip-effect="dark" height="50vh" style="width: 100%"
        @current-change="handleSeckillCurrentChange" @selection-change="handleSeckillSelectionChange"
        @cell-click="seckillGoodsCellClick">
        <el-table-column v-if="!isLink" :reserve-selection="true" width="55" type="selection"></el-table-column>
        <el-table-column prop="Name" label="活动名称"></el-table-column>
        <el-table-column prop="GoodsType" label="商品类型" :formatter="grouponGoodsTypeFormatter"></el-table-column>
        <el-table-column label="活动时间">
          <template slot-scope="scope">
            <div style="white-space: pre-line">
              {{ scope.row.BeginDateTime }} - {{ scope.row.EndDateTime }}
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 页码 -->
      <div class="pad_10 dis_flex flex_x_end">
        <el-pagination background :current-page.sync="seckillPaginations.page" :layout="seckillPaginations.layout"
          :total="seckillPaginations.total" @current-change="seckillPageChange"></el-pagination>
      </div>
      <!-- 底部 -->
      <span v-if="!isLink" slot="footer" class="dialog-footer">
        <el-button size="small" @click="seckilllDialog = false">取 消</el-button>
        <el-button size="small" type="primary" @click="saveSeckillGoodsClick" v-prevent-click>确 定</el-button>
      </span>
    </el-dialog>
    <!-- 编写 跳转写程序链接 -->
    <el-dialog :visible.sync="showLinkMiniProgram" title="跳转小程序" width="500px">
      <el-row>
        <el-col :span="6">
          <span>小程序链接：</span>

          <el-popover placement="left-start" width="375" trigger="hover">
            <p>1、小程序链接可以通过【小程序菜单】->【复制链接】获取。</p>
            <p>
              <el-image :src="'https://mfl-saas-data.oss-cn-shanghai.aliyuncs.com/20230328145612543'"></el-image>
            </p>
            <el-button type="text" style="color: #dcdfe6" class="font_12 el-popover-botton-tip" icon="el-icon-info"
              slot="reference"></el-button>
          </el-popover>
        </el-col>
        <el-col :span="18">
          <el-input v-model="selectLinkComponent.link" size="small" placeholder="请输入小程序链接"></el-input>
        </el-col>
      </el-row>
      <!-- 底部 -->
      <span slot="footer" class="dialog-footer">
        <el-button size="small" @click="showLinkMiniProgram = false">取 消</el-button>
        <el-button size="small" type="primary" @click="confirmMiniProgramLikn" v-prevent-click>确 定</el-button>
      </span>
    </el-dialog>


    <el-dialog :visible.sync="showCustomPage" title="选择自定义界面" width="500px">
      <el-form size="small">
        <el-form-item label="自定义界面：">
          <el-select v-model="selectLinkComponent.linkKey" placeholder="请选择" filterable>
            <el-option v-for="item in customPages" :key="item.ID" :label="item.Title" :value="item.ID">
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <!-- 底部 -->
      <span slot="footer" class="dialog-footer">
        <el-button size="small" @click="showCustomPage = false">取 消</el-button>
        <el-button size="small" type="primary" @click="confirmCustomPageLink" v-prevent-click>确 定</el-button>
      </span>
    </el-dialog>
    <!-- 选择分类 -->
    <el-dialog :visible.sync="showAppletGoodsCategory" title="选择自定义界面" width="500px">
      <el-form size="small">
        <el-form-item label="商品分类：">
          <el-select v-model="selectComponentCategory" placeholder="请选择" filterable value-key="ID">
            <el-option v-for="item in appletGoodsCategory" :key="item.ID" :label="item.Name" :value="item">
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <!-- 底部 -->
      <span slot="footer" class="dialog-footer">
        <el-button size="small" @click="showAppletGoodsCategory = false">取 消</el-button>
        <el-button size="small" type="primary" @click="confirmAppletGoodsCategory" v-prevent-click>确 定</el-button>
      </span>
    </el-dialog>

    <!-- 图片上传组件辅助-->
    <el-upload class="richText-image-uploader" action="#" accept="image/*" :show-file-list="false"
      :before-upload="ricehTextBeforeUpload" multiple>
    </el-upload>

    <cropper-img :cropperImgDialog.sync="cropperImgDialog" :img="cropperImg" :fixed="true" :fixedNumber="[2, 1]"
      :autoCropWidth="580" :autoCropHeight="290" @getCropperImgBase64Data="getCropperImgBase64Data"></cropper-img>
  </div>
</template>

<script>
import miniProgramHomeComponents from "./miniProgramHomeComponents";
import draggable from "vuedraggable";
import API from "@/api/Marketing/EShopManage/EShopDecoration";
import utils from "@/components/js/utils.js";
import uploadApi from "@/api/Common/uploadAttachment.js";

import cropperImg from "@/components/cropperImg/cropperImg.vue";

import "quill/dist/quill.core.css";
import "quill/dist/quill.snow.css";
import "quill/dist/quill.bubble.css";
import { quillEditor } from "vue-quill-editor";

export default {
  name: "HomeDecoration",
  props: {},
  /**  引入的组件  */
  components: {
    draggable,
    quillEditor,
    cropperImg,
  },
  /**  Vue 实例的数据对象**/
  data() {
    return {
      showAppletGoodsCategory:false,
      showCustomPage: false,
      showLinkMiniProgram: false,
      isLink: false,
      cascaderOptions: [
        {
          value: 'basePage',
          label: "基础页面",
          children: [
            {
              value: '/pages/homePage/homePage',
              label: '首页',
            },
            {
              value: '/pages/EntityList/EntityList',
              label: '门店列表',
            },
            {
              value: '/pages/Appointment/Appointment',
              label: '预约',
            },
            {
              value: '/pages/GoodsCategory/GoodsCategory',
              label: '分类',
            },
            {
              value: '/pages/shoppingCart/shoppingCart',
              label: '购物车',
            },
          ]
        },
        {
          value: 'marketing',
          label: "营销工具",
          children: [
            {
              value: '/pages/groupGoodsDetails/groupGoodsDetails',
              label: '多人拼团',
            },
            {
              value: '/pages/seckillGoodsDetails/seckillGoodsDetails',
              label: '秒杀',
            }
          ]
        },
        {
          value: 'goodsList',
          label: '商品列表',
          children: [
            {
              value: '项目',
              label: '项目',
            },
            {
              value: '产品',
              label: '产品',
            },
            {
              value: '通用次卡',
              label: '通用次卡',
            },
            {
              value: '时效卡',
              label: '时效卡',
            },
            {
              value: '储值卡',
              label: '储值卡',
            },
            {
              value: '套餐卡',
              label: '套餐卡',
            },
          ]
        },
        {
          value: 'PersonalCenter',
          label: "个人中心",
          children: [
            {
              value: '/pages/PersonalCenter/PersonalCenter',
              label: '会员中心',
            },
            {
              value: '/subPersonalCenter/pages/reservationRecord/reservationRecord',
              label: '我的预约',
            },
          ]
        },
        {
          value: 'otherPage',
          label: "其他",
          children: [
            {
              value: '/sub_custom_page/page/subCustomPage/subCustomPage',
              label: '自定义页面',
            },
            {
              value: 'navigateToMiniProgram',
              label: '跳转小程序',
            },
          ]
        }
      ],
      pictureActive: ["add", "style"],
      puzzleActive: ["add", "style"],
      componentActiveNames: ["1", "2", "3"],
      cropperImgDialog: false,
      cropperImg: null,
      seckilllDialog: false,
      collapseActiveNames: ["1", "2", "3"],
      loading: false,
      comps: [],
      miniProgramHomeComponents: miniProgramHomeComponents,
      SelectComponent: {
        Conf: {
          Carousel_file_list: [],
          EntityInfoConf: ["Name", "Address", "Phone", "Describe"],
          AuxiliaryBlank: { Height: 15, Color: "#FFFFFF" },
          AuxiliaryLine: {
            isPadding: false,
            Color: "#eeeeee",
            borderStyle: "solid",
          },
          HotGoodsConf: {
            GoodsList: [],
            Conf: ["Name", "Price", "OriginalText"],
          },
          RichText: "",
          GrouponGoodsConf: {
            GoodsList: [],
            Conf: ["Time", "Name", "Price"],
          },
          SeckillConf: {
            GoodsList: [],
            Conf: ["Time", "Name", "Price"],
          },
        },
      },
      ComponentData: {},
      EntityName: "",
      paginations: {
        total: 0,
        layout: " prev, pager, next, jumper, ->, total, slot",
        page: 1,
      },
      carouselIndex: "",
      goodsSearchName: "",
      IsAllowSell: true,
      GoodList: [], //商品列表
      goodsClassifyBool: false, //商品列表弹出层
      goodsClassifyTitle: "", //商品列表弹出层标题
      apiType: "", // 请求类型
      isAddHotGoods: false,
      goodsType: "",
      tempSelectionHotGoods: [],
      blank_colorRadio: "1",
      blank_mini: 15,
      predefineColors: [
        "#ff4500",
        "#ff8c00",
        "#ffd700",
        "#90ee90",
        "#00ced1",
        "#1e90ff",
        "#c71585",
        "#FED439",
        "#A9DEB0",
        "#00BABC",
        "#39A2ED",
        "#FF001D",
      ],
      editorOption: "",

      grouponDialog: false,
      groupon_list: [],
      grouponPaginations: {
        total: 0,
        layout: " prev, pager, next, jumper, ->, total, slot",
        page: 1,
      },
      selectGrouponGoodsList: [],
      seckillList: [],
      seckillPaginations: {
        total: 0,
        layout: " prev, pager, next, jumper, ->, total, slot",
        page: 1,
      },
      selectSeckillGoodsList: [],
      selectComIndex: 0,
      dropdownItem: ["项目", "产品", "通用次卡", "时效卡", "储值卡", "套餐卡"],
      activePosition: "start",
      selectLayoutIndex: 0,
      selectLayoutItem: {
        start: [0, 0],
        width: 1,
        height: 1,
        url: "",
        Name: "",
        heightToWidthRatio: 1,
        linkKey: "",
        linkTitle: "",
        linkType: "",
        linkTypeName: "",
        linkParams: "",
        link: "",
      },
      customPages: [],
      selectLinkComponent: {
        linkKey: "",
        linkTitle: "",
        linkType: "",
        linkTypeName: "",
        linkParams: "",
        link: "",
      },
      appletGoodsCategory: [],// 分类列表
      selectComponentCategory:"",
      selectComponentCategoryCascader:{
        label:"",
        value:"",
      },
      puzzleSelectNodes:"",
    };
  },
  /**计算属性  */
  computed: {
    editor() {
      return this.$refs.myQuillEditor.quill;
    },
  },
  /**  方法集合  */
  methods: {
    /** 链接商品分类   */
    confirmAppletGoodsCategory(){
      let that = this;
      let index = that.appletGoodsCategory.findIndex(i=>i.ID == that.selectComponentCategory.ID);
      
      if (that.SelectComponent.Code == "Carousel") {
        let selectNodes = that.$refs[`cascader-panel-${that.carouselIndex}`][0].getCheckedNodes();
        let { label, value } = selectNodes[0];
        let item = that.SelectComponent.ContentProperty[that.carouselIndex];
        let temp = Object.assign(item, {
          linkKey: "",
          linkTitle: label + '-' + that.selectComponentCategory.Name,
          linkType: "",
          linkTypeName: "",
          linkParams: {
            index:index,
            categoryId:that.selectComponentCategory.ID
          },
          link: value,
        });
        that.SelectComponent.ContentProperty.splice(that.carouselIndex, 1, temp);
        that.$refs[`popoerRef-${that.carouselIndex}`][0].doClose(); //关闭弹框
      } else if (that.SelectComponent.Code == "Puzzle") {

        if (that.puzzleSelectNodes && that.puzzleSelectNodes.label && !that.selectComponentCategory) {
          that.selectLayoutItem.linkTitle = that.puzzleSelectNodes.label;
        }
        else{
          that.selectLayoutItem.linkTitle = that.puzzleSelectNodes.label + "-" + that.selectComponentCategory.Name;
        }
        that.selectLayoutItem.link = that.puzzleSelectNodes.value;
        that.selectLayoutItem.linkParams = {
            index:index,
            categoryId:that.selectComponentCategory.ID
          };
        that.$refs[`${that.SelectComponent.Code}-popoer-ref`].doClose(); //关闭弹框
 
      } 
      else {
        let { label, value } = that.selectComponentCategoryCascader;
        that.SelectComponent.ContentProperty = Object.assign(that.SelectComponent.ContentProperty, {
          linkKey: "",
          linkTitle: label + '-' + that.selectComponentCategory.Name,
          linkType: "",
          linkTypeName: "",
          linkParams: {
            index:index,
            categoryId:that.selectComponentCategory.ID
          },
          link: value,
        });
        that.$refs[`${that.SelectComponent.Code}-popoer-ref`].doClose(); //关闭弹框
      }
      that.showAppletGoodsCategory = false;
    
    },
    /**  确定自定义链接 自定义界面  */
    confirmCustomPageLink() {
      let that = this;
      if (that.selectLinkComponent.linkKey) {
        if (that.SelectComponent.Code == "Carousel") {
          let item = that.customPages.find(i => i.ID == that.selectLinkComponent.linkKey);
          that.selectLinkComponent.linkTitle = item.Title;
          that.selectLinkComponent.link = '/sub_custom_page/page/subCustomPage/subCustomPage';
          that.selectLinkComponent.linkParams = { ID: item.ID };

          let contentItem = that.SelectComponent.ContentProperty[that.carouselIndex];
          let temp = Object.assign(contentItem, that.selectLinkComponent);

          that.SelectComponent.ContentProperty.splice(that.carouselIndex, 1, temp);


        }
        else if (that.SelectComponent.Code == "Puzzle") {
          let item = that.customPages.find(i => i.ID == that.selectLinkComponent.linkKey);
          that.selectLinkComponent.linkTitle = item.Title;
          that.selectLinkComponent.link = '/sub_custom_page/page/subCustomPage/subCustomPage';
          that.selectLinkComponent.linkParams = { ID: item.ID };
          that.selectLayoutItem = that.selectLinkComponent;
          that.SelectComponent.ContentProperty.imgs.splice(that.selectLayoutIndex, 1, that.selectLayoutItem);

        }
        else {
          let item = that.customPages.find(i => i.ID == that.selectLinkComponent.linkKey);
          that.selectLinkComponent.linkTitle = item.Title;
          that.selectLinkComponent.linkType = "";
          that.selectLinkComponent.link = '/sub_custom_page/page/subCustomPage/subCustomPage';
          that.selectLinkComponent.linkParams = { ID: item.ID };
          that.SelectComponent.ContentProperty = Object.assign(that.SelectComponent.ContentProperty, that.selectLinkComponent);
          //
        }

        that.showCustomPage = false;
      }
    },
    /**  保存 跳转小程序链接  */
    confirmMiniProgramLikn() {
      let that = this;
      if (that.SelectComponent.Code == "Carousel") {
        that.selectLinkComponent.linkTitle = "跳转小程序";
        let contentItem = that.SelectComponent.ContentProperty[that.carouselIndex];
        console.log("🚀 ~ file: HomeDecoration.vue:972 ~ confirmMiniProgramLikn ~ contentItem:", contentItem)
        let temp = Object.assign(that.selectLinkComponent, contentItem);
        that.SelectComponent.ContentProperty.splice(that.carouselIndex, 1, temp);
      } else if (that.SelectComponent.Code == "Puzzle") {
        that.selectLinkComponent.linkTitle = "跳转小程序";
        that.selectLayoutItem.linkTitle = that.selectLinkComponent.linkTitle;
        that.selectLayoutItem.link = that.selectLinkComponent.link;
        that.SelectComponent.ContentProperty.imgs.splice(that.selectLayoutIndex, 1, that.selectLayoutItem);
      }
      else {
        that.selectLinkComponent.linkTitle = "跳转小程序";
        that.selectLinkComponent.linkType = "navigateToMiniProgram";
        that.SelectComponent.ContentProperty = Object.assign(that.SelectComponent.ContentProperty, that.selectLinkComponent);
      }

      that.showLinkMiniProgram = false;
    },
    /**    */
    popoverAfterLeave(type, index) {
      let that = this;
      if (type == 'carousel') {
        that.$refs[`cascader-panel-${index}`][0].clearCheckedNodes(); //关闭弹框
      }
      else {
        that.$refs[`${that.SelectComponent.Code}-cascader-ref`].clearCheckedNodes(); //关闭弹框
      }
    },
    /**  添加组件  */
    addComponent(item) {
      let that = this;
      let ConfigProperty = {
        Seckill: ["Time", "Name", "Price"],
        Groupon: ["Time", "Name", "Price"],
        GoodsList: ["Name", "Price", "OriginalText"],
        EntityInfo: ["Name", "Address", "Phone", "Describe"],
        AuxiliaryLine: {
          isPadding: false,
          Color: "#eeeeee",
          borderStyle: "solid",
        },
        AuxiliaryBlank: { Height: 30, Color: "#ffffff" },
        Picture: {
          width: "",
          height: "",
          position: "start",
          backgroundColor: "#f4f4f4",
          radius: 0,
        },
        Puzzle: {
          margin: 0,
        }
      };
      let ContentProperty = {
        RichText: "请输入文本内容",
        Seckill: [],
        Groupon: [],
        GoodsList: [],
        Carousel: [],
        Picture: {
          url: "",
          link: {
            GoodsType: "",
            GoodsTypeName: "",
            ID: null,
            Name: "",
          },
          linkType: "",
        },
        Puzzle: {
          key: 1,
          matrix: [1, 2],
          imgs: [
            {
              start: [0, 0],
              width: 1,
              height: 1,
              link: "",
              linkType: "",
              url: "",
              GoodsType: "",
              GoodsTypeName: "",
              ID: null,
              Name: "",
              heightToWidthRatio: 1,
            },
            {
              start: [1, 0],
              width: 1,
              height: 1,
              link: "",
              linkType: "",
              url: "",
              GoodsType: "",
              GoodsTypeName: "",
              ID: null,
              Name: "",
              heightToWidthRatio: 1,
            },
          ],
        },
      };

      for (let item of that.comps) {
        item.isEdit = false;
      }
      let comp = {
        Code: item.Code,
        Name: item.Name,
        Sequence: item.Sequence,
        isEdit: true,
        isShowBoder: false,
        ConfigProperty: ConfigProperty[item.Code],
        ContentProperty: ContentProperty[item.Code],
      };
      that.comps.splice(that.selectComIndex + 1, 0, comp);
      that.SelectComponent = comp;
    },

    /**  删除已添加的组件  */
    deleteComponent(indx) {
      let that = this;
      that.comps.splice(indx, 1);
      that.SelectComponent.Code = "null";
      that.selectComIndex = that.comps.length - 1;
    },
    /* 定义组件样式 */
    customComponentStyle(comp) {
      if (comp.isEdit) {
        return {
          border: "2px solid var(--zl-color-orange-primary)",
        };
      }
      if (!comp.isEdit & comp.isShowBoder) {
        return {
          border: "1px dashed  var(--zl-color-orange-primary)",
        };
      }
    },
    editStyleHidden() {
      return {
        border: "1px dashed rgba(0, 0, 0, 0)",
      };
    },

    /**  剪切图片  */
    getCropperImgBase64Data(data) {
      let that = this;
      that.upload_addAttachment(data).then((AttachmentURL) => {
        that.$nextTick(function () {
          that.SelectComponent.ContentProperty.push({
            url: AttachmentURL,
            goodsID: "",
            goodsName: "",
            GoodsType: "",
            GoodsTypeName: "",
          });
          that.cropperImgDialog = false;
        });
      });
    },

    /**  上传图片   */
    async ricehTextBeforeUpload(file) {
      let that = this;
      // const isLt2M = file.size / 1024 / 1024 < 5;
      // if (!isLt2M) {
      //   that.$message.error("上传图片大小不能超过 5M!");
      //   return false;
      // }
      if (that.SelectComponent.Code == "Carousel" || that.SelectComponent.Code == "RichText") {
        let imgBase64 = await utils.getImageBase64(file);
        let AttachmentURL = await this.upload_addAttachment(imgBase64);
        let quill = that.$refs.myQuillEditor.quill;
        let len = quill.getSelection().index;
        quill.insertEmbed(len, "image", AttachmentURL);
        quill.setSelection(len + 1);
      }
      if (that.SelectComponent.Code == "Picture") {
        that.asyncImgChecked(file).then((result) => {
          that.SelectComponent.ConfigProperty.width = result.width;
          that.SelectComponent.ConfigProperty.height = result.height;
        });
        that.upload_uploadFile(file).then((result) => {
          that.SelectComponent.ContentProperty.url = result;
        });
      }
      if (that.SelectComponent.Code == "Puzzle") {
        let url = await that.upload_uploadFile(file);
        that.selectLayoutItem.url = url;
        that.SelectComponent.ContentProperty.imgs.splice(that.selectLayoutIndex, 1, that.selectLayoutItem);

      }
      return false;
    },
    /**  上传 门店照片  */
    beforeSwiperUpload(file) {
      let that = this;
      // const isLt2M = file.size / 1024 / 1024 < 3;
      // if (!isLt2M) {
      //   that.$message.error("上传图片大小不能超过 3M");
      //   return false;
      // }
      utils.getImageBase64(file).then((imgBase64) => {
        that.cropperImgDialog = true;
        that.cropperImg = imgBase64;
      });
      return false;
    },
    /**  修改图片位置  type 0居左  1 居中  2 居右  */
    changePosition(type) {
      let that = this;
      that.activePosition = type;
      that.SelectComponent.ConfigProperty.position = type;
    },
    /**  重置 图片背景颜色  */
    resettingPictureBackgroundColor() {
      let that = this;
      that.SelectComponent.ConfigProperty.backgroundColor = "#f4f4f4";
    },
    /**  图片上传图片  */
    addPictureComponent() {
      document.querySelector(".richText-image-uploader input").click();
    },
    /**  设置圆角  */
    changeInputRadius(val) {
      let that = this;
      that.SelectComponent.ConfigProperty.radius = Number(val);
    },

    /* 获取图片尺寸 */
    asyncImgChecked(file) {
      return new Promise((resolve) => {
        let reader = new FileReader();
        reader.readAsDataURL(file); // 必须用file.raw
        reader.onload = () => {
          // 让页面中的img标签的src指向读取的路径
          let img = new Image();
          img.src = reader.result;
          if (img.complete) {
            // 如果存在浏览器缓存中
            let height = Math.ceil((750 * img.height) / img.width);
            resolve({
              width: 750,
              height: height,
            });
          } else {
            img.onload = () => {
              let height = Math.ceil((750 * img.height) / img.width);
              resolve({
                width: 750,
                height: height,
              });
            };
          }
        };
      });
    },
    /*  设置跳转小程序链接基础界面  */
    setCarouselMiniProgramLink(index) {
      let that = this;
      let selectNodes = that.$refs[`cascader-panel-${index}`][0].getCheckedNodes();
      let { label, value } = selectNodes[0];
      let item = that.SelectComponent.ContentProperty[index];
      let temp = Object.assign(item, {
        linkKey: "",
        linkTitle: label,
        linkType: "",
        linkTypeName: "",
        linkParams: "",
        link: value,
      });
      that.SelectComponent.ContentProperty.splice(index, 1, temp);
      that.$refs[`popoerRef-${index}`][0].doClose(); //关闭弹框

    },
    /*  拼图跳转  */
    setPuzzleMiniProgramLink() {
      let that = this;
      let selectNodes = that.$refs[`${that.SelectComponent.Code}-cascader-ref`].getCheckedNodes();
      let { label, value } = selectNodes[0];
      that.selectLayoutItem.linkTitle = label;
      that.selectLayoutItem.link = value;
      that.$refs[`${that.SelectComponent.Code}-popoer-ref`].doClose(); //关闭弹框
    },
    /*  设置跳转小程序链接基础界面  */
    setMiniProgramLink() {
      let that = this;
      let selectNodes = that.$refs[`${that.SelectComponent.Code}-cascader-ref`].getCheckedNodes();
      let { label, value } = selectNodes[0];
      that.SelectComponent.ContentProperty = Object.assign(that.SelectComponent.ContentProperty, {
        linkKey: "",
        linkTitle: label,
        linkType: "",
        linkTypeName: "",
        linkParams: "",
        link: value,
      });
      that.$refs[`${that.SelectComponent.Code}-popoer-ref`].doClose(); //关闭弹框
    },
    /**  添加跳转连接--轮播图-图片 -魔方【拼图】  */
    addNavigateLink(envent, index) {
      let that = this;
      let type = "";
      let isAdd = false;
      let linkType = envent[0];

      if (linkType == 'basePage') {
        let value = envent[1];
        if (value == '/pages/GoodsCategory/GoodsCategory') {
          that.carouselIndex = index;
          that.showAppletGoodsCategory = true;
          if (that.SelectComponent.Code == "Carousel") {
            let selectNodes = that.$refs[`cascader-panel-${index}`][0].getCheckedNodes();
            let { label, value } = selectNodes[0];
            that.selectComponentCategoryCascader = {
              label, value
            }
          } else if (that.SelectComponent.Code == "Puzzle") {
            let selectNodes = that.$refs[`${that.SelectComponent.Code}-cascader-ref`].getCheckedNodes();
            let { label, value } = selectNodes[0];
            that.puzzleSelectNodes = {
              label, value
            }
            that.selectComponentCategoryCascader = {
              label, value
            }
          } 
          else {
            let selectNodes = that.$refs[`${that.SelectComponent.Code}-cascader-ref`].getCheckedNodes();
            let { label, value } = selectNodes[0];
            that.selectComponentCategoryCascader = {
              label, value
            }
          }
        }
        else {
          if (that.SelectComponent.Code == "Carousel") {
            that.setCarouselMiniProgramLink(index);
          } else if (that.SelectComponent.Code == "Puzzle") {
            that.setPuzzleMiniProgramLink();
          } 
          else {
            that.setMiniProgramLink();
          }
        }
      }
      /* 营销活动 */
      if (linkType == 'marketing') {
        that.carouselIndex = index;
        let value = envent[1];
        /* 拼团 */
        if (value == '/pages/groupGoodsDetails/groupGoodsDetails') {
          that.getGroupon_list().then(() => {
            that.isLink = true;
            that.grouponDialog = true;
          });
        }
        /* 秒杀 */
        if (value == '/pages/seckillGoodsDetails/seckillGoodsDetails') {
          that.getSeckill_list().then(() => {
            that.isLink = true;
            that.seckilllDialog = true;
          });
        }

      }

      if (linkType == 'goodsList') {
        type = envent[1];
        that.paginations.page = 1;
        that.isAddHotGoods = isAdd;
        that.goodsType = type;
        that.tempSelectionHotGoods = [];
        switch (type) {
          case "项目":
            that.apiType = "GoodsCategoryProject";
            that.goodsClassifyTitle = "项目列表";
            that.SelectComponent.ContentProperty.linkType = "goods";
            break;
          case "产品":
            that.apiType = "GoodsCategoryProduct";
            that.goodsClassifyTitle = "产品列表";
            that.SelectComponent.ContentProperty.linkType = "goods";
            break;
          case "通用次卡":
            that.apiType = "GoodsCategoryGeneralCard";
            that.goodsClassifyTitle = "通用次卡列表";
            that.SelectComponent.ContentProperty.linkType = "goods";
            break;
          case "时效卡":
            that.apiType = "GoodsCategoryTimeCard";
            that.goodsClassifyTitle = "时效卡列表";
            that.SelectComponent.ContentProperty.linkType = "goods";
            break;
          case "储值卡":
            that.apiType = "GoodsCategorySavingCard";
            that.goodsClassifyTitle = "储值卡列表";
            that.SelectComponent.ContentProperty.linkType = "goods";
            break;
          case "套餐卡":
            that.apiType = "GoodsCategoryPackageCard";
            that.goodsClassifyTitle = "套餐卡列表";
            that.SelectComponent.ContentProperty.linkType = "goods";
            break;
        }
        that.carouselIndex = index;
        that.getGoodAll(that.apiType);
        that.goodsClassifyBool = true;
      }

      if (linkType == 'PersonalCenter') {
        if (that.SelectComponent.Code == "Carousel") {
          let selectNodes = that.$refs[`cascader-panel-${index}`][0].getCheckedNodes();
          let { label, value } = selectNodes[0];
          let item = that.SelectComponent.ContentProperty[index];
          let temp = Object.assign(item, {
            linkKey: "",
            linkTitle: label,
            linkType: "",
            linkTypeName: "",
            linkParams: "",
            link: value,
          });
          that.SelectComponent.ContentProperty.splice(index, 1, temp);
          that.$refs[`popoerRef-${index}`][0].doClose(); //关闭弹框
        } else if (that.SelectComponent.Code == "Puzzle") {
          let selectNodes = that.$refs[`${that.SelectComponent.Code}-cascader-ref`].getCheckedNodes();
          let { label, value } = selectNodes[0];
          let item = {
            linkKey: "",
            linkTitle: label,
            linkType: "",
            linkTypeName: "",
            linkParams: "",
            link: value,
          }
          that.selectLayoutItem = Object.assign(that.selectLayoutItem, item);

        }
        else {
          that.setMiniProgramLink();
        }
      }

      if (linkType == "otherPage") {
        let value = envent[1];
        if (value == "navigateToMiniProgram") {
          if (that.SelectComponent.Code == "Carousel") {
            that.carouselIndex = index;
            let item = that.SelectComponent.ContentProperty[index];
            let temp = Object.assign(item, {
              linkKey: "",
              linkTitle: "",
              linkType: "navigateToMiniProgram",
              linkTypeName: "",
              linkParams: "",
              link: "",
            });
            that.selectLinkComponent = temp;
            that.SelectComponent.ContentProperty.splice(index, 1, temp);
          } else if (that.SelectComponent.Code == "Puzzle") {
            let item = {
              linkKey: "",
              linkTitle: "",
              linkType: "navigateToMiniProgram",
              linkTypeName: "",
              linkParams: "",
              link: "",
            }
            that.selectLayoutItem = Object.assign(that.selectLayoutItem, item);
          }
          // else {
          // }
          that.showLinkMiniProgram = true;
        }
        else {
          if (that.SelectComponent.Code == "Carousel") {
            that.carouselIndex = index;
            let item = that.SelectComponent.ContentProperty[index];
            that.selectLinkComponent = Object.assign(item, {
              linkType: "",
              linkTypeName: "",
              linkParams: "",
            });
          }
          else if (that.SelectComponent.Code == "Puzzle") {
            if(that.selectLayoutItem.link == envent[1]){
              that.selectLinkComponent = that.selectLayoutItem;
            }
            else{
              that.selectLinkComponent = Object.assign(that.selectLayoutItem,that.selectLinkComponent);
            }
          }
          else {
            if(that.selectLayoutItem.link == envent[1]){
              that.selectLinkComponent = that.SelectComponent.ContentProperty;
            }
            else{
            that.selectLinkComponent = Object.assign(that.SelectComponent.ContentProperty,that.selectLinkComponent);
            }
          }

          that.showCustomPage = true;
        }
      }
    },

    /**  链接添加 拼团活动点击 活动  */
    grouponGoodsCellClick(row) {
      let that = this;
      if (that.isLink) {
        if (that.SelectComponent.Code == "Carousel") {
          let item = that.SelectComponent.ContentProperty[that.carouselIndex];
          let temp = Object.assign(item, {
            linkKey: "",
            linkTitle: row.Name,
            linkType: "拼团",
            linkTypeName: "",
            linkParams: { ID: row.ID },
            link: "/pages/groupGoodsDetails/groupGoodsDetails",
          });

          that.SelectComponent.ContentProperty.splice(that.carouselIndex, 1, temp);

        } else if (that.SelectComponent.Code == "Puzzle") {
          that.selectLayoutItem.linkTitle = row.Name;
          that.selectLayoutItem.linkParams = { ID: row.ID },
            that.selectLayoutItem.link = "/pages/groupGoodsDetails/groupGoodsDetails";
          that.SelectComponent.ContentProperty.imgs.splice(that.selectLayoutIndex, 1, that.selectLayoutItem);
        }
        else {
          let item = that.SelectComponent.ContentProperty;
          let temp = Object.assign(item, {
            linkKey: "",
            linkTitle: row.Name,
            linkType: "拼团",
            linkTypeName: "",
            linkParams: { ID: row.ID },
            link: "/pages/groupGoodsDetails/groupGoodsDetails",
          });

          that.SelectComponent.ContentProperty = temp;
        }
        that.grouponDialog = false;
      }
    },
    /**  链接添加  秒杀活动  */
    seckillGoodsCellClick(row) {
      let that = this;
      if (that.isLink) {
        if (that.SelectComponent.Code == "Carousel") {
          let item = that.SelectComponent.ContentProperty[that.carouselIndex];
          let temp = Object.assign(item, {
            linkKey: "",
            linkTitle: row.Name,
            linkType: "拼团",
            linkTypeName: "",
            linkParams: { ID: row.ID },
            link: "/pages/seckillGoodsDetails/seckillGoodsDetails",
          });
          that.SelectComponent.ContentProperty.splice(that.carouselIndex, 1, temp);

        } else if (that.SelectComponent.Code == "Puzzle") {
          that.selectLayoutItem.linkTitle = row.Name;
          that.selectLayoutItem.linkParams = { ID: row.ID },
            that.selectLayoutItem.link = "/pages/seckillGoodsDetails/seckillGoodsDetails";
          that.SelectComponent.ContentProperty.imgs.splice(that.selectLayoutIndex, 1, that.selectLayoutItem);
        }
        else {
          let item = that.SelectComponent.ContentProperty;
          let temp = Object.assign(item, {
            linkKey: "",
            linkTitle: row.Name,
            linkType: "拼团",
            linkTypeName: "",
            linkParams: { ID: row.ID },
            link: "/pages/seckillGoodsDetails/seckillGoodsDetails",
          });
          that.SelectComponent.ContentProperty = temp;
        }
        that.seckilllDialog = false;
      }
    },
    /**  删除图片链接  */
    closePictureLink() {
      let that = this;
      that.selectComponent.ContentProperty.linkTitle = "";
      that.selectComponent.ContentProperty.linkTypeName = "";
      that.selectComponent.ContentProperty.linkKey = "";
      that.selectComponent.ContentProperty.linkParams = "";
      that.selectComponent.ContentProperty.linkType = "";
      that.selectLayoutItem.link = "";
      that.$refs[`${that.SelectComponent.Code}-popoer-ref`].doClose();
    },

    /**    */
    closePuzzleLink() {
      let that = this;
      that.selectLayoutItem.linkTitle = "";
      that.selectLayoutItem.linkTypeName = "";
      that.selectLayoutItem.linkKey = "";
      that.selectLayoutItem.linkParams = "";
      that.selectLayoutItem.linkType = "";
      that.selectLayoutItem.link = "";
      that.$refs['puzzle-popoer-ref'].doClose();
    },
    /* 获取布局设置 图片 */
    setLayItemImage(index) {
      let that = this;
      if (that.SelectComponent.ContentProperty.key == index) {
        return require("../../../assets/img/homeConf/active-buju" +
          index +
          ".png");
      }
      return require("../../../assets/img/homeConf/buju" + index + ".png");
    },
    /**  设置 魔方区域高度  */
    setLayoutStyle() {
      let that = this;
      let matrix = that.SelectComponent.ContentProperty.matrix;
      let width = 296 / matrix[1];
      let height = width * matrix[0];
      return {
        height: `${height}px`
      }
    },
    /**   设置 默然区域占位文字 */
    setLayoutItemPlaceholderText(item) {
      let that = this;
      let matrix = that.SelectComponent.ContentProperty.matrix;
      let ratio = 1 / matrix[1];
      let width = Math.ceil(750 * ratio * item.width);
      let height = Math.ceil(750 * ratio * item.height);
      if (width > 188) {
        return `${width} x ${height}像素 或同等比例`;
      }
      else {
        return `${width} x ${height}`;
      }
    },
    /* 设置 魔方 布局 样式 */
    getPuzzleItemStyle(item) {
      let that = this;
      let matrix = that.SelectComponent.ContentProperty.matrix;
      let width, height, left, top = 0;
      width = 1 / matrix[1] * item.width * 100;
      height = 1 / matrix[0] * item.height * 100;
      left = 1 / matrix[1] * item.start[0] * 100;
      top = 1 / matrix[0] * item.start[1] * 100;

      return {
        width: `${width}%`,
        height: `${height}%`,
        left: `${left}%`,
        top: `${top}%`,
      }
    },
    /**  点击图片布局 item  */
    selectLayoutItemClick(index) {
      let that = this;
      that.selectLayoutIndex = 0;
      switch (index) {
        case 1:
          that.SelectComponent.ContentProperty.key = 1;
          that.SelectComponent.ContentProperty.matrix = [1, 2];
          that.SelectComponent.ContentProperty.imgs = [
            {
              start: [0, 0],
              width: 1,
              height: 1,
              url: "",
              Name: "",
              heightToWidthRatio: 1,
              linkKey: "",
              linkTitle: "",
              linkType: "",
              linkTypeName: "",
              linkParams: "",
              link: "",
            },
            {
              start: [1, 0],
              width: 1,
              height: 1,
              url: "",
              Name: "",
              heightToWidthRatio: 1,
              linkKey: "",
              linkTitle: "",
              linkType: "",
              linkTypeName: "",
              linkParams: "",
              link: "",
            },
          ];

          break;
        case 2:
          that.SelectComponent.ContentProperty.key = 2;
          that.SelectComponent.ContentProperty.matrix = [1, 3];
          that.SelectComponent.ContentProperty.imgs = [
            {
              start: [0, 0],
              width: 1,
              height: 1,
              url: "",
              Name: "",
              heightToWidthRatio: 1,
              linkKey: "",
              linkTitle: "",
              linkType: "",
              linkTypeName: "",
              linkParams: "",
              link: "",
            },
            {
              start: [1, 0],
              width: 1,
              height: 1,
              url: "",
              Name: "",
              heightToWidthRatio: 1,
              linkKey: "",
              linkTitle: "",
              linkType: "",
              linkTypeName: "",
              linkParams: "",
              link: "",
            },
            {
              start: [2, 0],
              width: 1,
              height: 1,
              url: "",
              Name: "",
              heightToWidthRatio: 1,
              linkKey: "",
              linkTitle: "",
              linkType: "",
              linkTypeName: "",
              linkParams: "",
              link: "",
            },
          ];

          break;
        case 3:
          that.SelectComponent.ContentProperty.key = 3;
          that.SelectComponent.ContentProperty.matrix = [1, 4];
          that.SelectComponent.ContentProperty.imgs = [
            {
              start: [0, 0],
              width: 1,
              height: 1,
              url: "",
              Name: "",
              heightToWidthRatio: 1,
              linkKey: "",
              linkTitle: "",
              linkType: "",
              linkTypeName: "",
              linkParams: "",
              link: "",
            },
            {
              start: [1, 0],
              width: 1,
              height: 1,
              url: "",
              Name: "",
              heightToWidthRatio: 1,
              linkKey: "",
              linkTitle: "",
              linkType: "",
              linkTypeName: "",
              linkParams: "",
              link: "",
            },
            {
              start: [2, 0],
              width: 1,
              height: 1,
              url: "",
              Name: "",
              heightToWidthRatio: 1,
              linkKey: "",
              linkTitle: "",
              linkType: "",
              linkTypeName: "",
              linkParams: "",
              link: "",
            },
            {
              start: [3, 0],
              width: 1,
              height: 1,
              url: "",
              Name: "",
              heightToWidthRatio: 1,
              linkKey: "",
              linkTitle: "",
              linkType: "",
              linkTypeName: "",
              linkParams: "",
              link: "",
            },
          ];

          break;
        case 4:
          that.SelectComponent.ContentProperty.key = 4;
          that.SelectComponent.ContentProperty.matrix = [2, 2];
          that.SelectComponent.ContentProperty.imgs = [
            {
              start: [0, 0],
              width: 1,
              height: 1,
              url: "",
              Name: "",
              heightToWidthRatio: 1,
              linkKey: "",
              linkTitle: "",
              linkType: "",
              linkTypeName: "",
              linkParams: "",
              link: "",
            },
            {
              start: [1, 0],
              width: 1,
              height: 1,
              url: "",
              Name: "",
              heightToWidthRatio: 1,
              linkKey: "",
              linkTitle: "",
              linkType: "",
              linkTypeName: "",
              linkParams: "",
              link: "",
            },
            {
              start: [0, 1],
              width: 1,
              height: 1,
              url: "",
              Name: "",
              heightToWidthRatio: 1,
              linkKey: "",
              linkTitle: "",
              linkType: "",
              linkTypeName: "",
              linkParams: "",
              link: "",
            },
            {
              start: [1, 1],
              width: 1,
              height: 1,
              url: "",
              Name: "",
              heightToWidthRatio: 1,
              linkKey: "",
              linkTitle: "",
              linkType: "",
              linkTypeName: "",
              linkParams: "",
              link: "",
            },
          ];

          break;
        case 5:
          that.SelectComponent.ContentProperty.key = 5;
          that.SelectComponent.ContentProperty.matrix = [2, 2];
          that.SelectComponent.ContentProperty.imgs = [
            {
              start: [0, 0],
              width: 1,
              height: 2,
              url: "",
              Name: "",
              heightToWidthRatio: 1,
              linkKey: "",
              linkTitle: "",
              linkType: "",
              linkTypeName: "",
              linkParams: "",
              link: "",
            },
            {
              start: [1, 0],
              width: 1,
              height: 1,
              url: "",
              Name: "",
              heightToWidthRatio: 1,
              linkKey: "",
              linkTitle: "",
              linkType: "",
              linkTypeName: "",
              linkParams: "",
              link: "",
            },
            {
              start: [1, 1],
              width: 1,
              height: 1,
              url: "",
              Name: "",
              heightToWidthRatio: 1,
              linkKey: "",
              linkTitle: "",
              linkType: "",
              linkTypeName: "",
              linkParams: "",
              link: "",
            },
          ];

          break;
        case 6:
          that.SelectComponent.ContentProperty.key = 6;
          that.SelectComponent.ContentProperty.matrix = [2, 2];
          that.SelectComponent.ContentProperty.imgs = [
            {
              start: [0, 0],
              width: 2,
              height: 1,
              url: "",
              Name: "",
              heightToWidthRatio: 1,
              linkKey: "",
              linkTitle: "",
              linkType: "",
              linkTypeName: "",
              linkParams: "",
              link: "",
            },
            {
              start: [0, 1],
              width: 1,
              height: 1,
              url: "",
              Name: "",
              heightToWidthRatio: 1,
              linkKey: "",
              linkTitle: "",
              linkType: "",
              linkTypeName: "",
              linkParams: "",
              link: "",
            },
            {
              start: [1, 1],
              width: 1,
              height: 1,
              url: "",
              Name: "",
              heightToWidthRatio: 1,
              linkKey: "",
              linkTitle: "",
              linkType: "",
              linkTypeName: "",
              linkParams: "",
              link: "",
            },
          ];

          break;
        case 7:
          that.SelectComponent.ContentProperty.key = 7;
          that.SelectComponent.ContentProperty.matrix = [4, 4];
          that.SelectComponent.ContentProperty.imgs = [
            {
              start: [0, 0],
              width: 2,
              height: 4,
              url: "",
              Name: "",
              heightToWidthRatio: 1,
              linkKey: "",
              linkTitle: "",
              linkType: "",
              linkTypeName: "",
              linkParams: "",
              link: "",
            },
            {
              start: [2, 0],
              width: 2,
              height: 2,
              url: "",
              Name: "",
              heightToWidthRatio: 1,
              linkKey: "",
              linkTitle: "",
              linkType: "",
              linkTypeName: "",
              linkParams: "",
              link: "",
            },
            {
              start: [2, 2],
              width: 1,
              height: 2,
              url: "",
              Name: "",
              heightToWidthRatio: 1,
              linkKey: "",
              linkTitle: "",
              linkType: "",
              linkTypeName: "",
              linkParams: "",
              link: "",
            },
            {
              start: [3, 2],
              width: 1,
              height: 2,
              url: "",
              Name: "",
              heightToWidthRatio: 1,
              linkKey: "",
              linkTitle: "",
              linkType: "",
              linkTypeName: "",
              linkParams: "",
              link: "",
            },
          ];

          break;
        case 8:
          that.SelectComponent.ContentProperty.key = 8;
          that.SelectComponent.ContentProperty.matrix = [4, 4];
          that.SelectComponent.ContentProperty.imgs = [
            {
              start: [0, 0],
              width: 1,
              height: 1,
              url: "",
              Name: "",
              heightToWidthRatio: 1,
              linkKey: "",
              linkTitle: "",
              linkType: "",
              linkTypeName: "",
              linkParams: "",
              link: "",
            },
            {
              start: [0, 1],
              width: 1,
              height: 1,
              url: "",
              Name: "",
              heightToWidthRatio: 1,
              linkKey: "",
              linkTitle: "",
              linkType: "",
              linkTypeName: "",
              linkParams: "",
              link: "",
            },
            {
              start: [0, 2],
              width: 1,
              height: 1,
              url: "",
              Name: "",
              heightToWidthRatio: 1,
              linkKey: "",
              linkTitle: "",
              linkType: "",
              linkTypeName: "",
              linkParams: "",
              link: "",
            },
            {
              start: [0, 3],
              width: 1,
              height: 1,
              url: "",
              Name: "",
              heightToWidthRatio: 1,
              linkKey: "",
              linkTitle: "",
              linkType: "",
              linkTypeName: "",
              linkParams: "",
              link: "",
            },
            {
              start: [1, 0],
              width: 1,
              height: 1,
              url: "",
              Name: "",
              heightToWidthRatio: 1,
              linkKey: "",
              linkTitle: "",
              linkType: "",
              linkTypeName: "",
              linkParams: "",
              link: "",
            },
            {
              start: [1, 1],
              width: 1,
              height: 1,
              url: "",
              Name: "",
              heightToWidthRatio: 1,
              linkKey: "",
              linkTitle: "",
              linkType: "",
              linkTypeName: "",
              linkParams: "",
              link: "",
            },
            {
              start: [1, 2],
              width: 1,
              height: 1,
              url: "",
              Name: "",
              heightToWidthRatio: 1,
              linkKey: "",
              linkTitle: "",
              linkType: "",
              linkTypeName: "",
              linkParams: "",
              link: "",
            },
            {
              start: [1, 3],
              width: 1,
              height: 1,
              url: "",
              Name: "",
              heightToWidthRatio: 1,
              linkKey: "",
              linkTitle: "",
              linkType: "",
              linkTypeName: "",
              linkParams: "",
              link: "",
            },
            {
              start: [2, 0],
              width: 1,
              height: 1,
              url: "",
              Name: "",
              heightToWidthRatio: 1,
              linkKey: "",
              linkTitle: "",
              linkType: "",
              linkTypeName: "",
              linkParams: "",
              link: "",
            },
            {
              start: [2, 1],
              width: 1,
              height: 1,
              url: "",
              Name: "",
              heightToWidthRatio: 1,
              linkKey: "",
              linkTitle: "",
              linkType: "",
              linkTypeName: "",
              linkParams: "",
              link: "",
            },
            {
              start: [2, 2],
              width: 1,
              height: 1,
              url: "",
              Name: "",
              heightToWidthRatio: 1,
              linkKey: "",
              linkTitle: "",
              linkType: "",
              linkTypeName: "",
              linkParams: "",
              link: "",
            },
            {
              start: [2, 3],
              width: 1,
              height: 1,
              url: "",
              Name: "",
              heightToWidthRatio: 1,
              linkKey: "",
              linkTitle: "",
              linkType: "",
              linkTypeName: "",
              linkParams: "",
              link: "",
            },
            {
              start: [3, 0],
              width: 1,
              height: 1,
              url: "",
              Name: "",
              heightToWidthRatio: 1,
              linkKey: "",
              linkTitle: "",
              linkType: "",
              linkTypeName: "",
              linkParams: "",
              link: "",
            },
            {
              start: [3, 1],
              width: 1,
              height: 1,
              url: "",
              Name: "",
              heightToWidthRatio: 1,
              linkKey: "",
              linkTitle: "",
              linkType: "",
              linkTypeName: "",
              linkParams: "",
              link: "",
            },
            {
              start: [3, 2],
              width: 1,
              height: 1,
              url: "",
              Name: "",
              heightToWidthRatio: 1,
              linkKey: "",
              linkTitle: "",
              linkType: "",
              linkTypeName: "",
              linkParams: "",
              link: "",
            },
            {
              start: [3, 3],
              width: 1,
              height: 1,
              url: "",
              Name: "",
              heightToWidthRatio: 1,
              linkKey: "",
              linkTitle: "",
              linkType: "",
              linkTypeName: "",
              linkParams: "",
              link: "",
            },
          ];

          break;
      }
      that.selectLayoutItem = that.SelectComponent.ContentProperty.imgs[0];
    },
    /**  点击 魔方 图片区域  */
    selectLayoutContentItemClick(item, index) {
      let that = this;
      that.selectLayoutIndex = index;
      that.selectLayoutItem = item;
    },
    /**  轮播图添加 图片 商品  */
    addGoods(type, index, isAddHotGoods) {
      let that = this;
      that.paginations.page = 1;
      that.isAddHotGoods = isAddHotGoods;
      that.goodsType = type;
      that.tempSelectionHotGoods = [];
      let typeCode = "";
      switch (type) {
        case "project":
          that.apiType = "GoodsCategoryProject";
          that.goodsClassifyTitle = "项目列表";
          typeCode = "20";
          break;
        case "product":
          that.apiType = "GoodsCategoryProduct";
          that.goodsClassifyTitle = "产品列表";
          typeCode = "10";
          break;
        case "generalCard":
          that.apiType = "GoodsCategoryGeneralCard";
          that.goodsClassifyTitle = "通用次卡列表";
          typeCode = "30";
          break;
        case "timeCard":
          that.apiType = "GoodsCategoryTimeCard";
          that.goodsClassifyTitle = "时效卡列表";
          typeCode = "40";
          break;
        case "saveCard":
          that.apiType = "GoodsCategorySavingCard";
          that.goodsClassifyTitle = "储值卡列表";
          typeCode = "50";
          break;
        case "packageCard":
          that.apiType = "GoodsCategoryPackageCard";
          that.goodsClassifyTitle = "套餐卡列表";
          typeCode = "60";
          break;
      }
      that.carouselIndex = index;
      that.getGoodAll(that.apiType);
      that.goodsClassifyBool = true;
      that.$nextTick(() => {
        that.$refs.HotGoodsRef.clearSelection();
        if (that.SelectComponent.ContentProperty.length) {
          that.SelectComponent.ContentProperty.forEach((row) => {
            if (row.GoodsType == typeCode) {
              that.$refs.HotGoodsRef.toggleRowSelection(row);
            }
          });
        }
      });
    },
    /*  */
    formatterTimeCardDay(row) {
      if (row.ConsumeCycle == 0 || row.CycleLimitAmount == 0) {
        return "不限制";
      } else {
        return row.ConsumeCycle;
      }
    },
    /*  */
    formatterTimeCardNum(row) {
      if (row.ConsumeCycle == 0 || row.CycleLimitAmount == 0) {
        return "不限制";
      } else {
        return row.CycleLimitAmount;
      }
    },
    /**  关闭商品弹窗  */
    goodsDialogClose() { },
    /** 修改商品分页   */
    pageChange(page) {
      let that = this;
      that.paginations.page = page;
      that.getGoodAll(that.apiType);
    },
    /**  选择商品  */
    handleCurrentChange(row) {
      let that = this;
      if (that.SelectComponent.Code == "Carousel") {
        let item = that.SelectComponent.ContentProperty[that.carouselIndex];
        let temp = {
          linkKey: row.ID,
          linkTitle: row.Name,
          linkType: row.GoodsType,
          linkTypeName: row.GoodsTypeName,
          linkParams: { ID: row.ID, goodsType: row.GoodsType },
          link: "/pages/GoodsDetails/GoodsDetails",
        }
        that.SelectComponent.ContentProperty[that.carouselIndex] = Object.assign(item, temp);
        that.goodsClassifyBool = false;
      }
      else if (that.SelectComponent.Code == "Puzzle") {
        let item = {
          linkKey: row.ID,
          linkTitle: row.Name,
          linkType: row.GoodsType,
          linkTypeName: row.GoodsTypeName,
          linkParams: { ID: row.ID, goodsType: row.GoodsType },
          link: "/pages/GoodsDetails/GoodsDetails",
        }
        that.selectLayoutItem = Object.assign(that.selectLayoutItem, item);

        that.goodsClassifyBool = false;
      } else {
        if (that.isAddHotGoods) {
          that.$refs.HotGoodsRef.toggleRowSelection(row);
        } else {
          let item = {
            linkKey: row.ID,
            linkTitle: row.Name,
            linkType: row.GoodsType,
            linkTypeName: row.GoodsTypeName,
            linkParams: { ID: row.ID, goodsType: row.GoodsType },
            link: "/pages/GoodsDetails/GoodsDetails",
          }
          let temp = that.SelectComponent.ContentProperty
          that.SelectComponent.ContentProperty = Object.assign(temp, item);
          that.goodsClassifyBool = false;
        }
      }
    },
    /**  删除 链接的商品  */
    deleteSwiperGoods() {
      let that = this;
      that.SelectComponent.ContentProperty[that.carouselIndex].goodsID = "";
      that.SelectComponent.ContentProperty[that.carouselIndex].goodsName = "";
      that.SelectComponent.ContentProperty[that.carouselIndex].GoodsType = "";
      that.SelectComponent.ContentProperty[that.carouselIndex].GoodsTypeName =
        "";
    },
    /**  删除轮播图 中的item  */
    deleteCarouseClick(indx) {
      let that = this;
      that.SelectComponent.ContentProperty.splice(indx, 1);
    },

    // 点击推荐
    recommend() {
      const that = this;
      that.RmDialog = true;
      that.Title = "商品列表";
      that.getGoodAll();
    },
    /**  鼠标移入组件  */
    compontenMouse(comp) {
      let that = this;
      for (let item of that.comps) {
        item.isShowBoder = false;
      }
      comp.isShowBoder = true;
    },
    /**  鼠标移出 组件  */
    compontenMouseLeave(comp) {
      comp.isShowBoder = false;
    },
    /** 选中 组件   */
    selectComponentClick(comp, index) {
      let that = this;
      that.selectComIndex = index;
      for (let item of that.comps) {
        item.isEdit = false;
      }
      comp.isEdit = true;
      that.SelectComponent = comp;
      if (that.SelectComponent.Code == 'Puzzle') {
        that.selectLayoutItem = that.SelectComponent.ContentProperty.imgs[0];
      }
    },

    /**  门店名称  */
    getEntityName(val) {
      let that = this;
      that.EntityName = val;
    },
    /** 热门商品选项发生改变时  */
    hotGoodsSelectionChange(selection) {
      let that = this;
      that.tempSelectionHotGoods = selection;
    },
    /**  保存热门推荐商品  */
    saveHotGoods() {
      let that = this;
      let tempArr = that.tempSelectionHotGoods.filter((val) => {
        let typeGoods = that.SelectComponent.ContentProperty.filter(
          (item) => val.GoodsType == item.GoodsType
        );
        let is = typeGoods.some((v) => v.ID == val.ID);
        if (!is) return val;
      });
      that.SelectComponent.ContentProperty.push.apply(
        that.SelectComponent.ContentProperty,
        tempArr
      );
      that.goodsClassifyBool = false;
    },
    /**    */
    deleteHotGoodsItemClick(indx) {
      let that = this;
      that.SelectComponent.ContentProperty.splice(indx, 1);
    },
    /**  重置 空白元素颜色  */
    resetColor() {
      let that = this;
      that.SelectComponent.ConfigProperty.Color = "#FFFFFF";
    },
    /**  搜索商品  */
    SearchGoods() {
      let that = this;
      that.paginations.page = 1;
      that.getGoodAll(that.apiType);
    },

    /*** 拼团 **********************************/
    /**  添加拼团活动  */
    addGrouponClick() {
      let that = this;
      that.getGroupon_list().then(() => {
        that.isLink = false;
        that.grouponDialog = true;
      });
    },
    /**  拼团翻页  */
    grouponPageChange(page) {
      let that = this;
      that.grouponPaginations.page = page;
      that.getGroupon_list();
    },
    /**  拼团商品类型   */
    grouponGoodsTypeFormatter(row) {
      switch (row.GoodsType) {
        case "10":
          return "产品";
        case "20":
          return "项目";
        case "30":
          return "通用次卡";
        case "40":
          return "时效卡";
        default:
          return "";
      }
    },
    /**  成团人数和价格   */
    grouponGrouponPriceFormatter(row) {
      if (Array.from(row.GrouponPrice).length == 0) return "";
      var filter_NumFormat = this.$options.filters["NumFormat"];
      let temp = Array.from(row.GrouponPrice)
        .map((val) => {
          return (
            val.PeopleNum + "人/" + filter_NumFormat(val.GroupPrice) + "元"
          );
        })
        .join("\n");
      return temp;
    },
    /**  选中拼团商品item  */
    handleGrouponCurrentChange(row) {
      let that = this;
      that.$refs.GrouponGoodsRef.toggleRowSelection(row, true);
    },
    /**  选中拼团商品item  */
    handleGrouponSelectionChange(selection) {
      let that = this;
      if (selection.length > 3) {
        that.$message.error("不能超出3个选项");
        let row = selection[selection.length - 1];
        that.$refs.GrouponGoodsRef.toggleRowSelection(row, false);
        return;
      }
      that.selectGrouponGoodsList = selection;
    },
    /**   保存选中的拼团商品 */
    saveGroupinGoodsClick() {
      let that = this;
      that.SelectComponent.ContentProperty.push.apply(
        that.SelectComponent.ContentProperty,
        that.selectGrouponGoodsList
      );

      that.grouponDialog = false;
    },
    /**  删除拼团商品  */
    removeGrouponGoodsItemClick(index) {
      let that = this;
      that.SelectComponent.ContentProperty.splice(index, 1);
    },

    /*** 秒杀 **********************************/

    /**  添加秒杀活动  */
    addSeckillClick() {
      let that = this;
      that.getSeckill_list().then(() => {
        that.isLink = false;
        that.seckilllDialog = true;
      });
    },
    /**  秒杀修改  */
    handleSeckillCurrentChange(row) {
      let that = this;
      that.$refs.SeckillGoodsRef.toggleRowSelection(row, true);
    },
    /**   秒杀选择活动  */
    handleSeckillSelectionChange(selection) {
      let that = this;
      if (selection.length > 3) {
        that.$message.error("不能超出3个选项");
        let row = selection[selection.length - 1];
        that.$refs.SeckillGoodsRef.toggleRowSelection(row, false);
        return;
      }
      that.selectSeckillGoodsList = selection;
    },
    /**  秒杀活动修改分页  */
    seckillPageChange(page) {
      let that = this;
      that.seckillPaginations.page = page;
      that.seckill_list();
    },
    /**  保存秒杀商品  */
    saveSeckillGoodsClick() {
      let that = this;
      that.SelectComponent.ContentProperty.push.apply(
        that.SelectComponent.ContentProperty,
        that.selectSeckillGoodsList
      );
      that.seckilllDialog = false;
    },
    /**  删除拼团商品  */
    removeSeckillGoodsItemClick(index) {
      let that = this;
      that.SelectComponent.ContentProperty.splice(index, 1);
    },

    /** 网络请求  ****************************************  */
    // 获取分类商品列表
    getGoodAll(apiType) {
      const that = this;
      const params = {
        PageNum: that.paginations.page,
        Name: that.goodsSearchName,
        IsAllowSell: that.IsAllowSell,
      };
      API[apiType](params).then((res) => {
        if (res.StateCode == 200) {
          that.GoodList = res.List;
          that.paginations.total = res.Total;
        } else {
          that.$message.error({
            message: res.Message,
            duration: 2000,
          });
        }
      });
    },
    /**  请求组件  */
    getComponents() {
      let that = this;
      let params = {};
      API.getComponents(params).then((res) => {
        if (res.StateCode == 200) {
          that.ComponentData = res.Data;
          // that.ComponentData.BaseComponent.push({
          //   Code: "Puzzle",
          //   Name: "拼图",
          //   Icon: "Puzzle",
          //   Type: "200",
          //   Sequence: 40,
          // });
        } else {
          that.$message.error({
            message: res.Message,
            duration: 2000,
          });
        }
      });
    },
    getComponentsProperty(val) {
      if (!val) {
        return "";
      }
      if (val.Code == "Groupon") {
        let goodsJSON = {
          GoodsList: val.ContentProperty,
          Conf: val.ConfigProperty,
        };
        return JSON.stringify(goodsJSON);
      }
      if (val.Code == "Seckill") {
        let goodsJSON = {
          GoodsList: val.ContentProperty,
          Conf: val.ConfigProperty,
        };
        return JSON.stringify(goodsJSON);
      }
      if (val.Code == "GoodsList") {
        let goodsJSON = {
          GoodsList: val.ContentProperty,
          Conf: val.ConfigProperty,
        };
        return JSON.stringify(goodsJSON);
      }
      if (val.Code == "EntityInfo") {
        return JSON.stringify(val.ConfigProperty);
      }

      if (val.Code == "AuxiliaryBlank") {
        return JSON.stringify(val.ConfigProperty);
      }

      if (val.Code == "AuxiliaryLine") {
        return JSON.stringify(val.ConfigProperty);
      }
      if (val.Code == "Picture") {
        let pictureJSON = {
          ContentProperty: val.ContentProperty,
          ConfigProperty: val.ConfigProperty,
        };
        return JSON.stringify(pictureJSON);
      }
      if (val.Code == "Puzzle") {
        let pictureJSON = {
          ContentProperty: val.ContentProperty,
          ConfigProperty: val.ConfigProperty,
        };
        return JSON.stringify(pictureJSON);
      }
      return JSON.stringify(val.ContentProperty ? val.ContentProperty : "");
    },
    /** 保存首页配置   */
    HomeConfcreate() {
      let that = this;
      // .filter(i => i.Code != 'Puzzle')
      let HomeConfList = that.comps.map((val, indx) => {
        let Component = {
          ComponentsCode: val.Code,
          ComponentsProperty: that.getComponentsProperty(val),
          Sequence: indx,
        };
        return Component;
      });
      let params = { HomeConfList: HomeConfList };
      that.loading = true;
      API.HomeConfcreate(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.$message.success({
              message: "保存成功",
              duration: 2000,
            });
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(() => {
          that.loading = false;
        });
    },
    /**  获取首页配置  */
    getHomeConf() {
      let that = this;
      API.getHomeConf().then((res) => {
        if (res.StateCode == 200) {
          that.comps = res.Data.map((val) => {
            return that.getComponentPropertyJSON(val);
          });
        } else {
          that.$message.error({
            message: res.Message,
            duration: 2000,
          });
        }
      });
    },

    getHomeComponentContentPropertyJSonParse(val) {
      if (!val) {
        return "";
      }
      if (val.ComponentsCode == "GoodsList") {
        return JSON.parse(val.ComponentsProperty).GoodsList;
      }
      if (val.ComponentsCode == "Seckill") {
        return JSON.parse(val.ComponentsProperty).GoodsList;
      }
      if (val.ComponentsCode == "Groupon") {
        return JSON.parse(val.ComponentsProperty).GoodsList;
      }
      if (val.ComponentsCode == "Picture") {
        return JSON.parse(val.ComponentsProperty).ContentProperty;
      }

      if (val.ComponentsCode == "Puzzle") {
        return JSON.parse(val.ComponentsProperty).ContentProperty;
      }
      return JSON.parse(val.ComponentsProperty);
    },
    getHomeComponentConfigPropertyJSonParse(val) {
      if (!val) {
        return "";
      }
      if (val.ComponentsCode == "GoodsList") {
        return JSON.parse(val.ComponentsProperty).Conf;
      }

      if (val.ComponentsCode == "Seckill") {
        return JSON.parse(val.ComponentsProperty).Conf;
      }

      if (val.ComponentsCode == "Groupon") {
        return JSON.parse(val.ComponentsProperty).Conf;
      }
      if (val.ComponentsCode == "Picture") {
        return JSON.parse(val.ComponentsProperty).ConfigProperty;
      }
      if (val.ComponentsCode == "Puzzle") {
        return JSON.parse(val.ComponentsProperty).ConfigProperty;
      }
      return JSON.parse(val.ComponentsProperty);
    },
    /**  获取 组件属性  */
    getComponentPropertyJSON(val) {
      const that = this;
      let comp = {
        Code: val.ComponentsCode,
        Name: val.ComponentsCode,
        Sequence: val.Sequence,
        isEdit: false,
        isShowBoder: false,
        ConfigProperty: that.getHomeComponentConfigPropertyJSonParse(val),
        ContentProperty: that.getHomeComponentContentPropertyJSonParse(val),
      };
      return comp;
    },
    /**   富文本上传图片 */
    async upload_addAttachment(base64) {
      let that = this;
      let params = { AttachmentURL: base64 };
      that.logding = true;
      let res = await API.upload_addAttachment(params);
      if (res.StateCode == 200) {
        return res.Data.AttachmentURL;
      } else {
        that.$message.error(res.Message);
      }
      that.logding = false;
    },

    /**  上传文件  */
    async upload_uploadFile(file) {
      let that = this;
      that.logding = true;
      try {
        let params = {
          file: file,
        };
        let res = await uploadApi.upload_uploadFile(params);
        if (res.StateCode == 200) {
          that.logding = false;
          return res.Data.ID;
        } else {
          that.logding = false;
          that.$message.error(res.Message);
        }
        that.logding = false;
      } catch (error) {
        that.$message.error(error);
        that.logding = false;
      }
    },
    /**   富文本 配置 */
    QuillEditorOptions() {
      let that = this;
      //  富文本编辑器配置
      var toolbarOptions = [
        ["bold", "italic", "underline", "strike"], // 加粗 斜体 下划线 删除线 -----['bold', 'italic', 'underline', 'strike']
        ["blockquote", "code-block"], // 引用  代码块-----['blockquote', 'code-block']
        [{ header: 1 }, { header: 2 }], // 1、2 级标题-----[{ header: 1 }, { header: 2 }]
        [{ list: "ordered" }, { list: "bullet" }], // 有序、无序列表-----[{ list: 'ordered' }, { list: 'bullet' }]
        [{ script: "sub" }, { script: "super" }], // 上标/下标-----[{ script: 'sub' }, { script: 'super' }]
        [{ indent: "-1" }, { indent: "+1" }], // 缩进-----[{ indent: '-1' }, { indent: '+1' }]
        [{ direction: "rtl" }], // 文本方向-----[{'direction': 'rtl'}]
        [{ size: ["small", false, "large", "huge"] }], // 字体大小-----[{ size: ['small', false, 'large', 'huge'] }]
        [{ header: [1, 2, 3, 4, 5, 6, false] }], // 标题-----[{ header: [1, 2, 3, 4, 5, 6, false] }]
        [{ color: [] }, { background: [] }], // 字体颜色、字体背景颜色-----[{ color: [] }, { background: [] }]
        [{ font: [] }], // 字体种类-----[{ font: [] }]
        [{ align: [] }], // 对齐方式-----[{ align: [] }]
        ["clean"], // 清除文本格式-----['clean']
        ["image"],
      ]; // 链接、图片、视频-----['link', 'image', 'video']

      that.editorOption = {
        modules: {
          //工具栏定义的
          toolbar: {
            container: toolbarOptions,
            handlers: {
              image: function (val) {
                if (val) {
                  // 通过input的type=file唤醒选择弹框，选择之后自定义上传路径
                  // document.querySelector(".detail-image-uploader input").click();
                  document
                    .querySelector(".richText-image-uploader input")
                    .click();
                } else {
                  this.quill.format("image", false);
                }
              },
            },
          },
        },
        //主题
        theme: "snow",
        placeholder: "请输入正文",
      };
    },
    /**  获取拼团列表  */
    async getGroupon_list() {
      let that = this;
      let params = {
        PageNum: that.grouponPaginations.page,
      };
      let res = await API.groupon_list(params);
      if (res.StateCode == 200) {
        that.groupon_list = res.List;
        that.grouponPaginations.total = res.Total;
      } else {
        that.$message.error(res.Message);
      }
    },
    /**  获取秒杀列表  */
    async getSeckill_list() {
      let that = this;
      let params = {
        PageNum: that.seckillPaginations.page,
      };
      let res = await API.seckill_list(params);
      if (res.StateCode == 200) {
        that.seckillList = res.List;
        that.seckillPaginations.total = res.Total;
      } else {
        that.$message.error(res.Message);
      }
    },
    /**  获取自定义页面列表  */
    async customPage_list() {
      let that = this;
      try {
        let params = {};
        let res = await API.customPage_list(params);
        if (res.StateCode == 200) {
          that.customPages = res.Data;
        }
        else {
          that.$message.error(res.Message);
        }
      } catch (error) {
        that.$message.error(error);
      }

    },
    // 获取分类配置
    GoodsCategoryAll() {
      const that = this;
      API.GoodsCategoryAll().then((res) => {
        if (res.StateCode == 200) {
          if (!res.Data.length) return;
          that.appletGoodsCategory = res.Data.map(i => {
            return {
              ID:i.ID,
              Name:i.Name,
              ParentID:i.ParentID,
            }
          })
          that.goodsClassify = res.Data;
        } else {
          that.$message.error({
            message: res.Message,
            duration: 2000,
          });
        }
      });
    },
  },
  /** 监听数据变化   */
  watch: {},

  /**  实例创建完成之后  */
  created() {
    let that = this;
    that.QuillEditorOptions();
    that.EntityName = JSON.parse(
      localStorage.getItem("access-user")
    ).EntityName;
    that.getComponents();
    that.getHomeConf();
    that.customPage_list();
    that.GoodsCategoryAll();
  },
  /**  实例被挂载后调用  */
  mounted() { },
};
</script>

<style lang="scss">
.HomeDecoration {
  height: calc(100vh - 165px);

  .custom-page_edit {
    position: relative;
    z-index: 2;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    height: 100%;
    display: flex;
    background: #ffffff;
    .custom-page-left {
      width: 320px;
      height: 100%;
      box-sizing: border-box;
      overflow-y: auto;
      overflow-x: hidden;
      float: left;
      background: #fff;
      position: absolute;
      left: 0;
      z-index: 5;
      .custom-page-left-dl {
        display: flex;
        flex-wrap: wrap;
        text-align: center;
        margin: 8px 0;

        .custom-page-left-dl-item {
          cursor: pointer;
          box-sizing: border-box;
          padding: 15px 0;
          margin: 0 0 8px 6px;
          width: 95px;
          height: 95px;
        }
      }

      .el-scrollbar__thumb {
        //可设置滚动条颜色
        background: transparent; //这里我设置成了透明色,可以根据需求添加自己想要的颜色
      }

      .el-collapse-item__content {
        padding-bottom: unset;
      }
    }

    .custom-page-view {
      box-sizing: border-box;
      padding: 48px 0;
      overflow-y: auto;
      position: absolute;
      top: 0;
      bottom: 0;
      left: 320px;
      right: 320px;
      background: #ebedf0;
      display: flex;
      justify-content: center;
      margin: 0;
      border: 0;
      font-size: 100%;

      .custom-phone {
        width: 375px;
        flex-shrink: 0;
        box-shadow: 0px 2px 12px 0px rgb(119, 119, 119);
        position: relative;
        border: 0;
        font-size: 100%;
        height: fit-content;

        .custom-phone-nav-bar {
          width: 100%;
          height: 64px;
        }

        .custom-phone-search {
          height: 40px;
          width: 100%;
          background: #ffffff;
          box-sizing: border-box;
          padding: 6px 12px;

          .search-content {
            height: 30px;
            background: #ebedf0;
            display: flex;
            align-items: center;
            border-radius: 18px;
            padding: 0px 12px;
          }
        }

        .custom-phone-content {
          min-height: 535px;
          height: 535px;

          .drag-box {
            position: relative;
            width: 100%;
          }

          .active {
            .operate-info {
              display: -webkit-flex;
              display: -ms-flexbox;
              display: flex;
              position: absolute;
              z-index: 2;
              right: -2px;
              top: 0px;
              z-index: 18;
            }
          }

          .active:before {
            content: "";
            position: absolute;
            width: 100%;
            height: 100%;
            left: 0;
            top: 0;
            box-sizing: border-box;
            border: 2px solid var(--zl-color-orange-primary);
            z-index: 10;
            cursor: move;
          }

          .drag-box:not(.active):hover:before {
            content: "";
            position: absolute;
            width: 100%;
            height: 100%;
            left: 0;
            top: 0;
            box-sizing: border-box;
            border: 1px dashed var(--zl-color-orange-primary);
            z-index: 10;
          }
        }

        .el-scrollbar__thumb {
          //可设置滚动条颜色
          background: transparent; //这里我设置成了透明色,可以根据需求添加自己想要的颜色
        }

        .custom-phone-tabbar {
          display: flex;
          width: 375px;
          left: unset;
          right: unset;
          top: unset;
          bottom: unset;
          height: 49px;
          background: #ffffff;
          box-shadow: 0px -2px 10px 0px rgb(203, 203, 203);

          div {
            width: 50%;
          }
        }
      }
    }

    .custom-page-right {
      position: absolute;
      right: 0;
      top: 0;
      bottom: 0;
      z-index: 5;
      box-sizing: border-box;
      overflow-y: auto;
      width: 320px;
      background: #fff;

      .Carousel {
        background: #f7f8fa;

        .carousel-item {
          margin: 5px;
          background: #ffffff;
          padding: 8px;
          display: flex;
          align-items: center;
          position: relative;
          border-radius: 6px;
        }

        .swiperImage {
          width: 75px;
          height: 75px;
          overflow: hidden;
          border: 1px solid #c0ccda;
          border-radius: 6px;
          -webkit-box-sizing: border-box;
          box-sizing: border-box;
          margin: 0 8px 0px 0;
          display: inline-block;
          flex-shrink: 0;
        }

        .carousel-upload {
          .el-upload.el-upload--picture-card {
            width: 75px !important;
            height: 75px !important;
            line-height: 90px;
          }
        }
      }

      .picture-container {
        padding: 18px 0px;

        .header-title {
          margin: 0 0 6px 16px;
          font-size: 16px;
          font-weight: 500;
        }

        .el-collapse {
          border-top: unset;

          .el-collapse-item__header {
            padding-left: 12px;
            padding-right: 12px;
            background: #f7f8fa;
            margin-top: 12px;
          }

          .el-collapse-item__wrap {
            border-bottom: unset;

            .el-collapse-item__content {
              background: #f7f8fa;
              padding-bottom: unset;
            }
          }

          .add-img {
            display: flex;
            justify-content: space-between;
            padding: 12px;

            .custom-upload {
              width: 66px;
              height: 66px;
              display: flex;
              align-items: center;
              justify-content: center;
              border-radius: 2px;
              border: none;
              background: #ffffff;
              flex-shrink: 0;


              .img {
                width: 100%;
                height: 100%;
              }
            }

            .link-address {
              display: flex;
              align-items: center;
              flex-shrink: 0;
              padding-left: 6px;
            }
          }
        }

        .picture-styl-container {
          padding-bottom: 5px;

          .picture-style-box {
            display: flex;
            justify-content: space-between;
            padding: 6px 12px;
            align-items: center;
            margin-bottom: 20px;

            .title {}

            .custom-picture-input {
              width: 100px;
            }

            .picture-color {
              display: flex;
              align-items: center;
            }
          }
        }
      }

      .puzzle-container {
        padding: 18px 0px;

        .header-title {
          margin: 0 0 6px 16px;
          font-size: 16px;
          font-weight: 500;
        }

        .el-collapse {
          border-top: unset;
          background: #f7f8fa;

          .el-collapse-item__header {
            padding-left: 12px;
            padding-right: 12px;
            background: #f7f8fa;
          }

          .el-collapse-item__wrap {
            border-bottom: unset;

            .el-collapse-item__content {
              background: #f7f8fa;
              padding-bottom: 12px;
            }
          }
        }

        .layout-header {
          display: flex;
          padding: 6px 12px;

          .title {}

          .layout-item-content {
            flex: 1;
            padding-left: 12px;

            .layout-box {
              width: 228px;
              width: 100%;
              display: flex;
              flex-wrap: wrap;

              .layout-item {
                margin-right: 1px;
                width: 56px;
                height: 32px;
                display: flex;
                justify-content: center;
                align-items: center;
                background: #ffffff;
                border-radius: 2px 0px 0px 0px;
                cursor: pointer;

                .img {
                  width: 15px;
                  height: 15px;
                }
              }

              .selectedState {
                background-color: var(--zl-color-orange-primary-end);
              }
            }
          }
        }

        .layout-content {
          border-top: 1px solid #f7f8fa;
          border-left: 1px solid #f7f8fa;
          width: 296px;
          position: relative;
          padding: 0px 12px;

          .layout-item {
            display: flex;
            justify-content: center;
            align-items: center;
            box-sizing: border-box;
            position: absolute;
            background: #ffffff;
            font-size: 12px;
            width: 72px;
            height: 72px;
            border: 1px solid #f7f8fa;
            cursor: pointer;

            .placeholderText {
              text-align: center;
              padding: 6px 12px;
              color: #a9acb3;
            }

            .img_wrap {
              width: 100%;
              height: 100%;

              .img {
                width: 100%;
                height: 100%;
              }
            }
          }

          .selected {
            background: var(--zl-color-orange-primary-end);
          }

          .selected:before {
            content: '';
            width: calc(100%);
            height: calc(100%);
            position: absolute;
            left: -1px;
            top: -1px;
            border: 1px solid var(--zl-color-orange-primary);
            z-index: 3;
          }
        }

        .add-img {
          display: flex;
          justify-content: space-between;
          padding: 12px;
          background: #ffffff;
          margin-top: 12px;

          .custom-upload {
            width: 66px;
            height: 66px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 2px;
            border: none;
            background: #f7f8fa;
            flex-shrink: 0;

            .img {
              width: 100%;
              height: 100%;
            }
          }

          .link-address {
            display: flex;
            align-items: center;
            flex-shrink: 0;
            padding-left: 6px;

          }
        }

        .puzzle-style-box {

          display: flex;
          justify-content: space-between;
          padding: 6px 12px;
          align-items: center;
          margin-bottom: 20px;
        }
      }
    }

    .custom-button {
      padding: 4px 12px;
      border-top-left-radius: 0;
    }
  }


}

.custom-component-popover-class {
  padding: unset;
}
</style>
