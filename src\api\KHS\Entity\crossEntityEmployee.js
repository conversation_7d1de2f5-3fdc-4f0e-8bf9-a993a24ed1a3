/**
 * Created by preference on 2022/12/08
 *  zmx
 */

import * as API from "@/api/index";
export default {
  /** 本店员工  */
  employee_belongCurrentEntity: (params) => {
    return API.POST("api/employee/belongCurrentEntity", params);
  },
  /** 跨店员工  */
  employee_belongCurrentNoPrimaryEntity: (params) => {
    return API.POST("api/employee/belongCurrentNoPrimaryEntity", params);
  },
  /** 跨店员工-可添加的员工  */
  employee_belongCurrentNoEntity: (params) => {
    return API.POST("api/employee/belongCurrentNoEntity", params);
  },
  /**  添加跨店员工 */
  employee_createBelongEntity: (params) => {
    return API.POST("api/employee/createBelongEntity", params);
  },
  /** 删除跨店员工  */
  employee_deleteBelongEntity: (params) => {
    return API.POST("api/employee/deleteBelongEntity", params);
  },
};
