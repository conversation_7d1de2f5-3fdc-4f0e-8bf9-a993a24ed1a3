<template>
  <div class="SaleModifyPrice content_body">
    <!-- 头部 -->
    <div class="nav_header nav_header_1">
      <el-row>
        <el-col :span="22">
          <el-form :inline="true" size="small" @submit.native.prevent @keyup.enter.native="handleSearch">
            <el-form-item label="名称">
              <el-input v-model="searchForm.Name" placeholder="请输入职位名称" @change="handleSearch" clearable></el-input>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleSearch" size="small" v-prevent-click>搜索</el-button>
            </el-form-item>
          </el-form>
        </el-col>
        <el-col :span="2" class="text_right" align="right">
          <el-button type="primary" size="small" v-prevent-click @click="addSaleDiscountClick"> 新增 </el-button>
        </el-col>
      </el-row>
    </div>

    <el-table size="small" :data="tableData">
      <el-table-column label="职位名称" prop="JobTypeName"></el-table-column>
      <el-table-column label="职位描述" prop="JobDescription"></el-table-column>
      <el-table-column label="折扣" prop="Discount">
        <template slot-scope="scope"> {{ scope.row.Discount / 10 }} 折 </template>
      </el-table-column>

      <el-table-column label="操作" width="150">
        <template slot-scope="scope">
          <el-button type="primary" size="small" @click="editSaleDiscountClick(scope.row)">编辑</el-button>
          <el-button type="danger" size="small" @click="deleteSaleDiscountClick(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <div class="pad_15 text_right">
      <el-pagination background v-if="paginations.total > 0" @current-change="handleCurrentChange" :current-page.sync="paginations.page" :page-size="paginations.page_size" :layout="paginations.layout" :total="paginations.total"></el-pagination>
    </div>

    <el-dialog :title="isAdd ? '新增销售改价' : '编辑销售改价'" :visible.sync="dialogVisible" width="800px">
      <el-tabs v-model="activeName">
        <el-tab-pane label="基本信息" name="1">
          <el-form :model="ruleForm_add" :rules="rules_add" ref="ruleForm_add" label-width="90px" size="small">
            <el-form-item label="员工职位" prop="JobTypeID">
              <el-select v-model="ruleForm_add.JobTypeID" placeholder="请选择" filterable clearable>
                <el-option v-for="item in jobTypeList" :key="item.ID" :label="item.JobName" :value="item.ID"> </el-option>
              </el-select>
            </el-form-item>

            <el-form-item label="折扣" prop="Discount">
              <el-input v-model="ruleForm_add.Discount" v-input-fixed="2">
                <template slot="append">折</template>
              </el-input>
            </el-form-item>
          </el-form>
        </el-tab-pane>
        <el-tab-pane label="适用范围" name="2">
          <div class="message el-message--info marbm_10">
            <i class="el-message__icon el-icon-info"></i>
            <p class="el-message__content">适用于同级所有节点，则只需勾选父节点。比如：适用于所有节点，只需勾选“顶级/第一个”节点。</p>
          </div>
          <el-scrollbar class="el-scrollbar_height">
            <el-tree ref="tree_entity_ref" :expand-on-click-node="false" :check-on-click-node="true" :check-strictly="true" :data="assignEntitylist" show-checkbox node-key="ID" :default-checked-keys="defaultCheckedKeys" :default-expanded-keys="defaultExpandedKeys" :props="defaultProps"></el-tree>
          </el-scrollbar>
        </el-tab-pane>
      </el-tabs>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false" size="small" v-prevent-click>取 消</el-button>
        <el-button type="primary" size="small" v-prevent-click @click="saveEmployeeJobSaleDiscountClick" :loading="modalLoading">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import API from "@/api/KHS/Setting/saleModifyPrice.js";
export default {
  name: "SaleModifyPrice",
  props: {},
  /** 监听数据变化   */
  watch: {},
  /**  引入的组件  */
  components: {},
  /**  Vue 实例的数据对象**/
  data() {
    var validateDiscount = (rule, value, callback) => {
      if (value === "" || value === null) {
        callback(new Error("请输入折扣"));
      } else {
        if (value > 100 || value < 0) {
          callback(new Error("折扣必须在0-100之间"));
        } else {
          callback();
        }
      }
    };
    return {
      isAdd: false,
      modalLoading: false,
      activeName: "1",
      dialogVisible: false,
      entityList: [],
      assignEntitylist: [],
      jobTypeList: [],
      defaultCheckedKeys: [],
      defaultExpandedKeys: [],
      defaultProps: {
        children: "Child",
        label: "EntityName",
      },
      searchForm: {
        Name: "",
      },
      tableData: [],
      paginations: {
        page: 1, // 当前位于哪页
        total: 0, // 总数
        page_size: 10, // 1页显示多少条
        layout: "total, prev, pager, next,jumper", // 翻页属性
      },
      ruleForm_add: {
        JobTypeID: "", //职位编号
        Discount: "", //折扣比
        Entity: [], //门店
      },
      rules_add: {
        JobTypeID: [{ required: true, message: "请选择职务", trigger: ["change", "blur"] }],
        Discount: [{ validator: validateDiscount, required: true, trigger: ["change", "blur"] }],
      },
    };
  },
  /**计算属性  */
  computed: {},
  /**  方法集合  */
  methods: {
    /**  保存新增  */
    saveEmployeeJobSaleDiscountClick() {
      this.$refs["ruleForm_add"].validate((valid) => {
        if (valid) {
          if (this.isAdd) {
            this.employeeJobSaleDiscount_create();
          } else {
            this.employeeJobSaleDiscount_update();
          }
        }
      });
    },
    /**   新增 */
    addSaleDiscountClick() {
      this.dialogVisible = true;
      this.isAdd = true;
      this.ruleForm_add = {
        JobTypeID: "", //职位编号
        Discount: "", //折扣比
        Entity: [], //门店
      };
      this.assignEntitylist = [];
      Object.assign(this.assignEntitylist, this.entityList);
      this.defaultCheckedKeys = [];
      this.defaultExpandedKeys = [1];
    },
    /**  编辑  */
    editSaleDiscountClick(row) {
      this.dialogVisible = true;
      this.isAdd = false;
      let EntityIDs = row.Entity.map((item) => item.EntityID);
      this.ruleForm_add = {
        ID: row.ID,
        JobTypeID: row.JobTypeID, //职位编号
        Discount: row.Discount / 10, //折扣比
        Entity: [], //门店
      };
      this.assignEntitylist = [];
      Object.assign(this.assignEntitylist, this.entityList);
      this.defaultCheckedKeys = EntityIDs;
      this.defaultExpandedKeys = EntityIDs;
    },
    /**  删除  */
    deleteSaleDiscountClick(row) {
      this.$confirm("此操作将永久删除该数据, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.employeeJobSaleDiscount_delete(row.ID);
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    /**  换页  */
    handleCurrentChange(page) {
      this.paginations.page = page;
      this.employeeJobSaleDiscount_list();
    },
    /**  搜索  */
    handleSearch() {
      this.paginations.page = 1;
      this.employeeJobSaleDiscount_list();
    },
    /* •••••••••••••••••••••••••••••••••••••••••••••••• */
    /**   职位列表 */
    getJobtype_all() {
      let that = this;
      let params = {
        JobTypeName: "", //名称搜索
      };
      API.jobtype_all(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.jobTypeList = res.Data;
          } else {
            that.$message.error(res.Message);
          }
        })
        .catch((fail) => {
          that.$message.error(fail);
        });
    },
    /**  门店列表  */
    getEntity_list() {
      let that = this;
      let params = {
        SearchKey: "", //搜索
        Active: true, //有效性
      };
      API.entity_list(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.entityList = res.Data;
          } else {
            that.$message.error(res.Message);
          }
        })
        .catch((fail) => {
          that.$message.error(fail);
        });
    },
    /**  列表  */
    employeeJobSaleDiscount_list() {
      let params = {
        PageNum: this.paginations.page, //页码
        Name: "", //职位搜索
      };
      API.employeeJobSaleDiscount_list(params)
        .then((res) => {
          if (res.StateCode == 200) {
            this.tableData = res.List;
            this.paginations.total = res.Total;
            this.paginations.page_size = res.PageSize;
          } else {
            this.$message.error(res.Message);
          }
        })
        .catch((fail) => {
          this.$message.error(fail);
        });
    },
    /**   添加  */
    employeeJobSaleDiscount_create() {
      let that = this;
      let Entity = this.$refs.tree_entity_ref.getCheckedKeys();
      if (!Entity || Entity.length == 0 ) {
        that.$message.error("请选择使用范围");
        return;
      }
      let params = {
        JobTypeID: this.ruleForm_add.JobTypeID, //职位编号
        Discount: this.ruleForm_add.Discount * 10, //折扣比
        Entity: this.$refs.tree_entity_ref.getCheckedKeys(), //门店
      };
      this.modalLoading = true;
      API.employeeJobSaleDiscount_create(params)
        .then((res) => {
          if (res.StateCode == 200) {
            this.employeeJobSaleDiscount_list();
            this.dialogVisible = false;
            this.modalLoading = false;
          } else {
            this.modalLoading = false;
            that.$message.error(res.Message);
          }
        })
        .catch((fail) => {
          this.modalLoading = false;
          that.$message.error(fail);
        });
    },
    /**  更新  */
    employeeJobSaleDiscount_update() {
      let that = this;
      let params = {
        ID: this.ruleForm_add.ID, //职位编号
        JobTypeID: this.ruleForm_add.JobTypeID, //职位编号
        Discount: this.ruleForm_add.Discount * 10,//折扣比
        Entity: this.$refs.tree_entity_ref.getCheckedKeys(), //门店
      };
      this.modalLoading = true;
      API.employeeJobSaleDiscount_update(params)
        .then((res) => {
          if (res.StateCode == 200) {
            this.employeeJobSaleDiscount_list();
            this.dialogVisible = false;
            this.modalLoading = false;
          } else {
            this.modalLoading = false;
            that.$message.error(res.Message);
          }
        })
        .catch((fail) => {
          this.modalLoading = false;
          that.$message.error(fail);
        });
    },
    /**   删除 */
    employeeJobSaleDiscount_delete(ID) {
      let that = this;
      let params = { ID: ID };
      API.employeeJobSaleDiscount_delete(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.$message.success("删除成功");
            this.employeeJobSaleDiscount_list();
          } else {
            that.$message.error(res.Message);
          }
        })
        .catch((fail) => {
          that.$message.error(fail);
        });
    },
  },
  /** 创建实例之前执行函数   */
  beforeCreate() {},
  /**  实例创建完成之后  */
  created() {},
  /**  在挂载开始之前被调用  */
  beforeMount() {},
  /**  实例被挂载后调用  */
  mounted() {
    this.getJobtype_all();
    this.getEntity_list();
    this.employeeJobSaleDiscount_list();
  },
  /**  数据更新 之前 调用  */
  beforeUpdate() {},
  /** 数据更新 完成 调用   */
  updated() {},
  /**  实例销毁之前调用  */
  beforeDestroy() {},
  /**  实例销毁后调用  */
  destroyed() {},
};
</script>

<style lang="scss">
.SaleModifyPrice {
  .el-scrollbar_height {
    height: 50vh;
    .el-scrollbar__wrap {
      overflow-x: hidden;
    }
  }
}
</style>
