<template>
  <div class="ChannelEmployeeSalaryPerformanceScheme content_body">
    <!-- 头部 -->
    <div class="nav_header">
      <el-row>
        <el-col :span="23">
          <el-form :model="searchData" size="small" :inline="true" @submit.native.prevent @keyup.enter.native="handleSearch">
            <el-form-item label="市场业绩取值方案">
              <el-input placeholder="输入市场业绩取值方案" v-model="searchData.Name" clearable @clear="handleSearch"></el-input>
            </el-form-item>
            <el-form-item label="有效性">
              <el-select v-model="searchData.Active" placeholder="请选择" clearable @change="handleSearch">
                <el-option label="有效" :value="true"></el-option>
                <el-option label="无效" :value="false"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" size="small" @click="handleSearch" v-prevent-click>搜索</el-button>
            </el-form-item>
          </el-form>
        </el-col>
        <el-col :span="1">
          <el-button type="primary" size="small" @click="addEmployeePerformaceSchemeClick" v-prevent-click>新增</el-button>
        </el-col>
      </el-row>
    </div>
    <!-- 表格 -->
    <el-table :data="tableData" v-loading="loading" size="small">
      <el-table-column prop="Name" label="市场业绩取值方案"></el-table-column>
      <el-table-column prop="IsCalculateChannelDeveloper" label="是否包含开发人员业绩">
        <template slot-scope="scope">
          {{ scope.row.IsCalculateChannelDeveloper ? "是" : "否" }}
        </template>
      </el-table-column>
      <el-table-column prop="IsCalculateChannelConsultant" label="是否包含市场咨询业绩">
        <template slot-scope="scope">
          {{ scope.row.IsCalculateChannelConsultant ? "是" : "否" }}
        </template>
      </el-table-column>
      <el-table-column prop="Active" label="有效性">
        <template slot-scope="scope">
          {{ scope.row.Active ? "有效" : "无效" }}
        </template>
      </el-table-column>
      <el-table-column label="操作" width="80">
        <template slot-scope="scope">
          <el-button type="primary" size="small" @click="editEmployeePerformaceSchemeClick(scope.row)" v-prevent-click>编辑</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <div class="pad_15 text_right">
      <el-pagination
        background
        v-if="paginations.total > 0"
        @current-change="handleCurrentChange"
        :current-page.sync="paginations.page"
        :page-size="paginations.page_size"
        :layout="paginations.layout"
        :total="paginations.total"
      ></el-pagination>
    </div>
    <!-- 新增弹框 -->
    <el-dialog title="新增市场业绩取值方案" :visible.sync="dialogVisible" width="30%">
      <el-form
        :model="employeePerformaceSchemeForm"
        size="small"
        @submit.native.prevent
        :rules="employeePerformaceSchemeFormRule"
        ref="employeePerformaceSchemeFormRef"
      >
        <el-form-item label="市场业绩取值方案" prop="Name" label-width="140px">
          <el-input v-model="employeePerformaceSchemeForm.Name" placeholder="请输入市场业绩取值方案"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false" size="small" v-prevent-click>取 消</el-button>
        <el-button type="primary" @click="saveEmployeePerformaceSchemeClick" v-prevent-click size="small">确 定</el-button>
      </span>
    </el-dialog>
    <!-- 编辑弹框 -->
    <el-dialog title="编辑市场业绩取值方案" :visible.sync="editdialogVisible" width="1100px" custom-class="editDialog">
      <el-form
        :model="editEmployeePerformaceSchemeForm"
        :inline="true"
        size="small"
        @submit.native.prevent
        :rules="employeePerformaceSchemeFormRule"
        ref="editEmployeePerformaceSchemeFormRef"
      >
        <el-row>
          <el-form-item label="市场业绩取值方案" prop="Name" label-width="140px">
            <el-input v-model="editEmployeePerformaceSchemeForm.Name"></el-input>
          </el-form-item>

          <el-form-item label="有效性" label-width="60px" prop="effectiveness">
            <el-radio v-model="editEmployeePerformaceSchemeForm.Active" :label="true">有效</el-radio>
            <el-radio v-model="editEmployeePerformaceSchemeForm.Active" :label="false">无效</el-radio>
          </el-form-item>
        </el-row>

        <el-row>
          <el-form-item prop="IsCalculateChannelDeveloper">
            <el-checkbox v-model="editEmployeePerformaceSchemeForm.IsCalculateChannelDeveloper">是否包含开发人员</el-checkbox>
          </el-form-item>
          <el-form-item prop="IsCalculateChannelConsultant">
            <el-checkbox v-model="editEmployeePerformaceSchemeForm.IsCalculateChannelConsultant">是否包含市场咨询</el-checkbox>
          </el-form-item>
        </el-row>
      </el-form>
      <el-tabs v-model="activeName">
        <el-tab-pane label="销售-产品" name="sale-product">
          <el-table
            :data="employeePerformaceSchemeDetail.ProductCategory"
            size="small"
            max-height="480px"
            row-key="CategoryID"
            :tree-props="{ children: 'Child' }"
            v-loading="saleProductCategoryLoading"
          >
            <el-table-column prop="CategoryName" label="产品分类"></el-table-column>
            <el-table-column prop="PayPerformanceRate" label="现金业绩">
              <template slot-scope="scope">
                <el-input
                  type="number"
                  size="mini"
                  min="0"
                  max="100"
                  v-model="scope.row.PayPerformanceRate"
                  v-input-fixed="2"
                  @input="changePayPerformanceRate(scope.row)"
                >
                  <template slot="append">%</template>
                </el-input>
              </template>
            </el-table-column>
            <el-table-column prop="SavingCardPerformanceRate" label="卡抵扣业绩">
              <template slot-scope="scope">
                <el-input
                  type="number"
                  size="mini"
                  min="0"
                  max="100"
                  v-model="scope.row.SavingCardPerformanceRate"
                  v-input-fixed="2"
                  @input="changeSavingCardPerformanceRate(scope.row)"
                >
                  <template slot="append">%</template>
                </el-input>
              </template>
            </el-table-column>
            <el-table-column prop="SavingCardPerformanceLargessRate" label="赠送卡抵扣业绩">
              <template slot-scope="scope">
                <el-input
                  type="number"
                  size="mini"
                  min="0"
                  max="100"
                  v-model="scope.row.SavingCardPerformanceLargessRate"
                  v-input-fixed="2"
                  @input="changeSavingCardPerformanceLargessRate(scope.row)"
                >
                  <template slot="append">%</template>
                </el-input>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="130px">
              <template slot-scope="scope">
                <el-button v-if="scope.row.ParentID" type="primary" size="mini" @click="showSaleProduct(scope.row)"> 产品业绩</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
        <el-tab-pane label="销售-项目" name="sale-project">
          <el-table
            :data="employeePerformaceSchemeDetail.ProjectCategory"
            size="small"
            max-height="480px"
            row-key="CategoryID"
            :tree-props="{ children: 'Child' }"
            v-loading="saleProductCategoryLoading"
          >
            <el-table-column prop="CategoryName" label="项目分类"></el-table-column>
            <el-table-column prop="PayPerformanceRate" label="现金业绩">
              <template slot-scope="scope">
                <el-input
                  type="number"
                  size="mini"
                  min="0"
                  max="100"
                  v-model="scope.row.PayPerformanceRate"
                  v-input-fixed="2"
                  @input="changePayPerformanceRate(scope.row)"
                >
                  <template slot="append">%</template>
                </el-input>
              </template>
            </el-table-column>
            <el-table-column prop="SavingCardPerformanceRate" label="卡抵扣业绩">
              <template slot-scope="scope">
                <el-input
                  type="number"
                  size="mini"
                  min="0"
                  max="100"
                  v-model="scope.row.SavingCardPerformanceRate"
                  v-input-fixed="2"
                  @input="changeSavingCardPerformanceRate(scope.row)"
                >
                  <template slot="append">%</template>
                </el-input>
              </template>
            </el-table-column>
            <el-table-column prop="SavingCardPerformanceLargessRate" label="赠送卡抵扣业绩">
              <template slot-scope="scope">
                <el-input
                  type="number"
                  size="mini"
                  min="0"
                  max="100"
                  v-model="scope.row.SavingCardPerformanceLargessRate"
                  v-input-fixed="2"
                  @input="changeSavingCardPerformanceLargessRate(scope.row)"
                >
                  <template slot="append">%</template>
                </el-input>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="130px">
              <template slot-scope="scope">
                <el-button v-if="scope.row.ParentID" type="primary" size="mini" @click="showSaleProject(scope.row)"> 项目业绩</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
        <el-tab-pane label="销售-储值卡" name="slae-savingCard">
          <el-table
            :data="employeePerformaceSchemeDetail.SavingCardCategory"
            size="small"
            max-height="480px"
            row-key="CategoryID"
            :tree-props="{ children: 'Child' }"
          >
            <el-table-column prop="CategoryName" label="储值卡分类"></el-table-column>
            <el-table-column prop="PayPerformanceRate" label="现金业绩">
              <template slot-scope="scope">
                <el-input
                  type="number"
                  size="mini"
                  min="0"
                  max="100"
                  v-model="scope.row.PayPerformanceRate"
                  v-input-fixed="2"
                  @input="changePayPerformanceRate(scope.row)"
                >
                  <template slot="append">%</template>
                </el-input>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="130px">
              <template slot-scope="scope">
                <el-button type="primary" size="mini" @click="showSaleSavingCard(scope.row)"> 储值卡业绩</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
        <el-tab-pane label="销售-时效卡" name="sale-timeCard">
          <el-table
            :data="employeePerformaceSchemeDetail.TimeCardCategory"
            size="small"
            max-height="480px"
            row-key="CategoryID"
            :tree-props="{ children: 'Child' }"
          >
            <el-table-column prop="CategoryName" label="时效卡分类"></el-table-column>
            <el-table-column prop="PayPerformanceRate" label="现金业绩">
              <template slot-scope="scope">
                <el-input
                  type="number"
                  size="mini"
                  min="0"
                  max="100"
                  v-model="scope.row.PayPerformanceRate"
                  v-input-fixed="2"
                  @input="changePayPerformanceRate(scope.row)"
                >
                  <template slot="append">%</template>
                </el-input>
              </template>
            </el-table-column>
            <el-table-column prop="SavingCardPerformanceRate" label="卡抵扣业绩">
              <template slot-scope="scope">
                <el-input
                  type="number"
                  size="mini"
                  min="0"
                  max="100"
                  v-model="scope.row.SavingCardPerformanceRate"
                  v-input-fixed="2"
                  @input="changeSavingCardPerformanceRate(scope.row)"
                >
                  <template slot="append">%</template>
                </el-input>
              </template>
            </el-table-column>
            <el-table-column prop="SavingCardPerformanceLargessRate" label="赠送卡抵扣业绩">
              <template slot-scope="scope">
                <el-input
                  type="number"
                  size="mini"
                  min="0"
                  max="100"
                  v-model="scope.row.SavingCardPerformanceLargessRate"
                  v-input-fixed="2"
                  @input="changeSavingCardPerformanceLargessRate(scope.row)"
                >
                  <template slot="append">%</template>
                </el-input>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="130px">
              <template slot-scope="scope">
                <el-button type="primary" size="mini" @click="showSaleTimeCard(scope.row)"> 时效卡业绩</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
        <el-tab-pane label="销售-通用次卡" name="sale-generalCard">
          <el-table
            :data="employeePerformaceSchemeDetail.GeneralCardCategory"
            size="small"
            max-height="480px"
            row-key="CategoryID"
            :tree-props="{ children: 'Child' }"
          >
            <el-table-column prop="CategoryName" label="通用次卡分类"></el-table-column>
            <el-table-column prop="PayPerformanceRate" label="现金业绩">
              <template slot-scope="scope">
                <el-input
                  type="number"
                  size="mini"
                  min="0"
                  max="100"
                  v-model="scope.row.PayPerformanceRate"
                  v-input-fixed="2"
                  @input="changePayPerformanceRate(scope.row)"
                >
                  <template slot="append">%</template>
                </el-input>
              </template>
            </el-table-column>
            <el-table-column prop="SavingCardPerformanceRate" label="卡抵扣业绩">
              <template slot-scope="scope">
                <el-input
                  type="number"
                  size="mini"
                  min="0"
                  max="100"
                  v-model="scope.row.SavingCardPerformanceRate"
                  v-input-fixed="2"
                  @input="changeSavingCardPerformanceRate(scope.row)"
                >
                  <template slot="append">%</template>
                </el-input>
              </template>
            </el-table-column>
            <el-table-column prop="SavingCardPerformanceLargessRate" label="赠送卡抵扣业绩">
              <template slot-scope="scope">
                <el-input
                  type="number"
                  size="mini"
                  min="0"
                  max="100"
                  v-model="scope.row.SavingCardPerformanceLargessRate"
                  v-input-fixed="2"
                  @input="changeSavingCardPerformanceLargessRate(scope.row)"
                >
                  <template slot="append">%</template>
                </el-input>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="130px">
              <template slot-scope="scope">
                <el-button type="primary" size="mini" @click="showSaleGeneralCard(scope.row)"> 通用次卡业绩</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
        <el-tab-pane label="消耗-产品" name="treat-product">
          <el-table
            :data="employeePerformaceSchemeDetail.TreatProductCategory"
            size="small"
            max-height="480px"
            row-key="CategoryID"
            :tree-props="{ children: 'Child', hasChildren: 'hasChildren' }"
          >
            <el-table-column prop="CategoryName" label="产品分类"></el-table-column>
            <el-table-column prop="PerformancePayRate" label="现金业绩">
              <template slot-scope="scope">
                <el-input
                  type="number"
                  size="mini"
                  min="0"
                  max="100"
                  v-model="scope.row.PerformancePayRate"
                  v-input-fixed="2"
                  @input="chengePerformancePayRate(scope.row)"
                >
                  <template slot="append">%</template>
                </el-input>
              </template>
            </el-table-column>
            <el-table-column prop="PerformanceCardRate" label="卡抵扣业绩">
              <template slot-scope="scope">
                <el-input
                  type="number"
                  size="mini"
                  min="0"
                  max="100"
                  v-model="scope.row.PerformanceCardRate"
                  v-input-fixed="2"
                  @input="changePerformanceCardRate(scope.row)"
                >
                  <template slot="append">%</template>
                </el-input>
              </template>
            </el-table-column>
            <el-table-column prop="PerformanceCardLargessRate" label="赠送卡抵扣业绩">
              <template slot-scope="scope">
                <el-input
                  type="number"
                  size="mini"
                  min="0"
                  max="100"
                  v-model="scope.row.PerformanceCardLargessRate"
                  v-input-fixed="2"
                  @input="changePerformanceCardLargessRate(scope.row)"
                >
                  <template slot="append">%</template>
                </el-input>
              </template>
            </el-table-column>
            <el-table-column prop="PerformanceLargessRate" label="赠送业绩">
              <template slot-scope="scope">
                <el-input
                  type="number"
                  size="mini"
                  min="0"
                  max="100"
                  v-model="scope.row.PerformanceLargessRate"
                  v-input-fixed="2"
                  @input="changePerformanceLargessRate(scope.row)"
                >
                  <template slot="append">%</template>
                </el-input>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="130px">
              <template slot-scope="scope">
                <el-button v-if="scope.row.ParentID" type="primary" size="mini" @click="showTreatProduct(scope.row)"> 产品业绩</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
        <el-tab-pane label="消耗-项目" name="treat-project">
          <el-table
            :data="employeePerformaceSchemeDetail.TreatProjectCategory"
            size="small"
            max-height="480px"
            row-key="CategoryID"
            :tree-props="{ children: 'Child', hasChildren: 'hasChildren' }"
          >
            <el-table-column prop="CategoryName" label="项目分类"></el-table-column>
            <el-table-column prop="PerformancePayRate" label="现金业绩">
              <template slot-scope="scope">
                <el-input
                  type="number"
                  size="mini"
                  min="0"
                  max="100"
                  v-model="scope.row.PerformancePayRate"
                  v-input-fixed="2"
                  @input="chengePerformancePayRate(scope.row)"
                >
                  <template slot="append">%</template>
                </el-input>
              </template>
            </el-table-column>
            <el-table-column prop="PerformanceCardRate" label="卡抵扣业绩">
              <template slot-scope="scope">
                <el-input
                  type="number"
                  size="mini"
                  min="0"
                  max="100"
                  v-model="scope.row.PerformanceCardRate"
                  v-input-fixed="2"
                  @input="changePerformanceCardRate(scope.row)"
                >
                  <template slot="append">%</template>
                </el-input>
              </template>
            </el-table-column>
            <el-table-column prop="PerformanceCardLargessRate" label="赠送卡抵扣业绩">
              <template slot-scope="scope">
                <el-input
                  type="number"
                  size="mini"
                  min="0"
                  max="100"
                  v-model="scope.row.PerformanceCardLargessRate"
                  v-input-fixed="2"
                  @input="changePerformanceCardLargessRate(scope.row)"
                >
                  <template slot="append">%</template>
                </el-input>
              </template>
            </el-table-column>
            <el-table-column prop="PerformanceLargessRate" label="赠送业绩">
              <template slot-scope="scope">
                <el-input
                  type="number"
                  size="mini"
                  min="0"
                  max="100"
                  v-model="scope.row.PerformanceLargessRate"
                  v-input-fixed="2"
                  @input="changePerformanceLargessRate(scope.row)"
                >
                  <template slot="append">%</template>
                </el-input>
              </template>
            </el-table-column>
            <el-table-column prop="e" label="操作" width="130px">
              <template slot-scope="scope">
                <el-button v-if="scope.row.ParentID" type="primary" size="mini" @click="showTreatProject(scope.row)"> 项目业绩</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
        <el-tab-pane label="消耗-储值卡" name="treat-savingCard">
          <el-table
            :data="employeePerformaceSchemeDetail.TreatSavingCardCategory"
            size="small"
            max-height="450px"
            row-key="CategoryID"
            :tree-props="{ children: 'Child' }"
          >
            <el-table-column prop="CategoryName" label="商品分类"></el-table-column>
            <el-table-column label="卡抵扣业绩">
              <template slot-scope="scope">
                <el-input
                  type="number"
                  size="mini"
                  v-input-fixed
                  v-model="scope.row.PerformanceCardRate"
                  class="input_type"
                  @input="changePerformanceCardRate(scope.row)"
                >
                  <template slot="append">%</template>
                </el-input>
              </template>
            </el-table-column>
            <el-table-column label="赠送卡抵扣业绩">
              <template slot-scope="scope">
                <el-input
                  type="number"
                  size="mini"
                  v-input-fixed
                  v-model="scope.row.PerformanceCardLargessRate"
                  class="input_type"
                  @input="changePerformanceCardLargessRate(scope.row)"
                >
                  <template slot="append">%</template>
                </el-input>
              </template>
            </el-table-column>

            <el-table-column label="操作" width="115px">
              <template slot-scope="scope">
                <el-button type="primary" size="mini" @click="showTreatSavingCard(scope.row)"> 储值卡业绩</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
        <el-tab-pane label="消耗-时效卡" name="treat-timeCard">
          <el-table
            :data="employeePerformaceSchemeDetail.TreatTimeCardCategory"
            size="small"
            max-height="450px"
            row-key="CategoryID"
            :tree-props="{ children: 'Child' }"
          >
            <el-table-column prop="CategoryName" label="商品分类"></el-table-column>

            <el-table-column label="现金业绩">
              <template slot-scope="scope">
                <el-input
                  type="number"
                  size="mini"
                  v-input-fixed
                  v-model="scope.row.PerformancePayRate"
                  class="input_type"
                  @input="chengePerformancePayRate(scope.row)"
                >
                  <template slot="append">%</template>
                </el-input>
              </template>
            </el-table-column>
            <el-table-column label="卡抵扣业绩">
              <template slot-scope="scope">
                <el-input
                  type="number"
                  size="mini"
                  v-input-fixed
                  v-model="scope.row.PerformanceCardRate"
                  class="input_type"
                  @input="changePerformanceCardRate(scope.row)"
                >
                  <template slot="append">%</template>
                </el-input>
              </template>
            </el-table-column>
            <el-table-column label="赠送卡抵扣业绩">
              <template slot-scope="scope">
                <el-input
                  type="number"
                  size="mini"
                  v-input-fixed="2"
                  v-model="scope.row.PerformanceCardLargessRate"
                  class="input_type"
                  @input="changePerformanceCardLargessRate(scope.row)"
                >
                  <template slot="append">%</template>
                </el-input>
              </template>
            </el-table-column>
            <el-table-column label="赠送业绩">
              <template slot-scope="scope">
                <el-input
                  type="number"
                  size="mini"
                  v-input-fixed="2"
                  v-model="scope.row.PerformanceLargessRate"
                  class="input_type"
                  @input="changePerformanceLargessRate(scope.row)"
                >
                  <template slot="append">%</template>
                </el-input>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="115px">
              <template slot-scope="scope">
                <el-button type="primary" size="mini" @click="showTreatTimeCard(scope.row)"> 时效卡业绩</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
        <el-tab-pane label="消耗-通用次卡" name="treat-generalCard">
          <el-table
            :data="employeePerformaceSchemeDetail.TreatGeneralCardCategory"
            size="small"
            max-height="450px"
            row-key="CategoryID"
            :tree-props="{ children: 'Child' }"
          >
            <el-table-column prop="CategoryName" label="商品分类"></el-table-column>

            <el-table-column label="现金业绩">
              <template slot-scope="scope">
                <el-input
                  type="number"
                  size="mini"
                  v-input-fixed="2"
                  v-model="scope.row.PerformancePayRate"
                  class="input_type"
                  @input="chengePerformancePayRate(scope.row)"
                >
                  <template slot="append">%</template>
                </el-input>
              </template>
            </el-table-column>
            <el-table-column label="卡抵扣业绩">
              <template slot-scope="scope">
                <el-input
                  type="number"
                  size="mini"
                  v-input-fixed="2"
                  v-model="scope.row.PerformanceCardRate"
                  class="input_type"
                  @input="changePerformanceCardRate(scope.row)"
                >
                  <template slot="append">%</template>
                </el-input>
              </template>
            </el-table-column>
            <el-table-column label="赠送卡抵扣业绩">
              <template slot-scope="scope">
                <el-input
                  type="number"
                  size="mini"
                  v-input-fixed="2"
                  v-model="scope.row.PerformanceCardLargessRate"
                  class="input_type"
                  @input="changePerformanceCardLargessRate(scope.row)"
                >
                  <template slot="append">%</template>
                </el-input>
              </template>
            </el-table-column>
            <el-table-column label="赠送业绩">
              <template slot-scope="scope">
                <el-input
                  type="number"
                  size="mini"
                  v-input-fixed="2"
                  v-model="scope.row.PerformanceLargessRate"
                  class="input_type"
                  @input="changePerformanceLargessRate(scope.row)"
                >
                  <template slot="append">%</template>
                </el-input>
              </template>
            </el-table-column>

            <el-table-column label="操作" width="125px">
              <template slot-scope="scope">
                <el-button type="primary" size="mini" @click="showTreatGeneralCard(scope.row)"> 通用次卡业绩</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
      </el-tabs>
      <span slot="footer" class="dialog-footer">
        <el-button @click="editdialogVisible = false" v-prevent-click size="small">取 消</el-button>
        <el-button type="primary" @click="saveEditEmployeePerformaceScheme" v-prevent-click size="small" :loading="confrimLoading">确 定</el-button>
      </span>
    </el-dialog>

    <!-- 产品业绩 -->
    <el-dialog :title="GoodsTitle" :visible.sync="editDialogVisibleSaleProduct" width="1000px" custom-class="editDialog">
      <el-table :data="saleProductDetail" size="small" max-height="480px" v-loading="goodsLoading">
        <el-table-column prop="Name" label="产品名称"></el-table-column>
        <el-table-column prop="PayPerformanceRate" label="现金业绩">
          <template slot-scope="scope">
            <el-input
              type="number"
              size="mini"
              min="0"
              max="100"
              v-model="scope.row.PayPerformanceRate"
              v-input-fixed="2"
              @input="changePayPerformanceRate(scope.row)"
            >
              <template slot="append">%</template>
            </el-input>
          </template>
        </el-table-column>
        <el-table-column prop="SavingCardPerformanceRate" label="卡抵扣业绩">
          <template slot-scope="scope">
            <el-input
              type="number"
              size="mini"
              min="0"
              max="100"
              v-model="scope.row.SavingCardPerformanceRate"
              v-input-fixed="2"
              @input="changeSavingCardPerformanceRate(scope.row)"
            >
              <template slot="append">%</template>
            </el-input>
          </template>
        </el-table-column>
        <el-table-column prop="SavingCardPerformanceLargessRate" label="赠送卡抵扣业绩">
          <template slot-scope="scope">
            <el-input
              type="number"
              size="mini"
              min="0"
              max="100"
              v-model="scope.row.SavingCardPerformanceLargessRate"
              v-input-fixed="2"
              @input="changeSavingCardPerformanceLargessRate(scope.row)"
            >
              <template slot="append">%</template>
            </el-input>
          </template>
        </el-table-column>
      </el-table>
      <span slot="footer" class="dialog-footer">
        <el-button @click="editDialogVisibleSaleProduct = false" v-prevent-click size="small">取 消</el-button>
        <el-button type="primary" @click="saveSaleProduct" v-prevent-click size="small">确 定</el-button>
      </span>
    </el-dialog>
    <!-- 项目业绩 -->
    <el-dialog :title="GoodsTitle" :visible.sync="editDialogVisibleSaleProject" width="1000px" custom-class="editDialog">
      <el-table :data="saleProjectDetail" size="small" max-height="480px" v-loading="goodsLoading">
        <el-table-column prop="Name" label="项目名称"></el-table-column>
        <el-table-column prop="PayPerformanceRate" label="现金业绩">
          <template slot-scope="scope">
            <el-input
              type="number"
              size="mini"
              min="0"
              max="100"
              v-model="scope.row.PayPerformanceRate"
              v-input-fixed="2"
              @input="changePayPerformanceRate(scope.row)"
            >
              <template slot="append">%</template>
            </el-input>
          </template>
        </el-table-column>
        <el-table-column prop="SavingCardPerformanceRate" label="卡抵扣业绩">
          <template slot-scope="scope">
            <el-input
              type="number"
              size="mini"
              min="0"
              max="100"
              v-model="scope.row.SavingCardPerformanceRate"
              v-input-fixed="2"
              @input="changeSavingCardPerformanceRate(scope.row)"
            >
              <template slot="append">%</template>
            </el-input>
          </template>
        </el-table-column>
        <el-table-column prop="SavingCardPerformanceLargessRate" label="赠送卡抵扣业绩">
          <template slot-scope="scope">
            <el-input
              type="number"
              size="mini"
              min="0"
              max="100"
              v-model="scope.row.SavingCardPerformanceLargessRate"
              v-input-fixed="2"
              @input="changeSavingCardPerformanceLargessRate(scope.row)"
            >
              <template slot="append">%</template>
            </el-input>
          </template>
        </el-table-column>
      </el-table>
      <span slot="footer" class="dialog-footer">
        <el-button @click="editDialogVisibleSaleProject = false" v-prevent-click size="small">取 消</el-button>
        <el-button type="primary" @click="saveSaleProject" v-prevent-click size="small">确 定</el-button>
      </span>
    </el-dialog>
    <!-- 储值卡业绩 -->
    <el-dialog :title="GoodsTitle" :visible.sync="editDialogVisibleSaleSavingCard" width="750px" custom-class="editDialog">
      <el-table :data="saleSavingCardDetail" size="small" max-height="480px" v-loading="goodsLoading">
        <el-table-column prop="Name" label="储值卡名称"></el-table-column>
        <el-table-column prop="PayPerformanceRate" label="现金业绩" width="250">
          <template slot-scope="scope">
            <el-input
              type="number"
              size="mini"
              min="0"
              max="100"
              v-model="scope.row.PayPerformanceRate"
              v-input-fixed="2"
              @input="changePayPerformanceRate(scope.row)"
            >
              <template slot="append">%</template>
            </el-input>
          </template>
        </el-table-column>
        <!-- <el-table-column prop="SavingCardPerformanceRate" label="卡抵扣业绩">
          <template slot-scope="scope">
            <el-input type="number" size="mini" min="0" max="100" v-model="scope.row.SavingCardPerformanceRate" v-input-fixed="2" @input="royaltyRateChangeTwo(scope.row)">
              <template slot="append">%</template>
            </el-input>
          </template>
        </el-table-column> -->
        <!-- <el-table-column prop="SavingCardPerformanceLargessRate" label="赠送卡抵扣业绩">
          <template slot-scope="scope">
            <el-input type="number" size="mini" min="0" max="100" v-model="scope.row.SavingCardPerformanceLargessRate" v-input-fixed="2" @input="royaltyRateChangeTwo(scope.row)">
              <template slot="append">%</template>
            </el-input>
          </template>
        </el-table-column> -->
      </el-table>
      <span slot="footer" class="dialog-footer">
        <el-button @click="editDialogVisibleSaleSavingCard = false" v-prevent-click size="small">取 消</el-button>
        <el-button type="primary" @click="saveSaleSavingCard" v-prevent-click size="small">确 定</el-button>
      </span>
    </el-dialog>
    <!-- 时效卡业绩 -->
    <el-dialog :title="GoodsTitle" :visible.sync="editDialogVisibleSaleTimeCard" width="1000px" custom-class="editDialog">
      <el-table :data="saleTimeCardDetail" size="small" max-height="480px" v-loading="goodsLoading">
        <el-table-column prop="Name" label="时效卡名称"></el-table-column>
        <el-table-column prop="PayPerformanceRate" label="现金业绩">
          <template slot-scope="scope">
            <el-input
              type="number"
              size="mini"
              min="0"
              max="100"
              v-model="scope.row.PayPerformanceRate"
              v-input-fixed="2"
              @input="changePayPerformanceRate(scope.row)"
            >
              <template slot="append">%</template>
            </el-input>
          </template>
        </el-table-column>
        <el-table-column prop="SavingCardPerformanceRate" label="卡抵扣业绩">
          <template slot-scope="scope">
            <el-input
              type="number"
              size="mini"
              min="0"
              max="100"
              v-model="scope.row.SavingCardPerformanceRate"
              v-input-fixed="2"
              @input="changeSavingCardPerformanceRate(scope.row)"
            >
              <template slot="append">%</template>
            </el-input>
          </template>
        </el-table-column>
        <el-table-column prop="SavingCardPerformanceLargessRate" label="赠送卡抵扣业绩">
          <template slot-scope="scope">
            <el-input
              type="number"
              size="mini"
              min="0"
              max="100"
              v-model="scope.row.SavingCardPerformanceLargessRate"
              v-input-fixed="2"
              @input="changeSavingCardPerformanceLargessRate(scope.row)"
            >
              <template slot="append">%</template>
            </el-input>
          </template>
        </el-table-column>
      </el-table>
      <span slot="footer" class="dialog-footer">
        <el-button @click="editDialogVisibleSaleTimeCard = false" v-prevent-click size="small">取 消</el-button>
        <el-button type="primary" @click="saveSaleTimeCard" v-prevent-click size="small">确 定</el-button>
      </span>
    </el-dialog>
    <!-- 通用次卡业绩 -->
    <el-dialog :title="GoodsTitle" :visible.sync="editDialogVisibleSaleGeneral" width="1000px" custom-class="editDialog">
      <el-table :data="saleGeneralCardDetail" size="small" max-height="480px" v-loading="goodsLoading">
        <el-table-column prop="Name" label="通用次卡名称"></el-table-column>
        <el-table-column prop="PayPerformanceRate" label="现金业绩">
          <template slot-scope="scope">
            <el-input
              type="number"
              size="mini"
              min="0"
              max="100"
              v-model="scope.row.PayPerformanceRate"
              v-input-fixed="2"
              @input="changePayPerformanceRate(scope.row)"
            >
              <template slot="append">%</template>
            </el-input>
          </template>
        </el-table-column>
        <el-table-column prop="SavingCardPerformanceRate" label="卡抵扣业绩">
          <template slot-scope="scope">
            <el-input
              type="number"
              size="mini"
              min="0"
              max="100"
              v-model="scope.row.SavingCardPerformanceRate"
              v-input-fixed="2"
              @input="changeSavingCardPerformanceRate(scope.row)"
            >
              <template slot="append">%</template>
            </el-input>
          </template>
        </el-table-column>
        <el-table-column prop="SavingCardPerformanceLargessRate" label="赠送卡抵扣业绩">
          <template slot-scope="scope">
            <el-input
              type="number"
              size="mini"
              min="0"
              max="100"
              v-model="scope.row.SavingCardPerformanceLargessRate"
              v-input-fixed="2"
              @input="changeSavingCardPerformanceLargessRate(scope.row)"
            >
              <template slot="append">%</template>
            </el-input>
          </template>
        </el-table-column>
      </el-table>
      <span slot="footer" class="dialog-footer">
        <el-button @click="editDialogVisibleSaleGeneral = false" v-prevent-click size="small">取 消</el-button>
        <el-button type="primary" @click="saveSaleGeneralCard" v-prevent-click size="small">确 定</el-button>
      </span>
    </el-dialog>
    <!-- 消耗--产品业绩 -->
    <el-dialog :title="GoodsTitle" :visible.sync="editDialogVisibleTreatProduct" width="1000px" custom-class="editDialog">
      <el-table :data="treatProductDetail" size="small" max-height="480px" v-loading="goodsLoading">
        <el-table-column prop="Name" label="产品名称"></el-table-column>
        <el-table-column prop="PerformancePayRate" label="现金业绩">
          <template slot-scope="scope">
            <el-input
              type="number"
              size="mini"
              min="0"
              max="100"
              v-model="scope.row.PerformancePayRate"
              v-input-fixed="2"
              @input="chengePerformancePayRate(scope.row)"
            >
              <template slot="append">%</template>
            </el-input>
          </template>
        </el-table-column>
        <el-table-column prop="PerformanceCardRate" label="卡抵扣业绩">
          <template slot-scope="scope">
            <el-input
              type="number"
              size="mini"
              min="0"
              max="100"
              v-model="scope.row.PerformanceCardRate"
              v-input-fixed="2"
              @input="changePerformanceCardRate(scope.row)"
            >
              <template slot="append">%</template>
            </el-input>
          </template>
        </el-table-column>
        <el-table-column prop="PerformanceCardLargessRate" label="赠送卡抵扣业绩">
          <template slot-scope="scope">
            <el-input
              type="number"
              size="mini"
              min="0"
              max="100"
              v-model="scope.row.PerformanceCardLargessRate"
              v-input-fixed="2"
              @input="changePerformanceCardLargessRate(scope.row)"
            >
              <template slot="append">%</template>
            </el-input>
          </template>
        </el-table-column>
        <el-table-column prop="PerformanceLargessRate" label="赠送业绩">
          <template slot-scope="scope">
            <el-input
              type="number"
              size="mini"
              min="0"
              max="100"
              v-model="scope.row.PerformanceLargessRate"
              v-input-fixed="2"
              @input="changePerformanceLargessRate(scope.row)"
            >
              <template slot="append">%</template>
            </el-input>
          </template>
        </el-table-column>
      </el-table>
      <span slot="footer" class="dialog-footer">
        <el-button @click="editDialogVisibleTreatProduct = false" v-prevent-click size="small">取 消</el-button>
        <el-button type="primary" @click="saveTreatProduct" v-prevent-click size="small">确 定</el-button>
      </span>
    </el-dialog>
    <!-- 消耗--项目业绩 -->
    <el-dialog :title="GoodsTitle" :visible.sync="editDialogVisibleTreatProject" width="1000px" custom-class="editDialog">
      <el-table :data="treatProjectDetail" size="small" max-height="480px" v-loading="goodsLoading">
        <el-table-column prop="Name" label="项目名称"></el-table-column>
        <el-table-column prop="PerformancePayRate" label="现金业绩">
          <template slot-scope="scope">
            <el-input
              type="number"
              size="mini"
              min="0"
              max="100"
              v-model="scope.row.PerformancePayRate"
              v-input-fixed="2"
              @input="chengePerformancePayRate(scope.row)"
            >
              <template slot="append">%</template>
            </el-input>
          </template>
        </el-table-column>
        <el-table-column prop="PerformanceCardRate" label="卡抵扣业绩">
          <template slot-scope="scope">
            <el-input
              type="number"
              size="mini"
              min="0"
              max="100"
              v-model="scope.row.PerformanceCardRate"
              v-input-fixed="2"
              @input="changePerformanceCardRate(scope.row)"
            >
              <template slot="append">%</template>
            </el-input>
          </template>
        </el-table-column>
        <el-table-column prop="PerformanceCardLargessRate" label="赠送卡抵扣业绩">
          <template slot-scope="scope">
            <el-input
              type="number"
              size="mini"
              min="0"
              max="100"
              v-model="scope.row.PerformanceCardLargessRate"
              v-input-fixed="2"
              @input="changePerformanceCardLargessRate(scope.row)"
            >
              <template slot="append">%</template>
            </el-input>
          </template>
        </el-table-column>
        <el-table-column prop="PerformanceLargessRate" label="赠送业绩">
          <template slot-scope="scope">
            <el-input
              type="number"
              size="mini"
              min="0"
              max="100"
              v-model="scope.row.PerformanceLargessRate"
              v-input-fixed="2"
              @input="changePerformanceLargessRate(scope.row)"
            >
              <template slot="append">%</template>
            </el-input>
          </template>
        </el-table-column>
      </el-table>
      <span slot="footer" class="dialog-footer">
        <el-button @click="editDialogVisibleTreatProject = false" v-prevent-click size="small">取 消</el-button>
        <el-button type="primary" @click="saveTreatProject" v-prevent-click size="small">确 定</el-button>
      </span>
    </el-dialog>

    <!-- 消耗--储值卡 业绩 -->
    <el-dialog :title="GoodsTitle" :visible.sync="editDialogVisibleTreatSavingCard" width="1000px" custom-class="editDialog">
      <el-table :data="treatSavingCardDetail" size="small" max-height="480px" v-loading="goodsLoading">
        <el-table-column prop="Name" label="项目名称"></el-table-column>
        <el-table-column prop="PerformanceCardRate" label="卡抵扣业绩">
          <template slot-scope="scope">
            <el-input
              type="number"
              size="mini"
              min="0"
              max="100"
              v-model="scope.row.PerformanceCardRate"
              v-input-fixed="2"
              @input="changePerformanceCardRate(scope.row)"
            >
              <template slot="append">%</template>
            </el-input>
          </template>
        </el-table-column>
        <el-table-column prop="PerformanceCardLargessRate" label="赠送卡抵扣业绩">
          <template slot-scope="scope">
            <el-input
              type="number"
              size="mini"
              min="0"
              max="100"
              v-model="scope.row.PerformanceCardLargessRate"
              v-input-fixed="2"
              @input="changePerformanceCardLargessRate(scope.row)"
            >
              <template slot="append">%</template>
            </el-input>
          </template>
        </el-table-column>
      </el-table>
      <span slot="footer" class="dialog-footer">
        <el-button @click="editDialogVisibleTreatSavingCard = false" v-prevent-click size="small">取 消</el-button>
        <el-button type="primary" @click="saveTreatSavingCard" v-prevent-click size="small">确 定</el-button>
      </span>
    </el-dialog>

    <!-- 消耗--时效卡业绩 -->
    <el-dialog :title="GoodsTitle" :visible.sync="editDialogVisibleTreatTimeCard" width="1000px" custom-class="editDialog">
      <el-table :data="treatTimeCardDetail" size="small" max-height="480px" v-loading="goodsLoading">
        <el-table-column prop="Name" label="项目名称"></el-table-column>
        <el-table-column prop="PerformancePayRate" label="现金业绩">
          <template slot-scope="scope">
            <el-input
              type="number"
              size="mini"
              min="0"
              max="100"
              v-model="scope.row.PerformancePayRate"
              v-input-fixed="2"
              @input="chengePerformancePayRate(scope.row)"
            >
              <template slot="append">%</template>
            </el-input>
          </template>
        </el-table-column>
        <el-table-column prop="PerformanceCardRate" label="卡抵扣业绩">
          <template slot-scope="scope">
            <el-input
              type="number"
              size="mini"
              min="0"
              max="100"
              v-model="scope.row.PerformanceCardRate"
              v-input-fixed="2"
              @input="changePerformanceCardRate(scope.row)"
            >
              <template slot="append">%</template>
            </el-input>
          </template>
        </el-table-column>
        <el-table-column prop="PerformanceCardLargessRate" label="赠送卡抵扣业绩">
          <template slot-scope="scope">
            <el-input
              type="number"
              size="mini"
              min="0"
              max="100"
              v-model="scope.row.PerformanceCardLargessRate"
              v-input-fixed="2"
              @input="changePerformanceCardLargessRate(scope.row)"
            >
              <template slot="append">%</template>
            </el-input>
          </template>
        </el-table-column>
        <el-table-column prop="PerformanceLargessRate" label="赠送业绩">
          <template slot-scope="scope">
            <el-input
              type="number"
              size="mini"
              min="0"
              max="100"
              v-model="scope.row.PerformanceLargessRate"
              v-input-fixed="2"
              @input="changePerformanceLargessRate(scope.row)"
            >
              <template slot="append">%</template>
            </el-input>
          </template>
        </el-table-column>
      </el-table>
      <span slot="footer" class="dialog-footer">
        <el-button @click="editDialogVisibleTreatTimeCard = false" v-prevent-click size="small">取 消</el-button>
        <el-button type="primary" @click="saveTreatTimeCard" v-prevent-click size="small">确 定</el-button>
      </span>
    </el-dialog>

    <!-- 消耗--通用次卡业绩 -->
    <el-dialog :title="GoodsTitle" :visible.sync="editDialogVisibleTreatGeneralCard" width="1000px" custom-class="editDialog">
      <el-table :data="treatGeneralCardDetail" size="small" max-height="480px" v-loading="goodsLoading">
        <el-table-column prop="Name" label="项目名称"></el-table-column>
        <el-table-column prop="PerformancePayRate" label="现金业绩">
          <template slot-scope="scope">
            <el-input
              type="number"
              size="mini"
              min="0"
              max="100"
              v-model="scope.row.PerformancePayRate"
              v-input-fixed="2"
              @input="chengePerformancePayRate(scope.row)"
            >
              <template slot="append">%</template>
            </el-input>
          </template>
        </el-table-column>
        <el-table-column prop="PerformanceCardRate" label="卡抵扣业绩">
          <template slot-scope="scope">
            <el-input
              type="number"
              size="mini"
              min="0"
              max="100"
              v-model="scope.row.PerformanceCardRate"
              v-input-fixed="2"
              @input="changePerformanceCardRate(scope.row)"
            >
              <template slot="append">%</template>
            </el-input>
          </template>
        </el-table-column>
        <el-table-column prop="PerformanceCardLargessRate" label="赠送卡抵扣业绩">
          <template slot-scope="scope">
            <el-input
              type="number"
              size="mini"
              min="0"
              max="100"
              v-model="scope.row.PerformanceCardLargessRate"
              v-input-fixed="2"
              @input="changePerformanceCardLargessRate(scope.row)"
            >
              <template slot="append">%</template>
            </el-input>
          </template>
        </el-table-column>
        <el-table-column prop="PerformanceLargessRate" label="赠送业绩">
          <template slot-scope="scope">
            <el-input
              type="number"
              size="mini"
              min="0"
              max="100"
              v-model="scope.row.PerformanceLargessRate"
              v-input-fixed="2"
              @input="changePerformanceLargessRate(scope.row)"
            >
              <template slot="append">%</template>
            </el-input>
          </template>
        </el-table-column>
      </el-table>
      <span slot="footer" class="dialog-footer">
        <el-button @click="editDialogVisibleTreatGeneralCard = false" v-prevent-click size="small">取 消</el-button>
        <el-button type="primary" @click="saveTreatGeneralCard" v-prevent-click size="small">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import API from "@/api/KHS/ChannelEmployeeSalary/performanceScheme.js";
export default {
  name: "ChannelEmployeeSalaryPerformanceScheme",
  props: {},
  /** 监听数据变化   */
  watch: {},
  /**  引入的组件  */
  components: {},
  /**  Vue 实例的数据对象**/
  data() {
    return {
      dialogVisible: false, // 新增弹框
      editdialogVisible: false, // 编辑弹框
      saleProductCategoryLoading: false,

      searchData: {
        Name: "",
        Active: true,
      },
      tableData: [], // 列表
      performanceSchemeAllList: [],
      paginations: {
        page: 1, // 当前位于哪页
        total: 0, // 总数
        page_size: 10, // 1页显示多少条
        layout: "total, prev, pager, next,jumper", // 翻页属性
      },
      employeePerformaceSchemeForm: {
        Name: "", // 新增市场业绩取值方案
      },
      employeePerformaceSchemeFormRule: {
        Name: [{ required: true, message: "请输入市场业绩业绩取值方案名称", trigger: "blur" }],
      },

      activeName: "sale-product", // tabs
      editEmployeePerformaceSchemeForm: {
        Name: "", // 新增市场业绩取值方案
        Active: true, // 有效性,
        IsCalculateChannelDeveloper: true,
        IsCalculateChannelConsultant: true,
      },
      employeePerformaceSchemeDetail: {},

      PerformanceSchemeID: "", // 业绩方案ID
      CategoryID: "", // 分类ID
      goodsLoading: false,
      confrimLoading: false,
      loading: false,

      editDialogVisibleSaleProduct: false, // 产品业绩弹框

      editDialogVisibleSaleProject: false, // 项目业绩弹框
      editDialogVisibleSaleSavingCard: false, // 储值卡业绩弹框
      editDialogVisibleSaleTimeCard: false, // 时效卡业绩弹框
      editDialogVisibleSaleGeneral: false, // 通用次卡业绩弹框

      editDialogVisibleTreatProduct: false, // 消耗产品业绩
      editDialogVisibleTreatProject: false, // 消耗项目业绩
      editDialogVisibleTreatSavingCard: false,
      editDialogVisibleTreatTimeCard: false,
      editDialogVisibleTreatGeneralCard: false,
      GoodsTitle: "", //产品业绩标题
      performModel: {
        performanceName: "", // 市场业绩取值方案
        isValidity: true, // 有效性
      },

      saleProductDetail: [], // 产品业绩
      saleProjectDetail: [], // 项目业绩
      saleSavingCardDetail: [], // 储值卡业绩
      saleTimeCardDetail: [], // 储值卡业绩
      saleGeneralCardDetail: [], // 通用次卡业绩

      treatProductDetail: [], // 消耗-项目业绩详情
      treatProjectDetail: [], // 消耗-产品业绩详情
      treatSavingCardDetail: [], // 消耗储值卡
      treatTimeCardDetail: [], // 消耗-时效卡
      treatGeneralCardDetail: [], // 消耗 通用次卡
    };
  },
  /**计算属性  */
  computed: {},
  /**  方法集合  */
  methods: {
    /**    */
    handleSearch() {
      let that = this;
      that.paginations.page = 1;
      that.channelPerformanceScheme_all();
    },
    /**    */
    handleCurrentChange(page) {
      let that = this;
      that.paginations.page = page;
      that.channelPerformanceScheme_all();
    },
    /**  新增  */
    addEmployeePerformaceSchemeClick() {
      let that = this;
      (that.employeePerformaceSchemeForm = {
        Name: "", // 新增市场业绩取值方案
      }),
        (this.dialogVisible = true);
    },
    /**   保存取值方案 */
    saveEmployeePerformaceSchemeClick() {
      let that = this;
      that.$refs.employeePerformaceSchemeFormRef.validate((valid) => {
        if (valid) {
          that.channelPerformanceScheme_create();
        }
      });
    },
    /**  编辑业绩  */
    editEmployeePerformaceSchemeClick(row) {
      let that = this;
      that.PerformanceSchemeID = row.ID;
      that.activeName = "sale-product"; // tabs
      that.editEmployeePerformaceSchemeForm = Object.assign(that.editEmployeePerformaceSchemeForm, row);
      that.channelPerformanceScheme_detail(row.ID);
      that.editdialogVisible = true;
    },
    /**   保存编辑 */
    saveEditEmployeePerformaceScheme() {
      let that = this;
      that.$refs.editEmployeePerformaceSchemeFormRef.validate((valid) => {
        if (valid) {
          that.channelEmployeePerformanceScheme_update();
        }
      });
    },
    /**  销售 ————————————————————————————————————————  */
    /**    */
    changePayPerformanceRate(row) {
      if (row.PayPerformanceRate !== "" && Number(row.PayPerformanceRate) > 100) {
        row.PayPerformanceRate = 100;
      }
    },
    /**    */
    changeSavingCardPerformanceLargessRate(row) {
      if (row.SavingCardPerformanceLargessRate !== "" && Number(row.SavingCardPerformanceLargessRate) > 100) {
        row.SavingCardPerformanceLargessRate = 100;
      }
    },
    /**    */
    changeSavingCardPerformanceRate(row) {
      if (row.SavingCardPerformanceRate !== "" && Number(row.SavingCardPerformanceRate) > 100) {
        row.SavingCardPerformanceRate = 100;
      }
    },

    /**  产品  */
    showSaleProduct(row) {
      let that = this;
      that.GoodsTitle = `产品销售业绩取值-${row.ParentName}-${row.CategoryName}`;
      that.CategoryID = row.CategoryID;
      that.editDialogVisibleSaleProduct = true;
      that.channelPerformanceSchemeProduct_all();
    },
    /**    */
    saveSaleProduct() {
      let that = this;
      that.channelPerformanceSchemeProduct_create();
    },
    /**  项目  */
    showSaleProject(row) {
      let that = this;
      that.GoodsTitle = `项目销售业绩取值-${row.ParentName}-${row.CategoryName}`;
      that.CategoryID = row.CategoryID;
      that.editDialogVisibleSaleProject = true;
      that.channelPerformanceSchemeProject_all();
    },
    /**    */
    saveSaleProject() {
      let that = this;
      that.channelPerformanceSchemeProject_create();
    },
    /**  储值卡  */
    showSaleSavingCard(row) {
      let that = this;
      that.GoodsTitle = `储值卡销售业绩取值-${row.CategoryName}`;
      that.CategoryID = row.CategoryID;
      that.editDialogVisibleSaleSavingCard = true;
      that.channelPerformanceSchemeSavingCard_all();
    },
    /**    */
    saveSaleSavingCard() {
      let that = this;
      that.channelPerformanceSchemeSavingCard_create();
    },
    /**  时效卡  */
    showSaleTimeCard(row) {
      let that = this;
      that.GoodsTitle = `时效卡销售业绩取值-${row.CategoryName}`;
      that.CategoryID = row.CategoryID;
      that.editDialogVisibleSaleTimeCard = true;
      that.channelPerformanceSchemeTimeCard_all();
    },
    /**    */
    saveSaleTimeCard() {
      let that = this;
      that.channelPerformanceSchemeTimeCard_create();
    },
    /**  通用次卡  */
    showSaleGeneralCard(row) {
      let that = this;
      that.GoodsTitle = `通用次卡销售业绩取值-${row.CategoryName}`;
      that.CategoryID = row.CategoryID;
      that.editDialogVisibleSaleGeneral = true;
      that.channelPerformanceSchemeGeneralCard_all();
    },
    /**    */
    saveSaleGeneralCard() {
      let that = this;
      that.channelPerformanceSchemeGeneralCard_create();
    },
    /**  消耗 ————————————————————————————————————————  */
    /**    */
    changePerformanceCardLargessRate(row) {
      if (row.PerformanceCardLargessRate !== "" && Number(row.PerformanceCardLargessRate) > 100) {
        row.PerformanceCardLargessRate = 100;
      }
    },
    /**    */
    changePerformanceCardRate(row) {
      if (row.PerformanceCardRate !== "" && Number(row.PerformanceCardRate) > 100) {
        row.PerformanceCardRate = 100;
      }
    },
    /**    */
    changePerformanceLargessRate(row) {
      if (row.PerformanceLargessRate !== "" && Number(row.PerformanceLargessRate) > 100) {
        row.PerformanceLargessRate = 100;
      }
    },
    /**    */
    chengePerformancePayRate(row) {
      if (row.PerformancePayRate !== "" && Number(row.PerformancePayRate) > 100) {
        row.PerformancePayRate = 100;
      }
    },

    /**  产品  */
    showTreatProduct(row) {
      let that = this;
      that.GoodsTitle = `产品消耗业绩取值-${row.ParentName}-${row.CategoryName}`;
      that.CategoryID = row.CategoryID;
      that.editDialogVisibleTreatProduct = true;
      that.channelPerformanceSchemeTreatProduct_all();
    },
    /**    */
    saveTreatProduct() {
      let that = this;
      that.channelPerformanceSchemeTreatProduct_create();
    },
    /**  项目  */
    showTreatProject(row) {
      let that = this;
      that.GoodsTitle = `项目消耗业绩取值-${row.ParentName}-${row.CategoryName}`;
      that.CategoryID = row.CategoryID;
      that.editDialogVisibleTreatProject = true;
      that.channelPerformanceSchemeTreatProject_all();
    },
    /**    */
    saveTreatProject() {
      let that = this;
      that.channelPerformanceSchemeTreatProject_create();
    },
    /**  储值卡  */
    showTreatSavingCard(row) {
      let that = this;
      that.GoodsTitle = `储值卡消耗业绩取值-${row.CategoryName}`;
      that.CategoryID = row.CategoryID;
      that.editDialogVisibleTreatSavingCard = true;
      that.channelEmployeePerformanceSchemeTreatSavingCard_all();
    },
    /**    */
    saveTreatSavingCard() {
      let that = this;
      that.channelEmployeePerformanceSchemeTreatSavingCard_create();
    },
    /**  时效卡  */
    showTreatTimeCard(row) {
      let that = this;
      that.GoodsTitle = `时效卡消耗业绩取值-${row.CategoryName}`;
      that.CategoryID = row.CategoryID;
      that.editDialogVisibleTreatTimeCard = true;
      that.channelPerformanceSchemeTreatTimeCard_all();
    },
    /**    */
    saveTreatTimeCard() {
      let that = this;
      that.channelPerformanceSchemeTreatTimeCard_create();
    },
    /**  通用次卡  */
    showTreatGeneralCard(row) {
      let that = this;
      that.GoodsTitle = `通用次卡消耗业绩取值-${row.CategoryName}`;
      that.CategoryID = row.CategoryID;
      that.editDialogVisibleTreatGeneralCard = true;
      that.channelPerformanceSchemeTreatGeneralCard_all();
    },
    /**    */
    saveTreatGeneralCard() {
      let that = this;
      that.channelPerformanceSchemeTreatGeneralCard_create();
    },

    /**•••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••  */
    /** 业绩取值方案查询   */
    async channelPerformanceScheme_all() {
      let that = this;
      try {
        let params = {
          PageNum: that.paginations.page,
          Name: that.searchData.Name, //模糊搜索
          Active: that.searchData.Active, //有效性
        };
        let res = await API.channelPerformanceScheme_all(params);
        if (res.StateCode == 200) {
          that.tableData = res.List;
          that.paginations.total = res.Total;
        } else {
          that.$message.error(res.Message);
        }
      } catch (error) {
        that.$message.error(error);
      }
    },
    /**  业绩取值方案查询-不加分页  */
    async channelPerformanceScheme_valid() {
      let that = this;
      try {
        let params = {};
        let res = await API.channelPerformanceScheme_valid(params);
        if (res.StateCode == 200) {
          that.performanceSchemeAllList = res.Data;
        } else {
          that.$message.error(res.Message);
        }
      } catch (error) {
        that.$message.error(error);
      }
    },
    /**  业绩取值方案添加  */
    async channelPerformanceScheme_create() {
      let that = this;
      try {
        let params = {
          Name: that.employeePerformaceSchemeForm.Name, //名称
        };
        let res = await API.channelPerformanceScheme_create(params);
        if (res.StateCode == 200) {
          that.$message.success("保存成功");
          that.dialogVisible = false;
          that.channelPerformanceScheme_all();
          that.channelPerformanceScheme_valid();
        } else {
          that.$message.error(res.Message);
        }
      } catch (error) {
        that.$message.error(error);
      }
    },
    /** 业绩取值方案修改  */
    async channelEmployeePerformanceScheme_update() {
      let that = this;
      try {
        let params = {
          ID: that.editEmployeePerformaceSchemeForm.ID, //业绩方案ID
          Name: that.editEmployeePerformaceSchemeForm.Name, //名称
          Active: that.editEmployeePerformaceSchemeForm.Active, //有效性
          IsCalculateChannelDeveloper: that.editEmployeePerformaceSchemeForm.IsCalculateChannelDeveloper, //是否包含开发人员
          IsCalculateChannelConsultant: that.editEmployeePerformaceSchemeForm.IsCalculateChannelConsultant, //是否包含市场咨询
          ProductCategory: that.employeePerformaceSchemeDetail.ProductCategory, //销售产品
          ProjectCategory: that.employeePerformaceSchemeDetail.ProjectCategory, //销售项目
          GeneralCardCategory: that.employeePerformaceSchemeDetail.GeneralCardCategory, //销售通用次卡
          TimeCardCategory: that.employeePerformaceSchemeDetail.TimeCardCategory, //销售时效卡
          SavingCardCategory: that.employeePerformaceSchemeDetail.SavingCardCategory, //销售储值卡
          TreatProductCategory: that.employeePerformaceSchemeDetail.TreatProductCategory, //消耗产品
          TreatProjectCategory: that.employeePerformaceSchemeDetail.TreatProjectCategory, //消耗项目
          TreatGeneralCardCategory: that.employeePerformaceSchemeDetail.TreatGeneralCardCategory, //消耗项目
          TreatTimeCardCategory: that.employeePerformaceSchemeDetail.TreatTimeCardCategory, //消耗项目
          TreatSavingCardCategory: that.employeePerformaceSchemeDetail.TreatSavingCardCategory, //消耗项目
        };
        let res = await API.channelEmployeePerformanceScheme_update(params);
        if (res.StateCode == 200) {
          that.$message.success("保存成功");
          that.channelPerformanceScheme_all();
          that.channelPerformanceScheme_valid();
          that.editdialogVisible = false;
        } else {
          that.$message.error(res.Message);
        }
      } catch (error) {
        that.$message.error(error);
      }
    },
    /**  业绩取值方案详  */
    async channelPerformanceScheme_detail(ID) {
      let that = this;
      that.saleProductCategoryLoading = true;
      try {
        let params = {
          ID: ID, //业绩方案ID
        };
        let res = await API.channelPerformanceScheme_detail(params);
        if (res.StateCode == 200) {
          that.employeePerformaceSchemeDetail = res.Data;
        } else {
          that.$message.error(res.Message);
        }
        that.saleProductCategoryLoading = false;
      } catch (error) {
        that.saleProductCategoryLoading = false;
        that.$message.error(error);
      }
    },
    /**  业绩取值方案产品详情  */
    async channelPerformanceSchemeProduct_all() {
      let that = this;
      that.goodsLoading = true;
      try {
        let params = {
          PerformanceSchemeID: that.PerformanceSchemeID,
          CategoryID: that.CategoryID,
        };
        let res = await API.channelPerformanceSchemeProduct_all(params);
        if (res.StateCode == 200) {
          that.saleProductDetail = res.Data;
        } else {
          that.$message.error(res.Message);
        }
        that.goodsLoading = false;
      } catch (error) {
        that.goodsLoading = false;
        that.$message.error(error);
      }
    },
    /**   业绩取值方案产品保存 */
    async channelPerformanceSchemeProduct_create() {
      let that = this;
      try {
        let params = {
          PerformanceSchemeID: that.PerformanceSchemeID, //业绩方案ID
          CategoryID: that.CategoryID, //分类ID
          Good: that.saleProductDetail
            .filter((i) => i.PayPerformanceRate || i.SavingCardPerformanceRate || i.SavingCardPerformanceLargessRate)
            .map((i) => {
              return {
                GoodID: i.ID, //商品ID
                PayPerformanceRate: i.PayPerformanceRate,
                SavingCardPerformanceRate: i.SavingCardPerformanceRate,
                SavingCardPerformanceLargessRate: i.SavingCardPerformanceLargessRate,
              };
            }),
        };
        let res = await API.channelPerformanceSchemeProduct_create(params);
        if (res.StateCode == 200) {
          that.editDialogVisibleSaleProduct = false;
          that.$message.success("操作成功");
        } else {
          that.$message.error(res.Message);
        }
      } catch (error) {
        that.$message.error(error);
      }
    },
    /**  业绩取值方案项目详情  */
    async channelPerformanceSchemeProject_all() {
      let that = this;
      that.goodsLoading = true;
      try {
        let params = {
          PerformanceSchemeID: that.PerformanceSchemeID,
          CategoryID: that.CategoryID,
        };
        let res = await API.channelPerformanceSchemeProject_all(params);
        if (res.StateCode == 200) {
          that.saleProjectDetail = res.Data;
        } else {
          that.$message.error(res.Message);
        }
        that.goodsLoading = false;
      } catch (error) {
        that.goodsLoading = false;
        that.$message.error(error);
      }
    },
    /**  业绩取值方案项目保存  */
    async channelPerformanceSchemeProject_create() {
      let that = this;
      try {
        let params = {
          PerformanceSchemeID: that.PerformanceSchemeID, //业绩方案ID
          CategoryID: that.CategoryID, //分类ID
          Good: that.saleProjectDetail
            .filter((i) => i.PayPerformanceRate || i.SavingCardPerformanceRate || i.SavingCardPerformanceLargessRate)
            .map((i) => {
              return {
                GoodID: i.ID, //商品ID
                PayPerformanceRate: i.PayPerformanceRate,
                SavingCardPerformanceRate: i.SavingCardPerformanceRate,
                SavingCardPerformanceLargessRate: i.SavingCardPerformanceLargessRate,
              };
            }),
        };
        let res = await API.channelPerformanceSchemeProject_create(params);
        if (res.StateCode == 200) {
          that.editDialogVisibleSaleProject = false;
          that.$message.success("操作成功");
        } else {
          that.$message.error(res.Message);
        }
      } catch (error) {
        that.$message.error(error);
      }
    },
    /**  业绩取值方案通用次卡详情  */
    async channelPerformanceSchemeGeneralCard_all() {
      let that = this;
      that.goodsLoading = true;
      try {
        let params = {
          PerformanceSchemeID: that.PerformanceSchemeID,
          CategoryID: that.CategoryID,
        };
        let res = await API.channelPerformanceSchemeGeneralCard_all(params);
        if (res.StateCode == 200) {
          that.saleGeneralCardDetail = res.Data;
        } else {
          that.$message.error(res.Message);
        }
        that.goodsLoading = false;
      } catch (error) {
        that.goodsLoading = false;
        that.$message.error(error);
      }
    },
    /**  业绩取值方案通用次卡保存  */
    async channelPerformanceSchemeGeneralCard_create() {
      let that = this;
      try {
        let params = {
          PerformanceSchemeID: that.PerformanceSchemeID, //业绩方案ID
          CategoryID: that.CategoryID, //分类ID
          Good: that.saleGeneralCardDetail
            .filter((i) => i.PayPerformanceRate || i.SavingCardPerformanceRate || i.SavingCardPerformanceLargessRate)
            .map((i) => {
              return {
                GoodID: i.ID, //商品ID
                PayPerformanceRate: i.PayPerformanceRate,
                SavingCardPerformanceRate: i.SavingCardPerformanceRate,
                SavingCardPerformanceLargessRate: i.SavingCardPerformanceLargessRate,
              };
            }),
        };
        let res = await API.channelPerformanceSchemeGeneralCard_create(params);
        if (res.StateCode == 200) {
          that.editDialogVisibleSaleGeneral = false;
          that.$message.success("操作成功");
        } else {
          that.$message.error(res.Message);
        }
      } catch (error) {
        that.$message.error(error);
      }
    },
    /**  业绩取值方案时效卡详情  */
    async channelPerformanceSchemeTimeCard_all() {
      let that = this;
      that.goodsLoading = true;
      try {
        let params = {
          PerformanceSchemeID: that.PerformanceSchemeID,
          CategoryID: that.CategoryID,
        };
        let res = await API.channelPerformanceSchemeTimeCard_all(params);
        if (res.StateCode == 200) {
          that.saleTimeCardDetail = res.Data;
        } else {
          that.$message.error(res.Message);
        }
        that.goodsLoading = false;
      } catch (error) {
        that.goodsLoading = false;
        that.$message.error(error);
      }
    },
    /**  业绩取值方案时效卡保存  */
    async channelPerformanceSchemeTimeCard_create() {
      let that = this;
      try {
        let params = {
          PerformanceSchemeID: that.PerformanceSchemeID, //业绩方案ID
          CategoryID: that.CategoryID, //分类ID
          Good: that.saleTimeCardDetail
            .filter((i) => i.PayPerformanceRate || i.SavingCardPerformanceRate || i.SavingCardPerformanceLargessRate)
            .map((i) => {
              return {
                GoodID: i.ID, //商品ID
                PayPerformanceRate: i.PayPerformanceRate,
                SavingCardPerformanceRate: i.SavingCardPerformanceRate,
                SavingCardPerformanceLargessRate: i.SavingCardPerformanceLargessRate,
              };
            }),
        };
        let res = await API.channelPerformanceSchemeTimeCard_create(params);
        if (res.StateCode == 200) {
          that.editDialogVisibleSaleTimeCard = false;
          that.$message.success("操作成功");
        } else {
          that.$message.error(res.Message);
        }
      } catch (error) {
        that.$message.error(error);
      }
    },
    /**  业绩取值方案储值卡详情  */
    async channelPerformanceSchemeSavingCard_all() {
      let that = this;
      that.goodsLoading = true;
      try {
        let params = {
          PerformanceSchemeID: that.PerformanceSchemeID,
          CategoryID: that.CategoryID,
        };
        let res = await API.channelPerformanceSchemeSavingCard_all(params);
        if (res.StateCode == 200) {
          that.saleSavingCardDetail = res.Data;
        } else {
          that.$message.error(res.Message);
        }
        that.goodsLoading = false;
      } catch (error) {
        that.goodsLoading = false;
        that.$message.error(error);
      }
    },
    /**  业绩取值方案储值卡保存  */
    async channelPerformanceSchemeSavingCard_create() {
      let that = this;
      try {
        let params = {
          PerformanceSchemeID: that.PerformanceSchemeID, //业绩方案ID
          CategoryID: that.CategoryID, //分类ID
          Good: that.saleSavingCardDetail
            .filter((i) => i.PayPerformanceRate)
            .map((i) => {
              return {
                GoodID: i.ID, //商品ID
                PayPerformanceRate: i.PayPerformanceRate,
              };
            }), //商品集合
        };
        let res = await API.channelPerformanceSchemeSavingCard_create(params);
        if (res.StateCode == 200) {
          that.editDialogVisibleSaleSavingCard = false;
          that.$message.success("操作成功");
        } else {
          that.$message.error(res.Message);
        }
      } catch (error) {
        that.$message.error(error);
      }
    },
    /**  业绩取值方案消耗产品详情  */
    async channelPerformanceSchemeTreatProduct_all() {
      let that = this;
      that.goodsLoading = true;
      try {
        let params = {
          PerformanceSchemeID: that.PerformanceSchemeID,
          CategoryID: that.CategoryID,
        };
        let res = await API.channelPerformanceSchemeTreatProduct_all(params);
        if (res.StateCode == 200) {
          that.treatProductDetail = res.Data;
        } else {
          that.$message.error(res.Message);
        }
        that.goodsLoading = false;
      } catch (error) {
        that.goodsLoading = false;
        that.$message.error(error);
      }
    },
    /**  业绩取值方案消耗产品保存  */
    async channelPerformanceSchemeTreatProduct_create() {
      let that = this;
      try {
        let params = {
          PerformanceSchemeID: that.PerformanceSchemeID, //业绩方案ID
          CategoryID: that.CategoryID, //分类ID
          Good: that.treatProductDetail
            .filter((i) => i.PerformancePayRate || i.PerformanceCardRate || i.PerformanceCardLargessRate || i.PerformanceLargessRate)
            .map((i) => {
              return {
                GoodID: i.ID, //商品ID
                PerformancePayRate: i.PerformancePayRate,
                PerformanceCardRate: i.PerformanceCardRate,
                PerformanceCardLargessRate: i.PerformanceCardLargessRate,
                PerformanceLargessRate: i.PerformanceLargessRate, //赠送业绩
              };
            }),
        };
        let res = await API.channelPerformanceSchemeTreatProduct_create(params);
        if (res.StateCode == 200) {
          that.editDialogVisibleTreatProduct = false;
          that.$message.success("操作成功");
        } else {
          that.$message.error(res.Message);
        }
      } catch (error) {
        that.$message.error(error);
      }
    },
    /**  业绩取值方案消耗项目详情  */
    async channelPerformanceSchemeTreatProject_all() {
      let that = this;
      that.goodsLoading = true;
      try {
        let params = {
          PerformanceSchemeID: that.PerformanceSchemeID,
          CategoryID: that.CategoryID,
        };
        let res = await API.channelPerformanceSchemeTreatProject_all(params);
        if (res.StateCode == 200) {
          that.treatProjectDetail = res.Data;
        } else {
          that.$message.error(res.Message);
        }
        that.goodsLoading = false;
      } catch (error) {
        that.goodsLoading = false;
        that.$message.error(error);
      }
    },
    /** 业绩取值方案消耗项目保存   */
    async channelPerformanceSchemeTreatProject_create() {
      let that = this;
      try {
        let params = {
          PerformanceSchemeID: that.PerformanceSchemeID, //业绩方案ID
          CategoryID: that.CategoryID, //分类ID
          Good: that.treatProjectDetail
            .filter((i) => i.PerformancePayRate || i.PerformanceCardRate || i.PerformanceCardLargessRate || i.PerformanceLargessRate)
            .map((i) => {
              return {
                GoodID: i.ID, //商品ID
                PerformancePayRate: i.PerformancePayRate,
                PerformanceCardRate: i.PerformanceCardRate,
                PerformanceCardLargessRate: i.PerformanceCardLargessRate,
                PerformanceLargessRate: i.PerformanceLargessRate, //赠送业绩
              };
            }),
        };
        let res = await API.channelPerformanceSchemeTreatProject_create(params);
        if (res.StateCode == 200) {
          that.editDialogVisibleTreatProject = false;
          that.$message.success("操作成功");
        } else {
          that.$message.error(res.Message);
        }
      } catch (error) {
        that.$message.error(error);
      }
    },
    /** 业绩取值方案消耗通用此卡详情   */
    async channelPerformanceSchemeTreatGeneralCard_all() {
      let that = this;
      that.goodsLoading = true;
      try {
        let params = {
          PerformanceSchemeID: that.PerformanceSchemeID,
          CategoryID: that.CategoryID,
        };
        let res = await API.channelPerformanceSchemeTreatGeneralCard_all(params);
        if (res.StateCode == 200) {
          that.treatGeneralCardDetail = res.Data;
        } else {
          that.$message.error(res.Message);
        }
        that.goodsLoading = false;
      } catch (error) {
        that.goodsLoading = false;
        that.$message.error(error);
      }
    },
    /**  业绩取值方案消耗通用此卡保存  */
    async channelPerformanceSchemeTreatGeneralCard_create() {
      let that = this;
      try {
        let params = {
          PerformanceSchemeID: that.PerformanceSchemeID, //业绩方案ID
          CategoryID: that.CategoryID, //分类ID
          Good: that.treatGeneralCardDetail
            .filter((i) => i.PerformancePayRate || i.PerformanceCardRate || i.PerformanceCardLargessRate || i.PerformanceLargessRate)
            .map((i) => {
              return {
                GoodID: i.ID, //商品ID
                PerformancePayRate: i.PerformancePayRate,
                PerformanceCardRate: i.PerformanceCardRate,
                PerformanceCardLargessRate: i.PerformanceCardLargessRate,
                PerformanceLargessRate: i.PerformanceLargessRate, //赠送业绩
              };
            }),
        };
        let res = await API.channelPerformanceSchemeTreatGeneralCard_create(params);
        if (res.StateCode == 200) {
          that.editDialogVisibleTreatGeneralCard = false;
          that.$message.success("操作成功");
        } else {
          that.$message.error(res.Message);
        }
      } catch (error) {
        that.$message.error(error);
      }
    },
    /**  业绩取值方案消耗时效卡详情  */
    async channelPerformanceSchemeTreatTimeCard_all() {
      let that = this;
      that.goodsLoading = true;
      try {
        let params = {
          PerformanceSchemeID: that.PerformanceSchemeID,
          CategoryID: that.CategoryID,
        };
        let res = await API.channelPerformanceSchemeTreatTimeCard_all(params);
        if (res.StateCode == 200) {
          that.treatTimeCardDetail = res.Data;
        } else {
          that.$message.error(res.Message);
        }
        that.goodsLoading = false;
      } catch (error) {
        that.goodsLoading = false;
        that.$message.error(error);
      }
    },
    /**  业绩取值方案消耗时效卡保存  */
    async channelPerformanceSchemeTreatTimeCard_create() {
      let that = this;
      try {
        let params = {
          PerformanceSchemeID: that.PerformanceSchemeID, //业绩方案ID
          CategoryID: that.CategoryID, //分类ID
          Good: that.treatTimeCardDetail
            .filter((i) => i.PerformancePayRate || i.PerformanceCardRate || i.PerformanceCardLargessRate || i.PerformanceLargessRate)
            .map((i) => {
              return {
                GoodID: i.ID, //商品ID
                PerformancePayRate: i.PerformancePayRate,
                PerformanceCardRate: i.PerformanceCardRate,
                PerformanceCardLargessRate: i.PerformanceCardLargessRate,
                PerformanceLargessRate: i.PerformanceLargessRate, //赠送业绩
              };
            }),
        };
        let res = await API.channelPerformanceSchemeTreatTimeCard_create(params);
        if (res.StateCode == 200) {
          that.editDialogVisibleTreatTimeCard = false;
          that.$message.success("操作成功");
        } else {
          that.$message.error(res.Message);
        }
      } catch (error) {
        that.$message.error(error);
      }
    },
    /**  业绩取值方案消耗储值卡详情  */
    async channelEmployeePerformanceSchemeTreatSavingCard_all() {
      let that = this;
      that.goodsLoading = true;
      try {
        let params = {
          PerformanceSchemeID: that.PerformanceSchemeID,
          CategoryID: that.CategoryID,
        };
        let res = await API.channelEmployeePerformanceSchemeTreatSavingCard_all(params);
        if (res.StateCode == 200) {
          that.treatSavingCardDetail = res.Data;
        } else {
          that.$message.error(res.Message);
        }
        that.goodsLoading = false;
      } catch (error) {
        that.goodsLoading = false;
        that.$message.error(error);
      }
    },
    /**  业绩取值方案消耗储值卡保存  */
    async channelEmployeePerformanceSchemeTreatSavingCard_create() {
      let that = this;
      try {
        let params = {
          PerformanceSchemeID: that.PerformanceSchemeID, //业绩方案ID
          CategoryID: that.CategoryID, //分类ID
          Good: that.treatSavingCardDetail
            .filter((i) => i.PerformanceCardRate || i.PerformanceCardLargessRate)
            .map((i) => {
              return {
                GoodID: i.ID, //商品ID
                PerformanceCardRate: i.PerformanceCardRate,
                PerformanceCardLargessRate: i.PerformanceCardLargessRate,
              };
            }),
        };
        let res = await API.channelEmployeePerformanceSchemeTreatSavingCard_create(params);
        if (res.StateCode == 200) {
          that.editDialogVisibleTreatSavingCard = false;
          that.$message.success("操作成功");
        } else {
          that.$message.error(res.Message);
        }
      } catch (error) {
        that.$message.error(error);
      }
    },
  },
  /** 创建实例之前执行函数   */
  beforeCreate() {},
  /**  实例创建完成之后  */
  created() {},
  /**  在挂载开始之前被调用  */
  beforeMount() {},
  /**  实例被挂载后调用  */
  mounted() {
    this.channelPerformanceScheme_all();
    this.channelPerformanceScheme_valid();
  },
  /**  数据更新 之前 调用  */
  beforeUpdate() {},
  /** 数据更新 完成 调用   */
  updated() {},
  /**  实例销毁之前调用  */
  beforeDestroy() {},
  /**  实例销毁后调用  */
  destroyed() {},
};
</script>

<style lang="scss">
.ChannelEmployeeSalaryPerformanceScheme {
  .input_type {
    .el-input-group__append {
      padding: 0 10px;
    }
  }
  .el-input__inner {
    padding-right: 0;
  }
}
</style>
