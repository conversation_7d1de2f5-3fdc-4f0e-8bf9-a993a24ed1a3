import * as API from '@/api/index'

export default {
    /* 员工业绩统计表 */
    list_employeePerformanceCommissionDetailStatement: params => {
        return API.POST('api/employeePerformanceCommissionDetailStatement/list', params)
      },
    /* 员工业绩表导出 */
    excel_employeePerformanceCommissionDetailStatement: params => {
        return API.exportExcel('api/employeePerformanceCommissionDetailStatement/excel', params)
      },
    /* 员工列表 */
    list_employee: params => {
      return API.POST('api/employee/all', params)
    },

    /* 门店列表 */
    reportEntity_list: params => {
      return API.POST('api/reportEntity/storeList', params)
    },

    /* 门店下员工列表 */
    getEntityEmployee_list: params => {
      return API.POST('api/employee/entityEmployee', params)
    },
}