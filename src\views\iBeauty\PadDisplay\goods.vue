<template>
  <div class="Groupon content_body PadDisplayGoods" v-loading="loading">
    <!-- 搜索 -->
    <div class="nav_header">
      <el-row>
        <el-col :span="20">
          <el-form :inline="true" size="small" class="demo-form-inline" @submit.native.prevent @keyup.enter.native="handleSearch">
            <el-form-item label="名称">
              <el-input v-model="Name" @clear="handleSearch" clearable placeholder="请输入名称搜索"></el-input>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleSearch">搜索</el-button>
            </el-form-item>
          </el-form>
        </el-col>
        <el-col :span="4" class="text_right">
          <el-button type="primary" size="small" @click="showDialog('add')">新增</el-button>
        </el-col>
      </el-row>
    </div>
    <!-- 表格 -->
    <div>
      <el-table size="small" :data="GoodsDisplayAll" v-loading="GoodsListLoading">
        <el-table-column label="项目图片">
          <template slot-scope="scope">
            <el-image style="width: 35px; height: 35px" v-if="scope.row.ImageURL" :src="scope.row.ImageURL" fit="cover"></el-image>
          </template>
        </el-table-column>
        <el-table-column prop="CategoryName" label="所属分类"></el-table-column>
        <el-table-column prop="Name" label="名称"></el-table-column>
        <el-table-column prop="Price" label="价格">
          <template slot-scope="scope">
            {{ scope.row.Price | toFixed | NumFormat }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="145px">
          <template slot-scope="scope">
            <el-button type="primary" size="small" @click="showDialog('edit', scope.row)">编辑</el-button>
            <el-button v-if="!scope.row.ParentID" type="danger" size="small" @click="handelDelete(scope.row.ID)">删除 </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <!-- 分页 -->
    <div class="pad_15 text_right">
      <el-pagination
        background
        @current-change="handleGoodsListPageChange"
        :current-page.sync="GoodsListPaginations.page"
        :page-size="GoodsListPaginations.page_size"
        :layout="GoodsListPaginations.layout"
        :total="GoodsListPaginations.total"
      ></el-pagination>
    </div>
    <!-- 新增、编辑 -->
    <el-dialog :title="dialogTitle" :visible.sync="DialogVisible" width="1000px" @close="closeDialog">
      <el-scrollbar class="el_scrollbar_height">
        <el-form size="small" :model="formData" ref="formData" label-width="100px" class="demo-ruleForm">
          <el-form-item label="所属分类" prop="CategoryID" :rules="[{ required: true, message: '所属分类不能为空', trigger: 'change' }]">
            <el-cascader
              v-model="formData.CategoryID"
              :props="{ children: 'Child', value: 'ID', label: 'Name', emitPath: false, expandTrigger: 'hover' }"
              :show-all-levels="true"
              clearable
              filterable
              :options="CategoryList"
              @change="handleChange"
            ></el-cascader>
          </el-form-item>
          <el-form-item label="商品名称" prop="Name" :rules="[{ required: true, message: '请输入商品名称', trigger: 'blur' }]">
            <el-input v-model="formData.Name"></el-input>
          </el-form-item>
          <el-form-item label="商品价格">
            <el-input size="small" v-model="formData.Price" type="number" class="custom-input-number" v-input-fixed></el-input>
          </el-form-item>
          <el-form-item label="商品主图" prop="Logo_file">
            <span class="font_12 color_999">建议上传1:1比例图片</span>
            <el-upload
              action="#"
              :limit="1"
              list-type="picture-card"
              ref="goodsMainRef"
              :class="{hide:isLimit}"
              :file-list="formData.ImageURL"
              :before-upload="beforeAvatarUpload"
              :on-exceed="mainImageExceed"
              multiple
            >
              <i slot="default" class="el-icon-camera-solid" style="font-size: 40px; color: #999"></i>
              <div slot="file" slot-scope="{ file }">
                <el-image
                  class="el-upload-list__item-thumbnail"
                  :id="file.uid"
                  :src="file.url"
                  :preview-src-list="showFileList_1"
                  :z-index="9999"
                  fit="cover"
                ></el-image>
                <span class="el-upload-list__item-actions">
                  <span class="el-upload-list__item-preview" @click="DialogPreview(file)">
                    <i class="el-icon-zoom-in"></i>
                  </span>

                  <span class="el-upload-list__item-delete" @click="handleRemove(file)">
                    <i class="el-icon-delete"></i>
                  </span>
                </span>
              </div>
            </el-upload>
          </el-form-item>
          <el-form-item label="商品详情:">
            <!-- 富文本编辑器 -->
            <quill-editor ref="myTextEditor" v-model="formData.Memo" :options="editorOption" style="width: 90%; height: 400px"></quill-editor>
          </el-form-item>
        </el-form>
      </el-scrollbar>
      <span slot="footer" class="dialog-footer">
        <el-button size="small" @click="closeDialog" v-prevent-click>取 消</el-button>
        <el-button size="small" type="primary" @click="addGoodConfig" v-prevent-click :loading="isAddLoading">保 存</el-button>
      </span>
    </el-dialog>

    <!-- 图片上传组件辅助-->
    <el-upload class="goodsDetail-image-uploader" action="#" accept="image/*" :show-file-list="false" :before-upload="goodsDetailBeforeUpload" multiple> </el-upload>
  </div>
</template>

<script>
import API from "@/api/iBeauty/PadDisplay/goods";
import utils from "@/components/js/utils.js";
// 引入富文本编辑样式以及组件
import "quill/dist/quill.core.css";
import "quill/dist/quill.snow.css";
import "quill/dist/quill.bubble.css";
import { quillEditor } from "vue-quill-editor";
export default {
  name: "PadDisplayGoods",
  created() {
    var that = this;
    // 富文本编辑器的配置
    var toolbarOptions = [
      ["bold", "italic", "underline", "strike"], // 加粗 斜体 下划线 删除线 -----['bold', 'italic', 'underline', 'strike']
      ["blockquote", "code-block"], // 引用  代码块-----['blockquote', 'code-block']
      [{ header: 1 }, { header: 2 }], // 1、2 级标题-----[{ header: 1 }, { header: 2 }]
      [{ list: "ordered" }, { list: "bullet" }], // 有序、无序列表-----[{ list: 'ordered' }, { list: 'bullet' }]
      [{ script: "sub" }, { script: "super" }], // 上标/下标-----[{ script: 'sub' }, { script: 'super' }]
      [{ indent: "-1" }, { indent: "+1" }], // 缩进-----[{ indent: '-1' }, { indent: '+1' }]
      [{ direction: "rtl" }], // 文本方向-----[{'direction': 'rtl'}]
      [{ size: ["small", false, "large", "huge"] }], // 字体大小-----[{ size: ['small', false, 'large', 'huge'] }]
      [{ header: [1, 2, 3, 4, 5, 6, false] }], // 标题-----[{ header: [1, 2, 3, 4, 5, 6, false] }]
      [{ color: [] }, { background: [] }], // 字体颜色、字体背景颜色-----[{ color: [] }, { background: [] }]
      [{ font: [] }], // 字体种类-----[{ font: [] }]
      [{ align: [] }], // 对齐方式-----[{ align: [] }]
      ["clean"], // 清除文本格式-----['clean']
      ["image"],
    ]; // 链接、图片、视频-----['link', 'image', 'video']

    that.editorOption = {
      modules: {
        //工具栏定义的
        toolbar: {
          container: toolbarOptions,
          handlers: {
            image: function (val) {
              if (val) {
                // 通过input的type=file唤醒选择弹框，选择之后自定义上传路径
                // document.querySelector(".detail-image-uploader input").click();
                document.querySelector(".goodsDetail-image-uploader input").click();
              } else {
                this.quill.format("image", false);
              }
            },
          },
        },
      },
      //主题
      theme: "snow",
      placeholder: "请输入正文",
    };
  },
  components: {
    quillEditor,
  },
  data() {
    return {
      isLimit:false,
      Name: "",
      loading: false,
      isAddLoading: false,
      GoodsListLoading: false,
      formData: {
        CategoryID: "",
        Name: "",
        Price: "",
        ImageURL: [],
        Memo: "",
      },
      GoodsDisplayAll: [],
      searchValue: "",
      showFileList_1: [], //分类主图预览
      moveIndex: "", //移动类型
      moveID: "", //移动ID
      // 添加分类的弹窗是否开启
      DialogVisible: false,
      dialogTitle: "",
      dialogVisibleType: "",
      CategoryList: [], //分类
      GoodsListPaginations: {
        page: 1, // 当前位于哪页
        total: 0, // 总数
        page_size: 10, // 1页显示多少条
        layout: "total, prev, pager, next,jumper", // 翻页属性
      },
    };
  },

  mounted() {
    var that = this;
    that.handleSearch();
    // 获取分类列表
    that.getCategoryList();
  },

  methods: {
    /**  详情上传图片  */
    goodsDetailBeforeUpload(file) {
      let that = this;
      // let size = file.size / 1024 < 200;
      // if (!size) {
      //   this.$message.error("上传图片大小不能超过 200kb!");
      //   return false;
      // }
      utils.getImageBase64(file).then((base64) => {
        this.upload_addAttachment(base64).then((AttachmentURL) => {
          let quill = that.$refs.myTextEditor.quill;
          let len = quill.getSelection().index;
          quill.insertEmbed(len, "image", AttachmentURL);
          quill.setSelection(len + 1);
        });
      });
      return false;
    },
    // 获取分类列表
    getCategoryList() {
      const that = this;
      const params = {
        Name: that.name,
        Active: that.active,
      };
      that.CategoryListLoading = true;
      API.CategoryValid(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.CategoryList = res.Data;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(() => {
          that.CategoryListLoading = false;
        });
    },
    // 搜索
    handleSearch() {
      var that = this;
      that.GoodsListPaginations.page = 1;
      that.getGoodsList();
    },
    // 获取商品列表
    getGoodsList() {
      const that = this;
      const params = {
        Name: that.Name,
        PageNum: that.GoodsListPaginations.page,
      };
      API.GoodsDisplayAll(params).then((res) => {
        if (res.StateCode == 200) {
          that.GoodsDisplayAll = res.List;
          that.GoodsListPaginations.total = res.Total;
        } else {
          that.$message({
            message: res.Message,
            duration: 2000,
          });
        }
      });
    },
    // 确定
    addGoodConfig() {
      const that = this;
      that.$refs.formData.validate((valid) => {
        if (valid) {
          that.GoodsDisplayCreate();
        }
      });
    },
    // 添加商品
    GoodsDisplayCreate() {
      const that = this;
      const param = that.formData;
      const params = {
        CategoryID: param.CategoryID,
        Name: param.Name,
        Price: param.Price,
        ImageURL: param.ImageURL.length ? param.ImageURL[0].url : null,
        Memo: param.Memo,
      };
      let api = "GoodsDisplayCreate";
      if (that.dialogVisibleType == "edit") {
        params.ID = param.ID;
        api = "GoodsDisplayUpdate";
      }
      that.isAddLoading = true;
      API[api](params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.handleSearch();
            that.DialogVisible = false;
            that.$message.success({
              message: that.dialogVisibleType == "edit" ? "修改成功" : "添加成功",
              duration: 2000,
            });
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(() => {
          that.isAddLoading = false;
        });
    },
    handleChange() {},
    // 添加/编辑
    showDialog(type, row) {
      const that = this;
      that.dialogVisibleType = type;
      that.DialogVisible = true;
      this.$refs.formData ? this.$refs.formData.resetFields() : "";
      switch (type) {
        case "add":
          that.dialogTitle = "添加";
          that.formData = {
            CategoryID: "",
            Name: "",
            Price: "",
            ImageURL: [],
            Memo: "",
          };
          break;
        case "edit":
          that.dialogTitle = "编辑";
          if (row.ImageURL) {
            that.showFileList_1 = [row.ImageURL];
            that.formData = {
              ID: row.ID,
              Name: row.Name,
              CategoryID: row.CategoryID,
              Price: row.Price,
              ImageURL: [{ name: "", url: row.ImageURL }],
              Memo: that.formData.Memo,
            };
          } else {
            that.showFileList_1 = [];
            that.formData = {
              ID: row.ID,
              Name: row.Name,
              CategoryID: row.CategoryID,
              Price: row.Price,
              ImageURL: [],
              Memo: that.formData.Memo,
            };
          }
          API.GoodsDisplayMemo({ ID: row.ID }).then((res) => {
            if (res.StateCode == 200) {
              that.formData.Memo = res.Message;
              that.isLimit = that.formData.ImageURL.length > 0;
            }
          });

          break;
      }
    },
    closeDialog() {
      this.$refs.formData.resetFields();
      this.DialogVisible = false;
      this.formData = {
        CategoryID: "",
        Name: "",
        Price: "",
        ImageURL: [],
        Memo: "",
      };
    },
    // 删除
    handelDelete(ID) {
      this.$confirm("此操作将删除该商品, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          API.GoodsDisplayDelete({ ID }).then((res) => {
            if (res.StateCode == 200) {
              this.$message({
                type: "success",
                message: "删除成功!",
              });
              this.handleSearch();
            } else {
              this.$message.error({
                message: res.Message,
                duration: 2000,
              });
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    // 上传之前
    beforeAvatarUpload(file) {
      let that = this;
      // const isLt2M = file.size / 1024 < 200;
      // if (!isLt2M) {
      //   that.$message.error("上传图片大小不能超过 200kb!");
      //   return false;
      // }
      utils.getImageBase64(file).then((base64) => {
        that.upload_addAttachment(base64).then((AttachmentURL) => {
          that.$refs.goodsMainRef.clearFiles();
          that.formData.ImageURL.push({ name: file.name, url: AttachmentURL });
          that.showFileList_1 = [AttachmentURL];
          that.isLimit = that.formData.ImageURL.length > 0;
        });
      });
      return false;
    },
    // 查看大图
    DialogPreview(file) {
      document.getElementById(file.uid).click();
    },
    // 删除图片
    handleRemove(file) {
      let that = this;
      that.formData.ImageURL = that.formData.ImageURL.filter((i) => i.url != file.url);
      that.$refs.goodsMainRef.clearFiles();
      that.showFileList_1 = that.formData.ImageURL.map((i) => i.url);
      that.isLimit = that.formData.ImageURL.length > 0;
    },
    /**    */
    mainImageExceed() {
      // let that = this;
      console.log("00000");
    },
    handleGoodsListPageChange() {
      this.getGoodsList();
    },
    /**  监听富文本 内容变化  */
    // onEditorChange({ html }) {
    // let that = this;
    // const regex = new RegExp("<img", "gi");
    // that.Memo_1 = html.replace(regex, `<img style="max-width: 100%;"`);
    // },

    /**    */
    async upload_addAttachment(base64) {
      let that = this;
      let params = { AttachmentURL: base64 };
      that.logding = true;
      let res = await API.upload_addAttachment(params);
      if (res.StateCode == 200) {
        // let quill = that.$refs.myTextEditor.quill;
        // let len = quill.getSelection().index;
        // quill.insertEmbed(len, "image", res.Data.AttachmentURL);
        // quill.setSelection(len + 1);

        return res.Data.AttachmentURL;
      } else {
        that.$message.error(res.Message);
      }
      that.logding = false;
    },
  },
};
</script>

<style lang="scss" >
.PadDisplayGoods {
  .el-dialog {
    // height: 60vh;
  }
  .el_scrollbar_height {
    height: 55vh;
    .el-scrollbar__wrap {
      overflow-x: hidden !important;
    }
    #container {
      width: 100%;
      height: 55vh;
    }
    .el-upload-list--picture-card .el-upload-list__item {
      width: 100px;
      height: 100px;
    }
    .el-upload.el-upload--picture-card {
      width: 100px !important;
      height: 100px !important;
      line-height: 115px;
    }
  }
  .el-scrollbar_height_range {
    height: 35vh;
    .el-scrollbar__wrap {
      overflow-x: hidden;
    }
  }

  // 隐藏上传按钮
  .hide .el-upload--picture-card {
    display: none;
  }
}
</style>