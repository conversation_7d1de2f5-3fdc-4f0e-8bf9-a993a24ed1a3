<!DOCTYPE html>
<html lang="">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <link href="medicalEditor.css" rel="stylesheet">
    <style>
        html,
        body {
            height: 100%;
            margin: 0;
        }

        body {
            display: flex;
            flex-direction: column;
        }
    </style>
</head>

<body>
    <div style="text-align: center;margin:10px;">
        <button id="data">保存数据</button>
        <button id="temp">保存模板</button>
        <button id="editMode">编辑模式</button>
        <button id="designMode">设计模式</button>
        <button id="clearData">清空数据</button>
        <button id="destroyData">销毁数据</button>
        <button id="hideHeader">隐藏页眉</button>
        <button id="showHeader">显示页眉</button>
        <button id="readonly">只读模式true</button>
        <button id="readonlyfalse">只读模式false</button>
        <button id="superPrint">超级打印</button>
        <button id="validate">校验</button>
    </div>

    <div id="app"></div>
    <script src="chunk-vendors-editor.js"></script>
    <script src="chunk-editor.js"></script>
    <script src="medicalEditor.js"></script>
    <script src="pdf.min.js"></script>
    <script>
        PDFJS.workerSrc = 'pdf.worker.min.js';
    </script>
    <script>
        //编辑器上下文，编辑器外部关联环境的一下参数
        let docContext = {
            paperSetting: localStorage.getItem("editorHeader") ? JSON.parse(localStorage.getItem("editorHeader")) : {
                size: "A4",			// 纸张尺寸，目前只有两个值：A4 和 A5
                marginX: 10, 		//打印时，左右两边的边距，单位：毫米
                marginTop: 20,		//打印时，上边的边距，单位：毫米
                marginBottom: 10,	//打印时，下边的边距，单位：毫米
                headerContent: "" 	//页面的html内容
            },
            fieldData: {}, //页眉字段值，用来自动填充字段数据的，像是下面这样的：{user: {age: 28, name: "孙悟空"}, doctor: {name:"华佗在世"}}
            fieldDict: [] //页眉字段的词典，用来做设计时，选择字段类型，像是下面这样的：[{label: "医生 · 主治医生", value: "doctor.name", icon: "hm-icon-doctor"}]
        }

        //文档数据，就是要保存到数据的 key-value 对象
        let docData = {}
        //字段值，用来自动填充字段数据的，像是下面这样的：{user: {age: 28, name: "孙悟空"}, doctor: {name:"华佗在世"}}
        let fieldData = {}
        // 字段的词典，用来做设计时，选择字段类型，像是下面这样的：[{label: "医生 · 主治医生", value: "doctor.name", icon: "hm-icon-doctor"}]
        let fieldDict = []
        // 词条字典，用来在设计模式下，选择词条类型，像是下面这样的：[{"value": "kqzhk", "label": "口腔综合科"}]
        let wordDict = []

        let editor = createEditor(document.querySelector("#app"), docContext, docData, fieldData, fieldDict, wordDict)

        console.log({ docContext, docData, fieldData, fieldDict, wordDict })

        /**
         * 编辑器内核是在 iframe 里初始化的，iframe 加载需要一定时间，编辑器初始化需要一点时间
         * 编辑器初始化完毕后，会触发 "initComplete" 事件
         */
        editor.$on("initComplete", () => {
            // 初始化词条字典
            editor.axios.get(`wrodentryParent.json?xxxId=123333`).then((data) => {
                wordDict.length = 0 // 清空原有的词条字典
                data = data.map((d) => {
                    return {
                        value: d.H_Id,
                        label: d.O_Name
                    }
                })
                data.unshift({
                    value: "__doctor",
                    label: "医生"
                })
                //词条字典支持二级
                data[3].children = [{
                    "value": "kqwk",
                    "label": "口腔外科"
                }, {
                    "value": "kqnk",
                    "label": "口腔内科"
                }]

                Array.prototype.push.apply(wordDict, data); //把获取回来的词条全面添加到 wordDict 里
            })
            // 初始化词条字典
            editor.axios.get(`fieldDict.json`).then((data) => {
                Array.prototype.push.apply(fieldDict, data); //把获取回来的词条全面添加到 wordDict 里
            })
            //editor.resetDocContent(localStorage.getItem("editorTemp") || "") //重置编辑器的内容，旧的历史记录会清空
            editor.setDocContent(localStorage.getItem("editorTemp") || "") //设置模板内容，历史记录不会清空
            Object.assign(docData, JSON.parse(localStorage.getItem("docData") || "{}")) // 更新文档数据
            Object.assign(fieldData, {
                user: { age: 32, name: "患者1" }, doctor: { name: "医生1" } // 更新字段数据
            })
        }).$on("transaction", (html) => {
            console.log("文档内容变化")
            localStorage.setItem("editorTemp", editor.getDocContent())
        }).$on("paperchange", (setting) => {
            console.log("纸张设置变化（包括页眉）")
            localStorage.setItem("editorHeader", JSON.stringify(setting))
        }).$on("sign", (id) => {
            browser(".png, .jpg", false, (files) => {
                let f = files[0]
                if (!f) return
                if (f.size > 1024 * 1024) { //1M 大小
                    this.tip({
                        type: "warning",
                        msg: "不支持超过 1 M 的图片文件"
                    })
                }
                //获取文件
                var reader = new FileReader();
                //读取完成
                reader.onload = (e) => {
                    docData[id] = reader.result
                };
                reader.readAsDataURL(f);
            })
        })

        document.querySelector("#data").onclick = function () { //保存数据
            localStorage.setItem("docData", JSON.stringify(docData))
        }
        document.querySelector("#temp").onclick = function () { //保存模板
            localStorage.setItem("editorTemp", editor.getDocContent())
        }
        document.querySelector("#editMode").onclick = function () { //编辑
            editor.setEditable(false)
        }
        document.querySelector("#designMode").onclick = function () { //设计
            editor.setEditable(true)
        }
        document.querySelector("#clearData").onclick = function () { //清空数据
            editor.clearData()
        }
        document.querySelector("#clearData").onclick = function () { //销毁数据，销毁后表单数据不再响应当前文档内容的输入变化
            editor.destroyData()
        }
        document.querySelector("#hideHeader").onclick = function () { //隐藏页眉
            editor.showHeader(false)
        }
        document.querySelector("#showHeader").onclick = function () { //显示页眉
            editor.showHeader(true)
        }
        document.querySelector("#readonly").onclick = function () { //只读模式
            editor.setReadonly(true)
        }
        document.querySelector("#readonlyfalse").onclick = function () { //只读模式
            editor.setReadonly(false)
        }
        document.querySelector("#superPrint").onclick = function () { //多模板连续打印
            let printer = superPrinter()
            printer.$on("initComplete", () => {
                printer.print([{
                    docCtx: docContext,
                    docData: docData,
                    fieldData: fieldData,
                    html: localStorage.getItem("editorTemp")
                }, {
                    docCtx: docContext,
                    docData: docData,
                    fieldData: fieldData,
                    html: localStorage.getItem("editorTemp")
                }])
            })
        }
        document.querySelector("#validate").onclick = function () { //多模板连续打印
            editor.validate()
        }


        /**
         *	@param accept
         *	accept="image/png" or accept=".png" — 只接受 png 图片.
         *	accept="image/png, image/jpeg" or accept=".png, .jpg, .jpeg" — PNG/JPEG 文件.
         *	accept="image/*" — 接受任何图片文件类型.
         *	accept=".doc,.docx,.xml,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document" — 接受任何 MS Doc 文件类型.
         *	@param isMultiple 是否多选
         *	@param onchange 回调函数
         */
        function browser(accept, isMultiple, onchange) {
            let input = document.createElement("input");
            input.type = "file";

            if (accept) {
                input.accept = accept;
            }
            if (isMultiple === true) {
                input.multiple = true;
            }
            input.style.display = "none";
            input.onchange = () => {
                onchange(input.files);
                document.body.removeChild(input);
            }
            document.body.appendChild(input);
            input.click();
        }
    </script>
</body>

</html>