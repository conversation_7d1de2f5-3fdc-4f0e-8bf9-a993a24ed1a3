<template>
<!-- 成长值设置 -->
  <div class="content_body font_14">
      <div class="nav_title font_15 border_box" style="border-bottom:3px solid #E4E4E4">会员成长值规则设置</div>
      <div class="back_f8 padlt_10 padrt_20 marbm_15 dis_flex flex_y_center flex_x_between ">
        <div class="label dis_flex flex_y_center" style="margin-top:0">启用成长值</div>
        <el-switch v-model="growthValue"></el-switch>
      </div>
      <div class="color_999 padlt_20 line_26 marbm_15">
        启用后，会员等级随成长值变化，可针对单个会员手动调整和锁定等级<br>不启用，则会员等级手动调整
        </div>
        <!-- 配置模块 -->
        <div v-if="growthValue">
          <div class="padlt_20 marbm_15">
         <div class="marbm_15">来店一次产生成长值 
           <el-input type="number" class="width_120" v-model.number="GrowthValue" size="small"></el-input> 点/次
           </div>
           <div>完成预约产生成长值 
           <el-input type="number" class="width_120" v-model.number="taskValue" size="small"></el-input> 点/次
           </div>
        </div>
        <el-table :data="tableData" class="border_left border_right">
          <el-table-column label="商品类型" prop="goodsType">
            <template slot="header">
              <div class="text_center">商品类型</div>
            </template>
            <template slot-scope="scope">
              <div class="text_center">
              {{scope.row.goodsType}}
              </div>
            </template>
          </el-table-column>
          <el-table-column label="门店销售现金支付1元产生成长值" prop="StoreSales">
            <template slot-scope="scope">
              <el-input v-model.number="scope.row.StoreSales" size="small" class="inputValue" clearable onkeyup="this.value=this.value=this.value.replace(/\D/g,'')"></el-input>
            </template>
          </el-table-column>
          <el-table-column label="门店销售卡扣1元产生成长值" prop="CardDeduction">
            <template slot-scope="scope">
              <el-input v-model.number="scope.row.CardDeduction" size="small" class="inputValue" clearable
              onkeyup="this.value=this.value=this.value.replace(/\D/g,'')"></el-input>
            </template>
          </el-table-column>
          <el-table-column label="门店销售赠送卡扣1元产生成长值" prop="giving">
            <template slot-scope="scope">
              <el-input v-model.number="scope.row.giving" size="small" class="inputValue" clearable onkeyup="this.value=this.value=this.value.replace(/\D/g,'')"></el-input>
            </template>
          </el-table-column>
          <el-table-column label="网店销售现金支付1元产生成长值" prop="payments">
            <template slot-scope="scope">
              <el-input v-model.number="scope.row.payments" size="small" class="inputValue" clearable onkeyup="this.value=this.value=this.value.replace(/\D/g,'')"></el-input>
            </template>
          </el-table-column>
          <el-table-column label="消耗现金支付1元产生成长值" prop="consumption">
            <template slot-scope="scope">
              <el-input v-model.number="scope.row.consumption" size="small" class="inputValue" clearable onkeyup="this.value=this.value=this.value.replace(/\D/g,'')"></el-input>
            </template>
          </el-table-column>
        </el-table>
        <div class="text_center martp_25">
         <el-button type="primary" size="small" class="font_16">保存</el-button>
        </div>
        </div>
  </div>
</template>

<script>
export default {
  name: "GrowthValue",
  /**  引入的组件  */
  components: {},
  /**  Vue 实例的数据对象**/
  data() {
    return {
growthValue:false, //成长值
GrowthValue:"", //来店一次
taskValue:"", //完成预约
tableData:[
{
  goodsType:"产品",
  StoreSales:"",
  CardDeduction:"",
giving:"",
payments:"",
consumption:""
},
{
  goodsType:"项目",
  StoreSales:"",
  CardDeduction:"",
giving:"",
payments:"",
consumption:""
},
{
  goodsType:"储值卡",
  StoreSales:"",
  CardDeduction:"",
giving:"",
payments:"",
consumption:""
},
{
  goodsType:"通用次卡",
  StoreSales:"",
  CardDeduction:"",
giving:"",
payments:"",
consumption:""
},
{
  goodsType:"时效卡",
  StoreSales:"",
  CardDeduction:"",
giving:"",
payments:"",
consumption:""
},
{
  goodsType:"套餐卡",
  StoreSales:"",
  CardDeduction:"",
giving:"",
payments:"",
consumption:""
},
],

    };
  },
  /**计算属性  */
  computed: {},
  /**  方法集合  */
  methods:{},
  /** 监听数据变化   */
  watch: {},
  /** 创建实例之前执行函数   */
  beforeCreate() {},
  /**  实例创建完成之后  */
  created() {},
  /**  在挂载开始之前被调用  */
  beforeMount() {},
  /**  实例被挂载后调用  */
  mounted() {},
  /**  数据更新 之前 调用  */
  beforeUpdate() {},
  /** 数据更新 完成 调用   */
  updated() {},
  /**  实例销毁之前调用  */
  beforeDestroy() {},
  /**  实例销毁后调用  */
  destroyed() {},
};
</script>

<style lang="scss">
.nav_title{
  padding: 20px;
  width: calc(100% + 30px);
  transform: translate(-15px,-15px);
}
.label::before {
    content: "";
    display: inline-block;
    width: 4px;
    height: 15px;
    margin: 10px 10px;
    background-color: #018dff;
  }
  .width_120{
    width: 120px;
  }
  .inputValue{
    width: 120px;
  }
</style>
