/**
 * Created by <PERSON><PERSON><PERSON> on 2022/01/11.
 * 门店业绩-储值卡消耗门店业绩
 */
import * as API from '@/api/index'

export default {
  // 获取门店储值卡消耗业绩方案列表
  getSavingCardEntityPerformanceScheme: params => {
    return API.POST('api/treatSavingCardEntityPerformanceScheme/list', params)
  },

  // 创建门店储值卡消耗业绩方案
  createSavingCardEntityPerformanceScheme: params => {
    return API.POST('api/treatSavingCardEntityPerformanceScheme/create', params)
  },

  // 删除门店储值卡消耗业绩方案
  deleteSavingCardEntityPerformanceScheme: params => {
    return API.POST('api/treatSavingCardEntityPerformanceScheme/delete', params)
  },

  // 编辑的点击
  getSavingCardCategoryEntityPerformance: params => {
    return API.POST('api/treatSavingCardCategoryEntityPerformance/all', params)
  },

  // 编辑的保存
  updateSavingCardCategoryEntityPerformance: params => {
    return API.POST('api/treatSavingCardCategoryEntityPerformance/update', params)
  },

  // 储值卡业绩的点击
  getSavingCardEntityPerformance: params => {
    return API.POST('api/treatSavingCardEntityPerformance/all', params)
  },

  // 储值卡业绩的保存
  updateSavingCardEntityPerformance: params => {
    return API.POST('api/treatSavingCardEntityPerformance/update', params)
  },

}
