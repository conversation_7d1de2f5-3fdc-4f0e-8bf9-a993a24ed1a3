<template>
  <div class="EmployeeTreatPerformanceCommissionStatistics content_body"  v-loading="loading">
    <div class="nav_header">
      <el-form :inline="true" size="small" :model="searchData" @submit.native.prevent>
        <el-form-item label="时间筛选">
          <el-date-picker
            v-model="searchData.QueryDate"
            :picker-options="pickerOptions"
            unlink-panels
            type="daterange"
            range-separator="至"
            value-format="yyyy-MM-dd"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            @change="handleSalePerformanceCommissionSearch"
          ></el-date-picker>
        </el-form-item>
        <el-form-item label="员工姓名">
          <el-input
            v-model="searchData.EmployeeName"
            clearable
            @keyup.enter.native="handleSalePerformanceCommissionSearch"
            @clear="handleSalePerformanceCommissionSearch"
            placeholder="请输入员工姓名"
          ></el-input>
        </el-form-item>
        <el-form-item v-if="storeEntityList.length > 1" label="所属门店">
          <el-select
            v-model="searchData.EntityID"
            clearable
            filterable
            placeholder="请选择门店"
            :default-first-option="true"
            @change="handleSalePerformanceCommissionSearch"
          >
            <el-option v-for="item in storeEntityList" :key="item.ID" :label="item.EntityName" :value="item.ID"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="员工职务">
          <el-select v-model="searchData.JobID" filterable placeholder="选择员工职务" @change="handleSalePerformanceCommissionSearch" clearable>
            <el-option v-for="item in jobTypeList" :key="item.ID" :label="item.JobName" :value="item.ID"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" v-prevent-click @click="handleSalePerformanceCommissionSearch">搜索</el-button>
        </el-form-item>
        <el-form-item>
          <el-button v-if="isExport" type="primary" size="small" :loading="downloadLoading" @click="downloadExcel">导出</el-button>
        </el-form-item>
      </el-form>
    </div>
    <el-table size="small" show-summary :summary-method="gettableDataSummaries" :data="tableData">
      <el-table-column prop="EmployeeName" label="员工姓名" width="80" fixed></el-table-column>
      <el-table-column prop="EmployeeID" label="员工编号" fixed></el-table-column>
      <el-table-column prop="JobName" label="职务" width="110" fixed></el-table-column>
      <el-table-column prop="EntityName" label="所属组织" width="170" fixed></el-table-column>
      <el-table-column label="消耗信息" align="center">
        <el-table-column prop="TreatPayPerformance" label="实收业绩" align="right" width="100">
          <template slot-scope="scope">
            {{ scope.row.TreatPayPerformance | toFixed | NumFormat }}
          </template>
        </el-table-column>
        <el-table-column prop="TreatPayCommission" label="实收提成" align="right" width="100">
          <template slot-scope="scope">
            {{ scope.row.TreatPayCommission | toFixed | NumFormat }}
          </template>
        </el-table-column>
        <el-table-column prop="TreatCardPerformance" label="卡扣业绩" align="right" width="100">
          <template slot-scope="scope">
            {{ scope.row.TreatCardPerformance | toFixed | NumFormat }}
          </template>
        </el-table-column>
        <el-table-column prop="TreatCardCommission" label="卡扣提成" align="right" width="100">
          <template slot-scope="scope">
            {{ scope.row.TreatCardCommission | toFixed | NumFormat }}
          </template>
        </el-table-column>
        <el-table-column prop="TreatCardLargessPerformance" label="赠卡扣业绩" align="right" width="100">
          <template slot-scope="scope">
            {{ scope.row.TreatCardLargessPerformance | toFixed | NumFormat }}
          </template>
        </el-table-column>
        <el-table-column prop="TreatCardLargessCommission" label="赠卡扣提成" align="right" width="100">
          <template slot-scope="scope">
            {{ scope.row.TreatCardLargessCommission | toFixed | NumFormat }}
          </template>
        </el-table-column>

        <el-table-column prop="TreatLargessPerformance" label="赠送业绩" align="right" width="100">
          <template slot-scope="scope">
            {{ scope.row.TreatLargessPerformance | toFixed | NumFormat }}
          </template>
        </el-table-column>
        <el-table-column prop="TreatLargessCommission" label="赠送提成" align="right" width="100">
          <template slot-scope="scope">
            {{ scope.row.TreatLargessCommission | toFixed | NumFormat }}
          </template>
        </el-table-column>
        <el-table-column prop="TreatSpecialBenefitCommission" label="无业绩奖励" align="right" width="100">
          <template slot-scope="scope">
            {{ scope.row.TreatSpecialBenefitCommission | toFixed | NumFormat }}
          </template>
        </el-table-column>
      </el-table-column>
      <el-table-column label="退消耗信息" align="center">
        <el-table-column prop="RefundTreatPayPerformance" label="退实收业绩" align="right" width="100">
          <template slot-scope="scope">
            {{ scope.row.RefundTreatPayPerformance | toFixed | NumFormat }}
          </template>
        </el-table-column>
        <el-table-column prop="RefundTreatPayCommission" label="退实收提成" align="right" width="100">
          <template slot-scope="scope">
            {{ scope.row.RefundTreatPayCommission | toFixed | NumFormat }}
          </template>
        </el-table-column>
        <el-table-column prop="RefundTreatCardPerformance" label="退卡扣业绩" align="right" width="100">
          <template slot-scope="scope">
            {{ scope.row.RefundTreatCardPerformance | toFixed | NumFormat }}
          </template>
        </el-table-column>
        <el-table-column prop="RefundTreatCardCommission" label="退卡扣提成" align="right" width="100">
          <template slot-scope="scope">
            {{ scope.row.RefundTreatCardCommission | toFixed | NumFormat }}
          </template>
        </el-table-column>
        <el-table-column prop="RefundTreatCardLargessPerformance" label="退赠卡扣业绩" align="right" width="100">
          <template slot-scope="scope">
            {{ scope.row.RefundTreatCardLargessPerformance | toFixed | NumFormat }}
          </template>
        </el-table-column>
        <el-table-column prop="RefundTreatCardLargessCommission" label="退赠卡扣提成" align="right" width="100">
          <template slot-scope="scope">
            {{ scope.row.RefundTreatCardLargessCommission | toFixed | NumFormat }}
          </template>
        </el-table-column>
        <el-table-column prop="RefundTreatLargessPerformance" label="退赠送业绩" align="right" width="100">
          <template slot-scope="scope">
            {{ scope.row.RefundTreatLargessPerformance | toFixed | NumFormat }}
          </template>
        </el-table-column>
        <el-table-column prop="RefundTreatLargessCommission" label="退赠送提成" align="right" width="100">
          <template slot-scope="scope">
            {{ scope.row.RefundTreatLargessCommission | toFixed | NumFormat }}
          </template>
        </el-table-column>
        <el-table-column prop="RefundTreatSpecialBenefitCommission" label="退无业绩奖励" align="right" width="100">
          <template slot-scope="scope">
            {{ scope.row.RefundTreatSpecialBenefitCommission | toFixed | NumFormat }}
          </template>
        </el-table-column>
      </el-table-column>
      <el-table-column label="合计" align="center">
        <el-table-column prop="TotalPayPerformance" label="实收业绩" align="right" width="100">
          <template slot-scope="scope">
            <div v-if="scope.row.TotalPayPerformance < 0" class="color_red">{{ scope.row.TotalPayPerformance | toFixed | NumFormat }}</div>
            <div v-else-if="scope.row.TotalPayPerformance > 0" class="color_green">+{{ scope.row.TotalPayPerformance | toFixed | NumFormat }}</div>
            <div v-else>0.00</div>
          </template>
        </el-table-column>
        <el-table-column prop="TotalPayCommission" label="实收提成" align="right" width="100">
          <template slot-scope="scope">
            <div v-if="scope.row.TotalPayCommission < 0" class="color_red">{{ scope.row.TotalPayCommission | toFixed | NumFormat }}</div>
            <div v-else-if="scope.row.TotalPayCommission > 0" class="color_green">+{{ scope.row.TotalPayCommission | toFixed | NumFormat }}</div>
            <div v-else>0.00</div>
          </template>
        </el-table-column>
        <el-table-column prop="TotalCardPerformance" label="卡扣业绩" align="right" width="100">
          <template slot-scope="scope">
            <div v-if="scope.row.TotalCardPerformance < 0" class="color_red">{{ scope.row.TotalCardPerformance | toFixed | NumFormat }}</div>
            <div v-else-if="scope.row.TotalCardPerformance > 0" class="color_green">+{{ scope.row.TotalCardPerformance | toFixed | NumFormat }}</div>
            <div v-else>0.00</div>
          </template>
        </el-table-column>
        <el-table-column prop="TotalCardCommission" label="卡扣提成" align="right" width="100">
          <template slot-scope="scope">
            <div v-if="scope.row.TotalCardCommission < 0" class="color_red">{{ scope.row.TotalCardCommission | toFixed | NumFormat }}</div>
            <div v-else-if="scope.row.TotalCardCommission > 0" class="color_green">+{{ scope.row.TotalCardCommission | toFixed | NumFormat }}</div>
            <div v-else>0.00</div>
          </template>
        </el-table-column>
        <el-table-column prop="TotalCardLargessPerformance" label="赠卡扣业绩" align="right" width="100">
          <template slot-scope="scope">
            <div v-if="scope.row.TotalCardLargessPerformance < 0" class="color_red">
              {{ scope.row.TotalCardLargessPerformance | toFixed | NumFormat }}
            </div>
            <div v-else-if="scope.row.TotalCardLargessPerformance > 0" class="color_green">
              +{{ scope.row.TotalCardLargessPerformance | toFixed | NumFormat }}
            </div>
            <div v-else>0.00</div>
          </template>
        </el-table-column>
        <el-table-column prop="TotalCardLargessCommission" label="赠卡扣提成" align="right" width="100">
          <template slot-scope="scope">
            <div v-if="scope.row.TotalCardLargessCommission < 0" class="color_red">
              {{ scope.row.TotalCardLargessCommission | toFixed | NumFormat }}
            </div>
            <div v-else-if="scope.row.TotalCardLargessCommission > 0" class="color_green">
              +{{ scope.row.TotalCardLargessCommission | toFixed | NumFormat }}
            </div>
            <div v-else>0.00</div>
          </template>
        </el-table-column>

        <el-table-column prop="TotalLargessPerformance" label="赠送业绩" align="right" width="100">
          <template slot-scope="scope">
            <div v-if="scope.row.TotalLargessPerformance < 0" class="color_red">
              {{ scope.row.TotalLargessPerformance | toFixed | NumFormat }}
            </div>
            <div v-else-if="scope.row.TotalLargessPerformance > 0" class="color_green">+{{ scope.row.TotalLargessPerformance | toFixed | NumFormat }}</div>
            <div v-else>0.00</div>
          </template>
        </el-table-column>
        <el-table-column prop="TotalLargessCommission" label="赠送提成" align="right" width="100">
          <template slot-scope="scope">
            <div v-if="scope.row.TotalLargessCommission < 0" class="color_red">
              {{ scope.row.TotalLargessCommission | toFixed | NumFormat }}
            </div>
            <div v-else-if="scope.row.TotalLargessCommission > 0" class="color_green">+{{ scope.row.TotalLargessCommission | toFixed | NumFormat }}</div>
            <div v-else>0.00</div>
          </template>
        </el-table-column>
        <el-table-column prop="TotalSpecialBenefitCommission" label="无业绩奖励" align="right" width="100">
          <template slot-scope="scope">
            <div v-if="scope.row.TotalSpecialBenefitCommission < 0" class="color_red">{{ scope.row.TotalSpecialBenefitCommission | toFixed | NumFormat }}</div>
            <div v-else-if="scope.row.TotalSpecialBenefitCommission > 0" class="color_green">
              +{{ scope.row.TotalSpecialBenefitCommission | toFixed | NumFormat }}
            </div>
            <div v-else>0.00</div>
          </template>
        </el-table-column>
      </el-table-column>
    </el-table>
    <div class="pad_15 text_right">
      <el-pagination
        background
        v-if="tableDataPaginations.total > 0"
        @current-change="handleSalePerformanceCommissionPageChange"
        :current-page.sync="tableDataPaginations.page"
        :page-size="tableDataPaginations.page_size"
        :layout="tableDataPaginations.layout"
        :total="tableDataPaginations.total"
      ></el-pagination>
    </div>
  </div>
</template>

<script>
import API from "@/api/Report/Employee/treatPerformanceCommissionStatistics.js";
import EntityAPI from "@/api/Report/Common/entity";
import APIJob from "@/api/KHS/Entity/jobtype";
const dayjs = require("dayjs");
const isoWeek = require("dayjs/plugin/isoWeek");
dayjs.extend(isoWeek);
export default {
  name: "EmployeeTreatPerformanceCommissionStatistics",

  beforeRouteEnter(to, from, next) {
    next((vm) => {
      vm.isExport = vm.$permission.permission(to.meta.Permission, "Report-Employee-TreatPerformanceCommissionStatistics-Export");
    });
  },
  props: {},
  /** 监听数据变化   */
  watch: {},
  /**  引入的组件  */
  components: {},
  /**  Vue 实例的数据对象**/
  data() {
    return {
      isExport: false,
      loading: false,
      downloadLoading: false,
      storeEntityList: [], //门店列表
      jobTypeList: [], //职务列表
      searchData: {
        QueryDate: [new Date(), new Date()],
        GoodsTypeName: "",
        EmployeeName: "",
        JobID: "",
        CustomerName: "",
        CategoryID: "",
        GoodsName: "",
      },
      tableData: [],
      tableDataSum: {},
      //需要给分页组件传的信息
      tableDataPaginations: {
        page: 1, // 当前位于哪页
        total: 0, // 总数
        page_size: 10, // 1页显示多少条
        layout: "total, prev, pager, next,jumper", // 翻页属性
      },
      pickerOptions: {
        shortcuts: [
          {
            text: "今天",
            onClick: (picker) => {
              let end = new Date();
              let start = new Date();
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "本周",
            onClick: (picker) => {
              let end = new Date();
              let weekDay = dayjs(end).isoWeekday();
              let start = dayjs(end)
                .subtract(weekDay - 1, "day")
                .toDate();
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "本月",
            onClick: (picker) => {
              let end = new Date();
              let start = dayjs(dayjs(end).format("YYYY-MM") + "01").toDate();
              picker.$emit("pick", [start, end]);
            },
          },
        ],
      },
      categoryList: [],
      cascaderProps: {
        checkStrictly: true,
        label: "Name",
        value: "ID",
        children: "Child",
      },
    };
  },
  /**计算属性  */
  computed: {},
  /**  方法集合  */
  methods: {
    handleSalePerformanceCommissionSearch() {
      var that = this;
      that.tableDataPaginations.page = 1;
      that.salePerformanceCommissionDetail();
    },
    handleSalePerformanceCommissionPageChange(page) {
      this.tableDataPaginations.page = page;
      this.salePerformanceCommissionDetail();
    },
    // 销售搜索
    salePerformanceCommissionDetail() {
      var that = this;
      if (that.searchData.QueryDate != null) {
        if (dayjs(that.searchData.QueryDate[0]).add(366, "day").valueOf() < dayjs(that.searchData.QueryDate[1]).valueOf()) {
          that.$message.error("时间筛选范围不能超366天");
          return;
        }
        var params = {
          EntityID: that.searchData.EntityID,
          StartDate: that.searchData.QueryDate[0],
          EndDate: that.searchData.QueryDate[1],
          EmployeeName: that.searchData.EmployeeName.trim(),
          PageNum: that.tableDataPaginations.page,
          JobID: that.searchData.JobID,
        };
        that.loading = true;
        API.employeeTreatPerformanceCommissionDetailStatement_statistics(params)
          .then((res) => {
            if (res.StateCode == 200) {
              that.tableDataSum = res.Data.employeeTreatPerformanceCommissionSumStatementForm;
              that.tableData = res.Data.employeeTreatPerformanceCommissionDetailStatementForms.List;
              that.tableDataPaginations.total = res.Data.employeeTreatPerformanceCommissionDetailStatementForms.Total;
              that.tableDataPaginations.page_size = res.Data.employeeTreatPerformanceCommissionDetailStatementForms.PageSize;
            } else {
              that.$message.error({
                message: res.Message,
                duration: 2000,
              });
            }
          })
          .finally(function () {
            that.loading = false;
          });
      }
    },


    gettableDataSummaries({ columns }) {
      const sums = [];
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = <span class="font_weight_600">合计</span>;
          return;
        }
        var filter_NumFormat = this.$options.filters["NumFormat"];
        var filter_toFixed = this.$options.filters["toFixed"];
        switch (column.property) {
          case "TreatPayPerformance":
            sums[index] = <span class="font_weight_600">{filter_NumFormat(this.tableDataSum ? this.tableDataSum.TreatPayPerformance : 0)}</span>;
            break;
          case "TreatPayCommission":
            sums[index] = <span class="font_weight_600">{filter_NumFormat(this.tableDataSum ? this.tableDataSum.TreatPayCommission : 0)}</span>;
            break;
          case "TreatCardPerformance":
            sums[index] = <span class="font_weight_600">{filter_NumFormat(this.tableDataSum ? this.tableDataSum.TreatCardPerformance : 0)}</span>;
            break;
          case "TreatCardCommission":
            sums[index] = <span class="font_weight_600">{filter_NumFormat(this.tableDataSum ? this.tableDataSum.TreatCardCommission : 0)}</span>;
            break;
          case "TreatCardLargessPerformance":
            sums[index] = <span class="font_weight_600">{filter_NumFormat(this.tableDataSum ? this.tableDataSum.TreatCardLargessPerformance : 0)}</span>;
            break;
          case "TreatCardLargessCommission":
            sums[index] = <span class="font_weight_600">{filter_NumFormat(this.tableDataSum ? this.tableDataSum.TreatCardLargessCommission : 0)}</span>;
            break;
          case "TreatLargessPerformance":
            sums[index] = <span class="font_weight_600">{filter_NumFormat(this.tableDataSum ? this.tableDataSum.TreatLargessPerformance : 0)}</span>;
            break;
          case "TreatLargessCommission":
            sums[index] = <span class="font_weight_600">{filter_NumFormat(this.tableDataSum ? this.tableDataSum.TreatLargessCommission : 0)}</span>;
            break;
          case "TreatSpecialBenefitCommission":
            sums[index] = <span class="font_weight_600">{filter_NumFormat(this.tableDataSum ? this.tableDataSum.TreatSpecialBenefitCommission : 0)}</span>;
            break;
          case "RefundTreatPayPerformance":
            sums[index] = <span class="font_weight_600">{filter_NumFormat(this.tableDataSum ? this.tableDataSum.RefundTreatPayPerformance : 0)}</span>;
            break;
          case "RefundTreatPayCommission":
            sums[index] = <span class="font_weight_600">{filter_NumFormat(this.tableDataSum ? this.tableDataSum.RefundTreatPayCommission : 0)}</span>;
            break;
          case "RefundTreatCardPerformance":
            sums[index] = <span class="font_weight_600">{filter_NumFormat(this.tableDataSum ? this.tableDataSum.RefundTreatCardPerformance : 0)}</span>;
            break;
          case "RefundTreatCardCommission":
            sums[index] = <span class="font_weight_600">{filter_NumFormat(this.tableDataSum ? this.tableDataSum.RefundTreatCardCommission : 0)}</span>;
            break;
          case "RefundTreatCardLargessPerformance":
            sums[index] = <span class="font_weight_600">{filter_NumFormat(this.tableDataSum ? this.tableDataSum.RefundTreatCardLargessPerformance : 0)}</span>;
            break;
          case "RefundTreatCardLargessCommission":
            sums[index] = <span class="font_weight_600">{filter_NumFormat(this.tableDataSum ? this.tableDataSum.RefundTreatCardLargessCommission : 0)}</span>;
            break;
          case "RefundTreatLargessPerformance":
            sums[index] = <span class="font_weight_600">{filter_NumFormat(this.tableDataSum ? this.tableDataSum.RefundTreatLargessPerformance : 0)}</span>;
            break;
          case "RefundTreatLargessCommission":
            sums[index] = <span class="font_weight_600">{filter_NumFormat(this.tableDataSum ? this.tableDataSum.RefundTreatLargessCommission : 0)}</span>;
            break;
          case "RefundTreatSpecialBenefitCommission":
            sums[index] = (
              <span class="font_weight_600">{filter_NumFormat(this.tableDataSum ? this.tableDataSum.RefundTreatSpecialBenefitCommission : 0)}</span>
            );
            break;
          case "TotalPayPerformance":
            if (this.tableDataSum && this.tableDataSum.TotalPayPerformance < 0) {
              sums[index] = <div class="color_red font_weight_600">{filter_NumFormat(filter_toFixed(this.tableDataSum.TotalPayPerformance))}</div>;
            } else if (this.tableDataSum && this.tableDataSum.TotalPayPerformance > 0) {
              sums[index] = <div class="color_green font_weight_600">+{filter_NumFormat(filter_toFixed(this.tableDataSum.TotalPayPerformance))}</div>;
            } else {
              sums[index] = <div class="font_weight_600">0.00</div>;
            }
            break;
          case "TotalPayCommission":
            if (this.tableDataSum && this.tableDataSum.TotalPayCommission < 0) {
              sums[index] = <div class="color_red font_weight_600">{filter_NumFormat(filter_toFixed(this.tableDataSum.TotalPayCommission))}</div>;
            } else if (this.tableDataSum && this.tableDataSum.TotalPayCommission > 0) {
              sums[index] = <div class="color_green font_weight_600">+{filter_NumFormat(filter_toFixed(this.tableDataSum.TotalPayCommission))}</div>;
            } else {
              sums[index] = <div class="font_weight_600">0.00</div>;
            }
            break;
          case "TotalCardPerformance":
            if (this.tableDataSum && this.tableDataSum.TotalCardPerformance < 0) {
              sums[index] = <div class="color_red font_weight_600">{filter_NumFormat(filter_toFixed(this.tableDataSum.TotalCardPerformance))}</div>;
            } else if (this.tableDataSum && this.tableDataSum.TotalCardPerformance > 0) {
              sums[index] = <div class="color_green font_weight_600">+{filter_NumFormat(filter_toFixed(this.tableDataSum.TotalCardPerformance))}</div>;
            } else {
              sums[index] = <div class="font_weight_600">0.00</div>;
            }
            break;
          case "TotalCardCommission":
            if (this.tableDataSum && this.tableDataSum.TotalCardCommission < 0) {
              sums[index] = <div class="color_red font_weight_600">{filter_NumFormat(filter_toFixed(this.tableDataSum.TotalCardCommission))}</div>;
            } else if (this.tableDataSum && this.tableDataSum.TotalCardCommission > 0) {
              sums[index] = <div class="color_green font_weight_600">+{filter_NumFormat(filter_toFixed(this.tableDataSum.TotalCardCommission))}</div>;
            } else {
              sums[index] = <div class="font_weight_600">0.00</div>;
            }
            break;
          case "TotalCardLargessPerformance":
            if (this.tableDataSum && this.tableDataSum.TotalCardLargessPerformance < 0) {
              sums[index] = <div class="color_red font_weight_600">{filter_NumFormat(filter_toFixed(this.tableDataSum.TotalCardLargessPerformance))}</div>;
            } else if (this.tableDataSum && this.tableDataSum.TotalCardLargessPerformance > 0) {
              sums[index] = <div class="color_green font_weight_600">+{filter_NumFormat(filter_toFixed(this.tableDataSum.TotalCardLargessPerformance))}</div>;
            } else {
              sums[index] = <div class="font_weight_600">0.00</div>;
            }
            break;
          case "TotalCardLargessCommission":
            if (this.tableDataSum && this.tableDataSum.TotalCardLargessCommission < 0) {
              sums[index] = <div class="color_red font_weight_600">{filter_NumFormat(filter_toFixed(this.tableDataSum.TotalCardLargessCommission))}</div>;
            } else if (this.tableDataSum && this.tableDataSum.TotalCardLargessCommission > 0) {
              sums[index] = <div class="color_green font_weight_600">+{filter_NumFormat(filter_toFixed(this.tableDataSum.TotalCardLargessCommission))}</div>;
            } else {
              sums[index] = <div class="font_weight_600">0.00</div>;
            }
            break;
          case "TotalLargessPerformance":
            if (this.tableDataSum && this.tableDataSum.TotalLargessPerformance < 0) {
              sums[index] = <div class="color_red font_weight_600">{filter_NumFormat(filter_toFixed(this.tableDataSum.TotalLargessPerformance))}</div>;
            } else if (this.tableDataSum && this.tableDataSum.TotalLargessPerformance > 0) {
              sums[index] = <div class="color_green font_weight_600">+{filter_NumFormat(filter_toFixed(this.tableDataSum.TotalLargessPerformance))}</div>;
            } else {
              sums[index] = <div class="font_weight_600">0.00</div>;
            }
            break;
          case "TotalLargessCommission":
            if (this.tableDataSum && this.tableDataSum.TotalLargessCommission < 0) {
              sums[index] = <div class="color_red font_weight_600">{filter_NumFormat(filter_toFixed(this.tableDataSum.TotalLargessCommission))}</div>;
            } else if (this.tableDataSum && this.tableDataSum.TotalLargessCommission > 0) {
              sums[index] = <div class="color_green font_weight_600">+{filter_NumFormat(filter_toFixed(this.tableDataSum.TotalLargessCommission))}</div>;
            } else {
              sums[index] = <div class="font_weight_600">0.00</div>;
            }
            break;
          case "TotalSpecialBenefitCommission":
            if (this.tableDataSum && this.tableDataSum.TotalSpecialBenefitCommission < 0) {
              sums[index] = <div class="color_red font_weight_600">{filter_NumFormat(filter_toFixed(this.tableDataSum.TotalSpecialBenefitCommission))}</div>;
            } else if (this.tableDataSum && this.tableDataSum.TotalSpecialBenefitCommission > 0) {
              sums[index] = <div class="color_green font_weight_600">+{filter_NumFormat(filter_toFixed(this.tableDataSum.TotalSpecialBenefitCommission))}</div>;
            } else {
              sums[index] = <div class="font_weight_600">0.00</div>;
            }
            break;
        }
      });
      return sums;
    },

    /** 数据导出 */
    downloadExcel() {
      var that = this;
      if (that.searchData.QueryDate != null) {
        if (dayjs(that.searchData.QueryDate[0]).add(366, "day").valueOf() < dayjs(that.searchData.QueryDate[1]).valueOf()) {
          that.$message.error("时间筛选范围不能超366天");
          return;
        }

        let params = {
          EntityID: that.searchData.EntityID,
          StartDate: that.searchData.QueryDate[0],
          EndDate: that.searchData.QueryDate[1],
          EmployeeName: that.searchData.EmployeeName.trim(),
          PageNum: that.tableDataPaginations.page,
          JobID: that.searchData.JobID,
        };
        that.downloadLoading = true;
        API.employeeTreatPerformanceCommissionDetailStatement_statisticsExcel(params)
          .then((res) => {
            this.$message.success({
              message: "正在导出",
              duration: "4000",
            });
            const link = document.createElement("a");
            let blob = new Blob([res], { type: "application/octet-stream" });
            link.style.display = "none";
            link.href = URL.createObjectURL(blob);
            link.download = "员工消耗业绩统计.xlsx"; //下载的文件名
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
          })
          .finally(function () {
            that.downloadLoading = false;
          });
      }
    },
    // 职务ID
    async getJobID() {
      var that = this;
      var params = {
        JobTypeName: "",
      };
      let res = await APIJob.getJobJobtypeAll(params);
      if (res.StateCode == 200) {
        that.jobTypeList = res.Data;
      } else {
        that.$message.error({
          message: res.Message,
          duration: 2000,
        });
      }
    },

    //获得当前用户下的权限门店
    getstoreEntityList() {
      var that = this;
      that.loading = true;
      EntityAPI.getStoreEntityList()
        .then((res) => {
          if (res.StateCode == 200) {
            that.storeEntityList = res.Data;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },
  },
  /** 创建实例之前执行函数   */
  beforeCreate() {},
  /**  实例创建完成之后  */
  created() {},
  /**  在挂载开始之前被调用  */
  beforeMount() {},
  /**  实例被挂载后调用  */
  mounted() {
    this.isExport = this.$permission.permission(this.$route.meta.Permission, "Report-Employee-TreatPerformanceCommissionStatistics-Export");
    this.getJobID();
    this.getstoreEntityList();
    this.handleSalePerformanceCommissionSearch();
  },
  /**  数据更新 之前 调用  */
  beforeUpdate() {},
  /** 数据更新 完成 调用   */
  updated() {},
  /**  实例销毁之前调用  */
  beforeDestroy() {},
  /**  实例销毁后调用  */
  destroyed() {},
};
</script>

<style lang="scss">
.EmployeeTreatPerformanceCommissionStatistics {
}
</style>
