<template>
  <div class="Groupon content_body" v-loading="loading">
    <!-- 搜索框 -->
    <div class="nav_header">
      <el-row>
        <el-col :span="20">
          <el-form :inline="true" size="small" class="demo-form-inline" @keyup.enter.native="handleSearch">
            <el-form-item label="活动名称">
              <el-input v-model="searchName" @clear="handleSearch" clearable placeholder="输入线上名称搜索"></el-input>
            </el-form-item>
            <el-form-item label="活动状态">
              <el-select @change="handleSearch" v-model="Status" placeholder="请选择" clearable>
                <el-option label="未开始" value="10"></el-option>
                <el-option label="进行中" value="20"></el-option>
                <el-option label="已结束" value="30"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleSearch">搜索</el-button>
            </el-form-item>
          </el-form>
        </el-col>
        <el-col :span="4" class="text_right">
          <el-button type="primary" size="small" @click="addHandleGroupClick">新增</el-button>
        </el-col>
      </el-row>
    </div>
    <!-- 表格 -->
    <div>
      <el-table size="small" ref="multipleTable" :data="tableData" tooltip-effect="light">
        <el-table-column prop="Name" label="活动名称"></el-table-column>
        <el-table-column prop="BeginDateTime" label="开始时间">
          <template slot-scope="scope">
            {{ scope.row.BeginDateTime | dateFormat("YYYY-MM-DD HH:mm") }}
          </template>
        </el-table-column>
        <el-table-column prop="EndDateTime" label="结束时间">
          <template slot-scope="scope">
            {{ scope.row.EndDateTime | dateFormat("YYYY-MM-DD HH:mm") }}
          </template>
        </el-table-column>
        <el-table-column label="状态" prop="Status">
          <template slot-scope="scope">
            <span v-if="scope.row.Status == 10">未开始</span>
            <span v-if="scope.row.Status == 20">进行中</span>
            <span v-if="scope.row.Status == 30">已结束</span>
            <span v-else></span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="80px">
          <template slot-scope="scope">
            <el-button :disabled="scope.row.Status == 30" type="primary" size="small" @click="editGrouponInfoClick(scope.row)" v-prevent-click>编辑</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <!-- 分页 -->
    <div class="pad_10 dis_flex flex_x_end" v-if="paginations.total > 0">
      <el-pagination
        background
        :current-page.sync="paginations.page"
        :layout="paginations.layout"
        :total="paginations.total"
        @current-change="currentChange"
      ></el-pagination>
    </div>

    <!-- 新增拼团活动弹出层 -->
    <el-dialog :title="DialogTitle" :visible.sync="dialogVisible" width="1100px" custom-class="addGrouponClass">
      <el-row :gutter="20">
        <el-col :span="7">
          <el-carousel :interval="4000" height="525px" indicator-position="outside" style="margin-top: 14px">
            <el-carousel-item v-for="item in grouponImageName" :key="item">
              <el-image :src="getImgUrl(item)"></el-image>
            </el-carousel-item>
          </el-carousel>
        </el-col>

        <el-col :span="17">
          <el-form :model="addRuleForm" :rules="rules" ref="addRuleForm" label-width="100px" size="small">
            <el-tabs v-model="activeName">
              <el-tab-pane label="基础信息" name="0">
                <el-scrollbar class="add_el_scrollbar_height">
                  <el-form-item label="活动名称" prop="Name">
                    <el-input v-model="addRuleForm.Name" size="small" placeholder="作为标题在页面上展示，长度2到20个字"></el-input>
                  </el-form-item>

                  <el-form-item label="活动时间" prop="DateTime">
                    <el-col :span="9" style="padding-left: 0px">
                      <el-form-item prop="BeginDateTime" style="margin-bottom: 0">
                        <el-date-picker
                          :disabled="Status != 10 && !isAdd"
                          size="small"
                          type="datetime"
                          placeholder="开始时间"
                          :picker-options="BeginDateTimeDisabled"
                          v-model="addRuleForm.BeginDateTime"
                          @change="beginDataChange"
                          default-time="08:00:00"
                          value-format="yyyy-MM-dd HH:mm:ss"
                        ></el-date-picker>
                      </el-form-item>
                    </el-col>
                    <el-col :span="1">
                      <span class="color_666"> - </span>
                    </el-col>
                    <el-col :span="9">
                      <el-form-item prop="EndDateTime" style="margin-bottom: 0">
                        <el-date-picker
                          :disabled="Status == 30"
                          size="small"
                          type="datetime"
                          placeholder="结束时间"
                          :picker-options="EndDateTimeDisabled"
                          v-model="addRuleForm.EndDateTime"
                          @change="endDataChange"
                          default-time="17:00:00"
                          value-format="yyyy-MM-dd HH:mm:ss"
                        ></el-date-picker>
                      </el-form-item>
                    </el-col>
                  </el-form-item>

                  <el-form-item label="团有效期" prop="GroupTimeOut">
                    <el-row>
                      <el-col :span="3">
                        <el-select v-model="addRuleForm.GroupTimeOut[0]" placeholder="请选择">
                          <el-option v-for="(item, index) in 31" :key="index" :label="index" :value="index"> </el-option>
                        </el-select>
                      </el-col>
                      <el-col :span="1" class="text_center"> 天 </el-col>
                      <el-col :span="3">
                        <el-select v-model="addRuleForm.GroupTimeOut[1]" placeholder="请选择">
                          <el-option v-for="(item, index) in 24" :key="index" :label="index" :value="index"> </el-option>
                        </el-select>
                      </el-col>
                      <el-col :span="1" class="text_center"> 时 </el-col>
                      <el-col :span="3">
                        <el-select v-model="addRuleForm.GroupTimeOut[2]" placeholder="请选择">
                          <el-option v-for="(item, index) in 60" :key="index" :label="index" :value="index"> </el-option>
                        </el-select>
                      </el-col>
                      <el-col :span="1" class="text_center"> 分 </el-col>
                    </el-row>
                  </el-form-item>

                  <el-form-item label="参团限制" prop="GroupLimit">
                    <el-radio-group :disabled="Status == 20" v-model="addRuleForm.GroupLimit">
                      <el-radio :label="false">所有客户均可参团</el-radio>
                      <el-radio :label="true">仅新客参团</el-radio>
                    </el-radio-group>
                  </el-form-item>

                  <el-form-item label="活动限购" prop="Quota">
                    <el-col :span="7">
                      <el-radio-group :disabled="Status == 20" v-model="addRuleForm.isQuota">
                        <el-radio :label="false">不限</el-radio>
                        <el-radio :label="true">限购</el-radio>
                      </el-radio-group>
                    </el-col>
                    <el-col :span="5" v-if="addRuleForm.isQuota">
                      <el-input :disabled="Status == 20" size="small" min="1" placeholder="请输入" v-model="addRuleForm.Quota"></el-input>
                    </el-col>
                  </el-form-item>

                  <el-form-item label="活动商品" prop="GoodsID">
                    <el-dropdown v-if="addRuleForm.GoodsLst.length == 0">
                      <span class="el-dropdown-link"> 选择商品<i class="el-icon-arrow-down el-icon--right"></i> </span>
                      <el-dropdown-menu slot="dropdown">
                        <el-dropdown-item @click.native="selectGoodsClick('20')">项目</el-dropdown-item>
                        <el-dropdown-item @click.native="selectGoodsClick('10')">产品</el-dropdown-item>
                        <el-dropdown-item @click.native="selectGoodsClick('30')">通用次卡</el-dropdown-item>
                        <el-dropdown-item @click.native="selectGoodsClick('40')">时效卡</el-dropdown-item>
                      </el-dropdown-menu>
                    </el-dropdown>

                    <el-table v-if="addRuleForm.GoodsLst.length > 0" size="small" :data="addRuleForm.GoodsLst" tooltip-effect="light">
                      <el-table-column prop="Name" label="商品名称"></el-table-column>
                      <el-table-column prop="Price" label="商品价格">
                        <template slot-scope="scope">
                          {{ scope.row.Price | toFixed | NumFormat }}
                        </template>
                      </el-table-column>
                      <el-table-column prop="ActivityStock">
                        <div slot="header">
                          <span>活动库存</span>
                          <el-popover
                            content="客户开团即扣减成团所需库存，建议库存数量设置多一些（比如，客户丽丽开了 5 人团，库存数量会扣减 5，保证该团可以成团）"
                            placement="top-start"
                            width="300"
                            trigger="hover"
                          >
                            <i slot="reference" class="el-icon-info" style="color: #dcdfe6"></i>
                          </el-popover>
                        </div>
                        <template slot-scope="scope">
                          <el-form-item :prop="'GoodsLst.' + scope.$index + '.ActivityStock'" :rules="rules.ActivityStock">
                            <el-input
                              class="custom_input"
                              v-model="addRuleForm.GoodsLst[scope.$index].ActivityStock"
                              size="small"
                              type="number"
                              v-input-fixed="0"
                              style="width: 70%"
                            ></el-input>
                          </el-form-item>
                        </template>
                      </el-table-column>
                      <el-table-column label="操作" width="80px">
                        <template slot-scope="scope">
                          <el-button :disabled="Status == 20" type="primary" size="small" @click="handleRemoveGrouponClick(scope.row)" v-prevent-click
                            >删 除</el-button
                          >
                        </template>
                      </el-table-column>
                    </el-table>
                  </el-form-item>

                  <el-form-item label="凑团" prop="JoinGroup">
                    <el-switch :disabled="Status == 20" v-model="addRuleForm.JoinGroup" style="display: block; margin-top: 5px"></el-switch>
                    <span class="font_12 color_999"
                      >开启凑团后，对于未参团的买家，活动商品详情页会显示待成团的团列表，买家可以直接任选一个参团，提升成团率。</span
                    >
                  </el-form-item>

                  <el-form-item label="模拟成团" prop="GroupDynamic">
                    <el-switch :disabled="Status == 20" v-model="addRuleForm.GroupDynamic" style="display: block; margin-top: 5px"></el-switch>
                    <span class="font_12 color_999"
                      >开启模拟成团后，该团有效期结束时，人数未满的团，系统将会模拟“匿名买家”凑满人数，使该团成团。
                      只有已付款参团的真实买家才会获得商品，建议合理开启，以提高成团率。</span
                    >
                  </el-form-item>

                  <el-form-item label="分享文案" prop="ShareText">
                    <span :disabled="Status == 20" class="font_14">
                      <span class="color_999">[拼团人数]</span>
                      人拼团只要
                      <span class="color_999">[拼团价]</span>
                      元，快来一起拼团
                    </span>
                    <div style="background-color: f8f8f8">
                      <el-button size="small" type="primary" @click="customShareTextClick">自定义文案</el-button>
                      <span class="font_12 color_999 marlt_10">精彩的分享文案，可以有效提升拼团链接的打开几率。</span>
                      <div class="martp_10">
                        <el-input v-if="isCustomShareText" v-model="addRuleForm.ShareText" size="small" placeholder="请输入分享文案"></el-input>
                      </div>
                    </div>
                  </el-form-item>
                </el-scrollbar>
              </el-tab-pane>

              <el-tab-pane label="活动价格" name="2">
                <el-scrollbar class="add_el_scrollbar_height">
                  <el-col class="dis_flex flex_dir_row" :span="24" v-for="(item, index) in addRuleForm.GrouponPrice" :key="index">
                    <el-form-item label="拼团人数:" :prop="'GrouponPrice.' + index + '.PeopleNum'" :rules="rules.PeopleNum">
                      <el-input
                        style="display: inline-block; width: 100px"
                        type="number"
                        class="marlt_10 marrt_20 custom_input"
                        v-input-fixed="0"
                        placeholder="拼团人数"
                        v-model="item.PeopleNum"
                        size="small"
                      ></el-input>
                    </el-form-item>

                    <el-form-item label-width="20px" :prop="'GrouponPrice.' + index + '.GroupPrice'" :rules="rules.GroupPrice">
                      <el-input
                        type="number"
                        class="marlt_10 marrt_20 custom_input"
                        v-input-fixed
                        style="width: 150px"
                        placeholder="拼团价格"
                        v-model="item.GroupPrice"
                        size="small"
                      >
                        <template slot="append">元</template>
                      </el-input>
                    </el-form-item>

                    <el-form-item label-width="20px" :prop="'GrouponPrice.' + index + '.DriverPrice'">
                      <el-input
                        type="number"
                        class="marlt_10 marrt_20 custom_input"
                        v-input-fixed
                        style="width: 150px"
                        placeholder="团长价格"
                        v-model="item.DriverPrice"
                        size="small"
                      >
                        <template slot="append">元</template>
                      </el-input>
                    </el-form-item>

                    <el-form-item label-width="0">
                      <el-button
                        type="danger"
                        icon="el-icon-close"
                        circle
                        size="mini"
                        @click="removeGrouponPriceClick(index)"
                        v-if="index + 1 != 1"
                      ></el-button>
                      <el-button
                        type="primary"
                        icon="el-icon-plus"
                        circle
                        size="mini"
                        @click="addGrouponPriceClick"
                        v-if="index + 1 == addRuleForm.GrouponPrice.length"
                      ></el-button>
                    </el-form-item>
                  </el-col>
                </el-scrollbar>
              </el-tab-pane>

              <el-tab-pane label="活动门店" name="1">
                <div class="message el-message--info marbm_10">
                  <i class="el-message__icon el-icon-info"></i>
                  <p class="el-message__content">适用于同级所有节点，则只需勾选父节点。比如：适用于所有节点，只需勾选“顶级/第一个”节点。</p>
                </div>
                <el-scrollbar class="add_el_scrollbar_height">
                  <el-tree
                    ref="grouponEntity"
                    :expand-on-click-node="false"
                    :check-on-click-node="true"
                    :check-strictly="true"
                    :data="scopeData"
                    show-checkbox
                    node-key="ID"
                    :default-checked-keys="defaultCheckedKeys"
                    :default-expanded-keys="defaultExpandedKeys"
                    :props="defaultProps"
                  ></el-tree>
                </el-scrollbar>
              </el-tab-pane>
            </el-tabs>
          </el-form>
        </el-col>
      </el-row>
      <span slot="footer" class="dialog-footer">
        <el-button size="small" @click="dialogVisible = false" v-prevent-click>取 消</el-button>
        <el-button size="small" type="primary" @click="saveGrouponInfoClick" v-prevent-click>保 存</el-button>
      </span>
    </el-dialog>
 
    <!-- 新增选择指定商品弹出层 -->
    <el-dialog title="选择拼团商品" :visible.sync="selShopDialogVisible" width="900">
      <el-row>
        <el-col :span="22">
          <el-form
            :inline="true"
            size="small"
            label-width="auto"
            class="demo-form-inline"
            @keyup.enter.native="selectGoodsSearchHandleClick"
            @submit.native.prevent
          >
            <el-form-item label="商品名称">
              <el-input v-model="searchGoodsName" @clear="selectGoodsSearchHandleClick" clearable placeholder="输入商品线上名称搜索"></el-input>
            </el-form-item>
          </el-form>
        </el-col>
        <el-col :span="2">
          <el-button size="small" type="primary" @click="selectGoodsSearchHandleClick">搜索</el-button>
        </el-col>
      </el-row>
      <!-- <el-scrollbar class="el_scrollbar_height goods"> -->
      <el-table size="small" ref="multipleTable" :data="goodsList" tooltip-effect="light" height="55vh">
        <el-table-column prop="Name" label="商品名称"></el-table-column>
        <el-table-column prop="Price" label="销售价格">
          <template slot-scope="scope"> ￥{{ scope.row.Price | toFixed | NumFormat }} </template>
        </el-table-column>
        <el-table-column label="操作" width="80px">
          <template slot-scope="scope">
            <el-button type="primary" size="small" @click="selectGoodsClickItem(scope.row)" v-prevent-click>选择</el-button>
          </template>
        </el-table-column>
      </el-table>
      <!-- </el-scrollbar> -->
      <div class="pad_10 dis_flex flex_x_end" v-if="goodspaginations.total > 0">
        <el-pagination
          background
          :current-page.sync="goodspaginations.page"
          :layout="goodspaginations.layout"
          :total="goodspaginations.total"
          @current-change="goodscurrentChange"
        ></el-pagination>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import API from "@/api/Marketing/MarketingPlay/Groupon";
var dayjs = require('dayjs')
export default {
  name: "Groupon",
  /**  引入的组件  */
  components: {},
  /**  Vue 实例的数据对象**/
  data() {
    return {
      searchGoodsName:"",
      BeginDateTimeDisabled: {
        disabledDate(time) {
          return time.getTime() <= new Date(new Date().setDate(new Date().getDate() - 1)).getTime();
        },
      },
      EndDateTimeDisabled: {
        disabledDate(time) {
          return time.getTime() <= new Date(new Date().setDate(new Date().getDate() - 1)).getTime();
        },
      },

      loading: false,
      searchName: "", //线上名称
      Status: "", //状态
      activeName: "0",
      tableData: [],
      entityList: [],
      paginations: {
        page: 1, // 当前位于哪页
        total: 0, // 总数
        page_size: 10, // 1页显示多少条
        layout: "total, prev, pager, next,jumper", // 翻页属性
      },
      grouponImageName: ["groupon_1", "groupon_2", "groupon_3", "groupon_4"],
      DialogTitle: "",
      dialogVisible: false,
      selShopDialogVisible: false,
      addRuleForm: {
        Name: "", //活动名称
        DateTime: [new Date(), new Date()], //活动时间
        BeginDateTime: null, //开始时间
        EndDateTime: null, // 结束时间
        GroupTimeOut: [0, 0, 0], //活动有效期
        GroupTimeOutDay: "", //天
        GroupTimeOutHour: "", //小时
        GroupTimeOutMinuter: "", //分钟

        GroupLimit: "", //参团限制 （新客）
        Quota: "", //活动限购
        GoodsLst: [],
        GoodsType: "", //商品类型
        GoodsID: "", //商品ID
        ActivityStock: "", //活动库存
        JoinGroup: "", // 凑团
        GroupDynamic: "", // 模拟成团
        ShareText: "$N人拼团只要$P元，快来一起拼团", //分享文案
        GrouponEntity: [], //活动门店

        GrouponPrice: [
          {
            PeopleNum: "", //拼团人数
            GroupPrice: "", //拼团价
            DriverPrice: "", //团长价
          },
        ], //活动价格
      }, //新增编辑
      rules: {
        Name: [
          {
            required: true,
            message: "请输入活动名称",
            trigger: ["change", "blur"],
          },
        ], //活动名称
        DateTime: [
          {
            required: true,
            message: "请选择活动时间",
            trigger: ["change", "blur"],
          },
        ], //活动时间

        BeginDateTime: [
          {
            required: true,
            message: "请选择活动开始时间",
            trigger: ["change", "blur"],
          },
        ], //活动时间

        EndDateTime: [
          {
            required: true,
            message: "请选择活动结束时间",
            trigger: ["change", "blur"],
          },
        ], //活动时间
        GroupTimeOut: [
          {
            required: true,
            message: "请选择活动有效期",
            trigger: ["change", "blur"],
          },
        ], //活动有效期
        GroupLimit: [
          {
            required: true,
            message: "请选择参团限制",
            trigger: ["change", "blur"],
          },
        ], //参团限制 （新客）
        Quota: [
          {
            required: true,
            message: "请选择活动限制",
            trigger: ["change", "blur"],
          },
        ], //活动限购
        GoodsID: [
          {
            required: true,
            message: "请选择活动商品",
            trigger: ["change", "blur"],
          },
        ], //商品ID
        ActivityStock: [
          {
            required: true,
            message: "请输入活动库存",
            trigger: ["change", "blur"],
          },
        ], //活动库存
        EntityIDs: [
          {
            required: true,
            message: "请选择活动门店",
            trigger: ["change", "blur"],
          },
        ], //活动门店

        PeopleNum: [
          {
            required: true,
            message: "请输入拼团人数",
            trigger: ["change", "blur"],
          },
        ],
        GroupPrice: [
          {
            required: true,
            message: "请输入拼团价格",
            trigger: ["change", "blur"],
          },
        ],
        // DriverPrice: [ { required: true, message: "请输入团长价格", trigger: ["change","blur"] },],
      },
      scopeData: [], // 销售范围数据
      defaultCheckedKeys: [],
      defaultExpandedKeys: [1],
      defaultProps: {
        children: "Child",
        label: "EntityName",
      }, // 销售范围选择配置项
      goodsList: [],
      goodspaginations: {
        page: 1, // 当前位于哪页
        total: 0, // 总数
        page_size: 10, // 1页显示多少条
        layout: "total, prev, pager, next,jumper", // 翻页属性
      },
      goodsType: "",
      isCustomShareText: false,
      isAdd: false,
    };
  },
  /**计算属性  */
  computed: {},
  /**  方法集合  */
  methods: {
    //  起始时间改变的时候，将此之前的时间禁用
    beginDataChange(times) {
      if (times) {
        this.EndDateTimeDisabled = {
          disabledDate(time) {
            let endTime = dayjs(time).hour(17);
            return endTime < dayjs(times).valueOf();
          },
        };
      }
    },
    //  终止时间改变的时候，将此之后的时间禁用
    endDataChange(times) {
      if (times) {
        this.BeginDateTimeDisabled = {
          disabledDate(time) {
            let beginTime = dayjs(time).hour(8);
            return beginTime > dayjs(times).valueOf();
          },
        };
      }
    },

    getImgUrl(img) {
      return require("@/assets/img/store/" + img + ".png");
    },
    // 拼团搜索
    handleSearch() {
      let that = this;
      that.paginations.page = 1;
      that.groupon_all();
    },
    // 拼团列表分页
    currentChange(page) {
      let that = this;
      that.paginations.page = page;
      that.groupon_all();
    },

    /**    */
    addHandleGroupClick() {
      let that = this;
      that.DialogTitle = "新增拼团";
      that.Status = "";
      that.EndDateTimeDisabled = {};
      that.BeginDateTimeDisabled = {};
      that.addRuleForm = {
        Name: "", //活动名称
        DateTime: [new Date(that.$formatDate(new Date(), "YYYY-MM-DD") + " 08:00:00"), new Date(that.$formatDate(new Date(), "YYYY-MM-DD") + " 22:00:00")], //活动时间
        BeginDateTime: null, //开始时间
        EndDateTime: null, // 结束时间
        GroupTimeOut: [0, 0, 0], //活动有效期
        GroupTimeOutDay: "", //天
        GroupTimeOutHour: "", //小时
        GroupTimeOutMinuter: "", //分钟

        GroupLimit: false, //参团限制 （新客）
        isQuota: false, //活动限购
        Quota: 1, //活动限购
        GoodsLst: [],
        GoodsType: "", //商品类型
        GoodsID: "", //商品ID
        ActivityStock: "", //活动库存
        JoinGroup: false, // 凑团
        GroupDynamic: false, // 模拟成团
        ShareText: "$N人拼团只要$P元，快来一起拼团", //分享文案
        GrouponEntity: [], //活动门店

        GrouponPrice: [
          {
            PeopleNum: "", //拼团人数
            GroupPrice: "", //拼团价
            DriverPrice: "", //团长价
          },
        ], //活动价格
      };
      that.activeName = "0";
      that.scopeData = [];
      that.defaultCheckedKeys = [];
      that.defaultExpandedKeys = [1];
      Object.assign(that.scopeData, that.entityList);
      that.isAdd = true;
      that.dialogVisible = true;
    },

    // 选择活动商品
    selectGoodsClick(type) {
      let that = this;
      that.goodsList = [];
      that.goodspaginations.page = 1;
      that.goodsType = type;
      switch (type) {
        case "20":
          that.getProjectList();
          break;

        case "10":
          that.getProductList();
          break;

        case "30":
          that.getGeneralCardList();
          break;

        case "40":
          that.getTimeCardList();
          break;
      }
    },

    // 商品分页
    goodscurrentChange(page) {
      let that = this;
      that.goodspaginations.page = page;
      switch (that.goodsType) {
        case "20":
          that.getProjectList();
          break;

        case "10":
          that.getProductList();
          break;

        case "30":
          that.getGeneralCardList();
          break;

        case "40":
          that.getTimeCardList();
          break;
      }
    },
    /**  搜索商品  */
    selectGoodsSearchHandleClick() {
      let that = this;
      that.selectGoodsClick(that.goodsType);
    },

    /**  选择商品  */
    selectGoodsClickItem(row) {
      let that = this;
      that.addRuleForm.GoodsLst.push({
        Name: row.Name,
        Price: row.Price,
        ActivityStock: "",
        ID: row.ID,
      });
      that.addRuleForm.GoodsType = that.goodsType;
      that.addRuleForm.GoodsID = row.ID;
      that.selShopDialogVisible = false;
    },
    /**  删除 选择的商品  */
    handleRemoveGrouponClick(row) {
      let that = this;
      that.addRuleForm.GoodsLst = Array.from(that.addRuleForm.GoodsLst).filter((val) => val.ID != row.ID);
    },

    /**  添加活动价格  */
    addGrouponPriceClick() {
      let that = this;
      if (that.addRuleForm.GrouponPrice.length >= 3) {
        that.$message.error("拼团价格最多添加三阶", 1000);
        return;
      }
      that.addRuleForm.GrouponPrice.push({
        PeopleNum: "", //拼团人数
        GroupPrice: "", //拼团价
        DriverPrice: "", //团长价
      });
    },
    /**    */
    removeGrouponPriceClick(index) {
      let that = this;
      that.addRuleForm.GrouponPrice.splice(index, 1);
    },

    /**  自定义文案  */
    customShareTextClick() {
      let that = this;
      that.isCustomShareText = true;
    },

    /**  创建拼团 编辑拼团  */
    saveGrouponInfoClick() {
      let that = this;
      that.$refs.addRuleForm.validate((valid, errorMsg) => {
        if (valid) {
          if (that.isAdd) {
            that.groupon_create();
          } else {
            that.groupon_update();
          }
        } else {
          let errorItem = Object.keys(errorMsg);
          let msg = errorMsg[errorItem[0]][0].message;
          that.$message.error(msg ? msg : "请补全信息");
        }
      });
    },

    /**  编辑拼团信息  */
    editGrouponInfoClick(row) {
      let that = this;
      that.loading = true;
      that.activeName = "0";
      that.scopeData = [];
      that.defaultCheckedKeys = [];
      that.defaultExpandedKeys = [1];
      Object.assign(that.scopeData, that.entityList);

      that.groupon_info(row.ID);
    },

    /**************************************************    */
    /**  拼团列表  */
    async groupon_all() {
      let that = this;
      let params = {
        PageNum: that.paginations.page,
        Name: that.searchName,
        Status: that.Status,
      };
      that.loading = true;
      let res = await API.groupon_all(params);
      if (res.StateCode == 200) {
        that.tableData = res.List;
        that.paginations.total = res.Total;
      } else {
        that.$message.error(res.Message);
      }
      that.loading = false;
    },

    /**  拼团创建  */
    async groupon_create() {
      let that = this;
      let params = Object.assign({}, that.addRuleForm);
      params.Quota = params.isQuota ? params.Quota : 0;
      delete params.isQuota;
      // params.BeginDateTime = that.$formatDate(params.BeginDateTime,"YYYY-MM-DD hh:mm:ss");
      // params.EndDateTime = that.$formatDate(params.EndDateTime,"YYYY-MM-DD hh:mm:ss");
      // params.BeginDateTime = params.BeginDateTime ? params.DateTime[0] : new Date(that.$formatDate(new Date(), "YYYY-MM-DD") + " 08:00:00");
      // params.EndDateTime = params.DateTime ? params.DateTime[1] : new Date(that.$formatDate(new Date(), "YYYY-MM-DD") + " 22:00:00");
      delete params.DateTime;

      params.GroupTimeOutDay = params.GroupTimeOut ? params.GroupTimeOut[0] : "0";
      params.GroupTimeOutHour = params.GroupTimeOut ? params.GroupTimeOut[1] : "0";
      params.GroupTimeOutMinuter = params.GroupTimeOut ? params.GroupTimeOut[2] : "0";
      delete params.GroupTimeOut;

      params.ActivityStock = params.GoodsLst ? params.GoodsLst[0].ActivityStock : "0";
      delete params.GoodsLst;

      params.GrouponEntity = that.$refs.grouponEntity.getCheckedKeys();

      that.loading = true;
      let res = await API.groupon_create(params);
      if (res.StateCode == 200) {
        that.$message.success("创建成功");
        that.dialogVisible = false;
        that.handleSearch();
      } else {
        that.$message.error(res.Message);
      }
      that.loading = false;
    },

    /** 更新   */
    async groupon_update() {
      let that = this;
      let params = Object.assign({}, that.addRuleForm);
      params.Quota = params.isQuota ? params.Quota : 0;
      delete params.isQuota;
      params.BeginDateTime = params.DateTime ? params.DateTime[0] : new Date(that.$formatDate(new Date(), "YYYY-MM-DD") + " 08:00:00");
      params.EndDateTime = params.DateTime ? params.DateTime[1] : new Date(that.$formatDate(new Date(), "YYYY-MM-DD") + " 22:00:00");
      delete params.DateTime;

      params.GroupTimeOutDay = params.GroupTimeOut ? params.GroupTimeOut[0] : "0";
      params.GroupTimeOutHour = params.GroupTimeOut ? params.GroupTimeOut[1] : "0";
      params.GroupTimeOutMinuter = params.GroupTimeOut ? params.GroupTimeOut[2] : "0";
      delete params.GroupTimeOut;

      params.ActivityStock = params.GoodsLst ? params.GoodsLst[0].ActivityStock : "0";
      delete params.GoodsLst;

      params.GrouponEntity = that.$refs.grouponEntity.getCheckedKeys();

      let res = await API.groupon_update(params);
      if (res.StateCode == 200) {
        that.$message.success("保存成功");
        that.dialogVisible = false;
        that.handleSearch();
      } else {
        that.$message.error(res.Message);
      }
    },

    /** 详情结果   */
    async groupon_info(ID) {
      let that = this;
      let params = { ID: ID };
      let res = await API.groupon_info(params);
      // 详情结果
      if (res.StateCode == 200) {
        let tempRow = Object.assign({}, res.Data);
        that.addRuleForm = Object.assign(that.addRuleForm, tempRow);
        that.addRuleForm.DateTime = [that.addRuleForm.BeginDateTime, that.addRuleForm.EndDateTime];
        that.addRuleForm.GroupTimeOut = [that.addRuleForm.GroupTimeOutDay, that.addRuleForm.GroupTimeOutHour, that.addRuleForm.GroupTimeOutMinuter];
        that.addRuleForm.GoodsLst = [
          {
            Name: that.addRuleForm.GoodsName,
            Price: "",
            ActivityStock: that.addRuleForm.ActivityStock,
            ID: that.addRuleForm.GoodsID,
          },
        ];
        that.addRuleForm.isQuota = tempRow.Quota == 0 ? false : true;
        that.addRuleForm.ID = ID;
        that.DialogTitle = "编辑拼团";
        that.isAdd = false;
        that.dialogVisible = true;
      } else {
        that.$message.error(res.Message);
      }
      // 活动价格
      let res_price = await API.groupon_price(params);
      if (res_price.StateCode == 200) {
        that.addRuleForm.GrouponPrice = res_price.Data;
      } else {
        that.$message.error(res.Message);
      }
      // 适用门店
      let res_entity = await API.groupon_entity(params);
      if (res_entity.StateCode == 200) {
        that.defaultCheckedKeys = res_entity.Data;
        that.defaultCheckedKeys = res_entity.Data;
      } else {
        that.$message.error(res.Message);
      }

      that.loading = false;
    },

    // 新增时获取权限范围
    async getEntityList() {
      var that = this;
      var params = {
        SearchKey: "",
        Active: "",
      };
      let res = await API.getEntityList(params);
      if (res.StateCode == 200) {
        that.entityList = res.Data;
      } else {
        that.$message.error({
          message: res.Message,
          duration: 2000,
        });
      }
    },

    /**  项目列表  */
    async getProjectList() {
      let that = this;
      let params = {
        IsAllowSell: true,
        Name: that.searchGoodsName,
        PageNum: that.goodspaginations.page,
        ProjectBrandID: "",
        ProjectCategoryID: "",
      };
      let res = await API.getProjectList(params);
      if (res.StateCode == 200) {
        that.goodsList = res.List;
        that.goodspaginations.total = res.Total;
        that.selShopDialogVisible = true;
      } else {
        that.$message.error(res.Message);
      }
    },

    /**  产品列表  */
    async getProductList() {
      let that = this;
      let params = {
        IsAllowSell: true,
        Name: that.searchGoodsName,
        PageNum: that.goodspaginations.page,
        PCategoryID: "",
        ProductBrandID: "",
      };
      let res = await API.getProductList(params);
      if (res.StateCode == 200) {
        that.goodsList = res.List;
        that.goodspaginations.total = res.Total;
        that.selShopDialogVisible = true;
      } else {
        that.$message.error(res.Message);
      }
    },

    /**  通用次卡列表  */
    async getGeneralCardList() {
      let that = this;
      let params = {
        IsAllowSell: true,
        Name: that.searchGoodsName,
        PageNum: that.goodspaginations.page,
        GeneralCardCategoryID: "",
      };
      let res = await API.getGeneralCardList(params);
      if (res.StateCode == 200) {
        that.goodsList = res.List;
        that.goodspaginations.total = res.Total;
        that.selShopDialogVisible = true;
      } else {
        that.$message.error(res.Message);
      }
    },

    /**  时效卡列表  */
    async getTimeCardList() {
      let that = this;
      let params = {
        IsAllowSell: true,
        Name: that.searchGoodsName,
        PageNum: that.goodspaginations.page,
        TimeCardCategoryID: "",
      };
      let res = await API.getTimeCardList(params);
      if (res.StateCode == 200) {
        that.goodsList = res.List;
        that.goodspaginations.total = res.Total;
        that.selShopDialogVisible = true;
      } else {
        that.$message.error(res.Message);
      }
    },
  },
  /** 监听数据变化   */
  watch: {},
  /** 创建实例之前执行函数   */
  beforeCreate() {},
  /**  实例创建完成之后  */
  created() {
    let that = this;
    that.groupon_all();
    that.getEntityList();
  },
  /**  在挂载开始之前被调用  */
  beforeMount() {},
  /**  实例被挂载后调用  */
  mounted() {},
  /**  数据更新 之前 调用  */
  beforeUpdate() {},
  /** 数据更新 完成 调用   */
  updated() {},
  /**  实例销毁之前调用  */
  beforeDestroy() {},
  /**  实例销毁后调用  */
  destroyed() {},
};
</script>

<style lang="scss">
.Groupon {
  .el_scrollbar_height.new {
    // height: 60vh;
    #container {
      // width: 600px;
      height: 75vh;
    }
  }
  .el_scrollbar_height.goods {
    height: 55vh;
    // #container {
    //   width: 600px;
    //   height: 70vh;
    // }
    .el-scrollbar__wrap {
      overflow-x: hidden;
    }
  }

  .el-dropdown-link {
    cursor: pointer;
    color: var(--zl-color-orange-primary);
  }
  .el-icon-arrow-down {
    font-size: 13px;
  }
  .addGrouponClass {
    .add_el_scrollbar_height {
      height: 60vh;
      .el-scrollbar__wrap {
        overflow-x: hidden;
      }
    }

    .el-carousel__item:nth-child(2n) {
      background-color: #99a9bf;
    }

    .el-carousel__item:nth-child(2n + 1) {
      background-color: #d3dce6;
    }
  }
  .custom_input {
    .el-input__inner {
      padding: 0 0 0 15px;
    }
  }
}
</style>
