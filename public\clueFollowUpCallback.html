<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <title>巨量授权回调</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <style>
    body { font-family: Arial, sans-serif; text-align: center; padding: 40px; }
    .msg { font-size: 20px; margin-top: 40px; }
    .loading { color: #888; }
    .success { color: #4caf50; }
    .error { color: #f44336; }
  </style>
</head>
<body>
  <h2>授权处理中，请稍候...</h2>
  <div class="msg loading" id="msg">正在处理授权...</div>
  <script>
    function getQueryParam(name) {
      const url = window.location.search;
      const params = new URLSearchParams(url);
      return params.get(name);
    }

    const state = getQueryParam('state');
    const auth_code = getQueryParam('auth_code');
    const msgDiv = document.getElementById('msg');

    if (state && auth_code) {
      fetch('https://erp.37mei.com/api/clueDistribution/authCode?state=' + encodeURIComponent(state) + '&auth_code=' + encodeURIComponent(auth_code), {
        method: 'GET',
        credentials: 'include',
        headers: {
          'Accept': 'application/json'
        }
      })
      .then(response => response.json())
      .then(data => {
        if (data && data.StateCode === 200) {
          msgDiv.textContent = '授权成功！';
          msgDiv.className = 'msg success';
        } else {
          msgDiv.textContent = '授权失败：' + (data && data.msg ? data.msg : '未知错误');
          msgDiv.className = 'msg error';
        }
      })
      .catch(err => {
        msgDiv.textContent = '网络错误：' + err.message;
        msgDiv.className = 'msg error';
      });
    } else {
      msgDiv.textContent = '缺少必要参数';
      msgDiv.className = 'msg error';
    }
  </script>
</body>
</html>