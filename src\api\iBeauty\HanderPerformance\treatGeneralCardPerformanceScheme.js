/**
 * Created by wsf on 2022/01/10
 */
 import * as API from '@/api/index'

 export  default {
    // 获取员工通用次卡消耗业绩方案列表
    getTreatGeneralCardPerformanceScheme: params => {
        return API.POST('api/treatGeneralCardPerformanceScheme/list', params)
    },
    // 保存员工通用次卡消耗业绩方案
    createTreatGeneralCardPerformanceScheme: params => {
        return API.POST('api/treatGeneralCardPerformanceScheme/create', params)
    },
    // 删除员工通用次卡消耗业绩方案
    deleteTreatGeneralCardPerformanceScheme: params => {
        return API.POST('api/treatGeneralCardPerformanceScheme/delete', params)
    },
    // 获取通用次卡分类业绩
    getTreatGeneralCardCategoryPerformance: params => {
        return API.POST('api/treatGeneralCardCategoryPerformance/all', params)
    },
    // 保存通用次卡分类业绩
    updateTreatGeneralCardCategoryPerformance: params => {
        return API.POST('api/treatGeneralCardCategoryPerformance/update', params)
    },
    // 获取所有通用次卡经手人业绩
    getTreatGeneralCardSchemeHandlerPerformance: params => {
        return API.POST('api/treatGeneralCardSchemeHandlerPerformance/all', params)
    },
    // 保存所有通用次卡经手人业绩
    updateTreatGeneralCardSchemeHandlerPerformance: params => {
        return API.POST('api/treatGeneralCardSchemeHandlerPerformance/update', params)
    },
    // 获取分类通用次卡经手人业绩
    getTreatGeneralCardCategoryHandlerPerformance: params => {
        return API.POST('api/treatGeneralCardCategoryHandlerPerformance/all', params)
    },
    // 保存分类通用次卡经手人业绩
    updateTreatGeneralCardCategoryHandlerPerformance: params => {
        return API.POST('api/treatGeneralCardCategoryHandlerPerformance/update', params)
    },
    // 获取通用次卡业绩
    getTreatGeneralCardPerformance: params => {
        return API.POST('api/treatGeneralCardPerformance/all', params)
    },
    // 保存通用次卡业绩
    updateTreatGeneralCardPerformance: params => {
        return API.POST('api/treatGeneralCardPerformance/update', params)
    },
    // 获取通用次卡下的经手人业绩
    getTreatGeneralCardHandlerPerformance: params => {
        return API.POST('api/treatGeneralCardHandlerPerformance/all', params)
    },
    // 保存通用次卡下的经手人业绩
    updateTreatGeneralCardHandlerPerformance: params => {
        return API.POST('api/treatGeneralCardHandlerPerformance/update', params)
    },
    // 获取通用次卡项目业绩
    getTreatGeneralCardProjectPerformance: params => {
        return API.POST('api/treatGeneralCardProjectPerformance/all', params)
    },
    // 保存通用次卡项目业绩
    updateTreatGeneralCardProjectPerformance: params => {
        return API.POST('api/treatGeneralCardProjectPerformance/update', params)
    },
    // 获取通用次卡项目经手人业绩
    getTreatGeneralCardProjectHandlerPerformance: params => {
        return API.POST('api/treatGeneralCardProjectHandlerPerformance/all', params)
    },
    // 保存通用次卡项目经手人业绩
    updateTreatGeneralCardProjectHandlerPerformance: params => {
        return API.POST('api/treatGeneralCardProjectHandlerPerformance/update', params)
    }
 }