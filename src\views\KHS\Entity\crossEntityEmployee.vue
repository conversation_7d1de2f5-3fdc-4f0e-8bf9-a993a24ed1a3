<template>
  <div class="crossEntityEmployee content_body_nopadding">
    <el-tabs v-model="activeName" size="small" type="border-card">
      <el-tab-pane label="本店员工" name="ourStoreEmployee">
        <el-form ref="form" label-width="50px" size="small" :inline="true"
          @keyup.enter.native="searchBelongCurrentEntityNameClick" @submit.native.prevent>
          <el-form-item label="搜索">
            <el-input v-model="searchBelongCurrentEntityName" placeholder="请输入员工姓名、编号搜索" clearable
              @clear="searchBelongCurrentEntityNameClick"></el-input>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="searchBelongCurrentEntityNameClick">搜索</el-button>
          </el-form-item>
        </el-form>
        <el-table :data="belongCurrentEntityEmployee" size="small">
          <el-table-column prop="EmployeeID" label="员工编号"> </el-table-column>
          <el-table-column prop="EmployeeName" label="员工姓名"> </el-table-column>
          <el-table-column prop="JobName" label="职务"> </el-table-column>
        </el-table>

        <div class="pad_15 text_right">
          <el-pagination background v-if="belongCurrentEntityPaginations.total > 0"
            @current-change="belongCurrentEntityEmployeeChange" :current-page.sync="belongCurrentEntityPaginations.page"
            :page-size="belongCurrentEntityPaginations.page_size" :layout="belongCurrentEntityPaginations.layout"
            :total="belongCurrentEntityPaginations.total"></el-pagination>
        </div>
      </el-tab-pane>
      <el-tab-pane label="跨店员工" name="interStoreEmployee">
        <el-row>
          <el-col :span="22">
            <el-form ref="form" label-width="50px" size="small" :inline="true"
              @keyup.enter.native="searchBelongNotCurrentEntityNameClick" @submit.native.prevent>
              <el-form-item label="搜索">
                <el-input v-model="searchBelongNotCurrentEntityName" placeholder="请输入员工姓名、编号搜索" clearable
                  @clear="searchBelongNotCurrentEntityNameClick"></el-input>
              </el-form-item>

              <el-form-item>
                <el-button type="primary" @click="searchBelongNotCurrentEntityNameClick">搜索</el-button>
              </el-form-item>
            </el-form>
          </el-col>
          <el-col :span="2" class="text_right">
            <el-button @click="addInterStoreEmployeeClick" type="primary" size="small">新增跨店员工</el-button>
          </el-col>
        </el-row>
        <el-table :data="belongNotCurrentEntityEmployee" size="small">
          <el-table-column prop="EmployeeID" label="员工编号"> </el-table-column>
          <el-table-column prop="EmployeeName" label="员工姓名"> </el-table-column>
          <el-table-column prop="JobName" label="职务"> </el-table-column>
          <el-table-column label="操作" width="80">
            <template slot-scope="scope">
              <el-button type="danger" size="small" @click="removeNotCurrentEntityEmployee(scope.row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>

        <div class="pad_15 text_right">
          <el-pagination background v-if="belongNotCurrentEntityPaginations.total > 0"
            @current-change="belongNotCurrentEntityPaginationsChange"
            :current-page.sync="belongNotCurrentEntityPaginations.page"
            :page-size="belongNotCurrentEntityPaginations.page_size" :layout="belongNotCurrentEntityPaginations.layout"
            :total="belongNotCurrentEntityPaginations.total"></el-pagination>
        </div>
      </el-tab-pane>
    </el-tabs>

    <el-dialog :visible.sync="visibleInterStore" title="跨店员工">
      <el-form ref="form" label-width="50px" size="small" :inline="true"
        @keyup.enter.native="searchNotCurrentEntityNameClick" @submit.native.prevent>
        <el-form-item label="搜索">
          <el-input v-model="searchNotCurrentEntityName" placeholder="请输入员工姓名、编号搜索" clearable
            @clear="searchNotCurrentEntityNameClick"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="searchNotCurrentEntityNameClick">搜索</el-button>
        </el-form-item>
      </el-form>
      <el-table :data="notCurrentEntityEmployee" size="small">
        <el-table-column prop="EmployeeID" label="编号"> </el-table-column>
        <el-table-column prop="EmployeeName" label="姓名"> </el-table-column>
        <el-table-column prop="JobName" label="职务"> </el-table-column>
        <el-table-column label="操作" width="80">
          <template slot-scope="scope">
            <el-button size="small" type="primary" @click="addNotCurrentEntityClick(scope.row)"
              v-prevent-click>添加</el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="pad_15 text_right">
        <el-pagination background v-if="notCurrentEntityPaginations.total > 0"
          @current-change="notCurrentEntityPaginationsChange" :current-page.sync="notCurrentEntityPaginations.page"
          :page-size="notCurrentEntityPaginations.page_size" :layout="notCurrentEntityPaginations.layout"
          :total="notCurrentEntityPaginations.total"></el-pagination>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import API from "@/api/KHS/Entity/crossEntityEmployee.js";
export default {
  name: "CrossEntityEmployee",
  props: {},
  /** 监听数据变化   */
  watch: {},
  /**  引入的组件  */
  components: {},
  /**  Vue 实例的数据对象**/
  data() {
    return {
      visibleInterStore: false,
      activeName: "ourStoreEmployee",
      searchBelongCurrentEntityName: "",
      belongCurrentEntityEmployee: [],
      //需要给分页组件传的信息
      belongCurrentEntityPaginations: {
        page: 1, // 当前位于哪页
        total: 0, // 总数
        page_size: 10, // 1页显示多少条
        layout: "total, prev, pager, next,jumper", // 翻页属性
      },
      searchBelongNotCurrentEntityName: "",
      belongNotCurrentEntityEmployee: [],
      belongNotCurrentEntityPaginations: {
        page: 1, // 当前位于哪页
        total: 0, // 总数
        page_size: 10, // 1页显示多少条
        layout: "total, prev, pager, next,jumper", // 翻页属性
      },
      searchNotCurrentEntityName: "",
      notCurrentEntityEmployee: [],
      notCurrentEntityPaginations: {
        page: 1, // 当前位于哪页
        total: 0, // 总数
        page_size: 10, // 1页显示多少条
        layout: "total, prev, pager, next,jumper", // 翻页属性
      },
    };
  },
  /**计算属性  */
  computed: {},
  /**  方法集合  */
  methods: {
    /**    */
    addInterStoreEmployeeClick() {
      let that = this;
      that.searchNotCurrentEntityName = "";
      that.visibleInterStore = true;
      that.employee_belongCurrentNoEntity();
    },
    /**    */
    searchBelongCurrentEntityNameClick() {
      let that = this;
      that.belongCurrentEntityPaginations.page = 1;
      that.employee_belongCurrentEntity();
    },
    /**    */
    belongCurrentEntityEmployeeChange(page) {
      let that = this;
      that.belongCurrentEntityPaginations.page = page;
      that.employee_belongCurrentEntity();
    },
    /**    */
    searchBelongNotCurrentEntityNameClick() {
      let that = this;
      that.belongNotCurrentEntityPaginations.page = 1;
      that.employee_belongCurrentNoPrimaryEntity();
    },
    /**    */
    belongNotCurrentEntityPaginationsChange(page) {
      let that = this;
      that.belongNotCurrentEntityPaginations.page = page;
      that.employee_belongCurrentNoPrimaryEntity();
    },
    /**    */
    removeNotCurrentEntityEmployee(item) {
      let that = this;
      that
        .$confirm("确定将要删除当前跨店员工？", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
        .then(() => {
          that.employee_deleteBelongEntity(item.EmployeeID);
        })
        .catch(() => {
          that.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    /**    */
    notCurrentEntityPaginationsChange(page) {
      let that = this;
      that.notCurrentEntityPaginations.page = page;
      that.employee_belongCurrentNoEntity();
    },
    /**    */
    searchNotCurrentEntityNameClick() {
      let that = this;
      that.notCurrentEntityPaginations.page = 1;
      that.employee_belongCurrentNoEntity();
    },
    /**    */
    addNotCurrentEntityClick(item) {
      let that = this;
      that.employee_createBelongEntity(item.EmployeeID);
    },
    /* •••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••• */
    /** 本店员工   */
    async employee_belongCurrentEntity() {
      let that = this;
      try {
        let params = {
          PageNum: that.belongCurrentEntityPaginations.page,
          SearchKey: that.searchBelongCurrentEntityName,
        };
        let res = await API.employee_belongCurrentEntity(params);
        if (res.StateCode == 200) {
          that.belongCurrentEntityEmployee = res.List;
          that.belongCurrentEntityPaginations.total = res.Total;
        } else {
          that.$message.error(res.Message);
        }
      } catch (error) {
        that.$message.error(error);
      }
    },
    /**  跨店员工  */
    async employee_belongCurrentNoPrimaryEntity() {
      let that = this;
      try {
        let params = {
          PageNum: that.belongNotCurrentEntityPaginations.page,
          SearchKey: that.searchBelongNotCurrentEntityName,
        };
        let res = await API.employee_belongCurrentNoPrimaryEntity(params);
        if (res.StateCode == 200) {
          that.belongNotCurrentEntityEmployee = res.List;
          that.belongNotCurrentEntityPaginations.total = res.Total;
        } else {
          that.$message.error(res.Message);
        }
      } catch (error) {
        that.$message.error(error);
      }
    },

    /**  跨店员工-可添加的员工  */
    async employee_belongCurrentNoEntity() {
      let that = this;
      try {
        let params = {
          PageNum: that.notCurrentEntityPaginations.page,
          SearchKey: that.searchNotCurrentEntityName,
        };
        let res = await API.employee_belongCurrentNoEntity(params);
        if (res.StateCode == 200) {
          that.notCurrentEntityEmployee = res.List;
          that.notCurrentEntityPaginations.total = res.Total;
        } else {
          that.$message.error(res.Message);
        }
      } catch (error) {
        that.$message.error(error);
      }
    },
    /** 添加跨店员工   */
    async employee_createBelongEntity(EmployeeID) {
      let that = this;
      try {
        let params = {
          EmployeeID: EmployeeID,
        };
        let res = await API.employee_createBelongEntity(params);
        if (res.StateCode == 200) {
          that.visibleInterStore = false;
          that.$message.success("操作成功");
          that.employee_belongCurrentNoPrimaryEntity();
        } else {
          that.$message.error(res.Message);
        }
      } catch (error) {
        that.$message.error(error);
      }
    },
    /**  删除跨店员工  */
    async employee_deleteBelongEntity(EmployeeID) {
      let that = this;
      try {
        let params = {
          EmployeeID: EmployeeID,
        };
        let res = await API.employee_deleteBelongEntity(params);
        if (res.StateCode == 200) {
          that.$message.success("操作成功");
          that.employee_belongCurrentNoPrimaryEntity();
        } else {
          that.$message.error(res.Message);
        }
      } catch (error) {
        that.$message.error(error);
      }
    },
  },
  /** 创建实例之前执行函数   */
  beforeCreate() { },
  /**  实例创建完成之后  */
  created() {
    let that = this;
    that.employee_belongCurrentEntity();
    that.employee_belongCurrentNoPrimaryEntity();
  },
  /**  在挂载开始之前被调用  */
  beforeMount() { },
  /**  实例被挂载后调用  */
  mounted() { },
  /**  数据更新 之前 调用  */
  beforeUpdate() { },
  /** 数据更新 完成 调用   */
  updated() { },
  /**  实例销毁之前调用  */
  beforeDestroy() { },
  /**  实例销毁后调用  */
  destroyed() { },
};
</script>

<style lang="scss">
.crossEntityEmployee {

  .el-tabs--border-card {
    border: 0px !important;
    box-shadow: 0 0px 0px 0 rgba(0, 0, 0, 0), 0 0 0px 0 rgba(0, 0, 0, 0);
  }
}
</style>
