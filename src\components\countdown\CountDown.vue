<template>
  <div class="countDown_ ">
    <slot 
    :days="days" 
    :hours="hours" 
    :minutes="minutes" 
    :seconds="seconds"
    :milliseconds="milliseconds" 
    :totalDays="totalDays"
    :totalHours="totalHours"
    :totalMinutes="totalMinutes"
    :totalSeconds="totalSeconds"
    :totalMilliseconds="totalMilliseconds"
    ></slot>
  </div>
</template>

<script>

var MILLISECONDS_SECOND = 1000;
var MILLISECONDS_MINUTE = 60 * MILLISECONDS_SECOND;
var MILLISECONDS_HOUR = 60 * MILLISECONDS_MINUTE;
var MILLISECONDS_DAY = 24 * MILLISECONDS_HOUR;
var EVENT_ABORT = 'abort';
var EVENT_END = 'end';
var EVENT_PROGRESS = 'progress';
var EVENT_START = 'start';
var EVENT_VISIBILITY_CHANGE = 'visibilitychange';

export default {
  name: 'VueCountdown',
    props: {
        /**
         * Starts the countdown automatically when initialized.
         */
        autoStart: {
            type: Boolean,
            "default": true
        },
        /**
         * Emits the countdown events.
         */
        emitEvents: {
            type: Boolean,
            "default": true
        },
        /**
         * The interval time (in milliseconds) of the countdown progress.
         */
        interval: {
            type: Number,
            "default": 1000,
            validator: function (value) { return value >= 0; }
        },
        /**
         * Generate the current time of a specific time zone.
         */
        now: {
            type: Function,
            "default": function () { return Date.now(); }
        },
        /**
         * The tag name of the component's root element.
         */
        tag: {
            type: String,
            "default": 'span'
        },
        /**
         * The time (in milliseconds) to count down from.
         */
        time: {
            type: Number,
            "default": 0,
            validator: function (value) { return value >= 0; }
        },
        /**
         * Transforms the output props before render.
         */
        transform: {
            type: Function,
            "default": function (props) { return props; }
        }
    },
    data: function () {
        return {
            /**
             * It is counting down.
             * @type {boolean}
             */
            counting: false,
            /**
             * The absolute end time.
             * @type {number}
             */
            endTime: 0,
            /**
             * The remaining milliseconds.
             * @type {number}
             */
            totalMilliseconds: 0,
            /**
             * The request ID of the requestAnimationFrame.
             * @type {number}
             */
            requestId: 0
        };
    },
    computed: {
        /**
         * Remaining days.
         * @returns {number} The computed value.
         */
        days: function () {
            return Math.floor(this.totalMilliseconds / MILLISECONDS_DAY);
        },
        /**
         * Remaining hours.
         * @returns {number} The computed value.
         */
        hours: function () {
            return Math.floor((this.totalMilliseconds % MILLISECONDS_DAY) / MILLISECONDS_HOUR);
        },
        /**
         * Remaining minutes.
         * @returns {number} The computed value.
         */
        minutes: function () {
            return Math.floor((this.totalMilliseconds % MILLISECONDS_HOUR) / MILLISECONDS_MINUTE);
        },
        /**
         * Remaining seconds.
         * @returns {number} The computed value.
         */
        seconds: function () {
            return Math.floor((this.totalMilliseconds % MILLISECONDS_MINUTE) / MILLISECONDS_SECOND);
        },
        /**
         * Remaining milliseconds.
         * @returns {number} The computed value.
         */
        milliseconds: function () {
            return Math.floor(this.totalMilliseconds % MILLISECONDS_SECOND);
        },
        /**
         * Total remaining days.
         * @returns {number} The computed value.
         */
        totalDays: function () {
            return this.days;
        },
        /**
         * Total remaining hours.
         * @returns {number} The computed value.
         */
        totalHours: function () {
            return Math.floor(this.totalMilliseconds / MILLISECONDS_HOUR);
        },
        /**
         * Total remaining minutes.
         * @returns {number} The computed value.
         */
        totalMinutes: function () {
            return Math.floor(this.totalMilliseconds / MILLISECONDS_MINUTE);
        },
        /**
         * Total remaining seconds.
         * @returns {number} The computed value.
         */
        totalSeconds: function () {
            return Math.floor(this.totalMilliseconds / MILLISECONDS_SECOND);
        }
    },
    watch: {
        $props: {
            deep: true,
            immediate: true,
            /**
             * Update the countdown when props changed.
             */
            handler: function () {
                this.totalMilliseconds = this.time;
                this.endTime = this.now() + this.time;
                if (this.autoStart) {
                    this.start();
                }
            }
        }
    },
    mounted: function () {
      document.addEventListener(EVENT_VISIBILITY_CHANGE, this.handleVisibilityChange);
      // this.$slots["default"] = {
      //           days: this.days,
      //           hours: this.hours,
      //           minutes: this.minutes,
      //           seconds: this.seconds,
      //           milliseconds: this.milliseconds,
      //           totalDays: this.totalDays,
      //           totalHours: this.totalHours,
      //           totalMinutes: this.totalMinutes,
      //           totalSeconds: this.totalSeconds,
      //           totalMilliseconds: this.totalMilliseconds
      //       }
    },
    beforeDestroy: function () {
        document.removeEventListener(EVENT_VISIBILITY_CHANGE, this.handleVisibilityChange);
        this.pause();
    },
    methods: {
        /**
         * Starts to countdown.
         * @public
         * @emits Countdown#start
         */
        start: function () {
            if (this.counting) {
                return;
            }
            this.counting = true;
            if (this.emitEvents) {
                /**
                 * Countdown start event.
                 * @event Countdown#start
                 */
                this.$emit(EVENT_START);
            }
            if (document.visibilityState === 'visible') {
                this["continue"]();
            }
        },
        /**
         * Continues the countdown.
         * @private
         */
        "continue": function () {
            var _this = this;
            if (!this.counting) {
                return;
            }
            var delay = Math.min(this.totalMilliseconds, this.interval);
            if (delay > 0) {
                var init_1;
                var prev_1;
                var step_1 = function (now) {
                    if (!init_1) {
                        init_1 = now;
                    }
                    if (!prev_1) {
                        prev_1 = now;
                    }
                    var range = now - init_1;
                    if (range >= delay
                        // Avoid losing time about one second per minute (now - prev ≈ 16ms) (#43)
                        || range + ((now - prev_1) / 2) >= delay) {
                        _this.progress();
                    }
                    else {
                        _this.requestId = requestAnimationFrame(step_1);
                    }
                    prev_1 = now;
                };
                this.requestId = requestAnimationFrame(step_1);
            }
            else {
                this.end();
            }
        },
        /**
         * Pauses the countdown.
         * @private
         */
        pause: function () {
            cancelAnimationFrame(this.requestId);
        },
        /**
         * Progresses to countdown.
         * @private
         * @emits Countdown#progress
         */
        progress: function () {
            if (!this.counting) {
                return;
            }
            this.totalMilliseconds -= this.interval;
            if (this.emitEvents && this.totalMilliseconds > 0) {
                /**
                 * Countdown progress event.
                 * @event Countdown#progress
                 */
                this.$emit(EVENT_PROGRESS, {
                    days: this.days,
                    hours: this.hours,
                    minutes: this.minutes,
                    seconds: this.seconds,
                    milliseconds: this.milliseconds,
                    totalDays: this.totalDays,
                    totalHours: this.totalHours,
                    totalMinutes: this.totalMinutes,
                    totalSeconds: this.totalSeconds,
                    totalMilliseconds: this.totalMilliseconds
                });
            }
            this["continue"]();
        },
        /**
         * Aborts the countdown.
         * @public
         * @emits Countdown#abort
         */
        abort: function () {
            if (!this.counting) {
                return;
            }
            this.pause();
            this.counting = false;
            if (this.emitEvents) {
                /**
                 * Countdown abort event.
                 * @event Countdown#abort
                 */
                this.$emit(EVENT_ABORT);
            }
        },
        /**
         * Ends the countdown.
         * @public
         * @emits Countdown#end
         */
        end: function () {
            if (!this.counting) {
                return;
            }
            this.pause();
            this.totalMilliseconds = 0;
            this.counting = false;
            if (this.emitEvents) {
                /**
                 * Countdown end event.
                 * @event Countdown#end
                 */
                this.$emit(EVENT_END);
            }
        },
        /**
         * Updates the count.
         * @private
         */
        update: function () {
            if (this.counting) {
                this.totalMilliseconds = Math.max(0, this.endTime - this.now());
            }
        },
        /**
         * visibility change event handler.
         * @private
         */
        handleVisibilityChange: function () {
            switch (document.visibilityState) {
                case 'visible':
                    this.update();
                    this["continue"]();
                    break;
                case 'hidden':
                    this.pause();
                    break;
                default:
            }
        }
    },
}
</script>

<style lang="scss">

.countDown_{

 }
</style>
