@font-face {
  font-family: "iconfont"; /* Project id 2802124 */
  src: url('iconfont.woff2?t=1631183103003') format('woff2'),
       url('iconfont.woff?t=1631183103003') format('woff'),
       url('iconfont.ttf?t=1631183103003') format('truetype');
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.el-iconfont {
  font-family: "iconfont" !important;
  font-weight:500;
  font-variant:normal;
  line-height:1px;
  font-style: normal;
  margin-right: 5px;
  vertical-align:baseline;
  display:inline-block;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-mini-program-fill:before {
  content: "\e909";
}

