/**
 * Created by preference on 2023/05/08
 *  zmx
 */

import * as API from "@/api/index";
export default {
  /**  处方列表 */
  prescriptionBill_list: (params) => {
    return API.POST("api/prescriptionBill/list", params);
  },
  /**  处方列表 */
  customer_prescriptionBill: (params) => {
    return API.POST("api/customer/prescriptionBill", params);
  },
  /** 处方详情  */
  prescriptionBill_detail: (params) => {
    return API.POST("api/prescriptionBill/detail", params);
  },
  /**  科室列表 */
  prescriptionBill_department: (params) => {
    return API.POST("api/prescriptionBill/department", params);
  },
  /**  仓库列表 */
  entity_warehouseByEntity: (params) => {
    return API.POST("api/entity/warehouseByEntity", params);
  },
  /**  药品列表 */
  stock_entityMedicineList: (params) => {
    return API.POST("api/stock/entityMedicineList", params);
  },
  /** 创建处方  */
  prescriptionBill_create: (params) => {
    return API.POST("api/prescriptionBill/create", params);
  },
  /**  处方提交 */
  prescriptionBill_submit: (params) => {
    return API.POST("api/prescriptionBill/submit", params);
  },
  /**  处方收款未发药 */
  prescriptionBill_pay: (params) => {
    return API.POST("api/prescriptionBill/pay", params);
  },
  /**  处方取消 */
  prescriptionBill_cancel: (params) => {
    return API.POST("api/prescriptionBill/cancel", params);
  },
  /**  处方作废 */
  prescriptionBill_cancellation: (params) => {
    return API.POST("api/prescriptionBill/cancellation", params);
  },
  /** 处方收款发药  */
  prescriptionBill_dispenseMedicine: (params) => {
    return API.POST("api/prescriptionBill/dispenseMedicine", params);
  },
  /** 医生列表  */
  customerMedicalRecord_doctor: (params) => {
    return API.POST("api/customerMedicalRecord/doctor", params);
  },
  //  查找会员
  getSaleCustomer: (params) => {
    return API.POST("api/saleCustomer/customer", params);
  },
  //  查找会员
  customerMedicalRecord_search: (params) => {
    return API.POST("api/customerMedicalRecord/search", params);
  },

};
