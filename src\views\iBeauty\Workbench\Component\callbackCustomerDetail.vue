<template>
  <div class="workbenchCustomerDetail">
    <el-drawer v-drawerDrag:changeMinWidth="changeMinWidth" :visible.sync="show" @close="closeDialog" :wrapperClosable="false" custom-class="custom-class-drawer" :size="drawerSize" :show-close="false">
      <div slot="title" class="dis_flex flex_x_between">
        <div></div>
        <div class="text_right" style="width: 100px">
          <i @click="changeDrawerSize" class="el-icon-rank font_24 marrt_15"></i>
          <i @click="closeDrawerClick" class="el-icon-close font_24"></i>
        </div>
      </div>
      <el-tabs class="custom-tabs-class" v-model="tabPane" @tab-click="handleClick">
        <el-tab-pane label="基本档案" name="0">
          <workbench-customer-basic-files v-if="customerID" :customerID="customerID" :isCustomerPhoneNumberView="isCustomerPhoneNumberView" :isCustomerPhoneNumberModify="isCustomerPhoneNumberModify" ref="customerbasicfiles"></workbench-customer-basic-files>
        </el-tab-pane>
        <el-tab-pane label="跟进记录" name="1">
          <workbench-followUpRecord v-if="customerID" :customerID="customerID" :isCallBack="isCallBack" ref="followUpRecord"> </workbench-followUpRecord>
        </el-tab-pane>
        <!-- <el-tab-pane label="企微会话" name="8">
          <workbenchDialogue v-if="customerID" :customerID="customerID" ref="workbenchDialogue"></workbenchDialogue>
        </el-tab-pane> -->
        <el-tab-pane label="卡项信息" name="2">
          <workbench-customer-account v-if="customerID" :customerID="customerID" ref="customerAccount"></workbench-customer-account>
        </el-tab-pane>
        <el-tab-pane label="订单信息" name="3">
          <workbench-customer-bill v-if="customerID" :customerID="customerID" ref="customerBill"></workbench-customer-bill>
        </el-tab-pane>
        <el-tab-pane label="预约记录" name="4">
          <workbench-appointment-record v-if="customerID" :customerID="customerID" ref="customerAppointmentRecord"></workbench-appointment-record>
        </el-tab-pane>
        <el-tab-pane label="服务日志" name="5">
          <workbench-customer-nursing-log v-if="customerID" :customerID="customerID" ref="customerNursingLog"></workbench-customer-nursing-log>
        </el-tab-pane>
        <el-tab-pane label="文件档案" name="6">
          <workbench-customer-file-upload v-if="customerID" :customerID="customerID" :isDeleteFile="isDeleteFile" ref="coustomerFileUpload"></workbench-customer-file-upload>
        </el-tab-pane>
        <el-tab-pane label="历史病历" name="8">
          <workbench-medical-record v-if="customerID && tabPane == 8" :CustomerID="customerID" :CustomerName="customerName" :CustomerCases="CustomerCases" ref="workbenchMedicalRecord"></workbench-medical-record>
        </el-tab-pane>
        <el-tab-pane label="客户处方" name="9">
          <workbench-prescription v-if="customerID" :CustomerID="customerID" :CustomerName="customerName" :CustomerCases="CustomerCases" ref="workbenchOrescriptionRef"></workbench-prescription>
        </el-tab-pane>
        <el-tab-pane label="对比照" name="10">
          <workbench-effect-comparison v-if="customerID" :CustomerID="customerID" :CustomerName="customerName" :CustomerCases="CustomerCases" ref="workbenchOrescriptionRef"></workbench-effect-comparison>
        </el-tab-pane>
        <el-tab-pane label="皮肤数据预览" name="11" style="height: 100%" v-if="yjEntity">
          <iframe :src="'https://yanjia.xiaofutech.com/access_detail?scope=OPEN:basic&channel_id=' + yjEntity.YJInstitutionID + '&store_id=' + yjEntity.YJEntityID + '&openid=' + customerID" frameborder="0" style="width: 100%; height: 500px" id="pIframe"></iframe>e>
        </el-tab-pane>
        <el-tab-pane label="通话记录" name="14">
          <workbenchCallBackLog :CustomerID="customerID" ref="customerCallBackLogRef"></workbenchCallBackLog>
        </el-tab-pane>
      </el-tabs>
    </el-drawer>
  </div>
</template>

<script>
import workbenchFollowUpRecord from "@/views/iBeauty/Workbench/Component/workbenchFollowUpRecord";
import workbenchAppointmentRecord from "@/views/iBeauty/Workbench/Component/workbenchAppointmentRecord";
import workbenchCustomerFileUpload from "@/views/iBeauty/Workbench/Component/workbenchCustomerFileUpload";
import workbenchCustomerAccount from "@/views/iBeauty/Workbench/Component/workbenchCustomerAccount";
import workbenchCustomerBasicFiles from "@/views/iBeauty/Workbench/Component/workbenchCustomerBasicFiles.vue";
import workbenchCustomerBill from "@/views/iBeauty/Workbench/Component/workbenchCustomerBill";
import workbenchCustomerNursingLog from "@/views/iBeauty/Workbench/Component/workbenchCustomerNursingLog";
import workbenchMedicalRecord from "@/views/iBeauty/Workbench/Component/workbenchMedicalRecord";
import workbenchPrescription from "@/views/iBeauty/Workbench/Component/workbenchPrescription";
import workbenchEffectComparison from "@/views/iBeauty/Workbench/Component/workbenchEffectComparison";
import workbenchCallBackLog from "@/views/iBeauty/Workbench/Component/workbenchCallBackLog.vue";
import cusAPI from "@/api/CRM/Customer/customer";

export default {
  name: "workbenchCustomerDetail",
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    customerID: {
      type: Number,
      require: true,
    },
    customerName: {
      type: String,
      require: true,
    },
    isCallBack: {
      type: Boolean,
    },
    isCustomerPhoneNumberView: {
      type: Boolean,
      default: false,
    },
    isCustomerPhoneNumberModify: {
      type: Boolean,
      default: false,
    },
    CustomerCases: {
      Type: Boolean,
      default() {
        return {
          Add: false,
          Update: false,
          Delete: false,
          SelectStencil: false,
          SaveStencil: false,
          DeleteStencil: false,
          PrescriptionAdd: false,
          PrescriptionUpdate: false,
        };
      },
    },
  },
  /** 监听数据变化   */
  watch: {
    visible: {
      immediate: true,
      handler(val) {
        this.show = val;
        this.tabPane = "0";
        this.isFullscreen = false;
        this.drawerSize = "60%";
      },
    },
    customerID: {
      immediate: true,
      handler(val) {
        if (val) {
          this.$nextTick(() => {
            this.handleClick();
          });
        }
      },
    },
  },
  /**  引入的组件  */
  components: {
    workbenchFollowUpRecord,
    workbenchAppointmentRecord,
    workbenchCustomerFileUpload,
    workbenchCustomerAccount,
    workbenchCustomerBasicFiles,
    workbenchCustomerBill,
    workbenchCustomerNursingLog,
    workbenchMedicalRecord,
    workbenchPrescription,
    workbenchEffectComparison,
    workbenchCallBackLog,
  },
  /**  Vue 实例的数据对象**/
  data() {
    return {
      show: false,
      moreShow: true,
      isDeleteFile: false,
      tabPane: "0",
      circleUrl: "https://cube.elemecdn.com/3/7c/********************************.png", //默认头像
      drawerSize: "60%",
      isFullscreen: false,
      yjEntity: "",
    };
  },
  /**计算属性  */
  computed: {},
  /**  方法集合  */
  methods: {
    /**    */
    changeMinWidth() {
      // console.log("==========");
    },
    /**  全品按钮  */
    changeDrawerSize() {
      let that = this;
      that.isFullscreen = !that.isFullscreen;
      if (that.isFullscreen) {
        that.drawerSize = "100%";
      } else {
        that.drawerSize = "60%";
      }
    },
    /**    */
    closeDrawerClick() {
      let that = this;
      that.show = false;
    },
    /**    */
    closeDialog() {
      let that = this;
      that.$emit("update:visible", false);
    },
    showConsumeInfo() {
      this.moreShow = !this.moreShow;
    },
    /* 切换 */
    handleClick() {
      let that = this;
      let tabPane = this.tabPane;
      if (that.customerID) {
        switch (tabPane) {
          case "0":
            {
              that.$refs.customerbasicfiles.getCustomerInfoData();
            }
            break;

          case "1":
            {
              that.$refs.followUpRecord.getCustomerFollowUp();
            }
            break;

          case "2":
            {
              that.$refs.customerAccount.activeName = "0";
              that.$refs.customerAccount.handleClick();
            }
            break;

          case "3":
            {
              that.$refs.customerBill.searchSaleBill();
            }
            break;

          case "4":
            {
              that.$refs.customerAppointmentRecord.getAppointmentRecordList();
            }
            break;

          case "5":
            {
              that.$refs.customerNursingLog.clearListData();
              that.$refs.customerNursingLog.getNursingLogList();
            }
            break;

          case "6":
            {
              that.$refs.coustomerFileUpload.claerCoustomerFileUploadData();
              that.$refs.coustomerFileUpload.getCoustomerFileUploadData();
            }
            break;

          case "7":
            {
              that.$refs.customerElectronicMedicalRecord.claerElectronicMedicalRecordData();
              that.$refs.customerElectronicMedicalRecord.getElectronicMedicalRecordData();
            }
            break;
          case "8":
            that.$nextTick(() => {
              that.$refs.workbenchMedicalRecord.initWorkbenchMedicakRecordData();
            });

            break;
          case "9":
            that.$nextTick(() => {
              that.$refs.workbenchOrescriptionRef.initWorkbenchPrescriptionData();
            });

            break;
          case "10":
            that.$nextTick(() => {
              that.$refs.effectComparison.CustomerID = that.customerID;
              that.$refs.effectComparison.photoCompare();
              that.$refs.effectComparison.clearData();
            });
            break;

          case "14":
            that.$refs.customerCallBackLogRef.phoneCallBack_callBackLog();
            break;
        }
      }
    },
    /**  已弃用的API  */
    // customer_yjEntity() {
    //   let that = this;
    //   let params = {};
    //   cusAPI
    //     .customer_yjEntity(params)
    //     .then((res) => {
    //       if (res.StateCode == 200) {
    //         that.yjEntity = res.Data;
    //       } else {
    //         that.$message.error(res.Message);
    //       }
    //     })
    //     .catch((fail) => {
    //       that.$message.error(fail);
    //     });
    // },
  },
  /** 创建实例之前执行函数   */
  beforeCreate() {},
  /**  实例创建完成之后  */
  created() {},
  /**  在挂载开始之前被调用  */
  beforeMount() {},
  /**  实例被挂载后调用  */
  mounted() {
    // this.customer_yjEntity(); // 已弃用的API调用
    /**  删除文件档案中文件权限  */
    this.isDeleteFile = this.$permission.getCustomerDetailPermission(this.$router, "iBeauty-Customer-Customer-DeleteFile");
  },
  /**  数据更新 之前 调用  */
  beforeUpdate() {},
  /** 数据更新 完成 调用   */
  updated() {},
  /**  实例销毁之前调用  */
  beforeDestroy() {},
  /**  实例销毁后调用  */
  destroyed() {},
};
</script>

<style lang="scss">
.workbenchCustomerDetail {
  min-width: 600px;
  height: 100%;
  max-width: 100vw;
  .custom-class-drawer {
    .el-drawer__header {
      margin-bottom: unset;
      padding-top: 5px;
      padding-bottom: 5px;
      background: #f5f7fa;
    }
    .el-drawer__body {
      padding: 8px;
    }
  }
  .custom-tabs-class {
    display: flex;
    flex-direction: column;
    height: 100%;
    .el-tabs__content {
      /*height: calc(100vh - 110px);*/
      // height: 100%;
      overflow-y: auto;
      flex: 1;
      .el-tab-pane {
        // height: 100%;
      }
    }
  }
  .el-tabs__header {
    margin: 0 0 5px;
  }
  .el-icon-rank {
    transform: rotate(45deg);
  }
}
</style>
