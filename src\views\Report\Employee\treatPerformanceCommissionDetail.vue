<template>
  <div class="content_body" v-loading="loading">
    <div class="nav_header">
      <el-form :inline="true" size="small" :model="searchTreatPerformanceCommissionDetailData" @submit.native.prevent>
        <!-- <el-row> -->
          <!-- <el-form-item v-if="storeEntityList.length > 1" label="开单门店">
            <el-select v-model="searchTreatPerformanceCommissionDetailData.EntityID" clearable filterable
              placeholder="请选择门店" :default-first-option="true" @change="handleTreatPerformanceCommissionSearch">
              <el-option v-for="item in storeEntityList" :key="item.ID" :label="item.EntityName"
                :value="item.ID"></el-option>
            </el-select>
          </el-form-item> -->
          <el-form-item label="时间筛选">
            <el-date-picker v-model="searchTreatPerformanceCommissionDetailData.QueryDate"
              :picker-options="pickerOptions" unlink-panels type="daterange" range-separator="至"
              value-format="yyyy-MM-dd" start-placeholder="开始日期" end-placeholder="结束日期"
              @change="handleTreatPerformanceCommissionSearch"></el-date-picker>
          </el-form-item>
          <el-form-item label="卡项类型">
            <el-select v-model="searchTreatPerformanceCommissionDetailData.TreatCardTypeName" clearable filterable
              placeholder="请选择卡项类型" :default-first-option="true" @change="handleTreatPerformanceCommissionSearch">
              <el-option label="项目卡" value="项目卡"></el-option>
              <el-option label="储值卡" value="储值卡"></el-option>
              <el-option label="时效卡" value="时效卡"></el-option>
              <el-option label="通用次卡" value="通用次卡"></el-option>
              <el-option label="产品卡" value="产品卡"></el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="商品分类">
            <el-cascader v-model="searchTreatPerformanceCommissionDetailData.CategoryID" :options="categoryList"
              :props="cascaderProps" @change="handleTreatPerformanceCommissionSearch" clearable></el-cascader>
          </el-form-item>

          <el-form-item label="商品名称">
            <el-input v-model="searchTreatPerformanceCommissionDetailData.GoodsName" clearable
              @keyup.enter.native="handleTreatPerformanceCommissionSearch"
              @clear="handleTreatPerformanceCommissionSearch" placeholder="请输入商品名称"></el-input>
          </el-form-item>
          
        <!-- </el-row> -->

        <el-form-item label="单据编号">
          <el-input v-model="searchTreatPerformanceCommissionDetailData.BillID" clearable
            @keyup.enter.native="handleTreatPerformanceCommissionSearch" @clear="handleTreatPerformanceCommissionSearch"
            placeholder="请输入单据编号"></el-input>
        </el-form-item>
        
        <el-form-item label="是否赠送">
          <el-select v-model="searchTreatPerformanceCommissionDetailData.IsLargess" clearable placeholder="请选择"
            @change="handleTreatPerformanceCommissionSearch" @clear="handleTreatPerformanceCommissionSearch">
            <el-option label="是" :value="true"></el-option>
            <el-option label="否" :value="false"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="客户姓名">
          <el-input v-model="searchTreatPerformanceCommissionDetailData.CustomerName" clearable
            @keyup.enter.native="handleTreatPerformanceCommissionSearch" @clear="handleTreatPerformanceCommissionSearch"
            placeholder="请输入顾客姓名"></el-input>
        </el-form-item>

        <el-form-item label="客户等级">
          <el-select v-model="searchTreatPerformanceCommissionDetailData.CustomerLevelID" placeholder="请选择客户等级"
            @clear="handleTreatPerformanceCommissionSearch" clearable @change="handleTreatPerformanceCommissionSearch">
            <el-option v-for="item in customerLevelList" :label="item.Name" :value="item.ID" :key="item.ID"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="员工姓名">
          <el-input v-model="searchTreatPerformanceCommissionDetailData.EmployeeName" clearable
            @keyup.enter.native="handleTreatPerformanceCommissionSearch" @clear="handleTreatPerformanceCommissionSearch"
            placeholder="请输入员工姓名"></el-input>
        </el-form-item>


        <el-form-item v-if="storeEntityList.length > 1" label="员工归属门店">
          <el-select v-model="searchTreatPerformanceCommissionDetailData.EntityID" clearable filterable
            placeholder="请选择门店" :default-first-option="true" @change="handleTreatPerformanceCommissionSearch">
            <el-option v-for="item in storeEntityList" :key="item.ID" :label="item.EntityName"
              :value="item.ID"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="员工职务">
          <el-select v-model="searchTreatPerformanceCommissionDetailData.JobID" filterable placeholder="选择员工职务"
            @change="handleTreatPerformanceCommissionSearch" clearable>
            <el-option v-for="item in jobTypeList" :key="item.ID" :label="item.JobName" :value="item.ID"></el-option>
          </el-select>
        </el-form-item>


        <el-form-item>
          <el-button type="primary" v-prevent-click @click="handleTreatPerformanceCommissionSearch">搜索</el-button>
        </el-form-item>
        <el-form-item>
          <el-button v-if="treatPerformanceCommissionDetailExport" type="primary" size="small"
            :loading="downloadLoading" @click="downloadExcel">导出</el-button>
        </el-form-item>
      </el-form>
    </div>
    <el-table size="small" show-summary :summary-method="getTreatPerformanceCommissionDetailListSummaries"
      :data="TreatPerformanceCommissionDetailList">
      <el-table-column prop="TreatBillID" label="订单编号"></el-table-column>
      <el-table-column prop="BillDate" label="下单日期">
        <template slot-scope="scope">{{ scope.row.BillDate | dateFormat("YYYY-MM-DD HH:mm") }}</template>
      </el-table-column>
      <el-table-column prop="EntityName" label="下单门店"></el-table-column>
      <el-table-column label="客户信息">
        <el-table-column prop="CustomerName" label="客户姓名"></el-table-column>
        <el-table-column label="客户等级" prop="CustomerLevelName"></el-table-column>
        <el-table-column prop="CustomerSourceName" label="客户来源"></el-table-column>
        <el-table-column prop="CustomerEntityName" label="所属门店"></el-table-column>
        <el-table-column prop="ChannelName" label="归属渠道"></el-table-column>
      </el-table-column>
      <el-table-column label="员工信息">
        <el-table-column prop="EmployeeName" label="员工姓名"></el-table-column>
        <el-table-column prop="EmployeeID" label="员工编号"></el-table-column>
        <el-table-column prop="JobName" label="员工职务"></el-table-column>
        <el-table-column prop="TreatHandlerName" label="经手人名称" width="100"></el-table-column>
        <el-table-column prop="EmployeeEntityName" label="所属门店"></el-table-column>
      </el-table-column>
      <el-table-column label="卡项信息">
        <el-table-column prop="CardName" label="卡项名称"></el-table-column>
        <el-table-column prop="TreatCardTypeName" label="卡项类型"></el-table-column>
        <el-table-column prop="IsLargess" label="是否赠送" width="70">
          <template slot-scope="scope">{{ scope.row.IsLargess ? "是" : "否" }}</template>
        </el-table-column>

        <el-table-column prop="AccountRemark" label="备注信息" show-overflow-tooltip></el-table-column>
      </el-table-column>
      <el-table-column label="消耗商品信息">
        <el-table-column prop="GoodName" label="商品名称"></el-table-column>
        <el-table-column prop="CategoryName" label="商品分类"></el-table-column>
        <el-table-column prop="Price" label="单价">
          <template slot-scope="scope">
            {{ scope.row.Price | toFixed | NumFormat }}
          </template>
        </el-table-column>
        <el-table-column prop="Quantity" label="数量"></el-table-column>
        <el-table-column prop="PreferentialAmount" label="优惠金额">
          <template slot-scope="scope">
            <span v-if="scope.row.PreferentialAmount < 0" class="color_red">{{ scope.row.PreferentialAmount | toFixed |
              NumFormat }}</span>
            <span v-else-if="scope.row.PreferentialAmount > 0" class="color_green">+{{ scope.row.PreferentialAmount |
              toFixed | NumFormat }}</span>
            <span v-else>0.00</span>
          </template>
        </el-table-column>
        <el-table-column prop="TotalAmount" label="合计金额">
          <template slot-scope="scope">
            {{ scope.row.TotalAmount | toFixed | NumFormat }}
          </template>
        </el-table-column>
        <el-table-column prop="PayAmount" label="现金">
          <template slot-scope="scope">
            {{ scope.row.PayAmount | toFixed | NumFormat }}
          </template>
        </el-table-column>
        <el-table-column prop="CardDeductionAmount" label="卡扣金额">
          <template slot-scope="scope">
            {{ scope.row.CardDeductionAmount | toFixed | NumFormat }}
          </template>
        </el-table-column>
        <el-table-column prop="LargessCardDeductionAmount" label="赠卡扣金额">
          <template slot-scope="scope">
            {{ scope.row.LargessCardDeductionAmount | toFixed | NumFormat }}
          </template>
        </el-table-column>
        <el-table-column prop="Scale" label="经手人比例">
          <template slot-scope="scope">
            <span v-if="scope.row.Scale > 0">{{ (scope.row.Scale * 100) | toFixed | NumFormat }}%</span>
            <span v-else>{{ scope.row.Scale }}</span>
          </template>
        </el-table-column>
      </el-table-column>
      <el-table-column label="业绩提成">
        <el-table-column align="right" prop="PayPerformance" label="现金业绩">
          <template align="right" slot-scope="scope">
            <div v-if="scope.row.PayPerformance < 0" class="color_red">{{ scope.row.PayPerformance | toFixed | NumFormat
              }}</div>
            <div v-else-if="scope.row.PayPerformance > 0" class="color_green">+{{ scope.row.PayPerformance | toFixed |
              NumFormat }}</div>
            <div v-else>0.00</div>
          </template>
        </el-table-column>
        <el-table-column align="right" prop="PayPerformanceCommission" label="现金提成">
          <template align="right" slot-scope="scope">
            <div v-if="scope.row.PayPerformanceCommission < 0" class="color_red">{{ scope.row.PayPerformanceCommission |
              toFixed | NumFormat }}</div>
            <div v-else-if="scope.row.PayPerformanceCommission > 0" class="color_green">+{{
              scope.row.PayPerformanceCommission | toFixed | NumFormat }}</div>
            <div v-else>0.00</div>
          </template>
        </el-table-column>

        <el-table-column align="right" prop="CardPerformance" label="卡扣业绩">
          <template align="right" slot-scope="scope">
            <div v-if="scope.row.CardPerformance < 0" class="color_red">{{ scope.row.CardPerformance | toFixed |
              NumFormat }}</div>
            <div v-else-if="scope.row.CardPerformance > 0" class="color_green">+{{ scope.row.CardPerformance | toFixed |
              NumFormat }}</div>
            <div v-else>0.00</div>
          </template>
        </el-table-column>
        <el-table-column align="right" prop="CardPerformanceCommission" label="卡扣提成">
          <template align="right" slot-scope="scope">
            <div v-if="scope.row.CardPerformanceCommission < 0" class="color_red">{{ scope.row.CardPerformanceCommission
              | toFixed | NumFormat }}</div>
            <div v-else-if="scope.row.CardPerformanceCommission > 0" class="color_green">+{{
              scope.row.CardPerformanceCommission | toFixed | NumFormat }}</div>
            <div v-else>0.00</div>
          </template>
        </el-table-column>

        <el-table-column align="right" prop="CardLargessPerformance" label="赠卡扣业绩">
          <template align="right" slot-scope="scope">
            <div v-if="scope.row.CardLargessPerformance < 0" class="color_red">{{ scope.row.CardLargessPerformance |
              toFixed | NumFormat }}</div>
            <div v-else-if="scope.row.CardLargessPerformance > 0" class="color_green">+{{
              scope.row.CardLargessPerformance | toFixed | NumFormat }}</div>
            <div v-else>0.00</div>
          </template>
        </el-table-column>
        <el-table-column align="right" prop="CardLargessPerformanceCommission" label="赠卡扣提成">
          <template align="right" slot-scope="scope">
            <div v-if="scope.row.CardLargessPerformanceCommission < 0" class="color_red">
              {{ scope.row.CardLargessPerformanceCommission | toFixed | NumFormat }}
            </div>
            <div v-else-if="scope.row.CardLargessPerformanceCommission > 0" class="color_green">+{{
              scope.row.CardLargessPerformanceCommission | toFixed | NumFormat }}</div>
            <div v-else>0.00</div>
          </template>
        </el-table-column>

        <el-table-column align="right" prop="LargessPerformance" label="赠送业绩">
          <template align="right" slot-scope="scope">
            <div v-if="scope.row.LargessPerformance < 0" class="color_red">{{ scope.row.LargessPerformance | toFixed |
              NumFormat }}</div>
            <div v-else-if="scope.row.LargessPerformance > 0" class="color_green">+{{ scope.row.LargessPerformance |
              toFixed | NumFormat }}</div>
            <div v-else>0.00</div>
          </template>
        </el-table-column>
        <el-table-column align="right" prop="LargessPerformanceCommission" label="赠送提成">
          <template align="right" slot-scope="scope">
            <div v-if="scope.row.LargessPerformanceCommission < 0" class="color_red">{{
              scope.row.LargessPerformanceCommission | toFixed | NumFormat }}</div>
            <div v-else-if="scope.row.LargessPerformanceCommission > 0" class="color_green">+{{
              scope.row.LargessPerformanceCommission | toFixed | NumFormat }}</div>
            <div v-else>0.00</div>
          </template>
        </el-table-column>
        <el-table-column align="right" prop="SpecialBenefitCommission" label="无业绩奖励">
          <template align="right" slot-scope="scope">
            <div v-if="scope.row.SpecialBenefitCommission < 0" class="color_red">{{ scope.row.SpecialBenefitCommission |
              toFixed | NumFormat }}</div>
            <div v-else-if="scope.row.SpecialBenefitCommission > 0" class="color_green">+{{
              scope.row.SpecialBenefitCommission | toFixed | NumFormat }}</div>
            <div v-else>0.00</div>
          </template>
        </el-table-column>
      </el-table-column>

      <el-table-column prop="BillRemark" label="订单备注" show-overflow-tooltip></el-table-column>
    </el-table>
    <div class="pad_15 text_right">
      <el-pagination background v-if="TreatPerformanceCommissionDetailPaginations.total > 0"
        @current-change="handleTreatPerformanceCommissionPageChange"
        :current-page.sync="TreatPerformanceCommissionDetailPaginations.page"
        :page-size="TreatPerformanceCommissionDetailPaginations.page_size"
        :layout="TreatPerformanceCommissionDetailPaginations.layout"
        :total="TreatPerformanceCommissionDetailPaginations.total"></el-pagination>
    </div>
  </div>
</template>
<script>
import EntityAPI from "@/api/Report/Common/entity";
import API from "@/api/Report/Employee/treatPerformanceCommissionDetailStatement";
import permission from "@/components/js/permission.js";
import APIJob from "@/api/KHS/Entity/jobtype";
const dayjs = require("dayjs");
const isoWeek = require("dayjs/plugin/isoWeek");
dayjs.extend(isoWeek);

export default {
  name: "EmployeeTreatPerformanceCommissionDetail",
  data () {
    return {
      radio: "",
      downloadLoading: false,
      loading: false,
      jobTypeList: [], //职务列表
      storeEntityList: [], //门店列表
      searchTreatPerformanceCommissionDetailData: {
        EntityID: null,
        QueryDate: [new Date(), new Date()],
        TreatCardTypeName: "",
        EmployeeName: "",
        JobID: "",
        CustomerName: "",
        IsLargess: "",
        CategoryID: "",
        GoodsName: "",
        CustomerLevelID: "",
      },
      TreatPerformanceCommissionDetailList: [],
      TreatPerformanceCommissionDetailSum: {},
      //需要给分页组件传的信息
      TreatPerformanceCommissionDetailPaginations: {
        page: 1, // 当前位于哪页
        total: 0, // 总数
        page_size: 10, // 1页显示多少条
        layout: "total, prev, pager, next,jumper", // 翻页属性
      },
      treatPerformanceCommissionDetailExport: false,
      pickerOptions: {
        shortcuts: [
          {
            text: "今天",
            onClick: (picker) => {
              let end = new Date();
              let start = new Date();
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "本周",
            onClick: (picker) => {
              let end = new Date();
              let weekDay = dayjs(end).isoWeekday();
              let start = dayjs(end)
                .subtract(weekDay - 1, "day")
                .toDate();
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "本月",
            onClick: (picker) => {
              let end = new Date();
              let start = dayjs(dayjs(end).format("YYYY-MM") + "01").toDate();
              picker.$emit("pick", [start, end]);
            },
          },
        ],
      },
      categoryList: [],
      cascaderProps: {
        checkStrictly: true,
        label: "Name",
        value: "ID",
        children: "Child",
      },
      customerLevelList: [],
    };
  },

  beforeRouteEnter (to, from, next) {
    next((vm) => {
      vm.treatPerformanceCommissionDetailExport = permission.permission(to.meta.Permission, "Report-Employee-TreatPerformanceCommissionDetail-Export");
    });
  },
  methods: {
    //获得当前用户下的权限门店
    getstoreEntityList () {
      var that = this;
      that.loading = true;
      EntityAPI.getStoreEntityList()
        .then((res) => {
          if (res.StateCode == 200) {
            that.storeEntityList = res.Data;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },

    handleTreatPerformanceCommissionSearch () {
      var that = this;
      that.TreatPerformanceCommissionDetailPaginations.page = 1;
      that.TreatPerformanceCommissionDetail();
    },
    handleTreatPerformanceCommissionPageChange (page) {
      this.TreatPerformanceCommissionDetailPaginations.page = page;
      this.TreatPerformanceCommissionDetail();
    },
    // 销售搜索
    TreatPerformanceCommissionDetail () {
      var that = this;
      if (that.searchTreatPerformanceCommissionDetailData.QueryDate != null) {
        if (dayjs(that.searchTreatPerformanceCommissionDetailData.QueryDate[0]).add(366, "day").valueOf() < dayjs(that.searchTreatPerformanceCommissionDetailData.QueryDate[1]).valueOf()) {
          that.$message.error("时间筛选范围不能超366天");
          return;
        }

        let CategoryID = "";
        if (that.searchTreatPerformanceCommissionDetailData.CategoryID.length == 2) {
          CategoryID = that.searchTreatPerformanceCommissionDetailData.CategoryID[1];
        }

        if (that.searchTreatPerformanceCommissionDetailData.CategoryID.length == 3) {
          CategoryID = that.searchTreatPerformanceCommissionDetailData.CategoryID[2];
        }
        var params = {
          EntityID: that.searchTreatPerformanceCommissionDetailData.EntityID,
          StartDate: that.searchTreatPerformanceCommissionDetailData.QueryDate[0],
          EndDate: that.searchTreatPerformanceCommissionDetailData.QueryDate[1],
          TreatCardTypeName: that.searchTreatPerformanceCommissionDetailData.TreatCardTypeName.trim(),
          EmployeeName: that.searchTreatPerformanceCommissionDetailData.EmployeeName.trim(),
          PageNum: that.TreatPerformanceCommissionDetailPaginations.page,
          JobID: that.searchTreatPerformanceCommissionDetailData.JobID,
          IsLargess: that.searchTreatPerformanceCommissionDetailData.IsLargess,
          CustomerName: that.searchTreatPerformanceCommissionDetailData.CustomerName,
          BillID: that.searchTreatPerformanceCommissionDetailData.BillID,
          GoodsTypeName: that.searchTreatPerformanceCommissionDetailData.CategoryID.length ? that.getGoodsTypeID(that.searchTreatPerformanceCommissionDetailData.CategoryID[0]) : "",
          CategoryID: CategoryID,
          GoodsName: that.searchTreatPerformanceCommissionDetailData.GoodsName,
          CustomerLevelID: that.searchTreatPerformanceCommissionDetailData.CustomerLevelID,

        };
        that.loading = true;
        API.getEmployeeTreatPerformanceCommissionDetail(params)
          .then((res) => {
            if (res.StateCode == 200) {
              that.TreatPerformanceCommissionDetailSum = res.Data.employeeTreatPerformanceCommissionSumStatementForm;
              that.TreatPerformanceCommissionDetailList = res.Data.employeeTreatPerformanceCommissionDetailStatementForms.List;
              that.TreatPerformanceCommissionDetailPaginations.total = res.Data.employeeTreatPerformanceCommissionDetailStatementForms.Total;
              that.TreatPerformanceCommissionDetailPaginations.page_size = res.Data.employeeTreatPerformanceCommissionDetailStatementForms.PageSize;
            } else {
              that.$message.error({
                message: res.Message,
                duration: 2000,
              });
            }
          })
          .finally(function () {
            that.loading = false;
          });
      }
    },
    /**    */
    getGoodsTypeID (type) {
      switch (type) {
        case 10:
          return "产品";
        case 20:
          return "项目";
      }
    },
    getTreatPerformanceCommissionDetailListSummaries ({ columns }) {
      const sums = [];
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = <span class="font_weight_600">合计</span>;
          return;
        }
        var filter_NumFormat = this.$options.filters["NumFormat"];
        switch (column.property) {
          case "TotalAmount":
          case "PayAmount":
          case "CardDeductionAmount":
          case "LargessCardDeductionAmount":
          case "PayPerformance":
          case "PayPerformanceCommission":
          case "CardPerformance":
          case "CardPerformanceCommission":
          case "SpecialBenefitCommission":
          case "CardLargessPerformance":
          case "CardLargessPerformanceCommission":
          case "LargessPerformance":
          case "LargessPerformanceCommission":
            {
              let value = this.TreatPerformanceCommissionDetailSum ? this.TreatPerformanceCommissionDetailSum[column.property] : 0;
              sums[index] = <span class="font_weight_600">{filter_NumFormat(value)}</span>;
            }
            break;
          default:
            sums[index] = "";
        }
      });
      return sums;
    },
    /** 数据导出 */
    downloadExcel () {
      var that = this;

      if (that.searchTreatPerformanceCommissionDetailData.QueryDate != null) {
        if (dayjs(that.searchTreatPerformanceCommissionDetailData.QueryDate[0]).add(366, "day").valueOf() < dayjs(that.searchTreatPerformanceCommissionDetailData.QueryDate[1]).valueOf()) {
          that.$message.error("时间筛选范围不能超366天");
          return;
        }
        let CategoryID = "";
        if (that.searchTreatPerformanceCommissionDetailData.CategoryID.length == 2) {
          CategoryID = that.searchTreatPerformanceCommissionDetailData.CategoryID[1];
        }

        if (that.searchTreatPerformanceCommissionDetailData.CategoryID.length == 3) {
          CategoryID = that.searchTreatPerformanceCommissionDetailData.CategoryID[2];
        }
        let params = {
          EntityID: that.searchTreatPerformanceCommissionDetailData.EntityID,
          StartDate: that.searchTreatPerformanceCommissionDetailData.QueryDate[0],
          EndDate: that.searchTreatPerformanceCommissionDetailData.QueryDate[1],
          TreatCardTypeName: that.searchTreatPerformanceCommissionDetailData.TreatCardTypeName.trim(),
          EmployeeName: that.searchTreatPerformanceCommissionDetailData.EmployeeName.trim(),
          JobID: that.searchTreatPerformanceCommissionDetailData.JobID,
          CustomerName: that.searchTreatPerformanceCommissionDetailData.CustomerName,
          IsLargess: that.searchTreatPerformanceCommissionDetailData.IsLargess,
          BillID: that.searchTreatPerformanceCommissionDetailData.BillID,
          GoodsTypeName: that.searchTreatPerformanceCommissionDetailData.CategoryID.length ? that.getGoodsTypeID(that.searchTreatPerformanceCommissionDetailData.CategoryID[0]) : "",
          CategoryID: CategoryID,
          GoodsName: that.searchTreatPerformanceCommissionDetailData.GoodsName,
          CustomerLevelID: that.searchTreatPerformanceCommissionDetailData.CustomerLevelID,
        };
        that.downloadLoading = true;
        API.exportemployeeTreatPerformanceCommissionDetailStatementt(params)
          .then((res) => {
            this.$message.success({
              message: "正在导出",
              duration: "4000",
            });
            const link = document.createElement("a");
            let blob = new Blob([res], { type: "application/octet-stream" });
            link.style.display = "none";
            link.href = URL.createObjectURL(blob);
            link.download = "员工消耗业绩.xlsx"; //下载的文件名
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
          })
          .finally(function () {
            that.downloadLoading = false;
          });
      }
    },
    // 职务ID
    async getJobID () {
      var that = this;
      var params = {
        JobTypeName: "",
      };
      let res = await APIJob.getJobJobtypeAll(params);
      if (res.StateCode == 200) {
        that.jobTypeList = res.Data;
      } else {
        that.$message.error({
          message: res.Message,
          duration: 2000,
        });
      }
    },
    /**  分类  */
    async entitySaleGoodsDetailStatement_productAndProjectCategory () {
      let that = this;
      let res = await API.entitySaleGoodsDetailStatement_productAndProjectCategory();
      if (res.StateCode == 200) {
        that.categoryList = res.Data;
      } else {
        that.$message.error(res.Message);
      }
    },
    /**  获取客户等级  */
    getCustomerLevel_all () {
      let that = this;
      let params = {
        Name: "",
        Active: true, //有效性
      };
      API.customerLevel_all(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.customerLevelList = res.Data;
          } else {
            that.$message.error(res.Message);
          }
        })
        .catch((fail) => {
          that.$message.error(fail);
        });
    },
  },
  mounted () {
    var that = this;
    that.treatPerformanceCommissionDetailExport = permission.permission(that.$route.meta.Permission, "Report-Employee-TreatPerformanceCommissionDetail-Export");
    that.getstoreEntityList();
    that.getJobID();
    that.handleTreatPerformanceCommissionSearch();
    that.entitySaleGoodsDetailStatement_productAndProjectCategory();
    that.getCustomerLevel_all();
  },
};
</script>
