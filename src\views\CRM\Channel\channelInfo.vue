<template>
  <div class="channelInfo content_body" v-loading="loading">
    <el-container style="height: 100%">
      <el-aside>
        <el-scrollbar class="custom-scrollbarClass">
          <div class="pad_10" style="border-bottom: 1px solid #e4e6e9">
            <el-input placeholder="输入渠道名称进行搜索" v-model.trim="salesName" size="small" prefix-icon="el-icon-search" clearable @keyup.enter.native="contactQuery" @clear="contactQuery"></el-input>
          </div>

          <el-tree
            class="padtp_10"
            :expand-on-click-node="false"
            :data="treeData"
            @node-click="handleNodeClick"
            node-key="ID"
            :props="defaultProps"
            :auto-expand-parent="true"
            :default-expanded-keys="organDefaultExpandedKeys"
            @node-drop="handleDrop"
            draggable
            :allow-drop="allowDrop"
            :allow-drag="allowDrag"
          ></el-tree>
        </el-scrollbar>
      </el-aside>

      <el-container>
        <el-header style="height: auto; padding: 0px">
          <div class="nav_header martp_15 custom_header">
            <el-row>
              <el-col :span="22">
                <el-form :inline="true" size="small" @keyup.enter.native="handleSearch" label-width="80px">
                  <el-form-item label="渠道名称">
                    <el-input size="small" v-model="Name" placeholder="输入渠道名称搜索" clearable @change="handleSearch" @clear="handleSearch"></el-input>
                  </el-form-item>
                  <el-form-item label="上级渠道" size="small">
                    <el-select v-model="ParentID" placeholder="请输入上级渠道" popper-class="custom_channelPopperClass" filterable remote reserve-keyword size="small" clearable :remote-method="searchChannelInfo" @focus="focusChannel" @clear="focusChannel" @change="handleSearch">
                      <el-option v-for="item in allChannelList" :key="item.ID" :label="item.Name" :value="item.ID">
                        <div style="padding-top: 4px; padding-bottom: 4px">
                          <div>{{ item.Name }}</div>
                          <div class="font_12 color_999">{{ item.ParentName }}</div>
                        </div>
                      </el-option>
                    </el-select>
                  </el-form-item>
                  <el-form-item label="渠道类型">
                    <el-select v-model="ChannelTypeID" placeholder="请选择渠道类型" filterable clearable @change="handleSearch" @clear="handleSearch" size="small">
                      <el-option v-for="item in channelTypeList" :key="item.ID" :label="item.Name" :value="item.ID"> </el-option>
                    </el-select>
                  </el-form-item>
                  <el-form-item label="渠道等级">
                    <el-select v-model="ChannelLevelID" placeholder="请选择渠道等级" filterable clearable @change="handleSearch" @clear="handleSearch" size="small">
                      <el-option v-for="item in channelLevelList" :key="item.ID" :label="item.Name" :value="item.ID"> </el-option>
                    </el-select>
                  </el-form-item>
                  <el-form-item label="开发人员">
                    <el-select v-model="DeveloperID" placeholder="请选择开发人员" filterable clearable @change="handleSearch" @clear="handleSearch" size="small" popper-class="empPopper_custom">
                      <el-option v-for="item in EmployeeList" :key="item.ID" :label="item.Name" :value="item.ID">
                        <div style="padding-bottom: 8px">
                          <div>{{ item.Name }}</div>
                          <div class="font_12 color_999">{{ item.EntityName }}</div>
                        </div>
                      </el-option>
                    </el-select>
                  </el-form-item>

                  <el-form-item label="市场咨询">
                    <el-select v-model="ConsultantID" placeholder="请选择市场咨询" filterable clearable @change="handleSearch" @clear="handleSearch" size="small" popper-class="empPopper_custom">
                      <el-option v-for="item in EmployeeList" :key="item.ID" :label="item.Name" :value="item.ID">
                        <div style="padding-bottom: 8px">
                          <div>{{ item.Name }}</div>
                          <div class="font_12 color_999">{{ item.EntityName }}</div>
                        </div>
                      </el-option>
                    </el-select>
                  </el-form-item>

                  <el-form-item label="有效性">
                    <el-select size="small" v-model="Active" placeholder="请选择有效性" clearable @change="handleSearch" @clear="handleSearch">
                      <el-option :value="true" label="有效"></el-option>
                      <el-option :value="false" label="无效"></el-option>
                    </el-select>
                  </el-form-item>
                  <el-form-item>
                    <el-button size="small" type="primary" @click="handleSearch" v-prevent-click>搜索</el-button>
                  </el-form-item>

                  <el-form-item>
                    <el-button v-if="isExport" type="primary" size="small" :loading="downloadLoading" @click="downloadCusAccountExcel">导出</el-button>
                  </el-form-item>

                  <el-button size="small" type="primary" @click="transferDeveloperClick('转移开发人员')" v-prevent-click>转移开发人员</el-button>
                  <el-button size="small" type="primary" @click="transferDeveloperClick('转移市场咨询')" v-prevent-click>转移市场咨询</el-button>
                </el-form>
              </el-col>
              <el-col :span="2" class="text_right">
                <el-button size="small" type="primary" @click="addChannelInfo" v-prevent-click>新增</el-button>
              </el-col>
            </el-row>
          </div>
        </el-header>

        <el-main style="padding: 0px">
          <div>
            <el-table size="small" :data="tableData" @selection-change="batchSettingSelection">
              <el-table-column type="selection" width="55"></el-table-column>
              <el-table-column label="渠道名称" prop="Name"></el-table-column>
              <el-table-column label="联系人" prop="ContactPersonName"></el-table-column>
              <el-table-column label="联系人手机号" prop="ContactPersonMobile"></el-table-column>
              <el-table-column label="渠道类型" prop="ChannelTypeName"></el-table-column>
              <el-table-column label="渠道等级" prop="ChannelLevelName"></el-table-column>
              <el-table-column label="上级渠道" prop="ParentName"></el-table-column>
              <el-table-column label="开发人员">
                <!-- :formatter="developerFormatter" -->
                <template slot-scope="scope">
                  <el-popover placement="top-start" width="250" trigger="hover" popper-class="custom_channel_popover">
                    <el-table size="small" :data="scope.row.DeveloperList">
                      <el-table-column label="开发人员名称" prop="EmployeeName"></el-table-column>
                      <el-table-column label="业绩比例" prop="PerformanceRate"></el-table-column>
                    </el-table>
                    <div slot="reference" style="text-overflow: ellipsis; white-space: nowrap; overflow: hidden">
                      <span v-if="scope.row.DeveloperList.length > 0">
                        <span>{{ developerFormatter(scope.row) }}</span>
                      </span>
                    </div>
                  </el-popover>
                </template>
              </el-table-column>

              <el-table-column label="市场咨询">
                <template slot-scope="scope">
                  <el-popover placement="top-start" width="250" trigger="hover" popper-class="custom_channel_popover">
                    <el-table size="small" :data="scope.row.ConsultantList">
                      <el-table-column label="市场咨询名称" prop="EmployeeName"></el-table-column>
                      <el-table-column label="业绩比例" prop="PerformanceRate"></el-table-column>
                    </el-table>
                    <div slot="reference" style="text-overflow: ellipsis; white-space: nowrap; overflow: hidden">
                      <span v-if="scope.row.ConsultantList.length > 0">
                        <span>{{ consultantFormatter(scope.row) }}</span>
                      </span>
                    </div>
                  </el-popover>
                </template>
              </el-table-column>
              <el-table-column label="有效性" prop="Active">
                <template slot-scope="scope">
                  <span>{{ scope.row.Active ? "有效" : "无效" }}</span>
                </template>
              </el-table-column>
              <el-table-column label="创建时间" prop="CreatedOn" width="145"> </el-table-column>
              <el-table-column label="操作" width="80">
                <template slot-scope="scope">
                  <el-button type="primary" size="small" @click="editChannelInfo(scope.row)" v-prevent-click>编辑 </el-button>
                </template>
              </el-table-column>
            </el-table>
            <div style="display: flex">
              <div class="pad_15 text_left" style="flex: 1">
                <el-dropdown trigger="click" @command="dropdownClick">
                  <el-button type="primary" size="small">
                    批量设置
                    <i class="el-icon-arrow-down el-icon--right"></i>
                  </el-button>
                  <el-dropdown-menu slot="dropdown">
                    <el-dropdown-item command="1">上级渠道</el-dropdown-item>
                    <!-- <el-dropdown-item command="2">业务代表</el-dropdown-item> -->
                  </el-dropdown-menu>
                </el-dropdown>
              </div>
              <div class="pad_15 text_right" style="flex: 1">
                <el-pagination background @current-change="handleCurrentChange" :current-page.sync="paginations.page" :page-size="paginations.page_size" :layout="paginations.layout" :total="paginations.total"> </el-pagination>
              </div>
            </div>
          </div>
        </el-main>
      </el-container>
    </el-container>

    <!--增加、编辑弹出框-->
    <el-dialog :title="isEdit ? '编辑渠道' : '新增渠道'" :visible.sync="dialogVisible" width="1000px">
      <el-tabs v-model="activeName" class="editTabs">
        <el-tab-pane label="基本信息" name="first">
          <el-form :model="ruleFormAdd" :rules="rules_add" ref="ruleFormAdd" label-width="120px" class="demo-ruleForm" size="small" @submit.native.prevent>
            <el-row>
              <el-col :span="12">
                <el-form-item label="渠道名称" prop="Name">
                  <el-input v-model="ruleFormAdd.Name" placeholder="请输入渠道名称"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item>
                  <span slot="label">上级渠道</span>
                  <el-select v-model="ruleFormAdd.ParentID" placeholder="请输入渠道名称搜索" popper-class="custom_channelPopperClass" filterable remote reserve-keyword size="small" clearable :remote-method="searchChannelInfo" @focus="focusChannel" @clear="focusChannel">
                    <el-option v-for="item in allChannelList" :key="item.ID" :label="item.Name" :value="item.ID">
                      <div style="padding-bottom: 8px">
                        <div>{{ item.Name }}</div>
                        <div class="font_12 color_999">上级渠道：{{ item.ParentName }}</div>
                      </div>
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="渠道类型" prop="ChannelTypeID">
                  <el-select v-model="ruleFormAdd.ChannelTypeID" placeholder="请选择渠道类型" filterable clearable size="small">
                    <el-option v-for="item in channelTypeList" :key="item.ID" :label="item.Name" :value="item.ID"> </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="渠道等级" prop="channelLevel">
                  <el-select v-model="ruleFormAdd.ChannelLevelID" placeholder="请选择渠道等级" filterable clearable size="small">
                    <el-option v-for="item in channelLevelList" :key="item.ID" :label="item.Name" :value="item.ID"> </el-option>
                  </el-select>
                </el-form-item>
              </el-col>

              <el-col :span="12">
                <el-form-item label="联系人">
                  <el-input v-model="ruleFormAdd.ContactPersonName" placeholder="请输入联系人"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="联系电话">
                  <el-input placeholder="请输入手机号码" v-model="ruleFormAdd.ContactPersonMobile" maxlength="11"></el-input>
                </el-form-item>
              </el-col>

              <el-col :span="12">
                <el-form-item label="身份证号">
                  <el-input placeholder="请输入身份证号" v-model="ruleFormAdd.ContactPersonIDNumber"></el-input>
                </el-form-item>
              </el-col>

              <el-col :span="12">
                <el-form-item label="有效性" prop="Active" v-if="isEdit">
                  <el-radio-group v-model="ruleFormAdd.Active">
                    <el-radio :label="true">有效</el-radio>
                    <el-radio :label="false">无效</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>

              <el-col :span="24">
                <el-form-item label="省市区">
                  <el-cascader clearable placeholder="请选择省 / 市 / 区" size="small" :options="regionData" v-model="regionDataSelArr"> </el-cascader>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="详细地址">
                  <el-input v-model="ruleFormAdd.AddressDetail" class="marrt_10"></el-input>
                  <el-button type="primary" plain @click="getPlaceSearch">搜索地图</el-button>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="地图定位">
                  <map-container :markOption="markOption" @searchAddresAddListener="searchAddresAddListener" @mapSelection="mapSelection" ref="map_container"></map-container>
                  <!-- <div id="gdmapContainer"></div> -->
                  <!-- 展示搜索结果的容器 -->
                  <!-- <div id="gdmapPanel"></div> -->
                </el-form-item>
              </el-col>

              <el-col :span="24">
                <el-form-item label="备注信息">
                  <el-input type="textarea" rows="3" v-model="ruleFormAdd.Remark"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-tab-pane>
        <el-tab-pane label="开发人员" name="50">
          <el-row :gutter="20" class="pad_10_0">
            <el-col :span="10">
              <el-input placeholder="输入员工名称、编号搜索" size="small" v-model="searchChannelDevName" clearable></el-input>
            </el-col>
            <el-col :span="14">
              <el-button type="primary" @click="addChannelEmployeeClick('开发人员')" size="small">添加开发人员 </el-button>
            </el-col>
          </el-row>
          <el-table size="small" :data="ruleFormAdd.DeveloperList.filter((i) => !searchChannelDevName || i.EmployeeName.toLowerCase().includes(searchChannelDevName.toLowerCase()))" max-height="450">
            <el-table-column prop="EmployeeName" label="开发人员"></el-table-column>
            <el-table-column prop="PerformanceRate" label="业绩比例">
              <template slot-scope="scope">{{ scope.row.PerformanceRate }}%</template>
            </el-table-column>
            <el-table-column label="操作" width="145">
              <template slot-scope="scope">
                <el-button type="primary" size="mini" @click="editDeveloperItemClick(scope.row, scope.$index)">编辑 </el-button>
                <el-button type="danger" size="mini" @click="removeDeveloperClick(scope.$index)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
        <el-tab-pane label="市场咨询" name="60">
          <el-row :gutter="20" class="pad_10_0">
            <el-col :span="10">
              <el-input placeholder="输入员工名称、编号搜索" size="small" v-model="searchChannelConsultName" clearable></el-input>
            </el-col>
            <el-col :span="14">
              <el-button type="primary" @click="addChannelEmployeeClick('市场咨询')" size="small">添加市场咨询 </el-button>
            </el-col>
          </el-row>
          <el-table size="small" :data="ruleFormAdd.ConsultantList.filter((i) => !searchChannelConsultName || i.EmployeeName.toLowerCase().includes(searchChannelConsultName.toLowerCase()))" max-height="450">
            <el-table-column prop="EmployeeName" label="市场咨询"></el-table-column>
            <el-table-column prop="PerformanceRate" label="业绩比例">
              <template slot-scope="scope">{{ scope.row.PerformanceRate }}%</template>
            </el-table-column>
            <el-table-column label="操作" width="145">
              <template slot-scope="scope">
                <el-button type="primary" size="mini" @click="editCosultItemClick(scope.row, scope.$index)">编辑 </el-button>
                <el-button type="danger" size="mini" @click="removeCosultListClick(scope.$index)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
        <el-tab-pane label="介绍人" name="second">
          <el-row :gutter="20" class="pad_10_0">
            <el-col :span="10">
              <el-input placeholder="输入渠道名称搜索" size="small" v-model="filterName" clearable></el-input>
            </el-col>
            <el-col :span="14">
              <el-button type="primary" @click="clickIntroducer" size="small">添加介绍人 </el-button>
            </el-col>
          </el-row>
          <el-table size="small" :data="IntroducerList.filter((data) => !filterName || data.Name.toLowerCase().includes(filterName.toLowerCase()))" max-height="450">
            <el-table-column prop="Name" label="渠道名称"></el-table-column>
            <el-table-column prop="ChannelTypeName" label="渠道类型"></el-table-column>
            <el-table-column prop="ParentName" label="上级渠道"></el-table-column>
            <el-table-column label="操作" width="80">
              <template slot-scope="scope">
                <el-button type="danger" size="mini" @click="deleteSelectedIntroducer(scope.row)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
        <el-tab-pane label="合同信息" name="30">
          <el-row :gutter="20" class="pad_10_0">
            <el-col :span="10">
              <el-input placeholder="输入合同编号搜索" size="small" v-model="ContractName" clearable></el-input>
            </el-col>
            <el-col :span="14">
              <el-button type="primary" @click="addContractClick" size="small">添加合同 </el-button>
            </el-col>
          </el-row>
          <el-table size="small" :data="ruleFormAdd.Contract.filter((i) => !ContractName || i.Code.toLowerCase().includes(ContractName.toLowerCase()))" max-height="450">
            <el-table-column prop="Code" label="合同编号"></el-table-column>
            <el-table-column prop="BeginDate" label="合同开始日期"></el-table-column>
            <el-table-column prop="EndDate" label="合同结束日期"></el-table-column>
            <el-table-column label="操作" width="145">
              <template slot-scope="scope">
                <el-button type="primary" size="mini" @click="editContractClick(scope.row, scope.$index)">编辑</el-button>
                <el-button type="danger" size="mini" @click="deleteContractClick(scope.$index)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
        <el-tab-pane label="法人信息" name="40">
          <el-row :gutter="20" class="pad_10_0">
            <el-col :span="10">
              <el-input placeholder="输入公司名称搜索" size="small" v-model="companyName" clearable></el-input>
            </el-col>
            <el-col :span="14">
              <el-button type="primary" @click="addCompanyClick" size="small">添加法人账户</el-button>
            </el-col>
          </el-row>
          <el-table size="small" :data="ruleFormAdd.Company.filter((i) => !companyName || i.Name.toLowerCase().includes(companyName.toLowerCase()))" max-height="450">
            <el-table-column prop="Name" label="公司名称"></el-table-column>
            <el-table-column prop="Amount" label="公司额度"></el-table-column>
            <el-table-column prop="LegalPerson" label="法人姓名"></el-table-column>
            <el-table-column prop="PhoneNumber" label="法人手机"></el-table-column>
            <el-table-column prop="BankName" label="开户行"></el-table-column>
            <el-table-column prop="BankBranchName" label="开户支行"></el-table-column>
            <el-table-column prop="BankAccount" label="开户行账号"></el-table-column>
            <el-table-column prop="Remark" label="备注信息"></el-table-column>
            <el-table-column label="操作" width="145">
              <template slot-scope="scope">
                <el-button type="primary" size="mini" @click="editCompanyClick(scope.row, scope.$index)">编辑</el-button>
                <el-button type="danger" size="mini" @click="deleteCompanyClick(scope.$index)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
      </el-tabs>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false" size="small">取 消</el-button>
        <el-button type="primary" size="small" @click="submitChannel('ruleFormAdd')" v-prevent-click :loading="modalLoading">保 存</el-button>
      </span>
    </el-dialog>
    <!-- 介绍人弹出框 -->
    <el-dialog title="添加介绍人" :visible.sync="addIntroducerDialog" width="900px">
      <el-row class="marbm_10">
        <el-col :span="6">
          <el-input v-model="IntroducerName" prefix-icon="el-icon-search" size="small" placeholder="请输入渠道名称" clearable @change="searchAddIntroducerList" @clear="searchAddIntroducerList"></el-input>
        </el-col>
        <el-col :span="4" class="marlt_5">
          <el-select v-model="IntroducerTypeId" placeholder="请选择渠道类型" filterable clearable @change="searchAddIntroducerList" @clear="searchAddIntroducerList" size="small">
            <el-option v-for="item in channelTypeList" :key="item.ID" :label="item.Name" :value="item.ID"> </el-option>
          </el-select>
        </el-col>
        <el-col :span="4" class="marlt_5">
          <el-button type="primary" size="small" @click="searchAddIntroducerList" v-prevent-click>搜 索</el-button>
        </el-col>
      </el-row>
      <el-table size="small" :row-key="getRowKeys" @cell-click="AddIntroducerListCellClick" @selection-change="selectionAddIntroducer_change" ref="multipleTable" :data="AddIntroducerList">
        <el-table-column type="selection" width="55" :reserve-selection="true"></el-table-column>
        <el-table-column prop="Name" label="渠道名称"></el-table-column>
        <el-table-column prop="ChannelTypeName" label="渠道类型"></el-table-column>
        <el-table-column prop="ParentName" label="上级渠道"></el-table-column>
      </el-table>

      <div class="pad_15 text_right">
        <el-pagination
          background
          v-if="AddIntroducerPaginations.total > 0"
          @current-change="handleAddIntroducerCurrentChange"
          :current-page.sync="AddIntroducerPaginations.page"
          :page-size="AddIntroducerPaginations.page_size"
          :layout="AddIntroducerPaginations.layout"
          :total="AddIntroducerPaginations.total"
        ></el-pagination>
      </div>
      <div slot="footer">
        <el-button @click="addIntroducerDialog = false" size="small" v-prevent-click>取 消</el-button>
        <el-button type="primary" @click="submitIntroducer" size="small" v-prevent-click>保 存</el-button>
      </div>
    </el-dialog>
    <!-- 上级渠道弹出框 -->
    <el-dialog title="上级渠道" :visible.sync="superiorChannelDialogVisible" width="400px" custom-class="custom-Dropdown">
      <el-form label-width="70px" size="small">
        <el-form-item>
          <span slot="label">上级渠道</span>
          <el-select v-model="ParentParentID" placeholder="请输入渠道信息搜索渠道" popper-class="custom_channelPopperClass" filterable remote reserve-keyword size="small" clearable :remote-method="searchChannelInfo" @focus="focusChannel" @clear="focusChannel">
            <el-option v-for="item in allChannelList" :key="item.ID" :label="item.Name" :value="item.ID">
              <div style="padding-bottom: 8px">
                <div>{{ item.Name }}</div>
                <div class="font_12 color_999">{{ item.ParentName }}</div>
              </div>
            </el-option>
          </el-select>
          <!-- <treeselect
            v-model="ParentParentID"
            :normalizer="normalizer"
            clearValueText
            :options="allChannelList"
            noResultsText="无匹配数据"
            placeholder="选择上级渠道"
          /> -->
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="superiorChannelDialogVisible = false" v-prevent-click>取 消</el-button>
        <el-button type="primary" size="small" :loading="modalLoading" v-prevent-click @click="submitDropdownParentIDClick">保存</el-button>
      </div>
    </el-dialog>

    <!-- 添加合同 -->
    <el-dialog title="添加合同" :visible.sync="contractDialog" width="900px">
      <el-form size="small" label-width="90px" :model="contractRuleFrom" :rules="contractRuleFromRules" ref="contractRuleFromRef">
        <el-form-item prop="Code">
          <span slot="label">合同编号</span>
          <el-input v-model="contractRuleFrom.Code"></el-input>
        </el-form-item>
        <el-form-item prop="ContractDates">
          <span slot="label">有效日期</span>
          <el-date-picker v-model="contractRuleFrom.ContractDates" unlink-panels type="daterange" range-separator="至" value-format="yyyy-MM-dd" start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
        </el-form-item>
        <el-form-item prop="Attachment">
          <span slot="label">上传合同</span>
          <el-upload action="#" list-type="picture-card" :before-upload="beforeFileUpload" :file-list="contractRuleFrom.Attachment" multiple>
            <!-- 加号图标 -->
            <i slot="default" class="el-icon-plus"></i>
            <div slot="file" slot-scope="{ file }">
              <el-image fit="cover" v-if="file.AttachmentType == '10'" class="el-upload-list__item-thumbnail" :src="file.AttachmentURL" alt="" :preview-src-list="contractRuleFrom.Attachment.filter((i) => i.AttachmentType == '10').map((i) => i.AttachmentURL)" style="height: 100%; width: 100%" />

              <div class="document_i" v-else>
                <div class="text_center">
                  <i v-if="checkFileType(file) == 'other'" class="el-icon-document" style="font-size: 90px; color: #aaaaaa; margin-top: 15px"></i>
                  <el-image v-else :src="require('@/assets/img/fileIcon/' + checkFileType(file) + '.png')" style="width: 90px; height: 90px; margin-top: 10px"></el-image>
                </div>
                <div class="pad_0_10 text_center" style="width: 100%; box-sizing: border-box">{{ changFileName(file.Name) }}.{{ file.MimeType }}</div>
              </div>
              <span class="el-upload-list__item-actions">
                <span v-if="file.AttachmentType == '10'" class="el-upload-list__item-preview" @click="handlePictureCardPreview(file)">
                  <i class="el-icon-zoom-in"></i>
                </span>
                <span class="el-upload-list__item-delete" @click="handleRemove(file)">
                  <i class="el-icon-delete"></i>
                </span>
              </span>
            </div>
          </el-upload>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="contractDialog = false" v-prevent-click>取 消</el-button>
        <el-button type="primary" size="small" :loading="modalLoading" v-prevent-click @click="submitContractDialog">保存 </el-button>
      </div>
    </el-dialog>

    <!-- 添加合同 -->
    <el-dialog title="新增法人" :visible.sync="companyDialog" width="800px">
      <el-form size="small" label-width="100px" :model="companyRuleFrom" :rules="companyRuleFromRules" ref="companyRuleFromRef">
        <el-row>
          <el-col :span="12">
            <el-form-item prop="Name">
              <span slot="label">公司名称</span>
              <el-input v-model="companyRuleFrom.Name"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="Amount">
              <span slot="label">公司额度</span>
              <el-input v-model="companyRuleFrom.Amount" v-input-fixed></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="LegalPerson">
              <span slot="label">法人姓名</span>
              <el-input v-model="companyRuleFrom.LegalPerson"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="PhoneNumber">
              <span slot="label">法人手机</span>
              <el-input v-model="companyRuleFrom.PhoneNumber"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="BankName">
              <span slot="label">开户行</span>
              <el-input v-model="companyRuleFrom.BankName"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="BankBranchName">
              <span slot="label">开户支行</span>
              <el-input v-model="companyRuleFrom.BankBranchName"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="BankAccount">
              <span slot="label">开户行账号</span>
              <el-input v-model="companyRuleFrom.BankAccount"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item prop="Remark">
              <span slot="label">备注信息</span>
              <el-input type="textarea" v-model="companyRuleFrom.Remark" :rows="3"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="companyDialog = false" v-prevent-click>取 消</el-button>
        <el-button type="primary" size="small" :loading="modalLoading" v-prevent-click @click="submitCompanyDialog">保存 </el-button>
      </div>
    </el-dialog>

    <!-- 开发人员  市场咨询人员 -->
    <el-dialog :title="empType" :visible.sync="channelEmpDialog" width="600px">
      <el-form size="small" label-width="100px" :model="addEmployeeRuleForm" :rules="addEmployeeRuleFormRules" ref="addEmployeeRuleFormRef">
        <el-form-item prop="EmployeeID">
          <span slot="label">{{ empType }}</span>
          <el-select v-model="addEmployeeRuleForm.EmployeeID" filterable placeholder="请输入员工名称搜索" popper-class="custom_channelPopperClass" @change="selectEmployee">
            <el-option v-for="item in EmployeeList" :key="item.ID" :label="item.Name" :value="item.ID">
              <div>{{ item.Name }}</div>
              <div class="font_12 color_999">职务：{{ item.JobName }}</div>
              <div class="font_12 color_999">部门：{{ item.EntityName }}</div>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item prop="PerformanceRate" label="业绩比例">
          <el-input v-model="addEmployeeRuleForm.PerformanceRate" v-input-fixed @input="changeEmployeePerformanceRate">
            <span slot="append">%</span>
          </el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="channelEmpDialog = false" v-prevent-click>取 消</el-button>
        <el-button type="primary" size="small" :loading="modalLoading" v-prevent-click @click="submitEmployeeDialog">保存 </el-button>
      </div>
    </el-dialog>

    <!-- 转义开发人员 -->
    <el-dialog :title="transferEmpType" :visible.sync="transferDialogVisible" width="500px">
      <el-form size="small" label-width="100px" :model="transferForm" :rules="transferFormRules" ref="transferEmployeeRuleFormRef">
        <el-form-item prop="OutEmployeeID" label="转出员工">
          <el-select v-model="transferForm.OutEmployeeID" filterable placeholder="请输入员工名称搜索" popper-class="custom_channelPopperClass" >
            <el-option v-for="item in EmployeeList" :key="item.ID" :label="item.Name" :value="item.ID">
              <div>{{ item.Name }}</div>
              <div class="font_12 color_999">职务：{{ item.JobName }}</div>
              <div class="font_12 color_999">部门：{{ item.EntityName }}</div>
            </el-option>
          </el-select>
        </el-form-item>

        
        <el-form-item prop="InEmployeeID" label="转入员工">
          <el-select v-model="transferForm.InEmployeeID" filterable placeholder="请输入员工名称搜索" popper-class="custom_channelPopperClass" >
            <el-option v-for="item in EmployeeList" :key="item.ID" :label="item.Name" :value="item.ID">
              <div>{{ item.Name }}</div>
              <div class="font_12 color_999">职务：{{ item.JobName }}</div>
              <div class="font_12 color_999">部门：{{ item.EntityName }}</div>
            </el-option>
          </el-select>
        </el-form-item>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="transferDialogVisible = false" v-prevent-click>取 消</el-button>
        <el-button type="primary" size="small" :loading="transferLoading" v-prevent-click @click="submitTransferEmployeeClick">保存 </el-button>
      </div>
    </el-dialog>

    <el-image-viewer v-if="showViewer" :initialIndex="initialIndex" :on-close="closeViewer" :url-list="previewImage" :z-index="3005" />
  </div>
</template>

<script>
import API from "@/api/CRM/Channel/channelInfo";
import channelLevelAPI from "@/api/CRM/Channel/channelLevel.js";
import channelTypeAPI from "@/api/CRM/Channel/channelType";
import uploadAPI from "@/api/Common/uploadAttachment.js";
import ElImageViewer from "element-ui/packages/image/src/image-viewer";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import { regionData, CodeToText } from "element-china-area-data";

import mapContainer from "@/components/gdMap/mapContainer.vue";

var Enumerable = require("linq");
// var placeSearch;
export default {
  name: "ChannelInfo",
  props: {},
  /**  引入的组件  */
  components: { ElImageViewer, mapContainer },
  /**  Vue 实例的数据对象**/
  data() {
    return {
      transferLoading:false,
      transferDialogVisible:false,
      isExport: false,
      downloadLoading: false,
      empLoading: false,
      isEditCompany: false,
      editCompanyIndex: 0,
      isEditContract: false,
      editContractIndex: 0,
      isEdit: false,
      loading: false,
      contractDialog: false,
      companyDialog: false,
      modalLoading: false,
      channelEmpDialog: false,
      addIntroducerDialog: false, // 介绍人弹出层
      superiorChannelDialogVisible: false, // 上级渠道弹出框
      businessAgentDialogVisible: false, // 业务代表弹出框
      dialogVisible: false, // 新增、编辑弹出框
      addorganization: false, // 新增组织弹出层
      Name: "",
      activeName: "first", // 当前标签页
      salesName: "", // 左侧树形搜索条件
      channelName: "", // 渠道名称
      ChannelTypeID: "", // 渠道类型
      ChannelLevelID: "", //渠道等级
      ParentID: null, // 上级渠道
      allChannelList: [], //渠道
      EmployeeID: "", // 业务代表
      DeveloperID: null, //开发人员ID
      ConsultantID: null, //咨询人员ID

      ParentEmployeeID: "", // 批量设置业务代表
      ParentParentID: null, // 批量设置上级渠道
      Active: true, // 有效性
      filterName: "",
      IntroducerName: "",
      IntroducerTypeId: "",
      regionData: regionData, // 省市区
      regionDataSelArr: [], // 已选择省市区
      IntroducerList: [],
      ContractName: "", //搜索合同名称
      companyName: "", // 法人-公司搜索名称
      AddIntroducerList: [],
      entityTreeList: [],
      selectIntroducerList: [], // 选中的介绍人
      multipleSelection: [], // 右边表格选择
      channelNum: "", // 右边表格选中数量
      ChannelIDList: [], // 右边表格选中的ID集合
      ChannelID: "",
      cascaderProps: {},
      organDefaultExpandedKeys: [1],
      channelTypeList: [], // 渠道类型
      EmployeeList: [], // 业务代表
      treeData: [], // 左侧树形列表数据
      tableData: [], // 表格数据
      treeParentID: "",
      ruleFormAdd: {
        Name: "",
        ChannelTypeID: "",
        EmployeeID: "",
        ParentID: null,
        ContactPersonName: "",
        ContactPersonMobile: "",
        ContactPersonIDNumber: "",
        ProvinceCode: "",
        CityCode: "",
        AreaCode: "",
        AddressDetail: "",
        Remark: "",
        Active: "",
        ChannelLevelID: "",
        Longitude: "",
        Latitude: "",
        Contract: [],
        Company: [],
        IntroducerList: [],
        DeveloperList: [],
        ConsultantList: [],
      },
      rules_add: {
        Name: [{ required: true, message: "请输入业务渠道", trigger: "change" }],
        ChannelTypeID: [{ required: true, message: "请选择渠道类型", trigger: "change" }],
        Active: [{ required: true, message: "请选择有效性", trigger: "change" }],
      },
      paginations: {
        page: 1, // 当前位于哪页
        total: 0, // 总数
        page_size: 10, // 1页显示多少条
        layout: "total, prev, pager, next,jumper", // 翻页属性
      },
      AddIntroducerPaginations: {
        page: 1, // 当前位于哪页
        total: 0, // 总数
        page_size: 10, // 1页显示多少条
        layout: "total, prev, pager, next, jumper", // 翻页属性
      }, //介绍人弹出框需要给分页组件传的信息
      defaultProps: {
        children: "Child",
        label: "Name",
      },
      // batchRuleFromEmpID: {
      //   ParentEmployeeID: "",
      // },
      // batchEmpIDRules: {
      //   ParentEmployeeID: [{ required: true, message: "请选择业务代表", trigger: "change" }],
      // },
      channelLevelList: [],
      // gdAMap: null,
      contractRuleFrom: {
        Code: "", //合同编号
        ContractDates: null,
        BeginDate: "", //开始时间
        EndDate: "", //结束时间
        Attachment: [], //附
      },
      contractRuleFromRules: {
        Code: [{ required: true, message: "请填写合同编号", trigger: "change" }],
        ContractDates: [{ required: true, message: "请选择合同日期", trigger: "change" }],
      },
      companyRuleFrom: {
        Name: "", //名称
        LegalPerson: "", //法人
        PhoneNumber: "", //手机号
        BankName: "", //开户行
        BankBranchName: "", //开户支行
        BankAccount: "", //开户账号
        Amount: null, //金额
        Remark: "", //备注
      },
      companyRuleFromRules: {
        Name: [{ required: true, message: "请输入公司名称", trigger: ["change", "blur"] }],
        LegalPerson: [{ required: true, message: "请输入法人姓名", trigger: ["change", "blur"] }],
        PhoneNumber: [{ required: true, message: "请输入手机号", trigger: ["change", "blur"] }],
        BankName: [{ required: true, message: "请输入开户行", trigger: ["change", "blur"] }],
        BankBranchName: [{ required: true, message: "请输入支行信息", trigger: ["change", "blur"] }],
        BankAccount: [{ required: true, message: "请输入开户行账号", trigger: ["change", "blur"] }],
        Amount: [{ required: true, message: "请输入公司额度", trigger: ["change", "blur"] }],
      },
      addEmployeeRuleForm: {
        EmployeeID: "",
        EmployeeName: "",
        PerformanceRate: "",
      },
      addEmployeeRuleFormRules: {
        EmployeeID: [{ required: true, message: "请选择员工信息", trigger: ["change", "blur"] }],
        PerformanceRate: [{ required: true, message: "请输入业绩比例", trigger: ["change", "blur"] }],
      },

      searchChannelDevName: "",
      searchChannelConsultName: "",
      empType: "开发人员",
      editDevIndex: 0,
      editCosultIndex: 0,
      isEmpEdit: false,

      showViewer: false,
      initialIndex: 0,
      previewImage: [],
      markOption: null,
      transferForm: {
        OutEmployeeID: "", //转出员工编号
        InEmployeeID: "", //转入员工编号
      },
      
      transferFormRules: {
        OutEmployeeID: [{ required: true, message: "请选择转出员工", trigger: "change" }],
        InEmployeeID: [{ required: true, message: "请选择转入员工", trigger: "change" }],
      },
      transferEmpType:"",
    };
  },
  /**计算属性  */
  computed: {},
  /**  方法集合  */
  methods: {
    /**  保存转移人员  */
    submitTransferEmployeeClick(){
      let that = this;
      that.$refs.transferEmployeeRuleFormRef.validate(valid=>{
        if (valid) {
          if (that.transferEmpType == "转移开发人员") {
            that.channel_transferDeveloper();
          }
          else{
            that.channel_transferConsultant();
          }
        }
      });
    },
    /**    */
    transferDeveloperClick(title){
      let that = this;
      that.transferEmpType = title;
      that.transferDialogVisible = true;
      that.transferForm = {
        OutEmployeeID: "", //转出员工编号
        InEmployeeID: "", //转入员工编号
      };
    },
    /**    */
    closeViewer() {
      let that = this;
      that.showViewer = false;
    },
    /**  预览文件  */
    handlePictureCardPreview(file) {
      let that = this;
      that.previewImage = that.contractRuleFrom.Attachment.filter((i) => i.AttachmentType == "10").map((i) => i.AttachmentURL);
      that.initialIndex = that.previewImage.findIndex((i) => i == file.AttachmentURL);
      that.showViewer = true;
    },
    /**  删除文件  */
    handleRemove(file) {
      let that = this;
      let index = that.contractRuleFrom.Attachment.findIndex((val) => val.uid == file.uid);
      that.contractRuleFrom.Attachment.splice(index, 1);
    },
    /**  格式华 开发人员  */
    developerFormatter(row) {
      return row.DeveloperList.map((i) => i.EmployeeName).join(",");
    },
    /**  格式华 市场咨询  */
    consultantFormatter(row) {
      return row.ConsultantList.map((i) => i.EmployeeName).join(",");
    },
    /**  编辑市场咨询  */
    editCosultItemClick(row, index) {
      let that = this;
      that.isEmpEdit = true;
      that.empType = "市场咨询";
      that.editCosultIndex = index;
      that.addEmployeeRuleForm = {
        EmployeeID: row.EmployeeID,
        EmployeeName: row.EmployeeName,
        PerformanceRate: row.PerformanceRate,
      };
      that.channelEmpDialog = true;
    },
    /**  编辑开发人员  */
    editDeveloperItemClick(row, index) {
      let that = this;
      that.isEmpEdit = true;
      that.editDevIndex = index;
      that.empType = "开发人员";
      that.addEmployeeRuleForm = {
        EmployeeID: row.EmployeeID,
        EmployeeName: row.EmployeeName,
        PerformanceRate: row.PerformanceRate,
      };
      that.channelEmpDialog = true;
    },
    /**  修改比例值  */
    changeEmployeePerformanceRate(val) {
      let that = this;
      if (val > 100) {
        that.addEmployeeRuleForm.PerformanceRate = 100;
      }
    },
    /**  删除市场咨询  */
    removeCosultListClick(index) {
      let that = this;
      that.ruleFormAdd.ConsultantList.splice(index, 1);
    },
    /**  删除开发人员  */
    removeDeveloperClick(index) {
      let that = this;
      that.ruleFormAdd.DeveloperList.splice(index, 1);
    },
    // /**    */
    // handleChangeClick() {
    //   if (this.activeName == "50") {
    //     this.empType = "开发人员";
    //   }
    //   if (this.activeName == "60") {
    //     this.empType = "市场咨询";
    //   }
    // },
    /**    */
    selectEmployee(val) {
      let that = this;
      let tmp = that.EmployeeList.filter((i) => i.ID == val);
      that.addEmployeeRuleForm.EmployeeName = tmp[0].Name;
    },
    /**    */
    submitEmployeeDialog() {
      let that = this;
      that.$refs.addEmployeeRuleFormRef.validate((valid) => {
        if (valid) {
          if (that.isEmpEdit) {
            if (that.empType == "开发人员") {
              that.ruleFormAdd.DeveloperList.splice(that.editDevIndex, 1, that.addEmployeeRuleForm);
            }
            if (that.empType == "市场咨询") {
              that.ruleFormAdd.ConsultantList.splice(that.editCosultIndex, 1, that.addEmployeeRuleForm);
            }
          } else {
            if (that.empType == "开发人员") {
              if (that.ruleFormAdd.DeveloperList.some((i) => i.EmployeeID == that.addEmployeeRuleForm.EmployeeID)) {
                that.$message.error("该员工已存在");
                return;
              }
              that.ruleFormAdd.DeveloperList.push(that.addEmployeeRuleForm);
            }
            if (that.empType == "市场咨询") {
              if (that.ruleFormAdd.ConsultantList.some((i) => i.EmployeeID == that.addEmployeeRuleForm.EmployeeID)) {
                that.$message.error("该员工已存在");
                return;
              }
              that.ruleFormAdd.ConsultantList.push(that.addEmployeeRuleForm);
            }
          }
          that.channelEmpDialog = false;
        }
      });
    },
    /**    */
    addChannelEmployeeClick(type) {
      let that = this;
      that.empType = type;
      that.isEmpEdit = false;
      that.addEmployeeRuleForm = {
        EmployeeID: "",
        EmployeeName: "",
        PerformanceRate: "",
      };
      that.channelEmpDialog = true;
    },
    /**   编辑法人信息 */
    editCompanyClick(row, index) {
      let that = this;
      that.isEditCompany = true;
      that.editCompanyIndex = index;
      that.companyRuleFrom = Object.assign({}, row);
      that.companyDialog = true;
    },
    /**  删除 法人信息  */
    deleteCompanyClick(index) {
      let that = this;
      that.ruleFormAdd.Company.splice(index, 1);
    },
    /**  保存 法人信息   */
    submitCompanyDialog() {
      let that = this;
      that.$refs.companyRuleFromRef.validate((validate) => {
        if (validate) {
          if (that.isEditCompany) {
            that.ruleFormAdd.Company.splice(that.editCompanyIndex, 1, that.companyRuleFrom);
          } else {
            that.ruleFormAdd.Company.push(that.companyRuleFrom);
          }

          that.companyDialog = false;
        }
      });
    },
    /**   添加法人信息 */
    addCompanyClick() {
      let that = this;
      that.isEditCompany = false;
      that.companyRuleFrom = {
        Name: "", //名称
        LegalPerson: "", //法人
        PhoneNumber: "", //手机号
        BankName: "", //开户行
        BankBranchName: "", //开户支行
        BankAccount: "", //开户账号
        Amount: null, //金额
        Remark: "", //备注
      };
      that.companyDialog = true;
    },
    /**  编辑合同信息  */
    editContractClick(row, index) {
      let that = this;
      that.isEditContract = true;
      that.editContractIndex = index;
      that.contractRuleFrom = Object.assign({}, row);
      that.contractRuleFrom.ContractDates = [that.contractRuleFrom.BeginDate, that.contractRuleFrom.EndDate];
      that.contractDialog = true;
    },
    /**  删除合同  */
    deleteContractClick(index) {
      let that = this;
      that.ruleFormAdd.Contract.splice(index, 1);
    },
    /**  格式化 合同有效期  */
    contractDateFormatter(row) {
      return row.BeginDate + " 至 " + row.EndDate;
    },
    /**  文件上传前回调   */
    beforeFileUpload(file) {
      let that = this;
      if (file.size > 10 * 1024 * 1024) {
        this.$message.error("上传文件不可大于10M");
        return false;
      }
      let index = file.name.lastIndexOf(".");
      //获取名称
      let name = file.name.substr(0, index);
      // 获取后缀
      let tailName = file.name.substr(index + 1);
      // 判断 文件是否是视频
      if (/image\//.test(file.type)) {
        that.uploadFileNetwork(file, name, tailName, "10");
      } else if (that.checkPictureName(file.type)) {
        that.uploadFileNetwork(file, name, tailName, "30");
      } else {
        that.$message.error("请上传图片或者 PDF 格式文件");
      }
      return false;
    },
    // 校验是否PDF
    checkPictureName(str) {
      var strRegex = "(.pdf)$"; //用于验证后缀是否是pdf
      var re = new RegExp(strRegex);
      if (re.test(str.toLowerCase())) {
        // PDF
        return true;
      } else {
        return false;
      }
    },
    /** 修改文件名称 超过五个字 前三 后二 中间以... 代替   */
    changFileName(file_name) {
      if (file_name.length > 5) {
        return file_name.substring(0, 3) + "..." + file_name.substring(file_name.length - 2, file_name.length);
      }
      return file_name;
    },
    /**  验证 文件 类型  */
    checkFileType(file) {
      let type = file.MimeType;
      /**  excel  */
      if (type === "xlsx" || type === "xls" || type === "xltx" || type === "xlt" || type === "xlsm" || type === "xlsb" || type === "xltm" || type === "csv") {
        return "excel";
      }
      /**  word  */
      if (type === "doc" || type === "docx") {
        return "word";
      }
      /**  pdf  */
      if (type === "pdf") {
        return "pdf";
      }
      /**  ppt  */
      if (type === "ppt" || type === "pptx") {
        return "ppt";
      }

      let video_type = ["avi", "wmv", "mpg", "mpeg", "mov", "rm", "ram", "swf", "flv", "mp4", "wma", "avi", "rm", "rmvb", "flv", "mpg", "mkv"];
      if (video_type.some((val) => val == type.toLowerCase())) {
        return "video";
      }

      let audio_types = ["mov", "mp3"];
      if (audio_types.some((val) => val == type.toLowerCase())) {
        return "audio";
      }

      let compressed_types = ["zip", "rar", "7z", "gz"];
      if (compressed_types.some((val) => val == type.toLowerCase())) {
        return "compressed";
      }
      return "other";
    },
    /**  保存合同信息  */
    submitContractDialog() {
      let that = this;
      that.$refs.contractRuleFromRef.validate((valid) => {
        if (valid) {
          if (that.isEditContract) {
            that.contractRuleFrom.BeginDate = that.contractRuleFrom.ContractDates[0];
            that.contractRuleFrom.EndDate = that.contractRuleFrom.ContractDates[1];
            that.ruleFormAdd.Contract.splice(that.editContractIndex, 1, that.contractRuleFrom);
          } else {
            that.contractRuleFrom.BeginDate = that.contractRuleFrom.ContractDates[0];
            that.contractRuleFrom.EndDate = that.contractRuleFrom.ContractDates[1];
            that.ruleFormAdd.Contract.push(that.contractRuleFrom);
          }
          that.contractDialog = false;
        }
      });
    },
    /**  添加合同  */
    addContractClick() {
      this.isEditContract = false;
      this.contractRuleFrom = {
        Code: "", //合同编号
        BeginDate: "", //开始时间
        EndDate: "", //结束时间
        Attachment: [], //附
      };
      this.contractDialog = true;
    },
    /**  高德地图 配置  */
    // gaodeAMapLoaderConf() {
    //   let that = this;
    //   /**  设置定图  */
    //   that.gdAMap = new gdMaps({
    //     dom: "gdmapContainer",
    //     setting: {
    //       zoom: 11, //设置地图缩放级别PC取值范围为3-18，移动端为3-19
    //       resizeEnable: true, //是否监控地图容器尺寸变化
    //     },
    //   });
    //   /**  创建地图  */
    //   that.gdAMap.createAMapLoader().then((res) => {
    //     that.AMap = res.AMap;
    //     that.gdAMap.setGdControl(res.AMap);
    //     // 点击地图
    //     res.gdmap.on("click", function (val) {
    //       that.ruleFormAdd.Longitude = val.lnglat.lng;
    //       that.ruleFormAdd.Latitude = val.lnglat.lat;
    //       if (placeSearch) {
    //         placeSearch.clear();
    //         if (placeSearch.render) {
    //           placeSearch.render.markerList.clear();
    //         }
    //       }
    //       /* 根据经纬度 转换  element-china-area-data code */
    //       that.gdAMap.getTextToCodeCode(val.lnglat).then((val) => {
    //         that.regionDataSelArr = [...val.codes];
    //         that.ruleFormAdd.AddressDetail = val.address;
    //       });
    //     });
    //   });
    // },
    /** 地点搜索服务插件，提供某一特定地区的位置查询服务   */
    getPlaceSearch() {
      let that = this;
      let cityName = "";
      if (that.regionDataSelArr.length > 1) {
        cityName = CodeToText[that.regionDataSelArr[1]];
        cityName = cityName == "市辖区" ? CodeToText[that.regionDataSelArr[0]] : cityName;
      }
      that.$refs.map_container.mapAddressPlaceSearch(that.ruleFormAdd.AddressDetail, cityName);
    },
    /** 搜索结果选择   */
    searchAddresAddListener(res) {
      let that = this;
      that.regionDataSelArr = [...res.codes];
      that.ruleFormAdd.AddressDetail = res.address;
      that.ruleFormAdd.Longitude = res.location.lng;
      that.ruleFormAdd.Latitude = res.location.lat;
    },
    /**  地图选点  */
    mapSelection(res) {
      let that = this;
      that.regionDataSelArr = [...res.codes];
      that.ruleFormAdd.AddressDetail = res.address;
      that.ruleFormAdd.Longitude = res.location.lng;
      that.ruleFormAdd.Latitude = res.location.lat;
    },

    /* 搜索 */
    handleSearch() {
      let that = this;
      that.paginations.page = 1;
      that.getChannelList();
    },
    /* 分页 */
    handleCurrentChange(page) {
      let that = this;
      that.paginations.page = page;
      that.getChannelList();
    },
    /* 新增 */
    addChannelInfo() {
      let that = this;
      that.isEdit = false;
      that.activeName = "first";
      that.IntroducerList = [];
      that.ruleFormAdd = {
        Name: "",
        ChannelTypeID: "",
        EmployeeID: "",
        ParentID: null,
        ContactPersonName: "",
        ContactPersonMobile: "",
        ContactPersonIDNumber: "",
        ProvinceCode: "",
        CityCode: "",
        AreaCode: "",
        AddressDetail: "",
        Remark: "",
        Active: "",
        ChannelLevelID: "",
        Longitude: "",
        Latitude: "",
        Contract: [],
        Company: [],
        IntroducerList: [],
        DeveloperList: [],
        ConsultantList: [],
      };
      that.filterName = "";
      that.regionDataSelArr = [];
      if (this.$refs.ruleFormAdd) {
        this.$refs["ruleFormAdd"].resetFields();
      }
      // that.gaodeAMapLoaderConf();
      that.dialogVisible = true;
    },
    /* 编辑 */
    editChannelInfo(row) {
      let that = this;
      that.isEdit = true;
      that.activeName = "first";
      that.ChannelID = row.ID;
      row.ParentID = row.ParentID == 0 ? null : row.ParentID;
      that.contractRuleFrom.Attachment = [];
      that.getChannelDetail(that.ChannelID);
    },
    // 新增组织弹窗
    addEntityModal: function () {
      let that = this;
      that.addorganization = true;
      // that.gaodeAMapLoaderConf();
    },
    /* 删除选择介绍人 */
    deleteSelectedIntroducer(val) {
      var that = this;
      that.IntroducerList.splice(
        that.IntroducerList.findIndex((p) => p.ID == val.ID),
        1
      );
    },
    /* 列表批量设置选择 */
    batchSettingSelection(val) {
      var that = this;
      var arr = [];
      that.multipleSelection = val;
      that.channelNum = that.multipleSelection.length;
      for (let i = 0; i <= that.multipleSelection.length - 1; i++) {
        arr.push(that.multipleSelection[i].ID);
      }
      that.ChannelIDList = arr;
    },
    /* 批量设置 */
    dropdownClick(index) {
      let that = this;
      if (that.channelNum == 0) {
        that.$message.error({
          message: "请选择渠道",
          duration: 2000,
        });
        return false;
      }
      if (index == 1) {
        that.superiorChannelDialogVisible = true;
        that.ParentParentID = null;
      } else if (index == 2) {
        // that.businessAgentDialogVisible = true;
        // that.batchRuleFromEmpID.ParentEmployeeID = "";
        // if (this.$refs.batchRuleFromEmp) {
        //   this.$refs.batchRuleFromEmp.clearValidate();
        //   this.$refs["batchRuleFromEmp"].resetFields();
        // }
      }
    },
    /* 介绍人添加 */
    clickIntroducer() {
      let that = this;
      that.addIntroducerDialog = true;
      that.IntroducerName = "";
      that.IntroducerTypeId = "";
      that.getIntroducerList();
      this.$nextTick(() => {
        that.$nextTick(() => {
          that.AddIntroducerList.forEach((val) => {
            that.$refs.multipleTable.toggleRowSelection(val, false);
          });
        });
        if (that.IntroducerList.length > 0) {
          var defaultCheckedKeys = Enumerable.from(that.IntroducerList)
            .select((val) => val.IntroducerID)
            .toArray();
          defaultCheckedKeys.forEach((item) => {
            that.AddIntroducerList.forEach((val) => {
              if (item == val.ID) {
                that.$nextTick(() => {
                  that.$refs.multipleTable.toggleRowSelection(val);
                });
              }
            });
          });
        }
      });
    },
    /* 获取介绍人列表 */
    getIntroducerList() {
      let that = this;
      let params = {
        Name: that.IntroducerName,
        ChannelTypeID: that.IntroducerTypeId,
        ParentID: "",
        EmployeeID: "",
        Active: true,
        PageNum: that.AddIntroducerPaginations.page,
      };
      API.getChannelList(params).then((res) => {
        if (res.StateCode == 200) {
          that.AddIntroducerList = res.List;
          that.AddIntroducerPaginations.total = res.Total;
          that.AddIntroducerPaginations.page_size = res.PangSize;
        }
      });
    },
    /* 介绍人搜索 */
    searchAddIntroducerList() {
      let that = this;
      that.AddIntroducerPaginations.page = 1;
      that.getIntroducerList();
    },
    /* 介绍人分页 */
    handleAddIntroducerCurrentChange(page) {
      let that = this;
      that.AddIntroducerPaginations.page = page;
      that.getIntroducerList();
    },
    /* 保存介绍人 */
    submitIntroducer() {
      let that = this;
      if (that.selectIntroducerList.length == 0) {
        that.$message.error({
          message: "您未选择介绍人, 请先选择介绍人",
          duration: 2000,
        });
        return;
      }
      that.IntroducerList = [];
      that.IntroducerList = Enumerable.from(that.selectIntroducerList)
        .distinct((i) => i.ID)
        .select((i) => {
          return {
            IntroducerID: i.ID,
            Name: i.Name,
            ParentID: i.ParentID,
            ParentName: i.ParentName,
            ChannelTypeName: i.ChannelTypeName,
          };
        })
        .toArray();
      that.addIntroducerDialog = false;
    },
    /* 设置key */
    getRowKeys(row) {
      return row.ID;
    },
    /* 点击介绍人行 */
    AddIntroducerListCellClick(row) {
      this.$refs.multipleTable.toggleRowSelection(row);
    },
    /* 当前选中值改变 */
    selectionAddIntroducer_change(selection) {
      let that = this;
      that.selectIntroducerList = selection;
    },

    /* 获取渠道类型 */
    getChannelTypeList() {
      var that = this;
      var params = {
        Name: "",
        Active: "",
      };
      channelTypeAPI
        .getChannelTypeList(params)
        .then((res) => {
          if (res.StateCode == 200) {
            res.Data.map((item) => {
              if (item.Active) {
                that.channelTypeList.push(item);
              }
            });
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },
    /* 获取业务代表 */
    geTemployeeAll() {
      let that = this;
      let params = {};
      API.geTemployeeAll(params).then((res) => {
        if (res.StateCode == 200) {
          that.EmployeeList = res.Data;
        }
      });
    },
    /* 左侧树形列表搜索 */
    contactQuery() {
      let that = this;
      that.getTreeList();
      this.paginations.page = 1;
      that.Name = that.salesName;
      that.getChannelList();
    },
    /* 左侧树形列表点击 */
    handleNodeClick(obj) {
      this.getAllchannel(obj.Name, obj.ID);
    },
    /* 拖拽成功完成时触发的事件
    draggingNode  当前拖拽的节点信息
    dropNode      拖拽时最后进入的节点
    dropType      被拖拽节点的放置位置（before、after、inner）
     */
    handleDrop(draggingNode, dropNode, dropType) {
      var MoveChannelID = draggingNode.data.ID;
      var DestParentID = "";
      var BeforeChannelID = "";

      if (dropType == "inner") {
        DestParentID = dropNode.data.ID;
        dropNode.data.Child.forEach(function (item, index) {
          if (item.ID == draggingNode.data.ID) {
            if (index > 0) {
              BeforeChannelID = dropNode.data.Child[index - 1].ID;
            }
          }
        });
      } else if (dropType == "before") {
        DestParentID = dropNode.parent.data.ID;
        dropNode.parent.data.Child.forEach(function (item, index) {
          if (item.ID == draggingNode.data.ID) {
            if (index > 0) {
              BeforeChannelID = dropNode.parent.data.Child[index - 1].ID;
            }
          }
        });
      } else {
        BeforeChannelID = dropNode.data.ID;
        DestParentID = dropNode.parent.data.ID;
      }
      this.dropEntity(MoveChannelID, DestParentID, BeforeChannelID);
    },
    allowDrag(node) {
      let that = this;
      let ID = "";
      that.treeData.map((item) => {
        ID = item.ID;
      });
      return node.data.ID != ID;
    },
    allowDrop(draggingNode, dropNode, type) {
      let that = this;
      let ID = "";
      that.treeData.map((item) => {
        ID = item.ID;
      });
      return !((type == "prev" || type == "next") && dropNode.data.ID == ID);
    },
    // 顺序切换
    /**    */
    /**
     * @description:  渠道属性结构 移动位置
     * @param {*} MoveChannelID 当前移动这的渠道ID
     * @param {*} DestParentID  移入的渠道父级ID
     * @param {*} BeforeChannelID 当前渠道 之前的父级ID
     * @return {*}
     */
    dropEntity: function (MoveChannelID, DestParentID, BeforeChannelID) {
      var that = this;
      var params = {
        MoveChannelID: MoveChannelID,
        DestParentID: DestParentID,
        BeforeChannelID: BeforeChannelID,
      };
      API.moveChannel(params).then((res) => {
        if (res.StateCode == 200) {
          that.getTreeList();
          that.getAllchannel();
          that.organDefaultExpandedKeys = [params.MoveChannelID];
        } else {
          that.$message.error({
            message: res.Message,
            duration: 2000,
          });
        }
      });
    },
    /* 树形结构数据转换 */
    normalizer(node) {
      return {
        id: node.ID,
        label: node.Name,
        children: node.Child,
      };
    },

    /**    */

    /* 新增、编辑渠道保存 */
    submitChannel(formName) {
      let that = this;
      that.$refs[formName].validate((valid) => {
        if (valid) {
          var defaultCheckedKeys = Enumerable.from(that.IntroducerList)
            .select((val) => val.IntroducerID)
            .toArray();
          that.ruleFormAdd.IntroducerList = defaultCheckedKeys.map((item) => {
            return { IntroducerID: item };
          });
          if (that.isEdit) {
            that.updateChannel();
          } else {
            that.creatChannel();
          }
        }
      });
    },
    /* 新增 */
    creatChannel() {
      let that = this;
      that.modalLoading = true;
      let params = Object.assign({}, that.ruleFormAdd);
      params.Active = true;
      if (that.regionDataSelArr.length) {
        params.ProvinceCode = that.regionDataSelArr[0];
        params.CityCode = that.regionDataSelArr[1];
        params.AreaCode = that.regionDataSelArr[2];
      }
      API.creatChannel(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.$message.success("新增成功");
            that.dialogVisible = false;
            that.handleSearch();
            that.getTreeList();
            that.getAllchannel();
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.modalLoading = false;
        });
    },
    /* 更新 */
    updateChannel() {
      let that = this;
      that.modalLoading = true;
      let params = Object.assign({}, that.ruleFormAdd);
      if (that.regionDataSelArr.length) {
        params.ProvinceCode = that.regionDataSelArr[0];
        params.CityCode = that.regionDataSelArr[1];
        params.AreaCode = that.regionDataSelArr[2];
      }
      API.updateChannel(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.$message.success("更新成功");
            that.dialogVisible = false;
            that.handleSearch();
            that.getTreeList();
            that.getAllchannel();
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.modalLoading = false;
        });
    },
    /* 获取渠道配置列表 */
    getChannelList() {
      let that = this;
      that.loading = true;
      let params = {
        Name: that.Name,
        ChannelTypeID: that.ChannelTypeID,
        ParentID: that.ParentID,
        EmployeeID: that.EmployeeID,
        Active: that.Active,
        PageNum: that.paginations.page,
        ChannelLevelID: that.ChannelLevelID,
        DeveloperID: that.DeveloperID, //开发人员ID
        ConsultantID: that.ConsultantID, //咨询人员ID
      };
      API.getChannelList(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.tableData = res.List;
            that.paginations.total = res.Total;
            that.paginations.page_size = res.PangSize;
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },

    /* 左侧树形列表获取 */
    getTreeList() {
      let that = this;
      let params = {
        Name: that.salesName,
      };
      API.getTreeList(params).then((res) => {
        if (res.StateCode == 200) {
          that.treeData = res.Data;
        }
      });
    },

    /**  渠道来源获取焦点 清除数据  */
    focusChannel() {
      let that = this;
      that.allChannelList = [];
    },
    /**    */
    searchChannelInfo(value) {
      this.getAllchannel(value);
    },
    /*  获取渠道 顶部筛选条件 */
    getAllchannel(value, ID) {
      let that = this;
      let params = { Name: value, Active: true };
      API.getChannelInfoList(params).then((res) => {
        if (res.StateCode == 200) {
          // this.setEditNodeDisabled(res.Data);
          that.allChannelList = res.Data;
          if (ID) {
            that.ParentID = ID;
            this.paginations.page = 1;
            that.getChannelList();
          }
        }
      });
    },
    /* 批量设置上级渠道保存 */
    submitDropdownParentIDClick() {
      let that = this;
      let ChannelList = [];
      that.ChannelIDList.map((item) => {
        ChannelList.push({
          ChannelID: item,
          ParentID: that.ParentParentID ? that.ParentParentID : 0,
        });
      });
      let params = {
        ChannelList: ChannelList,
        EmployeeList: [],
      };
      API.updateParentChannel(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.$message.success("上级渠道批量设置成功");
            that.superiorChannelDialogVisible = false;
            that.handleSearch();
            that.getTreeList();
            that.getAllchannel();
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.modalLoading = false;
        });
    },

    /** 渠道等级 列表   */
    async channelLevel_list() {
      let that = this;
      let params = {
        Name: "", //等级名称
        Active: true, //有效性
      };
      let res = await channelLevelAPI.channelLevel_list(params);
      if (res.StateCode == 200) {
        that.channelLevelList = res.Data;
      } else {
        that.$message.error(res.Message);
      }
    },
    /**  文件上传  */
    async uploadFileNetwork(file, fileName, fileTailName, type) {
      let that = this;
      let params = {
        file: file,
      };
      let res = await uploadAPI.uploadFile_network(params);
      if (res.StateCode == 200) {
        that.contractRuleFrom.Attachment.push({
          AttachmentType: type, //附件类型(10：图片，20：文件)
          AttachmentURL: res.Data.VideoId,
          Name: fileName, //文件名
          MimeType: fileTailName,
        });
      } else {
        that.$message.error(res.Message);
      }
    },
    /**   获取渠道详情 */
    async getChannelDetail(ID) {
      let that = this;
      let params = { ID: ID };
      let res = await API.getChannelDetail(params);
      if (res.StateCode == 200) {
        let tmp = Object.assign({}, res.Data);
        tmp.DeveloperList = tmp.Developer;
        tmp.ConsultantList = tmp.Consultant;
        if (!tmp.ParentID) {
          tmp.ParentID = "";
        }
        delete tmp.Developer;
        delete tmp.Consultant;
        that.ruleFormAdd = tmp;
        that.IntroducerList = Object.assign([], res.Data.IntroducerList);
        that.regionDataSelArr = [res.Data.ProvinceCode, res.Data.CityCode, res.Data.AreaCode];
        if (this.$refs.ruleFormAdd) {
          this.$refs.ruleFormAdd.clearValidate();
        }
        that.getAllchannel(res.Data.ParentName);
        // that.gaodeAMapLoaderConf();
        that.markOption = {
          position: [that.ruleFormAdd.Longitude, that.ruleFormAdd.Latitude], //标记中心点
          icon: false, //false 使用默认的，需要自定义的传入图标路径
          title: that.ruleFormAdd.AddressDetail, //给标记添加一个文章窗口描述
          clearMark: true, //是否清除之前的点,如果想添加多个点需要设置为false
        };
        that.dialogVisible = true;
      } else {
        that.$message.error(res.Message);
      }
    },
    // 导出
    downloadCusAccountExcel() {
      var that = this;
      let params = {
        Name: that.Name,
        ChannelTypeID: that.ChannelTypeID,
        ParentID: that.ParentID,
        EmployeeID: that.EmployeeID,
        Active: that.Active,
        PageNum: that.paginations.page,
        ChannelLevelID: that.ChannelLevelID,
        DeveloperID: that.DeveloperID, //开发人员ID
        ConsultantID: that.ConsultantID, //咨询人员ID
      };
      that.downloadLoading = true;
      API.channel_excel(params)
        .then((res) => {
          this.$message.success({
            message: "正在导出",
            duration: "4000",
          });
          const link = document.createElement("a");
          let blob = new Blob([res], { type: "application/octet-stream" });
          link.style.display = "none";
          link.href = URL.createObjectURL(blob);
          link.download = "渠道信息.xlsx"; //下载的文件名
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
        })
        .finally(function () {
          that.downloadLoading = false;
        });
    },
    /**   开发人员转移   */
    channel_transferDeveloper() {
      let that = this;
      let params = {
        OutEmployeeID: that.transferForm.OutEmployeeID, //转出员工编号
        InEmployeeID: that.transferForm.InEmployeeID,//转入员工编号
      };
      API.channel_transferDeveloper(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.$message.success("开发人员转移成功");
            that.transferDialogVisible = false;
            that.getChannelList();
          } else {
            that.$message.error(res.Message);
          }
        })
        .catch((fail) => {
          that.$message.error(fail);
        });
    },

    /**  市场咨询转移  */
    channel_transferConsultant() {
      let that = this;
      let params = {
        OutEmployeeID: that.transferForm.OutEmployeeID, //转出员工编号
        InEmployeeID: that.transferForm.InEmployeeID,//转入员工编号
      };
      API.channel_transferConsultant(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.$message.success("市场咨询转移成功");
            that.transferDialogVisible = false;
            that.getChannelList();
          } else {
            that.$message.error(res.Message);
          }
        })
        .catch((fail) => {
          that.$message.error(fail);
        });
    },
  },
  /** 监听数据变化   */
  watch: {
    IntroducerName() {
      var that = this;
      that.$nextTick(() => {
        that.IntroducerList.forEach((val) => {
          that.$refs.multipleTable.toggleRowSelection(val, false);
        });
      });
      if (that.selectIntroducerList.length > 0) {
        that.selectIntroducerList.forEach((item) => {
          that.IntroducerList.forEach((val) => {
            if (item.ID == val.IntroducerID) {
              that.$nextTick(() => {
                that.$refs.multipleTable.toggleRowSelection(val);
              });
            }
          });
        });
      }
    },
  },
  /** 创建实例之前执行函数   */
  beforeCreate() {},
  /**  实例创建完成之后  */
  created() {},
  /**  在挂载开始之前被调用  */
  beforeMount() {},
  /**  实例被挂载后调用  */
  mounted() {
    let that = this;
    that.isExport = that.$permission.permission(this.$route.meta.Permission, "CRM-Channel-ChannelInfo-Export");
    that.getTreeList();
    that.getAllchannel();
    that.geTemployeeAll();
    that.handleSearch();
    that.getIntroducerList();
    that.getChannelTypeList();
    that.channelLevel_list();
  },
  beforeRouteEnter(to, from, next) {
    next((vm) => {
      vm.isExport = vm.$permission.permission(to.meta.Permission, "CRM-Channel-ChannelInfo-Export");
    });
  },
  /**  数据更新 之前 调用  */
  beforeUpdate() {},
  /** 数据更新 完成 调用   */
  updated() {},
  /**  实例销毁之前调用  */
  beforeDestroy() {},
  /**  实例销毁后调用  */
  destroyed() {},
};
</script>

<style lang="scss">
.channelInfo {
  padding: 0px;
  height: 100%;

  .custom-scrollbarClass {
    height: 100%;
    border-right: 1px solid #ddd;

    .el-scrollbar__wrap {
      overflow-x: hidden;
    }
  }

  .custom-Dropdown {
    min-width: 500px;
  }

  #gdmapContainer {
    position: relative;
    width: 500px;
    height: 220px;
  }

  #gdmapPanel {
    position: absolute;
    background-color: white;
    max-height: 100%;
    overflow-y: auto;
    top: 0px;
    left: 500px;
    width: 200px;
    z-index: 101;
  }
}

.empPopper_custom {
  .el-select-dropdown__item {
    line-height: normal;
    height: auto;
  }
}

.custom_channelPopperClass {
  .el-select-dropdown__item {
    line-height: normal;
    height: auto;
    padding-top: 3px;
    padding-bottom: 3px;
  }
}

.document_i {
  position: absolute;
  width: 100%;
  height: 100%;
  text-align: center;
}
.custom_channel_popover {
  padding: unset;
}
</style>
