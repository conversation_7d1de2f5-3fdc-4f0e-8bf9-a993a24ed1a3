<template>
  <div class="callbackConfig content_body_nopadding" v-loading="loading">
    <el-tabs v-model="activeIndex" type="border-card" @tab-click="handleClick">
      <el-tab-pane label="回访方式" name="0">
        <!-- 搜索 新建 -->
        <div class="nav_header">
          <el-row>
            <el-col :span="20">
              <el-form :inline="true" size="small">
                <el-form-item label="回访方式">
                  <el-input v-model="Name" placeholder="输入回访类型名称搜索" clearable @clear="handleSearch" @keyup.enter.native="handleSearch"></el-input>
                </el-form-item>
                <el-form-item label="有效性">
                  <el-select v-model="Active" placeholder="请选择有效性" @change="handleSearch" clearable>
                    <el-option label="有效" :value="true"></el-option>
                    <el-option label="无效" :value="false"></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" @click="handleSearch" size="small" v-prevent-click>搜索</el-button>
                </el-form-item>
              </el-form>
            </el-col>
            <el-col :span="4" class="text_right">
              <el-button type="primary" size="small" v-prevent-click @click="showDialog">新增</el-button>
            </el-col>
          </el-row>
        </div>
        <!-- 列表数据 -->
        <el-table size="small" :data="tableData">
          <el-table-column prop="Name" label="回访方式"> </el-table-column>
          <el-table-column label="移动" min-width="180px">
            <template slot-scope="scope">
              <el-button
                size="small"
                type="primary"
                circle
                icon="el-icon-upload2"
                @click="upOneClick(scope.row, scope.$index)"
                v-prevent-click
                :disabled="scope.$index == 0"
              ></el-button>
              <el-button
                size="small"
                type="primary"
                circle
                icon="el-icon-top"
                @click="upClick(scope.row, scope.$index)"
                v-prevent-click
                :disabled="scope.$index == 0"
              ></el-button>
              <el-button
                size="small"
                type="primary"
                circle
                icon="el-icon-bottom"
                @click="downClick(scope.row, scope.$index)"
                v-prevent-click
                :disabled="scope.$index == tableData.length - 1"
              ></el-button>
              <el-button
                size="small"
                type="primary"
                circle
                icon="el-icon-download"
                @click="downOneClick(scope.row, scope.$index)"
                v-prevent-click
                :disabled="scope.$index == tableData.length - 1"
              ></el-button>
            </template>
          </el-table-column>
          <el-table-column prop="Active" label="有效性">
            <template slot-scope="scope">
              {{ scope.row.Active ? "有效" : "无效" }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="80">
            <template slot-scope="scope">
              <el-button type="primary" size="small" @click="editDialog(scope.row)">编辑</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-tab-pane>
      <el-tab-pane label="回访状态" name="1">
        <!-- 搜索 新建 -->
        <div class="nav_header">
          <el-row>
            <el-col :span="20">
              <el-form :inline="true" size="small">
                <el-form-item label="回访状态">
                  <el-input v-model="Name" placeholder="输入回访状态名称搜索" clearable @clear="handleSearch" @keyup.enter.native="handleSearch"></el-input>
                </el-form-item>
                <el-form-item label="有效性">
                  <el-select v-model="Active" placeholder="请选择有效性" @change="handleSearch" clearable>
                    <el-option label="有效" :value="true"></el-option>
                    <el-option label="无效" :value="false"></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" @click="handleSearch" size="small" v-prevent-click>搜索</el-button>
                </el-form-item>
              </el-form>
            </el-col>
            <el-col :span="4" class="text_right">
              <el-button type="primary" size="small" v-prevent-click @click="showDialog">新增</el-button>
            </el-col>
          </el-row>
        </div>
        <el-table size="small" :data="statustableData" style="width: 100%">
          <el-table-column prop="Name" label="回访状态"> </el-table-column>
          <el-table-column label="移动" min-width="180px">
            <template slot-scope="scope">
              <el-button
                size="small"
                type="primary"
                circle
                icon="el-icon-upload2"
                @click="upOneClick(scope.row, scope.$index)"
                v-prevent-click
                :disabled="scope.$index == 0"
              ></el-button>
              <el-button
                size="small"
                type="primary"
                circle
                icon="el-icon-top"
                @click="upClick(scope.row, scope.$index)"
                v-prevent-click
                :disabled="scope.$index == 0"
              ></el-button>
              <el-button
                size="small"
                type="primary"
                circle
                icon="el-icon-bottom"
                @click="downClick(scope.row, scope.$index)"
                v-prevent-click
                :disabled="scope.$index == statustableData.length - 1"
              ></el-button>
              <el-button
                size="small"
                type="primary"
                circle
                icon="el-icon-download"
                @click="downOneClick(scope.row, scope.$index)"
                v-prevent-click
                :disabled="scope.$index == statustableData.length - 1"
              ></el-button>
            </template>
          </el-table-column>
          <el-table-column prop="Active" label="有效性">
            <template slot-scope="scope">
              {{ scope.row.Active ? "有效" : "无效" }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="80">
            <template slot-scope="scope">
              <el-button type="primary" size="small" @click="editDialog(scope.row)">编辑</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-tab-pane>
    </el-tabs>
    <!-- 回访方式新增、编辑弹出层 -->
    <el-dialog :title="isEdit ? `编辑${DialogTitle}` : `新增${DialogTitle}`" :visible.sync="showdialog" width="450px">
      <div>
        <el-form ref="ruleForm" :rules="rules" :model="ruleForm" label-width="auto" size="small" @submit.native.prevent>
          <el-form-item :label="`${DialogTitle}名称`" prop="Name">
            <el-input v-model="ruleForm.Name" :placeholder="`请输入${DialogTitle}名称`"></el-input>
          </el-form-item>
          <el-form-item label="有效性" v-if="isEdit">
            <el-radio-group v-model="ruleForm.Active">
              <el-radio :label="true">有效</el-radio>
              <el-radio :label="false">无效</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="showdialog = false" size="small">取 消</el-button>
        <el-button type="primary" :loading="modalLoading" @click="savecallbackMethod('ruleForm')" v-prevent-click size="small">保 存</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import API from "@/api/KHS/Setting/callbackConfig";

export default {
  name: "CallbackConfig",

  props: {},
  /** 监听数据变化   */
  watch: {},
  /**  引入的组件  */
  components: {},
  /**  Vue 实例的数据对象**/
  data() {
    return {
      isEdit: false,
      loading: false,
      modalLoading: false,
      showdialog: false,
      activeIndex: "0",
      DialogTitle: "", // 弹出层标题
      ID: "", // 方案ID
      Name: "", // 方案名称
      Active: true, // 有效性
      tableData: [], // 回访方式表格数据
      statustableData: [], // 回访状态列表数据
      ruleForm: {
        Name: "",
        Active: "",
      },
      rules: {
        Name: [{ required: true, message: "请输入名称", trigger: "blur" }],
      },
    };
  },
  /**计算属性  */
  computed: {},
  /**  方法集合  */
  methods: {
    /* 搜索 */
    handleSearch() {
      let that = this;
      if (that.activeIndex == 0) {
        that.getAllCallbackMethod();
      } else {
        that.getAllCallBackStatus();
      }
    },
    /* tabs切换 */
    handleClick() {
      let that = this;
      that.Name = ""; // 方案名称
      that.Active = true; // 有效性
      that.handleSearch();
    },
    /* 新增 */
    showDialog() {
      let that = this;
      if (that.activeIndex == 0) {
        that.DialogTitle = "回访方式";
      } else {
        that.DialogTitle = "回访状态";
      }
      that.ruleForm = {
        Name: "",
        Active: "",
      };
      if (this.$refs.ruleForm) {
        that.$refs["ruleForm"].resetFields();
      }
      that.isEdit = false;
      that.showdialog = true;
    },
    /* 编辑 */
    editDialog(row) {
      let that = this;
      if (that.activeIndex == 0) {
        that.DialogTitle = "回访方式";
      } else {
        that.DialogTitle = "回访状态";
      }
      that.ruleForm = {
        Name: "",
        Active: "",
      };
      if (this.$refs.ruleForm) {
        that.$refs["ruleForm"].resetFields();
      }
      that.isEdit = true;
      that.showdialog = true;
      that.ID = row.ID;
      that.ruleForm.Name = row.Name;
      that.ruleForm.Active = row.Active;
    },
    /* 移动首部 */
    upOneClick: function (row) {
      let that = this;
      if (that.activeIndex == 0) {
        that.moveCallbackMethod(row.ID, "");
      } else {
        that.moveCallBackStatus(row.ID, "");
      }
    },
    /* 移动尾部 */
    downOneClick: function (row, index) {
      let that = this;
      let beforeId = "";
      if (that.activeIndex == 0) {
        let tableLength = that.tableData.length;
        if (index < tableLength - 1) {
          beforeId = that.tableData[tableLength - 1].ID;
        }
        that.moveCallbackMethod(row.ID, beforeId);
      } else {
        let tableLength = that.statustableData.length;
        if (index < tableLength - 1) {
          beforeId = that.statustableData[tableLength - 1].ID;
        }
        that.moveCallBackStatus(row.ID, beforeId);
      }
    },
    /* 向上移动 */
    upClick: function (row, index) {
      let that = this;
      let beforeId = "";
      if (that.activeIndex == 0) {
        if (index > 1) {
          beforeId = that.tableData[index - 2].ID;
        }
        that.moveCallbackMethod(row.ID, beforeId);
      } else {
        if (index > 1) {
          beforeId = that.statustableData[index - 2].ID;
        }
        that.moveCallBackStatus(row.ID, beforeId);
      }
    },
    /* 向下移动 */
    downClick: function (row, index) {
      let that = this;
      let beforeId = "";
      if (that.activeIndex == 0) {
        if (index + 1 != that.tableData.length) {
          beforeId = that.tableData[index + 1].ID;
        }
        that.moveCallbackMethod(row.ID, beforeId);
      } else {
        if (index + 1 != that.statustableData.length) {
          beforeId = that.statustableData[index + 1].ID;
        }
        that.moveCallBackStatus(row.ID, beforeId);
      }
    },
    /* 回访方式移动 */
    moveCallbackMethod(moveId, beforeId) {
      let that = this;
      that.loading = true;
      let params = {
        MoveID: moveId,
        BeforeID: beforeId,
      };
      API.moveCallbackMethod(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.$message.success("移动成功");
            that.handleSearch();
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },
    /* 回访状态移动 */
    moveCallBackStatus(moveId, beforeId) {
      let that = this;
      that.loading = true;
      let params = {
        MoveID: moveId,
        BeforeID: beforeId,
      };
      API.moveCallBackStatus(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.$message.success("移动成功");
            that.handleSearch();
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },
    /* 保存 */
    savecallbackMethod(ruleForm) {
      let that = this;
      that.$refs[ruleForm].validate((valid) => {
        if (valid) {
          if (that.activeIndex == 0) {
            if (that.isEdit) {
              that.updateCallbackMethod();
            } else {
              that.createCallbackMethod();
            }
          } else {
            if (that.isEdit) {
              that.updateCallBackStatus();
            } else {
              that.createCallBackStatus();
            }
          }
        }
      });
    },
    /* 获取回访方式列表 */
    getAllCallbackMethod() {
      let that = this;
      that.loading = true;
      let params = {
        Name: that.Name,
        Active: that.Active,
      };
      API.getAllCallbackMethod(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.tableData = res.Data;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },
    /* 新增回访方式 */
    createCallbackMethod() {
      let that = this;
      that.modalLoading = true;
      let params = {
        Name: that.ruleForm.Name,
      };
      API.createCallbackMethod(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.$message.success({
              message: "回访方式新增成功",
              duration: 2000,
            });
            that.showdialog = false;
            that.getAllCallbackMethod();
            that.isEdit = false;
            that.$refs["ruleForm"].resetFields();
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.modalLoading = false;
        });
    },
    /* 编辑回访方式 */
    updateCallbackMethod() {
      let that = this;
      that.modalLoading = true;
      let params = {
        ID: that.ID,
        Name: that.ruleForm.Name,
        Active: that.ruleForm.Active,
      };
      API.updateCallbackMethod(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.$message.success({
              message: "回访方式编辑成功",
              duration: 2000,
            });
            that.showdialog = false;
            that.getAllCallbackMethod();
            that.isEdit = false;
            that.$refs["ruleForm"].resetFields();
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.modalLoading = false;
        });
    },
    /* 获取回访状态列表 */
    getAllCallBackStatus() {
      let that = this;
      that.loading = true;
      let params = {
        Name: that.Name,
        Active: that.Active,
      };
      API.getAllCallBackStatus(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.statustableData = res.Data;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },
    /* 新增回访状态 */
    createCallBackStatus() {
      let that = this;
      that.modalLoading = true;
      let params = {
        Name: that.ruleForm.Name,
      };
      API.createCallBackStatus(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.$message.success({
              message: "回访状态新增成功",
              duration: 2000,
            });
            that.showdialog = false;
            that.getAllCallBackStatus();
            that.isEdit = false;
            that.$refs["ruleForm"].resetFields();
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.modalLoading = false;
        });
    },
    /* 编辑回访状态 */
    updateCallBackStatus() {
      let that = this;
      that.modalLoading = true;
      let params = {
        ID: that.ID,
        Name: that.ruleForm.Name,
        Active: that.ruleForm.Active,
      };
      API.updateCallBackStatus(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.$message.success({
              message: "回访状态编辑成功",
              duration: 2000,
            });
            that.showdialog = false;
            that.getAllCallBackStatus();
            that.isEdit = false;
            that.$refs["ruleForm"].resetFields();
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.modalLoading = false;
        });
    },
  },
  /** 创建实例之前执行函数   */
  beforeCreate() {},
  /**  实例创建完成之后  */
  created() {},
  /**  在挂载开始之前被调用  */
  beforeMount() {},
  /**  实例被挂载后调用  */
  mounted() {
    let that = this;
    that.getAllCallbackMethod();
  },
  /**  数据更新 之前 调用  */
  beforeUpdate() {},
  /** 数据更新 完成 调用   */
  updated() {},
  /**  实例销毁之前调用  */
  beforeDestroy() {},
  /**  实例销毁后调用  */
  destroyed() {},
};
</script>

<style lang="scss">
.callbackConfig {
  .el-tabs--border-card {
    border: 0px !important;
    box-shadow: 0 0px 0px 0 rgba(0, 0, 0, 0), 0 0 0px 0 rgba(0, 0, 0, 0);
  }
  .el-form-item--mini.el-form-item,
  .el-form-item--small.el-form-item {
    margin-bottom: 15px;
  }
}
</style>
