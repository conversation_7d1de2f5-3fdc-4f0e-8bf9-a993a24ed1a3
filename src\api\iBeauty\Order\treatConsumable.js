/**
 * Created by preference on 2022/08/25
 *  zmx
 */

import * as API from "@/api/index";
export default {
  /** 消耗项目-适用的模板  */
  treatBill_templateByProject: (params) => {
    return API.POST("api/treatBill/templateByProject", params);
  },
  /** 所有耗材  */
  treatBill_product: (params) => {
    return API.POST("api/treatBill/product", params);
  },
  /** 门店列表  */
  treatBill_productEntity: (params) => {
    return API.POST("api/treatBill/productEntity", params);
  },
  /** 产品库存  */
  treatBill_productQuantity: (params) => {
    return API.POST("api/stock/productQuantity", params);
  },
};
