/**
 * Created by wsf on 2022/05/25
 *  门店销售业绩明细报表 api
 */

import * as API from "@/api/index";

export default {
  /* 获取门店销售业绩明细报表 */
  getEntitySalePerformanceList: (params) => {
    return API.POST("api/entitySalePerformance/list", params);
  },
  // 导出
  entitySalePerformanceExcel: (params) => {
    return API.exportExcel("api/entitySalePerformance/excel", params);
  },
  /* 查询客户等级 */
  customerLevel_all: (params) => {
    return API.POST("api/customerLevel/all", params);
  },
};
