<template>
  <div class="addCustomer">
    <!--新增弹窗 :modal="false" -->
    <el-dialog :title="title" :visible.sync="addVisible" width="1000px" custom-class="custom-add-customer-class" @close="addCustomerClose">
      <el-scrollbar ref="addCustomerScroll" style="height: 60vh" class="customer-add-scrollbar">
        <div class="tip dis_flex margin-bottom">
          基础信息
          <span class="marlt_10">
            新增为会员：
            <el-switch v-model="ruleForm.IsMember" @change="isMemberChange" active-color="#ff8646" inactive-color="#dcdfe6"> </el-switch>
          </span>
        </div>
        <el-form :model="ruleForm" ref="ruleForm" label-width="90px" size="small">
          <el-row>
            <!---->
            <el-col v-if="ruleForm.IsMember" :span="24">
              <el-form-item label="会员等级" :rules="rules.CustomerLevelID" prop="CustomerLevelID">
                <el-select v-model="ruleForm.CustomerLevelID" placeholder="请选择会员等级" filterable size="small">
                  <el-option v-for="item in customerLevel" :key="item.ID" :label="item.Name" :value="item.ID"> </el-option>
                </el-select>
                <el-checkbox class="marlt_10" v-model="ruleForm.IsLockMemberLevel">锁定会员等级，等级不随成长值而变化</el-checkbox>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="客户姓名" prop="Name" :rules="getIsRequired('Name')">
                <el-input v-model="ruleForm.Name"></el-input>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="手机号码" prop="PhoneNumber" :rules="getIsRequired('PhoneNumber')">
                <el-input v-model="ruleForm.PhoneNumber" maxlength="11"></el-input>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item prop="Gender" label="客户性别" :rules="getIsRequired('Gender')">
                <div style="height: 33px">
                  <el-radio-group v-model="ruleForm.Gender">
                    <el-radio label="2">女</el-radio>
                    <el-radio label="1">男</el-radio>
                    <el-radio label="0">未知</el-radio>
                  </el-radio-group>
                </div>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item prop="Birthday" label=" " label-width="1" :rules="getIsRequired('Birthday')">
                <el-row :gutter="10">
                  <el-col :span="7">
                    <el-select v-model="ruleForm.BirthdayType" placeholder="请选择" size="small">
                      <el-option label="公历生日" :value="10"></el-option>
                      <el-option label="农历生日" :value="20"></el-option>
                    </el-select>
                  </el-col>
                  <el-col :span="13">
                    <el-date-picker size="small" v-model="ruleForm.Birthday" type="date" value-format="yyyy-MM-dd" placeholder="选择日期"> </el-date-picker>
                  </el-col>
                </el-row>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="信息来源" prop="CustomerSourceID" :rules="rules.CustomerSourceID">
                <el-cascader
                  v-model="ruleForm.CustomerSourceID"
                  :options="customerSource"
                  :props="{
                    checkStrictly: true,
                    children: 'Child',
                    value: 'ID',
                    label: 'Name',
                    emitPath: false,
                  }"
                  :show-all-levels="false"
                  filterable
                  placeholder="请选择信息来源"
                ></el-cascader>
              </el-form-item>
            </el-col>
            <el-col :span="12" v-if="isShowChannel">
              <el-form-item label="渠道来源" size="small" prop="ChannelID" :rules="getIsRequired('ChannelID')">
                <el-select
                  v-model="ruleForm.ChannelID"
                  placeholder="请输入渠道信息搜索客户渠道来源"
                  popper-class="custom_channelPopperClass"
                  filterable
                  remote
                  reserve-keyword
                  size="small"
                  clearable
                  :remote-method="searchChannelInfo"
                  @focus="focusChannel"
                  @clear="focusChannel"
                >
                  <el-option v-for="item in channelList" :key="item.ID" :label="item.Name" :value="item.ID">
                    <div style="padding-bottom: 8px">
                      <div>{{ item.Name }}</div>
                      <div class="font_12 color_999">渠道类型：{{ item.ChannelType }}</div>
                      <div v-if="item.ParentName" class="font_12 color_999">上级渠道：{{ item.ParentName }}</div>
                      <div v-if="item.ContactPersonName" class="font_12 color_999">联系人名称：{{ item.ContactPersonName }}</div>
                      <div v-if="item.ContactPersonMobile" class="font_12 color_999">联系人手机号：{{ item.ContactPersonMobile }}</div>
                    </div>
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="客户编号" prop="Code" :rules="getIsRequired('Code')">
                <el-input v-model="ruleForm.Code"></el-input>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="身份证号" prop="IdentityCard" :rules="getIsRequired('IdentityCard')">
                <el-input v-model="ruleForm.IdentityCard"></el-input>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="客户介绍人">
                <el-select
                  v-model="ruleForm.Introducer"
                  placeholder="请选择客户介绍人"
                  filterable
                  remote
                  reserve-keyword
                  size="small"
                  default-first-option
                  v-loadmore="customerIntroducerLoadMore"
                  :remote-method="remoteCusMethod"
                  clearable
                >
                  <el-option v-for="item in customerIntroducer" :key="item.ID" :label="item.Name" :value="item.ID - 0">
                    <span style="float: left">{{ item.Name }}</span>
                    <span style="float: right; color: #8492a6; font-size: 13px" v-if="item.PhoneNumber.length == 11">{{ item.PhoneNumber | hidephone }}</span>
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="职业" prop="Job" :rules="getIsRequired('Job')">
                <el-input v-model="ruleForm.Job"></el-input>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="省市区" prop="ProvinceCityArea" :rules="getIsRequired('ProvinceCityArea')">
                <el-cascader
                  clearable
                  placeholder="请选择省 / 市 / 区"
                  size="small"
                  :options="regionData"
                  v-model="regionDataSelArr"
                  @change="changeProvinceCityArea"
                >
                </el-cascader>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="详细地址" prop="Address" :rules="getIsRequired('Address')">
                <el-input style="width: 100%; max-width: unset" v-model="ruleForm.Address"></el-input>
              </el-form-item>
            </el-col>

            <el-col :span="24">
              <el-form-item label="备注" prop="Remark" :rules="getIsRequired('Remark')">
                <el-input type="textarea" rows="3" v-model="ruleForm.Remark"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <div v-show="customerServicer.length != 0" class="tip margin-bottom">服务人员</div>
        <el-form size="small">
          <el-col :span="12" v-for="item in customerServicer" :key="item.ID">
            <el-form-item :label="item.Name" label-width="90px">
              <el-select
                v-model="item.ServicerListArr"
                filterable
                placeholder="请选择服务人员"
                multiple
                collapse-tags
                reserve-keyword
                size="small"
                default-first-option
                @change="change"
              >
                <el-option v-for="serervicer in item.ServicerEmpList" :key="serervicer.ID" :label="serervicer.Name" :value="serervicer.ID">
                  <span>{{ serervicer.Name }}</span>
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-form>
      </el-scrollbar>
      <div slot="footer">
        <el-button size="small" @click="addVisible = false" v-prevent-click>取 消</el-button>
        <el-button type="primary" size="small" @click="submitAddCustomer" :loading="modalLoading" v-prevent-click>保 存</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import API from "@/api/CRM/Customer/customer";
import APICustomerLevel from "@/api/CRM/Customer/customerLevel";
import APICustomerSource from "@/api/CRM/Customer/customerSource";
import APIChannel from "@/api/CRM/Channel/channelInfo";
import APIMember from "@/api/CRM/CustomerLevel/customercondition";
import APIScene from "@/api/CRM/Customer/customerFileApplicationScene.js";
import validate from "@/components/js/validate.js";
import { regionData } from "element-china-area-data";

export default {
  name: "addCustomer",
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    title: {
      type: String,
      default: "",
    },
    customerPhoneNumber: {
      type: String,
      default: "",
    },
    isCreateMember:{
      type:Boolean,
      default:false,
    }
  },
  /** 监听数据变化   */
  watch: {
    visible: {
      immediate: true,
      handler(val) {
        this.addVisible = val;
        if (val) {
          this.ruleForm = {
            Name: "",
            PhoneNumber: "",
            Gender: "2",
            CustomerSourceID: null,
            list: [],
            // EmployeeID: [],
            Introducer: "",
            CustomerLevelID: "",
            Code: "",
            BirthdayType: 10,
            Birthday: "",
            ProvinceCode: "",
            CityCode: "",
            AreaCode: "",
            Job: "",
            Address: "",
            IdentityCard: "",
            Remark: "",
            Channel: null,
            IsMember: false, // 是否会员
            IsLockMemberLevel: false,
            ProvinceCityArea: [],
          };
          this.getCustomerConditionAll();
          this.$nextTick(() => {
            this.$refs.addCustomerScroll.wrap.scrollTop = 0;
          });
        }
      },
    },
    customerPhoneNumber:{
      immediate:true,
      handler(val){
        this.ruleForm.PhoneNumber = val;
      }
    }
  },
  /**  引入的组件  */
  components: {},
  /**  Vue 实例的数据对象**/
  data() {
    return {
      isShowChannel: false,
      customerIntroducerLoadMore: false,
      addVisible: false,
      modalLoading: false,
      regionData: regionData, //省市区
      regionDataSelArr: [], //已选择省市区
      customerSource: [],
      customerLevel: [],
      channelList: [],
      customerIntroducer: [],
      customerServicer: [],
      ruleForm: {
        Name: "",
        PhoneNumber: "",
        Gender: "2",
        CustomerSourceID: null,
        list: [],
        // EmployeeID: [],
        Introducer: "",
        CustomerLevelID: "",
        Code: "",
        BirthdayType: 10,
        Birthday: "",
        ProvinceCode: "",
        CityCode: "",
        AreaCode: "",
        Job: "",
        Address: "",
        IdentityCard: "",
        Remark: "",
        ChannelID: null,
        IsMember: false, // 是否会员
        IsLockMemberLevel: false,
        ProvinceCityArea: [],
      },
      rules: {
        Name: [{ required: true, message: "请输入会员名称", trigger: ["blur", "change"] }],
        PhoneNumber: [
          {
            validator: validate.validPhoneNumber,
            trigger: ["blur", "change"],
          },
        ],
        Gender: [{ message: "请选择会员性别", trigger: ["blur", "change"] }],
        Birthday: [{ message: "请选择会员生日", trigger: ["blur", "change"] }],
        CustomerSourceID: [{ required: true, message: "请选择会员信息来源", trigger: ["blur", "change"] }],
        ChannelID: [{ message: "请选择会员渠道来源", trigger: ["blur", "change"] }],
        Code: [{ message: "请输入会员编号", trigger: ["blur", "change"] }],
        IdentityCard: [
          {
            required: false,
            message: "请输入身份证号",
            trigger: ["blur", "change"],
          },
          {
            pattern:
              /(^[1-9]\d{5}(18|19|([23]\d))\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$)|(^[1-9]\d{5}\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{2}$)/,
            message: "请输入合法身份证号",
            trigger: "blur",
          },
        ],
        Job: [{ message: "请输入会员职业", trigger: ["blur", "change"] }],
        ProvinceCityArea: [{ message: "请选择会员地址", trigger: ["blur", "change"] }],
        Address: [{ message: "请输入会员详细地址", trigger: ["blur", "change"] }],
        Remark: [{ message: "请输入备注信息", trigger: ["blur", "change"] }],

        CustomerLevelID: [{ required: true, message: "请选择会员等级", trigger: ["change", "blur"] }],
      },
      sceneData: [],
    };
  },
  /**计算属性  */
  computed: {},
  /**  方法集合  */
  methods: {
    /**    */
    getIsRequired(code) {
      let that = this;
      let sceneCode = code;
      if (sceneCode == "CustomerSourceID") {
        sceneCode = "CustomerSource";
      }
      if (sceneCode == "ChannelID") {
        sceneCode = "Channel";
      }

      let item = that.sceneData.find((i) => i.Code == sceneCode);
      if (!item) return null;
      let rules = that.rules[code];
      if (!rules) return;
      rules[0].required = item.IsRequired;
      return rules;
    },
    /**    */
    changeProvinceCityArea(val) {
      let that = this;
      that.ruleForm.ProvinceCityArea = val;
    },
    change() {
      this.$forceUpdate();
    },
    /* 树形结构数据转换 */
    normalizer(node) {
      return {
        id: node.ID,
        label: node.Name,
        children: node.Child,
      };
    },
    /**  关闭添加弹窗  */
    addCustomerClose() {
      this.$refs["ruleForm"].resetFields();
      this.$emit("update:visible", false);
      this.$emit("close");
    },
    /**  修改是否新增为会员  */
    isMemberChange(val) {
      let that = this;
      if (val) {
        that.$nextTick(() => {
          that.ruleForm.CustomerLevelID = that.customerLevel[0] ? that.customerLevel[0].ID : null;
        });
      } else {
        that.ruleForm.CustomerLevelID = null;
      }
    },
    // 顾客介绍人远程搜索
    remoteCusMethod(value) {
      const that = this;
      that.customerIntroducer = [];
      that.CusPageNum = 1;
      that.CustomerIntroducer(value);
    },
    /**  请求  保存 新增、编辑  */
    submitAddCustomer: function () {
      var that = this;
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          that.modalLoading = true;
          let para = Object.assign({}, that.ruleForm);
          let tempArr = [];
          that.customerServicer.forEach((val) => {
            val.ServicerListArr.forEach((em) => {
              tempArr.push({
                ServicerID: val.ID,
                EmployeeID: em,
              });
            });
          });
          para.ServicerList = tempArr;
          if (that.regionDataSelArr.length) {
            para.ProvinceCode = that.regionDataSelArr[0];
            para.CityCode = that.regionDataSelArr[1];
            para.AreaCode = that.regionDataSelArr[2];
          }
          // if (that.isAdd) {
          API.createCustomer(para)
            .then(function (res) {
              if (res.StateCode === 200) {
                that.$message.success({
                  message: "新增成功",
                  duration: 2000,
                });
                that.$emit("update:visible", false);
                that.$emit("addCustomerSuccess", res.Data);
              } else {
                that.$message.error({
                  message: res.Message,
                  duration: 2000,
                });
              }
            })
            .finally(function () {
              that.modalLoading = false;
            });
          // } else {
          //   API.updateCustomer(para)
          //     .then(function (res) {
          //       if (res.StateCode === 200) {
          //         that.$message.success({
          //           message: "编辑成功",
          //           duration: 2000,
          //         });
          //         // that.customerDetail = Object.assign({}, res.Data);
          //         that.search();
          //         that.$refs["ruleForm"].resetFields();
          //         this.$emit("update:visible", false);
          //         // that.$refs.customerDetail.getCustomerDetail();
          //       } else {
          //         that.$message.error({
          //           message: res.Message,
          //           duration: 2000,
          //         });
          //       }
          //     })
          //     .finally(function () {
          //       that.modalLoading = false;
          //     });
          // }
        }
      });
    },
    // 顾客等级
    CustomerLevelData: function () {
      var that = this;
      var params = {
        Name: "",
        Active: true,
      };
      APICustomerLevel.getCustomerLevel(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.customerLevel = res.Data;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },
    // 顾客来源
    CustomerSourceData: function () {
      var that = this;
      var params = {
        Name: "",
        Active: true,
      };
      APICustomerSource.getCustomerSource(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.customerSource = res.Data;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },
    /**  渠道来源获取焦点 清除数据  */
    focusChannel() {
      let that = this;
      that.channelList = [];
    },
    /**    */
    searchChannelInfo(value) {
      this.getChannelList(value);
    },
    /* 获取渠道来源 */
    getChannelList(value) {
      let that = this;
      let params = { Name: value,Active:true, };
      APIChannel.channel_customerInfo(params).then((res) => {
        if (res.StateCode == 200) {
          that.channelList = res.Data;
        }
      });
    },
    // 顾客介绍人
    CustomerIntroducer(value) {
      var that = this;
      var params = {
        Name: value,
        PageNum: that.CusPageNum,
        Active: true,
      };
      API.customerAll(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.customerIntroducer = [...that.customerIntroducer, ...res.List];
            that.CusTotal = res.Total;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },
    // 新增获取服务人员
    getCustomerServicer() {
      let that = this;
      let params = {};
      let empInfo = JSON.parse(localStorage.getItem("access-user"));
      API.getCustomerServicer(params).then((res) => {
        if (res.StateCode == 200) {
          that.customerServicer = res.Data.map((i) => {
            let curItem = i.ServicerEmpList.find((j) => j.ID == empInfo.EmployeeID);
            if (curItem) {
              i.ServicerListArr = [curItem.ID];
            }
            return i;
          });
        } else {
          that.$message.error({
            message: res.Message,
            duration: 2000,
          });
        }
      });
    },
    /**  获取入会门槛信息  */
    async getCustomerConditionAll() {
      let that = this;
      let params = { Name: "", Active: true };
      let res = await APIMember.customerConditionAll(params);
      if (res) {
        that.ruleForm.IsMember = res.Data.Type == "10";
        if (that.ruleForm.IsMember) {
          that.$nextTick(() => {
            that.ruleForm.CustomerLevelID = that.customerLevel[0] ? that.customerLevel[0].ID : null;
          });
        } else {
          that.ruleForm.CustomerLevelID = null;
        }
      }
    },
    /* 获取顾客档案配置 */
    async customerFileApplicationScene_all() {
      let that = this;
      that.loading = true;
      try {
        let params = {};
        let res = await APIScene.customerFileApplicationScene_all(params);
        if (res.StateCode == 200) {
          that.sceneData = res.Data;
        } else {
          that.$message.error(res.Message);
        }
        that.loading = false;
      } catch (error) {
        that.loading = false;
        that.$message.error(error);
      }
    },
  },
  /** 创建实例之前执行函数   */
  beforeCreate() {},
  /**  实例创建完成之后  */
  created() {},
  /**  在挂载开始之前被调用  */
  beforeMount() {},
  /**  实例被挂载后调用  */
  mounted() {
    this.CustomerSourceData();
    this.CustomerLevelData();
    this.CustomerIntroducer();
    this.getCustomerServicer();
    this.customerFileApplicationScene_all();

    this.isShowChannel = this.$permission.getCustomerDetailPermission(this.$router, "iBeauty-Customer-Customer-Channel");
 
  },
  /**  数据更新 之前 调用  */
  beforeUpdate() {},
  /** 数据更新 完成 调用   */
  updated() {},
  /**  实例销毁之前调用  */
  beforeDestroy() {},
  /**  实例销毁后调用  */
  destroyed() {},
};
</script>

<style lang="scss">
.addCustomer {
  .custom-add-customer-class {
    .customer-add-scrollbar {
      .el-scrollbar__wrap {
        overflow-x: hidden;
      }
      .el-scrollbar__bar {
        opacity: 0;
      }
    }
  }
}
.custom_channelPopperClass {
  .el-select-dropdown__item {
    line-height: normal;
    height: auto;
  }
}
</style>
