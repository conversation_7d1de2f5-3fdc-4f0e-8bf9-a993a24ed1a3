/**
 * Created by waf on 2022/03/21
 */

import * as API from '@/api/index'
export default {
    /**  项目列表 */
    project_list: params => {
        return API.POST('api/customer/project', params)
    },
    /* 新增对比图片 */
    createPhotoCompare: params => {
        return API.POST('api/customer/createPhotoCompare', params)
    },
    /* 对比照片列表  */
    photoCompare: params => {
        return API.POST('api/customer/photoCompare', params)
    },
    /* 追加照片 */
    addToPhoto: params => {
        return API.POST('api/customer/addToPhoto', params)
    },
    /* 删除照片 */
    deletePhoto: params => {
        return API.POST('api/customer/deletePhoto', params)
    },
    /* 上传图片 */
    addAttachment: params => {
        return API.POST('api/upload/addAttachment', params) 
    }
}