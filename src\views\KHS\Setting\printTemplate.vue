<template>
  <div class="printTemplate content_body">
    <el-container style="height: 100%">
      <el-aside width="280px" style="border-right: 1px solid #ddd;padding:10px">
        <el-tree class="padtp_10 " :expand-on-click-node="false" :label="customLabelProperty" node-key="code"
          :data="templateTypeData" :default-expand-all="true" :auto-expand-parent="true" :props="defaultProps"
          @node-click="handleNodeClick" :default-expanded-keys="entityDefaultExpandedKeys">
          <span slot-scope="{ data }">
            <span class="font_14" :class="data.code == TemplateType ? 'customTreeColor' : ''">{{ data.label }} </span>
          </span>
        </el-tree>
      </el-aside>

      <el-container>
        <el-header style="height: auto; padding: 0px">
          <div class="nav_header marbm_10 martp_10">
            <el-row>
              <el-col :offset="22" :span="2" class="text_right">
                <el-button @click="addPrintTemplateClick" type="primary" size="small" v-prevent-click>新增</el-button>
              </el-col>
            </el-row>
          </div>
        </el-header>
        <el-main style="padding: 0px">
          <div>
            <el-table size="small" ref="multipleTable" :data="tableData" tooltip-effect="light">
              <el-table-column prop="Name" label="模板名称"></el-table-column>

              <el-table-column label="移动" min-width="180px">
                <template slot-scope="scope">
                  <el-button size="small" type="primary" circle icon="el-icon-upload2"
                    @click="upOneClick(scope.row, scope.$index)" v-prevent-click :disabled="scope.$index == 0">
                  </el-button>
                  <el-button size="small" type="primary" circle icon="el-icon-top"
                    @click="upClick(scope.row, scope.$index)" v-prevent-click :disabled="scope.$index == 0"></el-button>
                  <el-button size="small" type="primary" circle icon="el-icon-bottom"
                    @click="downClick(scope.row, scope.$index)" v-prevent-click
                    :disabled="scope.$index == tableData.length - 1">
                  </el-button>
                  <el-button size="small" type="primary" circle icon="el-icon-download"
                    @click="downOneClick(scope.row, scope.$index)" v-prevent-click
                    :disabled="scope.$index == tableData.length - 1"></el-button>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="145px">
                <template slot-scope="scope">
                  <el-button type="primary" size="small" @click="showEdit(scope.row)" v-prevent-click>编辑</el-button>
                  <el-button type="danger" size="small" @click="removePrinTmplateClick(scope.row)" v-prevent-click>删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-main>
      </el-container>
    </el-container>
    <!-- 新增 -->
    <el-dialog v-if="dialogVisible" :visible.sync="dialogVisible" :title="isAdd ? '新增打印模板' : '编辑打印模板'"
      :close-on-click-modal="false">
      <el-form ref="addRuleForm" size="small" :model="addRuleForm" :rules="rules">
        <el-row>
          <!-- <el-col :span="8">
						<el-form-item size="small">
							<el-button size="small" icon="el-icon-upload">导入HTML模板</el-button>
						</el-form-item>
					</el-col> -->

          <el-col :span="8">
            <el-form-item size="small" label="模板名称" label-width="100px" prop="Name">
              <el-input v-model="addRuleForm.Name" size="small" placeholder="请输入模板名称"> </el-input>
            </el-form-item>
          </el-col>

          <el-col :offset="8" :span="8" class="text_right">
            <el-form-item size="small">
              <el-button @click="getDefaultTemplateClick" size="small">还原系统默认模板</el-button>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item size="small" prop="Template">
          <editor :tools="editorTools" ref="editorRef" v-model="addRuleForm.Template" :resizable-content="true" :resizable-toolbar="true"
            style="height:500px">
          </editor>
        </el-form-item>
      </el-form>

      <div slot="footer">
        <el-button @click="dialogVisible = false" size="small" v-prevent-click>取 消</el-button>
        <el-button type="primary" @click="savePrintTemplateClick" :loading="addLoading" size="small" v-prevent-click>保存
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import "@progress/kendo-ui";
import "@progress/kendo-theme-default/dist/all.css";
import "@progress/kendo-ui/js/messages/kendo.messages.zh-CN.js";
import { Editor } from "@progress/kendo-editor-vue-wrapper";
import API from "@/api/KHS/Setting/printTemplate.js";

export default {
  name: "PrintTemplate",
  props: {},
  /**  引入的组件  */
  components: {
    editor: Editor
  },
  /**  Vue 实例的数据对象**/
  data() {
    return {
      dialogVisible: false,
      addLoading: false,
      isAdd: false,
      templateTypeData: [
        {
          label: "销售相关",
          code: "Sale",
          children: [
            {
              label: "销售订单",
              code: "SaleBill",
            },
            {
              label: "充值订单",
              code: "RechargeBill",
            },
            {
              label: "退款订单",
              code: "RefundSaleBill",
            },
            {
              label: "补欠款单",
              code: "ArrearsBill",
            }
          ]
        },
        {
          label: "消耗相关",
          code: "Treat",
          children: [
            {
              label: "消耗单",
              code: "TreatBill",
            },
            {
              label: "消耗退单",
              code: "RefundTreatBill",
            }
          ]
        },
        {
          label: "库存相关",
          code: "Stock",
          children: [
            {
              label: "入库单",
              code: "InStock",
            },
            {
              label: "出库单",
              code: "OutStock",
            },
            {
              label: "调拨单",
              code: "StockMove",
            },
            {
              label: "盘点单",
              code: "StockCheck",
            },
            {
              label: "采购入库单",
              code: "PurchaseStorage",
            },

            {
              label: "采购退货单",
              code: "PurchaseRefund",
            },
            {
              label: "门店要货单",
              code: "EntityApplyStock",
            },

            {
              label: "门店退货单",
              code: "EntityRefundStock",
            },
          ],
        },
      ],

      defaultProps: {
        children: "children",
        label: "label",
      },
      entityDefaultExpandedKeys: ["stock"],
      TemplateType: "SaleBill",

      editorTools: [
        "bold",
        "italic",
        "underline",
        "strikethrough",
        "justifyLeft",
        "justifyCenter",
        "justifyRight",
        "justifyFull",
        "insertUnorderedList",
        "insertOrderedList",
        "indent",
        "outdent",
        "createLink",
        "unlink",
        "insertImage",
        "insertFile",
        "fontSize",
        "tableWizard",
        "createTable",
        "addRowAbove",
        "addRowBelow",
        "addColumnLeft",
        "addColumnRight",
        "deleteRow",
        "deleteColumn",
        "formatting",
        "cleanFormatting",
        "viewHtml",
        "print",
        "pdf",
        "copyFormat",
        "applyFormat",
      ],
      tableData: [],

      addRuleForm: {
        Name: "",
        Template: "",
      },
      rules: {
        Name: [
          {
            required: true,
            message: "请输入模板名称",
            trigger: ["change", "blur"],
          },
        ],
        Template: [
          {
            required: true,
            message: "请编辑模板内容",
            trigger: ["change", "blur"],
          },
        ],
      },
    };
  },
  /**计算属性  */
  computed: {},
  /**  方法集合  */
  methods: {
    /**    */
    onBlur(e){
      console.log("🚀 ~ onBlur ~ e:", e)
      let that = this;
    
    },
    /**    */
    onPasteHtml(e){
      console.log("🚀 ~ onPasteHtml ~ e:", e)
      let that = this;
    
    },
    /**    */
    editorChange(event){
      console.log("🚀 ~ editorChange ~ event:", event)
      let that = this;
      // that.addRuleForm.Template = event.html;
    },
    /**    */
    customLabelProperty() { },
    /**  点击节点  */
    handleNodeClick(node) {
      let that = this;
      let typeCode = node.code;
      if (typeCode == "stock") {
        typeCode = that.TemplateType;
      }
      that.TemplateType = typeCode;

      that.template_list();
    },
    /**  添加打印模板  */
    addPrintTemplateClick() {
      let that = this;
      that.isAdd = true;
      that.addRuleForm = {
        Name: "",
        Template: "",
      };
      that.dialogVisible = true;
    },

    /**   保存打印模板 */
    savePrintTemplateClick() {
      let that = this;
       console.log("🚀 ~ savePrintTemplateClick ~ addRuleForm.Template:", that.addRuleForm.Template)
      console.log("🚀 ~ savePrintTemplateClick ~ that.$refs.editorRef:", that.$refs.editorRef)
      let html =  that.$refs.editorRef.value;
      
      console.log("🚀 ~ savePrintTemplateClick ~ html:", html)
      that.$refs.addRuleForm.validate((valid) => {
        if (valid) {
          if (this.isAdd) {
            that.template_create();
          } else {
            that.template_updatet();
          }
        }
      });
    },
    /**   删除 */
    removePrinTmplateClick(row) {
      let that = this;
      that
        .$confirm("此次操作将永久删除打印模板，是否继续？", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
        .then(() => {
          that.template_delete(row.ID);
        })
        .catch(() => {
          that.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    /**  编辑  */
    showEdit(row) {
      let that = this;
      that.isAdd = false;
      that.addRuleForm = {
        ID: row.ID,
        Name: row.Name,
        Template: row.Template,
      };
      that.dialogVisible = true;
    },
    /**  还原系统模板  */
    getDefaultTemplateClick() {
      let that = this;
      that.template_defaultTemplate();
    },

    // 移动首部
    upOneClick: function (row) {
      var that = this;
      that.template_move(row.ID, "");
    },
    // 移动尾部
    downOneClick: function (row, index) {
      var that = this;
      var tabIndex = that.tableData.length;
      var beforeId = "";
      if (index < tabIndex - 1) {
        beforeId = that.tableData[tabIndex - 1].ID;
      }
      that.template_move(row.ID, beforeId);
    },
    // 向上
    upClick: function (row, index) {
      var that = this;
      var beforeId = "";
      if (index > 1) {
        beforeId = that.tableData[index - 2].ID;
      }
      that.template_move(row.ID, beforeId);
    },
    // 向下
    downClick: function (row, index) {
      var that = this;
      var beforeId = "";
      if (index + 1 != that.tableData.length) {
        beforeId = that.tableData[index + 1].ID;
      }
      that.template_move(row.ID, beforeId);
    },

    /**  ***************************  */

    /**  列表  */
    async template_list() {
      let that = this;
      let params = { TemplateType: that.TemplateType };
      let res = await API.template_list(params);
      if (res.StateCode == 200) {
        that.tableData = res.Data;
      } else {
        that.$message.error(res.Message);
      }
    },
    /**  创建  */
    async template_create() {
      let that = this;
      that.addLoading = true;
      let params = {
        Name: that.addRuleForm.Name, //模板名称
        TemplateType: that.TemplateType, //类型（instock：入库单
        Template: that.addRuleForm.Template, //模板
      };
      let res = await API.template_create(params);
      if (res.StateCode == 200) {
        that.$message.success("创建成功");
        that.dialogVisible = false;
        that.template_list();
      } else {
        that.$message.error(res.Message);
      }
      that.addLoading = false;
    },
    /**  更新  */
    async template_updatet() {
      let that = this;
      let params = {
        ID: that.addRuleForm.ID,
        Name: that.addRuleForm.Name, //模板名称
        TemplateType: that.TemplateType, //类型（instock：入库单
        Template: that.addRuleForm.Template, //模板
      };
      let res = await API.template_updatet(params);
      if (res.StateCode == 200) {
        that.$message.success("更新成功");
        that.dialogVisible = false;
        that.template_list();
      } else {
        that.$message.error(res.Message);
      }
    },
    /**  删除  */
    async template_delete(ID) {
      let that = this;
      let params = { ID: ID };
      let res = await API.template_delete(params);
      if (res.StateCode == 200) {
        that.$message.success("操作成功");
        that.template_list();
      } else {
        that.$message.error(res.Message);
      }
    },
    /**  获取默认模板  */
    async template_defaultTemplate() {
      let that = this;
      let params = { TemplateType: that.TemplateType };
      let res = await API.template_defaultTemplate(params);
      if (res.StateCode == 200) {
        that.addRuleForm.Template = res.Message;
      } else {
        that.$message.error(res.Message);
      }
    },
    /**  移动  */
    async template_move(MoveID, BeforeID) {
      let that = this;
      let params = {
        MoveID: MoveID,
        BeforeID: BeforeID,
      };
      let res = await API.template_move(params);
      if (res.StateCode == 200) {
        that.$message.success("操作成功");
        that.template_list();
      } else {
        that.$message.error(res.Message);
      }
    },
  },
  /** 监听数据变化   */
  watch: {},
  /** 创建实例之前执行函数   */
  beforeCreate() { },
  /**  实例创建完成之后  */
  created() {
    let that = this;
    that.template_list();
  },
  /**  在挂载开始之前被调用  */
  beforeMount() { },
  /**  实例被挂载后调用  */
  mounted() { },
  /**  数据更新 之前 调用  */
  beforeUpdate() { },
  /** 数据更新 完成 调用   */
  updated() { },
  /**  实例销毁之前调用  */
  beforeDestroy() { },
  /**  实例销毁后调用  */
  destroyed() { },
};
</script>

<style lang="scss">
.printTemplate {
  padding: 0px;
  height: 100%;

  .el-header {
    padding: 0 0px;
    background-color: #fff;
  }

  .customTreeColor {
    color: var(--zl-color-orange-primary);
  }
}
</style>
