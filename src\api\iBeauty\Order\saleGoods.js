/**
 * Created by jerry on 2020/02/14.
 * 开单销售api
 */
import * as API from "@/api/index";

export default {
  // 搜索全部商品
  getSaleGoods: (params) => {
    return API.POST("api/saleGoods/all", params);
  },

  /**  **********全部商品*********  */
  /* 全部下的商品类型 */
  getSaleGoodsGoodsType: (params) => {
    return API.POST("api/saleGoods/goodsType", params);
  },
  /* 类型下的商品  */
  getSaleGoodsGoodsTypeGoods: (params) => {
    return API.POST("api/saleGoods/goods", params);
  },

  /**  **********全部商品 快速开单使用*********  */
  /* 全部下的商品类型 */
  getFastSaleGoodsGoodsType: (params) => {
    return API.POST("api/fastSaleGoods/goodsType", params);
  },
  /* 类型下的商品  */
  getFastSaleGoodsTypeGoods: (params) => {
    return API.POST("api/fastSaleGoods/goods", params);
  },
  /**  **********项目*********  */
  // 项目列表
  getSaleGoodsProject: (params) => {
    return API.POST("api/saleGoods/project", params);
  },
  // 项目分类 列表
  getSaleGoodsProjectCategory: (params) => {
    return API.POST("api/saleGoods/projectCategory", params);
  },
  // 分类下的项目
  getSaleGoodsProjectByCategoryy: (params) => {
    return API.POST("api/saleGoods/projectByCategory", params);
  },
  /**  **********产品*********  */
  //产品列表
  getSaleGoodsProduct: (params) => {
    return API.POST("api/saleGoods/product", params);
  },
  //产品分类列表
  getSaleGoodsProductCategory: (params) => {
    return API.POST("api/saleGoods/productCategory", params);
  },
  //分类下的产品
  getSaleGoodsProductByCategory: (params) => {
    return API.POST("api/saleGoods/productByCategory", params);
  },

  /**  **********通用次卡*******  */
  // 通用次卡列表
  getSaleGoodsGeneralCard: (params) => {
    return API.POST("api/saleGoods/generalCard", params);
  },
  // 通用次卡分类列表
  getSaleGoodsGeneralCardCategory: (params) => {
    return API.POST("api/saleGoods/generalCardCategory", params);
  },
  // 分类下的通用次卡
  getSaleGoodsGeneralCardByCategory: (params) => {
    return API.POST("api/saleGoods/generalCardByCategory", params);
  },
  /**  **********时效卡*******  */
  // 时效卡列表
  getSaleGoodsTimeCard: (params) => {
    return API.POST("api/saleGoods/timeCard", params);
  },
  // 时效卡分类列表
  getSaleGoodsTimeCardCategory: (params) => {
    return API.POST("api/saleGoods/timeCardCategory", params);
  },
  // 时效卡列表
  getSaleGoodsTimeCardByCategory: (params) => {
    return API.POST("api/saleGoods/timeCardByCategory", params);
  },

  /**  **********储值卡*******  */
  // 储值卡列表
  getSaleGoodsSavingCard: (params) => {
    return API.POST("api/saleGoods/savingCard", params);
  },
  // 储值分类列表
  getSaleGoodsSavingCardCategoryd: (params) => {
    return API.POST("api/saleGoods/savingCardCategory", params);
  },
  // 分类下的储值卡
  getSaleGoodsSavingCardByCategory: (params) => {
    return API.POST("api/saleGoods/savingCardByCategory", params);
  },
  /**  **********套餐卡*******  */
  //  套餐卡列表
  getSaleGoodsPackageCard: (params) => {
    return API.POST("api/saleGoods/packageCard", params);
  },
  //  套餐卡列表 分类
  getSaleGoodsPackageCardCategory: (params) => {
    return API.POST("api/saleGoods/packageCardCategory", params);
  },
  //  套餐卡列表 商品
  getSaleGoodsPackageCardByCategory: (params) => {
    return API.POST("api/saleGoods/packageCardByCategory", params);
  },

  /**  *****************  */

  //  查找会员
  getSaleCustomer: (params) => {
    return API.POST("api/saleCustomer/customer", params);
  },
  // 获取支付方式
  getSalePayMethod: (params) => {
    return API.POST("api/salePayMethod/all", params);
  },
  // 获取产品销售经手人
  getProductHandler: (params) => {
    return API.POST("api/saleHandler/productHandler", params);
  },
  // 获取项目销售经手人
  getProjectHandler: (params) => {
    return API.POST("api/saleHandler/projectHandler", params);
  },
  // 获取通用次卡销售经手人
  getGeneralCardHandler: (params) => {
    return API.POST("api/saleHandler/generalCardHandler", params);
  },
  // 获取时效卡销售经手人
  getTimeCardHandler: (params) => {
    return API.POST("api/saleHandler/timeCardHandler", params);
  },
  // 获取储值卡销售经手人
  getSavingCardHandler: (params) => {
    return API.POST("api/saleHandler/savingCardHandler", params);
  },
  // 获取套餐卡销售经手人
  getPackageCardHandler: (params) => {
    return API.POST("api/saleHandler/packageCardHandler", params);
  },
  // 获取会员通用储值卡
  getSavingCardAllGoods: (params) => {
    return API.POST("api/savingCardAccount/allGoods", params);
  },
  //  获取会员非通用储值卡（指定商品抵扣卡）
  getSavingCardSomeGoods: (params) => {
    return API.POST("api/savingCardAccount/someGoods", params);
  },
  // 获取可抵扣产品的储值卡列表
  getSavingCardProduct: (params) => {
    return API.POST("api/savingCardAccount/product", params);
  },
  // 获取可抵扣项目的储值卡列表
  getSavingCardProject: (params) => {
    return API.POST("api/savingCardAccount/project", params);
  },
  // 获取可抵扣通用次卡的储值卡列表
  getSavingCardGeneralCard: (params) => {
    return API.POST("api/savingCardAccount/generalCard", params);
  },
  //  获取可抵扣时效卡的储值卡列表
  getSavingCardTimeCard: (params) => {
    return API.POST("api/savingCardAccount/timeCard", params);
  },
  //  获取可抵扣套餐卡的储值卡列表
  getSavingCardPackageCard: (params) => {
    return API.POST("api/savingCardAccount/packageCard", params);
  },
  //  结帐
  createSaleBill: (params) => {
    return API.POST("api/saleBill/create", params);
  },
  //  结帐
  getDetailByCode: (params) => {
    return API.POST("api/customer/getDetailByCode", params);
  },
  //  销售经手人接口
  saleHandler_allHandler: (params) => {
    return API.POST("api/saleHandler/allHandler", params);
  },

  
  //  消耗经手人
  treatHandler_allHandler: (params) => {
    return API.POST("api/treatHandler/allHandler", params);
  },

  /** 查询项目会员折扣 */
  saleGoods_projectCustomerDiscount: (params) => {
    return API.POST("api/saleGoods/projectCustomerDiscount", params);
  },
  /**  查询产品会员折扣  */
  saleGoods_productCustomerDiscount: (params) => {
    return API.POST("api/saleGoods/productCustomerDiscount", params);
  },
  /**  查询通用次卡会员折扣 */
  saleGoods_generalCardCustomerDiscount: (params) => {
    return API.POST("api/saleGoods/generalCardCustomerDiscount", params);
  },
  /**  查询时效卡会员折扣 */
  saleGoods_timeCardCustomerDiscount: (params) => {
    return API.POST("api/saleGoods/timeCardCustomerDiscount", params);
  },
  /**  查询套餐卡会员折扣 */
  saleGoods_packageCardCustomerDiscount: (params) => {
    return API.POST("api/saleGoods/packageCardCustomerDiscount", params);
  },
  /**  查询员工折扣  */
  saleBill_employeeDiscount: (params) => {
    return API.POST("api/saleBill/employeeDiscount", params);
  },
};
