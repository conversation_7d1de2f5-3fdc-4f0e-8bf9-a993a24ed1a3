/**
 * Created by preference on 2022/08/11
 *  zmx
 */

import * as API from "@/api/index";
export default {
  // 获取门店
  getAllEntityApi: (params) => {
    return API.POST("api/entity/allEntity", params);
  },
  /**   */
  developerSalePerformanceDetailStatement_list: (params) => {
    return API.POST("api/developerSalePerformanceDetailStatement/list", params);
  },
  /**   */
  developerSalePerformanceDetailStatement_excel: (params) => {
    return API.exportExcel(
      "api/developerSalePerformanceDetailStatement/excel",
      params
    );
  },
  /* 业务代表 */
  geTemployeeAll: (params) => {
    return API.POST("api/channel/employeeAll", params);
  },
};
