/**
 * 消耗明细报表
 */
import * as API from '@/api/index';

export default {
  // 消耗报表
  getTreatDetailStatement: (params) => {
    return API.POST('api/entityTreatDetailStatement/list', params);
  },
  // 退消耗报表
  getTreatRefundDetailStatement: (params) => {
    return API.POST('api/entityTreatRefundDetailStatement/list', params);
  },

  // 营业报表-消耗统计
  allEntity: (params) => {
    return API.POST('api/entity/allEntity', params);
  },

  // 消耗报表
  exportTreatDetailStatement: (params) => {
    return API.exportExcel('api/entityTreatDetailStatement/excel', params);
  },
  // 退消耗报表
  exportTreatRefundDetailStatement: (params) => {
    return API.exportExcel('api/entityTreatRefundDetailStatement/excel', params);
  },

  // 消耗报表
  entityTreatDetailStatement_excelDisPlayPhone: (params) => {
    return API.exportExcel('api/entityTreatDetailStatement/excelDisPlayPhone', params);
  },
  // 退消耗报表
  entityTreatRefundDetailStatement_excelDisPlayPhone: (params) => {
    return API.exportExcel('api/entityTreatRefundDetailStatement/excelDisPlayPhone', params);
  },
};
