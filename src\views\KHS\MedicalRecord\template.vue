<template>
  <div class="MedicalRecordTemplate content_body_nopadding" v-loading="loading">
    <el-container>
      <el-aside>
        <div class="category_header">
          <el-select v-model="categoryModel" size="small" @change="changeMedicalRecordCategory">
            <el-option v-for="item in categoryList" :key="item.ID" :label="item.Name" :value="item.ID"></el-option>
          </el-select>
          <i class="el-icon-plus color_main text_bold padlt_10" style="cursor: pointer" @click="addMedicalRecordCatalogClick"></i>
          <i class="el-icon-edit color_main text_bold padrt_10 padlt_10" style="cursor: pointer" @click="editMedicalRecordCatalogClick"></i>
          <i class="el-icon-delete color_main text_bold padrt_10" style="cursor: pointer" @click="deleteMedicalRecordCatalogClick"></i>
        </div>
        <el-scrollbar class="category_content">
          <el-tree ref="catalogTree" :data="medicalRecordList" node-key="ID" :props="defaultProps" @node-click="medicalRecordCatalogItemClick" :default-expanded-keys="defaultExpandedKeys" draggable :allow-drop="catalogAllowDrop" @node-drop="catalogHandleDrop">
            <template slot-scope="{ node, data }">
              <div class="dis_flex">
                <i v-if="data.isCatalog" class="padrt_5" :class="node.expanded ? 'el-icon-folder-opened' : 'el-icon-folder'"></i>
                <i v-else class="el-icon-document padrt_5"></i>
                <div class="font_14 color_666">{{ data.Name }}</div>
              </div>
            </template>
          </el-tree>
        </el-scrollbar>
      </el-aside>
      <el-main>
        <el-header style="height: auto; padding: 0px">
          <div class="border_bottom pad_10">
            <el-row>
              <el-col :span="24" class="text_right">
                <el-button @click="saveEditMedicalRecord" type="primary" size="small" v-prevent-click :disabled="selectCatalogItem && selectCatalogItem.isCatalog">保存</el-button>
                <el-button @click="saveAddMedicalRecord" type="primary" size="small" v-prevent-click>另存新模板</el-button>
                <el-button @click="clearMedicalRecordData" type="primary" size="small" v-prevent-click>清空</el-button>
                <el-button @click="medicalRecirdPrint" type="primary" size="small" v-prevent-click>打印</el-button>
              </el-col>
            </el-row>
          </div>
        </el-header>
        <div class="flex_box">
          <medicalEditor ref="medicalRecordEditorRef" :docContent="medicalRecirContent" :paperOption="paperOption" @initEditorComplete="initEditorComplete"></medicalEditor>
        </div>
      </el-main>
    </el-container>

    <!-- 新增词条标签 内容  -->
    <el-dialog :title="isAddCatalog ? '新增病历目录' : '编辑病历目录'" :visible.sync="dialogVisible_catalog" width="500px">
      <el-form ref="ruleForm_catalog" :model="ruleForm_catalog" :rules="rules_catalog" label-width="100px" @submit.native.prevent size="small">
        <el-form-item label="病历分类" prop="CategoryID">
          <el-select v-model="ruleForm_catalog.CategoryID" size="small">
            <el-option v-for="item in categoryList" :key="item.ID" :label="item.Name" :value="item.ID"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="目录名称" prop="Name">
          <el-input size="small" v-model="ruleForm_catalog.Name"></el-input>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="dialogVisible_catalog = false" v-prevent-click>取 消</el-button>
        <el-button size="small" type="primary" @click="addSubmitCatalogClick" :loading="submitCatalogLoading" v-prevent-click>保 存</el-button>
      </div>
    </el-dialog>
    <!-- 新增并另存为新模板  -->
    <el-dialog :title="isAddTemplate ? '新增并另存为新模板' : '编辑病历模板'" :visible.sync="dialogVisible_template" width="600px">
      <el-form ref="ruleForm_template" :model="ruleForm_template" :rules="rules_template" label-width="120px" @submit.native.prevent size="small">
        <el-form-item label="病历分类" prop="CategoryID">
          <el-select v-model="ruleForm_template.CategoryID" size="small" @change="editChangeMedicalRecordCategory">
            <el-option v-for="item in categoryList" :key="item.ID" :label="item.Name" :value="item.ID"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="病历目录" prop="CatalogID">
          <el-select v-model="ruleForm_template.CatalogID" size="small">
            <el-option v-for="item in editCatalogList" :key="item.ID" :label="item.Name" :value="item.ID"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="模板名称" prop="Name">
          <el-input size="small" v-model="ruleForm_template.Name"></el-input>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="dialogVisible_template = false" v-prevent-click>取 消</el-button>
        <el-button size="small" type="primary" @click="addSubmitMedicalRecordTemplateClick" :loading="submitTemplateLoading" v-prevent-click>保 存</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import API from "@/api/KHS/MedicalRecord/template.js";
import API_ctegory from "@/api/KHS/MedicalRecord/category.js";
import medicalEditor from "@/components/medicalEditor/medicalEditor.vue";

export default {
  name: "MedicalRecordTemplate",

  props: {},
  /** 监听数据变化   */
  watch: {},
  /**  引入的组件  */
  components: {
    medicalEditor,
  },
  /**  Vue 实例的数据对象**/
  data() {
    return {
      loading: false,
      isAddTemplate: false,
      submitTemplateLoading: false,
      dialogVisible_template: false,
      dialogVisible_catalog: false,
      submitCatalogLoading: false,
      isAddCatalog: false,
      categoryList: [],
      categoryModel: "",
      medicalRecordList: [],
      editCatalogList:[],
      ruleForm_catalog: {
        Name: "", //名称
        CategoryID: "", //分类编号
      },
      rules_catalog: {
        Name: [
          { required: true, message: "请输入病历目录名称", trigger: "blur" },
          { min: 1, max: 50, message: "长度在 1 到 50 个字符", trigger: "blur" },
        ],
        CategoryID: [{ required: true, message: "请输入病历分类", trigger: "blur" }], //分类编号
      },
      defaultProps: {
        children: "Template",
        label: "Name",
      },
      selectCatalogItem: null,
      defaultExpandedKeys: [],
      ruleForm_template: {
        Name: "", //模版名称
        CategoryID: "", //分类编号
        CatalogID: "", //目录编号
        Content: "", //模版内容
        PrintJSON: "", //模版打印json
      },
      rules_template: {
        Name: [
          { required: true, message: "请输入病历目录名称", trigger: "blur" },
          { min: 1, max: 50, message: "长度在 1 到 50 个字符", trigger: "blur" },
        ],
        CategoryID: [{ required: true, message: "请选择病历分类", trigger: "blur" }], //分类编号
        CatalogID: [{ required: true, message: "请选择病历目录", trigger: "blur" }], //分类编号
      },
      paperOption: {
        size: "A4",
        marginX: 10,
        marginTop: 20,
        marginBottom: 0,
        headerContent: "",
        showHeader: false,
      },
      medicalRecirContent: "",
      addTemplateInfo: {
        ID: "",
        MedicalRecordCatalogID: "",
      },
      isAddTemplateInfo: false,
    };
  },
  /**计算属性  */
  computed: {},
  /**  方法集合  */
  methods: {
    /**  病历编辑器加载完成  */
    initEditorComplete() {
      let that = this;
      that.loading = false;
    },
    /**  保存新病历模板  */
    addSubmitMedicalRecordTemplateClick() {
      let that = this;
      that.$refs.ruleForm_template.validate((valid) => {
        if (valid) {
          if (that.isAddTemplate) {
            that.medicalRecord_createTemplate();
          } else {
            that.medicalRecord_updateTemplate(true);
          }
        }
      });
    },
    /**  打印模板  */
    medicalRecirdPrint() {
      let that = this;
      that.$refs.medicalRecordEditorRef.print();
    },
    /**  清空模板数据  */
    clearMedicalRecordData() {
      let that = this;
      that.$refs.medicalRecordEditorRef.clearData();
    },
    /**  另存为模板/ 保存新模板  */
    saveAddMedicalRecord() {
      let that = this;
      let Content = that.$refs.medicalRecordEditorRef.getDocContent();
      let Paper = that.$refs.medicalRecordEditorRef.getPaper();
      that.dialogVisible_template = true;
      that.isAddTemplate = true;
      let CatalogID = "";
      if (that.selectCatalogItem) {
        if (that.selectCatalogItem.isCatalog) {
          CatalogID = that.selectCatalogItem.ID;
        } else {
          CatalogID = that.selectCatalogItem.CatalogID;
        }
      }
      that.ruleForm_template = {
        Name: "", //模版名称
        CategoryID: that.categoryModel, //分类编号
        CatalogID: CatalogID, //目录编号
        Content: Content, //模版内容
        PrintJSON: JSON.stringify(Paper), //模版打印json
      };
    },
    /** 保存编辑后病历模板   */
    saveEditMedicalRecord() {
      let that = this;
      let Content = that.$refs.medicalRecordEditorRef.getDocContent();
      let Paper = that.$refs.medicalRecordEditorRef.getPaper();
      that.ruleForm_template = {
        Name: that.selectCatalogItem.Name, //模版名称
        ID: that.selectCatalogItem.ID,
        Content: Content, //模版内容
        PrintJSON: JSON.stringify(Paper), //模版打印json
      };
      that.medicalRecord_updateTemplate(false);
    },
    /**  完成拖拽  */
    catalogHandleDrop(draggingNode, dropNode, dropType) {
      let that = this;
      let outData = draggingNode.data;
      let intoData = dropNode.data;
      if (outData.isCatalog) {
        let beforeID = "";
        let intofindIndex = that.medicalRecordList.findIndex((i) => i.ID == intoData.ID);
        if (dropType == "before") {
          if (intofindIndex > 1) {
            beforeID = that.medicalRecordList[intofindIndex - 2].ID;
          }
        }
        if (dropType == "after") {
          beforeID = intoData.ID;
        }
        that.medicalRecord_moveCatalog(outData.ID, beforeID);
      } else {
        that.medicalRecord_moveTemplate(outData.ID, intoData.ID, intoData.CatalogID);
      }
    },
    /**  拖拽时判定目标节点能否被放置  */
    catalogAllowDrop(draggingNode, dropNode, type) {
      let outData = draggingNode.data;
      let intoData = dropNode.data;
      /* 不允许目录退拽进入目录 */
      if (outData.isCatalog && !intoData.isCatalog) {
        return false;
      }
      /* 不允许目录退拽进入目录 */
      if (!outData.isCatalog && intoData.isCatalog) {
        return false;
      }
      /* 不允许目录退拽进入目录 */
      if (outData.isCatalog && intoData.isCatalog && type == "inner") {
        return false;
      }

      return true;
    },
    /**  目录点击  */
    medicalRecordCatalogItemClick(data) {
      let that = this;
      that.selectCatalogItem = data;
      if (data.isCatalog) {
        that.medicalRecirContent = "";
        that.$refs.medicalRecordEditorRef.resetDocContent();
      } else {
        that
          .medicalRecord_getTemplate(data.ID)
          .then((data) => {
            that.medicalRecirContent = data.Content;
            that.paperOption = JSON.parse(data.PrintJSON);
          })
          .catch(() => {});
      }
    },
    /**  保存病历目录  */
    addSubmitCatalogClick() {
      let that = this;
      that.$refs.ruleForm_catalog.validate((valid) => {
        if (valid) {
          if (that.isAddCatalog) {
            that.medicalRecord_createCatalog();
          } else {
            that.medicalRecord_updateCatalog();
          }
        }
      });
    },
    /**  删除目录  */
    deleteMedicalRecordCatalogClick() {
      let that = this;
      if (Object.hasOwnProperty.call(that.selectCatalogItem, "Template")) {
        let catalogItem = that.selectCatalogItem;
        if (catalogItem.Template.length > 0) {
          that.$message.error("当前目录下存在模板，暂不可删除！");
          return;
        }
        that
          .$confirm("是否要删除病历目录", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          })
          .then(() => {
            that.medicalRecord_deleteCatalog(catalogItem.ID);
          })
          .catch(() => {
            that.$message({
              type: "info",
              message: "已取消删除",
            });
          });
      } else {
        that
          .$confirm("是否要删除病历模板", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          })
          .then(() => {
            that.medicalRecord_deleteTemplate(that.selectCatalogItem.ID);
          })
          .catch(() => {
            that.$message({
              type: "info",
              message: "已取消删除",
            });
          });
      }
    },
    /**  编辑目录 / 病历模板 */
    editMedicalRecordCatalogClick() {
      let that = this;
      /* 编辑增目录 */
      if (that.selectCatalogItem.isCatalog) {
        that.dialogVisible_catalog = true;
        that.isAddCatalog = false;
        that.ruleForm_catalog = {
          ID: that.selectCatalogItem.ID,
          Name: that.selectCatalogItem.Name, //名称
          CategoryID: that.categoryModel, //分类编号
        };
      } else {
        /* 编辑模板 */
        that.dialogVisible_template = true;
        that.isAddTemplate = false;
        let CatalogID = "";
        if (that.selectCatalogItem) {
          if (that.selectCatalogItem.isCatalog) {
            CatalogID = that.selectCatalogItem.ID;
          } else {
            CatalogID = that.selectCatalogItem.CatalogID;
          }
        }
        that.ruleForm_template = {
          Name: that.selectCatalogItem.Name, //模版名称
          CategoryID: that.categoryModel, //分类编号
          CatalogID: CatalogID, //目录编号
          Content: "", //模版内容
          PrintJSON: "", //模版打印json
          ID: that.selectCatalogItem.ID,
        };
      }
    },
    /**   新增目录 */
    addMedicalRecordCatalogClick() {
      let that = this;
      that.dialogVisible_catalog = true;
      that.isAddCatalog = true;
      that.ruleForm_catalog = {
        Name: "", //名称
        CategoryID: that.categoryModel, //分类编号
      };
    },
    /**  修改分类  */
    changeMedicalRecordCategory(val) {
      let that = this;
      that.categoryModel = val;
      that.medicalRecord_list();

    },
    /**    */
    editChangeMedicalRecordCategory() {
      let that = this;
      that.ruleForm_template.CatalogID = "";
      that.medicalRecord_list("edit");
    },
    /* ************************************************************************************************************ */
    /**  请求分类 */
    medicalRecordCategory_list() {
      let that = this;
      that.loading = true;
      let params = {
        Name: "", //名称
        Active: true, //有效性
      };
      API_ctegory.medicalRecordCategory_list(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.categoryList = res.Data;
            if (that.categoryList.length > 0) {
              that.categoryModel = that.categoryList[0].ID;
              that.medicalRecord_list();
              that.medicalRecord_list("edit");
            }
          } else {
            that.$message.error(res.Message);
          }
        })
        .catch((fail) => {
          that.$message.error(fail);
        });
    },
    /** 获取目录列表   */
    medicalRecord_list(type) {
      let that = this;
      // ruleForm_template.CategoryID
      let CategoryID = "";
      if (type == "edit") {
        CategoryID = that.ruleForm_template.CategoryID ? that.ruleForm_template.CategoryID:that.categoryModel;
      } else {
        CategoryID = that.categoryModel;
      }
      let params = {
        Name: "", //模版名称搜索
        CategoryID: CategoryID, //分类编号
      };
      API.medicalRecord_list(params)
        .then((res) => {
          if (res.StateCode == 200) {
            if (type == "edit") {
              that.editCatalogList = res.Data;
            } else {
              that.medicalRecordList = res.Data.map((i) => {
                i.isCatalog = true;
                return i;
              });
              if (that.medicalRecordList.length > 0) {
                let firstItem = that.medicalRecordList[0];
                that.$nextTick(() => {
                  if (that.isAddTemplateInfo) {
                    let currentCatalogItem = that.medicalRecordList.find((i) => i.ID == that.addTemplateInfo.MedicalRecordCatalogID);
                    if (currentCatalogItem) {
                      let currentItem = currentCatalogItem.Template.find((i) => that.addTemplateInfo.ID == i.ID);
                      if (currentItem) {
                        that.defaultExpandedKeys = [that.addTemplateInfo.MedicalRecordCatalogID];
                        that.selectCatalogItem = currentItem;
                        that.$refs.catalogTree.setCurrentKey(currentItem.ID);
                      }
                    }
                  } else {
                    that.defaultExpandedKeys = [firstItem.ID];
                    that.selectCatalogItem = firstItem;
                    if (that.$refs.catalogTree) {
                      that.$refs.catalogTree.setCurrentKey(firstItem.ID);
                    }
                  }
                });
              }
            }
          } else {
            that.$message.error(res.Message);
          }
        })
        .catch((fail) => {
          that.$message.error(fail);
        });
    },
    /**  新增目录  */
    medicalRecord_createCatalog() {
      let that = this;
      let params = {
        Name: that.ruleForm_catalog.Name, //名称
        CategoryID: that.ruleForm_catalog.CategoryID, //分类编号
      };
      API.medicalRecord_createCatalog(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.$message.success("操作成功");
            that.dialogVisible_catalog = false;
            that.medicalRecord_list();
            that.medicalRecord_list("edit");
          } else {
            that.$message.error(res.Message);
          }
        })
        .catch((fail) => {
          that.$message.error(fail);
        });
    },
    /**   编辑目录 */
    medicalRecord_updateCatalog() {
      let that = this;
      let params = {
        ID: that.ruleForm_catalog.ID, //编号
        Name: that.ruleForm_catalog.Name, //名称
        CategoryID: that.ruleForm_catalog.CategoryID, //分类编号
      };
      API.medicalRecord_updateCatalog(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.$message.success("操作成功");
            that.dialogVisible_catalog = false;
            that.medicalRecord_list();

            that.medicalRecord_list("edit");
          } else {
            that.$message.error(res.Message);
          }
        })
        .catch((fail) => {
          that.$message.error(fail);
        });
    },
    /** 删除目录   */
    medicalRecord_deleteCatalog(ID) {
      let that = this;
      let params = {
        ID: ID,
      };
      API.medicalRecord_deleteCatalog(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.$message.success("操作成功");
            that.medicalRecord_list();

            that.medicalRecord_list("edit");
          } else {
            that.$message.error(res.Message);
          }
        })
        .catch((fail) => {
          that.$message.error(fail);
        });
    },
    /**  目录移动  */
    medicalRecord_moveCatalog(MoveID, BeforeID) {
      let that = this;
      let params = {
        MoveID: MoveID,
        BeforeID: BeforeID,
        CategoryID: that.categoryModel, //目录编号
      };
      API.medicalRecord_moveCatalog(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.$message.success("操作成功");
            that.medicalRecord_list();

            that.medicalRecord_list("edit");
          } else {
            that.$message.error(res.Message);
          }
        })
        .catch((fail) => {
          that.$message.error(fail);
        });
    },
    /**  模板移动  */
    medicalRecord_moveTemplate(MoveID, BeforeID, CatalogID) {
      let that = this;
      let params = {
        MoveID: MoveID,
        BeforeID: BeforeID,
        CatalogID: CatalogID, //目录编号
      };
      API.medicalRecord_moveTemplate(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.$message.success("操作成功");
            that.medicalRecord_list();

            that.medicalRecord_list("edit");
          } else {
            that.$message.error(res.Message);
          }
        })
        .catch((fail) => {
          that.$message.error(fail);
        });
    },
    /**    删除病历模板 */
    medicalRecord_deleteTemplate(ID) {
      let that = this;
      let params = { ID: ID };
      API.medicalRecord_deleteTemplate(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.$message.success("操作成功");
            that.medicalRecord_list();

            that.medicalRecord_list("edit");
          } else {
            that.$message.error(res.Message);
          }
        })
        .catch((fail) => {
          that.$message.error(fail);
        });
    },
    /**  新增病历模板  */
    medicalRecord_createTemplate() {
      let that = this;
      let params = {
        Name: that.ruleForm_template.Name, //模版名称
        CategoryID: that.ruleForm_template.CategoryID, //分类编号
        CatalogID: that.ruleForm_template.CatalogID, //目录编号
        Content: that.ruleForm_template.Content, //模版内容
        PrintJSON: that.ruleForm_template.PrintJSON, //模版打印json
      };
      that.submitTemplateLoading = true;
      API.medicalRecord_createTemplate(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.$message.success("操作成功");
            that.dialogVisible_template = false;
            that.medicalRecord_list();

            that.medicalRecord_list("edit");
            that.submitTemplateLoading = false;
            that.addTemplateInfo = res.Data;
            that.isAddTemplateInfo = true;
          } else {
            that.submitTemplateLoading = false;
            that.$message.error(res.Message);
          }
        })
        .catch((fail) => {
          that.submitTemplateLoading = false;
          that.$message.error(fail);
        });
    },
    /**  编辑病历模板信息  */
    medicalRecord_updateTemplate(isRefresh) {
      let that = this;
      let params = {
        ID: that.ruleForm_template.ID, //编号
        Name: that.ruleForm_template.Name, //名称
        Content: that.ruleForm_template.Content, //模版内容
        PrintJSON: that.ruleForm_template.PrintJSON, //模版打印json
        CategoryID: that.ruleForm_template.CategoryID, //分类编号
        CatalogID: that.ruleForm_template.CatalogID, //目录编号
      };
      that.submitTemplateLoading = true;
      API.medicalRecord_updateTemplate(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.$message.success("操作成功");
            that.dialogVisible_template = false;
            that.submitTemplateLoading = false;
            // 是否否刷新病历目录
            if (isRefresh) {
              that.medicalRecord_list();

              that.medicalRecord_list("edit");
            }
          } else {
            that.submitTemplateLoading = false;
            that.$message.error(res.Message);
          }
        })
        .catch((fail) => {
          that.submitTemplateLoading = false;
          that.$message.error(fail);
        });
    },
    /**  获取病历信息  */
    async medicalRecord_getTemplate(ID) {
      let that = this;
      try {
        that.loading = true;
        let params = { ID: ID };
        let res = await API.medicalRecord_getTemplate(params);
        if (res.StateCode == 200) {
          that.loading = false;
          return res.Data;
        } else {
          that.loading = false;
          that.$message.error(res.Message);
        }
      } catch (error) {}
    },
  },
  /** 创建实例之前执行函数   */
  beforeCreate() {},
  /**  实例创建完成之后  */
  created() {},
  /**  在挂载开始之前被调用  */
  beforeMount() {},
  /**  实例被挂载后调用  */
  mounted() {
    let that = this;
    that.medicalRecordCategory_list();
  },
  /**  数据更新 之前 调用  */
  beforeUpdate() {},
  /** 数据更新 完成 调用   */
  updated() {},
  /**  实例销毁之前调用  */
  beforeDestroy() {},
  /**  实例销毁后调用  */
  destroyed() {},
};
</script>

<style lang="scss">
.MedicalRecordTemplate {
  box-sizing: border-box;
  height: 100%;
  .el-container {
    height: 100% !important;
    .el-aside {
      height: 100%;
      border-right: 1px solid #f0f0f0;
      display: flex;
      flex-direction: column;
      .category_header {
        display: flex;
        align-items: center;
        padding: 10px;
        border-bottom: 1px solid #eeeeee;
        .entry_title {
          margin: unset;
        }
      }
      .category_content {
        flex: 1;
        .el-scrollbar__wrap {
          overflow-x: hidden;
        }
        .el-tree {
          .el-tree-node__content:hover {
            background-color: var(--zl-color-orange-primary-header);
          }
          .is-current > .el-tree-node__content {
            background-color: var(--zl-color-orange-primary-header);
          }
        }
      }
    }
    .el-main {
      padding: unset;
      display: flex;
      flex-direction: column;
      .medicalRecordEdit_header {
        .el-form-item {
          margin-bottom: unset;
        }
      }
    }
  }
}
</style>
