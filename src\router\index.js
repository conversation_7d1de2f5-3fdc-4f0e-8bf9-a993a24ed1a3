import Vue from "vue";
import Router from "vue-router";
import Login from "@/views/login/login";
import BrandLogin from "@/views/login/brandLogin";
import ResetPassword from "@/views/login/resetPassword";
import AuthorizationError from "@/views/Marketing/App/WeiXinAuthorizationError";
import AuthorizationErrorSucceed from "@/views/Marketing/App/WeiXinAuthorizationSucceed";
import PadView from "../views/iBeauty/PadDisplay/PadView.vue";
import BindOpenid from "@/views/Marketing/App/BindOpenid.vue";
import BindOpenidSucceed from "@/views/Marketing/App/BindOpenidSucceed.vue";
import ConsultationForm from "@/views/CRM/Consultation/ConsultationForm.vue";

//

import API from "@/api/account";
const loginPath = "/Brand/:EnterpriseCode";

Vue.use(Router);
let route = new Router({
  routes: [
    {
      path: "/login",
      name: "登录",
      component: Login,
    },
    {
      path: loginPath,
      name: "品牌登录",
      component: BrandLogin,
    },
    {
      path: "/resetPassword",
      name: "修改密码",
      component: ResetPassword,
    },
    {
      path: "/Marketing/WeiXinAuthorizationError",
      name: "",
      component: AuthorizationError,
    },
    {
      path: "/Marketing/WeiXinAuthorizationSucceed",
      name: "",
      component: AuthorizationErrorSucceed,
    },
    {
      path: "/iBeauty/PadView",
      name: "",
      component: PadView,
    },
    {
      path: "/Marketing/BindOpenid",
      name: "BindOpenid",
      component: BindOpenid,
    },
    {
      path: "/Marketing/BindOpenidSucceed",
      name: "BindOpenidSucceed",
      component: BindOpenidSucceed,
    },
    {
      path: "/Customer/ConsultationForm",
      name: "ConsultationForm",
      component: ConsultationForm,
    },
  ],
  mode: "history",
});

route.$addRoutes = (params) => {
  route.matcher = new Router({
    routes: [
      {
        path: "/login",
        name: "登录",
        component: Login,
      },
      {
        path: loginPath,
        name: "品牌登录",
        component: BrandLogin,
      },
      {
        path: "/resetPassword",
        name: "修改密码",
        component: ResetPassword,
      },
      {
        path: "/Marketing/WeiXinAuthorizationError",
        name: "",
        component: AuthorizationError,
      },
      {
        path: "/Marketing/WeiXinAuthorizationSucceed",
        name: "",
        component: AuthorizationErrorSucceed,
      },
      {
        path: "/iBeauty/PadView",
        name: "",
        component: PadView,
      },
      {
        path: "/Marketing/BindOpenid",
        name: "BindOpenid",
        component: BindOpenid,
      },
      {
        path: "/Marketing/BindOpenidSucceed",
        name: "BindOpenidSucceed",
        component: BindOpenidSucceed,
      },
      {
        path: "/Customer/ConsultationForm",
        name: "ConsultationForm",
        component: ConsultationForm,
      },
    ],
    mode: "history",
  }).matcher;
  params.forEach(r => route.addRoute(r));
};
if (localStorage.getItem("access-user") != undefined) {
  API.getPCPermissionRouter().then((res) => {
    if (res.StateCode == 200) {
      var routerchildren = [];
      res.Data.forEach(function (item) {
        var routerChild = {};
        routerChild.path = item.RouterPath;
        routerChild.component = () => import(`@/${item.RouterComponentPath}`);
        routerChild.name = item.RouterName;
        var routerMeta = {};
        routerMeta.title = item.RouterMeta.Title;
        routerMeta.ExternalLinks = item.RouterMeta.ExternalLinks;
        routerMeta.IsVerifyStore = item.RouterMeta.IsVerifyStore;
        routerMeta.IsVerifyWarehouse = item.RouterMeta.IsVerifyWarehouse;
        routerMeta.Permission = item.RouterMeta.Permission;
        routerChild.meta = routerMeta;
        routerchildren.push(routerChild);
      });
      var routers = [];
      var router = {};
      router.path = "/";
      router.component = () => import("@/components/common/Master");
      router.children = routerchildren;
      routers.push(router);
      var routerNotFound = {};
      routerNotFound.path = "*";
      routerNotFound.redirect = "/";
      routers.push(routerNotFound);
      route.$addRoutes(routers);
    }
  });
} else {
  var routers = [];
  var router = {};
  router.path = "*";
  router.redirect = "/login";
  routers.push(router);
  route.$addRoutes(routers);
}

export default route;
