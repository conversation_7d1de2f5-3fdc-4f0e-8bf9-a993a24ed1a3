/**
 * Created by preference on 2022/08/11
 *  zmx
 */

import * as API from '@/api/index';
export default {
  /* 销售明细 */
  channelTreatDetailStatement_list: (params) => {
    return API.POST('api/channelTreatDetailStatement/list', params);
  },
  // 销售明细导出
  channelTreatDetailStatement_excel: (params) => {
    return API.exportExcel('api/channelTreatDetailStatement/excel', params);
  },

  /* 补欠款明细 */
  channelTreatRefundDetailStatement_list: (params) => {
    return API.POST('api/channelTreatRefundDetailStatement/list', params);
  },
  // 补欠款明细导出
  channelTreatRefundDetailStatement_excel: (params) => {
    return API.exportExcel('api/channelTreatRefundDetailStatement/excel', params);
  },

  // 营业报表-消耗统计
  allEntity: (params) => {
    return API.POST('api/entity/allEntity', params);
  },
};
