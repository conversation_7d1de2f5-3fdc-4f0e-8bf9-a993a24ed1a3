// 拼团专区
import * as API from '@/api/index'
export default {
  /**  拼团列表*/
  groupon_all: params => {
    return API.POST('api/groupon/all', params)
  },
  /**  拼团新增*/
  groupon_create: params => {
    return API.POST('api/groupon/create', params)
  },
  /**  拼团编辑*/
  groupon_update: params => {
    return API.POST('api/groupon/update', params)
  },
  /**  拼团详情   */
  groupon_info: params => {
    return API.POST('api/groupon/info', params)
  },
  /**  拼团详情 活动价格  */
  groupon_price: params => {
    return API.POST('api/groupon/price', params)
  },
  /**  拼团详情-适应门店  */
  groupon_entity: params => {
    return API.POST('api/groupon/entity',params)
  },

  // 新增销售范围、消耗范围 获取权限范围
  getEntityList:params => {
    return API.POST('api/entity/list',params)
  },
  // 项目列表
  getProjectList:params => {
    return API.POST('api/project/list',params)
  },

  // 产品列表
  getProductList:params => {
    return API.POST('api/productSale/list',params)
  },

  // 通用次卡列表
  getGeneralCardList:params => {
    return API.POST('api/generalCard/list',params)
  },
  // 时效卡列表
  getTimeCardList:params => {
    return API.POST('api/timeCard/list',params)
  },


 

}