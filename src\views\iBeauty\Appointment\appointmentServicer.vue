<template>
  <div class="appointmentServicer content_body">
    <div class="nav_header">
      <el-row>
        <el-col :span="20">
          <el-form :inline="true" size="small" @keyup.enter.native="handleSearch">
            <el-form-item label="预约角色名称">
              <el-input v-model="searchForm.Name" @clear="handleSearch" placeholder="输入预约角色搜索" clearable></el-input>
            </el-form-item>
            <el-form-item label="有效性">
              <el-select @change="handleSearch" @clear="handleSearch" v-model="searchForm.Active" placeholder="请选择" clearable>
                <el-option label="有效" :value="true"></el-option>
                <el-option label="无效" :value="false"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleSearch" v-prevent-click>搜索</el-button>
            </el-form-item>
          </el-form>
        </el-col>
        <el-col :span="4" class="text_right">
          <el-button type="primary" size="small" @click="showAddDialog">新增</el-button>
        </el-col>
      </el-row>
    </div>
    <div>
      <el-table size="small" :data="tableData" v-loading="loading">
        <el-table-column prop="Name" label="预约角色名称"></el-table-column>
        <el-table-column label="移动" min-width="180px">
          <template slot-scope="scope">
            <el-button
              size="small"
              type="primary"
              circle
              icon="el-icon-upload2"
              @click="upOneClick(scope.row, scope.$index)"
              v-prevent-click
              :disabled="scope.$index == 0"
            ></el-button>
            <el-button
              size="small"
              type="primary"
              circle
              icon="el-icon-top"
              @click="upClick(scope.row, scope.$index)"
              v-prevent-click
              :disabled="scope.$index == 0"
            ></el-button>
            <el-button
              size="small"
              type="primary"
              circle
              icon="el-icon-bottom"
              @click="downClick(scope.row, scope.$index)"
              v-prevent-click
              :disabled="scope.$index == tableData.length - 1"
            >
            </el-button>
            <el-button
              size="small"
              type="primary"
              circle
              icon="el-icon-download"
              @click="downOneClick(scope.row, scope.$index)"
              v-prevent-click
              :disabled="scope.$index == tableData.length - 1"
            ></el-button>
          </template>
        </el-table-column>
        <el-table-column prop="Active" label="有效性">
          <template slot-scope="scope">
            {{ scope.row.Active ? "有效" : "无效" }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="80">
          <template slot-scope="scope">
            <el-button type="primary" size="small" @click="showEditDialog(scope.row)">编辑</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!--弹窗-->
    <el-dialog :title="isAdd ? '新增预约角色' : '编辑预约角色'" :visible.sync="dialogVisible" width="850px" @close="addClose">
      <el-tabs v-model="activeName">
        <el-tab-pane label="基本信息" name="Info">
          <div>
            <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="160px" size="small">
              <el-form-item label="可预约角色" prop="Name">
                <el-input clearable v-model="ruleForm.Name"></el-input>
              </el-form-item>
              <el-form-item label="同时段最大预约数" prop="AppointmentNumber">
                <span slot="label">
                  同时段最大预约数
                  <el-popover placement="top-start" width="200px" trigger="hover">
                    <p>不限制同一时段预约数，请填写“0”</p>
                    <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                  </el-popover>
                </span>
                <el-input v-model="ruleForm.AppointmentNumber" v-input-fixed="0"></el-input>
              </el-form-item>
              <el-form-item label="是否有效" v-if="!isAdd">
                <el-radio-group v-model="ruleForm.Active">
                  <el-radio :label="true">是</el-radio>
                  <el-radio :label="false">否</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-form>
          </div>
        </el-tab-pane>

        <el-tab-pane label="适用职务" name="Jobtype">
          <el-row :gutter="20" class="pad_10_0">
            <el-col :span="10">
              <el-input placeholder="输入职务名称搜索" size="small" v-model="searchJobName" clearable></el-input>
            </el-col>
            <el-col :span="14">
              <el-button type="primary" @click="clickAddApplicableDuty" size="small">配置适用职务 </el-button>
            </el-col>
          </el-row>
          <el-table
            size="small"
            :data="ruleForm.JobTypeList.filter((data) => !searchJobName || data.JobName.toLowerCase().includes(searchJobName.toLowerCase()))"
            max-height="450"
          >
            <el-table-column label="职务名称" sortable>
              <template slot-scope="scope">
                <span>{{ scope.row.JobName }}</span>
              </template>
            </el-table-column>
            <el-table-column label="职务描述">
              <template slot-scope="scope">
                <span>{{ scope.row.JobDescription }}</span>
              </template> </el-table-column
            ><el-table-column label="移动" min-width="90px">
              <template slot-scope="scope">
                <el-button
                  size="small"
                  type="primary"
                  circle
                  icon="el-icon-upload2"
                  @click="upOneJobNameClick(scope.row, scope.$index)"
                  v-prevent-click
                  :disabled="scope.$index == 0"
                ></el-button>
                <el-button
                  size="small"
                  type="primary"
                  circle
                  icon="el-icon-top"
                  @click="upJobNameClick(scope.row, scope.$index)"
                  v-prevent-click
                  :disabled="scope.$index == 0"
                ></el-button>
                <el-button
                  size="small"
                  type="primary"
                  circle
                  icon="el-icon-bottom"
                  @click="downJobNameClick(scope.row, scope.$index)"
                  v-prevent-click
                  :disabled="scope.$index == ruleForm.JobTypeList.length - 1"
                >
                </el-button>
                <el-button
                  size="small"
                  type="primary"
                  circle
                  icon="el-icon-download"
                  @click="downOneJobNameClick(scope.row, scope.$index)"
                  v-prevent-click
                  :disabled="scope.$index == ruleForm.JobTypeList.length - 1"
                ></el-button>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="80">
              <template slot-scope="scope">
                <el-button type="danger" size="mini" @click="deleteSelectedJobType(scope.row)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
        <el-tab-pane label="适用门店" name="Entity">
          <div class="message el-message--info marbm_10">
            <i class="el-message__icon el-icon-info"></i>
            <p class="el-message__content">适用于同级所有节点，则只需勾选父节点。比如：适用于所有节点，只需勾选“顶级/第一个”节点。</p>
          </div>
          <el-scrollbar class="el-scrollbar_height">
            <el-tree
              ref="treeSale"
              :expand-on-click-node="false"
              :check-on-click-node="true"
              :check-strictly="true"
              :default-expanded-keys="defaultExpandedKeys"
              :default-checked-keys="defaultCheckedKeys"
              :data="salesScopeData"
              show-checkbox
              node-key="ID"
              :props="defaultProps"
            ></el-tree>
          </el-scrollbar>
        </el-tab-pane>
      </el-tabs>

      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="dialogVisible = false" v-prevent-click>取 消</el-button>
        <el-button type="primary" size="small" @click="addSubmit" :loading="modalLoading" v-prevent-click>保 存 </el-button>
      </div>
    </el-dialog>

    <!--添加适用职务弹出框-->
    <el-dialog :visible.sync="addApplicableDutyDialog" width="700px">
      <div slot="title">
        <span>配置适用职务</span>
      </div>
      <el-row>
        <el-col :span="10" class="pad_10_0">
          <el-input size="small" v-model="JobName" clearable placeholder="输入职务名称搜索"></el-input>
        </el-col>
      </el-row>
      <el-table
        size="small"
        :data="JobTypeList.filter((data) => !JobName || data.JobName.toLowerCase().includes(JobName.toLowerCase()))"
        max-height="480px"
        @selection-change="getSelectedJobType"
        @cell-click="jobListCellClick"
        ref="multipleTable"
      >
        <el-table-column type="selection" width="55"></el-table-column>
        <el-table-column prop="JobName" label="职务名称" sortable column-key="JobName"></el-table-column>
        <el-table-column prop="JobDescription" label="职务描述"></el-table-column>
      </el-table>
      <span slot="footer" class="dialog-footer">
        <el-button @click="addApplicableDutyDialog = false" v-prevent-click size="small">取 消</el-button>
        <el-button type="primary" size="small" @click="submitFormApplicableDuty" v-prevent-click>确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import API from "@/api/iBeauty/Appointment/appointmentServicer.js";
export default {
  name: "AppointmentServicer",

  props: {},
  /** 监听数据变化   */
  watch: {
    JobName() {
      var that = this;
      that.$nextTick(() => {
        that.JobTypeList.forEach((val) => {
          that.$refs.multipleTable.toggleRowSelection(val, false);
        });
      });

      if (that.selectedJobTypeList.length > 0) {
        that.selectedJobTypeList.forEach((item) => {
          that.JobTypeList.forEach((val) => {
            if (item.ID == val.ID) {
              that.$nextTick(() => {
                that.$refs.multipleTable.toggleRowSelection(val);
              });
            }
          });
        });
      }
    },
  },
  /**  引入的组件  */
  components: {},
  /**  Vue 实例的数据对象**/
  data() {
    return {
      loading: false,
      modalLoading: false,
      dialogVisible: false,
      addApplicableDutyDialog: false,
      isAdd: true,
      activeName: "Info",
      searchJobName: "",
      JobName: "",

      tableData: [],
      entityList: [], //门店列表
      JobTypeList: [],
      salesScopeData: [],
      selectedJobTypeList: [],
      defaultExpandedKeys: [1],
      defaultCheckedKeys: [],
      defaultProps: {
        children: "Child",
        label: "EntityName",
      }, // 适用范围选择配置项

      ruleForm: {
        Name: "",
        Active: true,
        ID: "",
        AppointmentNumber: "",
        JobTypeList: [],
        EntityList: [],
      },
      rules: {
        Name: [{ required: true, message: "请输入预约人名称", trigger: "blur" }],
        AppointmentNumber: [{ required: true, message: "请输入同时段最大预约数", trigger: "blur" }],
        Active: [{ required: true, message: "请选择有效性", trigger: "change" }],
      },
      searchForm: {
        Name: "", //预约角色名
        Active: true, //有效性
      },
    };
  },
  /**计算属性  */
  computed: {},
  /**  方法集合  */
  methods: {
    /**    */
    upOneJobNameClick(row, index) {
      let that = this;
      that.ruleForm.JobTypeList.splice(index, 1);
      that.ruleForm.JobTypeList.unshift(row);
    },
    /**    */
    upJobNameClick(row, index) {
      let that = this;
      that.ruleForm.JobTypeList.splice(index, 1);
      that.ruleForm.JobTypeList.splice(index - 1, 0, row);
    },
    /**    */
    downJobNameClick(row, index) {
      let that = this;
      that.ruleForm.JobTypeList.splice(index, 1);
      that.ruleForm.JobTypeList.splice(index + 1, 0, row);
    },
    /**    */
    downOneJobNameClick(row, index) {
      let that = this;
      that.ruleForm.JobTypeList.splice(index, 1);
      that.ruleForm.JobTypeList.push(row);
    },

    /**    */
    handleSearch() {
      let that = this;
      that.appointmentServicer_all();
    },
    // 移动首部
    // 移动首部
    upOneClick: function (row) {
      var that = this;
      that.appointmentServicer_move(row.ID, "");
    },
    // 移动尾部
    downOneClick: function (row, index) {
      var that = this;
      var tabIndex = that.tableData.length;
      var beforeId = "";
      if (index < tabIndex - 1) {
        beforeId = that.tableData[tabIndex - 1].ID;
      }
      that.appointmentServicer_move(row.ID, beforeId);
    },
    // 向上
    upClick: function (row, index) {
      var that = this;
      var beforeId = "";
      if (index > 1) {
        beforeId = that.tableData[index - 2].ID;
      }
      that.appointmentServicer_move(row.ID, beforeId);
    },
    // 向下
    downClick: function (row, index) {
      var that = this;
      var beforeId = "";
      if (index + 1 != that.tableData.length) {
        beforeId = that.tableData[index + 1].ID;
      }
      that.appointmentServicer_move(row.ID, beforeId);
    },
    /**   新增 */
    showAddDialog() {
      let that = this;
      that.isAdd = true;
      that.ruleForm = {
        Name: "", //预约角色列表
        AppointmentNumber: "", //同时间段最大预约数
        JobTypeList: [], //适用职务列表
        EntityList: [], //适用门店列表
      };
      that.defaultExpandedKeys = [1];
      that.defaultCheckedKeys = [];
      Object.assign(that.salesScopeData, that.entityList);
      that.dialogVisible = true;

      that.entity_list();
    },
    /**    */
    showEditDialog(row) {
      let that = this;
      that.dialogVisible = true;

      that.defaultExpandedKeys = [1];
      that.defaultCheckedKeys = [];
      that.isAdd = false;
      Object.assign(that.salesScopeData, that.entityList);

      that.entity_list();
      that.appointmentServicer_detail(row.ID);
    },
    /**  关闭   */
    addClose() {
      let that = this;
      that.$refs.treeSale.setCheckedKeys([]);
    },
    /*  */
    clickAddApplicableDuty() {
      var that = this;
      that.JobName = "";
      that.addApplicableDutyDialog = true;
      that.$nextTick(() => {
        that.$refs.multipleTable.clearSelection();
        if (that.ruleForm.JobTypeList.length > 0) {
          that.ruleForm.JobTypeList.forEach((item) => {
            that.JobTypeList.forEach((val) => {
              if (item.ID == val.ID) {
                that.$nextTick(() => {
                  that.$refs.multipleTable.toggleRowSelection(val, true);
                });
              }
            });
          });
        }
      });
    },
    /* 获取所选中的职务列表 */
    getSelectedJobType(list) {
      var that = this;
      that.selectedJobTypeList = list;
    },
    /**   单选 */
    jobListCellClick(row) {
      let that = this;
      that.$refs.multipleTable.toggleRowSelection(row);
    },
    /* 添加适用职务表单保存事件 */
    submitFormApplicableDuty() {
      var that = this;
      that.ruleForm.JobTypeList = Object.assign([], that.selectedJobTypeList);
      that.addApplicableDutyDialog = false;
    },
    /* 删除所选职务 */
    deleteSelectedJobType(val) {
      var that = this;
      that.ruleForm.JobTypeList.splice(
        that.ruleForm.JobTypeList.findIndex((p) => p.ID == val.ID),
        1
      );
    },

    /**    */
    addSubmit() {
      let that = this;
      that.$refs.ruleForm.validate((valid) => {
        if (valid) {
          if (that.isAdd) {
            that.appointmentServicer_create();
          } else {
            that.appointmentServicer_update();
          }
        }
      });
    },
    /**  ••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••  */
    /**  预约角色列表  */
    async appointmentServicer_all() {
      let that = this;
      try {
        that.loading = true;
        let params = {
          Name: that.searchForm.Name, //预约角色名
          Active: that.searchForm.Active, //有效性
        };
        let res = await API.appointmentServicer_all(params);
        if (res.StateCode == 200) {
          that.tableData = res.Data;
        } else {
          that.$message.error(res.Message);
        }
        that.loading = false;
      } catch (error) {
        that.$message.error(error);
      }
    },
    /**   预约角色详细 */
    async appointmentServicer_detail(ID) {
      let that = this;
      try {
        let params = { ID: ID };
        let res = await API.appointmentServicer_detail(params);
        if (res.StateCode == 200) {
          let tmp = res.Data;
          tmp.JobTypeList = tmp.JobTypeList.map((i) => {
            return {
              ID: i.JobType,
              JobName: i.JobName,
            };
          });
          that.ruleForm = Object.assign({}, tmp);
          that.defaultExpandedKeys = tmp.EntityList;
          that.defaultCheckedKeys = tmp.EntityList;
        } else {
          that.$message.error(res.Message);
        }
      } catch (error) {
        that.$message.error(error);
      }
    },
    /**   新建预约角色 */
    async appointmentServicer_create() {
      let that = this;
      that.modalLoading = true;
      try {
        let params = {
          Name: that.ruleForm.Name, //预约角色列表
          Active: true,
          AppointmentNumber: that.ruleForm.AppointmentNumber, //同时间段最大预约数
          JobTypeList: that.ruleForm.JobTypeList.map((i, index) => {
            return {
              JobTypeID: i.ID, //职务编号
              Sequence: index + 1, //排序字段
            };
          }), //适用职务列表
          EntityList: that.$refs.treeSale.getCheckedKeys(), //适用门店列表
        };
        let res = await API.appointmentServicer_create(params);
        if (res.StateCode == 200) {
          that.dialogVisible = false;
          that.$message.success("操作成功");
          that.appointmentServicer_all();
        } else {
          that.$message.error(res.Message);
        }
        that.modalLoading = false;
      } catch (error) {
        that.modalLoading = false;
        that.$message.error(error);
      }
    },
    /**  更新预约角色  */
    async appointmentServicer_update() {
      let that = this;
      that.modalLoading = true;
      try {
        let params = {
          ID: that.ruleForm.ID,
          Name: that.ruleForm.Name, //预约角色列表
          Active: that.ruleForm.Active,
          AppointmentNumber: that.ruleForm.AppointmentNumber, //同时间段最大预约数
          JobTypeList: that.ruleForm.JobTypeList.map((i, index) => {
            return {
              JobTypeID: i.ID, //职务编号
              Sequence: index + 1, //排序字段
            };
          }), //适用职务列表
          EntityList: that.$refs.treeSale.getCheckedKeys(), //适用门店列表
        };
        let res = await API.appointmentServicer_update(params);
        if (res.StateCode == 200) {
          that.dialogVisible = false;
          that.$message.success("操作成功");
          that.appointmentServicer_all();
        } else {
          that.$message.error(res.Message);
        }
        that.modalLoading = false;
      } catch (error) {
        that.modalLoading = false;
        that.$message.error(error);
      }
    },
    /**  移动  */
    async appointmentServicer_move(MoveID, BeforeID) {
      let that = this;
      try {
        let params = {
          MoveID: MoveID,
          BeforeID: BeforeID,
        };
        let res = await API.appointmentServicer_move(params);
        if (res.StateCode == 200) {
          that.$message.success("操作成功");
          that.appointmentServicer_all();
        } else {
          that.$message.error(res.Message);
        }
      } catch (error) {
        that.$message.error(error);
      }
    },
    /**  组织列表  */
    async entity_list() {
      let that = this;
      try {
        let params = {};
        let res = await API.entity_list(params);
        if (res.StateCode == 200) {
          that.entityList = res.Data;
        } else {
          that.$message.error(res.Message);
        }
      } catch (error) {
        that.$message.error(error);
      }
    },
    /** 职务列表  */
    async jobtype_all() {
      let that = this;
      try {
        let params = {};
        let res = await API.jobtype_all(params);
        if (res.StateCode == 200) {
          that.JobTypeList = res.Data;
        } else {
          that.$message.error(res.Message);
        }
      } catch (error) {
        that.$message.error(error);
      }
    },
  },
  /** 创建实例之前执行函数   */
  beforeCreate() {},
  /**  实例创建完成之后  */
  created() {},
  /**  在挂载开始之前被调用  */
  beforeMount() {},
  /**  实例被挂载后调用  */
  mounted() {
    this.appointmentServicer_all();
    this.jobtype_all();
    this.entity_list();
  },
  /**  数据更新 之前 调用  */
  beforeUpdate() {},
  /** 数据更新 完成 调用   */
  updated() {},
  /**  实例销毁之前调用  */
  beforeDestroy() {},
  /**  实例销毁后调用  */
  destroyed() {},
};
</script>

<style lang="scss">
.appointmentServicer {
  .el-scrollbar_height {
    height: 50vh;

    .el-scrollbar__wrap {
      overflow-x: hidden;
    }
  }
}
</style>
