<template>
  <div class="channelTreatPerformanceDetail content_body" v-loading="loading">
    <div class="nav_header">
      <el-form :inline="true" size="small" @keyup.enter.native="handleSearch">
        <el-form-item label="门店">
          <el-select placeholder="请选择门店" filterable clearable v-model="search.EntityID" @change="handleSearch" size="small">
            <el-option v-for="item in entityList" :key="item.ID" :label="item.Name" :value="item.ID"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="订单编号">
          <el-input size="small" v-model="search.BillID" @clear="handleSearch" clearable placeholder="输入订单编号搜索"></el-input>
        </el-form-item>
        <el-form-item label="时间筛选">
          <el-date-picker v-model="search.QueryDate" :picker-options="pickerOptions" unlink-panels type="daterange" range-separator="至" value-format="yyyy-MM-dd" start-placeholder="开始日期" end-placeholder="结束日期" @change="handleSearch" :clearable="false"></el-date-picker>
        </el-form-item>
        <el-form-item label="商品类型">
          <el-select v-model="search.TreatCardTypeName" clearable filterable placeholder="请选择商品类型" :default-first-option="true" @change="handleSearch">
            <el-option label="项目卡" value="项目卡"></el-option>
            <el-option label="储值卡" value="储值卡"></el-option>
            <el-option label="时效卡" value="时效卡"></el-option>
            <el-option label="通用次卡" value="通用次卡"></el-option>
            <el-option label="产品卡" value="产品卡"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="商品名称">
          <el-input size="small" v-model="search.GoodsName" @clear="handleSearch" clearable placeholder="输入商品名称搜索"></el-input>
        </el-form-item>

        <el-form-item label="客户信息">
          <el-input size="small" v-model="search.CustomerName" @clear="handleSearch" clearable placeholder="请输入客户姓名或手机"></el-input>
        </el-form-item>
        <el-form-item label="渠道名称">
          <el-input size="small" v-model="search.ChannelName" @clear="handleSearch" clearable placeholder="输入渠道名称搜索"></el-input>
        </el-form-item>
        <el-form-item label="渠道类型">
          <el-select v-model="search.ChannelTypeID" clearable filterable placeholder="请选择渠道类型" :default-first-option="true" @change="handleSearch">
            <el-option v-for="item in channelTypeList" :key="item.ID" :label="item.Name" :value="item.ID"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" v-prevent-click @click="handleSearch">搜索</el-button>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" size="small" :loading="downloadLoading" @click="downloadExcel" v-if="isExport">导出</el-button>
        </el-form-item>
      </el-form>
    </div>
    <el-table :data="dataList" size="small" show-summary :summary-method="countMethod">
      <el-table-column prop="TreatBillID" label="订单编号" width="150px"></el-table-column>
      <el-table-column prop="EntityName" label="下单门店"></el-table-column>
      <el-table-column prop="BillDate" label="下单日期" width="130">
        <template slot-scope="scope">
          {{ scope.row.BillDate | dateFormat("YYYY-MM-DD HH:mm") }}
        </template>
      </el-table-column>
      <el-table-column prop="BillType" label="订单类型" width="80"></el-table-column>
      <el-table-column label="渠道信息">
        <el-table-column prop="ChannelName" label="渠道名称"> </el-table-column>
        <el-table-column prop="ChannelType" label="渠道类型"> </el-table-column>
      </el-table-column>

      <el-table-column label="客户信息">
        <el-table-column prop="CustomerName" label="客户名称"> </el-table-column>
        <el-table-column prop="PhoneNumber" label="客户手机号"> </el-table-column>
          <el-table-column prop="Code" label="客户编号"> </el-table-column>
      </el-table-column>
      <el-table-column label="卡项信息">
        <el-table-column prop="CardName" label="卡项名称"> </el-table-column>
        <el-table-column prop="TreatCardTypeName" label="卡项类型" width="80"></el-table-column>
      </el-table-column>
      <el-table-column label="消耗商品信息">
        <el-table-column prop="GoodName" label="商品名称"> </el-table-column>
        <el-table-column prop="CategoryName" label="商品分类"></el-table-column>
      </el-table-column>
      <el-table-column label="消耗业绩">
        <el-table-column align="right" prop="PayPerformance" label="现金业绩">
          <template slot-scope="scope">
            <div v-if="scope.row.PayPerformance < 0" class="color_red">{{ scope.row.PayPerformance | toFixed | NumFormat }}</div>
            <div v-else-if="scope.row.PayPerformance > 0" class="color_green">+{{ scope.row.PayPerformance | toFixed | NumFormat }}</div>
            <div v-else>0.00</div>
          </template>
        </el-table-column>
        <el-table-column align="right" prop="CardPerformance" label="卡抵扣业绩">
          <template slot-scope="scope">
            <div v-if="scope.row.CardPerformance < 0" class="color_red">{{ scope.row.CardPerformance | toFixed | NumFormat }}</div>
            <div v-else-if="scope.row.CardPerformance > 0" class="color_green">+{{ scope.row.CardPerformance | toFixed | NumFormat }}</div>
            <div v-else>0.00</div>
          </template>
        </el-table-column>
        <el-table-column align="right" prop="CardLargessPerformance" label="赠送卡抵扣业绩">
          <template slot-scope="scope">
            <div v-if="scope.row.CardLargessPerformance < 0" class="color_red">{{ scope.row.CardLargessPerformance | toFixed | NumFormat }}</div>
            <div v-else-if="scope.row.CardLargessPerformance > 0" class="color_green">+{{ scope.row.CardLargessPerformance | toFixed | NumFormat }}</div>
            <div v-else>0.00</div>
          </template>
        </el-table-column>
        <el-table-column align="right" prop="LargessPerformance" label="赠送业绩">
          <template slot-scope="scope">
            <div v-if="scope.row.LargessPerformance < 0" class="color_red">{{ scope.row.LargessPerformance | toFixed | NumFormat }}</div>
            <div v-else-if="scope.row.LargessPerformance > 0" class="color_green">+{{ scope.row.LargessPerformance | toFixed | NumFormat }}</div>
            <div v-else>0.00</div>
          </template>
        </el-table-column>
      </el-table-column>
    </el-table>
    <div class="pad_15 text_right">
      <el-pagination background v-if="paginations.total > 0" @current-change="handleCurrentChange" :current-page.sync="paginations.page" :page-size="paginations.page_size" :layout="paginations.layout" :total="paginations.total"></el-pagination>
    </div>
  </div>
</template>

<script>
import API from "@/api/Report/Channel/treatPerformanceDetail";
import channelTypeAPI from "@/api/CRM/Channel/channelType.js";
const dayjs = require("dayjs");
const isoWeek = require("dayjs/plugin/isoWeek");
dayjs.extend(isoWeek);
export default {
  name: "ChannelTreatPerformanceDetail",
  beforeRouteEnter(to, from, next) {
    next((vm) => {
      vm.isExport = vm.$permission.permission(to.meta.Permission, "Report-Channel-TreatPerformanceDetail-Export");
    });
  },
  props: {},
  /** 监听数据变化   */
  watch: {},
  /**  引入的组件  */
  components: {},
  /**  Vue 实例的数据对象**/
  data() {
    return {
      loading: false,
      downloadLoading: false,
      isExport: false,
      entityList: [], //门店数据
      search: {
        PageNum: 1,
        GoodsName: "",
        EntityID: "",
        TreatCardTypeName: "",
        ChannelName: "",
        QueryDate: [dayjs().format("YYYY-MM-DD"), dayjs().format("YYYY-MM-DD")],
        BillID: "",
        ChannelTypeID: "",
        CustomerName: "",
      },
      dataList: [], //表格数据
      dataListInfo: {},
      //需要给分页组件传的信息
      paginations: {
        page: 1, // 当前位于哪页
        total: 0, // 总数
        page_size: 10, // 1页显示多少条
        layout: "total, prev, pager, next,jumper", // 翻页属性
      },
      pickerOptions: {
        shortcuts: [
          {
            text: "今天",
            onClick: (picker) => {
              let end = new Date();
              let start = new Date();
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "本周",
            onClick: (picker) => {
              let end = new Date();
              let weekDay = dayjs(end).isoWeekday();
              let start = dayjs(end)
                .subtract(weekDay - 1, "day")
                .toDate();
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "本月",
            onClick: (picker) => {
              let end = new Date();
              let start = dayjs(dayjs(end).format("YYYY-MM") + "01").toDate();
              picker.$emit("pick", [start, end]);
            },
          },
        ],
      },
      channelTypeList: [],
    };
  },
  /**计算属性  */
  computed: {},
  /**  方法集合  */
  methods: {
    /**  合计  */
    countMethod({ columns }) {
      const sums = [];
      var filter_NumFormat = this.$options.filters["NumFormat"];
      var filter_toFixed = this.$options.filters["toFixed"];
      columns.forEach((column, index) => {
        switch (column.property) {
          case "TreatBillID":
            sums[index] = <span class="font_weight_600">合计</span>;
            break;
          case "PayPerformance":
            {
              let PayPerformance = this.dataListInfo ? this.dataListInfo.PayPerformance : 0;
              if (PayPerformance < 0) {
                sums[index] = <span class="color_red">{filter_NumFormat(filter_toFixed(PayPerformance))}</span>;
              } else if (PayPerformance > 0) {
                sums[index] = <span class="color_green">+{filter_NumFormat(filter_toFixed(PayPerformance))}</span>;
              } else {
                sums[index] = <span>{filter_NumFormat(filter_toFixed(PayPerformance))}</span>;
              }
            }
            break;
          case "CardPerformance":
            {
              let CardPerformance = this.dataListInfo ? this.dataListInfo.CardPerformance : 0;
              if (CardPerformance < 0) {
                sums[index] = <span class="color_red">{filter_NumFormat(filter_toFixed(CardPerformance))}</span>;
              } else if (CardPerformance > 0) {
                sums[index] = <span class="color_green">+{filter_NumFormat(filter_toFixed(CardPerformance))}</span>;
              } else {
                sums[index] = <span>{filter_NumFormat(filter_toFixed(CardPerformance))}</span>;
              }
            }
            break;
          case "CardLargessPerformance":
            {
              let CardLargessPerformance = this.dataListInfo ? this.dataListInfo.CardLargessPerformance : 0;
              if (CardLargessPerformance < 0) {
                sums[index] = <span class="color_red">{filter_NumFormat(filter_toFixed(CardLargessPerformance))}</span>;
              } else if (CardLargessPerformance > 0) {
                sums[index] = <span class="color_green">+{filter_NumFormat(filter_toFixed(CardLargessPerformance))}</span>;
              } else {
                sums[index] = <span>{filter_NumFormat(filter_toFixed(CardLargessPerformance))}</span>;
              }
            }
            break;
          case "LargessPerformance":
            {
              let LargessPerformance = this.dataListInfo ? this.dataListInfo.LargessPerformance : 0;
              if (LargessPerformance < 0) {
                sums[index] = <span class="color_red">{filter_NumFormat(filter_toFixed(LargessPerformance))}</span>;
              } else if (LargessPerformance > 0) {
                sums[index] = <span class="color_green">+{filter_NumFormat(filter_toFixed(LargessPerformance))}</span>;
              } else {
                sums[index] = <span>{filter_NumFormat(filter_toFixed(LargessPerformance))}</span>;
              }
            }
            break;

          default:
            sums[index] = "";
            break;
        }
      });
      return sums;
    },
    /* 搜索 */
    handleSearch() {
      this.paginations.page = 1;
      this.dataListTreat();
    },
    /* 分页 */
    handleCurrentChange(page) {
      let that = this;
      that.paginations.page = page;
      that.dataListTreat();
    },
    /** 数据导出 */
    downloadExcel() {
      var that = this;
      if (dayjs(that.search.QueryDate[0]).add(366, "day").valueOf() < dayjs(that.search.QueryDate[1]).valueOf()) {
        that.$message.error("时间筛选范围不能超366天");
        return;
      }
      let params = {
        EntityID: that.search.EntityID,
        StartDate: that.search.QueryDate[0],
        EndDate: that.search.QueryDate[1],
        TreatCardTypeName: that.search.TreatCardTypeName,
        ChannelName: that.search.ChannelName.trim(), //渠道搜索
        GoodsName: that.search.GoodsName.trim(), //商品名搜索
        BillID: that.search.BillID,
        ChannelTypeID: that.search.ChannelTypeID,
        CustomerName: that.search.CustomerName,
      };
      that.downloadLoading = true;
      API.exportDataDetailTreatApi(params)
        .then((res) => {
          this.$message.success({
            message: "正在导出",
            duration: "4000",
          });
          const link = document.createElement("a");
          let blob = new Blob([res], { type: "application/octet-stream" });
          link.style.display = "none";
          link.href = URL.createObjectURL(blob);
          link.download = "渠道消耗业绩明细.xlsx"; //下载的文件名
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
        })
        .finally(function () {
          that.downloadLoading = false;
        });
    },

    /**
     * 请求********
     */
    dataListTreat() {
      var that = this;

      if (dayjs(that.search.QueryDate[0]).add(366, "day").valueOf() < dayjs(that.search.QueryDate[1]).valueOf()) {
        that.$message.error("时间筛选范围不能超366天");
        return;
      }
      var params = {
        PageNum: that.paginations.page,
        EntityID: that.search.EntityID, //门店
        ChannelName: that.search.ChannelName, //渠道搜索
        GoodsName: that.search.GoodsName, //商品名搜索
        TreatCardTypeName: that.search.TreatCardTypeName, //商品类别搜索
        StartDate: that.search.QueryDate[0], //开始时间
        EndDate: that.search.QueryDate[1], //结束时间
        BillID: that.search.BillID,
        ChannelTypeID: that.search.ChannelTypeID,
        CustomerName: that.search.CustomerName,
      };
      that.loading = true;
      API.getdataListTreatApi(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.dataList = res.Data.channelTreatPerformanceDetailStatementFormBasePageInfo.List;
            that.dataListInfo = res.Data.channelTreatPerformanceSumStatementForm;
            that.paginations.total = res.Data.channelTreatPerformanceDetailStatementFormBasePageInfo.Total;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },
    /* 获取门店 */
    getAllEntity() {
      let that = this;
      let params = {};
      API.getAllEntityApi(params).then((res) => {
        if (res.StateCode == 200) {
          that.entityList = res.Data;
        } else {
          that.$message.error({
            message: res.Message,
            duration: 2000,
          });
        }
      });
    },
    /** 请求渠道类型列表   */
    async getChannelTypeList() {
      let that = this;
      let params = {
        Name: "",
        Active: true,
      };
      let res = await channelTypeAPI.getChannelTypeList(params);
      if (res.StateCode == 200) {
        that.channelTypeList = res.Data;
      } else {
        that.$message.error(res.Message);
      }
    },
  },
  /** 创建实例之前执行函数   */
  beforeCreate() {},
  /**  实例创建完成之后  */
  created() {},
  /**  在挂载开始之前被调用  */
  beforeMount() {},
  /**  实例被挂载后调用  */
  mounted() {
    this.isExport = this.$permission.permission(this.$route.meta.Permission, "Report-Channel-TreatPerformanceDetail-Export");
    this.getAllEntity();
    this.handleSearch();
    this.getChannelTypeList();
  },
  /**  数据更新 之前 调用  */
  beforeUpdate() {},
  /** 数据更新 完成 调用   */
  updated() {},
  /**  实例销毁之前调用  */
  beforeDestroy() {},
  /**  实例销毁后调用  */
  destroyed() {},
};
</script>

<style lang="scss">
.channelTreatPerformanceDetail {
}
</style>
