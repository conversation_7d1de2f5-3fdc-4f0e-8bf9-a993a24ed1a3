<template>
  <div class="developerSalePerformanceDetail content_body" v-loading="loading">
    <div class="nav_header">
      <el-form :inline="true" size="small" @keyup.enter.native="handleSearch">
        <el-form-item label="门店">
          <el-select placeholder="请选择门店" filterable clearable v-model="search.EntityID" @change="handleSearch" size="small">
            <el-option v-for="item in entityList" :key="item.ID" :label="item.Name" :value="item.ID"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="订单编号">
          <el-input size="small" v-model="search.BillID" @clear="handleSearch" clearable placeholder="输入订单编号搜索"></el-input>
        </el-form-item>
        <el-form-item label="时间筛选">
          <el-date-picker v-model="search.QueryDate" :picker-options="pickerOptions" unlink-panels type="daterange" range-separator="至" value-format="yyyy-MM-dd" start-placeholder="开始日期" end-placeholder="结束日期" @change="handleSearch" :clearable="false"></el-date-picker>
        </el-form-item>
        <el-form-item label="商品类型">
          <el-select v-model="search.GoodsTypeName" clearable filterable placeholder="请选择商品类型" :default-first-option="true" @change="handleSearch">
            <el-option label="项目" value="项目"></el-option>
            <el-option label="储值卡" value="储值卡"></el-option>
            <el-option label="时效卡" value="时效卡"></el-option>
            <el-option label="通用次卡" value="通用次卡"></el-option>
            <el-option label="产品" value="产品"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="商品名称">
          <el-input size="small" v-model="search.GoodsName" @clear="handleSearch" clearable placeholder="输入商品名称搜索"></el-input>
        </el-form-item>
        <el-form-item label="客户信息">
          <el-input size="small" v-model="search.CustomerName" @clear="handleSearch" clearable placeholder="请输入客户姓名或手机"></el-input>
        </el-form-item>
        <el-form-item label="渠道名称">
          <el-input size="small" v-model="search.ChannelName" @clear="handleSearch" clearable placeholder="输入渠道名称搜索"></el-input>
        </el-form-item>
        <el-form-item label="渠道类型">
          <el-select v-model="search.ChannelTypeID" clearable filterable placeholder="请选择渠道类型" :default-first-option="true" @change="handleSearch">
            <el-option v-for="item in channelTypeList" :key="item.ID" :label="item.Name" :value="item.ID"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="开发人员">
          <el-select v-model="search.EmployeeID" clearable filterable placeholder="请选择开发人员" :default-first-option="true" @change="handleSearch">
            <el-option v-for="item in employeeList" :key="item.ID" :label="item.Name" :value="item.ID"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" v-prevent-click @click="handleSearch">搜索</el-button>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" size="small" :loading="downloadLoading" @click="developerSalePerformanceDetailStatement_excel" v-if="isExport">导出</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div>
      <el-table :data="tableData" size="small" show-summary :summary-method="tableDataSummaryMethod">
        <el-table-column prop="SaleBillID" label="订单编号" width="150"></el-table-column>
        <el-table-column prop="EntityName" label="下单门店"></el-table-column>
        <el-table-column prop="BillDate" label="下单日期">
          <template slot-scope="scope">
            {{ scope.row.BillDate | dateFormat("YYYY-MM-DD HH:mm") }}
          </template>
        </el-table-column>
        <el-table-column prop="BillType" label="订单类型" width="80"></el-table-column>
        <el-table-column label="客户信息">
          <el-table-column prop="CustomerName" label="客户名称"> </el-table-column>
          <el-table-column prop="PhoneNumber" label="客户手机号"> </el-table-column>
          <el-table-column prop="Code" label="客户编号"> </el-table-column>
        </el-table-column>
        <el-table-column prop="EmployeeName" label="开发人员">
          <template slot-scope="scope">
            <div>{{ scope.row.EmployeeName }}</div>
            <!-- <div v-if="scope.row.EntityName" class="color_999 font_12">{{ scope.row.EntityName }}</div> -->
          </template>
        </el-table-column>
        <el-table-column label="渠道信息">
          <el-table-column prop="ChannelName" label="渠道名称"></el-table-column>
          <el-table-column prop="ChannelType" label="渠道类型"></el-table-column>
        </el-table-column>

        <el-table-column label="商品信息">
          <el-table-column prop="GoodName" label="商品名称"></el-table-column>
          <el-table-column prop="CategoryName" label="商品分类"></el-table-column>
          <el-table-column prop="GoodsTypeName" label="商品类型"></el-table-column>
        </el-table-column>
        <el-table-column label="销售业绩">
          <el-table-column align="right" prop="PayPerformance" label="现金业绩">
            <template slot-scope="scope">
              <div v-if="scope.row.PayPerformance < 0" class="color_red">{{ scope.row.PayPerformance | toFixed | NumFormat }}</div>
              <div v-else-if="scope.row.PayPerformance > 0" class="color_green">+{{ scope.row.PayPerformance | toFixed | NumFormat }}</div>
              <div v-else>0.00</div>
            </template>
          </el-table-column>
          <el-table-column align="right" prop="SavingCardPerformance" label="卡抵扣业绩">
            <template slot-scope="scope">
              <div v-if="scope.row.SavingCardPerformance < 0" class="color_red">{{ scope.row.SavingCardPerformance | toFixed | NumFormat }}</div>
              <div v-else-if="scope.row.SavingCardPerformance > 0" class="color_green">+{{ scope.row.SavingCardPerformance | toFixed | NumFormat }}</div>
              <div v-else>0.00</div>
            </template>
          </el-table-column>
          <el-table-column align="right" prop="SavingCardLargessPerformance" label="赠送卡抵扣业绩">
            <template slot-scope="scope">
              <div v-if="scope.row.SavingCardLargessPerformance < 0" class="color_red">{{ scope.row.SavingCardLargessPerformance | toFixed | NumFormat }}</div>
              <div v-else-if="scope.row.SavingCardLargessPerformance > 0" class="color_green">+{{ scope.row.SavingCardLargessPerformance | toFixed | NumFormat }}</div>
              <div v-else>0.00</div>
            </template>
          </el-table-column>
        </el-table-column>
      </el-table>
      <div class="pad_15 text_right">
        <el-pagination background v-if="paginations.total > 0" @current-change="handleCurrentChange" :current-page.sync="paginations.page" :page-size="paginations.page_size" :layout="paginations.layout" :total="paginations.total"></el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
import API from "@/api/Report/Channel/developerSalePerformanceDetail";
import channelTypeAPI from "@/api/CRM/Channel/channelType.js";
const dayjs = require("dayjs");
const isoWeek = require("dayjs/plugin/isoWeek");
dayjs.extend(isoWeek);
export default {
  name: "ChannelDeveloperSalePerformanceDetail",

  beforeRouteEnter(to, from, next) {
    next((vm) => {
      vm.isExport = vm.$permission.permission(to.meta.Permission, "Report-Channel-DeveloperSalePerformanceDetail-Export");
    });
  },
  props: {},
  /** 监听数据变化   */
  watch: {},
  /**  引入的组件  */
  components: {},
  /**  Vue 实例的数据对象**/
  data() {
    return {
      isExport: false,
      loading: false,
      downloadLoading: false,
      entityList: [], //门店数组
      employeeList: [],
      search: {
        GoodsName: "",
        EntityID: "",
        ChannelName: "",
        GoodsTypeName: "",
        EmployeeID: "", //开发人员
        QueryDate: [dayjs().format("YYYY-MM-DD"), dayjs().format("YYYY-MM-DD")],
        BillID: "",
        ChannelTypeID: "",
        CustomerName:"",
      },
      tableData: [],
      tableDataSummary: {
        PayPerformance: 0,
        SavingCardPerformance: 0,
        SavingCardLargessPerformance: 0,
      },
      paginations: {
        page: 1, // 当前位于哪页
        total: 0, // 总数
        page_size: 10, // 1页显示多少条
        layout: "total, prev, pager, next,jumper", // 翻页属性
      },
      pickerOptions: {
        shortcuts: [
          {
            text: "今天",
            onClick: (picker) => {
              let end = new Date();
              let start = new Date();
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "本周",
            onClick: (picker) => {
              let end = new Date();
              let weekDay = dayjs(end).isoWeekday();
              let start = dayjs(end)
                .subtract(weekDay - 1, "day")
                .toDate();
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "本月",
            onClick: (picker) => {
              let end = new Date();
              let start = dayjs(dayjs(end).format("YYYY-MM") + "01").toDate();
              picker.$emit("pick", [start, end]);
            },
          },
        ],
      },
      channelTypeList: [],
    };
  },
  /**计算属性  */
  computed: {},
  /**  方法集合  */
  methods: {
    /**  搜索  */
    handleSearch() {
      let that = this;
      that.paginations.page = 1;
      that.developerSalePerformanceDetailStatement_list();
    },
    /** 修改分页   */
    handleCurrentChange(page) {
      let that = this;
      that.paginations.page = page;
      that.developerSalePerformanceDetailStatement_list();
    },
    /**    */
    tableDataSummaryMethod({ columns }) {
      const sums = [];
      var filter_NumFormat = this.$options.filters["NumFormat"];
      var filter_toFixed = this.$options.filters["toFixed"];
      columns.forEach((column, index) => {
        switch (column.property) {
          case "SaleBillID":
            sums[index] = <span class="font_weight_600">合计</span>;
            break;
          case "PayPerformance":
            {
              let PayPerformance = this.tableDataSummary ? this.tableDataSummary.PayPerformance : 0;
              if (PayPerformance < 0) {
                sums[index] = <span class="color_red">-{filter_NumFormat(filter_toFixed(PayPerformance))}</span>;
              } else if (PayPerformance > 0) {
                sums[index] = <span class="color_green">+{filter_NumFormat(filter_toFixed(PayPerformance))}</span>;
              } else {
                sums[index] = <span>{filter_NumFormat(filter_toFixed(PayPerformance))}</span>;
              }
            }
            break;
          case "SavingCardPerformance":
            {
              let SavingCardPerformance = this.tableDataSummary ? this.tableDataSummary.SavingCardPerformance : 0;
              if (SavingCardPerformance < 0) {
                sums[index] = <span class="color_red">-{filter_NumFormat(filter_toFixed(SavingCardPerformance))}</span>;
              } else if (SavingCardPerformance > 0) {
                sums[index] = <span class="color_green">+{filter_NumFormat(filter_toFixed(SavingCardPerformance))}</span>;
              } else {
                sums[index] = <span>{filter_NumFormat(filter_toFixed(SavingCardPerformance))}</span>;
              }
            }
            break;
          case "SavingCardLargessPerformance":
            {
              let SavingCardLargessPerformance = this.tableDataSummary ? this.tableDataSummary.SavingCardLargessPerformance : 0;
              if (SavingCardLargessPerformance < 0) {
                sums[index] = <span class="color_red">-{filter_NumFormat(filter_toFixed(SavingCardLargessPerformance))}</span>;
              } else if (SavingCardLargessPerformance > 0) {
                sums[index] = <span class="color_green">+{filter_NumFormat(filter_toFixed(SavingCardLargessPerformance))}</span>;
              } else {
                sums[index] = <span>{filter_NumFormat(filter_toFixed(SavingCardLargessPerformance))}</span>;
              }
            }
            break;
          default:
            sums[index] = "";
            break;
        }
      });
      return sums;
    },
    /**  ***************  */
    /**    */
    async developerSalePerformanceDetailStatement_list() {
      let that = this;
      if (dayjs(that.search.QueryDate[0]).add(366, "day").valueOf() < dayjs(that.search.QueryDate[1]).valueOf()) {
        that.$message.error("时间筛选范围不能超366天");
        return;
      }
      that.loading = true;
      let params = {
        PageNum: that.paginations.page,
        EntityID: that.search.EntityID,
        ChannelName: that.search.ChannelName,
        GoodsName: that.search.GoodsName,
        GoodsTypeName: that.search.GoodsTypeName,
        EmployeeID: that.search.EmployeeID, //开发人员
        StartDate: that.search.QueryDate[0],
        EndDate: that.search.QueryDate[1],
        BillID: that.search.BillID,
        ChannelTypeID: that.search.ChannelTypeID,
        CustomerName:that.search.CustomerName,
      };
      let res = await API.developerSalePerformanceDetailStatement_list(params);
      if (res.StateCode == 200) {
        that.tableData = res.Data.developerSalePerformanceDetailStatementFormBasePageInfo.List;
        that.paginations.total = res.Data.developerSalePerformanceDetailStatementFormBasePageInfo.Total;
        that.tableDataSummary = res.Data.developerSalePerformanceSumStatementForm;
        that.loading = false;
      } else {
        that.$message.error(res.Message);
        that.loading = false;
      }
    },
    /**    */
    developerSalePerformanceDetailStatement_excel() {
      let that = this;
      if (dayjs(that.search.QueryDate[0]).add(366, "day").valueOf() < dayjs(that.search.QueryDate[1]).valueOf()) {
        that.$message.error("时间筛选范围不能超366天");
        return;
      }
      that.downloadLoading = true;
      let params = {
        PageNum: that.paginations.page,
        EntityID: that.search.EntityID,
        ChannelName: that.search.ChannelName,
        GoodsName: that.search.GoodsName,
        GoodsTypeName: that.search.GoodsTypeName,
        EmployeeID: that.search.EmployeeID, //开发人员
        StartDate: that.search.QueryDate[0],
        EndDate: that.search.QueryDate[1],
        BillID: that.search.BillID,
        ChannelTypeID: that.search.ChannelTypeID,
        CustomerName:that.search.CustomerName,
      };
      API.developerSalePerformanceDetailStatement_excel(params)
        .then((res) => {
          this.$message.success({
            message: "正在导出",
            duration: "4000",
          });
          const link = document.createElement("a");
          let blob = new Blob([res], { type: "application/octet-stream" });
          link.style.display = "none";
          link.href = URL.createObjectURL(blob);
          link.download = "开发销售业绩明细.xlsx"; //下载的文件名
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
          that.downloadLoading = false;
        })
        .catch((fail) => {
          that.$message.error(fail);
          that.downloadLoading = false;
        });
    },

    /* 获取门店 */
    getAllEntity() {
      let that = this;
      let params = {};
      API.getAllEntityApi(params).then((res) => {
        if (res.StateCode == 200) {
          that.entityList = res.Data;
        } else {
          that.$message.error({
            message: res.Message,
            duration: 2000,
          });
        }
      });
    },
    /* 获取业务代表 */
    geTemployeeAll() {
      let that = this;
      let params = {};
      API.geTemployeeAll(params).then((res) => {
        if (res.StateCode == 200) {
          that.employeeList = res.Data;
        }
      });
    },
    /** 请求渠道类型列表   */
    async getChannelTypeList() {
      let that = this;
      let params = {
        Name: "",
        Active: true,
      };
      let res = await channelTypeAPI.getChannelTypeList(params);
      if (res.StateCode == 200) {
        that.channelTypeList = res.Data;
      } else {
        that.$message.error(res.Message);
      }
    },
  },
  /** 创建实例之前执行函数   */
  beforeCreate() {},
  /**  实例创建完成之后  */
  created() {},
  /**  在挂载开始之前被调用  */
  beforeMount() {},
  /**  实例被挂载后调用  */
  mounted() {
    this.developerSalePerformanceDetailStatement_list();
    this.getAllEntity();
    this.geTemployeeAll();
    this.getChannelTypeList();
    this.isExport = this.$permission.permission(this.$route.meta.Permission, "Report-Channel-DeveloperSalePerformanceDetail-Export");
  },
  /**  数据更新 之前 调用  */
  beforeUpdate() {},
  /** 数据更新 完成 调用   */
  updated() {},
  /**  实例销毁之前调用  */
  beforeDestroy() {},
  /**  实例销毁后调用  */
  destroyed() {},
};
</script>

<style lang="scss">
.developerSalePerformanceDetail {
}
</style>
