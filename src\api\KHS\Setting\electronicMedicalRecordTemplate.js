/**
 * Created by preference on 2021/12/27
 *  zmx 
 */

import * as API from '@/api/index'
export default {
  /** 病历模板分类查询  */
  electronicMedicalRecordCategory_all: params => {
    return API.POST('api/electronicMedicalRecordCategory/all', params)
  },
  /** 病历模板分类创建  */
  electronicMedicalRecordCategory_create: params => {
    return API.POST('api/electronicMedicalRecordCategory/create', params)
  },
  /**  病历模板分类删除  */
  electronicMedicalRecordCategory_delete: params => {
    return API.POST('api/electronicMedicalRecordCategory/delete', params)
  },
  /** 病历分类排序  */
  electronicMedicalRecordCategory_move: params => {
    return API.POST('api/electronicMedicalRecordCategory/move', params)
  },
  /**  病历模板查询 */
  electronicMedicalRecordTemplate_list: params => {
    return API.POST('api/electronicMedicalRecordTemplate/list', params)
  },
  /**  病历模板创建  */
  electronicMedicalRecordTemplate_create: params => {
    return API.POST('api/electronicMedicalRecordTemplate/create', params)
  },
  /**  病历模板编辑 */
  electronicMedicalRecordTemplate_update: params => {
    return API.POST('api/electronicMedicalRecordTemplate/update', params)
  },
  /** 病历模板删除   */
  electronicMedicalRecordTemplate_delete: params => {
    return API.POST('api/electronicMedicalRecordTemplate/delete', params)
  },
  /**  病历模板排序  */
  electronicMedicalRecordTemplate_move: params => {
    return API.POST('api/electronicMedicalRecordTemplate/move', params)
  },
}