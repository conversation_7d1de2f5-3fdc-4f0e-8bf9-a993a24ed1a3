<template>
  <div class="dispatchPrice content_body" v-loading="loading">
    <!-- 搜索 -->
    <div class="nav_header">
      <el-row>
        <el-col :span="20">
          <el-form :inline="true" size="small" @keyup.enter.native="distributionPriceSearch">
            <el-form-item label="配销方案">
              <el-input v-model="name" size="small" @clear="distributionPriceSearch" placeholder="输入配销方案搜索"
                clearable></el-input>
            </el-form-item>

            <el-form-item v-if="allWarehouse.length > 1" label="仓库/门店">
              <el-select v-model="searchEntityID" :default-first-option="true" @change="distributionPriceSearch"
                @clear="distributionPriceSearch" clearable filterable placeholder="请选择仓库">
                <el-option v-for="item in allWarehouse" :key="item.ID" :label="item.Name" :value="item.ID"></el-option>
              </el-select>
            </el-form-item>

            <el-form-item label="有效性">
              <el-select size="small" @change="distributionPriceSearch" @clear="distributionPriceSearch"
                v-model="active" placeholder="请选择" clearable>
                <el-option label="有效" :value="true"></el-option>
                <el-option label="无效" :value="false"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button size="small" type="primary" @click="distributionPriceSearch" v-prevent-click>搜索</el-button>
            </el-form-item>
          </el-form>
        </el-col>
        <el-col :span="4" class="text_right">
          <el-button type="primary" @click="addDistributionPriceClick" size="small" v-prevent-click>新增</el-button>
        </el-col>
      </el-row>
    </div>
    <!-- 表格 -->
    <div>
      <el-table size="small" :data="tableData">
        <el-table-column prop="Name" label="配销方案"></el-table-column>
        <el-table-column prop="Active" label="有效性">
          <template slot-scope="scope">
            {{ scope.row.Active == true ? "有效" : "无效" }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="80">
          <template slot-scope="scope">
            <el-button type="primary" size="small" @click="showEditDialog(scope.row)">编辑</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <!-- 分页 -->
    <div class="pad_15 text_right">
      <el-pagination background v-if="distributionPricepaginations.total > 0"
        @current-change="handleDistributionPriceChange" :current-page.sync="distributionPricepaginations.page"
        :page-size="distributionPricepaginations.page_size" :layout="distributionPricepaginations.layout"
        :total="distributionPricepaginations.total"></el-pagination>
    </div>
    <!-- 新增配销价格名称弹出层 -->
    <el-dialog title="新增配销方案" :visible.sync="adddialogVisible" width="450px">
      <el-form :model="addruleForm" :rules="addrules" ref="addruleForm" label-width="auto" size="small"
        @submit.native.prevent>
        <el-form-item label="配销方案" prop="Name">
          <el-input clearable v-model="addruleForm.Name" placeholder="请输入配销方案"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="addDialogVisibleClose" v-prevent-click>取 消</el-button>
        <el-button type="primary" size="small" @click="addSubmit('addruleForm')" v-prevent-click>保 存</el-button>
      </div>
    </el-dialog>
    <!--编辑配销价格弹出层-->
    <el-dialog title="编辑配销价格" :visible.sync="dialogVisible" width="950px">
      <div class="edit_content">
        <el-tabs v-model="activeName">
          <el-tab-pane label="基础信息" name="first">
            <el-form :model="ruleForm" ref="ruleForm" :rules="rules" label-width="auto" size="small" :inline="true">
              <el-form-item label="配销方案" prop="Name">
                <el-input v-model="ruleForm.Name" clearable></el-input>
              </el-form-item>

              <el-form-item label="有效性" prop="Active">
                <el-radio-group v-model="ruleForm.Active">
                  <el-radio :label="true">有效</el-radio>
                  <el-radio :label="false">无效</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-form>
            <el-table class="custom_Input" size="small" :data="purchasePriceData" max-height="500px"
              :row-key="getRowKeys" :tree-props="{ children: 'Child' }">
              <el-table-column label="产品分类" prop="CategoryName"></el-table-column>
              <el-table-column label="定价方法" :render-header="renderHeader">
                {{ "销售价折扣法" }}
              </el-table-column>
              <el-table-column label="比率" prop="Discount" width="150px">
                <template slot-scope="scope">
                  <el-input type="number" size="small" v-input-fixed="2" v-model="scope.row.Discount"
                    @input="royaltyRateChange(scope.row)">
                    <template slot="append">%</template>
                  </el-input>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="120px">
                <template slot-scope="scope">
                  <el-button v-if="scope.row.ParentID != 0 && scope.$index != 0" type="primary" size="small"
                    @click="productSettingsClick(scope.row)"> 产品设置</el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-tab-pane>
          <el-tab-pane label="适用门店/仓库" name="second">
            <span slot="label">
              适用门店/仓库
            </span>


            <el-scrollbar class="add_el_scrollbar_height">
              <el-tree ref="treeEntity" :expand-on-click-node="false" :check-on-click-node="true" :check-strictly="true"
                :data="entityList" show-checkbox node-key="ID" :default-checked-keys="defaultEntityCheckedKeys"
                :default-expanded-keys="defaultEntityExpandedKeys" :props="defaultProps"
                :filter-node-method="filterNode">
                <span slot-scope="{ data }">
                  <div class="pad_5_0">
                    <span class="font_14">{{ data.EntityName }}</span>
                    <el-tag v-if="data.IsStore" class="marlt_5" size="mini">门店</el-tag>
                    <el-tag v-if="data.IsWarehouse" class="marlt_5" size="mini">仓库</el-tag>
                  </div>
                </span>
              </el-tree>
            </el-scrollbar>

          </el-tab-pane>
        </el-tabs>
        <el-input v-if="activeName == 'second'" placeholder="请输入" v-model="filterEntity" class="serch_input"
          size="small" clearable>
        </el-input>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="dialogVisibleClose">取 消</el-button>
        <el-button type="primary" size="small" @click="editSubmit('ruleForm')" v-prevent-click>保 存</el-button>
      </div>
    </el-dialog>
    <!-- 分类-子分类弹出层 -->
    <el-dialog title="分类--子分类" :visible.sync="classificationDialogVisible" width="950px"
      @close="closeclassificationDialogVisible">
      <el-row>
        <el-form :inline="true" size="small" label-width="auto">
          <!-- <el-col :span="18"> -->
          <el-form-item label="产品名称">
            <el-input size="small" v-model="classificationName" placeholder="请输入产品名称搜索" clearable></el-input>
          </el-form-item>
          <!-- @clear="classificationSearch" -->
          <!-- </el-col> -->
          <!-- <el-col :span="6"> -->
          <!-- <el-form-item> -->
          <!-- <el-button type="primary" size="small" @click="classificationSearch" v-prevent-click>搜索</el-button> -->
          <!-- </el-form-item> -->
          <!-- </el-col> -->
        </el-form>
      </el-row>
      <!-- || item.ProductID.toLowerCase().includes(classificationName.toLowerCase() -->
      <el-table :data="classifiedData.filter(
        (item) => !classificationName || item.ProductName.toLowerCase().includes(classificationName.toLowerCase())
      )
        " size="small" max-height="500px">
        <el-table-column prop="ProductName" label="产品名称"></el-table-column>
        <el-table-column prop="Price" label="销售价格">
          <template slot-scope="scope">￥{{ scope.row.Price | toFixed | NumFormat }} </template>
        </el-table-column>
        <el-table-column prop="DispatchPrice" label="配销价">
          <template slot-scope="scope">
            <el-input class="custom_Input" type="number" size="small" v-input-fixed="2"
              v-model="scope.row.DispatchPrice">
              <template slot="append">元</template>
            </el-input>
          </template>
        </el-table-column>
      </el-table>
      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="classificationDialogVisibleClose">取 消</el-button>
        <el-button type="primary" size="small" @click="classificationSubmit" v-prevent-click>保 存</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import API from "@/api/PSI/Price/dispatchPrice";
export default {
  name: "dispatchPrice",
  props: {},
  /**  引入的组件  */
  components: {},
  /**  Vue 实例的数据对象**/
  data () {
    return {
      filterEntity: "",
      loading: false,
      adddialogVisible: false,
      dialogVisible: false,
      classificationDialogVisible: false,
      schemeID: "",
      searchEntityID: "",
      categoryID: "",
      name: "", //搜索
      classificationName: "", //分类搜索
      active: true,
      activeName: "first",
      tableData: [], //表格数据
      purchasePriceData: [], //组织采购价数据
      scopeData: [], // 适用门店/仓库数据
      entityList: [], //门店/仓库
      Entity: [], //选中的门店/仓库
      defaultEntityCheckedKeys: [], // 选中的门店
      defaultEntityExpandedKeys: [1], // 默认展开
      searchEntityName: "",
      distributionData: [],
      classifiedData: [], //分类-子分类列表数据

      defaultProps: {
        children: "Child",
        label: "EntityName",
        disabled: function (data) {
          return !data.Status;
        },
      }, // 销售范围选择配置项
      distributionPricepaginations: {
        page: 1, // 当前位于哪页
        total: 0, // 总数
        page_size: 0, // 1页显示多少条
        layout: "total, prev, pager, next, jumper", // 翻页属性
      }, //需要给分页组件传的信息
      addruleForm: {
        Name: "",
      },
      ruleForm: {
        Active: true,
        Name: "",
      },
      addrules: {
        Name: [{ required: true, message: "请输入配销方案", trigger: "blur" }],
      },
      rules: {
        Name: [
          {
            required: true,
            message: "请填写组织采购价名称",
            trigger: "blur",
          },
        ],
        Active: [{ required: true, message: "请选择有效性", trigger: "change" }],
      },
      allWarehouse: [],
    };
  },
  watch: {
    filterEntity (val) {
      this.$refs.treeEntity.filter(val);
    }
  },
  /**计算属性  */
  computed: {},
  /**  方法集合  */
  methods: {
    filterNode (value, data) {
      if (!value) return true;
      return data.EntityName.indexOf(value) !== -1;
    },
    /* 搜索 */
    distributionPriceSearch () {
      let that = this;
      that.distributionPricepaginations.page = 1;
      that.getDispatchPriceSchemeList();
    },
    /* 分页 */
    handleDistributionPriceChange (page) {
      let that = this;
      that.distributionPricepaginations.page = page;
      that.getDispatchPriceSchemeList();
    },
    /* 分类搜索 */
    // classificationSearch() {
    // 	let that = this;
    // 	var ClassData = that.classifiedData;
    // 	ClassData = Enumerable.from(ClassData)
    // 		.where(function(i) {
    // 			return i.ProductName == that.classificationName;
    // 		})
    // 		.toArray();
    // 	that.classifiedData = ClassData;
    // 	if (that.classificationName == "") {
    // 		that.getDispatchPriceProductList();
    // 	}
    // },
    /**  关闭子分类商品弹窗  */
    closeclassificationDialogVisible () {
      let that = this;
      that.classificationName = "";
    },
    /* 新增 */
    addDistributionPriceClick () {
      let that = this;
      that.adddialogVisible = true;
    },
    /* 编辑 */
    showEditDialog: function (row) {
      let that = this;
      that.searchEntityName = "";
      that.dialogVisible = true;
      that.activeName = "first";
      that.ruleForm.Name = row.Name;
      that.schemeID = row.ID;

      that.defaultEntityCheckedKeys = [];
      that.defaultEntityExpandedKeys = [1];
      that.getEntity(row.ID);
      that.getEntityList(row.ID);
      that.getDispatchPriceCategoryList(row);
    },
    /* 新增弹出框取消 */
    addDialogVisibleClose () {
      let that = this;
      that.adddialogVisible = false;
      that.addruleForm.Name = "";
    },
    /**  新增保存品牌信息    */
    addSubmit (addruleForm) {
      let that = this;
      this.$refs[addruleForm].validate((valid) => {
        if (valid) {
          that.createDispatchPriceScheme();
        }
      });
    },
    /* 编辑弹出框取消 */
    dialogVisibleClose () {
      let that = this;
      that.dialogVisible = false;
    },
    /* 编辑配销价格保存 */
    editSubmit (formName) {
      let that = this;
      let selEntity = that.$refs.treeEntity.getCheckedKeys();
      let allItem = that.purchasePriceData[0];

      let params = {
        ID: allItem.CategoryID,
        Name: that.ruleForm.Name,
        Active: that.ruleForm.Active,
        Discount: allItem.Discount,
        Entity: selEntity,
      };
      let tempData = JSON.parse(JSON.stringify(that.purchasePriceData));
      params.Category = tempData
        .filter((i) => {
          i.Child =
            i.Child &&
            i.Child.filter((val) => {
              return val.Discount !== "" && val.Discount !== null;
            });
          return (i.Child && i.Child.length > 0) || (!i.isWhole && i.Discount !== "" && i.Discount !== null);
        })
        .map((val) => {
          return val;
        });



      this.$refs[formName].validate((valid) => {
        if (valid) {
          that.updateDispatchPriceCategory(params);
        }
      });
    },
    /* 分类取消 */
    classificationDialogVisibleClose () {
      let that = this;
      that.classificationDialogVisible = false;
    },
    /* 分类保存 */
    classificationSubmit () {
      let that = this;
      var Product = that.classifiedData
        .filter((i) => {
          return i.DispatchPrice !== "" && i.DispatchPrice !== null;
        })
        .map((val) => {
          return {
            ProductID: val.ProductID, //产品ID
            DispatchPrice: val.DispatchPrice, //组织采购价
          };
        });
      var classificationDataSaving = {
        ID: that.schemeID,
        CategoryID: that.categoryID,
        Product: Product,
      };
      that.updateDispatchPriceProduct(classificationDataSaving);
    },
    /* 产品设置 */
    productSettingsClick (row) {
      let that = this;
      that.categoryID = row.CategoryID;
      that.getDispatchPriceProductList();
      that.classificationDialogVisible = true;
    },
    /* 价格设置列表 */
    getDispatchPriceSchemeList: function () {
      let that = this;
      that.loading = true;
      let params = {
        PageNum: that.distributionPricepaginations.page,
        Name: that.name,
        Active: that.active,
        EntityID: that.searchEntityID,
      };
      API.getDispatchPriceSchemeList(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.tableData = res.List;
            that.distributionPricepaginations.total = res.Total;
            that.distributionPricepaginations.page_size = res.PageSize;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },
    /* 价格设置新增 */
    createDispatchPriceScheme: function () {
      let that = this;
      let params = {
        Name: that.addruleForm.Name,
      };
      API.createDispatchPriceScheme(params).then((res) => {
        if (res.StateCode == 200) {
          that.getDispatchPriceSchemeList();
          that.adddialogVisible = false;
          that.addruleForm.Name = "";
          that.$message.success({
            message: "新增成功",
            duration: 2000,
          });
        } else {
          that.$message.error({
            message: res.Message,
            duration: 2000,
          });
        }
      });
    },
    /* 价格设置-分类 */
    getDispatchPriceCategoryList: function (row) {
      let that = this;
      let params = {
        ID: row.ID,
      };
      API.getDispatchPriceCategoryList(params).then((res) => {
        if (res.StateCode == 200) {
          var data = {
            CategoryID: res.Data.ID,
            CategoryName: "所有产品",
            Discount: res.Data.Discount,
            isWhole: true,
          };
          that.purchasePriceData = res.Data.Category;
          that.purchasePriceData.unshift(data);
        }
      });
    },
    /* 价格设置-分类添加 */
    updateDispatchPriceCategory: function (content) {
      let that = this;
      let params = content;
      API.updateDispatchPriceCategory(params).then((res) => {
        if (res.StateCode == 200) {
          that.$message.success({
            message: "保存成功",
            duration: 2000,
          });
          that.dialogVisible = false;
          that.getDispatchPriceSchemeList();
        } else {
          that.$message.error({
            message: res.Message,
            duration: 2000,
          });
        }
      });
    },
    /* 价格设置--分类下的产品 */
    getDispatchPriceProductList: function () {
      let that = this;
      let params = {
        ID: that.schemeID,
        CategoryID: that.categoryID,
      };
      API.getDispatchPriceProductList(params).then((res) => {
        if (res.StateCode == 200) {
          that.classifiedData = res.Data;
        } else {
          that.$message.error({
            message: res.Message,
            duration: 2000,
          });
        }
      });
    },
    /* 价格设置--分类下产品添加  */
    updateDispatchPriceProduct: function (contents) {
      let that = this;
      let params = contents;
      API.updateDispatchPriceProduct(params).then((res) => {
        if (res.StateCode == 200) {
          that.$message.success({
            message: "保存成功",
            duration: 2000,
          });
          that.classificationDialogVisible = false;
        } else {
          that.$message.error({
            message: res.Message,
            duration: 2000,
          });
        }
      });
    },
    /* 门店/仓库--全部 */
    async getEntityList (ID) {
      let that = this;
      let params = {
        ID: ID,
      };
      let res = await API.getEntityList(params);
      if (res.StateCode == 200) {
        that.entityList = res.Data.map((val) => {
          val.Checked = false;
          return val;
        });
      } else {
        that.$message.error({
          message: res.Message,
          duration: 2000,
        });
      }
    },
    /* 适用门店/仓库 -- 选中 */
    async getEntity (ID) {
      let that = this;
      let params = {
        ID: ID,
      };
      let res = await API.getEntity(params);
      if (res.StateCode == 200) {
        that.defaultEntityCheckedKeys = res.Data;
        that.defaultEntityExpandedKeys = res.Data;
        if (that.defaultEntityExpandedKeys.length == 0) {
          that.defaultEntityExpandedKeys = [1];
        }
      } else {
        that.$message.error({
          message: res.Message,
          duration: 2000,
        });
      }
    },
    /* 定价方法提示 */
    renderHeader (h, { column }) {
      // h 是一个渲染函数       column 是一个对象表示当前列      $index 第几列
      return h("div", [
        h("span", column.label + "  ", {
          align: "center",
          marginTop: "10px",
        }),
        h(
          "el-popover",
          {
            props: {
              placement: "top-start", // 一般 icon 处可添加浮层说明，浮层位置等属性
              width: "auto",
              trigger: "hover",
            },
          },
          [
            h("p", "销售价折扣法：销售价格 x 比率%", {
              class: "text-align: center; margin: 0",
            }),
            h("i", {
              // 生成 i 标签 ，添加icon 设置 样式，slot 必填
              class: "el-icon-info",
              style: "color:#dcdfe6;",
              slot: "reference",
            }),
          ]
        ),
      ]);
    },
    /* 比率 */
    royaltyRateChange: function (row) {
      if (row.Discount > 100) {
        row.Discount = 100;
      }
    },
    /* row-key设置 */
    getRowKeys (row) {
      return row.CategoryID + row.CategoryName;
    },
    /**  获取门店筛选  */
    async getEntity_allWarehouse () {
      let that = this;
      let params = {};
      let res = await API.getEntity_allWarehouse(params);
      if (res.StateCode == 200) {
        that.allWarehouse = res.Data;
      } else {
        that.$message.error(res.Message);
      }
    },
  },
  /** 创建实例之前执行函数   */
  beforeCreate () { },
  /**  实例创建完成之后  */
  created () { },
  /**  在挂载开始之前被调用  */
  beforeMount () { },
  /**  实例被挂载后调用  */
  mounted () {
    let that = this;
    that.getDispatchPriceSchemeList();
    that.getEntity_allWarehouse();
  },
  /**  数据更新 之前 调用  */
  beforeUpdate () { },
  /** 数据更新 完成 调用   */
  updated () { },
  /**  实例销毁之前调用  */
  beforeDestroy () { },
  /**  实例销毁后调用  */
  destroyed () { },
};
</script>

<style lang="scss">
.dispatchPrice {
  .add_el_scrollbar_height {
    height: 50vh;

    .el-scrollbar__wrap {
      overflow-x: hidden;
    }
  }

  .el-input-group__append {
    padding: 0 10px;
  }

  .custom_Input {
    .el-input__inner {
      padding: 0 0 0 10px;
    }
  }

  .edit_content {
    position: relative;

    .serch_input {
      position: absolute;
      top: 5px;
      right: 10px;
      width: 200px;
    }
  }
}
</style>
