<template>
  <div class="ChannelSalaryChannelCommissionScheme content_body">
    <!-- 头部搜索 -->
    <div class="nav_header">
      <el-row>
        <el-col :span="20">
          <el-form :inline="true" size="small" @submit.native.prevent @keyup.enter.native="handleSearch">
            <el-form-item label="渠道业绩提成方案">
              <el-input v-model="searchData.Name" size="small" placeholder="输入渠道业绩提成方案搜索" clearable @clear="handleSearch"></el-input>
            </el-form-item>
            <el-form-item label="渠道业绩取值方案">
              <el-select v-model="searchData.PerformanceSchemeID" placeholder="请选择渠道业绩取值方案" filterable clearable size="small" @change="handleSearch">
                <el-option v-for="item in performanceSchemeData" :key="item.ID" :label="item.Name" :value="item.ID"> </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="有效性">
              <el-select v-model="searchData.Active" placeholder="选择有效性" clearable size="small" @change="handleSearch">
                <el-option label="有效" :value="true"></el-option>
                <el-option label="无效" :value="false"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" size="small" @click="handleSearch">搜 索</el-button>
            </el-form-item>
          </el-form>
        </el-col>
        <el-col :span="4" class="text_right">
          <el-button type="primary" size="small" @click="addProgramme" v-prevent-click>新 增</el-button>
        </el-col>
      </el-row>
    </div>
    <!-- 表格 -->
    <div>
      <el-table size="small" :data="tableData" v-loading="loading">
        <el-table-column prop="Name" label="渠道业绩提成方案"></el-table-column>
        <el-table-column prop="PerformanceSchemeName" label="渠道业绩取值方案"></el-table-column>
        <el-table-column prop="Calculation" label="计算方式">
          <template slot-scope="scope">
            <span>{{ scope.row.Calculation == 10 ? "阶梯式计算" : "阶段式计算" }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="Active" label="有效性">
          <template slot-scope="scope">
            <span>{{ scope.row.Active ? "有效" : "无效" }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="80px">
          <template slot-scope="scope">
            <el-button type="primary" size="small" @click="editProgramme(scope.row)" v-prevent-click>编 辑</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <!-- 分页 -->
    <div class="pad_15 text_right">
      <el-pagination
        background
        v-if="paginations.total > 0"
        @current-change="handleCurrentChange"
        :current-page.sync="paginations.page"
        :page-size="paginations.page_size"
        :layout="paginations.layout"
        :total="paginations.total"
      ></el-pagination>
    </div>
    <!-- 新增、编辑弹出层 -->
    <el-dialog width="1000px" :title="isAdd ? '新增渠道业绩提成方案' : '编辑渠道业绩提成方案'" :visible.sync="dialogVisible">
      <el-tabs v-model="activeName">
        <el-tab-pane label="方案设置" name="first">
          <el-scrollbar class="el-scrollbar_height">
            <el-form size="small" :rules="formRules" ref="formData" :model="formData" label-width="140px">
              <el-form-item label="渠道提成方案名称" prop="Name">
                <el-input v-model="formData.Name" placeholder="请输入渠道提成方案名称"></el-input>
              </el-form-item>
              <el-form-item label="渠道业绩取值方案" prop="PerformanceSchemeID">
                <el-select v-model="formData.PerformanceSchemeID" placeholder="请选择渠道业绩取值方案" filterable clearable size="small">
                  <el-option v-for="item in performanceSchemeData" :key="item.ID" :label="item.Name" :value="item.ID"> </el-option>
                </el-select>
              </el-form-item>
              <el-form-item v-if="!isAdd" label="有效性">
                <el-radio-group v-model="formData.Active">
                  <el-radio :label="true">有效</el-radio>
                  <el-radio :label="false">无效</el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="计算方式">
                <span slot="label">
                  计算方式
                  <el-popover placement="top-start" width="600px" trigger="hover">
                    <p>按阶梯式计算：设置后呈阶梯式增长，按总业绩计算提成</p>
                    <p>例：1-10000时6%，10000-15000时10%，员工业绩13000，提成为：13000*10%</p>
                    <p>按阶段式计算：设置后分阶段式计算，根据区间计算提成</p>
                    <p>例：1-10000时6%，10000-15000时10%，员工业绩13000，提成为：10000*6%+3000*10%</p>
                    <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                  </el-popover>
                </span>
                <el-radio v-model="formData.Calculation" label="10">阶梯式计算</el-radio>
                <el-radio v-model="formData.Calculation" label="20">阶段式计算</el-radio>
              </el-form-item>
              <el-form-item label="提成方案" prop="Commission" :rules="{ required: true, message: '请设置提成方案' }">
                <span slot="label">
                  提成方案
                  <el-popover placement="top-start" width="600px" trigger="hover">
                    <p>提成方案的区间值，最小值包含该区间内，最大值不包含在该区间内。</p>
                    <p>比如：区间设置为1000～2000，计算提成时，如业绩值为1000，则符合该区间规则；如业绩值为2000时，则不符合该区间规则。</p>
                    <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                  </el-popover>
                </span>
                <el-button type="primary" size="small" @click="addCommission">新增提成方案</el-button>
              </el-form-item>
            </el-form>
            <el-table size="small" :data="formData.Commission" style="width: calc(100% - 140px); margin-left: 140px">
              <el-table-column prop="BeginPerformance" label="开始业绩(大于等于)">
                <template slot-scope="scope">{{ scope.row.BeginPerformance | toFixed | NumFormat }}</template>
              </el-table-column>
              <el-table-column prop="EndPerformance" label="结束业绩(小于)">
                <template slot-scope="scope">{{ scope.row.EndPerformance | toFixed | NumFormat }}</template>
              </el-table-column>
              <el-table-column prop="Rate" label="比例提成">
                <template slot-scope="scope">{{ scope.row.Rate | toFixed }}%</template>
              </el-table-column>
              <el-table-column prop="Fixed" label="固定提成(元)">
                <template slot-scope="scope">￥{{ scope.row.Fixed | toFixed | NumFormat }}</template>
              </el-table-column>
              <el-table-column prop="address" label="操作" width="160px">
                <template slot-scope="scope">
                  <el-button type="primary" size="small" @click="editCommission(scope)"> 编 辑</el-button>
                  <el-button type="danger" size="small" @click="deleteCommission(scope.$index)">删 除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-scrollbar>
        </el-tab-pane>
        <!-- v-if="isAdd" -->
        <el-tab-pane  label="适用渠道" name="second">
          <el-scrollbar class="el-scrollbar_height">
            <el-tree
              :data="allChannelList"
              show-checkbox
              node-key="ID"
              :props="defaultProps"
              ref="tree"
              :expand-on-click-node="false"
              :check-on-click-node="true"
              :check-strictly="true"
              :default-expanded-keys="defaultExpandedKeys"
              :default-checked-keys="defaultCheckedKeys"
            >
            </el-tree>
          </el-scrollbar>
        </el-tab-pane>
      </el-tabs>
      <span slot="footer" class="dialog-footer">
        <el-button size="small" @click="dialogVisible = false" v-prevent-click>取 消</el-button>
        <el-button size="small" type="primary" @click="saveProgramme" v-prevent-click :loading="saveloading">保 存</el-button>
      </span>
    </el-dialog>
    <!-- 新增、编辑提成方案弹出层 -->
    <el-dialog width="600px" :title="isAddCommission ? '新增提成方案' : '编辑提成方案'" :visible.sync="commissionDialogVisible">
      <el-form size="small" :rules="commissionFormRules" ref="commissionFormData" :model="commissionFormData" label-width="110px">
        <el-form-item>
          <span slot="label"><span style="margin-right: 4px; color: #f67979">*</span><span>业绩范围</span></span>
          <el-col :span="8">
            <el-form-item label-width="0" style="margin-bottom: 0px !important" prop="BeginPerformance">
              <el-input
                v-model="commissionFormData.BeginPerformance"
                type="number"
                v-input-fixed="2"
                @blur="setCriteriaAmount('BeginPerformance', $event)"
                placeholder="请输入开始业绩"
              >
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="2" class="dis_flex flex_x_center">至</el-col>
          <el-col :span="8">
            <el-form-item label-width="0" style="margin-bottom: 0px !important" prop="EndPerformance">
              <el-input
                v-model="commissionFormData.EndPerformance"
                type="number"
                v-input-fixed="2"
                @blur="setCriteriaAmount('EndPerformance', $event)"
                placeholder="请输入截止业绩"
              >
              </el-input>
            </el-form-item>
          </el-col>
        </el-form-item>
        <el-form-item label="比例提成" prop="Rate">
          <el-input v-model="commissionFormData.Rate" @input="setCriteriaRate" v-input-fixed="2" type="number">
            <template slot="append">%</template>
          </el-input>
        </el-form-item>
        <el-form-item label="固定提成" prop="Fixed">
          <el-input v-model="commissionFormData.Fixed" v-input-fixed="2" type="number" @blur="setCriteriaAmount('Fixed', $event)">
            <template slot="append">元</template>
          </el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button size="small" @click="commissionDialogVisible = false" v-prevent-click>取 消</el-button>
        <el-button size="small" type="primary" @click="saveCommission" v-prevent-click>保 存</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import API from "@/api/KHS/ChannelSalary/channelCommissionScheme";
import channelInfoAPI from "@/api/CRM/Channel/channelInfo";

export default {
  name: "ChannelSalaryChannelCommissionScheme",
  props: {},
  /** 监听数据变化   */
  watch: {},
  /**  引入的组件  */
  components: {},
  /**  Vue 实例的数据对象**/
  data() {
    return {
      loading: false,
      saveloading: false,
      dialogVisible: false,
      commissionDialogVisible: false,
      isAdd: true,
      isAddCommission: true,
      activeName: "first",
      commissionEditIndex: "",
      ID: "", // 渠道业绩提成方案ID
      tableData: [], // 表格数据
      performanceSchemeData: [], // 业绩取值方案数据
      allChannelList: [], // 适用渠道
      defaultCheckedKeys: [],
      defaultExpandedKeys: [11],
      selectChannel: [], // 选中的适用渠道
      formData: {
        Active: true,
        Name: "",
        PerformanceSchemeID: "",
        Commission: [],
        Calculation: "10",
      }, // 新增、编辑数据
      commissionFormData: {
        BeginPerformance: "",
        EndPerformance: "",
        Rate: "",
        Fixed: "",
      }, // 新增、编辑提成方案数据
      searchData: {
        Name: null,
        PerformanceSchemeID: null,
        Active: true,
      }, // 搜索数据
      paginations: {
        page: 1, // 当前位于哪页
        total: 0, // 总数
        page_size: 10, // 1页显示多少条
        layout: "total, prev, pager, next, jumper", // 翻页属性
      }, // 分页属性
      formRules: {
        Name: [{ required: true, message: "请输入渠道提成方案名称", trigger: "blur" }],
        PerformanceSchemeID: [{ required: true, message: "请选择渠道业绩取值方案", trigger: "change" }],
      }, // 新增、编辑验证
      commissionFormRules: {
        BeginPerformance: [{ required: true, message: "请输入开始业绩", trigger: "blur" }],
        EndPerformance: [{ required: true, message: "请输入截止业绩", trigger: "blur" }],
        Rate: [{ required: true, message: "请输入比例提成", trigger: "blur" }],
        Fixed: [{ required: true, message: "请输入固定提成", trigger: "blur" }],
      }, // 新增、编辑提成方案验证
      defaultProps: {
        children: "Child",
        label: "Name",
      }, // 适用渠道展示处理
    };
  },
  /**计算属性  */
  computed: {},
  /**  方法集合  */
  methods: {
    /* 搜索 */
    handleSearch() {
      let that = this;
      that.paginations.page = 1;
      that.getChannelCommissionSchemeAll();
    },
    /* 分页 */
    handleCurrentChange(page) {
      let that = this;
      that.paginations.page = page;
      that.getChannelCommissionSchemeAll();
    },
    /* 获取渠道业绩提成方案列表 */
    getChannelCommissionSchemeAll() {
      let that = this;
      that.loading = true;
      let params = {
        PageNum: that.paginations.page,
        Name: that.searchData.Name, //模糊搜索
        PerformanceSchemeID: that.searchData.PerformanceSchemeID, //业绩取值方案编号
        Active: that.searchData.Active, //有效性
      };
      API.getChannelCommissionSchemeAll(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.tableData = res.List;
            that.paginations.total = res.Total;
            that.paginations.page_size = res.PageSize;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(() => {
          that.loading = false;
        });
    },
    /* 新增方案 */
    addProgramme() {
      let that = this;
      that.isAdd = true;
      that.dialogVisible = true;
      that.activeName = "first";
      that.formData = {
        Active: true,
        Name: "",
        PerformanceSchemeID: "",
        Commission: [],
        Calculation: "10",
      };
      if (this.$refs.formData) {
        this.$refs.formData.resetFields();
      }
      this.$nextTick(() => {
        that.$refs.tree?that.$refs.tree.setCheckedKeys([]):"";
      });
      that.getValidChannelPerformanceScheme();
    },
    /* 编辑方案 */
    editProgramme(row) {
      let that = this;
      that.activeName = "first";
      that.isAdd = false;
      if (this.$refs.formData) {
        this.$refs.formData.resetFields();
      }
      that.ID = row.ID;
      that.formData.Name = row.Name;
      that.formData.Active = row.Active;
      that.formData.Calculation = row.Calculation;
      that.formData.PerformanceSchemeID = row.PerformanceSchemeID;
      this.$nextTick(() => {
        that.$refs.tree?that.$refs.tree.setCheckedKeys([]):"";
      });
      that.getCommission(row.ID);
      that.getRange(row.ID);
      that.getValidChannelPerformanceScheme();
      that.dialogVisible = true;
    },
    /* 保存方案 */
    saveProgramme() {
      let that = this;
      that.selectChannel = this.$refs.tree ? this.$refs.tree.getCheckedKeys() : null;
      that.$refs.formData.validate((valid) => {
        if (valid) {
          that.saveloading = true;
          if (that.isAdd) {
            that.createChannelCommissionScheme();
          } else {
            that.updateChannelCommissionScheme();
          }
        }
      });
    },
    /* 新增方案保存 */
    createChannelCommissionScheme() {
      let that = this;
      let params = {
        Name: that.formData.Name,
        PerformanceSchemeID: that.formData.PerformanceSchemeID,
        Calculation: that.formData.Calculation,
        Commission: that.formData.Commission,
        JobType: that.selectChannel,
      };
      API.createChannelCommissionScheme(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.$message.success({
              message: "渠道业绩提成方案新增成功",
              duration: 2000,
            });
            that.dialogVisible = false;
            that.getChannelCommissionSchemeAll();
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(() => {
          that.saveloading = false;
        });
    },
    /* 修改方案保存 */
    updateChannelCommissionScheme() {
      let that = this;
      let params = {
        ID: that.ID,
        Name: that.formData.Name,
        PerformanceSchemeID: that.formData.PerformanceSchemeID,
        Calculation: that.formData.Calculation,
        Active: that.formData.Active,
        Commission: that.formData.Commission,
        JobType: that.selectChannel,
      };
      API.updateChannelCommissionScheme(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.$message.success({
              message: "渠道业绩提成方案修改成功",
              duration: 2000,
            });
            that.dialogVisible = false;
            that.getChannelCommissionSchemeAll();
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(() => {
          that.saveloading = false;
        });
    },
    /* 新增提成方案 */
    addCommission() {
      let that = this;
      that.isAddCommission = true;
      that.commissionDialogVisible = true;
      that.commissionFormData = {
        BeginPerformance: "",
        EndPerformance: "",
        Rate: "",
        Fixed: "",
      };
      if (this.$refs.commissionFormData) {
        that.$refs.commissionFormData.resetFields();
      }
    },
    /* 编辑提成方案 */
    editCommission(scope) {
      let that = this;
      that.isAddCommission = false;
      that.commissionDialogVisible = true;
      that.commissionEditIndex = scope.$index;
      that.commissionFormData = {
        BeginPerformance: scope.row.BeginPerformance,
        EndPerformance: scope.row.EndPerformance,
        Rate: scope.row.Rate,
        Fixed: scope.row.Fixed,
      };
      if (this.$refs.commissionFormData) {
        that.$refs.commissionFormData.resetFields();
      }
    },
    /* 保存提成方案 */
    saveCommission() {
      let that = this;
      that.$refs.commissionFormData.validate((valid) => {
        if (valid) {
          if (that.commissionFormData.BeginPerformance - that.commissionFormData.EndPerformance > 0) {
            that.$message.error({
              message: "截止业绩数额不能小于开始业绩数额",
              duration: 2000,
            });
            return;
          }
          // 判断是否有相同的提成方案
          let commission = JSON.parse(JSON.stringify(that.formData.Commission));
          if (!that.isAddCommission) {
            let index = that.commissionEditIndex;
            commission.splice(index, 1);
          }
          let num = commission.every((item) => {
            const num1 = Number(item.BeginPerformance);
            const num2 = Number(item.EndPerformance);
            const num3 = Number(that.commissionFormData.BeginPerformance);
            const num4 = Number(that.commissionFormData.EndPerformance);
            if (num3 >= num2) return true;
            if (num4 <= num1) return true;
            return false;
          });
          if (!num) {
            that.$message.error({
              message: "条件设置存在重复数额",
              duration: 2000,
            });
            return;
          }

          if (that.isAddCommission) {
            that.formData.Commission.push({ ...that.commissionFormData });
          } else {
            let index = that.commissionEditIndex;
            that.formData.Commission.splice(index, 1, { ...that.commissionFormData });
          }
          that.commissionDialogVisible = false;
        }
      });
    },
    /* 删除提成方案 */
    deleteCommission(index) {
      this.$confirm("此操作将删除该选项, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.formData.Commission.splice(index, 1);
          this.$message({
            type: "success",
            message: "删除成功!",
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    /* 提成详情-条件 */
    getCommission(id) {
      let that = this;
      let params = { ID: id };
      API.getCommission(params).then((res) => {
        if (res.StateCode == 200) {
          that.formData.Commission = res.Data;
        }
      });
    },
    /* 提成详情-渠道范围 */
    getRange(id) {
      let that = this;
      let params = { ID: id };
      API.getRange(params).then((res) => {
        if (res.StateCode == 200) {
          that.defaultCheckedKeys = res.Data;
          that.defaultExpandedKeys = res.Data;
        }
      });
    },
    /* 渠道业绩取值方案 */
    getValidChannelPerformanceScheme() {
      let that = this;
      let params = {};
      API.getValidChannelPerformanceScheme(params).then((res) => {
        if (res.StateCode == 200) {
          that.performanceSchemeData = res.Data;
        } else {
          that.$message.error(res.Message);
        }
      });
    },
    /* 适用渠道 */
    getAllchannel() {
      let that = this;
      let params = {};
      channelInfoAPI.getAllchannel(params).then((res) => {
        if (res.StateCode == 200) {
          that.allChannelList = res.Data;
        }
      });
    },
    /* 提成比例限制 */
    setCriteriaRate(e) {
      if (Number(e) > 100) {
        this.commissionFormData.Rate = 100;
      }
    },
    /* 金额格式 */
    setCriteriaAmount(type, e) {
      if (e.target.value.indexOf(".") != -1) {
        this.commissionFormData[type] = Number(e.target.value).toFixed(2) - 0;
      } else {
        this.commissionFormData[type] = Number(e.target.value);
      }
    },
  },
  /** 创建实例之前执行函数   */
  beforeCreate() {},
  /**  实例创建完成之后  */
  created() {},
  /**  在挂载开始之前被调用  */
  beforeMount() {},
  /**  实例被挂载后调用  */
  mounted() {
    let that = this;
    that.getAllchannel();
    that.getChannelCommissionSchemeAll();
    that.getValidChannelPerformanceScheme();
  },
  /**  数据更新 之前 调用  */
  beforeUpdate() {},
  /** 数据更新 完成 调用   */
  updated() {},
  /**  实例销毁之前调用  */
  beforeDestroy() {},
  /**  实例销毁后调用  */
  destroyed() {},
};
</script>

<style lang="scss">
.ChannelSalaryChannelCommissionScheme {
  .nav_header {
    .el-input {
      width: 190px !important;
    }
  }
  .el-scrollbar_height {
    height: 55vh;
    .el-scrollbar__wrap {
      overflow-x: hidden;
    }
  }
  .el-input__inner {
    padding: 0 0 0 15px;
  }
  .el-dialog .el-select .el-input {
    width: 190px !important;
  }
}
</style>
