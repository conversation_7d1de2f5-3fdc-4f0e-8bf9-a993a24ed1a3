<template>
  <div class="memberPrice content_body_nopadding" v-loading="loading">
    <el-tabs type="border-card" v-model="goodsType" @tab-click="tabsHandleClick">
      <el-tab-pane label="项目" name="project"></el-tab-pane>
      <el-tab-pane label="产品" name="product"></el-tab-pane>
      <!-- <el-tab-pane label="储值卡" name="savingCard"></el-tab-pane> -->
      <el-tab-pane label="时效卡" name="timeCard"></el-tab-pane>
      <el-tab-pane label="通用次卡" name="generalCard"></el-tab-pane>
      <el-tab-pane label="套餐卡" name="packageCard"></el-tab-pane>
    </el-tabs>
    <div class="tab-content">
      <div class="tip color_333 font_14">所有{{ getGoodTypeName() }}设置会员价</div>
      <el-table :data="memberPriceAll" size="small">
        <el-table-column label="类别" prop="Name" mini-width="100"> </el-table-column>
        <el-table-column label="优惠方式" mini-width="100">折扣</el-table-column>
        <el-table-column v-for="item in customerLevelList" :key="'projecct-all-' + item.ID" :label="item.Name" :prop="'all_' + item.ID" mini-width="150" :formatter="formatMemberPrice"></el-table-column>
        <el-table-column label="操作" prop="Name" width="80">
          <template slot-scope="scope">
            <el-button type="primary" @click="editMemberPrice(scope.row)" size="small">编辑</el-button>
          </template>
        </el-table-column>
      </el-table>
      <!-- 分类 -->
      <div class="tip color_333 font_14 dis_flex flex_y_center flex_x_between martp_10">
        <div class="dis_flex flex_y_center">
          <div>按{{ getGoodTypeName() }}分类设置会员价</div>
          <div class="marlt_10">
            <el-button type="text" size="mini" @click="selectProjectCategory()">选择分类</el-button>
          </div>
        </div>
        <div class="dis_flex flex_y_center">
          <el-input size="small" placeholder="请输入名称" v-model="searchCategoryName" class="marrt_15" clearable @change="searchCategoryList"></el-input>
          <el-button type="primary" size="small" @click="searchCategoryList()">搜索</el-button>
        </div>
      </div>
      <el-table :data="customerLevelCategory" size="small" @selection-change="handleCategoryMemberPriceSelectionChange" @row-click="handleCategoryMemberPriceRowClick" ref="category_member_price_table">
        <el-table-column type="selection" width="55"> </el-table-column>
        <el-table-column label="类别" :prop="getGoodsCategoryNameKey()" mini-width="100"> </el-table-column>
        <el-table-column label="优惠方式" mini-width="100">折扣</el-table-column>
        <el-table-column v-for="item in customerLevelList" :key="'projecct-category-' + item.ID" :label="item.Name" :prop="'category_' + item.ID" mini-width="150" :formatter="formatCategoryMemberPrice"></el-table-column>
        <el-table-column label="操作" prop="Name" width="145">
          <template slot-scope="scope">
            <el-button type="primary" @click="editCategoryMemberPrice(scope.row)" size="small">编辑</el-button>
            <el-button type="danger" @click="deleteCategoryMemberPrice(scope.row)" size="small">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="pad_15 dis_flex flex_x_between">
        <el-popover placement="top" width="70" trigger="click">
          <div class="text_center">
            <el-button type="primary" @click="batchEditCategoryMemberPriceClick" size="small">编辑</el-button>
            <el-button type="danger" @click="batchDeleteCategoryMemberPriceClick" size="small">删除</el-button>
          </div>

          <el-button slot="reference" type="primary" size="small" :disabled="selectionMemberPriceCategoryList.length === 0">批量设置</el-button>
        </el-popover>
        <el-pagination background v-if="paginations_category.total > 0" @current-change="handleCategoryCurrentChange" :current-page.sync="paginations_category.page" :page-size="paginations_category.page_size" :layout="paginations_category.layout" :total="paginations_category.total"></el-pagination>
      </div>
      <!-- 项目 -->
      <div class="tip color_333 font_14 dis_flex flex_y_center flex_x_between martp_10">
        <div class="dis_flex flex_y_center">
          <div>单个{{ getGoodTypeName() }}设置会员价</div>
          <div class="marlt_10">
            <el-button type="text" size="mini" @click="selectGoodsItem()">选择{{ getGoodTypeName() }}</el-button>
          </div>
        </div>
        <div class="dis_flex flex_y_center">
          <el-input size="small" placeholder="请输入名称" v-model="searchItemName" class="marrt_15" clearable @change="searchGoodsList"></el-input>
          <el-button type="primary" size="small" @click="searchGoodsList()">搜索</el-button>
        </div>
      </div>
      <el-table :data="goodsDiscountPriceList" size="small" @selection-change="handleGoodsMemberPriceSelectionChange" @row-click="handleGoodsMemberPriceRowClick" ref="goods_member_price_table">
        <el-table-column type="selection" width="55"> </el-table-column>
        <el-table-column :label="getGoodTypeName() + '名称'" :prop="getGoodsTypeNameKey()" mini-width="100"> </el-table-column>
        <el-table-column v-for="item in customerLevelList" :key="'projecct-category-' + item.ID" :label="item.Name" :prop="'item_' + item.ID" mini-width="150" :formatter="formatCategoryMemberPrice"></el-table-column>

        <el-table-column label="操作" algin-right prop="Name" width="145">
          <template slot-scope="scope">
            <el-button type="primary" @click="editGoodsMemberPrice(scope.row)" size="small">编辑</el-button>
            <el-button type="danger" @click="deleteGoodsMemberPrice(scope.row)" size="small">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="pad_15 dis_flex flex_x_between">
        <el-popover placement="top" width="70" trigger="click">
          <div class="text_center">
            <el-button type="primary" @click="batchEditGoodsMemberPriceClick" size="small">编辑</el-button>
            <el-button type="danger" @click="batchDeleteGoodsMemberPriceClick" size="small">删除</el-button>
          </div>

          <el-button slot="reference" type="primary" size="small" :disabled="selectionMemberPriceGoodsList.length === 0">批量设置</el-button>
        </el-popover>
        <el-pagination
          background
          v-if="paginations_goodsItem.total > 0"
          @current-change="handleGoodsCurrentChange"
          :current-page.sync="paginations_goodsItem.page"
          :page-size="paginations_goodsItem.page_size"
          :layout="paginations_goodsItem.layout"
          :total="paginations_goodsItem.total"
        ></el-pagination>
      </div>
    </div>

    <!-- 编辑单个商品分类会员价格 -->
    <el-dialog title="设置会员价" :visible.sync="dialog_visible" width="1100">
      <div class="padbm_10">{{ goodsName }}</div>
      <el-form ref="memberPriceRef" :model="memberPriceModel" :rules="memberPriceRule" label-width="0" size="small">
        <el-table :data="editMemberPriceItem" size="small">
          <el-table-column v-for="(item, index) in memberPriceModel.customerLevelList" :key="'member-' + memberPriceModel.keyID + item.ID" :label="item.Name" :prop="'' + item.ID" mini-width="150" >

            <template slot="header" slot-scope="scope">
              <el-checkbox v-model="item.checked">{{ item.Name }}</el-checkbox>
            </template>

            <template slot-scope="scope">
              <el-form-item label="" :rules="item.checked ? memberPriceRule.DiscountPrice : []" :prop="'customerLevelList.' + index + '.DiscountPrice'" :show-message="item.checked">
                <el-input v-model="item.DiscountPrice" size="small" v-input-fixed="1" :disabled="!item.checked" class="custom-input-number">
                  <template slot="append">折</template>
                </el-input>
              </el-form-item>
            </template>
          </el-table-column>
        </el-table>
      </el-form>

      <span slot="footer" class="dialog-footer">
        <el-button @click="dialog_visible = false" size="small" v-prevent-click>取 消</el-button>
        <el-button type="primary" size="small" v-prevent-click @click="saveMemberPrice" :loading="modalAllLoading">确 定</el-button>
      </span>
    </el-dialog>

    <!-- 选择分类 -->
    <el-dialog title="选择分类" :visible.sync="dialog_visible_category" width="600px" @opened="category_dialog_opened">
      <el-table :data="categoryList" row-key="ID" default-expand-all :tree-props="{ children: 'Child' }" height="50vh" ref="category_table_ref" @selection-change="handleCategorySelectionChange" @row-click="handleCategoryRowClick">
        <el-table-column type="selection" width="55" :selectable="category_selectable"> </el-table-column>
        <el-table-column prop="Name" label="分类名称"> </el-table-column>
      </el-table>

      <span slot="footer" class="dialog-footer">
        <el-button @click="dialog_visible_category = false" size="small" v-prevent-click>取 消</el-button>
        <el-button type="primary" size="small" v-prevent-click @click="saveCategoryMemberPrice" :loading="saveCategoryLoading">确 定</el-button>
      </span>
    </el-dialog>

    <!-- 选择商品 -->
    <el-dialog title="选择商品" :visible.sync="dialog_visible_goods" width="980px" @opened="goods_dialog_opened">
      <el-form size="small" :inline="true" @submit.native.prevent>
        <el-form-item>
          <el-input size="small" placeholder="请输入名称" v-model="searchGoodsName" class="marrt_15" clearable @change="searchSelectGoodsList"></el-input>
          
        </el-form-item>
        <el-form-item>
          <el-button type="primary" size="small" @click="searchSelectGoodsList()">搜索</el-button>
        </el-form-item>
      </el-form>
      <el-table :data="goodsList" height="50vh" row-key="ID" ref="goods_table_ref" @selection-change="handleGoodsSelectionChange" @row-click="handleGoodsRowClick" size="small">
        <el-table-column type="selection" width="55" :selectable="goods_selectable" :reserve-selection="true"> </el-table-column>
        <el-table-column prop="Name" :label="getGoodTypeName() + '名称'"> </el-table-column>
        <el-table-column prop="Alias" label="别名"> </el-table-column>
        <el-table-column :prop="getGoodsCategoryNameKey()" label="分类名称"> </el-table-column>
        <el-table-column prop="Price" label="单价">
          <template slot-scope="scope">
            {{ scope.row.Price | toFixed | NumFormat }}
          </template>
        </el-table-column>
      </el-table>
      <div class="text_right pad_15">
        <el-pagination background v-if="paginations_goods.total > 0" @current-change="handleGoodsListCurrentChange" :current-page.sync="paginations_goods.page" :page-size="paginations_goods.page_size" :layout="paginations_goods.layout" :total="paginations_goods.total"></el-pagination>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialog_visible_goods = false" size="small" v-prevent-click>取 消</el-button>
        <el-button type="primary" size="small" v-prevent-click @click="saveGoodsMemberPrice" :loading="modalSelectLoading">确 定</el-button>
      </span>
    </el-dialog>

    <!-- 编辑单个商品会员价格 -->
    <el-dialog title="设置会员价" :visible.sync="dialog_visible_member_goods" width="1100">
      <div v-if="editDiscountPrice == 'goods'" class="padbm_10">
        <span>商品名称：</span>
        <span>{{ goodsMemberPriceModel.goodsName }}</span>
        <el-tag type="primary" size="small" class="marlt_10">{{ getGoodTypeName() }}</el-tag>
      </div>
      <el-form ref="gooods_memberPriceRef" :model="goodsMemberPriceModel" :rules="memberPriceRule" label-width="0" size="small">
        <el-table :data="editMemberPriceItem" size="small">
          <el-table-column v-if="editDiscountPrice == 'goods'" label="价格" prop="Price" mini-width="150">
            <template slot-scope="scope">
              <div style="margin-bottom: 18px">{{ scope.row.Price | toFixed | NumFormat }}</div>
            </template>
          </el-table-column>
          <el-table-column v-for="(item, index) in goodsMemberPriceModel.customerLevelList" :key="'member-' + item.ID" :label="item.Name" :prop="'' + item.ID" mini-width="180">
            <template slot="header" slot-scope="scope">
              <el-checkbox v-model="item.checked">{{ item.Name }}</el-checkbox>
            </template>

            <template slot-scope="scope">
              <!-- item.checked ? memberPriceRule.DiscountPrice : [] -->
              <el-form-item label="" :rules="goodsValidator(item).DiscountPrice" :prop="'customerLevelList.' + index + '.DiscountPrice'" :show-message="item.checked">
                <el-input v-model="item.DiscountPrice" size="small" v-input-fixed="1" :disabled="!item.checked" class="custom-input-number el_input_select">
                  <template slot="append">
                    <el-select v-model="item.PriceType" :disabled="!item.checked">
                      <el-option label="折" :value="1">折</el-option>
                      <el-option label="元" :value="2">元</el-option>
                    </el-select>
                  </template>
                </el-input>
              </el-form-item>
            </template>
          </el-table-column>
        </el-table>
      </el-form>

      <span slot="footer" class="dialog-footer">
        <el-button @click="dialog_visible_member_goods = false" size="small" v-prevent-click>取 消</el-button>
        <el-button type="primary" size="small" v-prevent-click @click="saveGoodsMemberDiscountPriceClick" :loading="modalGoodsLoading">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import API from "@/api/CRM/Operation/memberPrice.js";
const dayjs = require("dayjs");
const DEFAULT_VALUE = "-";
// 全部折扣请求api
const getDiscountAllAPIMap = {
  project: "customerPriceProjectConfig_customerLevelDiscountAll",
  product: "customerPriceProductConfig_customerLevelDiscountAll",
  timeCard: "customerPriceTimeCardConfig_customerLevelDiscountAll",
  generalCard: "customerPriceGeneralCardConfig_customerLevelDiscountAll",
  packageCard: "customerPricePackageCardConfig_customerLevelDiscountAll",
};
// 保存全部折扣请求api
const saveDiscountAllAPIMap = {
  project: "customerPriceProjectConfig_createCustomerLevelDiscountConfig",
  product: "customerPriceProductConfig_createCustomerLevelDiscountConfig",
  timeCard: "customerPriceTimeCardConfig_createCustomerLevelDiscountConfig",
  generalCard: "customerPriceGeneralCardConfig_createCustomerLevelDiscountConfig",
  packageCard: "customerPricePackageCardConfig_createCustomerLevelDiscountConfig",
};
// 获取分类折扣
const getDiscountCategoryAPIMap = {
  project: "customerPriceProjectConfig_customerLevelCategoryDiscountAll",
  product: "customerPriceProductConfig_customerLevelCategoryDiscountAll",
  timeCard: "customerPriceTimeCardConfig_customerLevelCategoryDiscountAll",
  generalCard: "customerPriceGeneralCardConfig_customerLevelCategoryDiscountAll",
  packageCard: "customerPricePackageCardConfig_customerLevelCategoryDiscountAll",
};

// 获取分类列表
const getCategoryListAPIMap = {
  project: "customerPriceProjectConfig_category",
  product: "customerPriceProductConfig_category",
  timeCard: "customerPriceTimeCardConfig_category",
  packageCard: "customerPricePackageCardConfig_category",
  generalCard: "customerPriceGeneralCardConfig_category",
};

// 获取已保存分类列表
const getSelectedCategoryIDAPIList = {
  project: "customerPriceProjectConfig_selectedCategory",
  product: "customerPriceProductConfig_selectedCategory",
  timeCard: "customerPriceTimeCardConfig_selectedCategory",
  packageCard: "customerPricePackageCardConfig_selectedCategory",
  generalCard: "customerPriceGeneralCardConfig_selectedCategory",
};

// 保存分类
const saveCategoryIDAPIMap = {
  project: "customerPriceProjectConfig_createCustomerLevelCategoryDiscountConfig",
  product: "customerPriceProductConfig_createCustomerLevelCategoryDiscountConfig",
  timeCard: "customerPriceTimeCardConfig_createCustomerLevelCategoryDiscountConfig",
  packageCard: "customerPricePackageCardConfig_createCustomerLevelCategoryDiscountConfig",
  generalCard: "customerPriceGeneralCardConfig_createCustomerLevelCategoryDiscountConfig",
};

// 更新分类折扣
const updateDiscountCategoryAPIMap = {
  project: "customerPriceProjectConfig_updateCustomerLevelCategoryDiscountConfig",
  product: "customerPriceProductConfig_updateCustomerLevelCategoryDiscountConfig",
  timeCard: "customerPriceTimeCardConfig_updateCustomerLevelCategoryDiscountConfig",
  generalCard: "customerPriceGeneralCardConfig_updateCustomerLevelCategoryDiscountConfig",
  packageCard: "customerPricePackageCardConfig_updateCustomerLevelCategoryDiscountConfig",
};

// 更新分类折扣
const batchUpdateDiscountCategoryAPIMap = {
  project: "customerPriceProjectConfig_batchUpdateCustomerLevelCategoryDiscountConfig",
  product: "customerPriceProductConfig_batchUpdateCustomerLevelCategoryDiscountConfig",
  timeCard: "customerPriceTimeCardConfig_batchUpdateCustomerLevelCategoryDiscountConfig",
  generalCard: "customerPriceGeneralCardConfig_batchUpdateCustomerLevelCategoryDiscountConfig",
  packageCard: "customerPricePackageCardConfig_batchUpdateCustomerLevelCategoryDiscountConfig",
};

// 删除分类折扣
const deleteDiscountCategoryAPIMap = {
  project: "customerPriceProjectConfig_deleteCustomerLevelCategoryDiscountConfig",
  product: "customerPriceProductConfig_deleteCustomerLevelCategoryDiscountConfig",
  timeCard: "customerPriceTimeCardConfig_deleteCustomerLevelCategoryDiscountConfig",
  generalCard: "customerPriceGeneralCardConfig_deleteCustomerLevelCategoryDiscountConfig",
  packageCard: "customerPricePackageCardConfig_deleteCustomerLevelCategoryDiscountConfig",
};

// 批量删除分类折扣
const batchDeleteDiscountCategoryAPIMap = {
  project: "customerPriceProjectConfig_batchDeleteCustomerLevelCategoryDiscountConfig",
  product: "customerPriceProductConfig_batchDeleteCustomerLevelCategoryDiscountConfig",
  timeCard: "customerPriceTimeCardConfig_batchDeleteCustomerLevelCategoryDiscountConfig",
  generalCard: "customerPriceGeneralCardConfig_batchDeleteCustomerLevelCategoryDiscountConfig",
  packageCard: "customerPricePackageCardConfig_batchDeleteCustomerLevelCategoryDiscountConfig",
};

// 获取商品列表
const getGoodsListAPIMap = {
  project: "customerPriceProjectConfig_project",
  product: "customerPriceProductConfig_product",
  timeCard: "customerPriceTimeCardConfig_timeCard",
  generalCard: "customerPriceGeneralCardConfig_generalCard",
  packageCard: "customerPricePackageCardConfig_packageCard",
};

// 获取已选择商品列表
const getSelectGoodsListAPIMap = {
  project: "customerPriceProjectConfig_selectedProject",
  product: "customerPriceProductConfig_selectedProduct",
  timeCard: "customerPriceTimeCardConfig_selectedTimeCard",
  generalCard: "customerPriceGeneralCardConfig_selectedGeneralCard",
  packageCard: "customerPricePackageCardConfig_selectedPackageCard",
};

// 获取已选择商品列表
const getGoodsDiscountPriceAllAPIMap = {
  project: "customerPriceProjectConfig_customerLevelProjectDiscountPriceAll",
  product: "customerPriceProductConfig_customerLevelProductDiscountPriceAll",
  timeCard: "customerPriceTimeCardConfig_customerLevelTimeCardDiscountPriceAll",
  generalCard: "customerPriceGeneralCardConfig_customerLevelGeneralCardDiscountPriceAll",
  packageCard: "customerPricePackageCardConfig_customerLevelPackageCardDiscountPriceAll",
};

// 获取已选择商品列表
const saveGoodsDiscountPriceAllAPIMap = {
  project: "customerPriceProjectConfig_createCustomerLevelProjectDiscountPriceConfig",
  product: "customerPriceProductConfig_createCustomerLevelProductDiscountPriceConfig",
  timeCard: "customerPriceTimeCardConfig_createCustomerLevelTimeCardDiscountPriceConfig",
  generalCard: "customerPriceGeneralCardConfig_createCustomerLevelGeneralCardDiscountPriceConfig",
  packageCard: "customerPricePackageCardConfig_createCustomerLevelPackageCardDiscountPriceConfig",
};

// 删除已选择商品会员价配置
const deleteGoodsDiscountPriceAllAPIMap = {
  project: "customerPriceProjectConfig_deleteCustomerLevelProjectDiscountConfig",
  product: "customerPriceProductConfig_deleteCustomerLevelProductDiscountConfig",
  timeCard: "customerPriceTimeCardConfig_deleteCustomerLevelTimeCardDiscountConfig",
  generalCard: "customerPriceGeneralCardConfig_deleteCustomerLevelGeneralCardDiscountConfig",
  packageCard: "customerPricePackageCardConfig_deleteCustomerLevelPackageCardDiscountConfig",
};

// 批量删除已选择商品会员价配置
const batchDeleteGoodsDiscountPriceAllAPIMap = {
  project: "customerPriceProjectConfig_batchDeleteCustomerLevelProjectDiscountConfig",
  product: "customerPriceProductConfig_batchDeleteCustomerLevelProductDiscountConfig",
  timeCard: "customerPriceTimeCardConfig_batchDeleteCustomerLevelTimeCardDiscountConfig",
  generalCard: "customerPriceGeneralCardConfig_batchDeleteCustomerLevelGeneralCardDiscountConfig",
  packageCard: "customerPricePackageCardConfig_batchDeleteCustomerLevelPackageCardDiscountConfig",
};

// 更新已选择商品会员价配置
const updateGoodsDiscountPriceAllAPIMap = {
  project: "customerPriceProjectConfig_updateCustomerLevelProjectDiscountConfig",
  product: "customerPriceProductConfig_updateCustomerLevelProductDiscountConfig",
  timeCard: "customerPriceTimeCardConfig_updateCustomerLevelTimeCardDiscountConfig",
  generalCard: "customerPriceGeneralCardConfig_updateCustomerLevelGeneralCardDiscountConfig",
  packageCard: "customerPricePackageCardConfig_updateCustomerLevelPackageCardDiscountConfig",
};
// 更新已选择商品会员价配置
const batchUpdateGoodsDiscountPriceAllAPIMap = {
  project: "customerPriceProjectConfig_batchUpdateCustomerLevelProjectDiscountConfig",
  product: "customerPriceProductConfig_batchUpdateCustomerLevelProductDiscountConfig",
  timeCard: "customerPriceTimeCardConfig_batchUpdateCustomerLevelTimeCardDiscountConfig",
  generalCard: "customerPriceGeneralCardConfig_batchUpdateCustomerLevelGeneralCardDiscountConfig",
  packageCard: "customerPricePackageCardConfig_batchUpdateCustomerLevelPackageCardDiscountConfig",
};

const categoryIDKey = {
  project: "ProjectCategoryID",
  product: "ProductCategoryID",
  timeCard: "TimeCardCategoryID",
  packageCard: "PackageCardCategoryID",
  generalCard: "GeneralCardCategoryID",
};
const goodsIDKey = {
  project: "ProjectID",
  product: "ProductID",
  timeCard: "TimeCardID",
  packageCard: "PackageCardID",
  generalCard: "GeneralCardID",
};
const DiscountType = {
  ALL: "all",
  CATEGORY: "category",
  BATCH_CATEGORY: "batch_category",
  GOODS: "goods",
  BATCH_GOODS: "batch_goods",
};
const goodsTypeNameKey = {
  project: "ProjectName",
  product: "ProductName",
  timeCard: "TimeCardName",
  packageCard: "PackageCardName",
  generalCard: "GeneralCardName",
};
export default {
  name: "MemberPrice",

  props: {},
  /** 监听数据变化   */
  watch: {},
  /**  引入的组件  */
  components: {},
  /**  Vue 实例的数据对象**/
  data() {
    var validateDiscountPrice = (rule, value, callback) => {
      if (value === "" || value === null) {
        callback(new Error("请输入折扣"));
      } else {
        if (value > 10 || value < 0) {
          callback(new Error("折扣必须在0-10之间"));
        } else {
          callback();
        }
      }
    };
    return {
      modalGoodsLoading: false,
      modalSelectLoading: false,
      saveCategoryLoading: false,
      modalAllLoading: false,
      dialog_visible_member_goods: false,
      dialog_visible_goods: false,
      dialog_visible_category: false,
      modalLoading: false,
      dialog_visible: false,
      loading: false,
      goodsType: "project",
      customerLevelList: [],
      levelItemMap: {},
      goodsName: "",
      memberPriceRule: {
        DiscountPrice: [{ validator: validateDiscountPrice, required: true, trigger: "blur" }],
      },
      memberPriceModel: {
        customerLevelList: [],
      },
      memberPriceAll: [
        {
          Name: "所有项目",
        },
      ],
      customerLevelAll: [],
      customerLevelCategory: [],
      categoryList: [],

      customerLevel_project: [],

      paginations_category: {
        page: 1, // 当前位于哪页
        total: 0, // 总数
        page_size: 10, // 1页显示多少条
        layout: "total, prev, pager, next,jumper", // 翻页属性
      },
      editDiscountPrice: "all",
      searchCategoryName: "",
      paginations_goodsItem: {
        page: 1, // 当前位于哪页
        total: 0, // 总数
        page_size: 10, // 1页显示多少条
        layout: "total, prev, pager, next,jumper", // 翻页属性
      },

      paginations_goods: {
        page: 1, // 当前位于哪页
        total: 0, // 总数
        page_size: 10, // 1页显示多少条
        layout: "total, prev, pager, next,jumper", // 翻页属性
      },
      searchItemName: "",
      editMemberPriceItem: [],
      projectCategoryList: [],
      selectionCategoryList: [],
      selectionMemberPriceCategoryList: [],

      goodsList: [],
      goodsSelectIDList: [],
      goodsDiscountPriceList: [],
      selectionGoodsList: [],
      selectionMemberPriceGoodsList: [],

      goodsMemberPriceModel: {
        customerLevelList: [],
      },
      searchGoodsName: "",
      selectedCategoryIDList:[],
    };
  },
  /**计算属性  */
  computed: {},
  /**  方法集合  */
  methods: {
    /**    */
    getGoodsTypeNameKey() {
      return goodsTypeNameKey[this.goodsType];
    },
    /* 返回商品类型名称  */
    getGoodTypeName() {
      const typeName = {
        project: "项目",
        product: "产品",
        packageCard: "套餐",
        timeCard: "时效卡",
        generalCard: "通用卡",
      };
      return typeName[this.goodsType];
    },
    /* 返回商品类型名称  */
    getGoodsCategoryNameKey() {
      const typeName = {
        project: "ProjectCategoryName",
        product: "ProductCategoryName",
        packageCard: "PackageCardCategoryName",
        timeCard: "TimeCardCategoryName",
        generalCard: "GeneralCardCategoryName",
      };
      return typeName[this.goodsType];
    },
    /**   设置分类是否可选 */
    goods_selectable(row) {
      if (!this.goodsSelectIDList || this.goodsSelectIDList.length == 0) {
        return true;
      }
      let index = this.goodsSelectIDList.findIndex((i) => i == row.ID);
      if (index != -1) {
        return false;
      }
      return true;
    },
    /**  搜索商品列表  */
    searchSelectGoodsList() {
      this.paginations_goods.page = 1;
      this.getGoodsListData();
    },
    /**  商品列表翻页  */
    handleGoodsListCurrentChange(page) {
      this.paginations_goods.page = page;
      this.getGoodsListData();
    },
    /** 商品折扣列表翻页   */
    handleGoodsCurrentChange(page) {
      this.paginations_goodsItem.page = page;
      this.getGoodsDiscountPriceAllListData();
    },
    /**  搜索商品列表  */
    searchGoodsList() {
      this.paginations_goodsItem.page = 1;
      this.getGoodsDiscountPriceAllListData();
    },
    /**  分类折扣列表翻页  */
    handleCategoryCurrentChange(page) {
      this.paginations_category.page = page;
      this.getCustomerLevelCategoryDiscountAll();
    },
    /**  分类搜索  */
    searchCategoryList() {
      this.paginations_category.page = 1;
      this.getCustomerLevelCategoryDiscountAll();
    },
    /**  单个商品验证规则  */
    goodsValidator(item) {
      return {
        DiscountPrice: [
          {
            validator: (rule, value, callback) => {
              if (!item.checked) {
                callback();
              }
              if (value === "" || value === null) {
                if (item.PriceType == 1) {
                  callback(new Error("请输入折扣"));
                } else {
                  callback(new Error("请输入会员价"));
                }
              } else {
                if (item.PriceType == 1 && (value > 10 || value < 0)) {
                  callback(new Error("折扣必须在0-10之间"));
                } else {
                  callback();
                }
              }
            },
            required: true,
            trigger: "blur",
          },
        ],
      };
    },
    /**    */
    formatterPriceType(row) {
      if (row.PriceType == 1) {
        return "折扣";
      }
      return "价格";
    },
    /**  批量删除会员价  */
    batchDeleteGoodsMemberPriceClick() {
      this.$confirm("此操作将删除该会员价, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.batchDeleteCustomerLevelProjectDiscountConfig();
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    /**   删除单个商品 */
    deleteGoodsMemberPrice(row) {
      this.$confirm("此操作将删除该商品, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.deleteCustomerLevelProjectDiscountConfig(row);
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    /**  批量编辑会员价  */
    batchEditGoodsMemberPriceClick() {
      this.dialog_visible_member_goods = true;
      this.goodsMemberPriceModel = {
        goodsName: "",
        goodsID: "",
        customerLevelList: this.customerLevelList.map((item) => {
          item.PriceType = 1;
          item.DiscountPrice = "";
          item.checked = true;
          return Object.assign({}, item);
        }),
      };
      this.editDiscountPrice = "batch_goods";
      this.editMemberPriceItem = [{ ...this.levelItemMap, Price: "" }];
    },
    /**  商品保存会员价  */
    saveGoodsMemberDiscountPriceClick() {
      this.$refs.gooods_memberPriceRef.validate((valid) => {
        if (valid) {
          this.getOperationByDiscountType(this.editDiscountPrice)();
        }
      });
    },
    /**  编辑单个商品  */
    editGoodsMemberPrice(row) {
      this.dialog_visible_member_goods = true;
      this.goodsMemberPriceModel = {
        goodsName: row[goodsTypeNameKey[this.goodsType]],
        goodsID: row[goodsIDKey[this.goodsType]],
        customerLevelList: this.customerLevelList.map((item) => {
          let temp = row.CustomerLevelDiscount.find((i) => i.CustomerLevelID == item.ID);
          if (temp) {
            item.PriceType = temp.PriceType ? temp.PriceType : 1;
            item.DiscountPrice = temp.DiscountPrice;
          } else {
            item.PriceType = 1;
            item.DiscountPrice = "";
          }
          item.checked = true;
          return Object.assign({}, item);
        }),
      };
      this.editDiscountPrice = "goods";
      this.editMemberPriceItem = [{ ...this.levelItemMap, Price: row.Price }];
    },
    /**   保存商品选择 */
    saveGoodsMemberPrice() {
      this.createCustomerLevelProductDiscountPriceConfig();
    },
    /**  点击商品  */
    handleGoodsRowClick(row) {
      this.$refs.goods_table_ref.toggleRowSelection(row);
    },
    /**  商品选择  */
    handleGoodsSelectionChange(selection) {
      this.selectionGoodsList = selection;
    },
    /**  点击弹出选择商品界面 动画结束 */
    goods_dialog_opened() {
      this.changeGoodsRowSelection();
    },
    /**  弹出商品选界面  */
    selectGoodsItem() {
      this.dialog_visible_goods = true;
      this.paginations_goods.page = 1;
      this.getGoodsListData();
    },
    /* 设置商品选中行 */
    changeGoodsRowSelection() {
      const selectedIDs = new Set(this.goodsSelectIDList);
      if (this.$refs.goods_table_ref) {
        this.goodsList.forEach((item) => {
          if (item.ID && selectedIDs.has(item.ID)) {
            this.$nextTick(() => {
              this.$refs.goods_table_ref.toggleRowSelection(item, true);
            });
          }
          else{
            this.$refs.goods_table_ref.toggleRowSelection(item, false);
          }
        });
      }
    },
    /**  点击商品折扣列  */
    handleGoodsMemberPriceRowClick(row) {
      this.$refs.goods_member_price_table.toggleRowSelection(row);
    },
    /**  选择商品折扣列  */
    handleGoodsMemberPriceSelectionChange(selection) {
      this.selectionMemberPriceGoodsList = selection;
    },

    /**  批量删除  */
    batchDeleteCategoryMemberPriceClick() {
      if (this.selectionMemberPriceCategoryList.length == 0) {
        return;
      }
      this.$confirm("此操作将批量删除分类, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.batchDeleteCustomerLevelCategoryDiscountConfig();
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    /**  批量设置分类  */
    batchEditCategoryMemberPriceClick() {
      this.dialog_visible = true;
      this.$nextTick(() => {
        this.memberPriceModel = {
        keyID: "keyID_" + dayjs().valueOf(),
          customerLevelList: this.customerLevelList.map((item) => {
            item.DiscountPrice = "";
            item.checked = true;
            return Object.assign({}, item);
          }),
        };

        this.editDiscountPrice = "batch_category";
        this.editMemberPriceItem = [this.levelItemMap];
        this.goodsName = this.getGoodTypeName() + "-分类批量设置会员价-";
      });
    },
    /**  点击分类折扣 批量操作  */
    handleCategoryMemberPriceRowClick(row) {
      this.$refs.category_member_price_table.toggleRowSelection(row);
    },
    /**  选择分类折扣 批量操作   */
    handleCategoryMemberPriceSelectionChange(selection) {
      this.selectionMemberPriceCategoryList = selection;
    },
    /**  删除分类折扣  */
    deleteCategoryMemberPrice(row) {
      this.$confirm("此操作将删除该分类折扣, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.deleteCustomerLevelCategoryDiscountConfig(row);
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    /**   编辑分类会员价 */
    editCategoryMemberPrice(row) {
      this.dialog_visible = true;
      this.$nextTick(() => {
        this.memberPriceModel = {

          keyID: "keyID_" + dayjs().valueOf(),
          CategoryID: row[categoryIDKey[this.goodsType]],
          customerLevelList: this.customerLevelList.map((item) => {
            let temp = row.CustomerLevelDiscount.find((i) => i.CustomerLevelID == item.ID);
            item.DiscountPrice = temp ? temp.DiscountPrice : "";
            item.checked = true;
            return item;
          }),
        };
        this.editDiscountPrice = "category";
        this.editMemberPriceItem = [this.levelItemMap];
        this.goodsName = this.getGoodTypeName() + "-分类-【" + row[this.getGoodsCategoryNameKey()] + "】";

      });
    },
    /**  保存商品分类 */
    saveCategoryMemberPrice() {
      this.createCustomerLevelCategoryDiscountConfig();
    },
    /**  分类选中一行  */
    handleCategoryRowClick(row) {
      this.$refs.category_table_ref.toggleRowSelection(row);
    },
    /**  table 多选事件  */
    handleCategorySelectionChange(val) {
      this.selectionCategoryList = val;
    },
    /**   设置分类是否可选 */
    category_selectable(row) {
      if (!this.selectedCategoryIDList || this.selectedCategoryIDList.length == 0) {
        return true;
      }
      let index = this.selectedCategoryIDList.findIndex((i) => i == row.ID);
      if (index != -1) {
        return false;
      }
      return true;
    },
    /** 分类格式化会员价格   */
    formatCategoryMemberPrice(row, column) {
      // 确保 CustomerLevelDiscount 不是 undefined 或空数组
      if (!row.CustomerLevelDiscount || row.CustomerLevelDiscount.length === 0) {
        return DEFAULT_VALUE;
      }
      const propValue = this.getPropertyValue(column.property, row);
      if (!propValue) return DEFAULT_VALUE;
      for (let discount of row.CustomerLevelDiscount) {
        if (propValue == discount.CustomerLevelID && this.levelItemMap[discount.CustomerLevelID]) {
          if (!discount.DiscountPrice) {
            return DEFAULT_VALUE;
          }
          if (discount.PriceType == 2) {
            var filter_NumFormat = this.$options.filters["NumFormat"];
            return filter_NumFormat(parseFloat(discount.DiscountPrice).toFixed(2)) + "元"; // 找到匹配项，返回折扣价格
          }
          return discount.DiscountPrice + "折"; // 找到匹配项，返回折扣价格
        }
      }
      return DEFAULT_VALUE;
    },
    // 根据属性名获取属性值的函数
    getPropertyValue(property, row) {
      if (!property || !row) return undefined;
      const properties = property.split("_");
      if (properties.length < 2) return undefined;
      return properties[1];
    },
    /**  选择商品分类  */
    selectProjectCategory() {
      this.dialog_visible_category = true;
      this.setSelectedCategory();
    },
    /**    */
    category_dialog_opened() {
      this.setSelectedCategory();
    },
    /**  设置选定的分类选中项。 */
    setSelectedCategory() {
      const selectedIDs = new Set(this.selectedCategoryIDList);
      function traverseTree(node, ref) {
        // 如果节点ID存在于选定ID集合中，则选中该节点
        if (node.ID && selectedIDs.has(node.ID)) {
          ref.toggleRowSelection(node, true);
        }
        else{
          ref.toggleRowSelection(node, false);
        }
        // 如果节点有子节点，则对子节点递归调用traverseTree
        if (Array.isArray(node.Child)) {
          node.Child.forEach((child) => {
            traverseTree(child, ref);
          });
        }
      }
      if (this.$refs.category_table_ref) {
        this.categoryList.forEach((rootNode) => {
          traverseTree(rootNode, this.$refs.category_table_ref);
        });
      }
    },
    /**  全部品项格式化会员价格  */
    formatMemberPrice(row, column) {
      let that = this;
      if (!row) {
        return DEFAULT_VALUE;
      }
      // 确保数据已经加载
      if (!this.customerLevelAll || this.customerLevelAll.length === 0) {
        return DEFAULT_VALUE;
      }
      const propValue = this.getPropertyValue(column.property, row);
      if (!propValue) return DEFAULT_VALUE;
      let index = that.customerLevelAll.findIndex((i) => i.CustomerLevelID == propValue);
      if (index == -1) {
        return DEFAULT_VALUE;
      }

      let item = that.customerLevelAll[index];
      return item.DiscountPrice;
    },
    /**  编辑全部  */
    editMemberPrice() {
      this.dialog_visible = true;
      this.memberPriceModel = {
        keyID: "keyID_" + dayjs().valueOf(),
        customerLevelList: this.customerLevelList.map((item) => {
          let temp = this.customerLevelAll.find((i) => i.CustomerLevelID == item.ID);
          item.DiscountPrice = temp ? temp.DiscountPrice : "";
          item.checked = true;
          return Object.assign({}, item);
        }),
      };
      this.editDiscountPrice = "all";
      this.editMemberPriceItem = [this.levelItemMap];
      this.goodsName = "所有" + this.getGoodTypeName();
    },
    /**  保存全部商品折扣  */
    saveMemberPrice() {
      this.$refs.memberPriceRef.validate((valid) => {
        if (valid) {
          this.getOperationByDiscountType(this.editDiscountPrice)();
        }
      });
    },
    // 获取操作方法
    getOperationByDiscountType(discountType) {
      const operations = {
        [DiscountType.ALL]: this.createCustomerLevelDiscountConfig,
        [DiscountType.CATEGORY]: this.updateCustomerLevelCategoryDiscountConfig,
        [DiscountType.BATCH_CATEGORY]: this.batchUpdateCustomerLevelCategoryDiscountConfig,
        [DiscountType.GOODS]: this.updateCustomerLevelProjectDiscountConfig,
        [DiscountType.BATCH_GOODS]: this.batchUpdateCustomerLevelProjectDiscountConfig,
      };
      return operations[discountType];
    },

    /**  tabs 切换   */
    tabsHandleClick() {
      this.memberPriceAll = [
        {
          Name: this.getGoodTypeName(),
        },
      ];
      this.getCustomerLevelDiscountAll();
      this.getCustomerLevelCategoryDiscountAll();
      this.customerPriceConfig_category();

      this.getGoodsDiscountPriceAllListData();
      this.getGoodsListData();
      this.getGoodsSelectIDListData();
    },
    /* •••••••••••••••••••••••••••••••••••••••••••••••••••••••• */
    /**  获取全部  */
    /* 获取全部客户等级折扣 */
    getCustomerLevelDiscountAll() {
      let that = this;
      let params = {};
      API[getDiscountAllAPIMap[this.goodsType]](params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.customerLevelAll = res.Data;
          } else {
            that.$message.error(res.Message);
          }
        })
        .catch((fail) => {
          that.$message.error(fail);
        });
    },
    /** 保存全部 */
    createCustomerLevelDiscountConfig() {
      let that = this;
      // let temp = this.memberPriceModel.customerLevelList.filter(i=>i.checked);
      // if (temp.length == 0) {
      //   this.$message.error("请选择会员等级");
      //   return;
      // }

      let params = this.memberPriceModel.customerLevelList.reduce((acc, item) => {
        // if (item.checked) {
          acc.push({
            CustomerLevelID: item.ID,
            DiscountPrice:item.checked? item.DiscountPrice:null,
          });
        // }
        return acc;
      }, []);
      this.modalAllLoading = true;
      API[saveDiscountAllAPIMap[this.goodsType]](params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.dialog_visible = false;
            that.$message.success("添加成功");
            that.getCustomerLevelDiscountAll();
            this.modalAllLoading = false;
          } else {
            this.modalAllLoading = false;
            that.$message.error(res.Message);
          }
        })
        .catch((fail) => {
          this.modalAllLoading = false;
          that.$message.error(fail);
        });
    },
    // 分类
    /**  获取分类折扣  */
    getCustomerLevelCategoryDiscountAll() {
      let params = {
        PageNum: this.paginations_category.page, //分页
        Name: this.searchCategoryName, //分类名称搜索
      };
      API[getDiscountCategoryAPIMap[this.goodsType]](params)
        .then((res) => {
          if (res.StateCode == 200) {
            this.customerLevelCategory = res.List;
            this.paginations_category.total = res.Total;
            this.paginations_category.page_size = res.PageSize;
          } else {
            this.$message.error(res.Message);
          }
        })
        .catch((fail) => {
          this.$message.error(fail);
        });
    },
    /** 获取 分类  */
    customerPriceConfig_category() {
      let that = this;
      let params = {};
      API[getCategoryListAPIMap[that.goodsType]](params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.categoryList = res.Data;
            that.customerPriceProjectConfig_selectedCategory();
          } else {
            that.$message.error(res.Message);
          }
        })
        .catch((fail) => {
          that.$message.error(fail);
        });
    },
    /**  获取已经保存的分类ID   */
    customerPriceProjectConfig_selectedCategory() {
      let that = this;
      let params = {};
      API[getSelectedCategoryIDAPIList[this.goodsType]](params)
        .then((res) => {
          if (res.StateCode == 200) {
            this.selectedCategoryIDList = res.Data;
          } else {
            that.$message.error(res.Message);
          }
        })
        .catch((fail) => {
          that.$message.error(fail);
        });
    },
    // 获取 selectionCategoryList 和 selectedCategoryIDList 的差集
    getDifference(selectionCategoryList, selectedCategoryIDList) {
      const difference = [];
      selectionCategoryList.forEach((item) => {
        if (selectedCategoryIDList.indexOf(item.ID) === -1) {
          difference.push(item);
        }
      });
      return difference;
    },
    /**  保存分类  */
    createCustomerLevelCategoryDiscountConfig() {
      let difference = this.getDifference(this.selectionCategoryList, this.selectedCategoryIDList);
      if (!difference || difference.length === 0) {
        this.$message.error("请选择分类");
        return;
      }
      let params = difference.reduce((pre, cur) => {
        let temp = this.customerLevelList.map((i) => {
          let item = {
            CustomerLevelID: i.ID, //等级编号
          };
          item[categoryIDKey[this.goodsType]] = cur.ID;
          return item;
        });
        return [...pre, ...temp];
      }, []);
      this.saveCategoryLoading = true;
      API[saveCategoryIDAPIMap[this.goodsType]](params)
        .then((res) => {
          if (res.StateCode == 200) {
            this.$message.success("添加成功");
            this.getCustomerLevelCategoryDiscountAll();
            this.customerPriceProjectConfig_selectedCategory();
            this.$refs.category_table_ref.clearSelection();
            this.dialog_visible_category = false;
            this.saveCategoryLoading = false;
          } else {
            this.saveCategoryLoading = false;
            this.$message.error(res.Message);
          }
        })
        .catch((fail) => {
          this.saveCategoryLoading = false;
          this.$message.error(fail);
        });
    },
    /**   更新客户等级项目分类折扣配置 */
    updateCustomerLevelCategoryDiscountConfig() {
      let that = this;
      // let temp = this.memberPriceModel.customerLevelList.filter(i=> i.checked );
      // if (temp.length == 0) {
      //   this.$message.error("请选择客户等级");
      //   return;
      // }
      let params = {
        CustomerLevelDiscountPriceList: this.memberPriceModel.customerLevelList.reduce((acc, item) => {
          // if (item.checked) {
            acc.push({
              CustomerLevelID: item.ID,
              DiscountPrice: item.checked? item.DiscountPrice:null,
            });
          // }
          return acc;
        }, []),
      };
      params[categoryIDKey[this.goodsType]] = this.memberPriceModel.CategoryID;
      API[updateDiscountCategoryAPIMap[this.goodsType]](params)
        .then((res) => {
          if (res.StateCode == 200) {
            this.$message.success("保存成功");
            this.dialog_visible = false;
            this.getCustomerLevelCategoryDiscountAll();
          } else {
            that.$message.error(res.Message);
          }
        })
        .catch((fail) => {
          that.$message.error(fail);
        });
    },
    /** 批量更新分类   */
    batchUpdateCustomerLevelCategoryDiscountConfig() {
      let that = this;

      let temp = this.memberPriceModel.customerLevelList.filter(i=> i.checked );
      // if (temp.length == 0) {
      //   this.$message.error("请选择客户等级");
      //   return;
      // }
      let params = this.selectionMemberPriceCategoryList.map((item) => {
        let temp = {
          CustomerLevelDiscountPriceList: this.memberPriceModel.customerLevelList.reduce((acc, item) => {
            // if (item.checked) {
              acc.push({
                CustomerLevelID: item.ID,
                DiscountPrice: item.checked? item.DiscountPrice:null,
              });
            // }
            return acc;
          }, []),
        };
        temp[categoryIDKey[this.goodsType]] = item[categoryIDKey[this.goodsType]];
        return temp;
      });
      API[batchUpdateDiscountCategoryAPIMap[this.goodsType]](params)
        .then((res) => {
          if (res.StateCode == 200) {
            this.$message.success("保存成功");
            this.dialog_visible = false;
            this.getCustomerLevelCategoryDiscountAll();
          } else {
            that.$message.error(res.Message);
          }
        })
        .catch((fail) => {
          that.$message.error(fail);
        });
    },
    /**  删除分类折扣  */
    deleteCustomerLevelCategoryDiscountConfig(row) {
      let that = this;
      let params = {};
      params[categoryIDKey[this.goodsType]] = row[categoryIDKey[this.goodsType]];
      API[deleteDiscountCategoryAPIMap[this.goodsType]](params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.$message.success("删除成功");
            this.getCustomerLevelCategoryDiscountAll();
            this.customerPriceProjectConfig_selectedCategory();
          } else {
            that.$message.error(res.Message);
          }
        })
        .catch((fail) => {
          that.$message.error(fail);
        });
    },
    /**  批量删除请求  */
    batchDeleteCustomerLevelCategoryDiscountConfig() {
      let that = this;
      let params = this.selectionMemberPriceCategoryList.map((item) => {
        let temp = {};
        temp[categoryIDKey[this.goodsType]] = item[categoryIDKey[this.goodsType]];
        return temp;
      });
      API[batchDeleteDiscountCategoryAPIMap[this.goodsType]](params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.$message.success("删除成功");
            that.getCustomerLevelCategoryDiscountAll();
            that.customerPriceProjectConfig_selectedCategory();
          } else {
            that.$message.error(res.Message);
          }
        })
        .catch((fail) => {
          that.$message.error(fail);
        });
    },

    /*  单个商品  •••••••••••••••••••••••••••••• */
    /**  获取商品列表  */
    getGoodsListData() {
      let that = this;
      let params = {
        PageNum: this.paginations_goods.page, //分页
        Name: this.searchGoodsName, //名称搜索
      };
      API[getGoodsListAPIMap[this.goodsType]](params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.goodsList = res.List;
            that.paginations_goods.total = res.Total;
            that.paginations_goods.page_size = res.PageSize;
            this.changeGoodsRowSelection();
          } else {
            that.$message.error(res.Message);
          }
        })
        .catch((fail) => {
          that.$message.error(fail);
        });
    },
    /** 获取已选择商品ID   */
    getGoodsSelectIDListData() {
      let that = this;
      let params = {};
      API[getSelectGoodsListAPIMap[this.goodsType]](params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.goodsSelectIDList = res.Data;
          } else {
            that.$message.error(res.Message);
          }
        })
        .catch((fail) => {
          that.$message.error(fail);
        });
    },

    /**   获取单个商品折扣列表 */
    getGoodsDiscountPriceAllListData() {
      let that = this;
      let params = {
        PageNum: this.paginations_goodsItem.page, //分页
        Name: this.searchItemName, //名称搜索
      };
      API[getGoodsDiscountPriceAllAPIMap[this.goodsType]](params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.goodsDiscountPriceList = res.List;
            this.paginations_goodsItem.total = res.Total;
            this.paginations_goodsItem.page_size = res.PageSize;
            this.getGoodsSelectIDListData();
          } else {
            that.$message.error(res.Message);
          }
        })
        .catch((fail) => {
          that.$message.error(fail);
        });
    },
    // 获取 goodsList 和 goodsSelectIDList 的差集
    getGoodsDifference(goodsList, goodsIDList) {
      const difference = [];
      goodsList.forEach((item) => {
        if (goodsIDList.indexOf(item.ID) === -1) {
          difference.push(item);
        }
      });
      return difference;
    },
    /**  保存商品  */
    createCustomerLevelProductDiscountPriceConfig() {
      let difference = this.getGoodsDifference(this.selectionGoodsList, this.goodsSelectIDList);
      let params = difference.reduce((pre, cur) => {
        let temp = this.customerLevelList.map((i) => {
          let item = {
            CustomerLevelID: i.ID, //等级编号
          };
          item[goodsIDKey[this.goodsType]] = cur.ID;
          return item;
        });
        return [...pre, ...temp];
      }, []);
      this.modalSelectLoading = true;
      API[saveGoodsDiscountPriceAllAPIMap[this.goodsType]](params)
        .then((res) => {
          if (res.StateCode == 200) {
            this.$message.success("保存成功");
            this.getGoodsDiscountPriceAllListData();
            this.dialog_visible_goods = false;
            this.modalSelectLoading = false;
          } else {
            this.modalSelectLoading = false;
            this.$message.error(res.Message);
          }
        })
        .catch((fail) => {
          this.modalSelectLoading = false;
          this.$message.error(fail);
        });
    },
    /**  删除商品会员价  */
    deleteCustomerLevelProjectDiscountConfig(row) {
      let params = {};
      params[goodsIDKey[this.goodsType]] = row[goodsIDKey[this.goodsType]];
      API[deleteGoodsDiscountPriceAllAPIMap[this.goodsType]](params)
        .then((res) => {
          if (res.StateCode == 200) {
            this.$message.success("删除成功");
            this.getGoodsDiscountPriceAllListData();
          } else {
            this.$message.error(res.Message);
          }
        })
        .catch((fail) => {
          this.$message.error(fail);
        });
    },

    /**  批量删除商品会员价  */
    batchDeleteCustomerLevelProjectDiscountConfig() {
      let params = this.selectionMemberPriceGoodsList.map((item) => {
        let temp = {};
        temp[goodsIDKey[this.goodsType]] = item[goodsIDKey[this.goodsType]];
        return temp;
      });
      API[batchDeleteGoodsDiscountPriceAllAPIMap[this.goodsType]](params)
        .then((res) => {
          if (res.StateCode == 200) {
            this.$message.success("删除成功");
            this.getGoodsDiscountPriceAllListData();
          } else {
            this.$message.error(res.Message);
          }
        })
        .catch((fail) => {
          this.$message.error(fail);
        });
    },

    /**  单个更新商品折扣  */
    updateCustomerLevelProjectDiscountConfig() {
      // let temp = this.goodsMemberPriceModel.customerLevelList.filter(i=> i.checked );
      // if (temp.length == 0) {
      //   this.$message.error("请选择客户等级");
      //   return;
      // }
      let params = {
        CustomerLevelDiscountPriceList: this.goodsMemberPriceModel.customerLevelList.reduce((acc, item) => {
          // if (item.checked) {
            acc.push({
              CustomerLevelID: item.ID,
              DiscountPrice: item.checked? item.DiscountPrice:null,
              PriceType: item.PriceType,
            });
          // }
          return acc;
        }, []),
      };
      params[goodsIDKey[this.goodsType]] = this.goodsMemberPriceModel.goodsID;
      this.modalGoodsLoading = true;
      API[updateGoodsDiscountPriceAllAPIMap[this.goodsType]](params)
        .then((res) => {
          if (res.StateCode == 200) {
            this.$message.success("保存成功");
            this.getGoodsDiscountPriceAllListData();
            this.dialog_visible_member_goods = false;
            this.modalGoodsLoading = false;
          } else {
            this.modalGoodsLoading = false;
            this.$message.error(res.Message);
          }
        })
        .catch((fail) => {
          this.modalGoodsLoading = false;
          this.$message.error(fail);
        });
    },

    /**  批量更新商品折扣  */
    batchUpdateCustomerLevelProjectDiscountConfig() {
      let that = this;

      // let temp = this.goodsMemberPriceModel.customerLevelList.filter(i=> i.checked );
      // if (temp.length == 0) {
      //   this.$message.error("请选择客户等级");
      //   return;
      // }
      let params = this.selectionMemberPriceGoodsList.map((item) => {
        let temp = {
          CustomerLevelDiscountPriceList: this.goodsMemberPriceModel.customerLevelList.reduce((acc, item) => {
            // if (item.checked) {
              acc.push({
                CustomerLevelID: item.ID,
                DiscountPrice: item.checked? item.DiscountPrice:null,
                PriceType: item.PriceType,
              });
            // }
            return acc;
          }, []),
        };
        temp[goodsIDKey[this.goodsType]] = item[goodsIDKey[this.goodsType]];
        return temp;
      });
      // params[goodsIDKey[this.goodsType]] = this.goodsMemberPriceModel.goodsID;
      API[batchUpdateGoodsDiscountPriceAllAPIMap[this.goodsType]](params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.$message.success("保存成功");
            that.getGoodsDiscountPriceAllListData();
            that.dialog_visible_member_goods = false;
          } else {
            that.$message.error(res.Message);
          }
        })
        .catch((fail) => {
          that.$message.error(fail);
        });
    },

    /**  等级列表  */
    customerLevel_all() {
      let that = this;
      let params = {
        Name: "",
        Active: true, //有效性
      };
      API.customerLevel_all(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.customerLevelList = res.Data.map((item) => {
              item.DiscountPrice = "";
              item.checked = true;
              return item;
            });
            that.levelItemMap = that.customerLevelList.reduce((map, item) => {
              map[item.ID] = item;
              return map;
            }, {});
          } else {
            that.$message.error(res.Message);
          }
        })
        .catch((fail) => {
          that.$message.error(fail);
        });
    },
  },
  /** 创建实例之前执行函数   */
  beforeCreate() {},
  /**  实例创建完成之后  */
  created() {},
  /**  在挂载开始之前被调用  */
  beforeMount() {},
  /**  实例被挂载后调用  */
  mounted() {
    this.customerLevel_all();

    this.getCustomerLevelDiscountAll();
    this.getCustomerLevelCategoryDiscountAll();
    this.getGoodsDiscountPriceAllListData();

    this.customerPriceConfig_category();
    this.getGoodsListData();
  },
  /**  数据更新 之前 调用  */
  beforeUpdate() {},
  /** 数据更新 完成 调用   */
  updated() {},
  /**  实例销毁之前调用  */
  beforeDestroy() {},
  /**  实例销毁后调用  */
  destroyed() {},
};
</script>

<style lang="scss">
.memberPrice {
  .el-tabs--border-card {
    border: 0px !important;
    box-shadow: 0 0px 0px 0 rgba(0, 0, 0, 0), 0 0 0px 0 rgba(0, 0, 0, 0);
    .el-tabs__content {
      padding: unset;
    }
  }
  .tab-content {
    padding: 15px;
  }

  .el_input_select {
    padding-top: 1px;
    .el-input-group__append {
      width: 54px;
      .el-input__inner {
        padding: 8px;
      }
    }
  }
}
</style>
