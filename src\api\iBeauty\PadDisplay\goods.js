// pad商品
import * as API from "@/api/index";
export default {
  /**  分类*/
  CategoryValid: (params) => {
    return API.POST("api/goodsDisplayCategory/valid", params);
  },
  // 商品列表
  GoodsDisplayAll: (params) => {
    return API.POST("api/goodsDisplay/all", params);
  },
  // 商品添加
  GoodsDisplayCreate: (params) => {
    return API.POST("api/goodsDisplay/create", params);
  },
  // 商品简介
  GoodsDisplayMemo: (params) => {
    return API.POST("api/goodsDisplay/memo", params);
  },
  // 商品编辑
  GoodsDisplayUpdate: (params) => {
    return API.POST("api/goodsDisplay/update", params);
  },
  // 商品删除
  GoodsDisplayDelete: (params) => {
    return API.POST("api/goodsDisplay/delete", params);
  },
  upload_addAttachment: (params) => {
    return API.POST("api/upload/addAttachment", params);
  },
};
