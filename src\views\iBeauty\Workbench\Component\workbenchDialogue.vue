<!-- 企微对话 -->
<template>
  <div class="workbenchDialogue">
    <el-row>
      <el-col :span="24" class="marbm_10">
        <el-radio-group v-model="type" size="small" @change="typeChange">
          <el-radio-button :label="0">全部</el-radio-button>
          <el-radio-button :label="1">文本</el-radio-button>
          <el-radio-button :label="2">语音</el-radio-button>
          <el-radio-button :label="3">图片</el-radio-button>
          <el-radio-button :label="4">视频</el-radio-button>
          <el-radio-button :label="5">文件</el-radio-button>
          <el-radio-button :label="6">链接</el-radio-button>
        </el-radio-group>
      </el-col>
      <el-col :span="24">
        <el-date-picker
          type="daterange"
          v-model="pickerTime"
          unlink-panels
          range-separator="至"
          value-format="yyyy-MM-dd"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          clearable
          @change="pickerTimeChange"
          @clear="pickerTimeChange"
          size="small"
        ></el-date-picker>
      </el-col>
    </el-row>
    <el-scrollbar class="custom-scrollbar_hidden-x" ref="scroll" id="resultScroll">
      <div v-if="loading" v-loading="loading" element-loading-spinner="el-icon-loading" style="height: 20px; margin-top: 20px"></div>
      <div class="chat-content" ref="chat-content">
        <!-- recordContent 聊天记录数组-->
        <div v-for="(item, index) in recordContent" :key="index">
          <!-- 对方 -->
          <div class="word" v-if="item.IsSendByAccount">
            <img :src="item.AccountAvatar ? item.AccountAvatar : circleUrl" />
            <div class="info">
              <p class="time">
                <span class="color_000 font_12 marrt_5">{{ item.SendName }}</span
                >{{ item.SendTime }}
              </p>
              <div class="info-content" :style="item.Type == 4 ? 'padding:unset' : ''">
                <div v-if="item.Type == 1">{{ item.Content.TextMessage }}</div>
                <div v-if="item.Type == 2">
                  <audio controls>
                    <source :src="item.Content.MediaUrl" type="audio/mpeg" />
                  </audio>
                </div>
                <div v-if="item.Type == 3">
                  <el-image :src="item.Content.MediaUrl" fit="cover"></el-image>
                </div>
                <div v-if="item.Type == 4">
                  <custom-video-player :src="item.Content.MediaUrl"></custom-video-player>
                </div>
                <div v-if="item.Type == 5">
                  <a :href="item.Content.MediaUrl">{{ item.Content.FileName }}</a>
                </div>
                <a :href="item.Content.LinkUrl" v-if="item.Type == 6">
                  <span class="LinkTitle">{{ item.Content.LinkTitle }}</span>
                  <div class="dis_flex flex_x_between">
                    <span>{{ item.Content.LinkDesc }}</span>
                    <el-image class="imgShowBox" :src="item.Content.CoverUrl" fit="cover"></el-image>
                  </div>
                </a>
              </div>
            </div>
          </div>
          <!-- 我的 -->
          <div class="word-my" v-else>
            <div class="info">
              <p class="time">
                <span class="color_000 font_12 marrt_5">{{ item.SendName }}</span
                >{{ item.SendTime }}
              </p>
              <div class="info-content" :style="item.Type == 4 ? 'padding:unset' : ''">
                <div v-if="item.Type == 1">{{ item.Content.TextMessage }}</div>
                <div v-if="item.Type == 2">
                  <audio controls>
                    <source :src="item.Content.MediaUrl" type="audio/mpeg" />
                  </audio>
                </div>
                <div v-if="item.Type == 3"><el-image :src="item.Content.MediaUrl" fit="cover"></el-image></div>
                <div v-if="item.Type == 4">
                  <custom-video-player :src="item.Content.MediaUrl"></custom-video-player>
                </div>
                <div v-if="item.Type == 5">
                  <a :href="item.Content.MediaUrl">{{ item.Content.FileName }}</a>
                </div>
                <a :href="item.Content.LinkUrl" :underline="false" v-if="item.Type == 6">
                  <div class="LinkTitle">{{ item.Content.LinkTitle }}</div>
                  <div class="dis_flex flex_x_between">
                    <span>{{ item.Content.LinkDesc }}</span>
                    <el-image class="imgShowBox" :src="item.Content.CoverUrl" fit="cover"></el-image>
                  </div>
                </a>
              </div>
            </div>
            <img :src="item.AccountAvatar ? item.AccountAvatar : circleUrl" />
          </div>
        </div>
      </div>
    </el-scrollbar>
  </div>
</template>

<script>
import API from "@/api/CRM/Customer/customerDialogue";
import customVideoPlayer from "@/views/CRM/Customer/Components/Customer/customVideoPlayer.vue";
const dayjs = require("dayjs");
export default {
  name: "workbenchDialogue",
  /** 监听数据变化   */
  watch: {},
  /**  引入的组件  */
  components: { customVideoPlayer },
  props: {
    customerID: Number,
  },
  /**  Vue 实例的数据对象**/
  data() {
    return {
      loading: false,
      pickerTime: [dayjs().subtract(1, "year").format("YYYY-MM-DD"), dayjs().format("YYYY-MM-DD")],
      type: "0",
      PageNum: 1,
      recordContent: [],
      Total: 0,
      PageSize: "20",
      scrollTop: 0,
      circleUrl: "https://cube.elemecdn.com/3/7c/********************************.png", //默认头像
      IsScroll: false,
      oldHeight: 0,
    };
  },
  /**计算属性  */
  computed: {},
  /**  方法集合  */
  methods: {
    /* 会话日期筛选 */
    pickerTimeChange() {
      let that = this;
      that.recordContent = [];
      that.PageNum = 1;
      that.IsScroll = false;
      that.getCustomerRecord();
    },
    /* 会话内容类型切换筛选 */
    typeChange() {
      let that = this;
      that.recordContent = [];
      that.PageNum = 1;
      that.IsScroll = false;
      that.getCustomerRecord();
    },
    /* 弹窗关闭清空筛选条件 */
    clearSeachData() {
      let that = this;
      that.recordContent = [];
      that.PageNum = 1;
      that.type = "0";
      that.IsScroll = false;
      that.pickerTime = [dayjs().subtract(1, "year").format("YYYY-MM-DD"), dayjs().format("YYYY-MM-DD")];
    },
    /* 获取scrollTop */
    handleScroll(event) {
      let that = this;

      let newHeight = event.target.scrollHeight;
      if (that.IsScroll) {
        let height = this.$refs["scroll"].$refs["wrap"].scrollTop;
        this.oldHeight = event.target.scrollHeight;
        if (height == 0 && that.recordContent.length < that.Total) {
          that.PageNum++;
          that.getCustomerRecord(newHeight);
        }
      }
    },
    // 获取会话记录
    getCustomerRecord(height) {
      let that = this;
      that.loading = true;
      let params = {
        PageIndex: that.PageNum,
        CustomerID: that.CustomerID,
        PageSize: that.PageSize,
        BeginTime: that.pickerTime ? that.pickerTime[0] : null,
        EndTime: that.pickerTime ? that.pickerTime[1] : null,
        Type: that.type,
      };
      API.getCustomerRecord(params)
        .then((res) => {
          if (res.StateCode == 200) {
            if (res.Data.List && res.Data.List.length > 0) {
              that.recordContent.unshift(...res.Data.List);
              that.Total = res.Data.Total;
              if (that.recordContent && that.recordContent.length <= res.Data.PageSize) {
                let demo = this.$refs["scroll"].$refs["wrap"];
                this.$nextTick(() => {
                  demo.scrollTop = demo.scrollHeight;
                });
              } else {
                let demo = this.$refs["scroll"].$refs["wrap"];
                this.$nextTick(() => {
                  demo.scrollTop = demo.scrollHeight - height;
                });
              }
            }
          } else {
            this.$message.error({
              message: res.Message,
            });
          }
        })
        .finally(function () {
          that.loading = false;
          that.IsScroll = true;
        });
    },
  },
  /** 创建实例之前执行函数   */
  beforeCreate() {},
  /**  实例创建完成之后  */
  created() {},
  /**  在挂载开始之前被调用  */
  beforeMount() {},
  /**  实例被挂载后调用  */
  mounted() {
    let that = this;
    document.getElementById("resultScroll").addEventListener("scroll", that.handleScroll, true);
  },
  /**  数据更新 之前 调用  */
  beforeUpdate() {},
  /** 数据更新 完成 调用   */
  updated() {},
  /**  实例销毁之前调用  */
  beforeDestroy() {},
  /**  实例销毁后调用  */
  destroyed() {},
};
</script>

<style lang="scss">
.workbenchDialogue {
  height: 100%;
  .custom-scrollbar_hidden-x {
    .el-scrollbar__wrap {
      overflow-x: hidden !important;
    }
    .el-scrollbar__view {
      position: relative;
    }
    height: 65vh;
  }

  .chat-content {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    width: 80%;
    padding: 20px;
    .word > img {
      width: 40px;
      height: 40px;
      border-radius: 50%;
    }
    .word {
      display: flex;
      margin-bottom: 20px;
      .info {
        margin-left: 10px;
        .time {
          font-size: 12px;
          color: rgba(51, 51, 51, 0.8);
          margin: 0;
          height: 20px;
          line-height: 20px;
          margin-top: -5px;
        }
        .info-content {
          padding: 10px;
          font-size: 13px;
          background: #f7f8fa;
          position: relative;
          margin-top: 8px;
          color: #000;
          box-shadow: 0px 5px 10px rgba(0, 0, 0, 0.08);
          border-radius: 6px;
        }
        //小三角形
        .info-content::before {
          position: absolute;
          left: -8px;
          top: 8px;
          content: "";
          border-right: 10px solid #f7f8fa;
          border-top: 8px solid transparent;
          border-bottom: 8px solid transparent;
        }
      }
    }
    .word-my > img {
      width: 40px;
      height: 40px;
      border-radius: 50%;
    }

    .word-my {
      display: flex;
      justify-content: flex-end;
      margin-bottom: 20px;
      .info {
        width: 90%;
        margin-left: 10px;
        text-align: right;
        .time {
          font-size: 12px;
          color: rgba(51, 51, 51, 0.8);
          margin: 0;
          height: 20px;
          line-height: 20px;
          margin-top: -5px;
          margin-right: 10px;
        }
        .info-content {
          max-width: 70%;
          padding: 10px;
          font-size: 12px;
          float: right;
          margin-right: 10px;
          position: relative;
          margin-top: 8px;
          background: #fff;
          text-align: left;
          color: #000;
          box-shadow: 0px 5px 10px rgba(0, 0, 0, 0.08);
          border-radius: 6px;
        }
        //小三角形
        .info-content::before {
          position: absolute;
          right: -8px;
          top: 8px;
          content: "";
          border-left: 10px solid #fff;
          border-top: 8px solid transparent;
          border-bottom: 8px solid transparent;
        }
      }
    }
    .LinkTitle {
      width: 230px;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
      overflow: hidden;
      margin-bottom: 5px;
    }
    .imgShowBox {
      display: inline-block;
      width: 50px;
      height: 50px;
      border-radius: 4px;
    }
  }
  audio {
    width: 300px;
    height: 20px;
  }
}
</style>
