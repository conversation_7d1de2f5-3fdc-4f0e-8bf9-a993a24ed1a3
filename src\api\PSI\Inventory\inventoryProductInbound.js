/**
 * Created by preference on 2020/12/01
 *  zmx 
 */

import * as API from '@/api/index'
export default {
  /**  10.1.产品入库列表 */
  get_list_inventoryProductInbound: params => {
    return API.POST('api/inventoryProductInbound/list', params)
  },
  /** 10.2.产品入库详情  */
  get_info_inventoryProductInbound: params => {
    return API.POST('api/inventoryProductInbound/info', params)
  },
  /**  10.3.产品入库 */
  get_create_inventoryProductInbound: params => {
    return API.POST('api/inventoryProductInbound/create', params)
  },

  /**  模板列表 */
  getPrintTemplate_list: params => {
    return API.POST('api/template/list', params)
  },

  /** 获取产品入库附件列表 */
  getAttachmentList: params => {
    return API.POST('api/inventoryProductInbound/attachment/list', params)
  },

  /** 删除产品入库附件 */
  deleteAttachment: params => {
    return API.POST('api/inventoryProductInbound/attachment/delete', params)
  },
}