<template>
  <div class="channelLevel content_body" :loading="loading">
    <div class="nav_header">
      <el-row>
        <el-col :span="20">
          <el-form :inline="true" size="small" @submit.native.prevent @keyup.enter.native="handleSearch">
            <el-form-item label="渠道等级">
              <el-input v-model="searchRuleForm.Name" placeholder="请输入渠道等级" clearable @clear="handleSearch"></el-input>
            </el-form-item>
            <el-form-item label="有效性">
              <el-select v-model="searchRuleForm.Active" placeholder="请选择有效性" clearable @change="handleSearch">
                <el-option label="有效" :value="true"></el-option>
                <el-option label="无效" :value="false"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" size="small" @click="handleSearch" v-prevent-click>搜索</el-button>
            </el-form-item>
          </el-form>
        </el-col>
        <el-col :span="4" class="text_right">
          <el-button type="primary" size="small" @click="addChannelLevel" v-prevent-click>新增</el-button>
        </el-col>
      </el-row>
    </div>

    <el-table size="small" :data="tableData">
      <el-table-column prop="Name" label="渠道等级"></el-table-column>
      <el-table-column label="移动" min-width="180px">
        <template slot-scope="scope">
          <el-button
            :disabled="scope.$index == 0"
            size="small"
            type="primary"
            circle
            icon="el-icon-upload2"
            @click="upOneClick(scope.row, scope.$index)"
          ></el-button>
          <el-button :disabled="scope.$index == 0" size="small" type="primary" circle icon="el-icon-top" @click="upClick(scope.row, scope.$index)"></el-button>
          <el-button
            :disabled="scope.$index == tableData.length - 1"
            size="small"
            type="primary"
            circle
            icon="el-icon-bottom"
            @click="downClick(scope.row, scope.$index)"
          ></el-button>
          <el-button
            :disabled="scope.$index == tableData.length - 1"
            size="small"
            type="primary"
            circle
            icon="el-icon-download"
            @click="downOneClick(scope.row, scope.$index)"
          ></el-button>
        </template>
      </el-table-column>

      <el-table-column prop="Active" label="有效性">
        <template slot-scope="scope">
          {{ scope.row.Active == true ? "有效" : "无效" }}
        </template>
      </el-table-column>
      <el-table-column label="操作" width="80px">
        <template slot-scope="scope">
          <el-button type="primary" size="small" @click="showEditDialog(scope.row)" v-prevent-click>编辑</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!--新增弹窗-->
    <el-dialog :title="isAdd ? '新增渠道等级' : '编辑渠道等级'" :visible.sync="dialogVisible" width="500px">
      <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="90px" size="small" @submit.native.prevent>
        <el-form-item label="渠道等级" prop="Name">
          <el-input size="small" width="60%" v-model="ruleForm.Name"></el-input>
        </el-form-item>
        <el-form-item label="是否有效" prop="Active" v-if="!isAdd">
          <el-radio-group v-model="ruleForm.Active">
            <el-radio :label="true">是</el-radio>
            <el-radio :label="false">否</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="dialogVisible = false" v-prevent-click>取 消</el-button>
        <el-button type="primary" size="small" :loading="modalLoading" v-prevent-click @click="saveChannelLevelClick">保存</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import API from "@/api/CRM/Channel/channelLevel.js";
export default {
  name: "ChannelLevel",

  props: {},
  /** 监听数据变化   */
  watch: {},
  /**  引入的组件  */
  components: {},
  /**  Vue 实例的数据对象**/
  data() {
    return {
      dialogVisible: false,
      loading: false,
      modalLoading: false,
      isAdd: false,
      searchRuleForm: {
        Name: "",
        Active: true,
      },
      ruleForm: {
        Name: "",
        Active: true,
      },

      rules: {
        Name: [{ required: true, message: "请输入渠道等级", trigger: "blur" }],
        Active: [{ required: true, message: "请选择是否有效", trigger: "change" }],
      }, // 编辑、新增表单规则验证
      tableData: [],
    };
  },
  /**计算属性  */
  computed: {},
  /**  方法集合  */
  methods: {
    /**  搜索  */
    handleSearch() {
      this.channelLevel_list();
    },
    /**  添加  */
    addChannelLevel() {
      let that = this;
      that.isAdd = true;
      that.ruleForm = {
        Name: "",
      };
      that.dialogVisible = true;
    },
    /** 编辑   */
    showEditDialog(row) {
      let that = this;
      that.isAdd = false;
      this.ruleForm = Object.assign({}, row);
      that.dialogVisible = true;
    },
    /**  保存 事件 */
    saveChannelLevelClick() {
      let that = this;
      that.$refs.ruleForm.validate((valid) => {
        if (valid) {
          if (that.isAdd) {
            that.channelLevel_create();
          } else {
            that.channelLevel_update();
          }
        }
      });
    },
    // 移动至顶部
    upOneClick(row) {
      let that = this;
      that.channelLevel_move(row.ID, "");
    },
    // 向上移动
    upClick(row, index) {
      let that = this;
      let beforeId = "";
      if (index > 1) {
        beforeId = that.tableData[index - 2].ID;
      }
      that.channelLevel_move(row.ID, beforeId);
    },
    // 向下移动
    downClick(row, index) {
      let that = this;
      let beforeId = "";
      let customerFileCategory = [];
      customerFileCategory = that.tableData;
      if (index + 1 != customerFileCategory.length) {
        beforeId = customerFileCategory[index + 1].ID;
      }
      that.channelLevel_move(row.ID, beforeId);
    },
    // 移动至底部
    downOneClick(row, index) {
      let that = this;
      let beforeId = "";
      let tableLength = 0;
      tableLength = that.tableData.length;
      if (index < tableLength - 1) {
        beforeId = that.tableData[tableLength - 1].ID;
      }
      that.channelLevel_move(row.ID, beforeId);
    },
    /****************************  请求  *****************************/
    /** 列表   */
    async channelLevel_list() {
      let that = this;
      let params = Object.assign({}, that.searchRuleForm);
      let res = await API.channelLevel_list(params);
      if (res.StateCode == 200) {
        that.tableData = res.Data;
      } else {
        that.$message.error(res.Message);
      }
    },
    /**  创建  */
    async channelLevel_create() {
      let that = this;
      let params = { Name: that.ruleForm.Name };
      that.modalLoading = true;
      let res = await API.channelLevel_create(params);
      if (res.StateCode == 200) {
        that.$message.success("添加成功");
        that.dialogVisible = false;
        that.channelLevel_list();
      } else {
        that.$message.error(res.Message);
      }
      that.modalLoading = false;
    },
    /**  更新  */
    async channelLevel_update() {
      let that = this;
      let params = Object.assign({}, that.ruleForm);
      let res = await API.channelLevel_update(params);
      if (res.StateCode == 200) {
        that.$message.success("更新成功");
        that.dialogVisible = false;
        that.channelLevel_list();
      } else {
        that.$message.error(res.Message);
      }
    },
    /**   移动 */
    async channelLevel_move(MoveID, BeforeID) {
      let that = this;
      let params = { MoveID: MoveID, BeforeID: BeforeID };
      let res = await API.channelLevel_move(params);
      if (res.StateCode == 200) {
        that.$message.success("操作成功");
        that.channelLevel_list();
      } else {
        that.$message.error(res.Message);
      }
    },
  },
  /** 创建实例之前执行函数   */
  beforeCreate() {},
  /**  实例创建完成之后  */
  created() {},
  /**  在挂载开始之前被调用  */
  beforeMount() {},
  /**  实例被挂载后调用  */
  mounted() {
    this.channelLevel_list();
  },
  /**  数据更新 之前 调用  */
  beforeUpdate() {},
  /** 数据更新 完成 调用   */
  updated() {},
  /**  实例销毁之前调用  */
  beforeDestroy() {},
  /**  实例销毁后调用  */
  destroyed() {},
};
</script>

<style lang="scss">
.channelLevel {
}
</style>

