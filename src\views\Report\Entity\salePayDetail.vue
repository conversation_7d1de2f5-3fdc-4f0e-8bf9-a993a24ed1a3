<template>
  <!-- 搜索 -->
  <div class="content_body ReportEntitySalePayDetail" v-loading="loading">
    <div class="nav_header">
      <el-form :inline="true" size="small" :model="searchSalePayData" @submit.native.prevent>
        <el-row>
          <el-form-item v-if="storeEntityList.length > 1" label="开单门店">
            <el-select
              v-model="searchSalePayData.EntityID"
              clearable
              filterable
              placeholder="请选择门店"
              :default-first-option="true"
              @change="handleSaleSearch"
            >
              <el-option v-for="item in storeEntityList" :key="item.ID" :label="item.EntityName" :value="item.ID"> </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="下单日期">
            <el-date-picker
              v-model="searchSalePayData.QueryDate"
              :picker-options="pickerOptions"
              :clearable="false"
              unlink-panels
              type="daterange"
              range-separator="至"
              value-format="yyyy-MM-dd"
              start-placeholder="下始日期"
              end-placeholder="结束日期"
              @change="handleSaleSearch"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item label="开单人">
            <el-input
              v-model="searchSalePayData.CreatedBy"
              clearable
              @keyup.enter.native="handleSaleSearch"
              @clear="handleSaleSearch"
              placeholder="请输入开单人"
            ></el-input>
          </el-form-item>
          <el-form-item label="支付类型">
            <el-select
              v-model="searchSalePayData.PayTypeName"
              clearable
              filterable
              placeholder="请选择支付类型"
              :default-first-option="true"
              @change="handleSaleSearch"
            >
              <el-option label="现金" value="现金"></el-option>
              <el-option label="本金" value="本金"></el-option>
              <el-option label="赠金" value="赠金"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="支付方式">
            <el-input
              v-model="searchSalePayData.PayName"
              clearable
              @keyup.enter.native="handleSaleSearch"
              @clear="handleSaleSearch"
              placeholder="请输入支付方式、储值卡名称"
            ></el-input>
          </el-form-item>
        </el-row>
        <el-form-item label="订单编号">
          <el-input
            v-model="searchSalePayData.BillID"
            clearable
            @keyup.enter.native="handleSaleSearch"
            @clear="handleSaleSearch"
            placeholder="请输入订单编号"
          ></el-input>
        </el-form-item>
        <el-form-item label="客户信息">
          <el-input
            v-model="searchSalePayData.Name"
            clearable
            @keyup.enter.native="handleSaleSearch"
            @clear="handleSaleSearch"
            placeholder="请输入客户姓名、编号"
          ></el-input>
        </el-form-item>

        <el-form-item label="会员等级">
          <el-select v-model="searchSalePayData.CustomerLevelID" placeholder="请选择会员等级" filterable size="small" clearable @change="handleSaleSearch">
            <el-option v-for="item in customerLevel" :key="item.ID" :label="item.Name" :value="item.ID"> </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="信息来源">
          <el-cascader
            v-model="searchSalePayData.CustomerSourceID"
            placeholder="请选择信息来源"
            :options="customerSource"
            :props="{
              checkStrictly: true,
              children: 'Child',
              value: 'ID',
              label: 'Name',
              emitPath: false,
            }"
            :show-all-levels="false"
            filterable
            clearable
            @change="handleSaleSearch"
          ></el-cascader>
        </el-form-item>

        <el-form-item label="渠道来源">
          <el-input
            v-model="searchSalePayData.ChannelName"
            clearable
            @keyup.enter.native="handleSaleSearch"
            @clear="handleSaleSearch"
            placeholder="请输入渠道来源"
          ></el-input>
        </el-form-item>

        <el-form-item label="介绍人">
          <el-input
            v-model="searchSalePayData.IntroducerName"
            clearable
            @keyup.enter.native="handleSaleSearch"
            @clear="handleSaleSearch"
            placeholder="请输入介绍人"
          ></el-input>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" v-prevent-click @click="handleSaleSearch">搜索</el-button>
        </el-form-item>
        <!-- <el-form-item>
          <el-button v-if="SalePayDetailExport" type="primary" size="small" :loading="downloadLoading" @click="downloadSalePayExcel">导出</el-button>
        </el-form-item> -->

        <el-form-item>
          <el-dropdown @command="downloadSalePayExcel_command" v-if="SalePayDetailExport && SalePayDetailExportDisPlayPhone" :loading="downloadLoading">
            <el-button type="primary"> 导出<i class="el-icon-arrow-down el-icon--right"></i> </el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item command="excelNoDisPlayPhone">导出</el-dropdown-item>
              <el-dropdown-item command="excelDisPlayPhone">导出(手机号)</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
          <el-button
            @click="downloadSalePayExcel_command('excelNoDisPlayPhone')"
            v-else-if="SalePayDetailExport"
            type="primary"
            v-prevent-click
            :loading="downloadLoading"
          >
            导出
          </el-button>
          <el-button
            @click="downloadSalePayExcel_command('excelDisPlayPhone')"
            v-else-if="SalePayDetailExportDisPlayPhone"
            type="primary"
            v-prevent-click
            :loading="downloadLoading"
          >
            导出(手机号）
          </el-button>
        </el-form-item>
      </el-form>
    </div>
    <!-- 表格 -->
    <el-table size="small" show-summary :summary-method="getsalePayDetailListSummaries" :data="salePayDetailList">
      <el-table-column prop="ID" label="订单编号"></el-table-column>
      <el-table-column prop="EntityName" label="下单门店"></el-table-column>
      <el-table-column prop="IsLargess" label="订单类型">
        <template v-slot="scope">
          <div v-if="scope.row.BillType == 10">销售单</div>
          <div v-else-if="scope.row.BillType == 20">销售退款单</div>
          <div v-else-if="scope.row.BillType == 30">补尾款单</div>
          <div v-else-if="scope.row.BillType == 40">充值单</div>
        </template>
      </el-table-column>
      <el-table-column prop="Amount" label="订单金额">
        <template v-slot="scope">
          {{ scope.row.Amount | toFixed | NumFormat }}
        </template>
      </el-table-column>
      <el-table-column prop="CreatedBy" label="开单人"> </el-table-column>
      <el-table-column prop="BillDate" label="下单日期">
        <template slot-scope="scope">
          {{ scope.row.BillDate | dateFormat('YYYY-MM-DD HH:mm') }}
        </template>
      </el-table-column>
      <el-table-column prop="CreatedOn" label="录单日期">
        <template slot-scope="scope">
          {{ scope.row.CreatedOn | dateFormat('YYYY-MM-DD HH:mm') }}
        </template>
      </el-table-column>
      <el-table-column label="客户信息">
        <el-table-column prop="BillID" label="客户" width="150px">
          <template slot-scope="scope">
            <div>{{ scope.row.Name }}</div>
            <div v-if="scope.row.CustomerPhoneNumber != null && scope.row.CustomerPhoneNumber != ''">{{ scope.row.CustomerPhoneNumber }}</div>
            <div v-if="scope.row.Code != null && scope.row.Code != ''">客户编号：{{ scope.row.Code }}</div>
          </template>
        </el-table-column>
        <el-table-column prop="CustomerLevelName" label="会员等级"></el-table-column>
        <el-table-column prop="CustomerSourceName" label="信息来源"></el-table-column>

        <el-table-column prop="ChannelName" label="渠道来源"></el-table-column>
        <el-table-column prop="IntroducerName" label="介绍人"></el-table-column>
      </el-table-column>
      <el-table-column label="支付信息">
        <el-table-column prop="PayTypeName" label="支付类型"></el-table-column>
        <el-table-column prop="BillID" label="支付方式">
          <template v-slot="scope">
            {{ scope.row.PayMethodName }}
          </template>
        </el-table-column>
        <el-table-column align="right" prop="PayAmount" label="支付金额">
          <template v-slot="scope">
            {{ scope.row.PayAmount | toFixed | NumFormat }}
          </template>
        </el-table-column>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <div class="pad_15 text_right">
      <el-pagination
        background
        v-if="salePayPaginations.total > 0"
        @current-change="handleSalePayDetailPageChange"
        :current-page.sync="salePayPaginations.page"
        :page-size="salePayPaginations.page_size"
        :layout="salePayPaginations.layout"
        :total="salePayPaginations.total"
      ></el-pagination>
    </div>
  </div>
</template>

<script>
import EntityAPI from '@/api/Report/Common/entity';
import APICustomerLevel from '@/api/CRM/Customer/customerLevel';
import APICustomerSource from '@/api/CRM/Customer/customerSource';
import API from '@/api/Report/Entity/salePayDetail';
import dateTime from '@/components/js/date';
import permission from '@/components/js/permission.js';
const dayjs = require('dayjs');
const isoWeek = require('dayjs/plugin/isoWeek');
dayjs.extend(isoWeek);
export default {
  name: 'ReportEntitySalePayDetail',

  components: {},

  directives: {},

  beforeRouteEnter(to, from, next) {
    next((vm) => {
      vm.SalePayDetailExport = permission.permission(to.meta.Permission, 'Report-Trade-SalePayDetail-Export');
      vm.SalePayDetailExportDisPlayPhone = permission.permission(to.meta.Permission, 'Report-Trade-SalePayDetail-ExportDisPlayPhone');
    });
  },
  data() {
    return {
      SalePayDetailExportDisPlayPhone: false,
      loading: false,
      downloadLoading: false,
      storeEntityList: [], //门店列表
      searchSalePayData: {
        EntityID: '',
        BillID: '',
        Name: '',
        PayTypeName: '',
        PayName: '',
        CreatedBy: '',
        QueryDate: [],
        CustomerLevelID: '',
        CustomerSourceID: '',
        IntroducerName: '', //介绍人
        ChannelName: '', //渠道
      },
      salePayPaginations: {
        page: 1, // 当前位于哪页
        total: 0, // 总数
        page_size: 10, // 1页显示多少条
        layout: 'total, prev, pager, next,jumper', // 翻页属性
      },
      SalePayDetailExport: false,
      salePayDetailList: [],
      entitySalePayDetailSumStatementForm: {},
      pickerOptions: {
        shortcuts: [
          {
            text: '今天',
            onClick: (picker) => {
              let end = new Date();
              let start = new Date();
              picker.$emit('pick', [start, end]);
            },
          },
          {
            text: '本周',
            onClick: (picker) => {
              let end = new Date();
              let weekDay = dayjs(end).isoWeekday();
              let start = dayjs(end)
                .subtract(weekDay - 1, 'day')
                .toDate();
              picker.$emit('pick', [start, end]);
            },
          },
          {
            text: '本月',
            onClick: (picker) => {
              let end = new Date();
              let start = dayjs(dayjs(end).format('YYYY-MM') + '01').toDate();
              picker.$emit('pick', [start, end]);
            },
          },
        ],
      },
      customerLevel: [],
      customerSource: [],
    };
  },
  created() {
    this.searchSalePayData.QueryDate = [dateTime.formatDate.format(new Date(), 'YYYY-MM-DD'), dateTime.formatDate.format(new Date(), 'YYYY-MM-DD')];
  },
  mounted() {
    const that = this;
    that.SalePayDetailExport = permission.permission(that.$route.meta.Permission, 'Report-Trade-SalePayDetail-Export');
    that.SalePayDetailExportDisPlayPhone = permission.permission(that.$route.meta.Permission, 'Report-Trade-SalePayDetail-ExportDisPlayPhone');
    that.getstoreEntityList();
    that.getOrderList();
    that.CustomerLevelData();
    that.CustomerSourceData();
  },

  methods: {
    //获得当前用户下的权限门店
    getstoreEntityList() {
      var that = this;
      that.loading = true;
      EntityAPI.getStoreEntityList()
        .then((res) => {
          if (res.StateCode == 200) {
            that.storeEntityList = res.Data;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },
    // 获取订单列表
    getOrderList() {
      const that = this;

      if (!that.searchSalePayData.QueryDate) {
        return;
      }
      if (dayjs(that.searchSalePayData.QueryDate[0]).add(366, 'day').valueOf() < dayjs(that.searchSalePayData.QueryDate[1]).valueOf()) {
        that.$message.error('时间筛选范围不能超366天');
        return;
      }
      that.loading = true;
      const params = {
        PageNum: that.salePayPaginations.page,
        EntityID: that.searchSalePayData.EntityID,
        BillID: that.searchSalePayData.BillID,
        Name: that.searchSalePayData.Name,
        StartTime: that.searchSalePayData.QueryDate[0],
        EndTime: that.searchSalePayData.QueryDate[1],
        PayTypeName: that.searchSalePayData.PayTypeName,
        PayName: that.searchSalePayData.PayName,
        CreatedBy: that.searchSalePayData.CreatedBy,
        CustomerLevelID: that.searchSalePayData.CustomerLevelID, //顾客等级
        CustomerSourceID: that.searchSalePayData.CustomerSourceID, //顾客来源
        IntroducerName: that.searchSalePayData.IntroducerName, //介绍人
        ChannelName: that.searchSalePayData.ChannelName, //渠道
      };
      API.getEntitySalePayDetailStatementList(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.salePayDetailList = res.Data.detail.List;
            that.entitySalePayDetailSumStatementForm = res.Data.entitySalePayDetailSumStatementForm;
            that.salePayPaginations.total = res.Data.detail.Total;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(() => {
          that.loading = false;
        });
    },
    handleSaleSearch() {
      const that = this;
      that.salePayPaginations.page = 1;
      that.getOrderList();
    },
    /**    */
    downloadSalePayExcel_command(type) {
      if (type == 'excelNoDisPlayPhone') {
        this.downloadSalePayExcel();
      }
      if (type == 'excelDisPlayPhone') {
        this.entitySalePayDetailStatement_excelDisPlayPhone();
      }
    },
    downloadSalePayExcel() {
      const that = this;
      if (!that.searchSalePayData.QueryDate) {
        return;
      }
      if (dayjs(that.searchSalePayData.QueryDate[0]).add(366, 'day').valueOf() < dayjs(that.searchSalePayData.QueryDate[1]).valueOf()) {
        that.$message.error('时间筛选范围不能超366天');
        return;
      }
      that.downloadLoading = true;
      const data = that.searchSalePayData;
      const params = {
        EntityID: data.EntityID, //门店
        BillID: data.BillID, //订单号
        Name: data.Name, //客户姓名/编号搜索
        StartTime: data.QueryDate[0], //开始时间
        EndTime: data.QueryDate[1], //结束时间
        PayTypeName: data.PayTypeName, //现金  本金 赠金（传汉字）
        PayName: data.PayName, //支付方式筛选（支付方式及储值卡名字
        CreatedBy: data.CreatedBy, //开单人
        CustomerLevelID: data.CustomerLevelID, //顾客等级
        CustomerSourceID: data.CustomerSourceID, //顾客来源
        IntroducerName: that.searchSalePayData.IntroducerName, //介绍人
        ChannelName: that.searchSalePayData.ChannelName, //渠道
      };
      API.EntitySalePayDetailStatementExcel(params)
        .then((res) => {
          this.$message.success({
            message: '正在导出',
            duration: '4000',
          });
          const link = document.createElement('a');
          let blob = new Blob([res], { type: 'application/octet-stream' });
          link.style.display = 'none';
          link.href = URL.createObjectURL(blob);
          link.download = '销售订单支付明细.xlsx'; //下载的文件名
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
        })
        .finally(() => {
          that.downloadLoading = false;
        });
    },

    entitySalePayDetailStatement_excelDisPlayPhone() {
      const that = this;
      if (!that.searchSalePayData.QueryDate) {
        return;
      }
      if (dayjs(that.searchSalePayData.QueryDate[0]).add(366, 'day').valueOf() < dayjs(that.searchSalePayData.QueryDate[1]).valueOf()) {
        that.$message.error('时间筛选范围不能超366天');
        return;
      }
      that.downloadLoading = true;
      const data = that.searchSalePayData;
      const params = {
        EntityID: data.EntityID, //门店
        BillID: data.BillID, //订单号
        Name: data.Name, //客户姓名/编号搜索
        StartTime: data.QueryDate[0], //开始时间
        EndTime: data.QueryDate[1], //结束时间
        PayTypeName: data.PayTypeName, //现金  本金 赠金（传汉字）
        PayName: data.PayName, //支付方式筛选（支付方式及储值卡名字
        CreatedBy: data.CreatedBy, //开单人
        CustomerLevelID: data.CustomerLevelID, //顾客等级
        CustomerSourceID: data.CustomerSourceID, //顾客来源
        IntroducerName: that.searchSalePayData.IntroducerName, //介绍人
        ChannelName: that.searchSalePayData.ChannelName, //渠道
      };
      API.entitySalePayDetailStatement_excelDisPlayPhone(params)
        .then((res) => {
          this.$message.success({
            message: '正在导出',
            duration: '4000',
          });
          const link = document.createElement('a');
          let blob = new Blob([res], { type: 'application/octet-stream' });
          link.style.display = 'none';
          link.href = URL.createObjectURL(blob);
          link.download = '销售订单支付明细(显示手机号).xlsx'; //下载的文件名
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
        })
        .finally(() => {
          that.downloadLoading = false;
        });
    },
    // 分页
    handleSalePayDetailPageChange() {
      this.getOrderList();
    },
    // 合计
    getsalePayDetailListSummaries(param) {
      const { columns } = param;
      const sums = [];
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = <span class="font_weight_600">合计</span>;
          return;
        }
        var filter_NumFormat = this.$options.filters['NumFormat'];
        switch (column.property) {
          case 'PayAmount':
            sums[index] = (
              <span class="font_weight_600">
                {filter_NumFormat(this.entitySalePayDetailSumStatementForm ? this.entitySalePayDetailSumStatementForm.PayAmount : 0)}
              </span>
            );
            break;
          default:
            sums[index] = <span class="font_weight_600"></span>;
        }
      });
      return sums;
    },
    /* 顾客等级 */
    CustomerLevelData() {
      var that = this;
      var params = {
        Name: '',
        Active: true,
      };
      APICustomerLevel.getCustomerLevel(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.customerLevel = res.Data;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },

    /* 顾客来源 */
    CustomerSourceData: function () {
      var that = this;
      var params = {
        Name: '',
        Active: true,
      };
      APICustomerSource.getCustomerSource(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.customerSource = res.Data;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },
  },
};
</script>

<style lang="scss"></style>
