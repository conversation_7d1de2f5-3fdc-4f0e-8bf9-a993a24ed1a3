<template>
  <div class="prepay content_body" v-loading="loading">
    <!-- 搜索 -->
    <div class="nav_header">
      <el-row>
        <el-col :span="20">
          <el-form :inline="true" size="small" @keyup.enter.native="handleSearchEvents" @submit.native.prevent>
            <el-form-item label="门店/仓库">
              <el-input v-model="searchEntityName" size="small" @clear="handleSearchEvents" placeholder="输入门店/仓库名称" clearable></el-input>
            </el-form-item>
            <el-form-item>
              <el-button size="small" type="primary" @click="handleSearchEvents" v-prevent-click>搜索</el-button>
              <el-button size="small" type="primary" @click="payment_excelPayment_excel" :loading="execlLoading" v-prevent-click>导出</el-button>
            </el-form-item>
          </el-form>
        </el-col>
      </el-row>
    </div>

    <!-- 表格 -->
    <div>
      <el-table size="small" :data="tableData" tooltip-effect="light">
        <el-table-column prop="EntityName" label="门店/仓库"></el-table-column>
        <el-table-column prop="Balance" label="本金余额">
          <template slot-scope="scope"> ￥{{ scope.row.Balance | toFixed | NumFormat }} </template>
        </el-table-column>
        <el-table-column prop="LargessBalance" label="赠金余额">
          <template slot-scope="scope"> ￥{{ scope.row.LargessBalance | toFixed | NumFormat }} </template>
        </el-table-column>
        <el-table-column prop="TotalBalance" label="合计余额">
          <template slot-scope="scope"> ￥{{ (scope.row.Balance + scope.row.LargessBalance) | toFixed | NumFormat }} </template>
        </el-table-column>
        <el-table-column label="操作" width="235px">
          <template slot-scope="scope">
            <el-button v-if="!scope.row.IsPrimaryEntity" type="primary" size="small" @click="checek_contactDetail(scope.row)">往来明细</el-button>
            <el-button v-if="!scope.row.IsPrimaryEntity" type="primary" size="small" @click="prepayRecharge(scope.row)">存入</el-button>
            <el-button v-if="!scope.row.IsPrimaryEntity" type="danger" size="small" @click="prepayRefund(scope.row)">退款</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <!-- 分页 -->
    <div class="pad_10 dis_flex flex_x_end" v-if="paginations.total > 0">
      <el-pagination
        background
        :current-page.sync="paginations.page"
        :layout="paginations.layout"
        :total="paginations.total"
        @current-change="currentChange"
      ></el-pagination>
    </div>

    <!--充值 -->
    <el-dialog title="预付余额存入" :visible.sync="dialogVisible" width="800px">
      <el-form ref="addRuleForm" :model="addRuleForm" :rules="addRules" label-width="auto" size="small" @submit.native.prevent>
        <el-row>
          <el-col :span="12">
            <el-form-item label="仓库门店" prop="EntityName">{{ addRuleForm.EntityName }}</el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="收付款类别" prop="PaymentCategoryID">
              <el-select value-key="ID" v-model="addRuleForm.PaymentCategoryID" filterable placeholder="请选择收付款类别">
                <el-option value-key="ID" v-for="item in prepayCategorys" :key="item.ID" :label="item.Name" :value="item.ID"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="存入金额" prop="Balance">
              <el-input v-model="addRuleForm.Balance" type="number" v-input-fixed class="custom_input" placeholder="请输入存入金额"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="赠送金额" prop="LargessBalance">
              <el-input v-model="addRuleForm.LargessBalance" type="number" v-input-fixed class="custom_input" placeholder="请输入赠送金额"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="付款方式" prop="PaymentWay">离线转账</el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="回单号码" prop="ReceiptNumber">
              <el-input v-model="addRuleForm.ReceiptNumber" placeholder="请输入回单号码"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="付款户名" prop="PaymentAccountName">
              <el-input v-model="addRuleForm.PaymentAccountName" placeholder="请输入付款户名"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="备注信息" prop="Remark">
              <el-input v-model="addRuleForm.Remark" placeholder="请输入备注信息"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false" size="small" v-prevent-click>取 消</el-button>
        <el-button type="primary" @click="submitprepayRechargeClick" :loading="rechargeLoading" size="small" v-prevent-click>保 存</el-button>
      </div>
    </el-dialog>

    <!--  退款 -->
    <el-dialog title="预付余额退款" :visible.sync="refundDialogVisible" width="800px">
      <el-form ref="refundRuleForm" :model="refundRuleForm" :rules="addRules" label-width="auto" size="small" @submit.native.prevent>
        <el-row>
          <el-col :span="12">
            <el-form-item label="门店仓库:" prop="EntityName">{{ refundRuleForm.EntityName }}</el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="收付款类别" prop="PaymentCategoryID">
              <el-select value-key="ID" v-model="refundRuleForm.PaymentCategoryID" filterable placeholder="请选择收付款类别">
                <el-option value-key="ID" v-for="item in prepayCategorys" :key="item.ID" :label="item.Name" :value="item.ID"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="退款金额" prop="Balance">
              <el-input
                v-model="refundRuleForm.Balance"
                type="number"
                v-input-fixed
                @input="changeBalance"
                class="custom_input"
                placeholder="请输入退款金额"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="退赠送金额" prop="LargessBalance">
              <el-input
                v-model="refundRuleForm.LargessBalance"
                type="number"
                v-input-fixed
                @input="changeLargessBalance"
                class="custom_input"
                placeholder="请输入退赠送金额"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="付款方式" prop="PaymentWay">离线转账</el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="回单号码" prop="ReceiptNumber">
              <el-input v-model="refundRuleForm.ReceiptNumber" placeholder="请输入回单号码"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="付款户名" prop="PaymentAccountName">
              <el-input v-model="refundRuleForm.PaymentAccountName" placeholder="请输入付款户名"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="备注信息" prop="Remark">
              <el-input v-model="refundRuleForm.Remark" placeholder="请输入备注信息"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="refundDialogVisible = false" size="small" v-prevent-click>取 消</el-button>
        <el-button type="primary" @click="submitprepayRefundClick" :loading="refundLoading" size="small" v-prevent-click>保 存</el-button>
      </div>
    </el-dialog>
    <!--    -->
    <el-dialog :title="'往来明细--' + selectDetailItem.EntityName" :visible.sync="detailDialogVisible" width="1500px">
      <el-row>
        <el-col :span="20">
          <el-form :inline="true" size="small" @keyup.enter.native="handleSearchEventsDetail" @submit.native.prevent>
            <el-form-item label="单据号">
              <el-input v-model="detailSearchData.ID" size="small" @clear="handleSearchEventsDetail" placeholder="输入单据号搜索" clearable></el-input>
            </el-form-item>
            <el-form-item label="单据类型">
              <el-select v-model="detailSearchData.BillType" placeholder="请选择单据类型" @change="handleSearchEventsDetail" clearable>
                <el-option label="预付款单" value="10"> </el-option>
                <el-option label="退款单" value="20"> </el-option>
                <el-option label="要货付款单" value="30"> </el-option>
                <el-option label="退货付款单" value="40"> </el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button size="small" type="primary" @click="handleSearchEventsDetail" v-prevent-click>搜索</el-button>
            </el-form-item>
          </el-form>
        </el-col>
      </el-row>
      <el-table size="small" :data="detailTableData" tooltip-effect="light" v-loading="detailLoading">
        <el-table-column prop="ID" label="单据编号" width="150"></el-table-column>

        <el-table-column prop="BillType" label="单据类型" :formatter="billTypeFormatter"></el-table-column>

        <el-table-column prop="Balance" label="本金金额">
          <template slot-scope="scope">
            <div v-if="scope.row.Balance < 0" class="color_red">{{ scope.row.Balance | toFixed | NumFormat }}</div>
            <div v-else-if="scope.row.Balance > 0" class="color_green">+{{ scope.row.Balance | toFixed | NumFormat }}</div>
            <div v-else>0.00</div>
          </template>
        </el-table-column>

        <el-table-column prop="LargessBalance" label="赠金金额">
          <template slot-scope="scope">
            <div v-if="scope.row.LargessBalance < 0" class="color_red">{{ scope.row.LargessBalance | toFixed | NumFormat }}</div>
            <div v-else-if="scope.row.LargessBalance > 0" class="color_green">+{{ scope.row.LargessBalance | toFixed | NumFormat }}</div>
            <div v-else>0.00</div>
          </template>
        </el-table-column>

        <el-table-column prop="TotalBalance" label="本金剩余">
          <template slot-scope="scope">
            <div v-if="scope.row.TotalBalance < 0" class="color_red">{{ scope.row.TotalBalance | toFixed | NumFormat }}</div>
            <div v-else-if="scope.row.TotalBalance > 0" class="color_green">+{{ scope.row.TotalBalance | toFixed | NumFormat }}</div>
            <div v-else>0.00</div>
          </template>
        </el-table-column>

        <el-table-column prop="TotalLargessBalance" label="赠金剩余">
          <template slot-scope="scope">
            <div v-if="scope.row.TotalLargessBalance < 0" class="color_red">{{ scope.row.TotalLargessBalance | toFixed | NumFormat }}</div>
            <div v-else-if="scope.row.TotalLargessBalance > 0" class="color_green">+{{ scope.row.TotalLargessBalance | toFixed | NumFormat }}</div>
            <div v-else>0.00</div>
          </template>
        </el-table-column>

        <el-table-column prop="CategoryName" label="收款类别"></el-table-column>
        <el-table-column prop="SourceBillType" label="来源单据类型" :formatter="sourceBillTypeFormatter"></el-table-column>
        <el-table-column prop="SourceBillNo" label="来源单据号" width="150"></el-table-column>
        <el-table-column prop="CreatedOn" label="制单时间">
          <template slot-scope="scope">
            {{ scope.row.CreatedOn | dateFormat }}
          </template>
        </el-table-column>
        <el-table-column prop="CreatedBy" label="操作人"></el-table-column>
      </el-table>
      <!-- 分页 -->
      <div class="pad_10 dis_flex flex_x_end" v-if="detailPaginations.total > 0">
        <el-pagination
          background
          :current-page.sync="detailPaginations.page"
          :layout="detailPaginations.layout"
          :total="detailPaginations.total"
          @current-change="currentDetailChange"
        ></el-pagination>
      </div>
    </el-dialog>
  </div>
</template>



<script>
import API from "@/api/PSI/Payment/prepay.js";
export default {
  name: "Prepay",
  props: {},
  /**  引入的组件  */
  components: {},
  /**  Vue 实例的数据对象**/
  data() {
    return {
      loading: false,
      dialogVisible: false,
      rechargeLoading: false,
      refundDialogVisible: false,
      refundLoading: false,
      detailDialogVisible: false,
      detailLoading: false,
      execlLoading: false,
      searchEntityName: "",
      tableData: [],
      paginations: {
        page: 1, // 当前位于哪页
        total: 0, // 总数
        page_size: 10, // 1页显示多少条
        layout: "total, prev, pager, next, jumper", // 翻页属性
      }, //需要给分页组件传的信息
      isAdd: false,
      catePage: 1, //分类分页
      catePageTotal: 0, //分类分页
      prepayCategorys: [],
      addRuleForm: {
        BillType: "10", //10：预付余额账户付款，20：预付余额账户退款，30：要货预付余额账户付款
        EntityID: "", //门店编号
        EntityName: "",
        PaymentCategoryID: "", //分类编号
        Balance: "", //本金
        LargessBalance: "", //赠额
        PaymentWay: "", //付款方式（10：离线转账，20：余额支付）
        ReceiptNumber: "", //回单号码（离线转账填写）
        PaymentAccountName: "", //付款户名（离线转账填写）
        Remark: "", //备注（离线转账填写）
      },
      addRules: {
        EntityID: [
          {
            required: true,
            message: "门店/仓库不能为空",
            trigger: ["blur", "change"],
          },
        ],
        PaymentCategoryID: [
          {
            required: true,
            message: "请选择收付款类别",
            trigger: ["blur", "change"],
          },
        ],
        Balance: [
          {
            required: true,
            message: "请输入存入金额",
            trigger: ["blur", "change"],
          },
        ],
        ReceiptNumber: [
          {
            required: true,
            message: "请输入回单号码",
            trigger: ["blur", "change"],
          },
        ],
        PaymentAccountName: [
          {
            required: true,
            message: "请输入付款户名",
            trigger: ["blur", "change"],
          },
        ],
      },
      refundRuleForm: {
        BillType: "20", //10：预付余额账户付款，20：预付余额账户退款，30：要货预付余额账户付款
        EntityID: "", //门店编号
        EntityName: "",
        PaymentCategoryID: "", //分类编号
        Balance: "", //本金
        LargessBalance: "", //赠额
        PaymentWay: "", //付款方式（10：离线转账，20：余额支付）
        ReceiptNumber: "", //回单号码（离线转账填写）
        PaymentAccountName: "", //付款户名（离线转账填写）
        Remark: "", //备注（离线转账填写）
      },
      refundRules: {
        EntityID: [
          {
            required: true,
            message: "门店/仓库不能为空",
            trigger: ["blur", "change"],
          },
        ],
        PaymentCategoryID: [
          {
            required: true,
            message: "请选择预付款类别",
            trigger: ["blur", "change"],
          },
        ],
        Balance: [
          {
            required: true,
            message: "请输入充值金额",
            trigger: ["blur", "change"],
          },
        ],
        ReceiptNumber: [
          {
            required: true,
            message: "请输入回单号",
            trigger: ["blur", "change"],
          },
        ],
        PaymentAccountName: [
          {
            required: true,
            message: "请输入付款户名",
            trigger: ["blur", "change"],
          },
        ],
      },
      selectDetailItem: {
        EntityName: "",
      },
      detailTableData: [],
      detailSearchData: {
        ID: "",
        BillType: "",
      },
      detailPaginations: {
        page: 1, // 当前位于哪页
        total: 0, // 总数
        page_size: 10, // 1页显示多少条
        layout: "total, prev, pager, next, jumper", // 翻页属性
      }, //需要给分页组件传的信息
    };
  },
  /**计算属性  */
  computed: {},
  /**  方法集合  */
  methods: {
    /**    */
    handleSearchEvents() {
      let that = this;
      that.paginations.page = 1;
      that.list_payment();
    },
    /**  充值  */
    prepayRecharge(row) {
      let that = this;
      that.addRuleForm = {
        BillType: "10", //10：预付余额账户付款，20：预付余额账户退款，30：要货预付余额账户付款
        EntityID: "", //门店编号
        EntityName: "",
        PaymentCategoryID: "", //分类编号
        Balance: "", //本金
        LargessBalance: "", //赠额
        PaymentWay: "10", //付款方式（10：离线转账，20：余额支付）
        ReceiptNumber: "", //回单号码（离线转账填写）
        PaymentAccountName: "", //付款户名（离线转账填写）
        Remark: "", //备注（离线转账填写）
      };
      that.addRuleForm.EntityID = row.EntityID;
      that.addRuleForm.EntityName = row.EntityName;
      that.isAdd = true;
      that.dialogVisible = true;
    },
    /**   退款 */
    prepayRefund(row) {
      let that = this;
      (that.refundRuleForm = {
        BillType: "20", //10：预付余额账户付款，20：预付余额账户退款，30：要货预付余额账户付款
        EntityID: row.EntityID, //门店编号
        EntityName: row.EntityName,
        PaymentCategoryID: "", //分类编号
        Balance: "", //本金
        Balance_all: row.Balance, //本金
        LargessBalance: "", //赠额
        LargessBalance_all: row.LargessBalance, //赠额
        PaymentWay: "10", //付款方式（10：离线转账，20：余额支付）
        ReceiptNumber: "", //回单号码（离线转账填写）
        PaymentAccountName: "", //付款户名（离线转账填写）
        Remark: "", //备注（离线转账填写）
      }),
        (that.isAdd = false);
      that.refundDialogVisible = true;
    },
    /**  退款修改本金金额  */
    changeBalance(val) {
      let that = this;
      if (val > that.refundRuleForm.Balance_all) {
        that.refundRuleForm.Balance = that.refundRuleForm.Balance_all;
        that.$message.error("退款金额不可以大于剩余金额");
      }
    },
    /**  退款修改赠金金额  */
    changeLargessBalance(val) {
      let that = this;
      if (val > that.refundRuleForm.LargessBalance_all) {
        that.refundRuleForm.LargessBalance = that.refundRuleForm.LargessBalance_all;
        that.$message.error("退款赠额不可以大于剩余赠额");
      }
    },
    /**   修改分页 */
    currentChange(page) {
      let that = this;
      that.paginations.page = page;
      that.list_payment();
    },
    /**  保存  */
    submitprepayRechargeClick() {
      let that = this;
      that.$refs.addRuleForm.validate((valid) => {
        if (valid) {
          that.create_payment();
        }
      });
    },

    /**  退款  */
    submitprepayRefundClick() {
      let that = this;
      that.$refs.refundRuleForm.validate((valid) => {
        if (valid) {
          that.refund_payment();
        }
      });
    },
    /**    */
    checek_contactDetail(row) {
      let that = this;
      that.selectDetailItem = row;
      that.payment_contactDetail();
    },
    /**  修改明细分页  */
    currentDetailChange(page) {
      let that = this;
      that.detailPaginations.page = page;
      that.payment_contactDetail();
    },
    /**    */
    billTypeFormatter(row) {
      switch (row.BillType) {
        case "10":
          return "预付款单";
        case "20":
          return "退款单";
        case "30":
          return "要货付款单";
        case "40":
          return "退货付款单";
      }
    },
    /**    */
    sourceBillTypeFormatter(row) {
      switch (row.sourceBillTypeFormatter) {
        case "10":
          return "要货单";
        case "20":
          return "退货单";
      }
    },
    /**    */
    handleSearchEventsDetail() {
      let that = this;
      that.detailPaginations.page = 1;
      that.payment_contactDetail();
    },

    /************************************/
    /**    */
    async list_payment() {
      let that = this;
      let params = {
        PageNum: that.paginations.page,
        EntityName: that.searchEntityName, //门店名称搜索
      };
      that.loading = true;
      let res = await API.list_payment(params);
      if (res.StateCode == 200) {
        that.tableData = res.List;
        that.paginations.total = res.Total;
      } else {
        that.$message.error(res.Message);
      }
      that.loading = false;
    },
    /**    */
    async create_payment() {
      let that = this;
      let params = Object.assign({}, that.addRuleForm);
      if (!that.addRuleForm.LargessBalance) {
        params.LargessBalance = 0;
      }
      let res = await API.create_payment(params);
      if (res.StateCode == 200) {
        that.$refs.addRuleForm.resetFields();
        that.list_payment();
        that.$message.success("充值成功");
        that.dialogVisible = false;
      } else {
        that.$message.error(res.Message);
      }
    },
    /**    */
    async refund_payment() {
      let that = this;
      let params = Object.assign({}, that.refundRuleForm);
      if (params.LargessBalance == "") {
        params.LargessBalance = 0;
      }
      let res = await API.refund_payment(params);
      if (res.StateCode == 200) {
        that.$refs.refundRuleForm.resetFields();
        that.list_payment();
        that.$message.success("退款成功");
        that.refundDialogVisible = false;
      } else {
        that.$message.error(res.Message);
      }
    },

    /**  分类  列表请求  */
    async prepayCategory_list() {
      let that = this;
      let params = {
        Name: "", //类别名称
        Active: true, //有效性
        PageNum: that.catePage,
      };
      that.loading = true;
      let res = await API.prepayCategory_list(params);
      if (res.StateCode == 200) {
        that.prepayCategorys = res.List;
        that.catePageTotal = res.Total;
      } else {
        that.$message.error(res.Message);
      }
      that.loading = false;
    },
    /**    */
    async payment_contactDetail() {
      let that = this;
      that.detailLoading = true;
      try {
        let params = {
          PageNum: that.detailPaginations.page,
          EntityID: that.selectDetailItem.EntityID, //门店编号
          ID: that.detailSearchData.ID, //订单号
          BillType: that.detailSearchData.BillType, //10：预付余额账户付款，20：预付余额账户退款，30：要货预付余额账户付款，40：退货付款单
        };
        let res = await API.payment_contactDetail(params);
        if (res.StateCode == 200) {
          that.detailTableData = res.List;
          that.detailPaginations.total = res.Total;
          that.detailDialogVisible = true;
        } else {
          that.$message.error(res.Message);
        }
        that.detailLoading = false;
      } catch (error) {
        that.detailLoading = false;
        that.$message.error(error);
      }
    },

    /**    */
    async payment_excelPayment_excel() {
      let that = this;
      that.execlLoading = true;
      let params = {
        PageNum: that.paginations.page,
        EntityName: that.searchEntityName, //门店名称搜索
      };
      let res = await API.payment_excelPayment_excel(params);

      this.$message.success({ message: "正在导出", duration: "4000" });
      const link = document.createElement("a");
      let blob = new Blob([res], { type: "application/octet-stream" });
      link.style.display = "none";
      link.href = URL.createObjectURL(blob);
      link.download = "预付款管理.xlsx"; //下载的文件名
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      that.execlLoading = false;
    },
  },
  /** 监听数据变化   */
  watch: {},
  /** 创建实例之前执行函数   */
  beforeCreate() {},
  /**  实例创建完成之后  */
  created() {
    let that = this;
    that.list_payment();
    that.prepayCategory_list();
  },
  /**  在挂载开始之前被调用  */
  beforeMount() {},
  /**  实例被挂载后调用  */
  mounted() {},
  /**  数据更新 之前 调用  */
  beforeUpdate() {},
  /** 数据更新 完成 调用   */
  updated() {},
  /**  实例销毁之前调用  */
  beforeDestroy() {},
  /**  实例销毁后调用  */
  destroyed() {},
};
</script>

<style lang="scss">
.prepay {
  .custom_input {
    .el-input__inner {
      padding: 0 0 0 10px;
    }
  }
}
</style>
