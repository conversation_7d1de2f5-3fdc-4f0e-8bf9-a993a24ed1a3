<template>
  <div class="treatConsumable">
    <el-col :span="24" class="padbm_0" v-show="consumableList.length > 0">
      <el-row class="consumableProduct_header">
        <el-col :span="5">耗材名称</el-col>
        <el-col :span="3">规格</el-col>
        <el-col :span="3">品牌</el-col>
        <el-col :span="3">库存</el-col>
        <el-col :span="4">仓库</el-col>
        <el-col :span="4">数量</el-col>
        <el-col :span="2" class="text_right">
          <!-- <el-link type="primary" :underline="false" @click="addConsumableClick" v-prevent-click>添加耗材</el-link> -->
          <el-link type="primary" :underline="false" @click="removeAllTreatConsumableClick" v-prevent-click>清空耗材</el-link>
        </el-col>
      </el-row>
      <el-row v-for="(item, index) in consumableList" :key="index" class="pad_5_10 border_bottom" type="flex" align="middle">
        <el-col :span="5">{{ item.ProductName }}</el-col>
        <el-col :span="3">{{ item.Specification }}</el-col>
        <el-col :span="3">{{ item.BrandName }}</el-col>
        <el-col :span="3">
          <span>
            {{ item.StockQuantity || 0 }}
            {{ item.MinimumUnitName }}
          </span>
        </el-col>
        <el-col :span="4">
          <el-select v-model="item.EntityID" @change="changeEntity(index, item)" size="small" class="marrt_15" placeholder="请选择仓库">
            <el-option v-for="entity in consumableEntityList" :key="entity.EntityID" :value="entity.EntityID" :label="entity.EntityName"></el-option>
          </el-select>
        </el-col>
        <el-col :span="4" class="padrt_15">
          <el-input v-model="item.Quantity" size="small" v-input-fixed="0">
            <el-button slot="append">{{ item.MinimumUnitName }}</el-button>
          </el-input>
        </el-col>
        <el-col :span="2" class="text_right">
          <el-button type="danger" icon="el-icon-delete" circle size="mini" @click="removeTreatConsumableClick(index)"></el-button>
        </el-col>
      </el-row>
    </el-col>
    <!-- 添加耗材 -->
    <el-dialog title="添加耗材" :visible.sync="consumableVisible" append-to-body custom-class="addConsumable">
      <el-tabs v-model="activeName">
        <el-tab-pane v-if="templateProjectList.length > 0" label="耗材模板" name="template">
          <el-table height="500px" :show-header="false" :data="templateProjectList">
            <el-table-column type="expand">
              <template slot-scope="scope">
                <el-table size="mini" :data="scope.row.Product" style="margin-left: 48px; width: 870px">
                  <el-table-column label="耗材名称" prop="ProductName"> </el-table-column>
                  <el-table-column label="规格" prop="Specification"> </el-table-column>
                  <el-table-column label="品牌" prop="BrandName"> </el-table-column>
                  <el-table-column label="数量" prop="MinimumUnitQuantity">
                    <template slot-scope="scope">
                      <div>{{ scope.row.MinimumUnitQuantity }} {{ scope.row.MinimumUnitName }}</div>
                    </template>
                  </el-table-column>
                </el-table>
              </template>
            </el-table-column>
            <el-table-column label="模板名称" prop="Name">
              <template slot-scope="scope">
                <div class="font_weight_600">{{ scope.row.Name }}</div>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="100px">
              <template slot-scope="scope">
                <el-button @click="addTmplateProductClick(scope.row)" v-prevent-click type="primary" size="small">选择模板</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
        <el-tab-pane label="所有耗材" name="allProduct">
          <el-row type="flex" align="middle" :gutter="20" class="marbm_10">
            <el-col :span="6">
              <el-input @change="searchProductClick" v-model="productName" size="small" placeholder="请输入耗材名称称搜索" clearable></el-input>
            </el-col>
            <el-col :span="18">
              <el-button @click="searchProductClick" type="primary" size="small" v-prevent-click>搜索</el-button>
            </el-col>
          </el-row>
          <el-table size="small" ref="table" :data="productList" @selection-change="changeSelectProduct" @cell-click="tableCellClick">
            <el-table-column type="selection" width="55"></el-table-column>
            <el-table-column prop="ProductName" label="名称"></el-table-column>
            <el-table-column prop="Specification" label="规格"></el-table-column>
            <el-table-column prop="BrandName" label="品牌"></el-table-column>
          </el-table>

          <div class="text_right pad_15">
            <el-pagination background v-if="paginations.total > 0" @current-change="handleCurrentChange" :current-page.sync="paginations.page" :page-size="paginations.page_size" :layout="paginations.layout" :total="paginations.total"></el-pagination>
          </div>
        </el-tab-pane>
      </el-tabs>
      <span slot="footer">
        <el-button size="small" @click="consumableVisible = false">取消</el-button>
        <el-button type="primary" @click="addProductClick" v-prevent-click size="small">确定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import API from "@/api/iBeauty/Order/treatConsumable.js";
export default {
  name: "treatConsumable",
  props: {
    /* 列表值  */
    consumableList: {
      type: Array,
      default() {
        return [];
      },
    },
    projectID: {
      type: Number,
    },

    consumableEntityList: {
      type: Array,
      default() {
        return [];
      },
    },
  },
  /** 监听数据变化   */
  watch: {},
  /**  引入的组件  */
  components: {},
  /**  Vue 实例的数据对象**/
  data() {
    return {
      consumableVisible: false,
      activeName: "template",
      number: "",
      value: "",
      templateProjectList: [],
      productList: [],
      productName: "",
      tmpSelectProductList: [],

      paginations: {
        page: 1, // 当前位于哪页
        total: 0, // 总数
        page_size: 10, // 1页显示多少条
        layout: "total, prev, pager, next,jumper", // 翻页属性
      },
    };
  },
  /**计算属性  */
  computed: {},
  /**  方法集合  */
  methods: {
    /**  选择全部耗材  */
    changeSelectProduct(selection) {
      let that = this;
      that.tmpSelectProductList = selection;
    },
    /**  表格点击    */
    tableCellClick(row) {
      let that = this;
      that.$refs.table.toggleRowSelection(row);
    },
    /**  删除事件  */
    removeTreatConsumableClick(index) {
      let that = this;
      that.$emit("remove", index);
    },
    /**  删除所有耗材事件  */
    removeAllTreatConsumableClick() {
      let that = this;
      that.$emit("removeAll");
    },
    /**   添加耗材 */
    addConsumableClick() {
      let that = this;
      that.consumableVisible = true;
      that.activeName = "template";
      that.treatBill_templateByProject();
      that.treatBill_product();
    },
    /**   添加 全部商品保存 */
    addProductClick() {
      let that = this;
      let EntityID ="";
      if(that.consumableEntityList.length ==1){
        EntityID = that.consumableEntityList[0].EntityID;
      }
      let tmp = that.tmpSelectProductList.map((i) => {
        return {
          EntityID: EntityID, //门店ID
          ProductID: i.ProductID, //产品编号
          ProductName: i.ProductName, //产品编号
          MinimumUnitID: i.MinimumUnitID, //最小包装单位id
          MinimumUnitName: i.MinimumUnitName,
          Quantity: "", //出库数量
          Specification: i.Specification, // 规格
          BrandName: i.BrandName, // 品牌
          StockQuantity: "", // 产品库存
        };
      });

      that.$emit("add", tmp);
      that.consumableVisible = false;
    },
    /**  添加模板   */
    addTmplateProductClick(item) {
      let that = this;
      let EntityID ="";
      if(that.consumableEntityList.length ==1){
        EntityID = that.consumableEntityList[0].EntityID;
      }
      let tmp = item.Product.map((i) => {
        return {
          EntityID: EntityID, //门店ID
          ProductID: i.ProductID, //产品编号
          ProductName: i.ProductName, //产品编号
          MinimumUnitID: i.MinimumUnitID, //最小包装单位id
          MinimumUnitName: i.MinimumUnitName,
          Quantity: i.MinimumUnitQuantity, //出库数量
          Specification: i.Specification, // 规格
          BrandName: i.BrandName, // 品牌
          StockQuantity: "", // 产品库存
        };
      });

      that.$emit("add", tmp);
      that.consumableVisible = false;
    },
    /**  搜索全部耗材  */
    searchProductClick() {
      let that = this;
      that.paginations.page = 1;
      that.treatBill_product();
    },
    /**  修改分页  */
    handleCurrentChange(page) {
      let that = this;
      that.paginations.page = page;
      that.treatBill_product();
    },
    /**   修改门店 或者去 产品库存 */
    changeEntity(index, item) {
      let that = this;
      that.treatBill_productQuantity(index, item);
    },
    /**  ——————————————————————————————————————————————————————  */
    /**  消耗项目-适用的模板   */
    async treatBill_templateByProject() {
      let that = this;
      let params = {
        ProjectID: that.projectID, //项目ID
      };
      let res = await API.treatBill_templateByProject(params);
      if (res.StateCode == 200) {
        that.templateProjectList = res.Data;
        if (!that.templateProjectList || that.templateProjectList.length == 0) {
          that.activeName = "allProduct";
        }
      } else {
        that.$message.error(res.Message);
      }
    },

    /**  所有耗材  */
    async treatBill_product() {
      let that = this;
      let params = {
        PageNum: that.paginations.page,
        Name: that.productName, //模糊搜索
      };
      let res = await API.treatBill_product(params);
      if (res.StateCode == 200) {
        that.productList = res.List;
        that.paginations.total = res.Total;
      } else {
        that.$message.error(res.Message);
      }
    },

    /**  门店列表  */
    // async treatBill_productEntity() {
    //   let that = this;
    //   let params = {};
    //   let res = await API.treatBill_productEntity(params);
    //   if (res.StateCode == 200) {
    //     that.consumableEntityList = res.Data;
    //   } else {
    //     that.$message.error(res.Message);
    //   }
    // },

    /**  产品库存  */
    async treatBill_productQuantity(index, item) {
      let that = this;
      let params = {
        ProductID: item.ProductID, //产品编号
        EntityID: item.EntityID, //门店标号
      };
      let res = await API.treatBill_productQuantity(params);
      if (res.StateCode == 200) {
        that.$nextTick(() => {
          if (!res.Data) {
            that.$set(item, "StockQuantity", 0);
          } else {
            that.$set(item, "StockQuantity", res.Data);
          }
        });
      } else {
        that.$message.error(res.Message);
      }
    },
  },
  /** 创建实例之前执行函数   */
  beforeCreate() {},
  /**  实例创建完成之后  */
  created() {},
  /**  在挂载开始之前被调用  */
  beforeMount() {},
  /**  实例被挂载后调用  */
  mounted() {
    // this.treatBill_productEntity();
  },
  /**  数据更新 之前 调用  */
  beforeUpdate() {},
  /** 数据更新 完成 调用   */
  updated() {},
  /**  实例销毁之前调用  */
  beforeDestroy() {},
  /**  实例销毁后调用  */
  destroyed() {},
};
</script>

<style lang="scss">
.treatConsumable {
  .consumableProduct_header {
    background-color: #f5f7fa;
    padding: 10px;
  }
}
.addConsumable {
  .temp_header {
    background-color: var(--zl-color-orange-primary-header);
  }
  .sub_header {
    background-color: var(--zl-color-subheader);
  }

  .el-scrollbar_height {
    height: 60vh;
    .el-scrollbar__wrap {
      overflow-x: hidden;
    }
  }
}
</style>
