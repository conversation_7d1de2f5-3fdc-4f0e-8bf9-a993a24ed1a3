<template>
  <div class="FollowUpConfig content_body_nopadding" v-loading="loading">
    <el-tabs v-model="activeIndex" @tab-click="handleClick" type="border-card">
      <el-tab-pane label="跟进方式" name="0">
        <div class="nav_header">
          <el-row>
            <el-col :span="20">
              <el-form :inline="true" size="small">
                <el-form-item label="跟进方式">
                  <el-input v-model="Name" placeholder="输入跟进方式" clearable @clear="search" @keyup.enter.native="search"></el-input>
                </el-form-item>
                <el-form-item label="有效性">
                  <el-select v-model="Active" placeholder="请选择有效性" @change="search" clearable>
                    <el-option label="有效" :value="true"></el-option>
                    <el-option label="无效" :value="false"></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" @click="search" size="small" v-prevent-click>搜索</el-button>
                </el-form-item>
              </el-form>
            </el-col>
            <el-col :span="4" class="text_right">
              <el-button type="primary" size="small" v-prevent-click @click="showAddDialog">新增</el-button>
            </el-col>
          </el-row>
        </div>
        <el-table size="small" :data="tableDataMethod" style="width: 100%">
          <el-table-column prop="Name" label="跟进方式">
          </el-table-column>
          <el-table-column label="移动" min-width="180px">

            <template slot-scope="scope">
              <el-button size="small" type="primary" circle icon="el-icon-upload2" @click="upOneClick(scope.row, scope.$index)" v-prevent-click :disabled="scope.$index==0"></el-button>
              <el-button size="small" type="primary" circle icon="el-icon-top" @click="upClick(scope.row, scope.$index)" v-prevent-click :disabled="scope.$index==0"></el-button>
              <el-button size="small" type="primary" circle icon="el-icon-bottom" @click="downClick(scope.row, scope.$index)" v-prevent-click :disabled="scope.$index == tableDataMethod.length-1"></el-button>
              <el-button size="small" type="primary" circle icon="el-icon-download" @click="downOneClick(scope.row, scope.$index)" v-prevent-click :disabled="scope.$index == tableDataMethod.length-1"></el-button>
            </template>

          </el-table-column>
          <el-table-column prop="Active" label="有效性" width="280" :formatter="status">
          </el-table-column>
          <el-table-column prop="date" label="操作" width="80">
            <template slot-scope="scope">
              <el-button type="primary" size="small" @click="editEditDialog(scope.row)">编辑</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-tab-pane>
      <el-tab-pane label="跟进状态" name="1">
        <div class="nav_header">
          <el-row>
            <el-col :span="20">
              <el-form :inline="true" size="small">
                <el-form-item label="跟进状态">
                  <el-input v-model="Name2" placeholder="输入跟进状态" clearable @clear="search" @keyup.enter.native="search"></el-input>
                </el-form-item>
                <el-form-item label="有效性">
                  <el-select v-model="Active2" placeholder="请选择有效性" @change="search" clearable>
                    <el-option label="有效" :value="true"></el-option>
                    <el-option label="无效" :value="false"></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item>
                  <el-button size="small" type="primary" @click="search" v-prevent-click>搜索</el-button>
                </el-form-item>
              </el-form>
            </el-col>
            <el-col :span="4" class="text_right">
              <el-button type="primary" size="small" v-prevent-click @click="showAddDialog">新增</el-button>
            </el-col>
          </el-row>
        </div>
        <el-table size="small" :data="tableDataStatus">
          <el-table-column prop="Name" label="跟进状态">
          </el-table-column>
          <el-table-column label="移动" min-width="180px">

            <template slot-scope="scope">
              <el-button size="small" type="primary" circle icon="el-icon-upload2" @click="upOneClick(scope.row, scope.$index)" v-prevent-click :disabled="scope.$index==0"></el-button>
              <el-button size="small" type="primary" circle icon="el-icon-top" @click="upClick(scope.row, scope.$index)" v-prevent-click :disabled="scope.$index==0"></el-button>
              <el-button size="small" type="primary" circle icon="el-icon-bottom" @click="downClick(scope.row, scope.$index)" v-prevent-click :disabled="scope.$index == tableDataStatus.length-1"></el-button>
              <el-button size="small" type="primary" circle icon="el-icon-download" @click="downOneClick(scope.row, scope.$index)" v-prevent-click :disabled="scope.$index == tableDataStatus.length-1"></el-button>
            </template>

          </el-table-column>
          <el-table-column prop="Active" label="有效性" width="280" :formatter="status">
          </el-table-column>
          <el-table-column prop="date" label="操作" width="80">
            <template slot-scope="scope">
              <el-button type="primary" size="small" @click="editEditDialog(scope.row)">编辑</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-tab-pane>
    </el-tabs>

    <!--   新增跟进方式-->
    <el-dialog :title='`新增${dialogTitle}`' :visible.sync="dialogVisible" width="450px">
      <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="auto" class="demo-ruleForm" size="small" @submit.native.prevent>
        <el-form-item :label="dialogTitle" prop="Name">
          <el-input v-model="ruleForm.Name" :placeholder='`请输入${dialogTitle}名称`'></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button size="small" @click="onClose">取 消</el-button>
        <el-button type="primary" size="small" :loading="modalLoading" @click="submitForm('ruleForm')" v-prevent-click>保 存</el-button>
      </span>
    </el-dialog>

    <!--编辑-->
    <el-dialog :title='`编辑${editDialogTitle}`' :visible.sync="editDialogVisible" width="450px">
      <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="auto" class="demo-ruleForm" size="small" @submit.native.prevent>
        <el-form-item :label='`${editDialogTitle}名称`' prop="Name">
          <el-input v-model="ruleForm.Name" :placeholder='`请输入${editDialogTitle}`'></el-input>
        </el-form-item>
        <el-form-item label="是否有效" prop="Active">
          <el-radio-group v-model="ruleForm.Active">
            <el-radio :label="true">是</el-radio>
            <el-radio :label="false">否</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="onClose" size="small" v-prevent-click>取 消</el-button>
        <el-button type="primary" size="small" :loading="modalLoading" @click="editSubmitForm('ruleForm')" v-prevent-click>保 存</el-button>
      </span>
    </el-dialog>

  </div>
</template>


<script>
import API from "@/api/KHS/Setting/followUpConfig.js";
export default {
  name: "FollowUpConfig",

  props: {},
  /**  引入的组件  */
  components: {},
  /**  Vue 实例的数据对象**/
  data() {
    return {
      loading: false,
      modalLoading: false, //loading
      dialogVisible: false, // 新增跟进弹框
      editDialogVisible: false, // 编辑跟进弹框
      dialogTitle: "", // 新增跟进弹框标题
      editDialogTitle: "", // 编辑跟进弹框标题
      activeIndex: "0",
      Name: "",
      Active: true,
      Name2: "",
      Active2: true,
      ruleForm: {
        Name: "",
        Active: "",
      },
      tableDataMethod: [], //跟进方式列表数据
      tableDataStatus: [], //跟进类型列表数据
      rules: {
        Name: [{ required: true, message: "请输入名称", trigger: "blur" }],
      },
    };
  },
  /**计算属性  */
  computed: {},
  /**  方法集合  */
  methods: {
    status(row) {
      return row.Active ? "有效" : "无效";
    },
    // tab 切换
    handleClick(e) {
      this.activeIndex = e.index;
    },
    // 搜索
    search() {
      var that = this;
      if (that.activeIndex == 0) {
        that.getFollowUpMethod();
      } else {
        that.getFollowUpStatus();
      }
    },
    // 新增按钮点击事件
    showAddDialog() {
      var that = this;
      that.ruleForm = {
        Name: "", // 新增名称
        Active: true, // 是否有效
      };
      if (that.activeIndex == 0) {
        that.dialogTitle = "跟进方式";
      } else if (that.activeIndex == 1) {
        that.dialogTitle = "跟进状态";
      }
      that.dialogVisible = true;
    },

    // 增加按钮点击事件
    submitForm(form) {
      var that = this;
      that.$refs[form].validate((valid) => {
        if (valid) {
          that.modalLoading = true;
          let para = Object.assign({}, that.ruleForm);
          if (that.activeIndex == 0) {
            that.createFollowUpMethod(para);
          } else {
            that.createFollowUpStatus(para);
          }
        }
      });
    },

    // 获取跟进方式列表
    getFollowUpMethod() {
      var that = this;
      var params = {
        Name: that.Name,
        Active: that.Active,
      };
      API.getFollowUpMethod(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.tableDataMethod = res.Data;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },
    // 获取跟进类型列表
    getFollowUpStatus() {
      var that = this;
      var params = {
        Name: that.Name2,
        Active: that.Active2,
      };
      API.getFollowUpStatus(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.tableDataStatus = res.Data;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },

    // 跟进方式新建
    createFollowUpMethod(params) {
      let that = this;
      API.createFollowUpMethod(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.$message.success({
              message: "新增成功",
              duration: 2000,
            });
            that.getFollowUpMethod();
            that.$refs["ruleForm"].resetFields();
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.dialogVisible = false;
          that.modalLoading = false;
        });
    },
    // 跟进类型新建
    createFollowUpStatus(params) {
      let that = this;
      API.createFollowUpStatus(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.$message.success({
              message: "新增成功",
              duration: 2000,
            });
            that.getFollowUpStatus();
            that.$refs["ruleForm"].resetFields();
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.dialogVisible = false;
          that.modalLoading = false;
        });
    },

    // 跟进方式编辑保存
    updateFollowUpMethod(params) {
      let that = this;
      API.updateFollowUpMethod(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.$message.success({
              message: "编辑成功",
              duration: 2000,
            });
            that.getFollowUpMethod();
            that.$refs["ruleForm"].resetFields();
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.editDialogVisible = false;
          that.modalLoading = false;
        });
    },
    // 跟进类型编辑保存
    updateFollowUpStatus(params) {
      let that = this;
      API.updateFollowUpStatus(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.$message.success({
              message: "编辑成功",
              duration: 2000,
            });
            that.getFollowUpStatus();
            that.$refs["ruleForm"].resetFields();
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.editDialogVisible = false;
          that.modalLoading = false;
        });
    },

    // 移动首部
    upOneClick: function (row) {
      var that = this;
      if (that.activeIndex == 0) {
        that.moveFollowUpMethod(row.ID, "");
      } else {
        that.moveFollowUpStatus(row.ID, "");
      }
    },
    // 移动尾部
    downOneClick: function (row, index) {
      var that = this;
      if (that.activeIndex == 0) {
        let tabIndex = that.tableDataMethod.length;
        let beforeId = "";
        if (index < tabIndex - 1) {
          beforeId = that.tableDataMethod[tabIndex - 1].ID;
        }
        that.moveFollowUpMethod(row.ID, beforeId);
      } else {
        let tabIndex = that.tableDataStatus.length;
        let beforeId = "";
        if (index < tabIndex - 1) {
          beforeId = that.tableDataStatus[tabIndex - 1].ID;
        }
        that.moveFollowUpStatus(row.ID, beforeId);
      }
    },
    // 向上
    upClick: function (row, index) {
      var that = this;
      if (that.activeIndex == 0) {
        let beforeId = "";
        if (index > 1) {
          beforeId = that.tableDataMethod[index - 2].ID;
        }
        that.moveFollowUpMethod(row.ID, beforeId);
      } else {
        let beforeId = "";
        if (index > 1) {
          beforeId = that.tableDataStatus[index - 2].ID;
        }
        that.moveFollowUpStatus(row.ID, beforeId);
      }
    },
    // 向下
    downClick: function (row, index) {
      var that = this;
      if (that.activeIndex == 0) {
        let beforeId = "";
        if (index + 1 != that.tableDataMethod.length) {
          beforeId = that.tableDataMethod[index + 1].ID;
        }
        that.moveFollowUpMethod(row.ID, beforeId);
      } else {
        let beforeId = "";
        if (index + 1 != that.tableDataStatus.length) {
          beforeId = that.tableDataStatus[index + 1].ID;
        }
        that.moveFollowUpStatus(row.ID, beforeId);
      }
    },
    // 移动
    // 跟进方式移动
    moveFollowUpMethod(moveId, beforeId) {
      var that = this;
      that.loading = true;
      var params = {
        MoveID: moveId,
        BeforeID: beforeId,
      };
      API.moveFollowUpMethod(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.$message.success({
              message: "移动成功",
              duration: 2000,
            });
            that.getFollowUpMethod();
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },
    // 跟进类型移动
    moveFollowUpStatus(moveId, beforeId) {
      var that = this;
      that.loading = true;
      var params = {
        MoveID: moveId,
        BeforeID: beforeId,
      };
      API.moveFollowUpStatus(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.$message.success({
              message: "移动成功",
              duration: 2000,
            });
            that.getFollowUpStatus();
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },

    // 编辑
    editEditDialog(row) {
      var that = this;
      that.editDialogVisible = true;
      if (that.activeIndex == "0") {
        that.editDialogTitle = "跟进方式";
      } else {
        that.editDialogTitle = "跟进状态";
      }
      that.ruleForm = Object.assign({}, row);
    },
    // 编辑按钮点击事件
    editSubmitForm(form) {
      var that = this;
      that.$refs[form].validate((valid) => {
        if (valid) {
          that.modalLoading = true;
          let para = Object.assign({}, that.ruleForm);
          if (that.activeIndex == 0) {
            that.updateFollowUpMethod(para);
          } else {
            that.updateFollowUpStatus(para);
          }
        }
      });
    },

    onClose() {
      this.dialogVisible = false;
      this.editDialogVisible = false;
      // this.$refs["ruleForm"].clearValidate();
      this.$refs.ruleForm.resetFields();
    },
  },
  /** 监听数据变化   */
  watch: {},
  /** 创建实例之前执行函数   */
  beforeCreate() {},
  /**  实例创建完成之后  */
  created() {},
  /**  在挂载开始之前被调用  */
  beforeMount() {},
  /**  实例被挂载后调用  */
  mounted() {
    this.getFollowUpMethod();
    this.getFollowUpStatus();
  },
  /**  数据更新 之前 调用  */
  beforeUpdate() {},
  /** 数据更新 完成 调用   */
  updated() {},
  /**  实例销毁之前调用  */
  beforeDestroy() {},
  /**  实例销毁后调用  */
  destroyed() {},
};
</script>

<style lang="scss">
.FollowUpConfig {
  .el-tabs--border-card {
    border: 0px !important;
    box-shadow: 0 0px 0px 0 rgba(0, 0, 0, 0), 0 0 0px 0 rgba(0, 0, 0, 0);
  }
  .el-form-item--mini.el-form-item,
  .el-form-item--small.el-form-item {
    margin-bottom: 15px;
  }
}
</style>
