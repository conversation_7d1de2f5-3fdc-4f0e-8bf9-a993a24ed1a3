<!-- /**  销售订单详情内容 */ --> 
<template>
  <section class="saleBillDetailContent">
    <div>
      <!-- 订单信息 -->
      <div>
        <div class="tip marbm_10" style="margin-top: 0">订单信息</div>
        <el-form label-width="100px" class="saleInfo" size="small">
          <el-row>
            <el-col :span="8">
              <el-form-item label="订单编号:">{{ saleOrderDetail.ID }}</el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="订单类型:">{{ getBillType(saleOrderDetail.BillType) }}</el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="订单状态:">{{ getBillState(saleOrderDetail.BillStatus) }}</el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="8">
              <el-form-item label="客户信息:">
                {{ saleOrderDetail.Name }}
                <span v-if="saleOrderDetail.PhoneNumber != null">({{ saleOrderDetail.PhoneNumber | hidephone }})</span>
                <el-popover placement="right" width="400" trigger="click" v-model="changeMemberDialogVisible">
                  <div class="font_13 color_333 marbm_10">请选择顾客</div>

                  <el-autocomplete popper-class="customer-autocomplete-bill" prefix-icon="el-icon-user-solid"
                    v-model="customerName" style="width: 100%" size="small" placeholder="请输入客户姓名、手机号、编号查找"
                    :fetch-suggestions="saleCustomerData" @select="handleCustomerSelect" :disabled="customerID != null"
                    :trigger-on-focus="false" :hide-loading="false" :highlight-first-item="true"
                    :select-when-unmatched="true">
                    <template slot="append">
                      <el-button icon="el-icon-delete" @click="removeCustomer"></el-button>
                    </template>
                    <template slot-scope="{ item }">
            <div class="name">
              {{ item.Name }}
              <el-tag size="mini" v-if="item.CustomerLevelName">{{ item.CustomerLevelName }}</el-tag>
            </div>
            <div class="info">手机号：{{ item.PhoneNumber | hidephone }}</div>
            <div class="info" v-if="item.Code">客户编号：{{ item.Code }}</div>
            <div class="info" v-if="item.EntityName">所属组织：{{ item.EntityName }}</div>
            <div class="info" v-if="item.ChannelName">渠道信息：{{ item.ChannelName }}</div>
                    </template>
                  </el-autocomplete>

                  <div class="text_right pad_10 martp_10">
                    <el-button class="marrt_10" size="mini" @click="changeMemberDialogVisible = false">取 消</el-button>
                    <el-button type="primary" size="mini" @click="updateSaleBillBindCutsomer">保 存</el-button>
                  </div>

                  <el-button @click="removeCustomer" slot="reference" class="ml_5"
                    v-if="saleOrderDetail.CustomerID == null" type="text" size="small">修改顾客</el-button>
                </el-popover>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="开单人:">{{ saleOrderDetail.EmployeeName }}</el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="订单金额:">￥{{ saleOrderDetail.Amount | toFixed | NumFormat }}</el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="8">
              <el-form-item label="欠款金额:">￥{{ saleOrderDetail.ArrearAmount | toFixed | NumFormat }}{{
                saleOrderDetail.Repayment > 0 ? saleOrderDetail.Repayment : "" }}</el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="下单时间:">
                <span class="marrt_5">{{ saleOrderDetail.BillDate | dateFormat("YYYY-MM-DD HH:mm") }}</span>
                <el-button
                  v-if="isModifyBillDate && !limitSealingAccount(saleOrderDetail.BillDate, ModifyBillDateRestriction)"
                  type="text" @click="ModifyBillDateClick">修改</el-button>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="录单时间:">{{ saleOrderDetail.CreatedOn | dateFormat("YYYY-MM-DD HH:mm") }}</el-form-item>
            </el-col>
          </el-row>
          <el-row v-if="saleOrderDetail.BillStatus == 30">
            <el-col :span="8">
              <el-form-item label="取消时间:">{{ saleOrderDetail.CancelCreatedOn | dateFormat("YYYY-MM-DD HH:mm")
              }}</el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="取消人:">{{ saleOrderDetail.CancelCreatedBy }}</el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="取消原因:">
                <span class="marrt_5">{{ saleOrderDetail.CancelRemark }}</span>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="8">
              <el-form-item label="订单备注:">
                <span v-if="saleOrderDetail.Remark" class="marrt_5">{{ saleOrderDetail.Remark }}</span>
                <el-button type="text" @click="ChangeRemarkinnerVisible">修改备注</el-button>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row></el-row>
        </el-form>
      </div>
      <!-- 消费信息 -->
      <div class="martp_10">
        <div class="tip marbm_10" style="margin-top: 0">消费信息</div>
        <div class="border_left border_right border_top">
          <!-- 项目 -->
          <div v-if="saleOrderDetail.Project != undefined && saleOrderDetail.Project.length > 0">
            <el-row class="row_header">
              <el-col :span="8">项目</el-col>
              <el-col :span="3">数量</el-col>
              <el-col :span="5">优惠金额</el-col>
              <el-col :span="4">欠款金额</el-col>
              <el-col :span="4">小计</el-col>
            </el-row>
            <el-row v-for="(item, index) in saleOrderDetail.Project" :key="index + 'x1'">
              <el-col :span="24" class="pad_10 border_bottom">
                <el-col :span="24">
                  <el-col :span="8">
                    <div>
                      {{ item.ProjectName }}
                      <span v-if="item.Alias">({{ item.Alias }})</span>
                      <el-tag v-if="item.IsLargess" size="mini" type="danger" class="marlt_10">赠</el-tag>
                      <span v-if="isRemark" class="marlt_5 color_primary cursor_pointer">
                        <el-button icon="el-icon-edit-outline" type="text" style="padding: unset"
                          @click="changeRemarkClick(item, 'updateProjectRemark')"></el-button>
                      </span>
                    </div>
                  </el-col>
                  <el-col :span="3">x {{ item.Quantity }}</el-col>
                  <el-col :span="5">
                    <span v-if="item.PreferentialTotalAmount < 0">+ ¥ {{ Math.abs(item.PreferentialTotalAmount) | toFixed
                      | NumFormat }}</span>
                    <span v-else-if="item.PreferentialTotalAmount > 0">- ¥ {{ Math.abs(item.PreferentialTotalAmount) |
                      toFixed | NumFormat }}</span>
                    <span v-else>¥ 0.00</span>
                  </el-col>
                  <el-col :span="4">¥ {{ item.ArrearAmount | toFixed | NumFormat }}</el-col>
                  <el-col :span="4">¥ {{ item.IsLargess ? 0 : item.TotalAmount | toFixed | NumFormat }}</el-col>
                </el-col>
                <el-col :span="24" class="martp_5">
                  <el-col :span="11">
                    <div class="color_red font_12">¥ {{ item.Price | toFixed | NumFormat }}</div>
                  </el-col>
                  <el-col :span="13">
                    <span class="color_gray font_12" v-if="item.PricePreferentialAmount != 0">
                      手动改价：
                      <span class="color_red" v-if="item.PricePreferentialAmount > 0">- ¥ {{ item.PricePreferentialAmount
                        | toFixed | NumFormat }}</span>
                      <span class="color_green" v-else>+ ¥ {{ Math.abs(item.PricePreferentialAmount) | toFixed | NumFormat
                      }}</span>
                    </span>
                    <span class="color_gray font_12" :class="item.PricePreferentialAmount != 0 ? 'marlt_15' : ''"
                      v-if="item.CardPreferentialAmount > 0">
                      卡优惠：
                      <span class="color_red">- ¥ {{ parseFloat(item.CardPreferentialAmount) | toFixed | NumFormat
                      }}</span>
                    </span>
                    
                    <span class="color_gray font_12" :class="item.PricePreferentialAmount != 0 ? 'marlt_15' : ''"
                      v-if="item.MemberPreferentialAmount > 0">
                      会员优惠：
                      <span class="color_red">- ¥ {{ parseFloat(item.MemberPreferentialAmount) | toFixed | NumFormat
                      }}</span>
                    </span>
                  </el-col>
                </el-col>
              </el-col>

              <el-col v-if="item.Remark" :span="24" class="pad_10 border_bottom font_12 color_333">备注信息：{{ item.Remark
              }}</el-col>

              <el-col :span="24" v-if="item.SaleBillHandler.length > 0" class="pad_10 padtp_5 padbm_5 border_bottom">
                <el-row v-for="(handler, pIndex) in item.SaleBillHandler" :key="pIndex + 'h1'">
                  <el-col :span="2">
                    <el-form class="saleHandler" :inline="true" size="mini" label-position="left">
                      <el-form-item :label="`${handler.SaleHandlerName}`"></el-form-item>
                    </el-form>
                  </el-col>
                  <el-col :span="22">
                    <el-form class="saleHandler" :inline="true" size="mini">
                      <el-form-item v-for="(employee, handleIndex) in handler.Employee" :key="handleIndex"
                        :label="`${employee.EmployeeName}:`">{{ (employee.Scale || 0).toFixed(2) }}%</el-form-item>
                    </el-form>
                  </el-col>
                </el-row>
              </el-col>
            </el-row>
          </div>
          <!-- 储值卡 -->
          <div v-if="saleOrderDetail.SavingCard != undefined && saleOrderDetail.SavingCard.length > 0">
            <el-row class="row_header">
              <el-col :span="8">储值卡</el-col>
              <el-col :span="3">数量</el-col>
              <el-col :span="3">充值金额</el-col>
              <el-col :span="3">赠送金额</el-col>
              <el-col :span="3">欠款金额</el-col>
              <el-col :span="4">小计</el-col>
            </el-row>

            <el-row v-for="(item, index) in saleOrderDetail.SavingCard" :key="index + 'x2'">
              <el-col :span="24" class="pad_10 border_bottom">
                <el-col :span="24">
                  <el-col :span="8">
                    <div>
                      {{ item.SavingCardName }}
                      <span v-if="item.Alias">({{ item.Alias }})</span>
                      <span v-if="isRemark" class="marlt_5 color_primary cursor_pointer">
                        <el-button icon="el-icon-edit-outline" type="text" style="padding: unset"
                          @click="changeRemarkClick(item, 'updateSavingCardRemark')"></el-button>
                      </span>
                    </div>
                  </el-col>
                  <el-col :span="3">x {{ item.Quantity }}</el-col>
                  <el-col :span="3">¥ {{ item.TotalAmount | toFixed | NumFormat }}</el-col>
                  <el-col :span="3">¥ {{ item.LargessAmount | toFixed | NumFormat }}</el-col>
                  <el-col :span="3">¥ {{ item.ArrearAmount | toFixed | NumFormat }}</el-col>
                  <el-col :span="4">¥ {{ item.TotalAmount | toFixed | NumFormat }}</el-col>
                </el-col>
                <el-col :span="24" class="martp_5">
                  <div>
                    <span class="color_red" v-if="item.Price">¥ {{ item.Price | toFixed | NumFormat }}</span>
                    <span class="marlt_10 font_12 color_gray" v-if="item.LargessPrice">赠送：¥ {{ item.LargessPrice }}</span>
                  </div>
                </el-col>
              </el-col>

              <el-col v-if="item.Remark" :span="24" class="pad_10 border_bottom font_12 color_333">备注信息：{{ item.Remark
              }}</el-col>

              <el-col :span="24" v-if="item.SaleBillHandler.length > 0" class="pad_10 padtp_5 padbm_5 border_bottom">
                <el-row v-for="(handler, pIndex) in item.SaleBillHandler" :key="pIndex + 'h2'">
                  <el-col :span="2">
                    <el-form class="saleHandler" :inline="true" size="mini" label-position="left">
                      <el-form-item :label="`${handler.SaleHandlerName}`"></el-form-item>
                    </el-form>
                  </el-col>
                  <el-col :span="22">
                    <el-form class="saleHandler" :inline="true" size="mini">
                      <el-form-item v-for="(employee, handleIndex) in handler.Employee" :key="handleIndex"
                        :label="`${employee.EmployeeName}:`">{{ (employee.Scale || 0).toFixed(2) }}%</el-form-item>
                    </el-form>
                  </el-col>
                </el-row>
              </el-col>
            </el-row>
          </div>
          <!-- 时效卡 -->
          <div v-if="saleOrderDetail.TimeCard != undefined && saleOrderDetail.TimeCard.length > 0">
            <el-row class="row_header">
              <el-col :span="8">时效卡</el-col>
              <el-col :span="3">数量</el-col>
              <el-col :span="5">优惠金额</el-col>
              <el-col :span="4">欠款金额</el-col>
              <el-col :span="4">小计</el-col>
            </el-row>
            <el-row v-for="(item, index) in saleOrderDetail.TimeCard" :key="index + 'x3'">
              <el-col :span="24" class="pad_10 border_bottom">
                <el-col :span="24">
                  <el-col :span="8">
                    <div>
                      {{ item.TimeCardName }}
                      <span v-if="item.Alias">({{ item.Alias }})</span>
                      <el-tag v-if="item.IsLargess" size="mini" type="danger" class="marlt_10">赠</el-tag>
                      <span v-if="isRemark" class="marlt_5 color_primary cursor_pointer">
                        <el-button icon="el-icon-edit-outline" type="text" style="padding: unset"
                          @click="changeRemarkClick(item, 'updateTimeCardRemark')"></el-button>
                      </span>
                    </div>
                  </el-col>
                  <el-col :span="3">x {{ item.Quantity }}</el-col>
                  <el-col :span="5">
                    <span v-if="item.PreferentialTotalAmount < 0">+ ¥ {{ Math.abs(item.PreferentialTotalAmount) | toFixed
                      | NumFormat }}</span>
                    <span v-else-if="item.PreferentialTotalAmount > 0">- ¥ {{ Math.abs(item.PreferentialTotalAmount) |
                      toFixed | NumFormat }}</span>
                    <span v-else>¥ 0.00</span>
                  </el-col>
                  <el-col :span="4">¥ {{ item.ArrearAmount | toFixed | NumFormat }}</el-col>
                  <el-col :span="4">¥ {{ item.IsLargess ? 0 : item.TotalAmount | toFixed | NumFormat }}</el-col>
                </el-col>
                <el-col :span="24" class="martp_5">
                  <el-col :span="11">
                    <div class="color_red font_12">¥ {{ item.Price | toFixed | NumFormat }}</div>
                  </el-col>
                  <el-col :span="13">
                    <span class="color_gray font_12" v-if="item.PricePreferentialAmount != 0">
                      手动改价：
                      <span class="color_red" v-if="item.PricePreferentialAmount > 0">- ¥ {{ item.PricePreferentialAmount
                        | toFixed | NumFormat }}</span>
                      <span class="color_green" v-else>+ ¥ {{ Math.abs(item.PricePreferentialAmount) | toFixed | NumFormat
                      }}</span>
                    </span>
                    <span class="color_gray font_12" :class="item.PricePreferentialAmount != 0 ? 'marlt_5' : ''"
                      v-if="item.CardPreferentialAmount > 0">
                      卡优惠：
                      <span class="color_red">- ¥ {{ parseFloat(item.CardPreferentialAmount) | toFixed | NumFormat
                      }}</span>
                    </span>
                    <span class="color_gray font_12" :class="item.PricePreferentialAmount != 0 ? 'marlt_15' : ''"
                      v-if="item.MemberPreferentialAmount > 0">
                      会员优惠：
                      <span class="color_red">- ¥ {{ parseFloat(item.MemberPreferentialAmount) | toFixed | NumFormat
                      }}</span>
                    </span>
                  </el-col>
                </el-col>
              </el-col>

              <el-col v-if="item.Remark" :span="24" class="pad_10 border_bottom font_12 color_333">备注信息：{{ item.Remark
              }}</el-col>

              <el-col :span="24" v-if="item.SaleBillHandler.length > 0" class="pad_10 padtp_5 padbm_5 border_bottom">
                <el-row v-for="(handler, pIndex) in item.SaleBillHandler" :key="pIndex + 'h3'">
                  <el-col :span="2">
                    <el-form class="saleHandler" :inline="true" size="mini" label-position="left">
                      <el-form-item :label="`${handler.SaleHandlerName}`"></el-form-item>
                    </el-form>
                  </el-col>
                  <el-col :span="22">
                    <el-form class="saleHandler" :inline="true" size="mini">
                      <el-form-item v-for="(employee, handleIndex) in handler.Employee" :key="handleIndex"
                        :label="`${employee.EmployeeName}:`">{{ employee.Scale.toFixed(2) }}%</el-form-item>
                    </el-form>
                  </el-col>
                </el-row>
              </el-col>
            </el-row>
          </div>
          <!-- 通用次卡 -->
          <div v-if="saleOrderDetail.GeneralCard != undefined && saleOrderDetail.GeneralCard.length > 0">
            <el-row class="row_header">
              <el-col :span="8">通用次卡</el-col>
              <el-col :span="3">数量</el-col>
              <el-col :span="5">优惠金额</el-col>
              <el-col :span="4">欠款金额</el-col>
              <el-col :span="4">小计</el-col>
            </el-row>
            <el-row v-for="(item, index) in saleOrderDetail.GeneralCard" :key="index + 'x4'">
              <el-col :span="24" class="pad_10 border_bottom">
                <el-col :span="24">
                  <el-col :span="8">
                    <div>
                      {{ item.GeneralCardName }}
                      <span v-if="item.Alias">({{ item.Alias }})</span>
                      <el-tag v-if="item.IsLargess" size="mini" type="danger" class="marlt_10">赠</el-tag>
                      <span v-if="isRemark" class="marlt_5 color_primary cursor_pointer">
                        <el-button icon="el-icon-edit-outline" type="text" style="padding: unset"
                          @click="changeRemarkClick(item, 'updateGeneralCardRemark')"></el-button>
                      </span>
                    </div>
                  </el-col>
                  <el-col :span="3">x {{ item.Quantity }}</el-col>
                  <el-col :span="5">
                    <span v-if="item.PreferentialTotalAmount < 0">+ ¥ {{ Math.abs(item.PreferentialTotalAmount) | toFixed
                      | NumFormat }}</span>
                    <span v-else-if="item.PreferentialTotalAmount > 0">- ¥ {{ Math.abs(item.PreferentialTotalAmount) |
                      toFixed | NumFormat }}</span>
                    <span v-else>¥ 0.00</span>
                  </el-col>
                  <el-col :span="4">¥ {{ item.ArrearAmount | toFixed | NumFormat }}</el-col>
                  <el-col :span="4">¥ {{ item.IsLargess ? 0 : item.TotalAmount | toFixed | NumFormat }}</el-col>
                </el-col>
                <el-col :span="24" class="martp_5">
                  <el-col :span="11">
                    <div class="color_red font_12">¥ {{ item.Price | toFixed | NumFormat }}</div>
                  </el-col>
                  <el-col :span="13">
                    <span class="color_gray font_12" v-if="item.PricePreferentialAmount != 0">
                      手动改价：
                      <span class="color_red" v-if="item.PricePreferentialAmount > 0">- ¥ {{ item.PricePreferentialAmount
                        | toFixed | NumFormat }}</span>
                      <span class="color_green" v-else>+ ¥ {{ Math.abs(item.PricePreferentialAmount) | toFixed | NumFormat
                      }}</span>
                    </span>
                    <span class="color_gray font_12" :class="item.PricePreferentialAmount != 0 ? 'marlt_5' : ''"
                      v-if="item.CardPreferentialAmount > 0">
                      卡优惠：
                      <span class="color_red">- ¥ {{ parseFloat(item.CardPreferentialAmount) | toFixed | NumFormat
                      }}</span>
                    </span>
                    <span class="color_gray font_12" :class="item.PricePreferentialAmount != 0 ? 'marlt_15' : ''"
                      v-if="item.MemberPreferentialAmount > 0">
                      会员优惠：
                      <span class="color_red">- ¥ {{ parseFloat(item.MemberPreferentialAmount) | toFixed | NumFormat
                      }}</span>
                    </span>
                  </el-col>
                </el-col>
              </el-col>

              <el-col v-if="item.Remark" :span="24" class="pad_10 border_bottom font_12 color_333">备注信息：{{ item.Remark
              }}</el-col>

              <el-col :span="24" v-if="item.SaleBillHandler.length > 0" class="pad_10 padtp_5 padbm_5 border_bottom">
                <el-row v-for="(handler, pIndex) in item.SaleBillHandler" :key="pIndex + 'h4'">
                  <el-col :span="2">
                    <el-form class="saleHandler" :inline="true" size="mini" label-position="left">
                      <el-form-item :label="`${handler.SaleHandlerName}`"></el-form-item>
                    </el-form>
                  </el-col>
                  <el-col :span="22">
                    <el-form class="saleHandler" :inline="true" size="mini">
                      <el-form-item v-for="(employee, handleIndex) in handler.Employee" :key="handleIndex"
                        :label="`${employee.EmployeeName}:`">{{ (employee.Scale || 0).toFixed(2) }}%</el-form-item>
                    </el-form>
                  </el-col>
                </el-row>
              </el-col>
            </el-row>
          </div>
          <!-- 产品 -->
          <div v-if="saleOrderDetail.Product != undefined && saleOrderDetail.Product.length > 0">
            <el-row class="row_header">
              <el-col :span="8">产品</el-col>
              <el-col :span="3">数量</el-col>
              <el-col :span="5">优惠金额</el-col>
              <el-col :span="4">欠款金额</el-col>
              <el-col :span="4">小计</el-col>
            </el-row>
            <el-row v-for="(item, index) in saleOrderDetail.Product" :key="index + 'x5'">
              <el-col :span="24" class="pad_10 border_bottom">
                <el-col :span="24">
                  <el-col :span="8">
                    <div>
                      {{ item.ProductName }}
                      <span v-if="item.Alias">({{ item.Alias }})</span>
                      <el-tag v-if="item.IsLargess" size="mini" type="danger" class="marlt_10">赠</el-tag>
                      <span v-if="isRemark" class="marlt_5 color_primary cursor_pointer">
                        <el-button icon="el-icon-edit-outline" type="text" style="padding: unset"
                          @click="changeRemarkClick(item, 'updateProductRemark')"></el-button>
                      </span>
                    </div>
                  </el-col>
                  <el-col :span="3">x {{ item.Quantity }}</el-col>
                  <el-col :span="5">
                    <span v-if="item.PreferentialTotalAmount < 0">+ ¥ {{ Math.abs(item.PreferentialTotalAmount) | toFixed
                      | NumFormat }}</span>
                    <span v-else-if="item.PreferentialTotalAmount > 0">- ¥ {{ Math.abs(item.PreferentialTotalAmount) |
                      toFixed | NumFormat }}</span>
                    <span v-else>¥ 0.00</span>
                  </el-col>
                  <el-col :span="4">¥ {{ item.ArrearAmount | toFixed | NumFormat }}</el-col>
                  <el-col :span="4">¥ {{ item.IsLargess ? 0 : item.TotalAmount | toFixed | NumFormat }}</el-col>
                </el-col>
                <el-col :span="24" class="martp_5">
                  <el-col :span="11">
                    <div class="color_red font_12">¥ {{ item.Price | toFixed | NumFormat }}</div>
                  </el-col>
                  <el-col :span="13">
                    <span class="color_gray font_12" v-if="item.PricePreferentialAmount != 0">
                      手动改价：
                      <span class="color_red" v-if="item.PricePreferentialAmount > 0">- ¥ {{ item.PricePreferentialAmount
                        | toFixed | NumFormat }}</span>
                      <span class="color_green" v-else>+ ¥ {{ Math.abs(item.PricePreferentialAmount) | toFixed | NumFormat
                      }}</span>
                    </span>
                    <span class="color_gray font_12" :class="item.PricePreferentialAmount != 0 ? 'marlt_5' : ''"
                      v-if="item.CardPreferentialAmount > 0">
                      卡优惠：
                      <span class="color_red">- ¥ {{ parseFloat(item.CardPreferentialAmount) | toFixed | NumFormat
                      }}</span>
                    </span>
                    <span class="color_gray font_12" :class="item.PricePreferentialAmount != 0 ? 'marlt_15' : ''"
                      v-if="item.MemberPreferentialAmount > 0">
                      会员优惠：
                      <span class="color_red">- ¥ {{ parseFloat(item.MemberPreferentialAmount) | toFixed | NumFormat
                      }}</span>
                    </span>
                  </el-col>
                </el-col>
              </el-col>

              <el-col v-if="item.Remark" :span="24" class="pad_10 border_bottom font_12 color_333">备注信息：{{ item.Remark
              }}</el-col>

              <el-col :span="24" v-if="item.SaleBillHandler.length > 0" class="pad_10 padtp_5 padbm_5 border_bottom">
                <el-row v-for="(handler, pIndex) in item.SaleBillHandler" :key="pIndex + 'h5'">
                  <el-col :span="2">
                    <el-form class="saleHandler" :inline="true" size="mini" label-position="left">
                      <el-form-item :label="`${handler.SaleHandlerName}`"></el-form-item>
                    </el-form>
                  </el-col>
                  <el-col :span="22">
                    <el-form class="saleHandler" :inline="true" size="mini">
                      <el-form-item v-for="(employee, handleIndex) in handler.Employee" :key="handleIndex"
                        :label="`${employee.EmployeeName}:`">{{ (employee.Scale || 0).toFixed(2) }}%</el-form-item>
                    </el-form>
                  </el-col>
                </el-row>
              </el-col>
            </el-row>
          </div>
          <!-- 套餐卡 -->
          <div v-if="saleOrderDetail.PackageCard != undefined && saleOrderDetail.PackageCard.length > 0">
            <el-row class="row_header">
              <el-col :span="8">套餐卡</el-col>
              <el-col :span="3">数量</el-col>
              <el-col :span="5">优惠金额</el-col>
              <el-col :span="4">欠款金额</el-col>
              <el-col :span="4">小计</el-col>
            </el-row>
            <el-row v-for="(item, index) in saleOrderDetail.PackageCard" :key="index + 'x6'">
              <el-col :span="24" class="pad_10 border_bottom">
                <el-col :span="24">
                  <el-col :span="8">
                    <div>
                      {{ item.PackageCardName }}
                      <span v-if="item.Alias">({{ item.Alias }})</span>
                      <el-tag v-if="item.IsLargess" size="mini" type="danger" class="marlt_10">赠</el-tag>
                      <span class="marlt_5 color_primary cursor_pointer" @click="packDetailClick(item)">
                        <el-button type="text">
                          <i class="el-icon-view el-icon--right"></i>
                        </el-button>
                      </span>
                      <span v-if="isRemark" class="marlt_5 color_primary cursor_pointer">
                        <el-button icon="el-icon-edit-outline" type="text" style="padding: unset"
                          @click="changeRemarkClick(item, 'updatePackageCardRemark')"></el-button>
                      </span>
                    </div>
                  </el-col>
                  <el-col :span="3">x {{ item.Quantity }}</el-col>
                  <el-col :span="5">
                    <span v-if="item.PreferentialTotalAmount < 0">+ ¥ {{ Math.abs(item.PreferentialTotalAmount) | toFixed
                      | NumFormat }}</span>
                    <span v-else-if="item.PreferentialTotalAmount > 0">- ¥ {{ Math.abs(item.PreferentialTotalAmount) |
                      toFixed | NumFormat }}</span>
                    <span v-else>¥ 0.00</span>
                  </el-col>
                  <el-col :span="4">¥ {{ item.ArrearAmount | toFixed | NumFormat }}</el-col>
                  <el-col :span="4">¥ {{ item.IsLargess ? 0 : item.TotalAmount | toFixed | NumFormat }}</el-col>
                </el-col>
                <el-col :span="24" class="martp_5">
                  <el-col :span="11">
                    <div class="color_red font_12">¥ {{ item.Price | toFixed | NumFormat }}</div>
                  </el-col>
                  <el-col :span="13">
                    <span class="color_gray font_12" v-if="item.PricePreferentialAmount != 0">
                      手动改价：
                      <span class="color_red" v-if="item.PricePreferentialAmount > 0">- ¥ {{ item.PricePreferentialAmount
                        | toFixed | NumFormat }}</span>
                      <span class="color_green" v-else>+ ¥ {{ Math.abs(item.PricePreferentialAmount) | toFixed | NumFormat
                      }}</span>
                    </span>
                    <span class="color_gray font_12" :class="item.PricePreferentialAmount != 0 ? 'marlt_5' : ''"
                      v-if="item.CardPreferentialAmount > 0">
                      卡优惠：
                      <span class="color_red">- ¥ {{ parseFloat(item.CardPreferentialAmount) | toFixed | NumFormat
                      }}</span>
                    </span>
                    <span class="color_gray font_12" :class="item.PricePreferentialAmount != 0 ? 'marlt_15' : ''"
                      v-if="item.MemberPreferentialAmount > 0">
                      会员优惠：
                      <span class="color_red">- ¥ {{ parseFloat(item.MemberPreferentialAmount) | toFixed | NumFormat
                      }}</span>
                    </span>
                  </el-col>
                </el-col>
              </el-col>
              <el-col :span="24" v-if="item.IsShowDetails">
                <!-- 套餐卡产品 -->
                <el-col :span="24" class="border_bottom pad_10 font_12"
                  v-for="packageDetail in item.PackageCardGoods.PackageCardProduct" :key="`${packageDetail.ProductID}`">
                  <el-col :span="24">
                    <el-col :span="8">
                      <span>{{ packageDetail.Name }} ×{{ packageDetail.Quantity }}</span>
                      <el-tag class="marlt_5" size="mini">产品</el-tag>
                      <span v-if="isRemark" class="marlt_5 color_primary cursor_pointer">
                        <el-button icon="el-icon-edit-outline" type="text" style="padding: unset"
                          @click="changeRemarkClick(packageDetail, 'updatePackageCardProductRemark')"></el-button>
                      </span>
                    </el-col>
                    <el-col :span="3">× {{ packageDetail.PackageQuantity }}</el-col>
                    <el-col :span="5">
                      <span v-if="packageDetail.PreferentialTotalAmount < 0">+ ¥ {{
                        Math.abs(packageDetail.PreferentialTotalAmount) | toFixed | NumFormat }}</span>
                      <span v-else-if="packageDetail.PreferentialTotalAmount > 0">- ¥ {{
                        Math.abs(packageDetail.PreferentialTotalAmount) | toFixed | NumFormat }}</span>
                      <span v-else>¥ 0.00</span>
                    </el-col>
                    <el-col :span="4">¥ {{ packageDetail.ArrearAmount | toFixed | NumFormat }}</el-col>
                    <el-col :span="4">¥ {{ item.IsLargess ? 0 : packageDetail.TotalAmount | toFixed | NumFormat
                    }}</el-col>
                  </el-col>
                  <el-col :span="24" style="margin-top: 4px">
                    <el-col :span="11" class="color_red">¥ {{ packageDetail.PackageTotalPrice | toFixed | NumFormat
                    }}</el-col>
                    <el-col :span="13">
                      <div>
                        <span class="color_gray font_12" v-if="packageDetail.PricePreferentialAmount != 0">
                          手动改价：
                          <span class="color_red" v-if="packageDetail.PricePreferentialAmount > 0">- ¥ {{
                            Math.abs(packageDetail.PricePreferentialAmount) | toFixed | NumFormat }}</span>
                          <span class="color_green" v-else-if="packageDetail.PricePreferentialAmount < 0">+ ¥ {{
                            Math.abs(packageDetail.PricePreferentialAmount) | toFixed | NumFormat }}</span>
                          <span v-else>¥ 0.00</span>
                        </span>
                        <span class="color_gray font_12"
                          :class="packageDetail.CardPreferentialAmount != 0 ? 'marlt_5' : ''"
                          v-if="packageDetail.CardPreferentialAmount > 0">
                          卡优惠：
                          <span class="color_red">- ¥ {{ parseFloat(packageDetail.CardPreferentialAmount) | toFixed |
                            NumFormat }}</span>
                        </span>
                        <span class="color_gray font_12" :class="packageDetail.PricePreferentialAmount != 0 ? 'marlt_15' : ''"  v-if="packageDetail.MemberPreferentialAmount > 0">
                            会员优惠：
                            <span class="color_red">- ¥ {{ parseFloat(packageDetail.MemberPreferentialAmount) | toFixed | NumFormat}}
                          </span>
                        </span>
                      </div>
                    </el-col>
                  </el-col>

                  <el-col v-if="packageDetail.Remark" :span="24" class="martp_5 font_12 color_333">备注信息：{{
                    packageDetail.Remark }}</el-col>
                </el-col>
                <!-- 套餐卡项目 -->
                <el-col :span="24" class="border_bottom pad_10 font_12"
                  v-for="packageDetail in item.PackageCardGoods.PackageCardProject" :key="`${packageDetail.ProjectID}`">
                  <el-col :span="24">
                    <el-col :span="8">
                      <span>{{ packageDetail.Name }} ×{{ packageDetail.Quantity }}</span>
                      <el-tag class="marlt_5" size="mini">项目</el-tag>
                      <span v-if="isRemark" class="marlt_5 color_primary cursor_pointer">
                        <el-button icon="el-icon-edit-outline" type="text" style="padding: unset"
                          @click="changeRemarkClick(packageDetail, 'updatePackageCardProjectRemark')"></el-button>
                      </span>
                    </el-col>
                    <el-col :span="3">× {{ packageDetail.PackageQuantity }}</el-col>
                    <el-col :span="5">
                      <span v-if="packageDetail.PreferentialTotalAmount < 0">+ ¥ {{
                        Math.abs(packageDetail.PreferentialTotalAmount) | toFixed | NumFormat }}</span>
                      <span v-else-if="packageDetail.PreferentialTotalAmount > 0">- ¥ {{
                        Math.abs(packageDetail.PreferentialTotalAmount) | toFixed | NumFormat }}</span>
                      <span v-else>¥ 0.00</span>
                    </el-col>
                    <el-col :span="4">¥ {{ packageDetail.ArrearAmount | toFixed | NumFormat }}</el-col>
                    <el-col :span="4">¥ {{ item.IsLargess ? 0 : packageDetail.TotalAmount | toFixed | NumFormat
                    }}</el-col>
                  </el-col>
                  <el-col :span="24" style="margin-top: 4px">
                    <el-col :span="11" class="color_red">¥ {{ packageDetail.PackageTotalPrice | toFixed | NumFormat
                    }}</el-col>
                    <el-col :span="13">
                      <div>
                        <span class="color_gray font_12" v-if="packageDetail.PricePreferentialAmount != 0">
                          手动改价：
                          <span class="color_red" v-if="packageDetail.PricePreferentialAmount > 0">- ¥ {{
                            Math.abs(packageDetail.PricePreferentialAmount) | toFixed | NumFormat }}</span>
                          <span class="color_green" v-else-if="packageDetail.PricePreferentialAmount < 0">+ ¥ {{
                            Math.abs(packageDetail.PricePreferentialAmount) | toFixed | NumFormat }}</span>
                          <span v-else>¥ 0.00</span>
                        </span>
                        <span class="color_gray font_12"
                          :class="packageDetail.CardPreferentialAmount != 0 ? 'marlt_5' : ''"
                          v-if="packageDetail.CardPreferentialAmount > 0">
                          卡优惠：
                          <span class="color_red">- ¥ {{ parseFloat(packageDetail.CardPreferentialAmount) | toFixed |
                            NumFormat }}</span>
                        </span>
                        <span class="color_gray font_12" :class="packageDetail.PricePreferentialAmount != 0 ? 'marlt_15' : ''"  v-if="packageDetail.MemberPreferentialAmount > 0">
                            会员优惠：
                            <span class="color_red">- ¥ {{ parseFloat(packageDetail.MemberPreferentialAmount) | toFixed | NumFormat}}
                          </span>
                          
                        </span>
                      </div>
                    </el-col>
                  </el-col>

                  <el-col v-if="packageDetail.Remark" :span="24" class="martp_5 font_12 color_333">备注信息：{{
                    packageDetail.Remark }}</el-col>
                </el-col>
                <!-- 套餐卡通用次卡 -->
                <el-col :span="24" class="border_bottom pad_10 font_12"
                  v-for="packageDetail in item.PackageCardGoods.PackageCardGeneralCard"
                  :key="`${packageDetail.GeneralCardID}`">
                  <el-col :span="24">
                    <el-col :span="8">
                      <span>{{ packageDetail.Name }} ×{{ packageDetail.Quantity }}</span>
                      <el-tag class="marlt_5" size="mini">通用次卡</el-tag>
                      <span v-if="isRemark" class="marlt_5 color_primary cursor_pointer">
                        <el-button icon="el-icon-edit-outline" type="text" style="padding: unset"
                          @click="changeRemarkClick(packageDetail, 'updatePackageCardGeneralCardRemark')"></el-button>
                      </span>
                    </el-col>
                    <el-col :span="3">× {{ packageDetail.PackageQuantity }}</el-col>
                    <el-col :span="5">
                      <span v-if="packageDetail.PreferentialTotalAmount < 0">+ ¥ {{
                        Math.abs(packageDetail.PreferentialTotalAmount) | toFixed | NumFormat }}</span>
                      <span v-else-if="packageDetail.PreferentialTotalAmount > 0">- ¥ {{
                        Math.abs(packageDetail.PreferentialTotalAmount) | toFixed | NumFormat }}</span>
                      <span v-else>¥ 0.00</span>
                    </el-col>
                    <el-col :span="4">¥ {{ packageDetail.ArrearAmount | toFixed | NumFormat }}</el-col>
                    <el-col :span="4">¥ {{ item.IsLargess ? 0 : packageDetail.TotalAmount | toFixed | NumFormat
                    }}</el-col>
                  </el-col>
                  <el-col :span="24" style="margin-top: 4px">
                    <el-col :span="11" class="color_red">¥ {{ packageDetail.PackageTotalPrice | toFixed | NumFormat
                    }}</el-col>
                    <el-col :span="13">
                      <div>
                        <span class="color_gray font_12" v-if="packageDetail.PricePreferentialAmount != 0">
                          手动改价：
                          <span class="color_red" v-if="packageDetail.PricePreferentialAmount > 0">- ¥ {{
                            Math.abs(packageDetail.PricePreferentialAmount) | toFixed | NumFormat }}</span>
                          <span class="color_green" v-else-if="packageDetail.PricePreferentialAmount < 0">+ ¥ {{
                            Math.abs(packageDetail.PricePreferentialAmount) | toFixed | NumFormat }}</span>
                          <span v-else>¥ 0.00</span>
                        </span>
                        <span class="color_gray font_12"
                          :class="packageDetail.CardPreferentialAmount != 0 ? 'marlt_5' : ''"
                          v-if="packageDetail.CardPreferentialAmount > 0">
                          卡优惠：
                          <span class="color_red">- ¥ {{ parseFloat(packageDetail.CardPreferentialAmount) | toFixed |
                            NumFormat }}</span>
                        </span>
                        <span class="color_gray font_12" :class="packageDetail.PricePreferentialAmount != 0 ? 'marlt_15' : ''"  v-if="packageDetail.MemberPreferentialAmount > 0">
                            会员优惠：
                            <span class="color_red">- ¥ {{ parseFloat(packageDetail.MemberPreferentialAmount) | toFixed | NumFormat}}
                          </span>
                          
                        </span>
                      </div>
                    </el-col>
                  </el-col>

                  <el-col v-if="packageDetail.Remark" :span="24" class="martp_5 font_12 color_333">备注信息：{{
                    packageDetail.Remark }}</el-col>
                </el-col>
                <!-- 套餐卡时效卡 -->
                <el-col :span="24" class="border_bottom pad_10 font_12"
                  v-for="packageDetail in item.PackageCardGoods.PackageCardTimeCard" :key="`${packageDetail.TimeCardID}`">
                  <el-col :span="24">
                    <el-col :span="8">
                      <span>{{ packageDetail.Name }} ×{{ packageDetail.Quantity }}</span>
                      <el-tag class="marlt_5" size="mini">时效卡</el-tag>
                      <span v-if="isRemark" class="marlt_5 color_primary cursor_pointer">
                        <el-button icon="el-icon-edit-outline" type="text" style="padding: unset"
                          @click="changeRemarkClick(packageDetail, 'updatePackageCardTimeCardRemark')"></el-button>
                      </span>
                    </el-col>
                    <el-col :span="3">× {{ packageDetail.PackageQuantity }}</el-col>
                    <el-col :span="5">
                      <span v-if="packageDetail.PreferentialTotalAmount < 0">+ ¥ {{
                        Math.abs(packageDetail.PreferentialTotalAmount) | toFixed | NumFormat }}</span>
                      <span v-else-if="packageDetail.PreferentialTotalAmount > 0">- ¥ {{
                        Math.abs(packageDetail.PreferentialTotalAmount) | toFixed | NumFormat }}</span>
                      <span v-else>¥ 0.00</span>
                    </el-col>
                    <el-col :span="4">¥ {{ packageDetail.ArrearAmount | toFixed | NumFormat }}</el-col>
                    <el-col :span="4">¥ {{ item.IsLargess ? 0 : packageDetail.TotalAmount | toFixed | NumFormat
                    }}</el-col>
                  </el-col>
                  <el-col :span="24" style="margin-top: 4px">
                    <el-col :span="11" class="color_red">¥ {{ packageDetail.PackageTotalPrice | toFixed | NumFormat
                    }}</el-col>
                    <el-col :span="13">
                      <div>
                        <span class="color_gray font_12" v-if="packageDetail.PricePreferentialAmount != 0">
                          手动改价：
                          <span class="color_red" v-if="packageDetail.PricePreferentialAmount > 0">- ¥ {{
                            Math.abs(packageDetail.PricePreferentialAmount) | toFixed | NumFormat }}</span>
                          <span class="color_green" v-else-if="packageDetail.PricePreferentialAmount < 0">+ ¥ {{
                            Math.abs(packageDetail.PricePreferentialAmount) | toFixed | NumFormat }}</span>
                          <span v-else>¥ 0.00</span>
                        </span>
                        <span class="color_gray font_12"
                          :class="packageDetail.CardPreferentialAmount != 0 ? 'marlt_5' : ''"
                          v-if="packageDetail.CardPreferentialAmount > 0">
                          卡优惠：
                          <span class="color_red">- ¥ {{ parseFloat(packageDetail.CardPreferentialAmount) | toFixed |
                            NumFormat }}</span>
                        </span>
                        <span class="color_gray font_12" :class="packageDetail.PricePreferentialAmount != 0 ? 'marlt_15' : ''"  v-if="packageDetail.MemberPreferentialAmount > 0">
                            会员优惠：
                            <span class="color_red">- ¥ {{ parseFloat(packageDetail.MemberPreferentialAmount) | toFixed | NumFormat}}
                          </span>
                          
                        </span>
                      </div>
                    </el-col>
                  </el-col>

                  <el-col v-if="packageDetail.Remark" :span="24" class="martp_5 font_12 color_333">备注信息：{{
                    packageDetail.Remark }}</el-col>
                </el-col>
                <!-- 套餐卡储值卡 -->
                <el-col :span="24" class="border_bottom pad_10 font_12"
                  v-for="packageDetail in item.PackageCardGoods.PackageCardSavingCard"
                  :key="`${packageDetail.SavingCardID}`">
                  <el-col :span="24">
                    <el-col :span="8">
                      <span>{{ packageDetail.Name }} ×{{ packageDetail.Quantity }}</span>
                      <el-tag class="marlt_5" size="mini">储值卡</el-tag>
                      <span v-if="isRemark" class="marlt_5 color_primary cursor_pointer">
                        <el-button icon="el-icon-edit-outline" type="text" style="padding: unset"
                          @click="changeRemarkClick(packageDetail, 'updatePackageCardSavingCardRemark')"></el-button>
                      </span>
                    </el-col>
                    <el-col :span="8">× {{ packageDetail.PackageQuantity }}</el-col>
                    <el-col :span="4">¥ {{ packageDetail.ArrearAmount | toFixed | NumFormat }}</el-col>
                    <el-col :span="4">¥ {{ item.IsLargess ? 0 : packageDetail.TotalAmount | toFixed | NumFormat
                    }}</el-col>
                  </el-col>
                  <el-col :span="24" style="margin-top: 4px">
                    <el-col :span="11" class="color_red">¥ {{ packageDetail.TotalPrice | toFixed | NumFormat }}</el-col>
                  </el-col>

                  <el-col v-if="packageDetail.Remark" :span="24" class="martp_5 font_12 color_333">备注信息：{{
                    packageDetail.Remark }}</el-col>
                </el-col>
                <!-- 套餐卡赠送产品 -->
                <el-col :span="24" class="border_bottom pad_10 font_12"
                  v-for="packageDetail in item.PackageCardLargessGoods.PackageCardProduct"
                  :key="`2-${packageDetail.ProductID}`">
                  <el-col :span="24">
                    <el-col :span="8">
                      <div>
                        <el-tag size="mini" type="danger">赠</el-tag>
                        <span>{{ packageDetail.Name }} × {{ packageDetail.Quantity }}</span>
                        <el-tag class="marlt_5" size="mini">产品</el-tag>
                        <span v-if="isRemark" class="marlt_5 color_primary cursor_pointer">
                          <el-button icon="el-icon-edit-outline" type="text" style="padding: unset"
                            @click="changeRemarkClick(packageDetail, 'updatePackageCardProductRemark')"></el-button>
                        </span>
                      </div>
                      <div class="color_red martp_5">¥ {{ packageDetail.PackageTotalPrice | toFixed | NumFormat }}</div>
                    </el-col>
                    <el-col :span="16">× {{ packageDetail.PackageQuantity }}</el-col>
                  </el-col>

                  <el-col v-if="packageDetail.Remark" :span="24" class="martp_5 font_12 color_333">备注信息：{{
                    packageDetail.Remark }}</el-col>
                </el-col>
                <!-- 套餐卡赠送项目 -->
                <el-col :span="24" class="border_bottom pad_10 font_12"
                  v-for="packageDetail in item.PackageCardLargessGoods.PackageCardProject"
                  :key="`2-${packageDetail.ProjectID}`">
                  <el-col :span="24">
                    <el-col :span="8">
                      <div>
                        <el-tag size="mini" type="danger">赠</el-tag>
                        <span>{{ packageDetail.Name }} × {{ packageDetail.Quantity }}</span>
                        <el-tag class="marlt_5" size="mini">项目</el-tag>
                        <span v-if="isRemark" class="marlt_5 color_primary cursor_pointer">
                          <el-button icon="el-icon-edit-outline" type="text" style="padding: unset"
                            @click="changeRemarkClick(packageDetail, 'updatePackageCardProjectRemark')"></el-button>
                        </span>
                      </div>
                      <div class="color_red martp_5">¥ {{ packageDetail.PackageTotalPrice | toFixed | NumFormat }}</div>
                    </el-col>
                    <el-col :span="16">× {{ packageDetail.PackageQuantity }}</el-col>
                  </el-col>

                  <el-col v-if="packageDetail.Remark" :span="24" class="martp_5 font_12 color_333">备注信息：{{
                    packageDetail.Remark }}</el-col>
                </el-col>
                <!-- 套餐卡赠送通用次卡 -->
                <el-col :span="24" class="border_bottom pad_10 font_12"
                  v-for="packageDetail in item.PackageCardLargessGoods.PackageCardGeneralCard"
                  :key="`2-${packageDetail.GeneralCardID}`">
                  <el-col :span="24">
                    <el-col :span="8">
                      <div>
                        <el-tag size="mini" type="danger">赠</el-tag>
                        <span>{{ packageDetail.Name }} × {{ packageDetail.Quantity }}</span>
                        <el-tag class="marlt_5" size="mini">通用次卡</el-tag>
                        <span v-if="isRemark" class="marlt_5 color_primary cursor_pointer">
                          <el-button icon="el-icon-edit-outline" type="text" style="padding: unset"
                            @click="changeRemarkClick(packageDetail, 'updatePackageCardGeneralCardRemark')"></el-button>
                        </span>
                      </div>
                      <div class="color_red martp_5">¥ {{ packageDetail.PackageTotalPrice | toFixed | NumFormat }}</div>
                    </el-col>
                    <el-col :span="16">× {{ packageDetail.PackageQuantity }}</el-col>
                  </el-col>

                  <el-col v-if="packageDetail.Remark" :span="24" class="martp_5 font_12 color_333">备注信息：{{
                    packageDetail.Remark }}</el-col>
                </el-col>
                <!-- 套餐卡赠送时效卡 -->
                <el-col :span="24" class="border_bottom pad_10 font_12"
                  v-for="packageDetail in item.PackageCardLargessGoods.PackageCardTimeCard"
                  :key="`2-${packageDetail.TimeCardID}`">
                  <el-col :span="24">
                    <el-col :span="8">
                      <div>
                        <el-tag size="mini" type="danger">赠</el-tag>
                        <span>{{ packageDetail.Name }} × {{ packageDetail.Quantity }}</span>
                        <el-tag class="marlt_5" size="mini">时效卡</el-tag>
                        <span v-if="isRemark" class="marlt_5 color_primary cursor_pointer">
                          <el-button icon="el-icon-edit-outline" type="text" style="padding: unset"
                            @click="changeRemarkClick(packageDetail, 'updatePackageCardTimeCardRemark')"></el-button>
                        </span>
                      </div>
                      <div class="color_red martp_5">¥ {{ packageDetail.PackageTotalPrice | toFixed | NumFormat }}</div>
                    </el-col>
                    <el-col :span="16">× {{ packageDetail.PackageQuantity }}</el-col>
                  </el-col>

                  <el-col v-if="packageDetail.Remark" :span="24" class="martp_5 font_12 color_333">备注信息：{{
                    packageDetail.Remark }}</el-col>
                </el-col>
                <!-- 套餐卡赠送储值卡 -->
                <el-col :span="24" class="border_bottom pad_10 font_12"
                  v-for="packageDetail in item.PackageCardLargessGoods.PackageCardSavingCard"
                  :key="`2-${packageDetail.SavingCardID}`">
                  <el-col :span="24">
                    <el-col :span="8">
                      <div>
                        <el-tag size="mini" type="danger">赠</el-tag>
                        <span>{{ packageDetail.Name }} × {{ packageDetail.Quantity }}</span>
                        <el-tag class="marlt_5" size="mini">储值卡</el-tag>
                        <span v-if="isRemark" class="marlt_5 color_primary cursor_pointer">
                          <el-button icon="el-icon-edit-outline" type="text" style="padding: unset"
                            @click="changeRemarkClick(packageDetail, 'updatePackageCardSavingCardRemark')"></el-button>
                        </span>
                      </div>
                      <div class="color_red martp_5">¥ {{ packageDetail.TotalPrice | toFixed | NumFormat }}</div>
                    </el-col>
                    <el-col :span="16">× {{ packageDetail.PackageQuantity }}</el-col>
                  </el-col>

                  <el-col v-if="packageDetail.Remark" :span="24" class="martp_5 font_12 color_333">备注信息：{{
                    packageDetail.Remark }}</el-col>
                </el-col>
              </el-col>

              <el-col v-if="item.Remark" :span="24" class="pad_10 border_bottom font_12 color_333">备注信息：{{ item.Remark
              }}</el-col>
              <el-col :span="24" v-if="item.SaleBillHandler.length > 0" class="pad_10 padtp_5 padbm_5 border_bottom">
                <el-row v-for="(handler, pIndex) in item.SaleBillHandler" :key="pIndex + 'h6'">
                  <el-col :span="2">
                    <el-form class="saleHandler" :inline="true" size="mini" label-position="left">
                      <el-form-item :label="`${handler.SaleHandlerName}`"></el-form-item>
                    </el-form>
                  </el-col>
                  <el-col :span="22">
                    <el-form class="saleHandler" :inline="true" size="mini">
                      <el-form-item v-for="(employee, handleIndex) in handler.Employee" :key="handleIndex"
                        :label="`${employee.EmployeeName}:`">{{ (employee.Scale || 0).toFixed(2) }}%</el-form-item>
                    </el-form>
                  </el-col>
                </el-row>
              </el-col>
            </el-row>
          </div>
        </div>
        <div class="border_left border_right border_bottom">
          <el-row>
            <el-col :span="6" :offset="17">
              <el-form class="saleInfo" size="mini">
                <el-form-item label="合计：">
                  <div class="text_right">￥{{ saleOrderDetail.TotalAmount | toFixed | NumFormat }}</div>
                </el-form-item>
                <el-form-item v-if="saleOrderDetail.PricePreferentialAmount < 0" label="手动改价：">
                  <div class="text_right">+￥{{ Math.abs(saleOrderDetail.PricePreferentialAmount) | toFixed | NumFormat }}
                  </div>
                </el-form-item>
                <el-form-item v-if="saleOrderDetail.PricePreferentialAmount > 0" label="手动改价：">
                  <div class="text_right">-￥{{ Math.abs(saleOrderDetail.PricePreferentialAmount) | toFixed | NumFormat }}
                  </div>
                </el-form-item>
                <el-form-item v-if="saleOrderDetail.CardPreferentialAmount > 0" label="卡优惠：">
                  <div class="text_right">-￥{{ saleOrderDetail.CardPreferentialAmount | toFixed | NumFormat }}</div>
                </el-form-item>
                <el-form-item label="订单金额：">
                  <div class="text_right">￥{{ saleOrderDetail.Amount | toFixed | NumFormat }}</div>
                </el-form-item>
              </el-form>
            </el-col>
          </el-row>
        </div>
      </div>
      <!-- 支付信息 -->
      <div class="payInfo">
        <div class="tip martp_10 marbm_10">支付信息</div>
        <el-card class="marbm_10" shadow="never"
          v-if="saleOrderDetail.SaleBillPay != undefined && saleOrderDetail.SaleBillPay.length > 0">
          <div slot="header">
            <span>支付收款</span>
          </div>
          <el-form class="saleInfo" :inline="true" size="mini" label-position="top">
            <el-form-item v-for="salepay in saleOrderDetail.SaleBillPay" :key="salepay.ID">
              <div slot="label">
                <span class="marrt_5">{{ salepay.Name }}</span>
                <el-button
                  v-if="isModifyPayMethod && !limitSealingAccount(saleOrderDetail.BillDate, ModifyBillPayMethodRestriction)"
                  type="text" @click="ModifyPayMethodClick(salepay)">修改</el-button>
              </div>
              ¥ {{ salepay.Amount | toFixed | NumFormat }}
            </el-form-item>
          </el-form>
        </el-card>
        <el-card shadow="never"
          v-if="saleOrderDetail.SaleBillPaySavingCardDeduction != undefined && saleOrderDetail.SaleBillPaySavingCardDeduction.length > 0">
          <div slot="header">
            <span>储值卡抵扣</span>
          </div>
          <el-form class="saleInfo" :inline="true" size="mini" label-position="top">
            <!-- <el-form-item v-for="savingCardReduction in saleOrderDetail.SaleBillPaySavingCardDeduction" :key="savingCardReduction.ID" :label="savingCardReduction.Name">¥ {{savingCardReduction.TotalAmount| NumFormat}}</el-form-item> -->
            <el-form-item v-for="savingCardReduction in saleOrderDetail.SaleBillPaySavingCardDeduction"
              :key="savingCardReduction.ID">
              <div slot="label">
                <span class="marrt_5">{{ savingCardReduction.Name }}</span>
              </div>
              <!-- <div v-if="savingCardReduction.Amount">合计：¥ {{savingCardReduction.TotalAmount| toFixed | NumFormat}}</div> -->
              <div v-if="savingCardReduction.Amount">本金：¥ {{ savingCardReduction.Amount | toFixed | NumFormat }}</div>
              <div v-if="savingCardReduction.LargessAmount">赠金：¥ {{ savingCardReduction.LargessAmount | toFixed |
                NumFormat }}</div>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </div>

    <!-- 修改备注弹框 -->
    <el-dialog width="30%" :visible.sync="innerVisible" append-to-body>
      <span slot="title" class="text_center">修改销售单备注</span>
      <el-input type="textarea" :rows="4" v-model="Remark" placeholder="请输入备注内容"></el-input>
      <span slot="footer" class="dialog-footer">
        <el-button @click="innerVisible = false" v-prevent-click size="small">取消</el-button>
        <el-button type="primary" @click="updateRemarkClick" v-prevent-click size="small">保存</el-button>
      </span>
    </el-dialog>

    <!-- 修改 下单时间 -->
    <el-dialog width="30%" :visible.sync="ModifyBillDateVisible" append-to-body>
      <span slot="title" class="text_center">修改下单时间</span>

      <el-form :model="billDateForm" size="mini" ref="ModifyBillDateRef">
        <el-form-item label="原下单时间:">{{ saleOrderDetail.BillDate | dateFormat("YYYY-MM-DD HH:mm") }}</el-form-item>
        <el-form-item label="新下单时间:" prop="BillDate"
          :rules="[{ required: true, message: '请选择下单时间', trigger: ['blur', 'change'] }]">
          <el-date-picker v-model="billDateForm.BillDate" :picker-options="pickerOptions" size="small" type="datetime"
            default-time="09:00" placeholder="选择日期" format="yyyy-MM-dd HH:mm"></el-date-picker>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="ModifyBillDateVisible = false" v-prevent-click size="small">取消</el-button>
        <el-button type="primary" @click="changeBillDate" v-prevent-click size="small">保存</el-button>
      </span>
    </el-dialog>

    <!-- 修改 支付方式 -->
    <el-dialog width="30%" :visible.sync="ModifyPayMethodVisible" append-to-body>
      <span slot="title" class="text_center">修改支付方式</span>

      <el-form :model="payTypeForm" size="mini" ref="ModifyPayMethodRef">
        <el-form-item label="原支付方式：">
          <span class="marrt_10">{{ currentPayType.Name }}</span>
        </el-form-item>
        <el-form-item label="支付金额：">
          <span>¥ {{ currentPayType.Amount | toFixed | NumFormat }}</span>
        </el-form-item>
        <el-form-item label="新支付方式：" prop="payType"
          :rules="[{ required: true, message: '请选择支付方式', trigger: ['blur', 'change'] }]">
          <el-select v-model="payTypeForm.payType" filterable placeholder="请选择" clearable>
            <el-option v-for="item in payTypeList" :key="item.ID" :label="item.Name" :value="item.ID"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="ModifyPayMethodVisible = false" v-prevent-click size="small">取消</el-button>
        <el-button type="primary" @click="changPayTypeClick" v-prevent-click size="small">保存</el-button>
      </span>
    </el-dialog>

    <el-dialog :visible.sync="projectRemarkDialogVisible" title="备注" width="500px" append-to-body>
      <el-input type="textarea" :autosize="{ minRows: 2, maxRows: 4 }" maxlength="100" show-word-limit placeholder="请输入内容"
        v-model="projectRemark"></el-input>

      <div slot="footer" class="dialog-footer">
        <el-button @click="projectRemarkDialogVisible = false" v-prevent-click size="small" :disabled="RemarkLoading">取
          消</el-button>
        <el-button type="primary" @click="saveRemarkClick" :loading="RemarkLoading" v-prevent-click size="small">保
          存</el-button>
      </div>
    </el-dialog>
  </section>
</template>

<script>
import saleAPI from "@/api/iBeauty/Order/saleGoods";
import API from "@/api/iBeauty/Order/saleBill";
const dayjs = require("dayjs");
import 'dayjs/locale/zh-cn' // 导入本地化语言
dayjs.locale('zh-cn')

export default {
  name: "saleBillDetailContent",
  props: {
    saleOrderDetail: {
      type: Object,
      default: function () {
        return {};
      },
    },
    isModifyBillDate: {
      type: Boolean,
      default: false,
    },
    isModifyPayMethod: {
      type: Boolean,
      default: false,
    },
    isRemark: {
      type: Boolean,
      default: false,
    },
    ModifyBillDateRestriction: {
      type: Object,
      default() {
        return {
          IsHaveRestriction: false, //为true有限制
          Deadline: "", //截止日期
        };
      },

    },
    ModifyBillPayMethodRestriction: {
      type: Object,
      default() {
        return {
          IsHaveRestriction: false, //为true有限制
          Deadline: "",  //截止日期  
        };
      }
    }

  },
  /**  引入的组件  */
  components: {},
  /** 监听数据变化   */
  watch: {
    saleOrderDetail: {
      handler(newVal) {
        this.billDateForm.BillDate = newVal.BillDate;
      },
      deep: true,
    },
    ModifyBillDateRestriction: {
      handler(newVal) {
        if (newVal.IsHaveRestriction) {
          let DeadlineTime = dayjs(newVal.Deadline).valueOf();
          this.pickerOptions.disabledDate = (time) => {
            return time.getTime() > Date.now() || DeadlineTime > time.getTime()
          }
        } else {
          this.pickerOptions.disabledDate = (time) => {
            return time.getTime() > Date.now()
          }
        }

      },
      deep: true,
      immediate: true,
    }
  },

  /**  Vue 实例的数据对象**/
  data() {
    return {
      // default_time: dateTime.formatDate.format(),
      ModifyBillDateVisible: false,
      ModifyPayMethodVisible: false,
      changeMemberDialogVisible: false,
      innerVisible: false, //备注弹窗
      customerName: "",
      customerID: null,
      Remark: "",
      billDateForm: {
        BillDate: "",
      },
      payTypeForm: {
        payType: "",
      },

      payTypeList: [],
      showPayType: false,
      pickerOptions: {
        disabledDate(time) {
          return (
            time.getTime() > Date.now()
            // ||
            // time.getTime() < Date.now() - 3600 * 1000 * 24 * 7
          );
        },
      },
      currentPayType: "",

      RemarkLoading: false,
      projectRemarkDialogVisible: false,
      currentItem: "",
      projectRemark: "",
      goodsTypeName: "",
    };
  },
  /**  方法集合  */
  methods: {
    /**    */
    limitSealingAccount(BillDate, limit) {
      let isBefore = dayjs(BillDate).isBefore(dayjs(limit.Deadline));
      if (isBefore && limit.IsHaveRestriction) {
        return true;
      }
      return false;
    },
    /**    */
    getBillState(BillStatus) {
      switch (BillStatus) {
        case "10":
          return "待结账";
        case "15":
          return " 已付款（未完成）";
        case "20":
          return "已完成";
        case "30":
          return "已取消";
        default:
          return "";
      }
    },
    /**    */
    changeRemarkClick(item, type) {
      let that = this;
      that.currentItem = item;
      that.goodsTypeName = type;
      that.projectRemark = item.Remark;
      that.projectRemarkDialogVisible = true;
    },
    /**  修改备注  */
    saveRemarkClick() {
      let that = this;
      let SaleBillGoodID = "";
      switch (that.goodsTypeName) {
        case "updateProjectRemark":
          SaleBillGoodID = that.currentItem.SaleBillProjectID;
          break;
        case "updateProductRemark":
          SaleBillGoodID = that.currentItem.SaleBillProductID;
          break;
        case "updateGeneralCardRemark":
          SaleBillGoodID = that.currentItem.SaleBillGeneralCardID;
          break;
        case "updateTimeCardRemark":
          SaleBillGoodID = that.currentItem.SaleBillTimeCardID;
          break;
        case "updateSavingCardRemark":
          SaleBillGoodID = that.currentItem.SaleBillSavingCardID;
          break;
        case "updatePackageCardRemark":
          SaleBillGoodID = that.currentItem.SaleBillPackageCardID;
          break;
        case "updatePackageCardProductRemark":
          SaleBillGoodID = that.currentItem.SaleBillPackageCardProductID;
          break;
        case "updatePackageCardProjectRemark":
          SaleBillGoodID = that.currentItem.SaleBillPackageCardProjectID;
          break;
        case "updatePackageCardGeneralCardRemark":
          SaleBillGoodID = that.currentItem.SaleBillPackageCardGeneralCardID;
          break;
        case "updatePackageCardTimeCardRemark":
          SaleBillGoodID = that.currentItem.SaleBillPackageCardTimeCardID;
          break;
        case "updatePackageCardSavingCardRemark":
          SaleBillGoodID = that.currentItem.SaleBillPackageCardSavingCardID;
          break;
        default:
          that.$message.error("明细ID获取失败");
          return;
      }

      let item = {
        Remark: that.projectRemark,
        SaleBillGoodID: SaleBillGoodID,
      };
      that.$emit("saveRemark", item, that.goodsTypeName, () => {
        that.currentItem.Remark = that.projectRemark;
        that.$message.success("修改成功");
        that.projectRemarkDialogVisible = false;
      });
    },
    /**  点击修改支付方式  */
    ModifyPayMethodClick(salepay) {
      let that = this;
      that.ModifyPayMethodVisible = true;
      that.currentPayType = salepay;
    },
    /** 修改时间   */
    ModifyBillDateClick() {
      let that = this;
      that.ModifyBillDateVisible = true;
    },
    /**  修改支付方式  */
    changPayTypeClick() {
      let that = this;
      that.$refs.ModifyPayMethodRef.validate((valid) => {
        if (valid) {
          let payType = that.payTypeList.find((val) => val.ID == that.payTypeForm.payType);
          let params = {
            newInfo: payType,
            oldInfo: that.currentPayType,
          };
          that.$emit("changPayType", params, () => {
            that.ModifyPayMethodVisible = false;
            that.$message.success("修改成功");
          });
        }
      });
    },
    /** 修改下单时间   */
    changeBillDate() {
      let that = this;
      that.$refs.ModifyBillDateRef.validate((valid) => {
        if (valid) {
          that.$emit("ModifyBillDate", that.billDateForm.BillDate, () => {
            that.ModifyBillDateVisible = false;
            that.$message.success("修改成功");
          });
        }
      });
    },

    /**  获取订单类型   */
    getBillType(BillType) {
      switch (BillType) {
        case "10":
          return "销售订单";
        case "20":
          return "退款订单";
        case "30":
          return "补欠款单";
        case "40":
          return "充值订单";
      }
    },
    /**  查看套餐卡明细  */
    packDetailClick(item) {
      item.IsShowDetails = !item.IsShowDetails;
    },
    // 顾客
    saleCustomerData: function (queryString, cb) {
      var that = this;
      that.loading = true;
      var params = {
        Name: queryString ? queryString : "",
      };
      saleAPI
        .getSaleCustomer(params)
        .then((res) => {
          if (res.StateCode == 200) {
            cb(res.Data);
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },

    handleCustomerSelect(item) {
      if (item.ID != undefined) {
        this.customerID = item.ID;
        this.customerFullName = item.Name;
        this.customerPhoneNumber = item.PhoneNumber;
        this.customerName = item.Name + "【" + item.PhoneNumber + "】";
        var filter_hidephone = this.$options.filters["hidephone"];
        if (item.PhoneNumber) {
          this.customerName = item.Name + "【" + filter_hidephone(item.PhoneNumber) + "】";
        } else {
          this.customerName = item.Name;
        }
        // this.customerChange();
      } else {
        // this.addNewCustomer();
      }
    },

    removeCustomer() {
      var that = this;
      that.customerID = null;
      that.customerFullName = "";
      that.customerPhoneNumber = "";
      that.customerName = "";
    },

    /**  更新 顾客网络请求  */
    updateSaleBillBindCutsomer() {
      var that = this;
      that.changeMemberDialogVisible = false;
      var params = {
        BillID: that.saleOrderDetail.ID,
        CustomerID: that.customerID,
      };
      API.updateSaleBillBindCutsomer(params).then((res) => {
        if (res.StateCode == 200) {
          that.saleOrderDetail.Name = that.customerName;
          that.saleOrderDetail.CustomerID = " ";
          // that.handleSearch();
          that.$emit("refreshBillList");
        } else {
          that.$message.error({
            message: res.Message,
            duration: 2000,
          });
        }
      });
    },
    /**  修改备注   */
    ChangeRemarkinnerVisible() {
      let that = this;
      that.Remark = that.saleOrderDetail.Remark;
      that.innerVisible = true;
    },
    //修改备注
    updateRemarkClick() {
      var that = this;
      var params = {
        SaleBillID: that.saleOrderDetail.ID,
        Remark: that.Remark,
      };
      API.updateRemark(params).then((res) => {
        if (res.StateCode == 200) {
          that.saleOrderDetail.Remark = that.Remark;
          that.Remark = "";
          that.innerVisible = false;
        } else {
          that.$message.error({
            message: res.Message,
            duration: 2000,
          });
        }
      });
    },

    // 支付方式
    salePayMethodData: function () {
      var that = this;
      that.loading = true;
      API.getSalePayMethod()
        .then((res) => {
          if (res.StateCode == 200) {
            that.payTypeList = res.Data;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },
  },
  /**  实例被挂载后调用  */
  mounted() {
    let that = this;
    that.billDateForm.BillDate = that.saleOrderDetail.BillDate;
    that.salePayMethodData();
  },
};
</script>

<style lang="scss">
.saleBillDetailContent {
  .el-scrollbar_height {
    height: 100%;

    .el-scrollbar__wrap {
      overflow-x: hidden;
    }
  }

  .el-form-item__label {
    font-size: 13px !important;
  }

  .el-form-item__content {
    font-size: 13px !important;
  }

  .el-form-item {
    margin-bottom: 0px;
  }

  .saleHandler {
    .el-form-item__label {
      font-size: 12px !important;
      line-height: 18px;
      color: #c0c4cc;
    }

    .el-form-item__content {
      font-size: 12px !important;
      line-height: 20px;
      color: #c0c4cc;
    }

    .el-form-item {
      margin-bottom: 0px;
    }
  }

  .payInfo {
    .el-card__header {
      padding-top: 10px;
      padding-bottom: 10px;
    }

    .el-card__body {
      padding-top: 10px;
      padding-bottom: 10px;
    }
  }

  .saleInfo {
    .el-form-item__label {
      font-size: 13px !important;
    }

    .el-form-item__content {
      font-size: 13px !important;
    }

    .el-form-item {
      margin-bottom: 0px;
    }
  }
}

.customer-autocomplete-bill {
  li {
    line-height: normal;
    padding: 7px;

    .name {
      text-overflow: ellipsis;
      overflow: hidden;
    }

    .info {
      font-size: 12px;
      color: #b4b4b4;
    }

    .highlighted .info {
      color: #ddd;
    }
  }
}
</style>
