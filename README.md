# project

V4.1.0
test

## Project setup

```js
node.js 14.版本

npm install

npm install linq -S
cnpm install --save @riophae/vue-treeselect
npm install --save js-md5



// kendo
npm i @progress/kendo-editor-vue-wrapper@2023.1.314
npm i @progress/kendo-theme-default@4.38.1
npm i @progress/kendo-ui@2021.3.914

```

### Compiles and hot-reloads for development

```
npm run serve:test
```

### Compiles and minifies for production

```
npm run build:prod
```

### Run your tests

```
npm run test
```

### Lints and fixes files

```
npm run lint
```

### 高德地图 JSAPI

npm i @amap/amap-jsapi-loader --save

### Customize configuration

See [Configuration Reference](https://cli.vuejs.org/config/).

organizationManage 组织管理
permissions 权限管理
role 角色
roleList 角色权限
user 用户
organization 组织架构
jobtype 职务
mailList 通讯录
companyInfo 公司信息
companyInfo

    setGoods                设置商品
       payMethod             支付方式
       goodsClass           商品分类
       goodsManage          商品管理
         project            项目
         setProject         编辑项目
         product            产品
         setProduct         编辑产品
         timeCard           时效卡
         numberCard         通用次卡
         storedCard         储值卡
         mealCard           套餐卡

materialSetup 物料设置
productClassification 产品分类
productInfo 产品资料
unitManagement 单位管理
