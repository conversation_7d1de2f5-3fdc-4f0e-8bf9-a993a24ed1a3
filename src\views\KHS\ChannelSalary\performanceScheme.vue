<template>
  <div class="ChannelSalaryPerformanceScheme content_body">
    <!-- 头部 -->
    <div class="nav_header">
      <el-row>
        <el-col :span="23">
            <el-form-item label="渠道业绩取值方案">
              <el-input placeholder="输入渠道业绩取值方案" v-model="performModel.performanceName" clearable @clear="handleSearch"></el-input>
            </el-form-item>
            <el-form-item label="有效性">
              <el-select v-model="performModel.isValidity" placeholder="请选择有效性" clearable @change="handleSearch">
                <el-option label="有效" :value="true"></el-option>
                <el-option label="无效" :value="false"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" size="small" @click="handleSearch" v-prevent-click>搜索</el-button>
            </el-form-item>
          </el-form>
        </el-col>
        <el-col :span="1">
          <el-button type="primary" size="small" @click="handleShow" v-prevent-click>新增</el-button>
        </el-col>
      </el-row>
    </div>
    <!-- 表格 -->
    <el-table :data="performData" v-loading="loading" size="small">
      <el-table-column prop="Name" label="渠道取值方案"></el-table-column>
      <el-table-column prop="IsCalculateChannel" label="是否包含渠道业绩">
        <template slot-scope="scope">
          {{ scope.row.IsCalculateChannel ? "是" : "否" }}
        </template>
      </el-table-column>
      <el-table-column prop="IsCalculateIntroducer" label="是否包含介绍人业绩">
        <template slot-scope="scope">
          {{ scope.row.IsCalculateIntroducer ? "是" : "否" }}
        </template>
      </el-table-column>
      <el-table-column prop="Active" label="有效性">
        <template slot-scope="scope">
          {{ scope.row.Active ? "有效" : "无效" }}
        </template>
      </el-table-column>
      <el-table-column label="操作" width="80">
        <template slot-scope="scope">
          <el-button type="primary" size="small" @click="handleDetail(scope.row)" v-prevent-click>编辑</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <div class="pad_15 text_right">
      <el-pagination
        background
        v-if="paginations.total > 0"
        @current-change="handleCurrentChange"
        :current-page.sync="paginations.page"
        :page-size="paginations.page_size"
        :layout="paginations.layout"
        :total="paginations.total"
      ></el-pagination>
    </div>
    <!-- 新增弹框 -->
    <el-dialog title="新增渠道业绩取值方案" :visible.sync="dialogVisible" width="30%">
      <el-form :model="dialogForm" size="small" @submit.native.prevent :rules="rules" ref="refOne">
        <el-form-item label="渠道业绩取值方案" prop="PerformancePlan" label-width="140px">
          <el-input v-model="dialogForm.PerformancePlan"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false" size="small" v-prevent-click>取 消</el-button>
        <el-button type="primary" @click="PerformancePlanClick" v-prevent-click size="small">确 定</el-button>
      </span>
    </el-dialog>
    <!-- 编辑弹框 -->
    <el-dialog title="编辑渠道业绩取值方案" :visible.sync="editdialogVisible" width="1150px" custom-class="editDialog">
      <el-form :model="editdialogForm" :inline="true" size="small" @submit.native.prevent :rules="rules" ref="refTwo">
        <el-row>
          <el-form-item label="渠道业绩取值方案" prop="editPerformancePlan" label-width="140px">
            <el-input v-model="editdialogForm.editPerformancePlan"></el-input>
          </el-form-item>

          <el-form-item label="有效性" label-width="60px" prop="effectiveness">
            <el-radio v-model="editdialogForm.effectiveness" :label="true">有效</el-radio>
            <el-radio v-model="editdialogForm.effectiveness" :label="false">无效</el-radio>
          </el-form-item>
        </el-row>
        <el-row>
          <el-form-item prop="IsCalculateChannel">
            <el-checkbox v-model="editdialogForm.IsCalculateChannel">是否包含渠道业绩</el-checkbox>
          </el-form-item>
          <el-form-item prop="IsCalculateIntroducer">
            <el-checkbox v-model="editdialogForm.IsCalculateIntroducer">是否包含介绍人业绩</el-checkbox>
          </el-form-item>
        </el-row>
      </el-form>
      <el-tabs v-model="activeName">
        <el-tab-pane label="销售-产品" name="first">
          <el-table
            :data="saleProduct.ProductCategory"
            size="small"
            max-height="480px"
            row-key="CategoryID"
            :tree-props="{ children: 'Child' }"
            v-loading="saleProductLoading"
          >
            <el-table-column prop="CategoryName" label="产品分类"></el-table-column>
            <el-table-column prop="PayPerformanceRate" label="现金业绩">
              <template slot-scope="scope">
                <el-input
                  type="number"
                  size="mini"
                  min="0"
                  max="100"
                  v-model="scope.row.PayPerformanceRate"
                  v-input-fixed="2"
                  @input="royaltyRateChange(1, scope.row)"
                >
                  <template slot="append">%</template>
                </el-input>
              </template>
            </el-table-column>
            <el-table-column prop="SavingCardPerformanceRate" label="卡抵扣业绩">
              <template slot-scope="scope">
                <el-input
                  type="number"
                  size="mini"
                  min="0"
                  max="100"
                  v-model="scope.row.SavingCardPerformanceRate"
                  v-input-fixed="2"
                  @input="royaltyRateChange(1, scope.row)"
                >
                  <template slot="append">%</template>
                </el-input>
              </template>
            </el-table-column>
            <el-table-column prop="SavingCardPerformanceLargessRate" label="赠送卡抵扣业绩">
              <template slot-scope="scope">
                <el-input
                  type="number"
                  size="mini"
                  min="0"
                  max="100"
                  v-model="scope.row.SavingCardPerformanceLargessRate"
                  v-input-fixed="2"
                  @input="royaltyRateChange(1, scope.row)"
                >
                  <template slot="append">%</template>
                </el-input>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="130px">
              <template slot-scope="scope">
                <el-button v-if="scope.row.ParentID" type="primary" size="mini" @click="handleShowTwo(scope.row, 1)"> 产品业绩</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
        <el-tab-pane label="销售-项目" name="second">
          <el-table :data="saleProduct.ProjectCategory" size="small" max-height="480px" row-key="CategoryID" :tree-props="{ children: 'Child' }">
            <el-table-column prop="CategoryName" label="项目分类"></el-table-column>
            <el-table-column prop="PayPerformanceRate" label="现金业绩">
              <template slot-scope="scope">
                <el-input
                  type="number"
                  size="mini"
                  min="0"
                  max="100"
                  v-model="scope.row.PayPerformanceRate"
                  v-input-fixed="2"
                  @input="royaltyRateChange(2, scope.row)"
                >
                  <template slot="append">%</template>
                </el-input>
              </template>
            </el-table-column>
            <el-table-column prop="SavingCardPerformanceRate" label="卡抵扣业绩">
              <template slot-scope="scope">
                <el-input
                  type="number"
                  size="mini"
                  min="0"
                  max="100"
                  v-model="scope.row.SavingCardPerformanceRate"
                  v-input-fixed="2"
                  @input="royaltyRateChange(2, scope.row)"
                >
                  <template slot="append">%</template>
                </el-input>
              </template>
            </el-table-column>
            <el-table-column prop="SavingCardPerformanceLargessRate" label="赠送卡抵扣业绩">
              <template slot-scope="scope">
                <el-input
                  type="number"
                  size="mini"
                  min="0"
                  max="100"
                  v-model="scope.row.SavingCardPerformanceLargessRate"
                  v-input-fixed="2"
                  @input="royaltyRateChange(2, scope.row)"
                >
                  <template slot="append">%</template>
                </el-input>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="130px">
              <template slot-scope="scope">
                <el-button v-if="scope.row.ParentID" type="primary" size="mini" @click="handleShowTwo(scope.row, 2)"> 项目业绩</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
        <el-tab-pane label="销售-储值卡" name="third">
          <el-table :data="saleProduct.SavingCardCategory" size="small" max-height="480px" row-key="CategoryID" :tree-props="{ children: 'Child' }">
            <el-table-column prop="CategoryName" label="储值卡分类"></el-table-column>
            <el-table-column prop="PayPerformanceRate" label="现金业绩">
              <template slot-scope="scope">
                <el-input
                  type="number"
                  size="mini"
                  min="0"
                  max="100"
                  v-model="scope.row.PayPerformanceRate"
                  v-input-fixed="2"
                  @input="royaltyRateChange(3, scope.row)"
                >
                  <template slot="append">%</template>
                </el-input>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="130px">
              <template slot-scope="scope">
                <el-button type="primary" size="mini" @click="handleShowTwo(scope.row, 3)"> 储值卡业绩</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
        <el-tab-pane label="销售-时效卡" name="fourth">
          <el-table :data="saleProduct.TimeCardCategory" size="small" max-height="480px" row-key="CategoryID" :tree-props="{ children: 'Child' }">
            <el-table-column prop="CategoryName" label="时效卡分类"></el-table-column>
            <el-table-column prop="PayPerformanceRate" label="现金业绩">
              <template slot-scope="scope">
                <el-input
                  type="number"
                  size="mini"
                  min="0"
                  max="100"
                  v-model="scope.row.PayPerformanceRate"
                  v-input-fixed="2"
                  @input="royaltyRateChange(4, scope.row)"
                >
                  <template slot="append">%</template>
                </el-input>
              </template>
            </el-table-column>
            <el-table-column prop="SavingCardPerformanceRate" label="卡抵扣业绩">
              <template slot-scope="scope">
                <el-input
                  type="number"
                  size="mini"
                  min="0"
                  max="100"
                  v-model="scope.row.SavingCardPerformanceRate"
                  v-input-fixed="2"
                  @input="royaltyRateChange(4, scope.row)"
                >
                  <template slot="append">%</template>
                </el-input>
              </template>
            </el-table-column>
            <el-table-column prop="SavingCardPerformanceLargessRate" label="赠送卡抵扣业绩">
              <template slot-scope="scope">
                <el-input
                  type="number"
                  size="mini"
                  min="0"
                  max="100"
                  v-model="scope.row.SavingCardPerformanceLargessRate"
                  v-input-fixed="2"
                  @input="royaltyRateChange(4, scope.row)"
                >
                  <template slot="append">%</template>
                </el-input>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="130px">
              <template slot-scope="scope">
                <el-button type="primary" size="mini" @click="handleShowTwo(scope.row, 4)"> 时效卡业绩</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
        <el-tab-pane label="销售-通用次卡" name="fifth">
          <el-table :data="saleProduct.GeneralCardCategory" size="small" max-height="480px" row-key="CategoryID" :tree-props="{ children: 'Child' }">
            <el-table-column prop="CategoryName" label="通用次卡分类"></el-table-column>
            <el-table-column prop="PayPerformanceRate" label="现金业绩">
              <template slot-scope="scope">
                <el-input
                  type="number"
                  size="mini"
                  min="0"
                  max="100"
                  v-model="scope.row.PayPerformanceRate"
                  v-input-fixed="2"
                  @input="royaltyRateChange(5, scope.row)"
                >
                  <template slot="append">%</template>
                </el-input>
              </template>
            </el-table-column>
            <el-table-column prop="SavingCardPerformanceRate" label="卡抵扣业绩">
              <template slot-scope="scope">
                <el-input
                  type="number"
                  size="mini"
                  min="0"
                  max="100"
                  v-model="scope.row.SavingCardPerformanceRate"
                  v-input-fixed="2"
                  @input="royaltyRateChange(5, scope.row)"
                >
                  <template slot="append">%</template>
                </el-input>
              </template>
            </el-table-column>
            <el-table-column prop="SavingCardPerformanceLargessRate" label="赠送卡抵扣业绩">
              <template slot-scope="scope">
                <el-input
                  type="number"
                  size="mini"
                  min="0"
                  max="100"
                  v-model="scope.row.SavingCardPerformanceLargessRate"
                  v-input-fixed="2"
                  @input="royaltyRateChange(5, scope.row)"
                >
                  <template slot="append">%</template>
                </el-input>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="130px">
              <template slot-scope="scope">
                <el-button type="primary" size="mini" @click="handleShowTwo(scope.row, 5)"> 通用次卡业绩</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
        <el-tab-pane label="消耗-产品" name="sixth">
          <el-table
            :data="saleProduct.TreatProductCategory"
            size="small"
            max-height="480px"
            row-key="CategoryID"
            :tree-props="{ children: 'Child', hasChildren: 'hasChildren' }"
          >
            <el-table-column prop="CategoryName" label="产品分类"></el-table-column>
            <el-table-column prop="PerformancePayRate" label="现金业绩">
              <template slot-scope="scope">
                <el-input
                  type="number"
                  size="mini"
                  min="0"
                  max="100"
                  v-model="scope.row.PerformancePayRate"
                  v-input-fixed="2"
                  @input="royaltyRateChange(6, scope.row)"
                >
                  <template slot="append">%</template>
                </el-input>
              </template>
            </el-table-column>
            <el-table-column prop="PerformanceCardRate" label="卡抵扣业绩">
              <template slot-scope="scope">
                <el-input
                  type="number"
                  size="mini"
                  min="0"
                  max="100"
                  v-model="scope.row.PerformanceCardRate"
                  v-input-fixed="2"
                  @input="royaltyRateChange(6, scope.row)"
                >
                  <template slot="append">%</template>
                </el-input>
              </template>
            </el-table-column>
            <el-table-column prop="PerformanceCardLargessRate" label="赠送卡抵扣业绩">
              <template slot-scope="scope">
                <el-input
                  type="number"
                  size="mini"
                  min="0"
                  max="100"
                  v-model="scope.row.PerformanceCardLargessRate"
                  v-input-fixed="2"
                  @input="royaltyRateChange(6, scope.row)"
                >
                  <template slot="append">%</template>
                </el-input>
              </template>
            </el-table-column>
            <el-table-column prop="PerformanceLargessRate" label="赠送业绩">
              <template slot-scope="scope">
                <el-input
                  type="number"
                  size="mini"
                  min="0"
                  max="100"
                  v-model="scope.row.PerformanceLargessRate"
                  v-input-fixed="2"
                  @input="royaltyRateChange(6, scope.row)"
                >
                  <template slot="append">%</template>
                </el-input>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="130px">
              <template slot-scope="scope">
                <el-button v-if="scope.row.ParentID" type="primary" size="mini" @click="handleShowTwo(scope.row, 6)"> 产品业绩</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
        <el-tab-pane label="消耗-项目" name="seventh">
          <el-table
            :data="saleProduct.TreatProjectCategory"
            size="small"
            max-height="480px"
            row-key="CategoryID"
            :tree-props="{ children: 'Child', hasChildren: 'hasChildren' }"
          >
            <el-table-column prop="CategoryName" label="项目分类"></el-table-column>
            <el-table-column prop="PerformancePayRate" label="现金业绩">
              <template slot-scope="scope">
                <el-input
                  type="number"
                  size="mini"
                  min="0"
                  max="100"
                  v-model="scope.row.PerformancePayRate"
                  v-input-fixed="2"
                  @input="royaltyRateChange(7, scope.row)"
                >
                  <template slot="append">%</template>
                </el-input>
              </template>
            </el-table-column>
            <el-table-column prop="PerformanceCardRate" label="卡抵扣业绩">
              <template slot-scope="scope">
                <el-input
                  type="number"
                  size="mini"
                  min="0"
                  max="100"
                  v-model="scope.row.PerformanceCardRate"
                  v-input-fixed="2"
                  @input="royaltyRateChange(7, scope.row)"
                >
                  <template slot="append">%</template>
                </el-input>
              </template>
            </el-table-column>
            <el-table-column prop="PerformanceCardLargessRate" label="赠送卡抵扣业绩">
              <template slot-scope="scope">
                <el-input
                  type="number"
                  size="mini"
                  min="0"
                  max="100"
                  v-model="scope.row.PerformanceCardLargessRate"
                  v-input-fixed="2"
                  @input="royaltyRateChange(7, scope.row)"
                >
                  <template slot="append">%</template>
                </el-input>
              </template>
            </el-table-column>
            <el-table-column prop="PerformanceLargessRate" label="赠送业绩">
              <template slot-scope="scope">
                <el-input
                  type="number"
                  size="mini"
                  min="0"
                  max="100"
                  v-model="scope.row.PerformanceLargessRate"
                  v-input-fixed="2"
                  @input="royaltyRateChange(7, scope.row)"
                >
                  <template slot="append">%</template>
                </el-input>
              </template>
            </el-table-column>
            <el-table-column prop="e" label="操作" width="130px">
              <template slot-scope="scope">
                <el-button v-if="scope.row.ParentID" type="primary" size="mini" @click="handleShowTwo(scope.row, 7)"> 项目业绩</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
        <el-tab-pane label="消耗-储值卡" name="treat-savingCard">
          <el-table :data="saleProduct.TreatSavingCardCategory" size="small" max-height="450px" row-key="CategoryID" :tree-props="{ children: 'Child' }">
            <el-table-column prop="CategoryName" label="商品分类"></el-table-column>
            <el-table-column label="卡抵扣业绩">
              <template slot-scope="scope">
                <el-input
                  type="number"
                  size="mini"
                  v-input-fixed
                  v-model="scope.row.PerformanceCardRate"
                  class="input_type"
                  @input="royaltyRateChange(6, scope.row)"
                >
                  <template slot="append">%</template>
                </el-input>
              </template>
            </el-table-column>
            <el-table-column label="赠送卡抵扣业绩">
              <template slot-scope="scope">
                <el-input
                  type="number"
                  size="mini"
                  v-input-fixed
                  v-model="scope.row.PerformanceCardLargessRate"
                  class="input_type"
                  @input="royaltyRateChange(7, scope.row)"
                >
                  <template slot="append">%</template>
                </el-input>
              </template>
            </el-table-column>

            <el-table-column label="操作" width="115px">
              <template slot-scope="scope">
                <el-button type="primary" size="mini" @click="handleTreatSavingCardCategoryShow(scope.row, 8)"> 储值卡业绩</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
        <el-tab-pane label="消耗-时效卡" name="treat-timeCard">
          <el-table :data="saleProduct.TreatTimeCardCategory" size="small" max-height="450px" row-key="CategoryID" :tree-props="{ children: 'Child' }">
            <el-table-column prop="CategoryName" label="商品分类"></el-table-column>

            <el-table-column label="现金业绩">
              <template slot-scope="scope">
                <el-input
                  type="number"
                  size="mini"
                  v-input-fixed
                  v-model="scope.row.PerformancePayRate"
                  class="input_type"
                  @input="royaltyRateChange(6, scope.row)"
                >
                  <template slot="append">%</template>
                </el-input>
              </template>
            </el-table-column>
            <el-table-column label="卡抵扣业绩">
              <template slot-scope="scope">
                <el-input
                  type="number"
                  size="mini"
                  v-input-fixed
                  v-model="scope.row.PerformanceCardRate"
                  class="input_type"
                  @input="royaltyRateChange(6, scope.row)"
                >
                  <template slot="append">%</template>
                </el-input>
              </template>
            </el-table-column>
            <el-table-column label="赠送卡抵扣业绩">
              <template slot-scope="scope">
                <el-input
                  type="number"
                  size="mini"
                  v-input-fixed="2"
                  v-model="scope.row.PerformanceCardLargessRate"
                  class="input_type"
                  @input="royaltyRateChange(6, scope.row)"
                >
                  <template slot="append">%</template>
                </el-input>
              </template>
            </el-table-column>
            <el-table-column label="赠送业绩">
              <template slot-scope="scope">
                <el-input
                  type="number"
                  size="mini"
                  v-input-fixed="2"
                  v-model="scope.row.PerformanceLargessRate"
                  class="input_type"
                  @input="royaltyRateChange(7, scope.row)"
                >
                  <template slot="append">%</template>
                </el-input>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="115px">
              <template slot-scope="scope">
                <el-button type="primary" size="mini" @click="handleTreatTimeCardCategoryShow(scope.row, 9)"> 时效卡业绩</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
        <el-tab-pane label="消耗-通用次卡" name="treat-generalCard">
          <el-table :data="saleProduct.TreatGeneralCardCategory" size="small" max-height="450px" row-key="CategoryID" :tree-props="{ children: 'Child' }">
            <el-table-column prop="CategoryName" label="商品分类"></el-table-column>

            <el-table-column label="现金业绩">
              <template slot-scope="scope">
                <el-input
                  type="number"
                  size="mini"
                  v-input-fixed="2"
                  v-model="scope.row.PerformancePayRate"
                  class="input_type"
                  @input="royaltyRateChange(6, scope.row)"
                >
                  <template slot="append">%</template>
                </el-input>
              </template>
            </el-table-column>
            <el-table-column label="卡抵扣业绩">
              <template slot-scope="scope">
                <el-input
                  type="number"
                  size="mini"
                  v-input-fixed="2"
                  v-model="scope.row.PerformanceCardRate"
                  class="input_type"
                  @input="royaltyRateChange(6, scope.row)"
                >
                  <template slot="append">%</template>
                </el-input>
              </template>
            </el-table-column>
            <el-table-column label="赠送卡抵扣业绩">
              <template slot-scope="scope">
                <el-input
                  type="number"
                  size="mini"
                  v-input-fixed="2"
                  v-model="scope.row.PerformanceCardLargessRate"
                  class="input_type"
                  @input="royaltyRateChange(6, scope.row)"
                >
                  <template slot="append">%</template>
                </el-input>
              </template>
            </el-table-column>
            <el-table-column label="赠送业绩">
              <template slot-scope="scope">
                <el-input
                  type="number"
                  size="mini"
                  v-input-fixed="2"
                  v-model="scope.row.PerformanceLargessRate"
                  class="input_type"
                  @input="royaltyRateChange(7, scope.row)"
                >
                  <template slot="append">%</template>
                </el-input>
              </template>
            </el-table-column>

            <el-table-column label="操作" width="125px">
              <template slot-scope="scope">
                <el-button type="primary" size="mini" @click="handleTreatGeneralCardCategoryShow(scope.row, 10)"> 通用次卡业绩</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
      </el-tabs>
      <span slot="footer" class="dialog-footer">
        <el-button @click="editdialogVisible = false" v-prevent-click size="small">取 消</el-button>
        <el-button type="primary" @click="editPerformancePlanClick" v-prevent-click size="small" :loading="confrimLoading">确 定</el-button>
      </span>
    </el-dialog>
    <!-- 产品业绩 -->
    <el-dialog :title="GoodsTitle" :visible.sync="editDialogVisibleTwo" width="1000px" custom-class="editDialog">
      <el-table :data="saleProductDetail" size="small" max-height="480px" v-loading="productLoading">
        <el-table-column prop="Name" label="产品名称"></el-table-column>
        <el-table-column prop="PayPerformanceRate" label="现金业绩">
          <template slot-scope="scope">
            <el-input
              type="number"
              size="mini"
              min="0"
              max="100"
              v-model="scope.row.PayPerformanceRate"
              v-input-fixed="2"
              @input="royaltyRateChangeTwo(scope.row)"
            >
              <template slot="append">%</template>
            </el-input>
          </template>
        </el-table-column>
        <el-table-column prop="SavingCardPerformanceRate" label="卡抵扣业绩">
          <template slot-scope="scope">
            <el-input
              type="number"
              size="mini"
              min="0"
              max="100"
              v-model="scope.row.SavingCardPerformanceRate"
              v-input-fixed="2"
              @input="royaltyRateChangeTwo(scope.row)"
            >
              <template slot="append">%</template>
            </el-input>
          </template>
        </el-table-column>
        <el-table-column prop="SavingCardPerformanceLargessRate" label="赠送卡抵扣业绩">
          <template slot-scope="scope">
            <el-input
              type="number"
              size="mini"
              min="0"
              max="100"
              v-model="scope.row.SavingCardPerformanceLargessRate"
              v-input-fixed="2"
              @input="royaltyRateChangeTwo(scope.row)"
            >
              <template slot="append">%</template>
            </el-input>
          </template>
        </el-table-column>
      </el-table>
      <span slot="footer" class="dialog-footer">
        <el-button @click="editDialogVisibleTwo = false" v-prevent-click size="small">取 消</el-button>
        <el-button type="primary" @click="editDialogVisibleTwoClick(1)" v-prevent-click size="small">确 定</el-button>
      </span>
    </el-dialog>
    <!-- 项目业绩 -->
    <el-dialog :title="GoodsTitle" :visible.sync="editDialogVisibleThree" width="1000px" custom-class="editDialog">
      <el-table :data="saleProjectDetail" size="small" max-height="480px" v-loading="productLoading">
        <el-table-column prop="Name" label="项目名称"></el-table-column>
        <el-table-column prop="PayPerformanceRate" label="现金业绩">
          <template slot-scope="scope">
            <el-input
              type="number"
              size="mini"
              min="0"
              max="100"
              v-model="scope.row.PayPerformanceRate"
              v-input-fixed="2"
              @input="royaltyRateChangeTwo(scope.row)"
            >
              <template slot="append">%</template>
            </el-input>
          </template>
        </el-table-column>
        <el-table-column prop="SavingCardPerformanceRate" label="卡抵扣业绩">
          <template slot-scope="scope">
            <el-input
              type="number"
              size="mini"
              min="0"
              max="100"
              v-model="scope.row.SavingCardPerformanceRate"
              v-input-fixed="2"
              @input="royaltyRateChangeTwo(scope.row)"
            >
              <template slot="append">%</template>
            </el-input>
          </template>
        </el-table-column>
        <el-table-column prop="SavingCardPerformanceLargessRate" label="赠送卡抵扣业绩">
          <template slot-scope="scope">
            <el-input
              type="number"
              size="mini"
              min="0"
              max="100"
              v-model="scope.row.SavingCardPerformanceLargessRate"
              v-input-fixed="2"
              @input="royaltyRateChangeTwo(scope.row)"
            >
              <template slot="append">%</template>
            </el-input>
          </template>
        </el-table-column>
      </el-table>
      <span slot="footer" class="dialog-footer">
        <el-button @click="editDialogVisibleThree = false" v-prevent-click size="small">取 消</el-button>
        <el-button type="primary" @click="editDialogVisibleTwoClick(2)" v-prevent-click size="small">确 定</el-button>
      </span>
    </el-dialog>
    <!-- 储值卡业绩 -->
    <el-dialog :title="GoodsTitle" :visible.sync="editDialogVisibleFour" width="750px" custom-class="editDialog">
      <el-table :data="saleSavingCardDetail" size="small" max-height="480px" v-loading="productLoading">
        <el-table-column prop="Name" label="储值卡名称"></el-table-column>
        <el-table-column prop="PayPerformanceRate" label="现金业绩" width="250">
          <template slot-scope="scope">
            <el-input
              type="number"
              size="mini"
              min="0"
              max="100"
              v-model="scope.row.PayPerformanceRate"
              v-input-fixed="2"
              @input="royaltyRateChangeTwo(scope.row)"
            >
              <template slot="append">%</template>
            </el-input>
          </template>
        </el-table-column>
        <!-- <el-table-column prop="SavingCardPerformanceRate" label="卡抵扣业绩">
          <template slot-scope="scope">
            <el-input type="number" size="mini" min="0" max="100" v-model="scope.row.SavingCardPerformanceRate" v-input-fixed="2" @input="royaltyRateChangeTwo(scope.row)">
              <template slot="append">%</template>
            </el-input>
          </template>
        </el-table-column> -->
        <!-- <el-table-column prop="SavingCardPerformanceLargessRate" label="赠送卡抵扣业绩">
          <template slot-scope="scope">
            <el-input type="number" size="mini" min="0" max="100" v-model="scope.row.SavingCardPerformanceLargessRate" v-input-fixed="2" @input="royaltyRateChangeTwo(scope.row)">
              <template slot="append">%</template>
            </el-input>
          </template>
        </el-table-column> -->
      </el-table>
      <span slot="footer" class="dialog-footer">
        <el-button @click="editDialogVisibleFour = false" v-prevent-click size="small">取 消</el-button>
        <el-button type="primary" @click="editDialogVisibleTwoClick(3)" v-prevent-click size="small">确 定</el-button>
      </span>
    </el-dialog>
    <!-- 时效卡业绩 -->
    <el-dialog :title="GoodsTitle" :visible.sync="editDialogVisibleFive" width="1000px" custom-class="editDialog">
      <el-table :data="saleTimeCardDetail" size="small" max-height="480px" v-loading="productLoading">
        <el-table-column prop="Name" label="时效卡名称"></el-table-column>
        <el-table-column prop="PayPerformanceRate" label="现金业绩">
          <template slot-scope="scope">
            <el-input
              type="number"
              size="mini"
              min="0"
              max="100"
              v-model="scope.row.PayPerformanceRate"
              v-input-fixed="2"
              @input="royaltyRateChangeTwo(scope.row)"
            >
              <template slot="append">%</template>
            </el-input>
          </template>
        </el-table-column>
        <el-table-column prop="SavingCardPerformanceRate" label="卡抵扣业绩">
          <template slot-scope="scope">
            <el-input
              type="number"
              size="mini"
              min="0"
              max="100"
              v-model="scope.row.SavingCardPerformanceRate"
              v-input-fixed="2"
              @input="royaltyRateChangeTwo(scope.row)"
            >
              <template slot="append">%</template>
            </el-input>
          </template>
        </el-table-column>
        <el-table-column prop="SavingCardPerformanceLargessRate" label="赠送卡抵扣业绩">
          <template slot-scope="scope">
            <el-input
              type="number"
              size="mini"
              min="0"
              max="100"
              v-model="scope.row.SavingCardPerformanceLargessRate"
              v-input-fixed="2"
              @input="royaltyRateChangeTwo(scope.row)"
            >
              <template slot="append">%</template>
            </el-input>
          </template>
        </el-table-column>
      </el-table>
      <span slot="footer" class="dialog-footer">
        <el-button @click="editDialogVisibleFive = false" v-prevent-click size="small">取 消</el-button>
        <el-button type="primary" @click="editDialogVisibleTwoClick(4)" v-prevent-click size="small">确 定</el-button>
      </span>
    </el-dialog>
    <!-- 通用次卡业绩 -->
    <el-dialog :title="GoodsTitle" :visible.sync="editDialogVisibleSix" width="1000px" custom-class="editDialog">
      <el-table :data="saleGeneralCardDetail" size="small" max-height="480px" v-loading="productLoading">
        <el-table-column prop="Name" label="通用次卡名称"></el-table-column>
        <el-table-column prop="PayPerformanceRate" label="现金业绩">
          <template slot-scope="scope">
            <el-input
              type="number"
              size="mini"
              min="0"
              max="100"
              v-model="scope.row.PayPerformanceRate"
              v-input-fixed="2"
              @input="royaltyRateChangeTwo(scope.row)"
            >
              <template slot="append">%</template>
            </el-input>
          </template>
        </el-table-column>
        <el-table-column prop="SavingCardPerformanceRate" label="卡抵扣业绩">
          <template slot-scope="scope">
            <el-input
              type="number"
              size="mini"
              min="0"
              max="100"
              v-model="scope.row.SavingCardPerformanceRate"
              v-input-fixed="2"
              @input="royaltyRateChangeTwo(scope.row)"
            >
              <template slot="append">%</template>
            </el-input>
          </template>
        </el-table-column>
        <el-table-column prop="SavingCardPerformanceLargessRate" label="赠送卡抵扣业绩">
          <template slot-scope="scope">
            <el-input
              type="number"
              size="mini"
              min="0"
              max="100"
              v-model="scope.row.SavingCardPerformanceLargessRate"
              v-input-fixed="2"
              @input="royaltyRateChangeTwo(scope.row)"
            >
              <template slot="append">%</template>
            </el-input>
          </template>
        </el-table-column>
      </el-table>
      <span slot="footer" class="dialog-footer">
        <el-button @click="editDialogVisibleSix = false" v-prevent-click size="small">取 消</el-button>
        <el-button type="primary" @click="editDialogVisibleTwoClick(5)" v-prevent-click size="small">确 定</el-button>
      </span>
    </el-dialog>
    <!-- 消耗--产品业绩 -->
    <el-dialog :title="GoodsTitle" :visible.sync="editDialogVisibleSeven" width="1000px" custom-class="editDialog">
      <el-table :data="saleTreatProductDetail" size="small" max-height="480px" v-loading="productLoading">
        <el-table-column prop="Name" label="产品名称"></el-table-column>
        <el-table-column prop="PerformancePayRate" label="现金业绩">
          <template slot-scope="scope">
            <el-input
              type="number"
              size="mini"
              min="0"
              max="100"
              v-model="scope.row.PerformancePayRate"
              v-input-fixed="2"
              @input="royaltyRateChangeTwo(scope.row, 1)"
            >
              <template slot="append">%</template>
            </el-input>
          </template>
        </el-table-column>
        <el-table-column prop="PerformanceCardRate" label="卡抵扣业绩">
          <template slot-scope="scope">
            <el-input
              type="number"
              size="mini"
              min="0"
              max="100"
              v-model="scope.row.PerformanceCardRate"
              v-input-fixed="2"
              @input="royaltyRateChangeTwo(scope.row, 1)"
            >
              <template slot="append">%</template>
            </el-input>
          </template>
        </el-table-column>
        <el-table-column prop="PerformanceCardLargessRate" label="赠送卡抵扣业绩">
          <template slot-scope="scope">
            <el-input
              type="number"
              size="mini"
              min="0"
              max="100"
              v-model="scope.row.PerformanceCardLargessRate"
              v-input-fixed="2"
              @input="royaltyRateChangeTwo(scope.row, 1)"
            >
              <template slot="append">%</template>
            </el-input>
          </template>
        </el-table-column>
        <el-table-column prop="PerformanceLargessRate" label="赠送业绩">
          <template slot-scope="scope">
            <el-input
              type="number"
              size="mini"
              min="0"
              max="100"
              v-model="scope.row.PerformanceLargessRate"
              v-input-fixed="2"
              @input="royaltyRateChangeTwo(scope.row, 1)"
            >
              <template slot="append">%</template>
            </el-input>
          </template>
        </el-table-column>
      </el-table>
      <span slot="footer" class="dialog-footer">
        <el-button @click="editDialogVisibleSeven = false" v-prevent-click size="small">取 消</el-button>
        <el-button type="primary" @click="editDialogVisibleTwoClick(6)" v-prevent-click size="small">确 定</el-button>
      </span>
    </el-dialog>
    <!-- 消耗--项目业绩 -->
    <el-dialog :title="GoodsTitle" :visible.sync="editDialogVisibleEight" width="1000px" custom-class="editDialog">
      <el-table :data="saleTreatProjectDetail" size="small" max-height="480px" v-loading="productLoading">
        <el-table-column prop="Name" label="项目名称"></el-table-column>
        <el-table-column prop="PerformancePayRate" label="现金业绩">
          <template slot-scope="scope">
            <el-input
              type="number"
              size="mini"
              min="0"
              max="100"
              v-model="scope.row.PerformancePayRate"
              v-input-fixed="2"
              @input="royaltyRateChangeTwo(scope.row, 1)"
            >
              <template slot="append">%</template>
            </el-input>
          </template>
        </el-table-column>
        <el-table-column prop="PerformanceCardRate" label="卡抵扣业绩">
          <template slot-scope="scope">
            <el-input
              type="number"
              size="mini"
              min="0"
              max="100"
              v-model="scope.row.PerformanceCardRate"
              v-input-fixed="2"
              @input="royaltyRateChangeTwo(scope.row, 1)"
            >
              <template slot="append">%</template>
            </el-input>
          </template>
        </el-table-column>
        <el-table-column prop="PerformanceCardLargessRate" label="赠送卡抵扣业绩">
          <template slot-scope="scope">
            <el-input
              type="number"
              size="mini"
              min="0"
              max="100"
              v-model="scope.row.PerformanceCardLargessRate"
              v-input-fixed="2"
              @input="royaltyRateChangeTwo(scope.row, 1)"
            >
              <template slot="append">%</template>
            </el-input>
          </template>
        </el-table-column>
        <el-table-column prop="PerformanceLargessRate" label="赠送业绩">
          <template slot-scope="scope">
            <el-input
              type="number"
              size="mini"
              min="0"
              max="100"
              v-model="scope.row.PerformanceLargessRate"
              v-input-fixed="2"
              @input="royaltyRateChangeTwo(scope.row, 1)"
            >
              <template slot="append">%</template>
            </el-input>
          </template>
        </el-table-column>
      </el-table>
      <span slot="footer" class="dialog-footer">
        <el-button @click="editDialogVisibleEight = false" v-prevent-click size="small">取 消</el-button>
        <el-button type="primary" @click="editDialogVisibleTwoClick(7)" v-prevent-click size="small">确 定</el-button>
      </span>
    </el-dialog>

    <!-- 消耗--储值卡 业绩 -->
    <el-dialog :title="GoodsTitle" :visible.sync="editDialogVisibleTreatSavingCard" width="1000px" custom-class="editDialog">
      <el-table :data="treatSavingCardDetail" size="small" max-height="480px" v-loading="productLoading">
        <el-table-column prop="Name" label="项目名称"></el-table-column>

        <el-table-column prop="PerformanceCardRate" label="卡抵扣业绩">
          <template slot-scope="scope">
            <el-input
              type="number"
              size="mini"
              min="0"
              max="100"
              v-model="scope.row.PerformanceCardRate"
              v-input-fixed="2"
              @input="royaltyRateChangeTwo(scope.row, 1)"
            >
              <template slot="append">%</template>
            </el-input>
          </template>
        </el-table-column>
        <el-table-column prop="PerformanceCardLargessRate" label="赠送卡抵扣业绩">
          <template slot-scope="scope">
            <el-input
              type="number"
              size="mini"
              min="0"
              max="100"
              v-model="scope.row.PerformanceCardLargessRate"
              v-input-fixed="2"
              @input="royaltyRateChangeTwo(scope.row, 1)"
            >
              <template slot="append">%</template>
            </el-input>
          </template>
        </el-table-column>

      </el-table>
      <span slot="footer" class="dialog-footer">
        <el-button @click="editDialogVisibleTreatSavingCard = false" v-prevent-click size="small">取 消</el-button>
        <el-button type="primary" @click="saveEditDialogTreatSavingCard" v-prevent-click size="small">确 定</el-button>
      </span>
    </el-dialog>

    <!-- 消耗--时效卡业绩 -->
    <el-dialog :title="GoodsTitle" :visible.sync="editDialogVisibleTreatTimeCard" width="1000px" custom-class="editDialog">
      <el-table :data="treatTimeCardDetail" size="small" max-height="480px" v-loading="productLoading">
        <el-table-column prop="Name" label="项目名称"></el-table-column>
        <el-table-column prop="PerformancePayRate" label="现金业绩">
          <template slot-scope="scope">
            <el-input
              type="number"
              size="mini"
              min="0"
              max="100"
              v-model="scope.row.PerformancePayRate"
              v-input-fixed="2"
              @input="royaltyRateChangeTwo(scope.row, 1)"
            >
              <template slot="append">%</template>
            </el-input>
          </template>
        </el-table-column>
        <el-table-column prop="PerformanceCardRate" label="卡抵扣业绩">
          <template slot-scope="scope">
            <el-input
              type="number"
              size="mini"
              min="0"
              max="100"
              v-model="scope.row.PerformanceCardRate"
              v-input-fixed="2"
              @input="royaltyRateChangeTwo(scope.row, 1)"
            >
              <template slot="append">%</template>
            </el-input>
          </template>
        </el-table-column>
        <el-table-column prop="PerformanceCardLargessRate" label="赠送卡抵扣业绩">
          <template slot-scope="scope">
            <el-input
              type="number"
              size="mini"
              min="0"
              max="100"
              v-model="scope.row.PerformanceCardLargessRate"
              v-input-fixed="2"
              @input="royaltyRateChangeTwo(scope.row, 1)"
            >
              <template slot="append">%</template>
            </el-input>
          </template>
        </el-table-column>
        <el-table-column prop="PerformanceLargessRate" label="赠送业绩">
          <template slot-scope="scope">
            <el-input
              type="number"
              size="mini"
              min="0"
              max="100"
              v-model="scope.row.PerformanceLargessRate"
              v-input-fixed="2"
              @input="royaltyRateChangeTwo(scope.row, 1)"
            >
              <template slot="append">%</template>
            </el-input>
          </template>
        </el-table-column>
      </el-table>
      <span slot="footer" class="dialog-footer">
        <el-button @click="editDialogVisibleTreatTimeCard = false" v-prevent-click size="small">取 消</el-button>
        <el-button type="primary" @click="saveEditDialogTreatTimeCard" v-prevent-click size="small">确 定</el-button>
      </span>
    </el-dialog>

    <!-- 消耗--通用次卡业绩 -->
    <el-dialog :title="GoodsTitle" :visible.sync="editDialogVisibleTreatGeneralCard" width="1000px" custom-class="editDialog">
      <el-table :data="treatGeneralCardDetail" size="small" max-height="480px" v-loading="productLoading">
        <el-table-column prop="Name" label="项目名称"></el-table-column>
        <el-table-column prop="PerformancePayRate" label="现金业绩">
          <template slot-scope="scope">
            <el-input
              type="number"
              size="mini"
              min="0"
              max="100"
              v-model="scope.row.PerformancePayRate"
              v-input-fixed="2"
              @input="royaltyRateChangeTwo(scope.row, 1)"
            >
              <template slot="append">%</template>
            </el-input>
          </template>
        </el-table-column>
        <el-table-column prop="PerformanceCardRate" label="卡抵扣业绩">
          <template slot-scope="scope">
            <el-input
              type="number"
              size="mini"
              min="0"
              max="100"
              v-model="scope.row.PerformanceCardRate"
              v-input-fixed="2"
              @input="royaltyRateChangeTwo(scope.row, 1)"
            >
              <template slot="append">%</template>
            </el-input>
          </template>
        </el-table-column>
        <el-table-column prop="PerformanceCardLargessRate" label="赠送卡抵扣业绩">
          <template slot-scope="scope">
            <el-input
              type="number"
              size="mini"
              min="0"
              max="100"
              v-model="scope.row.PerformanceCardLargessRate"
              v-input-fixed="2"
              @input="royaltyRateChangeTwo(scope.row, 1)"
            >
              <template slot="append">%</template>
            </el-input>
          </template>
        </el-table-column>
        <el-table-column prop="PerformanceLargessRate" label="赠送业绩">
          <template slot-scope="scope">
            <el-input
              type="number"
              size="mini"
              min="0"
              max="100"
              v-model="scope.row.PerformanceLargessRate"
              v-input-fixed="2"
              @input="royaltyRateChangeTwo(scope.row, 1)"
            >
              <template slot="append">%</template>
            </el-input>
          </template>
        </el-table-column>
      </el-table>
      <span slot="footer" class="dialog-footer">
        <el-button @click="editDialogVisibleTreatGeneralCard = false" v-prevent-click size="small">取 消</el-button>
        <el-button type="primary" @click="saveEditDialogTreatGeneralCard" v-prevent-click size="small">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import API from "@/api/KHS/ChannelSalary/performanceScheme";
export default {
  name: "ChannelSalaryPerformanceScheme",
  props: {},
  /** 监听数据变化   */
  watch: {},
  /**  引入的组件  */
  components: {},
  /**  Vue 实例的数据对象**/
  data() {
    return {
      confrimLoading: false,
      loading: false,
      productLoading: false, // 产品业绩loading
      saleProductLoading: false,
      dialogVisible: false, // 新增弹框
      editdialogVisible: false, // 编辑弹框
      editDialogVisibleTwo: false, // 产品业绩弹框
      editDialogVisibleThree: false, // 项目业绩弹框
      editDialogVisibleFour: false, // 储值卡业绩弹框
      editDialogVisibleFive: false, // 时效卡业绩弹框
      editDialogVisibleSix: false, // 通用次卡业绩弹框
      editDialogVisibleSeven: false, // 消耗产品业绩
      editDialogVisibleEight: false, // 消耗项目业绩
      editDialogVisibleTreatSavingCard: false,
      editDialogVisibleTreatTimeCard: false,
      editDialogVisibleTreatGeneralCard: false,
      // hasChildren: true, // tabs
      activeName: "first", // tabs
      GoodsTitle: "", //产品业绩标题
      PerformanceSchemeID: "", // 业绩方案ID
      CategoryID: "", // 分类ID
      performModel: {
        performanceName: "", // 渠道业绩取值方案
        isValidity: true, // 有效性
      },
      dialogForm: {
        PerformancePlan: "", // 新增渠道业绩取值方案
      },
      editdialogForm: {
        editPerformancePlan: "", // 新增渠道业绩取值方案
        effectiveness: true, // 有效性,
        IsCalculateChannel: true, // 是否计算渠道业绩
        IsCalculateIntroducer: true, // 是否计算介绍人业绩
      },
      performData: [], // 列表
      paginations: {
        page: 1, // 当前位于哪页
        total: 0, // 总数
        page_size: 10, // 1页显示多少条
        layout: "total, prev, pager, next,jumper", // 翻页属性
      },
      rules: {
        PerformancePlan: [
          {
            required: true,
            message: "请输入渠道业绩业绩取值方案名称",
            trigger: "blur",
          },
        ],
        editPerformancePlan: [
          {
            required: true,
            message: "请输入渠道业绩业绩取值方案名称",
            trigger: "blur",
          },
        ],
      },
      saleProduct: {}, // 销售-产品
      saleProductDetail: [], // 产品业绩
      saleProjectDetail: [], // 项目业绩
      saleSavingCardDetail: [], // 储值卡业绩
      saleTimeCardDetail: [], // 储值卡业绩
      saleGeneralCardDetail: [], // 通用次卡业绩
      saleTreatProductDetail: [], // 消耗-项目业绩详情
      saleTreatProjectDetail: [], // 消耗-产品业绩详情
      treatSavingCardDetail: [], // 消耗储值卡
      treatTimeCardDetail: [], // 消耗-时效卡
      treatGeneralCardDetail: [], // 消耗 通用次卡
    };
  },
  /**计算属性  */
  computed: {},
  /**  方法集合  */
  methods: {
    // 搜索
    handleSearch() {
      this.paginations.page = 1;
      this.getPerformanceData();
    },

    // 获取业绩取值列表
    getPerformanceData() {
      var that = this;
      let params = {
        PageNum: that.paginations.page,
        Name: that.performModel.performanceName,
        Active: that.performModel.isValidity,
      };
      API.queryPerformanceData(params).then((res) => {
        if (res.StateCode == 200) {
          that.performData = res.List;
          that.paginations.page_size = res.PageSize;
          that.paginations.total = res.Total;
        } else {
          that.$message.error(res.Message);
        }
      });
    },

    // 新增
    handleShow() {
      if (this.$refs.refOne) {
        this.$refs.refOne.clearValidate();
      }
      this.dialogForm = {
        PerformancePlan: "", // 新增渠道业绩取值方案
      };
      this.dialogVisible = true;
    },
    // 点击编辑-获取详情
    handleDetail(row) {
      var that = this;
      this.editdialogForm.editPerformancePlan = row.Name;
      this.PerformanceSchemeID = row.ID;
      this.editdialogForm.effectiveness = row.Active;
      this.editdialogForm.IsCalculateChannel = row.IsCalculateChannel;
      this.editdialogForm.IsCalculateIntroducer = row.IsCalculateIntroducer;
      this.activeName = "first";
      this.editdialogVisible = true;
      let params = {
        ID: row.ID,
      };
      this.saleProductLoading = true;
      API.detailPerformanceData(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.saleProduct = res.Data;
          } else {
            that.$message.error(res.Message);
          }
        })
        .finally(() => {
          this.saleProductLoading = false;
        });
    },
    // 分页
    handleCurrentChange(page) {
      this.paginations.page = page;
      this.getPerformanceData();
    },
    // 新增弹框点击确定
    PerformancePlanClick() {
      var that = this;
      that.$refs.refOne.validate((valid) => {
        if (!valid) return;
        let params = {
          Name: that.dialogForm.PerformancePlan,
        };
        API.createdPerformanceData(params).then((res) => {
          if (res.StateCode == 200) {
            that.$message.success("添加成功！");
            this.dialogVisible = false;
            that.getPerformanceData();
          } else {
            that.$message.error(res.Message);
          }
        });
      });
    },
    // 编辑弹框点击确定
    editPerformancePlanClick() {
      var that = this;
      let params = {
        ID: that.PerformanceSchemeID, //业绩方案ID
        Name: that.editdialogForm.editPerformancePlan, //名称
        Active: that.editdialogForm.effectiveness, //有效性
        IsCalculateChannel: that.editdialogForm.IsCalculateChannel, //是否计算渠道业绩
        IsCalculateIntroducer: that.editdialogForm.IsCalculateIntroducer, //是否计算介绍人业绩
        ProductCategory: that.saleProduct.ProductCategory, //销售产品
        ProjectCategory: that.saleProduct.ProjectCategory, //销售项目
        GeneralCardCategory: that.saleProduct.GeneralCardCategory, //销售通用次卡
        TimeCardCategory: that.saleProduct.TimeCardCategory, //销售时效卡
        SavingCardCategory: that.saleProduct.SavingCardCategory, //销售储值卡
        TreatProductCategory: that.saleProduct.TreatProductCategory, //消耗产品
        TreatProjectCategory: that.saleProduct.TreatProjectCategory, //消耗项目
        TreatGeneralCardCategory: that.saleProduct.TreatGeneralCardCategory, //消耗项目
        TreatTimeCardCategory: that.saleProduct.TreatTimeCardCategory, //消耗项目
        TreatSavingCardCategory: that.saleProduct.TreatSavingCardCategory, //消耗项目
      };
      that.confrimLoading = true;
      API.uploadPerformanceData(params).then((res) => {
        this.confrimLoading = false;
        if (res.StateCode == 200) {
          that.editdialogVisible = false;
          that.getPerformanceData();
          that.$message.success("保存成功！");
        } else {
          that.$message.error(res.Message);
        }
      });
    },
    // 销售-产品input
    royaltyRateChange(a, row) {
      if (row.PayPerformanceRate !== "" && Number(row.PayPerformanceRate) > 100) {
        row.PayPerformanceRate = 100;
      }
      if (row.SavingCardPerformanceRate !== "" && Number(row.SavingCardPerformanceRate) > 100) {
        row.SavingCardPerformanceRate = 100;
      }
      if (row.SavingCardPerformanceLargessRate !== "" && Number(row.SavingCardPerformanceLargessRate) > 100) {
        row.SavingCardPerformanceLargessRate = 100;
      }

      if (a == 6 || a == 7) {
        if (row.PerformancePayRate !== "" && Number(row.PerformancePayRate) > 100) {
          row.PerformancePayRate = 100;
        }
        if (row.PerformanceCardRate !== "" && Number(row.PerformanceCardRate) > 100) {
          row.PerformanceCardRate = 100;
        }
        if (row.PerformanceCardLargessRate !== "" && Number(row.PerformanceCardLargessRate) > 100) {
          row.PerformanceCardLargessRate = 100;
        }
        if (row.PerformanceLargessRate !== "" && Number(row.PerformanceLargessRate) > 100) {
          row.PerformanceLargessRate = 100;
        }
      }
    },
    // 销售取值详情
    royaltyRateChangeTwo(row, a) {
      if (row.PayPerformanceRate !== "" && Number(row.PayPerformanceRate) > 100) {
        row.PayPerformanceRate = 100;
      }
      if (row.SavingCardPerformanceRate !== "" && Number(row.SavingCardPerformanceRate) > 100) {
        row.SavingCardPerformanceRate = 100;
      }
      if (row.SavingCardPerformanceLargessRate !== "" && Number(row.SavingCardPerformanceLargessRate) > 100) {
        row.SavingCardPerformanceLargessRate = 100;
      }

      if (a == 1) {
        if (row.PerformancePayRate !== "" && Number(row.PerformancePayRate) > 100) {
          row.PerformancePayRate = 100;
        }
        if (row.PerformanceCardRate !== "" && Number(row.PerformanceCardRate) > 100) {
          row.PerformanceCardRate = 100;
        }
        if (row.PerformanceCardLargessRate !== "" && Number(row.PerformanceCardLargessRate) > 100) {
          row.PerformanceCardLargessRate = 100;
        }
        if (row.PerformanceLargessRate !== "" && Number(row.PerformanceLargessRate) > 100) {
          row.PerformanceLargessRate = 100;
        }
      }
    },
    // 点击显示业绩
    handleShowTwo(row, a) {
      this.CategoryID = row.CategoryID;
      if (a == 1) {
        // 显示产品业绩
        this.GoodsTitle = `产品销售业绩取值-${row.ParentName}-${row.CategoryName}`;
        this.editDialogVisibleTwo = true;
        this.productPerformance(row);
      } else if (a == 2) {
        // 显示项目业绩
        this.GoodsTitle = `项目销售业绩取值-${row.ParentName}-${row.CategoryName}`;
        this.editDialogVisibleThree = true;
        this.projectPerformance(row);
      } else if (a == 3) {
        // 显示储值卡业绩
        this.GoodsTitle = `储值卡销售业绩取值-${row.CategoryName}`;
        this.editDialogVisibleFour = true;
        this.savingCardPerformance(row);
      } else if (a == 4) {
        // 显示效卡业绩
        this.GoodsTitle = `时效卡销售业绩取值-${row.CategoryName}`;
        this.editDialogVisibleFive = true;
        this.timeCardPerformance(row);
      } else if (a == 5) {
        // 显示通用次卡业绩
        this.GoodsTitle = `通用次卡销售业绩取值-${row.CategoryName}`;
        this.editDialogVisibleSix = true;
        this.generalCardPerformance(row);
      } else if (a == 6) {
        // 显示消耗产品业绩
        this.GoodsTitle = `产品消耗业绩取值-${row.ParentName}-${row.CategoryName}`;
        this.editDialogVisibleSeven = true;
        this.saleProductPerformance(row);
      } else if (a == 7) {
        // 显示消耗项目业绩
        this.GoodsTitle = `项目消耗业绩取值-${row.ParentName}-${row.CategoryName}`;
        this.editDialogVisibleEight = true;
        this.saleProjectPerformance(row);
      }
    },
    /**  消耗-储值卡  */
    handleTreatSavingCardCategoryShow(row) {
      let that = this;
      that.CategoryID = row.CategoryID;
      // 显示消耗项目业绩
      this.GoodsTitle = `储值卡消耗业绩取值-${row.CategoryName}`;
      this.channelPerformanceSchemeTreatSavingCard_all();
      this.editDialogVisibleTreatSavingCard = true;
    },
    /**  消耗-时效卡  */
    handleTreatTimeCardCategoryShow(row) {
      let that = this;
      that.CategoryID = row.CategoryID;
      // 显示消耗项目业绩
      this.GoodsTitle = `时效卡消耗业绩取值-${row.CategoryName}`;
      this.channelPerformanceSchemeTreatTimeCard_all();
      this.editDialogVisibleTreatTimeCard = true;
    },
    /**  消耗-通用次卡  */
    handleTreatGeneralCardCategoryShow(row) {
      let that = this;
      that.CategoryID = row.CategoryID;
      // 显示消耗项目业绩
      this.GoodsTitle = `通用次卡消耗业绩取值-${row.CategoryName}`;
      this.channelPerformanceSchemeTreatGeneralCard_all();
      this.editDialogVisibleTreatGeneralCard = true;
    },
    // 产品业绩详情
    productPerformance(row) {
      var that = this;
      let params = {
        PerformanceSchemeID: that.PerformanceSchemeID,
        CategoryID: row.CategoryID,
      };
      that.productLoading = true;
      API.PerformanceDataProductDetail(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.saleProductDetail = res.Data;
          } else {
            that.$message.error(res.Message);
          }
        })
        .finally(() => {
          that.productLoading = false;
        });
    },
    // 项目业绩详情
    projectPerformance(row) {
      var that = this;
      let params = {
        PerformanceSchemeID: that.PerformanceSchemeID,
        CategoryID: row.CategoryID,
      };
      that.productLoading = true;
      API.PerformanceDataProjectDetail(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.saleProjectDetail = res.Data;
          } else {
            that.$message.error(res.Message);
          }
        })
        .finally(() => {
          that.productLoading = false;
        });
    },
    // 储值卡业绩详情
    savingCardPerformance(row) {
      var that = this;
      let params = {
        PerformanceSchemeID: that.PerformanceSchemeID,
        CategoryID: row.CategoryID,
      };
      that.productLoading = true;
      API.PerformanceDataSavingCardDetail(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.saleSavingCardDetail = res.Data;
          } else {
            that.$message.error(res.Message);
          }
        })
        .finally(() => {
          that.productLoading = false;
        });
    },
    // 时效卡业绩详情
    timeCardPerformance(row) {
      var that = this;
      let params = {
        PerformanceSchemeID: that.PerformanceSchemeID,
        CategoryID: row.CategoryID,
      };
      that.productLoading = true;
      API.PerformanceDataTimeCardDetail(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.saleTimeCardDetail = res.Data;
          } else {
            that.$message.error(res.Message);
          }
        })
        .finally(() => {
          that.productLoading = false;
        });
    },
    // 通用次卡业绩详情
    generalCardPerformance(row) {
      var that = this;
      let params = {
        PerformanceSchemeID: that.PerformanceSchemeID,
        CategoryID: row.CategoryID,
      };
      that.productLoading = true;
      API.PerformanceDataGeneralCardDetail(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.saleGeneralCardDetail = res.Data;
          } else {
            that.$message.error(res.Message);
          }
        })
        .finally(() => {
          that.productLoading = false;
        });
    },
    // 消耗--产品业绩详情
    saleProductPerformance(row) {
      var that = this;
      let params = {
        PerformanceSchemeID: that.PerformanceSchemeID,
        CategoryID: row.CategoryID,
      };
      that.productLoading = true;
      API.PerformanceConsumeProductDetail(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.saleTreatProductDetail = res.Data;
          } else {
            that.$message.error(res.Message);
          }
        })
        .finally(() => {
          that.productLoading = false;
        });
    },
    // 消耗--产品业绩详情
    saleProjectPerformance(row) {
      var that = this;
      let params = {
        PerformanceSchemeID: that.PerformanceSchemeID,
        CategoryID: row.CategoryID,
      };
      that.productLoading = true;
      API.PerformanceConsumeProjectDetail(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.saleTreatProjectDetail = res.Data;
          } else {
            that.$message.error(res.Message);
          }
        })
        .finally(() => {
          that.productLoading = false;
        });
    },
    /** 储值卡   */
    channelPerformanceSchemeTreatSavingCard_all() {
      let that = this;
      let params = {
        PerformanceSchemeID: that.PerformanceSchemeID,
        CategoryID: that.CategoryID,
      };
      API.channelPerformanceSchemeTreatSavingCard_all(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.treatSavingCardDetail = res.Data;
          } else {
            that.$message.error(res.Message);
          }
        })
        .catch((fail) => {
          that.$message.error(fail.Message);
        });
    },
    /** 时效卡   */
    channelPerformanceSchemeTreatTimeCard_all() {
      let that = this;
      let params = {
        PerformanceSchemeID: that.PerformanceSchemeID,
        CategoryID: that.CategoryID,
      };
      API.channelPerformanceSchemeTreatTimeCard_all(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.treatTimeCardDetail = res.Data;
          } else {
            that.$message.error(res.Message);
          }
        })
        .catch((fail) => {
          that.$message.error(fail.Message);
        });
    },
    /** 通用次卡   */
    channelPerformanceSchemeTreatGeneralCard_all() {
      let that = this;
      let params = {
        PerformanceSchemeID: that.PerformanceSchemeID,
        CategoryID: that.CategoryID,
      };
      API.channelPerformanceSchemeTreatGeneralCard_all(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.treatGeneralCardDetail = res.Data;
          } else {
            that.$message.error(res.Message);
          }
        })
        .catch((fail) => {
          that.$message.error(fail.Message);
        });
    },

    // 业绩保存
    editDialogVisibleTwoClick(state) {
      var that = this;
      if (state == 1) {
        let params = {
          PerformanceSchemeID: that.PerformanceSchemeID,
          CategoryID: that.CategoryID,
          Good: that.saleProductDetail
            .filter((i) => i.PayPerformanceRate || i.SavingCardPerformanceLargessRate || i.SavingCardPerformanceRate)
            .map((i) => {
              return {
                GoodID: i.ID,
                PayPerformanceRate: i.PayPerformanceRate,
                SavingCardPerformanceLargessRate: i.SavingCardPerformanceLargessRate,
                SavingCardPerformanceRate: i.SavingCardPerformanceRate,
              };
            }),
        };
        API.PerformanceDataProductCreate(params).then((res) => {
          if (res.StateCode == 200) {
            that.$message.success("保存成功！");
            that.editDialogVisibleTwo = false;
          } else {
            that.$message.error(res.Message);
          }
        });
      } else if (state == 2) {
        // that.saleProjectDetail.forEach((item) => {
        //   item.GoodID = item.ID;
        //   if (item.ID) {
        //     delete item.ID;
        //   }
        //   delete item.Name;
        // });
        let params = {
          PerformanceSchemeID: that.PerformanceSchemeID,
          CategoryID: that.CategoryID,
          Good: that.saleProjectDetail
            .filter((i) => i.PayPerformanceRate || i.SavingCardPerformanceLargessRate || i.SavingCardPerformanceRate)
            .map((i) => {
              return {
                GoodID: i.ID,
                PayPerformanceRate: i.PayPerformanceRate,
                SavingCardPerformanceLargessRate: i.SavingCardPerformanceLargessRate,
                SavingCardPerformanceRate: i.SavingCardPerformanceRate,
              };
            }),
        };
        API.PerformanceDataProjectCreate(params).then((res) => {
          if (res.StateCode == 200) {
            that.$message.success("保存成功！");
            that.editDialogVisibleThree = false;
          } else {
            that.$message.error(res.Message);
          }
        });
      } else if (state == 3) {
        that.saleSavingCardDetail.forEach((item) => {
          item.GoodID = item.ID;
          if (item.ID) {
            delete item.ID;
          }
          delete item.Name;
        });
        let params = {
          PerformanceSchemeID: that.PerformanceSchemeID,
          CategoryID: that.CategoryID,
          Good: that.saleSavingCardDetail,
        };
        API.PerformanceDataSavingCardCreate(params).then((res) => {
          if (res.StateCode == 200) {
            that.$message.success("保存成功！");
            that.editDialogVisibleFour = false;
          } else {
            that.$message.error(res.Message);
          }
        });
      } else if (state == 4) {
        // that.saleTimeCardDetail.forEach((item) => {
        //   item.GoodID = item.ID;
        //   if (item.ID) {
        //     delete item.ID;
        //   }
        //   delete item.Name;
        // });
        let params = {
          PerformanceSchemeID: that.PerformanceSchemeID,
          CategoryID: that.CategoryID,
          Good: that.saleTimeCardDetail
            .filter((i) => i.PayPerformanceRate)
            .map((i) => {
              return {
                GoodID: i.ID,
                PayPerformanceRate: i.PayPerformanceRate,
                SavingCardPerformanceLargessRate: i.SavingCardPerformanceLargessRate,
                SavingCardPerformanceRate: i.SavingCardPerformanceRate,
              };
            }),
        };
        API.PerformanceDataTimeCardCreate(params).then((res) => {
          if (res.StateCode == 200) {
            that.$message.success("保存成功！");
            that.editDialogVisibleFive = false;
          } else {
            that.$message.error(res.Message);
          }
        });
      } else if (state == 5) {
        // that.saleGeneralCardDetail.forEach((item) => {
        //   item.GoodID = item.ID;
        //   if (item.ID) {
        //     delete item.ID;
        //   }
        //   delete item.Name;
        // });
        let params = {
          PerformanceSchemeID: that.PerformanceSchemeID,
          CategoryID: that.CategoryID,
          Good: that.saleGeneralCardDetail
            .filter((i) => i.PayPerformanceRate || i.SavingCardPerformanceLargessRate || i.SavingCardPerformanceRate)
            .map((i) => {
              return {
                GoodID: i.ID,
                PayPerformanceRate: i.PayPerformanceRate,
                SavingCardPerformanceLargessRate: i.SavingCardPerformanceLargessRate,
                SavingCardPerformanceRate: i.SavingCardPerformanceRate,
              };
            }),
        };
        API.PerformanceDataGeneralCardCreate(params).then((res) => {
          if (res.StateCode == 200) {
            that.$message.success("保存成功！");
            that.editDialogVisibleSix = false;
          } else {
            that.$message.error(res.Message);
          }
        });
      } else if (state == 6) {
        // that.saleTreatProductDetail.forEach((item) => {
        //   item.GoodID = item.ID;
        //   if (item.ID) {
        //     delete item.ID;
        //   }
        //   delete item.Name;
        // });
        let paramsOne = {
          PerformanceSchemeID: that.PerformanceSchemeID,
          CategoryID: that.CategoryID,
          Good: that.saleTreatProductDetail
            .filter((i) => i.PerformanceCardLargessRate || i.PerformanceCardRate || i.PerformanceLargessRate || i.PerformancePayRate)
            .map((i) => {
              return {
                GoodID: i.ID,
                PerformancePayRate: i.PerformancePayRate,
                PerformanceLargessRate: i.PerformanceLargessRate,
                PerformanceCardRate: i.PerformanceCardRate,
                PerformanceCardLargessRate: i.PerformanceCardLargessRate,
              };
            }),
        };
        API.PerformanceConsumeProductCreate(paramsOne).then((res) => {
          if (res.StateCode == 200) {
            that.$message.success("保存成功！");
            that.editDialogVisibleSeven = false;
          } else {
            that.$message.error(res.Message);
          }
        });
      } else if (state == 7) {
    
        let params = {
          PerformanceSchemeID: that.PerformanceSchemeID,
          CategoryID: that.CategoryID,
          Good: that.saleTreatProjectDetail
            .filter((i) => i.PerformanceCardLargessRate || i.PerformanceCardRate || i.PerformanceLargessRate || i.PerformancePayRate)
            .map((i) => {
              return {
                GoodID: i.ID,
                PerformancePayRate: i.PerformancePayRate,
                PerformanceLargessRate: i.PerformanceLargessRate,
                PerformanceCardRate: i.PerformanceCardRate,
                PerformanceCardLargessRate: i.PerformanceCardLargessRate,
              };
            }),
        };
        API.PerformanceConsumeProjectCreate(params).then((res) => {
          if (res.StateCode == 200) {
            that.$message.success("保存成功！");
            that.editDialogVisibleEight = false;
          } else {
            that.$message.error(res.Message);
          }
        });
      }
    },

    /**  储值卡业绩保存 */
    async saveEditDialogTreatSavingCard() {
      let that = this;
      try {
        let params = {
          PerformanceSchemeID: that.PerformanceSchemeID, //业绩方案ID
          CategoryID: that.CategoryID, //分类ID
          Good: that.treatSavingCardDetail
            .filter((i) => i.PerformanceCardLargessRate || i.PerformanceCardRate || i.PerformanceLargessRate || i.PerformancePayRate)
            .map((i) => {
              return {
                GoodID: i.ID,
                PerformancePayRate: i.PerformancePayRate,
                PerformanceCardRate: i.PerformanceCardRate,
                PerformanceCardLargessRate: i.PerformanceCardLargessRate,
                PerformanceLargessRate: i.PerformanceLargessRate,
              };
            }),
        };
        let res = await API.channelPerformanceSchemeTreatSavingCard_create(params);
        if (res.StateCode == 200) {
          that.$message.success("保存成功！");
          that.editDialogVisibleTreatSavingCard = false;
        } else {
          that.$message.error(res.Message);
        }
      } catch (error) {
        that.$message.error(error);
      }
    },

    /**  时效卡业绩保存  */
    async saveEditDialogTreatTimeCard() {
      let that = this;
      try {
        let params = {
          PerformanceSchemeID: that.PerformanceSchemeID, //业绩方案ID
          CategoryID: that.CategoryID, //分类ID
          Good: that.treatTimeCardDetail
            .filter((i) => i.PerformanceCardLargessRate || i.PerformanceCardRate || i.PerformanceLargessRate || i.PerformancePayRate)
            .map((i) => {
              return {
                GoodID: i.ID,
                PerformancePayRate: i.PerformancePayRate,
                PerformanceCardRate: i.PerformanceCardRate,
                PerformanceCardLargessRate: i.PerformanceCardLargessRate,
                PerformanceLargessRate: i.PerformanceLargessRate,
              };
            }),
        };
        let res = await API.channelPerformanceSchemeTreatTimeCard_create(params);
        if (res.StateCode == 200) {
          that.$message.success("保存成功！");
          that.editDialogVisibleTreatTimeCard = false;
        } else {
          that.$message.error(res.Message);
        }
      } catch (error) {
        that.$message.error(error);
      }
    },

    /** 通用次卡业绩保存   */
    async saveEditDialogTreatGeneralCard() {
      let that = this;
      try {
        let params = {
          PerformanceSchemeID: that.PerformanceSchemeID, //业绩方案ID
          CategoryID: that.CategoryID, //分类ID
          Good: that.treatGeneralCardDetail
            .filter((i) => i.PerformanceCardLargessRate || i.PerformanceCardRate || i.PerformanceLargessRate || i.PerformancePayRate)
            .map((i) => {
              return {
                GoodID: i.ID,
                PerformancePayRate: i.PerformancePayRate,
                PerformanceCardRate: i.PerformanceCardRate,
                PerformanceCardLargessRate: i.PerformanceCardLargessRate,
                PerformanceLargessRate: i.PerformanceLargessRate,
              };
            }),
        };
        let res = await API.channelPerformanceSchemeTreatGeneralCard_create(params);
        if (res.StateCode == 200) {
          that.$message.success("保存成功！");
          that.editDialogVisibleTreatGeneralCard = false;
        } else {
          that.$message.error(res.Message);
        }
      } catch (error) {
        that.$message.error(error);
      }
    },
  },
  /** 创建实例之前执行函数   */
  beforeCreate() {},
  /**  实例创建完成之后  */
  created() {},
  /**  在挂载开始之前被调用  */
  beforeMount() {},
  /**  实例被挂载后调用  */
  mounted() {
    this.getPerformanceData();
  },
  /**  数据更新 之前 调用  */
  beforeUpdate() {},
  /** 数据更新 完成 调用   */
  updated() {},
  /**  实例销毁之前调用  */
  beforeDestroy() {},
  /**  实例销毁后调用  */
  destroyed() {},
};
</script>

<style lang="scss">
.ChannelSalaryPerformanceScheme {
  .editDialog {
    .el-input__inner {
      padding: 0 0 0 15px;
    }
  }
}
</style>
