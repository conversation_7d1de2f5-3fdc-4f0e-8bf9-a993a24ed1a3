<template>
  <div class="customergrowth content_body">
    <el-row class="tip color_333 font_14">
      <el-col :span="23" class="dis_flex">
        <div class="rduceTitle padlt_10">启用成长值</div>
        <div class="padlt_20 color_999">启用后，会员等级随成长值变化，可针对单个会员手动调整和锁定等级</div>
      </el-col>
      <el-col :span="1">
        <el-switch @change="uploadCustomerGrowthStatus" v-model="rduceSwutch" active-color="#ff8646" inactive-color="#dcdfe6"> </el-switch>
      </el-col>
    </el-row>
    <div v-if="rduceSwutch" class="martp_20">
      <el-table :data="growthData" size="small" :cell-class-name="goodsInfo">
        <el-table-column style="width: 120px" prop="CustomerGrowthTypeCodeName" label="成长值类型"></el-table-column>
        <el-table-column label="支付方式">
          <template slot-scope="scope">
            <div class="tableDetail" v-for="(itemDetail, indexDetail) in scope.row.Detail" :key="indexDetail">
              {{ itemDetail.CustomerGrowthPayChannelTypeName }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="成长值">
          <template slot-scope="scope">
            <div class="tableDetail" v-for="(itemDetail, indexDetail) in scope.row.Detail" :key="indexDetail">
              <el-input
                style="width: 120px; margin-right: 20px"
                v-input-fixed="0"
                v-model="itemDetail.Growth"
                size="small"
                placeholder="请输入成长值"
              ></el-input
              >点/元
            </div>
          </template>
        </el-table-column>
        <el-table-column label="日上限">
          <template slot-scope="scope">
            <div class="tableDetail" v-for="(itemDetail, indexDetail) in scope.row.Detail" :key="indexDetail">
              <el-radio-group v-model="itemDetail.Limited" @change="selectRadioChange(scope.row, indexDetail)">
                <el-radio :label="false">无上限</el-radio>
                <el-radio :label="true">
                  <el-input
                    style="width: 120px; margin-right: 20px"
                    :disabled="!itemDetail.Limited"
                    v-model="itemDetail.DayLimit"
                    size="small"
                    v-input-fixed="0"
                    placeholder="请输入"
                  ></el-input
                  >点/日
                </el-radio>
              </el-radio-group>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div v-if="rduceSwutch" class="dis_flex flex_x_center" style="margin-top: 20px">
      <el-button type="primary" size="small" @click="uploadCustomerGrowth">保 存</el-button>
    </div>
  </div>
</template>

<script>
import API from "@/api/CRM/CustomerLevel/customergrowth";
export default {
  name: "CustomerGrowth",

  props: {},
  /** 监听数据变化   */
  watch: {},
  /**  引入的组件  */
  components: {},
  /**  Vue 实例的数据对象**/
  data() {
    return {
      rduceSwutch: true, // 开关
      growthData: [],
      data: [{ title: "支付方式" }, { title: "成长值" }, { title: "日上线" }],
    };
  },
  /**计算属性  */
  computed: {},
  /**  方法集合  */
  methods: {
    // 添加类名
    goodsInfo(row) {
      if (row.columnIndex == 1 || row.columnIndex == 2 || row.columnIndex == 3) {
        return "goodsInfo";
      }
    },
    selectRadioChange(row, index) {
      if (!row.Detail[index].Limited) {
        row.Detail[index].DayLimit = "";
      }
    },
    // 获取成长值信息
    async getCustomerGrowth() {
      let params = {
        Name: "",
        Active: true,
      };
      let res = await API.getCustomerGrowth(params);
      if (res.StateCode == 200) {
        this.growthData = res.Data;
      } else {
        this.$message.error({
          message: res.Message,
          duration: 2000,
        });
      }
    },
    // 获取成长值状态
    async getCustomerGrowthStatus() {
      let res = await API.getCustomerGrowthStatus();
      if (res.StateCode == 200) {
        this.rduceSwutch = res.Data;
      } else {
        this.$message.error(res.Message);
      }
    },
    // 更新成长值状态
    async uploadCustomerGrowthStatus() {
      let params = {
        Status: this.rduceSwutch,
      };
      let res = await API.uploadCustomerGrowthStatus(params);
      if (res.StateCode == 200) {
        this.$message.success("操作成功");
        this.getCustomerGrowthStatus();
      } else {
        this.$message.error(res.Message);
      }
    },
    // 保存
    async uploadCustomerGrowth() {
      let that = this;
      var detail = [];
      that.growthData.forEach((item) => {
        item.Detail.forEach((itemDetail) => {
          var obj = new Object();
          obj.CustomerGrowthTypeCode = item.CustomerGrowthTypeCode;
          obj.CustomerGrowthPayChannelType = itemDetail.CustomerGrowthPayChannelType;
          obj.Growth = itemDetail.Growth;
          obj.Limited = itemDetail.Limited;
          obj.DayLimit = itemDetail.DayLimit;
          detail.push(obj);
        });
      });
      let params = {
        Detail: detail,
      };
      let res = await API.uploadCustomerGrowth(params);
      if (res.StateCode == 200) {
        that.$message.success("保存成功！");
        that.getCustomerGrowth();
      } else {
        that.$message.error(res.Message);
      }
    },
  },
  /** 创建实例之前执行函数   */
  beforeCreate() {},
  /**  实例创建完成之后  */
  created() {},
  /**  在挂载开始之前被调用  */
  beforeMount() {},
  /**  实例被挂载后调用  */
  mounted() {
    this.getCustomerGrowth();
    this.getCustomerGrowthStatus();
  },
  /**  数据更新 之前 调用  */
  beforeUpdate() {},
  /** 数据更新 完成 调用   */
  updated() {},
  /**  实例销毁之前调用  */
  beforeDestroy() {},
  /**  实例销毁后调用  */
  destroyed() {},
};
</script>

<style lang="scss">
.customergrowth {
  .el-table {
    .goodsInfo {
      padding: 0;
      .cell {
        padding: 0;
      }
    }
  }
  .tableDetail {
    height: 50px;
    line-height: 50px;
    padding-left: 10px;
    border: 1px solid #eee;
  }
}
</style>
