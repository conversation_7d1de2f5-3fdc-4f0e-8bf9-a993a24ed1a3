<template>
  <div class="Puzzle">
    <div v-if="!isPuzzleData()" class="empty-cube">点击编辑拼图</div>
    <div v-else class=" layout-content" :style="setLayoutStyle()">
      <div v-for="(item, index) in ContentProperty.imgs.filter(i => i.url)" :key="index" class="layout-item"
        :style="getPuzzleItemStyle(item)">
        <div class="img_wrap" v-if="item.url">
          <el-image :src="item.url" class="img"></el-image>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "Puzzle",

  props: {
    ContentProperty: {
      type: Object,
      default: () => {
        return {};
      },
    },

    ConfigProperty: {
      type: Object,
      default: () => {
        return {};
      },
    },
    phoneWidth: {
      type: Number,
      default: 375,
    }
  },
  /** 监听数据变化   */
  watch: {},
  /**  引入的组件  */
  components: {},
  /**  Vue 实例的数据对象**/
  data() {
    return {};
  },
  /**计算属性  */
  computed: {},
  /**  方法集合  */
  methods: {
    /**    */
    isPuzzleData() {
      let that = this;
      return that.ContentProperty.imgs.some(i => i.url);
    },
    /**  设置 魔方区域高度  */
    setLayoutStyle() {
      let that = this;
      let matrix = that.ContentProperty.matrix;
      let width = that.phoneWidth / matrix[1];
      let height = width * matrix[0];
      return {
        height: `${height}px`
      }
    },
    /* 设置 魔方 布局 样式 */
    getPuzzleItemStyle(item) {
      let that = this;
      let { margin } = that.ConfigProperty;
      let { matrix } = that.ContentProperty;
      let contentHeight = that.phoneWidth / matrix[1] * matrix[0];
      if (that.ContentProperty.key == 8) {
        let width, height, left, top = 0;
        width = (that.phoneWidth - margin * 3) / 4
        height = (contentHeight - margin * 3) / 4;
        left = item.start[0] * (width + margin);
        top = item.start[1] * (height + margin);
        return {
          width: `${width}px`,
          height: `${height}px`,
          left: `${left}px`,
          top: `${top}px`,
        }

      }
      else {
        if (matrix[0] == 1) {
          let marginValue = margin / matrix[1]
          let width, height, left, top = 0;
          width = 1 / matrix[1] * item.width * that.phoneWidth - marginValue;
          height = 1 / matrix[0] * item.height * contentHeight;
          left = 1 / matrix[1] * item.start[0] * that.phoneWidth + marginValue * item.start[0];
          top = 1 / matrix[0] * item.start[1] * contentHeight;
          return {
            width: `${width}px`,
            height: `${height}px`,
            left: `${left}px`,
            top: `${top}px`,
          }
        }
        if (matrix[0] == 2) {
          let widthMargin = margin * (1 - item.width / matrix[0]);
          let leftMargin = widthMargin * item.start[0];
          let heightMargin = margin * (1 - item.height / matrix[0]);
          let topMargin = heightMargin * item.start[1];
          let width, height, left, top = 0;
          width = 1 / matrix[1] * item.width * that.phoneWidth - widthMargin;
          height = 1 / matrix[0] * item.height * contentHeight - heightMargin;
          left = 1 / matrix[1] * item.start[0] * that.phoneWidth + leftMargin;
          top = 1 / matrix[0] * item.start[1] * contentHeight + topMargin;
          return {
            width: `${width}px`,
            height: `${height}px`,
            left: `${left}px`,
            top: `${top}px`,
          }
        }
        if (matrix[0] == 4) {
          let widthMargin = margin / matrix[0];
          let leftMargin = widthMargin * item.start[0];

          if (item.start[0] == 2 && item.start[1] == 2) {
            let width = (that.phoneWidth * 0.5 - margin * 1.5) / 2;
            let height = contentHeight * 0.5 - margin / 2;
            let left = that.phoneWidth * 0.5 + margin / 2;
            let top = contentHeight * 0.5 + margin / 2;
            return {
              width: `${width}px`,
              height: `${height}px`,
              left: `${left}px`,
              top: `${top}px`,
            }
          }
          else if (item.start[0] == 3 && item.start[1] == 2) {
            let width = (that.phoneWidth * 0.5 - margin * 1.5) / 2;
            let height = contentHeight * 0.5 - margin / 2;
            let left = that.phoneWidth * 0.5 + width + margin * 1.5;
            let top = contentHeight * 0.5 + margin / 2;
            return {
              width: `${width}px`,
              height: `${height}px`,
              left: `${left}px`,
              top: `${top}px`,
            }
          }
          else {
            let heightMargin = margin * (1 - item.height / matrix[0]);
            let topMargin = heightMargin * item.start[1];
            let width, height, left, top = 0;
            width = 1 / matrix[1] * item.width * that.phoneWidth - widthMargin * item.width;
            height = 1 / matrix[0] * item.height * contentHeight - heightMargin;
            left = 1 / matrix[1] * item.start[0] * that.phoneWidth + leftMargin;
            top = 1 / matrix[0] * item.start[1] * contentHeight + topMargin;
            return {
              width: `${width}px`,
              height: `${height}px`,
              left: `${left}px`,
              top: `${top}px`,
            }
          }
        }
      }
    },
  },
  /** 创建实例之前执行函数   */
  beforeCreate() { },
  /**  实例创建完成之后  */
  created() { },
  /**  在挂载开始之前被调用  */
  beforeMount() { },
  /**  实例被挂载后调用  */
  mounted() { },
  /**  数据更新 之前 调用  */
  beforeUpdate() { },
  /** 数据更新 完成 调用   */
  updated() { },
  /**  实例销毁之前调用  */
  beforeDestroy() { },
  /**  实例销毁后调用  */
  destroyed() { },
};
</script>

<style lang="scss">
.Puzzle {
  .magic-cube {
    position: relative;
    overflow: hidden;
    background: transparent;
  }

  .empty-cube {
    line-height: 136px;
    font-size: 14px;
    text-align: center;
    background-color: #ebf8fd;
    color: #88c4dc;
  }

  .layout-content {
    border-top: 1px solid #f7f8fa;
    border-left: 1px solid #f7f8fa;
    // width: 375px;
    position: relative;

    .layout-item {
      display: flex;
      justify-content: center;
      align-items: center;
      box-sizing: border-box;
      position: absolute;
      background: #ffffff;
      font-size: 12px;
      width: 72px;
      height: 72px;
      cursor: pointer;

      .img_wrap {
        width: 100%;
        height: 100%;

        .img {
          width: 100%;
          height: 100%;
        }
      }
    }
  }
}
</style>
