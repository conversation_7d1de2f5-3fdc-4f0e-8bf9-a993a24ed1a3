/**
 * Created by preference on 2021/11/16
 *  zmx 
 */

import * as API from '@/api/index'
export default {
  /** 提成方案列表  */
  treatTimeCardCommissionScheme_list: params => {
    return API.POST('api/treatTimeCardCommissionScheme/list', params)
  },
  /**  提成方案保存 */
  treatTimeCardCommissionScheme_create: params => {
    return API.POST('api/treatTimeCardCommissionScheme/create', params)
  },
  /**  提成方案删除 */
  treatTimeCardCommissionScheme_delete: params => {
    return API.POST('api/treatTimeCardCommissionScheme/delete', params)
  },
  /**  时效卡分类提成  */
  treatTimeCardProjectCategoryCommission_all: params => {
    return API.POST('api/treatTimeCardProjectCategoryCommission/all', params)
  },
  /** 时效卡分类提成保存  */
  treatTimeCardProjectCategoryCommission_update: params => {
    return API.POST('api/treatTimeCardProjectCategoryCommission/update', params)
  },
  /**  所有时效卡经手人提成 */
  treatTimeCardSchemeHandlerCommission_all: params => {
    return API.POST('api/treatTimeCardSchemeHandlerCommission/all', params)
  },
  /** 所有时效卡经手人提成保存   */
  treatTimeCardSchemeHandlerCommission_update: params => {
    return API.POST('api/treatTimeCardSchemeHandlerCommission/update', params)
  },
  /**  分类时效卡经手人提成 */
  treatTimeCardCategoryHandlerCommission_all: params => {
    return API.POST('api/treatTimeCardCategoryHandlerCommission/all', params)
  },
  /** 分类时效卡经手人提成保存   */
  treatTimeCardCategoryHandlerCommission_update: params => {
    return API.POST('api/treatTimeCardCategoryHandlerCommission/update', params)
  },
  /**  时效卡提成 */
  treatTimeCardCommission_all: params => {
    return API.POST('api/treatTimeCardCommission/all', params)
  },
  /** 时效卡提成保存   */
  treatTimeCardCommission_update: params => {
    return API.POST('api/treatTimeCardCommission/update', params)
  },
  /**  时效卡经手人提成 */
  treatTimeCardHandlerCommission_all: params => {
    return API.POST('api/treatTimeCardHandlerCommission/all', params)
  },
  /** 时效卡经手人提成保存   */
  treatTimeCardHandlerCommission_update: params => {
    return API.POST('api/treatTimeCardHandlerCommission/update', params)
  },
  /**  时效卡项目提成 */
  treatTimeCardProjectCommission_all: params => {
    return API.POST('api/treatTimeCardProjectCommission/all', params)
  },
  /** 时效卡项目提成保存  */
  treatTimeCardProjectCommission_update: params => {
    return API.POST('api/treatTimeCardProjectCommission/update', params)
  },
  /**  时效卡项目经手人提成 */
  treatTimeCardProjectHandlerCommission_all: params => {
    return API.POST('api/treatTimeCardProjectHandlerCommission/all', params)
  },
  /** 时效卡项目提成保存  */
  treatTimeCardProjectHandlerCommission_update: params => {
    return API.POST('api/treatTimeCardProjectHandlerCommission/update', params)
  },
}