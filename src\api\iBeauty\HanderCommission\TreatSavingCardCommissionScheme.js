/**
 * Created by preference on 2021/12/28
 *  zmx 
 */

import * as API from '@/api/index'
export default {
  /** 提成方案列表  */
  treatSavingCardCommissionScheme_list: params => {
    return API.POST('api/treatSavingCardCommissionScheme/list', params)
  },
  /** 提成方案保存  */
  treatSavingCardCommissionScheme_create: params => {
    return API.POST('api/treatSavingCardCommissionScheme/create', params)
  },
  /**  提成方案删除  */
  treatSavingCardCommissionScheme_delete: params => {
    return API.POST('api/treatSavingCardCommissionScheme/delete', params)
  },
  /**  储值卡分类提成 */
  treatSavingCardCategoryCommission_all: params => {
    return API.POST('api/treatSavingCardCategoryCommission/all', params)
  },
  /** 储值卡分类提成保存  */
  treatSavingCardCategoryCommission_update: params => {
    return API.POST('api/treatSavingCardCategoryCommission/update', params)
  },
  /** 所有储值卡经手人提成  */
  treatSavingCardSchemeHandlerCommission_all: params => {
    return API.POST('api/treatSavingCardSchemeHandlerCommission/all', params)
  },
  /** 所有储值卡经手人提成保存  */
  treatSavingCardSchemeHandlerCommission_update: params => {
    return API.POST('api/treatSavingCardSchemeHandlerCommission/update', params)
  },
  /** 分类储值卡经手人提成   */
  treatSavingCardCategoryHandlerCommission_all: params => {
    return API.POST('api/treatSavingCardCategoryHandlerCommission/all', params)
  },
  /** 分类储值卡经手人提成保存  */
  treatSavingCardCategoryHandlerCommission_update: params => {
    return API.POST('api/treatSavingCardCategoryHandlerCommission/update', params)
  },
  /**  储值卡提成 */
  treatSavingCardCommission_all: params => {
    return API.POST('api/treatSavingCardCommission/all', params)
  },
  /** 储值卡提成保存  */
  treatSavingCardCommission_update: params => {
    return API.POST('api/treatSavingCardCommission/update', params)
  },
  /**  储值卡经手人提成 */
  treatSavingCardHandlerCommission_all: params => {
    return API.POST('api/treatSavingCardHandlerCommission/all', params)
  },
  /** 储值卡经手人提成保存  */
  treatSavingCardHandlerCommission_update: params => {
    return API.POST('api/treatSavingCardHandlerCommission/update', params)
  },
  /**  获取组织  */
  getEntityAll: params => {
    return API.POST("api/entity/getStoreList", params)
  },
}