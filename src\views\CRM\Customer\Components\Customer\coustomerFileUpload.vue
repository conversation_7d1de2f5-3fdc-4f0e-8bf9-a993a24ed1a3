<!-- 文件上传 -->
<template>
  <div v-loading.fullscreen="loading" body class="coustomerFileUpload">
    <el-tabs v-model="activeName" type="border-card" @tab-click="listTabsChangeClick">
      <el-tab-pane label="全部" name="-1"></el-tab-pane>
      <el-tab-pane v-for="(item, index) in fileCategory" :key="index" :label="item.Name" :name="item.ID.toString()"></el-tab-pane>
    </el-tabs>
    <el-button style="position: absolute; right: 5px; top: 5px; z-index: 2" type="primary" size="small" @click="addUploadFlieClick" v-prevent-click
      >上传文件</el-button
    >

    <!-- 列表 -->
    <div style="flex: 1; height: calc(100% - 70px)">
      <el-scrollbar style="height: 100%">
        <div v-for="(item, index) in customerFileUploadData" :key="index">
          <!-- 创建人信息  -->
          <el-row class="pad_5 backf5f7fa dis_flex flex_y_center">
            <el-col :span="16" class="dis_flex flex_dir_column flex_x_center">
              <div class="font_14 font_weight_600 color_333">
                {{ item.FileCategoryName }}
              </div>
              <div class="martp_5 color_666"><i class="el-icon-s-custom color_999 marrt_5" />{{ item.CreatedBy }}</div>
            </el-col>
            <el-col :span="8" class="text_right" style="height: 100%">{{ item.CreatedOn | dateFormat("YYYY-MM-DD HH:mm") }}</el-col>
          </el-row>
          <!-- 备注信息 -->
          <el-row v-if="item.Remark" class="pad_5">
            <el-col :span="24">备注：{{ item.Remark }}</el-col>
          </el-row>
          <!-- 文档列表 -->
          <el-row class="pad_5">
            <el-col :span="24" class="dis_flex flex_dir_row">
              <div v-for="(file, fileIndex) in item.Attachment" :key="'file' + fileIndex" style="width: 150px; height: 150px" class="marrt_10 border radius5">
                <!-- 图片 -->
                <div v-if="file.AttachmentType == 10" class="dis_flex flex_dir_column" style="width: 150px; height: 150px; position: relative">
                  <!-- :preview-src-list="item.imageScrList" -->
                  
                  <!-- @click="handlePreviewImage(item.imageScrList,file.AttachmentURL)" -->
                  <el-image
                    :ref="`checkImgList${index}`"
                    :src="file.AttachmentURL +'?x-oss-process=image/resize,h_100,m_lfit'"
                    class="marrt_10"
                    style="width: 150px; height: 150px"
                    fit="cover"
                  >
                  </el-image>
                  <div class="dis_flex flex_dir_column">
                    <span class="file-item-actions">
                      <span v-if="file.AttachmentType == '10'" @click="handlePreviewImage(item.imageScrList,file.AttachmentURL)">
                        <i class="el-icon-zoom-in"></i>
                      </span>
                      <span class="marlt_5" @click="handleListFlieDownload(file)">
                        <i class="el-icon-download"></i>
                      </span>

                      <span v-if="isDeleteFile" class="marlt_5" @click="handleListRemove(file)">
                        <i class="el-icon-delete"></i>
                      </span>
                    </span>
                  </div>
                </div>
                <!-- 文档 -->
                <div v-else style="width: 150px; height: 150px; position: relative; text-align: center">
                  <!-- <el-image src="@/assets/img/fileIcon/exel.png"></el-image> -->
                  <!-- <i class="el-icon-document" style="font-size:70px;color:#999999;transform: translateY(30%);"></i> -->
                  <div>
                    <i v-if="checkFileType(file) == 'other'" class="el-icon-document" style="font-size: 90px; color: #aaaaaa; margin-top: 15px"></i>
                    <el-image
                      v-else
                      :src="require('@/assets/img/fileIcon/' + checkFileType(file) + '.png')"
                      style="width: 90px; height: 90px; margin-top: 10px"
                    ></el-image>
                  </div>

                  <div class="pad_0_10" style="width: 100%; box-sizing: border-box">{{ changFileName(file.AttachmentName) }}.{{ file.MimeType }}</div>

                  <div class="dis_flex flex_dir_column">
                    <span class="file-item-actions">
                      <span v-if="file.AttachmentType == '10'" @click="handleListPreview(index)">
                        <i class="el-icon-zoom-in"></i>
                      </span>
                      <span class="marlt_5" @click="handleListFlieDownload(file)">
                        <i class="el-icon-download"></i>
                      </span>

                      <!-- <el-link  target="_blank" :href="file.AttachmentURL" :underline="false" style="color:#ffffff">
												<i class="el-icon-download font_20"></i>
											</el-link> -->

                      <span v-if="isDeleteFile" class="marlt_5" @click="handleListRemove(file)">
                        <i class="el-icon-delete"></i>
                      </span>
                    </span>
                  </div>
                </div>
              </div>
            </el-col>
          </el-row>
        </div>
      </el-scrollbar>
    </div>

    <el-dialog title="上传文件" :visible.sync="dialogVisible" width="980px" append-to-body custom-class="file_custom_dialog_class">
      <el-form ref="ruleForm" :rules="rules" :model="ruleForm" label-width="120px">
        <el-form-item label="文件类别" prop="fileCategoryActive">
          <el-select size="small" v-model="ruleForm.fileCategoryActive" placeholder="请选择文件类别">
            <el-option v-for="item in fileCategory" :key="item.ID" :label="item.Name" :value="item.ID"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="选择文件" prop="uploadFileList">
          <el-upload action="#" list-type="picture-card" :before-upload="beforeFileUpload" :file-list="ruleForm.uploadFileList" multiple>
            <!-- 加号图标 -->
            <i slot="default" class="el-icon-plus"></i>
            <div slot="file" slot-scope="{ file }">
              <el-image
                ref="previewRef"
                fit="cover"
                v-if="file.AttachmentType == '10'"
                class="el-upload-list__item-thumbnail"
                :src="file.AttachmentURL"
                alt=""
                :preview-src-list="previewSrcList"
                style="height: 100%; width: 100%"
              />

              <div class="document_i" v-else>
                <div>
                  <i v-if="checkFileType(file) == 'other'" class="el-icon-document" style="font-size: 90px; color: #aaaaaa; margin-top: 15px"></i>
                  <el-image
                    v-else
                    :src="require('@/assets/img/fileIcon/' + checkFileType(file) + '.png')"
                    style="width: 90px; height: 90px; margin-top: 10px"
                  ></el-image>
                </div>
                <div class="pad_0_10" style="width: 100%; box-sizing: border-box">{{ changFileName(file.Name) }}.{{ file.MimeType }}</div>
              </div>

              <span class="el-upload-list__item-actions">
                <span v-if="file.AttachmentType == '10'" class="el-upload-list__item-preview" @click="handlePictureCardPreview(file)">
                  <i class="el-icon-zoom-in"></i>
                </span>
                <span class="el-upload-list__item-delete" @click="handleRemove(file)">
                  <i class="el-icon-delete"></i>
                </span>
              </span>
            </div>
          </el-upload>
        </el-form-item>
        <el-form-item label="备注信息">
          <el-input size="small" type="textarea" placeholder="请输入备注信息" v-model="ruleForm.remark"> </el-input>
        </el-form-item>
      </el-form>

      <span slot="footer" class="dialog-footer">
        <el-button size="small" @click="dialogVisible = false">取 消</el-button>
        <el-button size="small" type="primary" @click="saveUploadDocumentClick" v-prevent-click :loading="modeloading">保 存</el-button>
      </span>
    </el-dialog>
    <el-image-viewer v-if="showViewer" :initialIndex="initialIndex" :on-close="closeViewer" :url-list="previewImageList" :z-index="3005"/>
  </div>
</template>
<script>
import API from "@/api/CRM/Customer/coustomerFileUpload";
import ElImageViewer from "element-ui/packages/image/src/image-viewer";
export default {
  
  name: "customer_CustomerFileUpload",
  components: {
    ElImageViewer
  },
  props: {
    customerID: Number,
    isDeleteFile: {
      type: Boolean,
      default: false,
      require: true,
    },
  },
  data() {
    return {
      loading: false,
      dialogVisible: false,
      modeloading: false,
      activeName: "-1",
      // remark: "",
      customerFileUploadData: [],
      fileCategory: [],
      previewSrcList: [],
      ruleForm: {
        fileCategoryActive: "",
        uploadFileList: [],
        remark: "",
      },
      rules: {
        fileCategoryActive: [
          {
            required: true,
            message: "请选择文件档案分类",
            trigger: ["blur", "change"],
          },
        ],
        uploadFileList: [
          {
            required: true,
            message: "请选择需要上传的文件",
            trigger: "change",
          },
        ],
      },
      showViewer:false,
      initialIndex:0 ,
      previewImageList:[],
    };
  },
  watch: {
    "ruleForm.uploadFileList": {
      handler(newVal) {
        let that = this;
        that.previewSrcList = newVal
          .filter((val) => val.AttachmentType == "10")
          .map((val) => {
            return val.AttachmentURL;
          });
      },
      immediate: true,
    },
    customerID: {
      immediate: true,
      handler(val) {
        if (val) {
          this.activeName = "-1";
        }
      },
    },
  },
  methods: {
    /**    */
    closeViewer(){
      let that = this;
      that.showViewer = false;
    },
    /**    */
    handlePreviewImage(urls,url){
      let that = this;
      that.showViewer = true;
      that.initialIndex = urls.findIndex((i) => i == url);
      that.previewImageList = urls;
    
    },
    /** 文件清理   */
    claerCoustomerFileUploadData() {
      this.customerFileUploadData = [];
      this.fileCategory = [];
    },
    /** 文件清理   */
    getCoustomerFileUploadData() {
      this.customerFileList();
      this.getCustomerFileCategory();
    },
    /**  验证 文件 类型  */
    checkFileType(file) {
      let type = file.MimeType;
      /**  excel  */
      if (type === "xlsx" || type === "xls" || type === "xltx" || type === "xlt" || type === "xlsm" || type === "xlsb" || type === "xltm" || type === "csv") {
        return "excel";
      }
      /**  word  */
      if (type === "doc" || type === "docx") {
        return "word";
      }
      /**  pdf  */
      if (type === "pdf") {
        return "pdf";
      }
      /**  ppt  */
      if (type === "ppt" || type === "pptx") {
        return "ppt";
      }

      let video_type = ["avi", "wmv", "mpg", "mpeg", "mov", "rm", "ram", "swf", "flv", "mp4", "wma", "avi", "rm", "rmvb", "flv", "mpg", "mkv"];
      if (video_type.some((val) => val == type.toLowerCase())) {
        return "video";
      }

      let audio_types = ["mov", "mp3"];
      if (audio_types.some((val) => val == type.toLowerCase())) {
        return "audio";
      }

      let compressed_types = ["zip", "rar", "7z", "gz"];
      if (compressed_types.some((val) => val == type.toLowerCase())) {
        return "compressed";
      }
      return "other";
    },

    /** 修改文件名称 超过五个字 前三 后二 中间以... 代替   */
    changFileName(file_name) {
      if (file_name.length > 5) {
        return file_name.substring(0, 3) + "..." + file_name.substring(file_name.length - 2, file_name.length);
      }
      return file_name;
    },

    /**  删除列表文件  */
    handleListRemove(file) {
      let that = this;
      that
        .$confirm("确定要删除文件吗？", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
        .then(() => {
          that.customerFile_deleteFile(file.AttachmentID);
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消操作",
          });
        });
    },
    /**  列表 tab 切换   */
    listTabsChangeClick() {
      let that = this;
      that.customerFileList();
    },
    /**  列表文件下载  */
    handleListFlieDownload(file) {
      let that = this;
      if (file.AttachmentType == 20) {
        window.open(file.AttachmentURL);
      } else {
        let params = {
          AttachmentID: file.AttachmentID,
        };

        that.downloadLoading = true;
        API.customerFile_downFile(params)
          .then((res) => {
            this.$message.success({
              message: "正在下载",
              duration: "4000",
            });

            const link = document.createElement("a");

            let blob = new Blob([res], { type: "application/octet-stream" });

            link.style.display = "none";

            link.href = URL.createObjectURL(blob);

            link.download = file.AttachmentName + "." + file.MimeType; //下载的文件名

            document.body.appendChild(link);

            link.click();

            document.body.removeChild(link);
          })
          .finally(function () {
            that.downloadLoading = false;
          });
      }
    },

    /** 列表图片预览   */
    handleListPreview(index) {
      let that = this;
      that.$nextTick(() => {
        that.$refs[`checkImgList${index}`][0].clickHandler();
      });
    },
    /**  预览文件  */
    handlePictureCardPreview() {
      let that = this;
      that.$nextTick(() => {
        that.$refs.previewRef.clickHandler();
      });
    },
    /**  下载文件  */
    handleDownload() {},
    /**  删除文件  */
    handleRemove(file) {
      let that = this;
      let index = that.ruleForm.uploadFileList.findIndex((val) => val.uid == file.uid);
      that.ruleForm.uploadFileList.splice(index, 1);
    },
    /**  添加上传文件  */
    addUploadFlieClick() {
      let that = this;
      that.ruleForm.fileCategoryActive = "";
      that.ruleForm.uploadFileList = [];
      that.ruleForm.remark = "";

      that.dialogVisible = true;
    },

    /**  文件上传前回调   */
    beforeFileUpload(file) {
      let that = this;
      if (file.size > 30 * 1024 * 1024) {
        this.$message.error("上传文件不可大于30M");
        return false;
      }
      let index = file.name.lastIndexOf(".");
      //获取名称
      let name = file.name.substr(0, index);
      // 获取后缀
      let tailName = file.name.substr(index + 1);
      // 判断 文件是否是视频
      if (/video\//.test(file.type) || /audio\//.test(file.type)) {
        that.uploadVideoAndAudio(file, name, tailName);
      } else if (/image\//.test(file.type)) {
        that.customerFile_uploadFile(file, name, tailName, "10");
      } else {
        that.customerFile_uploadFile(file, name, tailName, "30");
      }
      return false;
    },
    /**  上传文件请求  */
    saveUploadDocumentClick() {
      let that = this;
      that.$refs.ruleForm.validate((valid) => {
        if (valid) {
          that.customerFile_create();
        }
      });
    },

    /*****************************/

    /**  列表  */
    async customerFileList() {
      let that = this;
      let params = {
        CustomerID: that.customerID, //顾客ID
        CategoryID: that.activeName == "-1" ? "" : that.activeName,
      };
      if (that.customerID) {
        let res = await API.customerFileList(params);
        if (res.StateCode == 200) {
          that.customerFileUploadData = res.Data.map((val) => {
            val.imageScrList = val.Attachment.filter((file) => file.AttachmentType == 10).map((file) => file.AttachmentURL);
            return val;
          });
        } else {
          that.$message.error(res.Message);
        }
      }
    },
    /** 上传 视频   */
    async uploadVideoAndAudio(file, fileName, fileTailName) {
      let that = this;
      let params = {
        file: file,
      };
      // let index = file.name.lastIndexOf(".");
      // //获取名称
      // let name = file.name.substr(0, index);
      // // 获取后缀
      // let tailName = file.name.substr(index + 1);
      that.loading = true;
      let res = await API.uploadVideoAndAudio(params);
      if (res.StateCode == 200) {
        that.ruleForm.uploadFileList.push({
          Name: fileName, //文件名
          MimeType: fileTailName, //文档类型（png,doc等）
          AttachmentURL: res.Data.VideoId, //地址
          AttachmentType: "20", //附件类型(10：图片，20：视频，30：文件)
        });
      } else {
        that.$message.error(res.Message);
      }
      that.loading = false;
    },
    /**    */
    async customerFile_uploadFile(file, fileName, fileTailName, type) {
      let that = this;
      let params = {
        file: file,
      };
      that.loading = true;
      let res = await API.customerFile_uploadFile(params);
      if (res.StateCode == 200) {
        that.ruleForm.uploadFileList.push({
          Name: fileName, //文件名
          MimeType: fileTailName, //文档类型（png,doc等）
          AttachmentURL: res.Data.VideoId, //地址
          AttachmentType: type, //附件类型(10：图片，20：视频，30：文件)
        });
      } else {
        that.$message.error(res.Message);
      }
      that.loading = false;
    },
    /**  保存上传 文件 记录   */
    async customerFile_create() {
      let that = this;
      let params = {
        CustomerID: that.customerID, //顾客ID
        Remark: that.ruleForm.remark, //备注
        CustomerFileCategoryID: that.ruleForm.fileCategoryActive,
        Attachment: that.ruleForm.uploadFileList,
      };
      that.modeloading = true;
      let res = await API.customerFile_create(params);
      if (res.StateCode == 200) {
        that.customerFileList();
        that.$message.success("上传成功");
        that.dialogVisible = false;
      } else {
        that.$message.error(res.Message);
      }
      that.modeloading = false;
    },

    // 列表数据请求
    getCustomerFileCategory() {
      var that = this;
      var params = {
        Name: "",
        Active: true,
      };
      API.getCustomerFileCategory(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.fileCategory = res.Data;
            // if (that.fileCategory.length > 0) {
            // that.fileCategoryActive = that.fileCategory[0].ID.toString();
            // that.activeName = that.fileCategory[0].ID.toString();
            // }
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {});
    },
    /**  删除档案文件   */
    async customerFile_deleteFile(ID) {
      let that = this;
      let params = { ID: ID };
      let res = await API.customerFile_deleteFile(params);
      if (res.StateCode == 200) {
        that.$message.success("操作成功");
        that.customerFileList();
      } else {
        that.$message.error(res.Message);
      }
    },
  },
  // created() {
  //   let that = this;
  //   that.customerFileList();
  //   that.getCustomerFileCategory();
  // },
};
</script>
<style lang="scss">
.coustomerFileUpload {
  height: 45vh;
  .file-item-actions {
    position: absolute;
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
    cursor: default;
    text-align: center;
    color: #fff;
    opacity: 0;
    font-size: 20px;
    background-color: rgba(0, 0, 0, 0.5);
    -webkit-transition: opacity 0.3s;
    transition: opacity 0.3s;
    line-height: 150px;
  }
  .el-tabs__nav-wrap.is-scrollable {
    width: 85%;
  }
  .el-tabs__content {
    padding: unset;
  }
  .el-scrollbar__wrap {
    overflow-x: hidden;
  }
}

.file_custom_dialog_class {
  .el-dialog__body {
    .el-tabs--border-card {
      .el-tabs__content {
        padding: unset;
      }
    }
  }
}

.file-item-actions:hover {
  opacity: 1;
}

.document_i {
  position: absolute;
  width: 100%;
  height: 100%;
  text-align: center;
}
</style>
