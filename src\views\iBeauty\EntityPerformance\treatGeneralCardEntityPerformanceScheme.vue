<template>
  <div class="treatGeneralCardEntityPerformanceScheme content_body">
    <!-- 搜索 -->
    <div class="nav_header">
      <el-row>
        <el-col :span="20">
          <el-form :inline="true" size="small" @submit.native.prevent>
            <el-form-item label="组织单位">
              <el-input
                @clear="handleSearch"
                v-model="Name"
                placeholder="输入组织单位名称搜索"
                clearable
                @keyup.enter.native="handleSearch"
              ></el-input>
            </el-form-item>
            <el-form-item>
              <el-button
                type="primary"
                size="small"
                @click="handleSearch"
                v-prevent-click
                >搜索</el-button
              >
            </el-form-item>
          </el-form>
        </el-col>
        <el-col :span="4" class="text_right">
          <el-button
            type="primary"
            size="small"
            @click="showAddDialog"
            v-prevent-click
            >新增</el-button
          >
        </el-col>
      </el-row>
    </div>
    <!-- 表格 -->
    <div>
      <el-table size="small" :data="treatGeneralCardEntityTableData">
        <el-table-column prop="EntityName" label="组织单位"></el-table-column>
        <el-table-column label="操作" width="145px">
          <template slot-scope="scope">
            <el-button
              type="primary"
              size="small"
              @click="showEditDialog(scope.row)"
              v-prevent-click
              >编辑</el-button
            >
            <el-button
              type="danger"
              size="small"
              @click="removeEntityClick(scope.row)"
              v-prevent-click
              v-if="isDelete"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <div class="page pad_10 text_right">
        <div class="text_right" v-if="paginations.total > 0">
          <el-pagination
            background
            @current-change="handleCurrentChange"
            :current-page.sync="paginations.page"
            :page-size="paginations.page_size"
            :layout="paginations.layout"
            :total="paginations.total"
          ></el-pagination>
        </div>
      </div>
    </div>
    <!-- 新增弹窗 -->
    <el-dialog
      title="新增通用次卡消耗门店业绩方案"
      :visible.sync="dialogVisible"
      width="30%"
      custom-class="custom-dialog-add"
    >
      <div>
        <el-form
          :model="ruleForm"
          :rules="rules"
          ref="ruleForm"
          label-width="120px"
          size="small"
        >
          <el-form-item label="组织单位" prop="EntityID">
            <span slot="label">
              适用组织
              <el-popover placement="top-start" width="200" trigger="hover">
                <p>适用于同级所有节点，则只需选择父节点。</p>
                <p>比如：适用于所有节点，只需选择“顶级/第一个”节点。</p>
                <el-button
                  type="text"
                  style="color: #dcdfe6"
                  icon="el-icon-info"
                  slot="reference"
                ></el-button>
              </el-popover>
            </span>
            <treeselect
              v-model="ruleForm.EntityID"
              :options="entityList"
              :normalizer="normalizer"
              clearValueText
              noResultsText="无匹配数据"
              placeholder="选择所属部门"
            />
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="dialogVisible = false" v-prevent-click
          >取 消</el-button
        >
        <el-button
          type="primary"
          size="small"
          :loading="modalLoading"
          v-prevent-click
          @click="submitGeneralCardEntityPerformanceClick"
          >保存</el-button
        >
      </div>
    </el-dialog>
    <!-- 编辑弹窗 -->
    <el-dialog :visible.sync="dialogEdit" custom-class="custom-dialog-edit" width="60%">
      <div slot="title">{{ entityName }} - 通用次卡分类消耗门店业绩方案</div>
      <el-table
        size="small"
        :data="treatGeneralCardEntityCategoryTableData"
        row-key="id"
        :tree-props="{ children: 'Child', hasChildren: 'hasChild' }"
        :row-class-name="tableRowClassName"
        max-height="500px"
      >
        <el-table-column
          prop="CategoryName"
          label="通用次卡分类"
          min-width="150px"
          fixed
        ></el-table-column>
        <el-table-column label="现金比例" min-width="105px">
          <template slot-scope="scope">
            <el-input
              size="mini"
              v-model="scope.row.PayRate"
              type="number"
              v-input-fixed="2"
              class="input_type"
              @input="royaltyRateChange(1, scope.row)"
            >
              <template slot="append">%</template>
            </el-input>
          </template>
        </el-table-column>
        <el-table-column label="卡抵扣比例" min-width="105px">
          <template slot-scope="scope">
            <el-input
              size="mini"
              v-model="scope.row.CardRate"
              v-input-fixed="2"
              type="number"
              class="input_type"
              @input="royaltyRateChange(2, scope.row)"
            >
              <template slot="append">%</template>
            </el-input>
          </template>
        </el-table-column>
        <el-table-column label="赠送卡抵扣比例" min-width="105px">
          <template slot-scope="scope">
            <el-input
              size="mini"
              v-model="scope.row.CardLargessRate"
              v-input-fixed="2"
              type="number"
              class="input_type"
              @input="royaltyRateChange(3, scope.row)"
            >
              <template slot="append">%</template>
            </el-input>
          </template>
        </el-table-column>
        <el-table-column label="赠送比例" min-width="105px">
            <template slot-scope="scope">
              <el-input type="number" v-model="scope.row.LargessRate" size="mini" v-input-fixed="2" class="input_type" @input="royaltyRateChange(4, scope.row)">
                <template slot="append">%</template>
              </el-input>
            </template>
        </el-table-column>
        <el-table-column label="操作" width="130px">
          <template slot-scope="scope">
            <el-button
              type="primary"
              size="mini"
              @click="generalCardEntityPerformance(scope.row)"
              v-if="!scope.row.isEntity"
              >通用次卡业绩</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="dialogEdit = false" v-prevent-click
          >取 消</el-button
        >
        <el-button
          type="primary"
          size="small"
          :loading="modalLoading"
          v-prevent-click
          @click="submitGeneralCardEntityCategoryClick"
          >保存</el-button
        >
      </div>
    </el-dialog>
    <!--通用次卡业绩弹窗-->
    <el-dialog :visible.sync="dialogGeneralCardEntity" width="40%" custom-class="custom-dialog-edit_GeneralCard">
      <div slot="title">
        {{ entityName }} - {{ categoryName }} - 通用次卡消耗门店业绩方案
      </div>
      <div>
        <el-form :inline="true" size="small" @submit.native.prevent>
          <el-form-item>
            <el-input
              v-model="searchGeneralCardName"
              placeholder="输入通用次卡名称搜索"
              prefix-icon="el-icon-search"
              clearable
            ></el-input>
          </el-form-item>
        </el-form>
        <el-table
          :data="
            generalCardEntityRoyaltyList.filter(
              (data) =>
                !searchGeneralCardName ||
                data.GoodName.toLowerCase().includes(
                  searchGeneralCardName.toLowerCase()
                )
            )
          "
          row-key="GoodID"
          size="small"
          max-height="500px"
        >
          <el-table-column
            prop="GoodName"
            label="通用次卡名称"
            min-width="150px"
            fixed
          ></el-table-column>
          <el-table-column label="现金比例" min-width="105px">
            <template slot-scope="scope">
              <el-input
                size="mini"
                v-model="scope.row.PayRate"
                type="number"
                v-input-fixed="2"
                class="input_type"
                @input="royaltyRateChange(1, scope.row)"
              >
                <template slot="append">%</template>
              </el-input>
            </template>
          </el-table-column>
          <el-table-column label="卡抵扣比例" min-width="105px">
            <template slot-scope="scope">
              <el-input
                size="mini"
                v-model="scope.row.CardRate"
                v-input-fixed="2"
                class="input_type"
                type="number"
                @input="royaltyRateChange(2, scope.row)"
              >
                <template slot="append">%</template>
              </el-input>
            </template>
          </el-table-column>
          <el-table-column label="赠送卡扣比例" min-width="105px">
            <template slot-scope="scope">
              <el-input
                size="mini"
                v-model="scope.row.CardLargessRate"
                v-input-fixed="2"
                class="input_type"
                type="number"
                @input="royaltyRateChange(3, scope.row)"
              >
                <template slot="append">%</template>
              </el-input>
            </template>
          </el-table-column>
          <el-table-column label="赠送比例" min-width="105px">
            <template slot-scope="scope">
              <el-input type="number" v-model="scope.row.LargessRate" size="mini" v-input-fixed="2" class="input_type" @input="royaltyRateChange(4, scope.row)">
                <template slot="append">%</template>
              </el-input>
            </template>
          </el-table-column>
           <el-table-column label="操作" width="115px">
            <template slot-scope="scope">
              <el-button type="primary" size="small" @click="generalCardsProjectPerformance(scope.row)">项目业绩</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button
          size="small"
          @click="dialogGeneralCardEntity = false"
          v-prevent-click
          >取 消</el-button
        >
        <el-button
          type="primary"
          size="small"
          :loading="modalLoading"
          v-prevent-click
          @click="updateGeneralCardEntityPerformance"
          >保存</el-button
        >
      </div>
    </el-dialog>
    <!--项目弹窗-->
    <el-dialog title="项目业绩设置" :visible.sync="dialogGeneralCardProject" width="40%" custom-class="custom-dialog-edit_GeneralCard">
      <div slot="title">{{ entityName }} - {{ categoryName }} - {{ generalName }} - 项目消耗业绩方案</div>
      <div>
        <el-form :inline="true" size="small" @submit.native.prevent>
          <el-form-item>
            <el-input v-model="searchKey" placeholder="请输入项目名称搜索" prefix-icon="el-icon-search" clearable></el-input>
          </el-form-item>
        </el-form>
        <el-table :data="treatLists.filter((data) => !searchKey || data.GoodName.toLowerCase().includes(searchKey.toLowerCase()))" row-key="ID" :tree-props="{ children: 'children', hasChildren: 'hasChildren' }" size="small" max-height="500px">
          <el-table-column prop="GoodName" label="项目名称" fixed min-width="150px"></el-table-column>

          <el-table-column prop="PayRate" label="现金比例" min-width="105px">
            <template slot-scope="scope">
              <el-input type="number" v-model="scope.row.PayRate" v-input-fixed="2" class="input_type" @input="royaltyRateChange(1, scope.row)" size="mini">
                <template slot="append">%</template>
              </el-input>
            </template>
          </el-table-column>

          <el-table-column prop="CardRate" label="卡抵扣比例" min-width="105px">
            <template slot-scope="scope">
              <el-input type="number" v-model="scope.row.CardRate" size="mini" @input="royaltyRateChange(2, scope.row)" v-input-fixed="2" class="input_type">
                <template slot="append">%</template>
              </el-input>
            </template>
          </el-table-column>

          <el-table-column prop="CardLargessRate" label="赠送卡抵扣比例" min-width="105px">
            <template slot-scope="scope">
              <el-input type="number" v-model="scope.row.CardLargessRate" size="mini" @input="royaltyRateChange(3, scope.row)" v-input-fixed="2" class="input_type">
                <template slot="append">%</template>
              </el-input>
            </template>
          </el-table-column>

          <el-table-column prop="LargessRate" label="赠送比例" min-width="105px">
            <template slot-scope="scope">
              <el-input type="number" v-model="scope.row.LargessRate" size="mini" v-input-fixed="2" class="input_type" @input="royaltyRateChange(4, scope.row)">
                <template slot="append">%</template>
              </el-input>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="dialogGeneralCardProject = false" v-prevent-click>取 消</el-button>
        <el-button type="primary" size="small" :loading="projectModalLoading" v-prevent-click @click="submitGeneralCardProject">保 存</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>

import API from "@/api/iBeauty/EntityPerformance/treatGeneralCardEntityPerformanceScheme"
import APIEntity from "@/api/KHS/Entity/entity";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
var Enumerable = require("linq");

export default {
  name: "treatGeneralCardEntityPerformanceScheme",

  beforeRouteEnter(to, from, next) {
    next((vm) => {
      vm.isDelete = vm.$permission.permission(
        to.meta.Permission,
        "KHS-EntityPerformance-TreatGeneralCardEntityPerformanceScheme-Delete"
      );
    });
  },
  props: {},
  /**  引入的组件  */
  components: { Treeselect },
  /**  Vue 实例的数据对象**/
  data() {
    return {
      isDelete: false,
      modalLoading: false,
      loading: false,
      dialogVisible: false,
      dialogEdit: false,
      dialogGeneralCardEntity: false,
      dialogGeneralCardProject: false,
      projectModalLoading: false,
      generalModalLoading: false,
      Name: "", // 搜索条件
      EntityID: "", //  当前的门店ID
      entityName: "",
      categoryName: "",
      generalName: "",
      generalCardCategoryID: "",
      generalCardID:"",
      searchKey: "", // 通用次卡项目搜索
      searchGeneralCardName: "", // 通用次卡搜索
      treatGeneralCardEntityTableData: [], //表格数据
      treatGeneralCardEntityCategoryTableData: [], //编辑弹窗表格数据
      generalCardEntityRoyaltyList: [], //通用次卡弹窗表格数据
      entityList: [], //门店数据
      treatLists: [], //项目数据
      ruleForm: {
        EntityID: null,
      },
      rules: {
        EntityID: [
          { required: true, message: "请选择组织", trigger: "change" },
        ],
      },
      //需要给分页组件传的信息
      paginations: {
        page: 1, // 当前位于哪页
        total: 0, // 总数
        page_size: 10, // 1页显示多少条
        layout: "total, prev, pager, next,jumper", // 翻页属性
      },
    };
  },
  /**计算属性  */
  computed: {},
  /**  方法集合  */
  methods: {
    /* 数据显示 */
    handleSearch() {
      let that = this;
      that.paginations.page = 1;
      that.getTreatGeneralCardEntityPerformanceScheme()
    },
    /* 上下分页 */
    handleCurrentChange(page) {
      let that = this;
      that.paginations.page = page;
      that.getTreatGeneralCardEntityPerformanceScheme()
    },
    /* 新增 */
    showAddDialog() {
      let that = this;
      that.ruleForm = {
        entity: null,
      };
      that.dialogVisible = true;
    },
    /* 编辑 */
    showEditDialog(row) {
      let that = this;

      that.entityName = row.EntityName;
      that.EntityID = row.EntityID;
      that.getTreatGeneralCardCategoryEntityPerformance()
    },
    /* 新增保存 */
    submitGeneralCardEntityPerformanceClick() {
       var that = this;
      that.$refs.ruleForm.validate((valid) => {
        if (valid) {
          that.modalLoading = true;
          let para = Object.assign({}, that.ruleForm);
          API.createTreatGeneralCardEntityPerformanceScheme(para)
            .then(function (res) {
              if (res.StateCode === 200) {
                that.$message.success({
                  message: "新增成功",
                  duration: 2000,
                });
                that.getTreatGeneralCardEntityPerformanceScheme();
                that.$refs["ruleForm"].resetFields();
                that.dialogVisible = false;
              } else {
                that.$message.error({
                  message: res.Message,
                  duration: 2000,
                });
              }
            })
            .finally(function () {
              that.modalLoading = false;
            });
        }
      });
    },
    /* 获取门店通用次卡消耗业绩方案列表 */
    getTreatGeneralCardEntityPerformanceScheme() {
      let that = this;
      that.loading = true;
      var params = {
        Name: that.Name,
        PageNum: that.paginations.page,
      };
      API.getTreatGeneralCardEntityPerformanceScheme(params)
					.then((res) => {
						if (res.StateCode == 200) {
							that.treatGeneralCardEntityTableData = res.List;
							that.paginations.total = res.Total;
							that.paginations.page_size = res.PageSize;
						} else {
							that.$message.error({
								message: res.Message,
								duration: 2000,
							});
						}
					})
					.finally(function() {
						that.loading = false;
					});
    },
    /* 获取分类通用次卡消耗业绩 */
    getTreatGeneralCardCategoryEntityPerformance() {
      var that = this;
      that.loading = true;
      var params = {
        EntityID: that.EntityID,
      };
      API.getTreatGeneralCardCategoryEntityPerformance(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.dialogEdit = true;
            res.Data.Category.forEach((item) => {
              item.Child = Enumerable.from(item.Child)
                .select((val) => ({
                  id: val.CategoryName + val.CategoryID + "h",
                  CategoryID: val.CategoryID,
                  CategoryName: val.CategoryName,
                  ParentID: val.ParentID,
                  PayRate: val.PayRate, //现金比例业绩
                  CardRate: val.CardRate, //卡本金比例业绩
                  CardLargessRate: val.CardLargessRate, //赠卡比例业绩
                  LargessRate: val.LargessRate,
                  isChild: true,
                }))
                .toArray();
            });

            var data = {
              id: res.Data.EntityID + "h" + res.Data.EntityName,
              CategoryID: res.Data.EntityID,
              CategoryName: "所有通用次卡",
              PayRate: res.Data.PayRate, //现金比例业绩
              CardRate: res.Data.CardRate, //卡本金比例业绩
              CardLargessRate: res.Data.CardLargessRate, //赠卡比例业绩
              LargessRate: res.Data.LargessRate,
              isEntity: true,
              isChild: false,
            };

            var Category = Enumerable.from(res.Data.Category)
              .select((val) => ({
                id: val.CategoryID + "h" + val.CategoryName,
                CategoryID: val.CategoryID,
                CategoryName: val.CategoryName,
                ParentID: val.ParentID,
                PayRate: val.PayRate, //现金比例业绩
                CardRate: val.CardRate, //卡本金比例业绩
                CardLargessRate: val.CardLargessRate, //赠卡比例业绩
                LargessRate: val.LargessRate,
                Child: val.Child,
                isEntity: false,
                isChild: false,
              }))
              .toArray();
            that.treatGeneralCardEntityCategoryTableData = Category;
            that.treatGeneralCardEntityCategoryTableData.unshift(data);
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },
    /* 保存分类通用次卡消耗业绩 */
    submitGeneralCardEntityCategoryClick() {
      var that = this;
      var params = {
        EntityID: that.treatGeneralCardEntityCategoryTableData[0].CategoryID,
        PayRate: that.treatGeneralCardEntityCategoryTableData[0].PayRate, //现金比例业绩
        CardRate: that.treatGeneralCardEntityCategoryTableData[0].CardRate, //卡本金比例业绩
        CardLargessRate: that.treatGeneralCardEntityCategoryTableData[0].CardLargessRate, //赠卡比例业绩
        LargessRate: that.treatGeneralCardEntityCategoryTableData[0].LargessRate,
      };
      var treatGeneralCardEntityCategoryTableData = JSON.parse(JSON.stringify(that.treatGeneralCardEntityCategoryTableData))
      treatGeneralCardEntityCategoryTableData.forEach((item) => {
        item.Child = Enumerable.from(item.Child)
          .where(function (i) {
            return (
              (i.PayRate !== "" && i.PayRate !== null) ||
              (i.CardRate !== "" && i.CardRate !== null) ||
              (i.CardLargessRate !== "" && i.CardLargessRate !== null) ||
              (i.LargessRate !== "" && i.LargessRate !== null)
            );
          })
          .select((val) => ({
            id: val.CategoryID + "h",
            CategoryID: val.CategoryID,
            ParentID: val.ParentID,
            PayRate: val.PayRate, //现金比例业绩
            CardRate: val.CardRate, //卡本金比例业绩
            CardLargessRate: val.CardLargessRate, //赠卡比例业绩
            LargessRate: val.LargessRate,
          }))
          .toArray();
      });
      treatGeneralCardEntityCategoryTableData = Enumerable.from(
        treatGeneralCardEntityCategoryTableData
      )
        .where(function (i) {
          return (
            (i.PayRate !== "" && i.PayRate !== null) ||
            (i.CardRate !== "" && i.CardRate !== null) ||
            (i.CardLargessRate !== "" && i.CardLargessRate !== null) ||
            (i.LargessRate !== "" && i.LargessRate !== null) ||
            i.Child.length > 0
          );
        })
        .toArray();

      that.modalLoading = true;
      var Category = Enumerable.from(treatGeneralCardEntityCategoryTableData)
        .where(function (i) {
          return !i.isEntity;
        })
        .select((val) => ({
          CategoryID: val.CategoryID,
          PayRate: val.PayRate, //现金比例业绩
          PayFixed: val.PayFixed, //现金固定业绩
          CardRate: val.CardRate, //卡本金比例业绩
          CardLargessRate: val.CardLargessRate, //赠卡比例业绩
          LargessRate: val.LargessRate,
          Child: val.Child,
        }))
        .toArray();
      params.Category = Category;
      API.updateTreatGeneralCardCategoryEntityPerformance(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.$message.success("业绩设置成功");
            that.dialogEdit = false;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.modalLoading = false;
        });
    },
    /* 通用次卡业绩设置 */
    generalCardEntityPerformance: function (row) {
      var that = this;
      that.generalCardCategoryID = row.CategoryID;
      that.categoryName = row.CategoryName;
      that.getTreatGeneralCardEntityPerformance()
    },
    /* 获取通用次卡业绩 */
    getTreatGeneralCardEntityPerformance() {
      var that = this;
      var params = {
        EntityID: that.EntityID,
        CategoryID: that.generalCardCategoryID,
      };
      API.getTreatGeneralCardEntityPerformance(params).then((res) => {
        if (res.StateCode == 200) {
          that.dialogGeneralCardEntity = true;
          that.generalCardEntityRoyaltyList = res.Data;
        } else {
          that.$message.error({
            message: res.Message,
            duration: 2000,
          });
        }
      });
    },
    /* 通用次卡业绩保存 */
    updateGeneralCardEntityPerformance: function () {
      let that = this;
      var generalCardList = [];
      generalCardList = Enumerable.from(that.generalCardEntityRoyaltyList)
        .where(function (i) {
          return (
            (i.PayRate !== "" && i.PayRate !== null) ||
            (i.CardRate !== "" && i.CardRate !== null) ||
            (i.CardLargessRate !== "" && i.CardLargessRate !== null) ||
            (i.LargessRate !== "" && i.LargessRate !== null)
          );
        })
        .select((val) => ({
          GoodID: val.GoodID,
          PayRate: val.PayRate, //现金比例业绩
          CardRate: val.CardRate, //卡本金比例业绩
          CardLargessRate: val.CardLargessRate, //赠卡比例业绩
          LargessRate: val.LargessRate,
        }))
        .toArray();
      let params = {
        EntityID: that.EntityID,
        Good: generalCardList,
        CategoryID: that.generalCardCategoryID,
      };
      that.generalModalLoading = true;
      API.updateTreatGeneralCardEntityPerformance(params)
        .then((res) => {
          if (res.StateCode == 200) {
          that.$message.success("业绩设置成功");
            this.dialogGeneralCardEntity = false;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(() => {
          that.generalModalLoading = false;
        });
    },
    /* 通用次卡项目业绩弹窗 */
    generalCardsProjectPerformance(row) {
      var that = this;
      that.generalName = row.GoodName
      that.generalCardID = row.GoodID;
      that.getTreatGeneralCardProjectEntityPerformance()
    },
    /* 获取通用次卡项目业绩 */
     getTreatGeneralCardProjectEntityPerformance() {
      var that = this;
      var params = {
        EntityID: that.EntityID,
        CardID: that.generalCardID,
      };
      API.getTreatGeneralCardProjectEntityPerformance(params).then((res) => {
        if (res.StateCode == 200) {
          that.dialogGeneralCardProject = true;
          that.treatLists = res.Data;
        } else {
          that.$message.error({
            message: res.Message,
            duration: 2000,
          });
        }
      });
    },
    /* 保存通用次卡项目业绩 */
    submitGeneralCardProject(){
      var that = this;
      that.projectModalLoading = true;
      var projectList = [];
      projectList = Enumerable.from(that.treatLists)
        .where(function (i) {
          return (
            (i.PayRate !== "" && i.PayRate !== null) ||
            (i.CardRate !== "" && i.CardRate !== null) ||
            (i.CardLargessRate !== "" && i.CardLargessRate !== null) ||
            (i.LargessRate !== "" && i.LargessRate !== null)
          );
        })
        .select((val) => ({
          GoodID: val.GoodID,
          PayRate: val.PayRate, //现金比例业绩
          CardRate: val.CardRate, //卡本金比例业绩
          CardLargessRate: val.CardLargessRate, //赠卡比例业绩
          LargessRate: val.LargessRate,
        }))
        .toArray();
      let params = {
        EntityID: that.EntityID,
        Good: projectList,
        CardID: that.generalCardID,
      };

      API.updateTreatGeneralCardProjectEntityPerformance(params)
        .then((res) => {
          if (res.StateCode == 200) {
          that.$message.success("业绩设置成功");
            that.dialogGeneralCardProject = false
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(() => {
          that.projectModalLoading = false;
        });
    },
    /* 删除通用次卡消耗业绩方案 */
    removeEntityClick: function (row) {
      var that = this;
      that
        .$confirm("此操作将永久删除该记录, 是否继续?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
        .then(() => {
           that.loading = true;
          var params = {
            EntityID: row.EntityID,
          };
          API.deleteTreatGeneralCardEntityPerformanceScheme(params)
            .then((res) => {
              if (res.StateCode == 200) {
                 that.$message.success({
                  message: "删除成功",
                  duration: 2000,
                });
                that.getTreatGeneralCardEntityPerformanceScheme();
              } else {
                that.$message.error({
                  message: res.Message,
                  duration: 2000,
                });
              }
            })
            .finally(function () {
              that.loading = false;
            });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    /* 所属单位 */
    entityData: function () {
      var that = this;
      APIEntity.getEntityAll()
        .then((res) => {
          if (res.StateCode == 200) {
            that.entityList = res.Data;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },
    /* 业绩比例约束 */
    royaltyRateChange: function (index, row) {
      switch (index) {
        case 1:
          row.PayRate = row.PayRate > 100 ? 100 : row.PayRate;
          break;
        case 2:
          row.CardRate = row.CardRate > 100 ? 100 : row.CardRate;
          break;
        case 3:
          row.CardLargessRate =
            row.CardLargessRate > 100 ? 100 : row.CardLargessRate;
          break;
        case 4:
          row.LargessRate = row.LargessRate > 100 ? 100 : row.LargessRate;
          break;
      }
    },
    /* 高亮第一级表格 */
    tableRowClassName({ rowIndex }) {
      if (rowIndex === 0) {
        return "info-row";
      }
      return "";
    },
    /* 树形结构数据转换 */
    normalizer(node) {
      return {
        id: node.ID,
        label: node.EntityName,
        children: node.Child,
      };
    },
  },
  /** 监听数据变化   */
  watch: {},
  /** 创建实例之前执行函数   */
  beforeCreate() {},
  /**  实例创建完成之后  */
  created() {},
  /**  在挂载开始之前被调用  */
  beforeMount() {},
  /**  实例被挂载后调用  */
  mounted() {
    let that = this;
    this.isDelete = this.$permission.permission(
      this.$route.meta.Permission,
      "KHS-EntityPerformance-TreatGeneralCardEntityPerformanceScheme-Delete"
    );
    that.handleSearch();
    that.entityData();
  },
  /**  数据更新 之前 调用  */
  beforeUpdate() {},
  /** 数据更新 完成 调用   */
  updated() {},
  /**  实例销毁之前调用  */
  beforeDestroy() {},
  /**  实例销毁后调用  */
  destroyed() {},
};
</script>

<style lang="scss">
.treatGeneralCardEntityPerformanceScheme {
   .input_type {
    .el-input-group__append {
      padding: 0 10px;
    }
  }
  .el-table .info-row {
    background: #c0c4cc;
  }
  .el-input__inner {
    padding-right: 0;
  }
  .custom-dialog-add{
    min-width: 500px;
  }
  .custom-dialog-edit{
    min-width: 950px;
  }
  .custom-dialog-edit_GeneralCard{
    min-width: 850px;
  }
}
</style>
