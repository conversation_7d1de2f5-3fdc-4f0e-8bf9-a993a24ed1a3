<template>
  <div>
    <!-- 成为客户日期 -->
    <el-card class="box-card" shadow="never">
      <div class="dis_flex flex_x_between flex_y_center marbm_20">
        <div>
          <span class="marrt_15">{{ title }}</span>
          <span v-if="subTitle" class="color_999">{{ subTitle }}</span>
        </div>
        <i class="el-icon-close" @click="handlerClose(Code)"></i>
      </div>
      <div class="dis_flex flex_y_center">
        <span class="marrt_15">{{ contentTitle }}</span>
        <el-date-picker
          v-model="contentValues_"
          type="daterange"
          size="small"
          :picker-options="options"
          value-format="yyyy-MM-dd"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          @change="changeFollowDateTime"
        >
        </el-date-picker>
      </div>
    </el-card>
  </div>
</template>

<script>
export default {
  name: "followDateTime",
  components: {},
  props: {
    title: {
      type: String,
      default: "",
    },
    subTitle: {
      type: String,
      default: null,
    },
    contentTitle: {
      type: String,
      default: null,
    },
    Code: {
      type: String,
      default: null,
    },
    contentValues: {
      type: Array,
      default: () => [],
    },
    options: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      contentValues_: [],
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() < Date.now() - 24 * 60 * 60 * 1000;
        },
      },
    };
  },
  computed: {},
  watch: {
    contentValues: {
      handler(val) {
        this.contentValues_ = val;
      },
      immediate: true,
    },
  },
  created() {},
  mounted() {},
  methods: {
    /**    */
    changeFollowDateTime() {
      this.$emit("handlerChildChange", this.contentValues_);
    },
    handlerClose(Code) {
      this.$emit("handlerChildClone", Code);
    },
  },
};
</script>

<style scoped lang="less">
