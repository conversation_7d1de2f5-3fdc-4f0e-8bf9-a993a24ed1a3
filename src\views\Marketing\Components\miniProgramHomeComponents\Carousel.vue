<template>
  <div class="Carousel">
    <!-- <el-image v-if="ContentProperty.length == 0" style="width: 100%; height: 140px" slot="placeholder" :src="require('@/assets/img/store/goodsempty.png')" fit="none"></el-image> -->
    <div v-if="ContentProperty.length == 0" class="empty">
      <p>点击编辑图片</p>
      <p class="martp_5 font_14">建议宽度750px</p>
    </div>
    <div v-else>
      <el-carousel height="187px" arrow="never">
        <el-carousel-item v-for="(item, index) in ContentProperty" :key="index">
          <el-image style="width: 100%; height: 100%" :src="item.url" fit="cover"></el-image>
        </el-carousel-item>
      </el-carousel>
    </div>
  </div>
</template>

<script>
export default {
  name: "Carousel",
  props: {
    ContentProperty: {
      type: Array,
      default: () => {
        return [];
      },
    },
  },
  /**  引入的组件  */
  components: {},
  /**  Vue 实例的数据对象**/
  data() {
    return {};
  },
  /**计算属性  */
  computed: {},
  /**  方法集合  */
  methods: {},
  /** 监听数据变化   */
  watch: {},
  /**  实例创建完成之后  */
  created() {},
  /**  实例被挂载后调用  */
  mounted() {},
};
</script>

<style lang="scss">
.Carousel {
  .empty {
    background: #f4dbb6;
    color: #fff;
    height: 180px;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    flex-direction: column;
    -webkit-align-items: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-justify-content: center;
    -ms-flex-pack: center;
    justify-content: center;
  }
  .el-carousel__indicator--horizontal {
    display: inline-block;
    padding: 12px 4px;
    .el-carousel__button{
      width: 6px;
      height: 6px;
      border-radius: 6px;
    }
  }
}
</style>
