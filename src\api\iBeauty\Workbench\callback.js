/**
 * Created by wsf on 2022/04/01.
 * 回访工作台 api
 */

import * as API from '@/api/index';

export default {
  /* 回访列表-只能看自己 */
  getCallBackList: (params) => {
    return API.POST('api/callback/list', params);
  },
  /* 回访列表-权限下 */
  getCallBackAll: (params) => {
    return API.POST('api/callback/all', params);
  },
  /* 回访添加 */
  createCallBack: (params) => {
    return API.POST('api/callback/create', params);
  },
  /* 回访修改 */
  updateCallBack: (params) => {
    return API.POST('api/callback/update', params);
  },
  /* 回访详情 */
  detailCallBack: (params) => {
    return API.POST('api/callback/detail', params);
  },
  /* 回访删除 */
  deleteCallBack: (params) => {
    return API.POST('api/callback/delete', params);
  },
  /* 批量分配 */
  massDistribution: (params) => {
    return API.POST('api/callback/massDistribution', params);
  },
  // 导出 隐藏手机号
  callback_excelNoDisPlayPhone: (params) => {
    return API.exportExcel('api/callback/excelNoDisPlayPhone', params);
  },
  // 导出 显示手机号
  callback_excelDisPlayPhone: (params) => {
    return API.exportExcel('api/callback/excelDisPlayPhone', params);
  },
    /* 查询会员等级 */
    customerLevel_all: (params) => {
      return API.POST('api/customerLevel/all', params);
    },
};
