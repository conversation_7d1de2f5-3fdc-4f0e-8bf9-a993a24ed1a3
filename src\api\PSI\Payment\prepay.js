import * as API from "@/api/index";
export default {
  /* 预付款列表 */
  list_payment: (params) => {
    return API.POST("api/payment/list", params);
  },
  /* 预付款添加 */
  create_payment: (params) => {
    return API.POST("api/payment/create", params);
  },
  /* 预付款退款  */
  refund_payment: (params) => {
    return API.POST("api/payment/refund", params);
  },

  /* 预付款类别列表 */
  prepayCategory_list: (params) => {
    return API.POST("api/paymentCategory/list", params);
  },
  /* 预付款类别列表 */
  payment_contactDetail: (params) => {
    return API.POST("api/payment/contactDetail", params);
  },
  /* 明细导出 */
  payment_excelPayment_excel: (params) => {
    return API.exportExcel("api/payment/excelPayment", params);
  },
};
