<template>
  <div class="followSection">
    <!-- 年龄 -->
    <el-card class="box-card" shadow="never">
      <div class="dis_flex flex_x_between flex_y_center marbm_20">
        <div>
          <span style="margin-right: 58px">{{title}}</span>
          <span class="color_999">{{subTitle}}</span>
        </div>
        <i class="el-icon-close" @click="handlerClose(Code)"></i>
      </div>
      <div class="dis_flex flex_y_center">
        <span style="margin-right: 30px">{{contentTitle}}</span>
        <el-input @change="changeStartValues"  min="0" v-model="contentValues_.startValue" style="width: 140px; height: 34px" size="small"  placeholder="请输入"></el-input>
        <span style="margin: 0 15px">-</span>
        <el-input @change="changeEndValues" min="0" v-model="contentValues_.endValue" style="width: 140px; height: 34px" size="small" placeholder="请输入"></el-input>
        <!-- <span class="marlt_10">岁</span> -->
      </div>
    </el-card>
   
  </div>
</template>

<script>
export default {
 name: 'followSection',
  props:{
    title: {
      type: String,
      default: "",
    },
    subTitle: {
      type: String,
      default: null,
    },
    contentTitle: {
      type: String,
      default: null,
    },
    Code:{
      type: String,
      default: null,
    },
    contentValues:{
      type: Object,
      default: function () {
        return {
          startValue: "",
          endValue: "",
        }
      },
    },
  },
  /** 监听数据变化   */
  watch: {
    contentValues: {
      handler: function (val) {
        this.contentValues_ = val
      },
      deep: true,
      immediate: true,
    },
  },
  /**  引入的组件  */
  components: {},
  /**  Vue 实例的数据对象**/
  data() {
    return {
      contentValues_:{
        startValue: "",
        endValue: "",
      },
    }
  },
   /**计算属性  */
  computed: {
  },
  /**  方法集合  */
  methods: {
    /**   修改开始值 */
    changeStartValues(value){
      this.$emit("changeStartValues",value)
    },

    /**  修改结束值  */
    changeEndValues(value){
      this.$emit("changeEndValues",value)
    },

    /**    */
    handlerClose(){
      this.$emit("handlerChildClone",);
    
    },


  },
  /** 创建实例之前执行函数   */
  beforeCreate() {},
  /**  实例创建完成之后  */
  created() {},
  /**  在挂载开始之前被调用  */
  beforeMount() {},
  /**  实例被挂载后调用  */
  mounted() {

  },
  /**  数据更新 之前 调用  */
  beforeUpdate() {},
  /** 数据更新 完成 调用   */
  updated() {},
  /**  实例销毁之前调用  */
  beforeDestroy() {},
  /**  实例销毁后调用  */
  destroyed() {},
}
</script>

<style lang="scss">

.followSection{

 }
</style>
