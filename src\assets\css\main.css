* {
  margin: 0;
  padding: 0;
}

html,
body,
#app,
.wrapper {
  width: 100%;
  height: 100%;
  overflow-y: hidden;
}
.zZindex {
  z-index: 3000 !important;
}
a {
  text-decoration: none;
  color: #333;
}
ul li {
  list-style: none;
}
.border {
  border: 1px solid #eeeeee;
}
.border_blue {
  border: 1px solid #7c8ffd;
}
.border_bottom {
  border-bottom: 1px solid #eeeeee;
}
.border_top {
  border-top: 1px solid #eeeeee;
}
.border_top_fa {
  border-top: 1px solid #fafafa;
}
.border_right {
  border-right: 1px solid #eeeeee !important;
}
.border_left {
  border-left: 1px solid #eeeeee;
}
/* 颜色 */
.backfff {
  background-color: #fff;
}
.backf5f7fa {
  background-color: #f5f7fa;
}
.back_F6F7FF {
  background-color: #f6f7ff;
}
.back_F5 {
  background-color: #f5f5f5;
}
.back_f8 {
  background-color: #f5f7fa;
}
.back_f7f8fa {
  background-color: #f7f8fa;
}

.back_main {
  background-color: var(--zl-color-orange-primary);
}
.bgcol-4860fd {
  background-color: #4860fd;
}
.bgcol-f8f9ff {
  background-color: #f8f9ff;
}
.bgcol-f3f4ff {
  background-color: #f3f4ff;
}
.bgcol-f8f9ff {
  background-color: #f8f9ff;
}
.bgcol-5B70FF {
  background-color: #5b70ff;
}
.color_main {
  color: var(--zl-color-orange-primary);
}

.color_blue {
  color: #7c8ffd;
}
.color_fff {
  color: #fff;
}
.color_primary {
  color: var(--zl-color-orange-primary);
}
.color_red {
  color: rgb(254, 34, 120) !important;
}
.color_maroon {
  color: #d40000;
}
.color_green {
  color: rgb(68, 187, 0);
}
.color_gray {
  color: #c0c4cc;
}
.color_orange {
  color: #ffa037;
}
.color_000 {
  color: #000000;
}
.color_333 {
  color: #333333;
}

.color_666 {
  color: #666666;
}

.color_999 {
  color: #999999;
}

/* 对齐方式 */
.text_right {
  text-align: right;
}
.text_left {
  text-align: left;
}
.text_center {
  text-align: center;
}
/*字体大小 */
.font_8 {
  font-size: 8px !important;
}
.font_9 {
  font-size: 9px !important;
}
.font_10 {
  font-size: 10px !important;
}
.font_11 {
  font-size: 11px !important;
}
.font_12 {
  font-size: 12px !important;
}
.font_13 {
  font-size: 13px !important;
}
.font_14 {
  font-size: 14px;
}
.font_15 {
  font-size: 15px;
}
.font_16 {
  font-size: 16px;
}
.font_18 {
  font-size: 18px;
}
.font_20 {
  font-size: 20px;
}
.font_24 {
  font-size: 24px;
}
.font_weight_300 {
  font-weight: 300;
}

.font_weight_600 {
  font-weight: 600;
}
/* 行间距 */
.line_15 {
  line-height: 15px;
}
.line_20 {
  line-height: 20px;
}
.line_26 {
  line-height: 26px;
}
.line_height_40 {
  line-height: 40px;
}
/* 手势 */
.cursor_pointer {
  cursor: pointer !important;
}
/*圆角*/
.radius5 {
  border-radius: 5px;
}
.radius10 {
  border-radius: 10px;
}
.radius15 {
  border-radius: 15px;
}
/* 长度 */
.width_auto {
  width: auto;
}

.width__fill_available {
  width: 100%;
}
.min_width_220 {
  min-width: 220px !important;
}
.width_220 {
  width: 220px !important;
  min-width: 220px !important;
}
.height_fill_available {
  height: 100%;
}

.dis_flex {
  display: flex !important;
}

.flex_wrap {
  flex-wrap: wrap;
}

.flex_box {
  flex: 1;
}

.flex_dir_row {
  flex-direction: row;
}

.flex_dir_column {
  flex-direction: column;
}

.flex_x_center {
  justify-content: center;
}

.flex_x_between {
  justify-content: space-between;
}

.flex_x_around {
  justify-content: space-around;
}

.flex_x_end {
  justify-content: flex-end;
}
.flex_y_end {
  align-items: flex-end;
}

.flex_y_center {
  align-items: center;
}
/* 盒模型 */
.border_box {
  box-sizing: border-box;
}
.content_box {
  box-sizing: content-box;
}
/*内边距 */
.pad_5 {
  padding: 5px;
}

.pad_10 {
  padding: 10px;
}

.pad_15 {
  padding: 15px;
}

.pad_20 {
  padding: 20px;
}

.padtp_5 {
  padding-top: 5px;
}

.padtp_10 {
  padding-top: 10px !important;
}

.padtp_15 {
  padding-top: 15px;
}

.padtp_20 {
  padding-top: 20px;
}
.padbm_0 {
  padding-bottom: 0px;
}
.padbm_5 {
  padding-bottom: 5px;
}

.padbm_10 {
  padding-bottom: 10px;
}

.padbm_15 {
  padding-bottom: 15px !important;
}

.padbm_20 {
  padding-bottom: 20px;
}

.padlt_5 {
  padding-left: 5px;
}

.padlt_10 {
  padding-left: 10px !important;
}

.padlt_15 {
  padding-left: 15px;
}

.padlt_20 {
  padding-left: 20px;
}

.padrt_5 {
  padding-right: 5px;
}

.padrt_10 {
  padding-right: 10px !important;
}

.padrt_15 {
  padding-right: 15px;
}

.padrt_20 {
  padding-right: 20px !important;
}

.pad_0_5 {
  padding: 0 5px;
}

.pad_0_10 {
  padding: 0 10px;
}
.pad_0_15 {
  padding: 0 15px;
}
.pad_0_20 {
  padding: 0 20px !important;
}
.pad_10_15 {
  padding: 10px 15px;
}
.pad_5_15 {
  padding: 5px 15px;
}
.pad_10_0 {
  padding: 10px 0;
}
.pad_15_0 {
  padding: 15px 0;
}
.pad_20_0 {
  padding: 20px 0;
}
.pad_10_20 {
  padding: 10px 20px;
}
.pad_5_10 {
  padding: 5px 10px;
}
.pad_5_0 {
  padding: 5px 0px;
}
.pad_5_20 {
  padding: 5px 20px;
}

/* 外边距 */
.mar_5 {
  margin: 5px;
}

.mar_10 {
  margin: 10px;
}

.mar_15 {
  margin: 15px;
}
.martp_3 {
  margin-top: 3px !important;
}

.martp_5 {
  margin-top: 5px !important;
}

.martp_10 {
  margin-top: 10px !important;
}

.martp_12 {
  margin-top: 12px;
}
.martp_15 {
  margin-top: 15px;
}

.martp_20 {
  margin-top: 20px;
}
.martp_25 {
  margin-top: 25px;
}
.marbm_5 {
  margin-bottom: 5px !important;
}

.marbm_10 {
  margin-bottom: 10px !important;
}

.marbm_15 {
  margin-bottom: 15px;
}

.marbm_20 {
  margin-bottom: 20px;
}

.marlt_5 {
  margin-left: 5px;
}

.marlt_10 {
  margin-left: 10px;
}

.marlt_15 {
  margin-left: 15px;
}

.marlt_20 {
  margin-left: 20px;
}

.marlt_25 {
  margin-left: 25px;
}

.marrt_5 {
  margin-right: 5px;
}

.marrt_10 {
  margin-right: 10px;
}

.marrt_15 {
  margin-right: 15px;
}

.marrt_20 {
  margin-right: 20px;
}
.marrt_25 {
  margin-right: 25px;
}
.mar_0_10 {
  margin: 0 10px;
}
.mar_5_10 {
  margin: 5px 10px;
}
.mar_0_15 {
  margin: 0 15px;
}

.mar_15_0 {
  margin: 15px 0;
}
.mar_10_0 {
  margin: 10px 0;
}
.mar_lr_center {
  margin: 0 auto;
}
.line_height_23 {
  line-height: 23px;
}
.line_height_24 {
  line-height: 24px;
}

.line_height_32 {
  line-height: 32px !important;
}

.tip {
  padding: 8px 16px;
  background-color: #fff7f3;
  border-radius: 4px;
  border-left: 5px solid #81D8D0;
  margin: 0px 0px 10px 0px;
}

.message {
  border-radius: 4px;
  border-width: 1px;
  border-style: solid;
  border-color: #fff7f3;
  background-color: #fff7f3;
  padding: 15px 15px 15px 20px;
  display: flex;
  align-items: center;
}
.message .el-icon-info {
  color: #019EAF;
}

.nav_header {
  padding: 3px 5px 0px 5px;
}

.el-table__header tr th {
  color: #909399;
  background-color: #f5f7fa !important;
}

.el-table__header .el-table-column--selection > .cell {
  padding-right: 14px !important;
  padding-left: 14px !important;
}

.content_body {
  min-height: calc(100% - 30px);
  background-color: #fff;
  padding: 15px;
}
.content_body_nopadding {
  min-height: 100%;
  background-color: #fff;
}

.content {
  height: 100%;
}

.el-dialog .el-dialog__body .el-form .el-input {
  max-width: 300px;
}

.el-tooltip__popper {
  max-width: 800px;
}

.scroll::-webkit-scrollbar {
  width: 5px;
  height: 5px;
  background-color: #f8f8f8;
}
.scroll::-webkit-scrollbar-thumb {
  /*滚动条里面小方块*/
  border-radius: 5px;
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  background: #9093994d;
}
.el-input.is-disabled .el-input__inner {
  color: #606266 !important;
}
.vue-treeselect--disabled .vue-treeselect__control {
  background-color: #f5f7fa !important;
}
.vue-treeselect__single-value {
  color: #606266 !important;
}
.custom-input-number .el-input__inner {
  padding: 0 0 0 10px;
}

.page-component__scroll .el-scrollbar__wrap {
  overflow-x: hidden;
}

.position_relative {
  position: relative;
}

.position_absolute {
  position: absolute;
}
.overflow_hidden {
  overflow: hidden;
}
.el-dialog__body {
  padding: 10px 20px !important;
}
.float_left {
  float: left;
}
.clamp1 {
  display: inline-block;
  white-space: nowrap;
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
}
.text-bold {
  font-weight: bold;
}
