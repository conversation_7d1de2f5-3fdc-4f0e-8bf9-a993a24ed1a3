<template>
  <div class="CommissionStatistics content_body">
    <!-- 搜索 -->
    <div class="nav_header" style="padding: 15px 0 0 0">
      <el-form :inline="true" size="small" :model="staffSearchFrom" @submit.native.prevent>
        <el-row>
          <el-col :span="22">
            <el-form-item label="所属门店">
              <el-select
                v-model="staffSearchFrom.EntityID"
                filterable
                placeholder="请选择所属门店"
                :default-first-option="true"
                @change="changeEntityHandleCommissionStatisticsSearch"
                @clear="changeEntityHandleCommissionStatisticsSearch"
              >
                <el-option v-for="item in Entity_List" :key="item.ID" :label="item.EntityName" :value="item.ID"> </el-option>
              </el-select>
            </el-form-item>

            <!-- <el-form-item label="员工姓名">
          <el-select v-model="staffSearchFrom.EmployeeID" clearable filterable placeholder="请选择员工姓名" :default-first-option="true" @change="handleCommissionStatisticsSearch" @clear="handleCommissionStatistics">
            <el-option v-for="item in employeelList" :key="item.ID" :label="item.Name" :value="item.ID">
            </el-option>
          </el-select>
        </el-form-item> -->
            <el-form-item label="会员姓名">
              <el-input
                placeholder="请输入会员姓名"
                @keyup.enter.native="handleCommissionStatisticsSearch"
                @clear="handleCommissionStatisticsSearch"
                clearable
                v-model="staffSearchFrom.CustomerName"
              ></el-input>
            </el-form-item>
            <el-form-item label="订单编号">
              <el-input
                placeholder="请输入订单编号"
                @keyup.enter.native="handleCommissionStatisticsSearch"
                @clear="handleCommissionStatisticsSearch"
                clearable
                v-model="staffSearchFrom.BillID"
              ></el-input>
            </el-form-item>
            <el-form-item label="业绩类型">
              <el-select
                v-model="staffSearchFrom.Type"
                clearable
                filterable
                placeholder="请选择业绩类型"
                :default-first-option="true"
                @change="handleCommissionStatisticsSearch"
              >
                <el-option label="售卡" value="10"></el-option>
                <el-option label="储值账户抵扣" value="20"></el-option>
                <el-option label="赠送储值账户抵扣" value="30"></el-option>
                <el-option label="赠送" value="40"></el-option>
                <el-option label="充值" value="50"></el-option>
                <el-option label="项目快速开单" value="60"></el-option>
                <el-option label="项目消耗" value="70"></el-option>
                <el-option label="产品销售" value="80"></el-option>
                <el-option label="产品消耗" value="90"></el-option>
              </el-select>
            </el-form-item>
            <!-- <el-form-item label="卡类型" width="70">
          <el-select v-model="staffSearchFrom.CategoryGoodsType" clearable filterable placeholder="请选择卡类型" :default-first-option="true" @change="handleCommissionStatisticsSearch">
            <el-option label="项目" value="项目"></el-option>
            <el-option label="储值卡" value="储值卡"></el-option>
            <el-option label="时效卡" value="时效卡"></el-option>
            <el-option label="通用次卡" value="通用次卡"></el-option>
            <el-option label="套餐卡" value="套餐卡"></el-option>
            <el-option label="产品" value="产品"></el-option>
          </el-select>
        </el-form-item> -->
            <el-form-item label="商品名称">
              <el-input
                clearable
                @keyup.enter.native="handleCommissionStatisticsSearch"
                @clear="handleCommissionStatisticsSearch"
                placeholder="请输入商品名称"
                v-model="staffSearchFrom.GoodsName"
              ></el-input>
            </el-form-item>
            <el-form-item label="分类">
              <el-cascader
                v-model="staffSearchFrom.CategoryID"
                :options="categoryList"
                :props="cascaderProps"
                @change="handleCommissionStatisticsSearch"
                @clear="handleCommissionStatisticsSearch"
                clearable
              ></el-cascader>
            </el-form-item>
            <el-form-item label="结算周期">
              <el-date-picker
                v-model="staffSearchFrom.QueryDate"
                unlink-panels
                type="daterange"
                range-separator="至"
                value-format="yyyy-MM-dd"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                @change="handleCommissionStatisticsSearch"
                @clear="handleCommissionStatisticsSearch"
                :clearable="false"
                :picker-options="pickerOptions"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="2">
            <el-form-item>
              <el-button type="primary" v-prevent-click @click="handleCommissionStatisticsSearch">搜索</el-button>
            </el-form-item>
            <el-form-item>
              <el-button v-if="CommissionStatisticsExport" type="primary" size="small" :loading="downloadLoading" @click="downloadCommissionStatistics"
                >导出</el-button
              >
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <el-tabs v-if="employeelList && employeelList.length > 0" v-model="empActiveName" type="border-card" class="custom-tabs" @tab-click="changeEmployee">
      <el-tab-pane v-for="(item, index) in employeelList" :key="item.ID" :name="getEmpActiveName(index)">
        <span slot="label">
          <span>{{ item.Name }}</span>
          <span v-if="item.ID" class="marlt_5">({{ item.ID }})</span>
        </span>
      </el-tab-pane>
    </el-tabs>
    <div class="table-box" style="height: 70vh">
      <el-scrollbar style="height: 100%; max-width: 100%">
        <el-table
          :row-class-name="tableRowClassName"
          style="width: 100%"
          :header-cell-style="{
            height: '45px',
          }"
          border
          :show-body="false"
        >
          <el-table-column prop="Type" label="业绩类型" :width="TypeWidth"></el-table-column>
          <el-table-column prop="BillID" label="订单编号" :width="BillIDWidth" show-overflow-tooltip></el-table-column>
          <el-table-column prop="BillDate" label="订单日期" width="90">
            <template slot-scope="scope">
              {{ scope.row.BillDate | dateFormat("YYYY-MM-DD") }}
            </template>
          </el-table-column>
          <el-table-column prop="CustomerName" label="会员/卡号" width="100">
            <template slot-scope="scope">
              <span>{{ scope.row.CustomerName }}</span>
              <span v-if="scope.row.CustomerCode">/{{ scope.row.CustomerCode }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="GoodsTypeName" label="卡类型" width="70"></el-table-column>
          <el-table-column prop="ParentCategoryName" label="分类" width="90">
            <template slot-scope="scope">
              {{ getCategory(scope.row) }}
            </template>
          </el-table-column>
          <el-table-column label="所属卡项" :width="OriginCardWidth" prop="OriginCard"></el-table-column>
          <el-table-column prop="GoodName" label="商品名称" width="110"></el-table-column>
          <el-table-column prop="Alias" label="代码" width="80"></el-table-column>
          <el-table-column prop="IsLargess" label="是否赠送" width="60">
            <template slot-scope="scope">
              {{ scope.row.IsLargess ? "是" : "否" }}
            </template>
          </el-table-column>
          <el-table-column prop="PayMethodName" label="支付方式" width="120"></el-table-column>
          <el-table-column label="原价" width="70" prop="Price">
            <template slot-scope="scope">
              {{ scope.row.Price | toFixed | NumFormat }}
            </template>
          </el-table-column>
          <el-table-column label="折扣" width="70" prop="Discount"></el-table-column>
          <el-table-column prop="TotalAmount" label="营业额">
            <template slot-scope="scope">
              {{ scope.row.TotalAmount | toFixed | NumFormat }}
            </template>
          </el-table-column>
          <el-table-column label="虚业绩" prop="OriginAmount">
            <template slot-scope="scope">
              {{ scope.row.OriginAmount | toFixed | NumFormat }}
            </template>
          </el-table-column>
          <el-table-column label="实业绩" prop="Performance">
            <template slot-scope="scope">
              {{ scope.row.Performance | toFixed | NumFormat }}
            </template>
          </el-table-column>
          <el-table-column label="提成" prop="Commission">
            <template slot-scope="scope">
              {{ scope.row.Commission | toFixed | NumFormat }}
            </template>
          </el-table-column>
          <el-table-column label="挂账" prop="ArrearAmount">
            <template slot-scope="scope">
              {{ scope.row.ArrearAmount | toFixed | NumFormat }}
            </template>
          </el-table-column>
          <el-table-column prop="detailInfo" width="200" label="明细"></el-table-column>
        </el-table>
        <!-- 表格 -->
        <div class="data-box">
          <el-scrollbar style="height: 70vh; width: 100%" v-loading="entityIncomeStatementLoading">
            <div style="width: 100%">
              <!-- 卡项 -->
              <el-table
                :row-class-name="tableRowClassName"
                :data="cardDetailList"
                style="width: 100%"
                show-summary
                :summary-method="getcardDetailSum"
                :show-header="false"
                v-if="cardDetailList && cardDetailList.length != 0"
                :span-method="cardDetailSpanMethod"
              >
                <el-table-column prop="Type" label="业绩类型" :width="TypeWidth"></el-table-column>
                <el-table-column prop="BillID" label="订单编号" :width="BillIDWidth" show-overflow-tooltip></el-table-column>
                <el-table-column prop="BillDate" label="订单日期" width="90">
                  <template slot-scope="scope">
                    {{ scope.row.BillDate | dateFormat("YYYY-MM-DD") }}
                  </template>
                </el-table-column>
                <el-table-column prop="CustomerName" label="会员名称" width="100">
                  <template slot-scope="scope">
                    <span>{{ scope.row.CustomerName }}</span>
                    <span v-if="scope.row.CustomerCode">/{{ scope.row.CustomerCode }}</span>
                  </template>
                </el-table-column>
                <el-table-column prop="GoodsTypeName" label="卡类型" width="70"></el-table-column>
                <el-table-column prop="ParentCategoryName" label="分类" width="90">
                  <template slot-scope="scope">
                    {{ getCategory(scope.row) }}
                  </template>
                </el-table-column>
                <el-table-column label="所属卡项"  :width="OriginCardWidth"  prop="OriginCard"></el-table-column>
                <el-table-column prop="GoodName" label="商品名称" width="110"></el-table-column>
                <el-table-column prop="Alias" label="代码" width="80"></el-table-column>
                <el-table-column prop="IsLargess" label="是否赠送" width="60">
                  <template slot-scope="scope">
                    {{ scope.row.IsLargess ? "是" : "否" }}
                  </template>
                </el-table-column>
                <el-table-column prop="PayMethodName" label="支付方式" width="120"></el-table-column>
                <el-table-column label="原价" width="70" prop="Price">
                  <template slot-scope="scope">
                    {{ scope.row.Price | toFixed | NumFormat }}
                  </template>
                </el-table-column>
                <el-table-column label="折扣" width="70" prop="Discount"></el-table-column>
                <el-table-column prop="TotalAmount" label="营业额">
                  <template slot-scope="scope">
                    {{ scope.row.TotalAmount | toFixed | NumFormat }}
                  </template>
                </el-table-column>
                <el-table-column label="虚业绩" prop="OriginAmount">
                  <template slot-scope="scope">
                    {{ scope.row.OriginAmount | toFixed | NumFormat }}
                  </template>
                </el-table-column>
                <el-table-column label="实业绩" prop="Performance">
                  <template slot-scope="scope">
                    {{ scope.row.Performance | toFixed | NumFormat }}
                  </template>
                </el-table-column>
                <el-table-column label="提成" prop="Commission">
                  <template slot-scope="scope">
                    {{ scope.row.Commission | toFixed | NumFormat }}
                  </template>
                </el-table-column>
                <el-table-column label="挂账" prop="ArrearAmount">
                  <template slot-scope="scope">
                    {{ scope.row.ArrearAmount | toFixed | NumFormat }}
                  </template>
                </el-table-column>
                <el-table-column prop="detailInfo" width="200" label="明细"></el-table-column>
              </el-table>
              <!-- 项目 -->
              <el-table
                :row-class-name="tableRowClassName"
                :data="projectDetailList"
                style="width: 100%"
                :show-header="false"
                show-summary
                :summary-method="getprojectDetailSum"
                v-if="projectDetailList && projectDetailList.length != 0"
              >

              
                <!-- :span-method="projectDetailSpanMethod" -->
                <el-table-column prop="Type" label="业绩类型" :width="TypeWidth"></el-table-column>
                <el-table-column prop="BillID" label="订单编号" :width="BillIDWidth" show-overflow-tooltip></el-table-column>
                <el-table-column prop="BillDate" label="订单日期" width="90">
                  <template slot-scope="scope">
                    {{ scope.row.BillDate | dateFormat("YYYY-MM-DD") }}
                  </template>
                </el-table-column>
                <el-table-column prop="CustomerName" label="会员名称" width="100">
                  <template slot-scope="scope">
                    <span>{{ scope.row.CustomerName }}</span>
                    <span v-if="scope.row.CustomerCode">/{{ scope.row.CustomerCode }}</span>
                  </template>
                </el-table-column>
                <el-table-column prop="GoodsTypeName" label="卡类型" width="70"></el-table-column>
                <el-table-column prop="ParentCategoryName" label="分类" width="90">
                  <template slot-scope="scope">
                    {{ getCategory(scope.row) }}
                  </template>
                </el-table-column>
                <el-table-column label="所属卡项" :width="OriginCardWidth" prop="OriginCard"></el-table-column>
                <el-table-column prop="GoodName" label="商品名称" width="110"></el-table-column>
                <el-table-column prop="Alias" label="代码" width="80"></el-table-column>
                <el-table-column prop="IsLargess" label="是否赠送" width="60">
                  <template slot-scope="scope">
                    {{ scope.row.IsLargess ? "是" : "否" }}
                  </template>
                </el-table-column>
                <el-table-column prop="PayMethodName" label="支付方式" width="120"></el-table-column>
                <el-table-column label="原价" width="70" prop="Price">
                  <template slot-scope="scope">
                    {{ scope.row.Price | toFixed | NumFormat }}
                  </template>
                </el-table-column>
                <el-table-column label="折扣" width="70" prop="Discount"></el-table-column>
                <el-table-column prop="TotalAmount" label="营业额">
                  <template slot-scope="scope">
                    {{ scope.row.TotalAmount | toFixed | NumFormat }}
                  </template>
                </el-table-column>
                <el-table-column label="虚业绩" prop="OriginAmount">
                  <template slot-scope="scope">
                    {{ scope.row.OriginAmount | toFixed | NumFormat }}
                  </template>
                </el-table-column>
                <el-table-column label="实业绩" prop="Performance">
                  <template slot-scope="scope">
                    {{ scope.row.Performance | toFixed | NumFormat }}
                  </template>
                </el-table-column>
                <el-table-column label="提成" prop="Commission">
                  <template slot-scope="scope">
                    {{ scope.row.Commission | toFixed | NumFormat }}
                  </template>
                </el-table-column>
                <el-table-column label="挂账" prop="ArrearAmount">
                  <template slot-scope="scope">
                    {{ scope.row.ArrearAmount | toFixed | NumFormat }}
                  </template>
                </el-table-column>
                <el-table-column prop="detailInfo" width="200" label="明细"></el-table-column>
              </el-table>
              <!-- 销售产品 -->
              <el-table
                :row-class-name="tableRowClassName"
                :data="saleProductDetailList"
                style="width: 100%"
                :show-header="false"
                show-summary
                :summary-method="getSaleProductDetailSum"
                :span-method="saleProductDetailSpanMethod"
                v-if="saleProductDetailList && saleProductDetailList.length != 0"
              >
                <el-table-column prop="Type" label="业绩类型" :width="TypeWidth"></el-table-column>
                <el-table-column prop="BillID" label="订单编号" :width="BillIDWidth" show-overflow-tooltip></el-table-column>
                <el-table-column prop="BillDate" label="订单日期" width="90">
                  <template slot-scope="scope">
                    {{ scope.row.BillDate | dateFormat("YYYY-MM-DD") }}
                  </template>
                </el-table-column>
                <el-table-column prop="CustomerName" label="会员名称" width="100">
                  <template slot-scope="scope">
                    <span>{{ scope.row.CustomerName }}</span>
                    <span v-if="scope.row.CustomerCode">/{{ scope.row.CustomerCode }}</span>
                  </template>
                </el-table-column>
                <el-table-column prop="GoodsTypeName" label="卡类型" width="70"></el-table-column>
                <el-table-column prop="ParentCategoryName" label="分类" width="90">
                  <template slot-scope="scope">
                    {{ getCategory(scope.row) }}
                  </template>
                </el-table-column>
                <el-table-column label="所属卡项" :width="OriginCardWidth" prop="OriginCard"></el-table-column>
                <el-table-column prop="GoodName" label="商品名称" width="110"></el-table-column>
                <el-table-column prop="Alias" label="代码" width="80"></el-table-column>
                <el-table-column prop="IsLargess" label="是否赠送" width="60">
                  <template slot-scope="scope">
                    {{ scope.row.IsLargess ? "是" : "否" }}
                  </template>
                </el-table-column>
                <el-table-column prop="PayMethodName" label="支付方式" width="120"></el-table-column>
                <el-table-column label="原价" width="70" prop="Price">
                  <template slot-scope="scope">
                    {{ scope.row.Price | toFixed | NumFormat }}
                  </template>
                </el-table-column>
                <el-table-column label="折扣" width="70" prop="Discount"></el-table-column>
                <el-table-column prop="TotalAmount" label="营业额">
                  <template slot-scope="scope">
                    {{ scope.row.TotalAmount | toFixed | NumFormat }}
                  </template>
                </el-table-column>
                <el-table-column label="虚业绩" prop="OriginAmount">
                  <template slot-scope="scope">
                    {{ scope.row.OriginAmount | toFixed | NumFormat }}
                  </template>
                </el-table-column>
                <el-table-column label="实业绩" prop="Performance">
                  <template slot-scope="scope">
                    {{ scope.row.Performance | toFixed | NumFormat }}
                  </template>
                </el-table-column>
                <el-table-column label="提成" prop="Commission">
                  <template slot-scope="scope">
                    {{ scope.row.Commission | toFixed | NumFormat }}
                  </template>
                </el-table-column>
                <el-table-column label="挂账" prop="ArrearAmount">
                  <template slot-scope="scope">
                    {{ scope.row.ArrearAmount | toFixed | NumFormat }}
                  </template>
                </el-table-column>
                <el-table-column prop="detailInfo" width="200" label="明细"></el-table-column>
              </el-table>
              <!-- 消耗产品 -->
              <el-table
                :row-class-name="tableRowClassName"
                :data="treatProductDetailList"
                style="width: 100%"
                :show-header="false"
                show-summary
                :summary-method="getTreatProductDetailSum"
                v-if="treatProductDetailList && treatProductDetailList.length != 0"
                :span-method="treatProductDetailSpanMethod"
              >
                <el-table-column prop="Type" label="业绩类型" :width="TypeWidth">
                  <template slot-scope="scope">
                    <span class="font_weight_600">{{ scope.row.Type }}</span>
                  </template>
                </el-table-column>
                <el-table-column prop="BillID" label="订单编号" :width="BillIDWidth" show-overflow-tooltip></el-table-column>
                <el-table-column prop="BillDate" label="订单日期" width="90">
                  <template slot-scope="scope">
                    {{ scope.row.BillDate | dateFormat("YYYY-MM-DD") }}
                  </template>
                </el-table-column>
                <el-table-column prop="CustomerName" label="会员名称" width="100">
                  <template slot-scope="scope">
                    <span>{{ scope.row.CustomerName }}</span>
                    <span v-if="scope.row.CustomerCode">/{{ scope.row.CustomerCode }}</span>
                  </template>
                </el-table-column>
                <el-table-column prop="GoodsTypeName" label="卡类型" width="70"></el-table-column>
                <el-table-column prop="ParentCategoryName" label="分类" width="90">
                  <template slot-scope="scope">
                    {{ getCategory(scope.row) }}
                  </template>
                </el-table-column>
                <el-table-column label="所属卡项" :width="OriginCardWidth" prop="OriginCard"></el-table-column>
                <el-table-column prop="GoodName" label="商品名称" width="110"></el-table-column>
                <el-table-column prop="Alias" label="代码" width="80"></el-table-column>
                <el-table-column prop="IsLargess" label="是否赠送" width="60">
                  <template slot-scope="scope">
                    {{ scope.row.IsLargess ? "是" : "否" }}
                  </template>
                </el-table-column>
                <el-table-column prop="PayMethodName" label="支付方式" width="120"></el-table-column>
                <el-table-column label="原价" width="70" prop="Price">
                  <template slot-scope="scope">
                    {{ scope.row.Price | toFixed | NumFormat }}
                  </template>
                </el-table-column>
                <el-table-column label="折扣" width="70" prop="Discount"></el-table-column>
                <el-table-column prop="TotalAmount" label="营业额">
                  <template slot-scope="scope">
                    {{ scope.row.TotalAmount | toFixed | NumFormat }}
                  </template>
                </el-table-column>
                <el-table-column label="虚业绩" prop="OriginAmount">
                  <template slot-scope="scope">
                    {{ scope.row.OriginAmount | toFixed | NumFormat }}
                  </template>
                </el-table-column>
                <el-table-column label="实业绩" prop="Performance">
                  <template slot-scope="scope">
                    {{ scope.row.Performance | toFixed | NumFormat }}
                  </template>
                </el-table-column>
                <el-table-column label="提成" prop="Commission">
                  <template slot-scope="scope">
                    {{ scope.row.Commission | toFixed | NumFormat }}
                  </template>
                </el-table-column>
                <el-table-column label="挂账" prop="ArrearAmount">
                  <template slot-scope="scope">
                    {{ scope.row.ArrearAmount | toFixed | NumFormat }}
                  </template>
                </el-table-column>
                <el-table-column prop="detailInfo" width="200" label="明细"></el-table-column>
              </el-table>
              <!-- 员工合计 -->
              <el-table
                :row-class-name="staffTableRowClassName"
                :data="staffTableData"
                style="width: 100%"
                :show-header="false"
                v-if="staffTableData && staffTableData.length != 0"
                :span-method="arraySpanMethod"
              >
                <el-table-column prop="Type" label="业绩类型" :width="TypeWidth">
                  <template slot-scope="scope">
                    <span class="font_weight_600">{{ scope.row.Type }}</span>
                  </template>
                </el-table-column>
                <el-table-column prop="BillID" label="订单编号" :width="BillIDWidth" show-overflow-tooltip></el-table-column>
                <el-table-column prop="BillDate" label="订单日期" width="90" :formatter="formatterBillDate"></el-table-column>
                <el-table-column prop="CustomerName" label="会员名称" width="100"></el-table-column>
                <el-table-column prop="GoodsTypeName" label="卡类型" width="70"></el-table-column>
                <el-table-column prop="ParentCategoryName" label="分类" width="90"></el-table-column>
                <el-table-column label="所属卡项" :width="OriginCardWidth"></el-table-column>
                <el-table-column prop="GoodName" label="商品名称" width="110"></el-table-column>
                <el-table-column prop="Alias" label="代码" width="80"></el-table-column>
                <el-table-column prop="IsLargess" label="是否赠送" width="60"> </el-table-column>
                <el-table-column prop="PayMethodName" label="支付方式" width="120">
                  <template slot-scope="scope">
                    <span class="font_weight_600">{{ scope.row.PayMethodName }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="原价" width="70"></el-table-column>
                <el-table-column label="折扣" width="70"></el-table-column>
                <el-table-column prop="TotalAmount" label="营业额">
                  <template slot-scope="scope">
                    <span class="font_weight_600">{{ scope.row.TotalAmount | toFixed | NumFormat }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="虚业绩" prop="OriginAmount">
                  <template slot-scope="scope">
                    <span class="font_weight_600">{{ scope.row.OriginAmount | toFixed | NumFormat }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="实业绩" prop="Performance">
                  <template slot-scope="scope">
                    <span class="font_weight_600">{{ scope.row.Performance | toFixed | NumFormat }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="提成" prop="Commission">
                  <template slot-scope="scope">
                    <span class="font_weight_600">{{ scope.row.Commission | toFixed | NumFormat }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="挂账" prop="ArrearAmount">
                  <template slot-scope="scope">
                    <span class="font_weight_600">{{ scope.row.ArrearAmount | toFixed | NumFormat }}</span>
                  </template>
                </el-table-column>
                <el-table-column prop="detailInfo" width="200" label="明细"></el-table-column>
              </el-table>
              <!-- 小计汇总 -->
              <el-table
                :row-class-name="setTableRowColor"
                v-if="subtotalSummary && subtotalSummary.length != 0"
                :data="subtotalSummary"
                style="width: 100%"
                :show-header="false"
              >
                <el-table-column prop="Type" label="业绩类型" :width="TypeWidth"></el-table-column>
                <el-table-column prop="BillID" label="订单编号" :width="BillIDWidth" show-overflow-tooltip></el-table-column>
                <el-table-column prop="BillDate" label="订单日期" width="90" :formatter="formatterBillDate"></el-table-column>
                <el-table-column prop="CustomerName" label="会员名称" width="100"></el-table-column>
                <el-table-column prop="GoodsTypeName" label="卡类型" width="70"></el-table-column>
                <el-table-column prop="ParentCategoryName" label="分类" width="90"></el-table-column>
                <el-table-column label="所属卡项" :width="OriginCardWidth"></el-table-column>
                <el-table-column prop="GoodName" label="商品名称" width="110"></el-table-column>
                <el-table-column prop="Alias" label="代码" width="80">
                  <template slot-scope="scope">
                    <span class="font_weight_600" v-if="scope.$index == 0">小计汇总</span>
                  </template>
                </el-table-column>
                <el-table-column prop="IsLargess" label="是否赠送" width="60"></el-table-column>
                <el-table-column prop="PayMethodName" label="支付方式" width="120"></el-table-column>
                <el-table-column label="原价" width="70"></el-table-column>
                <el-table-column label="折扣" width="70"></el-table-column>
                <el-table-column prop="TotalAmount" label="营业额">
                  <template slot-scope="scope">
                    {{ scope.row.TotalAmount | toFixed | NumFormat }}
                  </template>
                </el-table-column>
                <el-table-column label="虚业绩" prop="OriginAmount">
                  <template slot-scope="scope">
                    {{ scope.row.OriginAmount | toFixed | NumFormat }}
                  </template>
                </el-table-column>
                <el-table-column label="实业绩" prop="Performance">
                  <template slot-scope="scope">
                    {{ scope.row.Performance | toFixed | NumFormat }}
                  </template>
                </el-table-column>
                <el-table-column label="提成" prop="Commission">
                  <template slot-scope="scope">
                    {{ scope.row.Commission | toFixed | NumFormat }}
                  </template>
                </el-table-column>
                <el-table-column label="挂账" prop="ArrearAmount">
                  <template slot-scope="scope">
                    {{ scope.row.ArrearAmount | toFixed | NumFormat }}
                  </template>
                </el-table-column>
                <el-table-column prop="detailInfo" width="200" label="明细"></el-table-column>
              </el-table>
              <!-- 支付方式汇总 -->
              <el-table
                :row-class-name="tableRowClassName"
                v-if="payMethodFormList && payMethodFormList.length != 0"
                :data="payMethodFormList"
                style="width: 100%"
                :show-header="false"
                :span-method="arrayPayMethod"
              >
                <el-table-column prop="Type" label="业绩类型" :width="TypeWidth"></el-table-column>
                <el-table-column prop="BillID" label="订单编号" :width="BillIDWidth" show-overflow-tooltip></el-table-column>
                <el-table-column prop="BillDate" label="订单日期" width="90" :formatter="formatterBillDate"></el-table-column>
                <el-table-column prop="CustomerName" label="会员名称" width="100"></el-table-column>
                <el-table-column prop="GoodsTypeName" label="卡类型" width="70"></el-table-column>
                <el-table-column prop="ParentCategoryName" label="分类" width="90"></el-table-column>
                <el-table-column label="所属卡项" :width="OriginCardWidth"></el-table-column>
                <el-table-column prop="GoodName" label="商品名称" width="110"></el-table-column>
                <el-table-column prop="Alias" label="代码" width="80">
                  <template slot-scope="scope">
                    <span class="font_weight_600" v-if="scope.$index == 0">支付方式汇总</span>
                  </template>
                </el-table-column>
                <el-table-column prop="IsLargess" label="是否赠送" width="60"></el-table-column>
                <el-table-column label="支付方式" width="120">
                  <template slot-scope="scope">
                    {{ scope.row.Name }}
                  </template>
                </el-table-column>
                <el-table-column label="原价" width="70"></el-table-column>
                <el-table-column label="折扣" width="70"></el-table-column>
                <el-table-column prop="TotalAmount" label="营业额">
                  <template slot-scope="scope">
                    {{ scope.row.TotalAmount | toFixed | NumFormat }}
                  </template>
                </el-table-column>
                <el-table-column label="虚业绩" prop="OriginAmount">
                  <template slot-scope="scope">
                    {{ scope.row.OriginAmount | toFixed | NumFormat }}
                  </template>
                </el-table-column>
                <el-table-column label="实业绩" prop="Performance">
                  <template slot-scope="scope">
                    {{ scope.row.Performance | toFixed | NumFormat }}
                  </template>
                </el-table-column>
                <el-table-column label="提成" prop="Commission"></el-table-column>
                <el-table-column label="挂账" prop="ArrearAmount"></el-table-column>
                <el-table-column prop="detailInfo" width="200" label="明细"></el-table-column>
              </el-table>
              <!-- 账户 -->
              <el-table
                :row-class-name="tableRowClassName"
                v-if="accountList && accountList.length != 0"
                :data="accountList"
                style="width: 100%"
                :show-header="false"
              >
                <el-table-column prop="Type" label="业绩类型" :width="TypeWidth"></el-table-column>
                <el-table-column prop="BillID" label="订单编号" :width="BillIDWidth" show-overflow-tooltip></el-table-column>
                <el-table-column prop="BillDate" label="订单日期" width="90" :formatter="formatterBillDate"></el-table-column>
                <el-table-column prop="CustomerName" label="会员名称" width="100"></el-table-column>
                <el-table-column prop="GoodsTypeName" label="卡类型" width="70"></el-table-column>
                <el-table-column prop="ParentCategoryName" label="分类" width="90"></el-table-column>
                <el-table-column label="所属卡项" :width="OriginCardWidth"></el-table-column>
                <el-table-column prop="GoodName" label="商品名称" width="110"></el-table-column>
                <el-table-column prop="Alias" label="代码" width="80"></el-table-column>
                <el-table-column prop="IsLargess" label="是否赠送" width="60"> </el-table-column>
                <el-table-column prop="PayMethodName" label="支付方式" width="120"></el-table-column>
                <el-table-column label="原价" width="70"></el-table-column>
                <el-table-column label="折扣" width="70"></el-table-column>
                <el-table-column prop="TotalAmount" label="营业额">
                  <template slot-scope="scope">
                    {{ scope.row.TotalAmount | toFixed | NumFormat }}
                  </template>
                </el-table-column>
                <el-table-column label="虚业绩" prop="OriginAmount">
                  <template slot-scope="scope">
                    {{ scope.row.OriginAmount | toFixed | NumFormat }}
                  </template>
                </el-table-column>
                <el-table-column label="实业绩" prop="Performance">
                  <template slot-scope="scope">
                    {{ scope.row.Performance | toFixed | NumFormat }}
                  </template>
                </el-table-column>
                <el-table-column label="提成" prop="Commission"> </el-table-column>
                <el-table-column label="挂账" prop="ArrearAmount"> </el-table-column>
                <el-table-column prop="detailInfo" width="200" label="明细"></el-table-column>
              </el-table>
            </div>
          </el-scrollbar>
        </div>
      </el-scrollbar>
    </div>
  </div>
</template>
<script>
import API from "@/api/Report/YZH/Employee/commissionStatistics";
import APIClassification from "@/api/Report/Goods/SaleStatistics.js";
const dayjs = require("dayjs");
const isoWeek = require("dayjs/plugin/isoWeek");
dayjs.extend(isoWeek);
export default {
  name: "ReportYZHEmployeeCommissionStatistics",
  props: {},
  /**  引入的组件  */
  components: {},
  /**  Vue 实例的数据对象**/
  data() {
    return {
      TypeWidth: 100,
      BillIDWidth: 90,
      OriginCardWidth: 100,
      employeelList: [],
      Entity_List: [],
      cardSaleForm: {}, //销售卡项
      productSaleForm: {}, //产品销售
      serviceProjectForm: {}, //服务项目
      savingCardTreatForm: {}, //储值卡消耗
      projectTreatForm: {}, // 疗程账户合计
      generalCardTreatForm: {}, // 通用次卡账户
      timeCardTreatForm: {},
      packageCardTreatForm: {},
      savingCardDeductionForm: {}, //储值抵扣合计
      payMethodForm: {}, //支付方式
      projectFastBillForm: {}, //项目快速开单
      employeePerformanceCommissionSumForm: {}, //合计
      employeeCardPerformanceCommissionSumForm: {},
      employeeProjectPerformanceCommissionSumForm: {},
      employeeSaleProductPerformanceCommissionSumForm: {},
      employeeTreatProductPerformanceCommissionSumForm: {},
      largessSavingCardTreatForm: {},
      entityIncomeStatementLoading: false,
      CommissionStatisticsExport: true, //是否导出
      downloadLoading: false,
      tableLength: 1,
      staffAllTableData: [], //表格数据
      cardDetailList: [], //表格数据
      projectDetailList: [], //表格数据
      saleProductDetailList: [], //表格数据
      treatProductDetailList: [], // 表格数据
      staffTableData: [],
      subtotalSummary: [], // 小计汇总
      payMethodFormList: [], // 支付方式
      accountList: [],
      EmployeeID: "",
      EmployeeName: "",
      tableDataLength: 0,
      staffSearchFrom: {
        EntityID: "",
        EmployeeID: "", //员工编号
        CustomerName: "", //会员姓名
        BillID: "", //订单编号
        Type: "", //业绩类型
        GoodsName: "", //商品名称
        CategoryGoodsType: "", //卡类型
        CategoryID: "",
        QueryDate: [dayjs().format("YYYY-MM-DD"), dayjs().format("YYYY-MM-DD")],
      },
      staffPaginations: {
        page: 1, // 当前位于哪页
        total: 0, // 总数
        page_size: 0, // 1页显示多少条
        layout: "total, prev, pager, next,jumper", // 翻页属性
      },
      categoryList: [],
      cascaderProps: {
        checkStrictly: true,
        label: "Name",
        value: "ID",
        children: "Child",
      },

      pickerOptions: {
        // disabledDate(time) {
        //   const startDate = new Date();
        //   startDate.setTime(startDate.getTime() - 3600 * 1000 * 24 * 100);
        //   if (startDate.getTime() < time.getTime() && time.getTime() < new Date().getTime()) {
        //     return false;
        //   } else {
        //     return true;
        //   }
        // },

        shortcuts: [
          {
            text: "今天",
            onClick: (picker) => {
              let end = new Date();
              let start = new Date();
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "本周",
            onClick: (picker) => {
              let end = new Date();
              let weekDay = dayjs(end).isoWeekday();
              let start = dayjs(end)
                .subtract(weekDay - 1, "day")
                .toDate();
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "本月",
            onClick: (picker) => {
              let end = new Date();
              let start = dayjs(dayjs(end).format("YYYY-MM") + "01").toDate();
              picker.$emit("pick", [start, end]);
            },
          },
        ],
      },
      empActiveName: "0",
    };
  },
  /**计算属性  */
  computed: {},
  /**  方法集合  */
  methods: {
    getEmpActiveName(index) {
      if (index == null || index == undefined) {
        index = 0;
      }
      let name = index.toFixed();
      return name;
    },
    /**    */
    formatterBillDate(row) {
      if (!row || !row.BillDate) {
        return "";
      }
      return row.BillDate.split(" ")[0];
    },
    /**    */
    tableRowClassName({ rowIndex }) {
      if (rowIndex % 2 === 0) {
        return "warning-row";
      } else {
        return "success-row";
      }
    },
    staffTableRowClassName({ rowIndex }) {
      if (rowIndex == 0) {
        return "staffTableColor";
      }
    },
    setTableRowColor({ row }) {
      if (row.PayMethodName == "卡项销售小计") {
        return "cardColor";
      } else if (row.PayMethodName == "服务项目小计") {
        return "projectColor";
      } else if (row.PayMethodName == "产品销售小计") {
        return "productColor";
      } else if (row.PayMethodName == "产品消耗小计") {
        return "treatProductColor";
      }
    },
    handleCommissionStatistics() {
      let that = this;
      that.tishi();
    },
    /* 搜索 */
    handleCommissionStatisticsSearch() {
      let that = this;
      that.staffPaginations.page = 1;
      that.empActiveName = "0";
      that.employeelList = [];
      that.list_employeePerformanceCommissionDetailStatement();
    },
    /**   切换门店 */
    changeEntityHandleCommissionStatisticsSearch() {
      let that = this;
      that.staffPaginations.page = 1;
      that.employeelList = [];
      that.empActiveName = "0";
      // that.getEntityEmployee_list();
      that.list_employeePerformanceCommissionDetailStatement();
    },
    /** 切换员工   */
    changeEmployee(ev) {
      let that = this;
      that.tableDataLength = 0;

      let temp = JSON.parse(JSON.stringify(that.staffAllTableData[ev.index]));
      that.cardDetailList = temp.cardDetail;
      that.employeeCardPerformanceCommissionSumForm = temp.employeeCardPerformanceCommissionSumForm;
      that.employeePerformanceCommissionSumForm = temp.employeePerformanceCommissionSumForm;
      that.employeeSaleProductPerformanceCommissionSumForm = temp.employeeSaleProductPerformanceCommissionSumForm;
      that.employeeTreatProductPerformanceCommissionSumForm = temp.employeeTreatProductPerformanceCommissionSumForm;
      that.employeeProjectPerformanceCommissionSumForm = temp.employeeProjectPerformanceCommissionSumForm;
      that.largessSavingCardTreatForm = temp.largessSavingCardTreatForm;
      that.payMethodForm = temp.payMethodForm;
      that.saleProductDetailList = temp.saleProductDetail;
      that.treatProductDetailList = temp.treatProductDetail;
      that.projectDetailList = temp.projectDetail;
      that.projectTreatForm = temp.projectTreatForm;
      that.generalCardTreatForm = temp.generalCardTreatForm;
      that.timeCardTreatForm = temp.timeCardTreatForm;
      that.packageCardTreatForm = temp.packageCardTreatForm;
      that.savingCardTreatForm = temp.savingCardTreatForm;
      that.EmployeeID = temp.EmployeeID;
      that.EmployeeName = temp.EmployeeName;

      that.addRow();
    },
    /* 分页 */
    handlestaffPageChange(page) {
      let that = this;
      that.staffPaginations.page = page;
    },
    /* 导出 */
    downloadCommissionStatistics() {
      let that = this;
      // if (dayjs(that.staffSearchFrom.QueryDate[0]).add(366, "day").valueOf() < dayjs(that.staffSearchFrom.QueryDate[1]).valueOf()) {
      //   that.$message.error("时间筛选范围不能超366天");
      //   return;
      // }
      that.downloadLoading = true;
      let params = Object.assign({}, that.staffSearchFrom);
      /*  params.EmployeeID = params.EmployeeID;
      params.CustomerName = params.CustomerName;
      params.BillID = params.BillID;
      params.Type = params.Type;
      params.GoodsName = params.GoodsName; */
      // params.CategoryGoodsType = params.CategoryGoodsType
      /* params.CategoryID = null */
      if (Array.from(params.CategoryID).length > 0) {
        switch (params.CategoryID[0]) {
          case 10:
            params.CategoryGoodsType = "产品";
            break;
          case 20:
            params.CategoryGoodsType = "项目";
            break;

          case 30:
            params.CategoryGoodsType = "通用次卡";
            break;

          case 40:
            params.CategoryGoodsType = "时效卡";
            break;

          case 50:
            params.CategoryGoodsType = "储值卡";
            break;
          case 60:
            params.CategoryGoodsType = "套餐卡";
            break;

          default:
            break;
        }
      }
      if (Array.from(params.CategoryID).length == 2) {
        params.CategoryID = params.CategoryID[1];
      } else if (Array.from(params.CategoryID).length == 3) {
        params.CategoryID = params.CategoryID[2];
      } else {
        params.CategoryID = "";
      }
      // params.StartDate = "2021-09-15"
      // params.EndDate = "2021-09-15"
      params.StartDate = params.QueryDate ? params.QueryDate[0] : "";
      params.EndDate = params.QueryDate ? params.QueryDate[1] : "";
      API.excel_employeePerformanceCommissionDetailStatement(params)
        .then((res) => {
          this.$message.success({
            message: "正在导出",
            duration: "4000",
          });
          const link = document.createElement("a");
          let blob = new Blob([res], { type: "application/octet-stream" });
          link.style.display = "none";
          link.href = URL.createObjectURL(blob);
          link.download = "员工业绩统计明细表.xlsx"; //下载的文件名
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
        })
        .finally(() => {
          that.downloadLoading = false;
        });
    },
    /* 列表数据 */
    async list_employeePerformanceCommissionDetailStatement() {
      let that = this;

      // if (dayjs(that.staffSearchFrom.QueryDate[0]).add(366, "day").valueOf() < dayjs(that.staffSearchFrom.QueryDate[1]).valueOf()) {
      //   that.$message.error("时间筛选范围不能超366天");
      //   return;
      // }
      let params = Object.assign({}, that.staffSearchFrom);
      /*  params.EmployeeID = params.EmployeeID;
      params.CustomerName = params.CustomerName;
      params.BillID = params.BillID;
      params.Type = params.Type;
      params.GoodsName = params.GoodsName; */
      // params.CategoryGoodsType = params.CategoryGoodsType
      /* params.CategoryID = null */
      if (Array.from(params.CategoryID).length > 0) {
        switch (params.CategoryID[0]) {
          case 10:
            params.CategoryGoodsType = "产品";
            break;
          case 20:
            params.CategoryGoodsType = "项目";
            break;

          case 30:
            params.CategoryGoodsType = "通用次卡";
            break;

          case 40:
            params.CategoryGoodsType = "时效卡";
            break;

          case 50:
            params.CategoryGoodsType = "储值卡";
            break;
          case 60:
            params.CategoryGoodsType = "套餐卡";
            break;

          default:
            break;
        }
      }

      if (Array.from(params.CategoryID).length == 2) {
        params.CategoryID = params.CategoryID[1];
      } else if (Array.from(params.CategoryID).length == 3) {
        params.CategoryID = params.CategoryID[2];
      } else {
        params.CategoryID = "";
      }

      /*  params.StartDate = "2021-09-15"
       params.EndDate = "2021-09-15" */
      params.StartDate = params.QueryDate ? params.QueryDate[0] : "";
      params.EndDate = params.QueryDate ? params.QueryDate[1] : "";

      that.entityIncomeStatementLoading = true;
      let res = await API.list_employeePerformanceCommissionDetailStatement(params);

      if (res.StateCode == 200) {
        that.staffAllTableData = res.Data;
        that.employeelList = that.staffAllTableData.map((val) => {
          return {
            ID: val.EmployeeID,
            Name: val.EmployeeName,
          };
        });
        if (that.staffAllTableData.length > 0) {
          let temp = JSON.parse(JSON.stringify(that.staffAllTableData[0]));
          that.cardDetailList = temp.cardDetail;
          that.employeeCardPerformanceCommissionSumForm = temp.employeeCardPerformanceCommissionSumForm;
          that.employeePerformanceCommissionSumForm = temp.employeePerformanceCommissionSumForm;
          that.employeeSaleProductPerformanceCommissionSumForm = temp.employeeSaleProductPerformanceCommissionSumForm;
          that.employeeTreatProductPerformanceCommissionSumForm = temp.employeeTreatProductPerformanceCommissionSumForm;
          that.employeeProjectPerformanceCommissionSumForm = temp.employeeProjectPerformanceCommissionSumForm;
          that.largessSavingCardTreatForm = temp.largessSavingCardTreatForm;
          that.payMethodForm = temp.payMethodForm;
          that.saleProductDetailList = temp.saleProductDetail;
          that.treatProductDetailList = temp.treatProductDetail;
          that.projectDetailList = temp.projectDetail;
          that.projectTreatForm = temp.projectTreatForm;
          that.generalCardTreatForm = temp.generalCardTreatForm;
          that.timeCardTreatForm = temp.timeCardTreatForm;
          that.packageCardTreatForm = temp.packageCardTreatForm;
          that.savingCardTreatForm = temp.savingCardTreatForm;
          that.EmployeeID = temp.EmployeeID;
          that.EmployeeName = temp.EmployeeName;
          // that.projectFastBillForm = temp.projectFastBillForm;
          // that.serviceProjectForm = temp.serviceProjectForm;
          // that.productSaleForm = temp.productSaleForm;
          // that.cardSaleForm = temp.cardSaleForm;
          // that.projectSaleForm = temp.projectSaleForm;
          // that.savingCardDeductionForm = temp.savingCardDeductionForm;

          that.addRow();
        } else {
          that.tableDataLength = 0;
          that.staffTableData = [];
          that.payMethodForm = [];
          that.cardDetailList = [];
          that.projectDetailList = [];
          that.saleProductDetailList = [];
          that.treatProductDetailList = [];
          that.accountList = [];
          that.subtotalSummary = [];
          that.projectFastBillForm = {};
          that.serviceProjectForm = {};
          that.productSaleForm = {};
          that.cardSaleForm = {};
          that.projectSaleForm = {};
          that.savingCardDeductionForm = {};
          that.projectTreatForm = null;
          that.generalCardTreatForm = null;
          that.timeCardTreatForm = null;
          that.packageCardTreatForm = null;
          that.savingCardTreatForm = null;
          that.largessSavingCardTreatForm = null;
          that.employeeSaleProductPerformanceCommissionSumForm = null;
          that.employeeTreatProductPerformanceCommissionSumForm = null;
          that.employeeProjectPerformanceCommissionSumForm = null;
          that.employeeCardPerformanceCommissionSumForm = null;
          that.employeePerformanceCommissionSumForm = null;

          that.addRow();
        }
      } else {
        this.$message.error({
          message: res.Message,
          duration: 2000,
        });
      }
      that.entityIncomeStatementLoading = false;
    },
    /**  分类  */
    async entitySaleGoodsDetailStatement_category() {
      let that = this;
      let res = await APIClassification.entitySaleGoodsDetailStatement_category();
      if (res.StateCode == 200) {
        that.categoryList = res.Data;
      } else {
        that.$message.error(res.Message);
      }
    },

    addRow() {
      let that = this;
      that.staffTableData = [];
      /* 员工业绩 */
      if (that.employeePerformanceCommissionSumForm) {
        that.employeePerformanceCommissionSumForm.Type = "员工总合计";
        that.employeePerformanceCommissionSumForm.PayMethodName = that.EmployeeName + "/" + that.EmployeeID;
        that.staffTableData.push(that.employeePerformanceCommissionSumForm);
      }
      /* 小计汇总 */
      that.subtotalSummary = [];
      if (that.employeeCardPerformanceCommissionSumForm) {
        let IsAllZero = false;
        IsAllZero = Object.values(that.employeeCardPerformanceCommissionSumForm).some((item) => {
          return item > 0;
        });
        if (IsAllZero) {
          that.employeeCardPerformanceCommissionSumForm.PayMethodName = "卡项销售小计";
          that.subtotalSummary.push(that.employeeCardPerformanceCommissionSumForm);
        }
      }
      if (that.employeeProjectPerformanceCommissionSumForm) {
        let IsAllZero = false;
        IsAllZero = Object.values(that.employeeProjectPerformanceCommissionSumForm).some((item) => {
          return item > 0;
        });
        if (IsAllZero) {
          that.employeeProjectPerformanceCommissionSumForm.PayMethodName = "服务项目小计";
          that.subtotalSummary.push(that.employeeProjectPerformanceCommissionSumForm);
        }
      }
      if (that.employeeSaleProductPerformanceCommissionSumForm) {
        let IsAllZero = false;
        IsAllZero = Object.values(that.employeeProjectPerformanceCommissionSumForm).some((item) => {
          return item > 0;
        });
        if (IsAllZero) {
          that.employeeSaleProductPerformanceCommissionSumForm.PayMethodName = "产品销售小计";
          that.subtotalSummary.push(that.employeeSaleProductPerformanceCommissionSumForm);
        }
      }

      if (that.employeeTreatProductPerformanceCommissionSumForm) {
        let IsAllZero = false;
        IsAllZero = Object.values(that.employeeTreatProductPerformanceCommissionSumForm).some((item) => {
          return item > 0;
        });
        if (IsAllZero) {
          that.employeeTreatProductPerformanceCommissionSumForm.PayMethodName = "产品消耗小计";
          that.subtotalSummary.push(that.employeeTreatProductPerformanceCommissionSumForm);
        }
      }
      /* 支付方式 */
      that.payMethodFormList = [];
      if (that.payMethodForm) {
        that.payMethodFormList = that.payMethodForm;
      }
      /* 账户 */
      that.accountList = [];
      if (that.projectTreatForm) {
        that.projectTreatForm.PayMethodName = "疗程账户";
        that.accountList.push(that.projectTreatForm);
      }
      if (that.generalCardTreatForm) {
        that.generalCardTreatForm.PayMethodName = "通用次卡账户";
        that.accountList.push(that.generalCardTreatForm);
      }
      if (that.timeCardTreatForm) {
        that.timeCardTreatForm.PayMethodName = "时效卡账户";
        that.accountList.push(that.timeCardTreatForm);
      }
      if (that.packageCardTreatForm) {
        that.packageCardTreatForm.PayMethodName = "套餐卡账户";
        that.accountList.push(that.packageCardTreatForm);
      }
      if (that.savingCardTreatForm) {
        that.savingCardTreatForm.PayMethodName = "储值账户";
        that.accountList.push(that.savingCardTreatForm);
      }
      if (that.largessSavingCardTreatForm) {
        that.largessSavingCardTreatForm.PayMethodName = "赠送储值账户";
        that.accountList.push(that.largessSavingCardTreatForm);
      }
    },
    tishi() {
      this.$message({
        message: "请选择员工姓名",
        type: "error",
      });
    },
    /**  获取门店列表   */
    async reportEntity_list() {
      let that = this;
      let params = {};
      let res = await API.reportEntity_list(params);
      if (res.StateCode == 200) {
        that.Entity_List = res.Data;
        if (that.Entity_List.length > 0) {
          that.staffSearchFrom.EntityID = that.Entity_List[0].ID;
          // that.getEntityEmployee_list();
        }
      } else {
        that.$message.error(res.Message);
      }
    },
    /**  获取门店下员工列表  */
    async getEntityEmployee_list() {
      let that = this;
      let params = { EntityID: that.staffSearchFrom.EntityID };
      let res = await API.getEntityEmployee_list(params);
      if (res.StateCode == 200) {
        that.employeelList = res.Data;
      } else {
        that.$message.error(res.Message);
      }
    },
    /* 卡项合计 */
    getcardDetailSum(param) {
      const { columns } = param;
      const sums = [];
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = <span class="font_weight_600">卡项销售合计</span>;
          return;
        }
        var filter_NumFormat = this.$options.filters["NumFormat"];
        if (this.employeeCardPerformanceCommissionSumForm) {
          switch (column.property) {
            case "TotalAmount":
              sums[index] = (
                <span class="font_weight_600">
                  {filter_NumFormat(this.employeeCardPerformanceCommissionSumForm.TotalAmount ? this.employeeCardPerformanceCommissionSumForm.TotalAmount : "")}
                </span>
              );
              break;
            case "OriginAmount":
              sums[index] = (
                <span class="font_weight_600">
                  {filter_NumFormat(
                    this.employeeCardPerformanceCommissionSumForm.OriginAmount ? this.employeeCardPerformanceCommissionSumForm.OriginAmount : ""
                  )}
                </span>
              );
              break;
            case "Performance":
              sums[index] = (
                <span class="font_weight_600">
                  {filter_NumFormat(this.employeeCardPerformanceCommissionSumForm.Performance ? this.employeeCardPerformanceCommissionSumForm.Performance : "")}
                </span>
              );
              break;
            case "Commission":
              sums[index] = (
                <span class="font_weight_600">
                  {filter_NumFormat(this.employeeCardPerformanceCommissionSumForm.Commission ? this.employeeCardPerformanceCommissionSumForm.Commission : "")}
                </span>
              );
              break;
            case "ArrearAmount":
              sums[index] = (
                <span class="font_weight_600">
                  {filter_NumFormat(
                    this.employeeCardPerformanceCommissionSumForm.ArrearAmount ? this.employeeCardPerformanceCommissionSumForm.ArrearAmount : ""
                  )}
                </span>
              );
              break;
          }
        }
      });
      return sums;
    },
    /* 项目合计 */
    getprojectDetailSum(param) {
      const { columns } = param;
      const sums = [];
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = <span class="font_weight_600">服务项目合计</span>;
          return;
        }
        var filter_NumFormat = this.$options.filters["NumFormat"];
        if (this.employeeProjectPerformanceCommissionSumForm) {
          switch (column.property) {
            case "TotalAmount":
              sums[index] = (
                <span class="font_weight_600">
                  {filter_NumFormat(
                    this.employeeProjectPerformanceCommissionSumForm.TotalAmount ? this.employeeProjectPerformanceCommissionSumForm.TotalAmount : ""
                  )}
                </span>
              );
              break;
            case "OriginAmount":
              sums[index] = (
                <span class="font_weight_600">
                  {filter_NumFormat(
                    this.employeeProjectPerformanceCommissionSumForm.OriginAmount ? this.employeeProjectPerformanceCommissionSumForm.OriginAmount : ""
                  )}
                </span>
              );
              break;
            case "Performance":
              sums[index] = (
                <span class="font_weight_600">
                  {filter_NumFormat(
                    this.employeeProjectPerformanceCommissionSumForm.Performance ? this.employeeProjectPerformanceCommissionSumForm.Performance : ""
                  )}
                </span>
              );
              break;
            case "Commission":
              sums[index] = (
                <span class="font_weight_600">
                  {filter_NumFormat(
                    this.employeeProjectPerformanceCommissionSumForm.Commission ? this.employeeProjectPerformanceCommissionSumForm.Commission : ""
                  )}
                </span>
              );
              break;
            case "ArrearAmount":
              sums[index] = (
                <span class="font_weight_600">
                  {filter_NumFormat(
                    this.employeeProjectPerformanceCommissionSumForm.ArrearAmount ? this.employeeProjectPerformanceCommissionSumForm.ArrearAmount : ""
                  )}
                </span>
              );
              break;
          }
        }
      });
      return sums;
    },
    /* 产品销售合计 */
    getSaleProductDetailSum(param) {
      const { columns } = param;
      const sums = [];
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = <span class="font_weight_600">产品销售合计</span>;
          return;
        }
        var filter_NumFormat = this.$options.filters["NumFormat"];
        if (this.employeeSaleProductPerformanceCommissionSumForm) {
          switch (column.property) {
            case "TotalAmount":
              sums[index] = (
                <span class="font_weight_600">
                  {filter_NumFormat(
                    this.employeeSaleProductPerformanceCommissionSumForm.TotalAmount ? this.employeeSaleProductPerformanceCommissionSumForm.TotalAmount : ""
                  )}
                </span>
              );
              break;
            case "OriginAmount":
              sums[index] = (
                <span class="font_weight_600">
                  {filter_NumFormat(
                    this.employeeSaleProductPerformanceCommissionSumForm.OriginAmount ? this.employeeSaleProductPerformanceCommissionSumForm.OriginAmount : ""
                  )}
                </span>
              );
              break;
            case "Performance":
              sums[index] = (
                <span class="font_weight_600">
                  {filter_NumFormat(
                    this.employeeSaleProductPerformanceCommissionSumForm.Performance ? this.employeeSaleProductPerformanceCommissionSumForm.Performance : ""
                  )}
                </span>
              );
              break;
            case "Commission":
              sums[index] = (
                <span class="font_weight_600">
                  {filter_NumFormat(
                    this.employeeSaleProductPerformanceCommissionSumForm.Commission ? this.employeeSaleProductPerformanceCommissionSumForm.Commission : ""
                  )}
                </span>
              );
              break;
            case "ArrearAmount":
              sums[index] = (
                <span class="font_weight_600">
                  {filter_NumFormat(
                    this.employeeSaleProductPerformanceCommissionSumForm.ArrearAmount ? this.employeeSaleProductPerformanceCommissionSumForm.ArrearAmount : ""
                  )}
                </span>
              );
              break;
          }
        }
      });
      return sums;
    },
    /* 产品消耗合计 */
    getTreatProductDetailSum(param) {
      const { columns } = param;
      const sums = [];
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = <span class="font_weight_600">产品消耗合计</span>;
          return;
        }
        var filter_NumFormat = this.$options.filters["NumFormat"];
        if (this.employeeTreatProductPerformanceCommissionSumForm) {
          switch (column.property) {
            case "TotalAmount":
              sums[index] = (
                <span class="font_weight_600">
                  {filter_NumFormat(
                    this.employeeTreatProductPerformanceCommissionSumForm.TotalAmount ? this.employeeTreatProductPerformanceCommissionSumForm.TotalAmount : ""
                  )}
                </span>
              );
              break;
            case "OriginAmount":
              sums[index] = (
                <span class="font_weight_600">
                  {filter_NumFormat(
                    this.employeeTreatProductPerformanceCommissionSumForm.OriginAmount ? this.employeeTreatProductPerformanceCommissionSumForm.OriginAmount : ""
                  )}
                </span>
              );
              break;
            case "Performance":
              sums[index] = (
                <span class="font_weight_600">
                  {filter_NumFormat(
                    this.employeeTreatProductPerformanceCommissionSumForm.Performance ? this.employeeTreatProductPerformanceCommissionSumForm.Performance : ""
                  )}
                </span>
              );
              break;
            case "Commission":
              sums[index] = (
                <span class="font_weight_600">
                  {filter_NumFormat(
                    this.employeeTreatProductPerformanceCommissionSumForm.Commission ? this.employeeTreatProductPerformanceCommissionSumForm.Commission : ""
                  )}
                </span>
              );
              break;
            case "ArrearAmount":
              sums[index] = (
                <span class="font_weight_600">
                  {filter_NumFormat(
                    this.employeeTreatProductPerformanceCommissionSumForm.ArrearAmount ? this.employeeTreatProductPerformanceCommissionSumForm.ArrearAmount : ""
                  )}
                </span>
              );
              break;
          }
        }
      });
      return sums;
    },
    /* 员工业绩合并 */
    arraySpanMethod({ rowIndex, columnIndex }) {
      if (rowIndex % 2 === 0) {
        if (columnIndex === 0) {
          return [1, 2];
        } else if (columnIndex === 1) {
          return [0, 0];
        }
        if (columnIndex === 5) {
          return [1, 3];
        } else if (columnIndex === 6) {
          return [0, 0];
        } else if (columnIndex === 7) {
          return [0, 0];
        }
      }
    },
    arrayPayMethod({ rowIndex, columnIndex }) {
      if (rowIndex == 0) {
        if (columnIndex == 8) {
          return [1, 2];
        } else if (columnIndex === 9) {
          return [0, 0];
        }
      }
    },
    /* 卡项挂账明细合并 */ /**  去除  11 12 的合并  **/
    cardDetailSpanMethod({ row, rowIndex, columnIndex }) {
      let repeatIDList = this.cardDetailList.filter((val) => val.BillID == row.BillID);
      if (repeatIDList.length > 0) {
        let findindex = this.cardDetailList.findIndex((val) => val.BillID == row.BillID);
        if (columnIndex == 11 || columnIndex == 12 || columnIndex == 17) {
          let GoodIDlist = this.cardDetailList.filter((val) => val.BillID == row.BillID && val.GoodID == row.GoodID);
          if (GoodIDlist.length > 1) {
            if (findindex == rowIndex) {
              return {
                rowspan: GoodIDlist.length,
                colspan: 1,
              };
            } else {
              return {
                rowspan: 0,
                colspan: 0,
              };
            }
          }
        }
        if (columnIndex == 18) {
          let detailInfo = this.cardDetailList.filter((val) => val.BillID == row.BillID && val.detailInfo == row.detailInfo);
          if (detailInfo.length > 1) {
            if (findindex == rowIndex) {
              return {
                rowspan: detailInfo.length,
                colspan: 1,
              };
            } else {
              return {
                rowspan: 0,
                colspan: 0,
              };
            }
          }
        }
      }
    },
    /* 项目挂账明细合并 */
    projectDetailSpanMethod({ row, rowIndex, columnIndex }) {
      let repeatIDList = this.projectDetailList.filter((val) => val.BillID == row.BillID);

      if (repeatIDList.length > 1) {
        let findindex = this.projectDetailList.findIndex((val) => val.BillID == row.BillID);
        if (columnIndex == 11 || columnIndex == 12 || columnIndex == 17) {
          let GoodIDlist = this.projectDetailList.filter((val) => val.BillID == row.BillID && val.GoodID == row.GoodID);
          if (GoodIDlist.length > 1) {
            if (findindex == rowIndex) {
              return {
                rowspan: GoodIDlist.length,
                colspan: 1,
              };
            } else {
              return {
                rowspan: 1,
                colspan: 1,
              };
            }
          }
        }
        if (columnIndex == 18) {
          let detailInfo = this.projectDetailList.filter((val) => val.BillID == row.BillID && val.detailInfo == row.detailInfo);
          if (detailInfo.length > 1) {
            if (findindex == rowIndex) {
              return {
                rowspan: detailInfo.length,
                colspan: 1,
              };
            } else {
              return {
                rowspan: 0,
                colspan: 0,
              };
            }
          }
        }
      }
    },
    /* 销售产品挂账明细合并 */
    saleProductDetailSpanMethod({ row, rowIndex, columnIndex }) {
      let repeatIDList = this.saleProductDetailList.filter((val) => val.BillID == row.BillID);
      if (repeatIDList.length > 1) {
        let findindex = this.saleProductDetailList.findIndex((val) => val.BillID == row.BillID);
        if (columnIndex == 11 || columnIndex == 12 || columnIndex == 17) {
          let GoodIDlist = this.saleProductDetailList.filter((val) => val.BillID == row.BillID && val.GoodID == row.GoodID);
          if (GoodIDlist.length > 1) {
            if (findindex == rowIndex) {
              return {
                rowspan: GoodIDlist.length,
                colspan: 1,
              };
            } else {
              return {
                rowspan: 0,
                colspan: 0,
              };
            }
          }
        }
        if (columnIndex == 18) {
          let detailInfo = this.saleProductDetailList.filter((val) => val.BillID == row.BillID && val.detailInfo == row.detailInfo);
          if (detailInfo.length > 1) {
            if (findindex == rowIndex) {
              return {
                rowspan: detailInfo.length,
                colspan: 1,
              };
            } else {
              return {
                rowspan: 0,
                colspan: 0,
              };
            }
          }
        }
      }
    },
    /* 消耗产品挂账明细合并 */
    treatProductDetailSpanMethod({ row, rowIndex, columnIndex }) {
      let repeatIDList = this.treatProductDetailList.filter((val) => val.BillID == row.BillID);
      if (repeatIDList.length > 1) {
        let findindex = this.treatProductDetailList.findIndex((val) => val.BillID == row.BillID);
        if (columnIndex == 11 || columnIndex == 12 || columnIndex == 17) {
          let GoodIDlist = this.treatProductDetailList.filter((val) => val.BillID == row.BillID && val.GoodID == row.GoodID);
          if (GoodIDlist.length > 1) {
            if (findindex == rowIndex) {
              return {
                rowspan: GoodIDlist.length,
                colspan: 1,
              };
            } else {
              return {
                rowspan: 0,
                colspan: 0,
              };
            }
          }
        }
        if (columnIndex == 18) {
          let detailInfo = this.treatProductDetailList.filter((val) => val.BillID == row.BillID && val.detailInfo == row.detailInfo);
          if (detailInfo.length > 1) {
            if (findindex == rowIndex) {
              return {
                rowspan: detailInfo.length,
                colspan: 1,
              };
            } else {
              return {
                rowspan: 0,
                colspan: 0,
              };
            }
          }
        }
      }
    },
    getCategory(row) {
      if (row.ParentCategoryName && row.CategoryName) {
        return row.ParentCategoryName + "/" + row.CategoryName;
      } else if (row.ParentCategoryName) {
        return row.ParentCategoryName;
      } else {
        return row.CategoryName;
      }
    },
  },
  /** 监听数据变化   */
  watch: {},
  /** 创建实例之前执行函数   */
  beforeCreate() {},
  /**  实例创建完成之后  */
  created() {
    this.reportEntity_list();
  },
  /**  在挂载开始之前被调用  */
  beforeMount() {},
  /**  实例被挂载后调用  */
  mounted() {
    let that = this;
    // that.getJobType()
    // that.list_employeePerformanceCommissionDetailStatement()
    that.entitySaleGoodsDetailStatement_category();
    // that.jisuan();
    // that.list_employee()
  },
  /**  数据更新 之前 调用  */
  beforeUpdate() {},
  /** 数据更新 完成 调用   */
  updated() {},
  /**  实例销毁之前调用  */
  beforeDestroy() {},
  /**  实例销毁后调用  */
  destroyed() {},
};
</script>

<style lang="scss">
.CommissionStatistics {
  .custom-tabs {
    .el-tabs__content {
      padding: unset;
    }
  }

  .el-tabs--border-card > .el-tabs__header .el-tabs__item {
    color: black !important;
  }
  .el-tabs--border-card > .el-tabs__header .el-tabs__item.is-active {
    color: #ff8646 !important;
  }

  .el-table--border th {
    border-right: 1px solid black !important;

    border-bottom: 1px solid black !important;
  }
  .el-table--border {
    border: 1px solid black !important;
  }

  .el-table th.is-leaf {
    border-bottom: 1px solid black !important;
  }
  .el-table {
    border-bottom: 1px solid black !important;
    border-left: 1px solid black !important;
    border-right: 1px solid black !important;
  }
  .el-table--scrollable-x .el-table__body-wrapper {
    overflow-x: hidden !important;
  }
  .el-table--border td,
  .el-table--border th {
    border-right: none;
  }
  .el-table {
    .el-table__body tr:hover > td {
      background-color: #f0b89e;
    }
    .el-table__header tr th {
      background-color: #fdeadf !important;
    }
    .el-table__header tr th .cell {
      font-weight: 400;
      font-size: 15px;
      color: black;
    }

    .warning-row {
      background: #fcfcfc;
    }
    .success-row {
      background: #ffffff;
    }

    .staffTableColor {
      background: #b0e2ff;
    }
    .cardColor {
      background: #e2efda;
    }
    .projectColor {
      background: #fff2cc;
    }
    .productColor {
      background: #ddebf7;
    }
    .treatProductColor {
      background: #f7e0d5;
    }
    .cell {
      font-weight: 400;
      font-size: 12.5px;
      color: black;
    }
    th {
      padding: unset;
    }
    td {
      padding: unset;
    }
  }

  .el-scrollbar .el-scrollbar__wrap .el-scrollbar__view {
    display: grid;
    // white-space: nowrap;
    // display: inline-block;
  }

  .table-box > .el-scrollbar > .el-scrollbar__wrap > .el-scrollbar__view > .el-table > .el-table__body-wrapper {
    display: none;
  }
  .table-box > .el-scrollbar > .el-scrollbar__bar > .el-scrollbar__thumb {
    display: none;
  }
  .data-box {
    .el-scrollbar__wrap {
      overflow-x: auto;
    }
  }
}
</style>
