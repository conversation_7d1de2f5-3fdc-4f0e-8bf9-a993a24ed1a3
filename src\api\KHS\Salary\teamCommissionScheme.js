/**
 * Created by preference on 2022/06/09
 *  zmx
 */

import * as API from "@/api/index";
export default {
  /** 提成列表  */
  teamCommissionScheme_all: (params) => {
    return API.POST("api/teamCommissionScheme/all", params);
  },
  /** 提成添加  */
  teamCommissionScheme_create: (params) => {
    return API.POST("api/teamCommissionScheme/create", params);
  },
  /** 提成修改   */
  teamCommissionScheme_update: (params) => {
    return API.POST("api/teamCommissionScheme/update", params);
  },
  /** 团队成员详情   */
  teamCommissionScheme_employee: (params) => {
    return API.POST("api/teamCommissionScheme/employee", params);
  },
  /** 提成员工详情  */
  teamCommissionScheme_comissionEmployee: (params) => {
    return API.POST("api/teamCommissionScheme/comissionEmployee", params);
  },

  //员工列表
  getEntityCommissionSchemeAllEmployee: (params) => {
    return API.POST("api/entityCommissionScheme/allEmployee", params);
  },
  //详情--提成方案
  teamCommissionScheme_commission: (params) => {
    return API.POST("api/teamCommissionScheme/commission", params);
  },
};
