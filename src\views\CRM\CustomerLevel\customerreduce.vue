<template>
  <div class="customerreduce content_body">
    <el-row class="tip font_14 color_333">
      <el-col :span="24" class="dis_flex">
        <div class="rduceTitle padlt_10">配置升降级规则</div>
      </el-col>
    </el-row>
    <el-row>
      <el-radio-group v-model="ReduceRule">
        <el-col :span="24" class="rduceMain">
          <el-radio :label="30">暂不降级</el-radio>
        </el-col>

        <el-col :span="24" class="rduceMain">
          <el-radio :label="10">有效期模式</el-radio>
          <div class="EffectiveMain">
            <div>
              实时升级，会员入会，等级变更的
              <el-input
                v-model="VaildModeDay"
                type="number"
                :min="0"
                class="custom-input-number mar_0_10"
                v-input-fixed="0"
                :disabled="ReduceRule != 10"
                placeholder="请输入内容"
                size="small"
                style="width: 100px"
                @input="onInputVaildModeDay"
              ></el-input>天后，根据客户近
              <el-input
                v-model="VaildModeDay"
                type="number"
                :min="0"
                class="custom-input-number mar_0_10"
                v-input-fixed="0"
                :disabled="true"
                placeholder="请输入内容"
                size="small"
                style="width: 100px"
              ></el-input>天获得的成长值计算
            </div>
            <el-radio-group v-model="ReduceMode" :disabled="ReduceRule != 10">
              <div>
                <el-radio :label="10" class="martp_20">若达不到客户所在等级的成长值要求，会员等级降低一级</el-radio>
              </div>
              <el-radio :label="20" class="martp_20">对客户重新定级</el-radio>
            </el-radio-group>
          </div>
        </el-col>

        <el-col :span="24" class="rduceMain">
          <el-radio :label="20">定期更新模式</el-radio>
          <div class="EffectiveMain">
            <div>
              每
              <el-select
                v-model="RegularModeType"
                :disabled="ReduceRule != 20"
                placeholder="请选择"
                size="small"
                style="width: 120px"
                class="mar_0_10"
                @change="handlerRegularChange"
              >
                <el-option
                  v-for="item in SelectTime"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>的1号升级/降级，根据客户近
              <el-input
                v-model="RegularModeMonth"
                :disabled="ReduceRule != 20"
                type="number"
                :min="0"
                class="custom-input-number mar_0_10"
                v-input-fixed="0"
                placeholder="请输入"
                size="small"
                style="width: 100px"
              ></el-input>个月获得的成长值计算下次更新时间为：
              <span>{{ updateTime }}</span>月1日
            </div>
            <el-radio-group v-model="regularReduceMode" :disabled="ReduceRule != 20">
              <div>
                <el-radio
                  :label="10"
                  class="dis_flex flex_y_center martp_20 marbm_20"
                >若达不到客户所在等级的成长值要求，会员等级降低一级</el-radio>
              </div>
              <el-radio :label="20" class="dis_flex flex_y_center">对客户重新定级</el-radio>
            </el-radio-group>
          </div>
        </el-col>
      </el-radio-group>
      <div class="dis_flex flex_x_center" style="margin-top: 60px">
        <el-button
          :disabled="IsReduce == 30"
          type="primary"
          size="small"
          @click="createCustomerReducel"
        >保 存</el-button>
      </div>
    </el-row>
  </div>
</template>

<script>
import API from "@/api/CRM/CustomerLevel/customerreduce";
var dayjs = require("dayjs");
dayjs().format();
var quarterOfYear = require("dayjs/plugin/quarterOfYear");
dayjs.extend(quarterOfYear);
export default {
  name: "CustomerReduce",

  props: {},
  /** 监听数据变化   */
  watch: {},
  /**  引入的组件  */
  components: {},
  /**  Vue 实例的数据对象**/
  data() {
    return {
      IsReduce: "", // 开关
      ReduceRule: 10, // 有效期-定期更新
      VaildModeDay: 365, // 有效期模式天
      RegularModeType: 10, // 定期更新
      RegularModeMonth: "", // 定期更新成长值
      ReduceMode: 10, // 有效期降级规则
      regularReduceMode: 10, // 定期更新降级规则
      updateTime: "", // 更新时间
      SelectTime: [
        { value: 10, label: "月" },
        { value: 20, label: "季度" },
        { value: 30, label: "半年" },
        { value: 40, label: "年" }
      ]
    };
  },
  /**计算属性  */
  computed: {},
  /**  方法集合  */
  methods: {
    /**    */
    onInputVaildModeDay() {
      if (this.VaildModeDay < 1) {
        this.VaildModeDay = 1;
      }
    },
    handlerRegularChange(val) {
      switch (val) {
        case 10:
          this.updateTime = dayjs()
            .add(1, "month")
            .format("MM");
          break;
        case 20:
          this.updateTime = dayjs()
            .add(1, "quarter")
            .format("MM");
          break;
        case 30:
          if (dayjs().month() + 1 > 6) {
            this.updateTime = "0" + 1;
          } else {
            this.updateTime = "0" + 7;
          }
          break;
        case 40:
          this.updateTime = "0" + 1;
          break;
      }
    },
    /** 保存升降级规则 **/
    async createCustomerReducel() {
      var that = this;
      // that.RegularModeMonth
      if (that.ReduceRule == "20" && !that.RegularModeMonth) {
        this.$message.error("请输入定期更新模式条件");
        return;
      }

      var params = {
        IsReduce: that.ReduceRule != 30,
        ReduceRule: that.ReduceRule != 30 ? that.ReduceRule : "",
        VaildModeDay: that.ReduceRule == "10" ? that.VaildModeDay : 365,
        RegularModeType: that.ReduceRule == "20" ? that.RegularModeType : 10,
        RegularModeMonth: that.ReduceRule == "20" ? that.RegularModeMonth : 1
      };
      if (params.ReduceRule == 10) {
        params.ReduceMode = that.ReduceMode;
      }
      if (params.ReduceRule == 20) {
        params.ReduceMode = that.regularReduceMode;
      }
      let res = await API.createCustomerReducel(params);
      if (res.StateCode == 200) {
        that.$message.success("保存成功！");
        that.getCustomerReducel();
      } else {
        that.$message.error({
          message: res.Message,
          duration: 2000
        });
      }
    },
    /** 获取升降级规则信息 **/
    async getCustomerReducel() {
      let params = {
        Name: "",
        Active: true
      };
      let res = await API.getCustomerReducel(params);
      if (res.StateCode == 200) {
        this.IsReduce = res.Data.IsReduce;
        this.ReduceMode = Number(res.Data.ReduceMode ? res.Data.ReduceMode : 10);
        this.regularReduceMode = this.ReduceMode;
        this.ReduceRule = res.Data.IsReduce ? Number(res.Data.ReduceRule) : 30;
        this.RegularModeMonth = res.Data.RegularModeMonth;
        this.RegularModeType = res.Data.RegularModeType ? Number(res.Data.RegularModeType) : "";
        this.VaildModeDay = res.Data.ReduceRule == "10" ? res.Data.VaildModeDay : 365;
        this.handlerRegularChange(Number(res.Data.RegularModeType));
      } else {
        this.$message.error(res.Message);
      }
    },
    getModeType(Type) {
      if (Type == "月") {
        return "10";
      } else if (Type == "季度") {
        return 20;
      } else if (Type == "半年" || Type == "年") {
        return 30;
      }
    }
  },
  /** 创建实例之前执行函数   */
  beforeCreate() {},
  /**  实例创建完成之后  */
  created() {},
  /**  在挂载开始之前被调用  */
  beforeMount() {},
  /**  实例被挂载后调用  */
  mounted() {
    this.getCustomerReducel();
  },
  /**  数据更新 之前 调用  */
  beforeUpdate() {},
  /** 数据更新 完成 调用   */
  updated() {},
  /**  实例销毁之前调用  */
  beforeDestroy() {},
  /**  实例销毁后调用  */
  destroyed() {}
};
</script>

<style lang="scss">
.customerreduce {
  .rduceMain {
    color: #333333;
    font-size: 14px;
    padding-left: 30px;
    margin-top: 20px;
    box-sizing: border-box;
    .EffectiveMain {
      width: 98%;
      height: 160px;
      background-color: #f5f7fa;
      margin: 10px 0 0 28px;
      padding: 30px;
      border-radius: 5px;
      box-sizing: border-box;
    }
  }
}
</style>
