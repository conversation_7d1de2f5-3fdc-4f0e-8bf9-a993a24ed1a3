<template>
  <div class="appointmentPanel" style="width: 100%; height: 100%">
    <div class="panel-timegrid" id="panel-timegrid">
      <div style="width: 100px; box-sizing: border-box; height: 40px"></div>
      <div v-for="(item, index) in panelTimeAxis" :key="index" class="panel-timegrid-item" :style="itemSizeHeight()">
        {{ item }}
      </div>
    </div>

    <div class="panel-container">
      <div class="moad" style="line-height: 38px">
        <el-button style="margin-left: 13px" size="small" icon="el-icon-arrow-left" circle @click="scrollLeft">
        </el-button>
        <el-button size="small" icon="el-icon-arrow-right" circle @click="scrollRight"></el-button>
      </div>
      <div class="employeegrid-container" id="employeegrid-container">
        <div class="employeegrid">
          <div v-for="item in employeeList" :key="item.EmployeeID" :style="itemSizeWidth()" style="height: 40px"
            class="employeegrid-item">
            <div>
              {{ item.EmployeeName }}
              <span v-if="item.AppointmentScheduleName">({{ item.AppointmentScheduleName }})</span>
              <span v-else-if="item.IsRest">(休息)</span>
            </div>
          </div>
        </div>
      </div>
      <!-- 内容 -->
      <div class="panel-gridcontent" @scroll.passive="changeScroll" id="appointment-panel">
        <div class="panel-grid" :style="{ height: panelTimeAxis.length * itemSize.height + 'px' }">
          <div v-for="(item, index) in employeeList" :key="index" :style="itemSizeStyle()">
            <div class="panel-gridcontent-item" :class="[
              !axis.disabled && axis.showState ? 'add_appointment' : '',
              axis.disabled ? 'panel-disabled' : '',
              item.IsRest ? 'panel-disabled-linear' : '',
              timeIsRest(item, axis) ? 'panel-disabled-linear' : '',
            ]" v-for="(axis, cindex) in item.timeAxis" :key="cindex" :style="itemSizeStyle()"
              @mouseenter="panelContentItemMouseenter(axis, item)" @mouseleave="panelContentItemMouseleave(axis, item)"
              @click="panelContentItemClickAddAppointment(item, axis)">
              {{ axis.showState ? "新增预约" : "" }}
            </div>
          </div>
        </div>

        <div class="panel-modal-container" :style="{ height: panelTimeAxis.length * itemSize.height + 1 + 'px' }">
          <div style="" class="panel-gridcontent-moda">
            <div v-for="(item, index) in employeeList" :key="index" :style="itemSizeWidth()"
              class="panel-gridcontent-axis" style="height: 100%">
              <el-popover v-for="appointment in item.Appointment" :key="'appointment' + appointment.ID"
                placement="right-start" width="280" trigger="hover" @show="loadFollowUpRecords(appointment)">
                <el-row>
                  <el-col :span="18">
                    <div class="text-bold font_16">
                      {{ appointment.CustomerName }}
                      <el-tag class="marlt_5" size="mini" v-if="appointment.LevelName">{{ appointment.LevelName }}</el-tag>
                    </div>
                    <div>
                      {{ appointment.PhoneNumber | hidephone }}
                    </div>
                  </el-col>
                  <el-col v-if="appointment.AppointmentTypeName" :span="6" class="text_right">{{
                      appointment.AppointmentTypeName
                  }}</el-col>
                </el-row>
                <el-row v-if="appointment.Project && appointment.Project.length" class="font_13 martp_5">
                  <el-col :span="24" class="color_999">预约项目：</el-col>
                  <el-col :span="24" class="martp_5 color_666">
                    <el-tag v-for="project in appointment.Project" :key="'project' + project.ProjectID" type="primary"
                      size="mini" class="marrt_5 marbm_5">{{
                          project.ProjectName
                      }}</el-tag>
                  </el-col>
                </el-row>
                <el-row class="font_13 martp_5">
                  <el-col :span="24" class="color_999">预约时间：</el-col>
                  <el-col :span="24" class="color_666">{{ appointment.AppointmentDate | dateFormat("HH:mm") }}-{{
                      appointment.AppointmentDate |
                      dateAdd(appointment.Period, "minute") | dateFormat("HH:mm")
                  }}</el-col>
                </el-row>

                <el-row v-if="appointment.Remark" class="font_13 martp_5">
                  <el-col :span="24" class="color_999">商家备注：</el-col>
                  <el-col :span="24" class="color_666">{{ appointment.Remark }}</el-col>
                </el-row>

                <el-row v-if="appointment.Status != '20'" class="martp_10">
                  <el-col :span="8" class="dis_flex flex_x_end">
                    <el-button size="mini" type="danger" @click="cancelAppointmentClick(appointment)">取消预约</el-button>
                  </el-col>

                  <el-col :span="8" class="dis_flex flex_x_end">
                    <el-button size="mini" type="success" @click="editAppointmentClick(appointment)">修改预约</el-button>
                  </el-col>

                  <el-col :span="8" class="dis_flex flex_x_end">
                    <el-button size="mini" type="primary" @click="confrimAppointmentClick(appointment)">到店确认</el-button>
                  </el-col>
                </el-row>

                <!-- 新增跟进记录区域 -->
                <el-divider style="margin: 10px 0;"></el-divider>
                <div class="follow-up-section">
                  <div class="section-title">
                    <i class="el-icon-chat-line-round"></i>
                    <span>最近跟进</span>
                  </div>

                  <div v-if="appointment.followUpLoading" class="loading-container">
                    <i class="el-icon-loading"></i> 加载中...
                  </div>

                  <div v-else-if="appointment.recentFollowUps && appointment.recentFollowUps.length"
                       class="follow-up-list">
                    <div v-for="record in appointment.recentFollowUps" :key="record.ID"
                         class="follow-up-item">
                      <div class="follow-up-header">
                        <el-tag :type="getFollowUpTagType(record.Type)" size="mini">
                          {{ getFollowUpTypeName(record.Type) }}
                        </el-tag>
                        <span class="follow-up-time">{{ formatFollowUpTime(record.CreatedOn) }}</span>
                      </div>
                      <div class="follow-up-content">{{ record.FollowUpContent }}</div>
                    </div>
                  </div>

                  <div v-else class="no-follow-up">
                    <span class="color_999">暂无跟进记录</span>
                  </div>
                </div>

                <div slot="reference" class="panel-event-container radius5" :style="setStyleInset(appointment)">
                  <div class="dis_flex flex_x_between flex_y_center">
                    <div>
                      <span class="text-bold font_13">{{ appointment.CustomerName }}</span> 
                      <el-tag class="marlt_5" size="mini" v-if="appointment.LevelName">{{ appointment.LevelName }}</el-tag>
                    </div>

                    <div class=" font_13 color_666">{{ appointment.AppointmentTypeName }}</div>
                  </div>
                  
                  <div v-if="appointment.ChannelName" class="font_8 martp_3 color_666">渠道：{{ appointment.ChannelName }}</div>
                  <div v-if="appointment.ChannelConsultantEmployeeNames" class="font_8 martp_3 color_666">
                    市场：{{ appointment.ChannelConsultantEmployeeNames }}
                  </div>
                  <div class="font_8 martp_3 color_666">
                    <span v-for="project in appointment.Project" :key="'project' + project.ProjectID" class="marrt_5">{{
                        project.ProjectName
                    }}</span>
                  </div>
                  <div class="font_8 martp_3 color_666">
                    {{ appointment.AppointmentDate | dateFormat("HH:mm") }}-{{ appointment.AppointmentDate |
                        dateAdd(appointment.Period, "minute") | dateFormat("HH:mm")
                    }}
                  </div>
                  <div v-if="appointment.IntroducerName" class="font_8 martp_3 color_666">
                    客户介绍人：{{ appointment.IntroducerName }}
                  </div>
                  <div class="font_8 martp_3 color_666">
                    {{ appointment.Remark }}
                  </div>
                </div>
              </el-popover>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
const dayjs = require("dayjs");
var isBetween = require("dayjs/plugin/isBetween");
dayjs.extend(isBetween);
import FollowUpAPI from '@/api/iBeauty/Workbench/followUp';
export default {
  name: "appointmentPanel",
  props: {
    itemSize: {
      type: Object,
      default() {
        return {
          width: 150,
          height: 40,
        };
      },
    },
    panelTimeAxis: {
      type: Array,
      default() {
        return [];
      },
    },
    employeeList: {
      type: Array,
      default() {
        return [];
      },
    },
    currentDate: {
      type: String,
      default: dayjs().format("YYYY-MM-DD"),
    },
    AppointmentServicerIsRequired: {
      type: Boolean,
      default: false,
    },
  },
  /** 监听数据变化   */
  watch: {
    employeeList: {
      immediate: true,
      handler(val) {
        if (val && val.length > 0 && val[0].timeAxis != "undefined") {
          let that = this;
          let index = val[0].timeAxis.findIndex((i) => !i.disabled);
          if (dayjs().isSame(dayjs(that.currentDate), "day")) {
            document.querySelector(".panel-gridcontent").scrollTo(0, that.itemSize.height * index);
          } else {
            document.querySelector(".panel-gridcontent").scrollTo(0, 0);
          }
        }
      },
    },
  },
  /**计算属性  */
  computed: {},
  /**  引入的组件  */
  components: {},
  /**  Vue 实例的数据对象**/
  data() {
    return {
      isMouseleave: false,
      empList: [],
      autoRefreshTimer: null, // 自动刷新定时器
    };
  },

  /**  方法集合  */
  methods: {
    /**    */
    timeIsRest(item, axis) {
      let that = this;
      let curDate = that.currentDate + " " + axis.time;
      if (item.StartTime && item.EndTime) {
        let startDeta = item.ScheduleDate + " " + item.StartTime;
        let endDeta = item.ScheduleDate + " " + item.EndTime;
        let isBetween = dayjs(curDate).isBetween(startDeta, endDeta, "minute");
        if (isBetween) {
          return false;
        } else {
          return true;
        }
      }
      return false;
    },
    /**  预约状态标题  */
    appiontmentStatusTitle(item) {
      switch (item.Status) {
        case "10":
          return "未到店";
        case "20":
          return "已到店";
        case "30":
          return "已取消";
      }
    },
    /**   预约状态 */
    appiontmentStatus(item) {
      if (item.Status == "20") {
        return {
          color: "#fff",
          "background-color": "var(--zl-appointment-success-border)",
        };
      } else {
        return {
          color: "#fff",
          "background-color": "var(--zl-color-orange-primary)",
        };
      }
    },
    //
    setStyleInset(item) {
      let top = item.top + "px";
      let left = item.left + "px";
      let height = item.height + "px";
      let width = item.width + "px";
      let color = "var(--zl-color-orange-primary)";
      let borderColor = "var(--zl-color-orange-primary)";
      if (item.Status == "20") {
        color = "var(--zl-appointment-success)";
        borderColor = "var(--zl-appointment-success-border)";
      } else {
        color = "var(--zl-appointment-other)";
        borderColor = "var(--zl-color-orange-primary)";
      }
      return {
        top: top,
        left: left,
        height: height,
        width: width,
        "background-color": color,
        "border-left-color": borderColor,
        "border-left-width": "3px",
        "border-left-style": "solid",
        cursor: "pointer",
      };
    },
    /**    */
    changeScroll(event) {
      var vertical = event.target.scrollTop;
      var left = event.target.scrollLeft;
      document.querySelector("#panel-timegrid").scrollTop = vertical;
      document.querySelector("#employeegrid-container").scrollLeft = left;
    },

    scrollRight() {
      document.querySelector("#employeegrid-container").scrollLeft += this.itemSize.width * 2;
      document.querySelector("#appointment-panel").scrollLeft += this.itemSize.width * 2;
    },
    scrollLeft() {
      document.querySelector("#employeegrid-container").scrollLeft -= this.itemSize.width;
      document.querySelector("#appointment-panel").scrollLeft -= this.itemSize.width;
    },

    /**    */
    itemSizeStyle() {
      // 计算每列的实际宽度
      const actualWidth = this.getActualColumnWidth();
      return {
        height: this.itemSize.height + "px",
        width: actualWidth + "px"
      };
    },

    /**  获取实际的列宽度  */
    getActualColumnWidth() {
      try {
        const panelContainer = document.querySelector('.panel-gridcontent');
        const employeeCount = this.employeeList.length;
        if (panelContainer && employeeCount > 0) {
          const containerWidth = panelContainer.clientWidth;
          if (containerWidth > 0) {
            return Math.floor(containerWidth / employeeCount);
          }
        }
      } catch (error) {
        // 静默处理错误
      }
      // 如果无法获取DOM宽度，使用默认值
      return this.itemSize.width || 150;
    },

    itemSizeHeight() {
      return { height: this.itemSize.height + "px" };
    },
    itemSizeWidth() {
      // 计算自适应宽度
      const actualWidth = this.getActualColumnWidth();
      return { width: actualWidth + "px" };
    },
    /**    */
    panelContentItemMouseenter(cell, emp) {
      let that = this;
      if (cell.disabled || emp.IsRest || that.timeIsRest(emp, cell)) {
        return;
      }
      if (this.isMouseleave) {
        cell.showState = true;
        this.isMouseleave = false;
      }
    },
    /**    */
    panelContentItemMouseleave(cell, emp) {
      let that = this;
      if (cell.disabled || emp.IsRest || that.timeIsRest(emp, cell)) {
        return;
      }
      cell.showState = false;
      this.isMouseleave = true;
    },
    /**  添加预约  */
    panelContentItemClickAddAppointment(column, row) {
      let that = this;
      if (row.disabled) return;
      that.$emit("addAppiontment", { column: column, row: row });
    },
    /**   取消 */
    cancelAppointmentClick(item) {
      let that = this;
      that.$emit("cancel", item);
    },
    /**   编辑 */
    editAppointmentClick(item) {
      let that = this;
      that.$emit("edit", item);
    },
    /**   确定 */
    confrimAppointmentClick(item) {
      let that = this;
      that.$emit("confrim", item);
    },

    // 加载跟进记录
    async loadFollowUpRecords(appointment) {
      // 每次都重新加载最新数据，不使用缓存
      this.$set(appointment, 'followUpLoading', true);

      try {
        // 尝试不同的客户ID字段名
        const customerID = appointment.CustomerID || appointment.customerId || appointment.customer_id;

        if (!customerID) {
          console.error('未找到客户ID字段');
          this.$set(appointment, 'recentFollowUps', []);
          return;
        }

        const params = { CustomerID: customerID };
        const res = await this.$api.followUp.getCustomerFollowUp(params);

        if (res.StateCode === 200) {
          // 扁平化处理时间轴数据，获取最近5条记录
          const flatRecords = this.flattenFollowUpData(res.Data);
          const recentRecords = flatRecords.slice(0, 5);

          this.$set(appointment, 'recentFollowUps', recentRecords);
        } else {
          this.$set(appointment, 'recentFollowUps', []);
        }
      } catch (error) {
        console.error('加载跟进记录失败:', error);
        this.$set(appointment, 'recentFollowUps', []);
      } finally {
        this.$set(appointment, 'followUpLoading', false);
      }
    },

    // 扁平化跟进记录数据
    flattenFollowUpData(timelineData) {
      const records = [];

      if (!timelineData || !Array.isArray(timelineData)) {
        return records;
      }

      timelineData.forEach(yearItem => {
        if (yearItem.Child && Array.isArray(yearItem.Child)) {
          yearItem.Child.forEach(monthItem => {
            // 修正：这里应该是Log而不是Child
            if (monthItem.Log && Array.isArray(monthItem.Log)) {
              monthItem.Log.forEach(record => {
                // 添加安全检查，确保必要字段存在
                if (record && (record.FollowUpID || record.DiansosisID || record.CallbackID || record.YingXioaYunFollowUpID)) {
                  records.push({
                    ID: record.FollowUpID || record.DiansosisID || record.CallbackID || record.YingXioaYunFollowUpID || Date.now(),
                    FollowUpContent: record.Content || '无内容',
                    CreatedOn: record.FollowUpOn || record.PlannedOn || new Date().toISOString(),
                    Type: record.Type || 10,
                    FollowUpMethodName: record.MethodName || '未知',
                    FollowUpStatusName: record.Status || '',
                    CreatedByName: record.EmployeeName || '未知'
                  });
                }
              });
            }
          });
        }
      });

      // 按创建时间倒序排列
      return records.sort((a, b) => new Date(b.CreatedOn) - new Date(a.CreatedOn));
    },

    // 获取跟进类型标签样式
    getFollowUpTagType(type) {
      const typeMap = {
        10: '',        // 跟进 - 默认
        20: 'success', // 接诊 - 绿色
        30: 'danger',  // 回访 - 红色
        40: 'warning'  // 营销云 - 橙色
      };
      return typeMap[type] || '';
    },

    // 获取跟进类型名称
    getFollowUpTypeName(type) {
      const typeMap = {
        10: '跟进',
        20: '接诊',
        30: '回访',
        40: '营销云'
      };
      return typeMap[type] || '跟进';
    },

    // 格式化跟进时间
    formatFollowUpTime(dateTime) {
      if (!dateTime) return '';

      try {
        const date = new Date(dateTime);
        if (isNaN(date.getTime())) return '';

        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        const hours = String(date.getHours()).padStart(2, '0');
        const minutes = String(date.getMinutes()).padStart(2, '0');

        return `${month}-${day} ${hours}:${minutes}`;
      } catch (error) {
        console.error('时间格式化错误:', error);
        return '';
      }
    },

    // 启动自动刷新定时器
    startAutoRefresh() {
      // 清除可能存在的旧定时器
      this.stopAutoRefresh();

      // 设置每分钟刷新一次（60000毫秒 = 1分钟）
      this.autoRefreshTimer = setInterval(() => {
        // 通过事件通知父组件刷新数据
        this.$emit('autoRefresh');
      }, 60000);

      console.log('预约看板自动刷新已启动，每分钟刷新一次');
    },

    // 停止自动刷新定时器
    stopAutoRefresh() {
      if (this.autoRefreshTimer) {
        clearInterval(this.autoRefreshTimer);
        this.autoRefreshTimer = null;
        console.log('预约看板自动刷新已停止');
      }
    },

    // 手动触发刷新
    manualRefresh() {
      this.$emit('autoRefresh');
    }
  },
  /** 创建实例之前执行函数   */
  beforeCreate() { },
  /**  实例创建完成之后  */
  created() {
    // 将API挂载到实例上，方便使用
    this.$api = {
      followUp: FollowUpAPI
    };
  },
  /**  在挂载开始之前被调用  */
  beforeMount() { },
  /**  实例被挂载后调用  */
  mounted() {
    // 启动自动刷新定时器
    this.startAutoRefresh();
  },
  /**  数据更新 之前 调用  */
  beforeUpdate() { },
  /** 数据更新 完成 调用   */
  updated() { },
  /**  实例销毁之前调用  */
  beforeDestroy() {
    // 清理自动刷新定时器
    this.stopAutoRefresh();
  },
  /**  实例销毁后调用  */
  destroyed() { },
};
</script>


<style lang="scss">
.appointmentPanel {
  width: 100%;
  background-color: #fff;
  overflow: auto;
  font-size: 14px;
  display: flex;

  .panel-timegrid {
    box-sizing: border-box;
    border-top: 1px solid #ebedf0;
    border-right: 1px solid #ebedf0;
    background-color: #fff;
    float: left;
    overflow: hidden;

    .panel-timegrid-item {
      box-sizing: border-box;
      border-left: 1px solid #ebedf0;
      border-bottom: 1px solid #ebedf0;
      width: 100px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }

  .panel-container {
    width: 100%;
    flex: 1;
    overflow: hidden;

    .moad {
      position: absolute;
      left: 15px;
      top: 15px;
      background-color: #fff;
      border: 1px solid #ebedf0;
      width: 101px;
      height: 41px;
      box-sizing: border-box;

      button:hover {
        background-color: #fff;
        border-color: #dcdfe6;
        color: #606266;
      }

      button:focus {
        background-color: #fff;
        border-color: #dcdfe6;
        color: #606266;
      }
    }

    .employeegrid-container {
      width: calc(100% - 1px);
      overflow: hidden;

      .employeegrid {
        display: inline-flex;
        border-top: 1px solid #ebedf0;
        width: 100%;

        .employeegrid-item {
          box-sizing: border-box;
          border-right: 1px solid #ebedf0;
          border-bottom: 1px solid #ebedf0;
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
        }
      }
    }

    .panel-gridcontent {
      width: calc(100% - 1px);
      overflow: auto;
      height: calc(100% - 36px);
      position: relative;
      scrollbar-width: none;

      /* Firefox */
      .panel-grid {
        display: inline-flex;
        width: 100%;

        .panel-gridcontent-item {
          box-sizing: border-box;
          border-right: 1px solid #ebedf0;
          border-bottom: 1px solid #ebedf0;
          display: flex;
          justify-content: center;
          align-items: center;
        }

        .panel-disabled {
          background-color: #f5f7fa;
          cursor: not-allowed;
          border-right: 1px solid #c7c7c7 !important;
          border-bottom: 1px solid #c7c7c7 !important;
        }

        .panel-disabled-linear {
          color: #c8c9cc;
          cursor: not-allowed;
          background: repeating-linear-gradient(45deg, #d3d3d3, #d3d3d3 2px, #e5e5e5 0, #e5e5e5 16px);
          background-repeat: repeat-y;
          border: 1px solid #d3d3d3;
        }
      }

      .panel-modal-container {
        position: absolute;
        top: 0;
        left: 0;
        pointer-events: none;

        .panel-gridcontent-moda {
          position: relative;
          width: 100%;
          height: 100%;
          overflow: auto;
          display: flex;
          pointer-events: none;

          .panel-gridcontent-axis {
            pointer-events: none;
            position: relative;
            flex: 1;
            min-width: 0;

            .panel-event-container {
              pointer-events: auto;
              position: absolute;
              padding: 2px 0px 2px 4px;
              box-sizing: border-box;
              overflow: hidden;
            }
          }
        }
      }

      .add_appointment {
        background: #fff7f3;
        cursor: pointer;
        color: var(--zl-color-orange-primary);
        font-size: 12px;
        height: 100%;
      }
    }

    // Chrome和Safari浏览器
    .panel-gridcontent::-webkit-scrollbar {
      display: none;
    }
  }
}

.appiontmentStatus {
  position: relative;
  top: -12px;
  left: -12px;
  height: 20px;
  width: 58px;
  display: flex;
  align-items: center;
  border-top-right-radius: 8px;
  border-bottom-right-radius: 8px;
  padding-left: 5px;
  font-size: 13px;
}

// 跟进记录区域样式
.follow-up-section {
  .section-title {
    display: flex;
    align-items: center;
    font-size: 13px;
    font-weight: 500;
    color: #606266;
    margin-bottom: 8px;

    i {
      margin-right: 4px;
      color: #409EFF;
    }
  }

  .loading-container {
    text-align: center;
    color: #909399;
    font-size: 12px;
    padding: 10px 0;
  }

  .follow-up-list {
    max-height: 250px;
    overflow-y: auto;
    // 确保不影响popover的hover检测
    pointer-events: auto;
  }

  .follow-up-item {
    margin-bottom: 6px;
    padding: 5px 8px;
    background-color: #f8f9fa;
    border-radius: 4px;
    border-left: 3px solid #409EFF;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .follow-up-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 4px;
  }

  .follow-up-time {
    font-size: 11px;
    color: #909399;
  }

  .follow-up-content {
    font-size: 12px;
    color: #606266;
    line-height: 1.4;
    word-wrap: break-word;
    word-break: break-all;
  }

  .no-follow-up {
    text-align: center;
    padding: 15px 0;
    font-size: 12px;
  }
}

// 滚动条样式优化
.follow-up-list::-webkit-scrollbar {
  width: 4px;
}

.follow-up-list::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 2px;
}

.follow-up-list::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 2px;
}

.follow-up-list::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>