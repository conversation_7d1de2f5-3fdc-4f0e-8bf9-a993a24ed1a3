<template>
  <div>
    <!-- 生日 -->
    <el-card class="box-card" shadow="never">
      <div class="dis_flex flex_x_between flex_y_center marbm_20">
        <div>
          <span style="margin-right: 35px">{{title}}</span>
          <span class="color_999">{{subTitle}}</span>
        </div>
        <i class="el-icon-close" @click="handlerClose(Code)"></i>
      </div>
      <div>
        <span>{{contentTitle}}</span>
        <el-select v-model="contentValues_.startMonth"  @change="($event) => handlerChange($event,'startMonth')" placeholder="请选择" style="width: 110px; margin: 0 10px" size="small" clearable>
          <el-option v-for="item in selectMounth" :key="item.value" :label="item.label" :value="item.value"> </el-option>
        </el-select>
        <span>月</span>
        <el-select  v-model="contentValues_.startDay" @change="($event) => handlerChange($event,'startDay')" placeholder="请选择" style="width: 110px; margin: 0 10px" size="small" clearable>
          <el-option v-for="item in selectDay" :key="item.value" :label="item.label" :value="item.value"> </el-option>
        </el-select>
        <span>日</span>
        --
        <el-select v-model="contentValues_.endMonth" @change="($event) => handlerChange($event,'endMonth')" placeholder="请选择" style="width: 110px; margin: 0 10px" size="small" clearable>
          <el-option v-for="item in selectMounth" :key="item.value" :label="item.label" :value="item.value"> </el-option>
        </el-select>
        <span>月</span>
        <el-select v-model="contentValues_.endDay"  @change="($event) => handlerChange($event,'endDay')" placeholder="请选择" style="width: 110px; margin: 0 10px" size="small" clearable>
          <el-option v-for="item in selectDay" :key="item.value" :label="item.label" :value="item.value"> </el-option>
        </el-select>
        <span>日</span>
      </div>
    </el-card>
  </div>
</template>

<script>

export default {
  name: "followSelectDate",
  components: {},
  props: {
     title: {
      type: String,
      default: "",
    },
    subTitle: {
      type: String,
      default: null,
    },
    contentTitle: {
      type: String,
      default: null,
    },
    Code:{
       type: String,
      default: null,
    },
    contentValues: {
      type: Object,
      default: () => {
        return {
          startMonth: "", // 月
          startDay: "", // 日
          endMonth: "", // 月
          endDay:""
        }
      },
    },
  },
  data() {
    return {
      contentValues_:{
        startMonth: "", // 月
        startDay: "", // 日
        endMonth: "", // 月
        endDay:""
      },
      selectMounth: [
        { value: "01", label: "01" },
        { value: "02", label: "02" },
        { value: "03", label: "03" },
        { value: "04", label: "04" },
        { value: "05", label: "05" },
        { value: "06", label: "06" },
        { value: "07", label: "07" },
        { value: "08", label: "08" },
        { value: "09", label: "09" },
        { value: "10", label: "10" },
        { value: "11", label: "11" },
        { value: "12", label: "12" },
      ],
      selectDay: [
        { value: "01", label: "01" },
        { value: "02", label: "02" },
        { value: "03", label: "03" },
        { value: "04", label: "04" },
        { value: "05", label: "05" },
        { value: "06", label: "06" },
        { value: "07", label: "07" },
        { value: "08", label: "08" },
        { value: "09", label: "09" },
        { value: "10", label: "10" },
        { value: "11", label: "11" },
        { value: "12", label: "12" },
        { value: "13", label: "13" },
        { value: "14", label: "14" },
        { value: "15", label: "15" },
        { value: "16", label: "16" },
        { value: "17", label: "17" },
        { value: "18", label: "18" },
        { value: "19", label: "19" },
        { value: "20", label: "20" },
        { value: "21", label: "21" },
        { value: "22", label: "22" },
        { value: "23", label: "23" },
        { value: "24", label: "24" },
        { value: "25", label: "25" },
        { value: "26", label: "26" },
        { value: "27", label: "27" },
        { value: "28", label: "28" },
        { value: "29", label: "29" },
        { value: "30", label: "30" },
        { value: "31", label: "31" },
      ],
    };
  },
  computed: {},
   watch: {
    contentValues:  {
      handler(val) {
        if (val) {
          this.contentValues_ = val;  
        }
      },
      immediate: true,
    },
  },
  created() {},
  mounted() {
    
  },
  methods: {
    handlerClose(Code){
      this.$emit("handlerChildClone",Code)
    },
     // 选择生日
    handlerChange(){
      this.$emit('handlerChildChange',this.contentValues_)
    },    
  },
};
</script>

<style scoped lang="less">
