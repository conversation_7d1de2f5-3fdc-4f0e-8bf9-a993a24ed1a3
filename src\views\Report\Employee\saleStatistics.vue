<template>
  <div class="EmployeeSaleStatistics content_body" v-loading="loading">
    <div class="nav_header">
      <el-form :inline="true" size="small" :model="searchData" @submit.native.prevent>
        <el-form-item label="时间筛选">
          <el-date-picker v-model="searchData.QueryDate" :picker-options="pickerOptions" unlink-panels type="daterange" range-separator="至" value-format="yyyy-MM-dd" start-placeholder="开始日期" end-placeholder="结束日期" @change="handleSalePerformanceCommissionSearch"></el-date-picker>
        </el-form-item>
        <el-form-item label="员工姓名">
          <el-input v-model="searchData.EmployeeName" clearable @keyup.enter.native="handleSalePerformanceCommissionSearch" @clear="handleSalePerformanceCommissionSearch" placeholder="请输入员工姓名"></el-input>
        </el-form-item>
        <el-form-item v-if="storeEntityList.length > 1" label="所属门店">
          <el-select v-model="searchData.EntityID" clearable filterable placeholder="请选择门店" :default-first-option="true" @change="handleSalePerformanceCommissionSearch">
            <el-option v-for="item in storeEntityList" :key="item.ID" :label="item.EntityName" :value="item.ID"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="员工职务">
          <el-select v-model="searchData.JobID" filterable placeholder="选择员工职务" @change="handleSalePerformanceCommissionSearch" clearable>
            <el-option v-for="item in jobTypeList" :key="item.ID" :label="item.JobName" :value="item.ID"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" v-prevent-click @click="handleSalePerformanceCommissionSearch">搜索</el-button>
        </el-form-item>
        <el-form-item>
          <el-button v-if="isExport" type="primary" size="small" :loading="downloadLoading" @click="downloadExcel">导出</el-button>
        </el-form-item>
      </el-form>
    </div>
    <el-table size="small" show-summary :summary-method="gettableDataSummaries" :data="tableData">
      <el-table-column prop="EmployeeName" label="员工姓名" width="80"></el-table-column>
      <el-table-column prop="EmployeeID" label="员工编号" ></el-table-column>
      <el-table-column prop="JobName" label="职务" width="150"></el-table-column>
      <el-table-column prop="EntityName" label="所属组织" width="180"></el-table-column>
      <el-table-column prop="SaleQuantity" label="销售数量" width="90">
        <template slot="header">
          销售数量
          <el-popover placement="top-start" trigger="hover">
            <p>1.订单完成时间在统计时间内，员工销售商品的数量</p>
            <p>2.销售数量 = 商品数量 x 经手人比例</p>
            <el-button type="text" style="color: #dcdfe6; padding: 0" icon="el-icon-info" slot="reference"></el-button>
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column label="销售金额" align="center">
        <template slot="header">
          销售金额
          <el-popover placement="top-start" trigger="hover">
            <p>1.订单完成时间在统计时间内，员工销售商品（含赠送）金额</p>
            <p>2.金额包含：实收金额、卡抵扣金额、赠送卡抵扣金额、赠送金额</p>
            <p>3.金额 = 商品金额 x 经手人比例</p>
            <el-button type="text" style="color: #dcdfe6; padding: 0" icon="el-icon-info" slot="reference"></el-button>
          </el-popover>
        </template>
        <el-table-column prop="SalePayAmount" label="实收金额" align="right">
          <template slot-scope="scope">
            {{ scope.row.SalePayAmount | toFixed | NumFormat }}
          </template>
        </el-table-column>
        <el-table-column prop="SaleSavingCardDeductionAmount" label="卡抵扣金额" align="right">
          <template slot-scope="scope">
            {{ scope.row.SaleSavingCardDeductionAmount | toFixed | NumFormat }}
          </template>
        </el-table-column>
        <el-table-column prop="SaleLargessSavingCardDeductionAmount" label="赠送卡抵扣金额" align="right">
          <template slot-scope="scope">
            {{ scope.row.SaleLargessSavingCardDeductionAmount | toFixed | NumFormat }}
          </template>
        </el-table-column>
        <el-table-column prop="SaleLargessAmount" label="赠送金额" align="right">
          <template slot-scope="scope">
            {{ scope.row.SaleLargessAmount | toFixed | NumFormat }}
          </template>
        </el-table-column>
      </el-table-column>

      <el-table-column prop="ArrearAmount" label="补欠款金额">
        <el-table-column prop="ArrearAmount" label="补欠款金额">
          <template slot="header">
            补欠款金额
            <el-popover placement="top-start" trigger="hover">
              <p>1.订单完成时间在统计时间内，员工收取欠款金额</p>
              <p>2.金额 = 商品金额 x 经手人比例</p>
              <el-button type="text" style="color: #dcdfe6; padding: 0" icon="el-icon-info" slot="reference"></el-button>
            </el-popover>
          </template>
          <template slot-scope="scope">
            {{ scope.row.ArrearAmount | toFixed | NumFormat }}
          </template>
        </el-table-column>

        <el-table-column prop="ArrearSavingCardDeductionAmount" label="卡抵扣金额">
          <template slot-scope="scope">
            {{ scope.row.ArrearSavingCardDeductionAmount | toFixed | NumFormat }}
          </template>
        </el-table-column>

        <el-table-column prop="ArrearLargessSavingCardDeductionAmount" label="赠送卡抵扣金额">
          <template slot-scope="scope">
            {{ scope.row.ArrearLargessSavingCardDeductionAmount | toFixed | NumFormat }}
          </template>
        </el-table-column>
      </el-table-column>
      <el-table-column label="退款金额" align="center">
        <template slot="header">
          退款金额
          <el-popover placement="top-start" trigger="hover">
            <p>1.订单完成时间在统计时间内，员工退商品（含赠送）金额</p>
            <p>2.金额包含：退款金额、退回卡金额、退回赠送卡金额、退回赠送金额</p>
            <p>3.金额 = 商品金额 x 经手人比例</p>
            <el-button type="text" style="color: #dcdfe6; padding: 0" icon="el-icon-info" slot="reference"></el-button>
          </el-popover>
        </template>
        <el-table-column prop="RefundSalePayAmount" label="退款金额" align="right">
          <template slot-scope="scope">
            {{ scope.row.RefundSalePayAmount | toFixed | NumFormat }}
          </template>
        </el-table-column>
        <el-table-column prop="RefundSaleSavingCardDeductionAmount" label="退回卡金额" align="right">
          <template slot-scope="scope">
            {{ scope.row.RefundSaleSavingCardDeductionAmount | toFixed | NumFormat }}
          </template>
        </el-table-column>
        <el-table-column prop="RefundSaleLargessSavingCardDeductionAmount" label="退回赠送卡金额" align="right">
          <template slot-scope="scope">
            {{ scope.row.RefundSaleLargessSavingCardDeductionAmount | toFixed | NumFormat }}
          </template>
        </el-table-column>
        <el-table-column prop="RefundSaleLargessAmount" label="退回赠送金额" align="right">
          <template slot-scope="scope">
            {{ scope.row.RefundSaleLargessAmount | toFixed | NumFormat }}
          </template>
        </el-table-column>
      </el-table-column>
    </el-table>
    <div class="pad_15 text_right">
      <el-pagination
        background
        v-if="tableDataPaginations.total > 0"
        @current-change="handleSalePerformanceCommissionPageChange"
        :current-page.sync="tableDataPaginations.page"
        :page-size="tableDataPaginations.page_size"
        :layout="tableDataPaginations.layout"
        :total="tableDataPaginations.total"
      ></el-pagination>
    </div>
  </div>
</template>

<script>
import API from "@/api/Report/Employee/saleStatistics.js";
import EntityAPI from "@/api/Report/Common/entity";
import APIJob from "@/api/KHS/Entity/jobtype";
const dayjs = require("dayjs");
const isoWeek = require("dayjs/plugin/isoWeek");
dayjs.extend(isoWeek);
export default {
  name: "EmployeeSaleStatistics",
  beforeRouteEnter(to, from, next) {
    next((vm) => {
      vm.isExport = vm.$permission.permission(to.meta.Permission, "Report-Employee-SaleStatistics-Export");
    });
  },
  props: {},
  /** 监听数据变化   */
  watch: {},
  /**  引入的组件  */
  components: {},
  /**  Vue 实例的数据对象**/
  data() {
    return {
      isExport: false,
      loading: false,
      downloadLoading: false,
      storeEntityList: [], //门店列表
      jobTypeList: [], //职务列表
      searchData: {
        QueryDate: [new Date(), new Date()],
        GoodsTypeName: "",
        EmployeeName: "",
        JobID: "",
        CustomerName: "",
        CategoryID: "",
        GoodsName: "",
      },
      tableData: [],
      tableDataSum: {},
      //需要给分页组件传的信息
      tableDataPaginations: {
        page: 1, // 当前位于哪页
        total: 0, // 总数
        page_size: 10, // 1页显示多少条
        layout: "total, prev, pager, next,jumper", // 翻页属性
      },
      pickerOptions: {
        shortcuts: [
          {
            text: "今天",
            onClick: (picker) => {
              let end = new Date();
              let start = new Date();
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "本周",
            onClick: (picker) => {
              let end = new Date();
              let weekDay = dayjs(end).isoWeekday();
              let start = dayjs(end)
                .subtract(weekDay - 1, "day")
                .toDate();
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "本月",
            onClick: (picker) => {
              let end = new Date();
              let start = dayjs(dayjs(end).format("YYYY-MM") + "01").toDate();
              picker.$emit("pick", [start, end]);
            },
          },
        ],
      },
      categoryList: [],
      cascaderProps: {
        checkStrictly: true,
        label: "Name",
        value: "ID",
        children: "Child",
      },
    };
  },
  /**计算属性  */
  computed: {},
  /**  方法集合  */
  methods: {
    handleSalePerformanceCommissionSearch() {
      var that = this;
      that.tableDataPaginations.page = 1;
      that.salePerformanceCommissionDetail();
    },
    handleSalePerformanceCommissionPageChange(page) {
      this.tableDataPaginations.page = page;
      this.salePerformanceCommissionDetail();
    },
    // 销售搜索
    salePerformanceCommissionDetail() {
      var that = this;
      if (that.searchData.QueryDate != null) {
        if (dayjs(that.searchData.QueryDate[0]).add(366, "day").valueOf() < dayjs(that.searchData.QueryDate[1]).valueOf()) {
          that.$message.error("时间筛选范围不能超366天");
          return;
        }
        var params = {
          EntityID: that.searchData.EntityID,
          StartDate: that.searchData.QueryDate[0],
          EndDate: that.searchData.QueryDate[1],
          EmployeeName: that.searchData.EmployeeName.trim(),
          PageNum: that.tableDataPaginations.page,
          JobID: that.searchData.JobID,
        };
        that.loading = true;
        API.employeeSaleStatement_statistics(params)
          .then((res) => {
            if (res.StateCode == 200) {
              that.tableDataSum = res.Data.employeeSaleSumStatementForm;
              that.tableData = res.Data.employeeSaleStatementForms.List;
              that.tableDataPaginations.total = res.Data.employeeSaleStatementForms.Total;
              that.tableDataPaginations.page_size = res.Data.employeeSaleStatementForms.PageSize;
            } else {
              that.$message.error({
                message: res.Message,
                duration: 2000,
              });
            }
          })
          .finally(function () {
            that.loading = false;
          });
      }
    },

    gettableDataSummaries({ columns }) {
      const sums = [];
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = <span class="font_weight_600">合计</span>;
          return;
        }
        var filter_NumFormat = this.$options.filters["NumFormat"];
        switch (column.property) {
          case "SaleQuantity":
            sums[index] = <span class="font_weight_600">{filter_NumFormat(this.tableDataSum ? this.tableDataSum.SaleQuantity : 0)}</span>;
            break;
          case "SalePayAmount":
            sums[index] = <span class="font_weight_600">{filter_NumFormat(this.tableDataSum ? this.tableDataSum.SalePayAmount : 0)}</span>;
            break;
          case "SaleSavingCardDeductionAmount":
            sums[index] = <span class="font_weight_600">{filter_NumFormat(this.tableDataSum ? this.tableDataSum.SaleSavingCardDeductionAmount : 0)}</span>;
            break;
          case "SaleLargessSavingCardDeductionAmount":
            sums[index] = <span class="font_weight_600">{filter_NumFormat(this.tableDataSum ? this.tableDataSum.SaleLargessSavingCardDeductionAmount : 0)}</span>;
            break;
          case "SaleLargessAmount":
            sums[index] = <span class="font_weight_600">{filter_NumFormat(this.tableDataSum ? this.tableDataSum.SaleLargessAmount : 0)}</span>;
            break;
          case "ArrearAmount":
            sums[index] = <span class="font_weight_600">{filter_NumFormat(this.tableDataSum ? this.tableDataSum.ArrearAmount : 0)}</span>;
            break;
          case "RefundSalePayAmount":
            sums[index] = <span class="font_weight_600">{filter_NumFormat(this.tableDataSum ? this.tableDataSum.RefundSalePayAmount : 0)}</span>;
            break;
          case "SaleSpecialBenefitCommission":
            sums[index] = <span class="font_weight_600">{filter_NumFormat(this.tableDataSum ? this.tableDataSum.SaleSpecialBenefitCommission : 0)}</span>;
            break;
          case "RefundSaleSavingCardDeductionAmount":
            sums[index] = <span class="font_weight_600">{filter_NumFormat(this.tableDataSum ? this.tableDataSum.RefundSaleSavingCardDeductionAmount : 0)}</span>;
            break;
          case "RefundSaleLargessSavingCardDeductionAmount":
            sums[index] = <span class="font_weight_600">{filter_NumFormat(this.tableDataSum ? this.tableDataSum.RefundSaleLargessSavingCardDeductionAmount : 0)}</span>;
            break;
          case "RefundSaleLargessAmount":
            sums[index] = <span class="font_weight_600">{filter_NumFormat(this.tableDataSum ? this.tableDataSum.RefundSaleLargessAmount : 0)}</span>;
            break;
            case "ArrearSavingCardDeductionAmount":
            sums[index] = <span class="font_weight_600">{filter_NumFormat(this.tableDataSum ? this.tableDataSum.ArrearSavingCardDeductionAmount : 0)}</span>;
            break;
            case "ArrearLargessSavingCardDeductionAmount":
            sums[index] = <span class="font_weight_600">{filter_NumFormat(this.tableDataSum ? this.tableDataSum.ArrearLargessSavingCardDeductionAmount : 0)}</span>;
            break;



        }
      });
      return sums;
    },

    /** 数据导出 */
    downloadExcel() {
      var that = this;
      if (that.searchData.QueryDate != null) {
        if (dayjs(that.searchData.QueryDate[0]).add(366, "day").valueOf() < dayjs(that.searchData.QueryDate[1]).valueOf()) {
          that.$message.error("时间筛选范围不能超366天");
          return;
        }

        let params = {
          EntityID: that.searchData.EntityID,
          StartDate: that.searchData.QueryDate[0],
          EndDate: that.searchData.QueryDate[1],
          EmployeeName: that.searchData.EmployeeName.trim(),
          PageNum: that.tableDataPaginations.page,
          JobID: that.searchData.JobID,
        };
        that.downloadLoading = true;
        API.employeeSaleStatement_statisticsExcel(params)
          .then((res) => {
            this.$message.success({
              message: "正在导出",
              duration: "4000",
            });
            const link = document.createElement("a");
            let blob = new Blob([res], { type: "application/octet-stream" });
            link.style.display = "none";
            link.href = URL.createObjectURL(blob);
            link.download = "员工销售统计.xlsx"; //下载的文件名
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
          })
          .finally(function () {
            that.downloadLoading = false;
          });
      }
    },
    // 职务ID
    async getJobID() {
      var that = this;
      var params = {
        JobTypeName: "",
      };
      let res = await APIJob.getJobJobtypeAll(params);
      if (res.StateCode == 200) {
        that.jobTypeList = res.Data;
      } else {
        that.$message.error({
          message: res.Message,
          duration: 2000,
        });
      }
    },

    //获得当前用户下的权限门店
    getstoreEntityList() {
      var that = this;
      that.loading = true;
      EntityAPI.getStoreEntityList()
        .then((res) => {
          if (res.StateCode == 200) {
            that.storeEntityList = res.Data;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },
  },
  /** 创建实例之前执行函数   */
  beforeCreate() {},
  /**  实例创建完成之后  */
  created() {},
  /**  在挂载开始之前被调用  */
  beforeMount() {},
  /**  实例被挂载后调用  */
  mounted() {
    this.isExport = this.$permission.permission(this.$route.meta.Permission, "Report-Employee-SaleStatistics-Export");
    this.getJobID();
    this.getstoreEntityList();
    this.handleSalePerformanceCommissionSearch();
  },
  /**  数据更新 之前 调用  */
  beforeUpdate() {},
  /** 数据更新 完成 调用   */
  updated() {},
  /**  实例销毁之前调用  */
  beforeDestroy() {},
  /**  实例销毁后调用  */
  destroyed() {},
};
</script>

<style lang="scss">
.EmployeeSaleStatistics {
}
</style>
