<template>
  <div class="followUpSetting content_body" :loading="loading">
    <!-- 搜索 -->
    <div class="nav_header">
      <el-row>
        <el-col :span="20">
          <el-form :inline="true" size="small" :model="foollowModel" @submit.native.prevent>
            <el-form-item label="规则名称">
              <el-input v-model="foollowModel.ruleName" placeholder="请输入规则名称搜索" clearable @clear="handlerChange" @keyup.enter.native="handlerChange"></el-input>
            </el-form-item>
            <el-form-item label="有效性">
              <el-select v-model="foollowModel.Active" placeholder="请选择有效性" @change="handlerChange" clearable @clear="handlerChange">
                <el-option label="有效" :value="true"></el-option>
                <el-option label="无效" :value="false"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handlerChange" size="small" v-prevent-click>搜索</el-button>
            </el-form-item>
          </el-form>
        </el-col>
        <el-col :span="4" class="text_right">
          <el-button size="small" type="primary" v-prevent-click @click="foollowAddClick">新增</el-button>
        </el-col>
      </el-row>
    </div>

    <!-- 列表 -->
    <el-table :data="foollowData" size="small">
      <el-table-column prop="Name" label="跟进规则名称"></el-table-column>
      <el-table-column prop="MethodName" label="跟进方式"></el-table-column>
      <el-table-column prop="ServicerName" label="服务人员"></el-table-column>
      <el-table-column prop="EmployeeName" label="员工名称"></el-table-column>
      <el-table-column prop="Active" label="有效性">
        <template slot-scope="scope">
          {{ scope.row.Active ? "有效" : "无效" }}
        </template>
      </el-table-column>
      <el-table-column label="操作" width="80px">
        <template slot-scope="scope">
          <el-button size="small" type="primary" v-prevent-click @click="handlerEditClick(scope.row)">编辑</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <div class="pad_15 text_right">
      <el-pagination background v-if="paginations.total > 0" @current-change="handlePageChange" :current-page.sync="paginations.page" :page-size="paginations.page_size" :layout="paginations.layout" :total="paginations.total"></el-pagination>
    </div>

    <!-- 新增--编辑 -->
    <el-dialog :title="title" :visible.sync="dialogVisible" width="1050px">
      <el-tabs v-model="foollowActiveName">
        <el-tab-pane label="基本信息" name="information">
          <el-form size="small" :model="foollowModelAdd" ref="ruleForm" :rules="rules" label-width="120px">
            <el-form-item label="规则名称：" prop="ruleNameDialog">
              <el-input v-model="foollowModelAdd.ruleNameDialog" placeholder="请输入"></el-input>
            </el-form-item>

            <el-form-item label="跟进方式：" prop="foollowMode">
              <el-radio-group v-model="foollowModelAdd.foollowMode">
                <el-radio v-for="item in foollowModeType" :key="item.ID" :label="item.ID">{{ item.Name }}</el-radio>
              </el-radio-group>
            </el-form-item>

            <el-form-item label="跟进人员：" prop="foollowPersonnel">
              <template>
                <el-radio-group v-model="foollowModelAdd.foollowPersonnel" @change="groupChange">
                  <el-row class="marbm_10">
                    <el-col :span="24">
                      <el-radio :label="10"
                        >服务人员
                        <el-select v-model="service.ServicerID" :disabled="serveType !== 10" clearable placeholder="请选择">
                          <el-option v-for="item in foollowModePeople" :key="item.ID" :label="item.Name" :value="item.ID"></el-option>
                        </el-select>
                        <span class="marlt_10">当未分配服务人员时，默认</span>
                        <el-select :popper-append-to-body="false" v-model="service.EmployeeID" :disabled="serveType !== 10" filterable remote :remote-method="searchEmpRemote" placeholder="请输入员工姓名、编号查找" clearable @focus="getFocus">
                          <el-option v-for="item in allEmployee" :key="item.ID" :label="item.Name" :value="item.ID">
                            <div class="dis_flex flex_dir_column pad_5_0">
                              <div style="line-height: 25px">
                                <span style="float: left">{{ item.Name }}</span>
                                <span style="float: right; color: #8492a6; font-size: 13px">{{ item.ID }}</span>
                              </div>
                            </div>
                          </el-option>
                        </el-select>
                      </el-radio>
                    </el-col>
                  </el-row>
                  <el-row>
                    <el-radio :label="20">
                      固定跟进人员
                      <el-select :popper-append-to-body="false" v-model="service.CustomerCreationNameID" filterable remote :disabled="serveType !== 20" :remote-method="searchEmpRemote" clearable placeholder="请输入员工姓名、编号查找" @focus="getFocus"
                        ><el-option v-for="item in allEmployee" :key="item.ID" :label="item.Name" :value="item.ID">
                          <div class="dis_flex flex_dir_column pad_5_0">
                            <div style="line-height: 25px">
                              <span style="float: left">{{ item.Name }}</span>
                              <span style="float: right; color: #8492a6; font-size: 13px">{{ item.ID }}</span>
                            </div>
                          </div>
                        </el-option>
                      </el-select>
                    </el-radio>
                  </el-row>
                </el-radio-group>
              </template>
            </el-form-item>

            <el-form-item label="执行计划：" prop="foollowPerPlan">
              <template>
                <el-radio-group v-model="foollowModelAdd.foollowPerPlan" @change="groupChangeTime">
                  <el-row class="marbm_10">
                    <el-col :span="24">
                      <el-radio :label="10"
                        >执行一次，执行日期
                        <el-date-picker v-model="QueryDataPlan" :picker-options="pickerOptions" type="date" :disabled="ScheduleType !== 10" value-format="yyyy-MM-dd" placeholder="选择日期"> </el-date-picker>
                      </el-radio>
                    </el-col>
                  </el-row>
                  <el-row>
                    <el-radio :label="20">
                      重复执行，执行间隔
                      <el-input class="custom-input" :disabled="ScheduleType !== 20" v-model="ScheduleInterval" style="width: 215px" placeholder="请输入" type="number"></el-input>
                      天/次数，执行周期
                      <el-date-picker v-model="QueryDataPlanS" type="daterange" :disabled="ScheduleType !== 20" value-format="yyyy-MM-dd" :picker-options="pickerOptions" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"> </el-date-picker>
                    </el-radio>
                  </el-row>
                </el-radio-group>
              </template>
            </el-form-item>

            <el-form-item v-if="title == '编辑跟进规则'" label="是否有效" prop="Active">
              <el-radio-group v-model="foollowModelAdd.Active">
                <el-radio :label="true">是</el-radio>
                <el-radio :label="false">否</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="跟进内容：">
              <el-input type="textarea" placeholder="请输入内容" v-model="foollowModelAdd.followContent" :rows="5" resize="none"> </el-input>
            </el-form-item>
          </el-form>
        </el-tab-pane>
        <el-tab-pane label="触发条件" name="trigger" class="trigger">
          <el-row>
            <el-col :span="6">
              <el-scrollbar style="height: 600px">
                <div class="triggerLeft">
                  <div class="propertiesClass" v-for="(item, index) in propertiesData" :key="index">
                    <div class="propertiesTitle">
                      {{ getType(item.Type) }}
                    </div>
                    <div class="propertiesContent">
                      <div v-for="(itemChild, indexChild) in item.Detail" :key="indexChild" :class="['properties', itemChild.checked ? 'propertiesActive' : '']" @click="propertiesClick(itemChild, index, indexChild)">
                        {{ itemChild.Name }}
                      </div>
                    </div>
                  </div>
                </div>
              </el-scrollbar>
            </el-col>
            <el-col :span="18">
              <el-scrollbar ref="conditionsScrollbar" style="height: 600px">
                <div class="triggerRight">
                  <div class="triggerRightHeader">
                    <div>触发条件需要同时满足以下条件</div>
                    <div>
                      已选择<span>{{ selectConditions.length ? selectConditions.length : "0" }}</span
                      >个条件
                    </div>
                  </div>
                  <div v-for="(item, index) in selectConditions" :key="index">
                    <component
                      class="martp_10"
                      :is="followComponents[item.componentName]"
                      :title="item.title"
                      :Code="item.Code"
                      :subTitle="item.subTitle"
                      :tailTitle="item.tailTitle"
                      :contentTitle="item.contentTitle"
                      :contentValues="item.contentValues"
                      :metadata="item.metadata"
                      @handlerChildClone="handlerRemoveConditions(item, index)"
                      @handlerChildChange="($event) => handlerChildChange($event, index)"
                      @changeStartValues="($event) => changeStartValues($event, index)"
                      @changeEndValues="($event) => changeEndValues($event, index)"
                    >
                    </component>
                  </div>
                </div>
              </el-scrollbar>
            </el-col>
          </el-row>
        </el-tab-pane>
        <el-tab-pane label="适用组织" name="organization">
          <el-scrollbar class="el-scrollbar_height">
            <el-tree ref="tree" :expand-on-click-node="false" :check-on-click-node="true" :check-strictly="true" :data="entityList" show-checkbox node-key="ID" :default-checked-keys="defaultCheckedKeys" :default-expanded-keys="defaultExpandedKeys" :props="defaultProps"></el-tree>
          </el-scrollbar>
        </el-tab-pane>
      </el-tabs>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false" size="small">取 消</el-button>
        <el-button type="primary" @click="configFollowAdd" size="small">保 存</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import API from "@/api/KHS/Setting/followUpSetting.js";
import APIServicer from "@/api/CRM/Servicer/servicerConfig";
import APIFollowUp from "@/api/iBeauty/Workbench/followUp";
import APIEntity from "@/api/KHS/Entity/entity";
import APIFOLLOW from "@/api/KHS/Setting/followUpConfig.js";

import API_GRADE from "@/api/CRM/Customer/customerLevel";
import API_SOURCE from "@/api/CRM/Customer/customerSource";

import followComponents from "./components/followUpComponents";

export default {
  name: "FollowUpSetting",
  props: {},
  /** 监听数据变化   */
  watch: {},
  /**  引入的组件  */
  components: {
    ...followComponents,
  },
  /**  Vue 实例的数据对象**/
  data() {
    return {
      loading: false,
      followComponents: followComponents,
      levelList: [],
      sourceList: [],
      // 基本信息
      title: "",
      serveType: 10,
      ScheduleType: 10,
      dialogVisible: false, // 新增---编辑
      foollowActiveName: "information",
      QueryDataPlan: "", // 执行一次日期
      ScheduleInterval: "", // 重复执行的间隔
      QueryDataPlanS: [], // 重复执行的时间
      foollowModeType: [], // 跟进方式
      foollowModePeople: [], // 跟进人
      allEmployee: [], // 固定人员
      foollowModel: {
        ruleName: "", // 规则名称
        Active: true, // 有效性
      },
      foollowData: [], // 列表
      paginations: {
        page: 1, // 当前位于哪页
        total: 0, // 总数
        page_size: 10, // 1页显示多少条
        layout: "total, prev, pager, next,jumper", // 翻页属性
      },
      foollowModelAdd: {
        ruleNameDialog: "", // 规则名称
        foollowMode: "", // 跟进方式
        foollowPersonnel: 10, // 跟进人员
        foollowPerPlan: 10, // 执行计划
        followContent: "", // 跟进内容
        Active: true,
      },
      service: {
        ServicerID: "", // 服务人员
        EmployeeID: "", // 未分配服务人员
        CustomerCreationNameID: "", // 固定回访人
      },
      rules: {
        ruleNameDialog: [{ required: true, message: "请输入规则名称", trigger: "blur" }],
        foollowMode: [{ required: true, message: "请选择跟进方式", trigger: "blur" }],
        foollowPersonnel: [{ required: true, message: "请选择跟进人员", trigger: "blur" }],
        foollowPerPlan: [{ required: true, message: "请选择执行计划", trigger: "blur" }],
      },
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() < Date.now() - 24 * 60 * 60 * 1000;
        },
      },

      // 触发条件

      propertiesData: [],
      // 使用组织
      entityList: [], // 适用组织
      defaultCheckedKeys: [], // 默认选中
      defaultExpandedKeys: [1], // 默认展开
      defaultProps: {
        children: "Child",
        label: "EntityName",
      },

      selectConditions: [] /**  选择的条件  */,
    };
  },
  /**计算属性  */
  computed: {},
  /**  方法集合  */
  methods: {
    getType(Type) {
      if (Type == 100) {
        return "基本属性";
      } else if (Type == 200) {
        return "客户属性";
      } else if (Type == 300) {
        return "销售相关";
      } else if (Type == 400) {
        return "消耗相关";
      } else if (Type == 500) {
        return "资产相关";
      }
    },
    // 触发搜索
    handlerChange() {
      this.paginations.page = 1;
      this.search();
    },
    // 分页
    handlePageChange(page) {
      this.paginations.page = page;
      this.search();
    },
    // 新增规则
    foollowAddClick() {
      var that = this;
      that.PageNum = 1;
      that.Total = 0;
      that.tags = [];
      that.selectConditions = [];
      (that.foollowModelAdd = {
        ruleNameDialog: "", // 规则名称
        foollowMode: "", // 跟进方式
        foollowPersonnel: 10, // 跟进人员
        foollowPerPlan: 10, // 执行计划
        followContent: "", // 跟进内容
        Active: true,
      }),
        (that.service = {
          ServicerID: "", // 服务人员
          EmployeeID: "", // 未分配服务人员
          CustomerCreationNameID: "", // 固定回访人
        }),
        that.propertiesData.forEach((item) => {
          item.Detail.forEach((j) => {
            j.checked = false;
          });
        });
      that.title = "新增跟进规则";
      that.dialogVisible = true;
      that.$nextTick(() => {
        that.$refs.tree.setCheckedKeys([]);
      });
    },
    // 编辑规则
    handlerEditClick(row) {
      this.row = row;
      this.propertiesData.forEach((item) => {
        item.Detail.forEach((j) => {
          j.checked = false;
        });
      });
      this.selectConditions = [];
      this.title = "编辑跟进规则";
      this.propertiesData.forEach((condition) => {
        condition.Detail.forEach((conditionC) => {
          conditionC.checked = false;
        });
      });
      this.getFoollowDetail(row);
    },
    // radio
    groupChange(value) {
      if (value == 10) {
        this.serveType = 10;
        this.service.CustomerCreationNameID = "";
      } else {
        this.serveType = 20;
        this.service.ServicerID = "";
        this.service.EmployeeID = "";
      }
    },
    groupChangeTime(value) {
      if (value == 10) {
        this.ScheduleType = 10;
        this.ScheduleInterval = "";
        this.QueryDataPlanS = [];
      } else {
        this.ScheduleType = 20;
        this.QueryDataPlan = "";
      }
    },
    // 点击基本信息
    propertiesClick(item) {
      item.checked = !item.checked;
      if (item.checked) {
        if (item.Code == "Customer_Level") {
          // item.metadata = this.levelList;
          this.$set(item, "metadata", this.levelList);
        }
        if (item.Code == "Customer_Source") {
          // item.metadata = this.sourceList
          this.$set(item, "metadata", this.sourceList);
        }
        this.selectConditions.push(JSON.parse(JSON.stringify(item)));
      } else {
        this.selectConditions.filter(function (value, index, arr) {
          if (value.Code == item.Code) {
            arr.splice(index, 1);
          }
        });
      }
      this.$refs["conditionsScrollbar"].wrap.scrollTop = this.$refs["conditionsScrollbar"].wrap.scrollHeight;
    },

    getTriggetItem(i) {
      if (i.Code == "Age") {
        i.title = "年龄";
        i.subTitle = "客户的年龄在指定范围的人群(只能获取有录入生日的人群数据)";
        i.contentTitle = "选择年龄";
        i.componentName = "followSection";
        i.contentValues = i.contentValues ? i.contentValues : { startValue: "", endValue: "" };
      }
      if (i.Code == "Gender") {
        i.title = "性别";
        i.subTitle = "";
        i.contentTitle = "选择性别";
        i.componentName = "followRadio";
        i.contentValues = i.contentValues ? i.contentValues : null;
      }
      if (i.Code == "Birthday") {
        i.title = "生日";
        i.subTitle = "客户的生日在指定公历日期范围的人群(只能获取有录入生日的人群数据)";
        i.contentTitle = "选择生日";
        i.componentName = "followSelectDate";
        if (i.contentValues) {
          let starts = i.contentValues.startValue ? i.contentValues.startValue.split("-") : ["", ""];
          let ends = i.contentValues.endValue ? i.contentValues.endValue.split("-") : ["", ""];
          i.contentValues = {
            startMonth: starts[0], // 月
            startDay: starts[1], // 日
            endMonth: ends[0], // 月
            endDay: ends[1],
          };
        } else {
          i.contentValues = {
            startMonth: "", // 月
            startDay: "", // 日
            endMonth: "", // 月
            endDay: "",
          };
        }
      }
      if (i.Code == "RecentBirthday") {
        i.title = "最近生日";
        i.subTitle = "客户生日前多少天(只能获取有录入生日的人群数据)";
        i.contentTitle = "生日前";
        i.tailTitle = "天";
        i.componentName = "followInput";
        i.contentValues = i.contentValues ? i.contentValues : null;
      }
      if (i.Code == "Customer_Level") {
        i.title = "客户等级";
        i.subTitle = "客户所属会员等级的人群";
        i.contentTitle = "选择等级";
        i.componentName = "followCheckboxs";
        i.contentValues = i.contentValues ? i.contentValues : [];
        i.options = { ID: "ID", Name: "Name" };
        i.metadata = this.levelList;
      }
      if (i.Code == "CreatedOn_Day") {
        i.title = "成为客户天数";
        i.subTitle = "成为客户的日期在指定范围的人群";
        i.contentTitle = "输入时间";
        i.componentName = "followSection";
        i.contentValues = i.contentValues ? i.contentValues : { startValue: "", endValue: "" };
      }
      if (i.Code == "CreatedOn_Date") {
        i.title = "成为客户日期";
        i.subTitle = "成为客户的日期在指定范围的人群";
        i.contentTitle = "选择时间";
        i.componentName = "followDateTime";
        i.contentValues = i.contentValues ? [i.contentValues.startValue, i.contentValues.endValue] : [];
      }
      if (i.Code == "Customer_Source") {
        i.title = "客户来源";
        i.subTitle = "客户的来源在指定项的人群";
        i.contentTitle = "选择来源";
        i.componentName = "followCheckboxs";
        i.contentValues = i.contentValues ? i.contentValues : [];
        i.options = { ID: "ID", Name: "Name" };
        i.metadata = this.sourceList;
      }
      if (i.Code == "Customer_Tag") {
        i.title = "标签";
        i.subTitle = "客户满足指定项的人群";
        i.contentTitle = "选择标签";
        i.componentName = "followSelectTag";
      }
      if (i.Code == "Sale_Amount") {
        i.title = "消费金额";
        i.subTitle = "在店铺内成功销售总金额达到指定范围的客户";
        i.contentTitle = "输入金额";
        i.componentName = "followSection";
        i.contentValues = i.contentValues ? i.contentValues : { startValue: "", endValue: "" };
      }
      if (i.Code == "Sale_Count") {
        i.title = "消费次数";
        i.subTitle = "在店铺内成功销售总次数达到指定范围的客户";
        i.contentTitle = "输入次数";
        i.componentName = "followSection";
        i.contentValues = i.contentValues ? i.contentValues : { startValue: "", endValue: "" };
      }
      if (i.Code == "Sale_Average") {
        i.title = "消费客单价";
        i.subTitle = "在店铺内平均每次销售金额达到指定范围的客户";
        i.contentTitle = "输入金额";
        i.componentName = "followSection";
        i.contentValues = i.contentValues ? i.contentValues : { startValue: "", endValue: "" };
      }
      if (i.Code == "Treat_Amount") {
        i.title = "消耗金额";
        i.subTitle = "在店铺内成功消耗总金额达到指定范围的客户";
        i.contentTitle = "输入金额";
        i.componentName = "followSection";
        i.contentValues = i.contentValues ? i.contentValues : { startValue: "", endValue: "" };
      }
      if (i.Code == "Treat_Count") {
        i.title = "消耗次数";
        i.subTitle = "在店铺内成功消耗总次数达到指定范围的客户";
        i.contentTitle = "输入次数";
        i.componentName = "followSection";
        i.contentValues = i.contentValues ? i.contentValues : { startValue: "", endValue: "" };
      }
      if (i.Code == "Treat_Average") {
        i.title = "消耗客单价";
        i.subTitle = "在店铺内平均每次消耗次数达到指定范围的客户";
        i.contentTitle = "输入金额";
        i.componentName = "followSection";
        i.contentValues = i.contentValues ? i.contentValues : { startValue: "", endValue: "" };
      }
      if (i.Code == "Card_Balance_Amount") {
        i.title = "余额";
        i.subTitle = "客户的账户余额在指定范围的客户";
        i.contentTitle = "输入金额";
        i.componentName = "followSection";
        i.contentValues = i.contentValues ? i.contentValues : { startValue: "", endValue: "" };
      }
      if (i.Code == "Card_Expired_Day") {
        i.title = "剩余有效期";
        i.subTitle = "卡项(通用次卡，时效卡，储值卡，套餐卡)剩余有效期在指定范围的客户";
        i.contentTitle = "输入时间";
        i.componentName = "followSection";
        i.contentValues = i.contentValues ? i.contentValues : { startValue: "", endValue: "" };
      }
      if (i.Code == "Card_Balance_Times") {
        i.title = "卡剩余次数";
        i.subTitle = "卡项(项目，通用次卡)剩余次数在指定范围的客户";
        i.contentTitle = "输入次数";
        i.componentName = "followSection";
        i.contentValues = i.contentValues ? i.contentValues : { startValue: "", endValue: "" };
      }
      if (i.Code == "Sale_Recent") {
        i.title = "最近有消费";
        i.subTitle = "从此刻到选定时间内，在店铺有销售的客户";
        i.contentTitle = "输入时间";
        i.componentName = "followInput";
        i.contentValues = i.contentValues ? i.contentValues : null;
      }
      if (i.Code == "Sale_No_Recent") {
        i.title = "最近无消费";
        i.subTitle = "从此刻到选定时间内，在店铺无销售的客户";
        i.contentTitle = "输入时间";
        i.componentName = "followInput";
        i.contentValues = i.contentValues ? i.contentValues : null;
      }
      if (i.Code == "Treat_Recent") {
        i.title = "最近有消耗";
        i.subTitle = "从此刻到选定时间内，在店铺有消耗的客户";
        i.contentTitle = "输入时间";
        i.componentName = "followInput";
        i.contentValues = i.contentValues ? i.contentValues : null;
      }
      if (i.Code == "Treat_No_Recent") {
        i.title = "最近无消耗";
        i.subTitle = "从此刻到选定时间内，在店铺无消耗的客户";
        i.contentTitle = "输入时间";
        i.componentName = "followInput";
        i.contentValues = i.contentValues ? i.contentValues : null;
      }
      if (i.Code == "Card_Expired_Date") {
        i.title = "卡过期时间";
        i.subTitle = "卡项（通用次卡，时效卡，储值卡，套餐卡）过期时间在指定范围的客户";
        i.contentTitle = "选择时间";
        i.componentName = "followDateTime";
        i.contentValues = i.contentValues ? [i.contentValues.startValue, i.contentValues.endValue] : [];
      }
      return i;
    },

    /**  搜索固定人员  */
    searchEmpRemote(query) {
      let that = this;
      that.getallEmployee(query);
    },

    getFocus() {
      let that = this;
      that.allEmployee = [];
    },
    // 新增---编辑 保存
    configFollowAdd() {
      let that = this;
      this.$refs.ruleForm.validate(async (valid) => {
        if (valid) {
          if ((that.foollowModelAdd.foollowPersonnel == 10 && that.service.ServicerID == "") || (that.foollowModelAdd.foollowPersonnel == 20 && that.service.CustomerCreationNameID == "")) {
            that.$message({
              type: "info",
              message: "请选择服务人员!",
            });
            return;
          }
          if (that.foollowModelAdd.foollowPerPlan == 10 && that.QueryDataPlan == "") {
            that.$message({
              type: "info",
              message: "请选择执行日期!",
            });
            return;
          }
          if (that.foollowModelAdd.foollowPerPlan == 20 && that.ScheduleInterval == "") {
            that.$message({
              type: "info",
              message: "请选择输入执行间隔!",
            });
            return;
          }
          if (that.foollowModelAdd.foollowPerPlan == 20 && !that.QueryDataPlanS) {
            that.$message({
              type: "info",
              message: "请选择执行周期!",
            });
            return;
          }
          that.defaultCheckedKeys = that.$refs.tree.getCheckedKeys();

          let birthday = that.selectConditions.filter((i) => {
            return i.Code == "Birthday";
          });
          if (birthday && birthday.length > 0) {
            let temp = birthday[0];
            if (!temp.contentValues.startMonth || !temp.contentValues.startDay || !temp.contentValues.endMonth || !temp.contentValues.endDay) {
              that.$message.error("请补充完成生日内容");
              return;
            }
          }

          let Condition = that.selectConditions.map((i) => {
            if (i.componentName == "followDateTime") {
              i.Value = {
                startValue: i.contentValues[0],
                endValue: i.contentValues[1],
              };

              return {
                Code: i.Code,
                Value: JSON.stringify(i.Value),
              };
            }
            if (i.Code == "Birthday") {
              i.contentValues = {
                startValue: i.contentValues.startMonth + "-" + i.contentValues.startDay,
                endValue: i.contentValues.endMonth + "-" + i.contentValues.endDay,
              };
            }
            if (i.componentName == "followInput") {
              return {
                Code: i.Code,
                Value: i.contentValues,
              };
            } else {
              return {
                Code: i.Code,
                Value: JSON.stringify(i.contentValues),
              };
            }
          });

          if (that.title == "新增跟进规则") {
            let params = {
              Name: that.foollowModelAdd.ruleNameDialog, //规则名称
              FollowUpMethodID: that.foollowModelAdd.foollowMode, //跟进方式
              ServicerID: that.service.ServicerID,
              EmployeeID: that.foollowModelAdd.foollowPersonnel == 10 ? that.service.EmployeeID : that.service.CustomerCreationNameID,
              ScheduleType: that.ScheduleType, //计划类型（10：一次：20：重复执行）
              ScheduleInterval: that.ScheduleInterval, //计划间隔
              ScheduleBeginDate: that.ScheduleType == 10 ? that.QueryDataPlan : that.QueryDataPlanS[0], //执行开始日期
              ScheduleEndDate: that.ScheduleType == 10 ? that.QueryDataPlan : that.QueryDataPlanS[1], //执行结束日期
              FollowUpContent: that.foollowModelAdd.followContent, //跟进内容
              Entity: that.defaultCheckedKeys, //适用门店
              Condition: Condition,
            };
            let res = await API.createFollowUpRule(params);
            if (res.StateCode == 200) {
              that.$message.success("添加成功!");
              that.dialogVisible = false;
              // that.dialogClone();
              that.search();
            } else {
              that.$messge.error({
                message: res.Message,
                duration: 2000,
              });
            }
          } else {
            let params = {
              ID: that.row.ID,
              Name: that.foollowModelAdd.ruleNameDialog, //规则名称
              FollowUpMethodID: that.foollowModelAdd.foollowMode, //跟进方式
              ServicerID: that.service.ServicerID,
              EmployeeID: that.foollowModelAdd.foollowPersonnel == 10 ? that.service.EmployeeID : that.service.CustomerCreationNameID,
              ScheduleType: that.ScheduleType, //计划类型（10：一次：20：重复执行）
              ScheduleInterval: that.ScheduleInterval, //计划间隔
              ScheduleBeginDate: that.ScheduleType == 10 ? that.QueryDataPlan : that.QueryDataPlanS[0], //执行开始日期
              ScheduleEndDate: that.ScheduleType == 10 ? that.QueryDataPlan : that.QueryDataPlanS[1], //执行结束日期
              FollowUpContent: that.foollowModelAdd.followContent, //跟进内容
              Entity: that.defaultCheckedKeys, //适用门店
              Condition: Condition,
              Active: that.foollowModelAdd.Active,
            };
            let res = await API.uploadFollowUpRule(params);
            if (res.StateCode == 200) {
              that.$message.success("编辑成功!");
              that.dialogVisible = false;
              // that.dialogClone();
              that.search();
            } else {
              that.$messge.error({
                message: res.Message,
                duration: 2000,
              });
            }
          }
        }
      });
    },

    // 清空
    dialogClone() {
      this.service.ServicerID = "";
      this.service.EmployeeID = "";
      this.service.CustomerCreationNameID = "";
      this.QueryDataPlan = "";
      this.ScheduleInterval = "";
      this.QueryDataPlanS = [];
      this.foollowModelAdd.ruleNameDialog = "";
      this.foollowModelAdd.foollowMode = "";
      this.foollowModelAdd.foollowPersonnel = 10;
      this.foollowModelAdd.foollowPerPlan = 10;
      this.foollowModelAdd.followContent = "";
      this.foollowActiveName = "information";
      this.dialogVisible = false;
      this.selectConditions = [];
    },

    /**   修改开始值 */
    changeStartValues(value, index) {
      this.selectConditions[index].contentValues.startValue = value;
    },

    /**  修改结束值  */
    changeEndValues(value, index) {
      this.selectConditions[index].contentValues.endValue = value;
    },
    /**  修改触发条件内容  */
    handlerChildChange(event, index) {
      let item = this.selectConditions[index];
      item.contentValues = event;
    },
    /**  删除触发条件  */
    handlerRemoveConditions(item, index) {
      this.propertiesData.forEach((i) => {
        i.Detail.forEach((j) => {
          if (item.Code == j.Code) {
            j.checked = false;
          }
        });
      });
      this.selectConditions.splice(index, 1);
    },

    /**********  请求  ****************/
    /** 搜索列表   */
    async search() {
      var that = this;
      var params = {
        PageNum: that.paginations.page,
        Name: that.foollowModel.ruleName,
        Active: that.foollowModel.Active,
      };
      that.loading = true;
      const res = await API.getFollowUpRuleList(params);
      if (res.StateCode == 200) {
        that.foollowData = res.List;
        that.paginations.total = res.Total;
        that.paginations.page_size = res.PageSize;
      } else {
        that.$message.error({
          message: res.Message,
          duration: 2000,
        });
      }
      that.loading = false;
    },

    /**  获取跟进详情  */
    async getFoollowDetail(row) {
      var that = this;
      var params = {
        ID: row.ID,
      };
      let res = await API.getFollowUpRuleDetail(params);
      if (res.StateCode == 200) {
        that.QueryDataPlanS = [];
        that.selectConditions = res.Data.Condition.map((item) => {
          that.propertiesData.forEach((condition) => {
            condition.Detail.forEach((conditionC) => {
              if (item.Code == conditionC.Code) {
                conditionC.checked = true;
              }
            });
          });
          item.Value = JSON.parse(item.Value);
          item.contentValues = item.Value;
          that.getTriggetItem(item);
          return item;
        });
        that.foollowModelAdd.ruleNameDialog = res.Data.Name;
        that.foollowModelAdd.foollowMode = res.Data.FollowUpMethodID;
        that.foollowModelAdd.foollowPerPlan = Number(res.Data.ScheduleType);
        that.ScheduleType = Number(res.Data.ScheduleType);
        if (res.Data.ScheduleType == 10) {
          that.QueryDataPlan = res.Data.ScheduleBeginDate;
        } else {
          that.QueryDataPlanS[0] = res.Data.ScheduleBeginDate;
          that.QueryDataPlanS[1] = res.Data.ScheduleEndDate;
        }
        that.foollowModelAdd.Active = res.Data.Active;
        that.foollowModelAdd.followContent = res.Data.FollowUpContent;
        that.foollowModelAdd.foollowPersonnel = res.Data.ServicerID ? 10 : 20;
        that.service.ServicerID = res.Data.ServicerID;
        that.service.EmployeeID = that.foollowModelAdd.foollowPersonnel == 10 ? res.Data.EmployeeID : "";
        that.service.CustomerCreationNameID = that.foollowModelAdd.foollowPersonnel == 20 ? res.Data.EmployeeID : "";
        that.ScheduleInterval = res.Data.ScheduleInterval;
        that.defaultCheckedKeys = res.Data.Entity;
        if (that.service.EmployeeID || that.service.CustomerCreationNameID) {
          let EmployeeID = that.foollowModelAdd.foollowPersonnel == 10 ? that.service.EmployeeID : that.service.CustomerCreationNameID;
          that.getallEmployee(EmployeeID);
        }
        this.dialogVisible = true;
      } else {
        that.$message.error(res.Message);
      }
    },
    /** 获取门店   */
    getEntityData() {
      let that = this;
      var params = {
        SearchKey: "",
      };
      APIEntity.getEntity(params).then((res) => {
        if (res.StateCode == 200) {
          that.entityList = res.Data;
        } else {
          that.$message.error({
            message: res.Message,
            duration: 2000,
          });
        }
      });
    },
    /* 获取跟进方式 */
    getFollowUpMethod() {
      let that = this;
      let params = {
        Name: "",
        Active: true,
      };
      APIFOLLOW.getFollowUpMethod(params).then((res) => {
        if (res.StateCode == 200) {
          that.foollowModeType = res.Data;
        } else {
          this.$message.error(res.Message);
        }
      });
    },
    /* 获取服务人员列表 */
    getServiceList() {
      let that = this;
      let params = {
        Name: "",
        AddWhetherToShow: "",
        Active: true,
      };
      APIServicer.getServiceList(params).then((res) => {
        if (res.StateCode == 200) {
          that.foollowModePeople = res.Data;
        } else {
          this.$message.error(res.Message);
        }
      });
    },
    /* 获取固定人员 */
    getallEmployee(SearchKey) {
      let that = this;
      let params = {
        SearchKey: SearchKey,
      };
      APIFollowUp.getSearch(params).then((res) => {
        if (res.StateCode == 200) {
          that.allEmployee = res.Data;
        } else {
          this.$message.error(res.Message);
        }
      });
    },
    /**  获取全部触发条件  */
    async getFollowUpRuleCondition() {
      var that = this;
      let res = await API.getFollowUpRuleCondition();
      if (res.StateCode == 200) {
        res.Data.forEach((item) => {
          item.Detail.forEach((i) => {
            i.checked = false;
            that.getTriggetItem(i);
          });
          that.propertiesData = res.Data;
        });
      } else {
        that.$message.error({
          message: res.Message,
          duration: 2000,
        });
      }
    },
    /**  等级  */
    async getCustomerLevel() {
      let params = {
        Active: true,
        Name: "",
      };
      let res = await API_GRADE.getCustomerLevel(params);
      if (res.StateCode == 200) {
        this.levelList = res.Data;
      } else {
        this.$message.error({
          message: res.Message,
          duration: 2000,
        });
      }
    },
    /** 来源   */
    async getCustomerSource() {
      let params = {
        Active: true,
        Name: "",
      };
      let res = await API_SOURCE.getCustomerSource(params);
      if (res.StateCode == 200) {
        this.sourceList = res.Data;
      } else {
        this.$message.error({
          message: res.Message,
          duration: 2000,
        });
      }
    },
  },
  /** 创建实例之前执行函数   */
  beforeCreate() {},
  /**  实例创建完成之后  */
  created() {},
  /**  在挂载开始之前被调用  */
  beforeMount() {},
  /**  实例被挂载后调用  */
  mounted() {
    this.getCustomerLevel();
    this.getCustomerSource();

    this.search();
    this.getFollowUpMethod();
    this.getServiceList();
    this.getEntityData();
    this.getFollowUpRuleCondition();
  },
  /**  数据更新 之前 调用  */
  beforeUpdate() {},
  /** 数据更新 完成 调用   */
  updated() {},
  /**  实例销毁之前调用  */
  beforeDestroy() {},
  /**  实例销毁后调用  */
  destroyed() {},
};
</script>

<style lang="scss">
.followUpSetting {
  .triggerLeft {
    background: #f5f7fa;
    padding: 15px;
    box-sizing: border-box;
    .propertiesClass {
      position: relative;
      .propertiesTitle {
        margin-top: 20px;
      }
      .propertiesTitle::before {
        content: " ";
        position: absolute;
        left: -9px;
        top: 3px;
        width: 4px;
        height: 15px;
        background: #ff8646;
      }
      .propertiesContent {
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
        .properties {
          width: 100px;
          line-height: 35px;
          text-align: center;
          background: #fff;
          border: 1px solid #eee;
          margin-top: 15px;
          cursor: pointer;
          border-radius: 3px;
        }
      }
    }
  }
  .propertiesActive {
    border: 1px solid #ff8646 !important;
    color: #ff8646;
    border-radius: 3px;
  }
  .trigger {
    .el-scrollbar {
      .el-scrollbar__wrap {
        overflow-x: hidden;
      }
    }
  }
  .triggerRight {
    background: #f5f7fa;
    padding: 15px;
    box-sizing: border-box;
    margin-left: 12px;
    .triggerRightHeader {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }
  .el-scrollbar_height {
    height: 60vh;
    .el-scrollbar__wrap {
      overflow-x: hidden;
    }
  }
  .custom-input {
    .el-input__inner {
      padding: 0 0 0 10px;
    }
  }
  .el-scrollbar_height {
    height: 60vh;
    .el-scrollbar__wrap {
      overflow-x: hidden;
    }
  }
}
</style>
