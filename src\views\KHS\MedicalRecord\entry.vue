<template>
  <div class="MedicalRecordEntry content_body_nopadding">
    <el-container>
      <el-aside class="entry_category">
        <div class="category_header">
          <div class="tip flex_box font_14 color_666 entry_title">词条类别</div>
          <el-button @click="addEntryCategory" type="text" class="entry_add_class">新增</el-button>
        </div>
        <el-scrollbar class="category_content">
          <draggable v-model="category_list" chosenClass="chosen" :move="moveEntryCategory" @end="moveEndEntryCategory">
            <div v-for="item in category_list" :key="item.ID" class="entry_category_item"
              :class="selectCategory && selectCategory.ID == item.ID ? 'entry_category_itemSelect' : ''"
              @click="selectCategoryClick(item)">
              <div class="flex_box">{{ item.Name }}</div>
              <div class="option">
                <i @click="editEntryCategoryItemClick(item)" class="el-icon-edit color_main text-bold"></i>
                <i @click="deleteEntryCategoryItemClick(item)" class="el-icon-delete color_main marlt_10 text-bold"></i>
              </div>
            </div>
          </draggable>
        </el-scrollbar>
      </el-aside>
      <el-main>
        <el-header style="height: auto; padding: 0px">
          <div v-if="selectCategory" class="tip font_14 color_666">{{ selectCategory && selectCategory.Name }}</div>
          <div class="martp_15">
            <el-row>
              <el-col :span="22">
                <el-form inline size="small" label-width="80px">
                  <el-form-item>
                    <el-input v-model="searchEntryName" placeholder="请输入词条标题搜索"></el-input>
                  </el-form-item>
                  <el-form-item>
                    <el-button @click="handleSearchEntry" type="primary" size="small" v-prevent-click>搜索</el-button>
                  </el-form-item>
                </el-form>
              </el-col>
              <el-col :span="2" class="text_right">
                <el-button @click="addEntryLabelItem" type="primary" size="small" v-prevent-click>新增</el-button>
              </el-col>
            </el-row>
          </div>
        </el-header>
        <el-table :data="tableData" size="small">
          <el-table-column label="标签" prop="Name" width="200"></el-table-column>
          <el-table-column label="词条类别" width="200">
            <template>
              {{ selectCategory && selectCategory.Name }}
            </template>
          </el-table-column>
          <el-table-column label="内容">
            <template slot-scope="scope">
              <el-popover v-for="item in scope.row.content" :key="item.ID" placement="top-start" width="58"
                trigger="hover" popper-class="entry_content_class">
                <i @click="editEntryLabelContentItemClick(item)" class="el-icon-edit color_main text-bold pad_5"
                  style="cursor: pointer"></i>
                <i @click="deleteEntryLabelContentItemClick(item)"
                  class="el-icon-delete color_main marlt_10 text-bold pad_5" style="cursor: pointer"></i>
                <el-tag slot="reference" type="info" size="small" class="marrt_10" style="cursor: pointer">{{ item.Name
                  }}</el-tag>
              </el-popover>

              <el-button size="small" type="primary" circle icon="el-icon-plus"
                @click="addMedicalRecordEntryLabelContent(scope.row, scope.$index)" v-prevent-click></el-button>
            </template>
          </el-table-column>
          <el-table-column label="移动" width="300">
            <template slot-scope="scope">
              <el-button size="small" type="primary" circle icon="el-icon-upload2"
                @click="upOneClick(scope.row, scope.$index)" v-prevent-click :disabled="scope.$index == 0"></el-button>
              <el-button size="small" type="primary" circle icon="el-icon-top" @click="upClick(scope.row, scope.$index)"
                v-prevent-click :disabled="scope.$index == 0"></el-button>
              <el-button size="small" type="primary" circle icon="el-icon-bottom"
                @click="downClick(scope.row, scope.$index)" v-prevent-click
                :disabled="scope.$index == tableData.length - 1"> </el-button>
              <el-button size="small" type="primary" circle icon="el-icon-download"
                @click="downOneClick(scope.row, scope.$index)" v-prevent-click
                :disabled="scope.$index == tableData.length - 1"></el-button>
            </template>
          </el-table-column>

          <el-table-column label="操作" width="145px">
            <template slot-scope="scope">
              <el-button size="small" type="primary" @click="editEntryLabelClick(scope.row)"
                v-prevent-click>编辑</el-button>
              <el-button size="small" type="danger" @click="deleteEntryLabelClick(scope.row, scope.$index)"
                v-prevent-click>删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-main>
    </el-container>
    <!-- 新增词条分类 -->
    <el-dialog :title="isAddCategory ? '新增词条' : '编辑词条'" :visible.sync="dialogVisible" width="400px">
      <el-form ref="ruleForm_category" :model="ruleForm_category" :rules="rules_category" label-width="70px"
        @submit.native.prevent>
        <el-form-item label="名称" prop="Name">
          <el-input size="small" v-model="ruleForm_category.Name"></el-input>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="dialogVisible = false" v-prevent-click>取 消</el-button>
        <el-button size="small" type="primary" @click="addSubmitCategory" :loading="submitCategoryLoading"
          v-prevent-click>保
          存</el-button>
      </div>
    </el-dialog>
    <!-- 新增词条标签 -->
    <el-dialog :title="isAddEntryLabel ? '新增标签' : '编辑标签'" :visible.sync="dialogVisible_label" width="400px">
      <el-form ref="ruleForm_label" :model="ruleForm_label" :rules="rules_label" label-width="70px"
        @submit.native.prevent>
        <el-form-item label="名称" prop="Name">
          <el-input size="small" v-model="ruleForm_label.Name"></el-input>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="dialogVisible_label = false" v-prevent-click>取 消</el-button>
        <el-button size="small" type="primary" @click="addSubmitEntryLabel" :loading="submitLabelLoading"
          v-prevent-click>保
          存</el-button>
      </div>
    </el-dialog>

    <!-- 新增词条标签 内容  -->
    <el-dialog :title="isAddEntryLabelContent ? '新增标签内容' : '编辑标签内容'" :visible.sync="dialogVisible_content"
      width="400px">
      <el-form ref="ruleForm_label_content" :model="ruleForm_label_content" :rules="rules_label_content"
        label-width="70px" @submit.native.prevent>
        <el-form-item label="名称" prop="Name">
          <el-input size="small" v-model="ruleForm_label_content.Name"></el-input>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="dialogVisible_content = false" v-prevent-click>取 消</el-button>
        <el-button size="small" type="primary" @click="addSubmitEntryLabelContent" :loading="submitLabelContentLoading"
          v-prevent-click>保 存</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import API from "@/api/KHS/MedicalRecord/entry.js";
import draggable from "vuedraggable";
export default {
  name: "MedicalRecordEntry",

  props: {},
  /** 监听数据变化   */
  watch: {},
  /**  引入的组件  */
  components: {
    draggable,
  },
  /**  Vue 实例的数据对象**/
  data() {
    return {
      submitLabelContentLoading: false,
      submitLabelLoading: false,
      dialogVisible_content: false,
      isAddEntryLabelContent: false,
      isAddEntryLabel: false,
      dialogVisible_label: false,
      submitCategoryLoading: false,
      tableData: [],
      category_list: [],
      isAddCategory: false,
      dialogVisible: false,
      ruleForm_category: {
        Name: "",
      },
      rules_category: {
        Name: [
          { required: true, message: "请输入词条名称", trigger: "blur" },
          { min: 1, max: 50, message: "长度在 1 到 50 个字符", trigger: "blur" },
        ],
      },
      selectCategory: null,
      moveItem: null,
      beforeItem: null,
      beforeIndex: 0,
      moveIndex: 0,

      searchEntryName: "",
      ruleForm_label: {
        Name: "",
      },
      rules_label: {
        Name: [
          { required: true, message: "请输入标签名称", trigger: "blur" },
          { min: 1, max: 50, message: "长度在 1 到 50 个字符", trigger: "blur" },
        ],
      },
      ruleForm_label_content: {
        Name: "",
      },
      rules_label_content: {
        Name: [
          { required: true, message: "请输入标签内容名称", trigger: "blur" },
          { min: 1, max: 500, message: "长度在 1 到 500 个字符", trigger: "blur" },
        ],
      },
    };
  },
  /**计算属性  */
  computed: {},
  /**  方法集合  */
  methods: {
    /**  保存  */
    addSubmitEntryLabelContent() {
      let that = this;
      that.$refs.ruleForm_label_content.validate((valid) => {
        if (valid) {
          if (that.isAddEntryLabelContent) {
            that.medicalRecordEntryContent_create();
          } else {
            that.medicalRecordEntryContent_update();
          }
        }
      });
    },
    /**    */
    deleteEntryLabelContentItemClick(item) {
      let that = this;
      that
        .$confirm("是否要删除标签内容", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
        .then(() => {
          that.medicalRecordEntryContent_delete(item.ID);
        })
        .catch(() => {
          that.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    /**    */
    editEntryLabelContentItemClick(item) {
      let that = this;
      that.isAddEntryLabelContent = false;
      that.dialogVisible_content = true;
      that.ruleForm_label_content = {
        Name: item.Name,
        ID: item.ID,
      };
    },
    /**   添加词条内容 */
    addMedicalRecordEntryLabelContent(item) {
      let that = this;
      that.isAddEntryLabelContent = true;
      that.dialogVisible_content = true;
      that.ruleForm_label_content = {
        Name: "",
        ID: item.ID,
      };
    },
    // 移动首部
    upOneClick: function (row) {
      var that = this;
      that.medicalRecordEntryLabel_move(row.ID, "");
    },
    // 移动尾部
    downOneClick: function (row, index) {
      var that = this;
      var tabIndex = that.tableData.length;
      var beforeId = "";
      if (index < tabIndex - 1) {
        beforeId = that.tableData[tabIndex - 1].ID;
      }
      that.medicalRecordEntryLabel_move(row.ID, beforeId);
    },
    // 向上
    upClick: function (row, index) {
      var that = this;
      var beforeId = "";
      if (index > 1) {
        beforeId = that.tableData[index - 2].ID;
      }
      that.medicalRecordEntryLabel_move(row.ID, beforeId);
    },
    // 向下
    downClick: function (row, index) {
      var that = this;
      var beforeId = "";
      if (index + 1 != that.tableData.length) {
        beforeId = that.tableData[index + 1].ID;
      }
      that.medicalRecordEntryLabel_move(row.ID, beforeId);
    },
    /**    */
    deleteEntryLabelClick(item) {
      let that = this;
      that
        .$confirm("是否要删除词条标签", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
        .then(() => {
          that.medicalRecordEntryLabel_delete(item.ID);
        })
        .catch(() => {
          that.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    /**    */
    editEntryLabelClick(item) {
      let that = this;
      that.isAddEntryLabel = false;
      that.dialogVisible_label = true;
      that.ruleForm_label = {
        ID: item.ID,
        Name: item.Name,
      };
    },
    /**   添加标签 */
    addSubmitEntryLabel() {
      let that = this;
      that.$refs.ruleForm_label.validate((valid) => {
        if (valid) {
          if (that.isAddEntryLabel) {
            that.medicalRecordEntryLabel_create();
          } else {
            that.medicalRecordEntryLabel_update();
          }
        }
      });
    },
    /**  新标签  */
    addEntryLabelItem() {
      let that = this;
      that.dialogVisible_label = true;
      that.isAddEntryLabel = true;
      that.ruleForm_label = {
        Name: "",
      };
    },

    /**    */
    moveEndEntryCategory() {
      let that = this;
      let beforeId = that.beforeItem.ID;
      if (that.moveIndex > that.beforeIndex && that.beforeIndex > 0) {
        beforeId = that.category_list[that.beforeIndex - 1].ID;
      } else {
        if (that.beforeIndex == 0) {
          beforeId = "";
        }
      }
      that.medicalRecordEntryCategory_move(that.moveItem.ID, beforeId);
    },
    /**  移动分类  */
    moveEntryCategory(evt) {
      let that = this;
      that.moveItem = evt.draggedContext.element;
      that.beforeIndex = evt.draggedContext.futureIndex;
      that.moveIndex = evt.draggedContext.index;
      that.beforeItem = evt.relatedContext.element;
    },
    /**   删除分类 */
    deleteEntryCategoryItemClick(item) {
      let that = this;
      that
        .$confirm("是否要删除词条类别", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
        .then(() => {
          that.medicalRecordEntryCategory_delete(item.ID);
        })
        .catch(() => {
          that.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    /**  编辑分类  */
    editEntryCategoryItemClick(item) {
      let that = this;
      that.isAddCategory = false;
      that.dialogVisible = true;
      that.ruleForm_category = {
        ID: item.ID,
        Name: item.Name,
      };
    },
    /**  分类点击  */
    selectCategoryClick(item) {
      let that = this;
      if (item.ID == that.selectCategory.ID) {
        return;
      }
      that.selectCategory = item;
      that.searchEntryName = "";
      that.medicalRecordEntryLabel_list();
    },
    /**  添加词条分类  */
    addSubmitCategory() {
      let that = this;
      that.$refs.ruleForm_category.validate((valid) => {
        if (valid) {
          if (that.isAddCategory) {
            that.medicalRecordEntryCategory_create();
          } else {
            that.medicalRecordEntryCategory_update();
          }
        }
      });
    },
    /** 搜索词条   */
    handleSearchEntry() {
      let that = this;
    },
    /**  新增词条分类  */
    addEntryCategory() {
      let that = this;
      that.dialogVisible = true;
      that.isAddCategory = true;
      that.ruleForm_category = {
        Name: "",
      };
    },
    /* ****************************************************** */
    /**  查询词条分类  */
    medicalRecordEntryCategory_list() {
      let that = this;
      let params = {};
      API.medicalRecordEntryCategory_list(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.category_list = res.Data;
            if (that.category_list && that.category_list.length > 0) {
              that.selectCategory = that.category_list[0];
              that.searchEntryName = "";
              that.medicalRecordEntryLabel_list();
            }
          } else {
            that.$message.error(res.Message);
          }
        })
        .catch((fail) => {
          that.$message.error(fail);
        });
    },
    /**  创建词条分类    */
    medicalRecordEntryCategory_create() {
      let that = this;
      let params = {
        Name: that.ruleForm_category.Name,
      };
      that.submitCategoryLoading = true;
      API.medicalRecordEntryCategory_create(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.dialogVisible = false;
            that.$message.success("操作成功");
            that.medicalRecordEntryCategory_list();
            that.submitCategoryLoading = false;
          } else {
            that.submitCategoryLoading = false;
            that.$message.error(res.Message);
          }
        })
        .catch((fail) => {
          that.submitCategoryLoading = false;
          that.$message.error(fail);
        });
    },
    /**  编辑分类  */
    medicalRecordEntryCategory_update() {
      let that = this;
      let params = {
        ID: that.ruleForm_category.ID, //编号
        Name: that.ruleForm_category.Name, //词条名称
      };
      that.submitCategoryLoading = true;
      API.medicalRecordEntryCategory_update(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.dialogVisible = false;
            that.$message.success("操作成功");
            that.medicalRecordEntryCategory_list();
            that.submitCategoryLoading = false;
          } else {
            that.$message.error(res.Message);
            that.submitCategoryLoading = false;
          }
        })
        .catch((fail) => {
          that.submitCategoryLoading = false;
          that.$message.error(fail);
        });
    },
    /** 删除分类  */
    medicalRecordEntryCategory_delete(ID) {
      let that = this;
      let params = { ID: ID };
      API.medicalRecordEntryCategory_delete(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.$message.success("操作成功");
            that.medicalRecordEntryCategory_list();
          } else {
            that.$message.error(res.Message);
          }
        })
        .catch((fail) => {
          that.$message.error(fail);
        });
    },
    /**    */
    medicalRecordEntryCategory_move(MoveID, BeforeID) {
      let that = this;
      let params = {
        MoveID: MoveID,
        BeforeID: BeforeID,
      };
      API.medicalRecordEntryCategory_move(params)
        .then((res) => {
          if (res.StateCode == 200) {
            // that.$message.success("操作成功");
            that.medicalRecordEntryCategory_list();
          } else {
            that.$message.error(res.Message);
          }
        })
        .catch((fail) => {
          that.$message.error(fail);
        });
    },
    /**  查询词条类别  */
    medicalRecordEntryLabel_list() {
      let that = this;
      let params = {
        Name: that.searchEntryName,
        CategoryID: that.selectCategory.ID,
      };
      API.medicalRecordEntryLabel_list(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.tableData = res.Data;
          } else {
            that.$message.error(res.Message);
          }
        })
        .catch((fail) => {
          that.$message.error(fail);
        });
    },
    /**   创建标签 */
    medicalRecordEntryLabel_create() {
      let that = this;
      let params = {
        CategoryID: that.selectCategory.ID, //分类编号
        Name: that.ruleForm_label.Name, //类别名称
      };
      that.submitLabelLoading = true;
      API.medicalRecordEntryLabel_create(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.$message.success("操作成功");
            that.dialogVisible_label = false;
            that.medicalRecordEntryLabel_list();
            that.submitLabelLoading = false;
          } else {
            that.submitLabelLoading = false;
            that.$message.error(res.Message);
          }
        })
        .catch((fail) => {
          that.submitLabelLoading = false;
          that.$message.error(fail);
        });
    },
    /**  更新标签  */
    medicalRecordEntryLabel_update() {
      let that = this;
      let params = {
        ID: that.ruleForm_label.ID, //编号
        Name: that.ruleForm_label.Name, //类别名称
      };
      that.submitLabelLoading = true;
      API.medicalRecordEntryLabel_update(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.$message.success("操作成功");
            that.dialogVisible_label = false;
            that.medicalRecordEntryLabel_list();
            that.submitLabelLoading = false;
          } else {
            that.submitLabelLoading = false;
            that.$message.error(res.Message);
          }
        })
        .catch((fail) => {
          that.submitLabelLoading = false;
          that.$message.error(fail);
        });
    },
    /**  删除标签  */
    medicalRecordEntryLabel_delete(ID) {
      let that = this;
      let params = {
        ID: ID,
      };
      API.medicalRecordEntryLabel_delete(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.$message.success("操作成功");
            that.medicalRecordEntryLabel_list();
          } else {
            that.$message.error(res.Message);
          }
        })
        .catch((fail) => {
          that.$message.error(fail);
        });
    },
    /**  移动标签顺序  */
    medicalRecordEntryLabel_move(MoveID, BeforeID) {
      let that = this;
      let params = {
        CategoryID: that.selectCategory.ID, //分类编号
        MoveID: MoveID,
        BeforeID: BeforeID,
      };
      API.medicalRecordEntryLabel_move(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.medicalRecordEntryLabel_list();
          } else {
            that.$message.error(res.Message);
          }
        })
        .catch((fail) => {
          that.$message.error(fail);
        });
    },
    /**  创建类别内容  */
    medicalRecordEntryContent_create() {
      let that = this;
      let params = {
        Name: that.ruleForm_label_content.Name, //内容
        LabelID: that.ruleForm_label_content.ID, //类别编号
      };
      that.submitLabelContentLoading = true;
      API.medicalRecordEntryContent_create(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.dialogVisible_content = false;
            that.$message.success("操作成功");
            that.medicalRecordEntryLabel_list();
            that.submitLabelContentLoading = false;
          } else {
            that.submitLabelContentLoading = false;
            that.$message.error(res.Message);
          }
        })
        .catch((fail) => {
          that.submitLabelContentLoading = false;
          that.$message.error(fail);
        });
    },
    /**    */
    medicalRecordEntryContent_update() {
      let that = this;
      let params = {
        Name: that.ruleForm_label_content.Name, //内容
        ID: that.ruleForm_label_content.ID, //类别编号
      };
      that.submitLabelContentLoading = true;
      API.medicalRecordEntryContent_update(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.dialogVisible_content = false;
            that.$message.success("操作成功");
            that.medicalRecordEntryLabel_list();
            that.submitLabelContentLoading = false;
          } else {
            that.submitLabelContentLoading = false;
            that.$message.error(res.Message);
          }
        })
        .catch((fail) => {
          that.submitLabelContentLoading = false;
          that.$message.error(fail);
        });
    },
    /**    */
    medicalRecordEntryContent_delete(ID) {
      let that = this;
      let params = { ID: ID };
      API.medicalRecordEntryContent_delete(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.$message.success("操作成功");

            that.medicalRecordEntryLabel_list();
          } else {
            that.$message.error(res.Message);
          }
        })
        .catch((fail) => {
          that.$message.error(fail);
        });
    },
  },
  /** 创建实例之前执行函数   */
  beforeCreate() { },
  /**  实例创建完成之后  */
  created() { },
  /**  在挂载开始之前被调用  */
  beforeMount() { },
  /**  实例被挂载后调用  */
  mounted() {
    let that = this;
    that.medicalRecordEntryCategory_list();
  },
  /**  数据更新 之前 调用  */
  beforeUpdate() { },
  /** 数据更新 完成 调用   */
  updated() { },
  /**  实例销毁之前调用  */
  beforeDestroy() { },
  /**  实例销毁后调用  */
  destroyed() { },
};
</script>

<style lang="scss">
.MedicalRecordEntry {
  height: calc(100% - 30px);

  // 移动列中禁用按钮的样式
  .el-button--primary.is-disabled,
  .el-button--primary.is-disabled:active,
  .el-button--primary.is-disabled:focus,
  .el-button--primary.is-disabled:hover {
    color: #fff !important;
    background-color: #81D8D0 !important;
    border-color: #81D8D0 !important;
  }

  .el-container {
    height: 100% !important;

    .el-aside {
      height: 100%;
      border-right: 1px solid #f0f0f0;
      padding: 12px 0px;

      .category_header {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0px 0px 12px 12px;

        .entry_title {
          margin: unset;
        }

        .entry_add_class {
          width: 60px;
        }
      }

      .category_content {
        height: calc(100% - 52px);

        .el-scrollbar__wrap {
          overflow-x: hidden;
        }

        .entry_category_item {
          cursor: pointer;
          font-size: 14px;
          color: #666666;
          padding: 5px 10px;
          display: flex;
          justify-content: space-between;
          align-items: center;

          .option {
            display: none;
          }
        }

        .entry_category_item:hover {
          background-color: #f0fffe;

          .option {
            display: block;
          }
        }

        .entry_category_itemSelect {
          background-color: #f5f7fa;
        }
      }
    }

    .el-main {
      padding: 12px;
    }
  }
}

.entry_content_class {
  padding: 4px;
  min-width: unset;
}
</style>
