import * as API from '../../index';

export default {
    // 获取成长值信息
    getCustomerGrowth:params => {
        return API.POST('api/customerGrowth/all',params)
    },
    // 获取成长值状态
    getCustomerGrowthStatus:params => {
      return API.POST('api/customerGrowth/status',params)
    },
    // 更新成长值状态
    uploadCustomerGrowthStatus:params => {
        return API.POST('api/customerGrowth/updateStatus',params)
    },
    // 更新成长值信息
    uploadCustomerGrowth:params => {
        return API.POST('api/customerGrowth/update',params)
    }
}