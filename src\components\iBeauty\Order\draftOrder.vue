<template>
  <el-dialog title="取单" :visible.sync="draftOrderVisible_" width="1200px" class="draftOrderDialog">
    <el-tabs v-model="activeName">
      <el-tab-pane v-if="isTreatBilling" label="消耗挂单" name="0">
        <div class="nav_header">
          <el-form :inline="true" size="small" @submit.native.prevent @keyup.enter.native="handleSearch" label-width="auto">
            <el-form-item label="搜索">
              <el-input v-model="searchName" placeholder="请输入顾客姓名、电话搜索" clearable @clear="handleSearch"></el-input>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleSearch" v-prevent-click>搜 索</el-button>
            </el-form-item>
          </el-form>
        </div>
        <!-- el-scorll -->
        <!-- <el-scrollbar style="height: 50vh"> -->
        <el-table size="small" ref="multipleTable" :data="treatTableData" v-loading="loading" :default-expand-all="isExpand" :span-method="tableSpanMethod" :cell-style="tableCellStyle" class="customTreatTableClass" max-height="400">
          <el-table-column prop="ID" type="expand" width="1">
            <template slot-scope="scope">
              <el-table :data="scope.row.treatBillDetailList" :show-header="false" :cell-style="childTableCellStyle" :span-method="({ row, column, rowIndex, columnIndex }) => childTableSpanMethod(row, column, rowIndex, columnIndex, scope.row.treatBillDetailList.length)" border size="small">
                <el-table-column prop="GoodsName" label="名称"></el-table-column>
                <el-table-column prop="Quantity" label="数量"></el-table-column>
                <el-table-column prop="TotalAmount" label="金额">
                  <template slot-scope="scope">￥{{ scope.row.TotalAmount | toFixed | NumFormat }}</template>
                </el-table-column>
                <el-table-column label="客户">
                  <div>
                    <span class="">{{ scope.row.Name }}</span
                    ><span v-if="scope.row.Code">({{ scope.row.Code }})</span>
                  </div>
                  <div v-if="scope.row.CustomerID != null">手机号：{{ scope.row.PhoneNumber | hidephone }}</div>
                </el-table-column>
                <el-table-column label="录单人">
                  {{ scope.row.EmployeeName }}
                </el-table-column>
                <el-table-column label="备注">
                  {{ scope.row.Remark }}
                </el-table-column>
                <el-table-column label="操作" width="110px"></el-table-column>
              </el-table>
            </template>
          </el-table-column>
          <el-table-column label="商品">
            <template slot-scope="scope">
              <span>下单时间：{{ scope.row.BillDate | dateFormat("YYYY-MM-DD HH:mm") }}</span>
              <span class="marlt_15 color_999">订单编号：{{ scope.row.ID }}</span>
            </template>
          </el-table-column>
          <el-table-column label="数量"></el-table-column>
          <el-table-column label="金额"></el-table-column>
          <el-table-column label="客户"></el-table-column>
          <el-table-column label="录单人"></el-table-column>
          <el-table-column label="备注"></el-table-column>
          <el-table-column label="操作" width="90px">
            <template slot-scope="scope">
              <el-button @click="treatTakeOrderClick(scope.row)" v-prevent-click type="text" size="small">取单</el-button>
              <el-button @click="removeTratDraftOrderClick(scope.row, scope.$index)" v-prevent-click type="text" size="small">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        <!-- </el-scrollbar> -->
        <!-- <el-table :data="treatTableData" style="width: 100%" size="small" max-height="400">
          <el-table-column prop="ID" label="订单编号"> </el-table-column>
          <el-table-column prop="name" label="顾客信息">
            <template slot-scope="scope">
              <div>{{ scope.row.Name }}</div>
              <div>手机号: {{ scope.row.PhoneNumber | hidephone}}</div>
            </template>
          </el-table-column>
          <el-table-column prop="EmployeeName" label="录单人"> </el-table-column>
          <el-table-column prop="BillDate" label="下单时间">
            <template slot-scope="scope">
              {{ scope.row.BillDate | dateFormat("YYYY-MM-DD HH:mm") }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="150">
            <template slot-scope="scope">
              <el-button type="primary" size="small" v-prevent-click @click="treatTakeOrderClick(scope.row)">取 单</el-button>
              <el-button type="danger" size="small" v-prevent-click @click="removeTratDraftOrderClick(scope.row)">删 除</el-button>
            </template>
          </el-table-column>
        </el-table> -->

        <div class="pad_15 text_right">
          <el-pagination background v-if="treatPaginations.total > 0" @current-change="treatHandleCurrentChange" :current-page.sync="treatPaginations.page" :page-size="treatPaginations.page_size" :layout="treatPaginations.layout" :total="treatPaginations.total"></el-pagination>
        </div>
      </el-tab-pane>
      <el-tab-pane v-if="isSaleBilling" label="销售挂单" name="1">
        <div class="nav_header">
          <el-form :inline="true" size="small" @submit.native.prevent @keyup.enter.native="saleHandleSearch" label-width="auto">
            <el-form-item label="搜索">
              <el-input v-model="saleSearchName" placeholder="请输入顾客姓名、电话搜索" clearable @clear="saleHandleSearch"></el-input>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="saleHandleSearch" v-prevent-click>搜 索</el-button>
            </el-form-item>
          </el-form>
        </div>
        <el-table size="small" ref="multipleTable" :data="saleTableData" v-loading="loading" :default-expand-all="isExpand" :span-method="tableSpanMethod" :cell-style="tableCellStyle" class="customTreatTableClass" max-height="400">
          <el-table-column prop="ID" type="expand" width="1">
            <template slot-scope="scope">
              <el-table :data="scope.row.saleBillDetailList" :show-header="false" :cell-style="childTableCellStyle" :span-method="({ row, column, rowIndex, columnIndex }) => childTableSpanMethod(row, column, rowIndex, columnIndex, scope.row.saleBillDetailList.length)" border size="small">
                <el-table-column prop="GoodsName" label="名称"></el-table-column>
                <el-table-column prop="Quantity" label="数量"></el-table-column>
                <el-table-column prop="TotalAmount" label="金额">
                  <template slot-scope="scope">￥{{ scope.row.TotalAmount | toFixed | NumFormat }}</template>
                </el-table-column>
                <el-table-column label="客户">
                  <div>
                    <span class="">{{ scope.row.Name }}</span
                    ><span v-if="scope.row.Code">({{ scope.row.Code }})</span>
                  </div>
                  <div v-if="scope.row.CustomerID != null">手机号：{{ scope.row.PhoneNumber | hidephone }}</div>
                </el-table-column>
                <el-table-column label="录单人">
                  {{ scope.row.EmployeeName }}
                </el-table-column>
                <el-table-column label="备注">
                  {{ scope.row.Remark }}
                </el-table-column>
                <el-table-column label="操作" width="110px"></el-table-column>
              </el-table>
            </template>
          </el-table-column>
          <el-table-column label="商品">
            <template slot-scope="scope">
              <span>下单时间：{{ scope.row.BillDate | dateFormat("YYYY-MM-DD HH:mm") }}</span>
              <span class="marlt_15 color_999">订单编号：{{ scope.row.ID }}</span>
              <span class="marlt_15 color_999">订单类型：{{ billTypeFormatter(scope.row) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="数量"></el-table-column>
          <el-table-column label="金额"></el-table-column>
          <el-table-column label="客户"></el-table-column>
          <el-table-column label="录单人"></el-table-column>
          <el-table-column label="备注"></el-table-column>
          <el-table-column label="操作" width="90px">
            <template slot-scope="scope">
              <el-button @click="SaleOrderClick(scope.row)" v-prevent-click type="text" size="small">取单</el-button>
              <el-button @click="removeSaleDraftOrderClick(scope.row, scope.$index)" v-prevent-click type="text" size="small">删除</el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- <el-table :data="saleTableData" style="width: 100%" size="small">
          <el-table-column prop="ID" label="订单编号"> </el-table-column>
          <el-table-column prop="name" label="顾客信息">
            <template slot-scope="scope">
              <div>{{ scope.row.Name }}</div>
              <div>手机号: {{ scope.row.PhoneNumber | hidephone }}</div>
            </template>
          </el-table-column>
          <el-table-column prop="BillType" label="订单类型" :formatter="billTypeFormatter"> </el-table-column>
          <el-table-column prop="EmployeeName" label="录单人"> </el-table-column>
          <el-table-column prop="BillDate" label="下单时间">
            <template slot-scope="scope">
              {{ scope.row.BillDate | dateFormat("YYYY-MM-DD HH:mm") }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="150">
            <template slot-scope="scope">
              <el-button type="primary" size="small" v-prevent-click @click="SaleOrderClick(scope.row)">取 单</el-button>
              <el-button type="danger" size="small" v-prevent-click @click="removeSaleDraftOrderClick(scope.row)">删 除</el-button>
            </template>
          </el-table-column>
        </el-table> -->

        <div class="pad_15 text_right">
          <el-pagination background v-if="salePaginations.total > 0" @current-change="saleHandleCurrentChange" :current-page.sync="salePaginations.page" :page-size="salePaginations.page_size" :layout="salePaginations.layout" :total="salePaginations.total"></el-pagination>
        </div>
      </el-tab-pane>
    </el-tabs>
  </el-dialog>
</template>

<script>
import API from "@/api/iBeauty/Order/draftOrder";

export default {
  name: "draftOrder",
  props: {
    isSaleBilling: {
      type: Boolean,
      default: false,
    },
    isTreatBilling: {
      type: Boolean,
      default: false,
    },
  },
  /** 监听数据变化   */
  watch: {},
  /**  引入的组件  */
  components: {},
  /**  Vue 实例的数据对象**/
  data() {
    return {
      loading: false,
      isExpand: true,
      draftOrderVisible_: false,
      activeName: "0",
      customerID: "",
      searchName: "",
      saleSearchName: "",
      treatTableData: [],
      saleTableData: [],
      treatPaginations: {
        page: 1, // 当前位于哪页
        total: 0, // 总数
        page_size: 10, // 1页显示多少条
        layout: "total, prev, pager, next,jumper", // 翻页属性
      },
      salePaginations: {
        page: 1, // 当前位于哪页
        total: 0, // 总数
        page_size: 10, // 1页显示多少条
        layout: "total, prev, pager, next,jumper", // 翻页属性
      },
    };
  },
  /**计算属性  */
  computed: {},
  /**  方法集合  */
  methods: {
    /**    */
    childTableCellStyle() {
      return "background:#fff;";
    },
    /**    */
    tableCellStyle() {
      return "background:#f5f7fa";
    },
    /**    */
    childTableSpanMethod(row, column, rowIndex, columnIndex, length) {
      if (columnIndex === 3 || columnIndex === 4 || columnIndex === 5 || columnIndex === 6) {
        if (rowIndex == 0) {
          return {
            rowspan: length,
            colspan: 1,
          };
        } else {
          return {
            rowspan: 0,
            colspan: 0,
          };
        }
      }
      {
        return {
          rowspan: 1,
          colspan: 1,
        };
      }
    },
    /**    */
    tableSpanMethod({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 1) {
        return {
          rowspan: 1,
          colspan: 7,
        };
      } else if (columnIndex == 7) {
        return {
          rowspan: 1,
          colspan: 1,
        };
      } else {
        return {
          rowspan: 0,
          colspan: 0,
        };
      }
    },
    /**  格式化订单类型   */
    billTypeFormatter(row) {
      switch (row.BillType) {
        case "10":
          return "销售订单";
        case "30":
          return "补欠款单";
        case "40":
          return "充值订单";
      }
    },
    showDraftOrderVisible() {
      let that = this;
      that.activeName = "0";
      if (that.isSaleBilling) {
        that.activeName = "1";
      }
      if (that.isTreatBilling) {
        that.activeName = "0";
      }

      that.treatBillPendingOrder_list();
      that.saleBillPendingOrder();
      that.draftOrderVisible_ = true;
    },
    /**  消耗列表搜索  */
    handleSearch() {
      let that = this;
      that.treatPaginations.page = 1;
      that.treatBillPendingOrder_list();
    },
    /**    */
    saleHandleSearch() {
      let that = this;
      that.salePaginations.page = 1;
      that.saleBillPendingOrder();
    },
    /**  修改消耗挂单分页  */
    treatHandleCurrentChange(page) {
      let that = this;
      that.treatPaginations.page = page;
      that.treatBillPendingOrder_list();
    },
    /**    */
    saleHandleCurrentChange(page) {
      let that = this;
      that.treatPaginations.page = page;
      that.saleBillPendingOrder();
    },
    /**  删除  */
    removeTratDraftOrderClick(row) {
      let that = this;
      that
        .$confirm("是否确定删除挂单信息？", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
        .then(() => {
          that.treatBillPendingOrder_delete(row);
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },

    /**  消耗取单  */
    treatTakeOrderClick(row) {
      let that = this;
      that.pendingOrderInfo(row);
    },

    /**    */
    SaleOrderClick(row) {
      let that = this;
      that.pendingOrderInfo_saleBill(row);
    },

    /**    */
    removeSaleDraftOrderClick(row) {
      let that = this;
      that
        .$confirm("是否确定删除挂单信息？", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
        .then(() => {
          that.deleteBillPendingOrder(row.ID);
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },

    /**  *********************************  */
    /**  列表  */
    async treatBillPendingOrder_list() {
      let that = this;
      let params = {
        PageNum: that.treatPaginations.page, //分页
        Name: that.searchName, //模糊搜索
        BillStatus: "10", //订单状态
      };
      let res = await API.treatBillPendingOrder_list(params);
      if (res.StateCode == 200) {
        that.treatTableData = res.List;
        that.treatPaginations.total = res.Total;
      } else {
        that.$message.error(res.Message);
      }
    },

    /**  消耗详情  */
    async pendingOrderInfo(row) {
      let that = this;
      let params = { ID: row.ID };
      let res = await API.pendingOrderInfo(params);
      if (res.StateCode == 200) {
        that.$emit("treatTakeOrder", res.Data);
        that.draftOrderVisible_ = false;
      } else {
        that.$message.error(res.Message);
      }
    },

    /**  消耗删除  */
    async treatBillPendingOrder_delete(row) {
      let that = this;
      let params = {
        ID: row.ID, //分页
      };
      let res = await API.treatBillPendingOrder_delete(params);
      if (res.StateCode == 200) {
        that.$message.success("删除成功");
        that.handleSearch();
      } else {
        that.$message.error(res.Message);
      }
    },

    /**  销售列表  */
    async saleBillPendingOrder() {
      let that = this;
      let params = {
        PageNum: that.salePaginations.page, //分页
        Name: that.saleSearchName, //模糊搜索
        BillStatus: "10", //订单状态
      };
      let res = await API.saleBillPendingOrder(params);
      if (res.StateCode == 200) {
        that.saleTableData = res.List;
        that.salePaginations.total = res.Total;
      } else {
        that.$message.error(res.Message);
      }
    },

    /**  销售删除  */
    async deleteBillPendingOrder(ID) {
      let that = this;
      let params = { ID: ID };
      let res = await API.deleteBillPendingOrder(params);
      if (res.StateCode == 200) {
        that.$message.success("删除成功");
        that.saleHandleSearch();
      } else {
        that.$message.error(res.Message);
      }
    },

    /**  销售 挂单详情  */
    async pendingOrderInfo_saleBill(row) {
      let that = this;
      let params = { SaleBillID: row.ID };
      let res = await API.pendingOrderInfo_saleBill(params);
      if (res.StateCode == 200) {
        that.$emit("saleTakeOrder", res.Data);
        that.draftOrderVisible_ = false;
      } else {
        that.$message.error(res.Message);
      }
    },
  },
  /** 创建实例之前执行函数   */
  beforeCreate() {},
  /**  实例创建完成之后  */
  created() {},
  /**  在挂载开始之前被调用  */
  beforeMount() {},
  /**  实例被挂载后调用  */
  mounted() {},
  /**  数据更新 之前 调用  */
  beforeUpdate() {},
  /** 数据更新 完成 调用   */
  updated() {},
  /**  实例销毁之前调用  */
  beforeDestroy() {},
  /**  实例销毁后调用  */
  destroyed() {},
};
</script>

<style lang="scss">
.draftOrderDialog {
  .customTreatTableClass {
    .el-table__expand-icon {
      visibility: hidden !important;
    }
    .el-table__expanded-cell {
      padding-bottom: unset;
      padding-top: unset;
    }
    .el-table__row > td {
      /* 去除表格线 */
      // border: none;
      .el-table::before {
        /* 去除下边框 */
        height: 0;
      }
    }
  }
}
</style>
