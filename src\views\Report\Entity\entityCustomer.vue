<template>
  <div class="entityCustomer content_body" v-loading="loading">
    <!-- 搜索 -->
    <div class="nav_header">
      <el-form :inline="true" size="small" @submit.native.prevent>
        <el-form-item label="门店">
          <el-select v-model="EntityID" clearable filterable placeholder="请选择门店" :default-first-option="true" @change="handleSearch">
            <el-option v-for="item in EntityList" :key="item.ID" :label="item.EntityName" :value="item.ID"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" v-prevent-click @click="handleSearch">搜索</el-button>
        </el-form-item>
        <el-form-item>
          <el-button v-if="isExport" type="primary" size="small" v-prevent-click :loading="downloadLoading" @click="downloadSalePayExcel">导出</el-button>
        </el-form-item>
      </el-form>
    </div>
    <!-- 表格 -->
    <el-table :data="tableData" size="small" show-summary :summary-method="getSummary" >
      <el-table-column label="门店" prop="EntityName" fixed width="150"></el-table-column>
      <el-table-column label="客户数" prop="CustomerCount" align="right"></el-table-column>
      <el-table-column label="成交客户数" prop="DealCount" align="right"></el-table-column>
      <el-table-column label="性别" align="center">
        <el-table-column label="女客" prop="WomanCount" align="right"></el-table-column>
        <el-table-column label="男客" prop="ManCount" align="right"></el-table-column>
        <el-table-column label="未知" prop="UnknownCount" align="right"></el-table-column>
      </el-table-column>
      <el-table-column label="客户构成" align="center">
        <el-table-column label="会员" prop="MemberCount" align="right"></el-table-column>
        <el-table-column label="会员占比" prop="MemberRate" align="right">
          <template slot-scope="scope">
            <span v-if="scope.row.MemberRate > 0">{{ scope.row.MemberRate | toFixed | NumFormat }}%</span>
            <span v-else>{{ scope.row.MemberRate }}</span>
          </template>
        </el-table-column>
        <el-table-column label="非会员" prop="UnMemberCount" align="right"></el-table-column>
        <el-table-column label="非会员占比" prop="UnMemberRate" align="right">
          <template slot-scope="scope">
            <span v-if="scope.row.UnMemberRate > 0">{{ scope.row.UnMemberRate | toFixed | NumFormat }}%</span>
            <span v-else>{{ scope.row.UnMemberRate }}</span>
          </template>
        </el-table-column>
      </el-table-column>
      <el-table-column label="等级构成" align="center">
        <el-table-column v-for="item in SumOutputForm.Level" :key="item.ID" :label="item.Name" prop="Count" align="right">
          <template slot-scope="scope">{{ getCount(item.ID, scope.row.Level) }}</template>
        </el-table-column>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <div class="pad_15 text_right">
      <el-pagination
        background
        v-if="paginations.total > 0"
        @current-change="handlePageChange"
        :current-page.sync="paginations.page"
        :page-size="paginations.page_size"
        :layout="paginations.layout"
        :total="paginations.total"
      ></el-pagination>
    </div>
  </div>
</template>

<script>
import API from "@/api/Report/Entity/entityCustomer";
import APIStore from "@/api/Report/Entity/entityTrade";
var Enumerable = require("linq");
export default {
  name: "ReportEntityCustomer",

  beforeRouteEnter(to, from, next) {
    next((vm) => {
      vm.isExport = vm.$permission.permission(to.meta.Permission, "Report-Entity-EntityCustomer-Export");
    });
  },
  props: {},
  /** 监听数据变化   */
  watch: {},
  /**  引入的组件  */
  components: {},
  /**  Vue 实例的数据对象**/
  data() {
    return {
      isExport: false,
      loading: false,
      downloadLoading: false,
      EntityID: "", // 搜索门店数据
      EntityList: [], // 门店数据
      tableData: [], // 表格数据
      SumOutputForm: {}, // 合计数据
      paginations: {
        page: 1, // 当前位于哪页
        total: 0, // 总数
        page_size: 10, // 1页显示多少条
        layout: "total, prev, pager, next,jumper", // 翻页属性
      },
    };
  },
  /**计算属性  */
  computed: {},
  /**  方法集合  */
  methods: {
    /* 搜索 */
    handleSearch() {
      let that = this;
      that.paginations.page = 1;
      that.getEntityCustomerList();
    },
    /* 分页 */
    handlePageChange(page) {
      let that = this;
      that.paginations.page = page;
      that.getEntityCustomerList();
    },
    /* 导出 */
    downloadSalePayExcel() {
      let that = this;
      let params = {
        EntityID: that.EntityID,
      };
      that.downloadLoading = true;
      API.entityCustomerExcel(params)
        .then((res) => {
          this.$message.success({
            message: "正在导出",
            duration: "4000",
          });
          const link = document.createElement("a");
          let blob = new Blob([res], { type: "application/octet-stream" });
          link.style.display = "none";
          link.href = URL.createObjectURL(blob);
          link.download = "门店客户报表.xlsx"; //下载的文件名
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
        })
        .finally(function () {
          that.downloadLoading = false;
        });
    },
    /* 获取表格数据 */
    getEntityCustomerList() {
      let that = this;
      that.loading = true;
      let params = {
        EntityID: that.EntityID,
        PageNum: that.paginations.page,
      };
      API.getEntityCustomerList(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.tableData = res.Data.Detail.List;
            that.paginations.page_size = res.Data.Detail.PageSize;
            that.paginations.total = res.Data.Detail.Total;
            that.SumOutputForm = res.Data.SumOutputForm;
          } else {
            this.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },
    /* 表格数据合计 */
    getSummary({ columns }) {
      let sums = [];
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = <span class="font_weight_600">合计</span>;
          return;
        }

        switch (column.property) {
          case "CustomerCount":
            sums[index] = <span class="font_weight_600">{this.SumOutputForm ? this.SumOutputForm.CustomerCount : 0}</span>;
            break;
          case "DealCount":
            sums[index] = <span class="font_weight_600">{this.SumOutputForm ? this.SumOutputForm.DealCount : 0}</span>;
            break;
          case "ManCount":
            sums[index] = <span class="font_weight_600">{this.SumOutputForm ? this.SumOutputForm.ManCount : 0}</span>;
            break;
          case "WomanCount":
            sums[index] = <span class="font_weight_600">{this.SumOutputForm ? this.SumOutputForm.WomanCount : 0}</span>;
            break;
          case "UnknownCount":
            sums[index] = <span class="font_weight_600">{this.SumOutputForm ? this.SumOutputForm.UnknownCount : 0}</span>;
            break;
          case "MemberCount":
            sums[index] = <span class="font_weight_600">{this.SumOutputForm ? this.SumOutputForm.MemberCount : 0}</span>;
            break;
           case "MemberRate":
            sums[index] = <span class="font_weight_600">{this.SumOutputForm && this.SumOutputForm.MemberRate > 0 ? this.SumOutputForm.MemberRate + "%" : 0}</span>;
            break;
          case "UnMemberCount":
            sums[index] = <span class="font_weight_600">{this.SumOutputForm ? this.SumOutputForm.UnMemberCount : 0}</span>;
            break;
           case "UnMemberRate":
            sums[index] = <span class="font_weight_600">{this.SumOutputForm && this.SumOutputForm.UnMemberRate > 0 ? this.SumOutputForm.UnMemberRate + "%" : 0}</span>;
            break;
          case "Count":
            sums[index] = <span class="font_weight_600">{this.SumOutputForm.Level ? this.SumOutputForm.Level[index - 10].Count : 0}</span>;
            break;
        }
      });
      return sums;
    },
    /* 获取等级构成客户数 */
    getCount(item, Level) {
      var countList = Enumerable.from(Level)
        .where((L) => {
          return L.ID == item;
        })
        .toArray();
      if (countList.length > 0) {
        return countList[0].Count;
      } else {
        return 0;
      }
    },
    /* 获取门店 */
    async getStoreList() {
      var that = this;
      let res = await APIStore.getStoreList();
      if (res.StateCode == 200) {
        that.EntityList = res.Data;
      } else {
        that.$message.error({
          message: res.Message,
          duration: 2000,
        });
      }
    },
  },
  /** 创建实例之前执行函数   */
  beforeCreate() {},
  /**  实例创建完成之后  */
  created() {},
  /**  在挂载开始之前被调用  */
  beforeMount() {},
  /**  实例被挂载后调用  */
  mounted() {
    let that = this;
    this.isExport = this.$permission.permission(this.$route.meta.Permission, "Report-Entity-EntityCustomer-Export");
    that.getStoreList();
    that.getEntityCustomerList();
  },
  /**  数据更新 之前 调用  */
  beforeUpdate() {},
  /** 数据更新 完成 调用   */
  updated() {},
  /**  实例销毁之前调用  */
  beforeDestroy() {},
  /**  实例销毁后调用  */
  destroyed() {},
};
</script>

<style lang="scss">
.entityCustomer {
}
</style>
