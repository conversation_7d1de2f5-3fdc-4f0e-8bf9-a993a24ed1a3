/**
 * Created by preference on 2024/03/07
 *  zmx
 */

import * as API from "@/api/index";
export default {
  /**  查询病例分类 */
  medicalRecordCategory_list: (params) => {
    return API.POST("api/medicalRecordCategory/list", params);
  },
  /**  创建病例分类 */
  medicalRecordCategory_create: (params) => {
    return API.POST("api/medicalRecordCategory/create", params);
  },
  /** 更新病例分类  */
  medicalRecordCategory_update: (params) => {
    return API.POST("api/medicalRecordCategory/update", params);
  },
  /** 移动病例分类  */
  medicalRecordCategory_move: (params) => {
    return API.POST("api/medicalRecordCategory/move", params);
  },
};
