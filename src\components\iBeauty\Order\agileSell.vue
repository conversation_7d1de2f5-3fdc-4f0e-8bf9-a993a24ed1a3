<template>
  <div style="height: calc(100% - 63px)">
    <el-row class="sale_content">
      <el-col :span="9" class="project_left">
        <div class="pad_15">
          <el-input
            placeholder="请输入商品名称、别名关键字，按回车搜索"
            prefix-icon="el-icon-search"
            v-model="goodsName"
            clearable
            size="small"
            @keyup.enter.native="searchGoodsClick"
            @clear="clearClick"
          >
            <el-select v-model="typeIndex" slot="prepend" style="width: 100px">
              <el-option label="全部" value="0"></el-option>
              <el-option label="项目" value="1"></el-option>
              <el-option label="产品" value="2"></el-option>
            </el-select>
          </el-input>
        </div>
        <el-tabs v-model="tabPane" @tab-click="handleClick">
          <el-tab-pane label="全部" name="0">
            <el-container>
              <el-main>
                <el-row class="category_project">
                  <el-col :span="6" class="border_right text_center category">
                    <el-scrollbar class="el-scrollbar_height">
                      <div
                        v-for="(item, index) in allCategory"
                        :key="item.GoodsType"
                        :class="[allCategoryIndex == index ? 'category_select' : '', 'pad_10_0', 'border_bottom', 'cursor_pointer']"
                        @click="goodsCategoryChange(item, index)"
                      >
                        {{ item.GoodsTypeName }}
                      </div>
                    </el-scrollbar>
                  </el-col>
                  <el-col :span="18" class="project">
                    <div class="producct_list" v-loading="allLoading">
                      <el-scrollbar class="el-scrollbar_height">
                        <div v-for="product in goodsAll" :key="product.ID" class="border_bottom pad_5_10 cursor_pointer" @click="goodsChange(product)">
                          <div>
                            <span>{{ product.Name }}</span>
                            <span v-if="product.Alias">({{ product.Alias }})</span>
                            <span v-if="allCategoryItem.GoodsType == '30'" class="font_12 color_gray">× {{ product.Amount }}次</span>
                          </div>
                          <div>
                            <span class="color_red">¥ {{ product.Price | toFixed | NumFormat }}</span>
                            <span class="marlt_10 font_12 color_gray" v-if="product.LargessPrice"
                              >赠送：¥ {{ product.LargessPrice | toFixed | NumFormat }}</span
                            >
                            <span class="font_12 color_gray" style="float: right">{{ product.ValidDayName }}</span>
                          </div>
                        </div>
                      </el-scrollbar>
                    </div>
                  </el-col>
                </el-row>
              </el-main>
              <el-footer class="border_top">
                <el-row type="flex" align="middle">
                  <el-col :span="24" class="text_right">
                    <el-pagination
                      background
                      v-if="allPaginations.total > 0"
                      @current-change="handleAllGoodsCurrentChange"
                      :current-page.sync="allPaginations.page"
                      :page-size="allPaginations.page_size"
                      :layout="allPaginations.layout"
                      :total="allPaginations.total"
                      :pager-count="5"
                    ></el-pagination>
                  </el-col>
                </el-row>
              </el-footer>
            </el-container>
          </el-tab-pane>
          <el-tab-pane label="项目" v-if="IsExistProject" name="1">
            <el-container>
              <el-main>
                <el-row class="category_project">
                  <el-col :span="5" class="border_right text_center category">
                    <el-scrollbar class="el-scrollbar_height">
                      <div
                        v-for="(item, index) in projectCategory"
                        :key="index"
                        :class="[projectCategoryIndex == index ? 'category_select' : '', 'pad_10_0', 'border_bottom', 'cursor_pointer']"
                        @click="projectCategoryChange(item, index)"
                      >
                        {{ item.ParentName }}
                      </div>
                    </el-scrollbar>
                  </el-col>
                  <el-col :span="5" class="border_right text_center category">
                    <el-scrollbar class="el-scrollbar_height">
                      <div
                        v-for="(item, index) in projectSecondCategory"
                        :key="index"
                        :class="[projectSecondCategoryIndex == index ? 'category_select' : '', 'pad_10_0', 'border_bottom', 'cursor_pointer']"
                        @click="projectSecondCategoryChange(item, index)"
                      >
                        {{ item.CategoryName }}
                      </div>
                    </el-scrollbar>
                  </el-col>
                  <el-col :span="14" class="project">
                    <div class="project_list" v-loading="projectLoading">
                      <el-scrollbar class="el-scrollbar_height el_scrollbar_project">
                        <div v-for="project in projectList" class="border_bottom pad_5_10 cursor_pointer" :key="project.ID" @click="projectChange(project)">
                          <div>
                            <span>{{ project.Name }}</span>
                            <span v-if="project.Alias">({{ project.Alias }})</span>
                          </div>
                          <div class="color_red">¥ {{ project.Price | toFixed | NumFormat }}</div>
                        </div>
                      </el-scrollbar>
                    </div>
                  </el-col>
                </el-row>
              </el-main>
              <el-footer class="border_top">
                <el-row type="flex" align="middle">
                  <el-col :span="24" class="text_right">
                    <el-pagination
                      background
                      v-if="projectPaginations.total > 0"
                      @current-change="handleProjectCurrentChange"
                      :current-page.sync="projectPaginations.page"
                      :page-size="projectPaginations.page_size"
                      :layout="projectPaginations.layout"
                      :total="projectPaginations.total"
                      :pager-count="5"
                    ></el-pagination>
                  </el-col>
                </el-row>
              </el-footer>
            </el-container>
          </el-tab-pane>
          <el-tab-pane label="产品" v-if="IsExistProduct" name="2"
            ><el-container>
              <el-main
                ><el-row class="category_project">
                  <el-col :span="5" class="border_right text_center category">
                    <el-scrollbar class="el-scrollbar_height">
                      <div
                        v-for="(item, index) in productCategory"
                        :key="index"
                        :class="[productCategoryIndex == index ? 'category_select' : '', 'pad_10_0', 'border_bottom', 'cursor_pointer']"
                        @click="productCategoryChange(item, index)"
                      >
                        {{ item.ParentName }}
                      </div>
                    </el-scrollbar>
                  </el-col>
                  <el-col :span="5" class="border_right text_center category">
                    <el-scrollbar class="el-scrollbar_height">
                      <div
                        v-for="(item, index) in productSecondCategory"
                        :key="index"
                        :class="[productSecondCategoryIndex == index ? 'category_select' : '', 'pad_10_0', 'border_bottom', 'cursor_pointer']"
                        @click="productSecondCategoryChange(item, index)"
                      >
                        {{ item.CategoryName }}
                      </div>
                    </el-scrollbar>
                  </el-col>
                  <el-col :span="14" class="project">
                    <div class="producct_list" v-loading="productLoading">
                      <el-scrollbar class="el-scrollbar_height">
                        <div v-for="product in productList" class="border_bottom pad_5_10 cursor_pointer" :key="product.ID" @click="productChange(product)">
                          <div>
                            <span>{{ product.Name }}</span>
                            <span v-if="product.Alias">({{ product.Alias }})</span>
                          </div>
                          <div class="color_red">¥ {{ product.Price | toFixed | NumFormat }}</div>
                        </div>
                      </el-scrollbar>
                    </div>
                  </el-col>
                </el-row>
              </el-main>
              <el-footer class="border_top">
                <el-row type="flex" align="middle">
                  <el-col :span="24" class="text_right">
                    <el-pagination
                      background
                      v-if="productPaginations.total > 0"
                      @current-change="handleProductCurrentChange"
                      :current-page.sync="productPaginations.page"
                      :page-size="productPaginations.page_size"
                      :layout="productPaginations.layout"
                      :total="productPaginations.total"
                      :pager-count="5"
                    ></el-pagination>
                  </el-col>
                </el-row>
              </el-footer>
            </el-container>
          </el-tab-pane>
        </el-tabs>
      </el-col>
      <el-col :span="15" class="project_right position_relative">
        <el-container style="height: 100%">
          <el-main>
            <el-scrollbar class="el-scrollbar_height">
              <!--项目-->
              <div v-for="(item, index) in selectProject" :key="index + 'sel_Poj'">
                <el-row class="row_header border_bottom">
                  <el-col :span="24">
                    <el-col :span="8">项目</el-col>
                    <el-col :span="6">数量</el-col>
                    <el-col :span="5">金额</el-col>
                    <el-col :span="2">赠送</el-col>
                    <el-col :span="3"></el-col>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="24" class="pad_10 border_bottom">
                    <el-col :span="24">
                      <el-col :span="8">
                        <div>
                          <span>{{ item.Name }}</span>
                          <span v-if="item.Alias">({{ item.Alias }})</span>
                          <span class="marlt_5 color_primary cursor_pointer">
                            <el-button icon="el-icon-edit-outline" type="text" style="padding: unset" @click="editProjectRemarkClick(item)"></el-button>
                          </span>
                        </div>
                      </el-col>
                      <el-col :span="6">
                        <el-input-number
                          :min="1"
                          :max="999"
                          size="mini"
                          style="width: 100px"
                          v-model="item.number"
                          @change="numberChange(item)"
                        ></el-input-number>
                      </el-col>
                      <el-col :span="5">
                        <span class="marrt_10">{{ parseFloat(item.TotalAmount) | toFixed | NumFormat }}</span>
                        <el-button
                          type="primary"
                          icon="el-icon-edit"
                          circle
                          size="mini"
                          @click="savingCardDeductionClick(1, selectProject, item, index)"
                          v-if="!item.IsLargess"
                        ></el-button>
                      </el-col>
                      <el-col :span="2">
                        <el-checkbox v-show="item.IsAllowLargess" v-model="item.IsLargess" @change="largessChange(item)"></el-checkbox>
                      </el-col>
                      <el-col :span="3" :offset="item.IsAllowLargess ? 0 : 2" class="text_right">
                        <el-button
                          v-if="TreatPermission.isTreatConsumable"
                          type="success"
                          icon="el-icon-plus"
                          circle
                          size="mini"
                          @click="addProjecConsumable(`project_${index}`)"
                        ></el-button>
                        <el-button type="danger" icon="el-icon-delete" circle size="mini" @click="removeClick(1, index, item)"></el-button>
                      </el-col>
                    </el-col>

                    <el-col :span="24" style="margin-top: 4px">
                      <el-col :span="4">
                        <div class="color_red">¥ {{ item.Price | toFixed | NumFormat }}</div>
                      </el-col>
                      <el-col :span="20">
                        <div class="text_right">
                          <span class="font_12">支付金额：{{ (item.IsLargess ? 0 : item.PayAmount) | toFixed | NumFormat }}</span>
                          <span class="color_gray font_12 marlt_15" v-if="item.discountPrice != 0">
                            手动改价：
                            <span class="color_red" v-if="item.discountPrice > 0">-{{ item.discountPrice | toFixed | NumFormat }}</span>
                            <span class="color_green" v-else>+{{ mathAbsData(item.discountPrice) | toFixed | NumFormat }}</span>
                          </span>
                          <span class="color_gray font_12 marlt_15" v-if="item.CardDiscountPrice > 0">
                            卡优惠：
                            <span class="color_red">-{{ parseFloat(item.CardDiscountPrice) | toFixed | NumFormat }}</span>
                          </span>
                          <span class="color_gray font_12 marlt_15" v-if="item.CardDeductionAmount > 0">
                            卡抵扣：
                            <span class="color_red">-{{ parseFloat(item.CardDeductionAmount) | toFixed | NumFormat }}</span>
                          </span>

                          <span class="color_gray font_12 marlt_15" v-if="item.MemberPreferentialTotalAmount > 0">
                            会员优惠：
                            <span class="color_red">-{{ parseFloat(item.MemberPreferentialTotalAmount) | toFixed | NumFormat }}</span>
                          </span>
                        </div>
                      </el-col>
                    </el-col>
                  </el-col>
                  <el-col v-if="item.Remark" :span="24" class="pad_10 border_bottom font_12 color_333">备注信息: {{ item.Remark }} </el-col>
                  <!-- 销售经手人 -->
                  <el-col v-if="item.saleHandlerList.length > 0" :span="24" class="border_bottom pad_10 padbm_0">
                    <el-row
                      class="cursor_pointer"
                      v-for="(handler, pIndex) in item.saleHandlerList"
                      :key="pIndex + 'sale_poj'"
                      @click.native="employeeHandleClick(1, selectProject, item, index, true)"
                      type="flex"
                      align="top"
                    >
                      <el-col :span="4">
                        <el-form :inline="true" size="mini" label-position="left">
                          <el-form-item style="margin-bottom: 10px" :label="`${handler.Name}：`"></el-form-item>
                        </el-form>
                      </el-col>
                      <el-col :span="20">
                        <el-form :inline="true" size="mini">
                          <el-form-item
                            style="margin-bottom: 10px"
                            v-for="(employee, handleIndex) in handler.Employee"
                            :key="handleIndex"
                            :label="`${employee.EmployeeName}`"
                          >
                            <el-input
                              class="employee_num"
                              v-model="employee.Discount"
                              size="mini"
                              :min="0"
                              :max="100"
                              type="number"
                              v-on:click.native.stop
                              v-input-fixed
                              @input="handlerPercentChange(handler.Employee, employee)"
                            >
                              <template slot="append">%</template>
                            </el-input>
                            <i
                              class="el-icon-error marlt_5 font_16"
                              style="color: #909399; vertical-align: middle"
                              v-on:click.stop="removeHandleClick(handler, handleIndex)"
                            ></i>
                          </el-form-item>
                        </el-form>
                      </el-col>
                    </el-row>
                  </el-col>
                  <!-- 消耗经手人 -->
                  <el-col v-if="item.consumeHandlerList.length > 0" :span="24" class="border_bottom pad_10 padbm_0">
                    <el-row
                      class="cursor_pointer"
                      v-for="(handler, pIndex) in item.consumeHandlerList"
                      :key="pIndex + 'consume_poj'"
                      @click.native="employeeHandleClick(1, selectProject, item, index, false)"
                      type="flex"
                      align="top"
                    >
                      <el-col :span="4">
                        <el-form :inline="true" size="mini" label-position="left">
                          <el-form-item style="margin-bottom: 10px" :label="`${handler.Name}：`"></el-form-item>
                        </el-form>
                      </el-col>
                      <el-col :span="20">
                        <el-form :inline="true" size="mini">
                          <el-form-item
                            style="margin-bottom: 10px"
                            v-for="(employee, handleIndex) in handler.Employee"
                            :key="handleIndex"
                            :label="`${employee.EmployeeName}`"
                          >
                            <el-input
                              class="employee_num"
                              v-model="employee.Discount"
                              size="mini"
                              :min="0"
                              :max="100"
                              type="number"
                              v-on:click.native.stop
                              v-input-fixed
                              @input="handlerPercentChange(handler.Employee, employee)"
                            >
                              <template slot="append">%</template>
                            </el-input>
                            <i
                              class="el-icon-error marlt_5 font_16"
                              style="color: #909399; vertical-align: middle"
                              v-on:click.stop="removeHandleClick(handler, handleIndex)"
                            ></i>
                          </el-form-item>
                        </el-form>
                      </el-col>
                    </el-row>
                  </el-col>

                  <treat-consumable
                    :ref="`project_${index}`"
                    v-if="TreatPermission.isTreatConsumable"
                    @remove="(event) => removeTreatConsumableClick(event, item, index, 'Project')"
                    @removeAll="(event) => removeAllTreatConsumableClick(event, item, index, 'Project')"
                    @add="(e) => addTreatConsumableClick(e, item, index, 'Project')"
                    :projectID="item.ID"
                    :consumableList="item.Consumable"
                  ></treat-consumable>
                </el-row>
              </div>
              <!--产品-->
              <div v-for="(item, index) in selectProduct" :key="index + 'sel_prd'">
                <el-row class="row_header border_bottom">
                  <el-col :span="24">
                    <el-col :span="8">产品</el-col>
                    <el-col :span="6">数量</el-col>
                    <el-col :span="5">金额</el-col>
                    <el-col :span="2">赠送</el-col>
                    <el-col :span="3"></el-col>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="24" class="pad_10 border_bottom">
                    <el-col :span="24">
                      <el-col :span="8">
                        <div>
                          <span>{{ item.Name }}</span>
                          <span v-if="item.Alias">({{ item.Alias }})</span>
                          <span class="marlt_5 color_primary cursor_pointer">
                            <el-button icon="el-icon-edit-outline" type="text" style="padding: unset" @click="editProjectRemarkClick(item)"></el-button>
                          </span>
                        </div>
                      </el-col>
                      <el-col :span="6">
                        <el-input-number
                          :min="1"
                          :max="999"
                          size="mini"
                          style="width: 100px"
                          v-model="item.number"
                          @change="numberChange(item)"
                        ></el-input-number>
                      </el-col>
                      <el-col :span="5">
                        <span class="marrt_15">{{ parseFloat(item.TotalAmount) | toFixed | NumFormat }}</span>
                        <el-button
                          type="primary"
                          icon="el-icon-edit"
                          circle
                          size="mini"
                          @click="savingCardDeductionClick(2, selectProduct, item, index)"
                          v-if="!item.IsLargess"
                        ></el-button> 
                      </el-col>
                      <el-col :span="2" v-show="item.IsAllowLargess">
                        <el-checkbox v-model="item.IsLargess" @change="largessChange(item)"></el-checkbox>
                      </el-col>
                      <el-col :span="3" :offset="item.IsAllowLargess ? 0 : 2" class="text_right">
                        <el-button type="danger" icon="el-icon-delete" circle size="mini" @click="removeClick(2, index, item)"></el-button>
                      </el-col>
                    </el-col>
                    <el-col :span="24" style="margin-top: 4px">
                      <el-col :span="4">
                        <div class="color_red">¥ {{ item.Price | toFixed | NumFormat }}</div>
                      </el-col>
                      <el-col :span="20">
                        <div class="text_right font_12">
                          <span class="font_12">支付金额：{{ (item.IsLargess ? 0 : item.PayAmount) | toFixed | NumFormat }}</span>
                          <span class="color_gray font_12 marlt_15" v-if="item.discountPrice != 0">
                            手动改价：
                            <span class="color_red" v-if="item.discountPrice > 0">-{{ item.discountPrice | toFixed | NumFormat }}</span>
                            <span class="color_green" v-else>+{{ mathAbsData(item.discountPrice) | toFixed | NumFormat }}</span>
                          </span>
                          <span class="color_gray font_12 marlt_15" v-if="item.CardDiscountPrice > 0">
                            储值卡优惠：
                            <span class="color_red">- {{ item.CardDiscountPrice | toFixed | NumFormat }}</span>
                          </span>
                          <span class="color_gray font_12 marlt_15" v-if="item.CardDeductionAmount > 0">
                            储值卡抵扣：
                            <span class="color_red">- {{ item.CardDeductionAmount | toFixed | NumFormat }}</span>
                          </span>

                          <span class="color_gray font_12 marlt_15" v-if="item.MemberPreferentialTotalAmount > 0">
                            会员优惠：
                            <span class="color_red">-{{ parseFloat(item.MemberPreferentialTotalAmount) | toFixed | NumFormat }}</span>
                          </span>
                        </div>
                      </el-col>
                    </el-col>
                  </el-col>
                  <el-col v-if="item.Remark" :span="24" class="pad_10 border_bottom font_12 color_333">备注信息: {{ item.Remark }} </el-col>
                  <!-- 销售经手人 -->
                  <el-col v-if="item.saleHandlerList.length > 0" :span="24" class="border_bottom pad_10 padbm_0">
                    <el-row
                      class="cursor_pointer"
                      v-for="(handler, pIndex) in item.saleHandlerList"
                      :key="pIndex + 'sale_poj'"
                      @click.native="employeeHandleClick(2, selectProduct, item, index, true)"
                      type="flex"
                      align="top"
                    >
                      <el-col :span="4">
                        <el-form :inline="true" size="mini" label-position="left">
                          <el-form-item style="margin-bottom: 10px" :label="`${handler.Name}：`"></el-form-item>
                        </el-form>
                      </el-col>
                      <el-col :span="20">
                        <el-form :inline="true" size="mini">
                          <el-form-item
                            style="margin-bottom: 10px"
                            v-for="(employee, handleIndex) in handler.Employee"
                            :key="handleIndex"
                            :label="`${employee.EmployeeName}`"
                          >
                            <el-input
                              class="employee_num"
                              v-model="employee.Discount"
                              size="mini"
                              :min="0"
                              :max="100"
                              type="number"
                              v-on:click.native.stop
                              v-input-fixed
                              @input="handlerPercentChange(handler.Employee, employee)"
                            >
                              <template slot="append">%</template>
                            </el-input>
                            <i
                              class="el-icon-error marlt_5 font_16"
                              style="color: #909399; vertical-align: middle"
                              v-on:click.stop="removeHandleClick(handler, handleIndex)"
                            ></i>
                          </el-form-item>
                        </el-form>
                      </el-col>
                    </el-row>
                  </el-col>
                  <!-- 消耗经手人 -->
                  <el-col v-if="item.consumeHandlerList.length > 0" :span="24" class="border_bottom pad_10 padbm_0">
                    <el-row
                      class="cursor_pointer"
                      v-for="(handler, pIndex) in item.consumeHandlerList"
                      :key="pIndex + 'consume_poj'"
                      @click.native="employeeHandleClick(2, selectProduct, item, index, false)"
                      type="flex"
                      align="top"
                    >
                      <el-col :span="4">
                        <el-form :inline="true" size="mini" label-position="left">
                          <el-form-item style="margin-bottom: 10px" :label="`${handler.Name}：`"></el-form-item>
                        </el-form>
                      </el-col>
                      <el-col :span="20">
                        <el-form :inline="true" size="mini">
                          <el-form-item
                            style="margin-bottom: 10px"
                            v-for="(employee, handleIndex) in handler.Employee"
                            :key="handleIndex"
                            :label="`${employee.EmployeeName}`"
                          >
                            <el-input
                              class="employee_num"
                              v-model="employee.Discount"
                              size="mini"
                              :min="0"
                              :max="100"
                              type="number"
                              v-on:click.native.stop
                              v-input-fixed
                              @input="handlerPercentChange(handler.Employee, employee)"
                            >
                              <template slot="append">%</template>
                            </el-input>
                            <i
                              class="el-icon-error marlt_5 font_16"
                              style="color: #909399; vertical-align: middle"
                              v-on:click.stop="removeHandleClick(handler, handleIndex)"
                            ></i>
                          </el-form-item>
                        </el-form>
                      </el-col>
                    </el-row>
                  </el-col>
                </el-row>
              </div>
            </el-scrollbar>
          </el-main>
          <transition>
            <div v-show="showRemark" class="orderInfoRemark" @click="showRemark = false">
              <div @click.stop class="infoRemarContent">
                <el-row @click.native="hiddenInfoRemarkClick" style="height: 40px" class="dis_flex flex_y_center">
                  <el-col :span="12">订单信息</el-col>
                  <el-col :span="12" class="text_right">
                    <el-button @click="hiddenInfoRemarkClick" type="text">收起</el-button>
                    <i class="el-icon-arrow-down color_main font_16 text-bold"></i>
                  </el-col>
                </el-row>
                <div class="back_f7f8fa" style="padding: 20px 16px 2px 16px">
                  <el-form label-width="80px" size="small">
                    <el-form-item v-show="(selectProduct && selectProduct.length > 0) || (selectProject && selectProject.length > 0)" label="批量添加：">
                      <el-button @click="showSelectAllHandlerClick" icon="el-icon-plus">经手人</el-button>
                    </el-form-item>
                    <el-form-item label="订单备注：">
                      <el-input type="textarea" :rows="3" placeholder="请输入备注信息" v-model="Remark"></el-input>
                    </el-form-item>
                  </el-form>
                </div>
              </div>
            </div>
          </transition>
          <el-row @click.native="showRemarkClick" style="height: 40px" class="dis_flex flex_y_center pad_0_10 back_f8 font_13">
            <el-col :span="12" class="color_666">订单信息<span class="font_12 color_999">(已折叠)</span></el-col>
            <el-col :span="12" class="text_right">
              <el-button type="text">展开</el-button>
              <i class="el-icon-arrow-up color_main font_16 text-bold"></i>
            </el-col>
          </el-row>
          <el-footer class="border_top">
            <el-row type="flex" align="middle">
              <el-col :span="21">
                <span class="font_14 color_maroon marrt_15">待收款金额：¥ {{ PayAmount | toFixed | NumFormat }}</span>
                <!-- <el-button  type="text">修改</el-button> -->
                <span class="marlt_5 color_primary cursor_pointer">
                  <el-button @click="changOrderAmountClick" icon="el-icon-edit" type="text" style="padding: unset"></el-button>
                </span>
              </el-col>
              <!-- <el-col :span="15">
                <el-input type="textarea" :rows="1" placeholder="请输入备注信息" v-model="Remark"></el-input>
              </el-col> -->
              <el-col :span="3" class="text_right">
                <el-button type="primary" size="small" @click="billClick">收款</el-button>
              </el-col>
            </el-row>
          </el-footer>
        </el-container>
      </el-col>
    </el-row>

    <!--经手人-->
    <el-dialog title="经手人" :visible.sync="dialogVisible" width="800px">
      <div slot="title">
        <el-radio-group size="small" v-model="tabHandlePosition" @change="hangeHandleType">
          <el-radio-button label="saleHandler">销售经手人</el-radio-button>
          <el-radio-button label="consumeHandler">消耗经手人</el-radio-button>
        </el-radio-group>
      </div>
      <div>
        <el-row class="padbm_10">
          <el-col :span="8">
            <el-input placeholder="请输入员工编号、姓名" prefix-icon="el-icon-search" v-model="handlerName" size="small" clearable></el-input>
          </el-col>
        </el-row>
        <el-tabs v-model="tabHandle">
          <el-tab-pane :label="handler.Name" :name="`${index}`" v-for="(handler, index) in getHandlerList()" :key="index">
            <el-row style="max-height: 300px; overflow-y: auto">
              <el-col
                :span="12"
                v-for="item in handler.Employee.filter(
                  (item) =>
                    !handlerName ||
                    item.EmployeeName.toLowerCase().includes(handlerName.toLowerCase()) ||
                    item.EmployeeID.toLowerCase().includes(handlerName.toLowerCase())
                )"
                :key="item.EmployeeID"
                class="marbm_10 dis_flex flex_y_center"
              >
                <el-checkbox v-model="item.Checked" @change="handlerCheckedChange(handler.Employee, item)">
                  <span class="marrt_10">{{ item.EmployeeName }} [{{ item.EmployeeID }}]</span>
                </el-checkbox>
                <el-input
                  placeholder
                  v-model="item.Discount"
                  style="width: 120px"
                  type="number"
                  size="mini"
                  v-input-fixed
                  min="0"
                  max="100"
                  @input="handlerPercentChange(handler.Employee, item, 'dialog')"
                >
                  <template slot="append">%</template>
                </el-input>
              </el-col>
            </el-row>
          </el-tab-pane>
        </el-tabs>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false" size="small">取 消</el-button>
        <el-button type="primary" size="small" @click="submitHandleClick" v-prevent-click>确 定</el-button>
      </div>
    </el-dialog>

    <!--结账-->
    <el-dialog title="收银台" :visible.sync="dialogBill" :close-on-click-modal="false" :close-on-press-escape="false" :show-close="false" width="900px">
      <div>
        <el-row>
          <el-col :span="8">
            <el-scrollbar class="el-scrollbar_height" style="height: 500px">
              <div class="marrt_10">
                <div class="dis_flex">
                  <span class="flex_box text_center font_16" style="line-height: 32px">{{ entityName }}</span>
                </div>
                <el-divider>
                  <span class="font_12 color_gray">订单信息</span>
                </el-divider>
                <div class="dis_flex">
                  <span class="flex_box font_12 color_gray text-left line_height_24">下单时间</span>
                  <span class="font_12 text_right line_height_24" style="flex: 3">{{ getBillDate() | dateFormat('YYYY-MM-DD HH:mm') }}</span>
                </div>
                <div class="dis_flex">
                  <span class="flex_box font_12 color_gray text-left line_height_24">会员姓名</span>
                  <span class="flex_box font_12 text_right line_height_24">{{ customerFullName }}</span>
                </div>
                <div class="dis_flex">
                  <span class="flex_box font_12 color_gray text-left line_height_24">会员手机号</span>
                  <span class="flex_box font_12 text_right line_height_24">{{ customerPhoneNumber | hidephone }}</span>
                </div>
                <div class="dis_flex">
                  <span class="flex_box font_12 color_gray text-left line_height_24">开单人</span>
                  <span class="flex_box font_12 text_right line_height_24">{{ userName }}</span>
                </div>
                <el-divider>
                  <span class="font_12 color_gray">消费明细</span>
                </el-divider>
                <template v-for="(item, index) in selectProject">
                  <div :key="index + item.type + item.ID">
                    <div class="dis_flex">
                      <span class="font_12 color_gray text-left line_height_24" style="flex: 2">
                        {{ index + 1 }} {{ item.Name }}
                        <span class="font_12" size="mini" v-if="item.IsLargess">(赠)</span>
                      </span>
                      <span class="font_12 text_right line_height_24" style="flex: 1">￥{{ item.Price | toFixed | NumFormat }}</span>
                    </div>
                    <div class="dis_flex">
                      <span class="font_12 color_gray text-left line_height_24 marlt_10" style="flex: 2">数量</span>
                      <span class="font_12 text_right line_height_24" style="flex: 1">{{ item.number }}</span>
                    </div>
                    <div class="dis_flex" v-if="item.discountPrice != 0">
                      <span class="font_12 color_gray text-left line_height_24 marlt_10" style="flex: 2">手动改价</span>
                      <span class="font_12 text_right line_height_24" style="flex: 1" v-if="item.discountPrice > 0"
                        >-￥{{ item.discountPrice | toFixed | NumFormat }}</span
                      >
                      <span class="font_12 text_right line_height_24" style="flex: 1" v-else
                        >+￥{{ mathAbsData(item.discountPrice) | toFixed | NumFormat }}</span
                      >
                    </div>
                    <div class="dis_flex" v-if="item.CardDiscountPrice != 0">
                      <span class="font_12 color_gray text-left line_height_24 marlt_10" style="flex: 2">卡优惠</span>
                      <span class="font_12 text_right line_height_24" style="flex: 1">-￥{{ item.CardDiscountPrice | toFixed | NumFormat }}</span>
                    </div>
                    <div class="dis_flex">
                      <span class="font_12 color_gray text-left line_height_24 marlt_10" style="flex: 2">小计</span>
                      <span class="font_12 text_right line_height_24" style="flex: 1" v-if="item.IsLargess">￥0.00</span>
                      <span class="font_12 text_right line_height_24" style="flex: 1" v-else>￥{{ item.TotalAmount | toFixed | NumFormat }}</span>
                    </div>
                  </div>
                </template>
                <template v-for="(item, index) in selectSavingCard">
                  <div :key="index + item.type + item.ID">
                    <div class="dis_flex">
                      <span class="font_12 color_gray text-left line_height_24" style="flex: 2">{{ index + 1 + selectProject.length }} {{ item.Name }}</span>
                      <span class="font_12 text_right line_height_24" style="flex: 1">￥{{ (item.Amount / item.number).toFixed(2) | NumFormat }}</span>
                    </div>
                    <div class="dis_flex" v-if="item.LargessPrice > 0">
                      <span class="font_12 color_gray text-left line_height_24 marlt_10" style="flex: 2">充值赠送</span>
                      <span class="font_12 text_right line_height_24" style="flex: 1">￥{{ (item.LargessPrice / item.number).toFixed(2) | NumFormat }}</span>
                    </div>
                    <div class="dis_flex">
                      <span class="font_12 color_gray text-left line_height_24 marlt_10" style="flex: 2">数量</span>
                      <span class="font_12 text_right line_height_24" style="flex: 1">{{ item.number }}</span>
                    </div>
                    <div class="dis_flex">
                      <span class="font_12 color_gray text-left line_height_24 marlt_10" style="flex: 2">小计</span>
                      <span class="font_12 text_right line_height_24" style="flex: 1">￥{{ item.Amount | toFixed | NumFormat }}</span>
                    </div>
                  </div>
                </template>
                <template v-for="(item, index) in selectTimeCard">
                  <div :key="index + item.type + item.ID">
                    <div class="dis_flex">
                      <span class="font_12 color_gray text-left line_height_24" style="flex: 2">
                        {{ index + 1 + selectProject.length + selectSavingCard.length }}
                        {{ item.Name }}
                        <span class="font_12" size="mini" v-if="item.IsLargess">(赠)</span>
                      </span>
                      <span class="font_12 text_right line_height_24" style="flex: 1">￥{{ item.Price | toFixed | NumFormat }}</span>
                    </div>
                    <div class="dis_flex">
                      <span class="font_12 color_gray text-left line_height_24 marlt_10" style="flex: 2">数量</span>
                      <span class="font_12 text_right line_height_24" style="flex: 1">{{ item.number }}</span>
                    </div>
                    <div class="dis_flex" v-if="item.discountPrice != 0">
                      <span class="font_12 color_gray text-left line_height_24 marlt_10" style="flex: 2">手动改价</span>
                      <span class="font_12 text_right line_height_24" style="flex: 1" v-if="item.discountPrice > 0"
                        >-￥{{ item.discountPrice | toFixed | NumFormat }}</span
                      >
                      <span class="font_12 text_right line_height_24" style="flex: 1" v-else
                        >+￥{{ mathAbsData(item.discountPrice) | toFixed | NumFormat }}</span
                      >
                    </div>
                    <div class="dis_flex" v-if="item.CardDiscountPrice != 0">
                      <span class="font_12 color_gray text-left line_height_24 marlt_10" style="flex: 2">卡优惠</span>
                      <span class="font_12 text_right line_height_24" style="flex: 1">-￥{{ item.CardDiscountPrice | toFixed | NumFormat }}</span>
                    </div>
                    <div class="dis_flex">
                      <span class="font_12 color_gray text-left line_height_24 marlt_10" style="flex: 2">小计</span>
                      <span class="font_12 text_right line_height_24" style="flex: 1" v-if="item.IsLargess">￥0.00</span>
                      <span class="font_12 text_right line_height_24" style="flex: 1" v-else>￥{{ item.TotalAmount | toFixed | NumFormat }}</span>
                    </div>
                  </div>
                </template>
                <template v-for="(item, index) in selectGeneralCard">
                  <div :key="index + item.type + item.ID">
                    <div class="dis_flex">
                      <span class="font_12 color_gray text-left line_height_24" style="flex: 2">
                        {{ index + 1 + selectProject.length + selectSavingCard.length + selectTimeCard.length }}
                        {{ item.Name }}
                        <span class="font_12" size="mini" v-if="item.IsLargess">(赠)</span>
                      </span>
                      <span class="font_12 text_right line_height_24" style="flex: 1">￥{{ item.Price | toFixed | NumFormat }}</span>
                    </div>
                    <div class="dis_flex">
                      <span class="font_12 color_gray text-left line_height_24 marlt_10" style="flex: 2">数量</span>
                      <span class="font_12 text_right line_height_24" style="flex: 1">{{ item.number }}</span>
                    </div>
                    <div class="dis_flex" v-if="item.discountPrice != 0">
                      <span class="font_12 color_gray text-left line_height_24 marlt_10" style="flex: 2">手动改价</span>
                      <span class="font_12 text_right line_height_24" style="flex: 1" v-if="item.discountPrice > 0"
                        >-￥{{ item.discountPrice | toFixed | NumFormat }}</span
                      >
                      <span class="font_12 text_right line_height_24" style="flex: 1" v-else
                        >+￥{{ mathAbsData(item.discountPrice) | toFixed | NumFormat }}</span
                      >
                    </div>
                    <div class="dis_flex" v-if="item.CardDiscountPrice != 0">
                      <span class="font_12 color_gray text-left line_height_24 marlt_10" style="flex: 2">卡优惠</span>
                      <span class="font_12 text_right line_height_24" style="flex: 1">-￥{{ item.CardDiscountPrice | toFixed | NumFormat }}</span>
                    </div>
                    <div class="dis_flex">
                      <span class="font_12 color_gray text-left line_height_24 marlt_10" style="flex: 2">小计</span>
                      <span class="font_12 text_right line_height_24" style="flex: 1" v-if="item.IsLargess">￥0.00</span>
                      <span class="font_12 text_right line_height_24" style="flex: 1" v-else>￥{{ item.TotalAmount | toFixed | NumFormat }}</span>
                    </div>
                  </div>
                </template>
                <template v-for="(item, index) in selectProduct">
                  <div :key="index + item.type + item.ID">
                    <div class="dis_flex">
                      <span class="font_12 color_gray text-left line_height_24" style="flex: 2">
                        {{ index + 1 + selectProject.length + selectSavingCard.length + selectTimeCard.length + selectGeneralCard.length }}
                        {{ item.Name }}
                        <span class="font_12" size="mini" v-if="item.IsLargess">(赠)</span>
                      </span>
                      <span class="font_12 text_right line_height_24" style="flex: 1">￥{{ item.Price | toFixed | NumFormat }}</span>
                    </div>
                    <div class="dis_flex">
                      <span class="font_12 color_gray text-left line_height_24 marlt_10" style="flex: 2">数量</span>
                      <span class="font_12 text_right line_height_24" style="flex: 1">{{ item.number }}</span>
                    </div>
                    <div class="dis_flex" v-if="item.discountPrice != 0">
                      <span class="font_12 color_gray text-left line_height_24 marlt_10" style="flex: 2">手动改价</span>
                      <span class="font_12 text_right line_height_24" style="flex: 1" v-if="item.discountPrice > 0"
                        >-￥{{ item.discountPrice | toFixed | NumFormat }}</span
                      >
                      <span class="font_12 text_right line_height_24" style="flex: 1" v-else
                        >+￥{{ mathAbsData(item.discountPrice) | toFixed | NumFormat }}</span
                      >
                    </div>
                    <div class="dis_flex" v-if="item.CardDiscountPrice != 0">
                      <span class="font_12 color_gray text-left line_height_24 marlt_10" style="flex: 2">卡优惠</span>
                      <span class="font_12 text_right line_height_24" style="flex: 1">-￥{{ item.CardDiscountPrice | toFixed | NumFormat }}</span>
                    </div>
                    <div class="dis_flex">
                      <span class="font_12 color_gray text-left line_height_24 marlt_10" style="flex: 2">小计</span>
                      <span class="font_12 text_right line_height_24" style="flex: 1" v-if="item.IsLargess">￥0.00</span>
                      <span class="font_12 text_right line_height_24" style="flex: 1" v-else>￥{{ item.TotalAmount | toFixed | NumFormat }}</span>
                    </div>
                  </div>
                </template>
                <template v-for="(item, index) in selectPackageCard">
                  <div :key="index + item.type + item.ID">
                    <div class="dis_flex">
                      <span class="font_12 color_gray text-left line_height_24" style="flex: 2">
                        {{ index + 1 + selectProject.length + selectSavingCard.length + selectTimeCard.length + selectGeneralCard.length }}
                        {{ item.Name }}
                        <span class="font_12" size="mini" v-if="item.IsLargess">(赠)</span>
                      </span>
                      <span class="font_12 text_right line_height_24" style="flex: 1">￥{{ item.Price | toFixed | NumFormat }}</span>
                    </div>
                    <div class="dis_flex">
                      <span class="font_12 color_gray text-left line_height_24 marlt_10" style="flex: 2">数量</span>
                      <span class="font_12 text_right line_height_24" style="flex: 1">{{ item.number }}</span>
                    </div>
                    <div class="dis_flex" v-if="item.discountPrice != 0">
                      <span class="font_12 color_gray text-left line_height_24 marlt_10" style="flex: 2">手动改价</span>
                      <span class="font_12 text_right line_height_24" style="flex: 1" v-if="item.discountPrice > 0"
                        >-￥{{ item.discountPrice | toFixed | NumFormat }}</span
                      >
                      <span class="font_12 text_right line_height_24" style="flex: 1" v-else
                        >+￥{{ mathAbsData(item.discountPrice) | toFixed | NumFormat }}</span
                      >
                    </div>
                    <div class="dis_flex" v-if="item.CardDiscountPrice != 0">
                      <span class="font_12 color_gray text-left line_height_24 marlt_10" style="flex: 2">卡优惠</span>
                      <span class="font_12 text_right line_height_24" style="flex: 1">-￥{{ item.CardDiscountPrice | toFixed | NumFormat }}</span>
                    </div>
                    <div class="dis_flex">
                      <span class="font_12 color_gray text-left line_height_24 marlt_10" style="flex: 2">小计</span>
                      <span class="font_12 text_right line_height_24" style="flex: 1" v-if="item.IsLargess">￥0.00</span>
                      <span class="font_12 text_right line_height_24" style="flex: 1" v-else>￥{{ item.TotalAmount | toFixed | NumFormat }}</span>
                    </div>
                  </div>
                </template>
                <el-divider class="sell-el-divider"></el-divider>
                <div class="dis_flex">
                  <span class="flex_box font_12 color_gray text-left line_height_24">合计</span>
                  <span class="flex_box font_12 text_right line_height_24"
                    >￥{{ (parseFloat(Amount) + parseFloat(PricePreferentialAmount) + parseFloat(CardPreferentialAmount)).toFixed(2) | NumFormat }}</span
                  >
                </div>
                <div class="dis_flex" v-if="PricePreferentialAmount != 0">
                  <span class="flex_box font_12 color_gray text-left line_height_24">手动改价</span>
                  <span class="flex_box font_12 text_right line_height_24" v-if="PricePreferentialAmount > 0"
                    >-￥{{ PricePreferentialAmount | toFixed | NumFormat }}</span
                  >
                  <span class="flex_box font_12 text_right line_height_24" v-else>+￥{{ mathAbsData(PricePreferentialAmount) | toFixed | NumFormat }}</span>
                </div>
                <div class="dis_flex" v-if="CardPreferentialAmount > 0">
                  <span class="flex_box font_12 color_gray text-left line_height_24">卡优惠</span>
                  <span class="flex_box font_12 text_right line_height_24">-￥{{ CardPreferentialAmount | toFixed | NumFormat }}</span>
                </div>
                <div class="dis_flex">
                  <span class="flex_box font_12 color_gray text-left line_height_24">订单金额</span>
                  <span class="flex_box font_12 text_right line_height_24">￥{{ Amount | toFixed | NumFormat }}</span>
                </div>
              </div>
            </el-scrollbar>
          </el-col>
          <el-col :span="16">
            <el-row type="flex" align="middle" class="dialog_bill_detail">
              <el-col :span="24">
                <div class="marbm_10">
                  <span class="font_20">待收款：</span>
                  <span class="font_20">¥{{ PayAmount | toFixed | NumFormat }}</span>
                </div>
                <div>
                  <span>订单金额：¥{{ Amount | toFixed | NumFormat }}</span>
                  <span v-if="ArrearAmount > 0" class="color_gray font_12 marlt_10">
                    欠款：
                    <span class="color_red">-¥{{ ArrearAmount | toFixed | NumFormat }}</span>
                  </span>
                  <span class="color_gray font_12 marlt_10" v-if="(parseFloat(CardDeductionAmount) + parseFloat(cardDeductionAmount)).toFixed(2) > 0">
                    <span>
                      卡抵扣：
                      <span class="color_red">-¥{{ (parseFloat(CardDeductionAmount) + parseFloat(cardDeductionAmount)).toFixed(2) | NumFormat }}</span>
                    </span>
                  </span>
                  <span v-if="PayCashAmount > 0" class="color_gray font_12 marlt_10">
                    付款：
                    <span class="color_red">-¥{{ PayCashAmount | toFixed | NumFormat }}</span>
                  </span>
                </div>
              </el-col>
            </el-row>
            <el-scrollbar class="el-scrollbar_height" style="height: 415px">
              <div class="tip" style="margin-top: 10px; margin-bottom: 0px" v-if="(savingCardAllGoods.length > 0) & (savingCardPrice > 0)">
                <i class="el-icon-warning-outline"></i>
                储值卡支付金额为￥{{ savingCardPrice | toFixed | NumFormat }}，且不能卡抵扣支付。
              </div>
              <el-table :data="savingCardAllGoods" class="saving_discount martp_10" v-if="savingCardAllGoods.length > 0" :show-header="false" size="small">
                <el-table-column type="selection" width="55">
                  <template slot-scope="scope">
                    <el-checkbox v-model="scope.row.checked" @change="savingCheckedAllChange(scope.row)"></el-checkbox>
                  </template>
                </el-table-column>
                <el-table-column label="商品信息">
                  <template slot-scope="scope">
                    <div>{{ scope.row.SavingCardName }}</div>
                    <div>可用金额：¥ {{ scope.row.TotalPrice | toFixed | NumFormat }}</div>
                    <div class="font_12 color_999">
                      本金：¥ {{ scope.row.Balance | toFixed | NumFormat }} 赠额：¥
                      {{ scope.row.LargessBalance | toFixed | NumFormat }}
                    </div>
                  </template>
                </el-table-column>
                <el-table-column label="卡扣金额">
                  <template slot-scope="scope">
                    <el-input
                      size="small"
                      type="number"
                      v-model="scope.row.TotalAmount"
                      v-input-fixed
                      placeholder="请输入抵扣金额"
                      @input="savingPriceAllChange(scope.row)"
                      :disabled="!scope.row.checked"
                    ></el-input>
                  </template>
                </el-table-column>
              </el-table>
              <el-table :data="payList" size="small" class="padtp_15" :show-header="false">
                <el-table-column prop="payName" label="选择收款方式">
                  <template slot-scope="scope">
                    <el-select
                      v-model="scope.row.PayMethodID"
                      placeholder="选择收款方式"
                      size="small"
                      clearable
                      filterable
                      @change="payMethodChange(scope.row)"
                    >
                      <el-option v-for="item in payTypeList" :key="item.ID" :label="item.Name" :value="item.ID"></el-option>
                    </el-select>
                  </template>
                </el-table-column>
                <el-table-column prop="price" label="支付金额">
                  <template slot-scope="scope">
                    <el-input
                      clearable
                      v-model="scope.row.Amount"
                      size="small"
                      type="number"
                      v-input-fixed
                      placeholder="请输入收款金额"
                      :disabled="scope.row.PayMethodID == ''"
                      @input="payPriceChange(scope.row)"
                    ></el-input>
                  </template>
                </el-table-column>
                <el-table-column prop="address" label="操作" width="100">
                  <template slot-scope="scope">
                    <el-button
                      type="danger"
                      icon="el-icon-close"
                      circle
                      size="mini"
                      @click="removePayClick(scope.$index)"
                      v-if="scope.$index + 1 != 1"
                    ></el-button>
                    <el-button type="primary" icon="el-icon-plus" circle size="mini" @click="addPayclick" v-if="scope.$index + 1 == payList.length"></el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-scrollbar>
          </el-col>
        </el-row>
      </div>
      <div slot="footer" class="dialog-footer">
        <!-- <el-button @click="dialogBill = false" size="small" :disabled="modalLoading">取 消</el-button> -->
        <el-button @click="onCancelSubmitPay" size="small" :disabled="modalLoading">取 消</el-button>
        <el-button type="primary" @click="submitPayClick" :loading="modalLoading" v-prevent-click size="small">确定收款</el-button>
      </div>
    </el-dialog>

    <!--储值卡抵扣(部分商品)-->
    <el-dialog title="商品价格调整/储值卡抵扣" :visible.sync="dialogDeduction" width="700px">
      <el-row class="border pad_10 marbm_10 radius5" style="height: 65px">
        <el-col :span="12" class="line_height_23">
          {{ selectGood.Name }}
          <span v-if="selectGood.Alias">({{ selectGood.Alias }})</span>
        </el-col>
        <el-col :span="6" class="color_gray font_13 line_height_23">¥ {{ selectGood.Price | toFixed | NumFormat }} × {{
          selectGood.number }}</el-col>
        <el-col :span="6" class="line_height_23">
          <div>
            ¥
            {{ (selectGood.Price * selectGood.number - selectGood.DeductionProjectAmount -
              selectGood.MemberPreferentialTotalAmount).toFixed(2) | NumFormat }}
          </div>
          <div class="color_gray font_12 line_height_23"
            v-show="selectGood.DeductionProjectAmount != 0 || selectGood.MemberPreferentialTotalAmount != 0">
            合计优惠：
            <span class="color_red"
              v-if="selectGood.DeductionProjectAmount > 0 || selectGood.MemberPreferentialTotalAmount > 0">-{{ getSaveCardDiscountAmount(selectGood.DeductionProjectAmount, selectGood.MemberPreferentialTotalAmount
              ) |
                toFixed | NumFormat }}</span>
            <span class="color_green" v-else>+{{ mathAbsData(selectGood.DeductionProjectAmount) | toFixed | NumFormat
              }}</span>
          </div>
        </el-col>
      </el-row>

      <el-row v-if="selectGood.isShowSelectMemberAmout" class="margin_top_10 marbm_10" type="flex" align="middle">
        <el-col :span="7">
          <el-select v-model="selectGood.isShowMemberAmout" @change="changeSelectMemberDiscountAmout(selectGood)" placeholder="请选择" size="mini">
            <el-option label="不使用会员折扣" :value="false"></el-option>
            <el-option label="使用会员折扣" :value="true"></el-option>
          </el-select>
        </el-col>
        <el-col :span="12" :offset="1">
          <span class="color_gray font_12" v-if="selectGood.MemberPreferentialAmount > 0 && selectGood.isShowMemberAmout">
            会员优惠：
            <span class="color_red">
              <span v-if="selectGood.MemberAmoutPriceType == 1">折扣：（{{ selectGood.MemberAmoutDiscountPrice * 10 }}折）</span>
              <span v-if="selectGood.MemberAmoutPriceType == 2">折扣价：（{{ selectGood.MemberAmoutDiscountPrice }}）</span>
              <span>-{{ parseFloat(selectGood.MemberPreferentialTotalAmount) | toFixed | NumFormat }}</span>
            </span>
          </span>
        </el-col>
      </el-row>
      <template v-if="showModifyPrices && selectGood.IsModifyPrice && !selectGood.isShowMemberAmout">
        <el-row class="border pad_10 marbm_10 martp_10 radius5" type="flex" align="middle" v-if="selectGood.isModify">
          <el-col :span="18">
            <span>手动改价:</span>
            <span class="mar_0_15">¥ {{ selectGood.Amount | toFixed | NumFormat }}</span>
            <el-button type="text" @click="selectGood.isModify = false" size="mini">改价</el-button>
          </el-col>
          <el-col :span="6" class="color_gray font_12" v-if="selectGood.discountPrice != 0">
            <span>手动改价：</span>
            <span class="color_red" v-if="selectGood.discountPrice > 0">-{{ selectGood.discountPrice | toFixed | NumFormat }}</span>
            <span class="color_green" v-else>+{{ mathAbsData(selectGood.discountPrice) | toFixed | NumFormat }}</span>
          </el-col>
        </el-row>
        <el-row class="border pad_10 marbm_10 martp_10 radius5" type="flex" align="middle" v-else>
          <el-col :span="4">
            <span>手动改价</span>
          </el-col>
          <el-col :span="14">
            <el-row type="flex" align="middle">
              <el-col :span="10">
                <el-input size="mini" v-model="selectGood.Amount" type="number" v-input-fixed @input="amountChange(selectGood)">
                  <template slot="prepend">¥</template>
                </el-input>
              </el-col>
              <el-col :span="2" class="text_center">
                <i class="el-icon-sort"></i>
              </el-col>
              <el-col :span="10">
                <el-input size="mini" v-model="selectGood.discount" type="number" v-input-fixed @input="discountChange(selectGood)">
                  <template slot="append">%</template>
                </el-input>
              </el-col>
            </el-row>
          </el-col>
          <el-col :span="6" class="text_right">
            <el-button type="primary" size="mini" @click="modifyChange(selectGood)">确认</el-button>
          </el-col>
        </el-row>
      </template>

      <el-table class="saving_discount radius5" :data="savingCardAll" size="small" v-if="savingCardAll.length > 0" :show-header="false" max-height="270px">
        <el-table-column type="selection" width="55">
          <template slot-scope="scope">
            <el-checkbox v-model="scope.row.checked" @change="savingCardCheckedChange(selectGood, scope.row)"></el-checkbox>
          </template>
        </el-table-column>
        <el-table-column label="储值卡">
          <template slot-scope="scope">
            <div class="font_14">
              {{ scope.row.SavingCardName }}
              <span class="color_gray font_12" v-if="scope.row.PriceType == 1">{{ scope.row.DiscountPrice }}折</span>
              <span class="color_gray font_12" v-else>¥ {{ scope.row.DiscountPrice | toFixed | NumFormat }}</span>
            </div>
            <div class="font_12">可用金额：¥ {{ scope.row.TotalPrice | toFixed | NumFormat }}</div>
          </template>
        </el-table-column>
        <el-table-column label="卡扣金额">
          <template slot-scope="scope">
            <el-input
              :disabled="!scope.row.checked"
              size="small"
              type="number"
              v-model="scope.row.TotalAmount"
              v-input-fixed
              placeholder="请输入抵扣金额"
              @input="savingCardPriceChange(selectGood, scope.row)"
            ></el-input>
            <div v-if="scope.row.cardDiscountPrice > 0" class="color_red">卡优惠：-{{ scope.row.cardDiscountPrice | toFixed | NumFormat }}</div>
          </template>
        </el-table-column>
      </el-table>
      <div slot="footer" class="dialog-footer">
        <el-row type="flex" align="middle">
          <el-col :span="18" class="text_left font_14">
            <div>
              <span>
                <span>支付金额：</span>
                <span class="color_red">¥ {{ selectGood.PayAmount | toFixed | NumFormat }}</span>
              </span>
              <span class="font_12 color_gray" v-if="selectGood.CardDeductionAmount > 0">
                （
                <span>卡抵扣：</span>
                <span>-¥ {{ selectGood.CardDeductionAmount | toFixed | NumFormat }}</span
                >）
              </span>
            </div>
          </el-col>
          <el-col :span="6">
            <el-button @click="dialogDeduction = false" size="small">取消</el-button>
            <el-button type="primary" @click="submitSavingCard" size="small" v-prevent-click>确认</el-button>
          </el-col>
        </el-row>
      </div>
    </el-dialog>

    <!--结账成功-->
    <el-dialog :visible.sync="dialogPay" width="800px" @close="closeSucceedDialog">
      <div class="text_center pad_15">
        <i class="el-icon-document" style="font-size: 80px; color: #999"></i>
        <div class="pad_15 color_primary font_weight_600 font_18">订单已结账成功</div>
      </div>
      <el-row class="pad_15 border_bottom">
        <el-col :span="12">销售订单号：</el-col>
        <el-col :span="12" class="text_right">{{ orderNumber }}</el-col>
      </el-row>
      <el-row class="pad_15 border_bottom">
        <el-col :span="12">消耗订单号：</el-col>
        <el-col :span="12" class="text_right">{{ consumeOrderNumber }}</el-col>
      </el-row>
      <el-row class="pad_15 border_bottom">
        <el-col :span="12">订单金额：</el-col>
        <el-col :span="12" class="color_red text_right">¥{{ orderAmount }}</el-col>
      </el-row>
      <el-row class="pad_15 border_bottom">
        <el-col :span="5">订单备注：</el-col>
        <el-col :span="19">{{ Remark }}</el-col>
      </el-row>

      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="dialogPay = false" size="small">继续开单</el-button>
        <el-button type="primary" @click="printNoteOfSmallDenomination" :loading="printSaleLoading" v-prevent-click size="small">打印销售小票</el-button>
        <el-button type="primary" @click="confrimPrintClick" size="small">打印销售单据</el-button>
        <el-button type="primary" @click="printNoteOfSmallDenominationTreatInfo" :loading="printConsumeLoading" v-prevent-click size="small"
          >打印消耗小票</el-button
        >
        <el-button type="primary" @click="confrimTreatPrintClick" size="small">打印消耗单据</el-button>
      </div>
    </el-dialog>

    <!--经手人-->
    <el-dialog title="经手人" :visible.sync="dialogVisibleAllHandler" width="800px" @close="closeHandlerAllClick">
      <div slot="title">
        <el-radio-group size="small" v-model="tabAllHandlePosition" @change="hangeAllHandleType">
          <el-radio-button label="saleHandler">销售经手人</el-radio-button>
          <el-radio-button label="consumeHandler">消耗经手人</el-radio-button>
        </el-radio-group>
      </div>
      <div>
        <el-row class="padbm_10">
          <el-col :span="8">
            <el-input placeholder="请输入员工编号、姓名" prefix-icon="el-icon-search" v-model="handlerAllName" size="small" clearable></el-input>
          </el-col>
        </el-row>
        <el-tabs v-model="tabAllHandle">
          <el-tab-pane :label="handler.Name" :name="`${index}`" v-for="(handler, index) in getAllHandlerList()" :key="index">
            <el-row style="max-height: 300px; overflow-y: auto">
              <el-col
                :span="12"
                v-for="item in handler.Employee.filter(
                  (item) =>
                    !handlerAllName ||
                    item.EmployeeName.toLowerCase().includes(handlerAllName.toLowerCase()) ||
                    item.EmployeeID.toLowerCase().includes(handlerAllName.toLowerCase())
                )"
                :key="item.EmployeeID"
                class="marbm_10 dis_flex flex_y_center"
              >
                <el-checkbox v-model="item.Checked" @change="handlerCheckedChange(handler.Employee, item)">
                  <span class="marrt_10">{{ item.EmployeeName }} [{{ item.EmployeeID }}]</span>
                </el-checkbox>
                <el-input
                  placeholder
                  v-model="item.Discount"
                  style="width: 120px"
                  type="number"
                  size="mini"
                  v-input-fixed
                  min="0"
                  max="100"
                  @input="handlerPercentChange(handler.Employee, item, 'dialog')"
                >
                  <template slot="append">%</template>
                </el-input>
              </el-col>
            </el-row>
          </el-tab-pane>
        </el-tabs>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisibleAllHandler = false" size="small">取 消</el-button>
        <el-button type="primary" size="small" @click="confirmAllHandlerClick" v-prevent-click>确 定</el-button>
      </div>
    </el-dialog>

    <!-- 整单修改价格 -->
    <el-dialog :visible.sync="orderChangeAmountVisible" title="修改金额" width="600px" @close="closeOrderChangeAmountDialog">
      <el-row class="border pad_10 marbm_10 radius5" type="flex" align="middle">
        <el-col :span="4">
          <span>手动改价</span>
        </el-col>
        <el-col :span="20"> 
          <el-row type="flex" align="middle">
            <el-col :span="11">
              <el-input size="mini" v-model="changOrderAmount" type="number" v-input-fixed @input="orderAmountChangeClick">
                <template slot="prepend">¥</template>
              </el-input>
            </el-col>
            <el-col :span="2" class="text_center">
              <i class="el-icon-sort"></i>
            </el-col>
            <el-col :span="11">
              <el-input size="mini" v-model="changOrderDiscount" type="number" v-input-fixed @input="orderDiscountChange">
                <template slot="append">%</template>
              </el-input>
            </el-col>
          </el-row>
        </el-col>
      </el-row>
      <div slot="footer" class="dialog-footer">
        <el-row type="flex" align="middle">
          <el-col :span="18" class="text_left font_14">
            <div>
              <span>待收款金额：</span>
              <span class="color_red">¥ {{ changOrderAmount | toFixed | NumFormat }}</span>
            </div>
          </el-col>
          <el-col :span="6">
            <el-button @click="orderChangeAmountVisibleClick" size="small">取消</el-button>
            <el-button type="primary" @click="confirmOrderChangeAmountClick" size="small" v-prevent-click>确认</el-button>
          </el-col>
        </el-row>
      </div>
    </el-dialog>

    <el-button v-show="false" ref="printButton" v-print="'printContent'">打印</el-button>
    <!-- 打印 -->
    <el-dialog title="选择打印模板" :visible.sync="printTemplateVisible" width="400px">
      <el-select size="small" v-model="printTemplateID">
        <el-option v-for="item in templateTypeList" :key="item.ID" :label="item.Name" :value="item.ID"></el-option>
      </el-select>
      <div slot="footer">
        <el-button @click="printTemplateVisible = false" size="small" v-prevent-click>取消</el-button>
        <el-button type="primary" @click="confirmSelectPrintTemplate" size="small" v-prevent-click>打印 </el-button>
      </div>
    </el-dialog>
    <div style="display: none">
      <div id="printContent">
        <component :is="printComponentName"></component>
      </div>
    </div>

    <el-button v-show="false" ref="printButtonTreat" v-print="'printContentTreatAglie'">打印</el-button>
    <!-- 打印 -->
    <el-dialog title="选择打印模板" :visible.sync="printTreatTemplateVisible" width="400px">
      <el-select size="small" v-model="printTreatTemplateID">
        <el-option v-for="item in treatTemplateTypeList" :key="item.ID" :label="item.Name" :value="item.ID"></el-option>
      </el-select>
      <div slot="footer">
        <el-button @click="printTreatTemplateVisible = false" size="small" v-prevent-click>取消</el-button>
        <el-button type="primary" @click="confirmTreatSelectPrintTemplate" size="small" v-prevent-click>打印 </el-button>
      </div>
    </el-dialog>
    <div style="display: none">
      <div id="printContentTreatAglie">
        <component :is="treatPrintComponentName"></component>
      </div>
    </div>

    <el-dialog :visible.sync="projectRemarkDialogVisible" title="备注信息" width="500px" append-to-body>
      <el-input type="textarea" :autosize="{ minRows: 2, maxRows: 4 }" maxlength="100" show-word-limit placeholder="请输入备注信息" v-model="projectRemark">
      </el-input>

      <div slot="footer" class="dialog-footer">
        <el-button @click="projectRemarkDialogVisible = false" size="small" :disabled="RemarkLoading">取 消</el-button>
        <el-button type="primary" @click="saveRemarkClick" :loading="RemarkLoading" v-prevent-click size="small">保 存</el-button>
      </div>
    </el-dialog>

    <treatCashierReceipt
      v-if="treatCashierReceiptDialogVisible"
      :visible.sync="treatCashierReceiptDialogVisible"
      :treatInfo="treatInfo"
      :entityName="entityName"
      :cashierReceipt="cashierReceipt"
    ></treatCashierReceipt>

    <saleCashierReceipt
      v-if="cashierReceiptDialogVisible"
      :visible.sync="cashierReceiptDialogVisible"
      :saleOrderDetail="saleOrderDetail"
      :entityName="entityName"
      :cashierReceipt="cashierReceipt"
    ></saleCashierReceipt>
  </div>
</template>

<script>
import API from '@/api/iBeauty/Order/saleGoods';
import consumeAPI from '@/api/iBeauty/Order/consumeGoods';
import agileSellAPI from '@/api/iBeauty/Order/agileSell';
import date from '@/components/js/date';
import treatConsumable from '@/components/iBeauty/Order/treatConsumable.vue';

import orderAPI from '@/api/iBeauty/Order/treatBill';
import orderSellAPI from '@/api/iBeauty/Order/saleBill';
import cashierAPI from '@/api/iBeauty/Order/cashierReceipt';

import printComponent from '@/views/iBeauty/Order/components/zl-print.js';
import print from 'vue-print-nb';

var Enumerable = require('linq');
const customerDiscountAPIMap = {
  project: 'saleGoods_projectCustomerDiscount',
  product: 'saleGoods_productCustomerDiscount',
};
const saleGoodsTypeMap = {
  project: 'ProjectID',
  product: 'ProductID',
};

export default {
  directives: {
    print,
  },
  props: {
    billDate: String,
    isSupplement: Boolean,
    customerID: Number,
    customerFullName: String,
    customerPhoneNumber: String,
    SellPermission: Object,
    TreatPermission: Object,
  },
  components: {
    treatConsumable,
    treatCashierReceipt: () => import('@/components/iBeauty/Order/cashierReceipt/treatCashierReceipt.vue'),
    saleCashierReceipt: () => import('@/components/iBeauty/Order/cashierReceipt/saleCashierReceipt.vue'),
  },
  data() {
    return {
      CardDiscountPrice:"",
      originalTotalAmount:0,
      printTreatTemplateVisible: false,
      treatTemplateTypeList: [],
      treatPrintComponentName: '',
      printTreatTemplateID: null,

      templateTypeList: [],
      printComponentName: '',
      printTemplateVisible: false,
      printTemplateID: null,

      modifyPayAmount: 0,
      totalLength: 0,
      changOrderDiscount: 100,
      changOrderAmount: 0,
      savingPayAmount: 0,
      packageSavingPayAmount: 0,
      orderChangeAmountVisible: false,
      isLockOrderAmount: false,
      oldPayAmount: '',
      dialogVisibleAllHandler: false,
      showRemark: false,
      printSaleLoading: false,
      printConsumeLoading: false,
      cashierReceiptDialogVisible: false,
      treatCashierReceiptDialogVisible: false,
      showModifyPrices: false,
      loading: false,
      modalLoading: false,
      dialogVisible: false,
      dialogBill: false,
      dialogDeduction: false,
      dialogDeductionPackage: false,
      dialogPay: false,
      goodsName: '',
      typeIndex: '0',
      categorySubIndex: '0',
      tabPane: '1',
      orderNumber: '',
      orderAmount: 0,
      BillID: '',
      Amount: 0, //订单金额
      PayAmount: 0, //待支付金额（待收款）
      PayCashAmount: 0, //现金支付金额
      payTotalPrice: 0,
      ArrearAmount: 0, //欠款金额
      cardDeductionAmount: 0,
      CardDeductionAmount: 0, // 储值卡抵扣金额
      PricePreferentialAmount: 0, //手动改价优惠金额
      CardPreferentialAmount: 0, //卡优惠金额
      savingCardPrice: 0, //储值卡金额
      Remark: '', //备注
      type: 1,
      handlerName: '',
      collapseName: [],
      goodsAll: [],

      /**  全部商品  */
      allLoading: false,
      allCategory: [], //项目
      allCategoryIndex: 0,
      allCategoryItem: null,
      goodsList: [], //全部商品
      allPaginations: {
        page: 1, // 当前位于哪页
        total: 0, // 总数
        page_size: 10, // 1页显示多少条
        layout: 'total, prev, pager, next', // 翻页属性
      },

      /**  项目  */
      projectLoading: false,
      projectCategory: [], //项目
      projectCategoryIndex: 0,
      projectSecondCategory: [], //项目二级分类
      projectSecondCategoryIndex: 0,
      projectSecondItem: null,
      projectList: [], //项目
      projectPaginations: {
        page: 1, // 当前位于哪页
        total: 0, // 总数
        page_size: 10, // 1页显示多少条
        layout: 'total, prev, pager, next', // 翻页属性
      },
      /**  产品  */
      productLoading: false,
      productCategory: [], //产品
      productCategoryIndex: 0,
      productSecondCategory: [], //项目二级分类
      productSecondCategoryIndex: 0,
      productSecondItem: null,
      productList: [], //产品
      productPaginations: {
        page: 1, // 当前位于哪页
        total: 0, // 总数
        page_size: 10, // 1页显示多少条
        layout: 'total, prev, pager, next', // 翻页属性
      },
      productCollapseName: [],
      selectProject: [],
      selectProduct: [],
      selectGeneralCard: [],
      selectTimeCard: [],
      selectSavingCard: [],
      selectPackageCard: [],
      selectGoods: '',
      selectGood: '',
      goodsIndex: '',

      handlerList: [], //公共经手人列表
      productHandlerList: [], //产品经手人列表
      productTreatHandlers: [], //产品消耗经手人
      projectHandlerList: [], //项目经手人列表
      projectTreatHandlers: [], //项目消耗经手人

      tabHandle: '0',
      savingCardAllGoods: [], // 通用储值卡
      savingCardSomeGoods: [], // 非通用储值卡
      savingCardAll: [],
      payTypeList: [],
      payList: [{ PayMethodID: '', Amount: '', price: 0 }],
      SavingCardDeduction: [],
      name: '',
      IsExistProduct: false,
      IsExistProject: false,
      userName: '',
      entityName: '',
      tabHandlePosition: 'saleHandler',
      cashierReceipt: {
        NameEncrypt: '',
      },
      treatInfo: null,
      saleOrderDetail: null,
      consumeOrderNumber: '',
      tabAllHandlePosition: 'saleHandler',
      tabAllHandle: '0',
      saleAllHandlerList: [],
      treatAllHandlerList: [],
      handlerAllName: '',

      projectRemark: '',
      currentRemarkItem: '',
      projectRemarkDialogVisible: false,
      RemarkLoading: false,
      goodsType: 'project',
      employeeDiscount: '',
      memberPreferentialAmount: 0,
    };
  },
  methods: {
    
    closeOrderChangeAmountDialog(){
      let that = this;
      that.payAmountData();
    },
    /**    */
    orderChangeAmountVisibleClick(){
      let that = this;
      that.orderChangeAmountVisible = false;
      that.payAmountData();
    },
      /*  */
      onCancelSubmitPay() {
      let that = this;
      that.dialogBill = false;
      that.savingCardAllGoods.forEach((i) => {
        i.checked = false;
      });
      that.savingDeductionPriceAll();
      that.payAmountData();
    },
    
    getSaveCardDiscountAmount (a, b) {
      return parseFloat(a || 0) + parseFloat(b || 0);
    },
    /**  修改是否使用会员折扣  */
    changeSelectMemberDiscountAmout(row) {
      let that = this;
      that.savingCardAll.forEach(function (item) {
          item.checked = false;
          item.TotalAmount = '';
          that.savingCardCheckedChange(that.selectGood, item);
        });
      if (row.isShowMemberAmout) {
        let Amount = row.Price ;
        if (row.MemberAmoutPriceType == 1) {
          Amount = row.Price * row.MemberAmoutDiscountPrice;
        }
        if (row.MemberAmoutPriceType == 2) {
          Amount = row.MemberAmoutDiscountPrice;
        }

        row.Amount = Amount * row.number;
        row.PayAmount = Amount  * row.number;
        row.TotalAmount = Amount  * row.number;
        row.totalPrice = Amount  * row.number;
        row.discountPrice = 0;
        row.discount = 100;
        row.MemberPreferentialAmount = row.Price - Amount;
        row.MemberPreferentialTotalAmount = parseFloat(row.MemberPreferentialAmount * row.number).toFixed(2);
      } else {
        let Amount = row.Price * row.number;
        row.Amount = Amount;
        row.PayAmount = Amount;
        row.TotalAmount = Amount;
        row.totalPrice = Amount;
        row.discountPrice = 0;
        row.discount = 100;
        row.MemberPreferentialTotalAmount = 0;
        row.DeductionProjectAmount = 0;
      }
    },
    /**  切换  */
    handleClick() {
      if (this.tabPane == 1) {
        this.goodsType = 'project';
      }
      if (this.tabPane == 2) {
        this.goodsType = 'product';
      }
    },
    /**  保存备注  */
    saveRemarkClick() {
      let that = this;
      that.currentRemarkItem.Remark = that.projectRemark;
      that.projectRemarkDialogVisible = false;
      that.projectRemark = '';
      that.currentRemarkItem = '';
    },
    /**  编辑项目 备注  */
    editProjectRemarkClick(item) {
      let that = this;
      that.projectRemarkDialogVisible = true;
      that.currentRemarkItem = item;
      that.projectRemark = that.currentRemarkItem.Remark;
    },
    /**  确定打印-消耗 -多  */
    confirmTreatSelectPrintTemplate() {
      let that = this;
      that.printTreatTemplateVisible = false;
      that.$nextTick(() => {
        let temp = that.treatTemplateTypeList.find((i) => {
          return i.ID == that.printTreatTemplateID;
        });
        that.treatPrintComponentName = printComponent.getPrintComponent(that.treatInfo, temp.Template);
        let buttonElement = that.$refs.printButtonTreat.$el;
        let clickEvent = new MouseEvent('click');
        buttonElement.dispatchEvent(clickEvent);
      });
    },
    /**  确认打印-消耗  */
    confrimTreatPrintClick() {
      let that = this;
      if (that.treatTemplateTypeList.length == 1) {
        let temp = that.treatTemplateTypeList[0].Template;
        that.$nextTick(() => {
          that.treatPrintComponentName = printComponent.getPrintComponent(that.treatInfo, temp);
          let buttonElement = that.$refs.printButtonTreat.$el;
          let clickEvent = new MouseEvent('click');
          buttonElement.dispatchEvent(clickEvent);
        });
      } else {
        if (!that.treatTemplateTypeList || !that.treatTemplateTypeList.length) {
          that.$message.error('暂无打印模板，请添加打印模板');
          return;
        }
        that.printTreatTemplateID = that.treatTemplateTypeList[0].ID;
        that.printTreatTemplateVisible = true;
      }
    },
    /**  确定打印   */
    confirmSelectPrintTemplate() {
      let that = this;
      that.printTemplateVisible = false;
      that.$nextTick(() => {
        let temp = that.templateTypeList.find((i) => {
          return i.ID == that.printTemplateID;
        });
        that.printComponentName = printComponent.getPrintComponent(that.saleOrderDetail, temp.Template);
        let buttonElement = that.$refs.printButton.$el;
        let clickEvent = new MouseEvent('click');
        buttonElement.dispatchEvent(clickEvent);
      });
    },
    /**  确认打印  */
    confrimPrintClick() {
      let that = this;
      if (that.templateTypeList.length == 1) {
        let temp = that.templateTypeList[0].Template;
        that.$nextTick(() => {
          that.printComponentName = printComponent.getPrintComponent(that.saleOrderDetail, temp);
          let buttonElement = that.$refs.printButton.$el;
          let clickEvent = new MouseEvent('click');
          buttonElement.dispatchEvent(clickEvent);
        });
      } else {
        if (!that.templateTypeList || !that.templateTypeList.length) {
          that.$message.error('暂无打印模板，请添加打印模板');
          return;
        }
        that.printTemplateID = that.templateTypeList[0].ID;
        that.printTemplateVisible = true;
      }
    },
    /**    */
    printNoteOfSmallDenomination() {
      this.cashierReceiptDialogVisible = true;
    },
    /**    */
    printNoteOfSmallDenominationTreatInfo() {
      this.treatCashierReceiptDialogVisible = true;
    },
    /**  修改折扣  */
    orderDiscountChange() {
      let that = this;
      that.changOrderAmount = ((Number(that.changOrderDiscount) * (Number(that.originalTotalAmount || 0) - Number(that.CardDeductionAmount || 0) - Number(that.CardDiscountPrice || 0))) / 100).toFixed(2);
    },
    /**  修改金额  */
    orderAmountChangeClick() {
      let that = this;
      that.changOrderDiscount = ((Number(that.changOrderAmount) / (Number(that.originalTotalAmount || 0) - Number(that.CardDeductionAmount || 0) - Number(that.CardDiscountPrice || 0))) * 100).toFixed(2);
    },
    /* 确定修改价格 */
    confirmOrderChangeAmountClick() {
      let that = this;
      if (that.changOrderDiscount < that.employeeDiscount * 100) {
        that.$message.error('折扣不能小于员工折扣');
        that.changOrderDiscount = that.employeeDiscount * 100;
        that.changOrderAmount = that.originalTotalAmount * that.employeeDiscount;
        return;
      }

      let currentLength = 1;
      let totalAllocated = 0;// 已分配金额
      let changOrderAmount = that.changOrderAmount; // 待分配总金额

      that.selectProject.forEach((i) => {
        if (!i.IsLargess && i.IsModifyPrice) {
          let PayAmount = 0;
          if (currentLength == that.totalLength) {
            PayAmount = (Number(changOrderAmount) - Number(totalAllocated)).toFixed(2);
          }else{
            PayAmount = ((Number(i.Price) * Number(i.number) - Number(i.CardDeductionAmount) - Number(i.CardDiscountPrice)) *
            Number(changOrderAmount) / 
             (Number(that.originalTotalAmount) - Number(that.CardDeductionAmount) - Number(that.CardDiscountPrice)) ).toFixed(2);  
          }
          totalAllocated += Number(PayAmount);
          currentLength += 1;
          i.PayAmount = Number(PayAmount).toFixed(2);
          i.Amount = Number(i.PayAmount) + Number(i.CardDeductionAmount);
          i.TotalAmount = Number(i.PayAmount) + Number(i.CardDeductionAmount);
          i.discount = that.changOrderDiscount;
          i.DeductionProjectAmount = (Number(i.Price) * Number(i.number) -
            Number(i.CardDeductionAmount) - Number(i.CardDiscountPrice) - Number(i.PayAmount) - Number(i.ArrearAmount)).toFixed(2);
            
          i.discountPrice = (Number(i.Price) * Number(i.number) -
            Number(i.CardDeductionAmount) - Number(i.CardDiscountPrice) - Number(PayAmount) - Number(i.ArrearAmount)).toFixed(2);
            
          i.MemberPreferentialAmountTotal = 0;
        }
      });
      that.selectProduct.forEach((i) => {
        if (!i.IsLargess && i.IsModifyPrice) {
          let PayAmount = 0;
          if (currentLength == that.totalLength) {
            PayAmount = (Number(changOrderAmount) - Number(totalAllocated)).toFixed(2);
          }else{
            PayAmount = ((Number(i.Price) * Number(i.number) - Number(i.CardDeductionAmount) - Number(i.CardDiscountPrice)) *
            Number(changOrderAmount) / 
             (Number(that.originalTotalAmount) - Number(that.CardDeductionAmount) - Number(that.CardDiscountPrice)) ).toFixed(2);  
          }
          totalAllocated += Number(PayAmount);
          currentLength += 1;
          
          i.PayAmount = Number(PayAmount).toFixed(2);
          i.Amount = parseFloat(i.PayAmount) + parseFloat(i.CardDeductionAmount);
          i.TotalAmount = parseFloat(i.PayAmount) + parseFloat(i.CardDeductionAmount);
          i.discount = that.changOrderDiscount;
          // i.DeductionProjectAmount = i.Price * i.number - i.CardDeductionAmount - i.PayAmount;
          // i.discountPrice = i.Price * i.number - i.CardDeductionAmount - i.CardDiscountPrice - i.PayAmount;
          i.DeductionProjectAmount = (Number(i.Price) * Number(i.number) -
            Number(i.CardDeductionAmount) - Number(i.CardDiscountPrice) - Number(i.PayAmount) - Number(i.ArrearAmount)).toFixed(2);
            
          i.discountPrice = (Number(i.Price) * Number(i.number) -
            Number(i.CardDeductionAmount) - Number(i.CardDiscountPrice) - Number(PayAmount) - Number(i.ArrearAmount)).toFixed(2);
            
          i.MemberPreferentialAmountTotal = 0;
        }
      });
      that.payAmountData();
      that.orderChangeAmountVisible = false;
    },
    /* 修改整单折扣 */
    changOrderAmountClick() {
      let that = this;
      if (that.selectProject.length == 0 && that.selectProduct.length == 0) {
        that.$message.error('请选择商品');
        return;
      }
      let isModifyPrice = false;
      let isShowMemberAmout = false;
      that.selectProject.forEach((i) => {
        if (i.IsModifyPrice  && !i.IsLargess) {
          isModifyPrice = true;
        }
        if (i.isShowMemberAmout  && !i.IsLargess) {
          isShowMemberAmout = true;
        }
      });
      that.selectProduct.forEach((i) => {
        if (i.IsModifyPrice  && !i.IsLargess) {
          isModifyPrice = true;
        }
        
        if (i.isShowMemberAmout  && !i.IsLargess) {
          isShowMemberAmout = true;
        }
      });
      if (!isModifyPrice) {
        that.$message.error('订单中不包含可以改价商品，不能整单改价');
        return;
      }  
      if (isShowMemberAmout) {
        that.$message.error('订单中包含会员优惠商品，不能整单改价');
        return;
      }


      // that.changOrderAmount = that.modifyPayAmount;
      that.changOrderDiscount = parseFloat(that.changOrderAmount / (Number(that.originalTotalAmount || 0) - Number(that.CardDeductionAmount || 0) - Number(that.CardDiscountPrice || 0)) * 100).toFixed(2);
      that.orderChangeAmountVisible = true;
    },
    closeHandlerAllClick() {
      let that = this;
      that.saleAllHandlerList = [];
      that.treatAllHandlerList = [];
    },
    /**  确认全部修改经手人  */
    confirmAllHandlerClick() {
      let that = this;
      /* 项目 */
      that.selectProject.forEach((j) => {
        j.saleHandlerList.forEach((n) => {
          that.saleAllHandlerList
            .filter((i) => i.Name == n.Name)
            .forEach((e) => {
              n.Employee = e.Employee.filter((em) => em.Checked).map((emp) => {
                emp.ID = `${n.ID}-${emp.EmployeeID}`;
                emp.SaleHandlerID = n.ID;
                return Object.assign({}, emp);
              });
            });
        });
        j.consumeHandlerList.forEach((n) => {
          that.treatAllHandlerList
            .filter((i) => i.Name == n.Name)
            .forEach((e) => {
              n.Employee = e.Employee.filter((em) => em.Checked).map((emp) => {
                emp.ID = `${n.ID}-${emp.EmployeeID}`;
                emp.TreatHandlerID = n.ID;
                return Object.assign({}, emp);
              });
            });
        });
      });

      /* 产品 */
      that.selectProduct.forEach((j) => {
        j.saleHandlerList.forEach((n) => {
          that.saleAllHandlerList
            .filter((i) => i.Name == n.Name)
            .forEach((e) => {
              n.Employee = e.Employee.filter((em) => em.Checked).map((emp) => {
                emp.ID = `${n.ID}-${emp.EmployeeID}`;
                emp.SaleHandlerID = n.ID;
                return Object.assign({}, emp);
              });
            });
        });
        j.consumeHandlerList.forEach((n) => {
          that.treatAllHandlerList
            .filter((i) => i.Name == n.Name)
            .forEach((e) => {
              n.Employee = e.Employee.filter((em) => em.Checked).map((emp) => {
                emp.ID = `${n.ID}-${emp.EmployeeID}`;
                emp.TreatHandlerID = n.ID;
                return Object.assign({}, emp);
              });
            });
        });
      });

      that.dialogVisibleAllHandler = false;
      that.showRemark = false;
      that.saleAllHandlerList = [];
      that.treatAllHandlerList = [];
    },

    /** 获取经手人   */
    getAllHandlerList() {
      var that = this;
      if (that.tabAllHandlePosition == 'saleHandler') {
        return that.saleAllHandlerList;
      } else {
        return that.treatAllHandlerList;
      }
    },

    /**  修改经手人类型 销售、消耗  */
    hangeAllHandleType() {
      var that = this;
      that.tabAllHandle = '0';
    },
    /**    */
    showSelectAllHandlerClick() {
      let that = this;

      let GoodTypes = [];
      if (that.selectProject && that.selectProject.length > 0) {
        GoodTypes.push('20');
      }
      if (that.selectProduct && that.selectProduct.length > 0) {
        GoodTypes.push('10');
      }

      that.treatHandler_allHandler(GoodTypes);
      that.saleHandler_allHandler(GoodTypes);
      that.dialogVisibleAllHandler = true;
    },
    /**    */
    hiddenInfoRemarkClick() {
      let that = this;
      that.showRemark = false;
    },
    /**    */
    showRemarkClick() {
      let that = this;
      that.showRemark = true;
    },

    /**  删除耗材  */
    removeTreatConsumableClick(conIndex, item) {
      item.Consumable.splice(conIndex, 1);
    },
    /**  删除所有耗材  */
    removeAllTreatConsumableClick(conIndex, item) {
      item.Consumable = [];
    },
    /**  添加耗材  */
    addTreatConsumableClick(event, item) {
      let that = this;
      let cur = item.Consumable ? item.Consumable : [];
      let temp = [...cur, ...event];

      that.$set(item, 'Consumable', temp);
    },
    addProjecConsumable(index) {
      this.$refs[`${index}`][0].addConsumableClick();
    },

    AverageClick(Employee) {
      if (Employee.length > 0) {
        Employee.forEach((item, index) => {
          item.Discount = Math.floor(100 / Employee.length);
          if (Employee.length - 1 == index) {
            item.Discount = Math.ceil(100 / Employee.length);
          }
        });
      }
    },
    mathAbsData: function (item) {
      return Math.abs(item);
    },
    // 修改会员
    changMemberOrType: function () {
      var that = this;
      if (that.customerID != null) {
        that.savingCardAllGoodsData();
        that.savingCardSomeGoodsData();
        that.getReceiptConfig();
      }
    },
    /**  清除卡抵扣信息  */
    clearAgileSellData() {
      var that = this;
      that.savingCardAllGoods = [];
      that.savingCardSomeGoods = [];
      that.selectProduct = [];
      that.selectProject = [];
      that.payAmountData();
    },
    //获取开单时间
    getBillDate: function () {
      var that = this;
      return that.isSupplement ? that.billDate : date.formatDate.format(new Date(), 'YYYY-MM-DD hh:mm:ss');
    },
    /**  全部分页切换  */
    handleAllGoodsCurrentChange(page) {
      let that = this;
      that.allPaginations.page = page;
      that.getFastSaleGoodsTypeGoods();
    },
    /* 全部 分类切换 */
    goodsCategoryChange: function (item, index) {
      console.log('item', item);
      var that = this;
      if (that.allCategoryIndex == index) {
        return;
      }
      if (item.GoodsType == '20') {
        this.goodsType = 'project';
      }
      if (item.GoodsType == '10') {
        this.goodsType = 'product';
      }
      that.allCategoryItem = item;
      that.allCategoryIndex = index;
      that.allPaginations.page = 1;
      that.goodsAll = [];
      that.getFastSaleGoodsTypeGoods();
    },
    /**    */
    async getFastSaleGoodsGoodsType() {
      let that = this;
      let params = {
        Name: that.goodsName,
        BillDate: that.getBillDate(),
      };
      let res = await API.getFastSaleGoodsGoodsType(params);
      if (res.StateCode == 200) {
        that.allCategory = res.Data;
        that.allCategory.forEach((i) => {
          switch (i.GoodsType) {
            case '10' /* 产品 */:
              that.IsExistProduct = true;
              break;
            case '20' /* 项目 */:
              that.IsExistProject = true;
              break;
          }
        });

        if (that.allCategory && that.allCategory.length) {
          that.allCategoryIndex = 0;
          that.allCategoryItem = that.allCategory[that.allCategoryIndex];
          that.getFastSaleGoodsTypeGoods();
        } else {
          that.goodsAll = [];
        }
      } else {
        that.$message.error(res.Message);
      }
    },
    /**  获取分类下的全部商品  */
    async getFastSaleGoodsTypeGoods() {
      let that = this;
      let params = {
        PageNum: that.allPaginations.page,
        GoodsType: that.allCategoryItem.GoodsType, //类型ID
        Name: that.typeIndex == '0' ? that.goodsName : '',
        BillDate: that.getBillDate(), //时间
      };
      that.allLoading = true;
      let res = await API.getFastSaleGoodsTypeGoods(params);
      if (res.StateCode == 200) {
        that.goodsAll = res.List;
        that.allPaginations.total = res.Total;
        that.allPaginations.page_size = res.PageSize;
      } else {
        that.$message.error(res.Message);
      }
      that.allLoading = false;
    },
    /**  项目 TODO ******************************  */
    // 项目一级分类
    projectCategoryChange: function (item, index) {
      var that = this;
      /* 已经选中的防止重复点击 */
      if (that.projectCategoryIndex == index) {
        return;
      }
      that.projectCategoryIndex = index;
      that.projectSecondCategoryIndex = 0;
      that.projectSecondCategory = item.Child;
      that.projectSecondItem = that.projectSecondCategory[that.projectSecondCategoryIndex];
      that.projectPaginations.page = 1;
      that.getSaleGoodsProjectByCategoryy();
    },
    /**   项目二级分类点击 */
    projectSecondCategoryChange(item, index) {
      let that = this;
      /* 已经选中的防止重复点击 */
      if (that.projectSecondCategoryIndex == index) {
        return;
      }
      that.projectSecondCategoryIndex = index;
      that.projectSecondItem = item;
      that.projectPaginations.page = 1;
      that.getSaleGoodsProjectByCategoryy();
    },
    /**  修改项目分页  */
    handleProjectCurrentChange(page) {
      let that = this;
      that.projectPaginations.page = page;
      that.getSaleGoodsProjectByCategoryy();
    },
    /**
     * @description: TODO 项目分类
     * @return {*}
     */
    async getSaleGoodsProjectCategory() {
      let that = this;
      let params = {
        Name: that.goodsName,
      };
      let res = await API.getSaleGoodsProjectCategory(params);
      if (res.StateCode == 200) {
        that.projectCategory = res.Data;
        if (that.projectCategory && that.projectCategory.length) {
          that.projectCategoryIndex = 0;
          let firstItem = that.projectCategory[that.projectCategoryIndex];
          if (firstItem.Child && firstItem.Child.length) {
            that.projectSecondCategory = firstItem.Child;
            that.projectSecondCategoryIndex = 0;
            that.projectSecondItem = firstItem.Child[that.projectSecondCategoryIndex];

            that.getSaleGoodsProjectByCategoryy();
          }
        } else {
          that.projectList = [];
          that.projectSecondCategory = [];
        }
      } else {
        that.$message.error(res.Message);
      }
    },
    /**
     * @description: 分类下的项目
     * @return {*}
     */
    async getSaleGoodsProjectByCategoryy() {
      let that = this;
      let params = {
        PageNum: that.projectPaginations.page,
        Name: that.typeIndex == '1' ? that.goodsName : '',
        CategoryID: that.projectSecondItem.CategoryID, //分类ID
      };
      that.projectLoading = true;
      let res = await API.getSaleGoodsProjectByCategoryy(params);
      if (res.StateCode == 200) {
        that.projectList = res.List;
        that.projectPaginations.total = res.Total;
        that.projectPaginations.page_size = res.PageSize;
      } else {
        that.$message.error(res.Message);
      }
      that.projectLoading = false;
    },
    // 产品一级分类
    productCategoryChange: function (item, index) {
      var that = this;
      /* 已经选中的防止重复点击 */
      if (that.productCategoryIndex == index) {
        return;
      }
      that.productCategoryIndex = index;
      that.productSecondCategoryIndex = 0;
      that.productSecondCategory = item.Child;
      that.productSecondItem = that.productSecondCategory[that.productSecondCategoryIndex];
      that.productPaginations.page = 1;
      that.getSaleGoodsProductByCategory();
    },
    /**  产品二级分类  */
    productSecondCategoryChange(item, index) {
      let that = this;
      /* 已经选中的防止重复点击 */
      if (that.productSecondCategoryIndex == index) {
        return;
      }
      that.productSecondCategoryIndex = index;
      that.productSecondItem = item;
      that.productPaginations.page = 1;
      that.getSaleGoodsProductByCategory();
    },
    /**  修改项目分页  */
    handleProductCurrentChange(page) {
      let that = this;
      that.productPaginations.page = page;
      that.getSaleGoodsProductByCategory();
    },
    /**
     * @description: TODO 产品分类
     * @return {*}
     */
    async getSaleGoodsProductCategory() {
      let that = this;
      let params = { Name: that.goodsName };
      let res = await API.getSaleGoodsProductCategory(params);
      if (res.StateCode == 200) {
        that.productCategory = res.Data;
        if (that.productCategory && that.productCategory.length) {
          that.productCategoryIndex = 0;
          let firstItem = that.productCategory[that.productCategoryIndex];
          if (firstItem.Child && firstItem.Child.length) {
            that.productSecondCategory = firstItem.Child;
            that.productSecondCategoryIndex = 0;
            that.productSecondItem = firstItem.Child[that.productSecondCategoryIndex];

            that.getSaleGoodsProductByCategory();
          }
        } else {
          that.productList = [];
          that.productSecondCategory = [];
        }
      } else {
        that.$message.error(res.Message);
      }
    },
    /**
     * @description: 产品列表
     * @return {*}
     */
    async getSaleGoodsProductByCategory() {
      let that = this;
      let params = {
        PageNum: that.productPaginations.page,
        Name: that.typeIndex == '2' ? that.goodsName : '',
        CategoryID: that.productSecondItem.CategoryID, //分类ID
      };
      that.productLoading = true;
      let res = await API.getSaleGoodsProductByCategory(params);
      if (res.StateCode == 200) {
        that.productList = res.List;
        that.productPaginations.total = res.Total;
        that.productPaginations.page_size = res.PageSize;
      } else {
        that.$message.error(res.Message);
      }
      that.productLoading = false;
    },

    // 销售项目经手人
    projectHandlerData: function () {
      var that = this;
      that.loading = true;
      API.getProjectHandler()
        .then((res) => {
          if (res.StateCode == 200) {
            that.projectHandlerList = res.Data;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },
    // 销售产品经手人
    productHandlerData: function () {
      var that = this;
      that.loading = true;
      API.getProductHandler()
        .then((res) => {
          if (res.StateCode == 200) {
            that.productHandlerList = res.Data;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },
    /**  52.11.获取项目消耗经手人 */
    treatProjectHandlerNet: function () {
      var that = this;
      that.loading = true;
      var params = {};
      consumeAPI
        .treatGoodsProjectHandler(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.projectTreatHandlers = res.Data;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },
    /**  52.12.获取产品消耗经手人 */
    treatProductHandlerNet: function () {
      var that = this;
      that.loading = true;
      var params = {};
      consumeAPI
        .treatGoodsProductHandler(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.productTreatHandlers = res.Data;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },

    // 支付方式
    salePayMethodData: function () {
      var that = this;
      that.loading = true;
      API.getSalePayMethod()
        .then((res) => {
          if (res.StateCode == 200) {
            that.payTypeList = res.Data;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },
    // 会员通用储值卡
    savingCardAllGoodsData: function () {
      var that = this;
      that.loading = true;
      var params = {
        CustomerID: that.customerID,
      };
      API.getSavingCardAllGoods(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.savingCardAllGoods = res.Data;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },
    // 会员非通用储值卡（指定商品抵扣卡）
    savingCardSomeGoodsData: function () {
      var that = this;
      that.loading = true;
      var params = {
        CustomerID: that.customerID,
      };
      API.getSavingCardSomeGoods(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.savingCardSomeGoods = res.Data;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },
    // 可抵扣产品的储值卡列表
    savingCardProductData: function (item) {
      var that = this;
      if (that.customerID == '' || that.customerID == null) {
        return false;
      }
      that.loading = true;
      var params = {
        CustomerID: that.customerID,
        ProductID: item.ID,
      };
      API.getSavingCardProduct(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.savingCardDeductionData(res.Data, item);
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },
    // 可抵扣项目的储值卡列表
    savingCardProjecctData: function (item) {
      var that = this;
      that.loading = true;
      if (that.customerID == '' || that.customerID == null) {
        return false;
      }
      var params = {
        CustomerID: that.customerID,
        ProjectID: item.ID,
      };
      API.getSavingCardProject(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.savingCardDeductionData(res.Data, item);
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },

    savingCardDeductionData: function (data, item) {
      var that = this;
      var savingCardAll = [];
      data.forEach(function (discount) {
        if (discount.PriceType == 1) {
          discount.DiscountPrice = discount.DiscountPrice * 10;
        }
      });

      that.savingCardSomeGoods.forEach(function (some) {
        data.forEach(function (project) {
          if (some.ID == project.ID) {
            savingCardAll.push({
              checked: false,
              AccountID: some.AccountID,
              Balance: some.Balance,
              ID: some.ID,
              LargessBalance: some.LargessBalance,
              SavingCardName: some.SavingCardName,
              TotalBalance: some.TotalBalance,
              TotalPrice: some.TotalBalance,
              Type: some.Type,
              DiscountPrice: project.DiscountPrice,
              PriceType: project.PriceType,
              TotalAmount: '',
              PreferentialAmount: 0,
              cardDiscountPrice: 0,
              cardDeductionAmount: 0,
            });
          }
        });
      });
      savingCardAll.forEach(function (saving) {
        item.savingCardDeduction.forEach(function (deduction) {
          if (saving.ID == deduction.ID) {
            saving.checked = true;
            saving.TotalAmount = deduction.TotalAmount || 0;
            saving.TotalBalance = (parseFloat(saving.TotalBalance) + parseFloat(deduction.TotalAmount)).toFixed(2);
            saving.TotalPrice = (saving.TotalBalance - saving.TotalAmount).toFixed(2);
            saving.PreferentialAmount = deduction.PreferentialAmount;
            saving.cardDiscountPrice = deduction.cardDiscountPrice;
            saving.cardDeductionAmount = deduction.cardDeductionAmount;
          }
        });
      });
      that.savingCardAll = Enumerable.from(savingCardAll)
        .where((i) => {
          return i.TotalBalance > 0;
        })
        .toArray();
    },
    // 搜索商品
    searchGoodsClick: function () {
      var that = this;
      that.tabPane = that.typeIndex;
      switch (that.typeIndex) {
        case '0':
          that.getFastSaleGoodsGoodsType();
          break;
        case '1':
          that.getSaleGoodsProjectCategory();
          break;
        case '2':
          that.getSaleGoodsProductCategory();
          break;
        default:
          that.getFastSaleGoodsGoodsType();
      }
    },
    // 清空
    clearClick: function () {
      var that = this;
      // that.searchGoodsClick();
      that.getFastSaleGoodsGoodsType();
      that.getSaleGoodsProjectCategory();
      that.getSaleGoodsProductCategory();
    },

    /** =====项目 =================================================================   */
    // 项目选择
    projectChange: function (row) {
      var that = this;
      if (that.customerID) {
        that.saleGoods_CustomerDiscount(row.ID).then((res) => {
          this.projectChangePush(row, res);
        });
      } else {
        this.projectChangePush(row);
      }
    },
    /* 添加项目 */
    projectChangePush(row, memberDiscount) {
      var that = this;
      let isShowMemberAmout = false;
      let isShowSelectMemberAmout = false;
      let MemberPreferentialAmount = '';
      let MemberAmoutDiscountPrice = '';
      let MemberAmoutPriceType = '';
      let Price = row.Price;
      if (memberDiscount) {
        isShowMemberAmout = true;
        MemberAmoutDiscountPrice = memberDiscount.DiscountPrice;
        MemberAmoutPriceType = memberDiscount.PriceType;
        isShowSelectMemberAmout = true;
        if (memberDiscount.PriceType == 1) {
          Price = parseFloat(row.Price) * parseFloat(memberDiscount.DiscountPrice);
          MemberPreferentialAmount = parseFloat(row.Price) - Price;
        }
        if (memberDiscount.PriceType == 2) {
          Price = parseFloat(memberDiscount.DiscountPrice);
          MemberPreferentialAmount = parseFloat(row.Price) - Price;
        }
      }

      var data = {
        type: 1,
        Alias: row.Alias,
        IsModifyPrice: row.IsModifyPrice,
        ID: row.ID,
        IsAllowLargess: row.IsAllowLargess,
        IsLargess: false,
        Name: row.Name,
        Price: row.Price,
        totalPrice: Price,
        number: 1,
        discount: 100,
        Amount: Price,
        PayAmount: Price,
        TotalAmount: Price,
        ArrearAmount: 0,
        isModify: true,
        DeductionProjectAmount: 0,
        discountPrice: 0,
        CardDeductionAmount: 0,
        CardDiscountPrice: 0,
        savingCardDeduction: [],
        saleHandlerList: [],
        consumeHandlerList: [],
        MemberPreferentialAmount, // 会员优惠金额
        MemberPreferentialTotalAmount:MemberPreferentialAmount, // 会员优惠金额
        isShowMemberAmout,
        isShowSelectMemberAmout,
        MemberAmoutDiscountPrice,
        MemberAmoutPriceType,
      };

      data.saleHandlerList = Enumerable.from(that.projectHandlerList)
        .select((item) => ({
          ID: item.ID,
          Name: item.Name,
          Employee: [],
        }))
        .toArray();

      data.consumeHandlerList = Enumerable.from(that.projectTreatHandlers)
        .select((item) => ({
          ID: item.ID,
          Name: item.Name,
          Employee: [],
        }))
        .toArray();

      that.selectProject.push(data);

      that.payAmountData();
    },

    /** =====产品 =================================================================   */
    // 二级分类
    // productCategorySubChange: function (index) {
    //   var that = this;
    //   that.categorySubIndex = index;
    // },
    // 产品选择
    productChange(row) {
      let that = this;
      if (that.customerID) {
        that.saleGoods_CustomerDiscount(row.ID).then((res) => {
          this.productChangePush(row, res);
        });
      } else {
        this.productChangePush(row);
      }
    },

    /**    */
    productChangePush(row, memberDiscount) {
      var that = this;
      let isShowMemberAmout = false;
      let isShowSelectMemberAmout = false;
      let MemberPreferentialAmount = '';
      let MemberAmoutDiscountPrice = '';
      let MemberAmoutPriceType = '';
      let Price = row.Price;
      if (memberDiscount) {
        isShowMemberAmout = true;
        MemberAmoutDiscountPrice = memberDiscount.DiscountPrice;
        MemberAmoutPriceType = memberDiscount.PriceType;
        isShowSelectMemberAmout = true;
        if (memberDiscount.PriceType == 1) {
          Price = parseFloat(row.Price) * parseFloat(memberDiscount.DiscountPrice);
          MemberPreferentialAmount = parseFloat(row.Price) - Price;
        }
        if (memberDiscount.PriceType == 2) {
          Price = parseFloat(memberDiscount.DiscountPrice);
          MemberPreferentialAmount = parseFloat(row.Price) - Price;
        }
      }

      var data = {
        type: 6,
        Alias: row.Alias,
        ID: row.ID,
        IsAllowLargess: row.IsAllowLargess,
        IsLargess: false,
        Name: row.Name,
        Price: row.Price,
        totalPrice: Price,
        number: 1,
        discount: 100,
        Amount: Price,
        PayAmount: Price,
        TotalAmount: Price,
        ArrearAmount: 0,
        isModify: true,
        DeductionProjectAmount: 0,
        discountPrice: 0,
        CardDeductionAmount: 0,
        CardDiscountPrice: 0,
        savingCardDeduction: [],
        saleHandlerList: [],
        consumeHandlerList: [],
        IsModifyPrice: row.IsModifyPrice,
        MemberPreferentialAmount, // 会员优惠金额
        isShowMemberAmout,
        isShowSelectMemberAmout,
        
        MemberPreferentialTotalAmount:MemberPreferentialAmount, // 会员优惠金额
        MemberAmoutDiscountPrice,
        MemberAmoutPriceType,
      };
      data.saleHandlerList = Enumerable.from(that.productHandlerList)
        .select((item) => ({
          ID: item.ID,
          Name: item.Name,
          Employee: [],
        }))
        .toArray();

      data.consumeHandlerList = Enumerable.from(that.productTreatHandlers)
        .select((item) => ({
          ID: item.ID,
          Name: item.Name,
          Employee: [],
        }))
        .toArray();

      that.selectProduct.push(data);
      that.payAmountData();
    },

    // 锚点
    navChange: function (index, selector) {
      var that = this;
      var anchor = this.$el.querySelector(selector);
      that.$el.querySelector('.el_scrollbar_project').scrollTop = anchor.offsetTop;
    },
    // 商品选择
    goodsChange: function (row) {
      var that = this;
      switch (that.allCategoryItem.GoodsType) {
        case '20':
          that.projectChange(row);
          break;
        case '10':
          that.productChange(row);
          break;
      }
    },

    /**  修改经手人类型 销售、消耗  */
    hangeHandleType() {
      var that = this;
      that.tabHandle = '0';
    },

    /** 获取经手人   */
    getHandlerList() {
      var that = this;
      if (that.tabHandlePosition == 'saleHandler') {
        return that.handlerList.saleHandlerList;
      } else {
        return that.handlerList.consumeHandlerList;
      }
    },

    // 经手人
    employeeHandleClick: function (type, row, item, index, isSale) {
      var that = this;
      var saleEmplayee = [];
      var consumeEmplayee = [];
      that.tabHandle = '0';
      that.handlerList = {};
      that.tabHandlePosition = isSale ? 'saleHandler' : 'consumeHandler';
      switch (type) {
        case 1:
          that.handlerList.saleHandlerList = that.projectHandlerList;
          that.handlerList.consumeHandlerList = that.projectTreatHandlers;
          break;
        case 2:
          that.handlerList.saleHandlerList = that.productHandlerList;
          that.handlerList.consumeHandlerList = that.productTreatHandlers;
          break;
      }

      /**  销售 ===================  */
      Enumerable.from(item.saleHandlerList).forEach(function (hand) {
        hand.Employee.forEach(function (emp) {
          saleEmplayee.push({ ID: emp.ID, Discount: emp.Discount });
        });
      });
      // 选中
      Enumerable.from(that.handlerList.saleHandlerList).forEach((handler) => {
        handler.Employee.forEach(function (emp) {
          emp.Checked = false;
          emp.Discount = '';
          Enumerable.from(saleEmplayee).forEach(function (i) {
            if (emp.ID == i.ID) {
              emp.Checked = true;
              emp.Discount = i.Discount;
            }
          });
        });
      });

      /**  消耗===================  */
      Enumerable.from(item.consumeHandlerList).forEach(function (hand) {
        hand.Employee.forEach(function (emp) {
          consumeEmplayee.push({ ID: emp.ID, Discount: emp.Discount });
        });
      });
      // 选中
      Enumerable.from(that.handlerList.consumeHandlerList).forEach((handler) => {
        handler.Employee.forEach(function (emp) {
          emp.Checked = false;
          emp.Discount = '';
          Enumerable.from(consumeEmplayee).forEach(function (i) {
            if (emp.ID == i.ID) {
              emp.Checked = true;
              emp.Discount = i.Discount;
            }
          });
        });
      });

      that.type = type;
      that.selectGoods = row;
      that.goodsIndex = index;
      that.dialogVisible = true;
    },
    // 经手人确认选择
    submitHandleClick: function () {
      var that = this;
      var goodsHandler = JSON.parse(JSON.stringify(that.handlerList));
      if (
        goodsHandler.saleHandlerList.some((item) => {
          return (
            item.Employee.reduce((pre, pri) => {
              return pre + Number(pri.Discount);
            }, 0) > 100
          );
        })
      ) {
        that.$message.error('比例总和不能超过100%');
        return;
      }
      if (
        goodsHandler.consumeHandlerList.some((item) => {
          return (
            item.Employee.reduce((pre, pri) => {
              return pre + Number(pri.Discount);
            }, 0) > 100
          );
        })
      ) {
        that.$message.error('比例总和不能超过100%');
        return;
      }
      Enumerable.from(goodsHandler).forEach((saleType) => {
        Enumerable.from(saleType.value).forEach(function (item) {
          item.Employee = Enumerable.from(item.Employee)
            .where(function (i) {
              return i.Checked;
            })
            .toArray();
        });
      });
      that.selectGoods[that.goodsIndex].saleHandlerList = goodsHandler.saleHandlerList;
      that.selectGoods[that.goodsIndex].consumeHandlerList = goodsHandler.consumeHandlerList;
      switch (that.type) {
        case 1:
          that.selectProject = that.selectGoods;
          break;
        case 2:
          that.selectProduct = that.selectGoods;
          break;
      }
      that.dialogVisible = false;
    },
    // 删除经手人
    removeHandleClick: function (item, index) {
      item.Employee.splice(index, 1);
    },
    // 删除项目
    removeClick: function (type, index, item) {
      var that = this;
      switch (type) {
        case 1:
          that.selectProject.splice(index, 1);
          break;
        case 2:
          that.selectProduct.splice(index, 1);
          break;
      }
      if (item.savingCardDeduction != undefined) {
        that.deductionReset(item);
      }
      that.payAmountData();
    },
    // 经手人选择
    handlerCheckedChange: function (row, item) {
      let checkedArr = row.filter((i) => {
        return i.Checked;
      });
      row.forEach((val) => {
        if (val.Checked) {
          val.Discount = Math.floor(100 / checkedArr.length);
          if (val.EmployeeID == checkedArr[checkedArr.length - 1].EmployeeID) {
            val.Discount = Math.ceil(100 / checkedArr.length);
          }
        }
      });
      var discount = 0;
      var employee = Enumerable.from(row)
        .where(function (i) {
          return i.Checked;
        })
        .toArray();
      employee.forEach(function (emp) {
        var Discount = emp.Discount;
        if (Discount == '') {
          Discount = 0;
        }
        discount = parseFloat(discount) + parseFloat(Discount);
      });
      if (!item.Checked) {
        item.Discount = '';
      } else {
        if (item.Discount == '') {
          if (discount > 100) {
            item.Discount = 0;
          } else {
            item.Discount = 100 - discount;
          }
        }
      }
    },
    // 百分比
    handlerPercentChange: function (row, item, type) {
      var that = this;
      var discount = 0;
      if (item.Discount != '') {
        item.Discount = parseFloat(item.Discount);
      }
      if (type !== 'dialog') {
        if (item.Discount > 100) {
          item.Discount = 100;
        }
      }
      var employee = Enumerable.from(row)
        .where(function (i) {
          return i.Checked;
        })
        .toArray();
      employee.forEach(function (emp) {
        var Discount = emp.Discount;
        if (Discount == '') {
          Discount = 0;
        }
        discount = parseFloat(discount) + parseFloat(Discount);
      });

      if (type !== 'dialog') {
        if (parseFloat(discount) > 100) {
          item.Discount = 100 - (discount - item.Discount);
          that.$message.error('比例总和不能超过100%');
        }
      }
    },

    // 储值卡抵扣(部分商品)
    savingCardDeductionClick: function (type, row, item, index) {
      var that = this;
      
      item.PayAmount = parseFloat(item.PayAmount) + (parseFloat(item.ArrearAmount) || 0);
      item.ArrearAmount = 0;
      that.savingCardAll = [];
      switch (type) {
        case 1:
          that.showModifyPrices = that.SellPermission.ModifyPrices_SaleProject;
          that.savingCardProjecctData(item);
          break;
        case 2:
          that.showModifyPrices = that.SellPermission.ModifyPrices_SaleProduct;
          that.savingCardProductData(item);
          break;
      }
      that.type = type;
      that.selectGoods = row;
      that.selectGood = Object.assign({}, item);
      that.goodsIndex = index;
      item.ArrearAmount = 0;
      that.dialogDeduction = true;
    },
    // 折扣
    discountChange: function (row) {
      var that = this;
      //清除选中的储值卡
      that.savingCardAll.forEach(function (item) {
        item.checked = false;
        item.TotalAmount = '';
        that.savingCardCheckedChange(row, item);
      });

      //计算金额
      row.Amount = ((row.Price * row.number * row.discount) / 100).toFixed(2);
      //计算优惠
      row.discountPrice = (row.totalPrice - row.Amount).toFixed(2);
      that.payPriceData(row);
    },
    // 折后金额
    amountChange: function (row) {
      var that = this;
      //清除选中的储值卡
      that.savingCardAll.forEach(function (item) {
        item.checked = false;
        item.TotalAmount = '';
        that.savingCardCheckedChange(row, item);
      });
      //计算折扣
      row.discount = parseInt((row.Amount / row.Price / row.number) * 100);
      //计算优惠
      row.discountPrice = (row.totalPrice - row.Amount).toFixed(2);
      that.payPriceData(row);
    },
    // 改价
    modifyChange: function (row) {
      var that = this;
      row.isModify = !row.isModify;
      if (row.Amount == '') {
        row.Amount = 0;
      }
      if (row.discount < that.employeeDiscount * 100) {
        that.$message.error('折扣不能小于员工最低折扣');
        row.discount = that.employeeDiscount * 100;
        //计算金额
        row.Amount = ((row.Price * row.number * row.discount) / 100).toFixed(2);
      }
      row.discountPrice = (row.totalPrice - row.Amount).toFixed(2);
      that.payPriceData(row);
    },
    // 储值卡抵扣选择
    savingCardCheckedChange: function (row, item) {
      var that = this;
      var amount;
      if (item.checked) {
        if (item.PriceType == 1) {
          var payAmount = (
            row.totalPrice -
            row.DeductionProjectAmount -
            row.CardDeductionAmount -
            row.ArrearAmount +
            parseFloat(item.cardDiscountPrice) +
            parseFloat(item.cardDeductionAmount)
          ).toFixed(2);
          amount = ((item.DiscountPrice / 10) * payAmount).toFixed(2);
          if (parseFloat(amount) > parseFloat(item.TotalBalance)) {
            item.TotalAmount = item.TotalBalance;
          } else {
            item.TotalAmount = amount;
          }
          item.PreferentialAmount = (item.TotalAmount / (item.DiscountPrice / 10)).toFixed(2);
        } else {
          amount = ((row.PayAmount * item.DiscountPrice) / row.Price).toFixed(2);
          if (parseFloat(amount) > parseFloat(item.TotalBalance)) {
            item.TotalAmount = item.TotalBalance;
          } else {
            item.TotalAmount = (row.PayAmount / (row.Price / item.DiscountPrice)).toFixed(2);
          }

          item.PreferentialAmount = ((row.Price / item.DiscountPrice) * item.TotalAmount).toFixed(2);
        }
        item.cardDeductionAmount = item.TotalAmount;

        item.cardDiscountPrice = (item.PreferentialAmount - item.TotalAmount).toFixed(2);
        item.TotalPrice = (item.TotalBalance - item.TotalAmount).toFixed(2);
      } else {
        item.TotalAmount = '';
        item.cardDeductionAmount = 0;
        item.cardDiscountPrice = 0;
        item.PreferentialAmount = 0;
        item.TotalPrice = item.TotalBalance;
      }
      that.savingCardDeductionPrice(row);
      if (parseFloat(amount) < parseFloat(item.TotalBalance) && parseFloat(row.PayAmount) < 0.1) {
        item.cardDiscountPrice = (parseFloat(item.cardDiscountPrice) + parseFloat(row.PayAmount)).toFixed(2);
        row.PayAmount = 0;
        that.savingCardDeductionPrice(row);
      }
    },
    // 储值卡抵扣金额变化
    savingCardPriceChange: function (row, item) {
      var that = this;
      var amount;
      if (item.PriceType == 1) {
        let payAmount = (
          row.totalPrice -
          row.DeductionProjectAmount -
          row.CardDeductionAmount -
          row.ArrearAmount +
          parseFloat(item.cardDiscountPrice) +
          parseFloat(item.cardDeductionAmount)
        ).toFixed(2);
        amount = ((item.DiscountPrice / 10) * payAmount).toFixed(2);
        if (parseFloat(amount) > parseFloat(item.TotalBalance)) {
          if (parseFloat(item.TotalAmount) > parseFloat(item.TotalBalance)) {
            item.TotalAmount = item.TotalBalance;
            that.$message.error('卡扣金额不能大于' + item.TotalBalance + '元');
          }
        } else {
          if (parseFloat(item.TotalAmount) > parseFloat(amount)) {
            item.TotalAmount = amount;
            that.$message.error('卡扣金额不能大于' + amount + '元');
          }
        }
        item.PreferentialAmount = (item.TotalAmount / (item.DiscountPrice / 10)).toFixed(2);
      } else {
        var payAmount = (
          row.totalPrice -
          row.DeductionProjectAmount -
          row.CardDeductionAmount -
          row.ArrearAmount +
          parseFloat(item.cardDiscountPrice) +
          parseFloat(item.cardDeductionAmount)
        ).toFixed(2);
        amount = ((payAmount * item.DiscountPrice) / row.Price).toFixed(2);
        if (parseFloat(amount) > parseFloat(item.TotalBalance)) {
          if (parseFloat(item.TotalAmount) > parseFloat(item.TotalBalance)) {
            item.TotalAmount = item.TotalBalance;
            that.$message.error('卡扣金额不能大于' + item.TotalBalance + '元');
          }
        } else {
          if (parseFloat(item.TotalAmount) > parseFloat(amount)) {
            item.TotalAmount = amount;
            that.$message.error('卡扣金额不能大于' + amount + '元');
          }
        }
        item.PreferentialAmount = ((row.Price / item.DiscountPrice) * item.TotalAmount).toFixed(2);
      }
      item.cardDiscountPrice = (item.PreferentialAmount - item.TotalAmount).toFixed(2);
      item.TotalPrice = (item.TotalBalance - item.TotalAmount).toFixed(2);
      item.cardDeductionAmount = item.TotalAmount;
      that.savingCardDeductionPrice(row);
      if (parseFloat(amount) < parseFloat(item.TotalBalance) && parseFloat(row.PayAmount) < 0.1) {
        item.cardDiscountPrice = (parseFloat(item.cardDiscountPrice) + parseFloat(row.PayAmount)).toFixed(2);
        row.PayAmount = 0;
        that.savingCardDeductionPrice(row);
      }
    },
    // 套餐卡储值卡抵扣选择

    // 储值卡抵扣确认
    submitSavingCard: function () {
      var that = this;
      that.selectGoods[that.goodsIndex] = that.selectGood;
      var savingCardAll = that.savingCardAll;
      that.savingCardSomeGoods.forEach(function (item) {
        that.savingCardAll.forEach(function (sav) {
          if (item.ID == sav.ID) {
            item.TotalBalance = sav.TotalPrice;
          }
        });
      });
      savingCardAll = Enumerable.from(savingCardAll)
        .where(function (i) {
          return i.checked;
        })
        .toArray();
      that.selectGoods[that.goodsIndex].savingCardDeduction = savingCardAll;
      switch (that.type) {
        case 1:
          that.selectProject = that.selectGoods;
          break;
        case 2:
          that.selectProduct = that.selectGoods;
          break;
      }
      that.dialogDeduction = false;
      that.payAmountData();
    },

    // 储值卡抵扣总金额
    savingCardDeductionPrice: function (row) {
      var that = this;
      var CardDeductionAmount = 0;
      var cardDiscountPrice = 0;
      var savingCardAll = Enumerable.from(that.savingCardAll)
        .where(function (i) {
          return i.checked;
        })
        .toArray();
      savingCardAll.forEach(function (item) {
        var cardDeductionAmount = parseFloat(item.cardDeductionAmount) || 0;
        if (cardDeductionAmount == '') {
          cardDeductionAmount = 0;
        }
        CardDeductionAmount = (parseFloat(CardDeductionAmount) + cardDeductionAmount).toFixed(2);
        cardDiscountPrice = (parseFloat(cardDiscountPrice) + parseFloat(item.cardDiscountPrice)).toFixed(2);
      });
      row.CardDiscountPrice = cardDiscountPrice;
      row.CardDeductionAmount = CardDeductionAmount;
      that.payPriceData(row);
      if (row.type == 5) {
        that.packageSavingCardDeductionAmount(row);
      }
    },

    // 储值卡抵扣选择(通用)
    savingCheckedAllChange: function (item) {
      var that = this;
      // var savingCardPrice = 0;
      var PayAmount = 0;
      // that.selectSavingCard.forEach(function(item) {
      //   savingCardPrice = (
      //     parseFloat(savingCardPrice) + parseFloat(item.PayAmount)
      //   ).toFixed(2);
      // });
      // that.selectPackageCard.forEach(function(item) {
      //   item.noLargess.forEach(function(noLargess) {
      //     if (noLargess.isCardType == 2) {
      //       savingCardPrice = (
      //         parseFloat(savingCardPrice) + parseFloat(noLargess.PayAmount)
      //       ).toFixed(2);
      //     }
      //   });
      // });
      PayAmount = (parseFloat(that.PayAmount) - parseFloat(that.savingCardPrice) + parseFloat(that.PayCashAmount)).toFixed(2);
      if (item.checked) {
        if (parseFloat(item.TotalBalance) < parseFloat(PayAmount)) {
          item.TotalAmount = item.TotalBalance;
        } else {
          item.TotalAmount = PayAmount;
        }
        item.TotalPrice = (item.TotalBalance - item.TotalAmount).toFixed(2);
      } else {
        item.TotalAmount = '';
        item.TotalPrice = item.TotalBalance;
      }
      item.cardDeductionAmount = item.TotalAmount;
      that.savingDeductionPriceAll();
    },
    // 储值卡抵扣金额变化（通用）
    savingPriceAllChange: function (item) {
      var that = this;
      // var savingCardPrice = 0;
      var PayAmount = 0;
      // that.selectSavingCard.forEach(function(item) {
      //   savingCardPrice = (
      //     parseFloat(savingCardPrice) + parseFloat(item.PayAmount)
      //   ).toFixed(2);
      // });
      // that.selectPackageCard.forEach(function(item) {
      //   item.noLargess.forEach(function(noLargess) {
      //     if (noLargess.isCardType == 2) {
      //       savingCardPrice = (
      //         parseFloat(savingCardPrice) + parseFloat(noLargess.PayAmount)
      //       ).toFixed(2);
      //     }
      //   });
      // });
      PayAmount = (
        parseFloat(that.PayAmount) -
        parseFloat(that.savingCardPrice) +
        parseFloat(item.cardDeductionAmount) +
        parseFloat(that.PayCashAmount)
      ).toFixed(2);
      if (parseFloat(item.TotalBalance) < parseFloat(PayAmount)) {
        if (parseFloat(item.TotalAmount) > parseFloat(item.TotalBalance)) {
          item.TotalAmount = item.TotalBalance;
          that.$message.error('卡扣金额不能大于' + item.TotalBalance + '元');
        }
      } else {
        if (parseFloat(item.TotalAmount) > parseFloat(PayAmount)) {
          item.TotalAmount = PayAmount;
          that.$message.error('卡扣金额不能大于' + PayAmount + '元');
        }
      }
      item.cardDeductionAmount = item.TotalAmount;
      item.TotalPrice = item.TotalBalance - item.TotalAmount;
      that.savingDeductionPriceAll();
    },
    // 储值卡抵扣金额(通用)
    savingDeductionPriceAll: function () {
      var that = this;
      var cardDeductionAmount = 0;
      var savingCardAllGoods = Enumerable.from(that.savingCardAllGoods)
        .where(function (i) {
          return i.checked;
        })
        .toArray();
      savingCardAllGoods.forEach(function (item) {
        cardDeductionAmount = (parseFloat(cardDeductionAmount) + (parseFloat(item.TotalAmount) || 0)).toFixed(2);
      });
      that.cardDeductionAmount = cardDeductionAmount;
      that.payAmountData();
    },

    // 数量
    numberChange: function (row) {
      console.log("largessChange===",row);
      var that = this;
      row.discount = 100;
      row.discountPrice = 0;
      row.ArrearAmount = 0;
      if (row.type != 2) {
        that.deductionReset(row);
        row.savingCardDeduction = [];
        if (row.isShowSelectMemberAmout && row.isShowMemberAmout && !row.IsLargess) {
          if (row.MemberAmoutPriceType == 1) {
            let Amount = (row.Price * row.number * row.MemberAmoutDiscountPrice).toFixed(2);
            row.Amount = Amount;
            row.totalPrice = Amount;
            row.MemberPreferentialTotalAmount = row.Price * row.number - Amount;
          }
          if (row.MemberAmoutPriceType == 2) {
            let Amount = (row.MemberAmoutDiscountPrice * row.number).toFixed(2);
            row.Amount = Amount;
            row.totalPrice = Amount;
            row.MemberPreferentialTotalAmount = row.Price * row.number - Amount;
          }
        } else {
          row.Amount = ((row.Price * row.number * row.discount) / 100).toFixed(2);
          row.totalPrice = ((row.Price * row.number * row.discount) / 100).toFixed(2);
        }
        that.payPriceData(row);
      } else {
        row.LargessPrice = (row.largessPrice * row.number).toFixed(2);
        row.Amount = (row.Price * row.number).toFixed(2);
        row.totalPrice = row.Amount;
        row.PayAmount = row.Amount;
      }
        

      if (row.type == 5) {
        row.noLargess.forEach(function (item) {
          item.number = row.number;
          item.totalAmount = (row.number * item.TotalPrice).toFixed(2);
          item.TotalAmount = item.totalAmount;
          item.cardDeductionAmount = 0;
          item.cardDiscountPrice = 0;
          item.ArrearAmount = '';
          item.PayAmount = (row.number * item.TotalPrice).toFixed(2);
        });
        row.largess.forEach(function (item) {
          item.number = row.number;
        });
      }
      that.payAmountData();
    },
    // 赠送
    largessChange: function (row) {
      var that = this;
      if (row.IsLargess) {
        row.discount = 100;
        row.discountPrice = 0;
        that.deductionReset(row);
        row.savingCardDeduction = [];
        row.Amount = ((row.Price * row.number * row.discount) / 100).toFixed(2);
        row.TotalAmount = row.Amount;
        row.ArrearAmount = 0;
        row.PayAmount = 0;
        row.DeductionProjectAmount = 0;
        row.discountPrice = 0;
        row.CardDeductionAmount = 0;
        row.CardDiscountPrice = 0;
        row.totalPrice = row.Amount;
        row.MemberPreferentialAmount = 0;
        row.MemberPreferentialTotalAmount = 0;

        if (row.type == 2) {
          row.LargessPrice = (row.largessPrice * row.number).toFixed(2);
        }
        if (row.type == 5) {
          row.noLargess.forEach(function (item) {
            item.number = row.number;
            item.totalAmount = (row.number * item.TotalPrice).toFixed(2);
            item.cardDeductionAmount = 0;
            item.cardDiscountPrice = 0;
            item.ArrearAmount = '';
            item.PayAmount = 0;
          });
        }
      } else {
        if (row.isShowSelectMemberAmout && row.isShowMemberAmout) {
          if (row.MemberAmoutPriceType == 1) {
            let Amount = (row.Price * row.number * row.MemberAmoutDiscountPrice).toFixed(2);
            row.Amount = Amount;
            row.totalPrice = Amount;
            row.MemberPreferentialTotalAmount = row.Price * row.number - Amount;
            row.PayAmount = Amount;
            row.TotalAmount = Amount;
          }
          if (row.MemberAmoutPriceType == 2) {
            let Amount = (row.MemberAmoutDiscountPrice * row.number).toFixed(2);
            row.Amount = Amount;
            row.totalPrice = Amount;
            row.MemberPreferentialTotalAmount = row.Price * row.number - Amount;
            row.PayAmount = Amount;
            row.TotalAmount = Amount;
          }
        } else {
          let Amount = ((row.Price * row.number * row.discount) / 100).toFixed(2);
          row.Amount = Amount;
          row.totalPrice = Amount;
          row.PayAmount = Amount;
        }
        if (row.type == 5) {
          row.noLargess.forEach(function (item) {
            item.number = row.number;
            item.totalAmount = (row.number * item.TotalPrice).toFixed(2);
            item.cardDeductionAmount = 0;
            item.cardDiscountPrice = 0;
            item.ArrearAmount = '';
            item.PayAmount = item.totalAmount;
          });
        }
      }

      that.payAmountData();
    },
 
    // 储值卡金额变化
    savingAmountChange: function (row) {
      var that = this;
      row.ArrearAmount = 0;
      row.Amount = row.Amount - 0;
      row.PayAmount = (row.Amount - row.ArrearAmount).toFixed(2);
      that.payAmountData();
    },
    payPriceData (item) {
      if (item.type == 2) {
        item.PayAmount = (item.Amount - (item.ArrearAmount || 0)).toFixed(2);
      } else {
        item.DeductionProjectAmount = (parseFloat(item.discountPrice) + parseFloat(item.CardDiscountPrice)).toFixed(2);
        if (!item.IsLargess) {
          item.PayAmount = (item.totalPrice - item.DeductionProjectAmount - item.CardDeductionAmount - item.ArrearAmount).toFixed(2);
        } else {
          item.PayAmount = 0;
        }
        item.TotalAmount = (item.Amount - item.CardDiscountPrice).toFixed(2);
      }
    },
    // 卡抵扣重置
    deductionReset: function (row) {
      var that = this;
      row.CardDeductionAmount = 0;
      row.CardDiscountPrice = 0;
      that.savingCardSomeGoods.forEach(function (item) {
        row.savingCardDeduction.forEach(function (sav) {
          if (item.ID == sav.ID) {
            item.TotalBalance = (parseFloat(item.TotalBalance) + parseFloat(sav.TotalAmount)).toFixed(2);
          }
        });
      });
    },
    // 删除会员卡抵扣重置
    deductionAllReset: function () {
      var that = this;
      that.selectProject.forEach(function (item) {
        item.CardDeductionAmount = 0;
        item.CardDiscountPrice = 0;
        item.DeductionProjectAmount = 0;
        item.discountPrice = 0;
        item.discount = 100;
        item.Amount = (item.number * item.Price).toFixed(2);
        item.totalPrice = item.Amount;
        item.TotalAmount = item.Amount;
        item.ArrearAmount = 0;
        item.PayAmount = item.Amount;
        item.IsLargess = false;
        item.savingCardDeduction = [];
      });

      that.selectProduct.forEach(function (item) {
        item.CardDeductionAmount = 0;
        item.CardDiscountPrice = 0;
        item.DeductionProjectAmount = 0;
        item.discountPrice = 0;
        item.discount = 100;
        item.Amount = (item.number * item.Price).toFixed(2);
        item.totalPrice = item.Amount;
        item.TotalAmount = item.Amount;
        item.ArrearAmount = 0;
        item.PayAmount = item.Amount;
        item.IsLargess = false;
        item.savingCardDeduction = [];
      });
    },
    // 计算总金额
    payAmountData() {
      var that = this;
      var payAmount = 0;
      var TotalAmount = 0; //订单金额
      var ArrearAmount = 0; //欠款金额
      var PricePreferentialAmount = 0;
      var CardPreferentialAmount = 0;
      var CardDeductionAmount = 0;
      
      let CardDiscountPrice = 0;
      var modifyPayAmount = 0;
      let memberPreferentialAmount = 0;
      let originalTotalAmount = 0;
      let totalLength = 0;
      let changOrderAmount = 0;
      that.selectProject.forEach(function (item) {
        if (!item.IsLargess) {
          payAmount = (parseFloat(payAmount) + parseFloat(item.PayAmount)).toFixed(2);
          ArrearAmount = (parseFloat(ArrearAmount) + parseFloat(item.ArrearAmount)).toFixed(2);
          TotalAmount = (parseFloat(TotalAmount) + parseFloat(item.TotalAmount)).toFixed(2);
          PricePreferentialAmount = (parseFloat(PricePreferentialAmount) + parseFloat(item.discountPrice)).toFixed(2);
          CardPreferentialAmount = (parseFloat(CardPreferentialAmount) + parseFloat(item.CardDiscountPrice)).toFixed(2);
          CardDeductionAmount = (parseFloat(CardDeductionAmount) + parseFloat(item.CardDeductionAmount)).toFixed(2);
          memberPreferentialAmount += parseFloat(item.MemberPreferentialTotalAmount || 0);
          if (item.IsModifyPrice) {
            changOrderAmount += Number(item.PayAmount || 0);
            modifyPayAmount = (parseFloat(modifyPayAmount) + parseFloat(item.PayAmount) + parseFloat(item.MemberPreferentialTotalAmount || 0)).toFixed(2);
            originalTotalAmount = (parseFloat(originalTotalAmount || 0) + parseFloat(item.Price * item.number) ).toFixed(2);
            CardDiscountPrice += Number(item.CardDiscountPrice || 0);
            totalLength += 1;
          }
        }
      });

      that.selectProduct.forEach(function (item) {
        if (!item.IsLargess) {
          payAmount = (parseFloat(payAmount) + parseFloat(item.PayAmount)).toFixed(2);
          ArrearAmount = (parseFloat(ArrearAmount) + parseFloat(item.ArrearAmount)).toFixed(2);
          TotalAmount = (parseFloat(TotalAmount) + parseFloat(item.TotalAmount)).toFixed(2);
          PricePreferentialAmount = (parseFloat(PricePreferentialAmount) + parseFloat(item.discountPrice)).toFixed(2);
          CardPreferentialAmount = (parseFloat(CardPreferentialAmount) + parseFloat(item.CardDiscountPrice)).toFixed(2);
          CardDeductionAmount = (parseFloat(CardDeductionAmount) + parseFloat(item.CardDeductionAmount)).toFixed(2);
          memberPreferentialAmount += parseFloat(item.MemberPreferentialTotalAmount || 0);
          if (item.IsModifyPrice) {
            changOrderAmount += Number(item.PayAmount || 0);
            modifyPayAmount = (parseFloat(modifyPayAmount) + parseFloat(item.PayAmount) + parseFloat(item.MemberPreferentialTotalAmount || 0)).toFixed(2);
            originalTotalAmount = (parseFloat(originalTotalAmount || 0) + parseFloat(item.Price * item.number) ).toFixed(2);
            CardDiscountPrice += Number(item.CardDiscountPrice || 0);
            totalLength += 1;
          }
        }
      });
      that.totalLength = totalLength;

      that.Amount = TotalAmount;
      that.PayAmount = (payAmount - that.cardDeductionAmount).toFixed(2);
      that.modifyPayAmount = parseFloat(modifyPayAmount - that.cardDeductionAmount).toFixed(2);
      that.changOrderAmount = changOrderAmount;
      console.log("🚀 ~ payAmountData ~ changOrderAmount:", changOrderAmount)
      
      that.oldPayAmount = that.modifyPayAmount;
      that.originalTotalAmount = originalTotalAmount;

      that.ArrearAmount = ArrearAmount;
      that.PricePreferentialAmount = PricePreferentialAmount;
      that.CardPreferentialAmount = CardPreferentialAmount;
      that.CardDeductionAmount = CardDeductionAmount;
      that.CardDiscountPrice = CardDiscountPrice;
      that.payList = [{ PayMethodID: '', Amount: '', price: 0 }];
      that.PayCashAmount = 0;
      that.payTotalPrice = that.PayAmount;
      //计算购买储值卡金额，用于结账储值卡不可抵扣储值卡
      that.savingCardPrice = 0;
      that.memberPreferentialAmount = parseFloat(memberPreferentialAmount).toFixed(2);
    },
    /*  */
    reviewHandledScale() {
      const that = this;
      const isProductHandleScale = that.selectProject.some((i) => {
        const isSale =
          i.saleHandlerList &&
          i.saleHandlerList.some((h) => {
            return h.Employee.some((e) => {
              return !e.Discount && e.Discount !== 0;
            });
          });
        const isTreat =
          i.consumeHandlerList &&
          i.consumeHandlerList.some((t) => {
            return t.Employee.some((e) => {
              return !e.Discount && e.Discount !== 0;
            });
          });
        return isSale || isTreat;
      });
      const isProjectHandleScale = that.selectProduct.some((i) => {
        const isSale =
          i.saleHandlerList &&
          i.saleHandlerList.some((h) => {
            return h.Employee.some((e) => {
              return !e.Discount && e.Discount !== 0;
            });
          });
        const isTreat =
          i.consumeHandlerList &&
          i.consumeHandlerList.some((t) => {
            return t.Employee.some((e) => {
              return !e.Discount && e.Discount !== 0;
            });
          });
        return isSale || isTreat;
      });

      return isProductHandleScale || isProjectHandleScale;
    },
    // 结账
    billClick: function () {
      var that = this;
      if ((that.selectProject.length <= 0) & (that.selectProduct.length <= 0)) {
        that.$message.error('请选择商品');
        return;
      }
      if (that.getBillDate() == null) {
        that.$message.error('请输入补录日期');
        return;
      }
      if (that.reviewHandledScale()) {
        that.$message.error('请输入经手人比例');
        return;
      }

      /* 判断项目是否 存在未添加经手人的 */
      let isProjectHanlder = that.selectProject.some((i) => {
        return (
          !i.saleHandlerList ||
          !i.saleHandlerList.some((j) => j.Employee && j.Employee.length) ||
          !i.consumeHandlerList ||
          !i.consumeHandlerList.some((j) => j.Employee && j.Employee.length)
        );
      });

      /* 判断产品是否 存在未添加经手人的 */
      let isProductHanlder = that.selectProduct.some((i) => {
        return (
          !i.saleHandlerList ||
          !i.saleHandlerList.some((j) => j.Employee && j.Employee.length) ||
          !i.consumeHandlerList ||
          !i.consumeHandlerList.some((j) => j.Employee && j.Employee.length)
        );
      });
      if (isProjectHanlder || isProductHanlder) {
        that
          .$confirm('存在未添加经手人的商品，是否继续收款', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          })
          .then(() => {
            that.billClick_method();
          })
          .catch(() => {
            that.$message({
              type: 'info',
              message: '已取消操作',
            });
          });
      } else {
        that.billClick_method();
      }
    },
    /**    */
    billClick_method() {
      let that = this;
      /**  •• 项目•••••••••••••••• •••••••••••••••••• •••••••••••••••••• ••••••••••••••••••  */
      let isEntit = that.selectProject.some((i) => {
        return (i.Consumable || []).some((j) => !j.EntityID);
      });
      if (isEntit) {
        that.$message.error('请选择项目耗材出库仓库');
        return false;
      }
      let isProject = that.selectProject.some((i) => {
        return (i.Consumable || []).some((j) => !j.Quantity || !j.EntityID);
      });

      if (isProject) {
        that.$message.error('请填写项目耗材出库数量');
        return false;
      }

      that.dialogBill = true;

      that.savingCardAllGoods = Enumerable.from(that.savingCardAllGoods)
        .select((val) => ({
          checked: false,
          AccountID: val.AccountID,
          Balance: val.Balance,
          ID: val.ID,
          LargessBalance: val.LargessBalance,
          SavingCardName: val.SavingCardName,
          TotalBalance: val.TotalBalance,
          TotalPrice: val.TotalBalance,
          Type: val.Type,
          TotalAmount: '',
        }))
        .toArray();
      that.savingDeductionPriceAll();
      that.payAmountData();
    },
    // 支付方式支付金额变化
    payPriceChange: function (item) {
      var that = this;
      var payAmount = (parseFloat(that.PayAmount) + parseFloat(item.price)).toFixed(2);
      if (parseFloat(item.Amount) > parseFloat(payAmount)) {
        item.Amount = payAmount;
      }
      item.price = item.Amount;
      that.paymentAmountData();
    },
    payMethodChange: function (item) {
      var that = this;
      if (item.PayMethodID == '') {
        item.Amount = '';
      } else {
        if (item.Amount == '') {
          item.Amount = parseFloat(that.PayAmount).toFixed(2);
        }
      }
      that.payPriceChange(item);
    },
    // 支付方式金额总计
    paymentAmountData: function () {
      var that = this;
      var amount = 0;
      that.payList.forEach(function (item) {
        amount = (parseFloat(amount) + (parseFloat(item.price) || 0)).toFixed(2);
      });
      that.PayCashAmount = amount;
      that.PayAmount = (that.payTotalPrice - amount).toFixed(2);
    },
    // 删除支付
    removePayClick: function (index) {
      var that = this;
      that.payList.splice(index, 1);
      that.paymentAmountData();
    },
    // 添加支付
    addPayclick: function () {
      var that = this;
      that.payList.push({ PayMethodID: '', Amount: '', price: 0 });
    },
    // 确认结帐
    submitPayClick: function () {
      var that = this;
      that.modalLoading = true;
      if (that.PayAmount != 0) {
        that.$message.error({
          message: '请填写收款金额。',
          duration: 2000,
        });
        that.modalLoading = false;
        return;
      }
      that.selectProject.forEach(function (item) {
        item.ProjectSaleHandler = [];
        item.ProjectTreatHandler = [];
        item.projectSavingCardDeduction = [];

        item.saleHandlerList.forEach(function (handler) {
          handler.Employee.forEach(function (employee) {
            item.ProjectSaleHandler.push({
              SaleHandlerID: employee.SaleHandlerID,
              EmployeeID: employee.EmployeeID,
              Scale: employee.Discount,
            });
          });
        });

        item.consumeHandlerList.forEach(function (handler) {
          handler.Employee.forEach(function (employee) {
            item.ProjectTreatHandler.push({
              TreatHandlerID: employee.TreatHandlerID,
              EmployeeID: employee.EmployeeID,
              Scale: employee.Discount,
            });
          });
        });

        item.projectSavingCardDeduction = Enumerable.from(item.savingCardDeduction)
          .select((val) => ({
            SavingCardAccountID: val.AccountID,
            DeductionAmount: val.TotalAmount,
            PreferentialAmount: val.cardDiscountPrice,
            Type: val.Type,
            ID: val.ID,
          }))
          .toArray();
      });

      that.selectProduct.forEach(function (item) {
        item.ProductSaleHandler = [];
        item.ProductTreatHandler = [];
        item.productSavingCardDeduction = [];
        item.saleHandlerList.forEach(function (handler) {
          handler.Employee.forEach(function (employee) {
            item.ProductSaleHandler.push({
              SaleHandlerID: employee.SaleHandlerID,
              EmployeeID: employee.EmployeeID,
              Scale: employee.Discount,
            });
          });
        });

        item.consumeHandlerList.forEach(function (handler) {
          handler.Employee.forEach(function (employee) {
            item.ProductTreatHandler.push({
              TreatHandlerID: employee.TreatHandlerID,
              EmployeeID: employee.EmployeeID,
              Scale: employee.Discount,
            });
          });
        });

        item.productSavingCardDeduction = Enumerable.from(item.savingCardDeduction)
          .select((val) => ({
            SavingCardAccountID: val.AccountID,
            DeductionAmount: val.TotalAmount,
            PreferentialAmount: val.cardDiscountPrice,
            Type: val.Type,
            ID: val.ID,
          }))
          .toArray();
      });

      var Project = Enumerable.from(that.selectProject)
        .select((val) => ({
          ProjectID: val.ID,
          Quantity: val.number,
          Price: parseFloat(val.Price).toFixed(2),
          TotalAmount: parseFloat(val.TotalAmount).toFixed(2),
          PayAmount: parseFloat(val.PayAmount).toFixed(2),
          ArrearAmount: parseFloat(val.ArrearAmount).toFixed(2),
          SavingCardDeductionAmount: parseFloat(val.CardDeductionAmount).toFixed(2),
          PricePreferentialAmount: parseFloat(val.discountPrice).toFixed(2),
          CardPreferentialAmount: parseFloat(val.CardDiscountPrice).toFixed(2),
          IsLargess: val.IsLargess,
          ProjectSaleHandler: val.ProjectSaleHandler,
          ProjectTreatHandler: val.ProjectTreatHandler,
          SavingCardDeduction: val.projectSavingCardDeduction,
          Remark: val.Remark,
          MemberPreferentialAmount: parseFloat(val.MemberPreferentialTotalAmount || 0).toFixed(2),
          Consumable: (val.Consumable || []).map((i) => {
            return {
              EntityID: i.EntityID, //门店ID
              ProductID: i.ProductID, //产品编号
              MinimumUnitID: i.MinimumUnitID, //最小包装单位id
              Quantity: i.Quantity, //库存数量
            };
          }),
        }))
        .toArray();

      var Product = Enumerable.from(that.selectProduct)
        .select((val) => ({
          ProductID: val.ID,
          Quantity: val.number,
          Price: parseFloat(val.Price).toFixed(2),
          TotalAmount: parseFloat(val.TotalAmount).toFixed(2),
          PayAmount: parseFloat(val.PayAmount).toFixed(2),
          ArrearAmount: parseFloat(val.ArrearAmount).toFixed(2),
          SavingCardDeductionAmount: parseFloat(val.CardDeductionAmount).toFixed(2),
          PricePreferentialAmount: parseFloat(val.discountPrice).toFixed(2),
          CardPreferentialAmount: parseFloat(val.CardDiscountPrice).toFixed(2),
          IsLargess: val.IsLargess,
          Remark: val.Remark,
          MemberPreferentialAmount: parseFloat(val.MemberPreferentialTotalAmount || 0).toFixed(2),
          ProductSaleHandler: val.ProductSaleHandler,
          ProductTreatHandler: val.ProductTreatHandler,
          SavingCardDeduction: val.productSavingCardDeduction,
        }))
        .toArray();

      var payList = Enumerable.from(that.payList)
        .where(function (i) {
          return i.PayMethodID != '' && i.Amount != '';
        })
        .select((val) => ({
          PayMethodID: val.PayMethodID,
          Amount: val.Amount,
        }))
        .toArray();

      that.SavingCardDeduction = Enumerable.from(that.savingCardAllGoods)
        .where(function (i) {
          return i.checked && parseFloat(i.TotalAmount) != 0;
        })
        .select((val) => ({
          Type: val.Type,
          SavingCardAccountID: val.AccountID,
          DeductionAmount: val.TotalAmount,
          ID: val.ID,
        }))
        .toArray();

      var CardDeductionAmount = (parseFloat(that.CardDeductionAmount) + parseFloat(that.cardDeductionAmount)).toFixed(2);
      var params = {
        BillID: that.BillID,
        CustomerID: that.customerID,
        BillDate: that.getBillDate(),
        Amount: parseFloat(that.Amount).toFixed(2),
        PayAmount: parseFloat(that.PayCashAmount).toFixed(2),
        ArrearAmount: parseFloat(that.ArrearAmount).toFixed(2),
        CardDeductionAmount: parseFloat(CardDeductionAmount).toFixed(2),
        PricePreferentialAmount: parseFloat(that.PricePreferentialAmount).toFixed(2),
        CardPreferentialAmount: parseFloat(that.CardPreferentialAmount).toFixed(2),
        MemberPreferentialAmount: parseFloat(that.memberPreferentialAmount || 0).toFixed(2),
        Remark: that.Remark,
        Project: Project,
        Product: Product,
        PayMethod: payList,
        SavingCardDeduction: that.SavingCardDeduction,
      };
      agileSellAPI
        .createAgilSaleBill(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.selectProject = [];
            that.selectProduct = [];

            that.orderAmount = that.Amount;
            that.Amount = 0; //订单金额
            that.PayAmount = 0; //现金支付金额
            that.payTotalPrice = 0; //待支付总金额
            that.ArrearAmount = 0; //欠款金额
            that.cardDeductionAmount = 0;
            that.CardDeductionAmount = 0; // 储值卡抵扣金额
            that.PricePreferentialAmount = 0; //手动改价优惠金额
            that.CardPreferentialAmount = 0; //卡优惠金额
            that.orderNumber = res.Data.SaleBillID;
            that.consumeOrderNumber = res.Data.TreatBillID;
            that.dialogBill = false;
            that.dialogPay = true;
            if (that.customerID != null) {
              that.savingCardAllGoodsData();
              that.savingCardSomeGoodsData();
            }
            // that.dialogPay = true;
            // that.$message.success({ message: "收款完成", duration: 3000 });
            that.getOrderDetail();

            that.showTreatInfo();
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.modalLoading = false;
        });
    },
    /** 消耗详情 详情 数据    */
    showTreatInfo: function () {
      var that = this;
      let params = {
        ID: that.consumeOrderNumber,
      };
      that.printConsumeLoading = true;
      orderAPI
        .treatBillinfo(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.treatInfo = res.Data;
            that.getPrintTemplate_list('treatbill');
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.printConsumeLoading = false;
          that.getReceiptConfig();
        });
    },
    /** 获取销售订单详情 */
    getOrderDetail() {
      var that = this;
      that.printSaleLoading = true;
      var params = {
        SaleBillID: that.orderNumber,
      };

      orderSellAPI
        .getOrderDetail(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.saleOrderDetail = res.Data;
            that.getPrintTemplate_list('salebill');
          }
        })
        .finally(function () {
          that.printSaleLoading = false;
        });
    },
    /**  获取小票配置信息  */
    getReceiptConfig() {
      var that = this;
      cashierAPI
        .getReceiptConfigBill()
        .then((res) => {
          if (res.StateCode == 200) {
            that.cashierReceipt = res.Data;
          }
        })
        .finally(() => {});
    },
    /**  关闭结账成功弹窗清除 备注信息  */
    closeSucceedDialog() {
      this.Remark = '';
    },
    // 获取 当前卡项的序号
    getCurrentCardLength(savingCards, index, chileIndex) {
      let number = chileIndex + 1;
      for (let i = 0; i < savingCards.length; i++) {
        const element = savingCards[i];
        if (i < index) {
          number += element.Project.length;
        }
      }
      return number;
    },

    /**  卡项中项目总长度  */
    getCardTotalLength(Cards) {
      let number = 0;
      for (let i = 0; i < Cards.length; i++) {
        const element = Cards[i];
        number += element.Project.length;
      }
      return number;
    },
    /**  销售 订单详情 */
    printSellOrderReceipt() {
      var that = this;
      that.getReceiptConfig();
    },
    /**  消耗 消耗 */
    printConsumeOrderReceipt() {
      // var that = this;
    },
    /** 销售经手人   */
    async saleHandler_allHandler(GoodTypes) {
      let that = this;
      try {
        let params = {
          GoodTypes: GoodTypes,
        };
        let res = await API.saleHandler_allHandler(params);
        if (res.StateCode == 200) {
          that.saleAllHandlerList = res.Data.map((i) => {
            return {
              Name: i.Name,
              Employee: i.Employee.map((j) => {
                j.Checked = false;
                return Object.assign({}, j);
              }),
            };
          });
        } else {
          that.$message.error(res.Message);
        }
      } catch (error) {
        //that.$message.error(error);
      }
    },

    /**  消耗经手人  */
    async treatHandler_allHandler(GoodTypes) {
      let that = this;
      try {
        let params = {
          GoodTypes: GoodTypes,
        };
        let res = await API.treatHandler_allHandler(params);
        if (res.StateCode == 200) {
          that.treatAllHandlerList = res.Data.map((i) => {
            return {
              Name: i.Name,
              Employee: i.Employee.map((j) => {
                j.Checked = false;
                return Object.assign({}, j);
              }),
            };
          });
        } else {
          that.$message.error(res.Message);
        }
      } catch (error) {
        //that.$message.error(error);
      }
    },
    /** 获取模板列表   */
    async getPrintTemplate_list(TemplateType) {
      let that = this;
      let params = { TemplateType: TemplateType };
      let res = await orderSellAPI.getPrintTemplate_list(params);
      if (res.StateCode == 200) {
        if (TemplateType == 'salebill') {
          that.templateTypeList = res.Data;
        } else {
          that.treatTemplateTypeList = res.Data;
        }
      } else {
        that.$message.error(res.Message);
      }
      return res;
    },
    /** 获取会员折扣   */
    async saleGoods_CustomerDiscount(ID) {
      let params = {
        CustomerID: this.customerID,
      };
      params[saleGoodsTypeMap[this.goodsType]] = ID;
      let res = await API[customerDiscountAPIMap[this.goodsType]](params);
      if (res.StateCode == 200) {
        return res.Data;
      } else {
        this.$message.error(res.Message);
      }
    },
    /**  获取员工最低折扣权限  */
    saleBill_employeeDiscount() {
      let that = this;
      let params = {};
      API.saleBill_employeeDiscount(params)
        .then((res) => {
          if (res.StateCode == 200) {
            this.employeeDiscount = res.Data ? res.Data.Discount : null;
          } else {
            that.$message.error(res.Message);
          }
        })
        .catch((fail) => {
          that.$message.error(fail);
        });
    },
  },
  mounted() {
    var that = this;
    if (localStorage.getItem('access-user')) {
      that.userName = JSON.parse(localStorage.getItem('access-user')).EmployeeName;
      that.entityName = JSON.parse(localStorage.getItem('access-user')).EntityName;
    }
    that.saleBill_employeeDiscount();
    that.getFastSaleGoodsGoodsType();
    that.getSaleGoodsProjectCategory();
    that.getSaleGoodsProductCategory();

    that.projectHandlerData();
    that.productHandlerData();
    that.treatProjectHandlerNet();
    that.treatProductHandlerNet();

    that.salePayMethodData();
    if (that.customerID != null) {
      that.savingCardAllGoodsData();
      that.savingCardSomeGoodsData();
    }
  },
};
</script>

<style lang="scss">
.agileSell {
  .sale_content {
    font-size: 13px;
    height: 100%;
    .project_left {
      border-right: 1px solid #eee;
      height: 100%;
      color: #303133;
      .el-tabs__content {
        div {
          line-height: 23px;
        }
      }
      .el-tabs {
        height: calc(100% - 62px);
        .el-tabs__header {
          margin: 0;
          .el-tabs__nav-scroll {
            // padding-left: 15px;
            margin-left: 15px;
          }
        }
        .el-tabs__content {
          height: calc(100% - 40px);
          .el-tab-pane {
            height: 100%;
            .category_project {
              height: 100%;
              .category {
                height: 100%;
                .category_select {
                  color: #039be5;
                }
              }
              .project {
                height: 100%;
                overflow: auto;
                .el-collapse {
                  .el-collapse-item__header {
                    background-color: #f5f7fa !important;
                    padding: 0 10px;
                  }
                  .el-collapse-item__content {
                    padding: 0;
                  }
                }
                .category_sub_list {
                  overflow-x: auto;
                  .category_sub_select {
                    color: #039be5;
                  }
                }
                .project_list {
                  // height: calc(100% - 53px);
                  height: 100%;
                }
                .producct_list {
                  // height: calc(100% - 53px);
                  height: 100%;
                }
              }
            }
          }
        }
      }
      .el-main {
        padding: 0px;
      }
      .el-footer {
        height: initial !important;
        padding: 10px;
      }
    }
    .project_right {
      height: 100%;
      .el-main {
        padding: 0;
        .row_header {
          background-color: #fff7f3;
          padding: 10px;
        }
        .employee_num {
          width: 90px;
          .el-input-group__append {
            padding: 0 10px;
          }
        }
        .el-form-item__label {
          font-size: 13px !important;
        }
      }
      .el-footer {
        height: initial !important;
        padding: 10px;
      }
    }
  }

  .el-icon-sort {
    color: #666;
    font-size: 14px;
    transform: rotate(90deg);
  }

  .dialog_bill_detail {
    background-color: #fff7f3;
    padding: 15px;
    border-radius: 5px;
  }

  .el-scrollbar_height {
    height: 100%;
    .el-scrollbar__wrap {
      overflow-x: hidden;
    }
  }

  .el-scrollbar_x_width {
    .el-scrollbar__wrap {
      .el-scrollbar__view {
        white-space: nowrap;
        .el-menu-item {
          display: inline-block;
        }
        .is-active {
          a {
            color: #ff8646;
          }
        }
      }
    }
  }

  .el-input__inner {
    padding: 0 0 0 10px;
  }

  .saving_discount {
    .el-table__row {
      background-color: #f5f7fa;
    }
  }

  .savingCardAmount {
    .el-input__inner {
      border-top-right-radius: 0px;
      border-bottom-right-radius: 0px;
    }
  }

  .savingCardLargessAmount {
    .el-input__inner {
      border-top-left-radius: 0px;
      border-bottom-left-radius: 0px;
    }
  }
  .el-divider {
    margin-top: 15px;
    margin-bottom: 15px;
  }
  .sell-el-divider {
    margin-top: 5px;
    margin-bottom: 5px;
  }
}

.orderInfoRemark {
  position: absolute;
  background-color: rgba($color: #333, $alpha: 0.3);
  top: 0;
  right: 0;
  width: 100%;
  height: calc(100% - 54px);
  z-index: 1;
  .infoRemarContent {
    padding: 0px 10px 10px;
    background-color: #fff;
    position: absolute;
    bottom: 0;
    right: 0;
    left: 0;
    // width: 100%;
    font-size: 13px;
    color: #666;
    .el-form {
      .el-form-item {
        .el-form-item__label {
          font-size: 13px;
        }
      }
    }
  }
  .v-enter-active,
  .v-leave-active {
    transition: all 0.2s ease;
  }
  .v-enter,
  .v-leave-to {
    transform: opacity 0.5s;
    opacity: 0;
  }
}
</style>
