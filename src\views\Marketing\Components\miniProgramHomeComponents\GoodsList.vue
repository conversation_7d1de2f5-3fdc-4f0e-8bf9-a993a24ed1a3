<template>
  <div class="GoodsList">
    <div v-if="ContentProperty.length > 0" class="goods-content">
      <div class="goods-content-item" v-for="(item, index) in ContentProperty" :key="index">
        <el-image :src="item.ImageURL" style="height: 170px; width: 170px" fit="cover"> </el-image>
        <div class="info">
          <div v-if="ConfigProperty.some((val) => val == 'Name')" class="title">
            <el-tag type="warning" size="mini">{{ item.GoodsTypeName }}</el-tag>
            <span class="color_333 font_14">{{ item.Name }}</span>
          </div>
          <div class="price-text">
            <div v-if="ConfigProperty.some((val) => val == 'Price')" class="color_red">¥{{ item.Price | NumFormat }}</div>
            <div v-if="ConfigProperty.some((val) => val == 'OriginalText')" class="through-price">
              {{ item.OriginalText }}
            </div>
          </div>
        </div>
      </div>
    </div>
    <div v-else class="goods-content">
      <div class="goods-content-item" v-for="(item, index) in 2" :key="index">
        <div class="empty"></div>
        <div class="info">
          <div class="title">
            <el-tag type="warning" size="mini" class="marrt_5">产品</el-tag>
            <span class="color_333 font_14">此处显示商品名称</span>
          </div>
          <div class="price-text" style="align-items: flex-end">
            <span class="color_red">¥999.00</span>
            <span class="marlt_5 color_999" style="text-decoration: line-through">¥1000</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "GoodsList",
  props: {
    ContentProperty: {
      type: Array,
      default: () => {
        return [];
      },
    },
    ConfigProperty: {
      type: Array,
      default: () => {
        return ["Name", "Price", "OriginalText"];
      },
    },
  },
  /**  引入的组件  */
  components: {},
  /**  Vue 实例的数据对象**/
  data() {
    return {
      plImage: [],
    };
  },
  /**计算属性  */
  computed: {},
  /**  方法集合  */
  methods: {},
  /** 监听数据变化   */
  watch: {},
  /**  实例创建完成之后  */
  created() {},
  /**  实例被挂载后调用  */
  mounted() {},
};
</script>

<style lang="scss">
.GoodsList {
  .commodity {
    width: 48%;
  }
  .commodity:nth-of-type(odd) {
    margin-right: 10px;
  }
  .goods-content {
    -webkit-flex-wrap: wrap;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    padding: 0 12px;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    .goods-content-item {
      width: calc(50% - 5px);
      float: left;
      box-sizing: border-box;
      background: #ffffff;
      margin-bottom: 10px;
      .empty {
        display: -webkit-flex;
        display: -ms-flexbox;
        display: flex;
        -webkit-align-items: center;
        -ms-flex-align: center;
        align-items: center;
        -webkit-justify-content: center;
        -ms-flex-pack: center;
        justify-content: center;
        position: relative;
        height: auto;
        background: #dbf5ff url("../../../../assets/img/goodsEmpty.png") no-repeat center;
        background-size: 63px 47px px;
        padding-bottom: 100%;
        height: 0px;
      }
      .info {
        background: #fff;
        padding: 12px 10px;
        .title {
          font-size: 14px;
          height: 36px;
          line-height: 18px;
          display: -webkit-box;
          overflow: hidden;
          box-orient: vertical;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
        }
        .price-text {
          font-size: 14px;
          display: flex;
          margin-top: 5px;
          .through-price{
            text-decoration: line-through;
            margin-left: 5px;
            color: #999999;
            font-size: 12px;
          }
        }
      }
    }
    // .goods-content-item:last-child{
    //   margin-bottom: 0px;
    // }
    // .goods-content-item:last-child(2){
    //   margin-bottom: 0px;
    // }


    .goods-content-item:nth-child(odd) {
      margin-right: 5px;
    }

    .goods-content-item:nth-child(even) {
      margin-left: 5px;
    }
  }
}
</style>
