import * as API from "@/api/index";

export default {
  // 获取门店
  getAllEntityApi: (params) => {
    return API.POST("api/entity/allEntity", params);
  },
  // 获取列表
  getdataListTreatApi: (params) => {
    return API.POST("api/channelTreatPerformanceDetailStatement/list", params);
  },
  // 导出
  exportDataDetailTreatApi: (params) => {
    return API.exportExcel(
      "api/channelTreatPerformanceDetailStatement/excel",
      params
    );
  },
};
