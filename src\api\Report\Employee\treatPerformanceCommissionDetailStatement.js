import * as API from "@/api/index";

export default {
  // 营业报表-实收统计
  getEmployeeTreatPerformanceCommissionDetail: (params) => {
    return API.POST("api/employeeTreatPerformanceCommissionDetailStatement/list", params);
  },
  // 54.10.员工消耗业绩提成明细导出
  exportemployeeTreatPerformanceCommissionDetailStatementt: (params) => {
    return API.exportExcel("api/employeeTreatPerformanceCommissionDetailStatement/excel", params);
  },
  /* 获取门店消耗业绩明细报表 */
  entitySaleGoodsDetailStatement_productAndProjectCategory: (params) => {
    return API.POST("api/entitySaleGoodsDetailStatement/productAndProjectCategory", params);
  },
  /* 查询客户等级 */
  customerLevel_all: (params) => {
    return API.POST("api/customerLevel/all", params);
  },
};
