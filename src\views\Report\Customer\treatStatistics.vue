<template>
  <div class="CustomerTreatStatistics content_body_nopadding" v-loading="loading">
    <el-tabs type="border-card" v-model="activeName" @tab-click="changeActiveName">
      <el-tab-pane label="客户消耗项目月度统计" name="month">
        <div class="nav_header">
          <el-form :inline="true" size="small" :model="searchFrom" @submit.native.prevent>
            <el-form-item label="月度筛选">
              <el-date-picker v-model="searchFrom.SearchDate" type="month" value-format="yyyy-MM" @change="handleSearch"
                :clearable="false" :picker-options="pickerOption"></el-date-picker>
            </el-form-item>
            <el-form-item label="客户信息">
              <el-input v-model="searchFrom.Name" clearable @keyup.enter.native="handleSearch" @clear="handleSearch"
                placeholder="请输入客户信息"></el-input>
            </el-form-item>
            <el-form-item v-if="storeEntityList.length > 1" label="所属组织">
              <el-select v-model="searchFrom.EntityID" clearable filterable placeholder="请选择客户所属组织"
                :default-first-option="true" @change="handleSearch">
                <el-option v-for="item in storeEntityList" :key="item.ID" :label="item.EntityName"
                  :value="item.ID"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="信息来源">
              <el-cascader v-model="searchFrom.SourceID" placeholder="请选择信息来源" :options="customerSource"
                :props="{ checkStrictly: true, children: 'Child', value: 'ID', label: 'Name', emitPath: false }"
                :show-all-levels="false" clearable filterable @change="handleSearch"></el-cascader>
            </el-form-item>
            <el-form-item label="会员等级">
              <el-select v-model="searchFrom.LevelID" clearable filterable placeholder="请选择会员等级"
                :default-first-option="true" @change="handleSearch">
                <el-option v-for="(item, index) in customerLevel" :key="index" :label="item.Name" :value="item.ID">
                </el-option>
              </el-select>
            </el-form-item>

            <el-form-item label="注册日期">
              <el-date-picker v-model="searchFrom.CreatedQueryDate" :picker-options="pickerOptions" unlink-panels
                type="daterange" range-separator="至" value-format="yyyy-MM-dd" start-placeholder="开始日期"
                end-placeholder="结束日期" @change="handleSearch"></el-date-picker>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" v-prevent-click @click="handleSearch">搜索</el-button>
            </el-form-item>
            <!-- <el-form-item>
              <el-button v-if="isExportTreatMouthStatistics" type="primary" size="small" :loading="downloadMonthLoading"
                @click="downloadMonthExcel">导出</el-button>
            </el-form-item> -->
            <el-form-item>
              <el-dropdown
                @command="downloadMonthExcel_command"
                v-if="isExportTreatMouthStatistics && isExportTreatMouthStatisticsDisPlayPhone"
                :loading="downloadLoading"
              >
                <el-button type="primary"> 导出<i class="el-icon-arrow-down el-icon--right"></i> </el-button>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item command="excelNoDisPlayPhone">导出</el-dropdown-item>
                  <el-dropdown-item command="excelDisPlayPhone">导出(手机号)</el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
              <el-button
                @click="downloadMonthExcel_command('excelNoDisPlayPhone')"
                v-else-if="isExportTreatMouthStatistics"
                type="primary"
                v-prevent-click
                :loading="downloadLoading"
              >
                导出
              </el-button>
              <el-button
                @click="downloadMonthExcel_command('excelDisPlayPhone')"
                v-else-if="isExportTreatMouthStatisticsDisPlayPhone"
                type="primary"
                v-prevent-click
                :loading="downloadLoading"
              >
                导出(手机号）
              </el-button>
            </el-form-item>

          </el-form>
        </div>
        <el-table size="small" :data="monthTableData" show-summary :summary-method="monthTableDataSummaries"
          ref="monthTable">
          <el-table-column prop="Name" label="客户姓名" width="80"></el-table-column>
          <el-table-column prop="PhoneNumber" label="手机号" width="120">
            <template slot-scope="scope">
              {{ scope.row.PhoneNumber | hidephone }}
            </template>
          </el-table-column>
          <el-table-column prop="Code" label="客户编号" width="80"></el-table-column>
          <el-table-column prop="EntityName" label="所属组织" width="120"></el-table-column>
          <el-table-column prop="CreatedOn" label="注册日期" width="150"></el-table-column>
          <el-table-column prop="SourceName" label="信息来源"></el-table-column>
          <el-table-column prop="LevelName" label="会员等级"></el-table-column>
          <el-table-column prop="TotalAmount" label="合计金额" width="80">
            <template slot-scope="scope">
              {{ scope.row.TotalAmount | Fixed_2 | NumFormat }}
            </template>
          </el-table-column>
          <el-table-column prop="Count" label="合计次数" width="80"></el-table-column>
          <el-table-column v-for="(item, index) in monthTableDataSum.DateData" :label="item.Date" :key="index"
            width="160" align="center">
            <el-table-column label="金额" width="80">
              <template slot-scope="scope">
                {{ getScopeDateData(scope.row.DateData, item, "Amount") | Fixed_2 | NumFormat }}
              </template>
            </el-table-column>
            <el-table-column label="到店" width="80" :prop="item.Date">
              <template slot-scope="scope">
                {{ getScopeDateData(scope.row.DateData, item, "Count") }}
              </template>
            </el-table-column>
          </el-table-column>
        </el-table>
        <div class="pad_15 text_right">
          <el-pagination background v-if="monthPaginations.total > 0" @current-change="handleMonthPageChange"
            :current-page.sync="monthPaginations.page" :page-size="monthPaginations.page_size"
            :layout="monthPaginations.layout" :total="monthPaginations.total"></el-pagination>
        </div>
      </el-tab-pane>
      <el-tab-pane label="客户消耗项目年度统计" name="year">
        <div class="nav_header">
          <el-form :inline="true" size="small" :model="searchYearFrom" @submit.native.prevent>

            <el-form-item label="年度筛选">
              <el-date-picker v-model="searchYearFrom.SearchDate" type="year" value-format="yyyy"
                @change="handleYearSearch" :clearable="false" :picker-options="pickerOption"></el-date-picker>
            </el-form-item>
            <el-form-item label="客户信息">
              <el-input v-model="searchYearFrom.Name" clearable @keyup.enter.native="handleYearSearch"
                @clear="handleYearSearch" placeholder="请输入客户信息"></el-input>
            </el-form-item>
            <el-form-item v-if="storeEntityList.length > 1" label="所属组织">
              <el-select v-model="searchYearFrom.EntityID" clearable filterable placeholder="请选择客户所属组织"
                :default-first-option="true" @change="handleYearSearch">
                <el-option v-for="item in storeEntityList" :key="item.ID" :label="item.EntityName"
                  :value="item.ID"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="信息来源">
              <el-cascader v-model="searchYearFrom.SourceID" placeholder="请选择信息来源" :options="customerSource"
                :props="{ checkStrictly: true, children: 'Child', value: 'ID', label: 'Name', emitPath: false }"
                :show-all-levels="false" clearable filterable @change="handleYearSearch"></el-cascader>
            </el-form-item>
            <el-form-item label="会员等级">
              <el-select v-model="searchYearFrom.LevelID" clearable filterable placeholder="请选择会员等级"
                :default-first-option="true" @change="handleYearSearch">
                <el-option v-for="(item, index) in customerLevel" :key="index" :label="item.Name" :value="item.ID">
                </el-option>
              </el-select>
            </el-form-item>

            <el-form-item label="注册日期">
              <el-date-picker v-model="searchYearFrom.CreatedQueryDate" :picker-options="pickerOptions" unlink-panels
                type="daterange" range-separator="至" value-format="yyyy-MM-dd" start-placeholder="开始日期"
                end-placeholder="结束日期" @change="handleYearSearch"></el-date-picker>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" v-prevent-click @click="handleYearSearch">搜索</el-button>
            </el-form-item>
            <!-- <el-form-item>
              <el-button v-if="isExportTreatYearStatistics" type="primary" size="small" :loading="downloadYearLoading"
                @click="downloadYearExcel">导出</el-button>
            </el-form-item> -->

            <el-form-item>
              <el-dropdown
                @command="downloadYearExcel_command"
                v-if="isExportTreatYearStatistics && isExportTreatYearStatisticsDisPlayPhone"
                :loading="downloadLoading"
              >
                <el-button type="primary"> 导出<i class="el-icon-arrow-down el-icon--right"></i> </el-button>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item command="excelNoDisPlayPhone">导出</el-dropdown-item>
                  <el-dropdown-item command="excelDisPlayPhone">导出(手机号)</el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
              <el-button
                @click="downloadYearExcel_command('excelNoDisPlayPhone')"
                v-else-if="isExportTreatYearStatistics"
                type="primary"
                v-prevent-click
                :loading="downloadLoading"
              >
                导出
              </el-button>
              <el-button
                @click="downloadYearExcel_command('excelDisPlayPhone')"
                v-else-if="isExportTreatYearStatisticsDisPlayPhone"
                type="primary"
                v-prevent-click
                :loading="downloadLoading"
              >
                导出(手机号）
              </el-button>
            </el-form-item>
          </el-form>
        </div>
        <el-table size="small" :data="yearTableData" show-summary :summary-method="yearTableDataSummaries"
          ref="yearTable">
          <el-table-column prop="Name" label="客户姓名" width="80"></el-table-column>
          <el-table-column prop="PhoneNumber" label="手机号" width="120">
            <template slot-scope="scope">
              {{ scope.row.PhoneNumber | hidephone }}
            </template>
          </el-table-column>
          <el-table-column prop="Code" label="客户编号" width="80"></el-table-column>
          <el-table-column prop="EntityName" label="所属组织" width="120"></el-table-column>
          <el-table-column prop="CreatedOn" label="注册日期" width="150"></el-table-column>
          <el-table-column prop="SourceName" label="信息来源"></el-table-column>
          <el-table-column prop="LevelName" label="会员等级"></el-table-column>
          <el-table-column prop="TotalAmount" label="合计金额" width="80">
            <template slot-scope="scope">
              {{ scope.row.TotalAmount | Fixed_2 | NumFormat }}
            </template>
          </el-table-column>
          <el-table-column prop="Count" label="合计次数" width="80"></el-table-column>
          <el-table-column v-for="(item, index) in yearTableDataSum.DateData" :label="item.Date" :key="index"
            width="120" align="center">
            <el-table-column label="金额" width="80">
              <template slot-scope="scope">
                {{ getScopeDateData(scope.row.DateData, item, "Amount") | Fixed_2 | NumFormat }}
              </template>
            </el-table-column>
            <el-table-column label="次数" width="80" :prop="item.Date">
              <template slot-scope="scope">
                {{ getScopeDateData(scope.row.DateData, item, "Count") }}
              </template>
            </el-table-column>
          </el-table-column>
        </el-table>
        <div class="pad_15 text_right">
          <el-pagination background v-if="yearPaginations.total > 0" @current-change="handleYearPageChange"
            :current-page.sync="yearPaginations.page" :page-size="yearPaginations.page_size"
            :layout="yearPaginations.layout" :total="yearPaginations.total"></el-pagination>
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import API from "@/api/Report/Customer/treatStatistics.js";
import EntityAPI from "@/api/Report/Common/entity";
import APICustomerLevel from "@/api/CRM/Customer/customerLevel";
import APICustomerSource from "@/api/CRM/Customer/customerSource";

const dayjs = require("dayjs");
const isoWeek = require("dayjs/plugin/isoWeek");
dayjs.extend(isoWeek);
export default {
  name: "CustomerTreatStatistics",

  beforeRouteEnter(to, from, next) {
    next((vm) => {
      vm.isExportTreatMouthStatistics = vm.$permission.permission(to.meta.Permission, "Report-Customer-TreatMouthStatistics-Export");
      vm.isExportTreatYearStatistics = vm.$permission.permission(to.meta.Permission, "Report-Customer-TreatYearStatistics-Export");


      vm.isExportTreatMouthStatisticsDisPlayPhone = vm.$permission.permission(to.meta.Permission, "Report-Customer-TreatMouthStatistics-ExportDisPlayPhone");
      vm.isExportTreatYearStatisticsDisPlayPhone = vm.$permission.permission(to.meta.Permission, "Report-Customer-TreatYearStatistics-ExportDisPlayPhone");
    });
  },
  props: {},
  /** 监听数据变化   */
  watch: {},
  /**  引入的组件  */
  components: {},
  /**  Vue 实例的数据对象**/
  data() {
    return {
      downloadLoading:false,
      isExportTreatMouthStatisticsDisPlayPhone: false,
      isExportTreatYearStatisticsDisPlayPhone:false,
      pickerOption:{
        disabledDate(time){
          return time.getTime() > Date.now();
        },
      },
      loading: false,
      isExportTreatMouthStatistics: false,
      isExportTreatYearStatistics: false,
      downloadMonthLoading: false,
      downloadYearLoading: false,
      activeName: "month",
      storeEntityList: [],
      searchFrom: {
        EntityID: null, //门店编号
        CreatedQueryDate: [],
        SearchDate: dayjs().format("YYYY-MM"), //时间筛选
        Name: "", //模糊搜索
        SourceID: null, //来源
        LevelID: null, //等级
      },

      searchYearFrom: {
        EntityID: null, //门店编号
        CreatedQueryDate: [],
        SearchDate: dayjs().format("YYYY"), //时间筛选
        Name: "", //模糊搜索
        SourceID: null, //来源
        LevelID: null, //等级
      },
      customerLevel: [],
      customerSource: [],

      pickerOptions: {
        shortcuts: [
          {
            text: "今天",
            onClick: (picker) => {
              let end = new Date();
              let start = new Date();
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "本周",
            onClick: (picker) => {
              let end = new Date();
              let weekDay = dayjs(end).isoWeekday();
              let start = dayjs(end)
                .subtract(weekDay - 1, "day")
                .toDate();
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "本月",
            onClick: (picker) => {
              let end = new Date();
              let start = dayjs(dayjs(end).format("YYYY-MM") + "01").toDate();
              picker.$emit("pick", [start, end]);
            },
          },
        ],
      },
      monthPaginations: {
        page: 1, // 当前位于哪页
        total: 0, // 总数
        page_size: 10, // 1页显示多少条
        layout: "total, prev, pager, next,jumper", // 翻页属性
      },
      yearPaginations: {
        page: 1, // 当前位于哪页
        total: 0, // 总数
        page_size: 10, // 1页显示多少条
        layout: "total, prev, pager, next,jumper", // 翻页属性
      },
      monthTableData: [],
      monthTableDataSum: {},

      yearTableData: [],
      yearTableDataSum: {},
    };
  },
  /**计算属性  */
  computed: {},
  /**  方法集合  */
  methods: {
    /**    */
    handleSearch() {
      let that = this;
      that.monthPaginations.page = 1;
      that.customerStatistics_monthlyTreatStatistics();
    },
    /**    */
    handleMonthPageChange(page) {
      let that = this;
      that.monthPaginations.page = page;
      that.customerStatistics_monthlyTreatStatistics();
    },
    /**    */
    changeActiveName() {
      // let that = this;
    },
    /**    */
    monthTableDataSummaries({ columns }) {
      const sums = [];
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = <span class="font_weight_600">合计</span>;
          return;
        }
        if (column.property == "Count") {
          sums[index] = <span class="font_weight_600">{this.monthTableDataSum.Count}</span>;
          // console.log("🚀 ~ file: saleStatistics.vue:372 ~ columns.forEach ~ index", index)
        }
        if (this.monthTableDataSum && this.monthTableDataSum.DateData && this.monthTableDataSum.DateData.length) {
          let temp = this.monthTableDataSum.DateData.find((i) => i.Date == column.property);
          if (temp) {
            sums[index] = <span class="font_weight_600">{temp.Count}</span>;
          }
        }
        // var filter_NumFormat = this.$options.filters["NumFormat"];
      });

      return sums;
    },

    /**    */
    yearTableDataSummaries({ columns }) {
      const sums = [];
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = <span class="font_weight_600">合计</span>;
          return;
        }
        if (column.property == "Count") {
          sums[index] = <span class="font_weight_600">{this.yearTableDataSum.Count}</span>;
        }
        if (this.yearTableDataSum && this.yearTableDataSum.DateData && this.yearTableDataSum.DateData.length) {
          let temp = this.yearTableDataSum.DateData.find((i) => i.Date == column.property);
          if (temp) {
            sums[index] = <span class="font_weight_600">{temp.Count}</span>;
          }
        }
      });

      return sums;
    },

    /**    */
    handleYearSearch() {
      let that = this;
      that.yearPaginations.page = 1;
      that.customerStatistics_yearTreatStatistics();
    },
    /**    */
    handleYearPageChange(page) {
      let that = this;
      that.yearPaginations.page = page;
      that.customerStatistics_yearTreatStatistics();
    },

    getScopeDateData(DateData, item, type) {
      let temp = DateData.find((i) => i.Date == item.Date);
      if (temp) {
        return temp[type];
      } else {
        return "0";
      }
    },

    /**  月度  */
    async customerStatistics_monthlyTreatStatistics() {
      let that = this;
      that.loading = true;
      try {
        let params = {
          PageNum: that.monthPaginations.page,
          EntityID: that.searchFrom.EntityID, //门店编号
          CreatedOnStartDate: that.searchFrom.CreatedQueryDate ? that.searchFrom.CreatedQueryDate[0] : "", //注册开始时间
          CreatedOnEndDate: that.searchFrom.CreatedQueryDate ? that.searchFrom.CreatedQueryDate[1] : "", //注册结束时间
          SearchDate: that.searchFrom.SearchDate, //时间筛选
          Name: that.searchFrom.Name, //模糊搜索
          SourceID: that.searchFrom.SourceID, //来源
          LevelID: that.searchFrom.LevelID, //等级
        };
        let res = await API.customerStatistics_monthlyTreatStatistics(params);
        if (res.StateCode == 200) {
          that.monthTableData = res.Data.Detail.List;
          that.monthPaginations.total = res.Data.Detail.Total;
          that.monthTableDataSum = res.Data.SumOutputForm;
          that.$nextTick(() => {
            that.$refs.monthTable.doLayout();
          });
        } else {
          that.$message.error(res.Message);
        }
        that.loading = false;
      } catch (error) {
        that.$message.error(error);
        that.loading = false;
      }
    },
    /** 年度   */
    async customerStatistics_yearTreatStatistics() {
      let that = this;
      that.loading = true;
      try {
        let params = {
          PageNum: that.yearPaginations.page,
          EntityID: that.searchYearFrom.EntityID, //门店编号
          CreatedOnStartDate: that.searchYearFrom.CreatedQueryDate ? that.searchYearFrom.CreatedQueryDate[0] : "", //注册开始时间
          CreatedOnEndDate: that.searchYearFrom.CreatedQueryDate ? that.searchYearFrom.CreatedQueryDate[1] : "", //注册结束时间
          SearchDate: that.searchYearFrom.SearchDate, //时间筛选
          Name: that.searchYearFrom.Name, //模糊搜索
          SourceID: that.searchYearFrom.SourceID, //来源
          LevelID: that.searchYearFrom.LevelID, //等级
        };
        let res = await API.customerStatistics_yearTreatStatistics(params);
        if (res.StateCode == 200) {
          that.yearTableData = res.Data.Detail.List;
          that.yearPaginations.total = res.Data.Detail.Total;
          that.yearTableDataSum = res.Data.SumOutputForm;
          that.$nextTick(() => {
            that.$refs.yearTable.doLayout();
          });
        } else {
          that.$message.error(res.Message);
        }
        that.loading = false;
      } catch (error) {
        that.$message.error(error);
        that.loading = false;
      }
    },
    /**    */
    downloadMonthExcel_command(type){
      if (type == "excelNoDisPlayPhone") {
        this.downloadMonthExcel();
      }
      if (type == "excelDisPlayPhone") {
        this.customerStatistics_monthlyTreatStatisticsExcelDisPlayPhone();
      }

    },
    /** 月度 数据导出 */
    downloadMonthExcel() {
      var that = this;
      let params = {
        PageNum: that.monthPaginations.page,
        EntityID: that.searchFrom.EntityID, //门店编号
        CreatedOnStartDate: that.searchFrom.CreatedQueryDate ? that.searchFrom.CreatedQueryDate[0] : "", //注册开始时间
        CreatedOnEndDate: that.searchFrom.CreatedQueryDate ? that.searchFrom.CreatedQueryDate[1] : "", //注册结束时间
        SearchDate: that.searchFrom.SearchDate, //时间筛选
        Name: that.searchFrom.Name, //模糊搜索
        SourceID: that.searchFrom.SourceID, //来源
        LevelID: that.searchFrom.LevelID, //等级
      };
      that.downloadMonthLoading = true;
      API.customerStatistics_monthlyTreatStatisticsExcel(params)
        .then((res) => {
          this.$message.success({
            message: "正在导出",
            duration: "4000",
          });
          const link = document.createElement("a");
          let blob = new Blob([res], { type: "application/octet-stream" });
          link.style.display = "none";
          link.href = URL.createObjectURL(blob);
          link.download = "客户消耗月度统计.xlsx"; //下载的文件名
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
        })
        .finally(function () {
          that.downloadMonthLoading = false;
        });
    },

    /** 月度 数据导出 */
    customerStatistics_monthlyTreatStatisticsExcelDisPlayPhone() {
      var that = this;
      let params = {
        PageNum: that.monthPaginations.page,
        EntityID: that.searchFrom.EntityID, //门店编号
        CreatedOnStartDate: that.searchFrom.CreatedQueryDate ? that.searchFrom.CreatedQueryDate[0] : "", //注册开始时间
        CreatedOnEndDate: that.searchFrom.CreatedQueryDate ? that.searchFrom.CreatedQueryDate[1] : "", //注册结束时间
        SearchDate: that.searchFrom.SearchDate, //时间筛选
        Name: that.searchFrom.Name, //模糊搜索
        SourceID: that.searchFrom.SourceID, //来源
        LevelID: that.searchFrom.LevelID, //等级
      };
      that.downloadMonthLoading = true;
      API.customerStatistics_monthlyTreatStatisticsExcelDisPlayPhone(params)
        .then((res) => {
          this.$message.success({
            message: "正在导出",
            duration: "4000",
          });
          const link = document.createElement("a");
          let blob = new Blob([res], { type: "application/octet-stream" });
          link.style.display = "none";
          link.href = URL.createObjectURL(blob);
          link.download = "客户消耗月度统计(显示手机号).xlsx"; //下载的文件名
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
        })
        .finally(function () {
          that.downloadMonthLoading = false;
        });
    },
    downloadYearExcel_command (type) {
      if (type == "excelNoDisPlayPhone") {
        this.downloadYearExcel();
      }
      if (type == "excelDisPlayPhone") {
        this.customerStatistics_yearTreatStatisticsExcelDisPlayPhone();
      }

    },
    /** 年度 数据导出 */
    downloadYearExcel() {
      var that = this;
      let params = {
        PageNum: that.yearPaginations.page,
        EntityID: that.searchYearFrom.EntityID, //门店编号
        CreatedOnStartDate: that.searchYearFrom.CreatedQueryDate ? that.searchYearFrom.CreatedQueryDate[0] : "", //注册开始时间
        CreatedOnEndDate: that.searchYearFrom.CreatedQueryDate ? that.searchYearFrom.CreatedQueryDate[1] : "", //注册结束时间
        SearchDate: that.searchYearFrom.SearchDate, //时间筛选
        Name: that.searchYearFrom.Name, //模糊搜索
        SourceID: that.searchYearFrom.SourceID, //来源
        LevelID: that.searchYearFrom.LevelID, //等级
      };
      that.downloadYearLoading = true;
      API.customerStatistics_yearTreatStatisticsExcel(params)
        .then((res) => {
          this.$message.success({
            message: "正在导出",
            duration: "4000",
          });
          const link = document.createElement("a");
          let blob = new Blob([res], { type: "application/octet-stream" });
          link.style.display = "none";
          link.href = URL.createObjectURL(blob);
          link.download = "客户消耗年度统计.xlsx"; //下载的文件名
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
        })
        .finally(function () {
          that.downloadYearLoading = false;
        });
    },

    /** 年度 数据导出 */
    customerStatistics_yearTreatStatisticsExcelDisPlayPhone() {
      var that = this;
      let params = {
        PageNum: that.yearPaginations.page,
        EntityID: that.searchYearFrom.EntityID, //门店编号
        CreatedOnStartDate: that.searchYearFrom.CreatedQueryDate ? that.searchYearFrom.CreatedQueryDate[0] : "", //注册开始时间
        CreatedOnEndDate: that.searchYearFrom.CreatedQueryDate ? that.searchYearFrom.CreatedQueryDate[1] : "", //注册结束时间
        SearchDate: that.searchYearFrom.SearchDate, //时间筛选
        Name: that.searchYearFrom.Name, //模糊搜索
        SourceID: that.searchYearFrom.SourceID, //来源
        LevelID: that.searchYearFrom.LevelID, //等级
      };
      that.downloadYearLoading = true;
      API.customerStatistics_yearTreatStatisticsExcelDisPlayPhone(params)
        .then((res) => {
          this.$message.success({
            message: "正在导出",
            duration: "4000",
          });
          const link = document.createElement("a");
          let blob = new Blob([res], { type: "application/octet-stream" });
          link.style.display = "none";
          link.href = URL.createObjectURL(blob);
          link.download = "客户消耗年度统计(显示手机号).xlsx"; //下载的文件名
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
        })
        .finally(function () {
          that.downloadYearLoading = false;
        });
    },
    //获得当前用户下的权限门店
    getstoreEntityList() {
      var that = this;
      that.loading = true;
      EntityAPI.getStoreEntityList()
        .then((res) => {
          if (res.StateCode == 200) {
            that.storeEntityList = res.Data;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },
    // 会员等级
    CustomerLevelData: function () {
      var that = this;
      var params = {
        Name: "",
        Active: true,
      };
      APICustomerLevel.getCustomerLevel(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.customerLevel = res.Data;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },
    // 信息来源
    CustomerSourceData: function () {
      var that = this;
      var params = {
        Name: "",
        Active: true,
      };
      APICustomerSource.getCustomerSource(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.customerSource = res.Data;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },
  },
  /** 创建实例之前执行函数   */
  beforeCreate() { },
  /**  实例创建完成之后  */
  created() { },
  /**  在挂载开始之前被调用  */
  beforeMount() { },
  /**  实例被挂载后调用  */
  mounted() {
    this.isExportTreatMouthStatistics = this.$permission.permission(this.$route.meta.Permission, "Report-Customer-TreatMouthStatistics-Export");
    this.isExportTreatYearStatistics = this.$permission.permission(this.$route.meta.Permission, "Report-Customer-TreatYearStatistics-Export");


    this.isExportTreatMouthStatisticsDisPlayPhone = this.$permission.permission(this.$route.meta.Permission, "Report-Customer-TreatMouthStatistics-ExportDisPlayPhone");
    this.isExportTreatYearStatisticsDisPlayPhone = this.$permission.permission(this.$route.meta.Permission, "Report-Customer-TreatYearStatistics-ExportDisPlayPhone");
    this.getstoreEntityList();
    this.CustomerSourceData();
    this.CustomerLevelData();
    this.customerStatistics_monthlyTreatStatistics();
    this.customerStatistics_yearTreatStatistics();
  },
  /**  数据更新 之前 调用  */
  beforeUpdate() { },
  /** 数据更新 完成 调用   */
  updated() { },
  /**  实例销毁之前调用  */
  beforeDestroy() { },
  /**  实例销毁后调用  */
  destroyed() { },
};
</script>

<style lang="scss">
.CustomerTreatStatistics {
  .el-tabs--border-card {
    border: 0px !important;
    box-shadow: 0 0px 0px 0 rgba(0, 0, 0, 0), 0 0 0px 0 rgba(0, 0, 0, 0);
  }
}
</style>
