<template>
  <div class="zl_custone_component_saleCashierReceipt">
    <!-- 打印 销售 小票 -->
    <el-dialog :visible.sync="dialogVisible_" width="300px" @close="onClose" append-to-body>
      <span slot="title" class="font_14 color_333">打印小票</span>
      <div v-if="saleOrderDetail && entityName">
        <el-row>
          <el-col :span="24">
            <el-scrollbar class="el-scrollbar_height" style="height: 500px">
              <div class="marrt_10">
                <div class="dis_flex">
                  <span class="flex_box text_center font_16" style="line-height: 32px">{{ entityName }}</span>
                </div>
                <el-divider>
                  <span class="font_12 color_gray">订单信息</span>
                </el-divider>

                <div class="dis_flex">
                  <span class="flex_box font_12 color_gray text-left line_height_24">订单编号</span>
                  <span class="font_12 text_right line_height_24" style="flex: 3">{{ saleOrderDetail.ID }}</span>
                </div>
                <div class="dis_flex">
                  <span class="flex_box font_12 color_gray text-left line_height_24">下单时间</span>
                  <span class="font_12 text_right line_height_24" style="flex: 3">{{ saleOrderDetail.BillDate | dateFormat("YYYY-MM-DD HH:mm") }}</span>
                </div>
                <div class="dis_flex">
                  <span class="flex_box font_12 color_gray text-left line_height_24">会员姓名</span>
                  <span class="flex_box font_12 text_right line_height_24">{{ cashierReceipt.NameEncrypt ? formatName(saleOrderDetail.Name) : saleOrderDetail.Name }}</span>
                </div>
                <div class="dis_flex">
                  <span class="flex_box font_12 color_gray text-left line_height_24">会员手机号</span>
                  <span class="flex_box font_12 text_right line_height_24">{{ cashierReceipt.MobileEncrypt ? formatPhone(saleOrderDetail.PhoneNumber) : saleOrderDetail.PhoneNumber }}</span>
                </div>
                <div class="dis_flex">
                  <span class="flex_box font_12 color_gray text-left line_height_24">开单人</span>
                  <span class="flex_box font_12 text_right line_height_24">{{ saleOrderDetail.EmployeeName }}</span>
                </div>
                <div v-if="cashierReceipt.EntityAddress" class="dis_flex">
                  <span class="flex_box6 color_gray text-left line_height_24">地址：</span>
                  <span class="flex_box font_12 text_right line_height_24">{{ saleOrderDetail.AddressDetail }}</span>
                </div>
                <el-divider>
                  <span class="font_12 color_gray">消费明细</span>
                </el-divider>
                <div v-for="(item, index) in saleOrderDetail.Project" :key="index + 'Project' + item.ProjectID">
                  <div class="dis_flex">
                    <span class="font_12 color_gray text-left line_height_24" style="flex: 2">
                      {{ index + 1 }} {{ item.ProjectName }}
                      <span class="font_12" size="mini" v-if="item.IsLargess">【赠】</span>
                    </span>
                    <span v-if="cashierReceipt.SaleGoodsOriginPrice" class="font_12 text_right line_height_24" style="flex: 1">￥{{ item.Price | toFixed | NumFormat }}</span>
                  </div>
                  <div class="dis_flex">
                    <span class="font_12 color_gray text-left line_height_24 marlt_10" style="flex: 2">数量</span>
                    <span class="font_12 text_right line_height_24" style="flex: 1">{{ item.Quantity }}</span>
                  </div>
                  <div class="dis_flex" v-if="item.PricePreferentialAmount != 0 && cashierReceipt.SalePromotions">
                    <span class="font_12 color_gray text-left line_height_24 marlt_10" style="flex: 2">手动改价</span>
                    <span class="font_12 text_right line_height_24" style="flex: 1" v-if="item.PricePreferentialAmount > 0">-￥{{ item.PricePreferentialAmount | toFixed | NumFormat }}</span>
                    <span class="font_12 text_right line_height_24" style="flex: 1" v-else>+￥{{ mathAbsData(item.PricePreferentialAmount) | toFixed | NumFormat }}</span>
                  </div>
                  <div class="dis_flex" v-if="item.CardPreferentialAmount != 0 && cashierReceipt.SalePromotions">
                    <span class="font_12 color_gray text-left line_height_24 marlt_10" style="flex: 2">卡优惠</span>
                    <span class="font_12 text_right line_height_24" style="flex: 1">-￥{{ item.CardPreferentialAmount | toFixed | NumFormat }}</span>
                  </div>
                  <div class="dis_flex" v-if="item.MemberPreferentialAmount != 0 && cashierReceipt.SalePromotions">
                    <span class="font_12 color_gray text-left line_height_24 marlt_10" style="flex: 2">会员优惠</span>
                    <span class="font_12 text_right line_height_24" style="flex: 1">-￥{{ item.MemberPreferentialAmount | toFixed | NumFormat }}</span>
                  </div>
                  <div class="dis_flex">
                    <span class="font_12 color_gray text-left line_height_24 marlt_10" style="flex: 2">小计</span>
                    <span class="font_12 text_right line_height_24" style="flex: 1" v-if="item.IsLargess">￥0.00</span>
                    <span class="font_12 text_right line_height_24" style="flex: 1" v-else>￥{{ item.TotalAmount | toFixed | NumFormat }}</span>
                  </div>
                </div>
                <div v-for="(item, index) in saleOrderDetail.SavingCard" :key="index + 'SavingCard' + item.SavingCardID">
                  <div class="dis_flex">
                    <span class="font_12 color_gray text-left line_height_24" style="flex: 2">{{ index + 1 + saleOrderDetail.Project.length }} {{ item.SavingCardName }}</span>
                    <span v-if="cashierReceipt.SaleGoodsOriginPrice" class="font_12 text_right line_height_24" style="flex: 1">￥{{ parseFloat(parseFloat(item.Amount?item.Amount:item.TotalAmount) / parseFloat(item.Quantity)).toFixed(2) | NumFormat }}</span>
                  </div>
                  <div class="dis_flex" v-if="item.LargessPrice > 0">
                    <span class="font_12 color_gray text-left line_height_24 marlt_10" style="flex: 2">充值赠送</span>
                    <span class="font_12 text_right line_height_24" style="flex: 1">￥{{ (item.LargessPrice / item.Quantity).toFixed(2) | NumFormat }}</span>
                  </div>
                  <div class="dis_flex">
                    <span class="font_12 color_gray text-left line_height_24 marlt_10" style="flex: 2">数量</span>
                    <span class="font_12 text_right line_height_24" style="flex: 1">{{ item.Quantity }}</span>
                  </div>
                  <div v-if="cashierReceipt.SaleGoodsOriginPrice" class="dis_flex">
                    <span class="font_12 color_gray text-left line_height_24 marlt_10" style="flex: 2">小计</span>
                    <span class="font_12 text_right line_height_24" style="flex: 1">￥{{ item.Amount?item.Amount:item.TotalAmount | toFixed | NumFormat }}</span>
                  </div>
                </div>
                <div v-for="(item, index) in saleOrderDetail.TimeCard" :key="index + 'TimeCard' + item.TimeCardID">
                  <div class="dis_flex">
                    <span class="font_12 color_gray text-left line_height_24" style="flex: 2">
                      {{ index + 1 + saleOrderDetail.Project.length + saleOrderDetail.SavingCard.length }}
                      {{ item.TimeCardName }}
                      <span class="font_12" size="mini" v-if="item.IsLargess">【赠】</span>
                    </span>
                    <span v-if="cashierReceipt.SaleGoodsOriginPrice" class="font_12 text_right line_height_24" style="flex: 1">￥{{ item.Price | toFixed | NumFormat }}</span>
                  </div>
                  <div class="dis_flex">
                    <span class="font_12 color_gray text-left line_height_24 marlt_10" style="flex: 2">数量</span>
                    <span class="font_12 text_right line_height_24" style="flex: 1">{{ item.Quantity }}</span>
                  </div>
                  <div class="dis_flex" v-if="item.PricePreferentialAmount != 0 && cashierReceipt.SalePromotions">
                    <span class="font_12 color_gray text-left line_height_24 marlt_10" style="flex: 2">手动改价</span>
                    <span class="font_12 text_right line_height_24" style="flex: 1" v-if="item.PricePreferentialAmount > 0">-￥{{ item.PricePreferentialAmount | toFixed | NumFormat }}</span>
                    <span class="font_12 text_right line_height_24" style="flex: 1" v-else>+￥{{ mathAbsData(item.PricePreferentialAmount) | toFixed | NumFormat }}</span>
                  </div>
                  <div class="dis_flex" v-if="item.CardPreferentialAmount != 0 && cashierReceipt.SalePromotions">
                    <span class="font_12 color_gray text-left line_height_24 marlt_10" style="flex: 2">卡优惠</span>
                    <span class="font_12 text_right line_height_24" style="flex: 1">-￥{{ item.CardPreferentialAmount | toFixed | NumFormat }}</span>
                  </div>
                  <div class="dis_flex" v-if="item.MemberPreferentialAmount != 0 && cashierReceipt.SalePromotions">
                    <span class="font_12 color_gray text-left line_height_24 marlt_10" style="flex: 2">会员优惠</span>
                    <span class="font_12 text_right line_height_24" style="flex: 1">-￥{{ item.MemberPreferentialAmount | toFixed | NumFormat }}</span>
                  </div>
                  
                  <div class="dis_flex">
                    <span class="font_12 color_gray text-left line_height_24 marlt_10" style="flex: 2">小计</span>
                    <span class="font_12 text_right line_height_24" style="flex: 1" v-if="item.IsLargess">￥0.00</span>
                    <span class="font_12 text_right line_height_24" style="flex: 1" v-else>￥{{ item.TotalAmount | toFixed | NumFormat }}</span>
                  </div>
                </div>
                <div v-for="(item, index) in saleOrderDetail.GeneralCard" :key="index + 'GeneralCard' + item.GeneralCardID">
                  <div class="dis_flex">
                    <span class="font_12 color_gray text-left line_height_24" style="flex: 2">
                      {{ index + 1 + saleOrderDetail.Project.length + saleOrderDetail.SavingCard.length + saleOrderDetail.TimeCard.length }}
                      {{ item.GeneralCardName }}
                      <span class="font_12" size="mini" v-if="item.IsLargess">【赠】</span>
                    </span>
                    <span v-if="cashierReceipt.SaleGoodsOriginPrice" class="font_12 text_right line_height_24" style="flex: 1">￥{{ item.Price ?item.Price:item.TotalAmount |  toFixed | NumFormat }}</span>
                  </div>
                  <div class="dis_flex">
                    <span class="font_12 color_gray text-left line_height_24 marlt_10" style="flex: 2">数量</span>
                    <span class="font_12 text_right line_height_24" style="flex: 1">{{ item.Quantity }}</span>
                  </div>
                  <div class="dis_flex" v-if="item.PricePreferentialAmount != 0 && cashierReceipt.SalePromotions">
                    <span class="font_12 color_gray text-left line_height_24 marlt_10" style="flex: 2">手动改价</span>
                    <span class="font_12 text_right line_height_24" style="flex: 1" v-if="item.PricePreferentialAmount > 0">-￥{{ item.PricePreferentialAmount | toFixed | NumFormat }}</span>
                    <span class="font_12 text_right line_height_24" style="flex: 1" v-else>+￥{{ mathAbsData(item.PricePreferentialAmount) | toFixed | NumFormat }}</span>
                  </div>
                  <div class="dis_flex" v-if="item.CardPreferentialAmount != 0 && cashierReceipt.SalePromotions">
                    <span class="font_12 color_gray text-left line_height_24 marlt_10" style="flex: 2">卡优惠</span>
                    <span class="font_12 text_right line_height_24" style="flex: 1">-￥{{ item.CardPreferentialAmount | toFixed | NumFormat }}</span>
                  </div>
                  <div class="dis_flex" v-if="item.MemberPreferentialAmount != 0 && cashierReceipt.SalePromotions">
                    <span class="font_12 color_gray text-left line_height_24 marlt_10" style="flex: 2">会员优惠</span>
                    <span class="font_12 text_right line_height_24" style="flex: 1">-￥{{ item.MemberPreferentialAmount | toFixed | NumFormat }}</span>
                  </div>
                  <div class="dis_flex">
                    <span class="font_12 color_gray text-left line_height_24 marlt_10" style="flex: 2">小计</span>
                    <span class="font_12 text_right line_height_24" style="flex: 1" v-if="item.IsLargess">￥0.00</span>
                    <span class="font_12 text_right line_height_24" style="flex: 1" v-else>￥{{ item.TotalAmount | toFixed | NumFormat }}</span>
                  </div>
                </div>
                <div v-for="(item, index) in saleOrderDetail.Product" :key="index + 'Product' + item.ProductID">
                  <div class="dis_flex">
                    <span class="font_12 color_gray text-left line_height_24" style="flex: 2">
                      {{ index + 1 + saleOrderDetail.Project.length + saleOrderDetail.SavingCard.length + saleOrderDetail.TimeCard.length + saleOrderDetail.GeneralCard.length }}
                      {{ item.ProductName }}
                      <span class="font_12" size="mini" v-if="item.IsLargess">【赠】</span>
                    </span>
                    <span v-if="cashierReceipt.SaleGoodsOriginPrice" class="font_12 text_right line_height_24" style="flex: 1">￥{{ item.Price | toFixed | NumFormat }}</span>
                  </div>
                  <div class="dis_flex">
                    <span class="font_12 color_gray text-left line_height_24 marlt_10" style="flex: 2">数量</span>
                    <span class="font_12 text_right line_height_24" style="flex: 1">{{ item.Quantity }}</span>
                  </div>
                  <div class="dis_flex" v-if="item.PricePreferentialAmount != 0 && cashierReceipt.SalePromotions">
                    <span class="font_12 color_gray text-left line_height_24 marlt_10" style="flex: 2">手动改价</span>
                    <span class="font_12 text_right line_height_24" style="flex: 1" v-if="item.PricePreferentialAmount > 0">-￥{{ item.PricePreferentialAmount | toFixed | NumFormat }}</span>
                    <span class="font_12 text_right line_height_24" style="flex: 1" v-else>+￥{{ mathAbsData(item.PricePreferentialAmount) | toFixed | NumFormat }}</span>
                  </div>
                  <div class="dis_flex" v-if="item.CardPreferentialAmount != 0 && cashierReceipt.SalePromotions">
                    <span class="font_12 color_gray text-left line_height_24 marlt_10" style="flex: 2">卡优惠</span>
                    <span class="font_12 text_right line_height_24" style="flex: 1">-￥{{ item.CardPreferentialAmount | toFixed | NumFormat }}</span>
                  </div>
                  <div class="dis_flex" v-if="item.MemberPreferentialAmount != 0 && cashierReceipt.SalePromotions">
                    <span class="font_12 color_gray text-left line_height_24 marlt_10" style="flex: 2">会员优惠</span>
                    <span class="font_12 text_right line_height_24" style="flex: 1">-￥{{ item.MemberPreferentialAmount | toFixed | NumFormat }}</span>
                  </div>
                  <div class="dis_flex">
                    <span class="font_12 color_gray text-left line_height_24 marlt_10" style="flex: 2">小计</span>
                    <span class="font_12 text_right line_height_24" style="flex: 1" v-if="item.IsLargess">￥0.00</span>
                    <span class="font_12 text_right line_height_24" style="flex: 1" v-else>￥{{ item.TotalAmount | toFixed | NumFormat }}</span>
                  </div>
                </div>
                <div v-for="(item, index) in saleOrderDetail.PackageCard" :key="index + 'PackageCard' + item.PackageCardID">
                  <div class="dis_flex">
                    <span class="font_12 color_gray text-left line_height_24" style="flex: 2">
                      {{ index + 1 + saleOrderDetail.Project.length + saleOrderDetail.SavingCard.length + saleOrderDetail.TimeCard.length + saleOrderDetail.GeneralCard.length + saleOrderDetail.Product.length }}
                      {{ item.PackageCardName }}
                      <span class="font_12" size="mini" v-if="item.IsLargess">【赠】</span>
                    </span>
                    <span v-if="cashierReceipt.SaleGoodsOriginPrice" class="font_12 text_right line_height_24" style="flex: 1">￥{{ item.Price | toFixed | NumFormat }}</span>
                  </div>
                  <div class="dis_flex">
                    <span class="font_12 color_gray text-left line_height_24 marlt_10" style="flex: 2">数量</span>
                    <span class="font_12 text_right line_height_24" style="flex: 1">{{ item.Quantity }}</span>
                  </div>
                  <div class="dis_flex" v-if="item.PricePreferentialAmount != 0 && cashierReceipt.SalePromotions">
                    <span class="font_12 color_gray text-left line_height_24 marlt_10" style="flex: 2">手动改价</span>
                    <span class="font_12 text_right line_height_24" style="flex: 1" v-if="item.PricePreferentialAmount > 0">-￥{{ item.PricePreferentialAmount | toFixed | NumFormat }}</span>
                    <span class="font_12 text_right line_height_24" style="flex: 1" v-else>+￥{{ mathAbsData(item.PricePreferentialAmount) | toFixed | NumFormat }}</span>
                  </div>
                  <div class="dis_flex" v-if="item.CardPreferentialAmount != 0 && cashierReceipt.SalePromotions">
                    <span class="font_12 color_gray text-left line_height_24 marlt_10" style="flex: 2">卡优惠</span>
                    <span class="font_12 text_right line_height_24" style="flex: 1">-￥{{ item.CardPreferentialAmount | toFixed | NumFormat }}</span>
                  </div>
                  <div class="dis_flex" v-if="item.MemberPreferentialAmount != 0 && cashierReceipt.SalePromotions">
                    <span class="font_12 color_gray text-left line_height_24 marlt_10" style="flex: 2">会员优惠</span>
                    <span class="font_12 text_right line_height_24" style="flex: 1">-￥{{ item.MemberPreferentialAmount | toFixed | NumFormat }}</span>
                  </div>
                  <div class="dis_flex">
                    <span class="font_12 color_gray text-left line_height_24 marlt_10" style="flex: 2">小计</span>
                    <span class="font_12 text_right line_height_24" style="flex: 1" v-if="item.IsLargess">￥0.00</span>
                    <span class="font_12 text_right line_height_24" style="flex: 1" v-else>￥{{ item.TotalAmount | toFixed | NumFormat }}</span>
                  </div>
                </div>
                <el-divider class="sell-el-divider"></el-divider>
                <div class="dis_flex" v-if="cashierReceipt.SaleTotalAmount">
                  <span class="flex_box font_12 color_gray text-left line_height_24">合计</span>
                  <span class="flex_box font_12 text_right line_height_24">￥{{ saleOrderDetail.TotalAmount | toFixed | NumFormat }}</span>
                </div>
                <div v-if="saleOrderDetail.SaleBillPay && saleOrderDetail.SaleBillPay.length > 0" class="dis_flex font_12">
                  <span class="flex_box6 color_gray text-left line_height_24">付款：</span>
                  <div class="flex_box">
                    <div class="dis_flex flex_box" v-for="pay in saleOrderDetail.SaleBillPay" :key="pay.ID + 'pay'">
                      <span class="flex_box color_gray line_height_24">{{ pay.Name }}</span>
                      <span class="flex_box text_right line_height_24">¥ {{ pay.Amount | toFixed | NumFormat }}</span>
                    </div>
                  </div>
                </div>
                <!--  -->
                <div v-if="saleOrderDetail.SaleBillPaySavingCardDeduction && saleOrderDetail.SaleBillPaySavingCardDeduction.length > 0" class="dis_flex font_12">
                  <span class="flex_box6 color_gray text-left line_height_24">卡抵扣：</span>
                  <div class="flex_box">
                    <div class="dis_flex flex_box" v-for="cardPay in saleOrderDetail.SaleBillPaySavingCardDeduction" :key="cardPay.ID + 'cardPay'">
                      <span class="flex_box color_gray line_height_24">{{ cardPay.Name }}</span>
                      <span class="flex_box text_right line_height_24">¥ {{ cardPay.TotalAmount | toFixed | NumFormat }}</span>
                    </div>
                  </div>
                </div>
                <div class="dis_flex" v-if="saleOrderDetail.PricePreferentialAmount != 0 && cashierReceipt.SalePromotions">
                  <span class="flex_box font_12 color_gray text-left line_height_24">手动改价</span>
                  <span class="flex_box font_12 text_right line_height_24" v-if="saleOrderDetail.PricePreferentialAmount > 0">-￥{{ saleOrderDetail.PricePreferentialAmount | toFixed | NumFormat }}</span>
                  <span class="flex_box font_12 text_right line_height_24" v-else>+￥{{ mathAbsData(saleOrderDetail.PricePreferentialAmount) | toFixed | NumFormat }}</span>
                </div>
                <div class="dis_flex" v-if="saleOrderDetail.CardPreferentialAmount > 0 && cashierReceipt.SalePromotions">
                  <span class="flex_box font_12 color_gray text-left line_height_24">卡优惠</span>
                  <span class="flex_box font_12 text_right line_height_24">-￥{{ saleOrderDetail.CardPreferentialAmount | toFixed | NumFormat }}</span>
                </div>
                  <div class="dis_flex" v-if="saleOrderDetail.MemberPreferentialAmount != 0 && cashierReceipt.SalePromotions" >
                    <span class="font_12 color_gray text-left line_height_24 " style="flex: 2">会员优惠</span>
                    <span class="font_12 text_right line_height_24" style="flex: 1">-￥{{ saleOrderDetail.MemberPreferentialAmount | toFixed | NumFormat }}</span>
                  </div>
                <el-divider class="sell-el-divider"></el-divider>
                <div class="dis_flex flex_dir_column font_14 font_weight_600 flex_y_center color_999 padbm_10">
                  <span>{{ cashierReceipt.WriteTextFirst }}</span>
                  <span>{{ cashierReceipt.WriteTextSecond }}</span>
                </div>

                <div class="dis_flex font_12">
                  <span class="flex_box6 color_gray text-left line_height_24">签字：</span>
                </div>
              </div>
            </el-scrollbar>
          </el-col>
        </el-row>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible_ = false" size="small" :disabled="modalLoading">取 消</el-button>
        <el-button type="primary" @click="getprintSaleBillContent" :loading="modalLoading" v-prevent-click size="small">打印</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import printReceipt from "@/components/js/print";
import cashierAPI from "@/api/iBeauty/Order/cashierReceipt";
var socket;
export default {
  name: "zl_custone_component_saleCashierReceipt",

  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    saleOrderDetail: {
      type: Object,
      default: () => {
        return {};
      },
    },
    cashierReceipt: {
      type: Object,
      default: () => {
        return {
          NameEncrypt: "",
        };
      },
    },
    entityName: {
      type: String,
      default: "",
    },
  },
  /** 监听数据变化   */
  watch: {
    visible: {
      deep: true,
      immediate: true,
      handler(val) {
        if (val) {
          this.dialogVisible_ = val;
        }
      },
    },
  },
  /**  引入的组件  */
  components: {},
  /**  Vue 实例的数据对象**/
  data() {
    return {
      dialogVisible_: false,
      modalLoading: false,
    };
  },
  /**计算属性  */
  computed: {},
  /**  方法集合  */
  methods: {
    mathAbsData: function (item) {
      return Math.abs(item);
    },
    /**  获取 销售 打印内容  */
    getprintSaleBillContent() {
      let that = this;
      let params = {
        SaleBillID: that.saleOrderDetail.ID,
      };
      cashierAPI
        .printSaleBillContent(params)
        .then((res) => {
          if (res.StateCode == 200) {
            for (let index = 0; index < res.Data.copies; index++) {
              printReceipt.doActionPrint(res.Data.printDocuments, (request) => {
                socket.send(JSON.stringify(request));
              });
            }
          }
        })
        .finally(() => {});
    },
    /**    */
    onClose() {
      this.$emit("update:visible", false);
    },
    // 姓名隐藏
    formatName(name) {
      return printReceipt.hiddenName(name);
    },
    // 手机号隐藏
    formatPhone(phone) {
      return printReceipt.hiddenPhone(phone);
    },
  },
  /** 创建实例之前执行函数   */
  beforeCreate() {},
  /**  实例创建完成之后  */
  created() {},
  /**  在挂载开始之前被调用  */
  beforeMount() {},
  /**  实例被挂载后调用  */
  mounted() {
    socket = printReceipt.getSocket((res) => {
      if (res.status == "success") {
        this.$message.success({
          message: "打印成功",
          duration: 2000,
        });
        this.dialogVisible_ = false;
        // that.cashierReceiptDialogVisible = false;
      }
    });
  },
  /**  数据更新 之前 调用  */
  beforeUpdate() {},
  /** 数据更新 完成 调用   */
  updated() {},
  /**  实例销毁之前调用  */
  beforeDestroy() {},
  /**  实例销毁后调用  */
  destroyed() {},
};
</script>

<style lang="scss">
.zl_custone_component_saleCashierReceipt {
  .el-scrollbar_height {
    height: 100%;
    .el-scrollbar__wrap {
      overflow-x: hidden;
    }
  }
}
</style>
