<!-- 线索跟进客户订单信息 - 简化版本 -->
<template>
  <div v-loading="loading" class="workbenchCustomerBill">
    <el-tabs v-model="activeName" @tab-click="handleClick" type="border-card" class="custom-bill-tabs">
      <!-- <el-tab-pane label="销售订单" name="1">
        <el-scrollbar style="height: calc(100% - 62px)" class="custom-scrollbar_hidden-x">
          <div style="width: calc(100% - 8px)">
            <el-form class="padtp_10" :inline="true" size="small" :label-position="position" label-width="120" @keyup.enter.native="handleSearch">
              <el-form-item label="订单类型">
                <el-select placeholder="请选择订单类型" clearable v-model="searchData.BillType" @change="handleSearch">
                  <el-option label="销售单" value="10"></el-option>
                  <el-option label="销售退款单" value="20"></el-option>
                  <el-option label="补欠款单" value="30"></el-option>
                  <el-option label="储值卡充值单" value="40"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="订单状态">
                <el-select placeholder="请选择订单状态" clearable v-model="searchData.BillStatus" @change="handleSearch">
                  <el-option label="待付款" value="10"></el-option>
                  <el-option label="已付款（未完成）" value="15"></el-option>
                  <el-option label="已完成" value="20"></el-option>
                  <el-option label="已取消" value="30"></el-option>
                </el-select>
              </el-form-item>
            </el-form>
            <el-table size="mini" :data="saleOrderList">
              <el-table-column label="订单编号" prop="ID"></el-table-column>
              <el-table-column label="订单金额" prop="Amount">
                <template slot-scope="scope">￥{{ scope.row.Amount | toFixed | NumFormat }}</template>
              </el-table-column>
              <el-table-column label="订单类型">
                <template slot-scope="scope">{{ getBillType(scope.row) }}</template>
              </el-table-column>
              <el-table-column label="订单状态" prop="BillStatus">
                <template slot-scope="scope">{{ scope.row.BillStatus == 10 ? '待结账' : scope.row.BillStatus == 20 ? '已完成' : '已取消' }}</template>
              </el-table-column>
              <el-table-column label="开单门店" prop="EntityName"></el-table-column>
              <el-table-column label="录单时间" prop="CreatedOn">
                <template slot-scope="scope">
                  {{ scope.row.CreatedOn | dateFormat('YYYY-MM-DD HH:mm') }}
                </template>
              </el-table-column>
              <el-table-column label="操作" width="80px">
                <template slot-scope="scope">
                  <el-button type="primary" size="small" @click="getOrderDetail(scope.row)">详情</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-scrollbar>
        <div class="pad_15 text_right">
          <el-pagination
            background
            v-if="paginations.total > 0"
            @current-change="handleCurrentChange"
            :current-page.sync="paginations.page"
            :page-size="paginations.page_size"
            :layout="paginations.layout"
            :total="paginations.total"
          ></el-pagination>
        </div>
      </el-tab-pane> -->
      <el-tab-pane label="销售明细" name="7">
        <el-scrollbar style="height: calc(100% - 62px)" class="custom-scrollbar_hidden-x">
          <div style="width: calc(100% - 8px)">
            <el-row>
              <el-col :span="24">
                <el-form class="padtp_10" :inline="true" size="small" :label-position="position" label-width="80px" @keyup.enter.native="handleSearch">
                  <el-form-item label="订单状态:">
                    <el-select placeholder="请选择订单状态" clearable v-model="searchData.BillStatus" @change="handleSearch">
                      <el-option label="待付款" value="10"></el-option>
                      <el-option label="已付款（未完成）" value="15"></el-option>
                      <el-option label="已完成" value="20"></el-option>
                      <el-option label="已取消" value="30"></el-option>
                    </el-select>
                  </el-form-item>
                  <el-form-item label="订单类型">
                    <el-select placeholder="请选择订单状态" clearable v-model="searchData.BillType" @change="handleSearch">
                      <el-option label="销售单" value="10"></el-option>
                      <el-option label="销售退款单" value="20"></el-option>
                      <el-option label="补尾款单" value="30"></el-option>
                      <el-option label="充值单" value="40"></el-option>
                    </el-select>
                  </el-form-item>
                  <el-form-item>
                    <el-button type="primary" @click="handleSearch" v-prevent-click size="small">搜索</el-button>
                  </el-form-item>
                </el-form>
              </el-col>
            </el-row>
            <el-table size="small" :data="saleDetailList">
              <el-table-column label="订单编号" prop="BillID" width="150px"></el-table-column>
              <el-table-column label="订单状态" prop="BillStatus" :formatter="formatterBillStatus"></el-table-column>
              <el-table-column label="订单类型" prop="BillType" :formatter="getBillType"></el-table-column>
              <el-table-column label="开单门店" prop="EntityName"></el-table-column>
              <el-table-column label="开单人" prop="EmployeeName"></el-table-column>
              <el-table-column label="商品类型" prop="GoodsTypeName"></el-table-column>
              <el-table-column label="商品分类" prop="CategoryName"></el-table-column>
              <el-table-column label="商品名称" prop="GoodName"></el-table-column>
              <el-table-column label="商品单价" prop="Price">
                <template slot-scope="scope">
                  {{ scope.row.Price | toFixed | NumFormat }}
                </template>
              </el-table-column>
              <el-table-column label="商品数量" prop="Quantity"></el-table-column>
              <el-table-column label="是否赠送" prop="IsLargess" :formatter="formatterIsLargess"></el-table-column>
              <el-table-column label="合计金额" prop="TotalAmount">
                <template slot-scope="scope">
                  {{ scope.row.TotalAmount | toFixed | NumFormat }}
                </template>
              </el-table-column>
              <el-table-column label="赠送金额" prop="LargessAmount">
                <template slot-scope="scope">
                  {{ scope.row.LargessAmount | toFixed | NumFormat }}
                </template>
              </el-table-column>
              <el-table-column label="欠款金额" prop="ArrearAmount">
                <template slot-scope="scope">
                  {{ scope.row.ArrearAmount | toFixed | NumFormat }}
                </template>
              </el-table-column>
              <el-table-column label="实收金额" prop="PayAmount">
                <template slot-scope="scope">
                  {{ scope.row.PayAmount | toFixed | NumFormat }}
                </template>
              </el-table-column>
              <el-table-column label="储值卡抵扣金额" prop="SavingCardDeductionAmount">
                <template slot-scope="scope">
                  {{ scope.row.SavingCardDeductionAmount | toFixed | NumFormat }}
                </template>
              </el-table-column>
              <el-table-column label="赠送储值卡抵扣金额" prop="LargessSavingCardDeductionAmount">
                <template slot-scope="scope">
                  {{ scope.row.LargessSavingCardDeductionAmount | toFixed | NumFormat }}
                </template>
              </el-table-column>

              <el-table-column label="下单时间" prop="Channel" width="150px">
                <template slot-scope="scope">
                  {{ scope.row.BillDate | dateFormat('YYYY-MM-DD HH:mm') }}
                </template>
              </el-table-column>
              <el-table-column label="录单时间" prop="CreatedOn" width="150px">
                <template slot-scope="scope">
                  {{ scope.row.CreatedOn | dateFormat('YYYY-MM-DD HH:mm') }}
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-scrollbar>
        <div class="pad_15 text_right">
          <el-pagination
            background
            v-if="paginations.total > 0"
            @current-change="handleCurrentChange"
            :current-page.sync="paginations.page"
            :page-size="paginations.page_size"
            :layout="paginations.layout"
            :total="paginations.total"
          ></el-pagination>
        </div>
      </el-tab-pane>

      <!-- <el-tab-pane label="支付明细" name="8">
        支付明细内容已注释隐藏
      </el-tab-pane> -->

      <!-- <el-tab-pane label="消耗订单" name="2">
        消耗订单内容已注释隐藏
      </el-tab-pane> -->
      
      <el-tab-pane label="消耗明细" name="9">
        <el-scrollbar style="height: calc(100% - 62px)" class="custom-scrollbar_hidden-x">
          <div style="width: calc(100% - 8px)">
            <el-row>
              <el-col :span="24">
                <el-form class="padtp_10" :inline="true" size="small" :label-position="position" label-width="80px" @keyup.enter.native="handleSearch">
                  <el-form-item label="订单状态:">
                    <el-select placeholder="请选择订单状态" clearable v-model="searchData.BillStatus" @change="handleSearch">
                      <el-option label="待付款" value="10"></el-option>
                      <el-option label="已付款（未完成）" value="15"></el-option>
                      <el-option label="已完成" value="20"></el-option>
                      <el-option label="已取消" value="30"></el-option>
                    </el-select>
                  </el-form-item>
                  <el-form-item label="订单类型">
                    <el-select placeholder="请选择订单状态" clearable v-model="searchData.BillType" @change="handleSearch">
                      <el-option label="销售单" value="10"></el-option>
                      <el-option label="销售退款单" value="20"></el-option>
                      <el-option label="补尾款单" value="30"></el-option>
                      <el-option label="充值单" value="40"></el-option>
                    </el-select>
                  </el-form-item>
                  <el-form-item>
                    <el-button type="primary" @click="handleSearch" v-prevent-click size="small">搜索</el-button>
                  </el-form-item>
                </el-form>
              </el-col>
            </el-row>
            <el-table size="small" :data="treatDetailList">
              <el-table-column label="订单编号" prop="BillID" width="150px"></el-table-column>
              <el-table-column label="订单状态" prop="BillStatus" :formatter="formatterTreatBillStatus"></el-table-column>
              <el-table-column label="订单类型" prop="BillType" :formatter="getBillTypeTreat"></el-table-column>

              <el-table-column label="开单门店" prop="EntityName"></el-table-column>
              <el-table-column label="商品类型" prop="TreatCardTypeName"></el-table-column>
              <el-table-column label="卡名称" prop="CardName"></el-table-column>
              <el-table-column label="商品分类" prop="CategoryName"></el-table-column>
              <el-table-column label="商品名称" prop="GoodName"></el-table-column>
              <el-table-column label="商品单价" prop="Price">
                <template slot-scope="scope">
                  {{ scope.row.Price | toFixed | NumFormat }}
                </template>
              </el-table-column>
              <el-table-column label="商品数量" prop="Quantity"></el-table-column>
              <el-table-column label="是否赠送" prop="IsLargess" :formatter="formatterIsLargess"></el-table-column>
              <el-table-column label="合计金额" prop="TotalAmount">
                <template slot-scope="scope">
                  {{ scope.row.TotalAmount | toFixed | NumFormat }}
                </template>
              </el-table-column>
              <el-table-column label="现金金额" prop="TreatPayAmount">
                <template slot-scope="scope">
                  {{ scope.row.TreatPayAmount | toFixed | NumFormat }}
                </template>
              </el-table-column>
              <el-table-column label="卡扣金额" prop="TreatCardAmount">
                <template slot-scope="scope">
                  {{ scope.row.TreatCardAmount | toFixed | NumFormat }}
                </template>
              </el-table-column>
              <el-table-column label="赠送卡扣金额" prop="TreatCardLargessAmount">
                <template slot-scope="scope">
                  {{ scope.row.TreatCardLargessAmount | toFixed | NumFormat }}
                </template>
              </el-table-column>
              <el-table-column label="赠送金额" prop="TreatLargessAmount">
                <template slot-scope="scope">
                  {{ scope.row.TreatLargessAmount | toFixed | NumFormat }}
                </template>
              </el-table-column>
              <el-table-column label="开单渠道" prop="Channel"></el-table-column>
              <el-table-column label="下单时间" prop="Channel" width="150px">
                <template slot-scope="scope">
                  {{ scope.row.BillDate | dateFormat('YYYY-MM-DD HH:mm') }}
                </template>
              </el-table-column>
              <el-table-column label="录单时间" prop="CreatedOn" width="150px">
                <template slot-scope="scope">
                  {{ scope.row.CreatedOn | dateFormat('YYYY-MM-DD HH:mm') }}
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-scrollbar>
        <div class="pad_15 text_right">
          <el-pagination
            background
            v-if="paginations.total > 0"
            @current-change="handleCurrentChange"
            :current-page.sync="paginations.page"
            :page-size="paginations.page_size"
            :layout="paginations.layout"
            :total="paginations.total"
          ></el-pagination>
        </div>
      </el-tab-pane>
      
      <!-- <el-tab-pane label="转账记录" name="5">
        转账记录内容已注释隐藏
      </el-tab-pane> -->
      
      <!-- <el-tab-pane label="延期记录" name="6">
        延期记录内容已注释隐藏
      </el-tab-pane> -->
    </el-tabs>

    <!-- 销售订单详情 -->
    <el-dialog custom-class="workbenchCustomerBilldialog" title="销售订单详情" :visible.sync="SaleListVisible" width="1100px" append-to-body>
      <div class="font_13" style="height: 60vh">
        <el-scrollbar class="customerBillScrollbar">
          <SaleBillDetailContent :saleOrderDetail="saleOrderDetail"></SaleBillDetailContent>
        </el-scrollbar>
      </div>
    </el-dialog>

    <!-- 销售 充值 单详情 -->
    <el-dialog custom-class="workbenchCustomerBilldialog" :visible.sync="rechargeDialogVisible" width="1100px" append-to-body>
      <div class="font_13" style="height: 60vh">
        <el-scrollbar class="customerBillScrollbar" style="height: 100%">
          <rechargeBillContent :saleOrderDetail="saleOrderDetail"></rechargeBillContent>
        </el-scrollbar>
      </div>
    </el-dialog>

    <!-- 销售订单 补欠款 详情 -->
    <el-dialog custom-class="workbenchCustomerBilldialog" title="补欠款订单详情" :visible.sync="arreardialogVisible" width="1100px" append-to-body>
      <div class="font_13" style="height: 60vh">
        <el-scrollbar class="customerBillScrollbar">
          <ArrearDetailContent :saleOrderDetail="saleOrderDetail"></ArrearDetailContent>
        </el-scrollbar>
      </div>
    </el-dialog>

    <!-- 销售订单 退款 订单详情 -->
    <el-dialog custom-class="workbenchCustomerBilldialog" title="退款订单详情" :visible.sync="refundDialogVisible" width="1100px" append-to-body>
      <div class="font_13" style="height: 60vh">
        <el-scrollbar class="customerBillScrollbar">
          <RefundDetailContent :saleOrderDetail="saleOrderDetail"></RefundDetailContent>
        </el-scrollbar>
      </div>
    </el-dialog>

    <!-- 消耗订单 -->
    <el-dialog custom-class="workbenchCustomerBilldialog" title="消耗单详情" width="1100px" :visible.sync="TreatBillDetailVisible" append-to-body>
      <div style="height: 60vh" class="font_13">
        <el-scrollbar class="customerBillScrollbar">
          <TreatBillDetailContent :treatInfo="treatInfo"></TreatBillDetailContent>
        </el-scrollbar>
      </div>
    </el-dialog>

    <!--  退 消耗订单 -->
    <el-dialog custom-class="workbenchCustomerBilldialog" title="消耗单详情" width="1100px" :visible.sync="RefundTreatBillDetailVisible" append-to-body>
      <div style="height: 60vh" class="font_13">
        <el-scrollbar class="customerBillScrollbar">
          <TreatRefundDetailContent :treatInfo="treatInfo"></TreatRefundDetailContent>
        </el-scrollbar>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import API from '@/api/iBeauty/Order/saleBill';

import SaleBillDetailContent from '@/views/iBeauty/Components/Bill/saleBillDetailContent';
import rechargeBillContent from '@/views/iBeauty/Components/Bill/rechargeBillContent';
import ArrearDetailContent from '@/views/iBeauty/Components/Bill/arrearDetailContent';
import RefundDetailContent from '@/views/iBeauty/Components/Bill/refundDetailContent';
import TreatBillDetailContent from '@/views/iBeauty/Components/Bill/treatBillDetailContent';
import TreatRefundDetailContent from '@/views/iBeauty/Components/Bill/treatRefundDetailContent';

export default {
  name: 'ClueFollowUpCustomerBill',
  components: {
    SaleBillDetailContent,
    rechargeBillContent,
    ArrearDetailContent,
    RefundDetailContent,
    TreatBillDetailContent,
    TreatRefundDetailContent,
  },
  props: {
    customerID: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      loading: false,
      activeName: '7', // 默认显示销售明细
      position: 'left',
      searchData: {
        createTime: '',
        EndDate: '',
        StartDate: '',
        BillType: '',
        Name: '',
        SaleBillID: '',
        BillStatus: '',
      },
      saleOrderList: [],
      saleDetailList: [],
      salePayList: [],
      consumeList: [],
      treatDetailList: [],
      TransferList: [],
      PostponeBillList: [],
      paginations: {
        page: 1,
        total: 0,
        page_size: 10,
        layout: 'total, prev, pager, next,jumper',
      },
      saleOrderDetail: {},
      SaleListVisible: false,
      rechargeDialogVisible: false,
      arreardialogVisible: false,
      refundDialogVisible: false,
      TreatBillDetailVisible: false,
      RefundTreatBillDetailVisible: false,
      treatInfo: {},
      dialogVisible: false,
      refoundInfo: {},
      transDetail: {},
      applyPostponeDetailVisible: false,
      postponeDetail: {},
      pickerOptions: {
        shortcuts: [
          {
            text: '最近一周',
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit('pick', [start, end]);
            },
          },
          {
            text: '最近一个月',
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
              picker.$emit('pick', [start, end]);
            },
          },
          {
            text: '最近三个月',
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
              picker.$emit('pick', [start, end]);
            },
          },
        ],
      },
    };
  },
  methods: {
    /**    */
    formatterIsLargess(row) {
      if (row.IsLargess) return '是';
      return '否';
    },
    /**    */
    getBillTypeTreat(row) {
      switch (row.BillType) {
        case '10':
          return '消耗单';
        case '20':
          return '消耗退单';
      }
    },
    /**    */
    formatterTreatBillStatus(row) {
      switch (row.BillStatus) {
        case '10':
          return '未完成';
        case '20':
          return '已完成';
        case '30':
          return '已取消';
      }
    },
    /**  获取订单状态   */
    formatterBillStatus(row) {
      switch (row.BillStatus) {
        case '10':
          return '待付款';
        case '15':
          return '已付款（未完成）';
        case '20':
          return '已完成';
        case '30':
          return '已取消';
      }
    },
    /**  获取订单类型   */
    getBillType(row) {
      switch (row.BillType) {
        case '10':
          return '销售单';
        case '20':
          return '销售退款单';
        case '30':
          return '补欠款单';
        case '40':
          return '充值单';
      }
    },
    handleClick() {
      var that = this;
      var tabIndex = that.activeName;
      that.paginations.page = 1;
      switch (tabIndex) {
        case '7':
          that.customerBill_saleDetailBill();
          break;
        case '9':
          that.customerBill_treatDetailBill();
          break;
      }
    },
    handleSearch() {
      var that = this;
      that.paginations.page = 1;
      var tabIndex = that.activeName;
      switch (tabIndex) {
        case '7':
          that.customerBill_saleDetailBill();
          break;
        case '9':
          that.customerBill_treatDetailBill();
          break;
      }
    },
    handleCurrentChange(page) {
      var that = this;
      that.paginations.page = page;
      var tabIndex = that.activeName;
      switch (tabIndex) {
        case '7':
          that.customerBill_saleDetailBill();
          break;
        case '9':
          that.customerBill_treatDetailBill();
          break;
      }
    },

    /** 销售明细   */
    customerBill_saleDetailBill() {
      let that = this;
      let params = {
        PageNum: that.paginations.page,
        CustomerID: that.customerID, //顾客编号
        BillStatus: that.searchData.BillStatus, //10:未完成，20:已完成，30:取消
        BillType: that.searchData.BillType, //10.销售单，20：销售退款单，30：补欠款单，40：充值单
      };
      API.customerBill_saleDetailBill(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.saleDetailList = res.List;
            that.paginations.total = res.Total;
            that.paginations.page_size = res.PageSize;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        })
        .catch((fail) => {
          that.$message.error(fail);
        });
    },

    /** 消耗明细   */
    customerBill_treatDetailBill() {
      let that = this;
      let params = {
        PageNum: that.paginations.page,
        CustomerID: that.customerID, //顾客编号
        BillStatus: that.searchData.BillStatus, //10:未完成，20:已完成，30:取消
        BillType: that.searchData.BillType, //10.消耗单，20：消耗退单
      };
      API.customerBill_treatDetailBill(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.treatDetailList = res.List;
            that.paginations.total = res.Total;
            that.paginations.page_size = res.PageSize;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        })
        .catch((fail) => {
          that.$message.error(fail);
        });
    },
  },

  mounted() {
    // 默认加载销售明细
    this.customerBill_saleDetailBill();
  },
};
</script>

<style scoped>
.workbenchCustomerBill {
  height: 100%;
}
.custom-bill-tabs {
  height: 100%;
}
.custom-scrollbar_hidden-x {
  overflow-x: hidden;
}
</style>
