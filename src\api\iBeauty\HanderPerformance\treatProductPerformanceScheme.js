/**
 * Created by JoJo on 2022/01/06.
 * 产品消耗业绩 api
 */
import * as API from '@/api/index'

export default {
    // 获取产品消耗组织单位业绩方案列表 
    getTreatProductPerformanceScheme: params => {
        return API.POST('api/treatProductPerformanceScheme/list', params)
    },

    // 创建产品消耗组织单位业绩方案 
    createTreatProductPerformanceScheme: params => {
        return API.POST('api/treatProductPerformanceScheme/create', params)
    },

    //  删除产品消耗业绩方案 
    deleteTreatProductPerformanceScheme: params => {
        return API.POST('api/treatProductPerformanceScheme/delete', params)
    },
    //  获取产品消耗分类提佣方案
    getTreatProductCategoryPerformance: params => {
        return API.POST('api/treatProductCategoryPerformance/all', params)
    },
    //  保存产品消耗分类业绩方案 未改
    updateTreatProductCategoryPerformance: params => {
        return API.POST('api/treatProductCategoryPerformance/update', params)
    },

    //  获取所有产品经手人提佣方案 即所有产品经手人/职务业绩
    treatProductSchemeHanderPerformance: params => {
        return API.POST('api/treatProductSchemeHandlerPerformance/all', params)
    },

    //  保存所有产品经手人业绩方案 
    treatProductSchemeHanderPerformanceupdate: params => {
        return API.POST('api/treatProductSchemeHandlerPerformance/update', params)
    },


    //  获取产品消耗分类经手人提佣方案 即所分类产品经手人/职务业绩 
     getTreatProductSchemeHandlerPerformance: params => {
         return API.POST('api/treatProductCategoryHandlerPerformance/all', params)
     },

    //  保存产品消耗分类经手人业绩方案
    updateTreatProductCategoryHandlerPerformance: params => {
        return API.POST('api/treatProductCategoryHandlerPerformance/update', params)
    },
    //  获取分类下的产品提佣方案
    getTreatProductPerformance: params => {
        return API.POST('api/treatProductPerformance/all', params)
    },
    //  保存产品消耗业绩方案
    updateTreatProductPerformance: params => {
        return API.POST('api/treatProductPerformance/update', params)
    },
    //  获取产品下经手人提佣方案
    getTreatProductHanderPerformance: params => {
        return API.POST('api/treatProductHandlerPerformance/all', params)
    },
    //  保存产品下经手人业绩方案
    updateTreatProductHanderPerformance: params => {
        return API.POST('api/treatProductHandlerPerformance/update', params)
    },


}