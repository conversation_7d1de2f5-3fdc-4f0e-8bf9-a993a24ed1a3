/**
 * Created by wsf on 2022/01/06.
 * 员工业绩 储值卡销售业绩设置 api
 */
 import * as API from '@/api/index'

 export default {
    // 获取员工储值卡业绩方案列表
    getSavingCardPerformanceScheme: params => {
        return API.POST('api/saleSavingCardPerformanceScheme/list', params)
    },
    // 员工储值卡业绩方案保存
    createSavingCardPerformanceScheme: params => {
        return API.POST('api/saleSavingCardPerformanceScheme/create', params)
    },
    // 员工储值卡业绩方案删除
    deleteSavingCardPerformanceScheme: params => {
        return API.POST('api/saleSavingCardPerformanceScheme/delete', params)
    },
    // 获取储值卡分类业绩
    getSavingCardCategoryPerformance: params => {
        return API.POST('api/saleSavingCardCategoryPerformance/all', params)
    },
    // 保存储值卡分类业绩
    updateSavingCardCategoryPerformance: params => {
        return API.POST('api/saleSavingCardCategoryPerformance/update', params)
    },
    // 获取所有储值卡经手人业绩
    getSavingCardSchemeHandlerPerformance: params => {
        return API.POST('api/saleSavingCardSchemeHandlerPerformance/all', params)
    },
    // 获取所有套餐卡储值卡经手人业绩 
    getPackageCardSavingCardSchemeHandlerPerformance: params => {
        return API.POST('api/saleSavingCardSchemeHandlerPerformance/packageCard', params)
    },
    // 所有储值卡经手人业绩保存
    updateSavingCardSchemeHandlerPerformance: params => {
        return API.POST('api/saleSavingCardSchemeHandlerPerformance/update', params)
    },
    // 获取分类储值卡经手人业绩 
    getSavingCardCategoryHandlerPerformance: params => {
        return API.POST('api/saleSavingCardCategoryHandlerPerformance/all', params)
    },
    // 获取分类套餐卡储值卡经手人业绩
    getPackageCardSavingCardCategoryHandlerPerformance: params => {
        return API.POST('api/saleSavingCardCategoryHandlerPerformance/packageCard', params)
    },
    // 分类储值卡经手人业绩保存
    updateSavingCardCategoryHandlerPerformance: params => {
        return API.POST('api/saleSavingCardCategoryHandlerPerformance/update', params)
    },
    // 获取储值卡业绩
    getSavingCardPerformance: params => {
        return API.POST('api/saleSavingCardPerformance/all', params)
    },
    // 储值卡业绩保存
    updateSavingCardPerformance: params => {
        return API.POST('api/saleSavingCardPerformance/update', params)
    },
    // 获取储值卡业绩下的储值卡经手人业绩
    getSavingCardHandlerPerformance: params => {
        return API.POST('api/saleSavingCardHandlerPerformance/all', params)
    },
    // 获取储值卡业绩下的套餐卡储值卡经手人业绩
    getPackageCardSavingCardHandlerPerformance: params => {
        return API.POST('api/saleSavingCardHandlerPerformance/packageCard', params)
    },
    // 储值卡业绩下的储值卡经手人业绩保存 
    updatesaleSavingCardHandlerPerformance: params => {
        return API.POST('api/saleSavingCardHandlerPerformance/update', params)
    }
 }