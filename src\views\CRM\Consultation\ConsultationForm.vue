<template>
  <div class="consultation-form" v-loading="loading">
    <!-- 头部标题 -->
    <div class="form-header">
      <div class="logo-section">
        <img src="@/assets/img/login_logo.png" alt="三七美LOGO" class="logo" />
        <div class="title-section">
          <h1>CONSULTING SERVICE FORM</h1>
          <h2>面诊单</h2>
        </div>
      </div>
    </div>

    <!-- 表单内容 -->
    <div class="form-content">
      <el-form :model="formData" :rules="rules" ref="consultationForm" label-width="120px" size="large">
        
        <!-- 个人信息 -->
        <div class="form-section">
          <h3 class="section-title">个人信息</h3>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="姓名" prop="name">
                <el-input v-model="formData.name" placeholder="请输入姓名"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="生日" prop="birthday">
                <el-date-picker v-model="formData.birthday" type="date" value-format="yyyy-MM-dd" placeholder="选择日期" style="width: 100%"></el-date-picker>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="性别" prop="gender">
                <el-select v-model="formData.gender" placeholder="请选择性别" style="width: 100%">
                  <el-option label="男" value="1"></el-option>
                  <el-option label="女" value="2"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="电话" prop="phoneNumber">
                <el-input v-model="formData.phoneNumber" placeholder="请输入手机号"></el-input>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="婚姻情况" prop="isMarried">
                <el-radio-group v-model="formData.isMarried">
                  <el-radio label="false">未婚</el-radio>
                  <el-radio label="true">已婚</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="12" v-if="formData.isMarried === 'true'">
              <el-form-item label="子女数量" prop="childrenCount">
                <el-radio-group v-model="formData.childrenCount">
                  <el-radio label="1">一孩</el-radio>
                  <el-radio label="2">二孩</el-radio>
                  <el-radio label="3">三孩及以上</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="省市区" prop="provinceCityArea">
                <el-cascader
                  clearable
                  placeholder="请选择省 / 市 / 区"
                  :options="regionData"
                  v-model="provinceCityArea"
                  @change="changeProvinceCityArea"
                >
                </el-cascader>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="详细地址" prop="address">
                <el-input v-model="formData.address" placeholder="请输入具体地址（街道/门牌号等）"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 祛斑经历 -->
        <div class="form-section">
          <h3 class="section-title">祛斑经历（多选）</h3>
          <el-checkbox-group v-model="formData.spotRemoval" class="checkbox-group">
            <el-checkbox label="激光">激光</el-checkbox>
            <el-checkbox label="皮秒">皮秒</el-checkbox>
            <el-checkbox label="液氮">液氮</el-checkbox>
            <el-checkbox label="中药膜/皮膜">中药膜/皮膜</el-checkbox>
            <el-checkbox label="点刺/火针">点刺/火针</el-checkbox>
            <el-checkbox label="焕肤">焕肤</el-checkbox>
            <el-checkbox label="点斑水">点斑水</el-checkbox>
            <el-checkbox label="祛斑霜">祛斑霜</el-checkbox>
            <el-checkbox label="磨削/刮痧">磨削/刮痧</el-checkbox>
            <el-checkbox label="无祛斑史">无祛斑史</el-checkbox>
          </el-checkbox-group>
        </div>

        <!-- 色斑出现时长 -->
        <div class="form-section">
          <h3 class="section-title">色斑出现时长（单选）</h3>
          <el-radio-group v-model="formData.stainsDuration" class="radio-group">
            <el-radio label="6个月之内">6个月之内</el-radio>
            <el-radio label="6个月-1年">6个月-1年</el-radio>
            <el-radio label="1-3年">1-3年</el-radio>
            <el-radio label="3-5年">3-5年</el-radio>
            <el-radio label="5年以上">5年以上</el-radio>
          </el-radio-group>
        </div>

        <!-- 美容经历 -->
        <div class="form-section">
          <h3 class="section-title">美容经历（多选）</h3>
          <el-checkbox-group v-model="formData.cosmetic" class="checkbox-group">
            <el-checkbox label="美容院护肤">美容院护肤</el-checkbox>
            <el-checkbox label="光电">光电</el-checkbox>
            <el-checkbox label="水光">水光</el-checkbox>
            <el-checkbox label="针剂">针剂</el-checkbox>
            <el-checkbox label="整形">整形</el-checkbox>
          </el-checkbox-group>
        </div>

        <!-- 家居护肤品 -->
        <div class="form-section">
          <h3 class="section-title">家居护肤品（多选）</h3>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-checkbox-group v-model="formData.skinCare" class="checkbox-group">
                <el-checkbox label="日化线">日化线</el-checkbox>
                <el-checkbox label="美容院">美容院</el-checkbox>
                <el-checkbox label="线上购买">线上购买</el-checkbox>
              </el-checkbox-group>
            </el-col>
            <el-col :span="12">
              <el-form-item label="使用品牌">
                <el-input v-model="formData.brand" placeholder="请输入品牌名称"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 改善诉求 -->
        <div class="form-section">
          <h3 class="section-title">改善诉求（多选）</h3>
          <el-checkbox-group v-model="formData.demand" class="checkbox-group">
            <el-checkbox label="色斑">色斑</el-checkbox>
            <el-checkbox label="美白">美白</el-checkbox>
            <el-checkbox label="敏感">敏感</el-checkbox>
            <el-checkbox label="修复">修复</el-checkbox>
            <el-checkbox label="皱纹">皱纹</el-checkbox>
            <el-checkbox label="提升">提升</el-checkbox>
            <el-checkbox label="紧致">紧致</el-checkbox>
          </el-checkbox-group>
        </div>

        <!-- 操作按钮 -->
        <div class="form-actions">
          <el-button @click="resetForm">重置</el-button>
          <el-button type="primary" @click="submitForm" :loading="submitLoading">提交</el-button>
        </div>
      </el-form>
    </div>
  </div>
</template>

<script>
import API from "@/api/CRM/Consultation/consultation";
import customerAPI from "@/api/CRM/Customer/customer";
import { regionData } from "element-china-area-data";

export default {
  name: "ConsultationForm",
  data() {
    return {
      loading: false,
      submitLoading: false,
      customerID: null,
      formData: {
        name: "",
        birthday: "",
        gender: "",
        phoneNumber: "",
        isMarried: "",
        childrenCount: "",
        address: "",
        spotRemoval: [],
        cosmetic: [],
        skinCare: [],
        brand: "",
        stainsDuration: "",
        demand: [],
      },
      rules: {
        name: [
          { required: true, message: "请输入姓名", trigger: "blur" }
        ],
        birthday: [
          { required: true, message: "请输入生日", trigger: "blur" }
        ],
        gender: [
          { required: true, message: "请选择性别", trigger: "change" }
        ],
        phoneNumber: [
          { required: true, message: "请输入手机号", trigger: "blur" },
          { pattern: /^1[3-9]\d{9}$/, message: "请输入正确的手机号", trigger: "blur" }
        ]
      },
      regionData: regionData,
      provinceCityArea: [],
    };
  },
  methods: {
    // 从URL参数中获取customerID
    getCustomerIDFromURL() {
      const urlParams = new URLSearchParams(window.location.search);
      const customerID = urlParams.get('customerID');
      return customerID ? parseInt(customerID) : null;
    },
    
    // 获取客户详情
    async getCustomerDetail() {
      if (!this.customerID) return;
      
      this.loading = true;
      try {
        const res = await customerAPI.getCustomerDetail({ CustomerID: this.customerID });
        if (res.StateCode === 200) {
          const customerData = res.Data;
          // 填充表单数据
          this.fillFormData(customerData);
        } else {
          this.$message.error(res.Message || "获取客户信息失败");
        }
      } catch (error) {
        console.error("获取客户信息失败:", error);
        this.$message.error("获取客户信息失败，请检查网络连接");
      } finally {
        this.loading = false;
      }
    },
    
    // 填充表单数据
    fillFormData(customerData) {
      this.formData.name = customerData.Name || "";
      this.formData.birthday = customerData.Birthday || "";
      this.formData.gender = customerData.Gender || "";
      this.formData.phoneNumber = customerData.PhoneNumber || "";
      this.formData.address = customerData.Address || "";
      
      // 设置省市区
      if (customerData.ProvinceCode && customerData.CityCode && customerData.AreaCode) {
        this.provinceCityArea = [customerData.ProvinceCode, customerData.CityCode, customerData.AreaCode];
      }
    },
    
    changeProvinceCityArea(val) {
      this.provinceCityArea = val;
    },
    submitForm() {
      this.$refs.consultationForm.validate((valid) => {
        if (valid) {
          this.submitLoading = true;
          
          // 构建提交数据
          const submitData = {
            ...this.formData,
            // 当选择未婚时，childrenCount 设置为空
            childrenCount: this.formData.isMarried === 'true' ? this.formData.childrenCount : '',
            provinceCode: this.provinceCityArea ? this.provinceCityArea[0] : "",
            cityCode: this.provinceCityArea ? this.provinceCityArea[1] : "",
            areaCode: this.provinceCityArea ? this.provinceCityArea[2] : "",
            // 添加customerID
            customerID: this.customerID || null,
          };

          // 调用API提交数据
          API.create(submitData)
            .then((res) => {
              if (res.StateCode === 200) {
                this.$message.success("咨询服务单提交成功！");
                this.resetForm();
              } else {
                this.$message.error(res.Message || "提交失败，请重试");
              }
            })
            .catch((error) => {
              console.error("提交失败:", error);
              this.$message.error("提交失败，请检查网络连接");
            })
            .finally(() => {
              this.submitLoading = false;
            });
        } else {
          this.$message.error("请完善必填信息");
          return false;
        }
      });
    },
    resetForm() {
      this.$refs.consultationForm.resetFields();
      this.formData = {
        name: "",
        birthday: "",
        gender: "",
        phoneNumber: "",
        isMarried: "",
        childrenCount: "",
        address: "",
        spotRemoval: [],
        cosmetic: [],
        skinCare: [],
        brand: "",
        stainsDuration: "",
        demand: []
      };
      this.provinceCityArea = [];
    }
  },
  mounted() {
    // 页面加载完成后的初始化操作
    // 从URL参数中获取customerID
    this.customerID = this.getCustomerIDFromURL();
    
    // 如果存在customerID，则获取客户详情
    if (this.customerID) {
      this.getCustomerDetail();
    }
  }
};
</script>

<style lang="scss" scoped>
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}
.el-cascader {
  width: 100%;
}
.consultation-form {
  font-family: '微软雅黑', sans-serif;
  background: #fff;
  min-height: 100vh;
  overflow: scroll;
  height: 100%;
  padding-bottom: 100px;
  
  .form-header {
    // background: linear-gradient(135deg, #a8e6cf 0%, #7fcdcd 100%);
    padding: 20px 40px;
    border-bottom: 3px solid #7fcdcd;
    
    .logo-section {
      display: flex;
      align-items: center;
      justify-content: space-between;
      
      .logo {
        height: 60px;
        width: auto;
      }
      
      .title-section {
        text-align: right;
        
        h1 {
          font-size: 20px;
          color: rgb(129,216,208);
          margin: 0 0 5px 0;
          font-weight: 300;
          letter-spacing: 2px;
        }
        
        h2 {
          font-size: 20px;
          color: rgb(129,216,208);
          margin: 0;
          font-weight: 500;
        }
      }
    }
  }
  
  .form-content {
    padding: 30px 40px;
    
    .form-section {
      margin-bottom: 40px;
      
      .section-title {
        font-size: 18px;
        color: #7fcdcd;
        margin-bottom: 20px;
        padding-bottom: 10px;
        border-bottom: 2px solid #7fcdcd;
        font-weight: 500;
      }
      
      .subsection {
        margin-top: 30px;
        padding-left: 20px;
        
        .subsection-title {
          font-size: 16px;
          color: #7fcdcd;
          margin-bottom: 15px;
          font-weight: 500;
        }
      }
    }
    
    .address-container {
      display: flex;
      align-items: center;
      gap: 10px;
    }
    
    .checkbox-group {
      display: flex;
      flex-wrap: wrap;
      gap: 20px;
      
      .el-checkbox {
        margin-right: 0;
        margin-bottom: 10px;
        
        &:deep(.el-checkbox__label) {
          font-size: 14px;
          color: #666;
        }
        
        &:deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
          background-color: #7fcdcd;
          border-color: #7fcdcd;
        }
      }
    }
    
    .radio-group {
      display: flex;
      flex-wrap: wrap;
      gap: 30px;
      
      .el-radio {
        margin-right: 0;
        margin-bottom: 10px;
        
        &:deep(.el-radio__label) {
          font-size: 14px;
          color: #666;
        }
        
        &:deep(.el-radio__input.is-checked .el-radio__inner) {
          background-color: #7fcdcd;
          border-color: #7fcdcd;
        }
      }
    }
    
    .form-actions {
      text-align: center;
      margin-top: 50px;
      padding-top: 30px;
      border-top: 1px solid #eee;
      
      .el-button {
        padding: 12px 40px;
        font-size: 16px;
        margin: 0 15px;
        
        &.el-button--primary {
          background-color: #7fcdcd;
          border-color: #7fcdcd;
          
          &:hover {
            background-color: #6bb6b6;
            border-color: #6bb6b6;
          }
        }
      }
    }
  }
}

// 全局样式覆盖
:deep(.el-form-item__label) {
  color: #333;
  font-weight: 500;
}

:deep(.el-input__inner) {
  border-color: #ddd;
  
  &:focus {
    border-color: #7fcdcd;
  }
}

:deep(.el-select .el-input__inner) {
  &:focus {
    border-color: #7fcdcd;
  }
}

:deep(.el-date-picker .el-input__inner) {
  &:focus {
    border-color: #7fcdcd;
  }
}
</style>
