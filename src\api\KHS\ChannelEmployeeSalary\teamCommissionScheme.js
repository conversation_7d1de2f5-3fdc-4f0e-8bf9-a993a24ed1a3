/**
 * Created by preference on 2022/09/02
 *  zmx
 */

import * as API from "@/api/index";
export default {
  /**   */
  channelEmployeeTeamCommissionScheme: (params) => {
    return API.POST("api/channelEmployeeTeamCommissionScheme/all", params);
  },
  /**   */
  channelEmployeeTeamCommissionScheme_create: (params) => {
    return API.POST("api/channelEmployeeTeamCommissionScheme/create", params);
  },
  /**   */
  channelEmployeeTeamCommissionScheme_commission: (params) => {
    return API.POST(
      "api/channelEmployeeTeamCommissionScheme/commission",
      params
    );
  },
  /**   */
  channelEmployeeTeamCommissionScheme_commissionEmployee: (params) => {
    return API.POST(
      "api/channelEmployeeTeamCommissionScheme/commissionEmployee",
      params
    );
  },
  /**   */
  channelEmployeeTeamCommissionScheme_employee: (params) => {
    return API.POST("api/channelEmployeeTeamCommissionScheme/employee", params);
  },
  /**   */
  channelEmployeeTeamCommissionScheme_update: (params) => {
    return API.POST("api/channelEmployeeTeamCommissionScheme/update", params);
  },
  /**  业绩取值方案查询-不加分页 */
  channelPerformanceScheme_valid: (params) => {
    return API.POST("api/channelEmployeePerformanceScheme/valid", params);
  },
  //员工列表
  getEntityCommissionSchemeAllEmployee: (params) => {
    return API.POST("api/entityCommissionScheme/allEmployee", params);
  },
};
