/**
 * Created by wsf on 2022/02/23.
 * 渠道业绩提成方案 api
 */

 import * as API from '@/api/index'

 export default {
    /* 提成查询 */
    getChannelCommissionSchemeAll: params => {
       return API.POST('api/channelCommissionScheme/all', params)
    },
    /* 提成添加 */
    createChannelCommissionScheme: params => {
        return API.POST('api/channelCommissionScheme/create', params)
    },
     /* 提成修改 */
    updateChannelCommissionScheme: params => {
        return API.POST('api/channelCommissionScheme/update', params)
    },
    /* 提成详情-条件 */
    getCommission: params => {
        return API.POST('api/channelCommissionScheme/commission', params)
    },
    /* 提成详情-渠道范围 */
    getRange: params => {
        return API.POST('api/channelCommissionScheme/range', params)
    },
    /* 业绩取值方案查询-不加分页 */
    getValidChannelPerformanceScheme: params => {
        return API.POST('api/channelPerformanceScheme/valid', params)
    }
 }