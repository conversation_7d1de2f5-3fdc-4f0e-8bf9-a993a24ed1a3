/**
 * Created by wsf on 2022/04/13
 */
 import * as API from '@/api/index'
 export default {
     // 获取跟进规则列表
     getFollowUpRuleList:params => {
         return API.POST('api/followUpRule/all',params)
     },
     // 获取全部触发条件
     getFollowUpRuleCondition:params => {
         return API.POST('api/followUpRule/condition',params)
     },
     // 获取跟进规则详情
     getFollowUpRuleDetail:params => {
         return API.POST('api/followUpRule/detail',params)
     },
     // 跟进规则添加
     createFollowUpRule:params => {
         return API.POST('api/followUpRule/create',params)
     },
     // 跟进规则修改
     uploadFollowUpRule:params => {
         return API.POST('api/followUpRule/update',params)
     },
 }