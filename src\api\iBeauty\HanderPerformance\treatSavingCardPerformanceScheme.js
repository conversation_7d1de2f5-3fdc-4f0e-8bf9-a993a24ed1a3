/**
 * Created by <PERSON><PERSON><PERSON> on 2021/12/28
 *
 */

import * as API from '@/api/index'
export default {
  /** 业绩方案列表  */
  treatSavingCardPerformanceScheme_list: params => {
    return API.POST('api/treatSavingCardPerformanceScheme/list', params)
  },
  /** 业绩方案保存  */
  treatSavingCardPerformanceScheme_create: params => {
    return API.POST('api/treatSavingCardPerformanceScheme/create', params)
  },
  /**  业绩方案删除  */
  treatSavingCardPerformanceScheme_delete: params => {
    return API.POST('api/treatSavingCardPerformanceScheme/delete', params)
  },
  /**  储值卡分类业绩 */
  treatSavingCardCategoryPerformance_all: params => {
    return API.POST('api/treatSavingCardCategoryPerformance/all', params)
  },
  /** 储值卡分类业绩保存  */
  treatSavingCardCategoryPerformance_update: params => {
    return API.POST('api/treatSavingCardCategoryPerformance/update', params)
  },
  /** 所有储值卡经手人业绩  */
  treatSavingCardSchemeHandlerPerformance_all: params => {
    return API.POST('api/treatSavingCardSchemeHandlerPerformance/all', params)
  },
  /** 所有储值卡经手人业绩保存  */
  treatSavingCardSchemeHandlerPerformance_update: params => {
    return API.POST('api/treatSavingCardSchemeHandlerPerformance/update', params)
  },
  /** 分类储值卡经手人业绩   */
  treatSavingCardCategoryHandlerPerformance_all: params => {
    return API.POST('api/treatSavingCardCategoryHandlerPerformance/all', params)
  },
  /** 分类储值卡经手人业绩保存  */
  treatSavingCardCategoryHandlerPerformance_update: params => {
    return API.POST('api/treatSavingCardCategoryHandlerPerformance/update', params)
  },
  /**  储值卡业绩 */
  treatSavingCardPerformance_all: params => {
    return API.POST('api/treatSavingCardPerformance/all', params)
  },
  /** 储值卡业绩保存  */
  treatSavingCardPerformance_update: params => {
    return API.POST('api/treatSavingCardPerformance/update', params)
  },
  /**  储值卡经手人业绩 */
  treatSavingCardHandlerPerformance_all: params => {
    return API.POST('api/treatSavingCardHandlerPerformance/all', params)
  },
  /** 储值卡经手人业绩保存  */
  treatSavingCardHandlerPerformance_update: params => {
    return API.POST('api/treatSavingCardHandlerPerformance/update', params)
  },

}
