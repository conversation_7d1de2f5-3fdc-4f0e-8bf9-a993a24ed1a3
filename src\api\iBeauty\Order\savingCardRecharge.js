/**
 * Created by preference on 2021/07/12
 *  zmx 
 */

import * as API from '@/api/index'
export default {
  /** 获取顾客存量  */
  getRechargeSavingCardAccount: params => {
    return API.POST('api/customerAccount/rechargeSavingCardAccount', params)
  },
  /** 开单  */
  saleBillRecharge: params => {
    return API.POST('api/saleBill/recharge', params)
  },
  /** 挂单  */
  createRechargePendingOrder: params => {
    return API.POST('api/saleBill/createRechargePendingOrder', params)
  },
}