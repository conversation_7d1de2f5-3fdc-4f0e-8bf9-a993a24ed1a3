<template>
  <div class="customercondition content_body">
    <div class="tip font_14">
      <span>入会门槛设置</span>
      <span class="padlt_20 color_999">升级会员的门槛设置</span>
    </div>

    <el-form size="small" label-width="150px">
      <el-form-item label="入会门槛设置：">
        <el-radio-group v-model="TypeRadio">
          <el-row class="marbm_10">
            <el-col>
              <el-radio :label="10" @change="getRadioType" class="line_height_32">
                无门槛
                <span class="color_999 marlt_10 font_13">授权手机号即可成为会员</span>
              </el-radio>
            </el-col>
          </el-row>
          <el-row class="marbm_10">
            <el-col>
              <el-radio :label="20" @change="getRadioType" class="line_height_32">
                完成首次销售
                <span class="color_999 marlt_10 font_13">销售订单状态为 "已完成" ，包括0元订单</span>
              </el-radio>
            </el-col>
          </el-row>
          <el-row class="marbm_10">
            <el-col>
              <el-radio :label="30" @change="getRadioType" class="line_height_32">
                完成首次消耗
                <span class="color_999 marlt_10 font_13">消耗订单状态为 "已完成" ，包括0元订单</span>
              </el-radio>
            </el-col>
          </el-row>
          <el-row class="marbm_10">
            <el-col>
              <el-radio :label="40" @change="getRadioType" class="line_height_32">
                单次销售满
                <el-input
                  style="width: 150px;"
                  class="custom-input-number"
                  type="number"
                  :min="0"
                  :disabled="TypeRadio !== 40"
                  v-model="singleSaleInput"
                  v-input-fixed="0"
                  size="small"
                  placeholder="请输入金额"
                >
                  <template slot="append">元</template>
                </el-input>
                <span class="color_999 marlt_10 font_13">仅实付金额</span>
              </el-radio>
            </el-col>
          </el-row>
          <el-row class="marbm_10">
            <el-col>
              <el-radio :label="50" @change="getRadioType" class="line_height_32">
                累计销售满
                <el-input
                  style="width: 150px;"
                  class="custom-input-number"
                  type="number"
                  :min="0"
                  :disabled="TypeRadio !== 50"
                  v-model="CumulativeSaleInput"
                  v-input-fixed="0"
                  size="small"
                  placeholder="请输入金额"
                >
                  <template slot="append">元</template>
                </el-input>
                <span class="color_999 marlt_10 font_13">仅实付金额</span>
              </el-radio>
            </el-col>
          </el-row>
          <el-row class="marbm_10">
            <el-col>
              <el-radio :label="60" @change="getRadioType" class="line_height_32">
                累计消耗满
                <el-input
                  style="width: 150px;"
                  class="custom-input-number"
                  type="number"
                  :min="0"
                  :disabled="TypeRadio !== 60"
                  v-model="CumulativeTreatInput"
                  v-input-fixed="0"
                  size="small"
                  placeholder="请输入金额"
                >
                  <template slot="append">元</template>
                </el-input>
                <span class="color_999 marlt_10 font_13">包括：实付金额、卡抵扣</span>
              </el-radio>
            </el-col>
          </el-row>
          <el-row class="marbm_10">
            <el-col :span="5">
              <el-radio :label="70" @change="getRadioType" class="line_height_32">销售指定商品类型</el-radio>
            </el-col>
            <el-col :span="19">
              <el-checkbox-group style="width:550px"
                v-model="appointTypeCheck"
                :disabled="TypeRadio !== 70"
                class="marlt_5"
              >
                <el-checkbox
                  class="line_height_32"
                  v-for="item in saleTypeCheckout"
                  :key="item.ID"
                  :label="item.ID + ''"
                >{{ item.Name }}</el-checkbox>
              </el-checkbox-group>
            </el-col>
          </el-row>
          <el-row class="marbm_10">
            <el-col>
              <el-radio :label="80" @change="getRadioType" class="line_height_32">销售指定商品</el-radio>
              <el-button
                class="marrt_10"
                type="text"
                @click="saleCreateClick"
                :disabled="TypeRadio !== 80"
              >添加</el-button>
            </el-col>
          </el-row>
        </el-radio-group>
      </el-form-item>
    </el-form>
    <el-table
      v-if="TypeRadio==80"
      :data="saleData"
      height="250"
      style="width: 700px; margin-left: 150px; overflow: auto"
      size="small"
    >
      <el-table-column prop="GoodsName" label="商品名称"></el-table-column>
      <el-table-column prop="GoodsType" label="商品类别">
        <template slot-scope="scope">{{ getCommodityType(scope.row.GoodsType) }}</template>
      </el-table-column>
      <el-table-column label="关联会员等级">
        <template slot-scope="scope">
          <el-select
            v-model="scope.row.CustomerLevelID"
            placeholder="请选择"
            :default-first-option="true"
            size="small"
            clearable
            @change="() => changeThis(scope.row, scope.$index)"
          >
            <el-option
              v-for="item in customerLevel"
              :key="item.ID"
              :label="item.Name"
              :value="item.ID"
            ></el-option>
          </el-select>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="80px">
        <template slot-scope="scope">
          <el-button type="danger" size="small" @click="saleDataDelete(scope.row, scope.$index)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-button
      type="primary"
      size="small"
      style="margin-left: 150px;margin-top: 20px "
      @click="createCustomerClick"
    >保 存</el-button>
    <!-- 添加指定商品 -->
    <el-dialog title="指定商品" :visible.sync="dialogVisible" width="1000px">
      <el-row :gutter="20">
        <el-col :span="8">
          <el-collapse v-model="collapseIndex" accordion @change="collapseChange">
            <el-collapse-item title="产品" name="1">
              <el-input v-model="productName" placeholder="请输入关键词进行搜索" clearable size="small"></el-input>
              <el-scrollbar class="el-scrollbar_height_range">
                <el-tree
                  ref="treeProduct"
                  :data="productList"
                  node-key="PID"
                  :props="{ children: 'Child', label: 'Name' }"
                  :filter-node-method="filterNode"
                  :default-checked-keys="productDefaultCheckedKeys"
                  :default-expanded-keys="productDefaultExpandedKeys"
                >
                  <div slot-scope="{ data }" style="width: 90%">
                    <el-row type="flex" justify="space-between">
                      <el-col :span="18" class="dis_flex flex_y_center">
                        <el-tooltip
                          :disabled="data.IsGoods != 1"
                          :content="data.Name"
                          placement="top"
                          effect="light"
                        >
                          <div class="clamp1">{{ data.Name }}</div>
                        </el-tooltip>

                        <el-tag class="marlt_5" type="info" size="mini" v-if="data.IsGoods == 0">分类</el-tag>
                      </el-col>
                      <el-col :span="6" style="text-align: right">
                        <el-button
                          type="text"
                          @click="changeAll(1, data)"
                          size="mini"
                          v-if="data.IsGoods == 1"
                        >添加</el-button>
                      </el-col>
                    </el-row>
                  </div>
                </el-tree>
              </el-scrollbar>
            </el-collapse-item>
            <el-collapse-item title="项目" name="2">
              <el-input v-model="projectName" placeholder="请输入关键词进行搜索" clearable size="small"></el-input>
              <el-scrollbar class="el-scrollbar_height_range">
                <el-tree
                  ref="treeProject"
                  :data="projectList"
                  node-key="PID"
                  :props="{ children: 'Child', label: 'Name' }"
                  :filter-node-method="filterNode"
                  :default-checked-keys="projectDefaultCheckedKeys"
                  :default-expanded-keys="projectDefaultExpandedKeys"
                >
                  <div slot-scope="{ data }" style="width: 90%">
                    <el-row type="flex" justify="space-between">
                      <el-col :span="18" class="dis_flex flex_y_center">
                        <el-tooltip
                          :disabled="data.IsGoods != 1"
                          :content="data.Name"
                          placement="top"
                          effect="light"
                        >
                          <div class="clamp1">{{ data.Name }}</div>
                        </el-tooltip>

                        <el-tag class="marlt_5" type="info" size="mini" v-if="data.IsGoods == 0">分类</el-tag>
                      </el-col>
                      <el-col :span="6" style="text-align: right">
                        <el-button
                          type="text"
                          @click="changeAll(2, data)"
                          size="mini"
                          v-if="data.IsGoods == 1"
                        >添加</el-button>
                      </el-col>
                    </el-row>
                  </div>
                </el-tree>
              </el-scrollbar>
            </el-collapse-item>
            <el-collapse-item title="通用次卡" name="3">
              <el-input v-model="generalName" placeholder="请输入关键词进行搜索" clearable size="small"></el-input>
              <el-scrollbar class="el-scrollbar_height_range">
                <el-tree
                  ref="treeGeneral"
                  :data="generalCardList"
                  node-key="PID"
                  :props="{ children: 'Child', label: 'Name' }"
                  :filter-node-method="filterNode"
                  :default-checked-keys="generalDefaultCheckedKeys"
                  :default-expanded-keys="generalDefaultExpandedKeys"
                >
                  <div slot-scope="{ data }" style="width: 90%">
                    <el-row type="flex" justify="space-between">
                      <el-col :span="18" class="dis_flex flex_y_center">
                        <el-tooltip
                          :disabled="data.IsGoods != 1"
                          :content="data.Name"
                          placement="top"
                          effect="light"
                        >
                          <div class="clamp1">{{ data.Name }}</div>
                        </el-tooltip>

                        <el-tag class="marlt_5" type="info" size="mini" v-if="data.IsGoods == 0">分类</el-tag>
                      </el-col>
                      <el-col :span="6" style="text-align: right">
                        <el-button
                          type="text"
                          @click="changeAll(3, data)"
                          size="mini"
                          v-if="data.IsGoods == 1"
                        >添加</el-button>
                      </el-col>
                    </el-row>
                  </div>
                </el-tree>
              </el-scrollbar>
            </el-collapse-item>
            <el-collapse-item title="时效卡" name="4">
              <el-input v-model="timeName" placeholder="请输入关键词进行搜索" clearable size="small"></el-input>
              <el-scrollbar class="el-scrollbar_height_range">
                <el-tree
                  ref="treeTime"
                  :data="timeCardList"
                  node-key="PID"
                  :props="{ children: 'Child', label: 'Name' }"
                  :filter-node-method="filterNode"
                  :default-checked-keys="timeDefaultCheckedKeys"
                  :default-expanded-keys="timeDefaultExpandedKeys"
                >
                  <div slot-scope="{ data }" style="width: 90%">
                    <el-row type="flex" justify="space-between">
                      <el-col :span="18" class="dis_flex flex_y_center">
                        <el-tooltip
                          :disabled="data.IsGoods != 1"
                          :content="data.Name"
                          placement="top"
                          effect="light"
                        >
                          <div class="clamp1">{{ data.Name }}</div>
                        </el-tooltip>
                        <el-tag class="marlt_5" type="info" size="mini" v-if="data.IsGoods == 0">分类</el-tag>
                      </el-col>
                      <el-col :span="6" style="text-align: right">
                        <el-button
                          type="text"
                          @click="changeAll(4, data)"
                          size="mini"
                          v-if="data.IsGoods == 1"
                        >添加</el-button>
                      </el-col>
                    </el-row>
                  </div>
                </el-tree>
              </el-scrollbar>
            </el-collapse-item>
            <el-collapse-item title="储值卡" name="5">
              <el-input v-model="chuzhiKaName" placeholder="请输入关键词进行搜索" clearable size="small"></el-input>
              <el-scrollbar class="el-scrollbar_height_range">
                <el-tree
                  ref="treeChuzhiKa"
                  :data="savingCardList"
                  node-key="PID"
                  :props="{ children: 'Child', label: 'Name' }"
                  :filter-node-method="filterNode"
                  :default-checked-keys="savingDefaultCheckedKeys"
                  :default-expanded-keys="savingDefaultExpandedKeys"
                >
                  <div slot-scope="{ data }" style="width: 90%">
                    <el-row type="flex" justify="space-between">
                      <el-col :span="18" class="dis_flex flex_y_center">
                        <el-tooltip
                          :disabled="data.IsGoods != 1"
                          :content="data.Name"
                          placement="top"
                          effect="light"
                        >
                          <div class="clamp1">{{ data.Name }}</div>
                        </el-tooltip>
                        <el-tag class="marlt_5" type="info" size="mini" v-if="data.IsGoods == 0">分类</el-tag>
                      </el-col>
                      <el-col :span="6" style="text-align: right">
                        <el-button
                          type="text"
                          @click="changeAll(5, data)"
                          size="mini"
                          v-if="data.IsGoods == 1"
                        >添加</el-button>
                      </el-col>
                    </el-row>
                  </div>
                </el-tree>
              </el-scrollbar>
            </el-collapse-item>
            <el-collapse-item title="套餐卡" name="6">
              <el-input v-model="packageCardName" placeholder="请输入关键字进行搜索" clearable size="small"></el-input>
              <el-scrollbar class="el-scrollbar_height_range">
                <el-tree
                  ref="treePackage"
                  :data="packageCardList"
                  node-key="PID"
                  :default-checked-keys="packageDefaultCheckedKeys"
                  :default-expanded-keys="packageDefaultExpandedKeys"
                  :props="{ children: 'Child', label: 'Name' }"
                  :filter-node-method="filterNode"
                >
                  <div slot-scope="{ data }" style="width: 90%">
                    <el-row type="flex" justify="space-between">
                      <el-col :span="18" class="dis_flex flex_y_center">
                        <el-tooltip
                          :disabled="data.IsGoods != 1"
                          :content="data.Name"
                          placement="top"
                          effect="light"
                        >
                          <div class="clamp1">{{ data.Name }}</div>
                        </el-tooltip>
                        <el-tag class="marlt_5" type="info" size="mini" v-if="data.IsGoods == 0">分类</el-tag>
                      </el-col>
                      <el-col :span="6" style="text-align: right">
                        <el-button
                          type="text"
                          @click="changeAll(6, data)"
                          size="mini"
                          v-if="data.IsGoods == 1"
                        >添加</el-button>
                      </el-col>
                    </el-row>
                  </div>
                </el-tree>
              </el-scrollbar>
            </el-collapse-item>
          </el-collapse>
        </el-col>
        <el-col :span="16">
          <el-table
            size="small"
            :data="packageCardProduct.filter((data) => !productName || data.Name.toLowerCase().includes(productName.toLowerCase()))"
            v-if="collapseIndex == 1"
          >
            <el-table-column prop="GoodsName" label="产品名称">
              <template slot-scope="scope">
                <span>{{ scope.row.GoodsName }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="GoodsCategoryName" label="产品分类"></el-table-column>
            <el-table-column label="销售价格" prop="Price">
              <template slot-scope="scope">{{ scope.row.Price | toFixed | NumFormat }}</template>
            </el-table-column>
            <el-table-column label="操作" width="80px">
              <template slot-scope="scope">
                <el-button type="danger" size="small" @click="remove(3, scope.$index)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
          <el-table
            size="small"
            :data="packageCardProject.filter((data) => !projectName || data.Name.toLowerCase().includes(projectName.toLowerCase()))"
            v-if="collapseIndex == 2"
          >
            <el-table-column prop="GoodsName" label="项目名称">
              <template slot-scope="scope">
                <span>{{ scope.row.GoodsName }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="GoodsCategoryName" label="项目分类"></el-table-column>
            <el-table-column label="销售价格" prop="Price">
              <template slot-scope="scope">{{ scope.row.Price | toFixed | NumFormat }}</template>
            </el-table-column>
            <el-table-column label="操作" width="80px">
              <template slot-scope="scope">
                <el-button type="danger" size="small" @click="remove(4, scope.$index)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
          <el-table
            size="small"
            :data="packageCardGeneral.filter((data) => !generalName || data.Name.toLowerCase().includes(generalName.toLowerCase()))"
            v-if="collapseIndex == 3"
          >
            <el-table-column prop="GoodsName" label="通用次卡名称">
              <template slot-scope="scope">
                <span>{{ scope.row.GoodsName }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="GoodsCategoryName" label="通用次卡分类"></el-table-column>
            <el-table-column label="售价" prop="Price">
              <template slot-scope="scope">{{ scope.row.Price | toFixed | NumFormat }}</template>
            </el-table-column>
            <el-table-column label="操作" width="80px">
              <template slot-scope="scope">
                <el-button type="danger" size="small" @click="remove(5, scope.$index)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
          <el-table
            size="small"
            :data="packageCardTime.filter((data) => !timeName || data.Name.toLowerCase().includes(timeName.toLowerCase()))"
            v-if="collapseIndex == 4"
          >
            <el-table-column prop="GoodsName" label="时效卡名称">
              <template slot-scope="scope">
                <span>{{ scope.row.GoodsName }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="GoodsCategoryName" label="时效卡分类"></el-table-column>
            <el-table-column label="售价" prop="Price">
              <template slot-scope="scope">{{ scope.row.Price | toFixed | NumFormat }}</template>
            </el-table-column>
            <el-table-column label="操作" width="80px">
              <template slot-scope="scope">
                <el-button type="danger" size="small" @click="remove(6, scope.$index)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
          <el-table
            size="small"
            :data="packageCardSaving.filter((data) => !chuzhiKaName || data.Name.toLowerCase().includes(chuzhiKaName.toLowerCase()))"
            v-if="collapseIndex == 5"
          >
            <el-table-column prop="GoodsName" label="储值卡名称">
              <template slot-scope="scope">
                <span>{{ scope.row.GoodsName }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="GoodsCategoryName" label="储值卡分类"></el-table-column>
            <el-table-column label="售价" prop="Price">
              <template slot-scope="scope">{{ scope.row.Price | toFixed | NumFormat }}</template>
            </el-table-column>
            <el-table-column label="操作" width="80px">
              <template slot-scope="scope">
                <el-button type="danger" size="small" @click="remove(7, scope.$index)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
          <el-table
            size="small"
            :data="selectPackageCard.filter((data) => !packageCardName || data.Name.toLowerCase().includes(packageCardName.toLowerCase()))"
            v-if="collapseIndex == 6"
          >
            <el-table-column prop="GoodsName" label="套餐卡名称">
              <template slot-scope="scope">
                <span>{{ scope.row.GoodsName }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="GoodsCategoryName" label="套餐卡分类"></el-table-column>
            <el-table-column label="售价" prop="Price">
              <template slot-scope="scope">{{ scope.row.Price | toFixed | NumFormat }}</template>
            </el-table-column>
            <el-table-column label="操作" width="80px">
              <template slot-scope="scope">
                <el-button type="danger" size="small" @click="remove(8, scope.$index)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
          <!--展示全部-->
          <el-table
            size="small"
            max-height="500px"
            :data="packageCardSelectAll"
            v-if="collapseIndex == ''"
          >
            <el-table-column prop="GoodsName" label="商品名称"></el-table-column>
            <el-table-column prop="GoodsType" label="商品类型" width="100px">
              <template slot-scope="scope">{{ getCommodityType(scope.row.GoodsType) }}</template>
            </el-table-column>
            <el-table-column prop="OldPrice" label="销售价格">
              <template slot-scope="scope">{{ scope.row.OldPrice | toFixed | NumFormat }}</template>
            </el-table-column>
            <el-table-column label="操作" width="80px">
              <template slot-scope="scope">
                <el-button type="danger" size="small" @click="remove(9, scope.row, scope.$index)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-col>
      </el-row>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false" size="small">取 消</el-button>
        <el-button type="primary" @click="appointAddClick" size="small">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import APISaving from "@/api/iBeauty/Goods/savingCard";
import APILEVEL from "@/api/CRM/Customer/customerLevel";
import API from "@/api/CRM/CustomerLevel/customercondition";
export default {
  name: "CustomerCondition",
  props: {},
  /** 监听数据变化   */
  watch: {
    productName(val) {
      this.$refs.treeProduct.filter(val);
    },
    projectName(val) {
      this.$refs.treeProject.filter(val);
    },
    generalName(val) {
      this.$refs.treeGeneral.filter(val);
    },
    timeName(val) {
      this.$refs.treeTime.filter(val);
    },
    chuzhiKaName(val) {
      this.$refs.treeChuzhiKa.filter(val);
    }
  },
  /**  引入的组件  */
  components: {},
  /**  Vue 实例的数据对象**/
  data() {
    return {
      dialogVisible: false, // 添加指定商品
      TypeRadio: "", // 单选
      singleSaleInput: "", // 单笔销售
      CumulativeSaleInput: "", // 累计销售
      CumulativeTreatInput: "", // 累计消耗
      appointTypeCheck: [], // 指定销售类型
      customerLevel: [], // 会员等级
      saleData: [], // 指定商品
      saleTypeCheckout: [
        { Name: "产品", ID: 10 },
        { Name: "项目", ID: 20 },
        { Name: "通用次卡", ID: 30 },
        { Name: "时效卡", ID: 40 },
        { Name: "储值卡", ID: 50 },
        { Name: "套餐卡", ID: 60 }
      ],

      collapseIndex: "", // 产品-项目-通用次卡-时效卡-储值卡
      productName: "", // 产品关键字搜索
      projectName: "", // 项目关键字搜索
      generalName: "", // 通用次卡搜索
      timeName: "", // 时效卡搜索
      chuzhiKaName: "", // 储值卡搜索
      packageCardName: "" /**  套餐卡  */,

      productList: [], //产品列表
      projectList: [], // 项目列表
      generalCardList: [], // 通用次卡列表
      timeCardList: [], // 时效卡列表
      savingCardList: [], // 储值卡列表
      packageCardList: [], // 储值卡列表

      packageDefaultCheckedKeys: [], // 套餐卡 回显
      packageDefaultExpandedKeys: [1],
      productDefaultCheckedKeys: [], // 产品 回显
      productDefaultExpandedKeys: [1],
      savingDefaultCheckedKeys: [], // 储值卡 回显
      savingDefaultExpandedKeys: [1],
      generalDefaultCheckedKeys: [], // 通用次卡 回显
      generalDefaultExpandedKeys: [1],
      timeDefaultCheckedKeys: [], // 时效卡 回显
      timeDefaultExpandedKeys: [1],
      projectDefaultCheckedKeys: [], // 项目 回显
      projectDefaultExpandedKeys: [1],
      packageCardProject: [],
      packageCardGeneral: [],
      packageCardProduct: [], //保存的产品
      packageCardTime: [],
      packageCardSaving: [],
      selectPackageCard: [],
      packageCardSelectAll: []
    };
  },
  /**计算属性  */
  computed: {},
  /**  方法集合  */
  methods: {
    // 商品类型
    getCommodityType(GoodsType) {
      if (GoodsType == 10) {
        return "产品";
      } else if (GoodsType == 20) {
        return "项目";
      } else if (GoodsType == 30) {
        return "通用次卡";
      } else if (GoodsType == 40) {
        return "时效卡";
      } else if (GoodsType == 50) {
        return "储值卡";
      } else if (GoodsType == 60) {
        return "套餐卡";
      }
    },
    // 切换设置
    getRadioType() {
      if (this.TypeRadio !== 40) {
        this.singleSaleInput = "";
      }
      if (this.TypeRadio !== 50) {
        this.CumulativeSaleInput = "";
      }
      if (this.TypeRadio !== 60) {
        this.CumulativeTreatInput = "";
      }
      if (this.TypeRadio !== 70) {
        this.appointTypeCheck = [];
      }
      if (this.TypeRadio !== 80) {
        this.saleData = [];
      }
    },
    // 点击添加指定商品
    saleCreateClick() {
      var that = this;
      that.productList = Object.assign([], that.productList);
      that.projectList = Object.assign([], that.projectList);
      that.generalCardList = Object.assign([], that.generalCardList);
      that.timeCardList = Object.assign([], that.timeCardList);
      that.savingCardList = Object.assign([], that.savingCardList);
      that.productDefaultCheckedKeys = []; //回显
      that.productDefaultExpandedKeys = [1];
      that.projectDefaultCheckedKeys = []; //回显
      that.projectDefaultExpandedKeys = [1];
      that.generalDefaultCheckedKeys = []; //回显
      that.generalDefaultExpandedKeys = [1];
      that.timeDefaultCheckedKeys = []; //回显
      that.timeDefaultExpandedKeys = [1];
      that.packageDefaultCheckedKeys = []; //回显
      that.packageDefaultExpandedKeys = [1];
      this.dialogVisible = true;
    },
    // 获取产品
    productData: function() {
      var that = this;
      let params = {
        productName: that.productName
      };
      APISaving.savingCardProduct(params).then(res => {
        if (res.StateCode == 200) {
          this.productList = res.Data;
          that.setRecursion(res.Data);
        } else {
          this.$message.error({
            message: res.Message,
            duration: 2000
          });
        }
      });
    },
    // 获取项目
    projectData: function() {
      var that = this;
      APISaving.savingCardProject().then(res => {
        if (res.StateCode == 200) {
          that.projectList = res.Data;
          that.setRecursion(res.Data);
        } else {
          that.$message.error({
            message: res.Message,
            duration: 2000
          });
        }
      });
    },
    // 获取通用次卡
    generalCardData: function() {
      var that = this;
      APISaving.savingCardGeneralCard().then(res => {
        if (res.StateCode == 200) {
          that.generalCardList = res.Data;
          that.setRecursion(res.Data);
        } else {
          that.$message.error({
            message: res.Message,
            duration: 2000
          });
        }
      });
    },
    // 获取储值卡列表
    savingCardData: function() {
      var that = this;
      APISaving.savingCard().then(res => {
        if (res.StateCode == 200) {
          that.savingCardList = res.Data;
          that.setRecursion(res.Data);
        } else {
          that.$message.error({
            message: res.Message,
            duration: 2000
          });
        }
      });
    },
    // 获取时效卡
    timeCardData: function() {
      var that = this;
      APISaving.savingCardTimeCard().then(res => {
        if (res.StateCode == 200) {
          that.timeCardList = res.Data;
          that.setRecursion(res.Data);
        } else {
          that.$message.error({
            message: res.Message,
            duration: 2000
          });
        }
      });
    },
    /**    */
    async savingCardPackageCard() {
      let that = this;
      let res = await APISaving.savingCardPackageCard();
      if (res.StateCode == 200) {
        that.packageCardList = res.Data;
        that.setRecursion(res.Data);
      } else {
        that.$message.error({
          message: res.Message,
          duration: 2000
        });
      }
    },

    // 递归
    setRecursion(data) {
      var that = this;
      for (let i = 0; i <= data.length - 1; i++) {
        if (data[i].IsGoods == 0) {
          data[i].PID = "0" + data[i].ID;
        } else {
          data[i].PID = "1" + data[i].ID;
        }
        if (data[i].Child) {
          that.setRecursion(data[i].Child);
        }
      }
    },
    filterNode(value, data) {
      if (!value) return true;
      return data.Name.indexOf(value) !== -1;
    },
    //设置明细中选择的产品、项目...
    changeAll(index, data) {
      if (index === 1) {
        this.packageCardProduct.push({
          PID: data.PID,
          ID: data.ID,
          Amount: "",
          OldPrice: data.Price,
          Price: data.Price,
          GoodsCategoryName: data.GoodsCategoryName,
          TotalPrice: "",
          Alias: "产品",
          GoodsName: data.Name,
          GoodsType: 10
        });
      } else if (index === 2) {
        this.packageCardProject.push({
          PID: data.PID,
          ID: data.ID,
          Amount: "",
          OldPrice: data.Price,
          Price: data.Price,
          GoodsCategoryName: data.GoodsCategoryName,
          TotalPrice: "",
          Alias: "项目",
          GoodsName: data.Name,
          GoodsType: 20
        });
      } else if (index === 3) {
        this.packageCardGeneral.push({
          PID: data.PID,
          ID: data.ID,
          Amount: "",
          OldPrice: data.Price,
          Price: data.Price,
          GoodsCategoryName: data.GoodsCategoryName,
          TotalPrice: "",
          Alias: "通用次卡",
          GoodsName: data.Name,
          GoodsType: 30
        });
      } else if (index === 4) {
        this.packageCardTime.push({
          PID: data.PID,
          ID: data.ID,
          Amount: "",
          OldPrice: data.Price,
          Price: data.Price,
          GoodsCategoryName: data.GoodsCategoryName,
          TotalPrice: "",
          Alias: "时效卡",
          GoodsName: data.Name,
          GoodsType: 40
        });
      } else if (index === 5) {
        this.packageCardSaving.push({
          PID: data.PID,
          ID: data.ID,
          Amount: "",
          OldPrice: data.Price,
          Price: data.Price,
          GoodsCategoryName: data.GoodsCategoryName,
          TotalPrice: "",
          Alias: "储值卡",
          GoodsName: data.Name,
          GoodsType: 50
        });
      } else if (index === 6) {
        this.selectPackageCard.push({
          PID: data.PID,
          ID: data.ID,
          Amount: "",
          OldPrice: data.Price,
          Price: data.Price,
          GoodsCategoryName: data.GoodsCategoryName,
          TotalPrice: "",
          Alias: "套餐卡",
          GoodsName: data.Name,
          GoodsType: 60
        });
      }
      this.packageCardSelectAll = this.packageCardProduct.concat(
        this.packageCardProject,
        this.packageCardGeneral,
        this.packageCardTime,
        this.packageCardSaving,
        this.selectPackageCard
      );
    },

    remove(index, rowindex, indexall) {
      var that = this;
      if (index === 1) {
        for (let i = 0; i < this.goodsRangeList.length; i++) {
          if (
            this.goodsRangeList[i].Alias == rowindex.Alias &&
            this.goodsRangeList[i].PID == rowindex.PID &&
            this.goodsRangeList[i].Amount == rowindex.Amount &&
            this.goodsRangeList[i].Price == rowindex.Price
          ) {
            that.goodsRangeList.splice(i, 1);
            this.allPrice();
            return;
          }
        }
      } else if (index == 2) {
        for (let i = 0; i < this.goodsRangeLargessList.length; i++) {
          if (
            this.goodsRangeLargessList[i].Alias == rowindex.Alias &&
            this.goodsRangeLargessList[i].PID == rowindex.PID &&
            this.goodsRangeLargessList[i].Amount == rowindex.Amount &&
            this.goodsRangeLargessList[i].Price == rowindex.Price
          ) {
            that.goodsRangeLargessList.splice(i, 1);
            return;
          }
        }
      } else if (index == 3) {
        this.packageCardProduct.splice(rowindex, 1);
      } else if (index == 4) {
        this.packageCardProject.splice(rowindex, 1);
      } else if (index == 5) {
        this.packageCardGeneral.splice(rowindex, 1);
      } else if (index == 6) {
        this.packageCardTime.splice(rowindex, 1);
      } else if (index == 7) {
        this.packageCardSaving.splice(rowindex, 1);
      } else if (index == 8) {
        this.selectPackageCard.splice(rowindex, 1);
      } else {
        if (rowindex.Alias == "产品") {
          this.packageCardProduct.splice(
            this.packageCardProduct.findIndex(p => p.PID == rowindex.PID),
            1
          );
        } else if (rowindex.Alias == "项目") {
          this.packageCardProject.splice(
            this.packageCardProject.findIndex(p => p.PID == rowindex.PID),
            1
          );
        } else if (rowindex.Alias == "通用次卡") {
          this.packageCardGeneral.splice(
            this.packageCardGeneral.findIndex(p => p.PID == rowindex.PID),
            1
          );
        } else if (rowindex.Alias == "时效卡") {
          this.packageCardTime.splice(
            this.packageCardTime.findIndex(p => p.PID == rowindex.PID),
            1
          );
        } else if (rowindex.Alias == "储值卡") {
          this.packageCardSaving.splice(
            this.packageCardSaving.findIndex(p => p.PID == rowindex.PID),
            1
          );
        } else {
          this.selectPackageCard.splice(
            this.packageCardSaving.findIndex(p => p.PID == rowindex.PID),
            1
          );
        }
        this.packageCardSelectAll.splice(indexall, 1);
      }
      // this.collapseChange();
    },
    collapseChange: function() {
      var that = this;
      that.packageCardSelectAll = that.packageCardProduct.concat(
        that.packageCardProject,
        that.packageCardGeneral,
        that.packageCardTime,
        that.packageCardSaving
      );
    },
    // 确定选择指定商品
    appointAddClick() {
      this.packageCardSelectAll.forEach(item => {
        this.saleData.forEach(i => {
          if (i.GoodsID == item.ID) {
            item.CustomerLevelID = i.CustomerLevelID;
            item.CustomerLevelName = i.CustomerLevelName;
          }
        });
      });
      this.saleData = this.packageCardSelectAll;
      this.dialogVisible = false;
    },
    // 获取关联会员等级
    getCustomerData() {
      var that = this;
      var params = {
        Name: "",
        Active: true
      };
      APILEVEL.getCustomerLevel(params).then(res => {
        if (res.StateCode == 200) {
          that.customerLevel = res.Data;
        } else {
          that.$message.error({
            message: res.Message,
            duration: 2000
          });
        }
      });
    },
    changeThis(row, index) {
      this.$set(this.saleData, index, row);
      this.$forceUpdate();
    },
    // 删除
    saleDataDelete(row, ind) {
      this.saleData.splice(ind, 1);
      this.packageCardProduct.forEach((item, index, arr) => {
        if (item.ID == row.ID) {
          arr.splice(index, 1);
        }
      });
      this.packageCardProject.forEach((item, index, arr) => {
        if (item.ID == row.ID) {
          arr.splice(index, 1);
        }
      });
      this.packageCardGeneral.forEach((item, index, arr) => {
        if (item.ID == row.ID) {
          arr.splice(index, 1);
        }
      });
      this.packageCardTime.forEach((item, index, arr) => {
        if (item.ID == row.ID) {
          arr.splice(index, 1);
        }
      });
      this.packageCardSaving.forEach((item, index, arr) => {
        if (item.ID == row.ID) {
          arr.splice(index, 1);
        }
      });
      this.collapseChange();
    },
    // 保存新增
    async createCustomerClick() {
      let that = this;
      if (that.TypeRadio !== "") {
        let Amount = 0;
        let state = false;
        if (this.saleData.length > 0) {
          this.saleData.forEach(item => {
            if (!item.CustomerLevelID) {
              state = true;
            }
          });
        }
        if (state) {
          that.$message.error("请选择关联会员等级！");
          return;
        }
        if (that.TypeRadio == 40) {
          Amount = that.singleSaleInput;
        } else if (that.TypeRadio == 50) {
          Amount = that.CumulativeSaleInput;
        } else if (that.TypeRadio == 60) {
          Amount = that.CumulativeTreatInput;
        }
        let Goods = [];
        this.saleData.forEach(item => {
          let obj = new Object();
          obj.ID = item.ID;
          obj.GoodsType = item.GoodsType;
          obj.CustomerLevelID = item.CustomerLevelID;
          obj.GoodsID = item.GoodsID ? item.GoodsID : item.ID;
          Goods.push(obj);
        });
        let params = {
          Type: that.TypeRadio,
          Amount: Amount,
          GoodsType: that.appointTypeCheck,
          Goods: that.TypeRadio == "80" ? Goods : []
        };
        let res = await API.CreateCustomercondTion(params);
        if (res.StateCode == 200) {
          that.$message.success("保存成功！");
          that.dialogVisible = false;
          that.packageCardProduct = []; //选中适用产品
          that.packageCardProject = []; //选中适用项目
          that.packageCardGeneral = []; //选中适用通用次卡
          that.packageCardTime = []; //选中适用项目
          that.savingCardPackage = []; //选中适用项目
          that.packageCardSaving = [];
          that.productDefaultCheckedKeys = []; //回显
          that.productDefaultExpandedKeys = [1];
          that.projectDefaultCheckedKeys = []; //回显
          that.projectDefaultExpandedKeys = [1];
          that.generalDefaultCheckedKeys = []; //回显
          that.generalDefaultExpandedKeys = [1];
          that.timeDefaultCheckedKeys = []; //回显
          that.timeDefaultExpandedKeys = [1];
          that.packageDefaultCheckedKeys = []; //回显
          that.packageDefaultExpandedKeys = [1];
          that.appointTypeCheck = [];
          that.TypeRadio = "";
          that.singleSaleInput = "";
          that.CumulativeSaleInput = "";
          that.CumulativeTreatInput = "";
          that.saleData = [];
          that.$forceUpdate();
          that.getCustomerConditionAll();
        } else {
          that.$message.error({
            message: res.Message,
            duration: 2000
          });
        }
      } else {
        that.$message.error("请选择门槛设置！");
      }
    },
    getGoodsType(item) {
      if (item.Alias == "产品") {
        return "10";
      } else if (item.Alias == "项目") {
        return "20";
      } else if (item.Alias == "通用次卡") {
        return "30";
      } else if (item.Alias == "时效卡") {
        return "40";
      } else if (item.Alias == "储值卡") {
        return "50";
      } else {
        return "60";
      }
    },
    /**  获取入会门槛信息  */
    async getCustomerConditionAll() {
      var that = this;
      let params = {};
      let res = await API.customerConditionAll(params);
      if (res.StateCode == 200) {
        this.$forceUpdate();
        this.TypeRadio = Number(res.Data.Type);
        this.packageCardSelectAll = Object.assign([], this.packageCardSelectAll);
        this.appointTypeCheck = Object.assign([], this.appointTypeCheck);
        this.saleData = Object.assign([], this.saleData);
        this.appointTypeCheck = res.Data.GoodsType;
        switch (res.Data.Type) {
          case "60":
            that.CumulativeTreatInput = res.Data.Amount;
            break;
          case "50":
            that.CumulativeSaleInput = res.Data.Amount;
            break;
          case "40":
            that.singleSaleInput = res.Data.Amount;
            break;
          case "80":
            {
              this.getSaleDialog(res.Data.Goods);
              this.saleData = res.Data.Goods;
            }
            break;
        }
      } else {
        this.$message.error(res.Message);
      }
    },
    getSaleDialog(Goods) {
      var that = this;
      if (Goods) {
        Goods.forEach(i => {
          if (i.GoodsType == "10") {
            that.productList.forEach(j => {
              j.Child.forEach(t => {
                t.Child &&
                  t.Child.forEach(s => {
                    if (s.ID == i.GoodsID) {
                      let temp = {
                        PID: s.PID,
                        ID: s.ID,
                        Amount: "",
                        OldPrice: s.Price,
                        Price: s.Price,
                        GoodsCategoryName: s.GoodsCategoryName,
                        TotalPrice: "",
                        Alias: "产品",
                        GoodsName: s.Name,
                        GoodsType: 10
                      };

                      this.packageCardSelectAll.push(temp);
                      this.packageCardProduct.push(temp);
                    }
                  });
              });
            });
          }
          if (i.GoodsType == "20") {
            that.projectList.forEach(j => {
              j.Child.forEach(t => {
                t.Child &&
                  t.Child.forEach(s => {
                    if (s.ID == i.GoodsID) {
                      let temp = {
                        PID: s.PID,
                        ID: s.ID,
                        Amount: "",
                        OldPrice: s.Price,
                        Price: s.Price,
                        GoodsCategoryName: s.GoodsCategoryName,
                        TotalPrice: "",
                        Alias: "项目",
                        GoodsName: s.Name,
                        GoodsType: 20
                      };

                      this.packageCardSelectAll.push(temp);
                      this.packageCardProject.push(temp);
                    }
                  });
              });
            });
          }
          if (i.GoodsType == 30) {
            that.generalCardList.forEach(j => {
              j.Child.forEach(t => {
                t.Child &&
                  t.Child.forEach(s => {
                    if (s.ID == i.GoodsID) {
                      let temp = {
                        PID: s.PID,
                        ID: s.ID,
                        Amount: "",
                        OldPrice: s.Price,
                        Price: s.Price,
                        GoodsCategoryName: s.GoodsCategoryName,
                        TotalPrice: "",
                        Alias: "通用次卡",
                        GoodsName: s.Name,
                        GoodsType: 30
                      };

                      this.packageCardSelectAll.push(temp);
                      this.packageCardGeneral.push(temp);
                    }
                  });
              });
            });
          }
          if (i.GoodsType == 40) {
            that.timeCardList.forEach(j => {
              j.Child.forEach(t => {
                if (t.Child) {
                  t.Child.forEach(s => {
                    if (s.ID == i.GoodsID) {
                      let temp = {
                        PID: s.PID,
                        ID: s.ID,
                        Amount: "",
                        OldPrice: s.Price,
                        Price: s.Price,
                        GoodsCategoryName: s.GoodsCategoryName,
                        TotalPrice: "",
                        Alias: "时效卡",
                        GoodsName: s.Name,
                        GoodsType: 40
                      };

                      this.packageCardSelectAll.push(temp);
                      this.packageCardTime.push(temp);
                    }
                  });
                }
              });
            });
          }
          if (i.GoodsType == 50) {
            that.savingCardList.forEach(j => {
              j.Child.forEach(t => {
                if (t.Child) {
                  t.Child.forEach(s => {
                    if (s.ID == i.GoodsID) {
                      let temp = {
                        PID: s.PID,
                        ID: s.ID,
                        Amount: "",
                        OldPrice: s.Price,
                        Price: s.Price,
                        GoodsCategoryName: s.GoodsCategoryName,
                        TotalPrice: "",
                        Alias: "储值卡",
                        GoodsName: s.Name,
                        GoodsType: 50
                      };

                      this.packageCardSelectAll.push(temp);
                      this.packageCardSaving.push(temp);
                    }
                  });
                }
              });
            });
          }

          if (i.GoodsType == 60) {
            that.savingCardList.forEach(j => {
              j.Child.forEach(t => {
                if (t.Child) {
                  t.Child.forEach(s => {
                    if (s.ID == i.GoodsID) {
                      let temp = {
                        PID: s.PID,
                        ID: s.ID,
                        Amount: "",
                        OldPrice: s.Price,
                        Price: s.Price,
                        GoodsCategoryName: s.GoodsCategoryName,
                        TotalPrice: "",
                        Alias: "套餐卡",
                        GoodsName: s.Name,
                        GoodsType: 60
                      };

                      this.packageCardSelectAll.push(temp);
                      this.selectPackageCard.push(temp);
                    }
                  });
                }
              });
            });
          }
        });
      }
    }
  },
  /** 创建实例之前执行函数   */
  beforeCreate() {},
  /**  实例创建完成之后  */
  created() {},
  /**  在挂载开始之前被调用  */
  beforeMount() {},
  /**  实例被挂载后调用  */
  mounted() {
    this.productData();
    this.projectData();
    this.generalCardData();
    this.savingCardData();
    this.timeCardData();
    this.getCustomerData();
    this.savingCardPackageCard();
    this.getCustomerConditionAll();
  },
  /**  数据更新 之前 调用  */
  beforeUpdate() {},
  /** 数据更新 完成 调用   */
  updated() {},
  /**  实例销毁之前调用  */
  beforeDestroy() {},
  /**  实例销毁后调用  */
  destroyed() {}
};
</script>

<style lang="scss">
.customercondition {
  .el-scrollbar_height_range {
    height: 35vh;
    .el-scrollbar__wrap {
      overflow-x: hidden;
    }
  }
  .el-scrollbar_height {
    height: 250px;
    .el-scrollbar__wrap {
      overflow-x: hidden;
    }
  }
}
</style>
