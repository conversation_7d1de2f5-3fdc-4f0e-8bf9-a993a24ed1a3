<template>
  <div class="content_body StorageConfirm">
    <!-- 搜索条件 -->
    <div class="search_header">
      <el-row>
        <el-col :span="22">
          <el-form :inline="true" :model="searchForm" size="small">
            <el-form-item label="单据号">
              <el-input v-model="searchForm.ID" placeholder="请输入单据号" @keyup.enter.native="handleSearchClick"></el-input>
            </el-form-item>
            <el-form-item label="供应商">
              <el-select v-model="searchForm.SupplierID" clearable placeholder="请选择供应商">
                <el-option v-for="item in supplierList" :key="item.ID" :label="item.Name" :value="item.ID"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="制单日期">
              <el-date-picker v-model="searchForm.createTime" unlink-panels type="daterange" range-separator="至" value-format="yyyy-MM-dd" start-placeholder="开始日期" end-placeholder="结束日期" @change="handleSearchClick"></el-date-picker>
            </el-form-item>
            <el-form-item label="单据状态">
              <el-select v-model="searchForm.BillStatus" clearable placeholder="请选择状态">
                <el-option v-for="item in statusList" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleSearchClick" v-prevent-click>搜索</el-button>
            </el-form-item>
          </el-form>
        </el-col>
      </el-row>
    </div>

    <!-- 入库单列表 -->
    <el-table size="small" :data="storageList" tooltip-effect="light" v-loading="loading">
      <el-table-column prop="ID" label="单据号"></el-table-column>
      <el-table-column prop="SupplierName" label="供应商"></el-table-column>
      <el-table-column prop="EntityName" label="仓库/门店"></el-table-column>
      <el-table-column prop="CreatedOn" label="制单时间">
        <template slot-scope="scope">
          {{ scope.row.CreatedOn | dateFormat("YYYY-MM-DD HH:mm") }}
        </template>
      </el-table-column>
      <el-table-column prop="EmployeeName" label="制单人"></el-table-column>
      <el-table-column prop="BillStatusName" label="单据状态">
        <template slot-scope="scope">
          <el-tag :type="getStatusTagType(scope.row.BillStatus)">{{ scope.row.BillStatusName }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="ConfirmedBy" label="确认人">
        <template slot-scope="scope">
          <span v-if="scope.row.ConfirmedBy">{{ scope.row.ConfirmedBy }}</span>
          <span v-else class="text-muted">-</span>
        </template>
      </el-table-column>
      <el-table-column prop="ConfirmedOn" label="确认时间">
        <template slot-scope="scope">
          <span v-if="scope.row.ConfirmedOn">{{ scope.row.ConfirmedOn | dateFormat("YYYY-MM-DD HH:mm") }}</span>
          <span v-else class="text-muted">-</span>
        </template>
      </el-table-column>
      <el-table-column prop="Remark" label="备注信息" show-overflow-tooltip></el-table-column>
      <el-table-column label="操作" width="200">
        <template slot-scope="scope">
          <div class="button-group">
            <el-button
              v-if="scope.row.BillStatus === '10'"
              type="primary"
              size="small"
              @click="confirmStorageClick(scope.row)"
              v-prevent-click>
              确认入库
            </el-button>
            <el-button
              v-if="scope.row.BillStatus === '15'"
              type="warning"
              size="small"
              @click="continueConfirmClick(scope.row)"
              v-prevent-click>
              继续确认
            </el-button>
            <el-button
              type="info"
              size="small"
              @click="viewDetailClick(scope.row)"
              v-prevent-click>
              查看详情
            </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <div class="pad_15 text_right">
      <el-pagination background v-if="paginations.total > 0" @current-change="handleCurrentChange" :current-page.sync="paginations.page" :page-size="paginations.page_size" :layout="paginations.layout" :total="paginations.total"></el-pagination>
    </div>

    <!-- 入库确认对话框 -->
    <el-dialog
      title="入库确认"
      :visible.sync="confirmDialogVisible"
      width="90%"
      :close-on-click-modal="false"
      @close="closeConfirmDialog">

      <!-- 基本信息展示 -->
      <div class="confirm-info-section">
        <div class="tip">基本信息</div>
        <el-form label-width="120px" size="small" class="storageInfoDetailFrom">
          <el-row>
            <el-col :span="8">
              <el-form-item label="单据号：">{{ confirmStorageInfo.ID }}</el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="仓库/门店：">{{ confirmStorageInfo.EntityName }}</el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="制单人：">{{ confirmStorageInfo.EmployeeName }}</el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="供应商：">{{ confirmStorageInfo.SupplierName }}</el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="入库时间：">{{ confirmStorageInfo.InDate | dateFormat("YYYY-MM-DD HH:mm") }}</el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="制单时间：">{{ confirmStorageInfo.CreatedOn | dateFormat("YYYY-MM-DD HH:mm") }}</el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="单据状态：">
                <el-tag :type="getStatusTagType(confirmStorageInfo.BillStatus)">{{ confirmStorageInfo.BillStatusName }}</el-tag>
              </el-form-item>
            </el-col>
            <el-col :span="24" v-if="confirmStorageInfo.Remark">
              <el-form-item label="备注信息：">{{ confirmStorageInfo.Remark }}</el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>

      <!-- 产品明细确认表格 -->
      <div class="confirm-product-section">
        <div class="tip">产品明细确认</div>
        <div class="table-toolbar">
          <el-button size="small" @click="selectAllProducts">全选</el-button>
          <el-button size="small" @click="selectNoneProducts">取消全选</el-button>
          <!-- <el-button size="small" @click="batchSetQuantity" :disabled="selectedConfirmProducts.length === 0">批量设置数量</el-button> -->
          <span class="toolbar-tip">提示：选择要确认的产品，设置本次入库数量</span>
        </div>

        <el-table
          ref="confirmProductTable"
          :data="confirmProductList"
          border
          @selection-change="handleConfirmSelectionChange"
          v-loading="confirmLoading"
          max-height="400">
          <el-table-column type="selection" width="55" />
          <el-table-column prop="ProductName" label="产品名称" min-width="120" />
          <el-table-column prop="Specification" label="规格" width="100" />
          <el-table-column prop="UnitName" label="单位" width="80" />
          <el-table-column prop="UnitPrice" label="单价" width="100">
            <template slot-scope="scope">
              ¥{{ scope.row.UnitPrice }}
            </template>
          </el-table-column>
          <el-table-column prop="Quantity" label="计划数量" width="100" />
          <el-table-column prop="ActualQuantity" label="实际入库数量" width="120">
            <template slot-scope="scope">
              <div>{{ scope.row.ActualQuantity || 0 }} {{ scope.row.UnitName }}</div>
              <div class="color_gray font_12" v-if="scope.row.ActualMinimumUnitQuantity">实际最小单位数量：{{ scope.row.ActualMinimumUnitQuantity || 0 }} {{ scope.row.MinimumUnitName || scope.row.UnitName }}</div>
            </template>
          </el-table-column>
          <el-table-column prop="RemainingQuantity" label="剩余待入库数量" width="140">
            <template slot-scope="scope">
              <div :class="{ 'text-warning': scope.row.RemainingQuantity > 0, 'text-success': scope.row.RemainingQuantity === 0 }">
                {{ scope.row.RemainingQuantity || scope.row.Quantity }} {{ scope.row.UnitName }}
              </div>
              <div class="color_gray font_12" v-if="scope.row.RemainingMinimumUnitQuantity">剩余最小单位数量：{{ scope.row.RemainingMinimumUnitQuantity || 0 }} {{ scope.row.MinimumUnitName || scope.row.UnitName }}</div>
            </template>
          </el-table-column>
          <el-table-column label="本次入库数量" width="150">
            <template slot-scope="scope">
              <el-input
                v-model="scope.row.ThisTimeQuantity"
                size="small"
                placeholder="本次入库数量"
                type="number"
                min="0"
                :max="scope.row.RemainingQuantity || scope.row.Quantity"
                @blur="calculateThisTimeAmount(scope.row)"
                @change="calculateThisTimeAmount(scope.row)"
                :class="{ 'input-error': scope.row.quantityError }"
              />
              <div v-if="scope.row.quantityError" class="error-message">{{ scope.row.quantityError }}</div>
            </template>
          </el-table-column>
          <el-table-column label="本次入库金额" width="120">
            <template slot-scope="scope">
              ¥{{ scope.row.ThisTimeAmount || 0 }}
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 附件上传 -->
      <div class="confirm-attachment-section">
        <div class="tip">附件上传</div>
        <div class="attachment-upload-area">
          <el-upload
            action="#"
            list-type="picture-card"
            :file-list="confirmForm.AttachmentList"
            :before-upload="handleAttachmentUpload"
            :on-remove="removeAttachment"
            multiple
            accept="image/*,.pdf,.doc,.docx,.xls,.xlsx"
            :limit="10"
            :on-exceed="handleAttachmentExceed"
          >
            <!-- 加号图标 -->
            <i slot="default" class="el-icon-plus"></i>
            <div slot="file" slot-scope="{ file }">
              <el-image
                v-if="isImageFile(file)"
                :ref="`previewImg_${file.uid}`"
                fit="cover"
                class="el-upload-list__item-thumbnail"
                :src="file.AttachmentURL || file.url"
                :preview-src-list="getImagePreviewList()"
                style="height: 100%; width: 100%"
              />

              <div class="document_i" v-else>
                <div>
                  <i :class="getFileIcon(file)" style="font-size: 90px; color: #aaaaaa; margin-top: 15px"></i>
                </div>
                <div class="pad_0_10" style="width: 100%; box-sizing: border-box">{{ getFileName(file) }}</div>
              </div>

              <span class="el-upload-list__item-actions">
                <span v-if="isImageFile(file)" class="el-upload-list__item-preview" @click="handlePictureCardPreview(file)">
                  <i class="el-icon-zoom-in"></i>
                </span>
                <span class="el-upload-list__item-delete" @click="removeAttachment(file)">
                  <i class="el-icon-delete"></i>
                </span>
              </span>
            </div>
          </el-upload>
          <div class="upload-tip">
            <p>支持上传图片、PDF、Word、Excel等文件，单个文件不超过10MB，最多上传10个文件</p>
            <p>支持格式：jpg、png、gif、pdf、doc、docx、xls、xlsx</p>
          </div>
        </div>
      </div>

      <!-- 确认备注 -->
      <div class="confirm-remark-section">
        <div class="tip">确认备注</div>
        <el-input
          v-model="confirmForm.Remark"
          type="textarea"
          :rows="3"
          placeholder="请输入确认备注（可选）"
          maxlength="500"
          show-word-limit
        />
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="closeConfirmDialog">取消</el-button>
        <el-button type="primary" @click="submitConfirm" :loading="confirmSubmitLoading">确认入库</el-button>
      </div>
    </el-dialog>

    <!-- 批量设置数量对话框 -->
    <el-dialog title="批量设置数量" :visible.sync="batchDialogVisible" width="400px">
      <el-form :model="batchForm" label-width="120px">
        <el-form-item label="本次入库数量">
          <el-input
            v-model="batchForm.quantity"
            type="number"
            min="0"
            placeholder="请输入数量"
          />
        </el-form-item>
        <el-form-item>
          <el-checkbox v-model="batchForm.useRemaining">使用剩余数量</el-checkbox>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="batchDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmBatchSet">确定</el-button>
      </div>
    </el-dialog>

    <!-- 入库单详情弹框 -->
    <el-dialog title="入库单详情" :visible.sync="detailDialogVisible" width="1000px">
      <div class="tip">入库信息</div>
      <el-form label-width="120px" size="small" class="storageInfoDetailFrom">
        <el-row>
          <el-col :span="8">
            <el-form-item label="单据号：">{{ detailStorageInfo.ID }}</el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="仓库/门店：">{{ detailStorageInfo.EntityName }}</el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="操作人：">{{ detailStorageInfo.EmployeeName }}</el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="供应商：">{{ detailStorageInfo.SupplierName }}</el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="入库时间：">{{ detailStorageInfo.InDate | dateFormat("YYYY-MM-DD HH:mm") }}</el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="制单时间：">{{ detailStorageInfo.CreatedOn | dateFormat("YYYY-MM-DD HH:mm") }}</el-form-item>
          </el-col>
          <el-col :span="8" v-if="detailStorageInfo.BillStatusName">
            <el-form-item label="单据状态：">
              <el-tag :type="getStatusTagType(detailStorageInfo.BillStatus)">{{ detailStorageInfo.BillStatusName }}</el-tag>
            </el-form-item>
          </el-col>
          <el-col :span="8" v-if="detailStorageInfo.ConfirmedByName">
            <el-form-item label="确认人：">{{ detailStorageInfo.ConfirmedByName }}</el-form-item>
          </el-col>
          <el-col :span="8" v-if="detailStorageInfo.ConfirmedOn">
            <el-form-item label="确认时间：">{{ detailStorageInfo.ConfirmedOn | dateFormat("YYYY-MM-DD HH:mm") }}</el-form-item>
          </el-col>
          <el-col :span="24" v-if="detailStorageInfo.Remark">
            <el-form-item label="备注信息：">{{ detailStorageInfo.Remark }}</el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div class="tip">产品明细</div>
      <el-table size="small" class="martp_10" max-height="300px" :data="detailStorageInfo.Detail" v-loading="detailLoading">
        <el-table-column label="产品" prop="ProductName">
          <template slot-scope="scope">
            <div>
              {{ scope.row.ProductName }}<span v-if="scope.row.Alias" class="color_gray font_12">({{ scope.row.Alias }})</span>
            </div>
            <div v-if="scope.row.Specification" class="color_gray font_12">规格：{{ scope.row.Specification }}</div>
          </template>
        </el-table-column>
        <el-table-column label="产品分类" prop="PCategoryName"></el-table-column>
        <el-table-column label="计划数量" prop="Quantity">
          <template slot-scope="scope">
            <div>{{ scope.row.Quantity }} {{ scope.row.UnitName }}</div>
            <div class="color_gray font_12" v-if="scope.row.MinimumUnitQuantity">最小包装数量：{{ scope.row.MinimumUnitQuantity }} {{ scope.row.MinimumUnitName || scope.row.UnitName }}</div>
          </template>
        </el-table-column>
        <el-table-column label="实际入库数量" prop="ActualQuantity">
          <template slot-scope="scope">
            <div>{{ scope.row.ActualQuantity || 0 }} {{ scope.row.UnitName }}</div>
            <div class="color_gray font_12" v-if="scope.row.ActualMinimumUnitQuantity">实际最小单位数量：{{ scope.row.ActualMinimumUnitQuantity || 0 }} {{ scope.row.MinimumUnitName || scope.row.UnitName }}</div>
          </template>
        </el-table-column>
        <el-table-column label="剩余待入库数量" prop="RemainingQuantity">
          <template slot-scope="scope">
            <div :class="{ 'text-success': scope.row.RemainingQuantity === 0, 'text-warning': scope.row.RemainingQuantity > 0 }">
              {{ scope.row.RemainingQuantity || scope.row.Quantity || 0 }} {{ scope.row.UnitName }}
            </div>
            <div class="color_gray font_12" v-if="scope.row.RemainingMinimumUnitQuantity || scope.row.MinimumUnitQuantity">剩余最小单位数量：{{ scope.row.RemainingMinimumUnitQuantity || scope.row.MinimumUnitQuantity || 0 }} {{ scope.row.MinimumUnitName || scope.row.UnitName }}</div>
          </template>
        </el-table-column>
        <el-table-column label="单价(元)">
          <template slot-scope="scope">{{ scope.row.UnitPrice | toFixed | NumFormat }}</template>
        </el-table-column>
        <el-table-column label="计划金额(元)">
          <template slot-scope="scope">{{ scope.row.Amount | toFixed | NumFormat }}</template>
        </el-table-column>
        <el-table-column label="实际金额(元)" v-if="detailStorageInfo.BillStatus !== '10'">
          <template slot-scope="scope">{{ (scope.row.ActualAmount || 0) | toFixed | NumFormat }}</template>
        </el-table-column>
        <el-table-column label="完成状态">
          <template slot-scope="scope">
            <el-tag :type="scope.row.IsCompleted ? 'success' : 'warning'">
              {{ scope.row.IsCompleted ? '已完成' : '未完成' }}
            </el-tag>
          </template>
        </el-table-column>
      </el-table>

      <!-- 附件信息 -->
      <div v-if="detailStorageInfo.AttachmentList && detailStorageInfo.AttachmentList.length > 0" class="attachment-section martp_20">
        <div class="section-title">
          <i class="el-icon-paperclip"></i>
          <span>相关附件 ({{ detailStorageInfo.AttachmentList.length }})</span>
        </div>

        <div class="attachment-list-display">
          <div
            v-for="attachment in detailStorageInfo.AttachmentList"
            :key="attachment.ID"
            class="attachment-item-display"
          >
            <!-- 图片附件 -->
            <div v-if="attachment.AttachmentType === 10" class="attachment-image">
              <el-image
                :src="attachment.AttachmentURL"
                fit="cover"
                class="attachment-thumbnail-display"
                :preview-src-list="getDetailImagePreviewList()"
              >
                <div slot="error" class="image-error">
                  <i class="el-icon-picture-outline"></i>
                  <div>图片加载失败</div>
                </div>
              </el-image>
              <div class="attachment-info">
                <div class="attachment-name" :title="attachment.AttachmentName">
                  {{ attachment.AttachmentName }}
                </div>
                <div class="attachment-meta">
                  <span class="attachment-size">{{ attachment.FileSizeFormatted || formatFileSize(attachment.FileSize) }}</span>
                  <span class="attachment-time">{{ attachment.CreatedOn | dateFormat('MM-DD HH:mm') }}</span>
                </div>
                <div v-if="attachment.Remark" class="attachment-remark">
                  {{ attachment.Remark }}
                </div>
              </div>
            </div>

            <!-- 文档附件 -->
            <div v-else class="attachment-document">
              <div class="document-icon">
                <i :class="getDetailFileIcon(attachment)" class="file-icon-large"></i>
              </div>
              <div class="attachment-info">
                <div class="attachment-name" :title="attachment.AttachmentName">
                  {{ attachment.AttachmentName }}
                </div>
                <div class="attachment-meta">
                  <span class="attachment-type">{{ attachment.AttachmentTypeName || getAttachmentTypeName(attachment.AttachmentType) }}</span>
                  <span class="attachment-size">{{ attachment.FileSizeFormatted || formatFileSize(attachment.FileSize) }}</span>
                  <span class="attachment-time">{{ attachment.CreatedOn | dateFormat('MM-DD HH:mm') }}</span>
                </div>
                <div v-if="attachment.Remark" class="attachment-remark">
                  {{ attachment.Remark }}
                </div>
                <div class="attachment-actions">
                  <el-button
                    type="text"
                    size="mini"
                    @click="previewDetailAttachment(attachment)"
                    icon="el-icon-view"
                  >
                    预览
                  </el-button>
                  <el-button
                    type="text"
                    size="mini"
                    @click="downloadAttachment(attachment)"
                    icon="el-icon-download"
                  >
                    下载
                  </el-button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 无附件提示 -->
      <div v-else class="no-attachment-tip martp_20">
        <i class="el-icon-document"></i>
        <span>暂无相关附件</span>
      </div>

      <div slot="footer">
        <el-button @click="detailDialogVisible = false" size="small" v-prevent-click>关闭</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import APIStorage from "@/api/PSI/Purchase/storage";

export default {
  name: 'StorageConfirm',
  data() {
    return {
      loading: false,
      searchForm: {
        ID: "", // 单据号
        SupplierID: "", // 供应商id
        createTime: "", // 时间
        BillStatus: "", // 单据状态
      },
      storageList: [], // 入库单列表
      supplierList: [], // 供应商列表
      statusList: [
        { value: '10', label: '待确认' },
        { value: '15', label: '部分确认' },
        { value: '20', label: '已确认' }
      ],
      paginations: {
        page: 1, // 当前位于哪页
        total: 0, // 总数
        page_size: 10, // 1页显示多少条
        layout: "total, prev, pager, next, jumper", // 翻页属性
      },
      // 确认对话框相关数据
      confirmDialogVisible: false,
      confirmLoading: false,
      confirmSubmitLoading: false,
      confirmStorageInfo: {},
      confirmProductList: [],
      selectedConfirmProducts: [],
      confirmForm: {
        PurchaseStorageID: '',
        Remark: '',
        ProductList: [],
        AttachmentList: []
      },
      // 附件上传相关
      uploadLoading: false,
      // 批量设置对话框
      batchDialogVisible: false,
      batchForm: {
        quantity: 0,
        useRemaining: false
      },
      // 详情弹框
      detailDialogVisible: false,
      detailLoading: false,
      detailStorageInfo: {}
    }
  },
  methods: {
    // 搜索
    handleSearchClick() {
      this.paginations.page = 1;
      this.getStorageListNetwork();
    },

    // 确认入库
    confirmStorageClick(row) {
      // 跳转到确认详情页面进行分批确认
      this.openConfirmDialog(row);
    },

    // 继续确认
    continueConfirmClick(row) {
      // 跳转到确认详情页面继续确认
      this.openConfirmDialog(row);
    },

    // 查看详情
    viewDetailClick(row) {
      // 使用现有的详情页面，通过弹窗方式显示
      this.showStorageDetail(row);
    },

    // 显示入库单详情
    async showStorageDetail(row) {
      this.detailDialogVisible = true;
      this.detailLoading = true;

      try {
        const response = await APIStorage.fullInfo({ ID: row.ID });
        if (response.StateCode === 200) {
          this.detailStorageInfo = response.Data;

          // 确保状态信息从列表行数据传递过来
          if (!this.detailStorageInfo.BillStatus && row.BillStatus) {
            this.detailStorageInfo.BillStatus = row.BillStatus;
            this.detailStorageInfo.BillStatusName = row.BillStatusName;
          }

          // 确保确认人和确认时间信息从列表传递过来
          if (!this.detailStorageInfo.ConfirmedByName && row.ConfirmedBy) {
            this.detailStorageInfo.ConfirmedByName = row.ConfirmedBy;
          }
          if (!this.detailStorageInfo.ConfirmedOn && row.ConfirmedOn) {
            this.detailStorageInfo.ConfirmedOn = row.ConfirmedOn;
          }
        } else {
          this.$message.error({
            message: response.Message || '获取详情失败',
            duration: 2000,
          });
          this.detailDialogVisible = false;
        }
      } catch (error) {
        console.error('获取入库单详情失败:', error);
        this.$message.error({
          message: "获取详情失败，请稍后重试",
          duration: 2000,
        });
        this.detailDialogVisible = false;
      } finally {
        this.detailLoading = false;
      }
    },

    // 打开确认对话框
    async openConfirmDialog(row) {
      this.confirmDialogVisible = true;
      this.confirmLoading = true;

      try {
        // 获取完整入库单详情（包含附件信息）
        const response = await APIStorage.fullInfo({ ID: row.ID });
        if (response.StateCode === 200) {
          this.confirmStorageInfo = response.Data;

          // 确保状态信息从列表行数据传递过来
          if (!this.confirmStorageInfo.BillStatus && row.BillStatus) {
            this.confirmStorageInfo.BillStatus = row.BillStatus;
            this.confirmStorageInfo.BillStatusName = row.BillStatusName;
          }

          // 回填已有的附件信息到确认表单
          if (response.Data.AttachmentList && response.Data.AttachmentList.length > 0) {
            this.confirmForm.AttachmentList = response.Data.AttachmentList.map(attachment => ({
              uid: attachment.ID || Date.now() + Math.random(),
              AttachmentName: attachment.AttachmentName,
              AttachmentURL: attachment.AttachmentURL,
              AttachmentType: attachment.AttachmentType,
              MimeType: attachment.MimeType,
              FileSize: attachment.FileSize,
              Remark: attachment.Remark || '',
              // 用于显示的属性
              name: attachment.AttachmentName,
              url: attachment.AttachmentURL,
              status: 'success',
              // 标记为已存在的附件（区别于新上传的）
              isExisting: true,
              originalId: attachment.ID
            }));
          } else {
            this.confirmForm.AttachmentList = [];
          }

          // 只处理未完成的产品明细（待确认的）
          const allDetails = response.Data.Detail || [];
          const pendingDetails = allDetails.filter(item => !item.IsCompleted);

          this.confirmProductList = pendingDetails.map(item => {
            // 确保数量字段都是数字类型
            const quantity = parseFloat(item.Quantity) || 0;
            const actualQuantity = parseFloat(item.ActualQuantity) || 0;

            // 计算剩余数量：如果API返回了RemainingQuantity就用它，否则用 计划数量 - 已入库数量
            let remainingQuantity;
            if (item.RemainingQuantity !== undefined && item.RemainingQuantity !== null) {
              remainingQuantity = parseFloat(item.RemainingQuantity) || 0;
            } else {
              remainingQuantity = quantity - actualQuantity;
            }

            const processedItem = {
              ...item,
              ThisTimeQuantity: 0,
              ThisTimeMinimumUnitQuantity: 0,
              ThisTimeAmount: 0,
              IsSelected: false,
              quantityError: '',
              Quantity: quantity,
              RemainingQuantity: remainingQuantity,
              ActualQuantity: actualQuantity,
              UnitPrice: parseFloat(item.UnitPrice) || 0,
              ConversionRate: parseFloat(item.ConversionRate) || 1,
              UnitAmount: parseFloat(item.UnitAmount) || 1
            };

            // 调试信息
            console.log('产品数据初始化:', {
              productName: item.ProductName,
              currentUnit: item.UnitName, // 当前单位名称
              minimumUnit: item.MinimumUnitName, // 最小单位名称
              UnitAmount: item.UnitAmount, // 当前单位对应的最小包装单位数量
              example: `如果本次入库1${item.UnitName || '单位'}，则对应${item.UnitAmount || 1}${item.MinimumUnitName || '最小单位'}`,
              // 检查所有相关字段
              allItemFields: Object.keys(item)
            });

            return processedItem;
          });
          this.confirmForm.PurchaseStorageID = response.Data.ID;
          this.confirmForm.Remark = '';
        } else {
          this.$message.error({
            message: response.Message || '获取入库单详情失败',
            duration: 2000,
          });
          this.confirmDialogVisible = false;
        }
      } catch (error) {
        console.error('获取入库单详情失败:', error);
        this.$message.error({
          message: "获取详情失败，请稍后重试",
          duration: 2000,
        });
        this.confirmDialogVisible = false;
      } finally {
        this.confirmLoading = false;
      }
    },

    // 关闭确认对话框
    closeConfirmDialog() {
      this.confirmDialogVisible = false;
      this.confirmStorageInfo = {};
      this.confirmProductList = [];
      this.selectedConfirmProducts = [];
      this.confirmForm = {
        PurchaseStorageID: '',
        Remark: '',
        ProductList: [],
        AttachmentList: []
      };
    },

    // 处理产品选择变化
    handleConfirmSelectionChange(selection) {
      this.selectedConfirmProducts = selection;
      // 更新选中状态
      this.confirmProductList.forEach(item => {
        item.IsSelected = selection.some(selected => selected.ID === item.ID);
      });
    },

    // 全选产品
    selectAllProducts() {
      this.$refs.confirmProductTable && this.$refs.confirmProductTable.toggleAllSelection();
    },

    // 取消全选
    selectNoneProducts() {
      this.$refs.confirmProductTable && this.$refs.confirmProductTable.clearSelection();
    },

    // 批量设置数量
    batchSetQuantity() {
      if (this.selectedConfirmProducts.length === 0) {
        this.$message.error({
          message: "请先选择要设置的产品",
          duration: 2000,
        });
        return;
      }
      this.batchForm.quantity = 0;
      this.batchForm.useRemaining = false;
      this.batchDialogVisible = true;
    },

    // 确认批量设置
    confirmBatchSet() {
      const quantity = this.batchForm.useRemaining ? null : parseFloat(this.batchForm.quantity) || 0;

      this.selectedConfirmProducts.forEach(product => {
        const targetQuantity = this.batchForm.useRemaining ? product.RemainingQuantity : quantity;
        if (targetQuantity >= 0 && targetQuantity <= product.RemainingQuantity) {
          product.ThisTimeQuantity = targetQuantity;
          this.calculateThisTimeAmount(product);
        }
      });

      this.batchDialogVisible = false;
      this.$message.success({
        message: "批量设置完成",
        duration: 2000,
      });
    },

    // 计算本次入库金额
    calculateThisTimeAmount(row) {
      // 清除之前的错误信息
      row.quantityError = '';

      // 处理空值情况
      if (row.ThisTimeQuantity === '' || row.ThisTimeQuantity === null || row.ThisTimeQuantity === undefined) {
        row.ThisTimeQuantity = 0;
        row.ThisTimeAmount = 0;
        row.ThisTimeMinimumUnitQuantity = 0;
        return;
      }

      // 转换为数字
      const quantity = parseFloat(row.ThisTimeQuantity);

      // 检查是否为有效数字
      if (isNaN(quantity)) {
        row.quantityError = '请输入有效数字';
        return;
      }

      // 更新为标准化的数字
      row.ThisTimeQuantity = quantity;

      // 验证数量
      if (quantity < 0) {
        row.quantityError = '数量不能小于0';
        return;
      }

      // 确保剩余数量是数字类型
      const remainingQuantity = parseFloat(row.RemainingQuantity) || 0;

      // 调试信息
      console.log('数量验证:', {
        产品名称: row.ProductName,
        输入数量: quantity,
        剩余数量: remainingQuantity,
        是否超出: quantity > remainingQuantity
      });

      if (quantity > remainingQuantity) {
        row.quantityError = `不能超过剩余数量(${remainingQuantity})`;
        return;
      }

      // 计算金额和最小单位数量
      const unitPrice = parseFloat(row.UnitPrice) || 0;
      // 使用UnitAmount字段（当前单位对应的最小包装单位数量）
      const unitAmount = parseFloat(row.UnitAmount) || 1;

      // 调试信息
      console.log('计算ThisTimeMinimumUnitQuantity:', {
        产品名称: row.ProductName,
        当前单位: row.UnitName,
        最小单位: row.MinimumUnitName,
        本次入库数量: quantity,
        当前单位对应最小单位数量: row.UnitAmount,
        计算公式: `${quantity}${row.UnitName || '单位'} × ${unitAmount} = ${quantity * unitAmount}${row.MinimumUnitName || '最小单位'}`,
        计算结果: quantity * unitAmount
      });

      row.ThisTimeAmount = (quantity * unitPrice).toFixed(2);
      row.ThisTimeMinimumUnitQuantity = quantity * unitAmount;
    },

    // ========== 附件上传相关方法 ==========

    // 处理附件上传
    async handleAttachmentUpload(file) {
      // 检查文件类型
      const allowedTypes = [
        'image/jpeg', 'image/png', 'image/gif',
        'application/pdf',
        'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      ];

      if (!allowedTypes.includes(file.type)) {
        this.$message.error('不支持的文件格式，请上传图片、PDF、Word或Excel文件');
        return false;
      }

      // 检查文件大小（10MB）
      const isLt10M = file.size / 1024 / 1024 < 10;
      if (!isLt10M) {
        this.$message.error('文件大小不能超过10MB');
        return false;
      }

      // 检查文件数量限制
      if (this.confirmForm.AttachmentList.length >= 10) {
        this.$message.error('最多只能上传10个文件');
        return false;
      }

      try {
        // 将文件转换为base64
        const base64 = await this.fileToBase64(file);

        // 确定附件类型
        let attachmentType = 30; // 默认其他类型
        if (file.type.startsWith('image/')) {
          attachmentType = 10; // 图片
        } else if (file.type === 'application/pdf' ||
                   file.type.includes('word') ||
                   file.type.includes('excel') ||
                   file.type.includes('sheet')) {
          attachmentType = 20; // 文档
        }

        // 生成唯一ID
        const uid = Date.now() + Math.random();

        // 添加到附件列表
        const attachment = {
          uid: uid,
          AttachmentName: file.name,
          AttachmentURL: base64,
          AttachmentType: attachmentType,
          MimeType: file.type,
          FileSize: file.size,
          Remark: '',
          // 用于显示的属性
          name: file.name,
          url: base64,
          status: 'success'
        };

        this.confirmForm.AttachmentList.push(attachment);

        this.$message.success('文件上传成功');

      } catch (error) {
        console.error('文件上传失败:', error);
        this.$message.error('文件上传失败，请重试');
      }

      // 阻止默认上传行为
      return false;
    },

    // 将文件转换为base64
    fileToBase64(file) {
      return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.readAsDataURL(file);
        reader.onload = () => resolve(reader.result);
        reader.onerror = error => reject(error);
      });
    },

    // 移除附件
    async removeAttachment(file) {
      const index = this.confirmForm.AttachmentList.findIndex(item =>
        item.uid === file.uid || item.AttachmentName === file.name
      );

      if (index > -1) {
        const attachment = this.confirmForm.AttachmentList[index];

        // 如果是已存在的附件，调用删除接口
        if (attachment.isExisting && attachment.originalId) {
          this.$confirm('确定要删除这个附件吗？删除后无法恢复。', '确认删除', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(async () => {
            try {
              // 调用删除附件接口
              const response = await APIStorage.deleteAttachment({
                ID: attachment.originalId
              });

              if (response.StateCode === 200) {
                // 删除成功，从列表中移除
                this.confirmForm.AttachmentList.splice(index, 1);
                this.$message.success('附件删除成功');
              } else {
                this.$message.error(response.Message || '附件删除失败');
              }
            } catch (error) {
              console.error('删除附件失败:', error);
              this.$message.error('附件删除失败，请稍后重试');
            }
          }).catch(() => {
            // 用户取消删除
          });
        } else {
          // 新上传的附件，直接从列表中删除
          this.confirmForm.AttachmentList.splice(index, 1);
          this.$message.success('附件已删除');
        }
      }
    },

    // 预览附件
    previewAttachment(file) {
      if (this.isImageFile(file)) {
        // 图片预览由el-image组件自动处理
        return;
      } else {
        // 非图片文件，提示下载或在新窗口打开
        if (file.AttachmentURL && file.AttachmentURL.startsWith('http')) {
          window.open(file.AttachmentURL, '_blank');
        } else {
          this.$message.info('该文件类型不支持预览，请下载后查看');
        }
      }
    },

    // 预览图片文件
    handlePictureCardPreview(file) {
      // 使用ref直接调用el-image的clickHandler方法
      this.$nextTick(() => {
        const refName = `previewImg_${file.uid}`;
        const imageRef = this.$refs[refName];
        if (imageRef && imageRef.length > 0) {
          imageRef[0].clickHandler();
        } else if (imageRef) {
          imageRef.clickHandler();
        }
      });
    },

    // 判断是否为图片文件
    isImageFile(file) {
      if (file.MimeType) {
        return file.MimeType.startsWith('image/');
      }
      if (file.type) {
        return file.type.startsWith('image/');
      }
      // 根据文件名判断
      const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'];
      const fileName = file.AttachmentName || file.name || '';
      return imageExtensions.some(ext => fileName.toLowerCase().endsWith(ext));
    },

    // 获取文件图标
    getFileIcon(file) {
      const mimeType = file.MimeType || file.type || '';

      if (mimeType.includes('pdf')) {
        return 'el-icon-document';
      } else if (mimeType.includes('word')) {
        return 'el-icon-document';
      } else if (mimeType.includes('excel') || mimeType.includes('sheet')) {
        return 'el-icon-s-grid';
      } else {
        return 'el-icon-document';
      }
    },

    // 获取文件名（截取显示）
    getFileName(file) {
      const name = file.AttachmentName || file.name || '未知文件';
      return name.length > 15 ? name.substring(0, 12) + '...' : name;
    },

    // 获取图片预览列表
    getImagePreviewList() {
      return this.confirmForm.AttachmentList
        .filter(file => this.isImageFile(file))
        .map(file => file.AttachmentURL || file.url);
    },



    // ========== 详情附件展示相关方法 ==========

    // 获取详情页面图片预览列表
    getDetailImagePreviewList() {
      if (!this.detailStorageInfo.AttachmentList) return [];
      return this.detailStorageInfo.AttachmentList
        .filter(attachment => attachment.AttachmentType === 10)
        .map(attachment => attachment.AttachmentURL);
    },

    // 获取详情页面文件图标
    getDetailFileIcon(attachment) {
      const mimeType = attachment.MimeType || '';

      if (mimeType.includes('pdf')) {
        return 'el-icon-document';
      } else if (mimeType.includes('word')) {
        return 'el-icon-document';
      } else if (mimeType.includes('excel') || mimeType.includes('sheet')) {
        return 'el-icon-s-grid';
      } else {
        return 'el-icon-document';
      }
    },

    // 获取附件类型名称
    getAttachmentTypeName(type) {
      switch (type) {
        case 10: return '图片';
        case 20: return '文档';
        case 30: return '其他';
        default: return '未知';
      }
    },

    // 格式化文件大小
    formatFileSize(bytes) {
      if (!bytes) return '0 B';
      const k = 1024;
      const sizes = ['B', 'KB', 'MB', 'GB'];
      const i = Math.floor(Math.log(bytes) / Math.log(k));
      return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
    },

    // 预览详情附件
    previewDetailAttachment(attachment) {
      if (attachment.AttachmentType === 10) {
        // 图片预览由el-image组件自动处理
        return;
      } else {
        // 非图片文件，在新窗口打开
        if (attachment.AttachmentURL) {
          window.open(attachment.AttachmentURL, '_blank');
        } else {
          this.$message.info('该文件暂无预览地址');
        }
      }
    },

    // 下载附件
    downloadAttachment(attachment) {
      if (attachment.AttachmentURL) {
        // 创建一个临时的a标签来触发下载
        const link = document.createElement('a');
        link.href = attachment.AttachmentURL;
        link.download = attachment.AttachmentName;
        link.target = '_blank';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      } else {
        this.$message.error('附件下载地址不存在');
      }
    },

    // 处理文件数量超出限制
    handleAttachmentExceed() {
      this.$message.warning('最多只能上传10个文件');
    },

    // 提交确认
    async submitConfirm() {
      // 验证是否有选中的产品
      if (this.selectedConfirmProducts.length === 0) {
        this.$message.error({
          message: "请至少选择一个产品进行确认",
          duration: 2000,
        });
        return;
      }

      // 验证选中产品的本次入库数量
      let hasError = false;
      let hasValidQuantity = false;

      for (let product of this.selectedConfirmProducts) {
        // 确保数据类型正确
        const thisTimeQuantity = parseFloat(product.ThisTimeQuantity) || 0;
        const remainingQuantity = parseFloat(product.RemainingQuantity) || parseFloat(product.Quantity) || 0;

        if (thisTimeQuantity > 0) {
          hasValidQuantity = true;
        }

        if (thisTimeQuantity < 0) {
          this.$message.error({
            message: `产品 ${product.ProductName} 的本次入库数量不能小于0`,
            duration: 2000,
          });
          hasError = true;
          break;
        }

        if (thisTimeQuantity > remainingQuantity) {
          this.$message.error({
            message: `产品 ${product.ProductName} 的本次入库数量不能超过剩余数量(${remainingQuantity})`,
            duration: 2000,
          });
          hasError = true;
          break;
        }
      }

      if (hasError) return;

      if (!hasValidQuantity) {
        this.$message.error({
          message: "请为选中的产品设置大于0的入库数量",
          duration: 2000,
        });
        return;
      }

      // 构建确认数据
      this.confirmForm.ProductList = this.confirmProductList.map(item => ({
        ID: item.ID,
        IsSelected: item.IsSelected,
        ThisTimeQuantity: item.ThisTimeQuantity || 0,
        ThisTimeMinimumUnitQuantity: item.ThisTimeMinimumUnitQuantity || 0,
        ThisTimeAmount: parseFloat(item.ThisTimeAmount || 0)
      }));

      // 构建附件数据（按照接口文档格式）
      // 只包含新上传的附件，已存在的附件删除是立即生效的
      const attachmentList = this.confirmForm.AttachmentList
        .filter(item => !item.isExisting) // 只包含新上传的附件
        .map(item => ({
          AttachmentName: item.AttachmentName,
          AttachmentURL: item.AttachmentURL,
          AttachmentType: item.AttachmentType,
          MimeType: item.MimeType,
          FileSize: item.FileSize,
          Remark: item.Remark || ''
        }));

      // 构建最终提交数据
      const submitData = {
        PurchaseStorageID: this.confirmForm.PurchaseStorageID,
        ProductList: this.confirmForm.ProductList.filter(item => item.IsSelected && item.ThisTimeQuantity > 0),
        AttachmentList: attachmentList, // 只包含新上传的附件
        Remark: this.confirmForm.Remark
      };

      this.confirmSubmitLoading = true;
      try {
        // 检查是否有新的确认接口
        if (typeof APIStorage.confirm === 'function') {
          console.log('提交确认数据:', submitData); // 调试信息
          const response = await APIStorage.confirm(submitData);
          if (response.StateCode === 200) {
            this.$message.success({
              message: "入库确认成功",
              duration: 2000,
            });
            this.closeConfirmDialog();
            this.getStorageListNetwork(); // 刷新列表
          } else {
            this.$message.error({
              message: response.Message || '入库确认失败',
              duration: 2000,
            });
          }
        } else {
          // 如果新接口不存在，显示提示信息
          this.$message.error({
            message: "入库确认功能正在开发中，请联系管理员配置后端接口",
            duration: 2000,
          });
          console.log('确认数据:', submitData); // 用于调试
        }
      } catch (error) {
        console.error('入库确认失败:', error);
        this.$message.error({
          message: "入库确认失败，请稍后重试",
          duration: 2000,
        });
      } finally {
        this.confirmSubmitLoading = false;
      }
    },

    // 获取状态标签类型
    getStatusTagType(status) {
      const statusMap = {
        '10': 'warning',  // 待确认 - 橙色
        '15': 'primary',  // 部分确认 - 蓝色
        '20': 'success'   // 已确认 - 绿色
      };
      return statusMap[status] || 'info';
    },

    // 修改页码
    handleCurrentChange(page) {
      var that = this;
      that.paginations.page = page;
      that.getStorageListNetwork();
    },

    // 获取入库单列表
    getStorageListNetwork() {
      var that = this;
      that.loading = true;
      var params = {
        PageNum: that.paginations.page,
        ID: that.searchForm.ID,
        SupplierID: that.searchForm.SupplierID,
        BillStatus: that.searchForm.BillStatus,
        StartDate: that.searchForm.createTime == null ? "" : that.searchForm.createTime[0],
        EndDate: that.searchForm.createTime == null ? "" : that.searchForm.createTime[1],
      };

      APIStorage.getpurchaseStorageList(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.storageList = res.List;
            that.paginations.page_size = res.PageSize;
            that.paginations.total = res.Total;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .catch((error) => {
          console.error('获取入库单列表失败:', error);
          that.$message.error({
            message: "获取入库单列表失败，请稍后重试",
            duration: 2000,
          });
        })
        .finally(function () {
          that.loading = false;
        });
    },

    // 获取供应商列表
    getSupplierListNetwork() {
      APIStorage.getpurchaseStorageSupplier({})
        .then((res) => {
          if (res.StateCode == 200) {
            this.supplierList = res.Data;
          }
        })
        .catch((error) => {
          console.error('获取供应商列表失败:', error);
        });
    },
  },

  mounted() {
    this.getStorageListNetwork();
    this.getSupplierListNetwork();
  }
}
</script>

<style lang="scss">
.StorageConfirm {
  .color_gray {
    color: #909399;
  }

  .font_12 {
    font-size: 12px;
  }

  .button-group {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;

    .el-button {
      margin-left: 0;
      margin-right: 0;
      margin-bottom: 4px;
    }
  }

  .el-table .button-group {
    justify-content: flex-start;
  }
}

  // 确认对话框样式
  .confirm-info-section, .confirm-product-section, .confirm-attachment-section, .confirm-remark-section {
    margin-bottom: 20px;
  }

  // 附件上传样式
  .confirm-attachment-section {
    .attachment-upload-area {
      .document_i {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 100%;
        text-align: center;
      }

      .pad_0_10 {
        padding: 0 10px;
        font-size: 12px;
        color: #606266;
        word-break: break-all;
        line-height: 1.2;
      }

      .upload-tip {
        margin-top: 10px;
        color: #909399;
        font-size: 12px;
        line-height: 1.5;

        p {
          margin: 0;
        }
      }
    }
  }



  .table-toolbar {
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    gap: 10px;

    .toolbar-tip {
      color: #909399;
      font-size: 12px;
      margin-left: auto;
    }
  }

  .input-error {
    .el-input-number__decrease,
    .el-input-number__increase {
      border-color: #F56C6C;
    }

    .el-input__inner {
      border-color: #F56C6C;
    }
  }

  .error-message {
    color: #F56C6C;
    font-size: 12px;
    margin-top: 2px;
    line-height: 1;
  }

  .text-success {
    color: #67C23A;
    font-weight: 600;
  }

  .text-warning {
    color: #E6A23C;
    font-weight: 600;
  }

  .dialog-footer {
    text-align: right;
  }

  // 详情弹框样式
  .tip {
    color: #303133;
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 15px;
  }

  .storageInfoDetailFrom {
    .el-form-item {
      margin-bottom: 10px;
    }

    .el-form-item__label {
      font-weight: 600;
      color: #606266;
    }
  }

  .martp_10 {
    margin-top: 10px;
  }

  .color_gray {
    color: #909399;
  }

  .font_12 {
    font-size: 12px;
  }

  .text-muted {
    color: #999;
    font-style: italic;
  }

  .martp_20 {
    margin-top: 20px;
  }

  // 详情附件展示样式
  .attachment-section {
    border-top: 1px solid #ebeef5;
    padding-top: 15px;

    .section-title {
      display: flex;
      align-items: center;
      margin-bottom: 15px;
      font-weight: 500;
      color: #303133;

      i {
        margin-right: 8px;
        color: #409eff;
      }
    }

    .attachment-list-display {
      display: flex;
      flex-wrap: wrap;
      gap: 15px;

      .attachment-item-display {
        border: 1px solid #ebeef5;
        border-radius: 6px;
        overflow: hidden;
        background: #fff;
        transition: all 0.3s;

        &:hover {
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        // 图片附件样式
        .attachment-image {
          width: 200px;

          .attachment-thumbnail-display {
            width: 100%;
            height: 120px;
            object-fit: cover;

            .image-error {
              display: flex;
              flex-direction: column;
              align-items: center;
              justify-content: center;
              height: 120px;
              background: #f5f7fa;
              color: #909399;

              i {
                font-size: 24px;
                margin-bottom: 5px;
              }

              div {
                font-size: 12px;
              }
            }
          }

          .attachment-info {
            padding: 10px;
          }
        }

        // 文档附件样式
        .attachment-document {
          width: 250px;
          display: flex;
          padding: 15px;

          .document-icon {
            margin-right: 15px;
            display: flex;
            align-items: center;

            .file-icon-large {
              font-size: 32px;
              color: #409eff;
            }
          }

          .attachment-info {
            flex: 1;
            min-width: 0;
          }
        }

        .attachment-info {
          .attachment-name {
            font-weight: 500;
            color: #303133;
            margin-bottom: 5px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }

          .attachment-meta {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-bottom: 5px;

            span {
              font-size: 12px;
              color: #909399;
              background: #f5f7fa;
              padding: 2px 6px;
              border-radius: 3px;
            }
          }

          .attachment-remark {
            font-size: 12px;
            color: #606266;
            margin-bottom: 8px;
            line-height: 1.4;
          }

          .attachment-actions {
            display: flex;
            gap: 5px;

            .el-button--text {
              padding: 0;
              font-size: 12px;
            }
          }
        }
      }
    }
  }

  .no-attachment-tip {
    text-align: center;
    color: #909399;
    padding: 20px;
    border-top: 1px solid #ebeef5;

    i {
      font-size: 24px;
      margin-right: 8px;
    }
  }

/* 全局样式，用于详情对话框 */
</style>

<style>
.storage-detail-dialog .el-message-box {
  width: 80%;
  max-width: 800px;
}

.storage-detail-dialog .el-message-box__content {
  max-height: 600px;
  overflow-y: auto;
}
</style>
