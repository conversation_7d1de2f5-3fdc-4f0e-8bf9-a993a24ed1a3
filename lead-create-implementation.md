# 线索跟进新增功能实现文档

## 修改概述

根据新增线索接口文档要求，对线索跟进页面的新增功能进行了重构，简化了客户创建流程，使用新的线索创建接口。

## 主要修改内容

### 1. API接口更新

**文件：** `src/api/iBeauty/Workbench/followUp.js`

```javascript
/* 新增线索 */
createLead: (params) => {
  return API.POST("api/lead/create", params);
},
```

### 2. 新增线索表单

**替换原有复杂的客户新增流程**，改为简化的线索创建表单：

#### 表单字段：
- **客户姓名** (必填)
- **手机号** (必填，格式验证)
- **性别** (必填：男/女/保密)
- **线索来源** (默认：自然到店)
- **分配给** (可选，从跟进人员列表选择)
- **省市区** (可选，三级联动选择)
- **详细地址** (可选)
- **备注** (可选)

#### 线索来源选项：
- 自然到店 (默认)
- 抖音信息流
- 微信朋友圈
- 老带新
- 美团点评

### 3. 数据结构

**新增数据字段：**

```javascript
// 创建线索表单
createLeadForm: {
  Name: '',
  PhoneNumber: '',
  Gender: '1',
  LeadSource: 'ZIRAN_DAODIAN',
  AssignedTo: '',
  ProvinceCode: '',
  CityCode: '',
  AreaCode: '',
  Address: '',
  Remark: ''
},

// 表单验证规则
createLeadRules: {
  Name: [{ required: true, message: '请输入客户姓名', trigger: 'blur' }],
  PhoneNumber: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { pattern: /^1[3456789]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
  ],
  Gender: [{ required: true, message: '请选择性别', trigger: 'change' }]
}
```

### 4. 核心方法

#### `addNewCustomer()` - 触发新增线索
```javascript
addNewCustomer: function () {
  var that = this;
  // 使用新的简化线索创建流程
  that.showCreateLeadDialog = true;
  // 如果输入的是手机号，预填充到表单中
  if (/^1[3456789]\d{9}$/.test(that.customerName)) {
    that.createLeadForm.PhoneNumber = that.customerName;
  }
}
```

#### `submitCreateLead()` - 提交创建线索
```javascript
async submitCreateLead() {
  // 1. 表单验证
  // 2. 调用新的线索创建接口
  // 3. 处理成功响应，设置客户信息
  // 4. 刷新列表
}
```

#### `changeProvinceCityArea()` - 省市区选择处理
```javascript
changeProvinceCityArea(value) {
  if (value && value.length >= 3) {
    this.createLeadForm.ProvinceCode = value[0];
    this.createLeadForm.CityCode = value[1];
    this.createLeadForm.AreaCode = value[2];
  }
}
```

### 5. 用户体验改进

#### 原有流程：
1. 输入客户信息 → 回车
2. 打开复杂的客户新增弹框
3. 填写大量客户信息字段
4. 保存客户 → 创建跟进

#### 新流程：
1. 输入客户信息 → 回车
2. 打开简化的线索创建弹框
3. 填写必要的线索信息（姓名、手机、性别）
4. 可选填写地址、备注等
5. 直接创建线索和客户 → 创建跟进

### 6. 接口对接

**请求参数格式：**
```javascript
{
  Name: "客户姓名",
  PhoneNumber: "手机号",
  Gender: "性别代码",
  LeadSource: "线索来源代码",
  AssignedTo: "分配人员ID",
  ProvinceCode: "省份代码",
  CityCode: "城市代码", 
  AreaCode: "区域代码",
  Address: "详细地址",
  Remark: "备注信息"
}
```

**响应处理：**
```javascript
if (res.StateCode === 200) {
  // 设置客户信息到跟进表单
  that.customerID = res.Data.customerId;
  that.customerFullName = res.Data.customerName;
  that.customerPhoneNumber = res.Data.phoneNumber;
  that.customerName = res.Data.customerName + "【" + res.Data.phoneNumber + "】";
  
  // 刷新列表
  that.getFollowUp();
}
```

## 技术要点

### 1. 省市区数据
- 使用 `element-china-area-data` 库
- 三级联动选择器
- 自动设置省市区代码

### 2. 表单验证
- 客户姓名必填
- 手机号必填且格式验证
- 性别必选

### 3. 智能预填充
- 如果用户输入的是手机号格式，自动预填充到手机号字段

### 4. 错误处理
- 接口调用失败提示
- 表单验证失败提示
- 网络异常处理

## 兼容性说明

- 保留了原有的 `add-customer` 组件，确保其他地方的调用不受影响
- 新增功能仅影响线索跟进页面的客户创建流程
- 所有原有功能保持不变

## 测试建议

1. **正常流程测试**：输入客户信息 → 回车 → 填写线索表单 → 提交
2. **手机号预填充测试**：直接输入手机号 → 回车 → 验证预填充
3. **表单验证测试**：空字段、错误格式手机号等
4. **省市区选择测试**：选择不同省市区组合
5. **接口异常测试**：网络异常、服务器错误等情况

## 后续优化建议

1. 可考虑添加更多线索来源选项
2. 可添加客户标签功能
3. 可优化省市区选择的用户体验
4. 可添加客户重复检查提示
