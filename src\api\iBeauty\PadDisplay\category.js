// pad分类
import * as API from "@/api/index";
export default {
  /**  分类 列表*/
  CategoryAll: (params) => {
    return API.POST("api/goodsDisplayCategory/all", params);
  },
  /**  分类*/
  CategoryValid: (params) => {
    return API.POST("api/goodsDisplayCategory/valid", params);
  },
  /** 添加 分类*/
  Create: (params) => {
    return API.POST("api/goodsDisplayCategory/create", params);
  },
  /** 修改分类*/
  Update: (params) => {
    return API.POST("api/goodsDisplayCategory/update", params);
  },
  /** 移动分类*/
  Move: (params) => {
    return API.POST("api/goodsDisplayCategory/move", params);
  },
  // 简介
  Memo: (params) => {
    return API.POST("api/goodsDisplayCategory/memo", params);
  },
  /** 图片上传  */
  upload_addAttachment: (params) => {
    return API.POST("api/upload/addAttachment", params);
  },
};
