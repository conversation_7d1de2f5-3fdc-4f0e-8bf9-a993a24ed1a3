const Timestamp = new Date().getTime();

module.exports = {
  runtimeCompiler: true,
  productionSourceMap: false,
  lintOnSave: false,
  configureWebpack: {
    // webpack 配置
    output: {
      // 输出重构  打包编译后的 文件名称  【模块名称.版本号.时间戳】
      filename: `[name].${Timestamp}.js`,
      chunkFilename: `[name].${Timestamp}.js`,
    },
    devtool: 'source map',
  },

  publicPath: '/',
  
  // 解决Invalid Host header问题
  devServer: {
    disableHostCheck: true
  },

  css: {
    loaderOptions: {
      sass: {
        implementation: require('sass'),
        sassOptions: {
          outputStyle: 'expanded'
        }
      },
      scss: {
        implementation: require('sass'),
        sassOptions: {
          outputStyle: 'expanded'
        }
      }
    }
  }
};
