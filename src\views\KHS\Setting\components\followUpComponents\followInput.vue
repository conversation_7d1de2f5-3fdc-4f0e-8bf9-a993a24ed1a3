<template>
  <div>
    <!-- 最近有销售 -->
    <el-card class="box-card" shadow="never">
      <div class="dis_flex flex_x_between flex_y_center marbm_20">
        <div>
          <span class="marrt_15">{{title}}</span>
          <span class="color_999">{{subTitle}}</span>
        </div>
        <i class="el-icon-close" @click="handlerClose(Code)"></i>
      </div>
      <div class="dis_flex flex_y_center">
        <div style="width: 83px">{{contentTitle}}</div>
        <el-input
          v-model="contentValues_"
          style="width: 140px; height: 34px"
          size="small"
          class="marrt_10"
          min="0"
          v-input-fixed="0"
          @change="changeContentValue"
          placeholder="请输入"
        ></el-input
        >{{tailTitle}}
      </div>
    </el-card>
  </div>
</template>

<script>
export default {
  name: "followInput",
  components: {},
  props: {
     title: {
      type: String,
      default: "",
    },
    subTitle: {
      type: String,
      default: null,
    },

    tailTitle:{
      type:String,
      default:"天内"
    },
    contentTitle: {
      type: String,
      default: null,
    },
    Code:{
      type: String,
      default: null,
    },
    contentValues:{
      type: String,
      default:""
    },
  },
  data() {
    return {
      contentValues_:"",
      triggerCondition: {
        HaveSalesTime: "",
        NothingSalesTime: "",

        HaveConsumeTime:"",
        NothingConsumeTime:"",
      },
    };
  },
  computed: {},
   watch: {
    contentValues:  {
      handler(val) {
        this.contentValues_ = val;
      },
      immediate: true,
    },
  },
  created() {},
  mounted() {},
  methods: {
    // 删除
    handlerClose(Code) {
      this.$emit("handlerChildClone", Code);
    },
    changeContentValue(){
      this.$emit('handlerChildChange',this.contentValues_)
    },
  },
};
</script>

<style scoped lang="less">
