<template>
  <div class="customerFileCategory content_body" v-loading="loading">
    <!-- 搜索 -->
    <div class="nav_header">
      <el-row>
        <el-col :span="20">
          <el-form :inline="true" size="small" @keyup.enter.native="handleSearch">
            <el-form-item label="文件档案分类">
              <el-input v-model="Name" placeholder="输入档案名称搜索" clearable @clear="handleSearch"></el-input>
            </el-form-item>
            <el-form-item label="有效性">
              <el-select v-model="Active" placeholder="请选择有效性" clearable @change="changeActive">
                <el-option label="有效" :value="true"></el-option>
                <el-option label="无效" :value="false"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" size="small" @click="handleSearch" v-prevent-click>搜索</el-button>
            </el-form-item>
          </el-form>
        </el-col>
        <el-col :span="4" class="text_right">
          <el-button type="primary" size="small" @click="showAddDialog" v-prevent-click>新增</el-button>
        </el-col>
      </el-row>
    </div>
    <!-- 表格 -->
    <div>
      <el-table :data="customerFileCategoryTableData" size="small" tooltip-effect="light">
        <el-table-column prop="Name" label="分类名称"></el-table-column>
        <el-table-column label="移动" min-width="180px">
          <template slot-scope="scope">
            <el-button :disabled="scope.$index==0" size="small" type="primary" circle icon="el-icon-upload2" @click="upOneClick(scope.row, scope.$index)"></el-button>
            <el-button :disabled="scope.$index==0" size="small" type="primary" circle icon="el-icon-top" @click="upClick(scope.row, scope.$index)"></el-button>
            <el-button :disabled="scope.$index ==customerFileCategoryTableData.length-1" size="small" type="primary" circle icon="el-icon-bottom" @click="downClick(scope.row, scope.$index)"></el-button>
            <el-button :disabled="scope.$index ==customerFileCategoryTableData.length-1" size="small" type="primary" circle icon="el-icon-download" @click="downOneClick(scope.row, scope.$index)"></el-button>
          </template>
        </el-table-column>
        <el-table-column label="有效性">
          <template slot-scope="scope">{{ scope.row.Active ? "有效" : "无效" }}</template>
        </el-table-column>
        <el-table-column label="操作" width="80px">
          <template slot-scope="scope">
            <el-button type="primary" size="small" @click="showEditDialog(scope.row)" v-prevent-click>编辑</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <!--编辑、新增弹框-->
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="500px">
      <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="120px" class="demo-ruleForm" size="small">
        <el-form-item label="分类名称" prop="Name">
          <el-input v-model="ruleForm.Name" placeholder="请输入档案分类名称"></el-input>
        </el-form-item>
        <el-form-item label="是否有效" prop="Active" v-if="whichDialog == '1'">
          <el-radio-group v-model="ruleForm.Active">
            <el-radio :label="true">是</el-radio>
            <el-radio :label="false">否</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false" size="small" v-prevent-click>取 消</el-button>
        <el-button type="primary" size="small" :loading="modalLoading" @click="submitForm('ruleForm')" v-prevent-click>保 存</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import API from "@/api/CRM/Customer/customerFileCategory";
export default {
  name: "CustomerFileCategory",
  props: {},
  /**  引入的组件  */
  components: {},
  /**  Vue 实例的数据对象**/
  data() {
    return {
      disabled: true,
      customerFileCategoryTableData: [], //表格数据
      loading: false,
      modalLoading: false,
      Name: "",
      Active: true,
      dialogVisible: false, // 编辑、新增弹框状态
      whichDialog: "", // 弹框类型
      dialogTitle: "", //弹框标题
      ruleForm: {
        Name: "", // 档案名称
        Active: true, // 是否有效
        ID: "", // 会员ID
      }, // 编辑、新增表单数据
      rules: {
        Name: [{ required: true, message: "请输入档案名称", trigger: "blur" }],
        Active: [
          { required: true, message: "请选择是否有效", trigger: "change" },
        ],
      },
    };
  },
  /**计算属性  */
  computed: {},
  /**  方法集合  */
  methods: {
    // 搜索
    handleSearch() {
      let that = this;
      that.getCustomerFileCategory();
    },
    // 改变有效性
    changeActive() {
      var that = this;
      that.handleSearch();
    },
    // 新增按钮点击事件
    showAddDialog() {
      var that = this;
      that.whichDialog = "0";
      that.ruleForm = {
        Name: "", // 档案名称
        Active: true, // 是否有效
        ID: "",
      };
      that.dialogTitle = "新增文件档案分类";
      that.dialogVisible = true;
    },
    // 编辑
    showEditDialog(row) {
      var that = this;
      that.whichDialog = "1";
      that.ruleForm.ID = row.ID;
      that.ruleForm.Name = row.Name;
      that.ruleForm.Active = row.Active;
      that.dialogTitle = "编辑文件档案分类";
      that.dialogVisible = true;
    },
    // 列表数据请求
    getCustomerFileCategory() {
      var that = this;
      that.loading = true;
      var params = {
        Name: that.Name,
        Active: that.Active,
      };
      API.getCustomerFileCategory(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.customerFileCategoryTableData = res.Data;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },
    // 编辑、增加弹框确认按钮点击事件
    submitForm(formName) {
      var that = this;
      that.$refs[formName].validate((valid) => {
        if (valid) {
          if (that.whichDialog == "0") {
            that.customerFileCategory();
          } else {
            that.updateCustomerFileCategory();
          }
        }
      });
    },
    // 新增档案类型
    customerFileCategory() {
      var that = this;
      that.modalLoading = true;
      var params = {
        Name: that.ruleForm.Name,
      };
      API.customerFileCategory(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.$message.success("档案类型新增成功");
            that.dialogVisible = false;
            that.getCustomerFileCategory();
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.modalLoading = false;
        });
    },
    // 更新档案类型
    updateCustomerFileCategory() {
      var that = this;
      that.modalLoading = true;
      API.updateCustomerFileCategory(that.ruleForm)
        .then((res) => {
          if (res.StateCode == 200) {
            that.$message.success("更新成功");
            that.dialogVisible = false;
            that.getCustomerFileCategory();
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.modalLoading = false;
        });
    },
    // 移动档案类型
    moveCustomerFileCategory(MoveID, BeforeID) {
      var that = this;
      that.loading = true;
      var params = {
        MoveID: MoveID,
        BeforeID: BeforeID,
      };
      API.moveCustomerFileCategory(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.$message.success("移动成功");
            that.getCustomerFileCategory();
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },
    // 移动至顶部
    upOneClick(row) {
      var that = this;
      that.moveCustomerFileCategory(row.ID, "");
    },
    // 向上移动
    upClick(row, index) {
      var that = this;
      var beforeId = "";
      if (index > 1) {
        beforeId = that.customerFileCategoryTableData[index - 2].ID;
      }
      that.moveCustomerFileCategory(row.ID, beforeId);
    },
    // 向下移动
    downClick(row, index) {
      var that = this;
      var beforeId = "";
      var customerFileCategory = [];
      customerFileCategory = that.customerFileCategoryTableData;
      if (index + 1 != customerFileCategory.length) {
        beforeId = customerFileCategory[index + 1].ID;
      }
      that.moveCustomerFileCategory(row.ID, beforeId);
    },
    // 移动至底部
    downOneClick(row, index) {
      var that = this;
      var beforeId = "";
      var tableLength = 0;
      tableLength = that.customerFileCategoryTableData.length;
      if (index < tableLength - 1) {
        beforeId = that.customerFileCategoryTableData[tableLength - 1].ID;
      }
      that.moveCustomerFileCategory(row.ID, beforeId);
    },
  },
  /** 监听数据变化   */
  watch: {},
  /** 创建实例之前执行函数   */
  beforeCreate() {},
  /**  实例创建完成之后  */
  created() {},
  /**  在挂载开始之前被调用  */
  beforeMount() {},
  /**  实例被挂载后调用  */
  mounted() {
    let that = this;
    that.getCustomerFileCategory();
  },
  /**  数据更新 之前 调用  */
  beforeUpdate() {},
  /** 数据更新 完成 调用   */
  updated() {},
  /**  实例销毁之前调用  */
  beforeDestroy() {},
  /**  实例销毁后调用  */
  destroyed() {},
};
</script>

<style lang="scss">
.customerFileCategory {
}
</style>
