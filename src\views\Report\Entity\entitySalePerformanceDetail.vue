<template>
  <div class="entitySalePerformanceDetail content_body" v-loading="loading">
    <!-- 搜索 -->
    <div class="nav_header">
      <el-form :inline="true" size="small" @submit.native.prevent>
        <el-form-item v-if="EntityList.length > 1" label="业绩门店">
          <el-select v-model="searchData.EntityID" clearable filterable placeholder="请选择门店" :default-first-option="true" @change="handleSearch">
            <el-option v-for="item in EntityList" :key="item.ID" :label="item.EntityName" :value="item.ID"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="时间筛选">
          <el-date-picker v-model="searchData.QueryDate" unlink-panels type="daterange" range-separator="至" value-format="yyyy-MM-dd" start-placeholder="开始日期" end-placeholder="结束日期" @change="handleSearch" :picker-options="pickerOptions"></el-date-picker>
        </el-form-item>

        <el-form-item label="订单编号">
          <el-input v-model="searchData.BillID" clearable placeholder="请输入订单编号" @keyup.enter.native="handleSearch" @clear="handleSearch"></el-input>
        </el-form-item>
        <el-form-item label="订单类型">
          <el-select v-model="searchData.BillType" placeholder="请选择订单类型" @clear="handleSearch" clearable @change="handleSearch">
            <el-option label="销售订单" value="10"></el-option>
            <el-option label="充值订单" value="40"></el-option>
            <el-option label="退款订单" value="20"></el-option>
            <el-option label="补欠款单" value="30"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="商品信息">
          <el-cascader v-model="searchData.CategoryID" :options="categoryList" :props="cascaderProps" @change="handleSearch" clearable></el-cascader>
        </el-form-item>
        <el-form-item v-if="false" label="客户姓名">
          <el-input v-model="searchData.CustomerName" clearable @keyup.enter.native="handleSearch" @clear="handleSearch" placeholder="请输入客户姓名"></el-input>
        </el-form-item>

        <el-form-item label="客户等级">
          <el-select v-model="searchData.CustomerLevelID" placeholder="请选择客户等级" @clear="handleSearch" clearable @change="handleSearch">
            <el-option v-for="item in customerLevelList" :label="item.Name" :value="item.ID" :key="item.ID"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" v-prevent-click @click="handleSearch">搜索</el-button>
        </el-form-item>
        <el-form-item>
          <el-button v-if="isExport" type="primary" size="small" v-prevent-click :loading="downloadLoading" @click="downloadSalePayExcel">导出</el-button>
        </el-form-item>
      </el-form>
    </div>
    <!-- 表格 -->
    <el-table size="small" :data="tableData" show-summary :summary-method="getSummary">
      <el-table-column label="业绩门店" prop="EntityName"></el-table-column>
      <el-table-column label="订单信息">
        <el-table-column label="订单编号" prop="SaleBillID"></el-table-column>
        <el-table-column label="下单门店" prop="BillEntityName"></el-table-column>
        <el-table-column label="下单日期" prop="BillDate">
          <template slot-scope="scope">{{ scope.row.BillDate | dateFormat("YYYY-MM-DD HH:mm") }}</template>
        </el-table-column>
        <el-table-column label="订单类型" prop="BillType">
          <template slot-scope="scope"> {{ getBillType(scope.row.BillType) }}</template>
        </el-table-column>
      </el-table-column>
      <el-table-column label="客户信息">
        <el-table-column label="客户姓名" prop="CustomerName"></el-table-column>
        <el-table-column label="手机号" prop="PhoneNumber">
          <template slot-scope="scope">
            {{ scope.row.PhoneNumber | hidephone }}
          </template>
        </el-table-column>
        <el-table-column label="客户编号" prop="Code"></el-table-column>
        <el-table-column label="客户等级" prop="CustomerLevelName"></el-table-column>
        <el-table-column label="归属门店" prop="BelongEntityName"></el-table-column>
      </el-table-column>
      <el-table-column label="商品信息">
        <el-table-column label="商品名称" prop="GoodName"></el-table-column>
        <el-table-column label="商品分类" prop="CategoryName"></el-table-column>
        <el-table-column label="商品类型" prop="GoodsTypeName"></el-table-column>
        <el-table-column label="是否赠送" prop="IsLargess">
          <template slot-scope="scope">{{ scope.row.IsLargess ? "是" : "否" }}</template>
        </el-table-column>
        <el-table-column label="单价" prop="Price">
          <template slot-scope="scope">{{ scope.row.Price | toFixed | NumFormat }}</template>
        </el-table-column>
        <el-table-column label="数量" prop="Quantity"></el-table-column>
        <el-table-column label="优惠金额" prop="PreferentialAmount">
          <template slot-scope="scope">
            <span v-if="scope.row.PreferentialAmount < 0" class="color_red">{{ scope.row.PreferentialAmount | toFixed | NumFormat }}</span>
            <span v-else-if="scope.row.PreferentialAmount > 0" class="color_green">+{{ scope.row.PreferentialAmount | toFixed | NumFormat }}</span>
            <span v-else>0.00</span>
          </template>
        </el-table-column>
        <el-table-column label="合计金额" prop="TotalAmount">
          <template slot-scope="scope">
            <span v-if="scope.row.TotalAmount < 0" class="color_red">{{ scope.row.TotalAmount | toFixed | NumFormat }}</span>
            <span v-else-if="scope.row.TotalAmount > 0" class="color_green">+{{ scope.row.TotalAmount | toFixed | NumFormat }}</span>
            <span v-else>0.00</span>
          </template>
        </el-table-column>
        <el-table-column label="欠款金额" prop="ArrearAmount">
          <template slot-scope="scope">
            <span class="color_red">{{ scope.row.ArrearAmount | toFixed | NumFormat }}</span>
          </template>
        </el-table-column>
      </el-table-column>
      <el-table-column label="门店业绩">
        <el-table-column label="现金业绩" align="right" prop="PayPerformance">
          <template slot-scope="scope">
            <span v-if="scope.row.PayPerformance < 0" class="color_red">{{ scope.row.PayPerformance | toFixed | NumFormat }}</span>
            <span v-else-if="scope.row.PayPerformance > 0" class="color_green">+{{ scope.row.PayPerformance | toFixed | NumFormat }}</span>
            <span v-else>0.00</span>
          </template>
        </el-table-column>
        <el-table-column label="卡扣业绩" align="right" prop="SavingCardPerformance">
          <template slot-scope="scope">
            <span v-if="scope.row.SavingCardPerformance < 0" class="color_red">{{ scope.row.SavingCardPerformance | toFixed | NumFormat }}</span>
            <span v-else-if="scope.row.SavingCardPerformance > 0" class="color_green">+{{ scope.row.SavingCardPerformance | toFixed | NumFormat }}</span>
            <span v-else>0.00</span>
          </template>
        </el-table-column>
        <el-table-column label="赠卡扣业绩" align="right" prop="SavingCardLargessPerformance">
          <template slot-scope="scope">
            <span v-if="scope.row.SavingCardLargessPerformance < 0" class="color_red">{{ scope.row.SavingCardLargessPerformance | toFixed | NumFormat }}</span>
            <span v-else-if="scope.row.SavingCardLargessPerformance > 0" class="color_green">+{{ scope.row.SavingCardLargessPerformance | toFixed | NumFormat }}</span>
            <span v-else>0.00</span>
          </template>
        </el-table-column>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <div class="pad_15 text_right">
      <el-pagination background v-if="paginations.total > 0" @current-change="handlePageChange" :current-page.sync="paginations.page" :page-size="paginations.page_size" :layout="paginations.layout" :total="paginations.total"></el-pagination>
    </div>
  </div>
</template>

<script>
import API from "@/api/Report/Entity/entitySalePerformanceDetail";
import APIStore from "@/api/Report/Entity/entityTrade";

import APICategory from "@/api/Report/Goods/SaleStatistics.js";
const dayjs = require("dayjs");
const isoWeek = require("dayjs/plugin/isoWeek");
dayjs.extend(isoWeek);
export default {
  name: "ReportEntitySalePerformanceDetail",

  beforeRouteEnter(to, from, next) {
    next((vm) => {
      vm.isExport = vm.$permission.permission(to.meta.Permission, "Report-Entity-EntitySalePerformanceDetail-Export");
    });
  },
  props: {},
  /** 监听数据变化   */
  watch: {},
  /**  引入的组件  */
  components: {},
  /**  Vue 实例的数据对象**/
  data() {
    return {
      isExport: false,
      loading: false,
      downloadLoading: false,
      EntityList: [], //门店数据
      tableData: [], // 表格数据
      searchData: {
        EntityID: null,
        QueryDate: [this.$formatDate(new Date(), "YYYY-MM-DD"), this.$formatDate(new Date(), "YYYY-MM-DD")],
        GoodsTypeName: null,
        CustomerName: "",
        BillType: "",
        CategoryID: "",
        BillID: "",
        CustomerLevelID: "",
      },
      paginations: {
        page: 1, // 当前位于哪页
        total: 0, // 总数
        page_size: 10, // 1页显示多少条
        layout: "total, prev, pager, next,jumper", // 翻页属性
      },
      pickerOptions: {
        shortcuts: [
          {
            text: "今天",
            onClick: (picker) => {
              let end = new Date();
              let start = new Date();
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "本周",
            onClick: (picker) => {
              let end = new Date();
              let weekDay = dayjs(end).isoWeekday();
              let start = dayjs(end)
                .subtract(weekDay - 1, "day")
                .toDate();
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "本月",
            onClick: (picker) => {
              let end = new Date();
              let start = dayjs(dayjs(end).format("YYYY-MM") + "01").toDate();
              picker.$emit("pick", [start, end]);
            },
          },
        ],
      },
      categoryList: [],
      cascaderProps: {
        checkStrictly: true,
        label: "Name",
        value: "ID",
        children: "Child",
      },
      customerLevelList: [],
    };
  },
  /**计算属性  */
  computed: {},
  /**  方法集合  */
  methods: {
    /* 搜索 */
    handleSearch() {
      let that = this;
      that.paginations.page = 1;
      that.getEntitySalePerformanceList();
    },
    /* 分页 */
    handlePageChange(page) {
      let that = this;
      that.paginations.page = page;
      that.getEntitySalePerformanceList();
    },
    /* 导出 */
    downloadSalePayExcel() {
      let that = this;
      if (that.searchData.QueryDate != null) {
        if (dayjs(that.searchData.QueryDate[0]).add(366, "day").valueOf() < dayjs(that.searchData.QueryDate[1]).valueOf()) {
          that.$message.error("时间筛选范围不能超366天");
          return;
        }
        let CategoryID = "";
        if (that.searchData.CategoryID.length == 2) {
          CategoryID = that.searchData.CategoryID[1];
        }

        if (that.searchData.CategoryID.length == 3) {
          CategoryID = that.searchData.CategoryID[2];
        }
        let params = {
          EntityID: that.searchData.EntityID,
          StartDate: that.searchData.QueryDate[0],
          EndDate: that.searchData.QueryDate[1],
          CustomerName: that.searchData.CustomerName,
          PageNum: that.paginations.page,
          BillType: that.searchData.BillType,
          GoodsTypeName: that.searchData.CategoryID.length ? that.getGoodsTypeID(that.searchData.CategoryID[0]) : "",
          CategoryID: CategoryID,
          BillID: that.searchData.BillID,
          CustomerLevelID: that.searchData.CustomerLevelID,
          
        };
        that.downloadLoading = true;
        API.entitySalePerformanceExcel(params)
          .then((res) => {
            this.$message.success({
              message: "正在导出",
              duration: "4000",
            });
            const link = document.createElement("a");
            let blob = new Blob([res], { type: "application/octet-stream" });
            link.style.display = "none";
            link.href = URL.createObjectURL(blob);
            link.download = "门店销售业绩明细报表.xlsx"; //下载的文件名
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
          })
          .finally(function () {
            that.downloadLoading = false;
          });
      } else {
        this.$message.error({
          message: "请选择查询时间",
          duration: 2000,
        });
      }
    },
    /* 获取表格数据 */
    getEntitySalePerformanceList() {
      let that = this;
      if (that.searchData.QueryDate != null) {
        if (dayjs(that.searchData.QueryDate[0]).add(366, "day").valueOf() < dayjs(that.searchData.QueryDate[1]).valueOf()) {
          that.$message.error("时间筛选范围不能超366天");
          return;
        }
        that.loading = true;
        let CategoryID = "";
        if (that.searchData.CategoryID.length == 2) {
          CategoryID = that.searchData.CategoryID[1];
        }

        if (that.searchData.CategoryID.length == 3) {
          CategoryID = that.searchData.CategoryID[2];
        }

        let params = {
          EntityID: that.searchData.EntityID,
          StartDate: that.searchData.QueryDate[0],
          EndDate: that.searchData.QueryDate[1],
          CustomerName: that.searchData.CustomerName,
          PageNum: that.paginations.page,
          BillType: that.searchData.BillType,
          GoodsTypeName: that.searchData.CategoryID.length ? that.getGoodsTypeID(that.searchData.CategoryID[0]) : "",
          CategoryID: CategoryID,
          BillID: that.searchData.BillID,
          CustomerLevelID: that.searchData.CustomerLevelID,
        };
        API.getEntitySalePerformanceList(params)
          .then((res) => {
            if (res.StateCode == 200) {
              that.tableData = res.Data.Detail.List;
              that.paginations.page_size = res.Data.Detail.PageSize;
              that.paginations.total = res.Data.Detail.Total;
              that.SumOutputForm = res.Data.SumOutputForm;
            } else {
              this.$message.error({
                message: res.Message,
                duration: 2000,
              });
            }
          })
          .finally(function () {
            that.loading = false;
          });
      } else {
        this.$message.error({
          message: "请选查询选时间",
          duration: 2000,
        });
      }
    },
    /**    */
    getGoodsTypeID(type) {
      switch (type) {
        case 10:
          return "产品";
        case 20:
          return "项目";
        case 30:
          return "通用次卡";
        case 40:
          return "时效卡";
        case 50:
          return "储值卡";
        case 60:
          return "套餐卡";
      }
    },
    /* 表格合计 */
    getSummary({ columns }) {
      const sums = [];
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = <span class="font_weight_600">合计</span>;
          return;
        }
        var filter_NumFormat = this.$options.filters["NumFormat"];
        switch (column.property) {
          case "PayPerformance":
            sums[index] = <span class="font_weight_600">{filter_NumFormat(this.SumOutputForm ? this.SumOutputForm.PayPerformance : 0)}</span>;
            break;
          case "SavingCardPerformance":
            sums[index] = <span class="font_weight_600">{filter_NumFormat(this.SumOutputForm ? this.SumOutputForm.SavingCardPerformance : 0)}</span>;
            break;
          case "SavingCardLargessPerformance":
            sums[index] = <span class="font_weight_600">{filter_NumFormat(this.SumOutputForm ? this.SumOutputForm.SavingCardLargessPerformance : 0)}</span>;
            break;
          case "TotalAmount":
            sums[index] = <span class="font_weight_600">{filter_NumFormat(this.SumOutputForm ? this.SumOutputForm.TotalAmount : 0)}</span>;
            break;
          case "ArrearAmount":
            sums[index] = <span class="font_weight_600">{filter_NumFormat(this.SumOutputForm ? this.SumOutputForm.ArrearAmount : 0)}</span>;
            break;
          case "PreferentialAmount":
            sums[index] = <span class="font_weight_600">{filter_NumFormat(this.SumOutputForm ? this.SumOutputForm.PreferentialAmount : 0)}</span>;
            break;
        }
      });
      return sums;
    },
    /*  获取订单类型   */
    getBillType(BillType) {
      switch (BillType) {
        case "10":
          return "销售订单";
        case "20":
          return "退款订单";
        case "30":
          return "补欠款单";
        case "40":
          return "充值订单";
      }
    },
    /* 获取门店 */
    async getStoreList() {
      var that = this;
      let res = await APIStore.getStoreList();
      if (res.StateCode == 200) {
        that.EntityList = res.Data;
      } else {
        that.$message.error({
          message: res.Message,
          duration: 2000,
        });
      }
    },
    /**  分类  */
    async entitySaleGoodsDetailStatement_category() {
      let that = this;
      let res = await APICategory.entitySaleGoodsDetailStatement_category();
      if (res.StateCode == 200) {
        that.categoryList = res.Data;
      } else {
        that.$message.error(res.Message);
      }
    },
    /**  获取客户等级  */
    getCustomerLevel_all() {
      let that = this;
      let params = {
        Name: "",
        Active: true, //有效性
      };
      API.customerLevel_all(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.customerLevelList = res.Data;
          } else {
            that.$message.error(res.Message);
          }
        })
        .catch((fail) => {
          that.$message.error(fail);
        });
    },
  },
  /** 创建实例之前执行函数   */
  beforeCreate() {},
  /**  实例创建完成之后  */
  created() {},
  /**  在挂载开始之前被调用  */
  beforeMount() {},
  /**  实例被挂载后调用  */
  mounted() {
    let that = this;
    this.isExport = this.$permission.permission(this.$route.meta.Permission, "Report-Entity-EntitySalePerformanceDetail-Export");
    that.getStoreList();
    that.getEntitySalePerformanceList();
    that.entitySaleGoodsDetailStatement_category();
    that.getCustomerLevel_all();
  },
  /**  数据更新 之前 调用  */
  beforeUpdate() {},
  /** 数据更新 完成 调用   */
  updated() {},
  /**  实例销毁之前调用  */
  beforeDestroy() {},
  /**  实例销毁后调用  */
  destroyed() {},
};
</script>

<style lang="scss">
.entitySalePerformanceDetail {
}
</style>
