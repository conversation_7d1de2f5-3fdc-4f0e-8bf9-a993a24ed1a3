<template>
  <div class="customerBasicFiles">
    <el-scrollbar v-show="customerID">
      <div>
        <div class="tip">
          <el-row type="flex" justify="space-between" align="middle">
            <el-col :span="12">标签</el-col>
            <el-col :span="12" class="text_right">
              <el-button size="mini" type="text" style="padding: 0px" @click="tagClick">编辑</el-button>
            </el-col>
          </el-row>
        </div>

        <div class="martp_5 marbm_5" style="flex: 2">
          <el-tag v-for="(item, index) in customerTag" :key="index" effect="plain" class="mar_5">{{ item.Name }} </el-tag>
        </div>
        <el-form label-width="110px" size="small" :inline="true" ref="ruleFormRef" :model="ruleForm" :rules="rules">
          <div class="tip dis_flex flex_x_between flex_y_center">
            <div>基本信息</div>
            <el-button size="mini" type="primary" @click="saveInfo(1)" :loading="modalLoading">保存</el-button>
          </div>

          <el-form-item label="会员姓名:" prop="Name">
            <el-input v-model="ruleForm.Name" style="width: 100%"></el-input>
          </el-form-item>

          <el-form-item label="手机号:" prop="PhoneNumber">
            <el-input v-model="ruleForm.PhoneNumber"></el-input>
          </el-form-item>

          <el-form-item label="客户编号:" prop="Code">
            <el-input v-model="ruleForm.Code" style="width: 100%"></el-input>
          </el-form-item>

          <el-form-item label="会员性别:">
            <el-radio-group v-model="ruleForm.Gender">
              <el-radio label="2">女</el-radio>
              <el-radio label="1">男</el-radio>
              <el-radio label="0">未知</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item>
            <div slot="label">
              <el-select placeholder="请选择" v-model="ruleForm.BirthdayType" style="width: 100%">
                <el-option label="公历生日" value="10"></el-option>
                <el-option label="农历生日" value="20"></el-option>
              </el-select>
            </div>
            <el-date-picker v-model="ruleForm.Birthday" type="date" value-format="yyyy-MM-dd" placeholder="选择日期"> </el-date-picker>
          </el-form-item>

          <el-form-item label="信息来源:">
            <el-cascader
              v-model="ruleForm.CustomerSourceID"
              :options="customerSource"
              :props="{
                checkStrictly: true,
                children: 'Child',
                value: 'ID',
                label: 'Name',
                emitPath: false,
              }"
              :show-all-levels="false"
              filterable
              clearable
            ></el-cascader>
          </el-form-item>

          <el-form-item label="会员介绍人:">
            <el-select
              v-model="ruleForm.Introducer"
              placeholder="请选择会员"
              filterable
              remote
              reserve-keyword
              size="small"
              default-first-option
              v-loadmore="customerIntroducerLoadMore"
              :remote-method="remoteCusMethod"
            >
              <el-option v-for="item in customerIntroducer" :key="item.ID" :label="item.Name" :value="item.ID - 0">
                <span style="float: left">{{ item.Name }}</span>
                <span style="float: right; color: #8492a6; font-size: 13px" v-if="item.PhoneNumber.length == 11">{{ item.PhoneNumber | hidephone }}</span>
              </el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="会员等级:">
            <el-select v-model="ruleForm.CustomerLevelID" placeholder="请选择会员等级" filterable size="small">
              <el-option v-for="item in customerLevel" :key="item.ID" :label="item.Name" :value="item.ID"> </el-option>
            </el-select>
          </el-form-item>

          <el-form-item
            label="身份证号:"
            prop="IdentityCard"
            :rules="[
              {
                required: false,
                message: '请输入身份证号',
                trigger: 'blur',
              },
              {
                pattern:
                  /(^[1-9]\d{5}(18|19|([23]\d))\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$)|(^[1-9]\d{5}\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{2}$)/,
                message: '请输入合法身份证号',
                trigger: 'blur',
              },
            ]"
          >
            <el-input v-model="ruleForm.IdentityCard"></el-input>
          </el-form-item>

          <el-form-item label="职业:">
            <el-input v-model="ruleForm.Job"></el-input>
          </el-form-item>

          <el-form-item label="省市区:">
            <el-cascader clearable placeholder="请选择省 / 市 / 区" size="small" :options="regionData" v-model="regionDataSelArr">
            </el-cascader>
          </el-form-item>

          <el-form-item label="详细地址:">
            <el-input style="width: 100%; max-width: unset" v-model="ruleForm.Address"></el-input>
          </el-form-item>

          <el-form-item label="备注信息:">
            <el-input type="textarea" v-model="ruleForm.Remark"></el-input>
          </el-form-item>

          <div v-if="isShowChannel && customerServicer.length > 0">
            <div class="tip dis_flex flex_x_between flex_y_center">
              <div>服务人员</div>
              <el-button size="mini" type="primary" @click="saveInfo(2)" :loading="ServeModalLoading">保存</el-button>
            </div>
            <el-row>
              <el-col :span="12" v-for="item in customerServicer" :key="item.ID">
                <el-form-item :label="item.Name" label-width="90px">
                  <el-select
                    v-model="item.ServicerListArr"
                    placeholder="请选择服务人员"
                    multiple
                    collapse-tags
                    reserve-keyword
                    filterable
                    size="small"
                    default-first-option
                    @change="change"
                  >
                    <el-option v-for="serervicer in item.ServicerEmpList" :key="serervicer.ID" :label="serervicer.Name" :value="serervicer.ID">
                      <span>{{ serervicer.Name }}</span>
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
          </div>

          <div class="tip dis_flex flex_x_between flex_y_center">
            <div>基本档案</div>
            <el-button size="mini" type="primary" @click="saveBasicFile" :loading="baseModalLoading">保存</el-button>
          </div>
          <el-row style="display: flex;flex-wrap: wrap;">
            <el-col :span="12" v-for="(item, index) in basicFileTerm" :key="index">
              <el-form-item v-if="item.Type == 10" :label="item.CustomerBasicFileName" label-width="90px">
                <el-input v-model="item.Value" placeholder="请输入文本内容" clearable size="small" sty="width:60px"></el-input>
              </el-form-item>
              <el-form-item v-if="item.Type == 20" :label="item.CustomerBasicFileName" label-width="90px">
                <el-date-picker v-model="item.Value" type="date" placeholder="请选择日期" clearable size="small"> </el-date-picker>
              </el-form-item>
              <el-form-item v-if="item.Type == 30" :label="item.CustomerBasicFileName" label-width="90px">
                <el-select v-model="item.Value" placeholder="请选择单选项" clearable size="small">
                  <el-option v-for="item in getComponentsProperty(item.ComponentsProperty)" :key="item.key" :label="item.value" :value="item.value">
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item v-if="item.Type == 40" :label="item.CustomerBasicFileName" label-width="90px">
                <el-select v-model="item.Value" multiple collapse-tags placeholder="请选择多选项" clearable size="small">
                  <el-option v-for="item in getComponentsProperty(item.ComponentsProperty)" :key="item.key" :label="item.value" :value="item.value">
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </el-scrollbar>

    <!--标签弹窗-->
    <el-dialog title="编辑标签" :visible.sync="dialogTag" width="1000px" append-to-body>
      <el-row style="max-height: 130px; overflow-y: auto">
        <el-tag v-for="(item, index) in editCustomerTag" :key="index" closable @close="removeTag(index)" effect="plain" class="mar_5">{{ item.Name }} </el-tag>
      </el-row>
      <el-row class="pad_5" v-if="customTagLibrary">
        <el-col :span="10">
          <div class="el-form-item el-form-item--small" style="margin-bottom: 0px">
            <label class="el-form-item__label" style="width: 98px">自定义标签：</label>
            <div class="el-form-item__content" style="margin-left: 98px">
              <div class="el-input el-input--small">
                <el-input type="text" autocomplete="off" placeholder="标签名限8个字" v-model="tagName" maxlength="8" size="small" clearable>
                  <template slot="append">
                    <el-button size="small" @click="addTagClick" clearable v-prevent-click>添加</el-button>
                  </template>
                </el-input>
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
      <el-row class="pad_5">
        <div class="pad_5_0">选择已有标签</div>
        <el-col style="height: 180px; overflow-y: auto" class="border radius5 pad_10">
          <el-tag v-for="item in tagList" :key="item.ID" :type="item.type" effect="plain" @click="tagSelectClick(item)" class="cursor_pointer mar_5"
            >{{ item.Name }}
          </el-tag>
        </el-col>
      </el-row>
      <div slot="footer">
        <el-button size="small" @click="dialogTag = false" v-prevent-click>取 消</el-button>
        <el-button type="primary" size="small" @click="tagSaveClick" :loading="modalLoading" v-prevent-click>保存 </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import permission from "@/components/js/permission.js";
import API from "@/api/CRM/Customer/customer";
import APICustomerSource from "@/api/CRM/Customer/customerSource";
import basicFileAPI from "@/api/CRM/Customer/customerBasicFile.js";
import APICustomerLevel from "@/api/CRM/Customer/customerLevel";
import APITagLibrary from "@/api/CRM/Customer/customerTagLibrary";
import { regionData} from "element-china-area-data";
var Enumerable = require("linq");

export default {
  name: "customerBasicFiles",
  props: {
    customerID: {
      type: Number,
      require: true,
    },
  },
  /** 监听数据变化   */
  watch: {
    customerID: {
      immediate: true,
      handler(val) {
        if (val) {
          this.getCustomerInfoData();
          this.customerTagData();
          this.getBasicFile();
        }
      },
    },
  },
  /**  引入的组件  */
  components: {},
  /**  Vue 实例的数据对象**/
  data() {
    return {
      modalLoading: false,
      baseModalLoading: false,
      ServeModalLoading: false,
      dialogTag: false,

      customTagLibrary: false, //顾客标签权限
      isShowChannel: false, //是否展示渠道

      customerLevel: [],
      regionData: regionData, //省市区
      customerDetail: {},
      customerIntroducer: [],
      regionDataSelArr: [],
      customerSource: [],
      customerServicer: [], // 服务人员
      basicFileTerm: [], // 基本档案数组

      customerTag: [], //顾客标签
      tagList: [], //全部标签
      editCustomerTag: [], //顾客修改标签
      tagName: "", //自定义标签输入框值

      ruleForm: {
        Name: "",
        PhoneNumber: "",
        Gender: "2",
        CustomerSourceID: null,
        // EmployeeID: [],
        Introducer: "",
        CustomerLevelID: "",
        Code: "",
        BirthdayType: 10,
        Birthday: "",
        ProvinceCode: "",
        CityCode: "",
        AreaCode: "",
        Job: "",
        Address: "",
        IdentityCard: "",
        Remark: "",
        ChannelID: null,
      },

      rules: {
        Name: [{ required: true, message: "请输入会员姓名", trigger: "blur" }],
        PhoneNumber: [{ required: true, message: "请输入手机号", trigger: "blur" }],
      },
    };
  },
  /**计算属性  */
  computed: {
    property() {
      const that = this;
      if (that.customerDetail.ProvinceCode && that.customerDetail.CityCode && that.customerDetail.AreaCode && that.dialogDetail) {
        const first = that.regionData.filter((item) => item.adcode == that.customerDetail.ProvinceCode);
        const second = first[0].districts.filter((item) => item.adcode == that.customerDetail.CityCode);
        const third = second[0].districts.filter((item) => item.adcode == that.customerDetail.AreaCode);
        if (first.length > 0 && second.length > 0 && third.length > 0) {
          return first[0].name + "/" + second[0].name + "/" + third[0].name;
        } else {
          return "";
        }
      } else {
        return "";
      }
    },
  },
  /**  方法集合  */
  methods: {
    // 基本信息 服务人员的保存
    saveInfo(e) {
      var that = this;
      that.$refs.ruleFormRef.validate((valid) => {
        if (valid) {
          if (e == 1) {
            that.modalLoading = true;
          } else {
            that.ServeModalLoading = true;
          }
          let para = Object.assign({}, that.ruleForm);
          if (that.regionDataSelArr.length) {
            para.ProvinceCode = that.regionDataSelArr[0];
            para.CityCode = that.regionDataSelArr[1];
            para.AreaCode = that.regionDataSelArr[2];
          }
          let tempArr = [];
          that.customerServicer.forEach((val) => {
            val.ServicerListArr.forEach((em) => {
              tempArr.push({
                ServicerID: val.ID,
                EmployeeID: em,
              });
            });
          });
          para.ServicerList = tempArr;
          API.updateCustomer(para)
            .then(function (res) {
              if (res.StateCode === 200) {
                that.$message.success({
                  message: "编辑成功",
                  duration: 2000,
                });
                that.customerDetail = Object.assign({}, res.Data);
                that.getCustomerDetail();
              } else {
                that.$message.error({
                  message: res.Message,
                  duration: 2000,
                });
              }
            })
            .finally(function () {
              that.modalLoading = false;
            });
        }
      });
    },

    // 基本档案的保存
    async saveBasicFile() {
      let that = this;
      let detail = that.basicFileTerm.map((val) => {
        let Value = "";
        if (val.Type == 40) {
          Value = JSON.stringify(val.Value);
        } else {
          Value = val.Value ? val.Value : "";
        }
        return {
          CustomerBasicFileID: val.CustomerBasicFileID,
          Value: Value,
        };
      });
      let params = {
        CustomerID: that.customerID,
        detail: detail,
      };
      that.baseModalLoading = true;
      let res = await basicFileAPI.createBasicFile(params);
      if (res.StateCode == 200) {
        that.baseModalLoading = false;
        that.$message.success("保存成功");
      } else {
        that.baseModalLoading = false;
        that.$message.error(res.Message);
      }
    },

    /* 顾客介绍人加载更多 */
    customerIntroducerLoadMore() {
      const that = this;
      if (that.customerIntroducer.length < that.CusTotal) {
        that.CusPageNum++;
        that.CustomerIntroducer();
      }
    },
    /* 顾客介绍人远程搜索 */
    remoteCusMethod(value) {
      const that = this;
      that.customerIntroducer = [];
      that.CusPageNum = 1;
      that.CustomerIntroducer(value);
    },
    /**  *******************************  */
    /**    */
    getCustomerInfoData() {
      // let that = this;
      this.getCustomerDetail();
    },
    /**  获取顾客详情  */
    async getCustomerDetail() {
      let that = this;
      let params = { CustomerID: that.customerID };
      let res = await API.getCustomerDetail(params);
      if (res.StateCode == 200) {
        this.customerDetail = res.Data;
        that.ruleForm = Object.assign({}, res.Data);
        that.regionDataSelArr = [res.Data.ProvinceCode, res.Data.CityCode, res.Data.AreaCode];

        this.customerDetail.ServicerEmployee.forEach((item) => {
          that.customerServicer.forEach((servicer) => {
            if (item.ID == servicer.ID) {
              servicer.ServicerListArr = item.ServicerEmpList.map((val) => val.ID);
            }
          });
        });
      } else {
        that.$message.error(res.Message);
      }
    },
    /*  顾客来源 */
    async getCustomerSourceData() {
      var that = this;
      var params = {
        Name: "",
        Active: true,
      };
      let res = await APICustomerSource.getCustomerSource(params);
      if (res.StateCode == 200) {
        that.customerSource = res.Data;
      } else {
        that.$message.error({
          message: res.Message,
          duration: 2000,
        });
      }
    },
    /* 顾客等级 */
    async getCustomerLevelData() {
      var that = this;
      var params = {
        Name: "",
        Active: true,
      };
      let res = await APICustomerLevel.getCustomerLevel(params);
      if (res.StateCode == 200) {
        that.customerLevel = res.Data;
      } else {
        that.$message.error({
          message: res.Message,
          duration: 2000,
        });
      }
    },
    /* 顾客介绍人 */
    async getCustomerIntroducer(value) {
      var that = this;
      var params = {
        Name: value,
        PageNum: that.CusPageNum,
        Active: true,
      };
      let res = await API.customerAll(params);
      if (res.StateCode == 200) {
        that.customerIntroducer = [...that.customerIntroducer, ...res.List];
        that.CusTotal = res.Total;
      } else {
        that.$message.error({
          message: res.Message,
          duration: 2000,
        });
      }
    },
    /**   服务人员   */
    getCustomerServicer() {
      let that = this;
      let params = {};
      API.getAllCustomerServicer(params).then((res) => {
        if (res.StateCode == 200) {
          that.customerServicer = res.Data;
          that.customerServicer.forEach((item) => {
            item.ServicerListArr = [];
          });

          this.customerDetail.ServicerEmployee.forEach((item) => {
            res.Data.forEach((servicer) => {
              if (item.ID == servicer.ID) {
                servicer.ServicerListArr = item.ServicerEmpList.map((val) => val.ID);
              }
            });
          });
        } else {
          that.$message.error({
            message: res.Message,
            duration: 2000,
          });
        }
      });
    },
    /**   获取基本信息档案项   */
    async getBasicFile() {
      let that = this;
      let params = {
        CustomerID: that.customerID, //顾客ID
      };
      let res = await basicFileAPI.getBasicFile(params);
      if (res.StateCode == 200) {
        that.basicFileTerm = res.Data.map((val) => {
          if (val.Type == 40 && typeof val.Value == "string") {
            val.Value = val.Value ? JSON.parse(val.Value) : [];
          }
          return val;
        });
      } else {
        that.$message.error(res.Message);
      }
    },

    change() {
      this.$forceUpdate();
    },
    /**  获取选择项   */
    getComponentsProperty(property) {
      if (!property) return [];
      return JSON.parse(property);
    },
    // 顾客标签
    customerTagData: function () {
      var that = this;
      var params = {
        ID: that.customerID,
      };
      API.getCustomerTag(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.customerTag = res.Data;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },
    // 标签列表
    tagData: function () {
      var that = this;
      APITagLibrary.customerTagLibraryAll()
        .then((res) => {
          if (res.StateCode == 200) {
            that.tagList = res.Data;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },
    // 标签
    tagClick: function () {
      var that = this;
      that.editCustomerTag = Object.assign([], that.customerTag);
      that.tagType();
      that.dialogTag = true;
    },
    tagType: function () {
      var that = this;
      that.tagList.forEach(function (item) {
        item.type = "info";
        that.editCustomerTag.forEach(function (tag) {
          if (item.ID == tag.ID) {
            item.type = "primary";
          }
        });
      });
    },
    // 删除标签
    removeTag: function (index) {
      var that = this;
      that.editCustomerTag.splice(index, 1);
      that.tagType();
    },
    // 添加标签
    addTagClick: function () {
      var that = this;
      var params = {
        Name: that.tagName,
      };
      APITagLibrary.customerTagLibraryCreate(params)
        .then(function (res) {
          if (res.StateCode === 200) {
            that.editCustomerTag.push(res.Data);
            that.tagList.push(res.Data);
            that.tagType();
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.modalLoading = false;
        });
    },
    // 选择标签
    tagSelectClick: function (row) {
      var that = this;
      if (row.type == "info") {
        that.editCustomerTag.push(row);
      }
      that.tagType();
    },
    // 标签保存
    tagSaveClick: function () {
      var that = this;
      var TagLibrary = Enumerable.from(that.editCustomerTag)
        .select((val) => val.ID)
        .toArray();
      var params = {
        ID: that.customerID,
        TagLibrary: TagLibrary,
      };
      API.updateCustomerTagLibrary(params)
        .then(function (res) {
          if (res.StateCode === 200) {
            that.dialogTag = false;
            that.customerTagData();
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {});
    },
  },

  /** 创建实例之前执行函数   */
  beforeCreate() {},
  /**  实例创建完成之后  */
  created() {
    // 顾客信息编辑权限
    this.customTagLibrary = permission.getCustomerDetailPermission(this.$router, "iBeauty-Customer-Customer-CustomTagLibrary");

    // 顾客详情渠道
    this.isShowChannel = permission.getCustomerDetailPermission(this.$router, "iBeauty-Customer-Customer-Channel");

    this.getCustomerSourceData();
    this.getCustomerLevelData();
    this.getCustomerIntroducer();
    this.getCustomerServicer();
    this.getBasicFile();
    // 全部标签列表
    this.tagData();


  },
  /**  在挂载开始之前被调用  */
  beforeMount() {},
  /**  实例被挂载后调用  */
  mounted() {},
  /**  数据更新 之前 调用  */
  beforeUpdate() {},
  /** 数据更新 完成 调用   */
  updated() {},
  /**  实例销毁之前调用  */
  beforeDestroy() {},
  /**  实例销毁后调用  */
  destroyed() {},
};
</script>

<style lang="scss">
.customerBasicFiles {
  overflow: hidden !important;
  font-size: 14px;
  color: #666666;
  /*height: calc(100vh - 23vh);*/
  height: 100%;
  .el-scrollbar {
    height: 100%;
    .el-scrollbar__wrap {
      overflow-x: hidden !important;
    }
  }
}
</style>
