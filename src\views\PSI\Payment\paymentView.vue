<template>
	<div class="paymentView content_body" v-loading="loading">
		<!-- 搜索 -->
		<div class="nav_header">
			<el-row>
				<el-col :span="24">
					<el-form :inline="true" size="small" @keyup.enter.native="handleSearchEvents" @submit.native.prevent>
						<el-form-item label="门店/仓库">
							<el-select size="small" @change="handleSearchEvents" @clear="handleSearchEvents" v-model="searchEntityID" filterable placeholder="请选择仓库/门店" clearable>
								<el-option v-for="item in warehouses" :key="item.ID" :label="item.EntityName" :value="item.ID"></el-option>
							</el-select>
						</el-form-item>

						<el-form-item label="单据类型">
							<el-select size="small" @change="handleSearchEvents" @clear="handleSearchEvents" v-model="searchBillType" placeholder="请选择单据类型" clearable>
								<el-option label="预付款单" :value="10"></el-option>
								<el-option label="退款单" :value="20"></el-option>
								<el-option label="要货付款单" :value="30"></el-option>
								<el-option label="退货付款单" :value="40"></el-option>
							</el-select>
						</el-form-item>

						<el-form-item label="收款类别">
							<el-select
								size="small"
								@change="handleSearchEvents"
								@clear="handleSearchEvents"
								v-model="searchPaymentCategory"
								placeholder="请选择收款类别"
								clearable
								filterable
								remote
								:remote-method="prepayCategory_list"
							>
								<el-option v-for="item in prepayCategoryList" :key="item.ID" :label="item.Name" :value="item.ID"></el-option>
							</el-select>
						</el-form-item>

						<el-form-item label="制单日期">
							<el-date-picker
								v-model="searchDateTime"
								unlink-panels
								type="daterange"
								range-separator="至"
								value-format="yyyy-MM-dd"
								start-placeholder="开始日期"
								end-placeholder="结束日期"
								:picker-options="pickerOptions"
								clearable
								@clear="handleSearchEvents"
								@change="handleSearchEvents"
							></el-date-picker>
						</el-form-item>
						<el-form-item>
							<el-button size="small" type="primary" @click="handleSearchEvents" v-prevent-click>搜索</el-button>

              <el-button v-if="isExport" size="small" type="primary" @click="paymentView_excel" :loading="execlLoading" v-prevent-click>导出</el-button>
						</el-form-item>
					</el-form>
				</el-col>
			</el-row>
		</div>

		<!-- 表格 -->
		<div>
			<el-table size="small" :data="tableData" tooltip-effect="light">
				<el-table-column prop="ID" label="单据编号"></el-table-column>
				<el-table-column prop="EntityName" label="门店/仓库"></el-table-column>
				<el-table-column prop="BillType" label="单据类型">
					<template slot-scope="scope">
						{{ billTypeFormat(scope.row.BillType) }}
					</template>
				</el-table-column>
				<el-table-column prop="Balance" label="本金金额">
					<template slot-scope="scope"> ￥{{ scope.row.Balance | toFixed | NumFormat }} </template>
				</el-table-column>
				<el-table-column prop="LargessBalance" label="赠金金额">
					<template slot-scope="scope"> ￥{{ scope.row.LargessBalance | toFixed | NumFormat }} </template>
				</el-table-column>

				<el-table-column prop="LargessBalance" label="合计金额">
					<template slot-scope="scope"> ￥{{ (scope.row.Balance + scope.row.LargessBalance) | toFixed | NumFormat }} </template>
				</el-table-column>

				<el-table-column prop="PaymentWay" label="付款方式">
					<template slot-scope="scope">
						{{ paymentWayFormat(scope.row.PaymentWay) }}
					</template>
				</el-table-column>

				<el-table-column prop="PaymentCategoryName" label="收款类别">
					<template slot-scope="scope">
						{{ scope.row.PaymentCategoryName }}
					</template>
				</el-table-column>
				<el-table-column prop="SourceBillType" label="来源单据类型">
					<template slot-scope="scope">
						{{ sourceBillTypeFormat(scope.row.SourceBillType) }}
					</template>
				</el-table-column>
				<el-table-column prop="SourceBillNo" label="来源单据号"> </el-table-column>
				<el-table-column prop="CreatedBy" label="操作人"> </el-table-column>
				<el-table-column prop="CreatedOn" label="制单时间"> 
					<template slot-scope="scope">
						{{scope.row.CreatedOn | dateFormat("YYYY-MM-DD HH:mm")}}
					</template>
				</el-table-column>

				<el-table-column label="操作" width="80px">
					<template slot-scope="scope">
						<el-button type="primary" size="small" @click="checkDetailClick(scope.row)">详情</el-button>
					</template>
				</el-table-column>
			</el-table>
		</div>
		<!-- 分页 -->
		<div class="pad_10 dis_flex flex_x_end" v-if="paginations.total > 0">
			<el-pagination background :current-page.sync="paginations.page" :layout="paginations.layout" :total="paginations.total" @current-change="currentChange"></el-pagination>
		</div>

		<!--  退款 -->
		<el-dialog title="收付款详情" :visible.sync="dialogVisible" width="880px">
			<el-form ref="infoForm" :model="infoForm" label-width="auto" size="small" @submit.native.prevent>
				<el-row>
					<el-col :span="8">
						<el-form-item label="订单编号：" prop="EntityName">{{ infoForm.ID }}</el-form-item>
					</el-col>
					<el-col :span="8">
						<el-form-item label="操作人：" prop="PaymentCategoryName">{{ infoForm.CreatedBy }}</el-form-item>
					</el-col>
					<el-col :span="8">
						<el-form-item label="制单时间：" prop="PaymentCategoryName">{{ infoForm.CreatedOn | dateFormat("YYYY-MM-DD HH:mm")}}</el-form-item>
					</el-col>
					<el-col :span="8">
						<el-form-item label="门店仓库：" prop="EntityName">{{ infoForm.EntityName }}</el-form-item>
					</el-col>
					<el-col :span="8">
						<el-form-item label="单据类型：" prop="EntityName">{{ billTypeFormat(infoForm.BillType) }}</el-form-item>
					</el-col>
					<el-col :span="8">
						<el-form-item label="收付款类别：" prop="PaymentCategoryName">{{ infoForm.PaymentCategoryName }}</el-form-item>
					</el-col>
					<el-col :span="8">
						<el-form-item label="付款方式：" prop="PaymentWay">{{ paymentWayFormat(infoForm.PaymentWay) }}</el-form-item>
					</el-col>
					<el-col :span="8">
						<el-form-item label="本金金额：" prop="Balance">￥{{ infoForm.Balance | toFixed | NumFormat }}</el-form-item>
					</el-col>
					<el-col :span="8">
						<el-form-item label="赠金金额：" prop="LargessBalance">￥{{ infoForm.LargessBalance | toFixed | NumFormat }}</el-form-item>
					</el-col>
					<el-col :span="8" v-if="infoForm.PaymentWay == '10'">
						<el-form-item label="回单号码：" prop="ReceiptNumber">{{ infoForm.ReceiptNumber }}</el-form-item>
					</el-col>
					<el-col :span="8" v-if="infoForm.PaymentWay == '10'">
						<el-form-item label="付款户名：" prop="PaymentAccountName">{{ infoForm.PaymentAccountName }}</el-form-item>
					</el-col>
					<el-col :span="8" v-if="infoForm.PaymentWay == '10'">
						<el-form-item label="备注信息：" prop="Remark">{{ infoForm.Remark }}</el-form-item>
					</el-col>
					<el-col :span="8" v-if="infoForm.PaymentWay == '30'">
						<el-form-item label="来源单据类型：" prop="SourceBillType">{{ infoForm.SourceBillType == "10" ? "要货单" : "" }}</el-form-item>
					</el-col>
					<el-col :span="8" v-if="infoForm.PaymentWay == '30'">
						<el-form-item label="来源单据号：" prop="SourceBillNo">{{ infoForm.SourceBillNo }}</el-form-item>
					</el-col>
				</el-row>
			</el-form>
			<div slot="footer" class="dialog-footer">
				<el-button @click="dialogVisible = false" size="small" v-prevent-click>取 消</el-button>
			</div>
		</el-dialog>
	</div>
</template>

<script>
	import API from "@/api/PSI/Payment/paymentView.js";
	import permission from "@/components/js/permission.js";
	export default {
		name: "paymentView",
		beforeRouteEnter(to, from, next) {
			next((vm) => {
				vm.isExport = permission.permission(to.meta.Permission, "PSI-Payment-PaymentView-Export");
			});
		},
		props: {},
		/**  引入的组件  */
		components: {},
		/**  Vue 实例的数据对象**/
		data() {
			return {
				isExport: false,
				loading: false,
				dialogVisible: false,
				execlLoading: false,
				searchEntityID: "",
				searchBillType: "",
				searchDateTime: "",
				searchPaymentCategory: "",
				tableData: [],
				paginations: {
					page: 1, // 当前位于哪页
					total: 0, // 总数
					page_size: 10, // 1页显示多少条
					layout: "total, prev, pager, next, jumper", // 翻页属性
				}, //需要给分页组件传的信息
				infoForm: "",
				pickerOptions: {
					disabledDate() {
						// return time.getTime() > Date.now() ? true : false;
					},
				},
				warehouses: [],
				prepayCategoryList: [],
			};
		},
		/**计算属性  */
		computed: {},
		/**  方法集合  */
		methods: {
			/**   搜索 */
			handleSearchEvents() {
				let that = this;
				that.paginations.page = 1;
				that.detail_payment();
			},
			/**    */
			currentChange(page) {
				let that = this;
				that.paginations.page = page;
				that.detail_payment();
			},
			/**   查看详情 */
			checkDetailClick(row) {
				let that = this;
				that.infoForm = row;
				that.dialogVisible = true;
			},
			/**    */
			billTypeFormat(BillType) {
				switch (BillType) {
					case "10":
						return "预付款单";
					case "20":
						return "退款单";
					case "30":
						return "要货付款单";
					case "40":
						return "退货付款单";
					default:
						return "";
				}
			},
			/**    */
			// balanceTitleFormat(BillType) {
			//   switch (BillType) {
			//     case "10":
			//       return "充值金额：";
			//     case "20":
			//       return "退款金额：";
			//     case "30":
			//       return "预付金额：";
			//     default:
			//       return "";
			//   }
			// },
			// /**    */
			// largessBalanceTitleFormat(BillType) {
			//   switch (BillType) {
			//     case "10":
			//       return "充值赠额：";
			//     case "20":
			//       return "退款赠额：";
			//     case "30":
			//       return "预付赠额：";
			//     default:
			//       return "";
			//   }
			// },
			/**    */
			paymentWayFormat(PaymentWay) {
				switch (PaymentWay) {
					case "10":
						return "离线转账";
					case "20":
						return "余额支付";
					default:
						return "";
				}
			},
			sourceBillTypeFormat(sourceBillType) {
				switch (sourceBillType) {
					case "10":
						return "门店要货单";
					case "20":
						return "门店退货单";
					default:
						return "";
				}
			},
			/**  **************************  */
			/**    */
			async detail_payment() {
				let that = this;
				let params = {
					PageNum: that.paginations.page, //分页
					EntityID: that.searchEntityID, //仓库编号
					BillType: that.searchBillType, //10：预付余额账户付款，20：预付余额账户退款，30：要货预付余额账户付款
					StartDate: that.searchDateTime ? that.searchDateTime[0] : "", //开始时间
					EndDate: that.searchDateTime ? that.searchDateTime[1] : "", //结束时间
					CategoryID: that.searchPaymentCategory,
					// that.searchForm.DateTime == null ? "" : that.searchForm.DateTime[0],
				};
				that.loading = true;
				let res = await API.detail_payment(params);
				if (res.StateCode == 200) {
					that.tableData = res.List;
					that.paginations.total = res.Total;
				} else {
					that.$message.error(res.Message);
				}
				that.loading = false;
			},
			/**    */
			async detail_allWarehouse() {
				let that = this;
				let params = {};
				let res = await API.detail_allWarehouse(params);
				if (res.StateCode == 200) {
					that.warehouses = res.Data;
				} else {
					that.$message.error(res.Message);
				}
			},

			/**  收款类别 列表请求  */
			async prepayCategory_list(val) {
				let that = this;
				let params = {
					Name: val, //类别名称
					Active: "", //有效性
					PageNum: 1,
				};
				that.loading = true;
				let res = await API.prepayCategory_list(params);
				if (res.StateCode == 200) {
					that.prepayCategoryList = res.List;
				} else {
					that.$message.error(res.Message);
				}
				that.loading = false;
			},
			/**    */
			async paymentView_excel() {
				let that = this;
				that.execlLoading = true;
				let params = {
					PageNum: that.paginations.page, //分页
					EntityID: that.searchEntityID, //仓库编号
					BillType: that.searchBillType, //10：预付余额账户付款，20：预付余额账户退款，30：要货预付余额账户付款
					StartDate: that.searchDateTime ? that.searchDateTime[0] : "", //开始时间
					EndDate: that.searchDateTime ? that.searchDateTime[1] : "", //结束时间
					CategoryID: that.searchPaymentCategory,
				};
				let res = await API.paymentView_excel(params);

				this.$message.success({ message: "正在导出", duration: "4000" });
				const link = document.createElement("a");
				let blob = new Blob([res], { type: "application/octet-stream" });
				link.style.display = "none";
				link.href = URL.createObjectURL(blob);
				link.download = "收付款明细.xlsx"; //下载的文件名
				document.body.appendChild(link);
				link.click();
				document.body.removeChild(link);

				that.execlLoading = false;
			},
		},
		/** 监听数据变化   */
		watch: {},
		/** 创建实例之前执行函数   */
		beforeCreate() {},
		/**  实例创建完成之后  */
		created() {
			let that = this;
			that.detail_payment();
			that.detail_allWarehouse();
			that.prepayCategory_list();
		},
		/**  在挂载开始之前被调用  */
		beforeMount() {},
		/**  实例被挂载后调用  */
		mounted() {},
		/**  数据更新 之前 调用  */
		beforeUpdate() {},
		/** 数据更新 完成 调用   */
		updated() {},
		/**  实例销毁之前调用  */
		beforeDestroy() {},
		/**  实例销毁后调用  */
		destroyed() {},
	};
</script>

<style lang="scss">
	.paymentView {
	}
</style>
