<template>
  <div class="servicerConfig content_body" v-loading="loading">
    <div class="nav_header">
      <el-row>
        <el-col :span="20">
          <el-form
            :inline="true"
            size="small"
            @keyup.enter.native="handleSearch"
          >
            <el-form-item label="服务人员">
              <el-input
                @clear="handleSearch"
                v-model="Name"
                clearable
                placeholder="输入服务人员搜索"
              ></el-input>
            </el-form-item>
            <el-form-item label="是否新增时展示">
              <el-select
                @change="handleSearch"
                @clear="handleSearch"
                v-model="AddWhetherToShow"
                placeholder="请选择是否新增时展示"
                clearable
              >
                <el-option label="是" :value="true"></el-option>
                <el-option label="否" :value="false"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="有效性">
              <el-select
                @change="handleSearch"
                @clear="handleSearch"
                v-model="Active"
                placeholder="请选择有效性"
                clearable
              >
                <el-option label="有效" :value="true"></el-option>
                <el-option label="无效" :value="false"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleSearch">搜索</el-button>
            </el-form-item>
          </el-form>
        </el-col>
        <el-col :span="4" class="text_right">
          <el-button type="primary" @click="showAddDialog" size="small"
            >新增</el-button
          >
        </el-col>
      </el-row>
    </div>
    <div class="handlerlist">
      <el-table :data="tableList" size="small" tooltip-effect="light">
        <el-table-column prop="Name" label="服务人员"></el-table-column>
        <el-table-column label="组织范围">
          <template slot-scope="scope">
            {{scope.row.EntityRange == '10'?'当前部门':scope.row.EntityRange == '20'?'跨部门':
            scope.row.EntityRange == '30'?'全组织':''}}
          </template>
        </el-table-column>
        <el-table-column label="是否新增时展示">
          <template slot-scope="scope">{{
            scope.row.AddWhetherToShow ? "是" : "否"
          }}</template>
        </el-table-column>
        <el-table-column label="移动" min-width="180px">
          <template slot-scope="scope">
            <el-button
              size="small"
              type="primary"
              circle
              icon="el-icon-upload2"
              @click="upOneClick(scope.row, scope.$index)"
              :disabled="scope.$index == 0"
            ></el-button>
            <el-button
              size="small"
              type="primary"
              circle
              icon="el-icon-top"
              @click="upClick(scope.row, scope.$index)"
              :disabled="scope.$index == 0"
            ></el-button>
            <el-button
              size="small"
              type="primary"
              circle
              icon="el-icon-bottom"
              @click="downClick(scope.row, scope.$index)"
              :disabled="scope.$index == tableList.length - 1"
            ></el-button>
            <el-button
              size="small"
              type="primary"
              circle
              icon="el-icon-download"
              @click="downOneClick(scope.row, scope.$index)"
              :disabled="scope.$index == tableList.length - 1"
            ></el-button>
          </template>
        </el-table-column>
        <el-table-column label="有效性">
          <template slot-scope="scope">{{
            scope.row.Active ? "有效" : "无效"
          }}</template>
        </el-table-column>
        <el-table-column label="操作" width="80">
          <template slot-scope="scope">
            <el-button
              type="primary"
              size="small"
              @click="showEditDialog(scope.row)"
              >编辑</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </div>
    <!--增加、编辑弹出框-->
    <el-dialog :title="isEdit?'编辑服务人员':'新增服务人员'" :visible.sync="dialogVisible" width="840px">
      <el-tabs v-model="activeName" class="editTabs">
        <el-tab-pane label="基本信息" name="first">
          <el-form
            :model="ruleFormAdd"
            :rules="rules_add"
            ref="ruleFormAdd"
            label-width="140px"
            class="demo-ruleForm"
            size="small"
            @submit.native.prevent
          >
            <el-form-item label="服务人员" prop="Name">
              <span slot="label"> 服务人员 </span>
              <el-input
                style="width: 100%"
                clearable
                v-model="ruleFormAdd.Name"
                placeholder="请输入服务人员"
              ></el-input>
            </el-form-item>
             <el-form-item label="组织范围" prop="EntityRange">
              <el-select v-model="ruleFormAdd.EntityRange" clearable filterable placeholder="请选择组织范围" :default-first-option="true">
                <el-option label="当前部门" value="10"></el-option>
                <el-option label="跨部门" value="20"></el-option>
                <el-option label="全组织" value="30"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="新增客户时展示" prop="AddWhetherToShow">
              <el-radio-group v-model="ruleFormAdd.AddWhetherToShow">
                <el-radio :label="true">是</el-radio>
                <el-radio :label="false">否</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item
              label="有效性"
              prop="Active"
              v-if="isEdit"
            >
              <el-radio-group v-model="ruleFormAdd.Active">
                <el-radio :label="true">有效</el-radio>
                <el-radio :label="false">无效</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-form>
        </el-tab-pane>
        <el-tab-pane label="适用职务" name="second">
          <el-row :gutter="20" class="pad_10_0">
            <el-col :span="10">
              <el-input
                placeholder="输入职务名称搜索"
                size="small"
                v-model="filterJobName"
                clearable
              ></el-input>
            </el-col>
            <el-col :span="14">
              <el-button
                type="primary"
                @click="clickAddApplicableDuty"
                size="small"
                >配置适用职务
              </el-button>
            </el-col>
          </el-row>
          <el-table
            size="small"
            :data="
              JobTypeList.filter(
                (data) =>
                  !filterJobName ||
                  data.JobName.toLowerCase().includes(
                    filterJobName.toLowerCase()
                  )
              )
            "
            max-height="450"
          >
            <el-table-column label="职务名称" sortable>
              <template slot-scope="scope">
                <span>{{ scope.row.JobName }}</span>
              </template>
            </el-table-column>
            <el-table-column label="职务描述">
              <template slot-scope="scope">
                <span>{{ scope.row.JobDescription }}</span>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="80">
              <template slot-scope="scope">
                <el-button
                  type="danger"
                  size="mini"
                  @click="deleteSelectedJobType(scope.row)"
                  >删除</el-button
                >
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
      </el-tabs>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false" size="small" v-prevent-click>取 消</el-button>
        <el-button
          type="primary"
          size="small"
          @click="submitFormAdd('ruleFormAdd')"
          :loading="modalLoading"
          v-prevent-click
          >保 存</el-button
        >
      </span>
    </el-dialog>
    <!--添加适用职务弹出框-->
    <el-dialog :visible.sync="addApplicableDutyDialog" width="700px">
      <div slot="title">
        <span>配置适用职务</span>
      </div>
      <el-row>
        <el-col :span="10" class="pad_10_0">
          <el-input
            size="small"
            v-model="JobTypeName"
            clearable
            placeholder="输入职务名称搜索"
          ></el-input>
        </el-col>
      </el-row>
      <el-table
        size="small"
        :data="
          applicableDutyList.filter(
            (data) =>
              !JobTypeName ||
              data.JobName.toLowerCase().includes(JobTypeName.toLowerCase())
          )
        "
        max-height="480px"
        @selection-change="getSelectedJobType"
        ref="multipleTable"
      >
        <el-table-column type="selection" width="55"></el-table-column>
        <el-table-column
          prop="JobName"
          label="职务名称"
          sortable
          column-key="JobName"
        ></el-table-column>
        <el-table-column
          prop="JobDescription"
          label="职务描述"
        ></el-table-column>
      </el-table>
      <span slot="footer" class="dialog-footer">
        <el-button @click="addApplicableDutyDialog = false" size="small" v-prevent-click
          >取 消</el-button
        >
        <el-button type="primary" size="small" @click="submitFormApplicableDuty" v-prevent-click
          >确 定</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>

<script>
import APIProduct from "@/api/iBeauty/HanderCommission/productSaleHandler";
import API from "@/api/CRM/Servicer/servicerConfig";
var Enumerable = require("linq");

export default {
  name: "servicerConfig",

  // beforeRouteEnter(to, from, next) {
  //  next((vm) => {
  //      vm.isDelete = vm.$permission.permission(
  //       to.meta.Permission,
  //       funName
  //     );
  //   });
  // },
  props: {},
  /**  引入的组件  */
  components: {},
  /**  Vue 实例的数据对象**/
  data() {
    return {
      loading: false,
      modalLoading: false,
      dialogVisible: false, // 新增、编辑弹出层
      activeName: "first", // 当前标签页
      Name: "",
      Active: true, // 有效性
      AddWhetherToShow: "", //是否新增是展示
      ID: "", // 服务人员ID
      tableList: [],
      filterJobName: "", // 职务搜索框数据
      applyStoreOptions: [], // 适用组织数据
      addApplicableDutyDialog: false, // 适用职务弹出框状态
      JobTypeName: "", // 适用职务搜索框数据
      dutyList: [], // 适用职务数据
      selectedJobTypeList: [], // 选中的适用职务数据
      applicableDutyList: [], // 适用职务弹出框表格数据
      JobTypeList: [],
      isEdit: false, // 判断为新增还是编辑
      ruleFormAdd: {
        Name: "", // 服务人员
        Active: "", // 有效性
        EntityRange: "", // 组织范围 
        AddWhetherToShow: true, //是否新增是展示
        JobTypeList: [], // 适用职务
      }, // 增加、编辑表单数据
      rules_add: {
        Name: [
          { required: true, message: "请输入服务人员", trigger: "change" },
        ],
        EntityRange: [
          { required: true, message: "请选择组织范围", trigger: "change" },
        ],
        AddWhetherToShow: [
          { required: true, message: "请选择新增顾客时是否展示", trigger: "change" },
        ],
        Active: [
          { required: true, message: "请选择有效性", trigger: "change" },
        ],
      }, // 增加、编辑表单验证
    };
  },
  /**计算属性  */
  computed: {},
  /**  方法集合  */
  methods: {
    /* 搜索 */
    handleSearch() {
      let that = this;
      that.getServiceList()
    },
    /* 新增 */
    showAddDialog() {
      var that = this;
      that.JobTypeName = "";
      that.JobTypeList = [];
      that.ruleFormAdd = {
        Name: "", // 服务人员
        Active: "", // 有效性
        AddWhetherToShow: true, //是否新增是展示
        JobTypeList: [], // 适用职务
      };
      that.isEdit = false;
      that.activeName = "first";
      that.filterJobName = "";
      that.dialogVisible = true;
    },
    /* 编辑 */
    showEditDialog(row) {
      var that = this;
      that.ID = row.ID;
      that.ruleFormAdd = Object.assign({}, row);
      that.JobTypeList = Object.assign([], row.JobTypeList);
      that.isEdit = true;
      that.activeName = "first";
      that.filterJobName = "";
      that.dialogVisible = true;
    },
    /* 获取服务人员列表 */
    getServiceList() {
      let that = this;
      that.loading = true;
      let params = {
        Name: that.Name,
        AddWhetherToShow: that.AddWhetherToShow, //新增时客户展示
        Active: that.Active,
      };
      API.getServiceList(params).then((res) => {
          if(res.StateCode == 200){
            that.tableList = res.Data
          }else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
      }).finally(function () {
          that.loading = false;
      });
    },
    /* 添加、编辑表单保存事件 */
    submitFormAdd(formName) {
      let that = this;
      that.$refs[formName].validate((valid) => {
        if (valid) {
          var defaultCheckedKeys = Enumerable.from(that.JobTypeList)
            .select((val) => val.JobTypeID)
            .toArray();
           that.ruleFormAdd.JobTypeList = defaultCheckedKeys.map(item=>{
               return {JobTypeID: item}
           })
            if (that.ruleFormAdd.JobTypeList.length > 0) {
                if(!that.isEdit){
                that.createService()
                }else{
                that.updateService()
              }
            }else {
              that.$message.error("请配置适用职务");
            }
        }
      });
    },
    /* 新增 */
    createService(){
      let that = this
      that.modalLoading = true;
      let params = that.ruleFormAdd;
      params.ID = that.ID;
      params.Active = true;
      API.createService(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.$message.success("新增成功");
            that.dialogVisible = false;
            that.getServiceList();
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.modalLoading = false;
        });
    },
    /* 更新 */
    updateService(){
      let that = this
      that.modalLoading = true;
      let params = that.ruleFormAdd;
      params.ID = that.ID;
      API.updateService(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.$message.success("更新成功");
            that.dialogVisible = false;
            that.getServiceList();
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.modalLoading = false;
        });
    },
    /* 移动首部 */
    upOneClick: function (row) {
      let that = this;
      that.movaClick(row.ID, "");
    },
    /* 移动尾部 */
    downOneClick: function (row, index) {
      let that = this;
      let beforeId = "";
      let tableLength = that.tableList.length
      if (index < tableLength - 1) {
         beforeId = that.tableList[tableLength - 1].ID;
      }
      that.movaClick(row.ID, beforeId);
    },
    /* 向上 */
    upClick: function (row, index) {
      let that = this;
      let beforeId = "";
      if(index > 1){
       beforeId = that.tableList[index - 2].ID; 
      }
      that.movaClick(row.ID, beforeId);
    },
    /* 向下 */
    downClick: function (row, index) {
      let that = this;
      let beforeId = "";
       if (index + 1 != that.tableList.length) {
        beforeId = that.tableList[index + 1].ID;
      }
      that.movaClick(row.ID, beforeId);
    },
    /* 移动顺序 */
    movaClick: function (moveId, beforeId) {
      var that = this;
      that.loading = true;
      var params = {
        MoveID: moveId,
        BeforeID: beforeId,
      };
      API.moveServicer(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.$message.success("移动成功");
            that.getServiceList()
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },
    /* 获取职务列表 */
    getJobTypeAll: function () {
      var that = this;
      that.loading = true;
      var params = {
        JobTypeName: that.JobTypeName,
      };
      APIProduct.getJobTypeAll(params)
        .then((res) => {
          if (res.StateCode == 200) {
            res.Data.forEach((item) => {
              item.JobTypeID = item.ID;
              item.JobTypeName = item.JobName;
            });
            that.applicableDutyList = res.Data;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },
    /* 获取所选中的职务列表 */
    getSelectedJobType(list) {
      var that = this;
      that.selectedJobTypeList = list;
    },
    /* 添加适用职务点击事件 */
    clickAddApplicableDuty() {
      let that = this;
      that.JobTypeName = "";
      that.addApplicableDutyDialog = true;
      this.$nextTick(() => {
        that.$nextTick(() => {
          that.applicableDutyList.forEach((val) => {
            that.$refs.multipleTable.toggleRowSelection(val, false);
          });
        });
        if (that.JobTypeList.length > 0) {
          var defaultCheckedKeys = Enumerable.from(that.JobTypeList)
            .select((val) => val.JobTypeID)
            .toArray();
          defaultCheckedKeys.forEach((item) => {
            that.applicableDutyList.forEach((val) => {
              if (item == val.JobTypeID) {
                that.$nextTick(() => {
                  that.$refs.multipleTable.toggleRowSelection(val);
                });
              }
            });
          });
        }
      });
    },
    /* 添加适用职务表单保存事件 */
    submitFormApplicableDuty() {
      var that = this;
      that.JobTypeList = Object.assign([], that.selectedJobTypeList);
      that.addApplicableDutyDialog = false;
    },
    /* 删除所选职务 */
    deleteSelectedJobType(val) {
      var that = this;
      that.JobTypeList.splice(
        that.JobTypeList.findIndex((p) => p.JobTypeID == val.JobTypeID),
        1
      );
    },
  },
  /** 监听数据变化   */
  watch: {
    JobTypeName() {
      var that = this;
      that.$nextTick(() => {
        that.applicableDutyList.forEach((val) => {
          that.$refs.multipleTable.toggleRowSelection(val, false);
        });
      });
      if (that.selectedJobTypeList.length > 0) {
        that.selectedJobTypeList.forEach((item) => {
          that.applicableDutyList.forEach((val) => {
            if (item.JobTypeID == val.JobTypeID) {
              that.$nextTick(() => {
                that.$refs.multipleTable.toggleRowSelection(val);
              });
            }
          });
        });
      }
    },
  },
  /** 创建实例之前执行函数   */
  beforeCreate() {},
  /**  实例创建完成之后  */
  created() {},
  /**  在挂载开始之前被调用  */
  beforeMount() {},
  /**  实例被挂载后调用  */
  mounted() {
    let that = this;
    // this.isDelete = this.$permission.permission(
    //   this.route.meta.Permission,
    //    funName
    // );
    that.getJobTypeAll();
    that.getServiceList()
  },
  /**  数据更新 之前 调用  */
  beforeUpdate() {},
  /** 数据更新 完成 调用   */
  updated() {},
  /**  实例销毁之前调用  */
  beforeDestroy() {},
  /**  实例销毁后调用  */
  destroyed() {},
};
</script>

<style lang="scss">
.servicerConfig {
}
</style>
