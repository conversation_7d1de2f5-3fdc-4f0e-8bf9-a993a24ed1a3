<template>
  <div class="SealingAccount content_body">
    <!-- 头部 -->
    <div class="nav_header nav_header_1">
      <el-row>
        <el-col :span="22">
          <el-form :inline="true" size="small" @submit.native.prevent @keyup.enter.native="handleSearch">
            <el-form-item label="名称">
              <el-input v-model="Name" placeholder="请输入关账名称" @change="handleSearch" clearable></el-input>
            </el-form-item>
            <el-form-item label="适用门店">
              <el-select v-model="EntityID" placeholder="适用门店" clearable filterable :default-first-option="true"
                size="small" @change="handleSearch">
                <el-option v-for="item in entitys" :key="item.ID" :label="item.Name" :value="item.ID">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleSearch" size="small" v-prevent-click>搜索</el-button>
            </el-form-item>
          </el-form>
        </el-col>
        <el-col :span="2" class="text_right" align="right">
          <el-button type="primary" size="small" v-prevent-click @click="addSealingAccountClick">
            新增 </el-button>
        </el-col>
      </el-row>
    </div>

    <el-table size="small" :data="tableData">
      <el-table-column label="关账名称" prop="Name"></el-table-column>
      <el-table-column label="账期时间">
        <template slot-scope='scope'>
          <span>{{ scope.row.StartAccountPeriod == '0' ? '上月' : '本月' }}</span>
          <span class="text-bold marlt_5 marrt_5">{{ scope.row.StartAccountDate }}号</span>
          <span>至</span>
          <span>{{ scope.row.EndAccountPeriod == '0' ? '上月' : '本月' }}</span>
          <span class="text-bold marlt_5 marrt_5">{{ scope.row.EndAccountDate }}号</span>
        </template>
      </el-table-column>
      <el-table-column label="关账日期">
        <template slot-scope='scope'>
          每月 {{ scope.row.CloseAccountDate }} 号
        </template>
      </el-table-column>
      <el-table-column label="限制补单">
        <template slot-scope='scope'>
          {{ scope.row.ReplacementOrder ? '限制' : '不限制' }}
        </template>
      </el-table-column>
      <el-table-column label="限制取消订单">
        <template slot-scope='scope'>
          {{ scope.row.CancelOrder ? '限制' : '不限制' }}
        </template>
      </el-table-column>
      <el-table-column label="限制修改订单时间">
        <template slot-scope='scope'>
          {{ scope.row.ModifyBillDate ? '限制' : '不限制' }}
        </template>
      </el-table-column>
      <el-table-column label="限制修改订单支付方式">
        <template slot-scope='scope'>
          {{ scope.row.ModifyBillPayMethod ? '限制' : '不限制' }}
        </template>
      </el-table-column>
      <el-table-column label="限制修改门店业绩">
        <template slot-scope='scope'>
          {{ scope.row.ModifyEntityPerformance ? '限制' : '不限制' }}
        </template>
      </el-table-column>
      <el-table-column label="限制修改员工业绩提成">
        <template slot-scope='scope'>
          {{ scope.row.ModifyEmployeePerformanceCommission ? '限制' : '不限制' }}
        </template>
      </el-table-column>
      <el-table-column label="限制日常收支">
        <template slot-scope='scope'>
          {{ scope.row.IncomeAndSpending ? '限制' : '不限制' }}
        </template>
      </el-table-column>
      <el-table-column label="操作" width="150">
        <template slot-scope="scope">
          <el-button type="primary" size="small" @click="showEditSealingAccount(scope.row)">编辑</el-button>
          <el-button type="danger" size="small" @click="deleteSealingAccount(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <div class="pad_15 text_right">
      <el-pagination background v-if="paginations.total > 0" @current-change="handleCurrentChange"
        :current-page.sync="paginations.page" :page-size="paginations.page_size" :layout="paginations.layout"
        :total="paginations.total"></el-pagination>
    </div>

    <el-dialog :visible.sync="showSealingAccount" :title="isAdd ? '新增关账设置' : '编辑关账设置'" width="900px"
      @close="closeSealingAccount">
      <el-tabs v-model="activeName">
        <el-tab-pane label="账期设置" name="1">
          <el-form ref="sealingAccountRef" size="small" label-width="90px" :model="addForm" :rules="rules">
            <el-form-item label="关账名称" prop="Name">
              <el-input v-model="addForm.Name" placeholder="请输入关账名称"></el-input>
            </el-form-item>
            <el-form-item label="账期时间">
              <el-row>
                <el-col :span="3">
                  <el-form-item prop="StartAccountPeriod" class="custom-form-item-bottom">
                    <el-select v-model="addForm.StartAccountPeriod" class="">
                      <el-option v-if="addForm.EndAccountPeriod == 1" :value="1" label="本月"></el-option>
                      <el-option :value="0" label="上月"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>

                <el-col :span="3">
                  <el-form-item prop="StartAccountDate" class="custom-form-item-bottom">
                    <el-select v-model="addForm.StartAccountDate" class="marlt_10 ">
                      <el-option v-for="item in 31" :key="item" :value="item" :label="item + '号'"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="2" align="center">
                  <span class="padlt_15 padrt_15">至</span>
                </el-col>

                <el-col :span="3">
                  <el-form-item prop="EndAccountPeriod" class="custom-form-item-bottom">
                    <el-select v-model="addForm.EndAccountPeriod" class=" ">
                      <el-option :value="1" label="本月"></el-option>
                      <el-option v-if="addForm.StartAccountPeriod == 0" :value="0" label="上月"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>

                <el-col :span="3">
                  <el-form-item prop="EndAccountDate" class="custom-form-item-bottom">
                    <el-select v-model="addForm.EndAccountDate" class="marlt_10 ">
                      <el-option v-for="item in 31" :key="item" :value="item" :label="item + '号'"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="24">
                  <div class="sealingTips">注：当前月份最大日期小于设定日期时，按月份最大日期计算。</div>
                </el-col>
              </el-row>

            </el-form-item>
            <el-form-item label="关账时间" prop="CloseAccountDate">
              <span>每月</span>
              <el-select v-model="addForm.CloseAccountDate" class="marlt_10 custom-select-style">
                <el-option v-for="item in 31" :key="item" :value="item" :label="item + '号'"></el-option>
              </el-select>
              <div class="sealingTips">注：过了关账时间后，门店员工不允许修改上个账期的数据。</div>

            </el-form-item>
            <el-form-item label="限制功能" prop="Limits">
              <el-checkbox-group v-model="addForm.Limits" class="dis_flex flex_dir_column">
                <el-checkbox label="ReplacementOrder">补单</el-checkbox>
                <el-checkbox label="CancelOrder">取消订单</el-checkbox>
                <el-checkbox label="ModifyBillDate">修改订单时间</el-checkbox>
                <el-checkbox label="ModifyBillPayMethod">订单支付方式</el-checkbox>
                <el-checkbox label="ModifyEntityPerformance">改门店业绩</el-checkbox>
                <el-checkbox label="ModifyEmployeePerformanceCommission">修改员工业绩提成</el-checkbox>
                <el-checkbox label="IncomeAndSpending">日常收支</el-checkbox>
              </el-checkbox-group>
            </el-form-item>
          </el-form>

        </el-tab-pane>
        <el-tab-pane label="不受限职务" name="2">
          <el-row :gutter="20" class="pad_10_0">
            <el-col :span="10">
              <el-input placeholder="输入职务名称搜索" size="small" v-model="filterJobName" clearable></el-input>
            </el-col>
            <el-col :span="14">
              <el-button type="primary" @click="addNotLimitJobType" size="small">配置不受限职务</el-button>
            </el-col>
          </el-row>

          <el-table size="small"
            :data="notLimitJobTypeList.filter((data) => !filterJobName || data.JobTypeName.toLowerCase().includes(filterJobName.toLowerCase()))"
            max-height="450">
            <el-table-column label="职务名称" sortable>
              <template slot-scope="scope">
                <span>{{ scope.row.JobTypeName }}</span>
              </template>
            </el-table-column>
            <el-table-column label="职务描述">
              <template slot-scope="scope">
                <span>{{ scope.row.JobDescription }}</span>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="80">
              <template slot-scope="scope">
                <el-button type="danger" size="mini" @click="deleteSelectedJobType(scope.row)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
        <el-tab-pane label="适用门店" name="3">

          <el-scrollbar class="el-scrollbar_height">
            <el-tree ref="treeEntitys" :expand-on-click-node="false" :check-on-click-node="true" :check-strictly="true"
              :data="sealingAccountEntitys" show-checkbox node-key="ID" :default-checked-keys="defaultCheckedKeys"
              :default-expanded-keys="defaultExpandedKeys" :props="defaultProps">
              <span slot-scope="{ data}">
                <span>{{ data.EntityName }}</span>
                <el-tag v-if="data.IsStore" class="marlt_5" size="mini">门店</el-tag>
              </span>
            </el-tree>
          </el-scrollbar>


        </el-tab-pane>
      </el-tabs>
      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="showSealingAccount = false" v-prevent-click>取 消</el-button>
        <el-button type="primary" size="small" @click="addSubmitSealingAccountClick" :loading="saveLoading"
          v-prevent-click>保 存
        </el-button>
      </div>
    </el-dialog>

    <!--添加适用职务弹出框-->
    <el-dialog :visible.sync="showJobTypeList" width="700px">
      <div slot="title">
        <span>配置适用职务</span>
      </div>
      <el-row>
        <el-col :span="10" class="pad_10_0">
          <el-input size="small" v-model="JobTypeName" clearable placeholder="输入职务名称搜索"></el-input>
        </el-col>
      </el-row>
      <el-table size="small"
        :data="jobTypeList.filter((data) => !JobTypeName || data.JobName.toLowerCase().includes(JobTypeName.toLowerCase()))"
        max-height="480px" @selection-change="getSelectedJobType" ref="jobTypeMultipleTable">
        <el-table-column type="selection" width="55"></el-table-column>
        <el-table-column prop="JobName" label="职务名称" sortable column-key="JobName"></el-table-column>
        <el-table-column prop="JobDescription" label="职务描述"></el-table-column>
      </el-table>
      <span slot="footer" class="dialog-footer">
        <el-button @click="showJobTypeList = false" v-prevent-click size="small">取 消</el-button>
        <el-button type="primary" size="small" @click="confirmNotLimitJobTypeListClick" v-prevent-click>确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import API from "@/api/KHS/Setting/sealingAccount.js";
export default {
  name: 'SealingAccount',
  props: {},
  /** 监听数据变化   */
  watch: {},
  /**  引入的组件  */
  components: {},
  /**  Vue 实例的数据对象**/
  data() {
    return {
      saveLoading: false,
      showJobTypeList: false,
      showSealingAccount: false,
      isAdd: true,
      switchvalue: false,
      tableData: [],
      entitys: [],
      jobTypeList: [],
      notLimitJobTypeList: [],
      notLimitselectedJobType: [],
      activeName: '1',
      JobTypeName: "",
      filterJobName: "",
      EntityID: "",
      Name: "",
      paginations: {
        page: 1, // 当前位于哪页
        total: 0, // 总数
        page_size: 10, // 1页显示多少条
        layout: "total, prev, pager, next,jumper", // 翻页属性
      },
      addForm: {
        Name: "",
        StartAccountPeriod: 0, //账期时间开始周期（0：上月：1：本月）
        StartAccountDate: 1, //账期时间开始日期（1-31）
        EndAccountPeriod: 0, //账期时间结束周期（0：上月：1：本月）
        EndAccountDate: 1, //账期时间结算日期（1-31）
        CloseAccountDate: 1, //关账日期（每月[1-31]）
        ReplacementOrder: false, //是否限制补单（false：不限制、true：限制）
        CancelOrder: false, //是否限制取消订单（false：不限制、true：限制）
        ModifyBillDate: false, //是否限制修改订单时间（false：不限制、true：限制）
        ModifyBillPayMethod: false, //是否限制修改订单支付方式（false：不限制、true：限制）
        ModifyEntityPerformance: false, //是否限制修改门店业绩（false：不限制、true：限制）
        ModifyEmployeePerformanceCommission: false, //是否限制修改员工业绩提成（false：不限制、true：限制）
        IncomeAndSpending: false, //是否限制日常收支（false：不限制、true：限制）
        Limits: ["ReplacementOrder"],
        JobType: [], //职务集合
        Entity: [], //门店集合
      },
      rules: {
        Name: [{ require: true, message: '请输入名称', trigger: ['change', 'blur'] }],
        StartAccountPeriod: [
          { required: true, message: '请选择账期时间', trigger: ['change', 'blur'] }
        ],
        StartAccountDate: [
          { required: true, message: '请选择账期时间', trigger: ['change', 'blur'] }
        ],
        EndAccountPeriod: [
          { required: true, message: '请选择账期时间', trigger: ['change', 'blur'] }
        ],
        EndAccountDate: [
          { required: true, message: '请选择账期时间', trigger: ['change', 'blur'] }
        ],
        CloseAccountDate: [
          { required: true, message: '请选择关账时间', trigger: ['change', 'blur'] }
        ],
        Limits: [{ required: true, message: '请至少选择一项限制功能', trigger: ['change', 'blur'] }]
      },
      sealingAccountEntitys: [],
      defaultCheckedKeys: [],
      defaultExpandedKeys: [1],
      defaultProps: {
        children: "Child",
        label: "EntityName",
        disabled: (data) => {
          if (this.defaultCheckedKeys.some(i => i == data.ID) && !this.isAdd) {
            return false;
          }
          return !data.IsCanChoose || !data.IsStore;
        },
      }
    }
  },
  /**计算属性  */
  computed: {
  },
  /**  方法集合  */
  methods: {
    /**    */
    handleSearch() {
      let that = this;
      that.paginations.page = 1;
      that.sealingAccount_all();
    },
    /**    */
    handleCurrentChange(page) {
      let that = this;
      that.paginations.page = page;
      that.sealingAccount_all();
    },
    /**    */
    closeSealingAccount() {
      let that = this;
      that.$nextTick(() => {
        that.$refs.treeEntitys.setCheckedKeys([])
      });
    },
    /**    */
    showEditSealingAccount(row) {
      let that = this;
      that.notLimitJobTypeList = [];
      that.defaultCheckedKeys = row.Entity;
      that.notLimitJobTypeList = row.JobType.map(i => {
        i.ID = i.JobTypeID;
        i.Name = i.JobTypeName;
        return i;
      });
      let LimitKeys = ["ReplacementOrder", "CancelOrder", "ModifyBillDate", "ModifyBillPayMethod", "ModifyEntityPerformance", "ModifyEmployeePerformanceCommission", "IncomeAndSpending"];
      let Limits = LimitKeys.filter(i => row[i]);

      that.addForm = {
        ID: row.ID,
        Name: row.Name,
        StartAccountPeriod: row.StartAccountPeriod, //账期时间开始周期（0：上月：1：本月）
        StartAccountDate: row.StartAccountDate, //账期时间开始日期（1-31）
        EndAccountPeriod: row.EndAccountPeriod, //账期时间结束周期（0：上月：1：本月）
        EndAccountDate: row.EndAccountDate, //账期时间结算日期（1-31）
        CloseAccountDate: row.CloseAccountDate, //关账日期（每月[1-31]）
        ReplacementOrder: false, //是否限制补单（false：不限制、true：限制）
        CancelOrder: false, //是否限制取消订单（false：不限制、true：限制）
        ModifyBillDate: false, //是否限制修改订单时间（false：不限制、true：限制）
        ModifyBillPayMethod: false, //是否限制修改订单支付方式（false：不限制、true：限制）
        ModifyEntityPerformance: false, //是否限制修改门店业绩（false：不限制、true：限制）
        ModifyEmployeePerformanceCommission: false, //是否限制修改员工业绩提成（false：不限制、true：限制）
        IncomeAndSpending: false, //是否限制日常收支（false：不限制、true：限制）
        Limits: Limits,
        JobType: [], //职务集合
        Entity: [], //门店集合
      };

      that.activeName = "1";
      that.isAdd = false;
      that.showSealingAccount = true;
    },
    /**    */
    addSealingAccountClick() {
      let that = this;
      that.notLimitJobTypeList = [];
      that.addForm = {
        Name: "",
        StartAccountPeriod: 0, //账期时间开始周期（0：上月：1：本月）
        StartAccountDate: 1, //账期时间开始日期（1-31）
        EndAccountPeriod: 1, //账期时间结束周期（0：上月：1：本月）
        EndAccountDate: 1, //账期时间结算日期（1-31）
        CloseAccountDate: 1, //关账日期（每月[1-31]）
        ReplacementOrder: false, //是否限制补单（false：不限制、true：限制）
        CancelOrder: false, //是否限制取消订单（false：不限制、true：限制）
        ModifyBillDate: false, //是否限制修改订单时间（false：不限制、true：限制）
        ModifyBillPayMethod: false, //是否限制修改订单支付方式（false：不限制、true：限制）
        ModifyEntityPerformance: false, //是否限制修改门店业绩（false：不限制、true：限制）
        ModifyEmployeePerformanceCommission: false, //是否限制修改员工业绩提成（false：不限制、true：限制）
        IncomeAndSpending: false, //是否限制日常收支（false：不限制、true：限制）
        Limits: ["ReplacementOrder"],
        JobType: [], //职务集合
        Entity: [], //门店集合
      };
      that.defaultCheckedKeys = [];
      that.activeName = "1";
      that.isAdd = true;
      that.showSealingAccount = true;
    },
    /**  删除 列表 数据  */
    deleteSealingAccount(row) {
      let that = this;
      that
        .$confirm("是否确定删除关账信息？", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }).then(() => {
          that.sealingAccount_delete(row.ID);
        }).catch(() => {
          that.$message({
            type: "info",
            message: "已取消操作",
          });
        });
    },

    /**  添加不限制 职务  */
    addNotLimitJobType() {
      let that = this;
      that.showJobTypeList = true;
      that.$nextTick(() => {
        that.$refs.jobTypeMultipleTable.clearSelection();
        if (that.notLimitJobTypeList.length > 0) {
          var defaultCheckedKeys = that.notLimitJobTypeList.map(i => i.JobTypeID)
          defaultCheckedKeys.forEach((item) => {
            that.jobTypeList.forEach((val) => {
              if (item == val.ID) {
                that.$nextTick(() => {
                  that.$refs.jobTypeMultipleTable.toggleRowSelection(val, true);
                });
              }
            });
          });
        }
      });
    },

    /**    */
    addSubmitSealingAccountClick() {
      let that = this;
      let entitys = that.$refs.treeEntitys.getCheckedKeys();
      if (!entitys || !entitys.length) {
        that.$message.error("请选择适用门店");
        return;
      }
      that.$refs.sealingAccountRef.validate(valid => {
        if (valid) {
          that.addForm.Limits.forEach(i => {
            that.addForm[i] = true;
          });
          if (that.isAdd) {
            that.sealingAccount_create();
          }
          else {
            that.sealingAccount_update();
          }

        }
      })
    },
    /**  不受限职务选择变动   */
    getSelectedJobType(selection) {
      let that = this;
      that.notLimitselectedJobType = selection;
    },
    /**   保存 不受限职务 */
    confirmNotLimitJobTypeListClick() {
      let that = this;
      that.showJobTypeList = false;
      that.notLimitJobTypeList = that.notLimitselectedJobType.map(i => {
        i.JobTypeID = i.ID;
        i.JobTypeName = i.JobName;
        return i;
      });
    },
    /**  删除已经选择的不受限职务  */
    deleteSelectedJobType(row) {
      let that = this;
      that.notLimitJobTypeList = that.notLimitJobTypeList.filter(i => i.JobTypeID != row.JobTypeID);
    },


    /**    */
    async sealingAccount_all() {
      let that = this;
      try {
        let params = {
          PageNum: that.paginations.page,
          EntityID: that.EntityID, //门店
          Name: that.Name,
        };
        let res = await API.sealingAccount_all(params);
        if (res.StateCode == 200) {
          that.tableData = res.List;
          that.paginations.total = res.Total;
        }
        else {
          that.$message.error(res.Message);
        }
      } catch (error) {
        that.$message.error(error);
      }

    },
    /**    */
    async sealingAccount_create() {
      let that = this;
      try {
        let params = that.addForm;
        params.JobType = that.notLimitJobTypeList.map(i => i.ID);
        params.Entity = that.$refs.treeEntitys.getCheckedKeys();
        let res = await API.sealingAccount_create(params);
        if (res.StateCode == 200) {
          that.$message.success("操作成功");
          that.sealingAccount_all();
          that.showSealingAccount = false;
        }
        else {
          that.$message.error(res.Message);
        }
      } catch (error) {
        that.$message.error(error);
      }

    },
    /**  编辑请求  */
    async sealingAccount_update() {
      let that = this;
      try {
        let params = that.addForm;
        params.JobType = that.notLimitJobTypeList.map(i => i.ID);
        params.Entity = that.$refs.treeEntitys.getCheckedKeys();
        let res = await API.sealingAccount_update(params);
        if (res.StateCode == 200) {
          that.$message.success("操作成功");
          that.sealingAccount_all();
          that.showSealingAccount = false;
        }
        else {
          that.$message.error(res.Message);
        }
      } catch (error) {
        that.$message.error(error);
      }

    },
    /**  删除请求  */
    async sealingAccount_delete(ID) {
      let that = this;
      try {
        let params = { ID: ID };
        let res = await API.sealingAccount_delete(params);
        if (res.StateCode == 200) {
          that.$message.success("操作成功");
          that.sealingAccount_all();
        }
        else {
          that.$message.error(res.Message);
        }
      } catch (error) {
        that.$message.error(error);
      }

    },
    /**  限制门店列表请求  */
    async sealingAccount_entity() {
      let that = this;
      try {
        let params = {};
        let res = await API.sealingAccount_entity(params);
        if (res.StateCode == 200) {
          that.sealingAccountEntitys = res.Data;
        }
        else {
          that.$message.error(res.Message);
        }
      } catch (error) {
        that.$message.error(error);
      }

    },
    /**  职务列表  */
    async jobtype_all() {
      let that = this;
      try {
        let params = {};
        let res = await API.jobtype_all(params);
        if (res.StateCode == 200) {
          that.jobTypeList = res.Data;
        }
        else {
          that.$message.error(res.Message);
        }
      } catch (error) {
        that.$message.error(error);
      }

    },
    /** 筛选门店列表   */
    async entity_permissionEtity() {
      let that = this;
      try {
        let params = {};
        let res = await API.entity_permissionEtity(params);
        if (res.StateCode == 200) {
          that.entitys = res.Data;
        }
        else {
          that.$message.error(res.Message);
        }
      } catch (error) {
        that.$message.error(error);
      }

    },
  },
  /** 创建实例之前执行函数   */
  beforeCreate() { },
  /**  实例创建完成之后  */
  created() { },
  /**  在挂载开始之前被调用  */
  beforeMount() { },
  /**  实例被挂载后调用  */
  mounted() {
    this.sealingAccount_all();
    this.jobtype_all();
    this.entity_permissionEtity();
    this.sealingAccount_entity();
  },
  /**  数据更新 之前 调用  */
  beforeUpdate() { },
  /** 数据更新 完成 调用   */
  updated() { },
  /**  实例销毁之前调用  */
  beforeDestroy() { },
  /**  实例销毁后调用  */
  destroyed() { },
}
</script>

<style lang="scss">
.SealingAccount {
  .custom-select-style {
    width: 100px;
  }

  .custom-form-item-bottom {
    margin-bottom: unset;
  }

  .sealingTips {
    margin-top: 8px;
    color: #808180;
    line-height: 16px;
    font-size: 12px;
  }

  .el-scrollbar_height {
    height: 50vh;

    .el-scrollbar__wrap {
      overflow-x: hidden;
    }
  }



}
</style>
