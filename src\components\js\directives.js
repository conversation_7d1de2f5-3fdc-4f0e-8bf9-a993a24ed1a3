import Vue from "vue";

Vue.directive("enterNumber", {
  inserted: function (el) {
    el.addEventListener("keyup", function (e) {
      e = e || window.event;
      let charcode = typeof e.charCode == "number" ? e.charCode : e.keyCode;
      let re = /\d/;
      if (!re.test(String.fromCharCode(charcode)) && charcode > 9 && !e.ctrlKey) {
        if (e.preventDefault) {
          e.preventDefault();
        } else {
          e.returnValue = false;
        }
      }
    });
  },
});
Vue.directive("enterNumber2", {
  inserted: function (el) {
    el.addEventListener("keyup", function (e) {
      e = e || window.event;
      //   通过正则过滤小数点后两位
      if (e.key !== undefined) {
        if (e.code !== "NumpadDecimal") {
          e.target.value = e.target.value.match(/^\d*(.?\d{0,2})/g)[0] || null;
        }
      } else if (e.keyCode !== undefined) {
        if (e.keyCode !== 110) {
          e.target.value = e.target.value.match(/^\d*(.?\d{0,2})/g)[0] || null;
        }
      }
    });
  },
});
Vue.directive("enterNumber3", {
  inserted: function (el) {
    el.addEventListener("keyup", function (e) {
      e = e || window.event;
      e.target.value = Math.abs(e.target.value);
    });
    el.addEventListener("focusout", (e) => {
      if (e.target.value.indexOf(".") != -1) {
        e.target.value = Number(e.target.value).toFixed(2);
      }
    });
  },
});
Vue.directive("enterInt", {
  inserted: function (el) {
    el.addEventListener("keyup", function (e) {
      e = e || window.event;
      // 过滤小数点
      e.target.value = e.target.value.match(/^\d*/g)[0] || null;
    });
  },
});

/**  默认 2位小数
 *  v-input-fixed='0'  正数
 *  v-input-fixed='1'  1位小数
 *  v-input-fixed='2'  2位小数
 */
Vue.directive("input-fixed", {
  bind: (el, binding, vnode) => {
    let input = null;
    if (vnode.elm.children.length > 1) {
      for (let index = 0; index < vnode.elm.children.length; index++) {
        const element = vnode.elm.children[index];
        if (element.tagName === "INPUT") {
          input = element;
        }
      }
    } else {
      input = vnode.elm.children[0];
    }
    input.addEventListener("compositionstart", () => {
      vnode.inputLocking = true;
    });
    input.addEventListener("compositionend", () => {
      vnode.inputLocking = false;
      input.dispatchEvent(new Event("input"));
    });
    input.addEventListener("input", () => {
      if (vnode.inputLocking) {
        return;
      }
      let oldValue = input.value;
      let newValue = input.value;
      switch (binding.value) {
        case 0 /** 整数  */:
          newValue = newValue.replace(/[^\d]/g, "");
          break;
        case 1 /**  保留一位小数  */:
          {
            newValue = newValue
              .replace(/[^\d^.]/g, "") /**  把不是数字，不是小数点的过滤掉  */
              .replace(".", "$#$")
              .replace(/\./g, "") /**  验证第一位是不是小数点  */
              .replace("$#$", ".")
              .replace(/^(-)*(\d+)\.(\d).*$/, "$1$2.$3");
          }
          break;
        default:
          {
            /**  保留两位小数  */
            newValue = newValue.replace(/[^\d.]/g, "");
            newValue = newValue.replace(/^\./g, "");
            newValue = newValue.replace(".", "$#$").replace(/\./g, "").replace("$#$", ".");
            newValue = newValue.replace(/^(-)*(\d+)\.(\d\d).*$/, "$1$2.$3");
          }
          break;
      }
      if (newValue) {
        let arr = newValue.split(".");
        // 去掉开头多余的0
        newValue = Number(arr[0]) + (arr[1] === undefined ? "" : "." + arr[1]);
      }
      // 判断是否需要更新，避免进入死循环
      if (newValue !== oldValue) {
        input.value = newValue;
        input.dispatchEvent(new Event("input")); // 通知v-model更新
      }
    });
    // input 事件无法处理小数点后面全是零的情况 因为无法确定用户输入的0是否真的应该清除，如3.02。放在blur中去处理
    input.addEventListener("blur", () => {
      let oldValue = input.value;
      let newValue = input.value;
      if (newValue) {
        newValue = Number(newValue).toString();
      }
      // 判断是否需要更新，避免进入死循环
      if (newValue !== oldValue) {
        input.value = newValue;
        input.dispatchEvent(new Event("input")); // 通知v-model更新
      }
    });
  },
});

Vue.directive("preventClick", {
  inserted: function (el, binding) {
    el.addEventListener("click", () => {
      if (!el.disabled) {
        el.disabled = true;
        setTimeout(() => {
          el.disabled = false;
        }, binding.value || 1000);
      }
    });
  },
});

/**  做下拉加载更多使用  */
Vue.directive("loadmore", {
  bind(el, binding) {
    // 获取element-ui定义好的scroll盒子

    const SELECTWRAP_DOM = el.querySelector(".el-select-dropdown .el-select-dropdown__wrap ");
    SELECTWRAP_DOM.addEventListener("scroll", function () {
      const CONDITION = this.scrollHeight - this.scrollTop <= this.clientHeight + 1;
      if (CONDITION) {
        binding.value();
      }
    });
  },
});

Vue.directive("scrollbar_loadmore", {
  bind(el, binding) {
    // 获取element-ui定义好的scroll盒子
    const SELECTWRAP_DOM = el.querySelector(".el-scrollbar .el-scrollbar__wrap");
    SELECTWRAP_DOM.addEventListener("scroll", function () {
      const CONDITION = this.scrollHeight - this.scrollTop <= this.clientHeight + 1;
      if (CONDITION) {
        binding.value();
      }
    });
  },
});

Vue.directive("formTooltip", {
  bind(el, binding) {
    const label = el.querySelector(".el-form-item__label");
    var labelText = label.innerText; // el-form-item label 名称
    var tipContent = binding.value; // v-form-tooltip 值
    const ep = new Vue({
      el: document.createElement("span"),
      template: `<el-popover
              content="${tipContent}"
              placement="top-start"
              width="200"
              :open-delay="500"
              trigger="hover">
              <span slot="reference">${labelText} <i  class="el-icon-info" style="color:#dcdfe6"></i></span>
            </el-popover>`,
    });
    label.innerText = "";
    label.appendChild(ep.$el);
  },
});

/* el-drawer  左右推动 指令 */
Vue.directive("drawerDrag", {
  bind(el) {
    const maxWidth = window.innerWidth - 30;
    const minWidth = window.innerWidth * 0.6;

    const dragDom = el.querySelector(".el-drawer");
    dragDom.style.overflow = "auto";
    dragDom.style.paddingLeft = "15px";
    dragDom.style.width = minWidth + "px";
    const resizeElL = document.createElement("div");
    // <el-button icon="el-icon-search" circle></el-button>
    // const iconElL = document.createElement("el-button");
    // iconElL.icon = "el-icon-d-arrow-right";

    // resizeElL.appendChild(iconElL);
    dragDom.appendChild(resizeElL);
    resizeElL.style.cursor = "ew-resize";
    resizeElL.style.position = "absolute";
    resizeElL.style.height = "100%";
    resizeElL.style.width = "10px";
    resizeElL.style.backgroundColor = "#eee";
    resizeElL.style.boxShadow = "5px 5px 5px #eeeeee";
    resizeElL.style.left = "0px";
    resizeElL.style.top = "0px";
    resizeElL.style.lineHeight = "50";

    resizeElL.onmousedown = (e) => {
      const elW = dragDom.clientWidth;
      const EloffsetLeft = dragDom.offsetLeft;
      const clientX = e.clientX;
      document.onmousemove = function (e) {
        e.preventDefault();
        // 左侧鼠标拖拽位置
        if (clientX > EloffsetLeft && clientX < EloffsetLeft + 10) {
          // 往左拖拽
          if (clientX > e.clientX) {
            if (dragDom.clientWidth <= maxWidth) {
              dragDom.style.width = elW + (clientX - e.clientX) + "px";
            }
          }
          // 往右拖拽
          if (clientX < e.clientX) {
            if (dragDom.clientWidth >= minWidth) {
              dragDom.style.width = elW - (e.clientX - clientX) + "px";
            }
          }
        }
      };
      // 拉伸结束
      document.onmouseup = function () {
        // e
        document.onmousemove = null;
        document.onmouseup = null;
      };
    };
  },
});

export default {};
