/**
 * Created by preference on 2023/04/07
 *  zmx
 */

import * as API from "@/api/index";
export default {
  /**   */
  sealingAccount_all: (params) => {
    return API.POST("api/sealingAccount/all", params);
  },
  /**   */
  sealingAccount_create: (params) => {
    return API.POST("api/sealingAccount/create", params);
  },
  /**   */
  sealingAccount_update: (params) => {
    return API.POST("api/sealingAccount/update", params);
  },
  /**   */
  sealingAccount_delete: (params) => {
    return API.POST("api/sealingAccount/delete", params);
  },
  /**   */
  sealingAccount_entity: (params) => {
    return API.POST("api/sealingAccount/entity", params);
  },
  /**   */
  jobtype_all: (params) => {
    return API.POST("api/jobtype/all", params);
  },
  /**   */
  entity_permissionEtity: (params) => {
    return API.POST("api/entity/allEntity", params);
  },

  /**  获取补单限制  */
  sealingAccount_getReplacementOrderRestriction: (params) => {
    return API.POST(
      "api/sealingAccount/getReplacementOrderRestriction",
      params
    );
  },
  /**  获取取消单据限制  */
  sealingAccount_getCancelOrderRestriction: (params) => {
    return API.POST("api/sealingAccount/getCancelOrderRestriction", params);
  },
  /**  获取修改订单时间限制  */
  sealingAccount_getModifyBillDateRestriction: (params) => {
    return API.POST("api/sealingAccount/getModifyBillDateRestriction", params);
  },
  /**  获取修改订单支付方式限制  */
  sealingAccount_getModifyBillPayMethodRestriction: (params) => {
    return API.POST(
      "api/sealingAccount/getModifyBillPayMethodRestriction",
      params
    );
  },
  /**  获取修改门店业绩限制  */
  sealingAccount_getModifyEntityPerformanceRestriction: (params) => {
    return API.POST(
      "api/sealingAccount/getModifyEntityPerformanceRestriction",
      params
    );
  },
  /**  获取修改员工业绩限制  */
  sealingAccount_getModifyEmployeePerformanceCommissionRestriction: (
    params
  ) => {
    return API.POST(
      "api/sealingAccount/getModifyEmployeePerformanceCommissionRestriction",
      params
    );
  },
  /**  获取修改日常收支限制  */
  sealingAccount_getModifyIncomeAndSpendingRestriction: (params) => {
    return API.POST(
      "api/sealingAccount/getModifyIncomeAndSpendingRestriction",
      params
    );
  },
};
