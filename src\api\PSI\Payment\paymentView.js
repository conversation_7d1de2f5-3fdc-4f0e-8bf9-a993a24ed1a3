import * as API from '@/api/index'
export default {
  /* 预付款列表 */
  detail_payment: params => {
    return API.POST('api/payment/detail', params)
  },
   /*  仓库列表 */
   detail_allWarehouse: params => {
    return API.POST('api/purchaseStorage/entity', params)
  },
  /* 预付款类别列表 */
  prepayCategory_list: params => {
    return API.POST('api/paymentCategory/list', params)
  },

  /* 明细导出 */
  paymentView_excel: params => {
    return API.exportExcel('api/payment/excel', params)
  },

  
}