<template>
  <div class="teamCommissionScheme content_body">
    <!-- 筛选 -->
    <div class="nav_header">
      <el-row>
        <el-col :span="20">
          <el-form :inline="true" size="small" @submit.native.prevent @keyup.enter.native="handleSearch">
            <el-form-item label="团队提成方案">
              <el-input v-model="searchData.Name" size="small" placeholder="输入团队提成方案搜索" clearable @clear="handleSearch"> </el-input>
            </el-form-item>
            <el-form-item label="业绩考核方案">
              <el-select v-model="searchData.PerformanceEvaluationSchemeID" placeholder="请选择" filterable clearable size="small" @change="handleSearch">
                <el-option v-for="item in performanceScheme" :key="item.ID" :label="item.Name" :value="item.ID"> </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="业绩计算方案">
              <el-select v-model="searchData.PerformanceCalculationSchemeID" placeholder="请选择" filterable clearable size="small" @change="handleSearch">
                <el-option v-for="item in performanceScheme" :key="item.ID" :label="item.Name" :value="item.ID"> </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="有效性">
              <el-select v-model="searchData.Active" placeholder="选择有效性" clearable size="small" @change="handleSearch">
                <el-option label="有效" :value="true"></el-option>
                <el-option label="无效" :value="false"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" size="small" @click="handleSearch" v-prevent-click>搜索</el-button>
            </el-form-item>
          </el-form>
        </el-col>
        <el-col :span="4" class="text_right">
          <el-button type="primary" size="small" @click="addTeamCommissionScheme" v-prevent-click>新增 </el-button>
        </el-col>
      </el-row>
    </div>
    <!-- 表格 -->
    <el-table size="small" :data="tableData" v-loading="loading">
      <el-table-column prop="Name" label="团队提成方案"></el-table-column>
      <el-table-column prop="PerformanceEvaluationSchemeName" label="业绩考核方案"></el-table-column>
      <el-table-column prop="PerformanceCalculationSchemeName" label="业绩计算方案"></el-table-column>
      <el-table-column :formatter="(row) => (row.Calculation == 10 ? '阶梯式计算' : '阶段式计算')" label="计算方式"></el-table-column>
      <el-table-column :formatter="(row) => (row.Active ? '有效' : '无效')" label="有效性"> </el-table-column>
      <el-table-column label="操作" width="80">
        <template slot-scope="scope">
          <el-button type="primary" size="small" @click="editTeamCommissionScheme(scope.row)" v-prevent-click>编辑</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <div class="pad_15 text_right">
      <el-pagination
        background
        v-if="paginations.total > 0"
        @current-change="handleCurrentChange"
        :current-page.sync="paginations.page"
        :page-size="paginations.page_size"
        :layout="paginations.layout"
        :total="paginations.total"
      ></el-pagination>
    </div>
    <!-- 新增编辑弹出层 -->
    <el-dialog :title="isAdd ? '新增团队提成方案' : '编辑团队提成方案'" :visible.sync="dialogVisible" width="1100px" :close-on-click-modal="false">
      <el-tabs v-model="activeName">
        <el-tab-pane label="方案设置" name="0">
          <el-scrollbar class="el-scrollbar_height">
            <el-form size="small" ref="formData" :model="formData" :rules="formDataRules" label-width="110px">
              <el-form-item label="团队提成方案" prop="Name">
                <el-input v-model="formData.Name" placeholder="请输入团队提成方案名称"></el-input>
              </el-form-item>
              <el-form-item label="业绩考核方案" prop="PerformanceEvaluationSchemeID">
                <el-select v-model="formData.PerformanceEvaluationSchemeID" placeholder="请选择" filterable>
                  <el-option v-for="item in performanceScheme" :key="item.ID" :label="item.Name" :value="item.ID"> </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="计算方式" prop="Calculation">
                <span slot="label">
                  计算方式
                  <el-popover placement="top-start" width="850px" trigger="hover">
                    <p>按阶梯式计算：设置后呈阶梯式增长，按总业绩计算提成</p>
                    <p>例：1-10000时6%，10000-15000时10%，团队业绩13000，提成为：13000*10%</p>
                    <p>按阶段式计算：设置后分阶段式计算，根据区间计算提成</p>
                    <p>例：1-10000时6%，10000-15000时10%，团队业绩13000，提成为：10000*6%+3000*10%</p>
                    <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                  </el-popover>
                </span>
                <el-radio v-model="formData.Calculation" label="10">阶梯式计算</el-radio>
                <el-radio v-model="formData.Calculation" label="20">阶段式计算</el-radio>
              </el-form-item>

              <el-form-item label="业绩计算方案" v-if="formData.Calculation == '10'" prop="PerformanceCalculationSchemeID">
                <el-select v-model="formData.PerformanceCalculationSchemeID" placeholder="请选择" filterable>
                  <el-option v-for="item in performanceScheme" :key="item.ID" :label="item.Name" :value="item.ID"> </el-option>
                </el-select>
              </el-form-item>
              <el-form-item v-if="!isAdd" prop="Active" label="有效性">
                <el-radio-group v-model="formData.Active">
                  <el-radio :label="true">有效</el-radio>
                  <el-radio :label="false">无效</el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="提成方案" prop="Commission">
                <span slot="label">
                  提成方案
                  <el-popover placement="top-start" width="850px" trigger="hover">
                    <p>提成方案的区间值，最小值包含该区间内，最大值不包含在该区间内。</p>
                    <p>比如：区间设置为1000～2000，计算提成时，如业绩值为1000，则符合该区间规则；如业绩值为2000时，则不符合该区间规则。</p>
                    <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                  </el-popover>
                </span>
                <el-button type="primary" size="small" @click="addCommissionScheme">新增提成方案 </el-button>
              </el-form-item>
            </el-form>
            <el-table size="small" :data="formData.Commission" style="width: calc(100% - 110px); margin-left: 110px" class="martp_10">
              <el-table-column prop="BeginPerformance" label="开始业绩(大于等于)">
                <template slot-scope="scope">{{ scope.row.BeginPerformance | toFixed | NumFormat }}</template>
              </el-table-column>
              <el-table-column prop="EndPerformance" label="结束业绩(小于)">
                <template slot-scope="scope">{{ scope.row.EndPerformance | toFixed | NumFormat }}</template>
              </el-table-column>
              <el-table-column prop="Rate" label="比例提成">
                <template slot-scope="scope">{{ scope.row.Rate | toFixed }}%</template>
              </el-table-column>
              <el-table-column prop="Fixed" label="固定提成(元)">
                <template slot-scope="scope">￥{{ scope.row.Fixed | toFixed | NumFormat }}</template>
              </el-table-column>
              <el-table-column prop="address" label="操作" width="145px">
                <template slot-scope="scope">
                  <el-button type="primary" size="small" @click="editCommission(scope.row, scope.$index)">编辑 </el-button>
                  <el-button type="danger" size="small" @click="deleteCommission(scope.$index)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-scrollbar>
        </el-tab-pane>
        <el-tab-pane label="提成员工" name="1">
          <el-row>
            <el-col :span="20">
              <el-form :inline="true" size="small" @submit.native.prevent>
                <el-form-item label="员工" label-width="40px">
                  <el-input v-model="searchCommissionEmps.Name" size="small" placeholder="输入员工姓名/编号搜索" clearable> </el-input>
                </el-form-item>
                <el-form-item label="职务" prop="JobID" label-width="60px">
                  <el-select v-model="searchCommissionEmps.JobID" placeholder="请选择" size="small" filterable clearable>
                    <el-option v-for="item in jobTypeData" :label="item.JobName" :value="item.ID" :key="item.ID"> </el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="在职状态" label-width="80px">
                  <el-select v-model="searchCommissionEmps.State" placeholder="请选择" size="small" filterable clearable>
                    <el-option label="在职" :value="true"></el-option>
                    <el-option label="离职" :value="false"> </el-option>
                  </el-select>
                </el-form-item>
              </el-form>
            </el-col>
            <el-col :span="4" class="text_right">
              <el-button type="primary" size="small" @click="addEmployee(true)" v-prevent-click>添加员工</el-button>
            </el-col>
          </el-row>
          <el-table
            size="small"
            :data="filterEmplyoee(commissionEmployee, 'searchCommissionEmps')"
            style="width: 100%"
            max-height="450px"
            tooltip-effect="light"
          >
            <el-table-column prop="Name" label="员工姓名"></el-table-column>
            <el-table-column show-overflow-tooltip label="员工所属单位">
              <template slot-scope="scope">
                {{ getEntityNames(scope.row.BelongEntity) }}
              </template>
            </el-table-column>
            <el-table-column prop="EmployeeID" label="员工编号"></el-table-column>
            <el-table-column prop="JobName" label="职务"></el-table-column>
            <el-table-column :formatter="(row) => (row.State ? '在职' : '离职')" label="在职状态"></el-table-column>
            <el-table-column label="操作" width="80">
              <template slot-scope="scope">
                <el-button size="small" type="danger" @click="handleDeleteApplyEmployee(scope.$index, true)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
        <el-tab-pane label="团队成员" name="2">
          <el-row>
            <el-col :span="20">
              <el-form :inline="true" size="small" @submit.native.prevent>
                <el-form-item label="员工" label-width="40px">
                  <el-input v-model="searchTempEmps.Name" size="small" placeholder="输入员工姓名/编号搜索" clearable> </el-input>
                </el-form-item>
                <el-form-item label="职务" prop="JobID" label-width="60px">
                  <el-select v-model="searchTempEmps.JobID" placeholder="请选择" size="small" filterable clearable>
                    <el-option v-for="item in jobTypeData" :label="item.JobName" :value="item.ID" :key="item.ID"> </el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="在职状态" label-width="80px">
                  <el-select v-model="searchTempEmps.State" placeholder="请选择" size="small" filterable clearable>
                    <el-option label="在职" :value="true"></el-option>
                    <el-option label="离职" :value="false"> </el-option>
                  </el-select>
                </el-form-item>
              </el-form>
            </el-col>
            <el-col :span="4" class="text_right">
              <el-button type="primary" size="small" @click="addEmployee(false)" v-prevent-click>添加员工</el-button>
            </el-col>
          </el-row>
          <el-table size="small" :data="filterEmplyoee(teamEmployee, 'searchTempEmps')" style="width: 100%" max-height="450px" tooltip-effect="light">
            <el-table-column prop="Name" label="员工姓名"></el-table-column>
            <el-table-column show-overflow-tooltip label="员工所属单位">
              <template slot-scope="scope">{{ getEntityNames(scope.row.BelongEntity) }}</template>
            </el-table-column>
            <el-table-column prop="EmployeeID" label="员工编号"></el-table-column>
            <el-table-column prop="JobName" label="职务"></el-table-column>
            <el-table-column :formatter="(row) => (row.State ? '在职' : '离职')" label="在职状态"></el-table-column>
            <el-table-column label="操作" width="80">
              <template slot-scope="scope">
                <el-button size="small" type="danger" @click="handleDeleteApplyEmployee(scope.$index, false)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
      </el-tabs>

      <span slot="footer" class="dialog-footer">
        <el-button size="small" @click="dialogVisible = false" v-prevent-click>取 消</el-button>
        <el-button size="small" type="primary" @click="handleSaveTeamCommissionScheme()" :loading="saveLoading" v-prevent-click>保 存 </el-button>
      </span>
    </el-dialog>
    <!-- 设置条件 -->
    <el-dialog width="600px" :title="isAddCommission ? '新增提成方案' : '编辑提成方案'" :visible.sync="addCommissionSchemeVisible">
      <el-form size="small" ref="commissionFormRef" :model="commission" :rules="commissionRules" label-width="110px">
        <el-form-item label="条件">
          <span slot="label"><span style="margin-right: 4px; color: #f67979">*</span><span>业绩范围</span></span>
          <el-col :span="8">
            <el-form-item label-width="0" style="margin-bottom: 0px !important" prop="BeginPerformance">
              <el-input v-model="commission.BeginPerformance" v-input-fixed="2" placeholder="请输入开始业绩"> </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="2" class="dis_flex flex_x_center">至</el-col>
          <el-col :span="8">
            <el-form-item label-width="0" style="margin-bottom: 0px !important" prop="EndPerformance">
              <el-input v-model="commission.EndPerformance" v-input-fixed="2" placeholder="请输入截止业绩"> </el-input>
            </el-form-item>
          </el-col>
        </el-form-item>
        <el-form-item label="比例提成" prop="Rate">
          <el-input v-model="commission.Rate" v-input-fixed="2" @input="changeCommissionRate">
            <template slot="append">%</template>
          </el-input>
        </el-form-item>
        <el-form-item label="固定提成" prop="Fixed">
          <el-input v-model="commission.Fixed" v-input-fixed="2">
            <template slot="append">元</template>
          </el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button size="small" @click="addCommissionSchemeVisible = false" v-prevent-click>取 消</el-button>
        <el-button size="small" type="primary" @click="handleSaveCommissin" v-prevent-click>保 存</el-button>
      </span>
    </el-dialog>
    <!-- 员工列表 -->
    <el-dialog title="添加员工" :visible.sync="commissionDialogVisible" width="1000px" @close="emplyoeeBeforeClose">
      <el-row>
        <el-col :span="24">
          <el-form :inline="true" size="small" @submit.native.prevent @keyup.enter.native="handleEmployeeSearch">
            <el-form-item label="搜索" label-width="40px">
              <el-input v-model="searchEmployeeData.Name" size="small" placeholder="输入员工名称/编号搜索" clearable @clear="handleEmployeeSearch"> </el-input>
            </el-form-item>
            <el-form-item label="职务" prop="JobID" label-width="60px">
              <el-select v-model="searchEmployeeData.JobID" placeholder="请选择" size="small" filterable clearable @change="handleEmployeeSearch">
                <el-option v-for="item in jobTypeData" :label="item.JobName" :value="item.ID" :key="item.ID"> </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="在职状态" label-width="80px">
              <el-select v-model="searchEmployeeData.State" placeholder="请选择" size="small" filterable clearable @change="handleEmployeeSearch">
                <el-option label="在职" :value="true"></el-option>
                <el-option label="离职" :value="false"> </el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" size="small" @click="handleEmployeeSearch" v-prevent-click>搜索</el-button>
            </el-form-item>
          </el-form>
        </el-col>
      </el-row>
      <el-table
        size="small"
        :data="emplyoeeList"
        ref="emplyoeeList"
        style="width: 100%"
        max-height="450px"
        :row-key="(row) => row.EmployeeID"
        @selection-change="handleSelectionChange"
        tooltip-effect="light"
      >
        <el-table-column type="selection" width="55" :reserve-selection="true"></el-table-column>
        <el-table-column prop="Name" label="员工姓名"></el-table-column>
        <el-table-column prop="EmployeeID" label="员工编号"></el-table-column>
        <el-table-column prop="JobName" label="职务"></el-table-column>
        <el-table-column :formatter="(row) => (row.State ? '在职' : '离职')" label="在职状态"></el-table-column>
        <el-table-column
          show-overflow-tooltip
          :formatter="
            (row) => {
              let str = '';
              row.Entity && row.Entity.length && row.Entity.forEach((item) => (str += item.EntityName + ';'));
              return str;
            }
          "
          label="所属单位"
        ></el-table-column>
      </el-table>
      <div class="pad_15 text_right">
        <el-pagination
          background
          v-if="EmployeePaginations.total > 0"
          @current-change="handleEmployeeCurrentChange"
          :current-page.sync="EmployeePaginations.page"
          :page-size="EmployeePaginations.page_size"
          :layout="EmployeePaginations.layout"
          :total="EmployeePaginations.total"
        ></el-pagination>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button size="small" @click="commissionDialogVisible = false" v-prevent-click>取 消</el-button>
        <el-button size="small" type="primary" @click="handleSaveEmployee" v-prevent-click>保 存</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import API from "@/api/KHS/Salary/teamCommissionScheme.js";
import performanceAPI from "@/api/KHS/Salary/performanceScheme";
import APIJob from "@/api/KHS/Entity/jobtype";
export default {
  name: "SalaryTeamCommissionScheme",

  props: {},
  /** 监听数据变化   */
  watch: {},
  /**  引入的组件  */
  components: {},
  /**  Vue 实例的数据对象**/
  data() {
    return {
      addCommissionSchemeVisible: false,
      saveLoading: false,
      loading: false,
      dialogVisible: false,
      commissionDialogVisible: false,
      isCommission: true,
      isAdd: true,
      isAddCommission: true,
      activeName: "0",
      searchData: {
        Name: "",
        PerformanceEvaluationSchemeID: null, //考核业绩
        PerformanceCalculationSchemeID: null, //结算业绩
        Active: true,
      },
      tableData: [],
      paginations: {
        page: 1, // 当前位于哪页
        total: 0, // 总数
        page_size: 10, // 1页显示多少条
        layout: "total, prev, pager, next,jumper", // 翻页属性
      },

      formData: {
        Name: "",
        PerformanceEvaluationSchemeID: "",
        PerformanceCalculationSchemeID: "",
        Commission: [],
        Calculation: "10",
      },
      formDataRules: {
        Name: [{ required: true, message: "请输入团队提成方案", trigger: ["blur", "change"] }],
        PerformanceEvaluationSchemeID: [{ required: true, message: "请选择业绩考核方案", trigger: "change" }],
        PerformanceCalculationSchemeID: [{ required: true, message: "请选择业绩计算方案", trigger: "change" }],
        Commission: [{ required: true, message: "请设置提成方案" }],
        Active: [{ required: true, message: "请选择方案有效性" }],
      },
      commission: {
        BeginPerformance: null, //开始
        EndPerformance: null, //结束
        Rate: null, //比例
        Fixed: null, //固定
      },
      commissionRules: {
        BeginPerformance: [{ required: true, message: "请输入开始业绩" }],
        EndPerformance: [{ required: true, message: "请输入截止业绩" }],
        Rate: [{ required: true, message: "请输入比例提成" }],
        Fixed: [{ required: true, message: "请输入固定提成" }],
      },
      emplyoeeList: [],
      searchEmployee: "",
      EmployeePaginations: {
        page: 1, // 当前位于哪页
        total: 0, // 总数
        page_size: 10, // 1页显示多少条
        layout: "total, prev, pager, next,jumper", // 翻页属性
      },
      searchEmployeeData: {
        Name: "",
        JobID: "",
        State: true,
      },
      commissionEmployee: [] /**  提成成员  */,
      teamEmployee: [] /**  团队成员  */,
      performanceScheme: [],
      searchCommissionEmps: {
        Name: "",
        JobID: "",
        State: "",
      },
      searchTempEmps: {
        Name: "",
        JobID: "",
        State: "",
      },
      jobTypeData: [],
      selectionEmp: [] /**  员工弹窗选中临时数据  */,
      editCommissionIndex: 0,
    };
  },
  /**计算属性  */
  computed: {},
  /**  方法集合  */
  methods: {
    /**  搜索  */
    handleSearch() {
      this.paginations.page = 1;
      this.teamCommissionScheme_all();
    },
    /**  修改分页  */
    handleCurrentChange(page) {
      this.paginations.page = page;
      this.teamCommissionScheme_all();
    },
    /**  新增业绩方案  */
    addTeamCommissionScheme() {
      this.formData = {
        Name: "",
        PerformanceEvaluationSchemeID: "",
        PerformanceCalculationSchemeID: "",
        Commission: [],
        Calculation: "10",
      };
      this.isAdd = true;
      this.dialogVisible = true;
    },
    /** 编辑业绩提成方案   */
    async editTeamCommissionScheme(row) {
      let that = this;
      that.formData = {
        Name: row.Name,
        PerformanceEvaluationSchemeID: row.PerformanceEvaluationSchemeID,
        PerformanceCalculationSchemeID: row.PerformanceCalculationSchemeID,
        Commission: row.Commission,
        Calculation: row.Calculation,
        Active: row.Active,
        ID: row.ID,
      };
      that.loading = true;
      that.formData.Commission = await that.teamCommissionScheme_commission(row.ID);

      that.isAdd = false;
      that.dialogVisible = true;

      that.teamCommissionScheme_employee(row.ID).then((res) => {
        that.teamEmployee = res;
      });
      that.teamCommissionScheme_comissionEmployee(row.ID).then((res) => {
        that.commissionEmployee = res;
      });
      that.loading = false;
    },
    /**  添加提成方案  */
    addCommissionScheme() {
      this.commission = {
        BeginPerformance: null, //开始
        EndPerformance: null, //结束
        Rate: null, //比例
        Fixed: null, //固定
      };
      this.isAddCommission = true;
      this.addCommissionSchemeVisible = true;
    },

    /**  编辑提成方案  */
    editCommission(row, index) {
      this.editCommissionIndex = index;
      this.commission = {
        BeginPerformance: row.BeginPerformance, //开始
        EndPerformance: row.EndPerformance, //结束
        Rate: row.Rate, //比例
        Fixed: row.Fixed, //固定
      };
      this.isAddCommission = false;
      this.addCommissionSchemeVisible = true;
    },
    /**  修改提成比例  */
    changeCommissionRate(event) {
      if (event > 100) {
        this.commission.Rate = 100;
      }
    },
    /**   保存提成方案 */
    handleSaveCommissin() {
      let that = this;
      this.$refs.commissionFormRef.validate((valid) => {
        if (valid) {
          if (that.commission.BeginPerformance - that.commission.EndPerformance > 0) {
            that.$message.error("结束数额需要大于开始数额");
            return;
          }
          // 条件不能重复
          let temp = that.formData.Commission;
          if (!that.isAddCommission) {
            temp = temp.filter((i, index) => index != that.editCommissionIndex);
          }
          // 条件不能重复
          let isRepeat = temp.every((item) => {
            const num1 = Number(item.BeginPerformance);
            const num2 = Number(item.EndPerformance);
            const num3 = Number(that.commission.BeginPerformance);
            const num4 = Number(that.commission.EndPerformance);
            if (num3 >= num2) return true;
            if (num4 <= num1) return true;
            return false;
          });
          if (!isRepeat) {
            that.$message.error({ message: "条件设置存在重复数额" });
            return;
          }
          if (that.isAddCommission) {
            that.formData.Commission.push(this.commission);
          } else {
            that.formData.Commission.splice(that.editCommissionIndex, 1, that.commission);
          }
          this.addCommissionSchemeVisible = false;
        }
      });
    },
    /**  删除提成方案  */
    deleteCommission(index) {
      this.formData.Commission.splice(index, 1);
    },
    /**   添加员工   */
    addEmployee(isCommission) {
      this.EmployeePaginations.page = 1;
      this.searchEmployeeData = {
        Name: "",
        JobID: "",
        State: true,
      };
      this.getEntityCommissionSchemeAllEmployee();
      if (this.isAdd) {
        this.addCommissionOrEmplyee(isCommission);
      } else {
        this.addCommissionOrEmplyee(isCommission);
      }
    },
    /**  删除员工  */
    handleDeleteApplyEmployee(index, isTeam) {
      if (isTeam) {
        this.commissionEmployee.splice(index, 1);
      } else {
        this.teamEmployee.splice(index, 1);
      }
    },
    /**  关闭员工选择弹窗  */
    emplyoeeBeforeClose() {
      this.$refs.emplyoeeList.clearSelection();
    },
    /**  获取员工所属门店名称组合  */
    getEntityNames(entitys = []) {
      let names = entitys.map((i) => {
        if (i.IsPrimaryEntity) {
          return i.EntityName + "[主]";
        } else {
          return i.EntityName;
        }
      });
      return names.join();
    },
    /**    */
    filterEmplyoee(emps, key) {
      let temp = emps.filter((i) => !this[key].Name || i.Name.toLowerCase().includes(this[key].Name.toLowerCase()) || i.ID == this[key].Name);
      temp = temp.filter((i) => !this[key].JobID || i.JobID == this[key].JobID);
      temp = temp.filter((i) => {
        if (this[key].State.length == 0) {
          return i;
        } else {
          return i.State == this[key].State;
        }
      });

      return temp;
    },
    /**  员工列表搜索  */
    handleEmployeeSearch() {
      this.EmployeePaginations.page = 1;
      this.getEntityCommissionSchemeAllEmployee();
    },
    /**  员工列表分页修改  */
    handleEmployeeCurrentChange(page) {
      this.EmployeePaginations.page = page;
      this.getEntityCommissionSchemeAllEmployee();
    },

    /**  添加提成员工或团队员工  */
    addCommissionOrEmplyee(isCommission) {
      this.isCommission = isCommission;
      this.commissionDialogVisible = true;
      if (isCommission) {
        this.$nextTick(() => {
          this.commissionEmployee.forEach((i) => {
            this.$refs.emplyoeeList.toggleRowSelection(i);
          });
        });
      } else {
        this.teamEmployee.forEach((i) => {
          this.$refs.emplyoeeList.toggleRowSelection(i);
        });
      }
    },

    /**   选择员工 */
    handleSelectionChange(selection) {
      this.selectionEmp = selection;
    },
    /**   保存选中员工 */
    handleSaveEmployee() {
      if (this.isCommission) {
        /**  提成员工  */
        this.commissionEmployee = this.selectionEmp.map((i) => {
          i.BelongEntity = i.Entity;
          return i;
        });
      } else {
        /**  团队员工  */
        this.teamEmployee = this.selectionEmp.map((i) => {
          i.BelongEntity = i.Entity;
          return i;
        });
      }
      this.commissionDialogVisible = false;
    },
    /**  保存员工提成方案  */
    handleSaveTeamCommissionScheme() {
      let that = this;
      this.$refs.formData.validate((valid) => {
        if (valid) {
          if (that.isAdd) {
            that.teamCommissionScheme_create();
          } else {
            this.teamCommissionScheme_update();
          }
        }
      });
    },

    /**  *************************  */

    /**  提成列表  */
    async teamCommissionScheme_all() {
      let that = this;
      let params = {
        PageNum: this.paginations.page,
      };
      that.loading = true;
      params = Object.assign(params, this.searchData);
      let res = await API.teamCommissionScheme_all(params);
      if (res.StateCode == 200) {
        this.tableData = res.List;
        this.paginations.total = res.Total;
      } else {
        that.$message.error(res.Message);
      }
      that.loading = false;
    },
    /**  提成添加  */
    async teamCommissionScheme_create() {
      let that = this;
      let params = Object.assign({}, this.formData);
      params.Employee = this.teamEmployee.map((i) => i.EmployeeID);
      params.CommissionEmployee = this.commissionEmployee.map((i) => i.EmployeeID);
      if (params.Calculation == "20") {
        params.PerformanceCalculationSchemeID = params.PerformanceEvaluationSchemeID;
      }
      that.saveLoading = true;
      let res = await API.teamCommissionScheme_create(params);
      if (res.StateCode == 200) {
        that.paginations.page = 1;
        that.teamCommissionScheme_all();
        that.$message.success("添加成功");
        that.dialogVisible = false;
      } else {
        that.$message.error(res.Message);
      }
      that.saveLoading = false;
    },
    /**  提成修改  */
    async teamCommissionScheme_update() {
      let that = this;
      let params = Object.assign({}, this.formData);
      params.Employee = this.teamEmployee.map((i) => i.EmployeeID);
      params.CommissionEmployee = this.commissionEmployee.map((i) => i.EmployeeID);
      if (params.Calculation == "20") {
        params.PerformanceCalculationSchemeID = params.PerformanceEvaluationSchemeID;
      }
      that.saveLoading = true;
      let res = await API.teamCommissionScheme_update(params);
      if (res.StateCode == 200) {
        that.paginations.page = 1;
        that.teamCommissionScheme_all();
        that.$message.success("保存成功");
        that.dialogVisible = false;
      } else {
        that.$message.error(res.Message);
      }
      that.saveLoading = false;
    },
    /**  团队成员详情  */
    async teamCommissionScheme_employee(ID) {
      let that = this;
      let params = { ID: ID };
      let res = await API.teamCommissionScheme_employee(params);
      if (res.StateCode == 200) {
        return res.Data;
      } else {
        that.$message.error(res.Message);
      }
    },
    /**  提成员工详情  */
    async teamCommissionScheme_comissionEmployee(ID) {
      let that = this;
      let params = { ID: ID };
      let res = await API.teamCommissionScheme_comissionEmployee(params);
      if (res.StateCode == 200) {
        return res.Data;
      } else {
        that.$message.error(res.Message);
      }
    },

    /**  获取详情提成方案  */
    async teamCommissionScheme_commission(ID) {
      let that = this;
      let params = { ID: ID };
      let res = await API.teamCommissionScheme_commission(params);
      if (res.StateCode == 200) {
        return res.Data;
      } else {
        that.$message.error(res.Message);
      }
    },

    /**    */
    async getEntityCommissionSchemeAllEmployee() {
      let that = this;
      let params = {
        PageNum: that.EmployeePaginations.page,
        Name: that.searchEmployeeData.Name,
        JobID: that.searchEmployeeData.JobID,
        State: that.searchEmployeeData.State,
      };
      let res = await API.getEntityCommissionSchemeAllEmployee(params);
      if (res.StateCode == 200) {
        this.emplyoeeList = res.List;
        this.EmployeePaginations.total = res.Total;
      } else {
        that.$message.error(res.Message);
      }
    },
    //初始化-获取业绩方案列表
    PerformanceSchemeValid() {
      const that = this;
      performanceAPI.PerformanceSchemeValid().then((res) => {
        if (res.StateCode == 200) {
          that.performanceScheme = res.Data;
        } else {
          that.$message.error({
            message: res.Message,
            duration: 2000,
          });
        }
      });
    },
    //获取全部职务列表
    getJobTypeAll: function () {
      var that = this;
      var params = {
        JobTypeName: that.JobTypeName,
      };
      APIJob.getJobJobtypeAll(params).then((res) => {
        if (res.StateCode == 200) {
          that.jobTypeData = res.Data;
        } else {
          that.$message.error({
            message: res.Message,
            duration: 2000,
          });
        }
      });
    },
  },
  /** 创建实例之前执行函数   */
  beforeCreate() {},
  /**  实例创建完成之后  */
  created() {},
  /**  在挂载开始之前被调用  */
  beforeMount() {},
  /**  实例被挂载后调用  */
  mounted() {
    this.teamCommissionScheme_all();
    this.PerformanceSchemeValid();
    this.getJobTypeAll();
  },
  /**  数据更新 之前 调用  */
  beforeUpdate() {},
  /** 数据更新 完成 调用   */
  updated() {},
  /**  实例销毁之前调用  */
  beforeDestroy() {},
  /**  实例销毁后调用  */
  destroyed() {},
};
</script>

<style lang="scss">
.teamCommissionScheme {
  .el-scrollbar_height {
    height: 55vh;
    .el-scrollbar__wrap {
      overflow-x: hidden;
    }
  }
}
</style>
