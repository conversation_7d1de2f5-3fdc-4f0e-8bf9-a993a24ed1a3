/**
 * Created by preference on 2022/01/19
 *  jojo
 */

import * as API from '@/api/index'

export default {
  getFollowUpMethod: params => {
    return API.POST('api/followUpMethod/all', params)
  },
  createFollowUpMethod: params => {
    return API.POST('api/followUpMethod/create', params)
  },
  updateFollowUpMethod: params => {
    return API.POST('api/followUpMethod/update', params)
  },
  moveFollowUpMethod: params => {
    return API.POST('api/followUpMethod/move', params)
  },
  getFollowUpStatus: params => {
    return API.POST('api/followUpStatus/all', params)
  },
  createFollowUpStatus: params => {
    return API.POST('api/followUpStatus/create', params)
  },
  updateFollowUpStatus: params => {
    return API.POST('api/followUpStatus/update', params)
  },
  moveFollowUpStatus: params => {
    return API.POST('api/followUpStatus/move', params)
  },
}
