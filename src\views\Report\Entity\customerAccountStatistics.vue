<template>
  <div class="ReportEntityCustomerAccountStatistics content_body" v-loading="loading">
    <div class="nav_header" style="padding: 0px">
      <el-form :inline="true" size="small" @submit.native.prevent>
        <el-form-item v-if="EntityList.length > 1" label="仓库/门店">
          <el-select v-model="EntityID" clearable filterable placeholder="请选择门店" :default-first-option="true" @change="handleSearchClick">
            <el-option v-for="item in EntityList" :key="item.ID" :label="item.EntityName" :value="item.ID"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" v-prevent-click @click="handleSearchClick">搜索</el-button>
        </el-form-item>
        <el-form-item>
          <el-button v-if="isExport" type="primary" size="small" :loading="downloadLoading" @click="downloadExcel">导出</el-button>
        </el-form-item>
      </el-form>
    </div>

    <el-table size="small" :data="tableData" show-summary :summary-method="getSummary">
      <el-table-column prop="EntityName" label="门店名称"></el-table-column>
      <el-table-column prop="TotalQuantity" label="顾客总人数"></el-table-column>
      <el-table-column prop="SavingCardCustomerQuantity" label="有剩余本金储值卡的人数"></el-table-column>
      <el-table-column prop="BalanceSavingCardTotalAmount" label="剩余本金储值卡总金额">
        <template slot-scope="scope">{{ scope.row.BalanceSavingCardTotalAmount | toFixed | NumFormat }}</template>
      </el-table-column>
      <el-table-column prop="BalanceSavingCardArrearAmount" label="本金储值卡欠款总金额">
        <template slot-scope="scope">{{ scope.row.BalanceSavingCardArrearAmount | toFixed | NumFormat }}</template>
      </el-table-column>

      <el-table-column prop="SavingCardLargessCustomerQuantity" label="有剩余赠金储值卡的人数"></el-table-column>
      <el-table-column prop="BalanceSavingCardLargessAmount" label="剩余赠金储值卡总金额">
        <template slot-scope="scope">{{ scope.row.BalanceSavingCardLargessAmount | toFixed | NumFormat }}</template>
      </el-table-column>
      <el-table-column prop="ProjectCustomerQuantity" label="有剩余非赠送疗程人数"></el-table-column>

      <el-table-column prop="ProjectValidBalance" label="剩余疗程总次数"></el-table-column>
      <el-table-column prop="ProjectTotalAmount" label="剩余疗程总金额">
        <template slot-scope="scope">{{ scope.row.ProjectTotalAmount | toFixed | NumFormat }}</template>
      </el-table-column>
      <el-table-column prop="ProjectArrearAmount" label="剩余疗程总欠款">
        <template slot-scope="scope">{{ scope.row.ProjectArrearAmount | toFixed | NumFormat }}</template>
      </el-table-column>

      <el-table-column prop="ProjectLargessCustomerQuantity" label="有赠送疗程人数"></el-table-column>
      <el-table-column prop="ProjectLargessValidBalance" label="剩余赠送疗程次数"></el-table-column>
      <el-table-column prop="ProjectLargessTotalAmount" label="剩余赠送疗程金额">
        <template slot-scope="scope">{{ scope.row.ProjectLargessTotalAmount | toFixed | NumFormat }}</template>
      </el-table-column>

      <el-table-column prop="ProductCustomerQuantity" label="有剩余非赠送产品人数"></el-table-column>
      <el-table-column prop="ProductValidBalance" label="剩余产品数量"></el-table-column>

      <el-table-column prop="ProductTotalAmount" label="剩余产品总金额">
        <template slot-scope="scope">{{ scope.row.ProductTotalAmount | toFixed | NumFormat }}</template>
      </el-table-column>
      <el-table-column prop="ProductArrearAmount" label="剩余产品总欠款">
        <template slot-scope="scope">{{ scope.row.ProductArrearAmount | toFixed | NumFormat }}</template>
      </el-table-column>
      <el-table-column prop="ProductLargessCustomerQuantity" label="有剩余赠送产品人数"></el-table-column>
      <el-table-column prop="ProductLargessValidBalance" label="剩余赠送产品数量"></el-table-column>
      <el-table-column prop="ProductLargessTotalAmount" label="剩余赠送产品金额">
        <template slot-scope="scope">{{ scope.row.ProductLargessTotalAmount | toFixed | NumFormat }}</template>
      </el-table-column>

      <el-table-column prop="BalanceCustomerTotalAmount" label="顾客剩余非赠送总金额">
        <template slot-scope="scope">{{ scope.row.BalanceCustomerTotalAmount | toFixed | NumFormat }}</template>
      </el-table-column>
      <el-table-column prop="BalanceCustomerLargessTotalAmount" label="顾客剩余赠送总金额">
        <template slot-scope="scope">{{ scope.row.BalanceCustomerLargessTotalAmount | toFixed | NumFormat }}</template>
      </el-table-column>
      <el-table-column prop="BalanceCustomerQuantity" label="顾客有剩余存量的人数"></el-table-column>
    </el-table>
    <div class="pad_15 text_right">
      <el-pagination background v-if="paginations.total > 0" @current-change="handlePageChange" :current-page.sync="paginations.page" :page-size="paginations.page_size" :layout="paginations.layout" :total="paginations.total"></el-pagination>
    </div>
  </div>
</template>

<script>
import API from "@/api/PSI/Entity/customerAccountStatistics.js";
import permission from "@/components/js/permission.js";
import APIStorage from "@/api/PSI/Purchase/storage";
export default {
  name: "ReportEntityCustomerAccountStatistics",
  beforeRouteEnter(to, from, next) {
    next((vm) => {
      vm.isExport = permission.permission(to.meta.Permission, "Report-Entity-CustomerAccountStatistics-Export");
    });
  },
  props: {},
  /**  引入的组件  */
  components: {},
  /**  Vue 实例的数据对象**/
  data() {
    return {
      isExport: false,
      loading: false,
      downloadLoading: false,
      EntityID: "",
      EntityList: [], //门店列表
      tableData: [],
      tableDataSum: {},

      //需要给分页组件传的信息
      paginations: {
        page: 1, // 当前位于哪页
        total: 0, // 总数
        page_size: 10, // 1页显示多少条
        layout: "total, prev, pager, next,jumper", // 翻页属性
      },
    };
  },
  /**计算属性  */
  computed: {},
  /**  方法集合  */
  methods: {
    /**  搜索  */
    handleSearchClick() {
      let that = this;
      that.paginations.page = 1;
      that.customerBalanceStatement_list();
    },
    /**    */
    handlePageChange(page) {
      let that = this;
      that.paginations.page = page;
      that.customerBalanceStatement_list();
    },

    /**    */
    downloadExcel() {
      let that = this;
      that.customerBalanceStatement_excel();
    },

    /**    */
    getSummary({ columns }) {
      let that = this;
      const sums = [];
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = <span class="font_weight_600">合计</span>;
          return;
        }

        var filter_NumFormat = this.$options.filters["NumFormat"];
        switch (column.property) {
          case "BalanceSavingCardTotalAmount":
          case "BalanceSavingCardArrearAmount":
          case "BalanceSavingCardLargessAmount":
          case "ProjectTotalAmount":
          case "ProjectLargessTotalAmount":
          case "ProductTotalAmount":
          case "ProductLargessTotalAmount":
          case "BalanceCustomerTotalAmount":
          case "BalanceCustomerLargessTotalAmount":
            {
              let value = that.tableDataSum ? that.tableDataSum[column.property] : 0;
              sums[index] = <span class="font_weight_600">¥ {filter_NumFormat(value)}</span>;
            }
            break;
          default:
            {
              let value_1 = that.tableDataSum ? that.tableDataSum[column.property] : 0;
              sums[index] = <span class="font_weight_600">{value_1}</span>;
            }
            break;
        }
      });
      return sums;
    },

    /**    */
    async customerBalanceStatement_list() {
      let that = this;
      let params = {
        PageNum: that.paginations.page,
        EntityID: that.EntityID,
      };
      that.loading = true;
      let res = await API.customerBalanceStatement_list(params);
      if (res.StateCode == 200) {
        that.tableData = res.Data.detail.List;
        that.tableDataSum = res.Data.customerBalanceSumStatementForm;
        that.paginations.total = res.Data.detail.Total;
      } else {
        that.$message.error(res.Message);
      }
      that.loading = false;
    },
    /**    */
    async customerBalanceStatement_excel() {
      let that = this;
      let params = {};
      that.downloadLoading = true;
      let res = await API.customerBalanceStatement_excel(params);

      this.$message.success({
        message: "正在导出",
        duration: "4000",
      });
      const link = document.createElement("a");
      let blob = new Blob([res], { type: "application/octet-stream" });
      link.style.display = "none";
      link.href = URL.createObjectURL(blob);
      link.download = "门店客户余额统计.xlsx"; //下载的文件名
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      that.downloadLoading = false;
    },

    /**  4.4.仓库列表  */
    async getStorageEntityNetwork() {
      var that = this;
      let res = await APIStorage.getpurchaseStorageEntity();
      if (res.StateCode == 200) {
        that.EntityList = res.Data;
      } else {
        that.$message.error({
          message: res.Message,
          duration: 2000,
        });
      }
    },


  },
  /** 监听数据变化   */
  watch: {},
  /** 创建实例之前执行函数   */
  beforeCreate() {},
  /**  实例创建完成之后  */
  created() {
    let that = this;
    that.customerBalanceStatement_list();
    that.getStorageEntityNetwork();
  },
  /**  在挂载开始之前被调用  */
  beforeMount() {},
  /**  实例被挂载后调用  */
  mounted() {},
  /**  数据更新 之前 调用  */
  beforeUpdate() {},
  /** 数据更新 完成 调用   */
  updated() {},
  /**  实例销毁之前调用  */
  beforeDestroy() {},
  /**  实例销毁后调用  */
  destroyed() {},
};
</script>

<style lang="scss">
.ReportEntityCustomerAccountStatistics {
}
</style>
