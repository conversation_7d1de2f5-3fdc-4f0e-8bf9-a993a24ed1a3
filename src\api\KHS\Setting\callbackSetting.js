/**
 * Created by wsf on 2022/03/28
 */
import * as API from '@/api/index'
export default {
    /* 回访计划列表 */
    getAllcallbackRule: params => {
        return API.POST('api/callbackRule/all', params)
    },
    /* 回访计划添加 */
    createcallbackRule: params => {
        return API.POST('api/callbackRule/create', params)
    },
    /* 回访计划修改 */
    updatecallbackRule: params => {
        return API.POST('api/callbackRule/update', params)
    },
    /* 回访计划删除 */
    deletecallbackRule: params => {
        return API.POST('api/callbackRule/delete', params)
    },
    /* 回访计划详情 */
    detailcallbackRule: params => {
        return API.POST('api/callbackRule/detail', params)
    },
}