/**
 * Created by preference on 2021/09/03
 *  zmx 
 */

import * as API from '@/api/index'
export default {
  /** 列表  */
  purchaseStorage_detail: params => {
    return API.POST('api/purchaseStorage/detail', params)
  },
  /** 导出  */
  purchaseStorage_excel: params => {
    return API.exportExcel('api/purchaseStorage/excel', params)
  },

  // 获取产品分类列表(可以，去除没有二级的类别)
  getValidProductCategory() {
    return API.POST('api/productCategory/valid')
  },
}