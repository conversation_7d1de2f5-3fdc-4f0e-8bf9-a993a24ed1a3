<template>
  <div class="Picture">
    <div v-if="!ContentProperty.url" class="empty">
      <div class="warp">
        <div class="f-tac">点击编辑图片</div>
        <div class="f-tac ft-16 pt-5">建议宽度750px</div>
      </div>
    </div>
    <div v-else :style="setImgContainerStyle()">
      <el-image :style="setImageStyle()" :src="ContentProperty.url"> </el-image>
    </div>
  </div>
</template>

<script>
export default {
  name: "Picture",
  props: {
    ContentProperty: {
      type: Object,
      default: () => {
        return {
          url: "",
          link: {},
        };
      },
    },
    ConfigProperty: {
      type: Object,
      default: () => {
        return {
          with: "",
          height: "",
          position: "",
          backgroundColor: "#f4f4f4",
          radius: 0,
        };
      },
    },
  },
  /** 监听数据变化   */
  watch: {},
  /**  引入的组件  */
  components: {},
  /**  Vue 实例的数据对象**/
  data() {
    return {};
  },
  /**计算属性  */
  computed: {},
  /**  方法集合  */
  methods: {
    /**    */
    setImgContainerStyle() {
      let that = this;
      return {
        width: "100%",
        height: that.ConfigProperty.height / 2 + 'px',
        position: "relative",
        display: 'flex',
        'justify-content': that.ConfigProperty.position,
        "background-color": that.ConfigProperty.backgroundColor,
      };
    },
    /**    */
    setImageStyle() {
      let that = this;
      return {
        height: that.ConfigProperty.height / 2 + 'px',
        width: that.ConfigProperty.width / 2 + 'px',
        "border-radius": that.ConfigProperty.radius + 'px',
        
      };
    },
  },
  /** 创建实例之前执行函数   */
  beforeCreate() {},
  /**  实例创建完成之后  */
  created() {},
  /**  在挂载开始之前被调用  */
  beforeMount() {},
  /**  实例被挂载后调用  */
  mounted() {},
  /**  数据更新 之前 调用  */
  beforeUpdate() {},
  /** 数据更新 完成 调用   */
  updated() {},
  /**  实例销毁之前调用  */
  beforeDestroy() {},
  /**  实例销毁后调用  */
  destroyed() {},
};
</script>

<style lang="scss">
.Picture {
  
  .empty {
    background: #f4dbb6;
    color: #fff;
    height: 180px;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-align-items: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-justify-content: center;
    -ms-flex-pack: center;
    justify-content: center;
  }
}
</style>
