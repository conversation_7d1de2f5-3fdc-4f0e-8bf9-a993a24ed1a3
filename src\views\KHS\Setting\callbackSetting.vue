<template>
  <div class="callbackSetting content_body">
    <!-- 搜索、新建 -->
    <div class="nav_header">
      <el-row>
        <el-col :span="20">
          <el-form :inline="true" size="small" @submit.native.prevent>
            <el-form-item label="规则名称">
              <el-input v-model="Name" placeholder="输入规则名称搜索" clearable @clear="handleSearch" @keyup.enter.native="handleSearch"></el-input>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleSearch" size="small" v-prevent-click>搜索</el-button>
            </el-form-item>
          </el-form>
        </el-col>
        <el-col :span="4" class="text_right">
          <el-button type="primary" size="small" v-prevent-click @click="showDialog">新增</el-button>
        </el-col>
      </el-row>
    </div>
    <!-- 列表数据 -->
    <el-table size="small" :data="tableData" tooltip-effect="light">
      <el-table-column label="规则名称" prop="Name"> </el-table-column>
      <el-table-column label="项目类型" prop="Project" show-overflow-tooltip>
        <template slot-scope="scope">
          <span v-for="(item, index) in scope.row.Project" :key="item.ID"
            >{{ item.Name }}<span v-if="!item.IsProject">(分类)</span><span v-if="index != scope.row.Project.length - 1">, </span></span
          >
        </template>
      </el-table-column>
      <el-table-column label="回访计划日期" prop="CallbackCycle">
        <template slot-scope="scope">
          <span>消耗{{ scope.row.CallbackCycle }}天后</span>
        </template>
      </el-table-column>
      <el-table-column label="回访内容" prop="CallbackContent" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.CallbackContent }}</span>
        </template>
      </el-table-column>
      <el-table-column label="回访方式" prop="MethodName"></el-table-column>
      <el-table-column label="操作" width="145px">
        <template slot-scope="scope">
          <el-button type="primary" size="small" @click="editDialog(scope.row)">编辑</el-button>
          <el-button type="danger" size="small" @click="remove(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <div class="pad_15 text_right">
      <el-pagination
        background
        v-if="paginations.total > 0"
        @current-change="handlePageChange"
        :current-page.sync="paginations.page"
        :page-size="paginations.page_size"
        :layout="paginations.layout"
        :total="paginations.total"
      ></el-pagination>
    </div>
    <!-- 回访计划弹出层 -->
    <el-dialog :title="isEdit ? '编辑回访计划' : '新增回访计划'" :visible.sync="showdialog" width="980px">
      <el-tabs v-model="activeIndex" @tab-click="handleClick">
        <el-tab-pane label="基本信息" name="1">
          <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="90px" size="small">
            <el-form-item label="规则名称" prop="Name">
              <el-input v-model="ruleForm.Name" placeholder="请输入规则名称"></el-input>
            </el-form-item>
            <el-form-item label="回访方式" prop="CallbackMethodID">
              <el-radio-group v-model="ruleForm.CallbackMethodID">
                <el-radio v-for="item in callbackMethod" :key="item.ID" :label="item.ID">{{ item.Name }}</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item>
              <span slot="label"><span style="color: red">*</span> 回访人员</span>
              <el-radio-group v-model="HandlerType" @change="groupChange">
                <el-row>
                  <el-col :span="24">
                    <el-radio :label="10">
                      <span class="marrt_5">客户服务人员</span>
                      <el-select v-model="Servicer.ServicerID" :disabled="HandlerType != 10" placeholder="请选择服务人员" clearable>
                        <el-option v-for="item in servicerEmployee" :key="item.ID" :label="item.Name" :value="item.ID"></el-option>
                      </el-select>
                      <span class="marlt_10 marrt_5">当未分配服务人员时，默认</span>
                      <el-select
                        :popper-append-to-body="false"
                        v-model="Servicer.EmployeeID"
                        filterable
                        remote
                        :remote-method="searchEmpRemote"
                        placeholder="请输入员工姓名、编号查找"
                        :disabled="HandlerType != 10"
                        clearable
                        @focus="getFocus"
                      >
                        <el-option v-for="item in allEmployee" :key="item.ID" :label="item.Name" :value="item.ID">
                          <div class="dis_flex flex_dir_column pad_5_0">
                            <div style="line-height: 25px">
                              <span style="float: left">{{ item.Name }}</span>
                              <span style="float: right; color: #8492a6; font-size: 13px">{{ item.ID }}</span>
                            </div>
                          </div>
                        </el-option>
                      </el-select>
                    </el-radio>
                  </el-col>
                </el-row>
                <el-row class="martp_10">
                  <el-col :span="24">
                    <el-radio :label="20">
                      <span class="marrt_5">项目消耗经手人</span>
                      <el-select v-model="TreatHandler.ProjectTreatHandlerID" :disabled="HandlerType != 20" placeholder="请选择项目消耗经手人" clearable>
                        <el-option v-for="item in projectConsumptionHandlerList" :key="item.ID" :label="item.Name" :value="item.ID"></el-option>
                      </el-select>
                      <span class="marlt_10 marrt_5">储值卡消耗项目经手人</span>
                      <el-select
                        v-model="TreatHandler.SavingCardProjectTreatHandlerID"
                        :disabled="HandlerType != 20"
                        placeholder="请选择储值卡消耗经手人"
                        clearable
                      >
                        <el-option v-for="item in valueCardConsumptionHandlerList" :key="item.ID" :label="item.Name" :value="item.ID"></el-option>
                      </el-select>
                      <br />
                      <span class="marlt_25 marrt_5 martp_10">当未分配消耗经手人时，默认</span>
                      <el-select
                        class="martp_10"
                        :popper-append-to-body="false"
                        v-model="TreatHandler.EmployeeID"
                        filterable
                        remote
                        :remote-method="searchEmpRemote"
                        :disabled="HandlerType != 20"
                        placeholder="请输入员工姓名、编号查找"
                        clearable
                        @focus="getFocus"
                        ><el-option v-for="item in allEmployee" :key="item.ID" :label="item.Name" :value="item.ID">
                          <div class="dis_flex flex_dir_column pad_5_0">
                            <div style="line-height: 25px">
                              <span style="float: left">{{ item.Name }}</span>
                              <span style="float: right; color: #8492a6; font-size: 13px">{{ item.ID }}</span>
                            </div>
                          </div>
                        </el-option>
                      </el-select>
                    </el-radio>
                  </el-col>
                </el-row>
                <el-row class="martp_10">
                  <el-col :span="24">
                    <el-radio :label="30">
                      <span class="marrt_5">固定回访人</span>
                      <el-select
                        :popper-append-to-body="false"
                        v-model="EmployeeID"
                        filterable
                        remote
                        :remote-method="searchEmpRemote"
                        clearable
                        :disabled="HandlerType != 30"
                        placeholder="请输入员工姓名、编号查找"
                        @focus="getFocus"
                        ><el-option v-for="item in allEmployee" :key="item.ID" :label="item.Name" :value="item.ID">
                          <div class="dis_flex flex_dir_column pad_5_0">
                            <div style="line-height: 25px">
                              <span style="float: left">{{ item.Name }}</span>
                              <span style="float: right; color: #8492a6; font-size: 13px">{{ item.ID }}</span>
                            </div>
                          </div>
                        </el-option>
                      </el-select>
                    </el-radio>
                  </el-col>
                </el-row>
              </el-radio-group>
            </el-form-item>
          </el-form>
        </el-tab-pane>
        <el-tab-pane label="回访周期" name="2">
          <el-form size="small">
            <el-form-item>
              <el-button type="primary" size="small" v-prevent-click @click="showCycleDialog">新增回访周期</el-button>
            </el-form-item>
          </el-form>
          <el-table size="small" :data="Plan" tooltip-effect="light">
            <el-table-column label="回访计划日期" prop="CallbackCycle">
              <template slot-scope="scope">
                <span>消耗{{ scope.row.CallbackCycle }}天后</span>
              </template>
            </el-table-column>
            <el-table-column label="回访内容" prop="CallbackContent" show-overflow-tooltip width="400px"></el-table-column>
            <el-table-column label="操作" width="145px">
              <template slot-scope="scope">
                <el-button type="primary" size="small" @click="editCycleDialog(scope)" v-prevent-click>编辑</el-button>
                <el-button type="danger" size="small" @click="removePlan(scope.$index)" v-prevent-click>删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
        <el-tab-pane label="回访项目" name="3">
          <el-row :gutter="20" class="marbm_10">
            <el-col :span="8">
              <el-input placeholder="输入商品名称/分类名称搜索" size="small" v-model="ProjectName" clearable></el-input>
            </el-col>
            <el-col :span="12">
              <el-button type="primary" size="small" @click="showconfigureDialog">配置项目</el-button>
            </el-col>
          </el-row>
          <el-table
            size="small"
            max-height="400"
            :data="ProjectList.filter((data) => !ProjectName || data.Name.toLowerCase().includes(ProjectName.toLowerCase()))"
          >
            <el-table-column prop="Name" label="商品名称/分类名称">
              <template slot-scope="scope">
                <span>{{ scope.row.Name }}</span>
                <el-tag type="info" size="mini" v-if="!scope.row.IsProject">分类</el-tag>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
        <el-tab-pane label="适用组织" name="4">
          <div class="message el-message--info marbm_10">
            <i class="el-message__icon el-icon-info"></i>
            <p class="el-message__content">适用于同级所有节点，则只需勾选父节点。比如：适用于所有节点，只需勾选“顶级/第一个”节点。</p>
          </div>
          <el-scrollbar class="el-scrollbar_height">
            <el-tree
              ref="tree"
              :expand-on-click-node="false"
              :check-on-click-node="true"
              :check-strictly="true"
              :data="entityList"
              show-checkbox
              node-key="ID"
              :default-checked-keys="defaultCheckedKeys"
              :default-expanded-keys="defaultExpandedKeys"
              :props="scopeDefaultProps"
            ></el-tree>
          </el-scrollbar>
        </el-tab-pane>
      </el-tabs>
      <span slot="footer" class="dialog-footer">
        <el-button @click="showdialog = false" size="small">取 消</el-button>
        <el-button type="primary" :loading="modalLoading" @click="savePlan('ruleForm')" v-prevent-click size="small">保 存</el-button>
      </span>
    </el-dialog>
    <!-- 回访周期弹出层 -->
    <el-dialog :title="isCycleEdit ? '编辑回访周期' : '新增回访周期'" :visible.sync="cycleDialog" width="650px" class="cycleDialog">
      <el-form :model="PlanRuleForm" :rules="PlanRules" ref="PlanRuleForm" label-width="90px" size="small">
        <el-form-item label="回访计划" prop="CallbackCycle">
          <span class="marrt_5">消耗项目</span>
          <el-input v-model="PlanRuleForm.CallbackCycle" placeholder="请输入回访周期" type="number"> </el-input>
          <span class="marlt_5">天后，创建回访任务</span>
        </el-form-item>
        <el-form-item label="回访内容">
          <el-input
            v-model="PlanRuleForm.CallbackContent"
            type="textarea"
            placeholder="请输入回访内容"
            show-word-limit
            maxlength="200"
            :autosize="{ minRows: 6, maxRows: 8 }"
          ></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="cycleDialog = false" size="small">取 消</el-button>
        <el-button type="primary" @click="savePlanRuleForm('PlanRuleForm')" v-prevent-click size="small">保 存</el-button>
      </span>
    </el-dialog>
    <!-- 配置项目弹出层 -->
    <el-dialog title="配置项目" :visible.sync="configureDialog" width="750px">
      <template>
        <el-row>
          <el-col :span="8">
            <el-input placeholder="输入关键字进行搜索" v-model="filterText" size="small" clearable></el-input>
            <el-scrollbar class="el-scrollbar_height">
              <el-tree
                ref="treeProject"
                :expand-on-click-node="false"
                :check-on-click-node="true"
                :check-strictly="true"
                :data="applyProject"
                show-checkbox
                accordion
                node-key="ID"
                :default-checked-keys="projectDefaultCheckedKeys"
                :default-expanded-keys="projectDefaultExpandedKeys"
                :props="{ children: 'Child', label: 'Name' }"
                :filter-node-method="filterNode"
                @check="changeProjectData"
              >
                <span slot-scope="{ data }">
                  <span>{{ data.Name }}</span>
                  <el-tag class="marlt_5" type="info" size="mini" v-if="!data.IsProject">分类</el-tag>
                </span>
              </el-tree>
            </el-scrollbar>
          </el-col>
          <el-col :span="16" class="border_left">
            <el-table
              size="small"
              :data="selectProject.filter((data) => !filterText || data.Name.toLowerCase().includes(filterText.toLowerCase()))"
              max-height="500px"
            >
              <el-table-column prop="Name" label="商品名称/分类名称">
                <template slot-scope="scope">
                  <span>{{ scope.row.Name }}</span>
                  <el-tag type="info" size="mini" v-if="!scope.row.IsProject">分类</el-tag>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="80px">
                <template slot-scope="scope">
                  <el-button type="danger" size="small" @click="deleteSelectRow(scope.row, scope.$index)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-col>
        </el-row>
      </template>
      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="configureDialog = false" v-prevent-click>取 消</el-button>
        <el-button type="primary" size="small" @click="submitProject" v-prevent-click>确 认</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
var Enumerable = require("linq");
import APIEntity from "@/api/KHS/Entity/entity";
import APICallback from "@/api/KHS/Setting/callbackConfig";
import APIServicer from "@/api/CRM/Servicer/servicerConfig";
import APIProjectTreat from "@/api/iBeauty/HanderCommission/projectTreatHandler";
import APICardProject from "@/api/iBeauty/HanderCommission/cardProjectTreatHandler";
import APIGeneralCard from "@/api/iBeauty/Goods/generalCard";
import APIFollowUp from "@/api/iBeauty/Workbench/followUp";
import API from "@/api/KHS/Setting/callbackSetting";

export default {
  name: "CallbackSetting",

  props: {},
  /** 监听数据变化   */
  watch: {
    filterText(val) {
      this.$refs.treeProject.filter(val);
    },
  },
  /**  引入的组件  */
  components: {},
  /**  Vue 实例的数据对象**/
  data() {
    return {
      isEdit: false,
      isCycleEdit: false,
      loading: false,
      modalLoading: false,
      showdialog: false, // 回访计划弹出层
      cycleDialog: false, // 回访周期设置弹出层
      configureDialog: false, // 配置项目弹出层
      HandlerType: 10,
      CallbackRuleID: "", // 规则ID
      Name: "", // 规则名称
      tableData: [], // 列表数据
      callbackMethod: [], // 回访方式
      callbackProject: [], // 回访项目
      entityList: [], // 适用组织
      filterText: "", // 适用项目搜索
      servicerEmployee: [], // 服务人员
      projectConsumptionHandlerList: [], // 项目消耗经手人
      valueCardConsumptionHandlerList: [], // 储值卡消耗经手人
      allEmployee: [], // 所有员工
      activeIndex: "1", // tabs页签
      ProjectName: "", // 回访项目搜索
      defaultCheckedKeys: [],
      defaultExpandedKeys: [1],
      projectDefaultCheckedKeys: [],
      projectDefaultExpandedKeys: [1],
      Plan: [], // 回访周期
      ProjectList: [], // 选中项目
      selectProject: [],
      applyProject: [], // 适用项目数据
      PlanRuleForm: {
        CallbackCycle: "",
        CallbackContent: "",
      },
      Servicer: {
        ServicerID: null,
        EmployeeID: null,
      }, // 服务人员
      TreatHandler: {
        ProjectTreatHandlerID: null,
        SavingCardProjectTreatHandlerID: null,
        EmployeeID: null,
      }, // 经手人
      EmployeeID: "", // 固定人员
      PlanIndex: "",
      ruleForm: {
        Name: "",
        CallbackMethodID: "",
      },
      rules: {
        Name: [{ required: true, message: "请输入规则名称", trigger: "blur" }],
        CallbackMethodID: [{ required: true, message: "请选择回访方式", trigger: "change" }],
        huifangID: [{ required: true, message: " ", trigger: "change" }],
      },
      PlanRules: {
        CallbackCycle: [{ required: true, message: "请输入回访计划周期", trigger: "blur" }],
      },
      paginations: {
        page: 1, // 当前位于哪页
        total: 0, // 总数
        page_size: 10, // 1页显示多少条
        layout: "total, prev, pager, next,jumper", // 翻页属性
      }, //需要给分页组件传的信息
      scopeDefaultProps: {
        children: "Child",
        label: "EntityName",
      }, // 适用组织配置项
    };
  },
  /**计算属性  */
  computed: {},
  /**  方法集合  */
  methods: {
    filterNode(value, data) {
      if (!value) return true;
      return data.Name.indexOf(value) !== -1;
    },
    /* 搜索 */
    handleSearch() {
      let that = this;
      that.paginations.page = 1;
      that.getAllcallbackRule();
    },
    /* 分页 */
    handlePageChange(page) {
      let that = this;
      that.paginations.page = page;
      that.getAllcallbackRule();
    },
    getFocus() {
      let that = this;
      that.allEmployee = [];
    },
    /* 回访人员类型切换 */
    groupChange() {
      let that = this;
      that.Servicer = {
        ServicerID: null,
        EmployeeID: null,
      }; // 服务人员
      that.TreatHandler = {
        ProjectTreatHandlerID: null,
        SavingCardProjectTreatHandlerID: null,
        EmployeeID: null,
      }; // 经手人
      that.EmployeeID = null; // 固定人员
    },
    /* 获取回访规则列表 */
    getAllcallbackRule() {
      let that = this;
      that.loading = true;
      let params = {
        Name: that.Name,
        PageNum: that.paginations.page,
      };
      API.getAllcallbackRule(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.tableData = res.List;
            that.paginations.total = res.Total;
            that.paginations.page_size = res.PageSize;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },
    /* 删除回访规则 */
    remove(row) {
      let that = this;
      that
        .$confirm("此操作将删除该回访规划, 是否继续?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
        .then(() => {
          let params = {
            CallbackPlanID: row.CallbackPlanID,
            CallbackRuleID: row.CallbackRuleID,
          };
          API.deletecallbackRule(params).then((res) => {
            if (res.StateCode == 200) {
              that.$message({
                type: "success",
                message: "删除成功!",
              });
              that.getAllcallbackRule();
            } else {
              that.$message.error({
                message: res.Message,
                duration: 2000,
              });
            }
          });
        })
        .catch(() => {
          that.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    /* 新增 */
    showDialog() {
      let that = this;
      that.isEdit = false;
      if (this.$refs.ruleForm) {
        that.$refs["ruleForm"].resetFields();
      }
      that.ruleForm = {
        Name: "",
        CallbackMethodID: "",
      };
      that.Plan = []; // 回访周期
      that.ProjectList = []; // 选中项目
      that.PlanRuleForm = {
        CallbackCycle: "",
        CallbackContent: "",
      };
      that.Servicer = {
        ServicerID: null,
        EmployeeID: null,
      }; // 服务人员
      that.TreatHandler = {
        ProjectTreatHandlerID: null,
        SavingCardProjectTreatHandlerID: null,
        EmployeeID: null,
      }; // 经手人
      that.EmployeeID = ""; // 固定人员
      that.HandlerType = 10;
      that.activeIndex = "1"; // tabs页签
      that.ProjectName = ""; // 回访项目搜索
      that.defaultCheckedKeys = [];
      that.defaultExpandedKeys = [1];
      that.showdialog = true;
    },
    /* 编辑 */
    editDialog(row) {
      let that = this;
      that.isEdit = true;
      that.activeIndex = "1"; // tabs页签
      that.ProjectName = ""; // 回访项目搜索
      that.CallbackRuleID = row.CallbackRuleID;
      that.defaultCheckedKeys = [];
      that.defaultExpandedKeys = [1];
      that.detailcallbackRule(row.CallbackRuleID);
    },
    /* 回访计划详情 */
    detailcallbackRule(ID) {
      let that = this;
      let params = {
        CallbackRuleID: ID,
      };
      API.detailcallbackRule(params).then((res) => {
        if (res.StateCode == 200) {
          that.ruleForm.Name = res.Data.Name;
          that.ruleForm.CallbackMethodID = res.Data.CallbackMethodID;
          that.HandlerType = Number(res.Data.HandlerType);
          that.Servicer.ServicerID = res.Data.ServicerID;
          that.TreatHandler.ProjectTreatHandlerID = res.Data.ProjectTreatHandlerID;
          that.TreatHandler.SavingCardProjectTreatHandlerID = res.Data.SavingCardProjectTreatHandlerID;
          that.ProjectList = Object.assign([], res.Data.Project);
          that.Plan = Object.assign([], res.Data.Plan);
          that.defaultCheckedKeys = res.Data.Entity;
          that.defaultExpandedKeys = res.Data.Entity;
          if (res.Data.HandlerType == 10) {
            that.Servicer.EmployeeID = res.Data.EmployeeID;
          }
          if (res.Data.HandlerType == 20) {
            that.TreatHandler.EmployeeID = res.Data.EmployeeID;
          }
          if (res.Data.HandlerType == 30) {
            that.EmployeeID = res.Data.EmployeeID;
          }
          if (res.Data.EmployeeID) {
            that.getallEmployee(res.Data.EmployeeID);
          }
          that.showdialog = true;
        } else {
          that.$message.error({
            message: res.Message,
            duration: 2000,
          });
        }
      });
    },
    /* tabs切换 */
    handleClick() {
      let that = this;
      if (that.activeIndex == 4) {
        that.$refs.tree.setCheckedKeys(that.defaultCheckedKeys);
      }
    },
    /* 保存回访计划 */
    savePlan(ruleForm) {
      let that = this;
      that.$refs[ruleForm].validate((valid) => {
        if (valid) {
          if (
            (that.HandlerType == 10 && that.Servicer.ServicerID == null && that.Servicer.EmployeeID == null) ||
            (that.HandlerType == 20 &&
              that.TreatHandler.ProjectTreatHandlerID == null &&
              that.TreatHandler.ServicerID == null &&
              that.SavingCardProjectTreatHandlerID.EmployeeID == null) ||
            (that.HandlerType == 30 && that.EmployeeID == null)
          ) {
            that.$message({
              type: "info",
              message: "请选择回访人员!",
            });
          } else {
            if (that.isEdit) {
              that.updatecallbackRule();
            } else {
              that.createcallbackRule();
            }
          }
        }
      });
    },
    /* 新增保存 */
    createcallbackRule() {
      let that = this;
      that.modalLoading = true;
      let Project = [];
      let ProjectCategory = [];
      that.defaultCheckedKeys = that.$refs.tree.getCheckedKeys();
      that.ProjectList.forEach((item) => {
        if (item.IsProject) {
          Project.push(item.ID);
        } else {
          ProjectCategory.push(item.ID);
        }
      });
      let params = {
        Name: that.ruleForm.Name, //规则名称
        CallbackMethodID: that.ruleForm.CallbackMethodID, //规则方式
        HandlerType: that.HandlerType, //回访人类型（10：服务人员，20：消耗经手人，30：固定回访人）
        ServicerID: null, //服务人员ID
        ProjectTreatHandlerID: null, //项目经手人ID
        SavingCardProjectTreatHandlerID: null, //储值卡消耗项目经手人
        EmployeeID: null, //固定回访人
        Plan: that.Plan, //回访计划
        Project, //回访项目集合
        ProjectCategory, //回访项目分类
        Entity: that.defaultCheckedKeys, //回访适应组织
      };
      if (that.HandlerType == 10) {
        params.ServicerID = that.Servicer.ServicerID;
        params.EmployeeID = that.Servicer.EmployeeID;
      } else if (that.HandlerType == 20) {
        params.ProjectTreatHandlerID = that.TreatHandler.ProjectTreatHandlerID;
        params.SavingCardProjectTreatHandlerID = that.TreatHandler.SavingCardProjectTreatHandlerID;
        params.EmployeeID = that.TreatHandler.EmployeeID;
      } else {
        params.EmployeeID = that.EmployeeID;
      }
      API.createcallbackRule(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.$message.success({
              message: "回访规则新增成功",
              duration: 2000,
            });
            that.showdialog = false;
            that.getAllcallbackRule();
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.modalLoading = false;
        });
    },
    /* 编辑保存 */
    updatecallbackRule() {
      let that = this;
      that.modalLoading = true;
      let Project = [];
      let ProjectCategory = [];
      that.defaultCheckedKeys = that.$refs.tree.getCheckedKeys();
      that.ProjectList.forEach((item) => {
        if (item.IsProject) {
          Project.push(item.ID);
        } else {
          ProjectCategory.push(item.ID);
        }
      });
      let params = {
        CallbackRuleID: that.CallbackRuleID,
        Name: that.ruleForm.Name, //规则名称
        CallbackMethodID: that.ruleForm.CallbackMethodID, //规则方式
        HandlerType: that.HandlerType, //回访人类型（10：服务人员，20：消耗经手人，30：固定回访人）
        ServicerID: null, //服务人员ID
        ProjectTreatHandlerID: null, //项目经手人ID
        SavingCardProjectTreatHandlerID: null, //储值卡消耗项目经手人
        EmployeeID: null, //固定回访人
        Plan: that.Plan, //回访计划
        Project, //回访项目集合
        ProjectCategory, //回访项目分类
        Entity: that.defaultCheckedKeys, //回访适应组织
      };
      if (that.HandlerType == 10) {
        params.ServicerID = that.Servicer.ServicerID;
        params.EmployeeID = that.Servicer.EmployeeID;
      } else if (that.HandlerType == 20) {
        params.ProjectTreatHandlerID = that.TreatHandler.ProjectTreatHandlerID;
        params.SavingCardProjectTreatHandlerID = that.TreatHandler.SavingCardProjectTreatHandlerID;
        params.EmployeeID = that.TreatHandler.EmployeeID;
      } else {
        params.EmployeeID = that.EmployeeID;
      }
      API.updatecallbackRule(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.$message.success({
              message: "回访规则编辑成功",
              duration: 2000,
            });
            that.showdialog = false;
            that.getAllcallbackRule();
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.modalLoading = false;
        });
    },
    /* 回访周期新增 */
    showCycleDialog() {
      let that = this;
      if (this.$refs.PlanRuleForm) {
        that.$refs["PlanRuleForm"].resetFields();
      }
      that.cycleDialog = true;
      that.isCycleEdit = false;
      that.PlanRuleForm = {
        CallbackCycle: "",
        CallbackContent: "",
      };
    },
    /* 编辑回访周期 */
    editCycleDialog(scope) {
      let that = this;
      if (this.$refs.PlanRuleForm) {
        that.$refs["PlanRuleForm"].resetFields();
      }
      that.cycleDialog = true;
      that.isCycleEdit = true;
      that.PlanIndex = scope.$index;
      that.PlanRuleForm.CallbackCycle = scope.row.CallbackCycle;
      that.PlanRuleForm.CallbackContent = scope.row.CallbackContent;
    },
    /* 保存回访周期 */
    savePlanRuleForm(PlanRuleForm) {
      let that = this;
      that.$refs[PlanRuleForm].validate((valid) => {
        if (valid) {
          if (that.isCycleEdit) {
            let index = that.PlanIndex;
            that.Plan.splice(index, 1, { ...that.PlanRuleForm });
          } else {
            that.Plan.push({ ...that.PlanRuleForm });
          }
          that.cycleDialog = false;
          that.$refs["PlanRuleForm"].resetFields();
        }
      });
    },
    /* 删除回访周期 */
    removePlan(index) {
      let that = this;
      that
        .$confirm("此操作将删除该回访周期, 是否继续?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
        .then(() => {
          that.Plan.splice(index, 1);
          that.$message({
            type: "success",
            message: "删除成功!",
          });
        })
        .catch(() => {
          that.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    /* 配置适用项目 */
    showconfigureDialog() {
      let that = this;
      that.configureDialog = true;
      that.selectProject = [];
      that.projectDefaultExpandedKeys = [1];
      that.selectProject = Object.assign([], that.ProjectList);
      let projectDefaultCheckedKeys = Enumerable.from(that.selectProject)
        .select((val) => val.ID)
        .toArray();
      that.$nextTick(() => {
        that.$refs.treeProject.setCheckedKeys(projectDefaultCheckedKeys);
      });
      that.projectDefaultCheckedKeys = projectDefaultCheckedKeys;
      that.projectDefaultExpandedKeys = projectDefaultCheckedKeys;
    },
    /* 适用项目保存 */
    submitProject() {
      let that = this;
      that.ProjectList = that.selectProject;
      that.configureDialog = false;
    },
    // 项目选择状态变化
    changeProjectData(val, val1) {
      let that = this;
      let projectDefaultCheckedKeys = Enumerable.from(val1.checkedNodes)
        .select((val) => ({
          PID: val.ParentID,
          ID: val.ID,
          IsProject: val.IsProject,
          Name: val.Name,
        }))
        .toArray();
      that.selectProject = projectDefaultCheckedKeys;
    },
    /* 删除选择项目 */
    deleteSelectRow(row, index) {
      let that = this;
      that.selectProject.splice(index, 1);
      let projectDefaultCheckedKeys = Enumerable.from(that.selectProject)
        .select((val) => val.ID)
        .toArray();
      that.$refs.treeProject.setCheckedKeys(projectDefaultCheckedKeys);
    },
    /* 获取回访方式 */
    getAllCallbackMethod() {
      let that = this;
      let params = {
        Name: "",
        Active: true,
      };
      APICallback.getAllCallbackMethod(params).then((res) => {
        if (res.StateCode == 200) {
          that.callbackMethod = res.Data;
        } else {
          that.$message.error({
            message: res.Message,
            duration: 2000,
          });
        }
      });
    },
    /* 获取组织数据 */
    getEntityData: function () {
      let that = this;
      var params = {
        SearchKey: "",
      };
      APIEntity.getEntity(params).then((res) => {
        if (res.StateCode == 200) {
          that.entityList = res.Data;
        } else {
          that.$message.error({
            message: res.Message,
            duration: 2000,
          });
        }
      });
    },
    /* 获取服务人员列表 */
    getServiceList() {
      let that = this;
      let params = {
        Name: "",
        AddWhetherToShow: "",
        Active: true,
      };
      APIServicer.getServiceList(params).then((res) => {
        if (res.StateCode == 200) {
          that.servicerEmployee = res.Data;
        } else {
          that.$message.error({
            message: res.Message,
            duration: 2000,
          });
        }
      });
    },
    // 获取项目消耗经手人
    getProjectTreatHandler: function () {
      let that = this;
      let params = {
        Name: "",
        Active: true,
        EntityID: "",
      };
      APIProjectTreat.getProjectTreatHandler(params).then((res) => {
        if (res.StateCode == 200) {
          that.projectConsumptionHandlerList = res.Data;
        } else {
          that.$message.error({
            message: res.Message,
            duration: 2000,
          });
        }
      });
    },
    // 获取储值卡消耗经手人
    getCardProjectTreatHandler: function () {
      let that = this;
      let params = {
        Name: "",
        Active: true,
        EntityID: "",
      };
      APICardProject.getCardProjectTreatHandler(params).then((res) => {
        if (res.StateCode == 200) {
          that.valueCardConsumptionHandlerList = res.Data;
        } else {
          that.$message.error({
            message: res.Message,
            duration: 2000,
          });
        }
      });
    },
    // 获取项目分类以及分类下项目
    getFindCategoryAndProject: function () {
      let that = this;
      let params = {
        IsAllowLargess: "",
      };
      APIGeneralCard.getFindCategoryAndProject(params).then((res) => {
        if (res.StateCode == 200) {
          that.applyProject = res.Data;
        } else {
          that.$message.error({
            message: res.Message,
            duration: 2000,
          });
        }
      });
    },
    /* 获取固定人员 */
    getallEmployee(SearchKey) {
      let that = this;
      let params = {
        SearchKey: SearchKey,
      };
      APIFollowUp.getSearch(params).then((res) => {
        if (res.StateCode == 200) {
          that.allEmployee = res.Data;
        } else {
          that.$message.error({
            message: res.Message,
            duration: 2000,
          });
        }
      });
    },
    /**  搜索固定人员  */
    searchEmpRemote(query) {
      let that = this;
      if (query) {
        that.getallEmployee(query);
      } else {
        that.allEmployee = [];
      }
    },
  },
  /** 创建实例之前执行函数   */
  beforeCreate() {},
  /**  实例创建完成之后  */
  created() {},
  /**  在挂载开始之前被调用  */
  beforeMount() {},
  /**  实例被挂载后调用  */
  mounted() {
    let that = this;
    that.getAllcallbackRule();
    that.getAllCallbackMethod();
    that.getEntityData();
    that.getServiceList();
    that.getProjectTreatHandler();
    that.getCardProjectTreatHandler();
    that.getFindCategoryAndProject();
  },
  /**  数据更新 之前 调用  */
  beforeUpdate() {},
  /** 数据更新 完成 调用   */
  updated() {},
  /**  实例销毁之前调用  */
  beforeDestroy() {},
  /**  实例销毁后调用  */
  destroyed() {},
};
</script>

<style lang="scss">
.callbackSetting {
  .el-scrollbar_height {
    height: 60vh;
    .el-scrollbar__wrap {
      overflow-x: hidden;
    }
  }
  .cycleDialog {
    .el-input__inner {
      padding: 0 0 0 15px;
    }
    .el-dialog .el-input {
      width: 150px !important;
    }
  }
  // .personnel {
  //   .el-radio__label {
  //     display: none;
  //   }
  //   .el-form-item--small.el-form-item {
  //     margin-bottom: 10px !important;
  //   }
  // }
}
</style>
