<template>
  <div class="content_body PassengerFlowStatistics" v-loading="loading">
    <!-- 筛选 -->
    <div class="nav_header">
      <el-form :inline="true" size="small" :model="searchFrom" @submit.native.prevent>
        <el-form-item label="时间筛选">
          <el-date-picker v-model="searchFrom.QueryDate" :picker-options="pickerOptions" unlink-panels type="daterange"
            range-separator="至" value-format="yyyy-MM-dd" start-placeholder="开始日期" end-placeholder="结束日期"
            @change="handleSearch"></el-date-picker>
        </el-form-item>
        <el-form-item label="员工姓名">
          <el-input v-model="searchFrom.Name" clearable @keyup.enter.native="handleSearch" @clear="handleSearch"
            placeholder="请输入员工姓名"></el-input>
        </el-form-item>
        <el-form-item v-if="storeEntityList.length > 1" label="所属门店">
          <el-select v-model="searchFrom.EntityID" clearable filterable placeholder="请选择员工所属门店"
            :default-first-option="true" @change="handleSearch">
            <el-option v-for="item in storeEntityList" :key="item.ID" :label="item.EntityName"
              :value="item.ID"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="员工职务">
          <el-select v-model="searchFrom.JobID" multiple collapse-tags clearable filterable placeholder="请选择员工职务"
            :default-first-option="true" @change="handleSearch" @remove-tag="handleSearch">
            <el-option v-for="item in jobTypeData" :label="item.JobName" :value="item.ID" :key="item.ID"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" v-prevent-click @click="handleSearch">搜索</el-button>
        </el-form-item>
        <el-form-item>
          <el-button v-if="EmployeeExport" type="primary" size="small" :loading="downloadLoading"
            @click="downloadExcel">导出</el-button>
        </el-form-item>
      </el-form>
    </div>
    <!-- 表格 -->
    <el-table size="small" show-summary :summary-method="getDetailListSummaries" :data="result.list">
      <el-table-column prop="Name" label="员工姓名"></el-table-column>
      <el-table-column prop="ID" label="员工编号" ></el-table-column>
      <el-table-column prop="JobName" label="员工职务"></el-table-column>
      <el-table-column prop="EntityName" label="员工所属门店"></el-table-column>
      <el-table-column prop="EmployeePassengerFlow" label="客流"></el-table-column>
      <el-table-column prop="EmployeeSalePassengerFlow" label="销售客流"></el-table-column>
      <el-table-column prop="EmployeeTreatPassengerFlow" label="服务客流"></el-table-column>
    </el-table>
    <!-- 分页 -->
    <div class="pad_15 text_right">
      <el-pagination background v-if="Paginations.total > 0" @current-change="PageChange"
        :current-page.sync="Paginations.page" :page-size="Paginations.page_size" :layout="Paginations.layout"
        :total="Paginations.total"></el-pagination>
    </div>
  </div>
</template>

<script>
import API from "@/api/Report/Employee/passengerFlowStatistics";
import APIJob from "@/api/KHS/Entity/jobtype";
import EntityAPI from "@/api/Report/Common/entity";
import dateTime from "@/components/js/date";
import permission from "@/components/js/permission.js";
const dayjs = require("dayjs");
const isoWeek = require("dayjs/plugin/isoWeek");
dayjs.extend(isoWeek);

export default {
  name: "PassengerFlowStatistics",

  components: {},

  directives: {},

  data () {
    return {
      loading: false,
      downloadLoading: false,
      jobTypeData: [], //职务
      storeEntityList: [], //门店
      searchFrom: {
        Name: "",
        EntityID: "",
        JobID: "",
        QueryDate: [],
      },
      result: {
        list: [],
        totalForm: {},
      },
      Paginations: {
        page: 1, // 当前位于哪页
        total: 0, // 总数
        page_size: 10, // 1页显示多少条
        layout: "total, prev, pager, next,jumper", // 翻页属性
      },
      passengerFlowStatistics: [],
      EmployeeExport: false,
      pickerOptions: {
        shortcuts: [
          {
            text: "今天",
            onClick: (picker) => {
              let end = new Date();
              let start = new Date();
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "本周",
            onClick: (picker) => {
              let end = new Date();
              let weekDay = dayjs(end).isoWeekday();
              let start = dayjs(end)
                .subtract(weekDay - 1, "day")
                .toDate();
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "本月",
            onClick: (picker) => {
              let end = new Date();
              let start = dayjs(dayjs(end).format("YYYY-MM") + "01").toDate();
              picker.$emit("pick", [start, end]);
            },
          },
        ],
      },
    };
  },
  created () {
    this.searchFrom.QueryDate = [dateTime.formatDate.format(new Date(), "YYYY-MM-DD"), dateTime.formatDate.format(new Date(), "YYYY-MM-DD")];
  },
  mounted () {
    const that = this;
    //   导出权限
    that.EmployeeExport = permission.permission(that.$route.meta.Permission, "Report-Employee-PassengerFlowStatistics-Export");
    that.getJobType();
    that.getstoreEntityList();
    that.getOrderList();
  },

  methods: {
    // 员工职务
    getJobType: function () {
      var that = this;
      var params = {
        JobTypeName: that.JobTypeName,
      };
      APIJob.getJobJobtypeAll(params).then((res) => {
        if (res.StateCode == 200) {
          that.jobTypeData = res.Data;
        } else {
          that.$message.error({
            message: res.Message,
            duration: 2000,
          });
        }
      });
    },
    //获得当前用户下的权限门店
    getstoreEntityList () {
      var that = this;
      EntityAPI.getStoreEntityList()
        .then((res) => {
          if (res.StateCode == 200) {
            that.storeEntityList = res.Data;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () { });
    },
    // 获取订单列表
    getOrderList () {
      const that = this;
      if (!that.searchFrom) return;
      if (that.searchFrom["QueryDate"] != null && dayjs(that.searchFrom.QueryDate[0]).add(366, "day").valueOf() < dayjs(that.searchFrom.QueryDate[1]).valueOf()) {
        that.$message.error("时间筛选范围不能超366天");
        return;
      }
      const params = {
        PageNum: that.Paginations.page,
        EntityID: that.searchFrom.EntityID, //门店
        Name: that.searchFrom.Name, //员工姓名搜索
        StartTime: that.searchFrom.QueryDate ? that.searchFrom.QueryDate[0] : "", //开始时间
        EndTime: that.searchFrom.QueryDate ? that.searchFrom.QueryDate[1] : "", //结束时间
        Jobs: that.searchFrom.JobID, //职位
      };
      that.loading = true;
      API.EmployeePassengerFlowStatementList(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.result.totalForm = res.Data.employeePassengerFlowSumStatementForm;
            that.result.list = res.Data.detail.List;
            that.Paginations.total = res.Data.detail.Total;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(() => {
          that.loading = false;
        });
    },
    // 搜素
    handleSearch () {
      var that = this;
      that.Paginations.page = 1;
      that.getOrderList();
    },
    //   导出
    downloadExcel () {
      const that = this;
      if (that.searchFrom["QueryDate"] != null && dayjs(that.searchFrom.QueryDate[0]).add(366, "day").valueOf() < dayjs(that.searchFrom.QueryDate[1]).valueOf()) {
        that.$message.error("时间筛选范围不能超366天");
        return;
      }
      that.downloadLoading = true;
      const params = {
        EntityID: that.searchFrom.EntityID, //门店
        Name: that.searchFrom.Name, //员工姓名搜索
        StartTime: that.searchFrom.QueryDate[0], //开始时间
        EndTime: that.searchFrom.QueryDate[1], //结束时间
        JobID: that.searchFrom.JobID, //职位
      };
      API.EmployeePassengerFlowStatementExcel(params)
        .then((res) => {
          this.$message.success({
            message: "正在导出",
            duration: "4000",
          });
          const link = document.createElement("a");
          let blob = new Blob([res], { type: "application/octet-stream" });
          link.style.display = "none";
          link.href = URL.createObjectURL(blob);
          link.download = "员工客流.xlsx"; //下载的文件名
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
        })
        .finally(() => {
          that.downloadLoading = false;
        });
    },
    // 分页
    PageChange () {
      this.getOrderList();
    },
    // 合计
    getDetailListSummaries (param) {
      const { columns } = param;
      const sums = [];
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = <span class="font_weight_600">合计</span>;
          return;
        }
        // var filter_NumFormat = this.$options.filters["NumFormat"];
        switch (column.property) {
          case "EmployeePassengerFlow":
            sums[index] = <span class="font_weight_600">{this.result.totalForm.EmployeePassengerFlow}</span>;
            break;
          case "EmployeeSalePassengerFlow":
            sums[index] = <span class="font_weight_600">{this.result.totalForm.EmployeeSalePassengerFlow}</span>;
            break;
          case "EmployeeTreatPassengerFlow":
            sums[index] = <span class="font_weight_600">{this.result.totalForm.EmployeeTreatPassengerFlow}</span>;
            break;

          default:
            sums[index] = <span class="font_weight_600"></span>;
        }
      });
      return sums;
    },
  },
};
</script>

<style lang="scss">
.PassengerFlowStatistics {}
</style>