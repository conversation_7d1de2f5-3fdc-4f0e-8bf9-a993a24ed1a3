<template>
  <div>
    <!-- 客户等级 -->
    <el-card class="box-card" shadow="never">
      <div class="dis_flex flex_x_between flex_y_center marbm_20">
        <div>
          <span class="marrt_15">{{ title }}</span>
          <span class="color_999">{{ subTitle }}</span>
        </div>
        <i class="el-icon-close" @click="handlerClose(Code)"></i>
      </div>
      <div v-if="metadata_ && metadata_.length > 0" class="dis_flex">
        <div class="marrt_15" style="width: 60px">{{ contentTitle }}</div>
        <el-checkbox-group v-model="contentValues_" @change="changeCheckBoxValue">
          <el-row>
            <el-col :span="6" v-for="item in metadata_" :key="getOptionKey(item)">
              <el-checkbox  :label="getOptionKey(item)" :value="getOptionKey(item)" >{{
                getOptionLabel(item)
              }}</el-checkbox>
            </el-col>
          </el-row>
        </el-checkbox-group>
      </div>
    </el-card>
  </div>
</template>

<script>
export default {
  name: "followCheckboxs",
  components: {},
  props: {
    title: {
      type: String,
      default: "",
    },
    subTitle: {
      type: String,
      default: null,
    },
    contentTitle: {
      type: String,
      default: null,
    },
    Code: {
      type: String,
      default: null,
    },
    contentValues: {
      type: Array,
      default: () => [],
    },
    options: {
      type: Object,
      default: () => {
        return {
          ID: "ID",
          Name: "Name",
        };
      },
    },
    metadata: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      metadata_: [],
      contentValues_: [],
    };
  },
  computed: {},
  watch: {
    contentValues: {
      handler(val) {
        this.contentValues_ = val;
      },
      immediate: true,
    },
    metadata: {
      handler(val) {
        this.metadata_ = val;
      },
      immediate: true,
    },
  },
  created() {},
  mounted() {},
  methods: {
    getOptionKey(item) {
      return item[this.options.ID];
    },

    getOptionLabel(item) {
      return item[this.options.Name];
    },
    handlerClose(Code) {
      this.$emit("handlerChildClone", Code);
    },
    changeCheckBoxValue() {
      this.$emit("handlerChildChange", this.contentValues_);
    },
  },
};
</script>

<style lang="less">
