
function getBase64Image(img, width, height) {
  var canvas = document.createElement("canvas");
  canvas.width = width ? width : img.width;
  canvas.height = height ? height : img.height;
  var ctx = canvas.getContext("2d");
  ctx.drawImage(img, 0, 0, canvas.width, canvas.height);
  var dataURL = canvas.toDataURL();
  return dataURL;
}


export default {
  // 微信公众平台图片地址代
  imgProxy(src){
    const base = process.env.VUE_APP_API_URL
    return base + "api/open/img/proxy?url=" +  src;
  },
  // 通过图片地址获取 base64
  getCanvasBase64(img) {
    return new Promise((resolve) => {
      var image = new Image();
      //至关重要
      image.crossOrigin = '';
      image.src = img;
      //至关重要
      if (img) {
        image.onload = function () {
          let base64 = getBase64Image(image);//将base64传给done上传处理
          resolve(base64);//将base64传给done上传处理
        }
        /**  图片加载失败  */
        image.onerror = function(){
          resolve("");
        }
        // return deferred;//问题要让onload完成后再return sessionStorage['imgTest']
      }
    })
  },
  // 函数节流
  throttle(fn,wait){
  var pre = Date.now();
  wait = wait || 500;
  return function(){
    var context = this;
    var args = arguments;
    var now = Date.now();
    if( now - pre >= wait){
        fn.apply(context,args);
        pre = Date.now();
    }
  }
  },

  /** * 函数防抖 */
  debounce(fn, delay) {
    // 记录上一次的延时器
    var timer = null;
    delay = delay || 200;
    return function() {
      var args = arguments;
      var that = this;
      // 清除上一次延时器
      clearTimeout(timer)
      timer = setTimeout(function() {
          fn.apply(that,args)
      }, delay);
    }
  },

  /**  压缩比例默认 0.5
   * 获取图片base64
   * quality
  */
  getImageBase64(file,quality = 1) {
    return new Promise((resolve, reject) => {
      let reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = (evt) => {
        let base64 = evt.target.result;
        this.imageCompress(base64,quality)
          .then((res) => {
            resolve(res);
          })
          .catch((fail) => {
            reject(fail);
          });
      };
      reader.onerror = () => {
        reject("获取错误");
      };
    });
  },
  /**  压缩图片
   * quality 压缩系数 0-1之间 默认0.5
   */
  imageCompress(base64, quality = 0.5) {
    // var img = document.getElementById("base64Url");
    return new Promise((resolve, reject) => {
      let img = new Image();
      // var quality = 0.6;    //压缩系数0-1之间
      img.src = base64;
      let width, height;
      // 加载完成执行
      img.onload = function () {
        // base64地址图片加载完毕后执行
        width = img.width;
        height = img.height;
        let canvas = document.createElement("canvas");
        let ctx = canvas.getContext("2d");
        canvas.width = width;
        canvas.height = height;
        // 铺底色
        ctx.fillStyle = "#fff";
        ctx.fillRect(0, 0, canvas.width, canvas.height);
        ctx.drawImage(img, 0, 0, width, height);
        // 进行最小压缩后的base64
        let compressBase64 = canvas.toDataURL("image/jpeg", quality);
        resolve(compressBase64);
      };
      img.onerror = function () {
        reject("图片加载失败");
      };
    });
  },


}