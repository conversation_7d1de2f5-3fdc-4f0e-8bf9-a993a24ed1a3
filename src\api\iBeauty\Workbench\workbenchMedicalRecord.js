/**
 * Created by preference on 2023/05/04
 *  zmx
 */

import * as API from "@/api/index";
export default {
  /**   */
  customerMedicalRecord_list: (params) => {
    return API.POST("api/customerMedicalRecord/list", params);
  },
  /**   */
  customerMedicalRecord_doctor: (params) => {
    return API.POST("api/customerMedicalRecord/doctor", params);
  },
  /**   */
  customerMedicalRecord_create: (params) => {
    return API.POST("api/customerMedicalRecord/create", params);
  },
  /**   */
  customerMedicalRecord_detail: (params) => {
    return API.POST("api/customerMedicalRecord/detail", params);
  },
  /**   */
  customerMedicalRecord_update: (params) => {
    return API.POST("api/customerMedicalRecord/update", params);
  },
  /**   */
  customerMedicalRecord_delete: (params) => {
    return API.POST("api/customerMedicalRecord/delete", params);
  },
  /**   */
  customerMedicalRecord_medicalRecordList: (params) => {
    return API.POST("api/customerMedicalRecord/medicalRecordList", params);
  },
  /**  科室列表 */
  department_all: (params) => {
    return API.POST("api/customerMedicalRecord/department", params);
  },
  /**  病历信息 */
  medicalRecord_entryInfo: (params) => {
    return API.POST("api/medicalRecord/entryInfo", params);
  },
  /** 模板类型  */
  medicalRecord_type: (params) => {
    return API.POST("api/medicalRecord/type", params);
  },
  /**  模板列表 */
  medicalRecord_template: (params) => {
    return API.POST("api/medicalRecord/template", params);
  },
  /**  新建病历模板 */
  customerMedicalRecord_createMedicalTemplate: (params) => {
    return API.POST("api/customerMedicalRecord/createMedicalTemplate", params);
  },
  /**  修改病历模板 */
  customerMedicalRecord_updateMedicalTemplate: (params) => {
    return API.POST("api/customerMedicalRecord/updateMedicalTemplate", params);
  },
  /**  模板查询 */
  customerElectronicMedicalRecord_getTemplate: (params) => {
    return API.POST("api/customerElectronicMedicalRecord/getTemplate", params);
  },
  /** 模板详情  */
  medicalRecord_getTemplate: (params) => {
    return API.POST("api/medicalRecord/getTemplate", params);
  },
  /** 删除病历模板  */
  customerMedicalRecord_deleteTemplate: (params) => {
    return API.POST("api/customerMedicalRecord/deleteTemplate", params);
  },
  /** 获取病历号  */
  customerMedicalRecord_getMedicalRecordNumber: (params) => {
    return API.POST("api/customerMedicalRecord/getMedicalRecordNumber", params);
  },
  /** 获取客户编号  */
  customer_getCustomerCode: (params) => {
    return API.POST("api/customer/getCustomerCode", params);
  },
  /** 获取病历编号  */
  customerMedicalRecord_getMedicalRecordCode: (params) => {
    return API.POST("api/customerMedicalRecord/getMedicalRecordCode", params);
  },
  /** 获取病历编号  */
  customerMedicalRecord_getMedicalRecordDetail: (params) => {
    return API.POST("api/customerMedicalRecord/getMedicalRecordDetail", params);
  },
  //  查询顾客的病历状态
  customerMedicalRecord_medicalRecordStatus: (params) => {
    return API.POST("api/customerMedicalRecord/medicalRecordStatus", params);
  },
};
