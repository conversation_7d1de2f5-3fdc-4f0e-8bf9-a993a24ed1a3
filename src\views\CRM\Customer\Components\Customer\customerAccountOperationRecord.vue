<template>
  <div class="customerAccountOperation">
    <!--操作记录 项目-->
    <el-dialog title="操作记录" :visible.sync="showProjectDialog" width="980px" @close="operatioClose" append-to-body>
      <el-tabs v-model="tabProject" @tab-click="handleProjct">
        <el-tab-pane label="消耗记录" name="0">
          <el-table size="small" :data="projectList">
            <el-table-column prop="BillDate" label="消耗时间">
               <template slot-scope="scope">
                {{scope.row.BillDate | dateFormat("YYYY-MM-DD HH:mm")}}
              </template>
            </el-table-column>
            <el-table-column prop="BillID" label="消耗订单"></el-table-column>
            <el-table-column label="消耗次数">
              <template slot-scope="scope">
                <span class="color_red">-{{ scope.row.Quantity }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="EntityName" label="录单门店"></el-table-column>
            <el-table-column prop="EmployeeName" label="录单人"></el-table-column>
          </el-table>
          <div class="text_right pad_15">
            <el-pagination
              background
              v-if="Paginations.total > 0"
              @current-change="HandleCurrentChange"
              :current-page.sync="Paginations.page"
              :page-size="Paginations.page_size"
              :layout="Paginations.layout"
              :total="Paginations.total"
            ></el-pagination>
          </div>
        </el-tab-pane>
        <el-tab-pane label="退消耗记录" name="2">
          <el-table size="small" :data="projectList">
            <el-table-column prop="BillDate" label="退消耗时间">
                <template slot-scope="scope">
                {{scope.row.BillDate | dateFormat("YYYY-MM-DD HH:mm")}}
              </template>
            </el-table-column>
            <el-table-column prop="BillID" label="退消耗订单"></el-table-column>
            <el-table-column label="退消耗次数">
              <template slot-scope="scope">
                <span class="color_green">+{{ scope.row.Quantity }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="EntityName" label="录单门店"></el-table-column>
            <el-table-column prop="EmployeeName" label="录单人"></el-table-column>
          </el-table>
          <div class="text_right pad_15">
            <el-pagination
              background
              v-if="Paginations.total > 0"
              @current-change="HandleCurrentChange"
              :current-page.sync="Paginations.page"
              :page-size="Paginations.page_size"
              :layout="Paginations.layout"
              :total="Paginations.total"
            ></el-pagination>
          </div>
        </el-tab-pane>
        <el-tab-pane label="退款记录" name="1">
          <el-table size="small" :data="projectList">
            <el-table-column prop="BillDate" label="退款时间">
               <template slot-scope="scope">
                {{scope.row.BillDate | dateFormat("YYYY-MM-DD HH:mm")}}
              </template>
            </el-table-column>
            <el-table-column prop="BillID" label="退款订单"></el-table-column>
            <el-table-column label="退款次数">
              <template slot-scope="scope">
                <span class="color_red">-{{ scope.row.Quantity }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="EntityName" label="录单门店"></el-table-column>
            <el-table-column prop="EmployeeName" label="录单人"></el-table-column>
          </el-table>
          <div class="text_right pad_15">
            <el-pagination
              background
              v-if="Paginations.total > 0"
              @current-change="HandleCurrentChange"
              :current-page.sync="Paginations.page"
              :page-size="Paginations.page_size"
              :layout="Paginations.layout"
              :total="Paginations.total"
            ></el-pagination>
          </div>
        </el-tab-pane>
        <el-tab-pane label="转账记录" name="3">
          <el-table size="small" :data="projectList">
            <el-table-column prop="BillDate" label="转账时间">
               <template slot-scope="scope">
                {{scope.row.BillDate | dateFormat("YYYY-MM-DD HH:mm")}}
              </template>
            </el-table-column>
            <el-table-column prop="BillID" label="转账订单"></el-table-column>
            <el-table-column label="转账次数">
              <template slot-scope="scope">
                <span v-if="scope.row.TransferType == 10" class="color_red">-{{ scope.row.Quantity }}</span>
                <span v-if="scope.row.TransferType == 20" class="color_green">+{{ scope.row.Quantity }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="EntityName" label="录单门店"></el-table-column>
            <el-table-column prop="EmployeeName" label="录单人"></el-table-column>
          </el-table>
          <div class="text_right pad_15">
            <el-pagination
              background
              v-if="Paginations.total > 0"
              @current-change="HandleCurrentChange"
              :current-page.sync="Paginations.page"
              :page-size="Paginations.page_size"
              :layout="Paginations.layout"
              :total="Paginations.total"
            ></el-pagination>
          </div>
        </el-tab-pane>
        <el-tab-pane label="还款记录" name="4">
          <el-table size="small" :data="projectList">
            <el-table-column prop="BillDate" label="还款时间">
               <template slot-scope="scope">
                {{scope.row.BillDate | dateFormat("YYYY-MM-DD HH:mm")}}
              </template>
            </el-table-column>
            <el-table-column prop="BillID" label="还款订单"></el-table-column>
            <el-table-column prop="Amount" label="还款金额">
              <template slot-scope="scope">
                <span class="color_green">￥{{ scope.row.Amount | toFixed | NumFormat }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="EntityName" label="录单门店"></el-table-column>
            <el-table-column prop="EmployeeName" label="录单人"></el-table-column>
          </el-table>
          <div class="text_right pad_15">
            <el-pagination
              background
              v-if="Paginations.total > 0"
              @current-change="HandleCurrentChange"
              :current-page.sync="Paginations.page"
              :page-size="Paginations.page_size"
              :layout="Paginations.layout"
              :total="Paginations.total"
            ></el-pagination>
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-dialog>

    <!--操作记录 储值卡 -->
    <el-dialog title="操作记录" :visible.sync="showSavingCardDialog" width="980px" @close="operatioClose" append-to-body>
      <el-tabs v-model="tabSavingcard" @tab-click="handleSavingCard">
        <el-tab-pane label="消耗记录" name="0">
          <el-table size="small" :data="savingCardList">
            <el-table-column prop="BillDate" label="消耗时间">
               <template slot-scope="scope">
                {{scope.row.BillDate | dateFormat("YYYY-MM-DD HH:mm")}}
              </template>
            </el-table-column>
            <el-table-column prop="BillID" label="消耗订单"></el-table-column>
            <el-table-column label="消耗金额">
              <template slot-scope="scope">
                <span class="color_red">-￥{{ scope.row.Amount | toFixed | NumFormat }}</span>
              </template>
            </el-table-column>
            <el-table-column label="消耗本金">
              <template slot-scope="scope">
                <span class="color_red">-￥{{ scope.row.CardTreatAmount | toFixed | NumFormat }}</span>
              </template>
            </el-table-column>
            <el-table-column label="消耗赠额">
              <template slot-scope="scope">
                <span class="color_red">-￥{{ scope.row.LargessCardTreatAmount | toFixed | NumFormat }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="EntityName" label="录单门店"></el-table-column>
            <el-table-column prop="EmployeeName" label="录单人"></el-table-column>
          </el-table>
          <div class="text_right pad_15">
            <el-pagination
              background
              v-if="Paginations.total > 0"
              @current-change="HandleCurrentChange"
              :current-page.sync="Paginations.page"
              :page-size="Paginations.page_size"
              :layout="Paginations.layout"
              :total="Paginations.total"
            ></el-pagination>
          </div>
        </el-tab-pane>
        <el-tab-pane label="退消耗记录" name="1">
          <el-table size="small" :data="savingCardList">
            <el-table-column prop="BillDate" label="退消耗时间">
               <template slot-scope="scope">
                {{scope.row.BillDate | dateFormat("YYYY-MM-DD HH:mm")}}
              </template>
            </el-table-column>
            <el-table-column prop="BillID" label="退消耗订单"></el-table-column>
            <el-table-column label="退消耗金额">
              <template slot-scope="scope">
                <span class="color_green">+￥{{ scope.row.TotalAmount | toFixed | NumFormat }}</span>
              </template>
            </el-table-column>
            <el-table-column label="退消耗本金">
              <template slot-scope="scope">
                <span class="color_green">+￥{{ scope.row.Amount | toFixed | NumFormat }}</span>
              </template>                                                 
            </el-table-column>
            <el-table-column label="退消耗赠额">
              <template slot-scope="scope">
                <span class="color_green">+￥{{ scope.row.LargessAmount | toFixed | NumFormat }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="EntityName" label="录单门店"></el-table-column>
            <el-table-column prop="EmployeeName" label="录单人"></el-table-column>
          </el-table>
          <div class="text_right pad_15">
            <el-pagination
              background
              v-if="Paginations.total > 0"
              @current-change="HandleCurrentChange"
              :current-page.sync="Paginations.page"
              :page-size="Paginations.page_size"
              :layout="Paginations.layout"
              :total="Paginations.total"
            ></el-pagination>
          </div>
        </el-tab-pane>
        <el-tab-pane label="退款记录" name="2">
          <el-table size="small" :data="savingCardList">
            <el-table-column prop="BillDate" label="退款时间"></el-table-column>
              <template slot-scope="scope">
                {{scope.row.BillDate | dateFormat("YYYY-MM-DD HH:mm")}}
              </template>
            <el-table-column prop="BillID" label="退款订单"></el-table-column>
            <el-table-column label="退金额">
              <template slot-scope="scope">
                <span class="color_red">-￥{{ scope.row.TotalAmount | toFixed | NumFormat }}</span>
              </template>
            </el-table-column>
            <el-table-column label="退本金">
              <template slot-scope="scope">
                <span class="color_red">-￥{{ scope.row.Amount | toFixed | NumFormat }}</span>
              </template>
            </el-table-column>
            <el-table-column label="退赠额">
              <template slot-scope="scope">
                <span class="color_red">-￥{{ scope.row.LargessAmount | toFixed | NumFormat }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="EntityName" label="录单门店"></el-table-column>
            <el-table-column prop="EmployeeName" label="录单人"></el-table-column>
          </el-table>
          <div class="text_right pad_15">
            <el-pagination
              background
              v-if="Paginations.total > 0"
              @current-change="HandleCurrentChange"
              :current-page.sync="Paginations.page"
              :page-size="Paginations.page_size"
              :layout="Paginations.layout"
              :total="Paginations.total"
            ></el-pagination>
          </div>
        </el-tab-pane>
        <el-tab-pane label="转账记录" name="3">
          <el-table size="small" :data="savingCardList">
            <el-table-column prop="BillDate" label="转账时间">
               <template slot-scope="scope">
                {{scope.row.BillDate | dateFormat("YYYY-MM-DD HH:mm")}}
              </template>
            </el-table-column>
            <el-table-column prop="BillID" label="转账订单"></el-table-column>
            <el-table-column label="转账金额">
              <template slot-scope="scope">
                <span v-if="scope.row.TransferType == 10" class="color_red">-￥{{ scope.row.TotalAmount | toFixed | NumFormat }}</span>
                <span v-if="scope.row.TransferType == 20" class="color_green">+￥{{ scope.row.TotalAmount | toFixed | NumFormat }}</span>
              </template>
            </el-table-column>
            <el-table-column label="转账本金">
              <template slot-scope="scope">
                <span v-if="scope.row.TransferType == 10" class="color_red">-￥{{ scope.row.Amount | toFixed | NumFormat }}</span>
                <span v-if="scope.row.TransferType == 20" class="color_green">+￥{{ scope.row.Amount | toFixed | NumFormat }}</span>
              </template>
            </el-table-column>
            <el-table-column label="转账赠额">
              <template slot-scope="scope">
                <span v-if="scope.row.TransferType == 10" class="color_red">-￥{{ scope.row.LargessAmount | toFixed | NumFormat }}</span>
                <span v-if="scope.row.TransferType == 20" class="color_green">+￥{{ scope.row.LargessAmount | toFixed | NumFormat }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="EntityName" label="录单门店"></el-table-column>
            <el-table-column prop="EmployeeName" label="录单人"></el-table-column>
          </el-table>
          <div class="text_right pad_15">
            <el-pagination
              background
              v-if="Paginations.total > 0"
              @current-change="HandleCurrentChange"
              :current-page.sync="Paginations.page"
              :page-size="Paginations.page_size"
              :layout="Paginations.layout"
              :total="Paginations.total"
            ></el-pagination>
          </div>
        </el-tab-pane>
        <el-tab-pane label="销售抵扣/退抵扣记录" name="4">
          <el-table size="small" :data="savingCardList">
            <el-table-column prop="BillDate" label="抵扣/退抵扣时间">
             <template slot-scope="scope">
                {{scope.row.BillDate | dateFormat("YYYY-MM-DD HH:mm")}}
              </template></el-table-column>
            <el-table-column prop="BillID" label="销售单"></el-table-column>
            <el-table-column label="抵扣/退抵扣金额">
              <template slot-scope="scope">
                <span v-if="scope.row.DeductionType == 10" class="color_red">-￥{{ scope.row.TotalAmount | toFixed | NumFormat }}</span>
                <span v-if="scope.row.DeductionType == 20" class="color_green">+￥{{ scope.row.TotalAmount | toFixed | NumFormat }}</span>
              </template>
            </el-table-column>
            <el-table-column label="抵扣/退抵扣本金">
              <template slot-scope="scope">
                <span v-if="scope.row.DeductionType == 10" class="color_red">-￥{{ scope.row.Amount | toFixed | NumFormat }}</span>
                <span v-if="scope.row.DeductionType == 20" class="color_green">+￥{{ scope.row.Amount | toFixed | NumFormat }}</span>
              </template>
            </el-table-column>
            <el-table-column label="抵扣/退抵扣赠额">
              <template slot-scope="scope">
                <span v-if="scope.row.DeductionType == 10" class="color_red">-￥{{ scope.row.LargessAmount }}</span>
                <span v-if="scope.row.DeductionType == 20" class="color_green">+￥{{ scope.row.LargessAmount }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="EntityName" label="录单门店"></el-table-column>
            <el-table-column prop="EmployeeName" label="录单人"></el-table-column>
          </el-table>
          <div class="text_right pad_15">
            <el-pagination
              background
              v-if="Paginations.total > 0"
              @current-change="HandleCurrentChange"
              :current-page.sync="Paginations.page"
              :page-size="Paginations.page_size"
              :layout="Paginations.layout"
              :total="Paginations.total"
            ></el-pagination>
          </div>
        </el-tab-pane>
        <el-tab-pane label="还款记录" name="5">
          <el-table size="small" :data="savingCardList">
            <el-table-column prop="BillDate" label="还款时间">
               <template slot-scope="scope">
                {{scope.row.BillDate | dateFormat("YYYY-MM-DD HH:mm")}}
              </template>
            </el-table-column>
            <el-table-column prop="BillID" label="还款订单"></el-table-column>
            <el-table-column prop="Amount" label="还款金额">
              <template slot-scope="scope">
                <span class="color_green">￥{{ scope.row.Amount | toFixed | NumFormat }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="EntityName" label="录单门店"></el-table-column>
            <el-table-column prop="EmployeeName" label="录单人"></el-table-column>
          </el-table>
          <div class="text_right pad_15">
            <el-pagination
              background
              v-if="Paginations.total > 0"
              @current-change="HandleCurrentChange"
              :current-page.sync="Paginations.page"
              :page-size="Paginations.page_size"
              :layout="Paginations.layout"
              :total="Paginations.total"
            ></el-pagination>
          </div>
        </el-tab-pane>
        <el-tab-pane label="充值记录" name="6">
          <el-table size="small" :data="savingCardList">
            <el-table-column label="充值时间" prop="BillDate">
               <template slot-scope="scope">
                {{scope.row.BillDate | dateFormat("YYYY-MM-DD HH:mm")}}
              </template>
            </el-table-column>
            <el-table-column label="订单编号" prop="BillID"></el-table-column>
            <el-table-column label="本金" prop="Amount">
              <template slot-scope="scope">
                <span>￥{{ scope.row.Amount }}</span>
              </template>
            </el-table-column>
            <el-table-column label="赠金" prop="LargessAmount">
              <template slot-scope="scope">
                <span>￥{{ scope.row.LargessAmount }}</span>
              </template>
            </el-table-column>
            <el-table-column label="下单门店" prop="EntityName"></el-table-column>
            <el-table-column label="录单人" prop="EmployeeName"></el-table-column>
          </el-table>
          <div class="text_right pad_15">
            <el-pagination
              background
              v-if="Paginations.total > 0"
              @current-change="HandleCurrentChange"
              :current-page.sync="Paginations.page"
              :page-size="Paginations.page_size"
              :layout="Paginations.layout"
              :total="Paginations.total"
            ></el-pagination>
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-dialog>

    <!--操作记录 通用次卡 -->
    <el-dialog title="操作记录" :visible.sync="showGeneralCardDialog" width="980px" @close="operatioClose" append-to-body>
      <el-tabs v-model="tabGeneralCard" @tab-click="handleGeneralCard">
        <el-tab-pane label="消耗记录" name="0">
          <el-table size="small" :data="generalCardList">
            <el-table-column prop="BillDate" label="消耗时间">
               <template slot-scope="scope">
                {{scope.row.BillDate | dateFormat("YYYY-MM-DD HH:mm")}}
              </template>
            </el-table-column>
            <el-table-column prop="BillID" label="消耗订单"></el-table-column>
            <el-table-column label="耗用次数">
              <template slot-scope="scope">
                <span class="color_red">-{{ scope.row.Quantity }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="EntityName" label="录单门店"></el-table-column>
            <el-table-column prop="EmployeeName" label="录单人"></el-table-column>
          </el-table>
          <div class="text_right pad_15">
            <el-pagination
              background
              v-if="Paginations.total > 0"
              @current-change="HandleCurrentChange"
              :current-page.sync="Paginations.page"
              :page-size="Paginations.page_size"
              :layout="Paginations.layout"
              :total="Paginations.total"
            ></el-pagination>
          </div>
        </el-tab-pane>
        <el-tab-pane label="退消耗记录" name="1">
          <el-table size="small" :data="generalCardList">
            <el-table-column prop="BillDate" label="退消耗时间">
               <template slot-scope="scope">
                {{scope.row.BillDate | dateFormat("YYYY-MM-DD HH:mm")}}
              </template>
            </el-table-column>
            <el-table-column prop="BillID" label="消耗订单"></el-table-column>
            <el-table-column label="退消耗次数">
              <template slot-scope="scope">
                <span class="color_green">+{{ scope.row.Quantity }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="EntityName" label="录单门店"></el-table-column>
            <el-table-column prop="EmployeeName" label="录单人"></el-table-column>
          </el-table>
          <div class="text_right pad_15">
            <el-pagination
              background
              v-if="Paginations.total > 0"
              @current-change="HandleCurrentChange"
              :current-page.sync="Paginations.page"
              :page-size="Paginations.page_size"
              :layout="Paginations.layout"
              :total="Paginations.total"
            ></el-pagination>
          </div>
        </el-tab-pane>
        <el-tab-pane label="退款记录" name="2">
          <el-table size="small" :data="generalCardList">
            <el-table-column prop="BillDate" label="退款时间">
              <template slot-scope="scope">
                {{scope.row.BillDate | dateFormat("YYYY-MM-DD HH:mm")}}
              </template>
            </el-table-column>
             
            <el-table-column prop="BillID" label="退款订单"></el-table-column>
            <el-table-column label="退款次数">
              <template slot-scope="scope">
                <span class="color_red">-{{ scope.row.Quantity }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="EntityName" label="录单门店"></el-table-column>
            <el-table-column prop="EmployeeName" label="录单人"></el-table-column>
          </el-table>
          <div class="text_right pad_15">
            <el-pagination
              background
              v-if="Paginations.total > 0"
              @current-change="HandleCurrentChange"
              :current-page.sync="Paginations.page"
              :page-size="Paginations.page_size"
              :layout="Paginations.layout"
              :total="Paginations.total"
            ></el-pagination>
          </div>
        </el-tab-pane>
        <el-tab-pane label="转账记录" name="3">
          <el-table size="small" :data="generalCardList">
            <el-table-column prop="BillDate" label="转账时间">
              <template slot-scope="scope">
                {{scope.row.BillDate | dateFormat("YYYY-MM-DD HH:mm")}}
              </template>
            </el-table-column>
            
             
            <el-table-column prop="BillID" label="转账订单"></el-table-column>
            <el-table-column label="转账次数">
              <template slot-scope="scope">
                <span v-if="scope.row.TransferType == 10" class="color_red">-{{ scope.row.Quantity }}</span>
                <span v-if="scope.row.TransferType == 20" class="color_green">+{{ scope.row.Quantity }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="EntityName" label="录单门店"></el-table-column>
            <el-table-column prop="EmployeeName" label="录单人"></el-table-column>
          </el-table>
          <div class="text_right pad_15">
            <el-pagination
              background
              v-if="Paginations.total > 0"
              @current-change="HandleCurrentChange"
              :current-page.sync="Paginations.page"
              :page-size="Paginations.page_size"
              :layout="Paginations.layout"
              :total="Paginations.total"
            ></el-pagination>
          </div>
        </el-tab-pane>
        <el-tab-pane label="还款记录" name="4">
          <el-table size="small" :data="generalCardList">
            <el-table-column prop="BillDate" label="还款时间">
               <template slot-scope="scope">
                {{scope.row.BillDate | dateFormat("YYYY-MM-DD HH:mm")}}
              </template>
            </el-table-column>
            <el-table-column prop="BillID" label="还款订单"></el-table-column>
            <el-table-column prop="Amount" label="还款金额">
              <template slot-scope="scope">
                <span class="color_green">￥{{ scope.row.Amount | toFixed | NumFormat }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="EntityName" label="录单门店"></el-table-column>
            <el-table-column prop="EmployeeName" label="录单人"></el-table-column>
          </el-table>
          <div class="text_right pad_15">
            <el-pagination
              background
              v-if="Paginations.total > 0"
              @current-change="HandleCurrentChange"
              :current-page.sync="Paginations.page"
              :page-size="Paginations.page_size"
              :layout="Paginations.layout"
              :total="Paginations.total"
            ></el-pagination>
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-dialog>

    <!--操作记录 时效卡 -->
    <el-dialog title="操作记录" :visible.sync="showTimeCardDialog" width="980px" @close="operatioClose" append-to-body>
      <el-tabs v-model="tabTimeCard" @tab-click="handleTimeCard">
        <el-tab-pane label="消耗记录" name="0">
          <el-table size="small" :data="timeCardList">
            <el-table-column prop="BillDate" label="消耗时间">
               <template slot-scope="scope">
                {{scope.row.BillDate | dateFormat("YYYY-MM-DD HH:mm")}}
              </template>
            </el-table-column>
            
        
            <el-table-column prop="BillID" label="消耗订单"></el-table-column>
            <el-table-column label="消耗次数">
              <template slot-scope="scope">
                <span class="color_red">-{{ scope.row.Quantity }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="EntityName" label="录单门店"></el-table-column>
            <el-table-column prop="EmployeeName" label="录单人"></el-table-column>
          </el-table>
          <div class="text_right pad_15">
            <el-pagination
              background
              v-if="Paginations.total > 0"
              @current-change="HandleCurrentChange"
              :current-page.sync="Paginations.page"
              :page-size="Paginations.page_size"
              :layout="Paginations.layout"
              :total="Paginations.total"
            ></el-pagination>
          </div>
        </el-tab-pane>
        <el-tab-pane label="退消耗记录" name="1">
          <el-table size="small" :data="timeCardList">
            <el-table-column prop="BillDate" label="退消耗时间">
               <template slot-scope="scope">
                {{scope.row.BillDate | dateFormat("YYYY-MM-DD HH:mm")}}
              </template>
            </el-table-column>
            
            <el-table-column prop="BillID" label="退消耗订单"></el-table-column>
            <el-table-column label="退消耗次数">
              <template slot-scope="scope">
                <span class="color_green">+{{ scope.row.Quantity }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="EntityName" label="录单门店"></el-table-column>
            <el-table-column prop="EmployeeName" label="录单人"></el-table-column>
          </el-table>
          <div class="text_right pad_15">
            <el-pagination
              background
              v-if="Paginations.total > 0"
              @current-change="HandleCurrentChange"
              :current-page.sync="Paginations.page"
              :page-size="Paginations.page_size"
              :layout="Paginations.layout"
              :total="Paginations.total"
            ></el-pagination>
          </div>
        </el-tab-pane>
        <el-tab-pane label="退款记录" name="2">
          <el-table size="small" :data="timeCardList">
            <el-table-column prop="BillDate" label="退款时间">
              <template slot-scope="scope">
                {{scope.row.BillDate | dateFormat("YYYY-MM-DD HH:mm")}}
              </template>
            </el-table-column>
            
             
            <el-table-column prop="BillID" label="退款订单"></el-table-column>
            <el-table-column prop="EntityName" label="录单门店"></el-table-column>
            <el-table-column prop="EmployeeName" label="录单人"></el-table-column>
          </el-table>
          <div class="text_right pad_15">
            <el-pagination
              background
              v-if="Paginations.total > 0"
              @current-change="HandleCurrentChange"
              :current-page.sync="Paginations.page"
              :page-size="Paginations.page_size"
              :layout="Paginations.layout"
              :total="Paginations.total"
            ></el-pagination>
          </div>
        </el-tab-pane>
        <el-tab-pane label="转账记录" name="3">
          <el-table size="small" :data="timeCardList">
            <el-table-column prop="BillDate" label="转账时间">
              <template slot-scope="scope">
                {{scope.row.BillDate | dateFormat("YYYY-MM-DD HH:mm")}}
              </template>
            </el-table-column>
             
            <el-table-column prop="BillID" label="转账订单"></el-table-column>
            <el-table-column label="转账类型">
              <template slot-scope="scope">
                <span v-if="scope.row.TransferType == 10" class="color_red">转出</span>
                <span v-if="scope.row.TransferType == 20" class="color_green">转入</span>
              </template>
            </el-table-column>
            <el-table-column prop="EntityName" label="录单门店"></el-table-column>
            <el-table-column prop="EmployeeName" label="录单人"></el-table-column>
          </el-table>
          <div class="text_right pad_15">
            <el-pagination
              background
              v-if="Paginations.total > 0"
              @current-change="HandleCurrentChange"
              :current-page.sync="Paginations.page"
              :page-size="Paginations.page_size"
              :layout="Paginations.layout"
              :total="Paginations.total"
            ></el-pagination>
          </div>
        </el-tab-pane>
        <el-tab-pane label="还款记录" name="4">
          <el-table size="small" :data="timeCardList">
            <el-table-column prop="BillDate" label="还款时间">
                <template slot-scope="scope">
                {{scope.row.BillDate | dateFormat("YYYY-MM-DD HH:mm")}}
              </template>
            </el-table-column>
           
            <el-table-column prop="BillID" label="还款订单"></el-table-column>
            <el-table-column prop="Amount" label="还款金额">
              <template slot-scope="scope">
                <span class="color_green">￥{{ scope.row.Amount | toFixed | NumFormat }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="EntityName" label="录单门店"></el-table-column>
            <el-table-column prop="EmployeeName" label="录单人"></el-table-column>
          </el-table>
          <div class="text_right pad_15">
            <el-pagination
              background
              v-if="Paginations.total > 0"
              @current-change="HandleCurrentChange"
              :current-page.sync="Paginations.page"
              :page-size="Paginations.page_size"
              :layout="Paginations.layout"
              :total="Paginations.total"
            ></el-pagination>
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-dialog>

    <!--操作记录 产品 -->
    <el-dialog title="操作记录" :visible.sync="showProductDialog" width="980px" @close="operatioClose" append-to-body>
      <el-tabs v-model="tabProduct" @tab-click="handleProduct">
        <el-tab-pane label="消耗记录" name="0">
          <el-table size="small" :data="productList">
            <el-table-column prop="BillDate" label="消耗时间">
               <template slot-scope="scope">
                {{scope.row.BillDate | dateFormat("YYYY-MM-DD HH:mm")}}
              </template>
            </el-table-column>
            
            <el-table-column prop="BillID" label="消耗订单"></el-table-column>
            <el-table-column label="消耗数量">
              <template slot-scope="scope">
                <span class="color_red">-{{ scope.row.Quantity }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="EntityName" label="录单门店"></el-table-column>
            <el-table-column prop="EmployeeName" label="录单人"></el-table-column>
          </el-table>
          <div class="text_right pad_15">
            <el-pagination
              background
              v-if="Paginations.total > 0"
              @current-change="HandleCurrentChange"
              :current-page.sync="Paginations.page"
              :page-size="Paginations.page_size"
              :layout="Paginations.layout"
              :total="Paginations.total"
            ></el-pagination>
          </div>
        </el-tab-pane>
        <el-tab-pane label="退消耗记录" name="1">
          <el-table size="small" :data="productList">
            <el-table-column prop="BillDate" label="退消耗时间">
               <template slot-scope="scope">
                {{scope.row.BillDate | dateFormat("YYYY-MM-DD HH:mm")}}
              </template>
            </el-table-column>
            <el-table-column prop="BillID" label="退消耗订单"></el-table-column>
            <el-table-column label="退消耗次数">
              <template slot-scope="scope">
                <span class="color_green">+{{ scope.row.Quantity }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="EntityName" label="录单门店"></el-table-column>
            <el-table-column prop="EmployeeName" label="录单人"></el-table-column>
          </el-table>
          <div class="text_right pad_15">
            <el-pagination
              background
              v-if="Paginations.total > 0"
              @current-change="HandleCurrentChange"
              :current-page.sync="Paginations.page"
              :page-size="Paginations.page_size"
              :layout="Paginations.layout"
              :total="Paginations.total"
            ></el-pagination>
          </div>
        </el-tab-pane>
        <el-tab-pane label="退款记录" name="2">
          <el-table size="small" :data="productList">
            <el-table-column prop="BillDate" label="退款时间">
               <template slot-scope="scope">
                {{scope.row.BillDate | dateFormat("YYYY-MM-DD HH:mm")}}
              </template>
            </el-table-column>
            <el-table-column prop="BillID" label="退款订单"></el-table-column>
            <el-table-column label="退款次数">
              <template slot-scope="scope">
                <span class="color_red">-{{ scope.row.Quantity }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="EntityName" label="录单门店"></el-table-column>
            <el-table-column prop="EmployeeName" label="录单人"></el-table-column>
          </el-table>
          <div class="text_right pad_15">
            <el-pagination
              background
              v-if="Paginations.total > 0"
              @current-change="HandleCurrentChange"
              :current-page.sync="Paginations.page"
              :page-size="Paginations.page_size"
              :layout="Paginations.layout"
              :total="Paginations.total"
            ></el-pagination>
          </div>
        </el-tab-pane>
        <el-tab-pane label="转账记录" name="3">
          <el-table size="small" :data="productList">
            <el-table-column prop="BillDate" label="转账时间">
               <template slot-scope="scope">
                {{scope.row.BillDate | dateFormat("YYYY-MM-DD HH:mm")}}
              </template>
            </el-table-column>
            <el-table-column prop="BillID" label="转账订单"></el-table-column>
            <el-table-column label="转账次数">
              <template slot-scope="scope">
                <span v-if="scope.row.TransferType == 10" class="color_red">-{{ scope.row.Quantity }}</span>
                <span v-if="scope.row.TransferType == 20" class="color_green">+{{ scope.row.Quantity }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="EntityName" label="录单门店"></el-table-column>
            <el-table-column prop="EmployeeName" label="录单人"></el-table-column>
          </el-table>
          <div class="text_right pad_15">
            <el-pagination
              background
              v-if="Paginations.total > 0"
              @current-change="HandleCurrentChange"
              :current-page.sync="Paginations.page"
              :page-size="Paginations.page_size"
              :layout="Paginations.layout"
              :total="Paginations.total"
            ></el-pagination>
          </div>
        </el-tab-pane>
        <el-tab-pane label="还款记录" name="4">
          <el-table size="small" :data="productList">
            <el-table-column prop="BillDate" label="还款时间">
               <template slot-scope="scope">
                {{scope.row.BillDate | dateFormat("YYYY-MM-DD HH:mm")}}
              </template>
            </el-table-column>
            <el-table-column prop="BillID" label="还款订单"></el-table-column>
            <el-table-column prop="Amount" label="还款金额">
              <template slot-scope="scope">
                <span class="color_green">￥{{ scope.row.Amount | toFixed | NumFormat }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="EntityName" label="录单门店"></el-table-column>
            <el-table-column prop="EmployeeName" label="录单人"></el-table-column>
          </el-table>
          <div class="text_right pad_15">
            <el-pagination
              background
              v-if="Paginations.total > 0"
              @current-change="HandleCurrentChange"
              :current-page.sync="Paginations.page"
              :page-size="Paginations.page_size"
              :layout="Paginations.layout"
              :total="Paginations.total"
            ></el-pagination>
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-dialog>
  </div>
</template>

<script>
import API from "@/api/CRM/Customer/customerAccount";

export default {
  name: "customerAccountOperation",
  props: {
    RecordDialog: {
      type: Boolean,
      default: false,
    },
    ID: {
      default: "",
    },
    Type: {
      default: "",
    },
  },
  /** 监听数据变化   */
  watch: {
    RecordDialog: {
      deep: true,
      immediate: true,
      handler(newval) {
        if (newval) {
          this.showDialog(newval);
        }
      },
    },
  },
  /**  引入的组件  */
  components: {},
  /**  Vue 实例的数据对象**/
  data() {
    return {
      tabProject: "0",
      tabSavingcard: "0",
      tabTimeCard: "0",
      tabProduct: "0",
      tabGeneralCard: "0",
      showProjectDialog: false,
      showSavingCardDialog: false,
      showTimeCardDialog: false,
      showGeneralCardDialog: false,
      showProductDialog: false,
      projectList: [],
      savingCardList: [],
      generalCardList: [],
      timeCardList: [],
      productList: [],
      Paginations: {
        page: 1, // 当前位于哪页
        total: 0, // 总数
        page_size: 10, // 1页显示多少条
        layout: "total, prev, pager, next,jumper", // 翻页属性
      }, //需要给分页组件传的信息
    };
  },
  /**计算属性  */
  computed: {},
  /**  方法集合  */
  methods: {
    showDialog(boolean) {
      let that = this;
      switch (that.Type) {
        case "Project":
          that.showProjectDialog = boolean;
          this.getTreatBill();
          break;
        case "SavingCard":
          that.showSavingCardDialog = boolean;
          this.getSavingCardTreatBill();
          break;
        case "TimeCard":
          that.showTimeCardDialog = boolean;
          this.getTimeCardTreatBill();
          break;
        case "GeneralCard":
          that.showGeneralCardDialog = boolean;
          this.getGeneralCardRefundBill();
          break;
        case "Product":
          that.showProductDialog = boolean;
          this.getProductAccountTreatBill();
          break;
      }
    },
    operatioClose() {
      let that = this;
      this.$emit("update:RecordDialog", false);
      switch (that.Type) {
        case "Project":
          that.showProjectDialog = false;
          that.tabProject = "0";
          break;
        case "SavingCard":
          that.showSavingCardDialog = false;
          that.tabSavingcard = "0";
          break;
        case "TimeCard":
          that.showTimeCardDialog = false;
          that.tabTimeCard = "0";
          break;
        case "GeneralCard":
          that.showGeneralCardDialog = false;
          that.tabGeneralCard = "0";
          break;
        case "Product":
          that.showProductDialog = false;
          that.tabProduct = "0";
          break;
      }
    },
    // 分页
    HandleCurrentChange(page) {
      let that = this;
      that.Paginations.page = page;
      switch (that.Type) {
        case "Project":
          that.getProjectNet();
          break;
        case "SavingCard":
          that.getSavingCardNet();
          break;
        case "TimeCard":
          that.getTimeCardNet();
          break;
        case "GeneralCard":
          that.getGeneralCardNet();
          break;
        case "Product":
          that.getProductNet();
          break;
      }
    },

    /* ==== 项目 ===== */
    //项目操作记录切换
    handleProjct() {
      let that = this;
      that.projectList = [];
      that.Paginations.page = 1;
      that.getProjectNet();
    },
    //项目操作记录请求
    getProjectNet() {
      let that = this;
      switch (this.tabProject) {
        case "0":
          that.getTreatBill();
          break;
        case "1":
          that.getRefundBill();
          break;
        case "2":
          that.getRefundTreatBill();
          break;
        case "3":
          that.getTransferBill();
          break;
        case "4":
          that.getRepayment();
          break;
      }
    },
    //项目耗用记录
    getTreatBill() {
      let that = this;
      let params = {
        PageNum: that.Paginations.page,
        ID: that.ID,
      };
      API.treatBill(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.Paginations.total = res.Total;
            that.Paginations.page_size = res.PageSize;
            that.projectList = res.List;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {});
    },
    // 项目退存量记录
    getRefundBill() {
      let that = this;
      let params = {
        PageNum: that.Paginations.page,
        ID: that.ID,
      };
      API.refundBill(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.Paginations.total = res.Total;
            that.Paginations.page_size = res.PageSize;
            that.projectList = res.List;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {});
    },
    //项目退消耗记录
    getRefundTreatBill() {
      let that = this;
      let params = {
        PageNum: that.Paginations.page,
        ID: that.ID,
      };
      API.refundTreatBill(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.Paginations.total = res.Total;
            that.Paginations.page_size = res.PageSize;
            that.projectList = res.List;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {});
    },
    //项目转出记录
    getTransferBill() {
      let that = this;
      let params = {
        PageNum: that.Paginations.page,
        ID: that.ID,
      };
      API.transferBill(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.Paginations.total = res.Total;
            that.Paginations.page_size = res.PageSize;
            that.projectList = res.List;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {});
    },
    //项目还款记录
    getRepayment() {
      let that = this;
      let params = {
        PageNum: that.Paginations.page,
        ID: that.ID,
      };
      API.repayment(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.Paginations.total = res.Total;
            that.Paginations.page_size = res.PageSize;
            that.projectList = res.List;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {});
    },

    /* ==== 储值卡 ===== */
    //储值卡操作记录切换
    handleSavingCard() {
      let that = this;
      that.savingCardList = [];
      that.Paginations.page = 1;
      that.getSavingCardNet();
    },
    //储值卡操作记录请求
    getSavingCardNet() {
      let that = this;
      switch (that.tabSavingcard) {
        case "0":
          that.getSavingCardTreatBill();
          break;
        case "1":
          that.getSavingCardRefundTreatBill();
          break;
        case "2":
          that.getSavingCardRefundBill();
          break;
        case "3":
          that.getSavingCardTransferBill();
          break;
        case "4":
          that.getSavingCardDeduction();
          break;
        case "5":
          that.getSavingCardRepayment();
          break;
        case "6":
          that.getSavingCardRechargeBill();
          break;
      }
    },
    //储值卡消耗记录
    getSavingCardTreatBill() {
      let that = this;
      let params = {
        PageNum: that.Paginations.page,
        ID: that.ID,
      };
      API.savingCardTreatBill(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.Paginations.total = res.Total;
            that.Paginations.page_size = res.PageSize;
            that.savingCardList = res.List;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {});
    },
    //储值卡退存量记录
    getSavingCardRefundBill() {
      let that = this;
      let params = {
        PageNum: that.Paginations.page,
        ID: that.ID,
      };
      API.savingCardRefundBill(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.Paginations.total = res.Total;
            that.Paginations.page_size = res.PageSize;
            that.savingCardList = res.List;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {});
    },
    //储值卡退消耗记录
    getSavingCardRefundTreatBill() {
      let that = this;
      let params = {
        PageNum: that.Paginations.page,
        ID: that.ID,
      };
      API.savingCardRefundTreatBill(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.Paginations.total = res.Total;
            that.Paginations.page_size = res.PageSize;
            that.savingCardList = res.List;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {});
    },
    //储值卡转出记录
    getSavingCardTransferBill() {
      let that = this;
      let params = {
        PageNum: that.Paginations.page,
        ID: that.ID,
      };
      API.savingCardTransferBill(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.Paginations.total = res.Total;
            that.Paginations.page_size = res.PageSize;
            that.savingCardList = res.List;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {});
    },
    //储值卡还款记录
    getSavingCardRepayment() {
      let that = this;
      let params = {
        PageNum: that.Paginations.page,
        ID: that.ID,
      };
      API.savingCardRepayment(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.Paginations.total = res.Total;
            that.Paginations.page_size = res.PageSize;
            that.savingCardList = res.List;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {});
    },
    // 销售抵扣/退销售抵扣记录
    getSavingCardDeduction() {
      let that = this;
      let params = {
        PageNum: that.Paginations.page,
        ID: that.ID,
      };
      API.savingCardDeduction(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.Paginations.total = res.Total;
            that.Paginations.page_size = res.PageSize;
            that.savingCardList = res.List;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {});
    },
    // 储值卡充值记录
    getSavingCardRechargeBill() {
      let that = this;
      let params = {
        PageNum: that.Paginations.page,
        ID: that.ID,
      };
      API.savingCardRechargeBill(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.Paginations.total = res.Total;
            that.Paginations.page_size = res.PageSize;
            that.savingCardList = res.List;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {});
    },

    /* ==== 通用次卡 ===== */
    //通用次卡异操作动记录切换
    handleGeneralCard() {
      let that = this;
      that.generalCardList = [];
      that.Paginations.page = 1;
      that.getGeneralCardNet();
    },
    //通用次卡操作记录
    getGeneralCardNet() {
      let that = this;
      switch (that.tabGeneralCard) {
        case "0":
          that.getGeneralCardRefundBill();
          break;
        case "1":
          that.getGeneralCardRefundTreatBill();
          break;
        case "2":
          that.getGeneralCardTreatBill();
          break;
        case "3":
          that.getGeneralCardRefundTransferBill();
          break;
        case "4":
          that.getGeneralCardRefundRepayment();
          break;
      }
    },
    //通用次卡消耗记录
    getGeneralCardRefundBill() {
      let that = this;
      let params = {
        PageNum: that.Paginations.page,
        ID: that.ID,
      };
      API.generalCardTreatBill(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.Paginations.total = res.Total;
            that.Paginations.page_size = res.PageSize;
            that.generalCardList = res.List;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {});
    },
    //通用次卡退存量记录
    getGeneralCardTreatBill() {
      let that = this;
      let params = {
        PageNum: that.Paginations.page,
        ID: that.ID,
      };
      API.generalCardRefundBill(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.Paginations.total = res.Total;
            that.Paginations.page_size = res.PageSize;
            that.generalCardList = res.List;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {});
    },
    //通用次卡退消耗记录
    getGeneralCardRefundTreatBill() {
      let that = this;
      let params = {
        PageNum: that.Paginations.page,
        ID: that.ID,
      };
      API.generalCardRefundTreatBill(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.Paginations.total = res.Total;
            that.Paginations.page_size = res.PageSize;
            that.generalCardList = res.List;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {});
    },
    //通用次卡转出记录
    getGeneralCardRefundTransferBill() {
      let that = this;
      let params = {
        PageNum: that.Paginations.page,
        ID: that.ID,
      };
      API.generalCardRefundTransferBill(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.Paginations.total = res.Total;
            that.Paginations.page_size = res.PageSize;
            that.generalCardList = res.List;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {});
    },
    //通用次卡还款记录
    getGeneralCardRefundRepayment() {
      let that = this;
      let params = {
        PageNum: that.Paginations.page,
        ID: that.ID,
      };
      API.generalCardRefundRepayment(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.Paginations.total = res.Total;
            that.Paginations.page_size = res.PageSize;
            that.generalCardList = res.List;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {});
    },

    /* ==== 时效卡 ===== */
    //时效卡操作记录切换
    handleTimeCard() {
      let that = this;
      that.timeCardList = [];
      that.Paginations.page = 1;
      that.getTimeCardNet();
    },
    //时效卡操作记录请求
    getTimeCardNet() {
      let that = this;
      switch (this.tabTimeCard) {
        case "0":
          that.getTimeCardTreatBill();
          break;
        case "1":
          that.getTimeCardRefundTreatBill();
          break;
        case "2":
          that.getTimeCardRefundBill();
          break;
        case "3":
          that.getTimeCardRefundTransferBill();
          break;
        case "4":
          that.getTimeCardRefundRepayment();
          break;
      }
    },
    //时效卡消耗记录
    getTimeCardTreatBill() {
      let that = this;
      let params = {
        PageNum: that.Paginations.page,
        ID: that.ID,
      };
      API.timeCardTreatBill(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.Paginations.total = res.Total;
            that.Paginations.page_size = res.PageSize;
            that.timeCardList = res.List;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {});
    },
    //时效卡退存量记录
    getTimeCardRefundBill() {
      let that = this;
      let params = {
        PageNum: that.Paginations.page,
        ID: that.ID,
      };
      API.timeCardRefundBill(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.Paginations.total = res.Total;
            that.Paginations.page_size = res.PageSize;
            that.timeCardList = res.List;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {});
    },
    //时效卡退消耗记录
    getTimeCardRefundTreatBill() {
      let that = this;
      let params = {
        PageNum: that.Paginations.page,
        ID: that.ID,
      };
      API.timeCardRefundTreatBill(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.Paginations.total = res.Total;
            that.Paginations.page_size = res.PageSize;
            that.timeCardList = res.List;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {});
    },
    //时效卡转出记录
    getTimeCardRefundTransferBill() {
      let that = this;
      let params = {
        PageNum: that.Paginations.page,
        ID: that.ID,
      };
      API.timeCardRefundTransferBill(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.Paginations.total = res.Total;
            that.Paginations.page_size = res.PageSize;
            that.timeCardList = res.List;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {});
    },
    //时效卡还款记录
    getTimeCardRefundRepayment() {
      let that = this;
      let params = {
        PageNum: that.Paginations.page,
        ID: that.ID,
      };
      API.timeCardRefundRepayment(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.Paginations.total = res.Total;
            that.Paginations.page_size = res.PageSize;
            that.timeCardList = res.List;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {});
    },

    /* ==== 产品 ===== */
    //产品操作记录切换
    handleProduct() {
      let that = this;
      that.productList = [];
      that.Paginations.page = 1;
      that.getProductNet();
    },
    // 产品操作记录
    getProductNet() {
      let that = this;
      switch (that.tabProduct) {
        case "0":
          that.getProductAccountTreatBill();
          break;
        case "1":
          that.getProductAccountRefundTreatBill();
          break;
        case "2":
          that.getProductAccountRefundBill();
          break;
        case "3":
          that.getProductAccountTransferBill();
          break;
        case "4":
          that.getProductAccountRepayment();
          break;
      }
    },
    //产品消耗记录
    getProductAccountTreatBill() {
      let that = this;
      let params = {
        PageNum: that.Paginations.page,
        ID: that.ID,
      };
      API.productAccountTreatBill(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.Paginations.total = res.Total;
            that.Paginations.page_size = res.PageSize;
            that.productList = res.List;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {});
    },
    //产品退存量记录
    getProductAccountRefundBill() {
      let that = this;
      let params = {
        PageNum: that.Paginations.page,
        ID: that.ID,
      };
      API.productAccountRefundBill(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.Paginations.total = res.Total;
            that.Paginations.page_size = res.PageSize;
            that.productList = res.List;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {});
    },
    //产品退消耗记录
    getProductAccountRefundTreatBill() {
      let that = this;
      let params = {
        PageNum: that.Paginations.page,
        ID: that.ID,
      };
      API.productAccountRefundTreatBill(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.Paginations.total = res.Total;
            that.Paginations.page_size = res.PageSize;
            that.productList = res.List;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {});
    },
    //产品转出记录
    getProductAccountTransferBill() {
      let that = this;
      let params = {
        PageNum: that.Paginations.page,
        ID: that.ID,
      };
      API.productAccountTransferBill(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.Paginations.total = res.Total;
            that.Paginations.page_size = res.PageSize;
            that.productList = res.List;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {});
    },
    //产品还款记录
    getProductAccountRepayment() {
      let that = this;
      let params = {
        PageNum: that.Paginations.page,
        ID: that.ID,
      };
      API.productAccountRepayment(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.Paginations.total = res.Total;
            that.Paginations.page_size = res.PageSize;
            that.productList = res.List;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {});
    },
  },
  /** 创建实例之前执行函数   */
  beforeCreate() {},
  /**  实例创建完成之后  */
  created() {},
  /**  在挂载开始之前被调用  */
  beforeMount() {},
  /**  实例被挂载后调用  */
  mounted() {},
  /**  数据更新 之前 调用  */
  beforeUpdate() {},
  /** 数据更新 完成 调用   */
  updated() {},
  /**  实例销毁之前调用  */
  beforeDestroy() {},
  /**  实例销毁后调用  */
  destroyed() {},
};
</script>

<style lang="scss">
.customerAccountOperation {
}
</style>
