<template>
  <div class="dispatchPriceView content_body" :loading="loading">
    <!-- 搜索框 -->
    <div class="nav_header">
      <el-row>
        <el-col :span="20">
          <el-form :inline="true" size="small" class="demo-form-inline" @keyup.enter.native="handleSearch">
            <el-form-item label="产品名称">
              <el-input v-model="searchName" @clear="handleSearch" clearable placeholder="输入产品名称搜索"></el-input>
            </el-form-item>

            <el-form-item label="产品分类">
              <el-cascader
                @change="handleSearch"
                @clear="handleSearch"
                :options="classifyList"
                :show-all-levels="true"
                clearable
                filterable
                :props="cascaderProps"
                v-model="searchCategoryID"
                placeholder="请选择产品分类"
              ></el-cascader>
            </el-form-item>
            <el-form-item label="产品品牌">
              <el-select
                v-model="searchnBrandID"
                clearable
                filterable
                placeholder="请选择产品品牌"
                :default-first-option="true"
                @change="handleSearch"
                @clear="handleSearch"
              >
                <el-option v-for="item in productBrandList" :key="item.ID" :label="item.Name" :value="item.ID"> </el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleSearch">搜索</el-button>
            </el-form-item>
          </el-form>
        </el-col>
      </el-row>
    </div>

    <!-- 表格 -->
    <div>
      <el-table size="small" :data="tableData" tooltip-effect="light">
        <el-table-column prop="ProductName" label="产品名称"></el-table-column>
        <el-table-column prop="BrandName" label="产品品牌"></el-table-column>
        <el-table-column prop="CategoryName" label="产品分类"></el-table-column>
        <el-table-column prop="DeliveryPrice" label="配销价格">
          <template slot-scope="scope"> ￥{{ scope.row.DeliveryPrice | toFixed | NumFormat }} </template>
        </el-table-column>
        <el-table-column prop="UnitName" label="最小包装单位"></el-table-column>
      </el-table>
    </div>
    <!-- 分页 -->
    <div class="pad_10 dis_flex flex_x_end" v-if="paginations.total > 0">
      <el-pagination
        background
        :current-page.sync="paginations.page"
        :layout="paginations.layout"
        :total="paginations.total"
        @current-change="currentChange"
      ></el-pagination>
    </div>
  </div>
</template>

<script>
import API from "@/api/PSI/Price/dispatchPriceView";
export default {
  name: "dispatchPriceView",
  props: {},
  /**  引入的组件  */
  components: {},
  /**  Vue 实例的数据对象**/
  data() {
    return {
      tableData: [],
      loading: false,
      searchName: "",
      searchCategoryID: "",
      searchnBrandID: "",
      classifyList: [],
      productBrandList: [],
      paginations: {
        page: 1, // 当前位于哪页
        total: 0, // 总数
        page_size: 0, // 1页显示多少条
        layout: "total, prev, pager, next, jumper", // 翻页属性
      }, //需要给分页组件传的信息

      cascaderProps: {
        checkStrictly: true,
        value: "ID",
        label: "Name",
        children: "Child",
        emitPath: false,
        expandTrigger: "hover",
      }, // 级联选择器配置项
    };
  },
  /**计算属性  */
  computed: {},
  /**  方法集合  */
  methods: {
    /**    */
    handleSearch() {
      let that = this;
      that.paginations.page = 1;
      that.getDispatchPriceGoodst_list();
    },
    /**    */
    currentChange(page) {
      let that = this;
      that.paginations.page = page;
      that.getDispatchPriceGoodst_list();
    },
    /************************************************  */
    /**   列表 */
    getDispatchPriceGoodst_list: function () {
      let that = this;
      that.loading = true;
      let params = {
        PageNum: that.paginations.page, //分页
        Name: that.searchName, //名称
        CategoryID: that.searchCategoryID, //分类
        BrandID: that.searchnBrandID, //品牌
      };
      API.getDispatchPriceGoodst_list(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.tableData = res.List;
            that.paginations.total = res.Total;
          } else {
            that.$message.error(res.Message);
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },
    // 获取产品分类列表
    getProductCategory: function () {
      var that = this;
      that.loading = true;
      API.getValidProductCategory()
        .then((res) => {
          if (res.StateCode == 200) {
            that.classifyList = res.Data;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },

    // 获取产品品牌列表
    getProductBrandList: function () {
      let that = this;
      var params = {
        Name: "",
        Active: true,
      };
      API.getProductBrandList(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.productBrandList = res.Data;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {});
    },
  },
  /** 监听数据变化   */
  watch: {},
  /** 创建实例之前执行函数   */
  beforeCreate() {},
  /**  实例创建完成之后  */
  created() {
    let that = this;
    that.getDispatchPriceGoodst_list();
    that.getProductBrandList();
    that.getProductCategory();
  },
  /**  在挂载开始之前被调用  */
  beforeMount() {},
  /**  实例被挂载后调用  */
  mounted() {},
  /**  数据更新 之前 调用  */
  beforeUpdate() {},
  /** 数据更新 完成 调用   */
  updated() {},
  /**  实例销毁之前调用  */
  beforeDestroy() {},
  /**  实例销毁后调用  */
  destroyed() {},
};
</script>

<style lang="scss">
.dispatchPriceView {
}
</style>
