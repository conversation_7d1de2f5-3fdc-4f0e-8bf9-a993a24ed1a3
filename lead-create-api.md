# 新增线索接口文档

## 接口概述

- **接口名称：** 新增线索
- **接口描述：** 创建新的线索记录，同时自动创建关联的客户信息
- **接口地址：** `POST /api/lead/create`
- **Content-Type：** `application/json`
- **开发者：** Ztx
- **版本：** 1.0
- **更新时间：** 2025-01-15

## 功能说明

此接口用于手动创建线索，前端只需要传递客户的基本信息（姓名、手机号、性别），系统会：
1. 自动创建客户记录
2. 创建关联的线索记录
3. 设置默认的线索来源为"自然到店"
4. 支持可选的分配人员和地址信息

## 请求参数

### 请求体 (LeadCreateForm)

| 字段名 | 类型 | 必填 | 描述 | 示例值 | 备注 |
|--------|------|------|------|--------|------|
| Name | String | ✅ | 客户姓名 | "张三" | 不能为空 |
| PhoneNumber | String | ✅ | 客户手机号 | "13800138000" | 不能为空，系统会检查重复 |
| Gender | String | ✅ | 客户性别 | "1" | 1-男，2-女，0-保密 |
| LeadSource | String | ❌ | 线索来源代码 | "ZIRAN_DAODIAN" | 不传则默认为自然到店 |
| LeadSourceDetail | String | ❌ | 线索来源详情 | "客户主动咨询" | 不传则默认为"手动新建线索" |
| AssignedTo | String | ❌ | 分配给谁（员工ID） | "EMP001" | 有效的员工ID |
| ProvinceCode | String | ❌ | 省份代码 | "110000" | 标准行政区划代码 |
| CityCode | String | ❌ | 城市代码 | "110100" | 标准行政区划代码 |
| AreaCode | String | ❌ | 区域代码 | "110101" | 标准行政区划代码 |
| Address | String | ❌ | 详细地址 | "朝阳区某某街道123号" | 具体地址信息 |
| Remark | String | ❌ | 备注信息 | "客户对美容项目感兴趣" | 其他说明 |

### 线索来源枚举值 (LeadSource)

| 代码 | 描述 | 使用场景 |
|------|------|----------|
| DOUYIN_XINXILIU | 抖音信息流 | 抖音广告投放 |
| DOUYIN_TUANGOU | 抖音线索-团购支付 | 抖音团购活动 |
| DOUYIN_ZIRANXIANSOU | 抖音线索-自然线索 | 抖音自然流量 |
| BENDI_XIANSUO_TUIGUANG | 本地线索推广 | 本地推广活动 |
| MEITUAN_DIANPING | 美团点评 | 美团平台 |
| BENDI_SHENGHUO_ZHIBO | 本地生活直播 | 直播带货 |
| DOUYIN_BENDITUISIXIN | 抖音本地推-私信 | 抖音私信咨询 |
| WEIXIN_PENGYOUQUAN | 微信朋友圈 | 微信朋友圈广告 |
| DOUYIN_ZAIXIANZIXUN | 抖音-在线咨询 | 抖音在线客服 |
| GAODE_DITU | 高德地图 | 地图搜索 |
| XIAOHONGSHU | 小红书 | 小红书平台 |
| ZIRAN_DAODIAN | 【自然到店】 | **默认值，手动创建** |
| LAO_DAI_XIN | 【老带新】 | 客户推荐 |

## 请求示例

### 最简请求（推荐）
```json
{
    "Name": "张三",
    "PhoneNumber": "13800138000",
    "Gender": "1"
}
```

### 完整请求示例
```json
{
    "Name": "李四",
    "PhoneNumber": "13900139000",
    "Gender": "2",
    "LeadSource": "WEIXIN_PENGYOUQUAN",
    "LeadSourceDetail": "微信朋友圈广告点击",
    "AssignedTo": "EMP001",
    "ProvinceCode": "110000",
    "CityCode": "110100",
    "AreaCode": "110101",
    "Address": "朝阳区某某街道123号",
    "Remark": "客户对面部护理项目感兴趣"
}
```

## 响应结果

### 成功响应
```json
{
    "StateCode": 200,
    "Message": "成功",
    "Data": {
        "leadId": 123,
        "customerId": 456,
        "customerName": "张三",
        "phoneNumber": "13800138000",
        "message": "线索创建成功"
    }
}
```

### 响应字段说明

| 字段名 | 类型 | 描述 |
|--------|------|------|
| StateCode | Integer | 状态码，200表示成功，1000表示失败 |
| Message | String | 响应消息 |
| Data | Object | 响应数据（成功时才有） |
| Data.leadId | Integer | 创建的线索ID |
| Data.customerId | Integer | 创建的客户ID |
| Data.customerName | String | 客户姓名 |
| Data.phoneNumber | String | 客户手机号 |
| Data.message | String | 创建结果消息 |

### 错误响应示例

#### 参数验证失败
```json
{
    "StateCode": 1000,
    "Message": "客户姓名不能为空"
}
```

#### 手机号重复
```json
{
    "StateCode": 1000,
    "Message": "手机号已存在，不用新建客户"
}
```

#### 性别参数错误
```json
{
    "StateCode": 1000,
    "Message": "客户性别不能为空"
}
```

#### 系统异常
```json
{
    "StateCode": 1000,
    "Message": "创建线索失败：数据库连接异常"
}
```

## 业务逻辑说明

### 执行流程
1. **参数验证：** 检查必填字段（姓名、手机号、性别）
2. **客户创建：** 调用CustomerService创建客户记录
3. **重复检查：** 系统自动检查手机号是否已存在
4. **线索创建：** 客户创建成功后，自动创建关联的线索记录
5. **返回结果：** 返回线索ID和客户ID

### 默认值设置
- **客户状态：** 默认为非会员（IsMember = false）
- **线索来源：** 默认为"ZIRAN_DAODIAN"（自然到店）
- **线索来源详情：** 默认为"手动新建线索"
- **线索状态：** 有分配人为"已分配"(1)，无分配人为"新线索"(0)

### 事务保证
整个过程在数据库事务中执行，确保数据一致性：
- 如果客户创建失败，不会创建线索
- 如果线索创建失败，客户创建也会回滚

## 错误码说明

| StateCode | 描述 | 常见原因 | 解决方案 |
|-----------|------|----------|----------|
| 200 | 成功 | 线索创建成功 | - |
| 1000 | 失败 | 参数验证失败 | 检查必填字段 |
| 1000 | 失败 | 手机号重复 | 使用其他手机号或查询现有客户 |
| 1000 | 失败 | 系统异常 | 联系技术支持 |

## 注意事项

### 重要提醒
1. **必填字段：** Name、PhoneNumber、Gender 为必填字段，不能为空或空字符串
2. **手机号唯一性：** 系统会检查手机号是否已存在，重复的手机号会创建失败
3. **性别值限制：** Gender字段只接受 "0"（保密）、"1"（男）、"2"（女）
4. **线索来源：** 建议使用枚举表中的标准值，避免使用自定义值
5. **员工分配：** AssignedTo字段需要传入有效的员工ID
6. **地址信息：** 省市区代码需要使用标准的行政区划代码

### 最佳实践
1. **前端验证：** 建议在前端也进行基本的参数验证
2. **错误处理：** 根据StateCode和Message给用户友好的提示
3. **成功处理：** 创建成功后可以跳转到线索详情页面
4. **日志记录：** 建议记录创建操作的日志便于追踪

## 前端集成示例

### JavaScript/jQuery 示例
```javascript
function createLead(name, phone, gender, options = {}) {
    const requestData = {
        Name: name,
        PhoneNumber: phone,
        Gender: gender,
        ...options
    };
    
    $.ajax({
        url: '/api/lead/create',
        type: 'POST',
        contentType: 'application/json',
        data: JSON.stringify(requestData),
        success: function(response) {
            if (response.StateCode === 200) {
                console.log('线索创建成功:', response.Data);
                alert(`线索创建成功！\n线索ID：${response.Data.leadId}\n客户ID：${response.Data.customerId}`);
                // 可以跳转到线索详情页面
                // window.location.href = `/lead/detail/${response.Data.leadId}`;
            } else {
                alert('创建失败：' + response.Message);
            }
        },
        error: function(xhr, status, error) {
            console.error('请求失败:', error);
            alert('网络错误，请稍后重试');
        }
    });
}

// 使用示例
createLead('张三', '13800138000', '1');

// 带可选参数的使用示例
createLead('李四', '13900139000', '2', {
    LeadSource: 'WEIXIN_PENGYOUQUAN',
    AssignedTo: 'EMP001',
    Remark: '客户对美容项目感兴趣'
});
```

### Vue.js 示例
```javascript
// 在Vue组件中
export default {
    data() {
        return {
            form: {
                name: '',
                phone: '',
                gender: '1',
                leadSource: 'ZIRAN_DAODIAN',
                assignedTo: '',
                remark: ''
            },
            loading: false
        }
    },
    methods: {
        async createLead() {
            // 前端验证
            if (!this.form.name || !this.form.phone || !this.form.gender) {
                this.$message.error('请填写完整的客户信息');
                return;
            }
            
            this.loading = true;
            try {
                const response = await this.$http.post('/api/lead/create', {
                    Name: this.form.name,
                    PhoneNumber: this.form.phone,
                    Gender: this.form.gender,
                    LeadSource: this.form.leadSource,
                    AssignedTo: this.form.assignedTo || undefined,
                    Remark: this.form.remark || undefined
                });
                
                if (response.data.StateCode === 200) {
                    this.$message.success('线索创建成功！');
                    const data = response.data.Data;
                    
                    // 可以触发事件或跳转页面
                    this.$emit('lead-created', data);
                    
                    // 重置表单
                    this.resetForm();
                } else {
                    this.$message.error(response.data.Message);
                }
            } catch (error) {
                console.error('创建线索失败:', error);
                this.$message.error('创建失败，请稍后重试');
            } finally {
                this.loading = false;
            }
        },
        
        resetForm() {
            this.form = {
                name: '',
                phone: '',
                gender: '1',
                leadSource: 'ZIRAN_DAODIAN',
                assignedTo: '',
                remark: ''
            };
        }
    }
}
```

### React 示例
```javascript
import { useState } from 'react';
import axios from 'axios';

function CreateLeadForm() {
    const [form, setForm] = useState({
        name: '',
        phone: '',
        gender: '1',
        leadSource: 'ZIRAN_DAODIAN',
        assignedTo: '',
        remark: ''
    });
    const [loading, setLoading] = useState(false);
    
    const handleSubmit = async (e) => {
        e.preventDefault();
        
        // 前端验证
        if (!form.name || !form.phone || !form.gender) {
            alert('请填写完整的客户信息');
            return;
        }
        
        setLoading(true);
        try {
            const response = await axios.post('/api/lead/create', {
                Name: form.name,
                PhoneNumber: form.phone,
                Gender: form.gender,
                LeadSource: form.leadSource,
                AssignedTo: form.assignedTo || undefined,
                Remark: form.remark || undefined
            });
            
            if (response.data.StateCode === 200) {
                alert('线索创建成功！');
                console.log('创建结果:', response.data.Data);
                
                // 重置表单
                setForm({
                    name: '',
                    phone: '',
                    gender: '1',
                    leadSource: 'ZIRAN_DAODIAN',
                    assignedTo: '',
                    remark: ''
                });
            } else {
                alert('创建失败：' + response.data.Message);
            }
        } catch (error) {
            console.error('创建线索失败:', error);
            alert('网络错误，请稍后重试');
        } finally {
            setLoading(false);
        }
    };
    
    return (
        <form onSubmit={handleSubmit}>
            {/* 表单组件 */}
            <button type="submit" disabled={loading}>
                {loading ? '创建中...' : '创建线索'}
            </button>
        </form>
    );
}
```

## 测试用例

### 正常流程测试
```bash
# 使用curl测试
curl -X POST http://localhost:8080/api/lead/create \
  -H "Content-Type: application/json" \
  -d '{
    "Name": "测试用户",
    "PhoneNumber": "13800138000",
    "Gender": "1"
  }'
```

### 异常情况测试
```bash
# 测试必填字段为空
curl -X POST http://localhost:8080/api/lead/create \
  -H "Content-Type: application/json" \
  -d '{
    "Name": "",
    "PhoneNumber": "13800138000",
    "Gender": "1"
  }'

# 测试手机号重复（需要先创建一个相同手机号的记录）
curl -X POST http://localhost:8080/api/lead/create \
  -H "Content-Type: application/json" \
  -d '{
    "Name": "重复测试",
    "PhoneNumber": "13800138000",
    "Gender": "2"
  }'
```

---

**文档版本：** 1.0  
**最后更新：** 2025-01-15  
**维护人员：** Ztx  
**联系方式：** 如有问题请联系后端开发团队
