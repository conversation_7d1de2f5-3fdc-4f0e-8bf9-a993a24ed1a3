<template>
  <div class="phoneCallBackConfig content_body" v-loading="loading">
    <div class="nav_header">
      <el-row>
        <el-col :span="20">
          <el-form :inline="true" size="small" @keyup.enter.native="handleSearch">
            <el-form-item label="员工">
              <el-input v-model="searchForm.Name" placeholder="请输入员工名称、编号" clearable @clear="handleSearch"></el-input>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleSearch" v-prevent-click>搜索</el-button>
            </el-form-item>
          </el-form>
        </el-col>
        <el-col :span="4" class="text_right">
          <el-button type="primary" size="small" @click="onAddPhoneCallBackConfig">新增</el-button>
        </el-col>
      </el-row>
    </div>

    <el-table size="small" :data="tableData">
      <el-table-column prop="EmployeeName" label="员工名称"></el-table-column>
      <el-table-column prop="EcpId" label="坐席号"></el-table-column>
      <el-table-column label="操作" width="80">
        <template slot-scope="scope">
          <el-button type="danger" size="small" @click="onRemoverPhoneCallBack(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <div class="text_right pad_15">
      <el-pagination background v-if="paginations.total > 0" @current-change="handleCurrentChange" :current-page.sync="paginations.page" :page-size="paginations.page_size" :layout="paginations.layout" :total="paginations.total"></el-pagination>
    </div>

    <el-dialog title="新增外呼配置" :visible.sync="dialogVisible" width="550px">
      <el-form :model="addRuleForm" :rules="rules" ref="addRuleFormRef" label-width="100px" size="small">
        <el-form-item label="员工" prop="EmployeeID">
          <el-select :popper-append-to-body="false" popper-class="custom-el-select" v-model="addRuleForm.EmployeeID" filterable remote :remote-method="searchEmpRemote" placeholder="请选择员工" clearable>
            <el-option v-for="item in searchData" :key="item.ID" :label="item.Name" :value="item.ID">
              <div class="dis_flex flex_dir_column pad_5_0">
                <div style="line-height: 25px">
                  <span style="float: left">{{ item.Name }}</span>
                  <span style="float: right; color: #8492a6; font-size: 13px">{{ item.ID }}</span>
                </div>
                <div style="line-height: 20px; color: #8492a6">
                  <span style="float: left">{{ item.JobName }}</span>
                  <span style="float: right; font-size: 13px" class="marlt_5">{{ item.JobName }}</span>
                </div>
              </div>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="坐席号" prop="EcpId">
          <el-input v-model="addRuleForm.EcpId" placeholder="请输入坐席号" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="dialogVisible = false" v-prevent-click>取消</el-button>
        <el-button type="primary" size="small" @click="onPhoneCallBack_add" :loading="addLoading" v-prevent-click>保存</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import API from "@/api/iBeauty/Basic/phoneCallBackConfig.js";
import FollowUpAPI from "@/api/iBeauty/Workbench/followUp";
export default {
  name: "phoneCallBackConfig",

  props: {},
  /** 监听数据变化   */
  watch: {},
  /**  引入的组件  */
  components: {},
  /**  Vue 实例的数据对象**/
  data() {
    return {
      loading: false,
      addLoading: false,
      dialogVisible: false,
      searchForm: {
        Name: "", //员工名称或编号
      },
      tableData: [],
      paginations: {
        page: 1, // 当前位于哪页
        total: 0, // 总数
        page_size: 10, // 1页显示多少条
        layout: "total, prev, pager, next,jumper", // 翻页属性
      }, //需要给分页组件传的信息
      addRuleForm: {
        EmployeeID: "", //员工编号
        EcpId: "", //坐席号
      },
      rules: {
        EmployeeID: [{ required: true, message: "请选择员工", trigger: "change" }],
        EcpId: [{ required: true, message: "请输入坐席号", trigger: "change" }],
      },
      searchData: [],
    };
  },
  /**计算属性  */
  computed: {},
  /**  方法集合  */
  methods: {
    /**    */
    onPhoneCallBack_add() {
      let that = this;
      that.$refs.addRuleFormRef.validate((valid) => {
        if (valid) {
          that.phoneCallBack_add();
        }
      });
    },
    /**  搜索其他人员  */
    searchEmpRemote(query) {
      this.getSearch(query);
    },
    /**  新增  */
    onAddPhoneCallBackConfig() {
      let that = this;
      that.addRuleForm = {
        EmployeeID: "", //员工编号
        EcpId: "", //坐席号
      };
      that.dialogVisible = true;
    },
    /**    */
    handleCurrentChange(page) {
      let that = this;
      that.paginations.page = page;
      that.phoneCallBack_list();
    },
    /**    */
    handleSearch() {
      let that = this;
      that.paginations.page = 1;
      that.phoneCallBack_list();
    },
    /**    */
    onRemoverPhoneCallBack(row) {
      let that = this;
      that.$confirm;

      that
        .$confirm("是否删除?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
        .then(() => {
          that.phoneCallBack_delete(row.ID);
        })
        .catch(() => {
          that.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    /* •••••••••••••••••••••••••••••••••••••••••• */
    /**    */
    phoneCallBack_list() {
      let that = this;
      let params = {
        PageNum: that.paginations.page, //页码
        Name: that.searchForm.Name, //员工名称或编号
      };
      API.phoneCallBack_list(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.tableData = res.List;
            that.paginations.total = res.Total;
            that.paginations.page_size = res.PageSize;
          } else {
            that.$message.error(res.Message);
          }
        })
        .catch((fail) => {
          that.$message.error(fail);
        });
    },
    /**    */
    phoneCallBack_add() {
      let that = this;
      that.addLoading = true;
      let params = {
        EmployeeID: that.addRuleForm.EmployeeID, //员工编号
        EcpId: that.addRuleForm.EcpId, //坐席号
      };
      API.phoneCallBack_add(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.dialogVisible = false;
            that.addLoading = false;
            that.phoneCallBack_list();
            that.$message.success("操作成功");
          } else {
            that.addLoading = false;
            that.$message.error(res.Message);
          }
        })
        .catch((fail) => {
          that.$message.error(fail);
          that.addLoading = false;
        });
    },
    /**    */
    phoneCallBack_delete(ID) {
      let that = this;
      that.loading = true;
      let params = {
        ID: ID, //编号
      };
      API.phoneCallBack_delete(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.loading = false;
            that.phoneCallBack_list();
            that.$message.success("操作成功");
          } else {
            that.loading = false;
            that.$message.error(res.Message);
          }
        })
        .catch((fail) => {
          that.loading = false;
          that.$message.error(fail);
        });
    },
    /**    */
    phoneCallBack_callBackLog() {
      let that = this;
      let params = {
        PageNum: 1, //页码
        CustomerID: "2600", //顾客编号
      };
      API.phoneCallBack_callBackLog(params)
        .then((res) => {
          if (res.StateCode == 200) {
            console.log(res);
          } else {
            that.$message.error(res.Message);
          }
        })
        .catch((fail) => {
          that.$message.error(fail);
        });
    },

    /* 获取指派-其他人员 */
    getSearch(SearchKey) {
      let that = this;
      let params = {
        SearchKey: SearchKey,
      };
      FollowUpAPI.getSearch(params).then((res) => {
        if (res.StateCode == 200) {
          that.searchData = res.Data;
        } else {
          that.$message.error({
            message: res.Message,
            duration: 2000,
          });
        }
      });
    },
  },
  /** 创建实例之前执行函数   */
  beforeCreate() {},
  /**  实例创建完成之后  */
  created() {},
  /**  在挂载开始之前被调用  */
  beforeMount() {},
  /**  实例被挂载后调用  */
  mounted() {
    this.phoneCallBack_list();
  },
  /**  数据更新 之前 调用  */
  beforeUpdate() {},
  /** 数据更新 完成 调用   */
  updated() {},
  /**  实例销毁之前调用  */
  beforeDestroy() {},
  /**  实例销毁后调用  */
  destroyed() {},
};
</script>

<style lang="scss">
.phoneCallBackConfig {
}
.custom-el-select {
  li {
    line-height: normal;
    height: auto;
  }
}
</style>
