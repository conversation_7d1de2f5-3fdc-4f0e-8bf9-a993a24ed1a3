# 线索跟进功能前端修改文档

## 文档信息
- **版本**: v1.0
- **创建日期**: 2025-01-15
- **创建人**: Ztx
- **修改类型**: 功能改造
- **影响范围**: 线索管理、跟进记录、预约功能

## 改造概述

### 改造目标
1. **数据分离**: 线索数据和跟进数据分离存储
2. **NEW标识**: 通过线索状态显示新客户标识
3. **权限清晰**: 区分线索查看和跟进操作权限
4. **流程优化**: 线索拉取 → 线索分配 → 跟进操作 → 预约转化

### 核心变化
- **新增线索表**: 独立管理线索数据
- **跟进表关联**: 通过LeadID关联线索
- **状态管理**: 通过Status字段管理线索生命周期
- **NEW标识**: Status=0或1时显示NEW标识

## 新增接口

### 1. 线索列表查询
```
POST /api/lead/list
```

**请求参数**:
```json
{
  "PageNum": 1,
  "PageSize": 20,
  "AssignedTo": "员工ID",           // 可选，分配给谁
  "Status": 0,                     // 可选，线索状态
  "LeadSource": "DOUYIN_XINXILIU", // 可选，线索来源
  "StartDate": "2025-01-01",       // 可选，开始时间
  "EndDate": "2025-01-31",         // 可选，结束时间
  "CustomerName": "张三",           // 可选，客户姓名或手机号(支持模糊搜索)
  "ProvinceCode": "110000",        // 可选，省份代码筛选
  "CityCode": "110100",            // 可选，城市代码筛选
  "AreaCode": "110101",            // 可选，区域代码筛选
  "IsDeal": true,                  // 可选，是否成交(true-有等级，false-无等级)
  "IsNewOnly": true,               // 可选，只显示新客户
  "IsOwnOnly": false               // 可选，只显示自己的线索
}
```

**响应数据**:
```json
{
  "Success": true,
  "Data": {
    "PageNum": 1,
    "PageSize": 20,
    "Total": 100,
    "List": [
      {
        "ID": 123,                           // 线索ID
        "CustomerID": 456,                   // 客户ID
        "CustomerName": "张三",               // 客户姓名
        "PhoneNumber": "13800138000",        // 客户电话
        "LeadSource": "DOUYIN_XINXILIU",     // 线索来源代码
        "LeadSourceName": "抖音信息流",       // 线索来源名称
        "LeadSourceDetail": "抖音信息流线索", // 线索来源详情
        "Status": 1,                         // 线索状态
        "StatusName": "已分配",               // 线索状态名称
        "AssignedTo": "emp001",              // 分配给谁
        "AssignedToName": "李客服",           // 分配给谁姓名
        "LeadCreatedOn": "2025-01-15 10:30:00", // 线索创建时间
        "AssignedOn": "2025-01-15 11:00:00", // 分配时间
        "Address": "北京市朝阳区xxx",         // 详细地址
        "Remark": "客户备注信息",             // 备注
        "IsNew": 1,                          // 是否新客户(1-新客户，0-老客户)
        "CustomerLevelID": 2,                // 客户等级ID
        "CustomerLevelName": "银卡会员",      // 客户等级名称
        "ProvinceCode": "110000",            // 省份代码
        "CityCode": "110100",                // 城市代码
        "AreaCode": "110101",                // 区域代码
        "ProvinceName": "北京市",             // 省份名称
        "CityName": "北京市",                 // 城市名称
        "AreaName": "东城区"                  // 区域名称
      }
    ]
  }
}
```

### 2. 我的线索列表
```
POST /api/lead/myList
```
**说明**: 只显示分配给当前用户的线索，参数同线索列表查询

### 3. 新客户线索列表
```
POST /api/lead/newLeadList
```
**说明**: 只显示NEW标识的线索(Status=0或1)，参数同线索列表查询

### 4. 线索详情查询
```
GET /api/lead/detail?leadId=123
```

**响应数据**:
```json
{
  "Success": true,
  "Data": {
    "ID": 123,
    "CustomerID": 456,
    "LeadSource": "DOUYIN_XINXILIU",
    "LeadSourceDetail": "抖音信息流线索",
    "OriginalData": "{...}",              // 原始数据JSON
    "LeadCreatedOn": "2025-01-15 10:30:00",
    "Status": 1,
    "AssignedTo": "emp001",
    "Address": "北京市朝阳区xxx",
    "Remark": "备注信息"
  }
}
```

### 5. 线索状态统计
```
GET /api/lead/statusCount
```

**响应数据**:
```json
{
  "Success": true,
  "Data": {
    "newLeadCount": 10,        // 新线索数量
    "assignedCount": 25,       // 已分配数量
    "followingCount": 15,      // 跟进中数量
    "convertedCount": 8,       // 已转化数量
    "closedCount": 2,          // 已关闭数量
    "totalCount": 60,          // 总数量
    "newCustomerCount": 35     // 新客户数量
  }
}
```

### 6. 线索跟进记录数量
```
GET /api/lead/followUpCount?leadId=123
```

**响应数据**:
```json
{
  "Success": true,
  "Data": {
    "leadId": 123,
    "followUpCount": 5,        // 跟进记录数量
    "isNew": false             // 是否为新客户
  }
}
```

### 7. 手动分配线索
```
POST /api/lead/assign?leadId=123&assignedTo=emp001
```

**响应数据**:
```json
{
  "Success": true,
  "Message": "线索分配成功"
}
```

### 8. 查询客户的所有预约记录
```
GET /api/lead/appointmentRecords?customerId=456
```

**响应数据**:
```json
{
  "Success": true,
  "Data": [
    {
      "ID": "APT001",                    // 预约ID
      "CustomerID": 456,                 // 客户ID
      "CustomerName": "张三",             // 客户姓名
      "PhoneNumber": "13800138000",      // 客户电话
      "AppointmentDate": "2025-01-20 14:00:00", // 预约时间
      "Period": 1,                       // 预约时段
      "Content": "面部护理体验",          // 预约内容
      "AppointmentStatus": "20",         // 预约状态代码
      "AppointmentStatusName": "已到店",  // 预约状态名称
      "AppointmentTypeID": 1,            // 预约类型ID
      "AppointmentTypeName": "体验预约",  // 预约类型名称
      "AppointmentCategory": 2,          // 预约类别代码
      "AppointmentCategoryName": "线索跟进客服预约", // 预约类别名称
      "EntityID": 1,                     // 门店ID
      "EntityName": "总店",              // 门店名称
      "CreatedOn": "2025-01-15 10:30:00", // 创建时间
      "CreatedByName": "李客服",         // 创建人姓名
      "Channel": "PC",                   // 渠道代码
      "ChannelName": "PC"                // 渠道名称
    }
  ]
}
```

### 8. 批量分配线索
```
POST /api/lead/batchAssign?assignedTo=emp001
```

**请求参数**:
```json
[123, 124, 125]  // 线索ID数组
```

**响应数据**:
```json
{
  "Success": true,
  "Message": "批量分配成功，共分配 3 条线索"
}
```

### 9. 手动标记转化
```
POST /api/lead/markConverted?leadId=123&remark=客户已成交
```

**响应数据**:
```json
{
  "Success": true,
  "Message": "线索已标记为转化"
}
```

### 10. 同步转化状态
```
POST /api/lead/syncConversion
```

**说明**: 检查所有跟进中的线索，如果对应客户有等级则自动标记为转化

**响应数据**:
```json
{
  "Success": true,
  "Message": "已同步 5 条线索的转化状态"
}
```

## 修改的接口

### 1. 跟进记录创建
```
POST /api/followUp/create
```

**请求参数变化**:
```json
{
  "CustomerID": 456,
  "LeadID": 123,                    // 新增必填：关联线索ID
  "FollowUpMethodID": 1,
  "FollowUpStatusID": 1,
  "FollowUpContent": "电话沟通了解需求",
  "LeadSource": "DOUYIN_XINXILIU",
  "IsNextFollowUp": true,
  "PlannedOn": "2025-01-20 14:00:00",
  "PlannedRemark": "下次预约到店体验"
}
```

**重要变化**:
- **新增LeadID字段**: 必填，用于关联线索
- **状态自动更新**: 创建跟进时自动将线索状态更新为"跟进中"(Status=2)

### 2. 预约创建
```
POST /api/followUp/createAppointmentBill
```

**请求参数变化**:
```json
{
  "CustomerID": 456,
  "LeadID": 123,                    // 新增：关联线索ID
  "EntityID": 1,
  "AppointmentDate": "2025-01-20 14:00:00",
  "Remark": "客户预约面部护理体验"
}
```

**重要变化**:
- **新增LeadID字段**: 用于关联线索
- **状态自动更新**: 创建预约时自动将线索状态更新为"已转化"(Status=3)

### 3. 跟进记录查询
**现有接口保持不变**，但响应数据中新增LeadID字段：

```json
{
  "ID": 789,
  "CustomerID": 456,
  "LeadID": 123,                    // 新增：关联线索ID
  "CustomerName": "张三",
  "FollowUpContent": "电话沟通",
  // ... 其他字段保持不变
}
```

## 线索状态说明

### 状态枚举
```javascript
const LeadStatus = {
  NEW: 0,           // 新线索
  ASSIGNED: 1,      // 已分配
  FOLLOWING: 2,     // 跟进中
  CONVERTED: 3      // 已转化
};

const LeadStatusName = {
  0: '新线索',
  1: '已分配',
  2: '跟进中',
  3: '已转化'
};
```

### NEW标识显示逻辑
```javascript
// 显示NEW标识的条件
function showNewBadge(status) {
  return status === 0 || status === 1;  // 新线索或已分配
}
```

### 状态流转
```
新线索(0) → 已分配(1) → 跟进中(2) → 已转化(3)
```

### 状态变更触发条件
- **新线索 → 已分配**: 手动分配或自动分配
- **已分配 → 跟进中**: 创建第一条跟进记录时自动更新
- **跟进中 → 已转化**:
  - 手动标记转化（客服确认客户已成交）
  - 自动检测转化（客户获得等级时自动标记）
  - 批量同步转化（定期检查客户等级状态）

### 转化判断逻辑
- **真正转化**: 客户在系统中有了CustomerLevelID（获得会员等级）
- **预约不等于转化**: 客户可能多次预约才真正成交
- **持续跟进**: 即使预约了也要继续跟进，直到客户真正成交

### 分配操作权限
- **新线索(0)**: 可以分配
- **已分配(1)**: 可以重新分配
- **跟进中(2)**: 不能重新分配（已在跟进中）
- **已转化(3)**: 不能重新分配（已完成转化）

## 预约记录功能

### 功能说明
在线索页面的双击功能中新增预约记录查看功能，类似于跟进记录，显示该客户的所有预约记录。

### 功能特点
- **只读查看**：预约记录只能查看，不能删除
- **显示所有预约**：显示客户的所有预约记录，不限于线索相关
- **预约类别区分**：通过 AppointmentCategory 字段区分预约类型
  - 1: 门店预约
  - 2: 线索跟进客服预约
  - 3: 其他预约类型
- **详细信息**：显示预约时间、状态、门店、创建人等完整信息
- **状态标识**：清晰显示预约状态（待确认、已确认、已到店、已取消等）

### 预约记录字段说明
- **预约时间**：客户预约的具体时间
- **预约状态**：当前预约的处理状态（未到店、已到店、已取消）
- **预约类型**：预约的服务类型
- **预约类别**：区分预约来源
  - 1: 门店预约
  - 2: 线索跟进客服预约
  - 3: 其他预约类型
- **门店信息**：预约的具体门店
- **创建信息**：记录创建时间和创建人

### 预约状态说明
根据项目中的实际定义：
- **'10'**: 未到店（待确认）
- **'20'**: 已到店（已完成）
- **'30'**: 已取消

### 数据来源说明
- **不需要新增字段**：使用现有 TB_AppointmentBill 表
- **通过 AppointmentCategory 区分**：无需增加 LeadID 字段
- **显示所有预约**：客户的所有预约记录都会显示，包括各种类别
- **使用专门的Form类**：AppointmentRecordOutputForm 作为返回参数

## 实际修改记录

### 6. 在双击弹框中添加预约记录功能 ✅
- **文件**:
  - `src/views/iBeauty/Workbench/Component/followUpCustomerDetail.vue`
  - `src/views/iBeauty/Workbench/Component/workbenchAppointmentRecord.vue`
- **重大改造**: 将预约记录改造为跟进记录样式的时间轴展示
- **修改内容**:
  - 启用客户详情弹框中的"预约记录"标签页（取消注释第27-29行）
  - **完全重构预约记录组件**，采用与跟进记录相同的时间轴样式：
    - 按年月日分组显示预约记录
    - 支持展开/收起年份和月份
    - 每条预约记录显示详细信息卡片
  - **新增预约类别筛选**：
    - 1: 门店预约
    - 2: 线索跟进客服预约
    - 3: 其他预约类型
  - **预约记录详细信息显示**：
    - 创建人姓名和职位（需要后台添加CreatedByJobName字段）
    - 预约类别标签（不同颜色区分）
    - 预约状态（未到店/已到店/已取消）
    - 预约类型、预约时间、预约时长
    - 预约备注（Content字段）
    - 创建时间
  - **操作功能**：
    - 新建预约按钮
    - 修改预约（仅未到店状态）
    - 取消预约（仅未到店状态）
  - **使用新API**: 调用 `appointmentBill_getCustomerAppointmentAll` 获取客户所有预约记录
- **功能说明**:
  - 双击线索列表中的任意行，会打开客户详情弹框
  - 在弹框中可以看到"基本档案"、"跟进记录"、"预约记录"、"订单信息"、"通话记录"等标签页
  - 预约记录标签页采用时间轴样式，与跟进记录保持一致的用户体验
  - 通过预约类别可以区分是门店预约还是线索跟进客服预约
  - 支持按预约类别筛选显示

### 🔧 修复记录

#### 修复组件属性类型警告
- **问题**: `clueFollowUpCustomerBill` 组件的 `customerID` 属性期望 String 类型，但传入的是 Number 类型
- **修复**: 在 `followUpCustomerDetail.vue` 中将传入的 `customerID` 转换为字符串：`:customerID="String(customerID)"`
- **位置**: 第25行

#### 修复预约记录组件初始化错误
- **问题**: `workbenchAppointmentRecord.vue` 组件在 data() 函数中访问 `this.$store.state.user.userInfo.EmployeeID` 时出错
- **原因**: 组件初始化时 store 可能还未完全加载，导致 `user` 或 `userInfo` 为 undefined
- **修复**:
  - 在 data() 中将 `employeeID` 设置为 `null`
  - 在 mounted() 生命周期中安全地获取用户ID
  - 添加安全检查：`if (this.$store.state.user && this.$store.state.user.userInfo)`
- **位置**: 第127行和第307-309行

#### 修复预约记录API调用错误 ⭐ **关键修复**
- **问题**: 预约记录组件使用了错误的API接口
- **原因**: 应该使用文档中定义的 `/api/lead/appointmentRecords?customerId=xxx` 接口，但代码中用的是其他接口
- **修复**:
  - 将API导入改为 `import * as API from "@/api/index"`
  - 使用正确的接口：`API.get('/api/lead/appointmentRecords?customerId=' + customerID)`
  - 修改响应数据处理：从 `res.StateCode === 200` 改为 `res.Success`
  - 取消预约接口改为：`API.POST('/api/appointment/cancel', { ID: item.ID })`
  - 添加调试日志以便排查问题
  - 添加无数据提示界面
- **位置**: 第110行（API导入）、第138行（接口调用）、第141行（响应处理）、第264行（取消预约）

#### 修复API导入错误 ✅ **2025-01-15 最新修复**
- **问题**: `workbenchAppointmentRecord.vue` 组件中导入了 `axios` 但使用的是 `API.get()` 方法
- **原因**: 代码第110行导入的是 `import axios from 'axios'`，但第138行使用的是 `API.get()`，导致API未定义错误
- **修复**:
  - 将第110行的 `import axios from 'axios'` 改为 `import * as API from "@/api/index"`
  - 确保API调用方法与导入的模块一致
- **位置**: 第110行（API导入修复）

#### 修复预约记录接口调用 ⭐ **2025-01-15 关键修复**
- **问题**: 预约记录组件使用了错误的API接口路径
- **原因**:
  - 文档中定义的 `/api/lead/appointmentRecords?customerId=xxx` 接口可能不存在
  - 项目中实际存在的接口是 `api/appointmentBill/getCustomerAppointmentAll`
- **修复**:
  - 将接口调用从 `API.get('/api/lead/appointmentRecords?customerId=' + customerID)`
  - 改为 `API.POST("api/appointmentBill/getCustomerAppointmentAll", { CustomerID: that.customerID })`
  - 将响应判断从 `res.Success` 改为 `res.StateCode === 200`
  - 使用POST方法并传递CustomerID参数
- **位置**: 第133-142行（接口调用和响应处理）

### 🔧 后台需要补充的字段

为了完美实现跟进记录样式的预约记录，建议后台在 `appointmentBill_getCustomerAppointmentAll` 接口返回数据中添加：

```json
{
  // 现有字段...
  "CreatedByJobName": "客服"  // 创建人职位名称
}
```

这样前端就能显示 "李客服[客服]" 的格式，与跟进记录保持一致。

### 测试要点
1. 验证NEW标识在Status=0和Status=1时正确显示
2. 测试线索状态筛选功能是否正常工作
3. 测试成交会员筛选功能是否正常工作
4. 验证跟进创建时LeadID参数正确传递
5. 验证预约创建时LeadID参数正确传递
6. **测试双击客户行时预约记录标签页是否正常显示**
7. **验证预约记录时间轴样式是否与跟进记录一致**
8. **测试预约类别筛选功能是否正常工作**
9. **验证预约记录的修改和取消功能**
10. 确认原有功能不受影响

## 前端页面修改建议

### 1. 线索列表页面
**新增功能**:
- 线索状态筛选
- NEW标识显示
- 线索来源筛选
- 状态统计面板

**页面布局建议**:
```html
<!-- 状态统计卡片 -->
<div class="status-cards">
  <div class="card">新客户: {{newCustomerCount}}</div>
  <div class="card">跟进中: {{followingCount}}</div>
  <div class="card">已转化: {{convertedCount}}</div>
</div>

<!-- 筛选条件 -->
<div class="filters">
  <select v-model="queryForm.Status">
    <option value="">全部状态</option>
    <option value="0">新线索</option>
    <option value="1">已分配</option>
    <option value="2">跟进中</option>
  </select>
  
  <checkbox v-model="queryForm.IsNewOnly">只显示新客户</checkbox>
</div>

<!-- 线索列表 -->
<div class="lead-list">
  <div class="lead-item" v-for="lead in leadList">
    <span class="new-badge" v-if="lead.IsNew">NEW</span>
    <span class="customer-name">{{lead.CustomerName}}</span>
    <span class="status">{{lead.StatusName}}</span>
    <button @click="createFollowUp(lead)">跟进</button>
  </div>
</div>
```

### 2. 跟进创建页面
**修改要点**:
- 传入LeadID参数
- 显示线索信息
- 创建成功后更新线索状态

```javascript
// 创建跟进
function createFollowUp(lead) {
  const form = {
    CustomerID: lead.CustomerID,
    LeadID: lead.ID,              // 必须传入线索ID
    FollowUpMethodID: 1,
    FollowUpContent: this.content,
    LeadSource: lead.LeadSource
  };
  
  api.post('/api/followUp/create', form).then(res => {
    if (res.Success) {
      // 创建成功，刷新线索列表
      this.refreshLeadList();
      // 线索状态会自动更新为"跟进中"
    }
  });
}
```

### 3. 预约创建页面
**修改要点**:
- 传入LeadID参数
- 创建成功后更新线索状态

```javascript
// 创建预约
function createAppointment(lead) {
  const form = {
    CustomerID: lead.CustomerID,
    LeadID: lead.ID,              // 传入线索ID
    EntityID: this.selectedEntity,
    AppointmentDate: this.appointmentDate,
    Remark: this.remark
  };
  
  api.post('/api/followUp/createAppointmentBill', form).then(res => {
    if (res.Success) {
      // 创建成功，线索状态会自动更新为"已转化"
      this.refreshLeadList();
    }
  });
}
```

## 样式建议

### NEW标识样式
```css
.new-badge {
  background: #ff4d4f;
  color: white;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: bold;
  margin-right: 8px;
}
```

### 状态标签样式
```css
.status-tag {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.status-new { background: #f0f0f0; color: #666; }
.status-assigned { background: #e6f7ff; color: #1890ff; }
.status-following { background: #fff2e8; color: #fa8c16; }
.status-converted { background: #f6ffed; color: #52c41a; }
```

## 注意事项

### 1. 兼容性处理
- 现有跟进记录查询接口保持兼容
- 客服预约表功能不受影响
- 原有筛选条件继续有效

### 2. 错误处理
- LeadID为空时的提示处理
- 线索不存在时的错误提示
- 权限不足时的提示

### 3. 性能优化
- 线索列表分页加载
- 状态统计缓存
- 避免频繁刷新

### 4. 用户体验
- NEW标识醒目显示
- 状态变更及时反馈
- 操作流程清晰引导

## 测试要点

### 功能测试
- [ ] 线索列表正常显示
- [ ] NEW标识正确显示
- [ ] 状态筛选功能正常
- [ ] 跟进创建功能正常
- [ ] 预约创建功能正常
- [ ] 状态自动更新正确

### 兼容性测试
- [ ] 现有跟进功能不受影响
- [ ] 客服预约表正常显示
- [ ] 原有筛选条件有效

### 性能测试
- [ ] 大数据量下响应正常
- [ ] 页面加载速度正常
- [ ] 内存使用正常

## 前端实现示例

### Vue.js 实现示例

#### 1. 线索列表组件
```vue
<template>
  <div class="lead-management">
    <!-- 状态统计 -->
    <div class="status-overview">
      <el-card class="status-card" v-for="(item, key) in statusCount" :key="key">
        <div class="status-number">{{ item.count }}</div>
        <div class="status-label">{{ item.label }}</div>
      </el-card>
    </div>

    <!-- 筛选条件 -->
    <el-form :model="queryForm" inline class="filter-form">
      <el-form-item label="线索状态">
        <el-select v-model="queryForm.Status" placeholder="全部状态" clearable>
          <el-option label="新线索" :value="0"></el-option>
          <el-option label="已分配" :value="1"></el-option>
          <el-option label="跟进中" :value="2"></el-option>
          <el-option label="已转化" :value="3"></el-option>
          <el-option label="已关闭" :value="4"></el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="线索来源">
        <el-select v-model="queryForm.LeadSource" placeholder="全部来源" clearable>
          <el-option label="抖音信息流" value="DOUYIN_XINXILIU"></el-option>
          <el-option label="抖音团购" value="DOUYIN_TUANGOU"></el-option>
          <el-option label="美团点评" value="MEITUAN_DIANPING"></el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="成交状态">
        <el-select v-model="queryForm.IsDeal" placeholder="全部状态" clearable>
          <el-option label="已成交(有等级)" :value="true"></el-option>
          <el-option label="未成交(无等级)" :value="false"></el-option>
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-checkbox v-model="queryForm.IsNewOnly">只显示新客户</el-checkbox>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" @click="searchLeads">查询</el-button>
        <el-button @click="resetForm">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 线索列表 -->
    <el-table :data="leadList" v-loading="loading">
      <el-table-column label="客户信息" min-width="200">
        <template slot-scope="scope">
          <div class="customer-info">
            <el-tag v-if="scope.row.IsNew" type="danger" size="mini" class="new-badge">NEW</el-tag>
            <div class="customer-name">{{ scope.row.CustomerName }}</div>
            <div class="customer-phone">{{ scope.row.PhoneNumber }}</div>
            <div class="customer-level" v-if="scope.row.CustomerLevelName">
              <el-tag type="info" size="mini">{{ scope.row.CustomerLevelName }}</el-tag>
            </div>
          </div>
        </template>
      </el-table-column>

      <el-table-column prop="LeadSourceName" label="线索来源" width="120"></el-table-column>

      <el-table-column label="地区" width="150">
        <template slot-scope="scope">
          <div class="location-info">
            <div v-if="scope.row.ProvinceName">{{ scope.row.ProvinceName }}</div>
            <div v-if="scope.row.CityName" class="city-name">{{ scope.row.CityName }}</div>
            <div v-if="scope.row.AreaName" class="area-name">{{ scope.row.AreaName }}</div>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="状态" width="100">
        <template slot-scope="scope">
          <el-tag :type="getStatusType(scope.row.Status)" size="mini">
            {{ scope.row.StatusName }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column prop="AssignedToName" label="分配给" width="100"></el-table-column>
      <el-table-column prop="LeadCreatedOn" label="创建时间" width="150"></el-table-column>

      <el-table-column label="操作" width="300">
        <template slot-scope="scope">
          <el-button size="mini" @click="viewDetail(scope.row)">详情</el-button>

          <!-- 分配按钮：只有新线索和已分配状态可以分配 -->
          <el-button
            v-if="scope.row.Status === 0 || scope.row.Status === 1"
            size="mini"
            type="warning"
            @click="assignLead(scope.row)">
            分配
          </el-button>

          <!-- 跟进按钮：已分配和跟进中状态可以跟进 -->
          <el-button
            v-if="scope.row.Status === 1 || scope.row.Status === 2"
            size="mini"
            type="primary"
            @click="createFollowUp(scope.row)">
            跟进
          </el-button>

          <!-- 预约按钮：跟进中状态可以预约 -->
          <el-button
            v-if="scope.row.Status === 2"
            size="mini"
            type="success"
            @click="createAppointment(scope.row)">
            预约
          </el-button>

          <!-- 预约记录按钮：查看预约记录 -->
          <el-button
            size="mini"
            type="info"
            @click="viewAppointmentRecords(scope.row)">
            预约记录
          </el-button>

          <!-- 标记转化按钮：跟进中状态可以标记转化 -->
          <el-button
            v-if="scope.row.Status === 2"
            size="mini"
            type="info"
            @click="markConverted(scope.row)">
            标记转化
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <el-pagination
      @current-change="handlePageChange"
      :current-page="queryForm.PageNum"
      :page-size="queryForm.PageSize"
      :total="total"
      layout="total, prev, pager, next, jumper">
    </el-pagination>
  </div>
</template>

<script>
export default {
  name: 'LeadManagement',
  data() {
    return {
      loading: false,
      leadList: [],
      total: 0,
      queryForm: {
        PageNum: 1,
        PageSize: 20,
        Status: null,
        LeadSource: '',
        ProvinceCode: '',
        CityCode: '',
        AreaCode: '',
        IsDeal: null,
        IsNewOnly: false,
        CustomerName: ''  // 支持姓名或手机号搜索
      },
      statusCount: {
        newCustomer: { count: 0, label: '新客户' },
        following: { count: 0, label: '跟进中' },
        converted: { count: 0, label: '已转化' }
      }
    }
  },
  mounted() {
    this.loadStatusCount();
    this.loadLeadList();
  },
  methods: {
    // 加载状态统计
    async loadStatusCount() {
      try {
        const res = await this.$api.get('/api/lead/statusCount');
        if (res.Success) {
          this.statusCount.newCustomer.count = res.Data.newCustomerCount;
          this.statusCount.following.count = res.Data.followingCount;
          this.statusCount.converted.count = res.Data.convertedCount;
        }
      } catch (error) {
        console.error('加载状态统计失败:', error);
      }
    },

    // 加载线索列表
    async loadLeadList() {
      this.loading = true;
      try {
        const res = await this.$api.post('/api/lead/list', this.queryForm);
        if (res.Success) {
          this.leadList = res.Data.List;
          this.total = res.Data.Total;
        }
      } catch (error) {
        this.$message.error('加载线索列表失败');
      } finally {
        this.loading = false;
      }
    },

    // 搜索线索
    searchLeads() {
      this.queryForm.PageNum = 1;
      this.loadLeadList();
    },

    // 重置表单
    resetForm() {
      this.queryForm = {
        PageNum: 1,
        PageSize: 20,
        Status: null,
        LeadSource: '',
        ProvinceCode: '',
        CityCode: '',
        AreaCode: '',
        IsDeal: null,
        IsNewOnly: false,
        CustomerName: ''
      };
      this.loadLeadList();
    },

    // 分页处理
    handlePageChange(page) {
      this.queryForm.PageNum = page;
      this.loadLeadList();
    },

    // 获取状态类型
    getStatusType(status) {
      const typeMap = {
        0: 'info',    // 新线索
        1: 'primary', // 已分配
        2: 'warning', // 跟进中
        3: 'success'  // 已转化
      };
      return typeMap[status] || 'info';
    },

    // 查看详情
    viewDetail(lead) {
      this.$router.push(`/lead/detail/${lead.ID}`);
    },

    // 创建跟进
    createFollowUp(lead) {
      this.$router.push({
        path: '/followup/create',
        query: {
          leadId: lead.ID,
          customerId: lead.CustomerID,
          customerName: lead.CustomerName
        }
      });
    },

    // 创建预约
    createAppointment(lead) {
      this.$router.push({
        path: '/appointment/create',
        query: {
          leadId: lead.ID,
          customerId: lead.CustomerID,
          customerName: lead.CustomerName
        }
      });
    },

    // 分配线索
    assignLead(lead) {
      this.$prompt('请选择分配给的员工', '分配线索', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputType: 'select',
        inputOptions: this.employeeOptions // 需要预先加载员工列表
      }).then(({ value }) => {
        this.$api.post(`/api/lead/assign?leadId=${lead.ID}&assignedTo=${value}`).then(res => {
          if (res.Success) {
            this.$message.success('分配成功');
            this.loadLeadList();
          } else {
            this.$message.error(res.Message || '分配失败');
          }
        });
      }).catch(() => {
        this.$message.info('已取消分配');
      });
    },

    // 标记转化
    markConverted(lead) {
      this.$prompt('请输入转化备注（如：客户已成交办卡）', '标记转化', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputType: 'textarea'
      }).then(({ value }) => {
        this.$api.post(`/api/lead/markConverted?leadId=${lead.ID}&remark=${encodeURIComponent(value)}`).then(res => {
          if (res.Success) {
            this.$message.success('线索已标记为转化');
            this.loadLeadList();
          } else {
            this.$message.error(res.Message || '标记失败');
          }
        });
      }).catch(() => {
        this.$message.info('已取消标记');
      });
    },

    // 查看预约记录
    viewAppointmentRecords(lead) {
      this.$api.get(`/api/lead/appointmentRecords?customerId=${lead.CustomerID}`).then(res => {
        if (res.Success) {
          // 显示预约记录弹窗
          this.showAppointmentRecordsDialog(res.Data, lead);
        } else {
          this.$message.error(res.Message || '查询预约记录失败');
        }
      });
    },

    // 显示预约记录弹窗
    showAppointmentRecordsDialog(records, lead) {
      // 这里可以打开一个弹窗显示预约记录列表
      // 类似跟进记录的显示方式，但没有删除按钮
      // 显示所有预约类型：门店预约、线索跟进客服预约、其他预约类型
      this.$alert(
        `客户：${lead.CustomerName} 的所有预约记录共 ${records.length} 条`,
        '预约记录',
        {
          confirmButtonText: '确定',
          callback: () => {
            // 可以在这里打开详细的预约记录页面
            console.log('预约记录:', records);
            // 记录中包含 AppointmentCategory 和 AppointmentCategoryName 字段
            // 可以根据预约类别进行分类显示
          }
        }
      );
    }
  }
}
</script>

<style scoped>
.lead-management {
  padding: 20px;
}

.status-overview {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
}

.status-card {
  flex: 1;
  text-align: center;
}

.status-number {
  font-size: 24px;
  font-weight: bold;
  color: #409EFF;
}

.status-label {
  color: #666;
  margin-top: 5px;
}

.filter-form {
  margin-bottom: 20px;
  padding: 20px;
  background: #f5f5f5;
  border-radius: 4px;
}

.customer-info {
  position: relative;
}

.new-badge {
  position: absolute;
  top: -5px;
  right: -5px;
}

.customer-name {
  font-weight: bold;
  margin-bottom: 5px;
}

.customer-phone {
  color: #666;
  font-size: 12px;
}

.customer-level {
  margin-top: 5px;
}

.location-info {
  font-size: 12px;
  line-height: 1.4;
}

.city-name {
  color: #666;
}

.area-name {
  color: #999;
}
</style>
```

#### 2. 跟进创建组件修改
```javascript
// 在跟进创建组件中的关键修改
export default {
  data() {
    return {
      form: {
        CustomerID: null,
        LeadID: null,        // 新增：线索ID
        FollowUpMethodID: null,
        FollowUpStatusID: null,
        FollowUpContent: '',
        LeadSource: '',
        IsNextFollowUp: false,
        PlannedOn: '',
        PlannedRemark: ''
      }
    }
  },
  mounted() {
    // 从路由参数获取线索信息
    this.form.LeadID = this.$route.query.leadId;
    this.form.CustomerID = this.$route.query.customerId;
    this.form.LeadSource = this.$route.query.leadSource;
  },
  methods: {
    async submitFollowUp() {
      // 验证LeadID
      if (!this.form.LeadID) {
        this.$message.error('线索ID不能为空');
        return;
      }

      try {
        const res = await this.$api.post('/api/followUp/create', this.form);
        if (res.Success) {
          this.$message.success('跟进记录创建成功');
          // 线索状态会自动更新为"跟进中"
          this.$router.back();
        }
      } catch (error) {
        this.$message.error('创建跟进记录失败');
      }
    }
  }
}
```

## API 调用示例

### JavaScript/Axios 示例
```javascript
// API 服务封装
class LeadAPI {
  // 获取线索列表
  static async getLeadList(params) {
    return await axios.post('/api/lead/list', params);
  }

  // 获取我的线索
  static async getMyLeads(params) {
    return await axios.post('/api/lead/myList', params);
  }

  // 获取新客户线索
  static async getNewLeads(params) {
    return await axios.post('/api/lead/newLeadList', params);
  }

  // 获取线索详情
  static async getLeadDetail(leadId) {
    return await axios.get(`/api/lead/detail?leadId=${leadId}`);
  }

  // 获取状态统计
  static async getStatusCount() {
    return await axios.get('/api/lead/statusCount');
  }

  // 创建跟进记录
  static async createFollowUp(data) {
    return await axios.post('/api/followUp/create', data);
  }

  // 创建预约
  static async createAppointment(data) {
    return await axios.post('/api/followUp/createAppointmentBill', data);
  }

  // 获取预约记录
  static async getAppointmentRecords(customerId) {
    return await axios.get(`/api/lead/appointmentRecords?customerId=${customerId}`);
  }
}

// 使用示例
async function loadLeadData() {
  try {
    // 加载线索列表
    const leadRes = await LeadAPI.getLeadList({
      PageNum: 1,
      PageSize: 20,
      IsNewOnly: true
    });

    // 加载状态统计
    const statusRes = await LeadAPI.getStatusCount();

    // 加载预约记录
    const appointmentRes = await LeadAPI.getAppointmentRecords(456); // 传入客户ID

    console.log('线索列表:', leadRes.data);
    console.log('状态统计:', statusRes.data);
    console.log('预约记录:', appointmentRes.data);
  } catch (error) {
    console.error('加载数据失败:', error);
  }
}
```

## 数据流程图

```
线索拉取 → 线索表(TB_Lead)
    ↓
线索分配 → Status=1(已分配) + NEW标识
    ↓
点击跟进 → 创建跟进记录 + Status=2(跟进中) + 移除NEW标识
    ↓
点击预约 → 创建预约记录 + Status=3(已转化)
```

## 联系方式

如有疑问，请联系：
- **后端开发**: [后端开发联系方式]
- **产品经理**: [产品经理联系方式]
- **测试团队**: [测试团队联系方式]
