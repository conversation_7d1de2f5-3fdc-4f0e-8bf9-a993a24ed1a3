/**
 * Created by preference on 2020/07/01
 */

import * as API from "@/api/index";
export default {
  /**  52.1.项目存量列表 */
  treatGoodsProjectAccount: (params) => {
    return API.POST("api/treatGoodsAccount/projectAccount", params);
  },
  /** 52.2.产品存量列表 */
  treatGoodsProductAccount: (params) => {
    return API.POST("api/treatGoodsAccount/productAccount", params);
  },
  /**  52.3.储值卡存量列表 */
  treatGoodsSavingCardAccount: (params) => {
    return API.POST("api/treatGoodsAccount/savingCardAccount", params);
  },
  /**  52.4.储值卡消耗适用项目 */
  treatGoodsSavingCardAccountProject: (params) => {
    return API.POST("api/treatGoodsAccount/savingCardAccountProject", params);
  },

  /** 储值卡使用项目分类 */
  treatGoodsAccount_savingCardAccountProjectCategory: (params) => {
    return API.POST("api/treatGoodsAccount/savingCardAccountProjectCategory", params);
  },
  /** 储值卡使用项目分类 分类下项目 */
  treatGoodsAccount_savingCardAccountProjectByCategory: (params) => {
    return API.POST("api/treatGoodsAccount/savingCardAccountProjectByCategory", params);
  },

  /**  52.5通用次卡存量列表 */
  treatGoodsGeneralCardAccount: (params) => {
    return API.POST("api/treatGoodsAccount/generalCardAccount", params);
  },
  /**  52.5.  .历史数据 耗卡历史数据最多可以消耗几个项目*/
  treatGoodsAccount_generalCardHistoricalData: (params) => {
    return API.POST("api/treatGoodsAccount/generalCardHistoricalData", params);
  },
  /**  52.6.通用次卡消耗适用项目 */
  treatGoodsGeneralCardAccountProject: (params) => {
    return API.POST("api/treatGoodsAccount/generalCardAccountProject", params);
  },

  /**通用次卡  使用项目分类 */
  treatGoodsAccount_generalCardAccountProjectCategory: (params) => {
    return API.POST("api/treatGoodsAccount/generalCardAccountProjectCategory", params);
  },
  /** 通用次卡使用项目分类 分类下项目 */
  treatGoodsAccount_generalCardAccountProjectByCategory: (params) => {
    return API.POST("api/treatGoodsAccount/generalCardAccountProjectByCategory", params);
  },

  /**  52.7.时效卡存量列表*/
  treatGoodsTimeCardAccount: (params) => {
    return API.POST("api/treatGoodsAccount/timeCardAccount", params);
  },
  /**  52.8.时效卡消耗适用项目 */
  treatGoodsTimeCardAccountProject: (params) => {
    return API.POST("api/treatGoodsAccount/timeCardAccountProject", params);
  },

  /**时效卡卡  使用项目分类 */
  treatGoodsAccount_timeCardAccountProjectCategory: (params) => {
    return API.POST("api/treatGoodsAccount/timeCardAccountProjectCategory", params);
  },
  /** 时效次卡使用项目分类 分类下项目 */
  treatGoodsAccount_timeCardAccountProjectByCategory: (params) => {
    return API.POST("api/treatGoodsAccount/timeCardAccountProjectByCategory", params);
  },

  /**  52.9.套餐卡存量列表 */
  treatGoodsPackageCardAccount: (params) => {
    return API.POST("api/treatGoodsAccount/packageCardAccount", params);
  },
  /**  52.10.套餐卡存量明细 */
  treatGoodsPackageCardAccountDetails: (params) => {
    return API.POST("api/treatGoodsAccount/packageCardAccountDetails", params);
  },
  /**  52.11.获取项目消耗经手人 */
  treatGoodsProjectHandler: (params) => {
    return API.POST("api/treatHandler/projectHandler", params);
  },
  /**  52.12.获取产品消耗经手人 */
  treatGoodsProductHandler: (params) => {
    return API.POST("api/treatHandler/productHandler", params);
  },
  /**  52.13.获取储值卡消耗经手人 */
  treatGoodsSavingCardHandler: (params) => {
    return API.POST("api/treatHandler/savingCardHandler", params);
  },
  /**  52.14.消耗结账 */
  treatBillCreate: (params) => {
    return API.POST("api/treatBill/create", params);
  },
  //  消耗经手人
  treatHandler_allHandler: (params) => {
    return API.POST("api/treatHandler/allHandler", params);
  },



  
  //  查询项目会员折扣
  treatGoodsAccount_savingCardAccountProjectCustomerDiscount: (params) => {
    return API.POST("api/treatGoodsAccount/savingCardAccountProjectCustomerDiscount", params);
  },
};
