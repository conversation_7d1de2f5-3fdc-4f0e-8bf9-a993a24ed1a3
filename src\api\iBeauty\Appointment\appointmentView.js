/**
 * Created by preference on 2020/08/07
 *  zmx
 */

import * as API from "@/api/index";
export default {
  /** 预约看板-服务人员*/
  appointmentBill_servicer: (params) => {
    return API.POST("api/appointmentBill/servicer", params);
  },

  /** 获取员工和顾客预约信息  */
  appointmentBillAll: (params) => {
    return API.POST("api/appointmentBill/all", params);
  },
  /** 服务人员员工 -新增修改时用  */
  appointmentBill_ervicerEmployee: (params) => {
    return API.POST("api/appointmentBill/servicerEmployee", params);
  },
  /** 创建预约  */
  appointmentBillCreate: (params) => {
    return API.POST("api/appointmentBill/create", params);
  },
  /** 修改预约  */
  appointmentBillUpdate: (params) => {
    return API.POST("api/appointmentBill/update", params);
  },
 /** 修改预约  */
 appointmentBill_updateStatus: (params) => {
  return API.POST("api/appointmentBill/updateStatus", params);
},

  
  /** 预约列表 */
  appointmentBillList: (params) => {
    return API.POST("api/appointmentBill/list", params);
  },
  /** 预约详情 */
  appointmentBillInfo: (params) => {
    return API.POST("api/appointmentBill/info", params);
  },
  /** 可预约员工列表 */
  getEmployeeList: (params) => {
    return API.POST("api/appointmentBill/employee", params);
  },

  getProjectList: (params) => {
    return API.POST("api/appointment/findCategoryAndProject", params);
  },

  excelappointmentBill: (params) => {
    return API.exportExcel("api/appointmentBill/excel", params);
  },

  appointmentBill_process: (params) => {
    return API.POST("api/appointmentBill/process", params);
  },
  
  employee_all: (params) => {
    return API.POST("api/employee/all", params);
  },
  /*  */
  appointmentBill_getCustomerAppointmentNumber: (params) => {
    return API.POST("api/appointmentBill/getCustomerAppointmentNumber", params);
  },
  /*  */
  appointmentBill_getCustomerAppointmentAll: (params) => {
    return API.POST("api/appointmentBill/getCustomerAppointmentAll", params);
  },

   
};
