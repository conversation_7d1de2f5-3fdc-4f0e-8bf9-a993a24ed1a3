# 开单界面回车新增客户功能实现

## 功能概述
在开单界面的客户搜索框中，用户可以通过按回车键来新增客户，并且确保信息来源字段为必填项。

## 修改内容

### 1. 开单页面 (src/views/iBeauty/Order/create.vue)

#### 添加回车事件监听
在客户搜索的 `el-autocomplete` 组件中添加了回车键事件监听：
```html
<el-autocomplete
  @keyup.enter.native="handleEnterKey"
  <!-- 其他属性... -->
>
```

#### 新增 handleEnterKey 方法
```javascript
// 处理回车键事件
handleEnterKey() {
  var that = this;
  // 如果已经选择了客户，不处理回车事件
  if (that.customerID != null) {
    return;
  }
  // 如果输入框为空，不处理
  if (!that.customerName || that.customerName.trim() === '') {
    return;
  }
  // 触发新增客户
  if (/^1[3456789]\d{9}$/.test(that.customerName)) {
    that.addCustomerPhoneNumber = that.customerName;
  }
  that.addNewCustomer();
}
```

#### 修改新增客户组件调用
传递手机号参数给新增客户组件：
```html
<add-customer 
  title="新增客户" 
  :visible.sync="isAddCustom" 
  :customerPhoneNumber="addCustomerPhoneNumber" 
  @addCustomerSuccess="addCustomerSuccess"
></add-customer>
```

#### 修改 addCustomerSuccess 方法
在新增客户成功后清空手机号缓存：
```javascript
addCustomerSuccess(info) {
  // ... 其他逻辑
  that.addCustomerPhoneNumber = ''; // 清空新增客户时的手机号
  that.customerChange();
}
```

### 2. 新增客户组件 (src/views/CRM/Customer/Components/CustomerDetail/addCustomer.vue)

#### 确保信息来源必填
将信息来源字段的验证规则从动态的 `getIsRequired('CustomerSourceID')` 改为固定的 `rules.CustomerSourceID`：
```html
<el-form-item label="信息来源" prop="CustomerSourceID" :rules="rules.CustomerSourceID">
  <el-cascader
    v-model="ruleForm.CustomerSourceID"
    :options="customerSource"
    placeholder="请选择信息来源"
    <!-- 其他属性... -->
  ></el-cascader>
</el-form-item>
```

在 rules 中，CustomerSourceID 已经设置为必填：
```javascript
rules: {
  CustomerSourceID: [{ required: true, message: "请选择会员信息来源", trigger: ["blur", "change"] }],
  // ... 其他规则
}
```

## 功能流程

1. **用户输入**：用户在开单页面的客户搜索框中输入客户信息
2. **按回车键**：用户按下回车键触发 `handleEnterKey` 方法
3. **验证输入**：系统检查是否已选择客户和输入是否为空
4. **手机号识别**：如果输入的是手机号格式，自动预填充到新增客户表单
5. **打开新增窗口**：调用 `addNewCustomer` 方法打开新增客户弹窗
6. **必填验证**：信息来源字段为必填项，用户必须选择才能保存
7. **保存成功**：新增客户成功后，自动选中该客户并清空手机号缓存

## 测试步骤

1. **基本回车功能测试**：
   - 打开开单页面
   - 在客户搜索框中输入不存在的客户名称
   - 按回车键
   - 验证是否弹出新增客户窗口

2. **手机号预填充测试**：
   - 在客户搜索框中输入手机号格式的内容（如：13812345678）
   - 按回车键
   - 验证新增客户窗口中手机号字段是否自动填充

3. **信息来源必填测试**：
   - 在新增客户窗口中填写其他必填信息
   - 不选择信息来源，直接点击保存
   - 验证是否显示"请选择会员信息来源"的错误提示
   - 选择信息来源后再次保存，验证是否能正常保存

4. **完整流程测试**：
   - 输入手机号按回车 → 填写客户信息 → 选择信息来源 → 保存
   - 验证客户是否成功创建并自动选中

## 注意事项

1. 只有在未选择客户且输入框不为空的情况下，回车键才会触发新增客户功能
2. 手机号格式验证使用正则表达式：`/^1[3456789]\d{9}$/`
3. 信息来源字段现在强制必填，不受动态配置影响
4. 新增客户成功后会自动清空手机号缓存，避免影响下次操作
