<template>
  <div class="saleProjectEntityPerformanceScheme content_body" v-loading="loading">
    <!-- 搜索 -->
    <div class="nav_header">
      <el-row>
        <el-col :span="20">
          <el-form :inline="true" size="small" @submit.native.prevent>
            <el-form-item label="组织单位">
              <el-input
                @clear="handleSearch"
                v-model="Name"
                placeholder="输入组织单位名称搜索"
                clearable
                @keyup.enter.native="handleSearch"
              ></el-input>
            </el-form-item>
            <el-form-item>
              <el-button
                type="primary"
                size="small"
                @click="handleSearch"
                v-prevent-click
                >搜索</el-button
              >
            </el-form-item>
          </el-form>
        </el-col>
        <el-col :span="4" class="text_right">
          <el-button
            type="primary"
            size="small"
            @click="showAddDialog"
            v-prevent-click
            >新增</el-button
          >
        </el-col>
      </el-row>
    </div>
    <!-- 表格 -->
    <div>
      <el-table size="small" :data="saleProjectEntityTableData">
        <el-table-column  prop="EntityName"  label="组织单位"></el-table-column>
        <el-table-column label="操作" width="145px">
          <template slot-scope="scope">
            <el-button
              type="primary"
              size="small"
              @click="showEditDialog(scope.row)"
              v-prevent-click
              >编辑</el-button
            >
            <el-button
              type="danger"
              size="small"
              @click="removeEntityClick(scope.row)"
              v-prevent-click
              v-if="isDelete"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <div class="page pad_10 text_right">
        <div class="text_right" v-if="paginations.total > 0">
          <el-pagination
            background
            @current-change="handleCurrentChange"
            :current-page.sync="paginations.page"
            :page-size="paginations.page_size"
            :layout="paginations.layout"
            :total="paginations.total"
          ></el-pagination>
        </div>
      </div>
   </div>
   <!-- 新增弹窗 -->
   <el-dialog
      title="新增项目销售门店业绩方案"
      :visible.sync="dialogVisible"
      width="30%"
      custom-class="custom-dialog-add"
    >
      <div>
        <el-form
          :model="ruleForm"
          :rules="rules"
          ref="ruleForm"
          label-width="120px"
          size="small"
        >
          <el-form-item label="组织单位" prop="EntityID">
            <span slot="label">
              适用组织
              <el-popover placement="top-start" width="200" trigger="hover">
                <p>适用于同级所有节点，则只需选择父节点。</p>
                <p>比如：适用于所有节点，只需选择“顶级/第一个”节点。</p>
                <el-button
                  type="text"
                  style="color: #dcdfe6"
                  icon="el-icon-info"
                  slot="reference"
                ></el-button>
              </el-popover>
            </span>
            <treeselect
              v-model="ruleForm.EntityID"
              :options="entityList"
              :normalizer="normalizer"
              clearValueText
              noResultsText="无匹配数据"
              placeholder="选择所属部门"
            />
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="dialogVisible = false" v-prevent-click
          >取 消</el-button
        >
        <el-button
          type="primary"
          size="small"
          :loading="modalLoading"
          v-prevent-click
          @click="submitProjectEntityPerformanceClick"
          >保存</el-button
        >
      </div>
   </el-dialog>
   <!-- 编辑弹窗 -->
   <el-dialog :visible.sync="dialogEdit" custom-class="custom-dialog-edit" width="60%">
      <div slot="title">{{ entityName }} - 项目分类销售门店业绩方案</div>
      <el-table
        size="small"
        :data="saleProjectEntityCategoryTableData"
        row-key="CategoryID"
        :tree-props="{ children: 'Child', hasChildren: 'hasChild' }"
        :row-class-name="tableRowClassName"
        max-height="500px"
      >
        <el-table-column
          prop="CategoryName"
          label="项目分类"
          min-width="150px"
          fixed
        ></el-table-column>
        <el-table-column label="现金比例" min-width="105px">
          <template slot-scope="scope">
            <el-input
              size="mini"
              v-model="scope.row.PayRate"
              v-input-fixed="2"
              type="number"
              class="input_type"
              @input="royaltyRateChange(1, scope.row)"
            >
              <template slot="append">%</template>
            </el-input>
          </template>
        </el-table-column>
        <el-table-column label="卡抵扣比例" min-width="105px">
          <template slot-scope="scope">
            <el-input
              size="mini"
              v-model="scope.row.SavingCardRate"
              v-input-fixed="2"
              type="number"
              class="input_type"
              @input="royaltyRateChange(2, scope.row)"
            >
              <template slot="append">%</template>
            </el-input>
          </template>
        </el-table-column>
        <el-table-column label="赠送卡扣比例" min-width="105px">
          <template slot-scope="scope">
            <el-input
              size="mini"
              v-model="scope.row.SavingCardLargessRate"
              v-input-fixed="2"
              type="number"
              class="input_type"
              @input="royaltyRateChange(3, scope.row)"
            >
              <template slot="append">%</template>
            </el-input>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="115px">
          <template slot-scope="scope">
            <el-button
              type="primary"
              size="mini"
              @click="projectEntityPerformance(scope.row)"
              v-if="!scope.row.isProjectEntityPerformance && !scope.row.isEntity"
              >项目业绩</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="dialogEdit = false" v-prevent-click
          >取 消</el-button
        >
        <el-button
          type="primary"
          size="small"
          :loading="modalLoading"
          v-prevent-click
          @click="submitProjectEntityCategoryClick"
          >保存</el-button
        >
      </div>
   </el-dialog>
    <!--项目业绩弹窗-->
   <el-dialog :visible.sync="dialogProjectEntity" width="40%" custom-class="custom-dialog-edit_Project">
      <div slot="title">
        {{ entityName }} - {{ categoryName }} - 项目销售门店业绩方案
      </div>
      <div>
        <el-form :inline="true" size="small" @submit.native.prevent>
          <el-form-item>
            <el-input
              v-model="SearchKey"
              placeholder="输入项目名称搜索"
              prefix-icon="el-icon-search"
              clearable
            ></el-input>
          </el-form-item>
        </el-form>
        <el-table
          :data="
            projectEntityRoyaltyList.filter(
              (data) =>
                !SearchKey ||
                data.GoodName.toLowerCase().includes(SearchKey.toLowerCase())
            )
          "
          row-key="GoodID"
          size="small"
          max-height="500px"
        >
          <el-table-column
            prop="GoodName"
            label="项目名称"
            min-width="150px"
            fixed
          ></el-table-column>
          <el-table-column label="现金比例" min-width="105px">
            <template slot-scope="scope">
              <el-input
                size="mini"
                v-model="scope.row.PayRate"
                type="number"
                v-input-fixed="2"
                class="input_type"
                @input="royaltyRateChange(1, scope.row)"
              >
                <template slot="append">%</template>
              </el-input>
            </template>
          </el-table-column>
          <el-table-column label="卡抵扣比例" min-width="105px">
            <template slot-scope="scope">
              <el-input
                size="mini"
                v-model="scope.row.SavingCardRate"
                v-input-fixed="2"
                class="input_type"
                type="number"
                @input="royaltyRateChange(2, scope.row)"
              >
                <template slot="append">%</template>
              </el-input>
            </template>
          </el-table-column>
          <el-table-column label="赠送卡扣比例" min-width="105px">
            <template slot-scope="scope">
              <el-input
                size="mini"
                v-model="scope.row.SavingCardLargessRate"
                v-input-fixed="2"
                class="input_type"
                type="number"
                @input="royaltyRateChange(3, scope.row)"
              >
                <template slot="append">%</template>
              </el-input>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="dialogProjectEntity = false" v-prevent-click
          >取 消</el-button
        >
        <el-button
          type="primary"
          size="small"
          :loading="modalLoading"
          v-prevent-click
          @click="updateProjectEntityPerformance"
          >保存</el-button
        >
      </div>
   </el-dialog>
  </div>
</template>

<script>

import API from "@/api/iBeauty/EntityPerformance/saleProjectEntityPerformanceScheme"
import APIEntity from "@/api/KHS/Entity/entity";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
var Enumerable = require("linq");

export default {
 name: 'saleProjectEntityPerformanceScheme',

beforeRouteEnter(to, from, next) {
 next((vm) => {
     vm.isDelete = vm.$permission.permission(
      to.meta.Permission,
      "KHS-EntityPerformance-SaleProjectEntityPerformanceScheme-Delete"
    );
  });
},
  props:{},
  /**  引入的组件  */
  components: {Treeselect},
  /**  Vue 实例的数据对象**/
  data() {
    return {
     isDelete: false,
     modalLoading: false,
     loading: false,
     dialogVisible: false,
     dialogEdit: false,
     dialogProjectEntity: false,
     Name: '', // 搜索条件
     EntityID: "", //  当前的门店ID
     entityName: "",
     categoryName: "",
     projectCategoryID: "",
     SearchKey: "",  // 项目搜索
     saleProjectEntityTableData: [], //表格数据
     saleProjectEntityCategoryTableData: [], //编辑弹窗表格数据
     projectEntityRoyaltyList: [], //项目弹窗表格数据
     entityList: [], //门店数据
     ruleForm: {
        EntityID: null,
        },
        rules: {
        EntityID: [
          { required: true, message: "请选择组织", trigger: "change" },
         ],
        },
     //需要给分页组件传的信息
        paginations: {
        page: 1, // 当前位于哪页
        total: 0, // 总数
        page_size: 10, // 1页显示多少条
        layout: "total, prev, pager, next,jumper", // 翻页属性
        },
    }
  },
   /**计算属性  */
  computed: {
  },
  /**  方法集合  */
  methods: {
    /* 数据显示 */
    handleSearch(){
      let that = this
      that.paginations.page = 1;
      that.getProjectEntityPerformanceScheme()
    },
    /* 上下分页 */
    handleCurrentChange(page) {
      let that = this;
      that.paginations.page = page;
      that.getProjectEntityPerformanceScheme()
    },
    /* 新增 */
    showAddDialog(){
      let that = this
      that.ruleForm = {
        entity: null,
      };
      that.dialogVisible = true;
    },
    /* 编辑 */
    showEditDialog(row){
      let that = this
      that.entityName = row.EntityName;
      that.EntityID = row.EntityID;
      that.getProjectCategoryEntityPerformance()
    },
    /* 新增保存 */
    submitProjectEntityPerformanceClick(){
      let that = this
      that.$refs.ruleForm.validate((valid) => {
        if (valid) {
          that.modalLoading = true;
          var params = {
            EntityID: that.ruleForm.EntityID,
          };
          API.createProjectEntityPerformanceScheme(params)
            .then((res) => {
              if (res.StateCode == 200) {
                that.$message.success("新增成功");
                that.dialogVisible = false;
                that.getProjectEntityPerformanceScheme();
              } else {
                that.$message.error({
                  message: res.Message,
                  duration: 2000,
                });
              }
            })
            .finally(function () {
              that.modalLoading = false;
            });
        }
      });
    },
    /* 获取门店项目销售业绩方案列表 */
     getProjectEntityPerformanceScheme: function () {
      var that = this;
      that.loading = true;
      var params = {
        PageNum: that.paginations.page,
        Name: that.Name,
      };
      API. getProjectEntityPerformanceScheme(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.saleProjectEntityTableData = res.List;
            that.paginations.total = res.Total;
            that.paginations.page_size = res.PageSize;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },
    /* 获取分类项目业绩 */
    getProjectCategoryEntityPerformance: function () {
      var that = this;
      that.loading = true;
      var params = {
        EntityID: that.EntityID,
      };
      API.getProjectCategoryEntityPerformance(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.dialogEdit = true
            var data = {
              EntityID: res.Data.EntityID,
              CategoryID: "0" + res.Data.EntityID,
              CategoryName: "所有项目",
              PayRate: res.Data.PayRate,
              SavingCardLargessRate: res.Data.SavingCardLargessRate,
              SavingCardRate: res.Data.SavingCardRate,
              isEntity: true,
              isProjectEntityPerformance: false,
            };
            var Category = Enumerable.from(res.Data.Category)
              .select((val) => ({
                CategoryID: val.CategoryID,
                CategoryName: val.CategoryName,
                PayRate: val.PayRate,
                SavingCardLargessRate: val.SavingCardLargessRate,
                SavingCardRate: val.SavingCardRate,
                isProjectEntityPerformance: true,
                Child: val.Child,
              }))
              .toArray();
            that.saleProjectEntityCategoryTableData = Object.assign([], Category);
            that.saleProjectEntityCategoryTableData.unshift(data);

          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },
    /* 项目分类销售门店业绩方案保存 */
    submitProjectEntityCategoryClick(){
      let that = this;
      let Category = that.saleProjectEntityCategoryTableData;
      Category = Enumerable.from(Category)
        .where(function (i) {
          return !i.isEntity;
        })
        .toArray();

      Category.forEach(function (item) {
        item.Child = Enumerable.from(item.Child)
          .where(function (i) {
            return (
              (i.PayRate !== "" && i.PayRate !== null) ||
              (i.SavingCardRate !== "" && i.SavingCardRate !== null) ||
              (i.SavingCardLargessRate !== "" &&
                i.SavingCardLargessRate !== null)
            );
          })
          .toArray();
      });

      Category = Enumerable.from(Category)
        .where(function (i) {
          return (
            (i.PayRate !== "" && i.PayRate !== null) ||
            (i.SavingCardRate !== "" && i.SavingCardRate !== null) ||
            (i.SavingCardLargessRate !== "" && i.SavingCardLargessRate !== null)  ||
            i.Child.length > 0
          );
        })
        .toArray();
      var params = {
        EntityID: that.saleProjectEntityCategoryTableData[0].EntityID,
        PayRate: that.saleProjectEntityCategoryTableData[0].PayRate,
        SavingCardRate: that.saleProjectEntityCategoryTableData[0].SavingCardRate,
        SavingCardLargessRate:
          that.saleProjectEntityCategoryTableData[0].SavingCardLargessRate,
        Category: Category,
      };

      that.modalLoading = true;
      API.updateProjectCategoryEntityPerformance(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.$message.success("业绩设置成功");
            that.dialogEdit = false;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.modalLoading = false;
        });
    },
    /* 项目业绩设置 */
    projectEntityPerformance: function (row) {
      var that = this;
      that.projectCategoryID = row.CategoryID;
      that.categoryName = row.CategoryName;
      that.getProjectEntityPerformance()
    },
    /* 获取项目业绩 */
     getProjectEntityPerformance: function () {
      var that = this;
      // that.loading = true;
      var params = {
        EntityID: that.EntityID,
        CategoryID: that.projectCategoryID,
      };
      API.getProjectEntityPerformance(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.dialogProjectEntity = true;
            that.projectEntityRoyaltyList = res.Data;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },
    /* 项目业绩保存 */
    updateProjectEntityPerformance: function (){
      let that = this;
      let projectRoyaltyList = Enumerable.from(that.projectEntityRoyaltyList)
        .where(function (i) {
          return (
            (i.PayRate !== "" && i.PayRate !== null) ||
            (i.SavingCardRate !== "" && i.SavingCardRate !== null) ||
            (i.SavingCardLargessRate !== "" &&
              i.SavingCardLargessRate !== null)
          );
        })
        .toArray();
      that.modalLoading = true;
      var params = {
        EntityID: that.EntityID,
        Good: projectRoyaltyList,
        CategoryID: that.projectCategoryID,
      };
      API.updateProjectEntityPerformance(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.$message.success("业绩设置成功");
            that.dialogProjectEntity = false;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.modalLoading = false;
        });
    },
    /* 删除项目销售业绩方案 */
    removeEntityClick: function (row) {
        let that = this;
      that
        .$confirm("此操作将永久删除该记录, 是否继续?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
        .then(() => {
          that.loading = false;
          var params = {
            EntityID: row.EntityID,
          };
          API.deleteProjectEntityPerformanceScheme(params)
            .then((res) => {
              if (res.StateCode == 200) {
                that.$message.success("删除成功");
                that.getProjectEntityPerformanceScheme();
              } else {
                that.$message.error({
                  message: res.Message,
                  duration: 2000,
                });
              }
            })
            .finally(function () {
              that.loading = false;
            });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    /* 所属单位 */
    entityData: function () {
      var that = this;
      APIEntity.getEntityAll()
        .then((res) => {
          if (res.StateCode == 200) {
            that.entityList = res.Data;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },
    /* 约束业绩比例 */
    royaltyRateChange: function (index, row) {
      if (index == 1) {
        if (row.PayRate > 100) {
          row.PayRate = 100;
        }
      } else if (index == 2) {
        if (row.SavingCardRate > 100) {
          row.SavingCardRate = 100;
        }
      } else if (index == 3) {
        if (row.SavingCardLargessRate > 100) {
          row.SavingCardLargessRate = 100;
        }
      }
    },
    /* 高亮第一级表格 */
    tableRowClassName({ rowIndex }) {
      if (rowIndex === 0) {
        return "info-row";
      }
      return "";
    },
    /* 树形结构数据转换 */
    normalizer(node) {
      return {
        id: node.ID,
        label: node.EntityName,
        children: node.Child,
      };
    },
  },
  /** 监听数据变化   */
  watch: {},
  /** 创建实例之前执行函数   */
  beforeCreate() {},
  /**  实例创建完成之后  */
  created() {},
  /**  在挂载开始之前被调用  */
  beforeMount() {},
  /**  实例被挂载后调用  */
  mounted() {
    let that = this
    this.isDelete = this.$permission.permission(
      this.$route.meta.Permission,
       "KHS-EntityPerformance-SaleProjectEntityPerformanceScheme-Delete"
    );
    that.handleSearch();
    that.entityData();
  },
  /**  数据更新 之前 调用  */
  beforeUpdate() {},
  /** 数据更新 完成 调用   */
  updated() {},
  /**  实例销毁之前调用  */
  beforeDestroy() {},
  /**  实例销毁后调用  */
  destroyed() {},
}
</script>

<style lang="scss">

.saleProjectEntityPerformanceScheme{
   .input_type {
    .el-input-group__append {
      padding: 0 10px;
    }
  }
  .el-table .info-row {
    background: #c0c4cc;
  }
  .el-input__inner {
    padding-right: 0;
  }
  .custom-dialog-add{
    min-width: 500px;
  }
  .custom-dialog-edit{
    min-width: 950px;
  }
  .custom-dialog-edit_Project{
    min-width: 850px;
  }
 }
</style>
