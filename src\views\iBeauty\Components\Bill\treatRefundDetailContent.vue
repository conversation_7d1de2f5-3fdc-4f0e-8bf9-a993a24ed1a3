<!-- /**  消耗 退单内容 */ --> 
<template>
  <section class="treatRefundDetailContent">
    <div>
      <!-- 订单信息 -->
      <div>
        <div class="tip">退消耗信息</div>
        <el-form label-width="100px" class="treatInfoClass" size="small">
          <el-row>
            <el-col :span="8">
              <el-form-item label="订单编号:">{{ treatInfo.ID }}</el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="订单类型:">{{ treatInfo.BillType == "10" ? "消耗单" : "消耗退单" }}</el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="订单状态:">{{ getBillState(treatInfo.BillStatus) }} </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="8">
              <el-form-item label="顾客信息:">{{ treatInfo.Name }} <span v-if="treatInfo.PhoneNumber != null">({{
                treatInfo.PhoneNumber | hidephone }})</span>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="操作人:">{{ treatInfo.EmployeeName }}</el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="退消耗金额:">￥{{ treatInfo.Amount | toFixed | NumFormat }}</el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="8">
              <el-form-item label="下单时间:">
                <span class="marrt_5">{{ treatInfo.BillDate | dateFormat("YYYY-MM-DD HH:mm") }}</span>
                <el-button v-if="isModifyBillDate && !limitSealingAccount(treatInfo.BillDate, ModifyBillDateRestriction)"
                  type="text" @click="ModifyBillDateClick">修改</el-button>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="录单时间:">{{ treatInfo.CreatedOn | dateFormat("YYYY-MM-DD HH:mm") }}</el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="订单备注:">
                <span v-if="treatInfo.Remark" class="marrt_5">{{ treatInfo.Remark }} </span>
                <el-button type="text" @click="upRemarkDialog"> 修改备注</el-button>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row> </el-row>
        </el-form>
      </div>
      <div>
        <div class="tip">退消耗明细</div>
        <!-- 项目 -->
        <div v-if="treatInfo.Project != undefined && treatInfo.Project.length > 0">
          <el-row class="tipback_col pad_10">
            <el-col :span="7">项目</el-col>
            <el-col :span="7">退消耗数量</el-col>
            <el-col :span="10">退消耗金额</el-col>
          </el-row>
          <el-row v-for="(item, index) in treatInfo.Project" :key="index + 'x1'"
            class="text_left border_right border_left">
            <el-col :span="24" class="pad_10 border_bottom">
              <el-col :span="7">
                <div>
                  {{ item.ProjectName }}
                  <span v-if="item.Alias">({{ item.Alias }})</span>
                  <el-tag v-if="item.IsLargess" size="mini" type="danger" class="marlt_10">赠</el-tag>
                </div>
              <!-- <div class="color_red martp_5 font_12">
                  ￥{{ item.Price | NumFormat}}
                                    </div> -->
              </el-col>
              <el-col :span="7">x {{ item.Quantity }}</el-col>
              <el-col :span="10">
                <div>¥ {{ item.TotalAmount | toFixed | NumFormat }}</div>
                <div class="dis_flex martp_5">
                  <div class="color_green font_12" v-if="item.PayAmount > 0">现金金额： {{ item.PayAmount | toFixed | NumFormat
                  }}</div>
                  <div class="color_green font_12" v-if="item.CardDeductionAmount > 0"
                    :class="item.PayAmount != 0 ? 'marlt_15' : ''">
                    卡抵扣金额： {{ item.CardDeductionAmount | toFixed | NumFormat }}
                  </div>

                  <div class="color_red font_12" v-if="item.LargessCardDeductionAmount > 0"
                    :class="item.CardDeductionAmount != 0 ? 'marlt_15' : ''">
                    赠送卡抵扣金额： {{ item.LargessCardDeductionAmount | toFixed | NumFormat }}
                  </div>

                  <div class="color_red font_12" v-if="item.LargessAmount > 0"
                    :class="item.LargessCardDeductionAmount != 0 ? 'marlt_15' : ''">
                    赠送金额：¥ {{ item.LargessAmount | toFixed | NumFormat }}
                  </div>
                </div>
              </el-col>
            </el-col>
            <el-col :span="24" v-if="item.TreatBillHandler.length > 0" class="pad_10 padtp_5 padbm_5 border_bottom">
              <el-row v-for="(handler, pIndex) in item.TreatBillHandler" :key="pIndex + 'h5'">
                <el-col :span="2">
                  <el-form @submit.native.prevent class="saleHandler" :inline="true" size="mini" label-position="left">
                    <el-form-item :label="`${handler.TreatHandlerName}`"></el-form-item>
                  </el-form>
                </el-col>
                <el-col :span="22">
                  <el-form @submit.native.prevent class="saleHandler" :inline="true" size="mini">
                    <el-form-item v-for="(employee, handleIndex) in handler.Employee" :key="handleIndex"
                      :label="`${employee.EmployeeName}:`">{{ employee.Scale.toFixed(2) }}%
                    </el-form-item>
                  </el-form>
                </el-col>
              </el-row>
            </el-col>
          </el-row>
        </div>
        <!-- 储值卡 -->
        <div v-if="treatInfo.SavingCard != undefined && treatInfo.SavingCard.length > 0">
          <el-row class="tipback_col pad_10">
            <el-col :span="7">储值卡</el-col>
            <el-col :span="7">退消耗数量</el-col>
            <el-col :span="10">退消耗金额</el-col>
          </el-row>
          <el-row v-for="(item, index) in treatInfo.SavingCard" :key="index + 'x4'"
            class="border_right item border_left text_left">
            <el-row style="background-color: #f5f7fa" class="pad_10 border_top border_bottom">
              <div>
                {{ item.SavingCardName }} <span v-if="item.Alias">({{ item.Alias }})</span>
              </div>
            </el-row>
            <el-row v-for="(childItem, childIndex) in item.Project" :key="childIndex + 'c3'">
              <el-col :span="24" class="pad_10 border_bottom">
                <el-col :span="24">
                  <el-col :span="7">
                    <div>
                      {{ childItem.ProjectName }} <span v-if="childItem.Alias">({{ childItem.Alias }})</span>
                      <el-tag v-if="item.IsLargess" size="mini" type="danger" class="marlt_10">赠</el-tag>
                    </div>
                  </el-col>
                  <el-col :span="7">x {{ childItem.Quantity }}</el-col>
                  <el-col :span="10">
                    <div>¥ {{ childItem.TotalAmount | toFixed | NumFormat }}</div>
                    <div class="dis_flex martp_5">
                      <!-- <div class="color_green font_12 " v-if="childItem.PayAmount > 0">现金金额： {{ childItem.PayAmount | NumFormat }}</div> -->
                      <div class="color_green font_12" v-if="childItem.CardDeductionAmount > 0">
                        卡抵扣金额： {{ childItem.CardDeductionAmount | toFixed | NumFormat }}
                      </div>

                      <div class="color_red font_12" v-if="childItem.LargessCardDeductionAmount > 0"
                        :class="childItem.CardDeductionAmount != 0 ? 'marlt_15' : ''">
                        赠送卡抵扣金额： {{ childItem.LargessCardDeductionAmount | toFixed | NumFormat }}
                      </div>

                      <!-- <div class="color_red font_12" v-if="childItem.LargessAmount > 0" :class="childItem.LargessCardDeductionAmount != 0 ? 'marlt_15' : ''">赠送金额：¥ {{ childItem.LargessAmount | NumFormat }}</div> -->
                    </div>
                  </el-col>
                </el-col>
              </el-col>
              <el-col :span="24" v-if="childItem.TreatBillHandler.length > 0"
                class="pad_10 padtp_5 padbm_5 border_bottom">
                <el-row v-for="(handler, pIndex) in childItem.TreatBillHandler" :key="pIndex + 'h5'">
                  <el-col :span="2">
                    <el-form @submit.native.prevent class="saleHandler" :inline="true" size="mini" label-position="left">
                      <el-form-item :label="`${handler.TreatHandlerName}`"></el-form-item>
                    </el-form>
                  </el-col>
                  <el-col :span="22">
                    <el-form @submit.native.prevent class="saleHandler" :inline="true" size="mini">
                      <el-form-item v-for="(employee, handleIndex) in handler.Employee" :key="handleIndex"
                        :label="`${employee.EmployeeName}:`">{{ employee.Scale.toFixed(2) }}%
                      </el-form-item>
                    </el-form>
                  </el-col>
                </el-row>
              </el-col>
            </el-row>
          </el-row>
        </div>
        <!-- 时效卡 -->
        <div v-if="treatInfo.TimeCard != undefined && treatInfo.TimeCard.length > 0">
          <el-row class="tipback_col pad_10">
            <el-col :span="7">时效卡</el-col>
            <el-col :span="7">退消耗数量</el-col>
            <el-col :span="10">退消耗金额</el-col>
          </el-row>
          <el-row v-for="(item, index) in treatInfo.TimeCard" :key="index + 'x4'" class="border_right item border_left">
            <el-row style="background-color: #f5f7fa" class="pad_10 border_top border_bottom">
              <div>
                {{ item.TimeCardName }} <span v-if="item.Alias">({{ item.Alias }})</span>
                <el-tag v-if="item.IsLargess" size="mini" type="danger" class="marlt_10">赠</el-tag>
              </div>
            </el-row>
            <el-row v-for="(childItem, childIndex) in item.Project" :key="childIndex + 'c3'">
              <el-col :span="24" class="pad_10 border_bottom">
                <el-col :span="7">
                  <div>
                    {{ childItem.ProjectName }}
                    <span v-if="childItem.Alias">({{ childItem.Alias }})</span>
                    <el-tag v-if="item.IsLargess" size="mini" type="danger" class="marlt_10">赠</el-tag>
                  </div>
                <!-- <div class="color_red martp_5 font_12">
                    ¥ {{ childItem.Price | NumFormat }}
                                      </div> -->
                </el-col>
                <el-col :span="7">x {{ childItem.Quantity }}</el-col>
                <el-col :span="10">
                  <div>¥ {{ childItem.TotalAmount | toFixed | NumFormat }}</div>
                  <div class="dis_flex martp_5">
                    <div class="color_green font_12" v-if="childItem.PayAmount > 0">现金金额： {{ childItem.PayAmount | toFixed
                      | NumFormat }}</div>
                    <div class="color_green font_12" v-if="childItem.CardDeductionAmount > 0"
                      :class="childItem.PayAmount != 0 ? 'marlt_15' : ''">
                      卡抵扣金额： {{ childItem.CardDeductionAmount | toFixed | NumFormat }}
                    </div>

                    <div class="color_red font_12" v-if="childItem.LargessCardDeductionAmount > 0"
                      :class="childItem.CardDeductionAmount != 0 ? 'marlt_15' : ''">
                      赠送卡抵扣金额： {{ childItem.LargessCardDeductionAmount | toFixed | NumFormat }}
                    </div>

                    <div class="color_red font_12" v-if="childItem.LargessAmount > 0"
                      :class="childItem.LargessCardDeductionAmount != 0 ? 'marlt_15' : ''">
                      赠送金额：¥ {{ childItem.LargessAmount | toFixed | NumFormat }}
                    </div>
                  </div>
                </el-col>
              </el-col>
              <el-col :span="24" v-if="childItem.TreatBillHandler.length > 0"
                class="pad_10 padtp_5 padbm_5 border_bottom">
                <el-row v-for="(handler, pIndex) in childItem.TreatBillHandler" :key="pIndex + 'h5'">
                  <el-col :span="2">
                    <el-form @submit.native.prevent class="saleHandler" :inline="true" size="mini" label-position="left">
                      <el-form-item :label="`${handler.TreatHandlerName}`"></el-form-item>
                    </el-form>
                  </el-col>
                  <el-col :span="22">
                    <el-form @submit.native.prevent class="saleHandler" :inline="true" size="mini">
                      <el-form-item v-for="(employee, handleIndex) in handler.Employee" :key="handleIndex"
                        :label="`${employee.EmployeeName}:`">{{ employee.Scale.toFixed(2) }}%
                      </el-form-item>
                    </el-form>
                  </el-col>
                </el-row>
              </el-col>
            </el-row>
          </el-row>
        </div>
        <!-- 通用次卡 -->
        <div v-if="treatInfo.GeneralCard != undefined && treatInfo.GeneralCard.length > 0">
          <el-row class="tipback_col pad_10">
            <el-col :span="7">通用次卡</el-col>
            <el-col :span="7">退消耗数量</el-col>
            <el-col :span="10">退消耗金额</el-col>
          </el-row>
          <el-row v-for="(item, index) in treatInfo.GeneralCard" :key="index + 'x3'"
            class="border_right item border_left">
            <el-row style="background-color: #f5f7fa" class="pad_10 border_top border_bottom">
              <div>
                {{ item.GeneralCardName }}<span v-if="item.Alias">({{ item.Alias }})</span>
                <el-tag v-if="item.IsLargess" size="mini" type="danger" class="marlt_10">赠 </el-tag>
              </div>
            </el-row>

            <el-row v-for="(childItem, childIndex) in item.Project" :key="childIndex + 'c3'">
              <el-col :span="24" class="pad_10 border_bottom">
                <el-col :span="7">
                  <div>
                    {{ childItem.ProjectName }}
                    <span v-if="childItem.Alias">({{ childItem.Alias }})</span>
                    <el-tag v-if="item.IsLargess" size="mini" type="danger" class="marlt_10">赠 </el-tag>
                  </div>
                </el-col>
                <el-col :span="7">x {{ childItem.Quantity }}</el-col>
                <el-col :span="10">
                  <div>¥ {{ childItem.TotalAmount | toFixed | NumFormat }}</div>
                  <div class="dis_flex martp_5">
                    <div class="color_green font_12" v-if="childItem.PayAmount > 0">现金金额： {{ childItem.PayAmount | toFixed
                      | NumFormat }}</div>
                    <div class="color_green font_12" v-if="childItem.CardDeductionAmount > 0"
                      :class="childItem.PayAmount != 0 ? 'marlt_15' : ''">
                      卡抵扣金额： {{ childItem.CardDeductionAmount | toFixed | NumFormat }}
                    </div>

                    <div class="color_red font_12" v-if="childItem.LargessCardDeductionAmount > 0"
                      :class="childItem.CardDeductionAmount != 0 ? 'marlt_15' : ''">
                      赠送卡抵扣金额： {{ childItem.LargessCardDeductionAmount | toFixed | NumFormat }}
                    </div>

                    <div class="color_red font_12" v-if="childItem.LargessAmount > 0"
                      :class="childItem.LargessCardDeductionAmount != 0 ? 'marlt_15' : ''">
                      赠送金额：¥ {{ childItem.LargessAmount | toFixed | NumFormat }}
                    </div>
                  </div>
                </el-col>
              </el-col>
              <el-col :span="24" v-if="childItem.TreatBillHandler.length > 0"
                class="pad_10 padtp_5 padbm_5 border_bottom">
                <el-row v-for="(handler, pIndex) in childItem.TreatBillHandler" :key="pIndex + 'h5'">
                  <el-col :span="2">
                    <el-form @submit.native.prevent class="saleHandler" :inline="true" size="mini" label-position="left">
                      <el-form-item :label="`${handler.TreatHandlerName}`"></el-form-item>
                    </el-form>
                  </el-col>
                  <el-col :span="22">
                    <el-form @submit.native.prevent class="saleHandler" :inline="true" size="mini">
                      <el-form-item v-for="(employee, handleIndex) in handler.Employee" :key="handleIndex"
                        :label="`${employee.EmployeeName}:`">{{ employee.Scale.toFixed(2) }}%
                      </el-form-item>
                    </el-form>
                  </el-col>
                </el-row>
              </el-col>
            </el-row>
          </el-row>
        </div>
        <!-- 产品 -->
        <div v-if="treatInfo.Product != undefined && treatInfo.Product.length > 0">
          <el-row class="tipback_col pad_10">
            <el-col :span="7">产品</el-col>
            <el-col :span="7">退消耗数量</el-col>
            <el-col :span="10">退消耗金额</el-col>
          </el-row>
          <el-row v-for="(item, index) in treatInfo.Product" :key="index + 'x1'"
            class="text_left border_right border_left">
            <el-col :span="24" class="pad_10 border_bottom">
              <el-col :span="7">
                <div>
                  {{ item.ProductName }}<span v-if="item.Alias">({{ item.Alias }})</span>
                  <el-tag v-if="item.IsLargess" size="mini" type="danger" class="marlt_10">赠 </el-tag>
                </div>
              <!-- <div class="color_red martp_5 font_12">
                  ￥{{ item.Price }}
                                    </div> -->
              </el-col>
              <el-col :span="7">x {{ item.Quantity }}</el-col>
              <el-col :span="10">
                <div>¥ {{ item.TotalAmount | toFixed | NumFormat }}</div>

                <div class="dis_flex martp_5">
                  <div class="color_green font_12" v-if="item.PayAmount > 0">现金金额： {{ item.PayAmount | toFixed | NumFormat
                  }}</div>
                  <div class="color_green font_12" v-if="item.CardDeductionAmount > 0"
                    :class="item.PayAmount != 0 ? 'marlt_15' : ''">
                    卡抵扣金额： {{ item.CardDeductionAmount | toFixed | NumFormat }}
                  </div>

                  <div class="color_red font_12" v-if="item.LargessCardDeductionAmount > 0"
                    :class="item.CardDeductionAmount != 0 ? 'marlt_15' : ''">
                    赠送卡抵扣金额： {{ item.LargessCardDeductionAmount | toFixed | NumFormat }}
                  </div>

                  <div class="color_red font_12" v-if="item.LargessAmount > 0"
                    :class="item.LargessCardDeductionAmount != 0 ? 'marlt_15' : ''">
                    赠送金额：¥ {{ item.LargessAmount | toFixed | NumFormat }}
                  </div>
                </div>
              </el-col>
            </el-col>
            <el-col :span="24" v-if="item.TreatBillHandler.length > 0" class="pad_10 padtp_5 padbm_5 border_bottom">
              <el-row v-for="(handler, pIndex) in item.TreatBillHandler" :key="pIndex + 'h5'">
                <el-col :span="2">
                  <el-form @submit.native.prevent class="saleHandler" :inline="true" size="mini" label-position="left">
                    <el-form-item :label="`${handler.TreatHandlerName}`"></el-form-item>
                  </el-form>
                </el-col>
                <el-col :span="22">
                  <el-form @submit.native.prevent class="saleHandler" :inline="true" size="mini">
                    <el-form-item v-for="(employee, handleIndex) in handler.Employee" :key="handleIndex"
                      :label="`${employee.EmployeeName}:`">{{ employee.Scale.toFixed(2) }}%
                    </el-form-item>
                  </el-form>
                </el-col>
              </el-row>
            </el-col>
          </el-row>
        </div>
        <!-- 套餐卡 -->
        <div v-if="treatInfo.PackageCard != undefined && treatInfo.PackageCard.length > 0">
          <el-row v-for="(item, index) in treatInfo.PackageCard" :key="index + 'x6'"
            class="text_left border_left border_right">
            <el-row class="tipback_col pad_10">
              <el-col :span="8">套餐卡{{ item.PackageCardName }}<span v-if="item.Alias">({{ item.Alias }})</span></el-col>
            </el-row>
            <!-- 项目 -->
            <el-row v-if="item.Project.length > 0">
              <el-row class="row_header_package_detail pad_10">
                <el-col :span="7">套餐卡项目</el-col>
                <el-col :span="7">退消耗数量</el-col>
                <el-col :span="10">退消耗金额</el-col>
              </el-row>
              <el-row v-for="(childItem, childIndex) in item.Project" :key="childIndex + 'c6-1'">
                <el-col :span="24" class="border_bottom pad_10">
                  <el-col :span="7">
                    {{ childItem.ProjectName }}
                    <span v-if="childItem.Alias">({{ childItem.Alias }})</span>
                    <el-tag v-if="childItem.IsLargess" size="mini" type="danger" class="marlt_5">赠</el-tag>
                  </el-col>
                  <el-col :span="7">x {{ childItem.Quantity }}</el-col>
                  <el-col :span="10">
                    <div>¥ {{ childItem.TotalAmount | toFixed | NumFormat }}</div>
                    <div class="dis_flex martp_5">
                      <div class="color_green font_12" v-if="childItem.PayAmount > 0">现金金额： {{ childItem.PayAmount |
                        toFixed | NumFormat }}</div>
                      <div class="color_green font_12" v-if="childItem.CardDeductionAmount > 0"
                        :class="childItem.PayAmount != 0 ? 'marlt_15' : ''">
                        卡抵扣金额： {{ childItem.CardDeductionAmount | toFixed | NumFormat }}
                      </div>

                      <div class="color_red font_12" v-if="childItem.LargessCardDeductionAmount > 0"
                        :class="childItem.CardDeductionAmount != 0 ? 'marlt_15' : ''">
                        赠送卡抵扣金额： {{ childItem.LargessCardDeductionAmount | toFixed | NumFormat }}
                      </div>

                      <div class="color_red font_12" v-if="childItem.LargessAmount > 0"
                        :class="childItem.LargessCardDeductionAmount != 0 ? 'marlt_15' : ''">
                        赠送金额：¥ {{ childItem.LargessAmount | toFixed | NumFormat }}
                      </div>
                    </div>
                  </el-col>
                </el-col>
                <el-col :span="24" v-if="childItem.TreatBillHandler.length > 0"
                  class="pad_10 padtp_5 padbm_5 border_bottom">
                  <el-row v-for="(handler, pIndex) in childItem.TreatBillHandler" :key="pIndex + 'h5'">
                    <el-col :span="2">
                      <el-form class="saleHandler" :inline="true" size="mini" label-position="left"
                        @submit.native.prevent>
                        <el-form-item :label="`${handler.TreatHandlerName}`"></el-form-item>
                      </el-form>
                    </el-col>
                    <el-col :span="22">
                      <el-form @submit.native.prevent class="saleHandler" :inline="true" size="mini">
                        <el-form-item v-for="(employee, handleIndex) in handler.Employee" :key="handleIndex"
                          :label="`${employee.EmployeeName}:`">{{ employee.Scale.toFixed(2) }}%
                        </el-form-item>
                      </el-form>
                    </el-col>
                  </el-row>
                </el-col>
              </el-row>
            </el-row>
            <!--  储值卡 -->
            <div class="border_right border_left" v-if="item.SavingCard.length > 0">
              <el-row v-for="(childItem, childIndex) in item.SavingCard" :key="childIndex + 'c2-3'">
                <el-row :span="24" class="pad_10 row_header_package_detail border_bottom">
                  <el-col :span="7">{{ childItem.SavingCardName }}
                    <span v-if="childItem.Alias">({{ childItem.Alias }})</span>
                    <el-tag v-if="childItem.IsLargess" size="mini" type="danger" class="marlt_5">赠</el-tag>
                    <el-tag size="mini" class="marlt_5">储值卡</el-tag>
                  </el-col>
                  <el-col :span="7">退消耗数量</el-col>
                  <el-col :span="10">退消耗金额</el-col>
                </el-row>
                <el-row v-for="(childItem_1, childIndex_1) in childItem.Project" :key="childIndex_1 + 'c6-3-1'"
                  class="item">
                  <el-col :span="24" class="border_bottom pad_10">
                    <el-col :span="7">{{ childItem_1.ProjectName }}<span v-if="childItem_1.Alias">({{ childItem_1.Alias
                    }})</span></el-col>
                    <el-col :span="7">x {{ childItem_1.Quantity }} </el-col>
                    <el-col :span="10">
                      <div>¥ {{ childItem_1.TotalAmount | toFixed | NumFormat }}</div>

                      <div class="dis_flex martp_5">
                        <!-- <div class="color_green font_12 " v-if="childItem_1.PayAmount > 0">现金金额： {{ childItem_1.PayAmount | NumFormat }}</div> -->
                        <div class="color_green font_12" v-if="childItem_1.CardDeductionAmount > 0">
                          卡抵扣金额： {{ childItem_1.CardDeductionAmount | toFixed | NumFormat }}
                        </div>

                        <div class="color_red font_12" v-if="childItem_1.LargessCardDeductionAmount > 0"
                          :class="childItem_1.CardDeductionAmount != 0 ? 'marlt_15' : ''">
                          赠送卡抵扣金额： {{ childItem_1.LargessCardDeductionAmount | toFixed | NumFormat }}
                        </div>

                        <!-- <div class="color_red font_12" v-if="childItem_1.LargessAmount > 0" :class="childItem_1.LargessCardDeductionAmount != 0 ? 'marlt_15' : ''">赠送金额：¥ {{ childItem_1.LargessAmount | NumFormat }}</div> -->
                      </div>
                    </el-col>
                  </el-col>
                  <el-col :span="24" v-if="childItem_1.TreatBillHandler.length > 0"
                    class="pad_10 padtp_5 padbm_5 border_bottom">
                    <el-row v-for="(handler, pIndex) in childItem_1.TreatBillHandler" :key="pIndex + 'h5'">
                      <el-col :span="2">
                        <el-form @submit.native.prevent class="saleHandler" :inline="true" size="mini"
                          label-position="left">
                          <el-form-item :label="`${handler.TreatHandlerName}`"></el-form-item>
                        </el-form>
                      </el-col>
                      <el-col :span="22">
                        <el-form class="saleHandler" :inline="true" size="mini">
                          <el-form-item @submit.native.prevent v-for="(employee, handleIndex) in handler.Employee"
                            :key="handleIndex" :label="`${employee.EmployeeName}:`">{{ employee.Scale.toFixed(2) }}%
                          </el-form-item>
                        </el-form>
                      </el-col>
                    </el-row>
                  </el-col>
                </el-row>
              </el-row>
            </div>
            <!-- 时效卡 -->
            <div class="border_right border_left" v-if="item.TimeCard.length > 0">
              <el-row v-for="(childItem, childIndex) in item.TimeCard" :key="childIndex + 'c1-3'">
                <el-row :span="24" class="pad_10 row_header_package_detail border_bottom">
                  <el-col :span="7">
                    {{ childItem.TimeCardName }}
                    <span v-if="childItem.Alias">({{ childItem.Alias }})</span>
                    <el-tag v-if="childItem.IsLargess" size="mini" type="danger" class="marlt_5">赠</el-tag>
                    <el-tag class="marlt_5" size="mini">时效卡</el-tag>
                  </el-col>
                  <el-col :span="7">退消耗数量</el-col>
                  <el-col :span="10">退消耗金额</el-col>
                </el-row>
                <el-row v-for="(childItem_1, childIndex_1) in childItem.Project" :key="childIndex_1 + 'c6-3-1'"
                  class="item">
                  <el-col :span="24" class="border_bottom pad_10">
                    <el-col :span="7">
                      {{ childItem_1.ProjectName }}
                      <span v-if="childItem_1.Alias">({{ childItem_1.Alias }})</span>
                      <el-tag v-if="childItem.IsLargess" size="mini" type="danger" class="marlt_5">赠</el-tag>
                    </el-col>
                    <el-col :span="7">x {{ childItem_1.Quantity }} </el-col>
                    <el-col :span="10">
                      <div>¥ {{ childItem_1.TotalAmount | toFixed | NumFormat }}</div>
                      <div class="dis_flex martp_5">
                        <div class="color_green font_12" v-if="childItem_1.PayAmount > 0">现金金额： {{ childItem_1.PayAmount |
                          toFixed | NumFormat }}</div>
                        <div class="color_green font_12" v-if="childItem_1.CardDeductionAmount > 0"
                          :class="childItem_1.PayAmount != 0 ? 'marlt_15' : ''">
                          卡抵扣金额： {{ childItem_1.CardDeductionAmount | toFixed | NumFormat }}
                        </div>

                        <div class="color_red font_12" v-if="childItem_1.LargessCardDeductionAmount > 0"
                          :class="childItem_1.CardDeductionAmount != 0 ? 'marlt_15' : ''">
                          赠送卡抵扣金额： {{ childItem_1.LargessCardDeductionAmount | toFixed | NumFormat }}
                        </div>

                        <div class="color_red font_12" v-if="childItem_1.LargessAmount > 0"
                          :class="childItem_1.LargessCardDeductionAmount != 0 ? 'marlt_15' : ''">
                          赠送金额：¥ {{ childItem_1.LargessAmount | toFixed | NumFormat }}
                        </div>
                      </div>
                    </el-col>
                  </el-col>
                  <el-col :span="24" v-if="childItem_1.TreatBillHandler.length > 0"
                    class="pad_10 padtp_5 padbm_5 border_bottom">
                    <el-row v-for="(handler, pIndex) in childItem_1.TreatBillHandler" :key="pIndex + 'h5'">
                      <el-col :span="2">
                        <el-form @submit.native.prevent class="saleHandler" :inline="true" size="mini"
                          label-position="left">
                          <el-form-item :label="`${handler.TreatHandlerName}`"></el-form-item>
                        </el-form>
                      </el-col>
                      <el-col :span="22">
                        <el-form @submit.native.prevent class="saleHandler" :inline="true" size="mini">
                          <el-form-item v-for="(employee, handleIndex) in handler.Employee" :key="handleIndex"
                            :label="`${employee.EmployeeName}:`">{{ employee.Scale.toFixed(2) }}%
                          </el-form-item>
                        </el-form>
                      </el-col>
                    </el-row>
                  </el-col>
                </el-row>
              </el-row>
            </div>
            <!-- 通用次卡 -->
            <div class="border_right border_left" v-if="item.GeneralCard.length > 0">
              <el-row v-for="(childItem, childIndex) in item.GeneralCard" :key="childIndex + 'c6-3'" class="item">
                <el-row :span="24" class="pad_10 row_header_package_detail border_bottom">
                  <el-col :span="7">
                    {{ childItem.GeneralCardName }}
                    <span v-if="childItem.Alias">({{ childItem.Alias }})</span>
                    <el-tag v-if="childItem.IsLargess" size="mini" type="danger" class="marlt_5"> 赠</el-tag>
                    <el-tag class="marlt_5" size="mini">通用次卡</el-tag>
                  </el-col>
                  <el-col :span="7">退消耗数量</el-col>
                  <el-col :span="10">退消耗金额</el-col>
                </el-row>
                <el-row v-for="(childItem_1, childIndex_1) in childItem.Project" :key="childIndex_1 + 'c6-3-1'">
                  <el-col :span="24" class="border_bottom pad_10">
                    <el-col :span="7">
                      {{ childItem_1.ProjectName }}<span v-if="childItem_1.Alias">({{ childItem_1.Alias }})</span>
                      <el-tag v-if="childItem.IsLargess" size="mini" type="danger" class="marlt_5">赠</el-tag>
                    </el-col>
                    <el-col :span="7">x {{ childItem_1.Quantity }} </el-col>
                    <el-col :span="10">
                      <div>¥ {{ childItem_1.TotalAmount | toFixed | NumFormat }}</div>
                      <div class="dis_flex martp_5">
                        <div class="color_green font_12" v-if="childItem_1.PayAmount > 0">现金金额： {{ childItem_1.PayAmount |
                          toFixed | NumFormat }}</div>
                        <div class="color_green font_12" v-if="childItem_1.CardDeductionAmount > 0"
                          :class="childItem_1.PayAmount != 0 ? 'marlt_15' : ''">
                          卡抵扣金额： {{ childItem_1.CardDeductionAmount | toFixed | NumFormat }}
                        </div>

                        <div class="color_red font_12" v-if="childItem_1.LargessCardDeductionAmount > 0"
                          :class="childItem_1.CardDeductionAmount != 0 ? 'marlt_15' : ''">
                          赠送卡抵扣金额： {{ childItem_1.LargessCardDeductionAmount | toFixed | NumFormat }}
                        </div>

                        <div class="color_red font_12" v-if="childItem_1.LargessAmount > 0"
                          :class="childItem_1.LargessCardDeductionAmount != 0 ? 'marlt_15' : ''">
                          赠送金额：¥ {{ childItem_1.LargessAmount | toFixed | NumFormat }}
                        </div>
                      </div>
                    </el-col>
                  </el-col>

                  <el-col :span="24" v-if="childItem_1.TreatBillHandler.length > 0"
                    class="pad_10 padtp_5 padbm_5 border_bottom">
                    <el-row v-for="(handler, pIndex) in childItem_1.TreatBillHandler" :key="pIndex + 'h5'">
                      <el-col :span="2">
                        <el-form @submit.native.prevent class="saleHandler" :inline="true" size="mini"
                          label-position="left">
                          <el-form-item :label="`${handler.TreatHandlerName}`"></el-form-item>
                        </el-form>
                      </el-col>
                      <el-col :span="22">
                        <el-form @submit.native.prevent class="saleHandler" :inline="true" size="mini">
                          <el-form-item v-for="(employee, handleIndex) in handler.Employee" :key="handleIndex"
                            :label="`${employee.EmployeeName}:`">{{ employee.Scale.toFixed(2) }}%
                          </el-form-item>
                        </el-form>
                      </el-col>
                    </el-row>
                  </el-col>
                </el-row>
              </el-row>
            </div>
            <!-- 产品 -->
            <el-row v-if="item.Product.length > 0">
              <el-row class="row_header_package_detail pad_10">
                <el-col :span="7">套餐卡产品</el-col>
                <el-col :span="7">退消耗数量</el-col>
                <el-col :span="10">退消耗金额</el-col>
              </el-row>
              <el-row v-for="(childItem, childIndex) in item.Product" :key="childIndex + 'c6-2'">
                <el-col :span="24" class="border_bottom pad_10">
                  <el-col :span="7">
                    {{ childItem.ProductName }}
                    <span v-if="childItem.Alias">({{ childItem.Alias }})</span>
                    <el-tag v-if="childItem.IsLargess" size="mini" type="danger" class="marlt_5">赠</el-tag>
                  </el-col>
                  <el-col :span="7">x {{ childItem.Quantity }}</el-col>
                  <el-col :span="10">
                    <div>¥ {{ childItem.TotalAmount | toFixed | NumFormat }}</div>
                    <div class="dis_flex martp_5">
                      <div class="color_green font_12" v-if="childItem.PayAmount > 0">现金金额： {{ childItem.PayAmount |
                        toFixed | NumFormat }}</div>
                      <div class="color_green font_12" v-if="childItem.CardDeductionAmount > 0"
                        :class="childItem.PayAmount != 0 ? 'marlt_15' : ''">
                        卡抵扣金额： {{ childItem.CardDeductionAmount | toFixed | NumFormat }}
                      </div>

                      <div class="color_red font_12" v-if="childItem.LargessCardDeductionAmount > 0"
                        :class="childItem.CardDeductionAmount != 0 ? 'marlt_15' : ''">
                        赠送卡抵扣金额： {{ childItem.LargessCardDeductionAmount | toFixed | NumFormat }}
                      </div>

                      <div class="color_red font_12" v-if="childItem.LargessAmount > 0"
                        :class="childItem.LargessCardDeductionAmount != 0 ? 'marlt_15' : ''">
                        赠送金额：¥ {{ childItem.LargessAmount | toFixed | NumFormat }}
                      </div>
                    </div>
                  </el-col>
                </el-col>
                <el-col :span="24" v-if="childItem.TreatBillHandler.length > 0"
                  class="pad_10 padtp_5 padbm_5 border_bottom">
                  <el-row v-for="(handler, pIndex) in childItem.TreatBillHandler" :key="pIndex + 'h5'">
                    <el-col :span="2">
                      <el-form @submit.native.prevent class="saleHandler" :inline="true" size="mini"
                        label-position="left">
                        <el-form-item :label="`${handler.TreatHandlerName}`"></el-form-item>
                      </el-form>
                    </el-col>
                    <el-col :span="22">
                      <el-form @submit.native.prevent class="saleHandler" :inline="true" size="mini">
                        <el-form-item v-for="(employee, handleIndex) in handler.Employee" :key="handleIndex"
                          :label="`${employee.EmployeeName}:`">{{ employee.Scale.toFixed(2) }}%
                        </el-form-item>
                      </el-form>
                    </el-col>
                  </el-row>
                </el-col>
              </el-row>
            </el-row>
          </el-row>
        </div>
      </div>
      <div class="border_left border_right border_bottom padtp_10 padbm_10" style="margin-bottom: 10px">
        <el-row>
          <el-col :span="6" :offset="17">
            <el-form class="saleInfo" size="mini">
              <el-form-item label="现金金额：" v-if="treatInfo.PayAmount">
                <div class="text_right">￥{{ treatInfo.PayAmount | toFixed | NumFormat }}</div>
              </el-form-item>

              <el-form-item label="卡抵扣金额：" v-if="treatInfo.CardDeductionAmount">
                <div class="text_right">￥{{ treatInfo.CardDeductionAmount | toFixed | NumFormat }}</div>
              </el-form-item>

              <el-form-item label="赠送卡抵扣金额：" v-if="treatInfo.LargessCardDeductionAmount">
                <div class="text_right">￥{{ treatInfo.LargessCardDeductionAmount | toFixed | NumFormat }}</div>
              </el-form-item>
              <el-form-item label="赠送金额：" v-if="treatInfo.LargessAmount">
                <div class="text_right">￥{{ treatInfo.LargessAmount | toFixed | NumFormat }}</div>
              </el-form-item>
              <el-form-item label="合计退消耗金额：">
                <div class="text_right">￥{{ treatInfo.Amount | toFixed | NumFormat }}</div>
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>
      </div>
    </div>
    <!-- 修改备注弹框 -->
    <el-dialog width="30%" title="修改消耗单备注" :visible.sync="innerVisible" append-to-body>
      <el-input type="textarea" :rows="4" v-model="Remark"></el-input>
      <span slot="footer" class="dialog-footer">
        <el-button @click="innerVisible = false" v-prevent-click size="small">取消</el-button>
        <el-button type="primary" @click="updateRemarkClick" v-prevent-click size="small">保存</el-button>
      </span>
    </el-dialog>

    <!-- 修改 下单时间 -->
    <el-dialog width="30%" :visible.sync="ModifyBillDateVisible" append-to-body>
      <span slot="title" class="text_center">修改下单时间</span>

      <el-form :model="billDateForm" size="mini" ref="ModifyBillDateRef">
        <el-form-item label="原下单时间:">{{ treatInfo.BillDate | dateFormat("YYYY-MM-DD HH:mm") }}</el-form-item>
        <el-form-item label="新下单时间:" prop="BillDate"
          :rules="[{ required: true, message: '请选择下单时间', trigger: ['blur', 'change'] }]">
          <el-date-picker v-model="billDateForm.BillDate" :picker-options="pickerOptions" size="small" type="datetime"
            default-time="09:00" placeholder="选择日期" format="yyyy-MM-dd HH:mm"></el-date-picker>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="ModifyBillDateVisible = false" v-prevent-click size="small">取消</el-button>
        <el-button type="primary" @click="changeBillDate" v-prevent-click size="small">保存</el-button>
      </span>
    </el-dialog>
  </section>
</template>

<script>
import API from "@/api/iBeauty/Order/treatBill";
const dayjs = require("dayjs");
import 'dayjs/locale/zh-cn' // 导入本地化语言
dayjs.locale('zh-cn')

export default {
  name: "treatRefundDetailContent",
  props: {
    treatInfo: Object,
    isModifyBillDate: {
      type: Boolean,
      default: false,
    },
    ModifyBillDateRestriction: {
      type: Object,
      default() {
        return {
          IsHaveRestriction: false, //为true有限制
          Deadline: "", //截止日期
        };
      }
    },
  },
  /** 监听数据变化   */
  watch: {
    treatInfo: {
      handler(newVal) {
        this.billDateForm.BillDate = newVal.BillDate;
      },
      deep: true,
    },
    ModifyBillDateRestriction: {
      handler(newVal) {
        if (newVal.IsHaveRestriction) {
          let DeadlineTime = dayjs(newVal.Deadline).valueOf();
          this.pickerOptions.disabledDate = (time) => {
            return time.getTime() > Date.now() || DeadlineTime > time.getTime()
          }
        } else {
          this.pickerOptions.disabledDate = (time) => {
            return time.getTime() > Date.now()
          }
        }

      },
      deep: true,
      immediate: true,
    }
  },
  /**  引入的组件  */
  components: {},
  /**  Vue 实例的数据对象**/
  data() {
    return {
      ModifyBillDateVisible: false,
      innerVisible: false,
      Remark: "",
      billDateForm: {
        BillDate: "",
      },
      payTypeForm: {
        payType: "",
      },

      pickerOptions: {
        disabledDate(time) {
          return (
            time.getTime() > Date.now()
            // ||
            // time.getTime() < Date.now() - 3600 * 1000 * 24 * 7
          );
        },
      },
    };
  },
  /**计算属性  */
  computed: {},
  /**  方法集合  */
  methods: {
    /**    */
    limitSealingAccount(BillDate, limit) {
      let isBefore = dayjs(BillDate).isBefore(dayjs(limit.Deadline));
      if (isBefore && limit.IsHaveRestriction) {
        return true;
      }
      return false;
    },
    /**    */
    getBillState(BillStatus) {
      switch (BillStatus) {
        case "10":
          return "待支付";
        case "20":
          return "已完成";
        case "30":
          return "已取消";
        default:
          return "";
      }
    },

    /** 修改时间   */
    ModifyBillDateClick() {
      let that = this;
      that.ModifyBillDateVisible = true;
    },

    /** 修改下单时间   */
    changeBillDate() {
      let that = this;
      that.$refs.ModifyBillDateRef.validate((valid) => {
        if (valid) {
          that.$emit("ModifyBillDate", that.billDateForm.BillDate, () => {
            that.ModifyBillDateVisible = false;
            that.$message.success("修改成功");
          });
        }
      });
    },
    upRemarkDialog() {
      var that = this;
      that.Remark = that.treatInfo.Remark;
      that.innerVisible = true;
    },

    //修改备注
    updateRemarkClick() {
      var that = this;
      var params = {
        TreatBillID: that.treatInfo.ID,
        Remark: that.Remark,
      };
      API.updateRemark(params).then((res) => {
        if (res.StateCode == 200) {
          that.innerVisible = false;
          that.treatInfo.Remark = that.Remark;
        } else {
          that.$message.error({
            message: res.Message,
            duration: 2000,
          });
        }
      });
    },
  },
  /** 创建实例之前执行函数   */
  beforeCreate() { },
  /**  实例创建完成之后  */
  created() { },
  /**  在挂载开始之前被调用  */
  beforeMount() { },
  /**  实例被挂载后调用  */
  mounted() {
    let that = this;
    that.billDateForm.BillDate = that.treatInfo.BillDate;
  },
  /**  数据更新 之前 调用  */
  beforeUpdate() { },
  /** 数据更新 完成 调用   */
  updated() { },
  /**  实例销毁之前调用  */
  beforeDestroy() { },
  /**  实例销毁后调用  */
  destroyed() { },
};
</script>

<style scoped lang="scss"></style>
