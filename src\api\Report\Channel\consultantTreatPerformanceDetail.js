/**
 * Created by preference on 2022/08/11
 *  zmx
 */

import * as API from "@/api/index";
export default {
  // 获取门店
  getAllEntityApi: (params) => {
    return API.POST("api/entity/allEntity", params);
  },
  /**   */
  consultantTreatPerformanceDetailStatement_list: (params) => {
    return API.POST(
      "api/consultantTreatPerformanceDetailStatement/list",
      params
    );
  },
  /**   */
  consultantTreatPerformanceDetailStatement_excel: (params) => {
    return API.exportExcel(
      "api/consultantTreatPerformanceDetailStatement/excel",
      params
    );
  },
  /* 业务代表 */
  geTemployeeAll: (params) => {
    return API.POST("api/channel/employeeAll", params);
  },
};
