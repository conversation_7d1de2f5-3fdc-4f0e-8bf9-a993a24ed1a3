<!-- /**  销售订单详情内容 */ --> 
<template>
  <section class="rechargeBillContent">
    <div>
      <!-- 订单信息 -->
      <div>
        <div class="tip marbm_10" style="margin-top:0">订单信息</div>
        <el-form label-width="100px" class="saleInfo" size="small">
          <el-row>
            <el-col :span="8">
              <el-form-item label="订单编号:">{{ saleOrderDetail.ID }}</el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="订单类型:">{{ getBillType(saleOrderDetail.BillType) }}</el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="订单状态:">{{ saleOrderDetail.BillStatus ==
                10 ? "待结账" : saleOrderDetail.BillStatus == 20 ? "已完成" : "已取消" }}</el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="8">
              <el-form-item label="顾客信息:">{{ saleOrderDetail.Name }} <span v-if="saleOrderDetail.PhoneNumber != null">({{
                saleOrderDetail.PhoneNumber | hidephone }})</span>
                <el-popover placement="right" width="400" trigger="click" v-model="changeMemberDialogVisible">
                  <div class="font_13 color_333 marbm_10">请选择顾客</div>

                  <el-autocomplete popper-class="customer-autocomplete-bill" prefix-icon="el-icon-user-solid"
                    v-model="customerName" style="width:100%" size="small" placeholder="请输入客户姓名、手机号、编号查找"
                    :fetch-suggestions="saleCustomerData" @select="handleCustomerSelect" :disabled="customerID != null"
                    :trigger-on-focus="false" :hide-loading="false" :highlight-first-item="true"
                    :select-when-unmatched="true">
                    <template slot="append">
                      <el-button icon="el-icon-delete" @click="removeCustomer"></el-button>
                    </template>
                    <template slot-scope="{ item }">
            <div class="name">
              {{ item.Name }}
              <el-tag size="mini" v-if="item.CustomerLevelName">{{ item.CustomerLevelName }}</el-tag>
            </div>
            <div class="info">手机号：{{ item.PhoneNumber | hidephone }}</div>
            <div class="info" v-if="item.Code">客户编号：{{ item.Code }}</div>
            <div class="info" v-if="item.EntityName">所属组织：{{ item.EntityName }}</div>
            <div class="info" v-if="item.ChannelName">渠道信息：{{ item.ChannelName }}</div>
                    </template>
                  </el-autocomplete>

                  <div class="text_right pad_10 martp_10">
                    <el-button class="marrt_10" size="mini" @click="changeMemberDialogVisible = false">取 消</el-button>
                    <el-button type="primary" size="mini" @click="updateSaleBillBindCutsomer">保 存</el-button>
                  </div>

                  <el-button @click="removeCustomer" slot="reference" class="ml_5"
                    v-if="saleOrderDetail.CustomerID == null" type="text" size="small">修改顾客</el-button>
                </el-popover>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="开单人:">{{ saleOrderDetail.EmployeeName }}</el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="订单金额:">￥{{ saleOrderDetail.Amount | NumFormat }}</el-form-item>
            </el-col>

          </el-row>
          <el-row>

            <el-col :span="8">
              <el-form-item label="欠款金额:">￥{{ saleOrderDetail.ArrearAmount | NumFormat }}{{ saleOrderDetail.Repayment > 0
                ?
                (saleOrderDetail.Repayment) : "" }}</el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="下单时间:">
                <span class="marrt_5">{{ saleOrderDetail.BillDate | dateFormat("YYYY-MM-DD HH:mm") }}</span>
                <el-button v-if="isModifyBillDate && !limitSealingAccount(saleOrderDetail.BillDate, ModifyBillDateRestriction)" type="text" @click="ModifyBillDateClick">修改</el-button>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="录单时间:">{{ saleOrderDetail.CreatedOn | dateFormat("YYYY-MM-DD HH:mm") }}</el-form-item>
            </el-col>
          </el-row>
          <el-row v-if="saleOrderDetail.BillStatus == 30">
            <el-col :span="8">
              <el-form-item label="取消时间:">{{ saleOrderDetail.CancelCreatedOn | dateFormat("YYYY-MM-DD HH:mm")
              }}</el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="取消人:">{{ saleOrderDetail.CancelCreatedBy }}</el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="取消原因:">
                <span class="marrt_5">{{ saleOrderDetail.CancelRemark }}</span>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="8">
              <el-form-item label="订单备注:">
                <span v-if="saleOrderDetail.Remark" class="marrt_5">{{ saleOrderDetail.Remark }}</span>
                <el-button type="text" @click="ChangeRemarkinnerVisible">修改备注</el-button>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row></el-row>
        </el-form>
      </div>
      <!-- 消费信息 -->
      <div class="martp_10">
        <div class="tip marbm_10" style="margin-top:0">消费信息</div>
        <div class="border_left border_right border_top">
          <!-- 储值卡 -->
          <div v-if="saleOrderDetail.SavingCard != undefined && saleOrderDetail.SavingCard.length > 0">
            <el-row class="row_header">
              <el-col :span="6">储值卡</el-col>
              <el-col :span="6">充值金额</el-col>
              <el-col :span="6">赠送金额</el-col>
              <el-col :span="6">小计</el-col>
            </el-row>
            <el-row v-for="(item, index) in saleOrderDetail.SavingCard" :key="index + 'x2'">
              <el-col :span="24" class="pad_10 border_bottom">
                <el-col :span="24">
                  <el-col :span="6">
                    <div>
                      {{ item.SavingCardName }}
                      <span v-if="item.Alias">({{ item.Alias }})</span>
                    </div>
                  </el-col>

                  <el-col :span="6">¥ {{ item.TotalAmount | NumFormat }}</el-col>
                  <el-col :span="6">¥ {{ item.LargessAmount | NumFormat }}</el-col>
                  <el-col :span="6">¥ {{ item.TotalAmount | NumFormat }}</el-col>
                </el-col>
              </el-col>
              <el-col :span="24" v-if="item.SaleBillHandler.length > 0" class="pad_10 padtp_5 padbm_5 border_bottom">
                <el-row v-for="(handler, pIndex) in item.SaleBillHandler" :key="pIndex + 'h2'">
                  <el-col :span="2">
                    <el-form class="saleHandler" :inline="true" size="mini" label-position="left">
                      <el-form-item :label="`${handler.SaleHandlerName}`"></el-form-item>
                    </el-form>
                  </el-col>
                  <el-col :span="22">
                    <el-form class="saleHandler" :inline="true" size="mini">
                      <el-form-item v-for="(employee, handleIndex) in handler.Employee" :key="handleIndex"
                        :label="`${employee.EmployeeName}:`">{{ (employee.Scale || 0).toFixed(2) }}%</el-form-item>
                    </el-form>
                  </el-col>
                </el-row>
              </el-col>
            </el-row>
          </div>
        </div>
        <div class="border_left border_right border_bottom">
          <el-row>
            <el-col :span="6" :offset="17">
              <el-form class="saleInfo" size="mini">
                <el-form-item label="合计：">
                  <div class="text_right">￥{{ saleOrderDetail.TotalAmount | NumFormat }}</div>
                </el-form-item>
                <el-form-item v-if="saleOrderDetail.PricePreferentialAmount < 0" label="手动改价：">
                  <div class="text_right">+￥{{ Math.abs(saleOrderDetail.PricePreferentialAmount) | NumFormat }}</div>
                </el-form-item>
                <el-form-item v-if="saleOrderDetail.PricePreferentialAmount > 0" label="手动改价：">
                  <div class="text_right">-￥{{ Math.abs(saleOrderDetail.PricePreferentialAmount) | NumFormat }}</div>
                </el-form-item>
                <el-form-item v-if="saleOrderDetail.CardPreferentialAmount > 0" label="卡优惠：">
                  <div class="text_right">-￥{{ saleOrderDetail.CardPreferentialAmount | NumFormat }}</div>
                </el-form-item>
                <el-form-item label="订单金额：">
                  <div class="text_right">￥{{ saleOrderDetail.Amount | NumFormat }}</div>
                </el-form-item>
              </el-form>
            </el-col>
          </el-row>
        </div>
      </div>
      <!-- 支付信息 -->
      <div class="payInfo">
        <div class="tip martp_10 marbm_10">支付信息</div>
        <el-card class="marbm_10" shadow="never"
          v-if="saleOrderDetail.SaleBillPay != undefined && saleOrderDetail.SaleBillPay.length > 0">
          <div slot="header">
            <span>支付收款</span>
          </div>
          <el-form class="saleInfo" :inline="true" size="mini" label-position="top">
            <el-form-item v-for="salepay in saleOrderDetail.SaleBillPay" :key="salepay.ID">
              <div slot="label">
                <span class="marrt_5">{{ salepay.Name }}</span>
                <el-button
                  v-if="isModifyPayMethod && !limitSealingAccount(saleOrderDetail.BillDate, ModifyBillPayMethodRestriction)"
                  type="text" @click="ModifyPayMethodClick(salepay)">修改</el-button>
              </div>
              ¥ {{ salepay.Amount | NumFormat }}
            </el-form-item>

          </el-form>
        </el-card>
        <el-card shadow="never"
          v-if="saleOrderDetail.SaleBillPaySavingCardDeduction != undefined && saleOrderDetail.SaleBillPaySavingCardDeduction.length > 0">
          <div slot="header">
            <span>储值卡抵扣</span>
          </div>
          <el-form class="saleInfo" :inline="true" size="mini" label-position="top">
            <el-form-item v-for="savingCardReduction in saleOrderDetail.SaleBillPaySavingCardDeduction"
              :key="savingCardReduction.ID" :label="savingCardReduction.Name">¥ {{ savingCardReduction.TotalAmount |
                NumFormat }}</el-form-item>
          </el-form>
        </el-card>
      </div>
    </div>

    <!-- 修改备注弹框 -->
    <el-dialog width="30%" :visible.sync="innerVisible" append-to-body>
      <span slot="title" class="text_center">修改销售单备注</span>
      <el-input type="textarea" :rows="4" v-model="Remark" placeholder="请输入备注内容"></el-input>
      <span slot="footer" class="dialog-footer">
        <el-button @click="innerVisible = false" size="small" v-prevent-click>取消</el-button>
        <el-button type="primary" @click="updateRemarkClick" v-prevent-click size="small">保存</el-button>
      </span>
    </el-dialog>

    <!-- 修改 下单时间 -->
    <el-dialog width="30%" :visible.sync="ModifyBillDateVisible" append-to-body>
      <span slot="title" class="text_center">修改下单时间</span>

      <el-form :model="billDateForm" size="mini" ref="ModifyBillDateRef">
        <el-form-item label="原下单时间:">{{ saleOrderDetail.BillDate | dateFormat("YYYY-MM-DD HH:mm") }}</el-form-item>
        <el-form-item label="新下单时间:" prop="BillDate"
          :rules="[{ required: true, message: '请选择下单时间', trigger: ['blur', 'change'] },]">
          <el-date-picker v-model="billDateForm.BillDate" :picker-options="pickerOptions" size="small" type="datetime"
            default-time="09:00" placeholder="选择日期" format="yyyy-MM-dd HH:mm"></el-date-picker>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="ModifyBillDateVisible = false" v-prevent-click size="small">取消</el-button>
        <el-button type="primary" @click="changeBillDate" v-prevent-click size="small">保存</el-button>
      </span>
    </el-dialog>

    <!-- 修改 下单时间 -->
    <el-dialog width="30%" :visible.sync="ModifyPayMethodVisible" append-to-body>
      <span slot="title" class="text_center">修改支付方式</span>

      <el-form :model="payTypeForm" size="mini" ref="ModifyPayMethodRef">
        <el-form-item label="原支付方式：">
          <span class="marrt_10">{{ currentPayType.Name }}</span>
        </el-form-item>
        <el-form-item label="支付金额：">
          <span>¥ {{ currentPayType.Amount | NumFormat }}</span>
        </el-form-item>
        <el-form-item label="新支付方式：" prop="payType"
          :rules="[{ required: true, message: '请选择支付方式', trigger: ['blur', 'change'] },]">
          <el-select v-model="payTypeForm.payType" filterable placeholder="请选择" clearable>
            <el-option v-for="item in payTypeList" :key="item.ID" :label="item.Name" :value="item.ID">
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="ModifyPayMethodVisible = false" v-prevent-click size="small">取消</el-button>
        <el-button type="primary" @click="changPayTypeClick" v-prevent-click size="small">保存</el-button>
      </span>
    </el-dialog>

  </section>
</template>

<script>
import saleAPI from "@/api/iBeauty/Order/saleGoods";
import API from "@/api/iBeauty/Order/saleBill";
const dayjs = require("dayjs");
import 'dayjs/locale/zh-cn' // 导入本地化语言
dayjs.locale('zh-cn')
export default {
  name: "rechargeBillContent",
  props: {
    saleOrderDetail: {
      type: Object,
      default: function () {
        return {};
      },
    },
    isModifyBillDate: {
      type: Boolean,
      default: false,
    },
    isModifyPayMethod: {
      type: Boolean,
      default: false,
    },
    ModifyBillDateRestriction: {
      type: Object,
      default() {
        return {
          IsHaveRestriction: false, //为true有限制
          Deadline: "",  //截止日期 
        };
      }
    },
    ModifyBillPayMethodRestriction: {
      type: Object,
      default() {
        return {
          IsHaveRestriction: false, //为true有限制
          Deadline: "",  //截止日期 
        };
      }
    }
  },
  /** 监听数据变化   */
  watch: {
    saleOrderDetail: {
      handler(newVal) {
        this.billDateForm.BillDate = newVal.BillDate;
      },
      deep: true,
    },
    ModifyBillDateRestriction: {
      handler(newVal) {
        if (newVal.IsHaveRestriction) {
          let DeadlineTime = dayjs(newVal.Deadline).valueOf();
          this.pickerOptions.disabledDate = (time) => {
            return time.getTime() > Date.now() || DeadlineTime > time.getTime()
          }
        } else {
          this.pickerOptions.disabledDate = (time) => {
            return time.getTime() > Date.now()
          }
        }

      },
      deep: true,
      immediate: true,
    }
  },

  /**  引入的组件  */
  components: {},
  /**  Vue 实例的数据对象**/
  data() {
    return {
      ModifyBillDateVisible: false,
      ModifyPayMethodVisible: false,
      changeMemberDialogVisible: false,
      innerVisible: false, //备注弹窗
      customerName: "",
      customerID: null,
      Remark: "",
      billDateForm: {
        BillDate: "",
      },
      payTypeForm: {
        payType: "",
      },

      payTypeList: [],
      showPayType: false,
      pickerOptions: {
        disabledDate(time) {
          return (
            time.getTime() > Date.now()
            // ||
            // time.getTime() < Date.now() - 3600 * 1000 * 24 * 7
          );
        },
      },
      currentPayType: "",
    };
  },
  /**  方法集合  */
  methods: {    /**    */
    limitSealingAccount(BillDate, limit) {
      let isBefore = dayjs(BillDate).isBefore(dayjs(limit.Deadline));
      if (isBefore && limit.IsHaveRestriction) {
        return true;
      }
      return false;
    },
    /**  点击修改支付方式  */
    ModifyPayMethodClick(salepay) {
      let that = this;
      that.ModifyPayMethodVisible = true;
      that.currentPayType = salepay;
    },
    /**    */
    ModifyBillDateClick() {
      let that = this;
      that.ModifyBillDateVisible = true;
    },
    /**  修改支付方式  */
    changPayTypeClick() {
      let that = this;
      that.$refs.ModifyPayMethodRef.validate((valid) => {
        if (valid) {
          let payType = that.payTypeList.find(
            (val) => val.ID == that.payTypeForm.payType
          );
          let params = {
            newInfo: payType,
            oldInfo: that.currentPayType,
          };
          that.$emit("changPayType", params, () => {
            that.ModifyPayMethodVisible = false;
            that.$message.success("修改成功");
          });
        }
      });
    },
    /** 修改下单时间   */
    changeBillDate() {
      let that = this;
      that.$refs.ModifyBillDateRef.validate((valid) => {
        if (valid) {
          that.$emit("ModifyBillDate", that.billDateForm.BillDate, () => {
            that.ModifyBillDateVisible = false;
            that.$message.success("修改成功");
          });
        }
      });
    },

    /**  获取订单类型   */
    getBillType(BillType) {
      switch (BillType) {
        case "10":
          return "销售订单";
        case "20":
          return "退款订单";
        case "30":
          return "补欠款单";
        case "40":
          return "充值订单";
      }
    },
    /**  查看套餐卡明细  */
    packDetailClick(item) {
      item.IsShowDetails = !item.IsShowDetails;
    },
    // 顾客
    saleCustomerData: function (queryString, cb) {
      var that = this;
      that.loading = true;
      var params = {
        Name: queryString ? queryString : "",
      };
      saleAPI
        .getSaleCustomer(params)
        .then((res) => {
          if (res.StateCode == 200) {
            cb(res.Data);
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },

    handleCustomerSelect(item) {
      if (item.ID != undefined) {
        this.customerID = item.ID;
        this.customerFullName = item.Name;
        this.customerPhoneNumber = item.PhoneNumber;
        this.customerName = item.Name + "【" + item.PhoneNumber + "】";
        // this.customerChange();
      } else {
        // this.addNewCustomer();
      }
    },

    removeCustomer() {
      var that = this;
      that.customerID = null;
      that.customerFullName = "";
      that.customerPhoneNumber = "";
      that.customerName = "";
    },

    /**  更新 顾客网络请求  */
    updateSaleBillBindCutsomer() {
      var that = this;
      that.changeMemberDialogVisible = false;
      var params = {
        BillID: that.saleOrderDetail.ID,
        CustomerID: that.customerID,
      };
      API.updateSaleBillBindCutsomer(params).then((res) => {
        if (res.StateCode == 200) {
          that.saleOrderDetail.Name = that.customerName;
          that.saleOrderDetail.CustomerID = " ";
          // that.handleSearch();
          that.$emit("refreshBillList");
        } else {
          that.$message.error({
            message: res.Message,
            duration: 2000,
          });
        }
      });
    },
    /**  修改备注   */
    ChangeRemarkinnerVisible() {
      let that = this;
      that.Remark = that.saleOrderDetail.Remark;
      that.innerVisible = true;
    },
    //修改备注
    updateRemarkClick() {
      var that = this;
      var params = {
        SaleBillID: that.saleOrderDetail.ID,
        Remark: that.Remark,
      };
      API.updateRemark(params).then((res) => {
        if (res.StateCode == 200) {
          that.saleOrderDetail.Remark = that.Remark;
          that.Remark = "";
          that.innerVisible = false;
        } else {
          that.$message.error({
            message: res.Message,
            duration: 2000,
          });
        }
      });
    },
    // 支付方式
    salePayMethodData: function () {
      var that = this;
      that.loading = true;
      API.getSalePayMethod()
        .then((res) => {
          if (res.StateCode == 200) {
            that.payTypeList = res.Data;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },
  },
  /**  实例被挂载后调用  */
  mounted() {
    let that = this;
    that.billDateForm.BillDate = that.saleOrderDetail.BillDate;
    that.salePayMethodData();
  },
};
</script>

<style lang="scss">
.rechargeBillContent {
  .el-scrollbar_height {
    height: 100%;

    .el-scrollbar__wrap {
      overflow-x: hidden;
    }
  }

  .el-form-item__label {
    font-size: 13px !important;
  }

  .el-form-item__content {
    font-size: 13px !important;
  }

  .el-form-item {
    margin-bottom: 0px;
  }

  .saleHandler {
    .el-form-item__label {
      font-size: 12px !important;
      line-height: 18px;
      color: #c0c4cc;
    }

    .el-form-item__content {
      font-size: 12px !important;
      line-height: 20px;
      color: #c0c4cc;
    }

    .el-form-item {
      margin-bottom: 0px;
    }
  }

  .payInfo {
    .el-card__header {
      padding-top: 10px;
      padding-bottom: 10px;
    }

    .el-card__body {
      padding-top: 10px;
      padding-bottom: 10px;
    }
  }

  .saleInfo {
    .el-form-item__label {
      font-size: 13px !important;
    }

    .el-form-item__content {
      font-size: 13px !important;
    }

    .el-form-item {
      margin-bottom: 0px;
    }
  }
}

.customer-autocomplete-bill {
  li {
    line-height: normal;
    padding: 7px;

    .name {
      text-overflow: ellipsis;
      overflow: hidden;
    }

    .info {
      font-size: 12px;
      color: #b4b4b4;
    }

    .highlighted .info {
      color: #ddd;
    }
  }
}</style>
