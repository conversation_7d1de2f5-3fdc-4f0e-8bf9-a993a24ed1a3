<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
    <style>
        div {
            background: red;
        }
    </style>
</head>

<body>
    <svg xmlns="http://www.w3.org/2000/svg" style="width:100%;height:100%;overflow:visible;display:inline;">
        <foreignObject style="width:100%;height:100%;overflow:visible;display:inline;">
            <div>

            </div>
        </foreignObject>
    </svg>
</body>
<script>
    var parser = new DOMParser();
    var htmlDoc = parser.parseFromString(`<svg xmlns="http://www.w3.org/2000/svg" style="width:100%;height:100%;overflow:visible;display:inline;">
        <foreignObject style="width:100%;height:100%;overflow:visible;display:inline;">
            <div>

            </div>
        </foreignObject>
    </svg>`, 'text/html');

    console.log(htmlDoc.querySelector("svg"))
</script>

</html>