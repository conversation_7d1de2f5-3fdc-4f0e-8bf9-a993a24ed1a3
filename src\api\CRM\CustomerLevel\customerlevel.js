import * as API from '../../index';

export default {
    // 获取顾客等级列表
    getCustomerLevelList:params => {
        return API.POST('api/customerLevel/all',params)
    },
    // 顾客等级添加
    createCustomerLeve:params => {
        return API.POST('api/customerLevel/create',params)
    },
    // 顾客等级修改
    updateCustomerLevel:params => {
        return API.POST('api/customerLevel/update',params)
    },
    // 顾客等级删除
    deleteCustomerLevel:params => {
        return API.POST('api/customerLevel/delete',params)
    },
    // 提示栏状态
    statusCustomerLevel:params => {
        return API.POST('api/customerLevel/status',params)
    },
    // 更新会员等级
    updateAllCustomerLevel:params => {
        return API.POST('api/customerLevel/updateAllCustomerLevel',params)
    },
}