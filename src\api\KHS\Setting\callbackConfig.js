/**
 * Created by wsf on 2022/03/25
 */
import * as API from '@/api/index'
export default {
    /* 回访方式列表 */
    getAllCallbackMethod: params => {
        return API.POST('api/callbackMethod/all', params)
    },
    /* 新建回访方式 */
    createCallbackMethod: params => {
        return API.POST('api/callbackMethod/create', params)
    },
    /* 修改回访方式 */
    updateCallbackMethod: params => {
        return API.POST('api/callbackMethod/update', params)
    },
    /* 移动回访方式 */
    moveCallbackMethod: params => {
        return API.POST('api/callbackMethod/move', params)
    },
    /* 回访状态列表 */
    getAllCallBackStatus: params => {
        return API.POST('api/callbackStatus/all', params)
    },
    /* 新建回访状态 */
    createCallBackStatus: params => {
        return API.POST('api/callbackStatus/create', params)
    },
    /* 修改回访状态 */
    updateCallBackStatus: params => {
        return API.POST('api/callbackStatus/update', params)
    },
    /* 移动回访状态 */
    moveCallBackStatus: params => {
        return API.POST('api/callbackStatus/move', params)
    },
}