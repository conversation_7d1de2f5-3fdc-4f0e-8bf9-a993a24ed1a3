/**
 * Created by preference on 2021/11/15
 *  zmx 
 */

import * as API from '@/api/index'
export default {
  /**  提成方案列表  */
  treatGeneralCardCommissionScheme_list: params => {
    return API.POST('api/treatGeneralCardCommissionScheme/list', params)
  },
  /**   提成方案保存  */
  treatGeneralCardCommissionScheme_create: params => {
    return API.POST('api/treatGeneralCardCommissionScheme/create', params)
  },
  /**  提成方案删除  */
  treatGeneralCardCommissionScheme_delete: params => {
    return API.POST('api/treatGeneralCardCommissionScheme/delete', params)
  },
  /**  通用次卡分类提成  */
  treatGeneralCardProjectCategoryCommission_all: params => {
    return API.POST('api/treatGeneralCardProjectCategoryCommission/all', params)
  },
  /**  通用次卡分类提成保存  */
  treatGeneralCardProjectCategoryCommission_update: params => {
    return API.POST('api/treatGeneralCardProjectCategoryCommission/update', params)
  },
  /**  所有通用次卡经手人提成  */
  treatGeneralCardSchemeHandlerCommission_all: params => {
    return API.POST('api/treatGeneralCardSchemeHandlerCommission/all', params)
  },
  /**  所有通用次卡经手人提成保存  */
  treatGeneralCardSchemeHandlerCommission_update: params => {
    return API.POST('api/treatGeneralCardSchemeHandlerCommission/update', params)
  },
  /**   分类通用次卡经手人提成 */
  treatGeneralCardCategoryHandlerCommission_all: params => {
    return API.POST('api/treatGeneralCardCategoryHandlerCommission/all', params)
  },
  /** 分类通用次卡经手人提成 保存   */
  treatGeneralCardCategoryHandlerCommission_update: params => {
    return API.POST('api/treatGeneralCardCategoryHandlerCommission/update', params)
  },
  /**  通用次卡提成  */
  treatGeneralCardCommission_all: params => {
    return API.POST('api/treatGeneralCardCommission/all', params)
  },
  /**  通用次卡提成保存  */
  treatGeneralCardCommission_update: params => {
    return API.POST('api/treatGeneralCardCommission/update', params)
  },
  /**  通用次卡提成  */
  treatGeneralCardHandlerCommission_all: params => {
    return API.POST('api/treatGeneralCardHandlerCommission/all', params)
  },
  /**  通用次卡经手人提成保存 */
  treatGeneralCardHandlerCommission_update: params => {
    return API.POST('api/treatGeneralCardHandlerCommission/update', params)
  },
  /**  通用次卡项目提成  */
  treatGeneralCardProjectCommission_all: params => {
    return API.POST('api/treatGeneralCardProjectCommission/all', params)
  },
  /**  通用次卡项目提成保存  */
  treatGeneralCardProjectCommission_update: params => {
    return API.POST('api/treatGeneralCardProjectCommission/update', params)
  },
  /**  通用次卡项目经手人提成 */
  treatGeneralCardProjectHandlerCommission_all: params => {
    return API.POST('api/treatGeneralCardProjectHandlerCommission/all', params)
  },
  /**  通用次卡项目经手人提成保存  */
  treatGeneralCardProjectHandlerCommission_update: params => {
    return API.POST('api/treatGeneralCardProjectHandlerCommission/update', params)
  },
}