<template>
  <div class="callback content_body">
    <!-- 搜索 -->
    <div class="nav_header">
      <el-form :inline="true" size="small" @keyup.enter.native="handleSearch">
        <el-form-item label="会员信息">
          <el-input size="small" v-model="search.Name" @clear="handleSearch" clearable placeholder="输入会员姓名、手机号或编号"></el-input>
        </el-form-item>
        <el-form-item label="是否回访">
          <el-select placeholder="请选择是否回访" clearable v-model="search.IsCallBack" @change="handleSearch" size="small">
            <el-option label="已回访" :value="true"></el-option>
            <el-option label="待回访" :value="false"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="回访方式">
          <el-select placeholder="请选择回访方式" filterable clearable v-model="search.callbackMethodID" @change="handleSearch" size="small">
            <el-option v-for="item in callbackMethod" :key="item.ID" :label="item.Name" :value="item.ID"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="回访状态">
          <el-select placeholder="请选择回访状态" filterable clearable v-model="search.callbackStatusID" @change="handleSearch" size="small">
            <el-option v-for="item in callBackStatus" :key="item.ID" :label="item.Name" :value="item.ID"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="计划回访时间">
          <el-date-picker
            v-model="search.QueryDate"
            unlink-panels
            type="daterange"
            range-separator="至"
            value-format="yyyy-MM-dd"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            @change="handleSearch"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="回访人员" v-if="isShowCallBack">
          <el-select placeholder="请选择回访人员" filterable clearable v-model="search.CallBackBy" @change="handleSearch" size="small">
            <el-option v-for="item in callBackEmployeeData" :key="item.ID" :label="item.Name" :value="item.ID"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="回访医院" v-if="isShowCallBack">
          <el-select placeholder="请选择回访医院" filterable clearable v-model="search.CallbackEntityID" @change="handleSearch" size="small">
            <el-option v-for="item in callBackEntityData" :key="item.ID" :label="item.Name" :value="item.ID"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="渠道信息">
          <el-input size="small" v-model="search.ChannelName" @clear="handleSearch" clearable placeholder="输入渠道名称"></el-input>
        </el-form-item>
        <el-form-item label="会员等级">
          <el-select placeholder="请选择会员等级" filterable clearable v-model="search.CustomerLevelID" @change="handleSearch" size="small">
            <el-option v-for="item in customerLevelList" :key="item.ID" :label="item.Name" :value="item.ID"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item v-if="isShowCallBack">
          <el-checkbox v-model="search.IsShowOwnCallBack" @change="IsShowOwnCallBackChange()">显示自己的回访</el-checkbox>
        </el-form-item>
        <el-form-item>
          <el-button size="small" type="primary" @click="handleSearch">搜索</el-button>
        </el-form-item>

        <el-form-item>
          <el-dropdown @command="customer_Export" :loading="downloadLoading">
            <el-button type="primary"> 导出<i class="el-icon-arrow-down el-icon--right"></i> </el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item command="excelNoDisPlayPhone">导出</el-dropdown-item>
              <el-dropdown-item command="excelDisPlayPhone">导出(手机号)</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
          <!-- <el-button @click="customer_Export('excelNoDisPlayPhone')"  type="primary" v-prevent-click :loading="downloadLoading"> 导出 </el-button>
          <el-button @click="customer_Export('excelDisPlayPhone')"  type="primary" v-prevent-click :loading="downloadLoading"> 导出(手机号）</el-button> -->
        </el-form-item>

        <!-- <el-form-item>
          <el-button size="small" type="primary" @click="callback_excelDisPlayPhone" :loading="downloadLoading">导出</el-button>
        </el-form-item>

        <el-form-item>
          <el-button size="small" type="primary" @click="callback_excelNoDisPlayPhone" :loading="downloadNoDisPlayLoading">隐藏手机号导出</el-button>
        </el-form-item> -->
      </el-form>
    </div>
    <!-- table表格 -->
    <el-table
      size="small"
      highlight-current-row
      :data="tableData"
      tooltip-effect="light"
      @row-click="rowClick"
      @selection-change="batchSettingSelection"
      :cell-class-name="cellClass"
      row-key="ID"
      ref="multipleTable"
    >
      <el-table-column fixed type="selection" width="55" :reserve-selection="true" :selectable="checkboxSelect"></el-table-column>
      <el-table-column fixed label="操作" width="80px">
        <template slot-scope="scope">
          <el-button v-if="!scope.row.IsCallback" type="primary" size="small" @click.stop="callbackClick(scope.row)">回访</el-button>
        </template>
      </el-table-column>
      <el-table-column label="是否回访" prop="IsCallback">
        <template slot-scope="scope">
          <span>{{ scope.row.IsCallback ? '已回访' : '待回访' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="计划回访时间" prop="PlannedOn" width="140px">
        <template slot-scope="scope">
          {{ scope.row.PlannedOn | dateFormat('YYYY-MM-DD HH:mm') }}
        </template>
      </el-table-column>
      <el-table-column label="回访时间" prop="CallbackOn" width="140px"></el-table-column>
      <el-table-column label="回访人员" prop="CallbackEmployeeName" show-overflow-tooltip></el-table-column>
      <el-table-column label="回访方式" prop="MethodName"></el-table-column>
      <el-table-column label="回访状态" prop="Status"></el-table-column>
      <!-- <el-table-column label="客户" prop="CustomerName"> </el-table-column> -->
      <el-table-column label="客户" prop="CustomerName" width="150px">
        <template slot-scope="scope">
          <div>
            {{ scope.row.CustomerName }} <span v-if="scope.row.Code">({{ scope.row.Code }})</span>
          </div>
          <div>{{ scope.row.PhoneNumber | hidephone }}</div>
        </template>
      </el-table-column>
      <el-table-column label="性别" prop="Gender" :formatter="formatGender"></el-table-column>
      <!-- <el-table-column label="手机" prop="PhoneNumber" width="100px">
        <template slot-scope="scope">
          {{ scope.row.PhoneNumber | hidephone }}
        </template>
      </el-table-column>
      <el-table-column label="编号" prop="Code"> </el-table-column> -->

      <el-table-column label="渠道" prop="ChannelName" width="100px"></el-table-column>
      <el-table-column label="会员等级" prop="CustomerLevelName"></el-table-column>

      <el-table-column label="服务人员" width="150px">
        <template slot-scope="scope">
          <el-tooltip placement="top" effect="light">
            <div slot="content">
              <el-descriptions size="mini" :column="1" border :colon="false" labelClassName="custom-customer-descLabel">
                <el-descriptions-item v-for="(item, index) in scope.row.ServicerEmployee" :key="index" :label="item.Name + ': '">
                  {{ getServicerEmpNames(item.ServicerEmpList) }}
                </el-descriptions-item>
              </el-descriptions>
            </div>
            <div style="text-overflow: ellipsis; white-space: nowrap; overflow: hidden">
              {{ getFirstServiceEmp(scope.row.ServicerEmployee) }}
            </div>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column label="信息来源" prop="CustomerSourceName"></el-table-column>
      <el-table-column label="生日" width="120px">
        <template slot-scope="scope">
          <span v-if="scope.row.Birthday">{{ scope.row.BirthdayType == 10 ? '公历 ' + scope.row.Birthday : '农历 ' + scope.row.Birthday }}</span>
        </template>
      </el-table-column>
      <el-table-column label="注册日期" prop="CustomerCreatedOn" width="140px">
        <template slot-scope="scope">
          {{ scope.row.CustomerCreatedOn | dateFormat('YYYY-MM-DD HH:mm') }}
        </template>
      </el-table-column>
    </el-table>
    <div style="display: flex">
      <div class="pad_15 text_left" style="flex: 1">
        <el-button size="small" type="primary" @click="batchClick"> 批量分配 </el-button>
      </div>
      <div class="pad_15 text_right" style="flex: 1">
        <el-pagination
          background
          v-if="paginations.total > 0"
          @current-change="handleCurrentChange"
          :current-page.sync="paginations.page"
          :page-size="paginations.page_size"
          :layout="paginations.layout"
          :total="paginations.total"
        ></el-pagination>
      </div>
    </div>
    <!-- </div>
        </el-main>
      </el-container> -->
    <!-- <el-aside :width="'680px'" class="customer-detail">
        <work-customer-detail
          :customerID="CustomerID"
          :isCallBack="isCallBack"
          ref="customerDetail"
          :isCustomerPhoneNumberView="isCustomerPhoneNumberView"
          :isCustomerPhoneNumberModify="isCustomerPhoneNumberModify"
        >
        </work-customer-detail>
      </el-aside>
    </el-container> -->

    <work-customer-detail
      v-if="customerDetailVisible"
      :visible.sync="customerDetailVisible"
      :customerID="CustomerID"
      :isCallBack="isCallBack"
      ref="customerDetail"
      :isCustomerPhoneNumberView="isCustomerPhoneNumberView"
      :isCustomerPhoneNumberModify="isCustomerPhoneNumberModify"
      :isCustomerBasicInformationModify="isCustomerBasicInformationModify"
      :isCustomerServicerModify="isCustomerServicerModify"
      :isCustomerBasicFileModify="isCustomerBasicFileModify"
    >
    </work-customer-detail>
    <!-- 批量分配弹出层 -->
    <el-dialog title="批量分配" :visible.sync="batchDialog" width="750px" custom-class="custom-Dropdown" :close-on-click-modal="false">
      <el-radio-group v-model="HandlerType" @change="groupChange">
        <el-row>
          <el-col :span="24">
            <el-radio :label="10">
              <span class="marrt_5">客户服务人员</span>
              <el-select v-model="Servicer.ServicerID" placeholder="请选择服务人员" clearable size="small" :disabled="HandlerType != 10">
                <el-option v-for="item in servicerEmployee" :key="item.ID" :label="item.Name" :value="item.ID"></el-option>
              </el-select>
              <span class="marlt_10 marrt_5">当未分配服务人员时，默认</span>
              <el-select
                :popper-append-to-body="false"
                v-model="Servicer.EmployeeID"
                filterable
                remote
                :remote-method="searchEmpRemote"
                placeholder="请输入员工姓名、编号查找"
                :disabled="HandlerType != 10"
                clearable
                size="small"
                @focus="getFocus"
              >
                <el-option v-for="item in allEmployee" :key="item.ID" :label="item.Name" :value="item.ID">
                  <div class="dis_flex flex_dir_column pad_5_0">
                    <div style="line-height: 25px">
                      <span style="float: left">{{ item.Name }}</span>
                      <span style="float: right; color: #8492a6; font-size: 13px">{{ item.ID }}</span>
                    </div>
                  </div>
                </el-option>
              </el-select>
            </el-radio>
          </el-col>
        </el-row>
        <el-row class="martp_10">
          <el-col :span="24">
            <el-radio :label="20">
              <span class="marrt_5">项目消耗经手人</span>
              <el-select v-model="TreatHandler.ProjectTreatHandlerID" :disabled="HandlerType != 20" placeholder="请选择项目消耗经手人" clearable size="small">
                <el-option v-for="item in projectConsumptionHandlerList" :key="item.ID" :label="item.Name" :value="item.ID"></el-option>
              </el-select>
              <span class="marlt_10 marrt_5">储值卡消耗项目经手人</span>
              <el-select
                v-model="TreatHandler.SavingCardProjectTreatHandlerID"
                :disabled="HandlerType != 20"
                placeholder="请选择储值卡消耗经手人"
                clearable
                size="small"
              >
                <el-option v-for="item in valueCardConsumptionHandlerList" :key="item.ID" :label="item.Name" :value="item.ID"></el-option>
              </el-select>
              <br />
              <span class="marlt_25 marrt_5 martp_10">当未分配消耗经手人时，默认</span>
              <el-select
                class="martp_10"
                :popper-append-to-body="false"
                v-model="TreatHandler.EmployeeID"
                filterable
                remote
                :remote-method="searchEmpRemote"
                :disabled="HandlerType != 20"
                placeholder="请输入员工姓名、编号查找"
                clearable
                size="small"
                @focus="getFocus"
                ><el-option v-for="item in allEmployee" :key="item.ID" :label="item.Name" :value="item.ID">
                  <div class="dis_flex flex_dir_column pad_5_0">
                    <div style="line-height: 25px">
                      <span style="float: left">{{ item.Name }}</span>
                      <span style="float: right; color: #8492a6; font-size: 13px">{{ item.ID }}</span>
                    </div>
                  </div>
                </el-option>
              </el-select>
            </el-radio>
          </el-col>
        </el-row>
        <el-row class="martp_10">
          <el-col :span="24">
            <el-radio :label="30">
              <span class="marrt_5">固定回访人</span>
              <el-select
                :popper-append-to-body="false"
                v-model="EmployeeID"
                filterable
                remote
                :remote-method="searchEmpRemote"
                clearable
                size="small"
                :disabled="HandlerType != 30"
                placeholder="请输入员工姓名、编号查找"
                @focus="getFocus"
                ><el-option v-for="item in allEmployee" :key="item.ID" :label="item.Name" :value="item.ID">
                  <div class="dis_flex flex_dir_column pad_5_0">
                    <div style="line-height: 25px">
                      <span style="float: left">{{ item.Name }}</span>
                      <span style="float: right; color: #8492a6; font-size: 13px">{{ item.ID }}</span>
                    </div>
                  </div>
                </el-option>
              </el-select>
            </el-radio>
          </el-col>
        </el-row>
      </el-radio-group>
      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="batchDialog = false" v-prevent-click>取 消</el-button>
        <el-button type="primary" size="small" v-prevent-click @click="submitBatchClick">保存</el-button>
      </div>
    </el-dialog>
    <!-- 处理回访弹出层 -->
    <el-dialog
      title="处理回访任务"
      :visible.sync="dialogVisible"
      width="980px"
      custom-class="custom-dialog"
      @close="closeAddCallBackDialog"
      :close-on-click-modal="false"
    >
      <el-scrollbar class="el_scrollbar_height_callback">
        <div class="information">
          <el-row type="flex" align="">
            <el-col :span="2">
              <el-avatar :size="50" :src="circleUrl"></el-avatar>
            </el-col>
            <el-col :span="22">
              <el-row type="flex" justify="space-between">
                <el-col :span="24">
                  <strong class="marrt_5 font_18">{{ customerDetail.Name }}</strong>
                  <el-image v-if="customerDetail.Gender == 2" style="width: 8px; height: 12px" :src="require('@/assets//img//gender-female.png')"></el-image>
                  <el-image v-if="customerDetail.Gender == 1" style="width: 8px; height: 12px" :src="require('@/assets/img/gender-male.png')"></el-image>
                </el-col>
              </el-row>
              <el-col justify="space-between">
                <el-col :span="8" class="color_999 martp_10"
                  >手机号：<span class="color_333">{{ customerDetail.PhoneNumber | hidephone }}</span></el-col
                >
                <el-col :span="8" class="color_999 martp_10"
                  >客户编号：<span class="color_333">{{ customerDetail.Code }}</span></el-col
                >
                <el-col :span="8" class="color_999 martp_10"
                  >注册时间：<span class="color_333">{{ customerDetail.CreatedOn }}</span></el-col
                >
                <el-col :span="8" class="color_999 martp_10"
                  >介绍人：<span class="color_333">{{ customerDetail.IntroducerName }}</span></el-col
                >
                <el-col :span="8" class="color_999 martp_10"
                  >渠道来源：<span class="color_333">{{ customerDetail.ChannelName }}</span></el-col
                >
                <el-col :span="8" class="color_999 martp_10"
                  >信息来源：<span class="color_333">{{ customerDetail.CustomerSourceName }}</span></el-col
                >
              </el-col>
            </el-col>
          </el-row>
        </div>
        <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="110px" size="small">
          <el-form-item label="回访方式" prop="CallbackMethodID">
            <el-radio-group v-model="ruleForm.CallbackMethodID">
              <div style="padding-top: 8px">
                <el-radio style="padding-bottom: 8px" v-for="item in callbackMethod" :key="item.ID" :label="item.ID">{{ item.Name }}</el-radio>
              </div>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="回访状态" prop="CallbackStatusID">
            <el-radio-group v-model="ruleForm.CallbackStatusID" class="align-items">
              <div style="padding-top: 8px">
                <el-radio style="padding-bottom: 8px" v-for="item in callBackStatus" :key="item.ID" :label="item.ID">{{ item.Name }}</el-radio>
              </div>
            </el-radio-group>
          </el-form-item>
          <div style="border-top: 1px solid #cfcfcf" v-for="(item, index) in ruleForm.Content" :key="item.CallbackRecordProjectID">
            <el-row style="margin-left: 42px; padding: 5px 0">
              <el-col :span="24" class="martp_5">
                <el-col :span="3" class="color_999">回访天数：</el-col>
                <el-col :span="21">{{ item.CallbackCycle }}天</el-col>
              </el-col>
              <el-col :span="24" class="martp_5">
                <el-col :span="3" class="color_999">回访项目：</el-col>
                <el-col :span="21">
                  <el-tag size="mini">{{ item.ProjectName }}</el-tag></el-col
                >
              </el-col>
              <el-col :span="24" class="martp_5">
                <el-col :span="3" class="color_999">回访内容：</el-col>
                <el-col :span="21"> {{ item.CallbackRemark }}</el-col>
              </el-col>
            </el-row>
            <el-form-item label="回访记录" :prop="'Content.' + index + '.CallbackContent'" :rules="rules.ContentInput">
              <el-input rows="4" type="textarea" v-model="item.CallbackContent"></el-input>
            </el-form-item>
            <el-form-item label="">
              <el-upload
                :limit="9"
                class="avatar-uploader"
                list-type="picture-card"
                action="#"
                :file-list="item.Attachment"
                :before-upload="(file) => commodityMainbeforeUpload(file, index)"
                :on-remove="(file) => commodityMainRemove(file, index)"
                accept="image/*"
                multiple
              >
                <i class="el-icon-plus avatar-uploader-icon"></i>
                <div slot="file" slot-scope="{ file }" style="height: 100px; width: 100px">
                  <el-image
                    :id="file.uid"
                    :src="file.AttachmentURL"
                    :preview-src-list="item.Attachment.map((val) => val.AttachmentURL)"
                    :z-index="9999"
                    fit="cover"
                    style="height: 100px; width: 100px"
                  ></el-image>
                  <span class="el-upload-list__item-actions">
                    <span class="el-upload-list__item-preview" @click="DialogPreview(file, index)">
                      <i class="el-icon-zoom-in"></i>
                    </span>
                    <span class="el-upload-list__item-preview" @click="commodityMainRemove(file, index)">
                      <i class="el-icon-delete"></i>
                    </span>
                  </span>
                </div>
              </el-upload>
            </el-form-item>
          </div>
        </el-form>
      </el-scrollbar>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false" v-prevent-click size="small">取 消</el-button>
        <el-button :loading="modalLoading" type="primary" v-prevent-click size="small" @click="submitCallBack">保 存</el-button>
      </span>
    </el-dialog>

    <div v-show="false" style="height: 10px; width: 100%">
      <medicalEditor ref="hiddenMedicalEditor"></medicalEditor>
    </div>
  </div>
</template>

<script>
import medicalEditor from '@/components/medicalEditor/medicalEditor.vue';
import API from '@/api/iBeauty/Workbench/callback';
import APIServicer from '@/api/CRM/Servicer/servicerConfig';
import APIProjectTreat from '@/api/iBeauty/HanderCommission/projectTreatHandler';
import APICallback from '@/api/KHS/Setting/callbackConfig';
import APICardProject from '@/api/iBeauty/HanderCommission/cardProjectTreatHandler';
import APIFollowUp from '@/api/iBeauty/Workbench/followUp';
import cusAPI from '@/api/CRM/Customer/customer';
// import Enumerable from "linq";
// import workCustomerDetail from "@/views/iBeauty/Workbench/Component/workbenchCustomerDetail";
import APIUpload from '@/api/Common/uploadAttachment.js';
import utils from '@/components/js/utils.js';
export default {
  name: 'Callback',

  props: {},
  /** 监听数据变化   */
  watch: {},
  /**  引入的组件  */
  components: {
    workCustomerDetail: () => import('@/views/iBeauty/Workbench/Component/workbenchCustomerDetail'),
    medicalEditor,
  },
  /**  Vue 实例的数据对象**/
  data() {
    return {
      customerLevelList: [],
      downloadLoading: false,
      downloadNoDisPlayLoading: false,
      isCustomerBasicFileModify: false,
      isCustomerServicerModify: false,
      isCustomerBasicInformationModify: false,

      customerDetailVisible: false,
      isCustomerPhoneNumberView: false,
      isCustomerPhoneNumberModify: false,
      isCallBack: true,
      isShowCallBack: false,
      batchDialog: false,
      dialogVisible: false,
      modalLoading: false,
      HandlerType: 10,
      CallbackRecordID: '', // 回访ID
      callBackCustomerID: '', // 顾客ID
      CustomerID: null, // 回访顾客
      customerDetail: {}, // 顾客信息
      preview_src_list: [],
      batchNum: 0,
      search: {
        Name: '',
        IsCallBack: false,
        callbackMethodID: '',
        QueryDate: [this.$formatDate(new Date(), 'YYYY-MM-DD'), this.$formatDate(new Date(), 'YYYY-MM-DD')],
        CallbackEntityID: '',
        CallBackBy: '',
        CallbackStatusID: '',
        IsShowOwnCallBack: true,
        ChannelName:"",
        CustomerLevelID:"",
      },
      tableData: [], // 列表数据
      callbackMethod: [], // 回访方式
      callBackStatus: [], // 回访状态
      callBackEntityData: [], // 回访部门
      callBackEmployeeData: [], // 回访人员
      servicerEmployee: [], // 服务人员
      projectConsumptionHandlerList: [], // 项目消耗经手人
      valueCardConsumptionHandlerList: [], // 储值卡消耗经手人
      allEmployee: [], // 所有员工
      customerName: '', // 新建跟进任务搜索
      Servicer: {
        ServicerID: null,
        EmployeeID: null,
      }, // 服务人员
      TreatHandler: {
        ProjectTreatHandlerID: null,
        SavingCardProjectTreatHandlerID: null,
        EmployeeID: null,
      }, // 经手人
      EmployeeID: null, // 固定人员
      ruleForm: {
        CallbackMethodID: '', // 回访方式
        CallbackStatusID: '', // 回访状态
        Content: [],
      },
      rules: {
        CallbackMethodID: [{ required: true, message: '请选择回访方式', trigger: 'change' }],
        CallbackStatusID: [{ required: true, message: '请选择回访状态', trigger: 'change' }],
        ContentInput: [{ required: true, trigger: 'blur', validator: this.ContentValidate }],
      },
      customerID: null,
      circleUrl: 'https://cube.elemecdn.com/3/7c/********************************.png', //默认头像
      paginations: {
        page: 1, // 当前位于哪页
        total: 0, // 总数
        page_size: 10, // 1页显示多少条
        layout: 'total, prev, pager, next,jumper', // 翻页属性
      },
      imgIndex: '',
    };
  },
  /**计算属性  */
  computed: {
    dateChange() {
      return this.rules.Content;
    },
  },
  beforeRouteEnter(to, from, next) {
    next((vm) => {
      vm.isShowCallBack = vm.$permission.permission(to.meta.Permission, 'iBeauty-Workbench-Callback-EntityRang');
      vm.isCustomerPhoneNumberView = vm.$permission.permission(to.meta.Permission, 'iBeauty-Workbench-Callback-CustomerPhoneNumberView');
      vm.isCustomerPhoneNumberModify = vm.$permission.permission(to.meta.Permission, 'iBeauty-Workbench-Callback-CustomerPhoneNumberModify');

      vm.isCustomerBasicInformationModify = vm.$permission.permission(to.meta.Permission, 'iBeauty-Workbench-Callback-CustomerBasicInformationModify');
      vm.isCustomerServicerModify = vm.$permission.permission(to.meta.Permission, 'iBeauty-Workbench-Callback-CustomerServicerModify');
      vm.isCustomerBasicFileModify = vm.$permission.permission(to.meta.Permission, 'iBeauty-Workbench-Callback-CustomerBasicFileModify');
    });
  },
  /**  方法集合  */
  methods: {
    /**    */
    customer_Export(type) {
      let that = this;
      if (type == 'excelNoDisPlayPhone') {
        that.callback_excelNoDisPlayPhone();
      }

      if (type == 'excelDisPlayPhone') {
        that.callback_excelDisPlayPhone();
      }
    },
    ContentValidate(rule, value, callback) {
      if (value == '') {
        callback(new Error('请输入回访记录'));
      } else {
        callback();
      }
    },
    /* 搜索 */
    handleSearch() {
      let that = this;
      that.paginations.page = 1;
      that.getCallBack();
    },
    /* 分页 */
    handleCurrentChange(page) {
      let that = this;
      that.paginations.page = page;
      that.getCallBack();
    },
    /* 显示自己的回访 */
    IsShowOwnCallBackChange() {
      let that = this;
      that.handleSearch();
    },
    /* 获取回访列表自己/权限下 */
    getCallBack() {
      let that = this;
      let params = {
        PageNum: that.paginations.page,
        Name: that.search.Name, //名称
        IsCallback: that.search.IsCallBack, //是否已回访
        CallbackMethodID: that.search.callbackMethodID, //回访方式
        CallbackStatusID: that.search.CallbackStatusID, //回访状态
        CallbackBy: that.search.CallBackBy, //回访人
        CallbackEntityID: that.search.CallbackEntityID, //回访部门
        StartDate: that.search.QueryDate ? this.search.QueryDate[0] : '', //开始时间
        EndDate: that.search.QueryDate ? this.search.QueryDate[1] : '', //结束时间
        IsShowOwnCallback: that.search.IsShowOwnCallBack, //是否显示自己的回访

        ChannelName: that.search.ChannelName, //渠道
        CustomerLevelID: that.search.CustomerLevelID, //会员等级
      };
      if (that.isShowCallBack) {
        that.getCallBackAll(params);
      } else {
        that.getCallBackList(params);
      }
    },
    /* 回访列表 自己 */
    getCallBackList(params) {
      let that = this;
      API.getCallBackList(params).then((res) => {
        if (res.StateCode == 200) {
          that.tableData = res.List;
          that.paginations.total = res.Total;
          that.paginations.page_size = res.PageSize;
        } else {
          this.$message.error({
            message: res.message,
            duration: 2000,
          });
        }
      });
    },
    /* 回访列表 权限下 */
    getCallBackAll(params) {
      let that = this;
      API.getCallBackAll(params).then((res) => {
        if (res.StateCode == 200) {
          that.tableData = res.List;
          that.paginations.total = res.Total;
          that.paginations.page_size = res.PageSize;
        } else {
          this.$message.error({
            message: res.message,
            duration: 2000,
          });
        }
      });
    },
    /* 行点击 */
    rowClick(row) {
      let that = this;
      that.CustomerID = row.CustomerID;
      that.customerDetailVisible = true;
    },
    /* 回访 */
    callbackClick(row) {
      let that = this;
      that.callBackCustomerID = row.CustomerID;
      that.CallbackRecordID = row.ID;
      that.dialogVisible = true;
      that.getCustomerDetail();
      that.detailCallBack();
    },
    /* 回访保存 */
    submitCallBack() {
      let that = this;
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          that.modalLoading = true;
          let params = {
            CallbackRecordID: that.CallbackRecordID,
            CallbackMethodID: that.ruleForm.CallbackMethodID,
            CallbackStatusID: that.ruleForm.CallbackStatusID,
            Content: that.ruleForm.Content,
          };
          params.Content.forEach((item) => {
            delete item.CallbackCycle;
            delete item.CallbackRemark;
            delete item.ProjectName;
          });
          API.createCallBack(params)
            .then((res) => {
              if (res.StateCode == 200) {
                this.$message.success({
                  message: '回访处理成功',
                  duration: 2000,
                });
                that.dialogVisible = false;
                that.getCallBack();
              } else {
                this.$message.error({
                  message: res.message,
                  duration: 2000,
                });
              }
            })
            .finally(function () {
              that.modalLoading = false;
            });
        }
      });
    },
    /* 回访弹出层关闭 */
    closeAddCallBackDialog() {
      let that = this;
      if (this.$refs.ruleForm) {
        this.$refs['ruleForm'].resetFields();
      }
      that.ruleForm = {
        CallbackMethodID: '', // 回访方式
        CallbackStatusID: '', // 回访状态
        Content: [],
      };
    },
    /* 回访详情 */
    detailCallBack() {
      let that = this;
      let params = {
        CallbackRecordID: that.CallbackRecordID,
      };
      API.detailCallBack(params).then((res) => {
        if (res.StateCode == 200) {
          that.ruleForm.Content = res.Data.Content;
          that.ruleForm.CallbackMethodID = res.Data.CallbackMethodID;
          that.ruleForm.CallbackStatusID = res.Data.CallbackStatusID;
        }
      });
    },
    /* 列表批量分配选择 */
    batchSettingSelection(val) {
      let that = this;
      let arr = [];
      let multipleSelection = val;
      that.batchNum = multipleSelection.length;
      for (let i = 0; i <= multipleSelection.length - 1; i++) {
        arr.push(multipleSelection[i].ID);
      }
      that.CallbackRecord = arr;
    },
    /* 批量分配 */
    batchClick() {
      let that = this;
      if (that.batchNum == 0) {
        that.$message.error({
          message: '请选择要批量分配的客户',
          duration: 2000,
        });
      } else {
        that.batchDialog = true;
        that.Servicer = {
          ServicerID: null,
          EmployeeID: null,
        }; // 服务人员
        that.TreatHandler = {
          ProjectTreatHandlerID: null,
          SavingCardProjectTreatHandlerID: null,
          EmployeeID: null,
        }; // 经手人
        that.EmployeeID = null; // 固定人员
      }
    },
    getFocus() {
      let that = this;
      that.allEmployee = [];
    },
    /* 回访人员类型切换 */
    groupChange() {
      let that = this;
      that.Servicer = {
        ServicerID: null,
        EmployeeID: null,
      }; // 服务人员
      that.TreatHandler = {
        ProjectTreatHandlerID: null,
        SavingCardProjectTreatHandlerID: null,
        EmployeeID: null,
      }; // 经手人
      that.EmployeeID = null; // 固定人员
    },
    /* 批量分配保存 */
    submitBatchClick() {
      let that = this;
      if (
        (that.HandlerType == 10 && that.Servicer.ServicerID == null && that.Servicer.EmployeeID == null) ||
        (that.HandlerType == 20 &&
          that.TreatHandler.ProjectTreatHandlerID == null &&
          that.TreatHandler.ServicerID == null &&
          that.SavingCardProjectTreatHandlerID.EmployeeID == null) ||
        (that.HandlerType == 30 && that.EmployeeID == null)
      ) {
        that.$message({
          type: 'info',
          message: '请选择回访人员!',
        });
      } else {
        let params = {
          HandlerType: that.HandlerType,
          ServicerID: null, //服务人员ID
          ProjectTreatHandlerID: null, //项目消耗项目经手人
          SavingCardProjectTreatHandlerID: null, //储值卡消耗项目经手人
          EmployeeID: null,
          CallbackRecord: that.CallbackRecord,
        };
        if (that.HandlerType == 10) {
          params.ServicerID = that.Servicer.ServicerID;
          params.EmployeeID = that.Servicer.EmployeeID;
        } else if (that.HandlerType == 20) {
          params.ProjectTreatHandlerID = that.TreatHandler.ProjectTreatHandlerID;
          params.SavingCardProjectTreatHandlerID = that.TreatHandler.SavingCardProjectTreatHandlerID;
          params.EmployeeID = that.TreatHandler.EmployeeID;
        } else {
          params.EmployeeID = that.EmployeeID;
        }
        API.massDistribution(params).then((res) => {
          if (res.StateCode == 200) {
            this.$message.success({
              message: '批量分配成功',
              duration: 2000,
            });
            that.$refs.multipleTable.clearSelection();
            that.batchDialog = false;
            that.getCallBack();
          }
        });
      }
    },
    /*  */
    /* 上传图片 */
    commodityMainbeforeUpload(file, index) {
      let that = this;
      that.imgIndex = index;
      // const isSize200kb = file.size / 1024 < 200;
      // if (!isSize200kb) {
      //   that.$message.error("上传图片大小不能超过 200kb!");
      //   return false;
      // }
      utils.getImageBase64(file).then((base64) => {
        this.addAttachment(base64).then((AttachmentURL) => {
          that.$nextTick(() => {
            that.ruleForm.Content[index].Attachment.push({
              AttachmentType: '10',
              AttachmentURL: AttachmentURL,
            });
          });
        });
      });
      return false;
    },

    /* 查看大图 */
    DialogPreview(file, index) {
      let that = this;
      that.imgIndex = index;
      document.getElementById(file.uid).click();
    },
    /* 删除图片 */
    commodityMainRemove(file, index) {
      if (file && file.status !== 'success') return;
      let that = this;
      that.imgIndex = index;
      let Index = that.ruleForm.Content[index].Attachment.findIndex((item) => item.AttachmentURL == file.AttachmentURL);
      that.ruleForm.Content[index].Attachment.splice(Index, 1);
    },
    /* 性别状态显示转换 */
    formatGender: function (row) {
      switch (row.Gender) {
        case '1':
          return '男';
        case '2':
          return '女';
        case '0':
          return '未知';
      }
    },
    /*  获取列表服务人员信息  */
    getFirstServiceEmp(ServicerEmployee) {
      if (!ServicerEmployee || ServicerEmployee.length == 0) {
        return '';
      }
      let firstItem = ServicerEmployee[0];
      return firstItem.Name + ':' + this.getServicerEmpNames(firstItem.ServicerEmpList);
    },
    /* 服务人员处理  */
    getServicerEmpNames(ServicerEmpList) {
      if (!ServicerEmpList) {
        return '';
      }
      return ServicerEmpList.map((val) => (val ? val.Name : '')).join(', ');
    },
    /* 获取回访方式 */
    getAllCallbackMethod() {
      let that = this;
      let params = {
        Name: '',
        Active: true,
      };
      APICallback.getAllCallbackMethod(params).then((res) => {
        if (res.StateCode == 200) {
          that.callbackMethod = res.Data;
        }
      });
    },
    /* 获取回访状态 */
    getAllCallBackStatus() {
      let that = this;
      let params = {
        Name: '',
        Active: true,
      };
      APICallback.getAllCallBackStatus(params).then((res) => {
        if (res.StateCode == 200) {
          that.callBackStatus = res.Data;
        }
      });
    },
    /* 获取服务人员列表 */
    getServiceList() {
      let that = this;
      let params = {
        Name: '',
        AddWhetherToShow: '',
        Active: true,
      };
      APIServicer.getServiceList(params).then((res) => {
        if (res.StateCode == 200) {
          that.servicerEmployee = res.Data;
        }
      });
    },
    // 获取项目消耗经手人
    getProjectTreatHandler: function () {
      let that = this;
      let params = {
        Name: '',
        Active: true,
        EntityID: '',
      };
      APIProjectTreat.getProjectTreatHandler(params).then((res) => {
        if (res.StateCode == 200) {
          that.projectConsumptionHandlerList = res.Data;
        }
      });
    },
    // 获取储值卡消耗经手人
    getCardProjectTreatHandler: function () {
      let that = this;
      let params = {
        Name: '',
        Active: true,
        EntityID: '',
      };
      APICardProject.getCardProjectTreatHandler(params).then((res) => {
        if (res.StateCode == 200) {
          that.valueCardConsumptionHandlerList = res.Data;
        }
      });
    },
    /* 获取固定人员 */
    getallEmployee(SearchKey) {
      let that = this;
      let params = {
        SearchKey: SearchKey,
      };
      APIFollowUp.getSearch(params).then((res) => {
        if (res.StateCode == 200) {
          that.allEmployee = res.Data;
        }
      });
    },
    /**  搜索固定人员  */
    searchEmpRemote(query) {
      let that = this;
      that.getallEmployee(query);
    },
    /* 获取顾客信息 */
    getCustomerDetail() {
      const that = this;
      cusAPI.getCustomerDetail({ CustomerID: that.callBackCustomerID }).then((res) => {
        if (res.StateCode == 200) {
          that.customerDetail = res.Data;
          that.dialogVisible = true;
        } else {
          that.$$message.error({
            message: res.Message,
            duration: 2000,
          });
        }
      });
    },
    /* 获取多选框样式 */
    cellClass({ row }) {
      if (row.IsCallback) {
        // 根据IsCallBack属性，添加样式，隐藏复选框
        return 'hide';
      }
    },
    checkboxSelect(row) {
      return row.IsCallback == false; //当满足什么条件时启用
    },
    /* 获取回访部门 */
    getEntity() {
      let that = this;
      let params = {};
      APIFollowUp.getFollowUpEntity(params).then((res) => {
        if (res.StateCode == 200) {
          that.callBackEntityData = res.Data;
        }
      });
    },
    /* 获取回访人员 */
    getEmployee() {
      let that = this;
      let params = {};
      APIFollowUp.getFollowUpEmployee(params).then((res) => {
        if (res.StateCode == 200) {
          that.callBackEmployeeData = res.Data;
        }
      });
    },
    /** 图片上传   */
    async addAttachment(base64) {
      let that = this;
      let params = { AttachmentURL: base64 };
      let res = await APIUpload.addAttachment(params);
      if (res.StateCode == 200) {
        return res.Data.AttachmentURL;
      } else {
        that.$message.error(res.Message);
      }
    },
    /* 导出 */
    callback_excelNoDisPlayPhone() {
      let that = this;
      let params = {
        Name: that.search.Name, //名称
        IsCallback: that.search.IsCallBack, //是否已回访
        CallbackMethodID: that.search.callbackMethodID, //回访方式
        CallbackStatusID: that.search.CallbackStatusID, //回访状态
        CallbackBy: that.search.CallBackBy, //回访人
        CallbackEntityID: that.search.CallbackEntityID, //回访部门
        StartDate: that.search.QueryDate ? this.search.QueryDate[0] : '', //开始时间
        EndDate: that.search.QueryDate ? this.search.QueryDate[1] : '', //结束时间
        IsShowOwnCallback: that.search.IsShowOwnCallBack, //是否显示自己的回访

        ChannelName: that.search.ChannelName, //渠道
        CustomerLevelID: that.search.CustomerLevelID, //会员等级
      };
      that.downloadNoDisPlayLoading = true;
      API.callback_excelNoDisPlayPhone(params)
        .then((res) => {
          this.$message.success({
            message: '正在导出',
            duration: '4000',
          });
          const link = document.createElement('a');
          let blob = new Blob([res], { type: 'application/octet-stream' });
          link.style.display = 'none';
          link.href = URL.createObjectURL(blob);
          link.download = '回访工作台隐藏手机号表.xlsx'; //下载的文件名
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
        })
        .finally(function () {
          that.downloadNoDisPlayLoading = false;
        });
    },
    /* 导出 */
    callback_excelDisPlayPhone() {
      let that = this;
      let params = {
        Name: that.search.Name, //名称
        IsCallback: that.search.IsCallBack, //是否已回访
        CallbackMethodID: that.search.callbackMethodID, //回访方式
        CallbackStatusID: that.search.CallbackStatusID, //回访状态
        CallbackBy: that.search.CallBackBy, //回访人
        CallbackEntityID: that.search.CallbackEntityID, //回访部门
        StartDate: that.search.QueryDate ? this.search.QueryDate[0] : '', //开始时间
        EndDate: that.search.QueryDate ? this.search.QueryDate[1] : '', //结束时间
        IsShowOwnCallback: that.search.IsShowOwnCallBack, //是否显示自己的回访

        ChannelName: that.search.ChannelName, //渠道
        CustomerLevelID: that.search.CustomerLevelID, //会员等级
      };
      that.downloadLoading = true;
      API.callback_excelDisPlayPhone(params)
        .then((res) => {
          this.$message.success({
            message: '正在导出',
            duration: '4000',
          });
          const link = document.createElement('a');
          let blob = new Blob([res], { type: 'application/octet-stream' });
          link.style.display = 'none';
          link.href = URL.createObjectURL(blob);
          link.download = '回访工作台表.xlsx'; //下载的文件名
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
        })
        .finally(function () {
          that.downloadLoading = false;
        });
    },

    /** 查询会员等级   */
    customerLevel_all() {
      let that = this;
      let params = {};
      API.customerLevel_all(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.customerLevelList = res.Data;
          } else {
            that.$message.error(res.Message);
          }
        })
        .catch((fail) => {
          that.$message.error(fail);
        });
    },
  },
  /** 创建实例之前执行函数   */
  beforeCreate() {},
  /**  实例创建完成之后  */
  created() {},
  /**  在挂载开始之前被调用  */
  beforeMount() {},
  /**  实例被挂载后调用  */
  mounted() {
    let that = this;
    that.isShowCallBack = that.$permission.permission(that.$route.meta.Permission, 'iBeauty-Workbench-Callback-EntityRang');
    that.isCustomerPhoneNumberView = that.$permission.permission(that.$route.meta.Permission, 'iBeauty-Workbench-Callback-CustomerPhoneNumberView');
    that.isCustomerPhoneNumberModify = that.$permission.permission(that.$route.meta.Permission, 'iBeauty-Workbench-Callback-CustomerPhoneNumberModify');

    that.isCustomerBasicInformationModify = that.$permission.permission(
      that.$route.meta.Permission,
      'iBeauty-Workbench-Callback-CustomerBasicInformationModify'
    );
    that.isCustomerServicerModify = that.$permission.permission(that.$route.meta.Permission, 'iBeauty-Workbench-Callback-CustomerServicerModify');
    that.isCustomerBasicFileModify = that.$permission.permission(that.$route.meta.Permission, 'iBeauty-Workbench-Callback-CustomerBasicFileModify');

    that.getCallBack();
    that.getAllCallbackMethod();
    that.getAllCallBackStatus();
    that.getServiceList();
    that.getProjectTreatHandler();
    that.getCardProjectTreatHandler();
    that.getEntity();
    that.getEmployee();
    that.customerLevel_all();
    that.$bus.$on(that.$bus.RefreshCallBackList, () => {
      that.getCallBack();
    });
  },
  /**  数据更新 之前 调用  */
  beforeUpdate() {},
  /** 数据更新 完成 调用   */
  updated() {},
  /**  实例销毁之前调用  */
  beforeDestroy() {
    this.$bus.$off(this.$bus.RefreshCallBackList);
  },
  /**  实例销毁后调用  */
  destroyed() {},
};
</script>

<style lang="scss">
.callback {
  // background-color: #f0f0f0;
  // padding: unset;
  // display: flex;
  // height: 100%;
  // max-height: 100%;
  // box-sizing: border-box;
  .workbench {
    background-color: #ffffff;
    box-sizing: border-box;
    padding: 15px;
    min-height: 100%;
    border-right: 8px solid #f0f0f0;
  }
  .customer-detail {
    background-color: #ffffff;
    padding: 15px;
    height: 100%;
    box-sizing: border-box;
  }
  .custom-Dropdown {
    min-width: 500px;
  }
  .custom-customer-descLabel {
    min-width: 80px;
    text-align: right;
    padding-right: 10px;
  }
  .el_scrollbar_height_callback {
    height: 65vh;
    .el-scrollbar__wrap {
      overflow-x: hidden;
    }
  }
  .customer-autocomplete {
    li {
      line-height: normal;
      padding: 7px;

      .name {
        text-overflow: ellipsis;
        overflow: hidden;
      }
      .info {
        font-size: 12px;
        color: #b4b4b4;
      }
      .highlighted .info {
        color: #ddd;
      }
    }
    .el-scrollbar {
      padding-bottom: 10px;
    }
    .tip {
      margin: 0px;
      background-color: #f7f8fa;
    }
    .margin-bottom {
      margin-bottom: 10px;
    }
  }
  .el-upload--picture-card {
    width: 100px;
    height: 100px;
    font-size: 16px !important;
  }
  .el-upload {
    width: 100px;
    height: 100px;
    line-height: 100px;
    font-size: 16px;
  }
  .el-upload-list--picture-card .el-upload-list__item {
    width: 100px;
    height: 100px;
    line-height: 100px;
    font-size: 16px;
  }
  .information {
    background-color: #f7f8fa;
    padding: 8px 8px 8px 8px;
    margin-bottom: 10px;
  }
  .hide .el-checkbox__input {
    display: none !important;
  }
}
</style>
