/**
 * Created by jerry on 2020/02/14.
 * 组织架构api
 */
import * as API from '@/api/index'

export default {
    // 通讯录-组织架构
    getEntityAll:() =>{
        return API.POST('api/entity/all')
    },
    getEntity: params => {
        return API.POST('api/entity/list', params)
    },
    // 通讯录-新增组织架构
    createEntity: params => {
        return API.POST('api/entity/create', params)
    },
    // 通讯录-编辑组织架构
    updateEntity: params => {
        return API.POST('api/entity/update', params)
    },
    // 获取门店其他信息
    getOnlineEntityByID: params => {
      return API.POST('api/onlineEntity/getOnlineEntityByID', params)
  },
    // 通讯录-组织架构调整顺序
    moveEntity: params => {
        return API.POST('api/entity/move', params)
    },
    // 获取跨门店范围
    getCrorssEntity: params => {
        return API.POST('api/entity/getCrorssEntity', params)
    },
}