<template>
  <div class="content_body order_create" v-loading="loading">
    <el-row class="border_bottom">
      <el-col :span="9" class="pad_15 border_right">
        <el-autocomplete
          @click="customerDetailClick"
          popper-class="customer-autocomplete"
          prefix-icon="el-icon-user-solid"
          v-model="customerName"
          style="width: 100%"
          size="small"
          placeholder="请输入客户姓名、手机号、编号查找，无匹配按回车新增"
          :fetch-suggestions="saleCustomerData"
          @select="handleCustomerSelect"
          @keyup.enter.native="handleEnterKey"
          :disabled="customerID != null"
          :trigger-on-focus="false"
          :hide-loading="false"
          :highlight-first-item="true"
          :select-when-unmatched="true"
        >
          <template v-if="isICCard" slot="prepend">
            <el-button size="small" type="primary" icon="el-icon-bank-card" @click="handleReadCard" :loading="isRead"> 读卡</el-button>
          </template>
          <template slot="append">
            <el-button v-if="customerID != null" icon="el-icon-view" @click="customerDetailClick"></el-button>
            <el-button icon="el-icon-delete" @click="removeCustomer"></el-button>
          </template>
          <template slot-scope="{ item }">
            <div class="name">
              {{ item.Name }}
              <el-tag size="mini" v-if="item.CustomerLevelName">{{ item.CustomerLevelName }}</el-tag>
            </div>
            <div class="info">手机号：{{ item.PhoneNumber | hidephone }}</div>
            <div class="info" v-if="item.Code">客户编号：{{ item.Code }}</div>
            <div class="info" v-if="item.EntityName">所属组织：{{ item.EntityName }}</div>
            <div class="info" v-if="item.ChannelName">渠道信息：{{ item.ChannelName }}</div>
          </template>
        </el-autocomplete>
      </el-col>
      <el-col :span="15" class="pad_15">
        <el-col :span="2" style="white-space: nowrap">
          <el-form :inline="true" size="small">
            <el-form-item style="margin-bottom: 0px; height: 32px">
              <div v-if="isReplacementOrder">
                <span slot="label">
                  补单
                  <el-popover placement="bottom-start" trigger="hover">
                    <p>1.补单多用于补开历史订单。</p>
                    <p>2.使用补单后该笔订单的相关营收将计入补录的时间。</p>
                    <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                  </el-popover>
                </span>
                <el-switch v-model="IsSupplement" @change="isSupplementChange"></el-switch>
              </div>
            </el-form-item>
            <el-form-item style="margin-bottom: 0px; height: 32px" v-show="IsSupplement">
              <el-date-picker
                v-model="BillDate"
                type="datetime"
                placeholder="选择日期时间"
                value-format="yyyy-MM-dd HH:mm:ss"
                :picker-options="pickerOptions"
                default-time="9:30:00"
                @change="dateChange"
              ></el-date-picker>
            </el-form-item>
          </el-form>
        </el-col>
        <el-col :span="22" class="text_right" style="white-space: nowrap">
          <el-button type="primary" size="small" plain @click="draftOrderClick()">取单</el-button>
          <el-button v-if="isAgileSell" type="primary" size="small" :plain="!(typeIndex == 0)" @click="typeChange(0)">开单</el-button>
          <el-button type="primary" size="small" :plain="!(typeIndex == 1)" @click="typeChange(1)">开卡</el-button>
          <el-button v-if="isSavingCardRecharge" type="primary" size="small" :plain="!(typeIndex == 4)" @click="typeChange(4)">充值</el-button>
          <el-button type="primary" size="small" :plain="!(typeIndex == 2)" @click="typeChange(2)">消耗</el-button>
          <el-button type="primary" size="small" :plain="!(typeIndex == 3)" @click="typeChange(3)">补欠款</el-button>
          <!-- <el-button type="primary" size="small" :plain="!(typeIndex==4)" @click="typeChange(4)">取单</el-button> -->
        </el-col>
      </el-col>
    </el-row>

    <!--新增 客户-->
    <add-customer title="新增客户" :visible.sync="isAddCustom" :customerPhoneNumber="addCustomerPhoneNumber" @addCustomerSuccess="addCustomerSuccess"></add-customer>

    <!-- 快速开单 -->
    <agileSell
      v-if="isAgileSell"
      v-show="typeIndex == 0"
      :isSupplement="IsSupplement"
      :billDate="BillDate"
      :customerID="customerID"
      :customerFullName="customerFullName"
      :customerPhoneNumber="customerPhoneNumber"
      :SellPermission="SellPermission"
      :TreatPermission="TreatPermission"
      ref="agileSellComponents"
    ></agileSell>
    <!-- 销售 -->
    <sell
      v-show="typeIndex == 1"
      :isSupplement="IsSupplement"
      :billDate="BillDate"
      :customerID="customerID"
      :customerFullName="customerFullName"
      :customerPhoneNumber="customerPhoneNumber"
      :SellPermission="SellPermission"
      ref="sellComponents"
    ></sell>

    <!-- 消耗 -->
    <consume
      v-show="typeIndex == 2"
      :isSupplement="IsSupplement"
      :billDate="BillDate"
      :customerID="customerID"
      :customerFullName="customerFullName"
      :customerPhoneNumber="customerPhoneNumber"
      :TreatPermission="TreatPermission"
      ref="consumeComponents"
    ></consume>
    <!--补欠款-->
    <workOffArrears
      v-show="typeIndex == 3"
      :isSupplement="IsSupplement"
      :billDate="BillDate"
      :customerID="customerID"
      :customerFullName="customerFullName"
      :customerPhoneNumber="customerPhoneNumber"
      :SellPermission="SellPermission"
      ref="workOffArrearsComponents"
    >
    </workOffArrears>
    <!-- 储值卡充值  -->
    <savingCardRecharge
      v-show="typeIndex == 4"
      :isSupplement="IsSupplement"
      :billDate="BillDate"
      :customerID="customerID"
      :customerFullName="customerFullName"
      :customerPhoneNumber="customerPhoneNumber"
      :SellPermission="SellPermission"
      ref="savingCardRechargeComponents"
    >
    </savingCardRecharge>
    <!-- 取单 -->
    <draftOrder :isTreatBilling="true" :isSaleBilling="true" @treatTakeOrder="treatTakeOrder" @saleTakeOrder="saleTakeOrder" ref="draftOrderRef"></draftOrder>

    <customer-detail :customerID="customerID" :visible.sync="customerDetailVisible"></customer-detail>
  </div>
</template>

<script>
import API from '@/api/iBeauty/Order/saleGoods';
import LimitAPI from '@/api/KHS/Setting/sealingAccount.js';
import AgileSell from '@/components/iBeauty/Order/agileSell';
import Sell from '@/components/iBeauty/Order/sell';
import savingCardRecharge from '@/components/iBeauty/Order/savingCardRecharge';
import Consume from '@/components/iBeauty/Order/consume';
import WorkOffArrears from '@/components/iBeauty/Order/workOffArrears';
import draftOrder from '@/components/iBeauty/Order/draftOrder';
import customerDetail from '@/views/CRM/Customer/Components/CustomerDetail/CustomerDetail';

import APICustomer from '@/api/CRM/Customer/customer';
import validate from '@/components/js/validate.js';
import permission from '@/components/js/permission.js';
import '@riophae/vue-treeselect/dist/vue-treeselect.css';
import axios from 'axios';
const dayjs = require('dayjs');
import 'dayjs/locale/zh-cn'; // 导入本地化语言
dayjs.locale('zh-cn');

import addCustomer from '@/views/CRM/Customer/Components/CustomerDetail/addCustomer.vue';
export default {
  name: 'Bill',
  components: {
    AgileSell,
    Sell,
    Consume,
    WorkOffArrears,
    savingCardRecharge,
    draftOrder,
    customerDetail,
    // Treeselect,
    addCustomer,
  },
  data() {
    return {
      IsHaveRestriction: false, //是否限制补单时间
      DeadlineDate: '', //限制补单时间的开始时间
      isSavingCardRecharge: false,
      isShowChannel: false, //是否展示渠道
      customerDetailVisible: false,
      draftOrderVisible: false, // 取单

      regionDataSelArr: [], //城市已选择
      isReplacementOrder: false,
      isAgileSell: false,
      SellPermission: {
        ModifyPrices_SaleGeneralCard: false,
        ModifyPrices_SalePackageCard: false,
        ModifyPrices_SaleProduct: false,
        ModifyPrices_SaleProject: false,
        // ModifyPrices_SaleSavingCard:false,
        ModifyPrices_SaleTimeCard: false,
        isSalePending: false,
        isSaleSettle: false,
      },
      TreatPermission: {
        ModifyPrices_TreatSavingCard: false,
        isTreatPending: false,
        isTreatSettle: false,
      },
      loading: false,
      isAddCustom: false,
      isRead: false,
      isInit: false,
      isICCard: false, //读写卡权限
      IsSupplement: false,
      BillDate: null,
      pickerOptions: {
        disabledDate(time) {
          return (
            time.getTime() > Date.now()
            // ||
            // time.getTime() < Date.now() - 3600 * 1000 * 24 * 7
          );
        },
      },
      customerName: '',
      customerID: null,
      customerFullName: '',
      customerPhoneNumber: '',
      typeIndex: 0,
      customerServicer: [], //服务人员

      modalLoading: false,
      customer: {
        Name: '',
        PhoneNumber: '',
        Gender: '2',
        CustomerSourceID: null,
        // EmployeeID: [],
        CustomerLevelID: '',
        Introducer: '',
        Code: '',
        BirthdayType: 10,
        Birthday: '',
        ProvinceCode: '',
        CityCode: '',
        AreaCode: '',
        Job: '',
        Address: '',
        IdentityCard: '',
        Remark: '',
        ChannelID: null,
        IsMember: false,
        IsLockMemberLevel: false,
      },
      customerRoules: {
        Name: [{ required: true, message: '请输入客户名称', trigger: 'blur' }],
        PhoneNumber: [
          {
            required: true,
            validator: validate.validPhoneNumber,
            trigger: 'blur',
          },
        ],
        Gender: [{ required: true, message: '请选择客户性别', trigger: 'change' }],
        CustomerSourceID: [{ required: true, message: '请选择客户来源', trigger: 'change' }],
        // EmployeeID: [
        //   { required: true, message: "请选择顾问", trigger: "change" },
        // ],
        CustomerLevelID: [{ required: true, message: '请选择客户等级', trigger: 'change' }],
        Code: [{ required: true, message: '请输入客户编号', trigger: 'blur' }],
      },
      employee: [], //营销顾问
      customerLevel: [], //顾客等级
      customerSource: [], //顾客来源
      customerIntroducer: [], //顾客介绍人
      IntroducerPageNum: '',
      IntroducerTotal: '',
      addCustomerPhoneNumber: '', // 新增时 顾客的手机号
      channelList: [], // 渠道来源
    };
  },
  beforeRouteEnter(to, from, next) {
    next((vm) => {
      vm.SellPermission.ModifyPrices_SaleGeneralCard = permission.permission(to.meta.Permission, 'iBeauty-Order-Bill-SaleGeneralCard-ModifyPrices');
      vm.SellPermission.ModifyPrices_SalePackageCard = permission.permission(to.meta.Permission, 'iBeauty-Order-Bill-SalePackageCard-ModifyPrices');
      vm.SellPermission.ModifyPrices_SaleProduct = permission.permission(to.meta.Permission, 'iBeauty-Order-Bill-SaleProduct-ModifyPrices');
      vm.SellPermission.ModifyPrices_SaleProject = permission.permission(to.meta.Permission, 'iBeauty-Order-Bill-SaleProject-ModifyPrices');
      vm.SellPermission.ModifyPrices_SaleTimeCard = permission.permission(to.meta.Permission, 'iBeauty-Order-Bill-SaleTimeCard-ModifyPrices');
      vm.SellPermission.isSalePending = permission.permission(to.meta.Permission, 'iBeauty-Order-Bill-SalePending');
      vm.SellPermission.isSaleSettle = permission.permission(to.meta.Permission, 'iBeauty-Order-Bill-SaleSettle');

      vm.TreatPermission.ModifyPrices_TreatSavingCard = permission.permission(to.meta.Permission, 'iBeauty-Order-Bill-TreatSavingCard-ModifyPrices');
      vm.TreatPermission.isTreatPending = permission.permission(to.meta.Permission, 'iBeauty-Order-Bill-TreatPending');
      vm.TreatPermission.isTreatSettle = permission.permission(to.meta.Permission, 'iBeauty-Order-Bill-TreatSettle');
      vm.TreatPermission.isTreatConsumable = permission.permission(to.meta.Permission, 'iBeauty-Order-Bill-TreatConsumable');
      vm.isSavingCardRecharge = permission.permission(to.meta.Permission, 'iBeauty-Order-Bill-SavingCardRecharge');
      // vm.isCreateMember = vm.$permission.permission(to.meta.Permission, "iBeauty-Order-Bill-CreateMember");

      // 是否显示开单
      vm.isAgileSell = permission.permission(to.meta.Permission, 'iBeauty-Order-Bill-AgileSell');
      // 是否显示补单
      vm.isReplacementOrder = permission.permission(to.meta.Permission, 'iBeauty-Order-Bill-ReplacementOrder');
      vm.isShowChannel = vm.$permission.permission(to.meta.Permission, 'iBeauty-Customer-Customer-Channel');
      if (to.params.customerID != undefined) {
        vm.typeIndex = vm.isAgileSell ? 0 : 1;
        APICustomer.getCustomerInfo({ ID: to.params.customerID }).then(function (res) {
          if (res.StateCode == 200) {
            vm.customerID = res.Data.ID;
            vm.customerFullName = res.Data.Name;
            vm.customerPhoneNumber = res.Data.PhoneNumber;
            var reg = /^(\d{3})\d{4}(\d{4})$/;
            if (res.Data.PhoneNumber) {
              vm.customerName = res.Data.Name + '【' + res.Data.PhoneNumber.replace(reg, '$1****$2') + '】';
            } else {
              vm.customerName = res.Data.Name;
            }
            vm.customerChange();
          } else {
            this.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        });
      }
    });
  },

  methods: {
    /**  消耗 取单  */
    treatTakeOrder(data) {
      let that = this;
      let BillDate = data.BillDate;
      let IsSupplement = !dayjs().isSame(dayjs(BillDate), 'day');
      if (that.IsHaveRestriction && dayjs(BillDate).isBefore(dayjs(that.DeadlineDate, 'day'))) {
        that.$message.error('当前单据时间已经关账，请删除后重新选择');
        return;
      }

      var filter_hidephone = this.$options.filters['hidephone'];
      that.customerFullName = data.Name;
      that.customerID = data.CustomerID;

      if (data.PhoneNumber) {
        that.customerName = data.Name + '【' + filter_hidephone(data.PhoneNumber) + '】';
      } else {
        that.customerName = data.Name;
      }
      that.customerPhoneNumber = data.PhoneNumber;
      that.typeIndex = 2;
      that.$nextTick(() => {
        that.typeChange(that.typeIndex, IsSupplement, BillDate);
        that.$refs.consumeComponents.treatTakeOrder(data);
      });
    },
    /**  销售  取单  */
    saleTakeOrder(data) {
      let that = this;
      let BillDate = data.BillDate;
      if (that.IsHaveRestriction && dayjs(BillDate).isBefore(dayjs(that.DeadlineDate, 'day'))) {
        that.$message.error('当前单据时间已经关账，请删除后重新选择');
        return;
      }

      let IsSupplement = !dayjs().isSame(dayjs(BillDate), 'day');
      var filter_hidephone = this.$options.filters['hidephone'];
      that.customerFullName = data.Name;
      that.customerID = data.CustomerID;
      if (data.PhoneNumber) {
        that.customerName = data.Name + '【' + filter_hidephone(data.PhoneNumber) + '】';
      } else {
        that.customerName = data.Name;
      }
      that.customerPhoneNumber = data.PhoneNumber;
      switch (data.BillType) {
        case '10':
          {
            that.typeIndex = 1;
            that.$nextTick(() => {
              that.typeChange(that.typeIndex, IsSupplement, BillDate);
              that.$refs.sellComponents.saleTakeOrder(data);
            });
          }

          break;
        case '30':
          {
            that.typeIndex = 3;
            that.$nextTick(() => {
              that.typeChange(that.typeIndex, IsSupplement, BillDate);
              that.$refs.workOffArrearsComponents.arrearTakeOrder(data);
            });
          }

          break;
        case '40':
          {
            that.typeIndex = 4;
            that.$nextTick(() => {
              that.typeChange(that.typeIndex, IsSupplement, BillDate);
              that.$refs.savingCardRechargeComponents.rechargeSavingTakeOrder(data);
            });
          }
          break;
      }
    },
    /**  修改是否新增为会员  */
    isMemberChange(val) {
      let that = this;
      if (val) {
        that.$nextTick(() => {
          that.customer.CustomerLevelID = that.customerLevel[0] ? that.customerLevel[0].ID : null;
        });
      } else {
        that.customer.CustomerLevelID = null;
      }
    },
    /**    */
    changeServicer() {
      this.$forceUpdate();
    },

    /**   查看顾客详情   */
    customerDetailClick() {
      let that = this;
      that.customerDetailVisible = true;
    },
    /**  取点按钮事件  */
    draftOrderClick() {
      let that = this;
      that.$refs.draftOrderRef.showDraftOrderVisible();
    },
    // 读卡
    handleReadCard() {
      var filter_hidephone = this.$options.filters['hidephone'];
      const that = this;
      const params = {
        OperationType: 'Read',
      };
      that.isRead = true;
      axios
        .post('https://127.0.0.1:16888/card', params)
        .then((resCard) => {
          if (resCard.data.StateCode == 200) {
            API.getDetailByCode({ Code: resCard.data.CardID })
              .then((res) => {
                if (res.StateCode == 200) {
                  if (res.Data == null) {
                    that.$message.error({
                      message: '卡号：' + resCard.data.CardID + '，未查找到顾客户信息。',
                      duration: 2000,
                    });
                  } else {
                    that.$message.success({
                      message: '读取成功',
                      duration: 2000,
                    });

                    // that.customerName = res.Data.Name + "【" + filter_hidephone(res.Data.PhoneNumber) + "】";
                    if (res.Data.PhoneNumber) {
                      that.customerName = res.Data.Name + '【' + filter_hidephone(res.Data.PhoneNumber) + '】';
                    } else {
                      that.customerName = res.Data.Name;
                    }

                    that.customerID = res.Data.ID;
                    that.customerFullName = res.Data.Name;
                    that.customerPhoneNumber = res.Data.PhoneNumber;
                    this.customerChange();
                  }
                } else {
                  that.$message.error({
                    message: res.Message,
                    duration: 2000,
                  });
                }
              })
              .finally(() => {
                that.isRead = false;
              });
          } else {
            that.$message.error({
              message: resCard.data.Message,
              duration: 2000,
            });
            that.isRead = false;
          }
        })
        .catch(() => {});
    },
    // 写入编号
    handleWriteCode() {
      const that = this;
      if (!that.customer.Code) {
        that.$message.error({
          message: '请输入编号',
        });
        return;
      }
      const reg = /^\w+$/;
      if (!reg.test(that.customer.Code)) return;
      that.isInit = true;
      that.isCode(that.customer.Code).then((res) => {
        if (res) {
          const params = {
            OperationType: 'Init',
            CardID: that.customer.Code,
          };
          axios
            .post('https://127.0.0.1:16888/card', params)
            .then((res) => {
              if (res.data.StateCode == 200) {
                that.$message.success({
                  message: '卡写入成功',
                  duration: 2000,
                });
              } else {
                that.$message.error({
                  message: res.data.Message,
                  duration: 2000,
                });
              }
            })
            .finally(() => {
              that.isInit = false;
            });
        } else {
          that.$message.error({
            message: '系统已存在此卡号。',
          });
          that.isInit = false;
        }
      });

      return;
    },
    // 确认编号是否有效
    isCode(code) {
      const that = this;
      let promise = new Promise(function (resolve, reject) {
        try {
          API.getDetailByCode({ Code: code }).then((res) => {
            if (res.StateCode == 200) {
              if (res.Data == null) {
                resolve(true);
              } else {
                resolve(false);
              }
            } else {
              that.$message.error({
                message: res.Message,
                duration: 2000,
              });
            }
          });
        } catch (err) {
          reject(err);
        }
      });
      return promise;
    },
    // 顾客
    saleCustomerData: function (queryString, cb) {
      var that = this;
      that.loading = true;
      var params = {
        Name: queryString ? queryString : '',
      };
      API.getSaleCustomer(params)
        .then((res) => {
          if (res.StateCode == 200) {
            cb(res.Data);
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },
    handleCustomerSelect(item) {
      var filter_hidephone = this.$options.filters['hidephone'];
      if (item.ID != undefined) {
        this.customerID = item.ID;
        this.customerFullName = item.Name;
        this.customerPhoneNumber = item.PhoneNumber;

        if (item.PhoneNumber) {
          this.customerName = item.Name + '【' + filter_hidephone(item.PhoneNumber) + '】';
        } else {
          this.customerName = item.Name;
        }
        this.customerChange();
      } else {
        if (/^1[3456789]\d{9}$/.test(this.customerName)) {
          this.addCustomerPhoneNumber = this.customerName;
        }
        this.addNewCustomer();
      }
    },
    // 处理回车键事件
    handleEnterKey() {
      var that = this;
      // 如果已经选择了客户，不处理回车事件
      if (that.customerID != null) {
        return;
      }
      // 如果输入框为空，不处理
      if (!that.customerName || that.customerName.trim() === '') {
        return;
      }
      // 触发新增客户
      if (/^1[3456789]\d{9}$/.test(that.customerName)) {
        that.addCustomerPhoneNumber = that.customerName;
      }
      that.addNewCustomer();
    },
    removeCustomer() {
      this.customerID = null;
      this.customerFullName = '';
      this.customerPhoneNumber = '';
      this.customerName = '';
      this.customerChange();
    },
    /**  清除上个客户已经选择的商品  */
    clearCustomerSelectData() {
      let that = this;
      if (that.$refs.agileSellComponents) {
        that.$refs.agileSellComponents.payAmountData();
        that.$refs.agileSellComponents.deductionAllReset();
        that.$refs.agileSellComponents.clearAgileSellData();
      }

      if (that.$refs.sellComponents) {
        that.$refs.sellComponents.savingCardAllGoods = [];
        that.$refs.sellComponents.savingCardSomeGoods = [];
        that.$refs.sellComponents.deductionAllReset();
        that.$refs.sellComponents.payAmountData();
        that.$refs.sellComponents.clearAllDateCardItem();
      }
      if (that.$refs.consumeComponents) {
        that.$refs.consumeComponents.clearConsumeNetWorkData();
      }

      if (that.$refs.workOffArrearsComponents) {
        that.$refs.workOffArrearsComponents.clearConsumeNetWorkData();
      }

      if (that.$refs.savingCardRechargeComponents) {
        that.$refs.savingCardRechargeComponents.clearRechargeSavingCardAccountData();
      }
    },
    // 会员更改
    customerChange: function () {
      var that = this;
      that.clearCustomerSelectData();
      switch (that.typeIndex) {
        case 0:
          {
            //快速开单
            if (that.customerID != null) {
              that.$nextTick(() => {
                that.$refs.agileSellComponents.changMemberOrType();
              });
            } else {
              that.$refs.agileSellComponents.payAmountData();
              that.$refs.agileSellComponents.deductionAllReset();
              that.$refs.agileSellComponents.clearAgileSellData();
            }
          }
          break;

        case 1:
          {
            //销售
            if (that.customerID != null) {
              that.$nextTick(() => {
                // that.$refs.sellComponents.customerID = that.customerID;
                that.$refs.sellComponents.savingCardAllGoodsData();
                that.$refs.sellComponents.savingCardSomeGoodsData();
              });
            } else {
              that.$refs.sellComponents.savingCardAllGoods = [];
              that.$refs.sellComponents.savingCardSomeGoods = [];
              that.$refs.sellComponents.deductionAllReset();
              that.$refs.sellComponents.payAmountData();
            }
          }
          break;

        case 2:
          {
            //消耗
            if (that.customerID != null) {
              that.$nextTick(() => {
                // that.$refs.consumeComponents.customerID = that.customerID;
                that.$refs.consumeComponents.changMemberOrType();
              });
            } else {
              that.$refs.consumeComponents.clearConsumeNetWorkData();
            }
          }

          break;

        case 3:
          {
            //补尾款
            if (that.customerID != null) {
              // that.$refs.workOffArrearsComponents.customerID = that.customerID;
              that.$nextTick(() => {
                that.$refs.workOffArrearsComponents.changeWorkOffArrear();
              });
            } else {
              that.$refs.workOffArrearsComponents.clearConsumeNetWorkData();
            }
          }
          break;
        case 4:
          {
            //充值
            if (that.customerID != null) {
              // that.$refs.savingCardRechargeComponents.customerID = that.customerID;
              that.$nextTick(() => {
                that.$refs.savingCardRechargeComponents.getRechargeSavingCardAccount();
              });
            } else {
              that.$refs.savingCardRechargeComponents.clearRechargeSavingCardAccountData();
            }
          }
          break;
      }
    },
    //补单更改
    isSupplementChange: function () {
      var that = this;
      if (!that.IsSupplement) {
        that.dateChange();
      } else {
        that.BillDate = null;
        if (that.typeIndex == 2) {
          that.$refs.consumeComponents.clearConsumeNetWorkData();
        }
      }
    },
    // 时间更改
    dateChange: function () {
      var that = this;
      //销售
      that.$refs.sellComponents.billDate = that.BillDate;
      if (that.BillDate != null) {
        if (that.$refs.sellComponents) {
          that.$refs.sellComponents.billDate = that.BillDate;
          // that.$refs.sellComponents.goodsDataInit();
          // that.$refs.sellComponents.goodsData();
          // that.$refs.sellComponents.generalCardData();
          // that.$refs.sellComponents.timeCardData();
          // that.$refs.sellComponents.savingCardData();
          // that.$refs.sellComponents.packageCardData();

          that.$refs.sellComponents.getSaleGoodsGoodsType();
          that.$refs.sellComponents.getSaleGoodsProjectCategory();
          that.$refs.sellComponents.getSaleGoodsProductCategory();
          that.$refs.sellComponents.getSaleGoodsGeneralCardCategory();
          that.$refs.sellComponents.getSaleGoodsTimeCardCategory();
          that.$refs.sellComponents.getSaleGoodsSavingCardCategoryd();
          that.$refs.sellComponents.getSaleGoodsPackageCardCategory();

          that.$refs.sellComponents.clearDateCardItem();
        }

        //消耗
        if (that.$refs.consumeComponents) {
          that.$refs.consumeComponents.changMemberOrType();
          that.$refs.consumeComponents.clearConsumeNetWorkData();
          that.$refs.consumeComponents.clearConsumeSelectItem();
        }
        //补尾款
        if (that.$refs.workOffArrearsComponents && that.customerID != null) {
          that.$refs.workOffArrearsComponents.customerID = that.customerID;
          that.$refs.workOffArrearsComponents.billDate = that.BillDate;
          that.$refs.workOffArrearsComponents.changeWorkOffArrear();
          that.$refs.workOffArrearsComponents.clearSelectTimeCard();
        }
        if (that.$refs.savingCardRechargeComponents) {
          that.$refs.savingCardRechargeComponents.clearSelectSavingCard();
        }
      }
    },
    /**  切换 开单销售  */
    typeChange: function (index, IsSupplement = false, BillDate = '') {
      var that = this;
      that.typeIndex = index;
      that.BillDate = BillDate;
      that.IsSupplement = IsSupplement;
      switch (index) {
        case 0: //开单
          if (that.customerID != null) {
            that.$refs.agileSellComponents.changMemberOrType();
            that.$refs.agileSellComponents.SellPermission = that.SellPermission;
          }
          break;
        case 1: //销售
          if (that.customerID != null) {
            that.$nextTick(() => {
              // that.$refs.sellComponents.deductionAllReset();
              that.$refs.sellComponents.payAmountData();
              that.$refs.sellComponents.savingCardAllGoodsData();
              that.$refs.sellComponents.savingCardSomeGoodsData();
              that.$refs.sellComponents.SellPermission = that.SellPermission;
            });
          }
          break;
        case 2: //消耗
          that.$nextTick(() => {
            that.$refs.consumeComponents.TreatPermission = that.TreatPermission;
            that.$refs.consumeComponents.changMemberOrType();
          });

          break;
        case 3: //补尾款
          that.$refs.workOffArrearsComponents.customerID = that.customerID;
          that.$refs.workOffArrearsComponents.changeWorkOffArrear();
          break;
        case 4: //充值
          that.$refs.savingCardRechargeComponents.getRechargeSavingCardAccount();
          break;
      }
    },
    // 新增顾客
    addNewCustomer: function () {
      var that = this;
      that.isAddCustom = true;
    },
    /**   新增客户成功 */
    addCustomerSuccess(info) {
      var filter_hidephone = this.$options.filters['hidephone'];
      let that = this;
      that.customerID = info.ID;
      that.customerFullName = info.Name;
      that.customerPhoneNumber = info.PhoneNumber;
      if (info.PhoneNumber) {
        that.customerName = info.Name + '【' + filter_hidephone(info.PhoneNumber) + '】';
      } else {
        that.customerName = info.Name;
      }
      that.addCustomerPhoneNumber = ''; // 清空新增客户时的手机号
      that.customerChange();
    },
    /**  是否限制开单时间  */
    async sealingAccount_getReplacementOrderRestriction() {
      let that = this;
      try {
        let params = {};
        let res = await LimitAPI.sealingAccount_getReplacementOrderRestriction(params);
        if (res.StateCode == 200) {
          that.IsHaveRestriction = res.Data.IsHaveRestriction;
          that.DeadlineDate = res.Data.Deadline;
          if (res.Data.IsHaveRestriction) {
            let DeadlineTime = dayjs(res.Data.Deadline).valueOf();
            that.pickerOptions.disabledDate = (time) => {
              return time.getTime() > Date.now() || DeadlineTime > time.getTime();
            };
          } else {
            that.pickerOptions.disabledDate = (time) => {
              return time.getTime() > Date.now();
            };
          }
        } else {
          that.$message.error(res.Message);
        }
      } catch (error) {
        that.$message.error(error);
      }
    },
  },
  mounted() {
    var that = this;
    that.SellPermission.ModifyPrices_SaleGeneralCard = permission.permission(that.$route.meta.Permission, 'iBeauty-Order-Bill-SaleGeneralCard-ModifyPrices');
    that.SellPermission.ModifyPrices_SalePackageCard = permission.permission(that.$route.meta.Permission, 'iBeauty-Order-Bill-SalePackageCard-ModifyPrices');
    that.SellPermission.ModifyPrices_SaleProduct = permission.permission(that.$route.meta.Permission, 'iBeauty-Order-Bill-SaleProduct-ModifyPrices');
    that.SellPermission.ModifyPrices_SaleProject = permission.permission(that.$route.meta.Permission, 'iBeauty-Order-Bill-SaleProject-ModifyPrices');

    that.SellPermission.ModifyPrices_SaleTimeCard = permission.permission(that.$route.meta.Permission, 'iBeauty-Order-Bill-SaleTimeCard-ModifyPrices');
    that.TreatPermission.ModifyPrices_TreatSavingCard = permission.permission(that.$route.meta.Permission, 'iBeauty-Order-Bill-TreatSavingCard-ModifyPrices');
    that.isShowChannel = that.$permission.permission(that.$route.meta.Permission, 'iBeauty-Customer-Customer-Channel');
    // 是否显示开单
    that.isAgileSell = permission.permission(that.$route.meta.Permission, 'iBeauty-Order-Bill-AgileSell');
    // 是否显示补单
    that.isReplacementOrder = permission.permission(that.$route.meta.Permission, 'iBeauty-Order-Bill-ReplacementOrder');
    // 是否读卡写卡
    that.isICCard = permission.permission(that.$route.meta.Permission, 'iBeauty-Order-Bill-ICCard');

    that.SellPermission.isSalePending = permission.permission(that.$route.meta.Permission, 'iBeauty-Order-Bill-SalePending');
    that.SellPermission.isSaleSettle = permission.permission(that.$route.meta.Permission, 'iBeauty-Order-Bill-SaleSettle');

    that.TreatPermission.isTreatPending = permission.permission(that.$route.meta.Permission, 'iBeauty-Order-Bill-TreatPending');
    that.TreatPermission.isTreatSettle = permission.permission(that.$route.meta.Permission, 'iBeauty-Order-Bill-TreatSettle');
    that.TreatPermission.isTreatConsumable = permission.permission(that.$route.meta.Permission, 'iBeauty-Order-Bill-TreatConsumable');
    // that.isCreateMember = that.$permission.permission(that.$route.meta.Permission, "iBeauty-Order-Bill-CreateMember");
    that.isSavingCardRecharge = permission.permission(that.$route.meta.Permission, 'iBeauty-Order-Bill-SavingCardRecharge');
    that.typeIndex = that.isAgileSell ? 0 : 1;
    that.sealingAccount_getReplacementOrderRestriction();
  },

  beforeDestroy() {},
};
</script>

<style lang="scss">
.order_create {
  padding: 0;
  height: 100%;
}

.customer-autocomplete {
  li {
    line-height: normal;
    padding: 7px;

    .name {
      text-overflow: ellipsis;
      overflow: hidden;
    }

    .info {
      font-size: 12px;
      color: #b4b4b4;
    }

    .highlighted .info {
      color: #ddd;
    }
  }

  .tip {
    margin: 0px;
    background-color: #f7f8fa;
  }

  .margin-bottom {
    margin-bottom: 10px;
  }
}

.vue-treeselect__control {
  height: 32px;

  .vue-treeselect__value-container {
    min-width: 215px;
  }
}

.vue-treeselect__placeholder,
.vue-treeselect__single-value {
  line-height: 32px;
}

.suoCustomerLeve {
  position: absolute;
  top: 32px;
  left: 50px;
}
</style>
