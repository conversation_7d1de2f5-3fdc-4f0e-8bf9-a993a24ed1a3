<template>
  <div class="EmployeeTreatStatistics content_body_nopadding" v-loading="loading">
    <el-tabs type="border-card" v-model="activeName" @tab-click="changeActiveName">
      <el-tab-pane label="员工消耗商品统计" name="goods">
        <span slot="label">
          员工消耗商品统计
          <el-popover placement="top-start" width="200" trigger="hover">
            <p>包含：项目、产品</p>
            <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
          </el-popover>
        </span>
        <div class="nav_header">
          <el-form :inline="true" size="small" :model="goodStatisticsSearchData" @submit.native.prevent>
            <el-form-item label="时间筛选">
              <el-date-picker v-model="goodStatisticsSearchData.QueryDate" :picker-options="pickerOptions" unlink-panels
                type="daterange" range-separator="至" value-format="yyyy-MM-dd" start-placeholder="开始日期"
                end-placeholder="结束日期" @change="handleSalePerformanceCommissionSearch"></el-date-picker>
            </el-form-item>
            <el-form-item label="员工姓名">
              <el-input v-model="goodStatisticsSearchData.EmployeeName" clearable
                @keyup.enter.native="handleSalePerformanceCommissionSearch"
                @clear="handleSalePerformanceCommissionSearch" placeholder="请输入员工姓名"></el-input>
            </el-form-item>
            <el-form-item v-if="storeEntityList.length > 1" label="所属门店">
              <el-select v-model="goodStatisticsSearchData.EntityID" clearable filterable placeholder="请选择门店"
                :default-first-option="true" @change="handleSalePerformanceCommissionSearch">
                <el-option v-for="item in storeEntityList" :key="item.ID" :label="item.EntityName"
                  :value="item.ID"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="员工职务">
              <el-select v-model="goodStatisticsSearchData.JobID" filterable placeholder="选择员工职务"
                @change="handleSalePerformanceCommissionSearch" clearable>
                <el-option v-for="item in jobTypeList" :key="item.ID" :label="item.JobName"
                  :value="item.ID"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" v-prevent-click @click="handleSalePerformanceCommissionSearch">搜索</el-button>
            </el-form-item>
            <el-form-item>
              <el-button v-if="isTreatGoodsStatisticsExport" type="primary" size="small" :loading="downloadLoading"
                @click="employeeTreatStatement_goodStatisticsExcel">导出</el-button>
            </el-form-item>
          </el-form>
        </div>
        <el-table size="small" show-summary :summary-method="gettableDataSummaries"
          :data="tableDataTreatGoodsStatistics">
          <el-table-column prop="EmployeeName" label="员工姓名" width="80"></el-table-column>
          <el-table-column prop="EmployeeID" label="员工编号" ></el-table-column>
          <el-table-column prop="JobName" label="职务" width="150"></el-table-column>
          <el-table-column prop="EntityName" label="所属组织" width="180"></el-table-column>
          <el-table-column prop="TreatQuantity" label="消耗数量" width="90" align="right">
            <template slot="header">
              消耗数量
              <el-popover placement="top-start" trigger="hover">
                <p>1.订单完成时间在统计时间内，员工消耗商品的数量</p>
                <p>2.消耗数量 = 商品消耗数量 x 经手人比例</p>
                <el-button type="text" style="color: #dcdfe6; padding: 0" icon="el-icon-info"
                  slot="reference"></el-button>
              </el-popover>
            </template>
          </el-table-column>

          <el-table-column label="消耗金额" align="center">
            <template slot="header">
              消耗金额
              <el-popover placement="top-start" trigger="hover">
                <p>1.订单完成时间在统计时间内，员工消耗商品（含赠送）金额</p>
                <p>2.金额包含：现金金额、卡抵扣金额、赠送卡抵扣金额、赠送金额</p>
                <p>3.金额 = 商品消耗金额 x 经手人比例</p>
                <el-button type="text" style="color: #dcdfe6; padding: 0" icon="el-icon-info"
                  slot="reference"></el-button>
              </el-popover>
            </template>
            <el-table-column prop="TreatPayAmount" label="现金金额" align="right">
              <template slot-scope="scope">
                {{ scope.row.TreatPayAmount | toFixed | NumFormat }}
              </template>
            </el-table-column>
            <el-table-column prop="TreatCardDeductionAmount" label="卡抵扣金额" align="right">
              <template slot-scope="scope">
                {{ scope.row.TreatCardDeductionAmount | toFixed | NumFormat }}
              </template>
            </el-table-column>
            <el-table-column prop="TreatLargessCardDeductionAmount" label="赠送卡抵扣" align="right">
              <template slot-scope="scope">
                {{ scope.row.TreatLargessCardDeductionAmount | toFixed | NumFormat }}
              </template>
            </el-table-column>
            <el-table-column prop="TreatLargessAmount" label="赠送金额" align="right">
              <template slot-scope="scope">
                {{ scope.row.TreatLargessAmount | toFixed | NumFormat }}
              </template>
            </el-table-column>
          </el-table-column>
          <el-table-column label="退消耗金额" align="center">
            <template slot="header">
              退消耗金额
              <el-popover placement="top-start" trigger="hover">
                <p>1.订单完成时间在统计时间内，员工退消耗商品（含赠送）金额</p>
                <p>2.金额包含：退回金额、退卡扣金额、退赠卡扣金额、退赠送金额</p>
                <p>3.金额 = 消耗商品金额 x 经手人比例</p>
                <el-button type="text" style="color: #dcdfe6; padding: 0" icon="el-icon-info"
                  slot="reference"></el-button>
              </el-popover>
            </template>
            <el-table-column prop="RefundTreatPayAmount" label="退回金额" align="right">
              <template slot-scope="scope">
                {{ scope.row.RefundTreatPayAmount | toFixed | NumFormat }}
              </template>
            </el-table-column>
            <el-table-column prop="RefundTreatCardDeductionAmount" label="退卡扣金额" align="right">
              <template slot-scope="scope">
                {{ scope.row.RefundTreatCardDeductionAmount | toFixed | NumFormat }}
              </template>
            </el-table-column>
            <el-table-column prop="RefundTreatLargessCardDeductionAmount" label="退赠卡扣金额" align="right">
              <template slot-scope="scope">
                {{ scope.row.RefundTreatLargessCardDeductionAmount | toFixed | NumFormat }}
              </template>
            </el-table-column>
            <el-table-column prop="RefundTreatLargessAmount" label="退赠送金额" align="right">
              <template slot-scope="scope">
                {{ scope.row.RefundTreatLargessAmount | toFixed | NumFormat }}
              </template>
            </el-table-column>
          </el-table-column>
        </el-table>
        <div class="pad_15 text_right">
          <el-pagination background v-if="treatGoodsStatisticsPaginations.total > 0"
            @current-change="handleSalePerformanceCommissionPageChange"
            :current-page.sync="treatGoodsStatisticsPaginations.page"
            :page-size="treatGoodsStatisticsPaginations.page_size" :layout="treatGoodsStatisticsPaginations.layout"
            :total="treatGoodsStatisticsPaginations.total"></el-pagination>
        </div>
      </el-tab-pane>
      <el-tab-pane label="员工消耗项目统计" name="project">
        <div class="nav_header">
          <el-form :inline="true" size="small" :model="projectStatisticsSearchData" @submit.native.prevent>
            <el-form-item label="时间筛选">
              <el-date-picker v-model="projectStatisticsSearchData.QueryDate" :picker-options="pickerOptions"
                unlink-panels type="daterange" range-separator="至" value-format="yyyy-MM-dd" start-placeholder="开始日期"
                end-placeholder="结束日期" @change="handleSalePerformanceCommissionSearch"></el-date-picker>
            </el-form-item>
            <el-form-item label="员工姓名">
              <el-input v-model="projectStatisticsSearchData.EmployeeName" clearable
                @keyup.enter.native="handleSalePerformanceCommissionSearch"
                @clear="handleSalePerformanceCommissionSearch" placeholder="请输入员工姓名"></el-input>
            </el-form-item>
            <el-form-item v-if="storeEntityList.length > 1" label="所属门店">
              <el-select v-model="projectStatisticsSearchData.EntityID" clearable filterable placeholder="请选择门店"
                :default-first-option="true" @change="handleSalePerformanceCommissionSearch">
                <el-option v-for="item in storeEntityList" :key="item.ID" :label="item.EntityName"
                  :value="item.ID"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="员工职务">
              <el-select v-model="projectStatisticsSearchData.JobID" filterable placeholder="选择员工职务"
                @change="handleSalePerformanceCommissionSearch" clearable>
                <el-option v-for="item in jobTypeList" :key="item.ID" :label="item.JobName"
                  :value="item.ID"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" v-prevent-click @click="handleSalePerformanceCommissionSearch">搜索</el-button>
            </el-form-item>
            <el-form-item>
              <el-button v-if="isTreatProjectStatisticsExport" type="primary" size="small" :loading="downloadLoading"
                @click="employeeTreatStatement_projectStatisticsExcel">导出</el-button>
            </el-form-item>
          </el-form>
        </div>
        <el-table size="small" show-summary :summary-method="gettableDataSummaries"
          :data="tableDataTreatProjectStatistics">
          <el-table-column prop="EmployeeName" label="员工姓名" width="80"></el-table-column>
          <el-table-column prop="EmployeeID" label="员工编号" ></el-table-column>
          <el-table-column prop="JobName" label="职务" width="150"></el-table-column>
          <el-table-column prop="EntityName" label="所属组织" width="180"></el-table-column>
          <el-table-column prop="TreatQuantity" label="消耗数量" width="90" align="right">
            <template slot="header">
              消耗数量
              <el-popover placement="top-start" trigger="hover">
                <p>1.订单完成时间在统计时间内，员工消耗项目的数量</p>
                <p>2.消耗数量 = 消耗项目数量 x 经手人比例</p>
                <el-button type="text" style="color: #dcdfe6; padding: 0" icon="el-icon-info"
                  slot="reference"></el-button>
              </el-popover>
            </template>
          </el-table-column>

          <el-table-column label="消耗金额" align="center">
            <template slot="header">
              消耗金额
              <el-popover placement="top-start" trigger="hover">
                <p>1.订单完成时间在统计时间内，员工消耗项目（含赠送）金额</p>
                <p>2.金额包含：现金金额、卡抵扣金额、赠送卡抵扣金额、赠送金额</p>
                <p>3.金额 = 消耗项目金额 x 经手人比例</p>
                <el-button type="text" style="color: #dcdfe6; padding: 0" icon="el-icon-info"
                  slot="reference"></el-button>
              </el-popover>
            </template>
            <el-table-column prop="TreatPayAmount" label="现金金额" align="right">
              <template slot-scope="scope">
                {{ scope.row.TreatPayAmount | toFixed | NumFormat }}
              </template>
            </el-table-column>
            <el-table-column prop="TreatCardDeductionAmount" label="卡抵扣金额" align="right">
              <template slot-scope="scope">
                {{ scope.row.TreatCardDeductionAmount | toFixed | NumFormat }}
              </template>
            </el-table-column>
            <el-table-column prop="TreatLargessCardDeductionAmount" label="赠送卡抵扣" align="right">
              <template slot-scope="scope">
                {{ scope.row.TreatLargessCardDeductionAmount | toFixed | NumFormat }}
              </template>
            </el-table-column>
            <el-table-column prop="TreatLargessAmount" label="赠送金额" align="right">
              <template slot-scope="scope">
                {{ scope.row.TreatLargessAmount | toFixed | NumFormat }}
              </template>
            </el-table-column>
          </el-table-column>
          <el-table-column label="退消耗金额" align="center">
            <template slot="header">
              退消耗金额
              <el-popover placement="top-start" trigger="hover">
                <p>1.订单完成时间在统计时间内，员工退项目消耗（含赠送）金额</p>
                <p>2.金额包含：退回金额、退卡扣金额、退赠卡扣金额、退赠送金额</p>
                <p>3.金额 = 退消耗项目金额 x 经手人比例</p>
                <el-button type="text" style="color: #dcdfe6; padding: 0" icon="el-icon-info"
                  slot="reference"></el-button>
              </el-popover>
            </template>
            <el-table-column prop="RefundTreatPayAmount" label="退回金额" align="right">
              <template slot-scope="scope">
                {{ scope.row.RefundTreatPayAmount | toFixed | NumFormat }}
              </template>
            </el-table-column>
            <el-table-column prop="RefundTreatCardDeductionAmount" label="退卡扣金额" align="right">
              <template slot-scope="scope">
                {{ scope.row.RefundTreatCardDeductionAmount | toFixed | NumFormat }}
              </template>
            </el-table-column>
            <el-table-column prop="RefundTreatLargessCardDeductionAmount" label="退赠卡扣金额" align="right">
              <template slot-scope="scope">
                {{ scope.row.RefundTreatLargessCardDeductionAmount | toFixed | NumFormat }}
              </template>
            </el-table-column>
            <el-table-column prop="RefundTreatLargessAmount" label="退赠送金额" align="right">
              <template slot-scope="scope">
                {{ scope.row.RefundTreatLargessAmount | toFixed | NumFormat }}
              </template>
            </el-table-column>
          </el-table-column>
        </el-table>
        <div class="pad_15 text_right">
          <el-pagination background v-if="treatProjectStatisticsPaginations.total > 0"
            @current-change="handleSalePerformanceCommissionPageChange"
            :current-page.sync="treatProjectStatisticsPaginations.page"
            :page-size="treatProjectStatisticsPaginations.page_size" :layout="treatProjectStatisticsPaginations.layout"
            :total="treatProjectStatisticsPaginations.total"></el-pagination>
        </div>
      </el-tab-pane>
      <el-tab-pane label="员工消耗产品统计" name="product">
        <div class="nav_header">
          <el-form :inline="true" size="small" :model="productStatisticsSearchData" @submit.native.prevent>
            <el-form-item label="时间筛选">
              <el-date-picker v-model="productStatisticsSearchData.QueryDate" :picker-options="pickerOptions"
                unlink-panels type="daterange" range-separator="至" value-format="yyyy-MM-dd" start-placeholder="开始日期"
                end-placeholder="结束日期" @change="handleSalePerformanceCommissionSearch"></el-date-picker>
            </el-form-item>
            <el-form-item label="员工姓名">
              <el-input v-model="productStatisticsSearchData.EmployeeName" clearable
                @keyup.enter.native="handleSalePerformanceCommissionSearch"
                @clear="handleSalePerformanceCommissionSearch" placeholder="请输入员工姓名"></el-input>
            </el-form-item>
            <el-form-item v-if="storeEntityList.length > 1" label="所属门店">
              <el-select v-model="productStatisticsSearchData.EntityID" clearable filterable placeholder="请选择门店"
                :default-first-option="true" @change="handleSalePerformanceCommissionSearch">
                <el-option v-for="item in storeEntityList" :key="item.ID" :label="item.EntityName"
                  :value="item.ID"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="员工职务">
              <el-select v-model="productStatisticsSearchData.JobID" filterable placeholder="选择员工职务"
                @change="handleSalePerformanceCommissionSearch" clearable>
                <el-option v-for="item in jobTypeList" :key="item.ID" :label="item.JobName"
                  :value="item.ID"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" v-prevent-click @click="handleSalePerformanceCommissionSearch">搜索</el-button>
            </el-form-item>
            <el-form-item>
              <el-button v-if="isTreatProductStatisticsExport" type="primary" size="small" :loading="downloadLoading"
                @click="employeeTreatStatement_productStatisticsExcel">导出</el-button>
            </el-form-item>
          </el-form>
        </div>
        <el-table size="small" show-summary :summary-method="gettableDataSummaries"
          :data="tableDataTreatProductStatistics">
          <el-table-column prop="EmployeeName" label="员工姓名" width="80"></el-table-column>
          <el-table-column prop="EmployeeID" label="员工编号" ></el-table-column>
          <el-table-column prop="JobName" label="职务" width="150"></el-table-column>
          <el-table-column prop="EntityName" label="所属组织" width="180"></el-table-column>
          <el-table-column prop="TreatQuantity" label="消耗数量" width="90" align="right">
            <template slot="header">
              消耗数量
              <el-popover placement="top-start" trigger="hover">
                <p>1.订单完成时间在统计时间内，员工消耗产品的数量</p>
                <p>2.消耗数量 = 消耗产品数量 x 经手人比例</p>
                <el-button type="text" style="color: #dcdfe6; padding: 0" icon="el-icon-info"
                  slot="reference"></el-button>
              </el-popover>
            </template>
          </el-table-column>

          <el-table-column label="消耗金额" align="center">
            <template slot="header">
              消耗金额
              <el-popover placement="top-start" trigger="hover">
                <p>1.订单完成时间在统计时间内，员工消耗产品（含赠送）金额</p>
                <p>2.金额包含：现金金额、卡抵扣金额、赠送卡抵扣金额、赠送金额</p>
                <p>3.金额 = 消耗产品金额 x 经手人比例</p>
                <el-button type="text" style="color: #dcdfe6; padding: 0" icon="el-icon-info"
                  slot="reference"></el-button>
              </el-popover>
            </template>
            <el-table-column prop="TreatPayAmount" label="现金金额" align="right">
              <template slot-scope="scope">
                {{ scope.row.TreatPayAmount | toFixed | NumFormat }}
              </template>
            </el-table-column>
            <el-table-column prop="TreatCardDeductionAmount" label="卡抵扣金额" align="right">
              <template slot-scope="scope">
                {{ scope.row.TreatCardDeductionAmount | toFixed | NumFormat }}
              </template>
            </el-table-column>
            <el-table-column prop="TreatLargessCardDeductionAmount" label="赠送卡抵扣" align="right">
              <template slot-scope="scope">
                {{ scope.row.TreatLargessCardDeductionAmount | toFixed | NumFormat }}
              </template>
            </el-table-column>
            <el-table-column prop="TreatLargessAmount" label="赠送金额" align="right">
              <template slot-scope="scope">
                {{ scope.row.TreatLargessAmount | toFixed | NumFormat }}
              </template>
            </el-table-column>
          </el-table-column>
          <el-table-column label="退消耗金额" align="center"> <template slot="header">
              退消耗金额
              <el-popover placement="top-start" trigger="hover">
                <p>1.订单完成时间在统计时间内，员工退产品消耗（含赠送）金额</p>
                <p>2.金额包含：退回金额、退卡扣金额、退赠卡扣金额、退赠送金额</p>
                <p>3.金额 = 退消耗产品金额 x 经手人比例</p>
                <el-button type="text" style="color: #dcdfe6; padding: 0" icon="el-icon-info"
                  slot="reference"></el-button>
              </el-popover>
            </template>
            <el-table-column prop="RefundTreatPayAmount" label="退回金额" align="right">
              <template slot-scope="scope">
                {{ scope.row.RefundTreatPayAmount | toFixed | NumFormat }}
              </template>
            </el-table-column>
            <el-table-column prop="RefundTreatCardDeductionAmount" label="退卡扣金额" align="right">
              <template slot-scope="scope">
                {{ scope.row.RefundTreatCardDeductionAmount | toFixed | NumFormat }}
              </template>
            </el-table-column>
            <el-table-column prop="RefundTreatLargessCardDeductionAmount" label="退赠卡扣金额" align="right">
              <template slot-scope="scope">
                {{ scope.row.RefundTreatLargessCardDeductionAmount | toFixed | NumFormat }}
              </template>
            </el-table-column>
            <el-table-column prop="RefundTreatLargessAmount" label="退赠送金额" align="right">
              <template slot-scope="scope">
                {{ scope.row.RefundTreatLargessAmount | toFixed | NumFormat }}
              </template>
            </el-table-column>
          </el-table-column>
        </el-table>
        <div class="pad_15 text_right">
          <el-pagination background v-if="treatProductStatisticsPaginations.total > 0"
            @current-change="handleSalePerformanceCommissionPageChange"
            :current-page.sync="treatProductStatisticsPaginations.page"
            :page-size="treatProductStatisticsPaginations.page_size" :layout="treatProductStatisticsPaginations.layout"
            :total="treatProductStatisticsPaginations.total"></el-pagination>
        </div>
      </el-tab-pane>
      <el-tab-pane label="员工耗卡统计" name="card">
        <span slot="label">
          员工耗卡统计
          <el-popover placement="top-start" width="400" trigger="hover">
            <p>只统计项目耗卡，包含：项目卡、通用次卡、时效卡、储值卡</p>
            <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
          </el-popover>
        </span>
        <div class="nav_header">
          <el-form :inline="true" size="small" :model="cardStatisticsSearchData" @submit.native.prevent>
            <el-form-item label="时间筛选">
              <el-date-picker v-model="cardStatisticsSearchData.QueryDate" :picker-options="pickerOptions" unlink-panels
                type="daterange" range-separator="至" value-format="yyyy-MM-dd" start-placeholder="开始日期"
                end-placeholder="结束日期" @change="handleSalePerformanceCommissionSearch"></el-date-picker>
            </el-form-item>
            <el-form-item label="员工姓名">
              <el-input v-model="cardStatisticsSearchData.EmployeeName" clearable
                @keyup.enter.native="handleSalePerformanceCommissionSearch"
                @clear="handleSalePerformanceCommissionSearch" placeholder="请输入员工姓名"></el-input>
            </el-form-item>
            <el-form-item v-if="storeEntityList.length > 1" label="所属门店">
              <el-select v-model="cardStatisticsSearchData.EntityID" clearable filterable placeholder="请选择门店"
                :default-first-option="true" @change="handleSalePerformanceCommissionSearch">
                <el-option v-for="item in storeEntityList" :key="item.ID" :label="item.EntityName"
                  :value="item.ID"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="员工职务">
              <el-select v-model="cardStatisticsSearchData.JobID" filterable placeholder="选择员工职务"
                @change="handleSalePerformanceCommissionSearch" clearable>
                <el-option v-for="item in jobTypeList" :key="item.ID" :label="item.JobName"
                  :value="item.ID"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" v-prevent-click @click="handleSalePerformanceCommissionSearch">搜索</el-button>
            </el-form-item>
            <el-form-item>
              <el-button v-if="isTreatCardStatisticsExport" type="primary" size="small" :loading="downloadLoading"
                @click="employeeTreatStatement_cardStatisticsExcel">导出</el-button>
            </el-form-item>
          </el-form>
        </div>
        <el-table size="small" show-summary :summary-method="gettableDataSummaries"
          :data="tableDataTreatCardStatistics">
          <el-table-column prop="EmployeeName" label="员工姓名" width="80"></el-table-column>
          <el-table-column prop="EmployeeID" label="员工编号" ></el-table-column>
          <el-table-column prop="JobName" label="职务" width="150"></el-table-column>
          <el-table-column prop="EntityName" label="所属组织" width="180"></el-table-column>
          <el-table-column prop="TreatQuantity" label="卡次数" width="80" align="right">
            <template slot="header">
              卡次数
              <el-popover placement="top-start" trigger="hover">
                <p>1.订单完成时间在统计时间内，员工消耗卡的数量</p>
                <p>2.卡包含：项目卡、通用次卡、时效卡、储值卡</p>
                <p>3.通用次卡按照消耗卡次数统计，其他按照项目数统计卡次数</p>
                <p>4.消耗数量 = 卡次数 x 经手人比例</p>
                <el-button type="text" style="color: #dcdfe6; padding: 0" icon="el-icon-info"
                  slot="reference"></el-button>
              </el-popover>
            </template>
          </el-table-column>

          <el-table-column label="消耗金额" align="center">
            <template slot="header">
              消耗金额
              <el-popover placement="top-start" trigger="hover">
                <p>1.订单完成时间在统计时间内，员工消耗项目（含赠送）金额</p>
                <p>2.金额包含：现金金额、卡抵扣金额、赠送卡抵扣金额、赠送金额</p>
                <p>3.金额 = 消耗项目金额 x 经手人比例</p>
                <el-button type="text" style="color: #dcdfe6; padding: 0" icon="el-icon-info"
                  slot="reference"></el-button>
              </el-popover>
            </template>
            <el-table-column prop="TreatPayAmount" label="现金金额" align="right">
              <template slot-scope="scope">
                {{ scope.row.TreatPayAmount | toFixed | NumFormat }}
              </template>
            </el-table-column>
            <el-table-column prop="TreatCardDeductionAmount" label="卡抵扣金额" align="right">
              <template slot-scope="scope">
                {{ scope.row.TreatCardDeductionAmount | toFixed | NumFormat }}
              </template>
            </el-table-column>
            <el-table-column prop="TreatLargessCardDeductionAmount" label="赠送卡抵扣" align="right">
              <template slot-scope="scope">
                {{ scope.row.TreatLargessCardDeductionAmount | toFixed | NumFormat }}
              </template>
            </el-table-column>
            <el-table-column prop="TreatLargessAmount" label="赠送金额" align="right">
              <template slot-scope="scope">
                {{ scope.row.TreatLargessAmount | toFixed | NumFormat }}
              </template>
            </el-table-column>
          </el-table-column>
          <el-table-column label="退消耗金额" align="center">
            <template slot="header">
              退消耗金额
              <el-popover placement="top-start" trigger="hover">
                <p>1.订单完成时间在统计时间内，员工退项目消耗（含赠送）金额</p>
                <p>2.金额包含：退回金额、退卡扣金额、退赠卡扣金额、退赠送金额</p>
                <p>3.金额 = 退消耗项目金额 x 经手人比例</p>
                <el-button type="text" style="color: #dcdfe6; padding: 0" icon="el-icon-info"
                  slot="reference"></el-button>
              </el-popover>
            </template>
            <el-table-column prop="RefundTreatPayAmount" label="退回金额" align="right">
              <template slot-scope="scope">
                {{ scope.row.RefundTreatPayAmount | toFixed | NumFormat }}
              </template>
            </el-table-column>
            <el-table-column prop="RefundTreatCardDeductionAmount" label="退卡扣金额" align="right">
              <template slot-scope="scope">
                {{ scope.row.RefundTreatCardDeductionAmount | toFixed | NumFormat }}
              </template>
            </el-table-column>
            <el-table-column prop="RefundTreatLargessCardDeductionAmount" label="退赠卡扣金额" align="right">
              <template slot-scope="scope">
                {{ scope.row.RefundTreatLargessCardDeductionAmount | toFixed | NumFormat }}
              </template>
            </el-table-column>
            <el-table-column prop="RefundTreatLargessAmount" label="退赠送金额" align="right">
              <template slot-scope="scope">
                {{ scope.row.RefundTreatLargessAmount | toFixed | NumFormat }}
              </template>
            </el-table-column>
          </el-table-column>
        </el-table>
        <div class="pad_15 text_right">
          <el-pagination background v-if="treatCardStatisticsPaginations.total > 0"
            @current-change="handleSalePerformanceCommissionPageChange"
            :current-page.sync="treatCardStatisticsPaginations.page"
            :page-size="treatCardStatisticsPaginations.page_size" :layout="treatCardStatisticsPaginations.layout"
            :total="treatCardStatisticsPaginations.total"></el-pagination>
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import API from "@/api/Report/Employee/treatStatistics.js";
import EntityAPI from "@/api/Report/Common/entity";
import APIJob from "@/api/KHS/Entity/jobtype";
const dayjs = require("dayjs");
const isoWeek = require("dayjs/plugin/isoWeek");
dayjs.extend(isoWeek);
const tableDataSum = {
  goods: "tableDataSumTreatGoodsStatistics",
  project: "tableDataSumTreatProjectStatistics",
  product: "tableDataSumTreatProductStatistics",
  card: "tableDataSumTreatCardStatistics",
};
export default {
  name: "EmployeeTreatStatistics",

  beforeRouteEnter(to, from, next) {
    next((vm) => {
      vm.isTreatGoodsStatisticsExport = vm.$permission.permission(to.meta.Permission, "Report-Employee-TreatGoodsStatistics-Export");
      vm.isTreatProjectStatisticsExport = vm.$permission.permission(to.meta.Permission, "Report-Employee-TreatProjectStatistics-Export");
      vm.isTreatProductStatisticsExport = vm.$permission.permission(to.meta.Permission, "Report-Employee-TreatProductStatistics-Export");

      vm.isTreatCardStatisticsExport = vm.$permission.permission(to.meta.Permission, "Report-Employee-TreatCardStatistics-Export");
    });
  },
  props: {},
  /** 监听数据变化   */
  watch: {},
  /**  引入的组件  */
  components: {},
  /**  Vue 实例的数据对象**/
  data() {
    return {
      isTreatGoodsStatisticsExport: false,
      isTreatProjectStatisticsExport: false,
      isTreatProductStatisticsExport: false,
      isTreatCardStatisticsExport: false,
      loading: false,
      downloadLoading: false,
      activeName: "goods",
      storeEntityList: [], //门店列表
      jobTypeList: [], //职务列表
      goodStatisticsSearchData: {
        QueryDate: [new Date(), new Date()],
        GoodsTypeName: "",
        EmployeeName: "",
        JobID: "",
        CustomerName: "",
      },
      projectStatisticsSearchData: {
        QueryDate: [new Date(), new Date()],
        GoodsTypeName: "",
        EmployeeName: "",
        JobID: "",
        CustomerName: "",
      },
      productStatisticsSearchData: {
        QueryDate: [new Date(), new Date()],
        GoodsTypeName: "",
        EmployeeName: "",
        JobID: "",
        CustomerName: "",
      },

      cardStatisticsSearchData: {
        QueryDate: [new Date(), new Date()],
        GoodsTypeName: "",
        EmployeeName: "",
        JobID: "",
        CustomerName: "",
      },
      tableDataTreatGoodsStatistics: [],
      tableDataSumTreatGoodsStatistics: {},
      tableDataTreatProjectStatistics: [],
      tableDataSumTreatProjectStatistics: {},
      tableDataTreatProductStatistics: [],
      tableDataSumTreatProductStatistics: {},
      tableDataTreatCardStatistics: [],
      tableDataSumTreatCardStatistics: {},
      //需要给分页组件传的信息
      treatProjectStatisticsPaginations: {
        page: 1, // 当前位于哪页
        total: 0, // 总数
        page_size: 10, // 1页显示多少条
        layout: "total, prev, pager, next,jumper", // 翻页属性
      },
      treatGoodsStatisticsPaginations: {
        page: 1, // 当前位于哪页
        total: 0, // 总数
        page_size: 10, // 1页显示多少条
        layout: "total, prev, pager, next,jumper", // 翻页属性
      },
      treatProductStatisticsPaginations: {
        page: 1, // 当前位于哪页
        total: 0, // 总数
        page_size: 10, // 1页显示多少条
        layout: "total, prev, pager, next,jumper", // 翻页属性
      },
      treatCardStatisticsPaginations: {
        page: 1, // 当前位于哪页
        total: 0, // 总数
        page_size: 10, // 1页显示多少条
        layout: "total, prev, pager, next,jumper", // 翻页属性
      },
      pickerOptions: {
        shortcuts: [
          {
            text: "今天",
            onClick: (picker) => {
              let end = new Date();
              let start = new Date();
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "本周",
            onClick: (picker) => {
              let end = new Date();
              let weekDay = dayjs(end).isoWeekday();
              let start = dayjs(end)
                .subtract(weekDay - 1, "day")
                .toDate();
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "本月",
            onClick: (picker) => {
              let end = new Date();
              let start = dayjs(dayjs(end).format("YYYY-MM") + "01").toDate();
              picker.$emit("pick", [start, end]);
            },
          },
        ],
      },
      categoryList: [],
      cascaderProps: {
        checkStrictly: true,
        label: "Name",
        value: "ID",
        children: "Child",
      },
    };
  },
  /**计算属性  */
  computed: {},
  /**  方法集合  */
  methods: {
    /**    */
    changeActiveName() {
      let that = this;
      switch (that.activeName) {
        case "goods":
          that.employeeTreatStatement_goodStatistics();
          break;
        case "project":
          that.employeeTreatStatement_projectStatistics();
          break;
        case "product":
          that.employeeTreatStatement_productStatistics();
          break;
        case "card":
          that.employeeTreatStatement_cardStatistics();
          break;
      }
    },
    handleSalePerformanceCommissionSearch() {
      var that = this;

      switch (that.activeName) {
        case "goods":
          that.treatGoodsStatisticsPaginations.page = 1;
          that.employeeTreatStatement_goodStatistics();
          break;
        case "project":
          that.treatProjectStatisticsPaginations.page = 1;
          that.employeeTreatStatement_projectStatistics();
          break;
        case "product":
          that.treatProductStatisticsPaginations.page = 1;
          that.employeeTreatStatement_productStatistics();
          break;
        case "card":
          that.treatCardStatisticsPaginations.page = 1;
          that.employeeTreatStatement_cardStatistics();
          break;
      }
    },
    handleSalePerformanceCommissionPageChange(page) {
      var that = this;
      switch (that.activeName) {
        case "goods":
          that.treatGoodsStatisticsPaginations.page = page;
          that.employeeTreatStatement_goodStatistics();
          break;
        case "project":
          that.treatProjectStatisticsPaginations.page = page;
          that.employeeTreatStatement_projectStatistics();
          break;
        case "product":
          that.treatProductStatisticsPaginations.page = page;
          that.employeeTreatStatement_productStatistics();
          break;
        case "card":
          that.treatCardStatisticsPaginations.page = page;
          that.employeeTreatStatement_cardStatistics();
          break;
      }
    },

    gettableDataSummaries({ columns }) {
      const that = this;
      const sums = [];
      const sumForm = tableDataSum[that.activeName];
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = <span class="font_weight_600">合计</span>;
          return;
        }
        var filter_NumFormat = this.$options.filters["NumFormat"];
        switch (column.property) {
          case "TreatQuantity":
            sums[index] = <span class="font_weight_600">{filter_NumFormat(that[sumForm] ? that[sumForm].TreatQuantity : 0)}</span>;
            break;
          case "TreatPayAmount":
            sums[index] = <span class="font_weight_600">{filter_NumFormat(that[sumForm] ? that[sumForm].TreatPayAmount : 0)}</span>;
            break;
          case "TreatCardDeductionAmount":
            sums[index] = <span class="font_weight_600">{filter_NumFormat(that[sumForm] ? that[sumForm].TreatCardDeductionAmount : 0)}</span>;
            break;
          case "TreatLargessCardDeductionAmount":
            sums[index] = <span class="font_weight_600">{filter_NumFormat(that[sumForm] ? that[sumForm].TreatLargessCardDeductionAmount : 0)}</span>;
            break;
          case "TreatLargessAmount":
            sums[index] = <span class="font_weight_600">{filter_NumFormat(that[sumForm] ? that[sumForm].TreatLargessAmount : 0)}</span>;
            break;
          case "RefundTreatPayAmount":
            sums[index] = <span class="font_weight_600">{filter_NumFormat(that[sumForm] ? that[sumForm].RefundTreatPayAmount : 0)}</span>;
            break;

          case "RefundTreatCardDeductionAmount":
            sums[index] = <span class="font_weight_600">{filter_NumFormat(that[sumForm] ? that[sumForm].RefundTreatCardDeductionAmount : 0)}</span>;
            break;
          case "RefundTreatLargessCardDeductionAmount":
            sums[index] = <span class="font_weight_600">{filter_NumFormat(that[sumForm] ? that[sumForm].RefundTreatLargessCardDeductionAmount : 0)}</span>;
            break;
          case "RefundTreatLargessAmount":
            sums[index] = <span class="font_weight_600">{filter_NumFormat(that[sumForm] ? that[sumForm].RefundTreatLargessAmount : 0)}</span>;
            break;
        }
      });
      return sums;
    },
    // 销售搜索
    employeeTreatStatement_goodStatistics() {
      var that = this;
      if (that.goodStatisticsSearchData.QueryDate != null) {
        if (dayjs(that.goodStatisticsSearchData.QueryDate[0]).add(366, "day").valueOf() < dayjs(that.goodStatisticsSearchData.QueryDate[1]).valueOf()) {
          that.$message.error("时间筛选范围不能超366天");
          return;
        }
        var params = {
          PageNum: that.treatGoodsStatisticsPaginations.page,
          EntityID: that.goodStatisticsSearchData.EntityID,
          StartDate: that.goodStatisticsSearchData.QueryDate[0],
          EndDate: that.goodStatisticsSearchData.QueryDate[1],
          EmployeeName: that.goodStatisticsSearchData.EmployeeName.trim(),
          JobID: that.goodStatisticsSearchData.JobID,
        };
        that.loading = true;
        API.employeeTreatStatement_goodStatistics(params)
          .then((res) => {
            if (res.StateCode == 200) {
              that.tableDataSumTreatGoodsStatistics = res.Data.employeeTreatGoodSumStatementForm;
              that.tableDataTreatGoodsStatistics = res.Data.employeeTreatGoodStatementForms.List;
              that.treatGoodsStatisticsPaginations.total = res.Data.employeeTreatGoodStatementForms.Total;
              that.treatGoodsStatisticsPaginations.page_size = res.Data.employeeTreatGoodStatementForms.PageSize;
            } else {
              that.$message.error({
                message: res.Message,
                duration: 2000,
              });
            }
          })
          .finally(function () {
            that.loading = false;
          });
      }
    },
    /** 数据导出 */
    employeeTreatStatement_goodStatisticsExcel() {
      var that = this;
      if (that.goodStatisticsSearchData.QueryDate != null) {
        if (dayjs(that.goodStatisticsSearchData.QueryDate[0]).add(366, "day").valueOf() < dayjs(that.goodStatisticsSearchData.QueryDate[1]).valueOf()) {
          that.$message.error("时间筛选范围不能超366天");
          return;
        }

        let params = {
          PageNum: that.treatProjectStatisticsPaginations.page,
          EntityID: that.goodStatisticsSearchData.EntityID,
          StartDate: that.goodStatisticsSearchData.QueryDate[0],
          EndDate: that.goodStatisticsSearchData.QueryDate[1],
          EmployeeName: that.goodStatisticsSearchData.EmployeeName.trim(),
          JobID: that.goodStatisticsSearchData.JobID,
        };
        that.downloadLoading = true;
        API.employeeTreatStatement_goodStatisticsExcel(params)
          .then((res) => {
            this.$message.success({
              message: "正在导出",
              duration: "4000",
            });
            const link = document.createElement("a");
            let blob = new Blob([res], { type: "application/octet-stream" });
            link.style.display = "none";
            link.href = URL.createObjectURL(blob);
            link.download = "员工消耗商品统计.xlsx"; //下载的文件名
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
          })
          .finally(function () {
            that.downloadLoading = false;
          });
      }
    },

    // 销售搜索
    employeeTreatStatement_projectStatistics() {
      var that = this;
      if (that.projectStatisticsSearchData.QueryDate != null) {
        if (dayjs(that.projectStatisticsSearchData.QueryDate[0]).add(366, "day").valueOf() < dayjs(that.projectStatisticsSearchData.QueryDate[1]).valueOf()) {
          that.$message.error("时间筛选范围不能超366天");
          return;
        }
        var params = {
          PageNum: that.treatProjectStatisticsPaginations.page,
          EntityID: that.projectStatisticsSearchData.EntityID,
          StartDate: that.projectStatisticsSearchData.QueryDate[0],
          EndDate: that.projectStatisticsSearchData.QueryDate[1],
          EmployeeName: that.projectStatisticsSearchData.EmployeeName.trim(),
          JobID: that.projectStatisticsSearchData.JobID,
        };
        that.loading = true;
        API.employeeTreatStatement_projectStatistics(params)
          .then((res) => {
            if (res.StateCode == 200) {
              that.tableDataSumTreatProjectStatistics = res.Data.employeeTreatGoodSumStatementForm;
              that.tableDataTreatProjectStatistics = res.Data.employeeTreatGoodStatementForms.List;
              that.treatProjectStatisticsPaginations.total = res.Data.employeeTreatGoodStatementForms.Total;
              that.treatProjectStatisticsPaginations.page_size = res.Data.employeeTreatGoodStatementForms.PageSize;
            } else {
              that.$message.error({
                message: res.Message,
                duration: 2000,
              });
            }
          })
          .finally(function () {
            that.loading = false;
          });
      }
    },
    /** 数据导出 */
    employeeTreatStatement_projectStatisticsExcel() {
      var that = this;
      if (that.projectStatisticsSearchData.QueryDate != null) {
        if (dayjs(that.projectStatisticsSearchData.QueryDate[0]).add(366, "day").valueOf() < dayjs(that.projectStatisticsSearchData.QueryDate[1]).valueOf()) {
          that.$message.error("时间筛选范围不能超366天");
          return;
        }

        let params = {
          PageNum: that.treatProjectStatisticsPaginations.page,
          EntityID: that.projectStatisticsSearchData.EntityID,
          StartDate: that.projectStatisticsSearchData.QueryDate[0],
          EndDate: that.projectStatisticsSearchData.QueryDate[1],
          EmployeeName: that.projectStatisticsSearchData.EmployeeName.trim(),
          JobID: that.projectStatisticsSearchData.JobID,
        };
        that.downloadLoading = true;
        API.employeeTreatStatement_projectStatisticsExcel(params)
          .then((res) => {
            this.$message.success({
              message: "正在导出",
              duration: "4000",
            });
            const link = document.createElement("a");
            let blob = new Blob([res], { type: "application/octet-stream" });
            link.style.display = "none";
            link.href = URL.createObjectURL(blob);
            link.download = "员工消耗项目统计.xlsx"; //下载的文件名
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
          })
          .finally(function () {
            that.downloadLoading = false;
          });
      }
    },

    // 销售搜索
    employeeTreatStatement_productStatistics() {
      var that = this;
      if (that.productStatisticsSearchData.QueryDate != null) {
        if (dayjs(that.productStatisticsSearchData.QueryDate[0]).add(366, "day").valueOf() < dayjs(that.productStatisticsSearchData.QueryDate[1]).valueOf()) {
          that.$message.error("时间筛选范围不能超366天");
          return;
        }
        var params = {
          EntityID: that.productStatisticsSearchData.EntityID,
          StartDate: that.productStatisticsSearchData.QueryDate[0],
          EndDate: that.productStatisticsSearchData.QueryDate[1],
          EmployeeName: that.productStatisticsSearchData.EmployeeName.trim(),
          PageNum: that.treatProductStatisticsPaginations.page,
          JobID: that.productStatisticsSearchData.JobID,
        };
        that.loading = true;
        API.employeeTreatStatement_productStatistics(params)
          .then((res) => {
            if (res.StateCode == 200) {
              that.tableDataSumTreatProductStatistics = res.Data.employeeTreatGoodSumStatementForm;
              that.tableDataTreatProductStatistics = res.Data.employeeTreatGoodStatementForms.List;
              that.treatProductStatisticsPaginations.total = res.Data.employeeTreatGoodStatementForms.Total;
              that.treatProductStatisticsPaginations.page_size = res.Data.employeeTreatGoodStatementForms.PageSize;
            } else {
              that.$message.error({
                message: res.Message,
                duration: 2000,
              });
            }
          })
          .finally(function () {
            that.loading = false;
          });
      }
    },
    /** 数据导出 */
    employeeTreatStatement_productStatisticsExcel() {
      var that = this;
      if (that.productStatisticsSearchData.QueryDate != null) {
        if (dayjs(that.productStatisticsSearchData.QueryDate[0]).add(366, "day").valueOf() < dayjs(that.productStatisticsSearchData.QueryDate[1]).valueOf()) {
          that.$message.error("时间筛选范围不能超366天");
          return;
        }

        let params = {
          EntityID: that.productStatisticsSearchData.EntityID,
          StartDate: that.productStatisticsSearchData.QueryDate[0],
          EndDate: that.productStatisticsSearchData.QueryDate[1],
          EmployeeName: that.productStatisticsSearchData.EmployeeName.trim(),
          PageNum: that.treatProductStatisticsPaginations.page,
          JobID: that.productStatisticsSearchData.JobID,
        };
        that.downloadLoading = true;
        API.employeeTreatStatement_productStatisticsExcel(params)
          .then((res) => {
            this.$message.success({
              message: "正在导出",
              duration: "4000",
            });
            const link = document.createElement("a");
            let blob = new Blob([res], { type: "application/octet-stream" });
            link.style.display = "none";
            link.href = URL.createObjectURL(blob);
            link.download = "员工消耗产品统计.xlsx"; //下载的文件名
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
          })
          .finally(function () {
            that.downloadLoading = false;
          });
      }
    },

    // 消耗卡 销售搜索
    employeeTreatStatement_cardStatistics() {
      var that = this;
      if (that.cardStatisticsSearchData.QueryDate != null) {
        if (dayjs(that.cardStatisticsSearchData.QueryDate[0]).add(366, "day").valueOf() < dayjs(that.cardStatisticsSearchData.QueryDate[1]).valueOf()) {
          that.$message.error("时间筛选范围不能超366天");
          return;
        }
        var params = {
          PageNum: that.treatCardStatisticsPaginations.page,
          EntityID: that.cardStatisticsSearchData.EntityID,
          StartDate: that.cardStatisticsSearchData.QueryDate[0],
          EndDate: that.cardStatisticsSearchData.QueryDate[1],
          EmployeeName: that.cardStatisticsSearchData.EmployeeName.trim(),
          JobID: that.cardStatisticsSearchData.JobID,
        };
        that.loading = true;
        API.employeeTreatStatement_cardStatistics(params)
          .then((res) => {
            if (res.StateCode == 200) {
              that.tableDataSumTreatCardStatistics = res.Data.employeeTreatGoodSumStatementForm;
              that.tableDataTreatCardStatistics = res.Data.employeeTreatGoodStatementForms.List;
              that.treatCardStatisticsPaginations.total = res.Data.employeeTreatGoodStatementForms.Total;
              that.treatCardStatisticsPaginations.page_size = res.Data.employeeTreatGoodStatementForms.PageSize;
            } else {
              that.$message.error({
                message: res.Message,
                duration: 2000,
              });
            }
          })
          .finally(function () {
            that.loading = false;
          });
      }
    },
    /** 消耗卡数据导出 */
    employeeTreatStatement_cardStatisticsExcel() {
      var that = this;
      if (that.cardStatisticsSearchData.QueryDate != null) {
        if (dayjs(that.cardStatisticsSearchData.QueryDate[0]).add(366, "day").valueOf() < dayjs(that.cardStatisticsSearchData.QueryDate[1]).valueOf()) {
          that.$message.error("时间筛选范围不能超366天");
          return;
        }

        let params = {
          PageNum: that.treatCardStatisticsPaginations.page,
          EntityID: that.cardStatisticsSearchData.EntityID,
          StartDate: that.cardStatisticsSearchData.QueryDate[0],
          EndDate: that.cardStatisticsSearchData.QueryDate[1],
          EmployeeName: that.cardStatisticsSearchData.EmployeeName.trim(),
          JobID: that.cardStatisticsSearchData.JobID,
        };
        that.downloadLoading = true;
        API.employeeTreatStatement_cardStatisticsExcel(params)
          .then((res) => {
            this.$message.success({
              message: "正在导出",
              duration: "4000",
            });
            const link = document.createElement("a");
            let blob = new Blob([res], { type: "application/octet-stream" });
            link.style.display = "none";
            link.href = URL.createObjectURL(blob);
            link.download = "员工消耗卡统计.xlsx"; //下载的文件名
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
          })
          .finally(function () {
            that.downloadLoading = false;
          });
      }
    },

    // 职务ID
    async getJobID() {
      var that = this;
      var params = {
        JobTypeName: "",
      };
      let res = await APIJob.getJobJobtypeAll(params);
      if (res.StateCode == 200) {
        that.jobTypeList = res.Data;
      } else {
        that.$message.error({
          message: res.Message,
          duration: 2000,
        });
      }
    },

    //获得当前用户下的权限门店
    getstoreEntityList() {
      var that = this;
      that.loading = true;
      EntityAPI.getStoreEntityList()
        .then((res) => {
          if (res.StateCode == 200) {
            that.storeEntityList = res.Data;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },
  },
  /** 创建实例之前执行函数   */
  beforeCreate() { },
  /**  实例创建完成之后  */
  created() { },
  /**  在挂载开始之前被调用  */
  beforeMount() { },
  /**  实例被挂载后调用  */
  mounted() {
    this.isTreatGoodsStatisticsExport = this.$permission.permission(this.$route.meta.Permission, "Report-Employee-TreatGoodsStatistics-Export");
    this.isTreatProjectStatisticsExport = this.$permission.permission(this.$route.meta.Permission, "Report-Employee-TreatProjectStatistics-Export");
    this.isTreatProductStatisticsExport = this.$permission.permission(this.$route.meta.Permission, "Report-Employee-TreatProductStatistics-Export");
    this.isTreatCardStatisticsExport = this.$permission.permission(this.$route.meta.Permission, "Report-Employee-TreatCardStatistics-Export");

    this.getJobID();
    this.getstoreEntityList();
    this.employeeTreatStatement_goodStatistics();
  },
  /**  数据更新 之前 调用  */
  beforeUpdate() { },
  /** 数据更新 完成 调用   */
  updated() { },
  /**  实例销毁之前调用  */
  beforeDestroy() { },
  /**  实例销毁后调用  */
  destroyed() { },
};
</script>

<style lang="scss">
.EmployeeTreatStatistics {
  .el-tabs--border-card {
    border: 0px !important;
    box-shadow: 0 0px 0px 0 rgba(0, 0, 0, 0), 0 0 0px 0 rgba(0, 0, 0, 0);
  }
}
</style>
