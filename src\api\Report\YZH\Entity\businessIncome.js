import * as API from '@/api/index'

export default {
    /* 营业收入明细表1 列表*/
    entityIncomeStatementOne: params => {
        return API.POST('api/entityIncomeStatement/dateDetail', params)
      },
    /* 营业收入明细表1 导出 */
    dateDetailExcel: params => {
        return API.exportExcel('api/entityIncomeStatement/dateDetailExcel', params)
      },
    /* 营业收入明细表2 列表*/
    entityIncomeStatementTwo: params => {
        return API.POST('api/entityIncomeStatement/entityDetail', params)
      },
    /* 营业收入明细表2 导出 */
    entityDetailExcel: params => {
        return API.exportExcel('api/entityIncomeStatement/entityDetailExcel', params)
      },
    /* 营业收入表汇总 列表*/
    entityIncomeStatement_list: params => {
        return API.POST('api/entityIncomeStatement/list', params)
      },
    /* 营业收入表汇总 导出 */
    entityDetail: params => {
        return API.exportExcel('api/entityIncomeStatement/excel', params)
      },
}