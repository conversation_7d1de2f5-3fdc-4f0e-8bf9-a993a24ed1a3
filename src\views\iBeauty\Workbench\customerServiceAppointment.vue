<template>
  <div class="content_body customerServiceAppointment" v-loading="loading">
    <!-- 搜索条件 -->
    <div class="nav_header">
      <el-form :inline="true" size="small" @submit.native.prevent>
        <el-form-item label="客户信息">
          <el-input
            v-model="search.CustomerName"
            placeholder="输入客户姓名或手机号"
            clearable
            @keyup.enter.native="handleSearch"
            @clear="handleSearch"
          ></el-input>
        </el-form-item>
        
        <el-form-item label="预约状态">
          <el-select
            v-model="search.AppointmentStatus"
            placeholder="请选择预约状态"
            clearable
            @change="handleSearch"
          >
            <el-option label="未到店" value="10"></el-option>
            <el-option label="已到店" value="20"></el-option>
            <el-option label="已取消" value="30"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="预约时间">
          <el-date-picker
            v-model="search.AppointmentDate"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            @change="handleSearch"
          ></el-date-picker>
        </el-form-item>

        <el-form-item label="线索来源">
          <el-select
            v-model="search.LeadSource"
            placeholder="请选择线索来源"
            clearable
            @change="handleSearch"
          >
            <el-option label="抖音信息流" value="DOUYIN_XINXILIU"></el-option>
            <el-option label="微信朋友圈" value="WEIXIN_PENGYOUQUAN"></el-option>
            <el-option label="抖音线索-团购支付" value="DOUYIN_TUANGOU"></el-option>
            <el-option label="抖音线索-自然线索" value="DOUYIN_ZIRANXIANSOU"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="预约人员">
          <el-select
            v-model="search.AppointmentBy"
            placeholder="请选择预约人员"
            clearable
            filterable
            @change="handleSearch"
          >
            <el-option
              v-for="item in appointmentByList"
              :key="item.ID"
              :label="item.Name"
              :value="item.ID"
            ></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="预约门店">
          <el-select
            v-model="search.EntityID"
            placeholder="请选择预约门店"
            clearable
            filterable
            @change="handleSearch"
          >
            <el-option
              v-for="item in entityList"
              :key="item.ID"
              :label="item.Name"
              :value="item.ID"
            ></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="成交会员">
          <el-select
            v-model="search.IsDeal"
            placeholder="请选择成交会员"
            clearable
            @change="handleSearch"
          >
            <el-option label="已成交" :value="true"></el-option>
            <el-option label="未成交" :value="false"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="handleSearch" v-prevent-click>搜索</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 数据表格 -->
    <div class="table-container">
      <el-table
        :data="appointmentList"
        :height="tableHeight"
        :min-height="400"
        size="small"
        stripe
        border
      >
        <el-table-column label="预约时间" width="180px" class-name="appointment-time-column">
          <template slot-scope="scope">
            <div class="appointment-time">
              <span class="time-part">{{ formatAppointmentTime(scope.row.AppointmentDate).time }}</span>
              <span class="date-part">{{ formatAppointmentTime(scope.row.AppointmentDate).date }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="预约门店" prop="EntityName" width="120px"></el-table-column>
        <el-table-column label="客户姓名" width="150px">
          <template slot-scope="scope">
            <div class="customer-info">
              <span
                class="customer-name clickable-customer-name"
                style="color: #409EFF; cursor: pointer;"
                @click="handleCustomerNameClick(scope.row)"
              >
                {{ scope.row.CustomerName }}
              </span>
              <el-tag
                v-if="scope.row.CustomerLevelName"
                class="customer-level-tag"
                type="warning"
                size="mini"
              >
                {{ scope.row.CustomerLevelName }}
              </el-tag>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="手机号码" prop="PhoneNumber" width="120px">
          <template slot-scope="scope">
            {{ scope.row.PhoneNumber | hidephone }}
          </template>
        </el-table-column>
       
        <el-table-column label="预约状态" width="100px">
          <template slot-scope="scope">
            <el-tag :type="getAppointmentTagType(scope.row.AppointmentStatus)" size="small">
              {{ scope.row.AppointmentStatusName }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="备注" prop="Remark" min-width="150px"></el-table-column>
        <el-table-column label="预约人员" prop="AppointmentByName" width="100px"></el-table-column>
        <el-table-column label="线索来源" prop="LeadSourceName" width="120px"></el-table-column>    
        <el-table-column label="预约单号" prop="AppointmentID" width="160px"></el-table-column>
        <el-table-column label="创建时间" prop="CreatedOn" width="150px"></el-table-column>
      </el-table>

    </div>
    <!-- 分页 -->
    <div class="pad_15 text_right">
      <el-pagination background v-if="paginations.total > 0" @current-change="handleCurrentChange" :current-page.sync="paginations.page" :page-size="paginations.page_size" :layout="paginations.layout" :total="paginations.total"></el-pagination>
    </div>

    <!-- 客户详情面板 -->
    <work-customer-detail
      :visible.sync="customerDetailVisible"
      :customerID="CustomerID"
      :customerName="customerInfo.Name"
      ref="customerDetail"
      :isCustomerPhoneNumberView="isCustomerPhoneNumberView"
      :isCustomerPhoneNumberModify="isCustomerPhoneNumberModify"
      :isCustomerBasicInformationModify="isCustomerBasicInformationModify"
      :isCustomerServicerModify="isCustomerServicerModify"
      :isCustomerBasicFileModify="isCustomerBasicFileModify"
      :appointmentID="customerInfo.AppointmentID"
      :appointmentStatus="customerInfo.AppointmentStatus"
      :leadStatus="customerInfo.Status"
      :leadID="customerInfo.LeadID"
      :followUpLayoutType="'appointment'"
      @appointmentUpdated="handleAppointmentUpdated"
      @openFollowUp="handleCustomerDetailFollowUp"
      @refreshList="handleRefreshList"
    ></work-customer-detail>

    <!-- 新建、处理跟进弹出框 -->
    <el-dialog :title="isAdd ? '新建跟进' : '处理跟进'" :visible.sync="dialogVisible" width="980px" custom-class="custom-dialog" @close="closeAddFollowUpDialog" :close-on-click-modal="false">
      <el-scrollbar class="el_scrollbar_height_followup">
        <el-autocomplete
          popper-class="customer-autocomplete"
          prefix-icon="el-icon-user-solid"
          v-model="customerName"
          style="width: 480px; margin-left: 30px; margin-bottom: 10px"
          size="small"
          placeholder="请输入客户姓名、手机号、编号查找，无匹配按回车新增"
          :fetch-suggestions="saleCustomerData"
          @select="handleCustomerSelect"
          :popper-append-to-body="false"
          :disabled="customerID != null"
          :trigger-on-focus="false"
          :hide-loading="false"
          :highlight-first-item="true"
          :select-when-unmatched="true"
          v-if="isAdd"
        >
          <template slot="append">
            <el-button icon="el-icon-delete" @click="removeCustomer"></el-button>
          </template>
          <template slot-scope="{ item }">
            <div class="name">
              {{ item.Name }}
              <el-tag size="mini" v-if="item.CustomerLevelName">{{ item.CustomerLevelName }} </el-tag>
            </div>
            <div class="info">手机号：{{ item.PhoneNumber | hidephone }}</div>
            <span class="info" v-if="item.Code">客户编号：{{ item.Code }}</span>
            <span class="info" v-if="item.EntityName">所属组织：{{ item.EntityName }}</span>
            <div class="info" v-if="item.ChannelDeveloperList && item.ChannelDeveloperList.length > 0">
              开发人员：
              <el-tooltip placement="top">
                <div slot="content">{{ getChannelNames(item.ChannelDeveloperList) }}</div>
                <span>{{ getChannelNames(item.ChannelDeveloperList) }}</span>
              </el-tooltip>
            </div>
            <div class="info" v-if="item.ChannelConsultantList && item.ChannelConsultantList.length > 0">
              市场咨询：
              <el-tooltip placement="top">
                <div slot="content">{{ getChannelNames(item.ChannelConsultantList) }}</div>
                <span>{{ getChannelNames(item.ChannelConsultantList) }}</span>
              </el-tooltip>
            </div>
          </template>
        </el-autocomplete>
        <div class="information" v-if="!isAdd">
          <el-row type="flex" align="" style="border-bottom: 1px solid #cfcfcf; padding-bottom: 5px">
            <el-col :span="2">
              <el-avatar :size="50" :src="circleUrl"></el-avatar>
            </el-col>
            <el-col :span="22">
              <el-row type="flex" justify="space-between">
                <el-col :span="24">
                  <strong class="marrt_5 font_18">{{ customerDetail.Name }}</strong>
                  <el-image v-if="customerDetail.Gender == 2" style="width: 8px; height: 12px" :src="require('@/assets//img//gender-female.png')"></el-image>
                  <el-image v-if="customerDetail.Gender == 1" style="width: 8px; height: 12px" :src="require('@/assets/img/gender-male.png')"></el-image>
                </el-col>
              </el-row>
              <el-col justify="space-between">
                <el-col :span="8" class="color_999 martp_10"
                  >手机号：<span class="color_333">{{ customerDetail.PhoneNumber | hidephone }}</span></el-col
                >
                <el-col :span="8" class="color_999 martp_10"
                  >会员编号：<span class="color_333">{{ customerDetail.Code }}</span></el-col
                >
                <el-col :span="8" class="color_999 martp_10"
                  >注册时间：<span class="color_333">{{ customerDetail.CreatedOn | dateFormat("YYYY-MM-DD HH:mm") }}</span></el-col
                >
                <el-col :span="8" class="color_999 martp_10"
                  >介绍人：<span class="color_333">{{ customerDetail.IntroducerName }}</span></el-col
                >
                <el-col :span="8" class="color_999 martp_10"
                  >渠道来源：<span class="color_333">{{ customerDetail.ChannelName }}</span></el-col
                >
                <el-col :span="8" class="color_999 martp_10"
                  >线索来源：<span class="color_333">{{ selectFollowUpRow.LeadSourceName }}</span></el-col
                >
              </el-col>
            </el-col>
          </el-row>
          <el-row class="martp_5">
            <el-col :span="24">
              <el-col :span="3">指派人员：</el-col>
              <el-col :span="21">{{ selectFollowUpRow.CreatedByName }}</el-col>
            </el-col>
            <el-col :span="24" v-if="PlannedRemark">
              <el-col :span="3">任务备注：</el-col>
              <el-col :span="21"> {{ selectFollowUpRow.PlannedRemark }}</el-col>
            </el-col>
          </el-row>
        </div>
        <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="110px" size="small">
          <el-form-item label="跟进方式" prop="FollowUpMethodID">
            <el-radio-group v-model="ruleForm.FollowUpMethodID">
              <el-radio v-for="item in clueTableDataMethod" :key="item.ID" :label="item.ID">{{ item.Name }}</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="跟进状态" prop="FollowUpStatusID">
            <el-radio-group v-model="ruleForm.FollowUpStatusID">
              <el-radio v-for="item in clueTableDataStatus" :key="item.ID" :label="item.ID">{{ item.Name }}</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="跟进记录" prop="FollowUpContent">
            <el-input rows="4" type="textarea" v-model="ruleForm.FollowUpContent"></el-input>
          </el-form-item>
          <el-form-item label="">
            <el-upload :limit="9" class="avatar-uploader" list-type="picture-card" action="#" :file-list="ruleForm.Attachment" :before-upload="commodityMainbeforeUpload" :on-remove="commodityMainRemove" accept="image/*" multiple>
              <i class="el-icon-plus avatar-uploader-icon"></i>
              <div slot="file" slot-scope="{ file }" style="height: 100px; width: 100px">
                <el-image :id="file.uid" :src="file.AttachmentURL" :preview-src-list="preview_src_list" :z-index="9999" fit="cover" style="height: 100px; width: 100px"></el-image>
                <span class="el-upload-list__item-actions">
                  <span class="el-upload-list__item-preview" @click="DialogPreview(file)">
                    <i class="el-icon-zoom-in"></i>
                  </span>
                  <span class="el-upload-list__item-preview" @click="commodityMainRemove(file)">
                    <i class="el-icon-delete"></i>
                  </span>
                </span>
              </div>
            </el-upload>
          </el-form-item>
          <el-form-item label="下次跟进计划" prop="IsNextFollowUp">
            <el-radio-group v-model="ruleForm.IsNextFollowUp">
              <el-radio :label="true">是</el-radio>
              <el-radio :label="false">否</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item
            v-if="ruleForm.IsNextFollowUp"
            prop="PlannedOn"
            label="下次跟进时间"
            :rules="[
              {
                required: ruleForm.IsNextFollowUp,
                message: '请选择下次跟进时间',
                trigger: 'change',
              },
            ]"
          >
            <el-date-picker v-model="ruleForm.PlannedOn" type="datetime" format="yyyy-MM-dd HH:mm" :default-time="nextDateTime" placeholder="请选择下次跟进日期"> </el-date-picker>
          </el-form-item>
          <el-form-item v-if="ruleForm.IsNextFollowUp" label="计划备注">
            <el-input type="textarea" rows="3" v-model="ruleForm.PlannedRemark"></el-input>
          </el-form-item>
        </el-form>
      </el-scrollbar>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false" v-prevent-click size="small">取 消</el-button>
        <el-button :loading="modalLoading" type="primary" v-prevent-click size="small" @click="submitFollowUp">保 存</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import API from "@/api/iBeauty/Workbench/customerServiceAppointment";
import workCustomerDetail from "@/views/iBeauty/Workbench/Component/customerServiceCustomerDetail";
// 跟进相关API
import FollowUpAPI from "@/api/iBeauty/Workbench/followUp";
import CustomerAPI from "@/api/iBeauty/Order/saleGoods";
import APIFollowUp from "@/api/KHS/Setting/followUpConfig.js";
import * as CommonAPI from "@/api/index";

export default {
  name: "CustomerServiceAppointment",
  components: {
    workCustomerDetail,
  },
  data() {
    return {
      loading: false,
      tableHeight: 500,
      appointmentList: [],
      appointmentByList: [], // 预约人员列表
      entityList: [], // 门店列表
      // 客户详情相关
      customerDetailVisible: false,
      CustomerID: null,
      customerInfo: {
        Name: "",
        ID: "",
      },
      // 权限控制
      isCustomerPhoneNumberView: false,
      isCustomerPhoneNumberModify: false,
      isCustomerBasicInformationModify: false,
      isCustomerServicerModify: false,
      isCustomerBasicFileModify: false,
      CustomerCases: {},
      search: {
        CustomerName: "", // 客户信息搜索
        PhoneNumber: "", // 手机号搜索
        AppointmentStatus: "", // 预约状态搜索
        LeadSource: "", // 线索来源搜索
        AppointmentDate: [], // 预约时间搜索 - 将在mounted中设置默认值
        AppointmentBy: "", // 预约人员搜索
        EntityID: "", // 预约门店搜索
        IsDeal: "", // 成交会员搜索
      },
      //需要给分页组件传的信息
      paginations: {
        page: 1, // 当前位于哪页
        total: 0, // 总数
        page_size: 10, // 1页显示多少条
        layout: "total, prev, pager, next,jumper", // 翻页属性
      },
      // 跟进弹框相关数据
      dialogVisible: false, // 跟进弹框显示状态
      isAdd: true, // 是否新建跟进
      modalLoading: false, // 跟进弹框加载状态
      customerName: "", // 客户搜索名称
      customerID: null, // 当前跟进的客户ID
      leadID: null, // 线索ID
      customerDetail: {}, // 客户详情
      selectFollowUpRow: {}, // 选中的跟进行数据
      PlannedRemark: "", // 计划备注
      circleUrl: "https://cube.elemecdn.com/3/7c/********************************.png",
      // 跟进表单数据
      ruleForm: {
        FollowUpMethodID: "",
        FollowUpStatusID: "",
        FollowUpContent: "",
        IsNextFollowUp: false,
        PlannedOn: "",
        PlannedRemark: "",
        Attachment: [],
      },
      // 跟进表单验证规则
      rules: {
        FollowUpMethodID: [{ required: true, message: "请选择跟进方式", trigger: "change" }],
        FollowUpContent: [{ required: true, message: "请输入跟进记录", trigger: "blur" }],
      },
      // 跟进方式和状态数据
      clueTableDataMethod: [], // 跟进方式列表
      clueTableDataStatus: [], // 跟进状态列表
      nextDateTime: "09:00:00", // 默认下次跟进时间
      preview_src_list: [], // 图片预览列表
    };
  },
  mounted() {
    // 设置默认日期范围
    this.search.AppointmentDate = this.getDefaultDateRange();
    this.initPage();

    // 监听窗口大小变化
    window.addEventListener('resize', this.calculateTableHeight);

    // 设置客户详情权限
    this.initCustomerDetailPermissions();

    // 初始化跟进相关数据
    this.getFollowUpMethodAndStatus();
  },

  beforeDestroy() {
    // 移除窗口大小变化监听
    window.removeEventListener('resize', this.calculateTableHeight);
  },
  methods: {
    // 获取默认日期范围（今天）
    getDefaultDateRange() {
      const today = new Date();
      const todayStr = today.getFullYear() + '-' + String(today.getMonth() + 1).padStart(2, '0') + '-' + String(today.getDate()).padStart(2, '0');
      return [todayStr, todayStr];
    },

    // 初始化页面
    initPage() {
      this.calculateTableHeight();
      this.getAppointmentByList();
      this.getEntityList();
      this.getAppointmentList();
    },

    // 计算表格高度
    calculateTableHeight() {
      this.$nextTick(() => {
        const windowHeight = window.innerHeight;
        // 减去页面头部、搜索区域、分页等占用的高度
        // 系统头部约60px + 搜索区域约100px + 分页约60px + 边距约60px = 280px
        let calculatedHeight = windowHeight - 280;

        // 确保最小高度，能显示至少10行数据
        // 表头约40px + 10行数据(每行约35px) + 滚动条等 = 约400px
        this.tableHeight = Math.max(calculatedHeight, 450);
      });
    },

    // 获取预约人员列表（与线索跟进的跟进人员使用同一个接口和数据结构）
    async getAppointmentByList() {
      try {
        const res = await API.getAppointmentByList({});
        if (res.StateCode === 200) {
          this.appointmentByList = res.Data || [];
        }
      } catch (error) {
        console.error("获取预约人员列表失败:", error);
        this.appointmentByList = [];
      }
    },

    // 获取门店列表（复制自线索跟进页面）
    async getEntityList() {
      try {
        const params = {};
        const res = await API.getEntityList(params);
        if (res.StateCode === 200) {
          this.entityList = (res.Data || []).map(item => ({
            ...item,
            ID: String(item.ID)
          }));
        } else {
          this.$message.error(res.Message || '获取门店列表失败');
        }
      } catch (error) {
        console.error('获取门店列表失败:', error);
        this.$message.error('获取门店列表失败，请稍后重试');
      }
    },

    // 获取预约列表数据
    async getAppointmentList() {
      this.loading = true;
      try {
        const params = {
          PageNum: this.paginations.page,
          PageSize: this.paginations.page_size,
          CustomerName: this.search.CustomerName || "",
          AppointmentStatus: this.search.AppointmentStatus || "",
          LeadSource: this.search.LeadSource || "",
          AppointmentStartDate: this.search.AppointmentDate && this.search.AppointmentDate[0] ? this.search.AppointmentDate[0] : "",
          AppointmentEndDate: this.search.AppointmentDate && this.search.AppointmentDate[1] ? this.search.AppointmentDate[1] : "",
          AppointmentBy: this.search.AppointmentBy || "",
          EntityID: this.search.EntityID || "",
        };

        // 成交状态特殊处理，确保false值也能正确传递
        if (this.search.IsDeal !== "") {
          params.IsDeal = this.search.IsDeal;
        }

        // 移除空值参数
        Object.keys(params).forEach(key => {
          if (params[key] === "" || params[key] === null || params[key] === undefined) {
            delete params[key];
          }
        });

        const res = await API.getCustomerServiceAppointmentList(params);
        if (res.StateCode === 200) {
          this.appointmentList = res.List || [];
          this.paginations.total = res.Total || 0;
        } else {
          this.$message.error(res.Message || "查询失败");
        }
      } catch (error) {
        console.error("获取预约列表失败:", error);
        this.$message.error("获取数据失败");
      } finally {
        this.loading = false;
      }
    },

    // 搜索
    handleSearch() {
      this.paginations.page = 1;
      this.getAppointmentList();
    },

    // 重置搜索条件
    handleReset() {
      this.search = {
        CustomerName: "",
        PhoneNumber: "",
        AppointmentStatus: "",
        LeadSource: "",
        AppointmentDate: this.getDefaultDateRange(),
        AppointmentBy: "",
        EntityID: "",
        IsDeal: "", // 成交会员重置
      };
      this.handleSearch();
    },

    // 当前页改变
    handleCurrentChange(val) {
      this.paginations.page = val;
      this.getAppointmentList();
    },

    // 获取预约状态文本
    getAppointmentStatusText(status) {
      const statusMap = {
        "10": "未到店",
        "20": "已到店", 
        "30": "已取消"
      };
      return statusMap[status] || "未知";
    },

    // 获取预约状态标签类型
    getAppointmentTagType(status) {
      switch (status) {
        case "10": return "warning"; // 未到店 - 橙色
        case "20": return "success"; // 已到店 - 绿色  
        case "30": return "danger";  // 已取消 - 红色
        default: return "info";
      }
    },

    // 格式化预约时间显示格式：从 "2025-08-05 09:30:00" 转换为 "09:30 2025-08-05"
    formatAppointmentTime(dateTimeStr) {
      if (!dateTimeStr) {
        return { time: '', date: '' };
      }
      
      // 分割日期和时间部分
      const parts = dateTimeStr.split(' ');
      if (parts.length < 2) {
        return { time: '', date: dateTimeStr };
      }
      
      const datePart = parts[0]; // 2025-08-05
      const timePart = parts[1]; // 09:30:00
      
      // 提取时分（去掉秒）
      const timeOnly = timePart.substring(0, 5); // 09:30
      
      return {
        time: timeOnly,
        date: datePart
      };
    },

    // 客户名称点击事件
    handleCustomerNameClick(row) {
      let that = this;
      // 检查必要参数
      if (!row.CustomerID) {
        that.$message.warning('客户ID不存在，无法查看详情');
        return;
      }

      // 设置客户详情数据
      console.log('设置客户详情可见性为 true');
      that.customerDetailVisible = true;
      that.CustomerID = parseInt(row.CustomerID); // 确保是 Number 类型
      console.log('设置 CustomerID:', that.CustomerID);
      console.log('customerDetailVisible 状态:', that.customerDetailVisible);
      that.customerInfo = {
        Name: row.CustomerName || '',
        ID: row.CustomerID,
        // 客服预约表中的预约状态信息
        HasAppointment: true, // 客服预约表中的都是已预约的
        AppointmentStatus: row.AppointmentStatus || "10", // 预约状态，确保是字符串类型
        AppointmentID: row.AppointmentID, // 预约ID
        Status: 1, // 默认状态
        LeadID: row.LeadID, // 从列表接口中获取线索ID
      };

      console.log('打开客户详情:', {
        CustomerID: row.CustomerID,
        CustomerName: row.CustomerName,
        AppointmentStatus: row.AppointmentStatus,
        HasAppointment: true,
        AppointmentID: row.AppointmentID
      });

      // 强制更新客户详情组件
      that.$nextTick(() => {
        console.log('nextTick 中强制更新客户详情组件');
        if (that.$refs.customerDetail) {
          console.log('找到客户详情组件引用，手动显示');
          that.$refs.customerDetail.showCustomerDetail(that.CustomerID, that.customerInfo.Name);
        } else {
          console.log('未找到客户详情组件引用');
        }
      });
    },

    // 初始化客户详情权限
    initCustomerDetailPermissions() {
      // 设置客户详情相关权限，可以根据实际需要调整权限代码
      // 这里先设置为true，让功能可用，实际项目中应该根据具体权限配置
      this.isCustomerPhoneNumberView = true; // 允许查看手机号
      this.isCustomerPhoneNumberModify = true; // 允许修改手机号
      this.isCustomerBasicInformationModify = true; // 允许修改基本信息
      this.isCustomerServicerModify = true; // 允许修改服务人员
      this.isCustomerBasicFileModify = true; // 允许修改基本档案

      // 如果需要根据实际权限控制，可以使用以下代码：
      // this.isCustomerPhoneNumberView = this.$permission.permission(this.$route.meta.Permission, "CustomerServiceAppointment-CustomerPhoneNumberView");
      // this.isCustomerPhoneNumberModify = this.$permission.permission(this.$route.meta.Permission, "CustomerServiceAppointment-CustomerPhoneNumberModify");
      // this.isCustomerBasicInformationModify = this.$permission.permission(this.$route.meta.Permission, "CustomerServiceAppointment-CustomerBasicInformationModify");
      // this.isCustomerServicerModify = this.$permission.permission(this.$route.meta.Permission, "CustomerServiceAppointment-CustomerServicerModify");
      // this.isCustomerBasicFileModify = this.$permission.permission(this.$route.meta.Permission, "CustomerServiceAppointment-CustomerBasicFileModify");
    },

    // 处理预约更新事件
    handleAppointmentUpdated() {
      let that = this;

      // 刷新当前页面数据
      that.getAppointmentList();
    },

    // 处理客户详情关闭时的刷新列表事件
    handleRefreshList() {
      let that = this;
      console.log('客户详情关闭，刷新预约列表');

      // 保持当前查询参数，刷新列表数据
      that.getAppointmentList();
    },

    // 处理客户详情中跟进记录的跟进事件
    handleCustomerDetailFollowUp(followUpData) {
      let that = this;
      console.log('跟进数据:', followUpData); // 调试信息

      // 使用与线索列表跟进按钮相同的逻辑
      that.isAdd = true; // 这是新建跟进
      that.customerID = followUpData.CustomerID;
      that.leadID = followUpData.LeadID; // 从列表接口中获取线索ID
      that.customerName = followUpData.CustomerName || followUpData.Name;
      that.CreatedByName = followUpData.CreatedByName || '';
      that.PlannedRemark = '';
      that.nextDateTime = this.$formatDate(new Date(), "hh:mm:ss");

      // 重置loading状态，确保按钮可以正常使用
      that.modalLoading = false;

      // 重置表单
      that.resetFollowUpForm();

      // 打开跟进弹框
      that.dialogVisible = true;

      // 获取跟进方式和状态数据
      that.getFollowUpMethodAndStatus();
    },



    // 重置跟进表单
    resetFollowUpForm() {
      this.ruleForm = {
        FollowUpMethodID: "", // 跟进方式
        FollowUpStatusID: "", // 跟进状态
        FollowUpContent: "", // 跟进记录
        PlannedOn: "", // 计划跟进时间
        IsNextFollowUp: true, // 下次是否跟进
        PlannedRemark: "", // 计划跟进备注
        Attachment: [],
      };
      if (this.$refs.ruleForm) {
        this.$refs["ruleForm"].resetFields();
      }
    },

    // 关闭跟进弹框
    closeAddFollowUpDialog() {
      this.ruleForm.Attachment = [];
      this.resetFollowUpForm();
    },

    // 获取跟进方式和状态数据
    async getFollowUpMethodAndStatus() {
      try {
        // 获取跟进方式
        const methodRes = await APIFollowUp.getFollowUpMethod({});
        if (methodRes.StateCode === 200) {
          this.clueTableDataMethod = methodRes.Data || [];
        }

        // 获取跟进状态
        const statusRes = await APIFollowUp.getFollowUpStatus({});
        if (statusRes.StateCode === 200) {
          this.clueTableDataStatus = statusRes.Data || [];
        }
      } catch (error) {
        console.error('获取跟进方式和状态失败:', error);
      }
    },

    // 客户搜索
    async saleCustomerData(queryString, cb) {
      if (!queryString || queryString.length < 2) {
        cb([]);
        return;
      }

      try {
        const params = {
          Name: queryString,
          PageNum: 1,
          PageSize: 20,
        };
        const res = await CustomerAPI.getSaleCustomer(params);
        if (res.StateCode === 200) {
          const customers = res.Data.map(item => ({
            ...item,
            value: item.Name, // autocomplete需要的value字段
          }));
          cb(customers);
        } else {
          cb([]);
        }
      } catch (error) {
        console.error('客户搜索失败:', error);
        cb([]);
      }
    },

    // 选择客户
    handleCustomerSelect(item) {
      this.customerID = item.ID;
      this.customerName = item.Name;
    },

    // 移除客户
    removeCustomer() {
      this.customerID = null;
      this.customerName = "";
    },

    // 获取渠道名称
    getChannelNames(channelList) {
      if (!channelList || !Array.isArray(channelList)) return '';
      return channelList.map(item => item.Name || item.EmployeeName).join(', ');
    },

    // 提交跟进
    submitFollowUp() {
      let that = this;
      if (!that.customerID) {
        this.$message.error('请选择客户');
        return;
      }

      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          that.modalLoading = true;
          let params = that.ruleForm;
          params.CustomerID = that.customerID;
          params.LeadID = that.leadID; // 添加线索ID
          params.PlannedOn = this.ruleForm.PlannedOn ? this.$formatDate(this.ruleForm.PlannedOn, "YYYY-MM-DD hh:mm") : "";

          console.log('提交跟进参数:', params); // 调试信息
          console.log('CustomerID:', params.CustomerID, 'LeadID:', params.LeadID); // 详细调试

          FollowUpAPI.followUpByLead(params)
            .then(function (res) {
              if (res.StateCode === 200) {
                that.$message.success({
                  message: "新建成功",
                  duration: 2000,
                });
                that.dialogVisible = false;
                that.resetFollowUpForm();

                // 刷新客户详情中的跟进记录
                if (that.$refs.customerDetail) {
                  that.$nextTick(() => {
                    // 刷新客户详情弹框中的跟进记录
                    if (that.$refs.customerDetail.$refs.followUpRecord) {
                      that.$refs.customerDetail.$refs.followUpRecord.getCustomerFollowUp();
                    }
                  });
                }
              } else {
                that.$message.error({
                  message: res.Message || '保存失败',
                  duration: 2000,
                });
              }
            })
            .catch(function (error) {
              console.error('提交跟进失败:', error);
              that.$message.error('保存失败');
            })
            .finally(function () {
              that.modalLoading = false;
            });
        }
      });
    },

    // 图片上传前处理
    async commodityMainbeforeUpload(file) {
      const isImage = file.type.indexOf('image') !== -1;
      const isLt10M = file.size / 1024 / 1024 < 10;

      if (!isImage) {
        this.$message.error('只能上传图片文件!');
        return false;
      }
      if (!isLt10M) {
        this.$message.error('上传图片大小不能超过 10MB!');
        return false;
      }

      try {
        const formData = new FormData();
        formData.append('file', file);

        const res = await CommonAPI.POST('api/upload/addAttachment', formData);
        if (res.StateCode === 200) {
          this.ruleForm.Attachment.push({
            uid: file.uid,
            name: file.name,
            AttachmentURL: res.Data.AttachmentURL,
            AttachmentID: res.Data.AttachmentID,
          });
          this.updatePreviewList();
        } else {
          this.$message.error('图片上传失败');
        }
      } catch (error) {
        console.error('图片上传失败:', error);
        this.$message.error('图片上传失败');
      }

      return false; // 阻止默认上传行为
    },

    // 移除图片
    commodityMainRemove(file) {
      const index = this.ruleForm.Attachment.findIndex(item => item.uid === file.uid);
      if (index > -1) {
        this.ruleForm.Attachment.splice(index, 1);
        this.updatePreviewList();
      }
    },

    // 更新预览列表
    updatePreviewList() {
      this.preview_src_list = this.ruleForm.Attachment.map(item => item.AttachmentURL);
    },

    // 图片预览
    DialogPreview() {
      // Element UI 的图片预览会自动处理
    }
  }
};
</script>

<style lang="scss" scoped>
.customerServiceAppointment {
  .nav_header {
    background-color: #f5f7fa;
    padding: 15px;
    border-radius: 4px;
    margin-bottom: 15px;
  }

  .table-container {
    background-color: #fff;
    padding: 15px;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  // 预约时间列样式
  .appointment-time {
    display: flex;
    flex-direction: column;
    align-items: center;
    line-height: 1.2;
    
    // 时分部分样式 - 醒目显示
    .time-part {
      font-size: 16px;
      font-weight: 700;
      color: #409EFF;
      margin-bottom: 2px;
      letter-spacing: 0.5px;
    }
    
    // 日期部分样式 - 较小字体
    .date-part {
      font-size: 12px;
      font-weight: 400;
      color: #666;
    }
  }

  // 预约时间列表头样式
  ::v-deep .appointment-time-column .cell {
    font-weight: 600;
    color: #303133;
    background: linear-gradient(90deg, #f0f9ff 0%, #ffffff 100%);
    padding: 8px 12px;
    border-radius: 4px;
  }

  // 客户信息样式
  .customer-info {
    display: flex;
    align-items: center;
    gap: 8px;
    flex-wrap: wrap;
  }

  .customer-name {
    font-weight: 500;
    color: #303133;
    font-size: 14px;
  }

  // 可点击的客户名称样式
  .clickable-customer-name {
    transition: all 0.3s ease;

    &:hover {
      color: #66b1ff !important;
      font-weight: bold;
      transform: scale(1.05);
    }

    &:active {
      color: #3a8ee6 !important;
      transform: scale(0.98);
    }
  }

  // 客户等级标签样式
  .customer-level-tag {
    background: linear-gradient(135deg, #ff9800 0%, #ff5722 100%) !important;
    border: none !important;
    color: #fff !important;
    font-weight: 500 !important;
    border-radius: 12px !important;
    padding: 2px 8px !important;
    font-size: 11px !important;
    box-shadow: 0 2px 4px rgba(255, 152, 0, 0.3) !important;
    transition: all 0.3s ease !important;
    
    &:hover {
      transform: translateY(-1px) !important;
      box-shadow: 0 4px 8px rgba(255, 152, 0, 0.4) !important;
    }
  }

  // 跟进弹框样式
  ::v-deep .custom-dialog {
    .el-dialog__body {
      padding: 10px 20px;
    }
  }

  .el_scrollbar_height_followup {
    height: 500px;
  }

  .customer-autocomplete {
    .name {
      font-weight: 500;
      color: #303133;
    }

    .info {
      font-size: 12px;
      color: #909399;
      margin-right: 10px;
    }
  }

  .information {
    margin: 15px 30px;
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 4px;
  }

  .avatar-uploader {
    .el-upload {
      border: 1px dashed #d9d9d9;
      border-radius: 6px;
      cursor: pointer;
      position: relative;
      overflow: hidden;
    }

    .el-upload:hover {
      border-color: #409EFF;
    }

    .avatar-uploader-icon {
      font-size: 28px;
      color: #8c939d;
      width: 100px;
      height: 100px;
      line-height: 100px;
      text-align: center;
    }
  }
}
</style>
