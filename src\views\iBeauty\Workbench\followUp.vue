<template>
  <div class="followUp content_body">
    <!-- 搜索 -->
    <div class="nav_header">
      <el-form :inline="true" size="small" @keyup.enter.native="handleSearch">
        <el-form-item label="客户信息">
          <el-input size="small" v-model="search.Name" @clear="handleSearch" clearable placeholder="输入姓名、手机号或客户编号"></el-input>
        </el-form-item>

        <el-form-item label="跟进方式">
          <el-select placeholder="请选择跟进方式" filterable clearable v-model="search.FollowUpMethodID" @change="handleSearch" size="small">
            <el-option v-for="item in tableDataMethod" :key="item.ID" :label="item.Name" :value="item.ID"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="跟进状态">
          <el-select placeholder="请选择跟进状态" filterable clearable v-model="search.FollowUpStatusID" @change="handleSearch" size="small">
            <el-option v-for="item in tableDataStatus" :key="item.ID" :label="item.Name" :value="item.ID"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="是否跟进">
          <el-select placeholder="请选择是否跟进" clearable v-model="search.IsFollowUp" @change="handleSearch" size="small">
            <el-option label="已跟进" :value="true"></el-option>
            <el-option label="待跟进" :value="false"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="计划跟进时间">
          <el-date-picker
            v-model="search.QueryDate"
            unlink-panels
            type="daterange"
            range-separator="至"
            value-format="yyyy-MM-dd"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            @change="handleSearch"
          >
          </el-date-picker>
        </el-form-item>

        <el-form-item label="跟进人员" v-if="isShowFollowUp">
          <el-select placeholder="请选择跟进人员" filterable clearable v-model="search.FollowUpBy" @change="handleSearch" size="small">
            <el-option v-for="item in followUpEmployeeData" :key="item.ID" :label="item.Name" :value="item.ID"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="跟进部门" v-if="isShowFollowUp">
          <el-select placeholder="请选择跟进部门" filterable clearable v-model="search.FollowUpEntity" @change="handleSearch" size="small">
            <el-option v-for="item in followUpEntityData" :key="item.ID" :label="item.Name" :value="item.ID"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="渠道信息">
          <el-input size="small" v-model="search.ChannelName" @clear="handleSearch" clearable placeholder="输入渠道名称"></el-input>
        </el-form-item>
        <el-form-item label="会员等级" >
          <el-select placeholder="请选择会员等级" filterable clearable v-model="search.CustomerLevelID" @change="handleSearch" size="small">
            <el-option v-for="item in customerLevelList" :key="item.ID" :label="item.Name" :value="item.ID"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item v-if="isShowFollowUp">
          <el-checkbox v-model="search.IsShowOwnFollowUp" @change="IsShowOwnFollowUpChange()">显示自己的跟进</el-checkbox>
        </el-form-item>
        <el-form-item>
          <el-button size="small" type="primary" @click="handleSearch">搜索</el-button>
        </el-form-item>
        <el-form-item>
          <el-button size="small" type="primary" @click="addFollowUp">新增</el-button>
        </el-form-item>


        <el-form-item>
          <el-dropdown @command="customer_Export" :loading="downloadLoading">
            <el-button type="primary"> 导出<i class="el-icon-arrow-down el-icon--right"></i> </el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item command="excelNoDisPlayPhone">导出</el-dropdown-item>
              <el-dropdown-item command="excelDisPlayPhone">导出(手机号)</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
          <!-- <el-button @click="customer_Export('excelNoDisPlayPhone')"  type="primary" v-prevent-click :loading="downloadLoading"> 导出 </el-button>
          <el-button @click="customer_Export('excelDisPlayPhone')"  type="primary" v-prevent-click :loading="downloadLoading"> 导出(手机号）</el-button> -->
        </el-form-item>
        <!-- <el-form-item>
          <el-button size="small" type="primary" @click="followUp_excelDisPlayPhone" :loading="downloadLoading">导出</el-button>
        </el-form-item>

        <el-form-item>
          <el-button size="small" type="primary" @click="followUp_excelNoDisPlayPhone" :loading="downloadNoDisPlayLoading">隐藏手机号导出</el-button>
        </el-form-item> -->
      </el-form>
    </div>
    <!-- 表格 -->
    <el-table size="small" highlight-current-row :data="tableData" tooltip-effect="light" @row-click="rowClick">
      <el-table-column fixed label="操作" width="80px">
        <template slot-scope="scope">
          <el-button type="primary" size="small" @click.stop="followUpClick(scope.row)" v-if="scope.row.IsFollowUp == 0">跟进</el-button>
        </template>
      </el-table-column>
      <el-table-column label="是否跟进" prop="IsFollowUp">
        <template slot-scope="scope">
          <span v-if="scope.row.IsFollowUp == 1" class="color_green">已跟进</span>
          <span v-if="scope.row.IsFollowUp == 0" class="color_orange">待跟进</span>
        </template>
      </el-table-column>
      <el-table-column label="指派/创建时间" prop="CreatedOn" width="140px">
        <template slot-scope="scope">
          {{ scope.row.CreatedOn | dateFormat('YYYY-MM-DD HH:mm') }}
        </template>
      </el-table-column>
      <el-table-column label="计划跟进时间" prop="PlannedOn" width="140px">
        <template slot-scope="scope">
          {{ scope.row.PlannedOn | dateFormat('YYYY-MM-DD HH:mm') }}
        </template>
      </el-table-column>
      <el-table-column label="实际跟进时间" prop="FollowUpOn" width="140px">
        <template slot-scope="scope">
          {{ scope.row.FollowUpOn | dateFormat('YYYY-MM-DD HH:mm') }}
        </template>
      </el-table-column>
      <el-table-column label="指派人" prop="CreatedByName"></el-table-column>
      <el-table-column label="跟进人员" prop="FollowUpByName"></el-table-column>
      <el-table-column label="跟进方式" prop="MethodName"></el-table-column>
      <el-table-column label="跟进状态" prop="Status" width="100"></el-table-column>
      <el-table-column label="客户" prop="CustomerName" width="150px"> 
        <template slot-scope="scope">
          <div>{{ scope.row.CustomerName }} <span v-if="scope.row.Code">({{ scope.row.Code }})</span> </div>
          <div>{{ scope.row.PhoneNumber | hidephone }}</div>
        </template>
      </el-table-column>
      <el-table-column label="性别" prop="Gender" :formatter="formatGender"></el-table-column>
      <!-- <el-table-column label="手机" prop="PhoneNumber" width="100px"></el-table-column> -->
      <!-- <el-table-column label="编号" prop="Code"></el-table-column> -->

      <el-table-column label="渠道" prop="ChannelName" width="100px"></el-table-column>
      <el-table-column label="会员等级" prop="CustomerLevelName"></el-table-column>


      <el-table-column label="服务人员">
        <template slot-scope="scope">
          <el-tooltip placement="top" effect="light">
            <div slot="content">
              <el-descriptions size="mini" :column="1" border :colon="false" labelClassName="custom-customer-descLabel">
                <el-descriptions-item v-for="(item, index) in scope.row.ServicerEmployee" :key="index" :label="item.Name + '：'">
                  {{ getServicerEmpNames(item.ServicerEmpList) }}
                </el-descriptions-item>
              </el-descriptions>
            </div>
            <div style="text-overflow: ellipsis; white-space: nowrap; overflow: hidden">
              {{ getFirstServiceEmp(scope.row.ServicerEmployee) }}
            </div>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column label="信息来源" prop="CustomerSourceName"></el-table-column>
      <el-table-column label="生日" width="120">
        <template slot-scope="scope">
          <span v-if="scope.row.Birthday">{{ scope.row.BirthdayType == 10 ? '公历 ' + scope.row.Birthday : '农历 ' + scope.row.Birthday }}</span>
        </template>
      </el-table-column>
      <el-table-column label="所属组织" prop="EntityName" width="120"></el-table-column>
      <el-table-column label="注册日期" prop="CustomerCreatedOn" width="140">
        <template slot-scope="scope">
          {{ scope.row.CustomerCreatedOn | dateFormat('YYYY-MM-DD HH:mm') }}
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <div class="pad_15 text_right">
      <el-pagination
        background
        v-if="paginations.total > 0"
        @current-change="handleCurrentChange"
        :current-page.sync="paginations.page"
        :page-size="paginations.page_size"
        :layout="paginations.layout"
        :total="paginations.total"
      ></el-pagination>
    </div>

    <work-customer-detail
      v-if="customerDetailVisible"
      :visible.sync="customerDetailVisible"
      :customerID="CustomerID"
      ref="customerDetail"
      :isCustomerPhoneNumberView="isCustomerPhoneNumberView"
      :isCustomerPhoneNumberModify="isCustomerPhoneNumberModify"
      :isCustomerBasicInformationModify="isCustomerBasicInformationModify"
      :isCustomerServicerModify="isCustomerServicerModify"
      :isCustomerBasicFileModify="isCustomerBasicFileModify"
    ></work-customer-detail>
    <!-- 新建、处理跟进弹出框 -->
    <el-dialog
      :title="isAdd ? '新建跟进' : '处理跟进'"
      :visible.sync="dialogVisible"
      width="980px"
      custom-class="custom-dialog"
      @close="closeAddFollowUpDialog"
      :close-on-click-modal="false"
    >
      <el-scrollbar class="el_scrollbar_height_followup">
        <el-autocomplete
          popper-class="customer-autocomplete"
          prefix-icon="el-icon-user-solid"
          v-model="customerName"
          style="width: 480px; margin-left: 30px; margin-bottom: 10px"
          size="small"
          placeholder="请输入客户姓名、手机号、编号查找，无匹配按回车新增"
          :fetch-suggestions="saleCustomerData"
          @select="handleCustomerSelect"
          :popper-append-to-body="false"
          :disabled="customerID != null"
          :trigger-on-focus="false"
          :hide-loading="false"
          :highlight-first-item="true"
          :select-when-unmatched="true"
          v-if="isAdd"
        >
          <template slot="append">
            <el-button icon="el-icon-delete" @click="removeCustomer"></el-button>
          </template>
          <template slot-scope="{ item }">
            <div class="name">
              {{ item.Name }}
              <el-tag size="mini" v-if="item.CustomerLevelName">{{ item.CustomerLevelName }}</el-tag>
            </div>
            <div class="info">手机号：{{ item.PhoneNumber | hidephone }}</div>
            <div class="info" v-if="item.Code">客户编号：{{ item.Code }}</div>
            <div class="info" v-if="item.EntityName">所属组织：{{ item.EntityName }}</div>
            <div class="info" v-if="item.ChannelName">渠道信息：{{ item.ChannelName }}</div>
          </template>
        </el-autocomplete>
        <div class="information" v-if="!isAdd">
          <el-row type="flex" align="" style="border-bottom: 1px solid #cfcfcf; padding-bottom: 5px">
            <el-col :span="2">
              <el-avatar :size="50" :src="circleUrl"></el-avatar>
            </el-col>
            <el-col :span="22">
              <el-row type="flex" justify="space-between">
                <el-col :span="24">
                  <strong class="marrt_5 font_18">{{ customerDetail.Name }}</strong>
                  <el-image v-if="customerDetail.Gender == 2" style="width: 8px; height: 12px" :src="require('@/assets//img//gender-female.png')"></el-image>
                  <el-image v-if="customerDetail.Gender == 1" style="width: 8px; height: 12px" :src="require('@/assets/img/gender-male.png')"></el-image>
                </el-col>
              </el-row>
              <el-col justify="space-between">
                <el-col :span="8" class="color_999 martp_10"
                  >手机号：<span class="color_333">{{ customerDetail.PhoneNumber | hidephone }}</span></el-col
                >
                <el-col :span="8" class="color_999 martp_10"
                  >客户编号：<span class="color_333">{{ customerDetail.Code }}</span></el-col
                >
                <el-col :span="8" class="color_999 martp_10"
                  >注册时间：<span class="color_333">{{ customerDetail.CreatedOn | dateFormat('YYYY-MM-DD HH:mm') }}</span></el-col
                >
                <el-col :span="8" class="color_999 martp_10"
                  >介绍人：<span class="color_333">{{ customerDetail.IntroducerName }}</span></el-col
                >
                <el-col :span="8" class="color_999 martp_10"
                  >渠道来源：<span class="color_333">{{ customerDetail.ChannelName }}</span></el-col
                >
                <el-col :span="8" class="color_999 martp_10"
                  >信息来源：<span class="color_333">{{ customerDetail.CustomerSourceName }}</span></el-col
                >
              </el-col>
            </el-col>
          </el-row>
          <!-- <el-form size="small"> -->
          <el-row class="martp_5">
            <el-col :span="24">
              <!-- <el-form-item style="margin-bottom: 0"> -->
              <el-col :span="3">指派人员：</el-col>
              <el-col :span="21">{{ selectFollowUpRow.CreatedByName }}</el-col>
              <!-- </el-form-item> -->
            </el-col>
            <el-col :span="24" v-if="PlannedRemark">
              <!-- <el-form-item style="margin-bottom: 0"> -->
              <el-col :span="3">任务备注：</el-col>
              <el-col :span="21"> {{ selectFollowUpRow.PlannedRemark }}</el-col>
              <!-- </el-form-item> -->
            </el-col>
          </el-row>
          <!-- </el-form> -->
        </div>
        <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="110px" size="small">
          <el-form-item label="跟进方式" prop="FollowUpMethodID">
            <el-radio-group v-model="ruleForm.FollowUpMethodID">
              <el-radio v-for="item in tableDataMethod" :key="item.ID" :label="item.ID">{{ item.Name }}</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="跟进状态" prop="FollowUpStatusID">
            <el-radio-group v-model="ruleForm.FollowUpStatusID">
              <el-radio v-for="item in tableDataStatus" :key="item.ID" :label="item.ID">{{ item.Name }}</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="跟进记录" prop="FollowUpContent">
            <el-input rows="4" type="textarea" v-model="ruleForm.FollowUpContent"></el-input>
          </el-form-item>
          <el-form-item label="">
            <el-upload
              :limit="9"
              class="avatar-uploader"
              list-type="picture-card"
              action="#"
              :file-list="ruleForm.Attachment"
              :before-upload="commodityMainbeforeUpload"
              :on-remove="commodityMainRemove"
              accept="image/*"
              multiple
            >
              <i class="el-icon-plus avatar-uploader-icon"></i>
              <div slot="file" slot-scope="{ file }" style="height: 100px; width: 100px">
                <el-image
                  :id="file.uid"
                  :src="file.AttachmentURL"
                  :preview-src-list="preview_src_list"
                  :z-index="9999"
                  fit="cover"
                  style="height: 100px; width: 100px"
                ></el-image>
                <span class="el-upload-list__item-actions">
                  <span class="el-upload-list__item-preview" @click="DialogPreview(file)">
                    <i class="el-icon-zoom-in"></i>
                  </span>
                  <span class="el-upload-list__item-preview" @click="commodityMainRemove(file)">
                    <i class="el-icon-delete"></i>
                  </span>
                </span>
              </div>
            </el-upload>
          </el-form-item>
          <el-form-item label="下次跟进计划" prop="IsNextFollowUp">
            <el-radio-group v-model="ruleForm.IsNextFollowUp">
              <el-radio :label="true">是</el-radio>
              <el-radio :label="false">否</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item
            v-if="ruleForm.IsNextFollowUp"
            prop="PlannedOn"
            label="下次跟进时间"
            :rules="[
              {
                required: ruleForm.IsNextFollowUp,
                message: '请选择下次跟进时间',
                trigger: 'change',
              },
            ]"
          >
            <el-date-picker
              v-model="ruleForm.PlannedOn"
              type="datetime"
              format="yyyy-MM-dd HH:mm"
              :default-time="nextDateTime"
              placeholder="请选择下次跟进日期"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item v-if="ruleForm.IsNextFollowUp" label="计划备注">
            <el-input type="textarea" rows="3" v-model="ruleForm.PlannedRemark"></el-input>
          </el-form-item>
        </el-form>
      </el-scrollbar>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false" v-prevent-click size="small">取 消</el-button>
        <el-button :loading="modalLoading" type="primary" v-prevent-click size="small" @click="submitFollowUp">保 存</el-button>
      </span>
    </el-dialog>
    <!-- 新建会员弹出框 -->
    <!--新增 客户-->
    <add-customer title="新增客户" :visible.sync="isAddCustom" @addCustomerSuccess="addCustomerSuccess"> </add-customer>
    <div v-show="false" style="height: 10px; width: 100%">
      <medicalEditor ref="hiddenMedicalEditor"></medicalEditor>
    </div>
  </div>
</template>

<script>
import API from '@/api/iBeauty/Workbench/followUp';
import CustomerAPI from '@/api/iBeauty/Order/saleGoods';
import cusAPI from '@/api/CRM/Customer/customer';
import APIFollowUp from '@/api/KHS/Setting/followUpConfig.js';

// import workCustomerDetail from "@/views/iBeauty/Workbench/Component/workbenchCustomerDetail";
import '@riophae/vue-treeselect/dist/vue-treeselect.css';
import addCustomer from '@/views/CRM/Customer/Components/CustomerDetail/addCustomer.vue';
import APIUpload from '@/api/Common/uploadAttachment.js';
import utils from '@/components/js/utils.js';
import Enumerable from 'linq';
import medicalEditor from '@/components/medicalEditor/medicalEditor.vue';

export default {
  name: 'FollowUp',

  props: {},
  /**  引入的组件  */
  components: {
    workCustomerDetail: () => import('@/views/iBeauty/Workbench/Component/workbenchCustomerDetail'),
    addCustomer,
    medicalEditor,
  },
  /**  Vue 实例的数据对象**/
  data() {
    return {
      customerLevelList:[],
      downloadNoDisPlayLoading:false,
      downloadLoading:false,
      isCustomerBasicFileModify: false,
      isCustomerServicerModify: false,
      isCustomerBasicInformationModify: false,
      customerDetailVisible: false,
      isCustomerPhoneNumberView: false,
      isCustomerPhoneNumberModify: false,
      loading: false,
      modalLoading: false,
      isAdd: true,
      show: true,
      dialogVisible: false,
      isAddCustom: false,
      isShowChannel: false, // 是否展示渠道
      isShowFollowUp: false, // 跟进工作台权限
      search: {
        Name: '', // 用户名称搜索
        IsFollowUp: false, // 是否已跟进搜索
        FollowUpMethodID: '', // 跟进方式搜索
        FollowUpStatusID: '', // 跟进状态搜索
        FollowUpBy: '', // 跟进人搜索
        FollowUpEntity: '', // 跟进部门搜索
        QueryDate: [this.$formatDate(new Date(), 'YYYY-MM-DD'), this.$formatDate(new Date(), 'YYYY-MM-DD')], // 计划跟进时间搜索
        IsShowOwnFollowUp: true, //是否显示自己的跟进
        ChannelName:"",
        CustomerLevelID:"",
      }, // 搜索条件
      customerName: '', // 新建跟进任务搜索
      channelList: [], // 渠道来源
      // customerServicer: [], // 服务人员
      tableData: [],
      customerDetail: {}, // 顾客信息
      CustomerID: null, // 跟进顾客ID
      ID: '', //跟进记录ID
      employee: [], //营销顾问
      followUpCustomerID: '', //顾客ID
      IntroducerPageNum: '',
      IntroducerTotal: '',
      addCustomerPhoneNumber: '', // 新增时 顾客的手机号
      regionDataSelArr: [], //城市已选择
      tableDataMethod: [], // 跟进方式
      tableDataStatus: [], // 跟进状态
      followUpEntityData: [], // 跟进部门
      followUpEmployeeData: [], // 跟进人员
      CreatedByName: '', // 指派人员
      PlannedRemark: '', // 任务备注
      selectFollowUpRow: {}, // 处理跟进任务时使用
      customerID: null,
      customerFullName: '',
      customerPhoneNumber: '',
      nextDateTime: '',
      circleUrl: 'https://cube.elemecdn.com/3/7c/********************************.png', //默认头像
      ruleForm: {
        FollowUpMethodID: '', // 跟进方式
        FollowUpStatusID: '', // 跟进状态
        FollowUpContent: '', // 跟进记录
        PlannedOn: '', // 计划跟进时间
        IsNextFollowUp: true, // 下次是否跟进
        PlannedRemark: '', // 计划跟进备注
        Attachment: [],
      },
      rules: {
        FollowUpMethodID: [{ required: true, message: '请选择跟进方式', trigger: 'change' }],
        FollowUpStatusID: [{ required: true, message: '请选择跟进状态', trigger: 'change' }],
        FollowUpContent: [{ required: true, message: '请填写跟进记录', trigger: 'blur' }],
        IsNextFollowUp: [{ required: true, message: '请选择下次是否跟进', trigger: 'change' }],
      },
      //需要给分页组件传的信息
      paginations: {
        page: 1, // 当前位于哪页
        total: 0, // 总数
        page_size: 10, // 1页显示多少条
        layout: 'total, prev, pager, next,jumper', // 翻页属性
      },
      preview_src_list: [],
    };
  },
  beforeRouteEnter(to, from, next) {
    next((vm) => {
      vm.isShowChannel = vm.$permission.permission(to.meta.Permission, 'iBeauty-Customer-Customer-Channel');
      vm.isShowFollowUp = vm.$permission.permission(to.meta.Permission, 'iBeauty-Workbench-FollowUp-EntityRang');
      vm.isCustomerPhoneNumberView = vm.$permission.permission(to.meta.Permission, 'iBeauty-Workbench-FollowUp-CustomerPhoneNumberView');
      vm.isCustomerPhoneNumberModify = vm.$permission.permission(to.meta.Permission, 'iBeauty-Workbench-FollowUp-CustomerPhoneNumberModify');

      vm.isCustomerBasicInformationModify = vm.$permission.permission(to.meta.Permission, 'iBeauty-Workbench-FollowUp-CustomerBasicInformationModify');
      vm.isCustomerServicerModify = vm.$permission.permission(to.meta.Permission, 'iBeauty-Workbench-FollowUp-CustomerServicerModify');
      vm.isCustomerBasicFileModify = vm.$permission.permission(to.meta.Permission, 'iBeauty-Workbench-FollowUp-CustomerBasicFileModify');

      if (to.params.customerID != undefined) {
        CustomerAPI.getCustomerInfo({ ID: to.params.customerID }).then(function (res) {
          if (res.StateCode == 200) {
            vm.customerID = res.Data.ID;
            vm.customerFullName = res.Data.Name;
            vm.customerPhoneNumber = res.Data.PhoneNumber;
            // vm.customerName = res.Data.Name + "【" + res.Data.PhoneNumber + "】";
            var filter_hidephone = this.$options.filters['hidephone'];
            if (res.Data.PhoneNumber) {
              vm.customerName = res.Data.Name + '【' + filter_hidephone(res.Data.PhoneNumber) + '】';
            } else {
              vm.customerName = res.Data.Name;
            }
          } else {
            this.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        });
      }
    });
  },
  watch: {
    'ruleForm.Attachment': {
      deep: true,
      immediate: true,
      handler(val) {
        this.preview_src_list = [];
        this.preview_src_list = val.map((i) => i.AttachmentURL);
      },
    },
  },
  /**计算属性  */
  computed: {
    /*     tableHeight: function() {
	     return (window.innerHeight-200) + 'px';
	   } */
  },
  /**  方法集合  */
  methods: {
    /**    */
    customer_Export(type){
      let that = this;
      if (type == "excelNoDisPlayPhone") {
        that.followUp_excelNoDisPlayPhone();
      }

      
      if (type == "excelDisPlayPhone") {
        that.followUp_excelDisPlayPhone();
      }
    
    },
    /* 性别状态显示转换 */
    formatGender: function (row) {
      switch (row.Gender) {
        case '1':
          return '男';
        case '2':
          return '女';
        case '0':
          return '未知';
      }
    },
    /* 搜索 */
    handleSearch() {
      let that = this;
      that.paginations.page = 1;
      that.CustomerID = null;
      that.getFollowUp();
    },
    /* 分页 */
    handleCurrentChange(page) {
      let that = this;
      that.paginations.page = page;
      that.CustomerID = null;
      that.getFollowUp();
    },
    /* 新建跟进 */
    addFollowUp() {
      let that = this;
      that.isAdd = true;
      that.customerName = null;
      that.customerID = null;
      that.nextDateTime = this.$formatDate(new Date(), 'hh:mm:ss');
      that.ruleForm = {
        FollowUpMethodID: '', // 跟进方式
        FollowUpStatusID: '', // 跟进状态
        FollowUpContent: '', // 跟进记录
        PlannedOn: '', // 计划跟进时间
        IsNextFollowUp: true, // 下次是否跟进
        PlannedRemark: '', // 计划跟进备注
        Attachment: [],
      };
      if (this.$refs.ruleForm) {
        this.$refs['ruleForm'].resetFields();
      }

      that.dialogVisible = true;
    },
    /* 处理跟进 */
    followUpClick(row) {
      let that = this;
      that.isAdd = false;
      that.followUpCustomerID = row.CustomerID;
      that.CreatedByName = row.CreatedByName;
      that.PlannedRemark = row.PlannedRemark;
      that.selectFollowUpRow = row;
      that.ID = row.ID;
      that.nextDateTime = this.$formatDate(new Date(), 'hh:mm:ss');
      that.ruleForm = {
        FollowUpMethodID: '', // 跟进方式
        FollowUpStatusID: '', // 跟进状态
        FollowUpContent: '', // 跟进记录
        PlannedOn: '', // 计划跟进时间
        IsNextFollowUp: true, // 下次是否跟进
        PlannedRemark: '', // 计划跟进备注
        Attachment: [],
      };
      if (this.$refs.ruleForm) {
        this.$refs['ruleForm'].resetFields();
      }
      that.getCustomerDetail();
    },
    /**  跟进弹窗关闭  */
    closeAddFollowUpDialog() {
      this.ruleForm.Attachment = [];
    },
    /* 跟进列表  只能看自己/权限下 */
    getFollowUp() {
      let that = this;
      let params = {
        PageNum: that.paginations.page,
        Name: that.search.Name, //名称
        IsFollowUp: that.search.IsFollowUp, //是否已跟进
        FollowUpMethodID: that.search.FollowUpMethodID, //跟进方式
        FollowUpStatusID: that.search.FollowUpStatusID, //跟进状态
        FollowUpBy: that.search.FollowUpBy, //跟进人
        FollowUpEntity: that.search.FollowUpEntity, //跟进部门
        StartDate: that.search.QueryDate ? this.search.QueryDate[0] : '', //开始时间
        EndDate: that.search.QueryDate ? this.search.QueryDate[1] : '', //结束时间
        IsShowOwnFollowUp: that.search.IsShowOwnFollowUp, //是否显示自己的跟进
        
        ChannelName: that.search.ChannelName, //渠道
        CustomerLevelID: that.search.CustomerLevelID, //会员等级
      };
      if (that.isShowFollowUp) {
        this.getFollowUpAll(params);
      } else {
        this.getFollowUpList(params);
      }
    },
    /** 权限下的跟进   */
    async getFollowUpAll(params) {
      let that = this;
      let res = await API.getFollowUpAll(params);
      if (res.StateCode == 200) {
        that.tableData = res.List;
        that.paginations.total = res.Total;
        that.paginations.page_size = res.PageSize;
      } else {
        that.$message.error({
          message: res.Message,
          duration: 2000,
        });
      }
    },
    /**  自己的跟进  */
    async getFollowUpList(params) {
      let that = this;
      that.loading = true;
      let res = await API.getFollowUpList(params);
      if (res.StateCode == 200) {
        that.tableData = res.List;
        that.paginations.total = res.Total;
        that.paginations.page_size = res.PageSize;
      } else {
        that.$message.error({
          message: res.Message,
          duration: 2000,
        });
      }

      that.loading = false;
    },
    /* 列表行点击事件 */
    rowClick(row) {
      let that = this;
      that.CustomerID = row.CustomerID;
      that.customerDetailVisible = true;
    },
    /* 是否只显示自己的跟进 */
    IsShowOwnFollowUpChange() {
      let that = this;
      that.handleSearch();
    },
    /* 保存 */
    submitFollowUp() {
      let that = this;
      if (that.isAdd) {
        if (that.customerID) {
          this.$refs.ruleForm.validate((valid) => {
            if (valid) {
              that.modalLoading = true;
              let params = that.ruleForm;
              params.CustomerID = that.customerID;
              params.PlannedOn = this.ruleForm.PlannedOn ? this.$formatDate(this.ruleForm.PlannedOn, 'YYYY-MM-DD hh:mm') : '';
              API.createFollowUp(params)
                .then(function (res) {
                  if (res.StateCode === 200) {
                    that.$message.success({
                      message: '新建成功',
                      duration: 2000,
                    });
                    that.dialogVisible = false;
                    that.getFollowUp();
                  } else {
                    that.$message.error({
                      message: res.Message,
                      duration: 2000,
                    });
                  }
                })
                .finally(function () {
                  that.modalLoading = false;
                });
            }
          });
        } else {
          this.$message('请先选择顾客');
        }
      } else {
        this.$refs.ruleForm.validate((valid) => {
          if (valid) {
            that.modalLoading = true;
            let params = that.ruleForm;
            params.CustomerID = that.followUpCustomerID;
            (params.PlannedOn = this.ruleForm.PlannedOn ? this.$formatDate(this.ruleForm.PlannedOn, 'YYYY-MM-DD hh:mm') : ''), (params.ID = that.ID);
            API.FollowUp(params)
              .then(function (res) {
                if (res.StateCode === 200) {
                  that.$message.success({
                    message: '跟进更新成功',
                    duration: 2000,
                  });
                  that.dialogVisible = false;
                  that.getFollowUp();
                } else {
                  that.$message.error({
                    message: res.Message,
                    duration: 2000,
                  });
                }
              })
              .finally(function () {
                that.modalLoading = false;
              });
          }
        });
      }
    },
    /* 上传图片 */
    commodityMainbeforeUpload(file) {
      let that = this;
      // const isSize200kb = file.size / 1024 < 200;
      // if (!isSize200kb) {
      //   that.$message.error("上传图片大小不能超过 200kb!");
      //   return false;
      // }
      utils.getImageBase64(file).then((base64) => {
        this.addAttachment(base64).then((AttachmentURL) => {
          that.$nextTick(() => {
            that.ruleForm.Attachment.push({
              AttachmentType: '10',
              AttachmentURL: AttachmentURL,
            });
          });
        });
      });
      // let reader = new FileReader();
      // reader.readAsDataURL(file);
      // reader.onload = function (evt) {
      //   let base64 = evt.target.result;
      //   that.$nextTick(() => {
      //     that.ruleForm.Attachment.push({
      //       AttachmentType: 10,
      //       AttachmentURL: base64,
      //     });
      //   });
      // };
      return false;
    },
    /* 查看大图 */
    DialogPreview(file) {
      document.getElementById(file.uid).click();
    },
    /* 删除图片 */
    commodityMainRemove(file) {
      if (file && file.status !== 'success') return;
      let that = this;
      let index = that.ruleForm.Attachment.findIndex((item) => item.AttachmentURL == file.AttachmentURL);
      that.ruleForm.Attachment.splice(index, 1);
    },
    /* 顾客 */
    saleCustomerData: function (queryString, cb) {
      var that = this;
      that.loading = true;
      var params = {
        Name: queryString ? queryString : '',
      };
      CustomerAPI.getSaleCustomer(params)
        .then((res) => {
          if (res.StateCode == 200) {
            cb(res.Data);
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },
    handleCustomerSelect(item) {
      var filter_hidephone = this.$options.filters['hidephone'];
      if (item.ID != undefined) {
        this.customerID = item.ID;
        this.customerFullName = item.Name;
        this.customerPhoneNumber = item.PhoneNumber;
        if (item.PhoneNumber) {
          this.customerName = item.Name + '【' + filter_hidephone(item.PhoneNumber) + '】';
        } else {
          this.customerName = item.Name;
        }
      } else {
        if (/^1[3456789]\d{9}$/.test(this.customerName)) {
          this.addCustomerPhoneNumber = this.customerName;
        }
        this.addNewCustomer();
      }
    },
    removeCustomer() {
      this.customerID = null;
      this.customerFullName = '';
      this.customerPhoneNumber = '';
      this.customerName = '';
    },
    /* 新增会员 */
    addNewCustomer: function () {
      var that = this;

      that.isAddCustom = true;
    },
    /**  新增会员成功  */
    addCustomerSuccess(info) {
      var filter_hidephone = this.$options.filters['hidephone'];
      let that = this;
      that.customerID = info.ID;
      that.customerFullName = info.Name;
      that.customerPhoneNumber = info.PhoneNumber;
      if (info.PhoneNumber) {
        that.customerName = info.Name + '【' + filter_hidephone(info.PhoneNumber) + '】';
      } else {
        that.customerName = info.Name;
      }
    },

    /**  获取列表服务人员信息  */
    getFirstServiceEmp(ServicerEmployee) {
      if (!ServicerEmployee || ServicerEmployee.length == 0) {
        return '';
      }
      let firstItem = ServicerEmployee[0];
      return firstItem.Name + ':' + this.getServicerEmpNames(firstItem.ServicerEmpList);
    },
    /* 服务人员处理  */
    getServicerEmpNames(ServicerEmpList) {
      if (!ServicerEmpList) {
        return '';
      }
      return ServicerEmpList.map((val) => (val ? val.Name : '')).join(', ');
    },
    /* 获取顾客信息 */
    getCustomerDetail() {
      const that = this;
      cusAPI.getCustomerDetail({ CustomerID: that.followUpCustomerID }).then((res) => {
        if (res.StateCode == 200) {
          that.customerDetail = res.Data;
          that.dialogVisible = true;
        } else {
          that.$$message.error({
            message: res.Message,
            duration: 2000,
          });
        }
      });
    },
    /* 获取跟进方式列表 */
    getFollowUpMethod() {
      var that = this;
      var params = {
        Name: '',
        Active: true,
      };
      APIFollowUp.getFollowUpMethod(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.tableDataMethod = res.Data;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },
    /* 获取跟进类型列表 */
    getFollowUpStatus() {
      var that = this;
      var params = {
        Name: '',
        Active: true,
      };
      APIFollowUp.getFollowUpStatus(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.tableDataStatus = res.Data;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },
    /* 获取跟进部门 */
    getFollowUpEntity() {
      let that = this;
      let params = {};
      API.getFollowUpEntity(params).then((res) => {
        if (res.StateCode == 200) {
          that.followUpEntityData = res.Data;
        }
      });
    },
    /* 获取跟进人员 */
    getFollowUpEmployee() {
      let that = this;
      let params = {};
      API.getFollowUpEmployee(params).then((res) => {
        if (res.StateCode == 200) {
          that.followUpEmployeeData = res.Data;
        }
      });
    },
    /** 图片上传   */
    async addAttachment(base64) {
      let that = this;
      let params = { AttachmentURL: base64 };
      let res = await APIUpload.addAttachment(params);
      if (res.StateCode == 200) {
        return res.Data.AttachmentURL;
      } else {
        that.$message.error(res.Message);
      }
    },

    /* 导出 */
    followUp_excelNoDisPlayPhone() {
      let that = this;
      let params = {
        Name: that.search.Name, //名称
        IsFollowUp: that.search.IsFollowUp, //是否已跟进
        FollowUpMethodID: that.search.FollowUpMethodID, //跟进方式
        FollowUpStatusID: that.search.FollowUpStatusID, //跟进状态
        FollowUpBy: that.search.FollowUpBy, //跟进人
        FollowUpEntity: that.search.FollowUpEntity, //跟进部门
        StartDate: that.search.QueryDate ? this.search.QueryDate[0] : '', //开始时间
        EndDate: that.search.QueryDate ? this.search.QueryDate[1] : '', //结束时间
        IsShowOwnFollowUp: that.search.IsShowOwnFollowUp, //是否显示自己的跟进
        
        ChannelName: that.search.ChannelName, //渠道
        CustomerLevelID: that.search.CustomerLevelID, //会员等级
      };
      that.downloadNoDisPlayLoading = true;
      API.followUp_excelNoDisPlayPhone(params)
        .then((res) => {
          this.$message.success({
            message: '正在导出',
            duration: '4000',
          });
          const link = document.createElement('a');
          let blob = new Blob([res], { type: 'application/octet-stream' });
          link.style.display = 'none';
          link.href = URL.createObjectURL(blob);
          link.download = '跟进工作台隐藏手机号表.xlsx'; //下载的文件名
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
        })
        .finally(function () {
          that.downloadNoDisPlayLoading = false;
        });
    },
    /* 导出 */
    followUp_excelDisPlayPhone() {
      let that = this;
      let params = {
        Name: that.search.Name, //名称
        IsFollowUp: that.search.IsFollowUp, //是否已跟进
        FollowUpMethodID: that.search.FollowUpMethodID, //跟进方式
        FollowUpStatusID: that.search.FollowUpStatusID, //跟进状态
        FollowUpBy: that.search.FollowUpBy, //跟进人
        FollowUpEntity: that.search.FollowUpEntity, //跟进部门
        StartDate: that.search.QueryDate ? this.search.QueryDate[0] : '', //开始时间
        EndDate: that.search.QueryDate ? this.search.QueryDate[1] : '', //结束时间
        IsShowOwnFollowUp: that.search.IsShowOwnFollowUp, //是否显示自己的跟进
        
        ChannelName: that.search.ChannelName, //渠道
        CustomerLevelID: that.search.CustomerLevelID, //会员等级
      };
      that.downloadLoading = true;
      API.followUp_excelDisPlayPhone(params)
        .then((res) => {
          this.$message.success({
            message: '正在导出',
            duration: '4000',
          });
          const link = document.createElement('a');
          let blob = new Blob([res], { type: 'application/octet-stream' });
          link.style.display = 'none';
          link.href = URL.createObjectURL(blob);
          link.download = '跟进工作台表.xlsx'; //下载的文件名
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
        })
        .finally(function () {
          that.downloadLoading = false;
        });
    },
    /** 查询会员等级   */
    customerLevel_all(){
      let that = this;
      let params = {};
      API.customerLevel_all(params)
       .then((res) => {
         if(res.StateCode == 200){
          that.customerLevelList = res.Data;
          }
          else{
             that.$message.error(res.Message);
           }
         })
         .catch((fail) => {
           that.$message.error(fail);
         });
    },
  },
  /** 创建实例之前执行函数   */
  beforeCreate() {},
  /**  实例创建完成之后  */
  created() {
    let that = this;
    that.preview_src_list = Enumerable.from(that.ruleForm.Attachment)
      .select((val) => val.AttachmentURL)
      .toArray();
  },
  /**  在挂载开始之前被调用  */
  beforeMount() {},
  /**  实例被挂载后调用  */
  mounted() {
    let that = this;
    that.isShowChannel = that.$permission.permission(that.$route.meta.Permission, 'iBeauty-Customer-Customer-Channel');
    that.isShowFollowUp = that.$permission.permission(that.$route.meta.Permission, 'iBeauty-Workbench-FollowUp-EntityRang');

    that.isCustomerPhoneNumberView = that.$permission.permission(that.$route.meta.Permission, 'iBeauty-Workbench-FollowUp-CustomerPhoneNumberView');
    that.isCustomerPhoneNumberModify = that.$permission.permission(that.$route.meta.Permission, 'iBeauty-Workbench-FollowUp-CustomerPhoneNumberModify');

    that.isCustomerBasicInformationModify = that.$permission.permission(
      that.$route.meta.Permission,
      'iBeauty-Workbench-FollowUp-CustomerBasicInformationModify'
    );
    that.isCustomerServicerModify = that.$permission.permission(that.$route.meta.Permission, 'iBeauty-Workbench-FollowUp-CustomerServicerModify');
    that.isCustomerBasicFileModify = that.$permission.permission(that.$route.meta.Permission, 'iBeauty-Workbench-FollowUp-CustomerBasicFileModify');

    that.getFollowUp();
    that.getFollowUpStatus();
    that.getFollowUpMethod();
    that.getFollowUpEmployee();
    that.getFollowUpEntity();
    that.customerLevel_all();
    that.$bus.$on(that.$bus.RefreshFollowUpList, () => {
      that.getFollowUp();
    });
  },
  /**  数据更新 之前 调用  */
  beforeUpdate() {},
  /** 数据更新 完成 调用   */
  updated() {},
  /**  实例销毁之前调用  */
  beforeDestroy() {
    this.$bus.$off(this.$bus.RefreshFollowUpList);
  },
  /**  实例销毁后调用  */
  destroyed() {},
};
</script>

<style lang="scss">
.followUp {
  // background-color: #f0f0f0;
  // padding: unset;
  // display: flex;
  // height: 100%;
  // max-height: 100%;
  // box-sizing: border-box;
  .el_scrollbar_height_followup {
    height: 65vh;
    .el-scrollbar__wrap {
      overflow-x: hidden;
    }
  }
  .custom-customer-descLabel {
    min-width: 80px;
    text-align: right;
    padding-right: 10px;
  }
  // .workbench {
  //   background-color: #ffffff;
  //   box-sizing: border-box;
  //   padding: 15px;
  //   min-height: 100%;
  //   border-right: 8px solid #f0f0f0;
  // }
  .customer-detail {
    background-color: #ffffff;
    padding: 15px;
    height: 100%;
    box-sizing: border-box;
  }
  .information {
    background-color: #f7f8fa;
    padding: 8px 8px 8px 8px;
    // margin-bottom: 5px;
  }
  .customer-autocomplete {
    li {
      line-height: normal;
      padding: 7px;

      .name {
        text-overflow: ellipsis;
        overflow: hidden;
      }
      .info {
        font-size: 12px;
        color: #b4b4b4;
      }
      .highlighted .info {
        color: #ddd;
      }
    }
    .tip {
      margin: 0px;
      background-color: #f7f8fa;
    }
    .margin-bottom {
      margin-bottom: 10px;
    }
  }
  .el-upload--picture-card {
    width: 100px;
    height: 100px;
    font-size: 16px !important;
  }
  .el-upload {
    width: 100px;
    height: 100px;
    line-height: 100px;
    font-size: 16px;
  }
  .el-upload-list--picture-card .el-upload-list__item {
    width: 100px;
    height: 100px;
    line-height: 100px;
    font-size: 16px;
  }
  .el-autocomplete-suggestion__wrap {
    margin-bottom: 0px !important;
  }
}
</style>
