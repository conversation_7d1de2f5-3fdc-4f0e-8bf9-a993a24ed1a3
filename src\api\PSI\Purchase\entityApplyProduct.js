/**
 * Created by preference on 2020/11/20
 *  zmx
 */

import * as API from "@/api/index";
export default {
  /**  6.1.门店要货申请列表 */
  inventoryApplyList: (params) => {
    return API.POST("api/inventoryApply/list", params);
  },
  /**  6.2.门店要货单详情 */
  inventoryApplyInfo: (params) => {
    return API.POST("api/inventoryApply/info", params);
  },
  /**  6.3.门店要货单申请 */
  inventoryApplyCreate: (params) => {
    return API.POST("api/inventoryApply/create", params);
  },
  /** 6.4.门店要货单审批  */
  inventoryApplyApproved: (params) => {
    return API.POST("api/inventoryApply/approved", params);
  },
  /** 门店要货单审批 产品实时库存  */
  inventoryProductStock: (params) => {
    return API.POST("api/stock/productStock", params);
  },
  /** 6.5.门店要货单配送出库  */
  inventoryProductOutbound: (params) => {
    return API.POST("api/inventoryApply/outbound", params);
  },
  /** 6.6.门店要货单入库  */
  inventoryProductInbound: (params) => {
    return API.POST("api/inventoryApply/inbound", params);
  },
  /** 6.7.门店要货单取消  */
  inventoryProductCancel: (params) => {
    return API.POST("api/inventoryApply/cancel", params);
  },

  /** 6.8.门店要货单状态统计  */
  inventoryProductBillStatusNumber: (params) => {
    return API.POST("api/inventoryApply/billStatusNumber", params);
  },

  /** 6.8.门店要货单 驳回 关闭  */
  inventoryProductCancelRejectApply: (params) => {
    return API.POST("api/inventoryApply/cancelRejectApply", params);
  },

  /** 门店要货产品获取配送价  */
  getProductDispatchPrice: (params) => {
    return API.POST("api/stock/productDispatchPrice", params);
  },

  /** 门店要货产品获取配送价  */
  inventoryApply_pay: (params) => {
    return API.POST("api/inventoryApply/pay", params);
  },

  /** 门店要货驳回后编辑  */
  inventoryApply_update: (params) => {
    return API.POST("api/inventoryApply/update", params);
  },

  /** 门店要货驳回后编辑 获取详情 */
  inventoryApply_dataDetail: (params) => {
    return API.POST("api/inventoryApply/dataDetail", params);
  },

  /**  模板列表 */
  getPrintTemplate_list: (params) => {
    return API.POST("api/template/list", params);
  },

  /**  不分页 产品列表 */
  getProductList_list: (params) => {
    return API.POST("api/stock/productList", params);
  },

  /* 确认付款驳回 */
  rejectInventoryApply: (params) => {
    return API.POST("api/inventoryApply/reject", params);
  },

  /* 选择商品弹窗请求 */
  getEntityProductDeliveryPrice: (params) => {
    return API.POST("api/stock/entityProductDeliveryPrice", params);
  },

  /* 选择商品弹窗请求 */
  inventoryProductOutbound_updateRemark: (params) => {
    return API.POST("api/inventoryProductOutbound/updateRemark", params);
  },

  inventoryApply_importProduct: (params) => {
    return API.importFile("api/inventoryApply/importProduct", params);
  },
  // 下载导入模板
  inventoryApply_template: (params) => {
    return API.exportExcel("api/inventoryApply/template", params);
  },
  
};
