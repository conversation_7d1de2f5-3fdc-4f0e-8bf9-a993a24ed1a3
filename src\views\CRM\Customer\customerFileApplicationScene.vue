<template>
  <div class="customerFileApplicationScene content_body">
    <el-table size="small" :data="tableData" v-loading="loading">
      <el-table-column prop="Name" label="档案名称"></el-table-column>
      <el-table-column prop="Type" label="类型" :formatter="formatterType"></el-table-column>
      <el-table-column prop="IsRequired" label="必填/选填" :formatter="formatterIsRequired"></el-table-column>
      <el-table-column prop="Name" label="操作" width="90">
        <template slot-scope="scope">
          <el-button @click="updateStatus(scope.row)" size="small" :disabled="!scope.row.AllowEdit" type="text">{{
            scope.row.IsRequired ? "设为选填" : "设为必填"
          }}</el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import API from "@/api/CRM/Customer/customerFileApplicationScene.js";
export default {
  name: "CustomerFileApplicationScene",
  props: {},
  /** 监听数据变化   */
  watch: {},
  /**  引入的组件  */
  components: {},
  /**  Vue 实例的数据对象**/
  data() {
    return {
      loading: false,
      tableData: [],
    };
  },
  /**计算属性  */
  computed: {},
  /**  方法集合  */
  methods: {
    /**    */
    formatterType(row) {
      switch (row.Type) {
        case 10:
          return "文本";
        case 20:
          return "日期";
        case 30:
          return "单选项";
        case 40:
          return "多选项";
      }
    },
    /**    */
    formatterIsRequired(row) {
      if (row.IsRequired) {
        return "必填";
      } else {
        return "选填";
      }
    },
    /**    */
    updateStatus(row) {
      let that = this;
      that
        .$confirm("确定要修改状态吗？", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
        })
        .then(() => {
          that.customerFileApplicationScene_updateStatus(row.Code, !row.IsRequired);
        })
        .catch(() => {
          that.$message.info("已取消操作");
        });
    },
    /**  ••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••  */
    /**    */
    async customerFileApplicationScene_all() {
      let that = this;
      that.loading = true;
      try {
        let params = {};
        let res = await API.customerFileApplicationScene_all(params);
        if (res.StateCode == 200) {
          that.tableData = res.Data;
        } else {
          that.$message.error(res.Message);
        }
        that.loading = false;
      } catch (error) {
        that.loading = false;
        that.$message.error(error);
      }
    },
    /**    */
    async customerFileApplicationScene_updateStatus(Code, IsRequired) {
      let that = this;
      try {
        let params = {
          Code: Code, //档案编号
          IsRequired: IsRequired, //是否必填
        };
        let res = await API.customerFileApplicationScene_updateStatus(params);
        if (res.StateCode == 200) {
          that.customerFileApplicationScene_all();
          that.$message.success("操作成功");
        } else {
          that.$message.error(res.Message);
        }
      } catch (error) {
        that.$message.error(error);
      }
    },
  },
  /** 创建实例之前执行函数   */
  beforeCreate() {},
  /**  实例创建完成之后  */
  created() {},
  /**  在挂载开始之前被调用  */
  beforeMount() {},
  /**  实例被挂载后调用  */
  mounted() {
    let that = this;
    that.customerFileApplicationScene_all();
  },
  /**  数据更新 之前 调用  */
  beforeUpdate() {},
  /** 数据更新 完成 调用   */
  updated() {},
  /**  实例销毁之前调用  */
  beforeDestroy() {},
  /**  实例销毁后调用  */
  destroyed() {},
};
</script>

<style lang="scss">
.customerFileApplicationScene {
}
</style>
