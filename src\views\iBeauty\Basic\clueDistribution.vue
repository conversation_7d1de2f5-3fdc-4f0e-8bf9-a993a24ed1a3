<template>
  <div class="ClueDistribution content_body">
    <div class="nav_header">
      <el-row>
        <el-col :span="20">
          <el-form :inline="true" size="small" @keyup.enter.native="handleSearch">
            <el-form-item label="区域">
              <el-cascader size="small" :options="searchOptions" v-model="searchArrears" @change="handleSearch" clearable> </el-cascader>
            </el-form-item>
            <el-form-item label="有效性">
              <el-select @change="handleSearch" @clear="handleSearch" v-model="Active" placeholder="请选择" clearable>
                <el-option label="是" :value="true"></el-option>
                <el-option label="否" :value="false"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleSearch" v-prevent-click>搜索</el-button>
            </el-form-item>
          </el-form>
        </el-col>
        <el-col :span="4" class="text_right">
          <el-button type="primary" size="small" @click="onClueDistribution_add">新增</el-button>
          <el-button type="primary" size="small" @click="onClueDistribution_addEmployeeConfig">配置默认负责员工</el-button>
        </el-col>
      </el-row>
    </div>

    <el-table size="small" :data="tableData">
      <el-table-column prop="ProvinceName" label="省"></el-table-column>
      <el-table-column prop="CityCodeName" label="市"></el-table-column>
      <el-table-column prop="Active" label="有效性" :formatter="formatStatus"></el-table-column>
      <el-table-column label="操作" width="80">
        <template slot-scope="scope">
          <el-button type="primary" size="small" @click="onEditClueDistribution(scope.row)">编辑</el-button>
        </template>
      </el-table-column>
    </el-table>

    <div class="text_right pad_15">
      <el-pagination background v-if="paginations.total > 0" @current-change="handleCurrentChange" :current-page.sync="paginations.page" :page-size="paginations.page_size" :layout="paginations.layout" :total="paginations.total"></el-pagination>
    </div>

    <el-dialog :title="isAdd ? '新增区域负责人' : '编辑区域负责人'" :visible.sync="dialogVisible" width="850px">
      <el-form :model="addRuleForm" :rules="rules" ref="addRuleFormRef" label-width="100px" size="small">
        <el-form-item label="区域" prop="ArreaCodes">
          <el-cascader clearable placeholder="请选择省 / 市" size="small" :options="districtData" v-model="addRuleForm.ArreaCodes"></el-cascader>
        </el-form-item>

        <el-form-item label="是否有效" v-if="!isAdd">
          <el-radio-group v-model="addRuleForm.Active">
            <el-radio :label="true">有效</el-radio>
            <el-radio :label="false">无效</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="" prop="">
          <el-button type="primary" size="small" @click="onArrearClueDistribution_add">新增员工</el-button>
        </el-form-item>
        <el-form-item label="" prop="">
          <el-table size="small" :data="addRuleForm.Detail">
            <el-table-column prop="Name" label="名称"></el-table-column>
            <el-table-column label="操作" width="80">
              <template slot-scope="scope">
                <el-button type="danger" size="small" @click="onRemoveEmployee(scope.$index)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="dialogVisible = false" v-prevent-click>取消</el-button>
        <el-button type="primary" size="small" @click="addSubmitClueDistribution" :loading="modalLoading" v-prevent-click>保存</el-button>
      </div>
    </el-dialog>
    <!-- 配置默认员工 -->
    <el-dialog title="配置默认负责员工" :visible.sync="dialogVisibleDefault" width="550px">
      <el-form :model="addDefaultRuleForm" :rules="defaultRules" ref="addRuleDefaultFormRef" label-width="100px" size="small" @submit.native.prevent>
        <el-form-item label="默认负责人" prop="defaultEmployeeID">
          <el-select :popper-append-to-body="false" popper-class="custom-el-select" v-model="addDefaultRuleForm.defaultEmployeeID" filterable remote :remote-method="searchEmpRemote" placeholder="请选择默认负责员工" clearable>
            <el-option v-for="item in searchData" :key="item.ID" :label="item.Name" :value="item.ID">
              <div class="dis_flex flex_dir_column pad_5_0">
                <div style="line-height: 25px">
                  <span style="float: left">{{ item.Name }}</span>
                  <span style="float: right; color: #8492a6; font-size: 13px">{{ item.ID }}</span>
                </div>
                <div style="line-height: 20px; color: #8492a6">
                  <span style="float: left">{{ item.JobName }}</span>
                  <span style="float: right; font-size: 13px" class="marlt_5">{{ item.JobName }}</span>
                </div>
              </div>
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="dialogVisibleDefault = false" v-prevent-click>取消</el-button>
        <el-button type="primary" size="small" @click="addSubmitDefaultClueDistribution" :loading="defaultLoading" v-prevent-click>保存</el-button>
      </div>
    </el-dialog>
    <!-- 选择员工 -->
    <el-dialog title="员工列表" :visible.sync="dialogVisibleEmployee" width="850px">
      <el-form :inline="true" size="small" @keyup.enter.native="handleSearchEmployee" @submit.native.prevent>
        <el-form-item label="名称">
          <el-input v-model="searchEmployeeName" @clear="handleSearchEmployee" placeholder="请输入员工名称" clearable></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearchEmployee" v-prevent-click>搜索</el-button>
        </el-form-item>
      </el-form>

      <el-table size="small" :data="employeeList" ref="empTableRef" @select-all="selectionEmployee_all" @cell-click="employeeListCellClick" @select="employeeListCheckboxClick">
        <el-table-column type="selection" width="55"> </el-table-column>
        <el-table-column prop="Name" label="名称"></el-table-column>
        <el-table-column prop="JobName" label="职务"></el-table-column>
      </el-table>
      <div class="text_right pad_15">
        <el-pagination background v-if="paginationsEmployee.total > 0" @current-change="handleCurrentEmployeeChange" :current-page.sync="paginationsEmployee.page" :page-size="paginationsEmployee.page_size" :layout="paginationsEmployee.layout" :total="paginationsEmployee.total"></el-pagination>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="dialogVisibleEmployee = false" v-prevent-click>取消</el-button>
        <el-button type="primary" size="small" @click="onConfirmSelectEmployee"  v-prevent-click>保存</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { provinceAndCityData, provinceAndCityDataPlus } from "element-china-area-data";
import API from "@/api/iBeauty/Basic/clueDistribution.js";
import FollowUpAPI from "@/api/iBeauty/Workbench/followUp";
export default {
  name: "ClueDistribution",

  beforeRouteEnter(to, from, next) {
    next((vm) => {
      vm.isAnewAssign = vm.$permission.permission(to.meta.Permission, "iBeauty-Workbench-FollowUp-AnewAssign");
    });
  },
  props: {},
  /** 监听数据变化   */
  watch: {},
  /**  引入的组件  */
  components: {},
  /**  Vue 实例的数据对象**/
  data() {
    return {
      dialogVisibleEmployee: false,
      defaultLoading: false,
      dialogVisibleDefault: false,
      modalLoading: false,
      dialogVisible: false,
      isAnewAssign: false,
      tableData: [],
      paginations: {
        page: 1, // 当前位于哪页
        total: 0, // 总数
        page_size: 10, // 1页显示多少条
        layout: "total, prev, pager, next,jumper", // 翻页属性
      }, //需要给分页组件传的信息
      paginationsEmployee: {
        page: 1, // 当前位于哪页
        total: 5, // 总数
        page_size: 10, // 1页显示多少条
        layout: "total, prev, pager, next,jumper", // 翻页属性
      }, //需要给分页组件传的信息
      isAdd: false,
      addRuleForm: {
        ArreaCodes: [],
        ProvinceCode: "", //省代码
        CityCode: "", //市代码
        Detail: [], //服务人明细
      },
      rules: {
        ArreaCodes: [{ required: true, message: "请选择区域", trigger: "change" }],
        Detail: [{ required: true, message: "请选择活动区域", trigger: "change" }],
      },

      addDefaultRuleForm: {
        defaultEmployeeID: "",
      },
      defaultRules: {
        defaultEmployeeID: [{ required: true, message: "请选择默认负责员工", trigger: "change" }],
      },
      districtData: provinceAndCityData,
      searchOptions: provinceAndCityDataPlus,
      searchData: [],
      employeeList: [],
      tempSelectEmployee: [],
      searchEmployeeName: "",
      searchArrears: [],
      Active: "",
    };
  },
  /**计算属性  */
  computed: {},
  /**  方法集合  */
  methods: {
    /**  编辑  */
    onEditClueDistribution(row) {
      let that = this;
      that.isAdd = false;
      that.dialogVisible = true;
      that.addRuleForm = {
        ID: row.ID,
        Active: row.Active,
        ArreaCodes: [row.ProvinceCode, row.CityCode],
        ProvinceCode: "", //省代码
        CityCode: "", //市代码
        Detail: row.Detail.map((i) => {
          return {
            Name: i.EmployeeName,
            ...i,
          };
        }), //服务人明细
      };
    },
    /**  搜索负责员工  */
    handleSearchEmployee() {
      let that = this;
      that.paginationsEmployee.page = 1;
      that.employee_listAll(that.searchEmployeeName);
    },
    /**  删除已选的员工  */
    onRemoveEmployee(index) {
      let that = this;
      that.addRuleForm.Detail.splice(index, 1);
    },
    /**  确认选择这员工  */
    onConfirmSelectEmployee() {
      let that = this;
      that.addRuleForm.Detail = that.tempSelectEmployee.map((i) => {
        return i;
      });
      that.dialogVisibleEmployee = false;
    },
    /** 点击行checkbox   */
    employeeListCheckboxClick(selection, row) {
      let that = this;
      let findIndex = that.tempSelectEmployee.findIndex((i) => {
        return row.ID == i.EmployeeID;
      });
      if (findIndex == -1) {
        that.tempSelectEmployee.push({
          EmployeeID: row.ID,
          ...row,
        });
        that.$refs.empTableRef.toggleRowSelection(row, true);
      } else {
        that.tempSelectEmployee.splice(findIndex, 1);
        that.$refs.empTableRef.toggleRowSelection(row, false);
      }
    },
    /**  点击员工  */
    employeeListCellClick(row) {
      let that = this;
      let findIndex = that.tempSelectEmployee.findIndex((i) => {
        return row.ID == i.EmployeeID;
      });
      if (findIndex == -1) {
        that.tempSelectEmployee.push({
          EmployeeID: row.ID,
          ...row,
        });
        that.$refs.empTableRef.toggleRowSelection(row, true);
      } else {
        that.tempSelectEmployee.splice(findIndex, 1);
        that.$refs.empTableRef.toggleRowSelection(row, false);
      }
    },
    /** 处理全选事件  */
    selectionEmployee_all(selection) {
      let that = this;
      if (selection.length > 0) {
        if (that.tempSelectEmployee.length > 0) {
          let tempEmps = selection.filter((i) => {
            return that.tempSelectEmployee.findIndex((j) => j.EmployeeID == i.ID) == -1;
          });

          tempEmps.forEach((i) => {
            i.EmployeeID = i.ID;
            that.tempSelectEmployee.push(Object.assign({}, i));
          });
        } else {
          selection.forEach((i) => {
            i.EmployeeID = i.ID;
            that.tempSelectEmployee.push(Object.assign({}, i));
          });
        }
      } else {
        that.tempSelectEmployee = that.tempSelectEmployee.filter((i) => {
          return that.employeeList.findIndex((j) => j.ID == i.EmployeeID) == -1;
        });
      }
    },
    /**  员工列表换页  */
    handleCurrentEmployeeChange(page) {
      let that = this;
      that.paginationsEmployee.page = page;
      that.employee_listAll();
    },
    /**  新增区域员工  */
    onArrearClueDistribution_add() {
      let that = this;
      that.dialogVisibleEmployee = true;
      that.tempSelectEmployee = that.addRuleForm.Detail.map((i) => {
        return i;
      });
      that.employee_listAll();
    },
    /**  保存默认负责员工  */
    addSubmitDefaultClueDistribution() {
      let that = this;
      that.$refs.addRuleDefaultFormRef.validate((valid) => {
        if (valid) {
          that.clueDistribution_addEmployeeConfig();
        }
      });
    },
    /**  搜索其他人员  */
    searchEmpRemote(query) {
      this.getSearch(query);
    },
    /**  新增保存 区域负责人  */
    addSubmitClueDistribution() {
      let that = this;
      that.$refs.addRuleFormRef.validate((valid) => {
        if (valid) {
          if (that.isAdd) {
            that.clueDistribution_add();
          } else {
            that.clueDistribution_update();
          }
        }
      });
    },
    /**    */
    handleSearch() {
      let that = this;
      that.paginations.page = 1;
      that.clueDistribution_list();
    },
    /**  新增默认负责员工  */
    onClueDistribution_addEmployeeConfig() {
      let that = this;
      that.dialogVisibleDefault = true;
    },
    /**  添加区域负责人  */
    onClueDistribution_add() {
      let that = this;
      that.dialogVisible = true;
      that.isAdd = true;
      that.addRuleForm = {
        ArreaCodes: [],
        ProvinceCode: "", //省代码
        CityCode: "", //市代码
        Detail: [], //服务人明细
      };
    },
    /**  格式换有效性  */
    formatStatus(row) {
      if (row.Active) {
        return "是";
      }
      return "否";
    },
    /**  修改分页  */
    handleCurrentChange(page) {
      let that = this;
      that.paginations.page = page;
      that.clueDistribution_list();
    },
    /**   •••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••    */
    /**  获取默认负责员工  */
    clueDistribution_employeeConfigAll() {
      let that = this;
      let params = {};
      API.clueDistribution_employeeConfigAll(params)
        .then((res) => {
          if (res.StateCode == 200) {
            if (res.Data) {
              that.addDefaultRuleForm.defaultEmployeeID = res.Data.EmployeeID;
              that.getSearch(res.Data.EmployeeName);
            }
          } else {
            that.$message.error(res.Message);
          }
        })
        .catch((fail) => {
          that.$message.error(fail);
        });
    },
    /**  保存默认员工  */
    clueDistribution_addEmployeeConfig() {
      let that = this;
      that.defaultLoading = true;
      let params = {
        EmployeeID: that.addDefaultRuleForm.defaultEmployeeID,
      };
      API.clueDistribution_addEmployeeConfig(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.dialogVisibleDefault = false;
            that.$message.success("操作成功");
            that.addDefaultRuleForm = {
              defaultEmployeeID: "",
            };
            that.clueDistribution_employeeConfigAll();
            that.defaultLoading = false;
          } else {
            that.defaultLoading = false;
            that.$message.error(res.Message);
          }
        })
        .catch((fail) => {
          that.defaultLoading = false;
          that.$message.error(fail);
        });
    },
    /**  获取列表  */
    clueDistribution_list() {
      let that = this;
      let params = {
        PageNum: that.paginations.page, //页码
        ProvinceCode: that.searchArrears[0], //省代码
        CityCode: that.searchArrears[1], //市代码
        Active: that.Active, //是否有效：true 有效，false 无效
      };
      API.clueDistribution_list(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.tableData = res.List;
            that.paginations.total = res.Total;
            that.paginations.page_size = res.PageSize;
          } else {
            that.$message.error(res.Message);
          }
        })
        .catch((fail) => {
          that.$message.error(fail);
        });
    },
    /** 获取详情  */
    clueDistribution_detail() {
      let that = this;
      let params = {};
      API.clueDistribution_detail(params)
        .then((res) => {
          if (res.StateCode == 200) {
            console.log(res);
          } else {
            that.$message.error(res.Message);
          }
        })
        .catch((fail) => {
          that.$message.error(fail);
        });
    },
    /**  新增  */
    clueDistribution_add() {
      let that = this;
      that.modalLoading = true;
      let params = {
        ProvinceCode: that.addRuleForm.ArreaCodes[0], //省代码
        CityCode: that.addRuleForm.ArreaCodes[1], //市代码
        Detail: that.addRuleForm.Detail.map((i) => {
          return {
            EmployeeID: i.EmployeeID,
          };
        }), //服务人明细
      };
      API.clueDistribution_add(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.$message.success("操作成功");
            that.dialogVisible = false;
            that.clueDistribution_list();
            that.modalLoading = false;
          } else {
            that.modalLoading = false;
            that.$message.error(res.Message);
          }
        })
        .catch((fail) => {
          that.modalLoading = false;
          that.$message.error(fail);
        });
    },
    /**    */
    clueDistribution_update() {
      let that = this;
      that.modalLoading = true;
      let params = {
        ID: that.addRuleForm.ID, //省代码
        Active: that.addRuleForm.Active, //市代码
        Detail: that.addRuleForm.Detail.map((i) => {
          return {
            EmployeeID: i.EmployeeID,
          };
        }), //服务人明细
      };
      API.clueDistribution_update(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.$message.success("操作成功");
            that.dialogVisible = false;
            that.clueDistribution_list();
            that.modalLoading = false;
          } else {
            that.modalLoading = false;
            that.$message.error(res.Message);
          }
        })
        .catch((fail) => {
          that.modalLoading = false;
          that.$message.error(fail);
        });
    },
    /* 获取指派-其他人员 */
    getSearch(SearchKey) {
      let that = this;
      let params = {
        SearchKey: SearchKey,
      };
      FollowUpAPI.getSearch(params).then((res) => {
        if (res.StateCode == 200) {
          that.searchData = res.Data;
        } else {
          that.$message.error({
            message: res.Message,
            duration: 2000,
          });
        }
      });
    },
    /** 查询员工列表   */
    employee_listAll(SearchKey) {
      let that = this;
      let params = {
        PageNum: that.paginationsEmployee.page,
        SearchKey: SearchKey, //搜索名称
      };
      API.employee_listAll(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.employeeList = res.List;
            that.employeeList.forEach((i) => {
              let findIndex = that.tempSelectEmployee.findIndex((j) => j.EmployeeID == i.ID);
              if (findIndex != -1) {
                that.$nextTick(() => {
                  that.$refs.empTableRef.toggleRowSelection(i, true);
                });
              }
            });
            that.paginationsEmployee.total = res.Total;
            that.paginationsEmployee.page_size = res.PageSize;
          } else {
            that.$message.error(res.Message);
          }
        })
        .catch((fail) => {
          that.$message.error(fail);
        });
    },
  },
  /** 创建实例之前执行函数   */
  beforeCreate() {},
  /**  实例创建完成之后  */
  created() {},
  /**  在挂载开始之前被调用  */
  beforeMount() {},
  /**  实例被挂载后调用  */
  mounted() {
    this.isAnewAssign = this.$permission.permission(this.$route.meta.Permission, "iBeauty-Workbench-FollowUp-AnewAssign");
    this.clueDistribution_list();
    this.clueDistribution_employeeConfigAll(); //获取默认负责员工

    // this.getSearch("");
  },
  /**  数据更新 之前 调用  */
  beforeUpdate() {},
  /** 数据更新 完成 调用   */
  updated() {},
  /**  实例销毁之前调用  */
  beforeDestroy() {},
  /**  实例销毁后调用  */
  destroyed() {},
};
</script>

<style lang="scss">
.ClueDistribution {
}
.custom-el-select {
  li {
    line-height: normal;
    height: auto;
  }
}
</style>
