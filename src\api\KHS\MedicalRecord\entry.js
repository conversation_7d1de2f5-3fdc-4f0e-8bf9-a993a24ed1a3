/**
 * Created by preference on 2024/03/07
 *  zmx
 */

import * as API from "@/api/index";
export default {
  /**  查询词条分类 */
  medicalRecordEntryCategory_list: (params) => {
    return API.POST("api/medicalRecordEntryCategory/list", params);
  },
  /**  创建词条分类 */
  medicalRecordEntryCategory_create: (params) => {
    return API.POST("api/medicalRecordEntryCategory/create", params);
  },
  /** 更新词条分类  */
  medicalRecordEntryCategory_update: (params) => {
    return API.POST("api/medicalRecordEntryCategory/update", params);
  },
  /**  删除词条分类 */
  medicalRecordEntryCategory_delete: (params) => {
    return API.POST("api/medicalRecordEntryCategory/delete", params);
  },
  /** 移动词条分类  */
  medicalRecordEntryCategory_move: (params) => {
    return API.POST("api/medicalRecordEntryCategory/move", params);
  },
  /** 查询词条类别 */
  medicalRecordEntryLabel_list: (params) => {
    return API.POST("api/medicalRecordEntryLabel/list", params);
  },
  /**  创建词条类别 */
  medicalRecordEntryLabel_create: (params) => {
    return API.POST("api/medicalRecordEntryLabel/create", params);
  },
  /** 更新词条类别  */
  medicalRecordEntryLabel_update: (params) => {
    return API.POST("api/medicalRecordEntryLabel/update", params);
  },
  /**  删除词条类别 */
  medicalRecordEntryLabel_delete: (params) => {
    return API.POST("api/medicalRecordEntryLabel/delete", params);
  },
  /**  移动词条类别 */
  medicalRecordEntryLabel_move: (params) => {
    return API.POST("api/medicalRecordEntryLabel/move", params);
  },
  /** 创建词条内容  */
  medicalRecordEntryContent_create: (params) => {
    return API.POST("api/medicalRecordEntryContent/create", params);
  },
  /**  更新词条内容 */
  medicalRecordEntryContent_update: (params) => {
    return API.POST("api/medicalRecordEntryContent/update", params);
  },
  /** 删除词条内容  */
  medicalRecordEntryContent_delete: (params) => {
    return API.POST("api/medicalRecordEntryContent/delete", params);
  },
};
