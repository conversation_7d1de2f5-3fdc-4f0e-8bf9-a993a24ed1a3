// 秒杀专区
import * as API from '@/api/index'
export default {
  /**  秒杀列表*/
  seckill_all: params => {
    return API.POST('api/seckill/all', params)
  },
  /**  秒杀新增  */
  seckill_create: params => {
    return API.POST('api/seckill/create', params)
  },
  /**  秒杀修改  */
  seckill_update: params => {
    return API.POST('api/seckill/update', params)
  },
  /**  秒杀详情 */
  seckill_info: params => {
    return API.POST('api/seckill/info', params)
  },
  /**  秒杀详情-适应门店 */
  seckill_entity: params => {
    return API.POST('api/seckill/entity', params)
  },
  
  // 新增销售范围、消耗范围 获取权限范围
  getEntityList:params => {
    return API.POST('api/entity/list',params)
  },

  // 项目列表
  getProjectList:params => {
    return API.POST('api/project/list',params)
  },

  // 产品列表
  getProductList:params => {
    return API.POST('api/productSale/list',params)
  },

  // 通用次卡列表
  getGeneralCardList:params => {
    return API.POST('api/generalCard/list',params)
  },
  // 时效卡列表
  getTimeCardList:params => {
    return API.POST('api/timeCard/list',params)
  },

    
}