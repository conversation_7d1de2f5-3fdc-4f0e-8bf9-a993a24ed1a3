<template>
  <div class="content_body Storage" v-loading="loading">
    <div class="nav_header">
      <el-row>
        <el-col :span="22">
          <el-form :inline="true" size="small" :model="searchForm">
            <el-form-item label="仓库/门店" v-if="purchaseStorage.length > 1">
              <el-select :default-first-option="true" @change="handleSearchStorageClick" @clear="handleSearchStorageClick" clearable filterable placeholder="请选择仓库" v-model="searchForm.EntityID">
                <el-option v-for="item in purchaseStorage" :key="item.ID" :label="item.EntityName" :value="item.ID"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="产品名称">
              <el-input v-model="searchForm.Name" placeholder="输入产品名称、别名搜索" clearable @clear="handleSearchStorageClick" @keyup.enter.native="handleSearchStorageClick"></el-input>
            </el-form-item>
            <el-form-item label="供应商名称">
              <el-select v-model="searchForm.SupplierID" :default-first-option="true" @change="handleSearchStorageClick" clearable filterable placeholder="请选择供应商">
                <el-option v-for="item in StorageList" :key="item.ID" :label="item.Name" :value="item.ID"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="制单日期">
              <el-date-picker v-model="searchForm.createTime" unlink-panels type="daterange" range-separator="至" value-format="yyyy-MM-dd" start-placeholder="开始日期" end-placeholder="结束日期" :picker-options="pickerOptions" @change="handleSearchStorageClick"></el-date-picker>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleSearchStorageClick" v-prevent-click>搜索</el-button>
            </el-form-item>
          </el-form>
        </el-col>
        <el-col :span="2" class="text_right">
          <el-button type="primary" @click="addStorageOrderClick" size="small" v-prevent-click>新增</el-button>
        </el-col>
      </el-row>
    </div>

    <el-table size="small" :data="storageList" tooltip-effect="light">
      <el-table-column prop="ID" label="单据号"></el-table-column>
      <el-table-column prop="EntityName" label="仓库/门店"></el-table-column>
      <el-table-column prop="InventoryType" label="入库类型"></el-table-column>
      <el-table-column prop="SupplierName" label="供应商"></el-table-column>
      <el-table-column prop="CreatedOn" label="制单时间">
        <template slot-scope="scope">
          {{ scope.row.CreatedOn | dateFormat("YYYY-MM-DD HH:mm") }}
        </template>
      </el-table-column>
      <!-- <el-table-column prop="InDate" label="入库时间"></el-table-column> -->
      <el-table-column prop="EmployeeName" label="操作人"></el-table-column>
      <el-table-column prop="BillStatusName" label="单据状态">
        <template slot-scope="scope">
          <el-tag :type="getStatusTagType(scope.row.BillStatus)">{{ scope.row.BillStatusName }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="Remark" label="备注信息" show-overflow-tooltip></el-table-column>
      <el-table-column label="操作" width="80">
        <template slot-scope="scope">
          <el-button type="primary" size="small" @click="showStorageInfoClick(scope.row)" v-prevent-click>详情</el-button>
        </template>
      </el-table-column>
    </el-table>

    <div class="pad_15 text_right">
      <el-pagination background v-if="paginations.total > 0" @current-change="handleCurrentChange" :current-page.sync="paginations.page" :page-size="paginations.page_size" :layout="paginations.layout" :total="paginations.total"></el-pagination>
    </div>

    <!-- 创建采购入库 -->
    <el-dialog title="采购订单" :visible.sync="dialogVisible" width="1100px" @closed="cancelAddSubmitStorageClick('addStorageForm')">
      <div class="tip marbm_10" style="margin-top: 0">采购信息</div>
      <el-form size="small" class="storageInfoFrom" :model="addStorageForm" :rules="addStorageRulesMsg" ref="addStorageForm" label-width="auto">
        <el-row>
          <el-col :span="12">
            <el-form-item label="仓库/门店" prop="EntityID" style="width: auto">
              <el-select class="StorageIn-info-width" value-key="ID" v-model="addStorageForm.EntityName" filterable placeholder="请选择仓库/门店" @change="handleSelectProductEntity">
                <el-option v-for="item in purchaseStorage" :key="item.ID" :label="item.EntityName" :value="item"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="采购时间" prop="InDate">
              <el-date-picker class="StorageIn-info-width" format="yyyy-MM-dd HH:mm:ss" value-format="yyyy-MM-dd HH:mm:ss" v-model="addStorageForm.InDate" type="datetime" :picker-options="pickerOptions" placeholder="选择入库日期"></el-date-picker>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="供应商名称">
              <el-select class="StorageIn-info-width" value-key="ID" v-model="addStorageForm.SupplierName" clearable filterable placeholder="请选择供应商" @change="handleSelectProductStorage">
                <el-option v-for="item in StorageList" :key="item.ID" :label="item.Name" :value="item"></el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="备注信息">
              <el-input type="textarea" style="width: 300px" :autosize="{ minRows: 1, maxRows: 3 }" v-model="addStorageForm.Remark" placeholder="请输入备注信息" size="small"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div class="tip marbm_10" style="margin-top: 0">采购明细</div>
      <el-row>
        <el-col :span="4">
          <el-button type="primary" size="small" @click="addProducts">添加产品</el-button>
          <el-button type="danger" size="small" @click="removeMultipleProduct" :disabled="removeDisabled">删除产品</el-button>
        </el-col>
      </el-row>

      <el-form :model="addStorageForm" :rules="addProductItemRules" size="small" :inline="true" :show-message="false" class="productFormInforClass" ref="addStorageFormProduct">
        <el-table empty-text="暂无产品" size="small" class="martp_15" max-height="300px" :data="addStorageForm.Product" @selection-change="handleChangeSelectProduct">
          <el-table-column type="selection" width="55"></el-table-column>
          <el-table-column label="产品名称" prop="ProductName" width="250">
            <template slot-scope="scope">
              <el-form-item :prop="'Product.' + scope.$index + '.ProductID'" :rules="addProductItemRules.ProductID">
                <el-tooltip :disabled="getShowProductNameTooltip(scope.row.ProductName)" class="item" effect="dark" :content="scope.row.ProductName" placement="top">
                  <!-- v-loadmore="loadMoreProduct" -->
                  <el-select
                    class="StorageIn-info-width"
                    v-model="scope.row.ID"
                    size="small"
                    filterable
                    remote
                    reserve-keyword
                    placeholder="请选择产品"
                    :remote-method="searchProductListMethod"
                    :loading="productLoading"
                    @change="(val) => handleSelectProduct(val, scope.row)"
                    @focus="selectFocus"
                    @clear="clearSelectProduct(scope.row)"
                    :default-first-option="true"
                    popper-class="Storage_custom_popper_class"
                  >
                    <el-option v-for="(item,index) in allProductList" :key="index + '-' + item.ID " :label="formatProductName(item.ProductName)" :value="item.ID" :disabled="item.IsLock">
                      <div class="dis_flex flex_dir_column pad_5_0">
                        <div>
                          <span>{{ item.ProductName }}</span>
                          <span class="color_gray marlt_5 font_12" v-if="item.Alias">({{ item.Alias }})</span>
                          <el-tag v-if="item.IsLock" size="mini" type="warning" effect="dark">{{ "盘点锁定" }}</el-tag>
                        </div>
                        <div :class="item.ID == scope.row.ID ? 'font_12 dis_flex  flex_x_between' : 'color_gray font_12 dis_flex flex_x_between'">
                          <span class="">{{ item.PCategoryName }}</span>
                          <span v-if="item.Specification">{{ item.Specification }}</span>
                        </div>
                      </div>
                    </el-option>
                  </el-select>
                </el-tooltip>
              </el-form-item>
            </template>
          </el-table-column>
          <el-table-column prop="Specification" label="产品规格">
            <template slot-scope="scope">{{ scope.row.Specification }}</template>
          </el-table-column>
          <el-table-column label="采购单位">
            <template slot-scope="scope">
              <el-form-item :prop="'Product.' + scope.$index + '.UnitName'" :rules="addProductItemRules.UnitID">
                <el-select value-key="UnitID" v-model="scope.row.Unit" size="small" filterable placeholder="采购单位" @change="(val) => handleSelectProductUnit(val, scope.row)" @clear="clearSelectProductUnit(scope.row)" :default-first-option="true">
                  <el-option v-for="item in scope.row.Units" :key="item.UnitID" :label="item.UnitName" :value="item"></el-option>
                </el-select>
              </el-form-item>
            </template>
          </el-table-column>
          <el-table-column prop="Quantity" label="采购单位数量">
            <template slot-scope="scope">
              <el-form-item :prop="'Product.' + scope.$index + '.Quantity'" :rules="addProductItemRules.Quantity">
                <el-input v-model="scope.row.Quantity" size="small" placeholder="采购单位数量" type="number" v-enterInt v-enterNumber2 v-enter-int v-enterNumber @input="changeQuantity(scope.row)" min="1"></el-input>
              </el-form-item>
            </template>
          </el-table-column>
          <el-table-column prop="UnitPrice" label="采购单位单价(元)">
            <template slot-scope="scope">
              <el-form-item :prop="'Product.' + scope.$index + '.UnitPrice'" :rules="addProductItemRules.UnitPrice">
                <el-input v-model="scope.row.UnitPrice" size="small" placeholder="采购单位单价" @input="changeUnitPrice(scope.row)" v-input-fixed validate-event v-enter-number2 min="0" type="number"></el-input>
              </el-form-item>
            </template>
          </el-table-column>
          <el-table-column prop="Amount" label="小计(元)">
            <template slot-scope="scope">{{ scope.row.Amount | toFixed | NumFormat }}</template>
          </el-table-column>
          <el-table-column prop="MinimumUnitQuantity" label="最小包装数量" :formatter="MinimumUnitQuantityAndUnit"></el-table-column>
        </el-table>
      </el-form>

      <div slot="footer">
        <el-button @click="cancelAddSubmitStorageClick('addStorageForm')" size="small" v-prevent-click>取 消</el-button>
        <el-button type="primary" @click="addSubmitStorageClick('addStorageForm')" :loading="modalLoading" size="small" v-prevent-click>确认下单</el-button>
      </div>
    </el-dialog>

    <!-- 采购入库详情 -->
    <el-dialog title="采购入库详情" :visible.sync="dialogVisibleInfo" width="1000px" v-loading="loading">
      <div class="tip">入库信息</div>
      <el-form label-width="120px" size="small" class="storageInfoDetailFrom">
        <el-row>
          <el-col :span="8">
            <el-form-item label="单据号：">{{ storageInfoDetailForm.ID }}</el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="仓库/门店：">{{ storageInfoDetailForm.EntityName }}</el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="操作人：">{{ storageInfoDetailForm.EmployeeName }}</el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="供应商：">{{ storageInfoDetailForm.SupplierName }}</el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="入库时间：">{{ storageInfoDetailForm.InDate | dateFormat("YYYY-MM-DD HH:mm") }}</el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="制单时间：">{{ storageInfoDetailForm.CreatedOn | dateFormat("YYYY-MM-DD HH:mm") }}</el-form-item>
          </el-col>
          <el-col :span="8" v-if="storageInfoDetailForm.BillStatusName">
            <el-form-item label="单据状态：">
              <el-tag :type="getStatusTagType(storageInfoDetailForm.BillStatus)">{{ storageInfoDetailForm.BillStatusName }}</el-tag>
            </el-form-item>
          </el-col>
          <el-col :span="8" v-if="storageInfoDetailForm.ConfirmedByName">
            <el-form-item label="确认人：">{{ storageInfoDetailForm.ConfirmedByName }}</el-form-item>
          </el-col>
          <el-col :span="8" v-if="storageInfoDetailForm.ConfirmedOn">
            <el-form-item label="确认时间：">{{ storageInfoDetailForm.ConfirmedOn | dateFormat("YYYY-MM-DD HH:mm") }}</el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="备注信息：">{{ storageInfoDetailForm.Remark || '无' }}</el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div class="tip" style="margin-top: 20px;">产品明细</div>
      <el-table size="small" class="martp_10" max-height="300px" :data="storageInfoDetailForm.Detail">
        <el-table-column label="产品" prop="ProductName">
          <template slot-scope="scope">
            <div>
              {{ scope.row.ProductName }}<span v-if="scope.row.Alias" class="color_gray font_12">({{ scope.row.Alias }})</span>
            </div>
            <div v-if="scope.row.Specification" class="color_gray font_12">规格：{{ scope.row.Specification }}</div>
          </template>
        </el-table-column>
        <el-table-column label="产品分类" prop="PCategoryName"></el-table-column>
        <el-table-column label="计划数量" prop="Quantity">
          <template slot-scope="scope">
            <div>{{ scope.row.Quantity }} {{ scope.row.UnitName }}</div>
            <div class="color_gray font_12" v-if="scope.row.MinimumUnitQuantity">最小包装数量：{{ scope.row.MinimumUnitQuantity }} {{ scope.row.MinimumUnitName || scope.row.UnitName }}</div>
          </template>
        </el-table-column>
        <el-table-column label="已入库数量" prop="ActualQuantity" v-if="storageInfoDetailForm.BillStatus !== '10'">
          <template slot-scope="scope">
            <div>{{ scope.row.ActualQuantity || 0 }} {{ scope.row.UnitName }}</div>
            <div class="color_gray font_12" v-if="scope.row.ActualMinimumUnitQuantity">最小包装数量：{{ scope.row.ActualMinimumUnitQuantity || 0 }} {{ scope.row.MinimumUnitName || scope.row.UnitName }}</div>
          </template>
        </el-table-column>
        <el-table-column label="剩余数量" prop="RemainingQuantity" v-if="storageInfoDetailForm.BillStatus === '15'">
          <template slot-scope="scope">
            <div :class="{ 'text-success': scope.row.RemainingQuantity === 0, 'text-warning': scope.row.RemainingQuantity > 0 }">
              {{ scope.row.RemainingQuantity || 0 }} {{ scope.row.UnitName }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="单价(元)">
          <template slot-scope="scope">{{ scope.row.UnitPrice | toFixed | NumFormat }}</template>
        </el-table-column>
        <el-table-column label="计划金额(元)">
          <template slot-scope="scope">{{ ((scope.row.Quantity || 0) * (scope.row.UnitPrice || 0)) | toFixed | NumFormat }}</template>
        </el-table-column>
        <el-table-column label="实际金额(元)" v-if="storageInfoDetailForm.BillStatus !== '10'">
          <template slot-scope="scope">{{ (scope.row.ActualAmount || 0) | toFixed | NumFormat }}</template>
        </el-table-column>
        <el-table-column label="完成状态" v-if="storageInfoDetailForm.BillStatus !== '10'">
          <template slot-scope="scope">
            <el-tag :type="scope.row.IsCompleted ? 'success' : 'warning'">
              {{ scope.row.IsCompleted ? '已完成' : '未完成' }}
            </el-tag>
          </template>
        </el-table-column>
      </el-table>

      <!-- 附件信息 -->
      <div v-if="storageInfoDetailForm.AttachmentList && storageInfoDetailForm.AttachmentList.length > 0" class="attachment-section martp_20">
        <div class="section-title">
          <i class="el-icon-paperclip"></i>
          <span>相关附件 ({{ storageInfoDetailForm.AttachmentList.length }})</span>
        </div>

        <div class="attachment-list-display">
          <div
            v-for="attachment in storageInfoDetailForm.AttachmentList"
            :key="attachment.ID"
            class="attachment-item-display"
          >
            <!-- 图片附件 -->
            <div v-if="attachment.AttachmentType === 10" class="attachment-image">
              <el-image
                :src="attachment.AttachmentURL"
                fit="cover"
                class="attachment-thumbnail-display"
                :preview-src-list="getDetailImagePreviewList()"
              >
                <div slot="error" class="image-error">
                  <i class="el-icon-picture-outline"></i>
                  <div>图片加载失败</div>
                </div>
              </el-image>
              <div class="attachment-info">
                <div class="attachment-name" :title="attachment.AttachmentName">
                  {{ attachment.AttachmentName }}
                </div>
                <div class="attachment-meta">
                  <span class="attachment-size">{{ attachment.FileSizeFormatted || formatFileSize(attachment.FileSize) }}</span>
                  <span class="attachment-time">{{ attachment.CreatedOn | dateFormat('MM-DD HH:mm') }}</span>
                </div>
                <div v-if="attachment.Remark" class="attachment-remark">
                  {{ attachment.Remark }}
                </div>
              </div>
            </div>

            <!-- 文档附件 -->
            <div v-else class="attachment-document">
              <div class="document-icon">
                <i :class="getDetailFileIcon(attachment)" class="file-icon-large"></i>
              </div>
              <div class="attachment-info">
                <div class="attachment-name" :title="attachment.AttachmentName">
                  {{ attachment.AttachmentName }}
                </div>
                <div class="attachment-meta">
                  <span class="attachment-type">{{ attachment.AttachmentTypeName || getAttachmentTypeName(attachment.AttachmentType) }}</span>
                  <span class="attachment-size">{{ attachment.FileSizeFormatted || formatFileSize(attachment.FileSize) }}</span>
                  <span class="attachment-time">{{ attachment.CreatedOn | dateFormat('MM-DD HH:mm') }}</span>
                </div>
                <div v-if="attachment.Remark" class="attachment-remark">
                  {{ attachment.Remark }}
                </div>
                <div class="attachment-actions">
                  <el-button
                    type="text"
                    size="mini"
                    @click="previewDetailAttachment(attachment)"
                    icon="el-icon-view"
                  >
                    预览
                  </el-button>
                  <el-button
                    type="text"
                    size="mini"
                    @click="downloadAttachment(attachment)"
                    icon="el-icon-download"
                  >
                    下载
                  </el-button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 无附件提示 -->
      <div v-else class="no-attachment-tip martp_20">
        <i class="el-icon-document"></i>
        <span>暂无相关附件</span>
      </div>

      <div slot="footer">
        <el-button @click="dialogVisibleInfo = false" size="small" v-prevent-click>关闭</el-button>
      </div>
    </el-dialog>
    <!-- 打印 -->
    <el-dialog title="选择打印模板" :visible.sync="printTemplateVisible" width="400px">
      <el-select size="small" v-model="printTemplateID" @change="changePrintTemplate">
        <el-option v-for="item in templateTypeList" :key="item.ID" :label="item.Name" :value="item.ID"></el-option>
      </el-select>
      <div slot="footer">
        <el-button @click="printTemplateVisible = false" size="small" v-prevent-click>取消</el-button>
        <el-button v-print="'printContent'" type="primary" @click="confirmPrintTemplate" size="small" v-prevent-click>打印</el-button>
      </div>
    </el-dialog>
    <div style="display: none">
      <component id="printContent" :is="printComponentName"></component>
    </div>

    <!-- 入库详情小票打印 -->
    <!--    <el-dialog :visible.sync="cashierReceiptDialogVisible" width="300px">
      <span slot="title" class="font_14 color_333">打印小票</span>
      <div v-if="storageInfoDetailForm && userInfo">
        <el-row>
          <el-col :span="24">
            <el-scrollbar class="el-scrollbar_height" style="height:500px">
              <div class="marrt_10">
                <div class="dis_flex">
                  <span
                    class="flex_box text_center font_16"
                    style="line-height:32px"
                  >{{userInfo.EntityName}}</span>
                </div>
                <el-divider>
                  <span class="font_12 color_gray">订单信息</span>
                </el-divider>

                <div class="dis_flex">
                  <span class="flex_box font_12 color_gray text-left line_height_24">单据号</span>
                  <span
                    class="font_12 text_right line_height_24"
                    style="flex:3;"
                  >{{storageInfoDetailForm.ID}}</span>
                </div>
                <div class="dis_flex">
                  <span class="flex_box font_12 color_gray text-left line_height_24">仓库/门店</span>
                  <span
                    class="flex_box font_12 text_right line_height_24"
                  >{{storageInfoDetailForm.EntityName}}</span>
                </div>
                <div class="dis_flex">
                  <span class="flex_box font_12 color_gray text-left line_height_24">供应商</span>
                  <span
                    class="flex_box font_12 text_right line_height_24">{{storageInfoDetailForm.SupplierName}}</span>
                </div>
                <div class="dis_flex">
                  <span class="flex_box font_12 color_gray text-left line_height_24">入库时间</span>
                  <span
                    class="font_12 text_right line_height_24"
                    style="flex:3;"
                  >{{storageInfoDetailForm.InDate}}</span>
                </div>
                <el-divider>
                  <span class="font_12 color_gray">采购明细</span>
                </el-divider>
                <template v-for="(item,index) in storageInfoDetailForm.Detail">
                  <div :key="index + 'Detail' + item.ProductID">
                    <div class="dis_flex">
                      <span class="font_12 color_gray text-left line_height_24" style="flex:2;">
                      {{item.ProductName}}
                      </span>
                      <span
                        class="font_12 text_right line_height_24"
                        style="flex:1;"
                      >￥{{item.Price | NumFormat}}</span>
                    </div>
                    <div class="dis_flex">
                      <span
                        class="font_12 color_gray text-left line_height_24 marlt_10"
                        style="flex:2;"
                      >数量</span>
                      <span
                        class="font_12 text_right line_height_24"
                        style="flex:1;"
                      >{{item.Quantity+item.UnitName}}</span>
                    </div>
                    <div class="dis_flex">
                      <span
                        class="font_12 color_gray text-left line_height_24 marlt_10"
                        style="flex:2;"
                      >小计</span>
                      <span
                        class="font_12 text_right line_height_24"
                        style="flex:1;"

                      >￥{{item.Amount | NumFormat}}</span>
                    </div>
                     <el-divider>

                </el-divider>
                  </div>

                </template>
              </div>
            </el-scrollbar>
          </el-col>
        </el-row>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button
          @click="cashierReceiptDialogVisible = false;"
          size="small"
          :disabled="modalLoading"
        >取 消</el-button>
        <el-button
          type="primary"
          @click="submitPrint"
          :loading="modalLoading"
          v-prevent-click
          size="small"
        >打印</el-button>
      </div>
    </el-dialog> -->
  </div>
</template>

<script>
import APIStorage from "@/api/PSI/Purchase/storage";
import APIStock from "@/api/PSI/Inventory/inventoryDetail";
var dayjs = require("dayjs");
var Enumerable = require("linq");
import print from "vue-print-nb";
import Vue from "vue";
export default {
  name: "Storage",
  directives: {
    print,
  },
  /**  引入的组件  */
  components: {},
  /**  Vue 实例的数据对象**/
  data() {
    return {
      printComponentName: "",
      printContent: "",
      printTemplateVisible: false,
      printTemplateID: "",
      templateTypeList: [],

      userInfo: {},
      modalLoading: false,
      loading: false,
      dialogVisible: false,
      dialogVisibleInfo: false,
      productLoading: false,
      removeDisabled: true,
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now() ? true : false;
        },
      },
      searchForm: {
        ID: "", // 单据号
        Name: "", //产品名称
        EntityID: "", //仓库id
        StorageID: "", // 供应商id
        createTime: "", //时间
      },
      storageList: [], //库存列表
      paginations: {
        page: 1, // 当前位于哪页
        total: 0, // 总数
        page_size: 10, // 1页显示多少条
        layout: "total, prev, pager, next, jumper", // 翻页属性
      }, //需要给分页组件传的信息
      purchaseStorage: [], // 仓库列表
      StorageList: [], //供应商列表
      allProductList: [], //可添加的产品列表
      addStorageRulesMsg: {
        InDate: [
          {
            required: true,
            message: "请选择入库时间",
            trigger: ["blur", "change"],
          },
        ],
        EntityID: [
          {
            required: true,
            message: "请选择仓库/门店",
            trigger: ["blur", "change"],
          },
        ],
      }, //创建入库 验证规则
      addStorageForm: {
        InDate: dayjs().format("YYYY-MM-DD HH:mm:ss"), //入库时间
        EntityID: "", //仓库ID
        EntityName: "",
        SupplierID: "", //供应商ID
        SupplierName: "",
        Remark: "", // 备注
        Product: [
          // {
          //   ProductID: "",
          //   UnitID: "",
          //   UnitPrice: "",
          //   Quantity: "",
          //   Amount: "",
          //   MinimumUnitID: "",
          //   MinimumUnitName: "",
          //   MinimumUnitQuantity: "",
          //   UnitAmount: "",
          //   Units: [],
          // },
        ], // 产品列表
      },
      addProductItemRules: {
        ProductID: [{ required: true, trigger: ["blur", "change"] }],
        UnitID: [{ required: true, trigger: ["blur", "change"] }],
        UnitPrice: [{ required: true, trigger: ["blur", "change"] }],
        Quantity: [{ required: true, trigger: ["blur", "change"] }],
      }, //产品验证规则
      storageInfoDetailForm: {}, //入库详情
      multipleProducts: [], //勾选的产品明细
      productPageNum: 1,
      allProducTotal: 0,
    };
  },
  /**计算属性  */
  computed: {},
  /**  方法集合  */
  methods: {
    /** 确定选择打印模板，并打印   */
    confirmPrintTemplate() {
      let that = this;
      that.printTemplateVisible = false;
      that.$nextTick(() => {
        that.createPrintComponent(that.storageInfoDetailForm, that.printContent);
      });
    },
    /** 修改打印模板   */
    changePrintTemplate(val) {
      let that = this;
      let tempItem = that.templateTypeList.filter((item) => item.ID == val);
      that.printContent = tempItem[0].Template;
    },
    // 创建打印组件
    createPrintComponent(info, printContent) {
      let tempInfo = info; //传入打印数据源
      let templateStr = printContent; //传入打印模板
      var timestamp = new Date().valueOf();
      var componentName = "print" + timestamp;

      //创建组件
      Vue.component(componentName, {
        data: function () {
          return {
            info: tempInfo, //传入打印数据源
          };
        },
        template: "<div>" + templateStr + "</div>", //打印模板
      });
      this.printComponentName = componentName; //显示打印组件
    },

    /**  打印  */
    /**    */
    printInfoTips() {
      let that = this;
      that.$message.error("暂无打印模板，请添加打印模板");
    },
    printInfoSelectTemplate() {
      let that = this;
      that.printTemplateID = "";
      that.printTemplateVisible = true;
    },
    printInfo() {
      let that = this;
      let tempPrintTemplate = that.templateTypeList.length == 1 ? that.templateTypeList[0].Template : "";
      that.createPrintComponent(that.storageInfoDetailForm, tempPrintTemplate);
    },
    /**  点击事件  */
    /**  搜索  */
    handleSearchStorageClick() {
      this.paginations.page = 1;
      this.getpurchaseStorageListNetwork();
    },




    /**  修改页码  */
    handleCurrentChange(page) {
      var that = this;
      that.paginations.page = page;
      that.getpurchaseStorageListNetwork();
    },

    // 获取状态标签类型
    getStatusTagType(status) {
      const statusMap = {
        '10': 'warning',  // 待确认 - 橙色
        '15': 'primary',  // 部分确认 - 蓝色
        '20': 'success'   // 已确认 - 绿色
      };
      return statusMap[status] || 'info';
    },

    /**  查看详情  */
    showStorageInfoClick(row) {
      var that = this;
      that.dialogVisibleInfo = true;
      that.loading = true;
      that.getStorageInfoNetwork(row.ID, row);
    },
    /**  添加 采购  */
    addStorageOrderClick() {
      var that = this;
      that.dialogVisible = true;
      that.addStorageForm = {
        InDate: dayjs().format("YYYY-MM-DD HH:mm:ss"), //入库时间
        EntityID: "", //仓库ID
        EntityName: "",
        SupplierID: "", //供应商ID
        SupplierName: "",
        Remark: "", // 备注
        Product: [], // 产品列表
      };
      if (that.purchaseStorage.length == 1) {
        //当仓库/门店数量为1，默认选中
        that.addStorageForm.EntityID = that.purchaseStorage[0].ID;
        that.addStorageForm.EntityName = that.purchaseStorage[0].EntityName;
      }
    },

    /**添加采购入库 选择仓库   */
    handleSelectProductEntity(item) {
      this.addStorageForm.EntityID = item.ID;
      this.addStorageForm.EntityName = item.EntityName;
      this.addStorageForm.Product = [];
    },
    /** 添加供应商  */
    handleSelectProductStorage(item) {
      this.addStorageForm.SupplierID = item.ID;
      this.addStorageForm.SupplierName = item.Name;
    },

    /** 添加产品   */
    addProducts() {
      var that = this;
      that.$refs["addStorageForm"].validateField("EntityID", (valid) => {
        if (!valid) {
          that.addStorageForm.Product.push({
            ProductID: "",
            Unit: "",
            UnitID: "",
            UnitName: "",
            UnitPrice: "",
            Quantity: "",
            Amount: "",
            MinimumUnitID: "",
            MinimumUnitName: "",
            MinimumUnitQuantity: "",
            Specification: "",
            UnitAmount: "",
            Units: [],
          });
        }
      });
    },
    /**  批量删除  */
    removeMultipleProduct() {
      var that = this;
      if (that.multipleProducts.length > 0) {
        for (var i = 0; i < that.addStorageForm.Product.length; i++) {
          that.multipleProducts.forEach(function (item) {
            if (that.addStorageForm.Product[i] == item) {
              that.addStorageForm.Product.splice(i, 1);
            }
          });
        }
      }
    },

    /**  多选删除  */
    handleChangeSelectProduct(selection) {
      this.multipleProducts = selection;
      if (this.multipleProducts.length > 0) {
        this.removeDisabled = false;
      } else {
        this.removeDisabled = true;
      }
    },
    /** 获取焦点   */
    selectFocus() {
      this.searchProductListMethod("");
    },
    /**  下拉选择产品  */
    handleSelectProduct(itemID, row) {
      var that = this;
      row.Unit = "";
      row.UnitID = "";
      row.UnitName = "";
      row.UnitAmount = "";
      row.MinimumUnitQuantity = "";
      row.MinimumUnitID = "";
      row.MinimumUnitName = "";
      row.Specification = "";
      row.Quantity = "";
      row.UnitPrice = "";
      row.Amount = "";
      row.MinimumUnitQuantity = "";
      var item = Enumerable.from(that.allProductList)
        .where(function (p) {
          return itemID == p.ID;
        })
        .toArray()[0];
      item.Unit.forEach((unit) => {
        if (unit.IsDefautSendReceive) {
          row.Unit = unit;
          row.UnitID = unit.UnitID;
          row.UnitName = unit.UnitName;
          row.UnitAmount = unit.Amount;
          row.MinimumUnitQuantity = unit.Amount * row.Quantity;
        }
        if (unit.IsMinimumUnit) {
          row.MinimumUnitID = unit.UnitID;
          row.MinimumUnitName = unit.UnitName;
        }
      });
      row.ProductID = itemID;
      row.Units = item.Unit;
      row.ProductName = item.ProductName;
      row.Specification = item.Specification;
    },
    /** 清除所选商品   */
    clearSelectProduct() {},

    /**  选择产品单位  */
    handleSelectProductUnit(Unit, row) {
      row.UnitID = Unit.UnitID;
      row.UnitName = Unit.UnitName;
      row.UnitAmount = Unit.Amount;
      row.MinimumUnitQuantity = Unit.Amount * row.Quantity;
    },
    /**  清除所选产品单位  */
    clearSelectProductUnit(row) {
      row.UnitID = "";
      row.UnitName = "";
      row.UnitPrice = "";
      row.Quantity = "";
      row.Amount = "";

      row.MinimumUnitQuantity = "";
      row.UnitAmount = "";
    },
    /**  修改产品单价  */
    changeUnitPrice(row) {
      if (row.Quantity) {
        row.Amount = (row.Quantity * row.UnitPrice).toFixed(2);
      }
    },
    /**  修改产品采购数量  */
    changeQuantity(row) {
      row.Quantity = Math.floor(row.Quantity);
      row.MinimumUnitQuantity = row.UnitAmount * row.Quantity;
      if (row.UnitPrice) {
        row.Amount = (row.Quantity * row.UnitPrice).toFixed(2);
      }
    },

    /** 取消   */
    cancelAddSubmitStorageClick() {
      var that = this;
      that.$refs["addStorageForm"].resetFields();
      that.$refs["addStorageFormProduct"].resetFields();
      that.dialogVisible = false;
    },
    /**   保存入库 */
    addSubmitStorageClick() {
      var that = this;
      var isValid = false;
      if (that.addStorageForm.Product.length == 0) {
        that.$message.error({
          message: "请添加入库产品",
          duration: 2000,
        });
        return;
      }
      var isValidProduct = false;
      that.$refs["addStorageForm"].validate((valid) => {
        isValid = valid;
      });

      that.$refs["addStorageFormProduct"].validate((valid) => {
        isValidProduct = valid;
      });

      if (isValid && isValidProduct) {
        that.modalLoading = true;
        that.getStorageCreateNetwork(that.addStorageForm);
      }
    },
    /** 远程搜索产品   */
    searchProductListMethod(query) {
      var that = this;
      that.productPageNum = 1;
      that.allProductList = [];
      that.getStorageListNetwork(query);
    },
    /**  加载更多产品  */
    loadMoreProduct() {
      var that = this;
      if (that.allProducTotal > that.allProductList.length) {
        that.productPageNum++;
        that.getStorageListNetwork("");
      }
    },

    /**  格式化 最小包装单位数量  */
    MinimumUnitQuantityAndUnit(row) {
      if (row.Quantity) {
        return row.MinimumUnitQuantity + " " + row.MinimumUnitName;
      }
      return "";
    },

    /**  网络请求  */
    /**  4.1.采购入库列表  */
    getpurchaseStorageListNetwork: function () {
      var that = this;
      that.loading = true;

      var params = {
        PageNum: that.paginations.page,
        ID: that.searchForm.ID,
        ProductName: that.searchForm.Name,
        EntityID: that.searchForm.EntityID,
        SupplierID: that.searchForm.SupplierID,
        StartDate: that.searchForm.createTime == null ? "" : that.searchForm.createTime[0],
        EndDate: that.searchForm.createTime == null ? "" : that.searchForm.createTime[1],
      };
      APIStorage.getpurchaseStorageList(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.storageList = res.List;
            that.paginations.page_size = res.PageSize;
            that.paginations.total = res.Total;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },
    /**  4.2.创建采购入库单（待确认状态） */
    getStorageCreateNetwork: function (params) {
      var that = this;
      // 使用新的createDraft接口创建待确认入库单
      APIStorage.createDraft(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.dialogVisible = false;
            that.getpurchaseStorageListNetwork();
            that.$message.success('入库单创建成功，状态为待确认');
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.modalLoading = false;
        });
    },
    /**  4.3.采购入库单详情  */
    getStorageInfoNetwork: function (ID, row) {
      var that = this;
      var params = {
        ID: ID,
      };
      // 使用fullInfo接口获取包含附件的完整信息
      APIStorage.fullInfo(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.storageInfoDetailForm = res.Data;

            // 确保状态信息从列表行数据传递过来
            if (row) {
              if (!that.storageInfoDetailForm.BillStatus && row.BillStatus) {
                that.storageInfoDetailForm.BillStatus = row.BillStatus;
                that.storageInfoDetailForm.BillStatusName = row.BillStatusName;
              }

              // 确保确认人和确认时间信息从列表传递过来
              if (!that.storageInfoDetailForm.ConfirmedByName && row.ConfirmedBy) {
                that.storageInfoDetailForm.ConfirmedByName = row.ConfirmedBy;
              }
              if (!that.storageInfoDetailForm.ConfirmedOn && row.ConfirmedOn) {
                that.storageInfoDetailForm.ConfirmedOn = row.ConfirmedOn;
              }
            }
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
      // that.getReceiptConfig();
    },
    /** 获取模板列表   */
    async getPrintTemplate_list() {
      let that = this;
      let params = { TemplateType: "purchasestorage" };
      let res = await APIStorage.getPrintTemplate_list(params);
      if (res.StateCode == 200) {
        that.templateTypeList = res.Data;
      } else {
        that.$message.error(res.Message);
      }
    },
    /**  获取小票配置信息  */
    /*  getReceiptConfig() {
      var that = this;
      APIStorage
        .getReceiptConfigBill()
        .then((res) => {
          if (res.StateCode == 200) {
            that.cashierReceipt = res.Data;
          }
        })
        .finally(() => {});
    },
 */
    // 小票详情
    /* submitPrint(){
 var that = this;
      var params = {
        ID: that.storageInfoDetailForm.ID,
      };
      APIStorage.print(params)
        .then((res) => {
          if (res.StateCode == 200) {
             for (
              let index = 0;
              index < res.Data.copies;
              index++
            ) {
              printReceipt.doActionPrint(res.Data.printDocuments, (request) => {
                socket.send(JSON.stringify(request));
              });
            }
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    }, */
    /**  4.4.仓库列表  */
    getStorageEntityNetwork: function () {
      var that = this;
      that.loading = true;
      var params = {};
      APIStorage.getpurchaseStorageEntity(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.purchaseStorage = res.Data;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },
    /** 4.5.供应商列表  */
    getStorageStorageNetwork: function () {
      var that = this;
      that.loading = true;
      var params = {};
      APIStorage.getpurchaseStorageSupplier(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.StorageList = res.Data;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },
    /**  4.6.采购入库产品列表 */
    getStorageListNetwork: function (query) {
      var that = this;
      var params = {
        PageNum: that.productPageNum,
        ProductName: query,
        EntityID: that.addStorageForm.EntityID,
      };
      APIStock.get_stock_list_entityProductListAll(params).then((res) => {
        if (res.StateCode == 200) {
          that.allProducTotal = res.Total;
          that.allProductList.push.apply(that.allProductList, res.List);
        } else {
          that.$message.error({
            message: res.Message,
            duration: 2000,
          });
        }
      });
    },
    /**  格式化明细商品名称 中间以... 代替  */
    formatProductName(name) {
      if (!name) return "";
      if (name.length > 20) {
        let frontStr = name.substr(0, 8);
        let afterStr = name.substr(name.length - 10, name.length);
        return frontStr + " ... " + afterStr;
      }
      return name;
    },
    /**  获取产品是否显示提示  */
    getShowProductNameTooltip(name) {
      if (!name || name == "") {
        return true;
      }
      if (name.length > 20) {
        return false;
      }
      return true;
    },

    // ========== 附件展示相关方法 ==========

    // 获取详情页面图片预览列表
    getDetailImagePreviewList() {
      if (!this.storageInfoDetailForm.AttachmentList) return [];
      return this.storageInfoDetailForm.AttachmentList
        .filter(attachment => attachment.AttachmentType === 10)
        .map(attachment => attachment.AttachmentURL);
    },

    // 获取详情页面文件图标
    getDetailFileIcon(attachment) {
      const mimeType = attachment.MimeType || '';

      if (mimeType.includes('pdf')) {
        return 'el-icon-document';
      } else if (mimeType.includes('word')) {
        return 'el-icon-document';
      } else if (mimeType.includes('excel') || mimeType.includes('sheet')) {
        return 'el-icon-s-grid';
      } else {
        return 'el-icon-document';
      }
    },

    // 获取附件类型名称
    getAttachmentTypeName(type) {
      switch (type) {
        case 10: return '图片';
        case 20: return '文档';
        case 30: return '其他';
        default: return '未知';
      }
    },

    // 格式化文件大小
    formatFileSize(bytes) {
      if (!bytes) return '0 B';
      const k = 1024;
      const sizes = ['B', 'KB', 'MB', 'GB'];
      const i = Math.floor(Math.log(bytes) / Math.log(k));
      return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
    },

    // 预览详情附件
    previewDetailAttachment(attachment) {
      if (attachment.AttachmentType === 10) {
        // 图片预览由el-image组件自动处理
        return;
      } else {
        // 非图片文件，在新窗口打开
        if (attachment.AttachmentURL) {
          window.open(attachment.AttachmentURL, '_blank');
        } else {
          this.$message.info('该文件暂无预览地址');
        }
      }
    },

    // 下载附件
    downloadAttachment(attachment) {
      if (attachment.AttachmentURL) {
        // 创建一个临时的a标签来触发下载
        const link = document.createElement('a');
        link.href = attachment.AttachmentURL;
        link.download = attachment.AttachmentName;
        link.target = '_blank';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      } else {
        this.$message.error('附件下载地址不存在');
      }
    },
  },
  /**  实例被挂载后调用  */
  mounted() {
    var that = this;
    // var date = new Date(),
    //   y = date.getFullYear(),
    //   m = date.getMonth();
    // that.searchForm.createTime = [new Date(y, m, 1), new Date()];
    that.userInfo = JSON.parse(localStorage.getItem("access-user"));
    that.getpurchaseStorageListNetwork();
    that.getStorageEntityNetwork();
    that.getStorageStorageNetwork();
    that.getPrintTemplate_list();
    /* socket = printReceipt.getSocket((res) => {
      if (res.status == "success") {
        that.$message.success({
          message: "打印成功",
          duration: 2000,
        });
        that.treatCashierReceiptDialogVisible = false;
        that.refundTreatCashierReceiptDialogVisible = false;
      }
    }); */
  },
};
</script>

<style lang="scss">
.Storage {
  .storageInfoFrom {
    .el-form-item__label {
      font-size: 13px !important;
    }
    .el-form-item__content {
      font-size: 13px !important;
    }
    // .el-form-item {
    //   margin-bottom: 5px;
    // }
  }
  .storageInfoDetailFrom {
    .el-form-item__label {
      font-size: 13px !important;
    }
    .el-form-item__content {
      font-size: 13px !important;
    }
    .el-form-item {
      margin-bottom: 0px;
    }
  }

  .productFormInforClass {
    .el-form-item__label {
      font-size: 13px !important;
    }
    .el-form-item__content {
      font-size: 13px !important;
    }
    .el-form-item {
      margin-bottom: 0px;
    }

    .el-input__inner {
      padding-right: 0;
    }
  }

  // 详情附件展示样式
  .attachment-section {
    border-top: 1px solid #ebeef5;
    padding-top: 15px;

    .section-title {
      display: flex;
      align-items: center;
      margin-bottom: 15px;
      font-weight: 500;
      color: #303133;

      i {
        margin-right: 8px;
        color: #409eff;
      }
    }

    .attachment-list-display {
      display: flex;
      flex-wrap: wrap;
      gap: 15px;

      .attachment-item-display {
        border: 1px solid #ebeef5;
        border-radius: 6px;
        overflow: hidden;
        background: #fff;
        transition: all 0.3s;

        &:hover {
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        // 图片附件样式
        .attachment-image {
          width: 200px;

          .attachment-thumbnail-display {
            width: 100%;
            height: 120px;
            object-fit: cover;

            .image-error {
              display: flex;
              flex-direction: column;
              align-items: center;
              justify-content: center;
              height: 120px;
              background: #f5f7fa;
              color: #909399;

              i {
                font-size: 24px;
                margin-bottom: 5px;
              }

              div {
                font-size: 12px;
              }
            }
          }

          .attachment-info {
            padding: 10px;
          }
        }

        // 文档附件样式
        .attachment-document {
          width: 250px;
          display: flex;
          padding: 15px;

          .document-icon {
            margin-right: 15px;
            display: flex;
            align-items: center;

            .file-icon-large {
              font-size: 32px;
              color: #409eff;
            }
          }

          .attachment-info {
            flex: 1;
            min-width: 0;
          }
        }

        .attachment-info {
          .attachment-name {
            font-weight: 500;
            color: #303133;
            margin-bottom: 5px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }

          .attachment-meta {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-bottom: 5px;

            span {
              font-size: 12px;
              color: #909399;
              background: #f5f7fa;
              padding: 2px 6px;
              border-radius: 3px;
            }
          }

          .attachment-remark {
            font-size: 12px;
            color: #606266;
            margin-bottom: 8px;
            line-height: 1.4;
          }

          .attachment-actions {
            display: flex;
            gap: 5px;

            .el-button--text {
              padding: 0;
              font-size: 12px;
            }
          }
        }
      }
    }
  }

  .no-attachment-tip {
    text-align: center;
    color: #909399;
    padding: 20px;
    border-top: 1px solid #ebeef5;

    i {
      font-size: 24px;
      margin-right: 8px;
    }
  }

  .martp_20 {
    margin-top: 20px;
  }
}
.StorageIn-info-width {
  width: 230px;
}
.Storage_custom_popper_class {
  .el-select-dropdown__item {
    line-height: normal;
    height: auto;
  }
}
.el-scrollbar_height {
  height: 100%;
  .el-scrollbar__wrap {
    overflow-x: hidden;
  }
}

@media print {
  html,
  body {
    height: inherit;
  }
}
/*.el-dialog .el-form-item__label{*/
/*  width:100px*/
/*}*/
</style>
