/**
 * Created by preference on 2024/03/07
 *  zmx
 */

import * as API from "@/api/index";
export default {
  /** 查询病例目录 */
  medicalRecord_list: (params) => {
    return API.POST("api/medicalRecord/list", params);
  },
  /**创建病例目录  */
  medicalRecord_createCatalog: (params) => {
    return API.POST("api/medicalRecord/createCatalog", params);
  },
  /** 更新病例目录  */
  medicalRecord_updateCatalog: (params) => {
    return API.POST("api/medicalRecord/updateCatalog", params);
  },
  /**  删除病例目录 */
  medicalRecord_deleteCatalog: (params) => {
    return API.POST("api/medicalRecord/deleteCatalog", params);
  },
  /**  移动病例目录 */
  medicalRecord_moveCatalog: (params) => {
    return API.POST("api/medicalRecord/moveCatalog", params);
  },

  /**  查询病例模版 */
  medicalRecord_getTemplate: (params) => {
    return API.POST("api/medicalRecord/getTemplate", params);
  },

  /**  创建病例模版 */
  medicalRecord_createTemplate: (params) => {
    return API.POST("api/medicalRecord/createTemplate", params);
  },

  /**  更新病例模版 */
  medicalRecord_updateTemplate: (params) => {
    return API.POST("api/medicalRecord/updateTemplate", params);
  },

  /**  删除病例模版 */
  medicalRecord_deleteTemplate: (params) => {
    return API.POST("api/medicalRecord/deleteTemplate", params);
  },

  /**  移动病例模版 */
  medicalRecord_moveTemplate: (params) => {
    return API.POST("api/medicalRecord/moveTemplate", params);
  },
  /**  查询词条类别 */
  medicalRecord_categoryAll: (params) => {
    return API.POST("api/medicalRecord/categoryAll", params);
  },
};
