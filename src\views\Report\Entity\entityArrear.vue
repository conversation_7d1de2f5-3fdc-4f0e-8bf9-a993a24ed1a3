<template>
  <div class="entityArrear content_body"  v-loading="loading">
    <!-- 搜索 -->
    <div class="nav_header">
      <el-form :inline="true" size="small" @submit.native.prevent>
        <el-form-item label="门店">
          <el-select v-model="EntityID" clearable filterable placeholder="请选择门店" :default-first-option="true" @change="handleSearch">
            <el-option v-for="item in EntityList" :key="item.ID" :label="item.EntityName" :value="item.ID"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" v-prevent-click @click="handleSearch">搜索</el-button>
        </el-form-item>
        <el-form-item>
          <el-button v-if="isExport" type="primary" size="small" v-prevent-click :loading="downloadLoading" @click="downloadSalePayExcel">导出</el-button>
        </el-form-item>
      </el-form>
    </div>
    <!-- 表格 -->
    <el-table :data="tableData" size="small" show-summary :summary-method="getSummary">
      <el-table-column label="门店" width="150" prop="EntityName"></el-table-column>
      <el-table-column label="合计欠款客户数" prop="ArrearCustomerCount"></el-table-column>
      <el-table-column label="合计欠款金额" prop="TotalAmount">
        <template slot-scope="scope">
          {{ scope.row.TotalAmount | toFixed | NumFormat }}
        </template>
      </el-table-column>
      <el-table-column label="项目" align="center">
        <el-table-column label="欠款客户数" prop="ProjectArrearCustomerCount" align="right"></el-table-column>
        <el-table-column label="欠款金额" prop="ProjectTotalAmount" align="right">
          <template slot-scope="scope">
            {{ scope.row.ProjectTotalAmount | toFixed | NumFormat }}
          </template>
        </el-table-column>
      </el-table-column>
      <el-table-column label="产品" align="center">
        <el-table-column label="欠款客户数" prop="ProductArrearCustomerCount" align="right"></el-table-column>
        <el-table-column label="欠款金额" prop="ProductTotalAmount" align="right">
          <template slot-scope="scope">
            {{ scope.row.ProductTotalAmount | toFixed | NumFormat }}
          </template>
        </el-table-column>
      </el-table-column>
      <el-table-column label="通用次卡" align="center">
        <el-table-column label="欠款客户数" prop="GeneralCardArrearCustomerCount" align="right"></el-table-column>
        <el-table-column label="欠款金额" prop="GeneralCardTotalAmount" align="right">
          <template slot-scope="scope">
            {{ scope.row.GeneralCardTotalAmount | toFixed | NumFormat }}
          </template>
        </el-table-column>
      </el-table-column>
      <el-table-column label="时效卡" align="center">
        <el-table-column label="欠款客户数" prop="TimeCardArrearCustomerCount" align="right"></el-table-column>
        <el-table-column label="欠款金额" prop="TimeCardTotalAmount" align="right">
          <template slot-scope="scope">
            {{ scope.row.TimeCardTotalAmount | toFixed | NumFormat }}
          </template>
        </el-table-column>
      </el-table-column>
      <el-table-column label="储值卡" align="center">
        <el-table-column label="欠款客户数" prop="SavingCardArrearCustomerCount" align="right"></el-table-column>
        <el-table-column label="欠款金额" prop="SavingCardTotalAmount" align="right">
          <template slot-scope="scope">
            {{ scope.row.SavingCardTotalAmount | toFixed | NumFormat }}
          </template>
        </el-table-column>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <div class="pad_15 text_right">
      <el-pagination
        background
        v-if="paginations.total > 0"
        @current-change="handlePageChange"
        :current-page.sync="paginations.page"
        :page-size="paginations.page_size"
        :layout="paginations.layout"
        :total="paginations.total"
      ></el-pagination>
    </div>
  </div>
</template>

<script>
import API from "@/api/Report/Entity/entityArrear";
import APIStore from "@/api/Report/Entity/entityTrade";
export default {
  name: "ReportEntityArrear",
  beforeRouteEnter(to, from, next) {
    next((vm) => {
      vm.isExport = vm.$permission.permission(to.meta.Permission, "Report-Entity-EntityArrear-Export");
    });
  },
  /** 监听数据变化   */
  watch: {},
  /**  引入的组件  */
  components: {},
  props: {},
  /**  Vue 实例的数据对象**/
  data() {
    return {
      isExport: false,
      loading: false,
      downloadLoading: false,
      EntityList: [], // 门店数据
      tableData: [], // 表格数据
      SumOutputForm: {}, // 合计数据
      EntityID: null,
      paginations: {
        page: 1, // 当前位于哪页
        total: 0, // 总数
        page_size: 10, // 1页显示多少条
        layout: "total, prev, pager, next,jumper", // 翻页属性
      },
    };
  },
  /**计算属性  */
  computed: {},
  /**  方法集合  */
  methods: {
    /* 搜索 */
    handleSearch() {
      let that = this;
      that.paginations.page = 1;
      that.getEntityArrearList();
    },
    /* 分页 */
    handlePageChange(page) {
      let that = this;
      that.paginations.page = page;
      that.getEntityArrearList();
    },
    /* 导出 */
    downloadSalePayExcel() {
      let that = this;
      let params = {
        EntityID: that.EntityID,
      };
      that.downloadLoading = true;
      API.entityArrearExcel(params)
        .then((res) => {
          this.$message.success({
            message: "正在导出",
            duration: "4000",
          });
          const link = document.createElement("a");
          let blob = new Blob([res], { type: "application/octet-stream" });
          link.style.display = "none";
          link.href = URL.createObjectURL(blob);
          link.download = "门店欠款报表.xlsx"; //下载的文件名
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
        })
        .finally(function () {
          that.downloadLoading = false;
        });
    },
    /* 获取表格数据 */
    getEntityArrearList() {
      let that = this;
      that.loading = true;
      let params = {
        EntityID: that.EntityID,
        PageNum: that.paginations.page,
      };
      API.getEntityArrearList(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.tableData = res.Data.Detail.List;
            that.paginations.page_size = res.Data.Detail.PageSize;
            that.paginations.total = res.Data.Detail.Total;
            that.SumOutputForm = res.Data.SumOutputForm;
          } else {
            this.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },
    /* 表格合计 */
    getSummary({ columns }) {
      const sums = [];
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = <span class="font_weight_600">合计</span>;
          return;
        }
        var filter_NumFormat = this.$options.filters["NumFormat"];
        switch (column.property) {
          case "ArrearCustomerCount":
            sums[index] = <span class="font_weight_600">{this.SumOutputForm ? this.SumOutputForm.ArrearCustomerCount : 0}</span>;
            break;
          case "TotalAmount":
            sums[index] = <span class="font_weight_600">{filter_NumFormat(this.SumOutputForm ? this.SumOutputForm.TotalAmount : 0)}</span>;
            break;
          case "ProjectArrearCustomerCount":
            sums[index] = <span class="font_weight_600">{this.SumOutputForm ? this.SumOutputForm.ProjectArrearCustomerCount : 0}</span>;
            break;
          case "ProjectTotalAmount":
            sums[index] = <span class="font_weight_600">{filter_NumFormat(this.SumOutputForm ? this.SumOutputForm.ProjectTotalAmount : 0)}</span>;
            break;
          case "ProductArrearCustomerCount":
            sums[index] = <span class="font_weight_600">{this.SumOutputForm ? this.SumOutputForm.ProductArrearCustomerCount : 0}</span>;
            break;
          case "ProductTotalAmount":
            sums[index] = <span class="font_weight_600">{filter_NumFormat(this.SumOutputForm ? this.SumOutputForm.ProductTotalAmount : 0)}</span>;
            break;
          case "GeneralCardArrearCustomerCount":
            sums[index] = <span class="font_weight_600">{this.SumOutputForm ? this.SumOutputForm.GeneralCardArrearCustomerCount : 0}</span>;
            break;
          case "GeneralCardTotalAmount":
            sums[index] = <span class="font_weight_600">{filter_NumFormat(this.SumOutputForm ? this.SumOutputForm.GeneralCardTotalAmount : 0)}</span>;
            break;
          case "TimeCardArrearCustomerCount":
            sums[index] = <span class="font_weight_600">{this.SumOutputForm ? this.SumOutputForm.TimeCardArrearCustomerCount : 0}</span>;
            break;
          case "TimeCardTotalAmount":
            sums[index] = <span class="font_weight_600">{filter_NumFormat(this.SumOutputForm ? this.SumOutputForm.TimeCardTotalAmount : 0)}</span>;
            break;
          case "SavingCardArrearCustomerCount":
            sums[index] = <span class="font_weight_600">{this.SumOutputForm ? this.SumOutputForm.SavingCardArrearCustomerCount : 0}</span>;
            break;
          case "SavingCardTotalAmount":
            sums[index] = <span class="font_weight_600">{filter_NumFormat(this.SumOutputForm ? this.SumOutputForm.SavingCardTotalAmount : 0)}</span>;
            break;
        }
      });
      return sums;
    },
    /* 获取门店 */
    async getStoreList() {
      var that = this;
      let res = await APIStore.getStoreList();
      if (res.StateCode == 200) {
        that.EntityList = res.Data;
      } else {
        that.$message.error({
          message: res.Message,
          duration: 2000,
        });
      }
    },
  },
  /** 创建实例之前执行函数   */
  beforeCreate() {},
  /**  实例创建完成之后  */
  created() {},
  /**  在挂载开始之前被调用  */
  beforeMount() {},
  /**  实例被挂载后调用  */
  mounted() {
    let that = this;
    this.isExport = this.$permission.permission(this.$route.meta.Permission, "Report-Entity-EntityArrear-Export");
    that.getStoreList();
    that.getEntityArrearList();
  },
  /**  数据更新 之前 调用  */
  beforeUpdate() {},
  /** 数据更新 完成 调用   */
  updated() {},
  /**  实例销毁之前调用  */
  beforeDestroy() {},
  /**  实例销毁后调用  */
  destroyed() {},
};
</script>

<style scoped lang="scss">
.entityArrear {
}
</style>
