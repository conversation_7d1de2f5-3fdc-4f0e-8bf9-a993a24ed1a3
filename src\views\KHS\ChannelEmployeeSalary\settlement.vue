<template>
  <div class="ChannelEmployeeSalarySettlement content_body">
    <!-- 头部搜索 -->
    <div class="nav_header">
      <el-row>
        <el-col :span="21">
          <el-form :inline="true" size="small" @submit.native.prevent @keyup.enter.native="handleSearch">
            <el-form-item label="结算周期" size="small">
              <el-select v-model="searchData.ChannelEmployeeSalarySettlementIntervalID" placeholder="选择结算周期" clearable
                filterable :default-first-option="true" size="small" popper-class="monthSel" @change="handleSearch">
                <el-option v-for="item in allSettlementInterval" :key="item.ID" :label="item.SettlementMonth"
                  :value="item.ID" style="height: 48px">
                  <div style="height: 25px; line-height: 30px">{{ item.SettlementMonth }}</div>
                  <div style="height: 18px; line-height: 18px; font-size: 12px" class="color_999">{{ item.StartDate }} -
                    {{ item.EndDate }}</div>
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="员工名称" size="small">
              <el-input v-model="searchData.Name" placeholder="输入员工名称搜索" clearable @clear="handleSearch"></el-input>
            </el-form-item>
            <el-form-item label="职务">
              <el-select v-model="searchData.JobID" placeholder="选择职务" clearable filterable :default-first-option="true"
                size="small" @change="handleSearch">
                <el-option v-for="item in jobTypeList" :key="item.ID" :label="item.JobName" :value="item.ID">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="单位">
              <treeselect v-model="searchData.EntityID" :options="EntityAllEntity" :normalizer="normalizer"
                clearValueText noResultsText="无匹配数据" placeholder="选择所属单位" @input="handleSearch" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" size="small" @click="handleSearch">搜 索</el-button>
            </el-form-item>
            <el-form-item>
              <el-checkbox v-model="searchData.IsHideZero" @change="handleSearch">仅显示有提成</el-checkbox>
            </el-form-item>
          </el-form>
        </el-col>
        <el-col :span="1" class="text_right">
          <el-button type="primary" size="small" v-prevent-click @click="channelEmployeeSalarySheet_excel"
            :loading="downloadLoading">导出 </el-button>
        </el-col>
        <el-col :span="2" class="text_right">
          <el-button type="primary" size="small" v-prevent-click @click="settlementClick">业绩提成结算 </el-button>
        </el-col>
      </el-row>
    </div>
    <!-- 表格 -->
    <el-table size="small" :data="tableData" v-loading="loading" tooltip-effect="light">
      <el-table-column prop="SettlementMonth" label="结算周期">
        <template slot-scope="scope">
          <div>{{scope.row.SettlementMonth}}</div>
          <div style="height: 18px; line-height: 18px; font-size: 12px" class="color_999">{{ scope.row.StartDate }} -
            {{ scope.row.EndDate }}</div>
        </template>
      </el-table-column>
      <el-table-column prop="EmployeeName" label="员工"></el-table-column>
      <el-table-column prop="JobName" label="职务"></el-table-column>
      <el-table-column prop="ChannelName" label="结算门店">
        <template slot-scope="scope">
          <el-popover placement="top-start" width="200" trigger="hover">
            <div v-for="entity in scope.row.Entity" :key="entity.EntityID" class="font_12 color_666 pad_5">{{
            entity.EntityName }}</div>
            <span slot="reference" class="clamp1">{{ getEntityNames(scope.row.Entity) }}</span>
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column prop="ChannelEmployeePerformanceCommission" label="个人提成">
        <template slot-scope="scope"> ￥{{ scope.row.ChannelEmployeePerformanceCommission | toFixed | NumFormat }}
        </template>
      </el-table-column>
      <el-table-column prop="ChannelEmployeeTeamPerformanceCommission" label="团队提成">
        <template slot-scope="scope"> ￥{{ scope.row.ChannelEmployeeTeamPerformanceCommission | toFixed | NumFormat }}
        </template>
      </el-table-column>
      <el-table-column prop="TotalCommission" label="合计">
        <template slot-scope="scope"> ￥{{ scope.row.TotalCommission | toFixed | NumFormat }} </template>
      </el-table-column>
      <el-table-column label="操作" width="100">
        <template slot-scope="scope">
          <el-button type="primary" size="small" v-prevent-click @click="settlementDetailsClick(scope.row)">结算明细
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <div class="pad_15 text_right">
      <el-pagination background v-if="paginations.total > 0" @current-change="handleCurrentChange"
        :current-page.sync="paginations.page" :page-size="paginations.page_size" :layout="paginations.layout"
        :total="paginations.total"></el-pagination>
    </div>
    <!-- 业绩提成结算 -->
    <el-dialog title="业绩提成结算" :visible.sync="settlementPerformanceDialogVisible" width="590px">
      <el-form size="small" :model="settlementPerformanceData" ref="settlementPerformanceRef"
        :rules="settlementPerformanceDataRules" label-width="100px" class="demo-ruleForm">
        <el-form-item label="结算月份" prop="ChannelEmployeeSalarySettlementIntervalID">
          <el-col :span="13">
            <el-select v-model="settlementPerformanceData.ChannelEmployeeSalarySettlementIntervalID"
              placeholder="请选择结算月份" filterable clearable>
              <el-option v-for="item in allSettlementInterval" :key="item.ID" :label="item.SettlementMonth"
                :value="item.ID"> </el-option>
            </el-select>
          </el-col>
          <el-col :span="11">
            <el-button type="primary" size="small" v-prevent-click @click="createSettlementIntervalClick"> 结算周期设置
            </el-button>
          </el-col>
        </el-form-item>
        <el-form-item label="结算周期" prop="QueryDate">
          <el-date-picker v-model="settlementPerformanceData.QueryDate" :disabled="true" type="daterange"
            range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" value-format="yyyy-MM-dd">
          </el-date-picker>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button size="small" @click="settlementPerformanceDialogVisible = false" v-prevent-click>取 消</el-button>
        <el-button size="small" type="primary" @click="confirmSettlementClick" v-prevent-click :loading="saveLoading">结
          算 </el-button>
      </span>
    </el-dialog>
    <!-- 结算周期设置 -->
    <el-dialog title="结算周期设置" :visible.sync="settlementPeriodDialogVisible" width="550px">
      <el-form size="small" :model="createSettlementInterval" ref="createSettlementIntervalRef"
        :rules="createSettlementIntervalRules" label-width="100px" class="demo-ruleForm">
        <el-form-item label="结算月份" prop="SettlementMonth">
          <el-input v-model="createSettlementInterval.SettlementMonth"></el-input>
        </el-form-item>
        <el-form-item label="结算周期" prop="QueryDate">
          <el-date-picker v-model="createSettlementInterval.QueryDate" type="daterange" range-separator="至"
            start-placeholder="开始日期" end-placeholder="结束日期" value-format="yyyy-MM-dd">
          </el-date-picker>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button size="small" @click="settlementPeriodDialogVisible = false" v-prevent-click>取 消</el-button>
        <el-button size="small" type="primary" @click="confirmCreateSettlementIntervalClick" v-prevent-click
          :loading="settlementSaveLoading">保 存</el-button>
      </span>
    </el-dialog>

    <!-- 结算明细弹出层 -->
    <el-dialog title="业绩提成结算明细" :visible.sync="settlementDetailVisible" width="1100px">
      <el-tabs v-model="activeName">
        <el-tab-pane label="个人业绩提成" name="employee">
          <el-table :data="employeeCommission" height="450px" size="small">
            <el-table-column prop="CommissionName" label="提成名称"> </el-table-column>
            <el-table-column prop="PerformanceName" label="业绩取值名称"></el-table-column>
            <el-table-column prop="Performance" label="业绩">
              <template slot-scope="scope">
                {{ scope.row.Performance | toFixed | NumFormat }}
              </template>
            </el-table-column>
            <el-table-column prop="Commission" label="提成">
              <template slot-scope="scope">
                {{ scope.row.Commission | toFixed | NumFormat }}
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
        <el-tab-pane label="团队业绩提成" name="team">
          <el-table :data="teamCommission" height="450px" size="small">
            <el-table-column prop="CommissionName" label="提成名称"> </el-table-column>
            <el-table-column prop="PerformanceName" label="业绩取值名称"></el-table-column>
            <el-table-column prop="Performance" label="业绩">
              <template slot-scope="scope">
                {{ scope.row.Performance | toFixed | NumFormat }}
              </template>
            </el-table-column>
            <el-table-column prop="Commission" label="提成">
              <template slot-scope="scope">
                {{ scope.row.Commission | toFixed | NumFormat }}
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
      </el-tabs>
      <span slot="footer" class="dialog-footer">
        <el-button size="small" @click="settlementDetailVisible = false">取 消</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import API from "@/api/KHS/ChannelEmployeeSalary/settlement.js";
import APIEntity from "@/api/KHS/Entity/entity";
import APIJob from "@/api/KHS/Entity/jobtype";
import Treeselect from "@riophae/vue-treeselect";
export default {
  name: "ChannelEmployeeSalarySettlement",
  props: {},
  /** 监听数据变化   */
  watch: {
    "settlementPerformanceData.ChannelEmployeeSalarySettlementIntervalID"(ID) {
      if (ID) {
        const item = this.allSettlementInterval.find((item) => item.ID == ID);
        if (item) {
          this.settlementPerformanceData.QueryDate = [item.StartDate, item.EndDate];
        }
      }
    },
    deep: true,
  },
  /**  引入的组件  */
  components: { Treeselect },
  /**  Vue 实例的数据对象**/
  data() {
    return {
      loading: false,
      settlementSaveLoading: false,
      saveLoading: false,
      downloadLoading: false,
      settlementPerformanceDialogVisible: false,
      settlementPeriodDialogVisible: false,
      settlementDetailVisible: false,
      searchData: {
        Name: "", //模糊搜索
        ChannelEmployeeSalarySettlementIntervalID: null, //結算周期ID
        JobID: "", //职务ID
        EntityID: null, //门店ID
        IsHideZero: true, //是否显示有提成
      }, // 搜索数据

      normalizer(node) {
        return {
          id: node.ID,
          label: node.EntityName,
          children: node.Child,
        };
      },
      EntityAllEntity: [], //门店
      jobTypeList: [], //职务

      allSettlementInterval: [], // 结算周期
      settlementPerformanceData: {
        ChannelEmployeeSalarySettlementIntervalID: "",
        QueryDate: [],
      },
      settlementPerformanceDataRules: {
        ChannelEmployeeSalarySettlementIntervalID: [{ required: true, message: "请选择结算月份", trigger: "change" }],
      },
      createSettlementInterval: {
        SettlementMonth: "", //结算月份
        QueryDate: [],
      },
      createSettlementIntervalRules: {
        SettlementMonth: [{ required: true, message: "请输入结算月份", trigger: "blur" }], //结算月份
        QueryDate: [{ required: true, message: "请输入结算周期", trigger: "change" }],
      },

      tableData: [], // 表格数据
      paginations: {
        page: 1, // 当前位于哪页
        total: 0, // 总数
        page_size: 10, // 1页显示多少条
        layout: "total, prev, pager, next,jumper", // 翻页属性
      }, // 分页属性
      activeName: "employee",
      employeeCommission: [],
      teamCommission: [],
    };
  },
  /**计算属性  */
  computed: {},
  /**  方法集合  */
  methods: {
    /**   搜索 */
    handleSearch() {
      let that = this;
      that.paginations.page = 1;
      that.channelEmployeeSalarySheet_all();
    },
    /**    */
    getEntityNames(entitys) {
      return entitys.map((i) => i.EntityName).join(",");
    },
    /**   切换分页 */
    handleCurrentChange(page) {
      let that = this;
      that.paginations.page = page;
      that.channelEmployeeSalarySheet_all();
    },
    /* 业绩提成结算 */
    settlementClick() {
      let that = this;
      that.settlementPerformanceDialogVisible = true;
      that.settlementPerformanceData = {
        ChannelEmployeeSalarySettlementIntervalID: "",
        QueryDate: [],
      };
      if (this.$refs.settlementPerformanceRef) {
        this.$refs.settlementPerformanceRef.resetFields();
      }
    },
    /**  结算周期设置  */
    createSettlementIntervalClick() {
      let that = this;
      that.createSettlementInterval = {
        SettlementMonth: "", //结算月份
        QueryDate: [],
      };
      that.settlementPeriodDialogVisible = true;
    },
    /**   保存结算周期设置 */
    confirmCreateSettlementIntervalClick() {
      let that = this;
      that.$refs.createSettlementIntervalRef.validate((valid) => {
        if (valid) {
          that.channelEmployeeSalarySheet_createSettlementInterval();
        }
      });
    },
    /**  结算  */
    confirmSettlementClick() {
      let that = this;
      that.$refs.settlementPerformanceRef.validate((valid) => {
        if (valid) {
          that.channelEmployeeSalarySheet_settlement();
        }
      });
    },
    /**  结算明细  */
    async settlementDetailsClick(row) {
      let that = this;
      await that.channelEmployeeSalarySheet_employeeCommission(row);
      await that.channelEmployeeSalarySheet_teamCommission(row);
      this.settlementDetailVisible = true;
    },
    /** 请求 •••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••  */
    /**  提成结算列表 */
    async channelEmployeeSalarySheet_all() {
      let that = this;
      try {
        let params = {
          PageNum: that.paginations.page,
          Name: that.searchData.Name, //模糊搜索
          ChannelEmployeeSalarySettlementIntervalID: that.searchData.ChannelEmployeeSalarySettlementIntervalID, //結算周期ID
          JobID: that.searchData.JobID, //职务ID
          EntityID: that.searchData.EntityID, //门店ID
          IsHideZero: that.searchData.IsHideZero, //是否显示有提成
        };
        let res = await API.channelEmployeeSalarySheet_all(params);
        if (res.StateCode == 200) {
          that.tableData = res.List;
          that.paginations.total = res.Total;
        } else {
          that.$message.error(res.Message);
        }
      } catch (error) {
        that.$message.error(error);
      }
    },
    /**  提成结算导出  */
    async channelEmployeeSalarySheet_excel() {
      let that = this;
      that.downloadLoading = true;
      try {
        let params = {
          PageNum: that.paginations.page,
          Name: that.searchData.Name, //模糊搜索
          ChannelEmployeeSalarySettlementIntervalID: that.searchData.ChannelEmployeeSalarySettlementIntervalID, //結算周期ID
          JobID: that.searchData.JobID, //职务ID
          EntityID: that.searchData.EntityID, //门店ID
          IsHideZero: that.searchData.IsHideZero, //是否显示有提成
        };
        let res = await API.channelEmployeeSalarySheet_excel(params);
        this.$message.success({
          message: "正在导出",
          duration: "4000",
        });
        const link = document.createElement("a");
        let blob = new Blob([res], { type: "application/octet-stream" });
        link.style.display = "none";
        link.href = URL.createObjectURL(blob);
        link.download = "市场业绩提成结算.xlsx"; //下载的文件名
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        that.downloadLoading = false;
      } catch (error) {
        that.$message.error(error);
        that.downloadLoading = false;
      }
    },
    /**  个人提成详情  */
    async channelEmployeeSalarySheet_employeeCommission(row) {
      let that = this;
      try {
        let params = {
          ChannelEmployeeSalarySettlementIntervalID: row.ChannelEmployeeSalarySettlementIntervalID, //计算周期ID
          EmployeeID: row.EmployeeID, //员工编号
        };
        let res = await API.channelEmployeeSalarySheet_employeeCommission(params);
        if (res.StateCode == 200) {
          that.employeeCommission = res.Data;
        } else {
          that.$message.error(res.Message);
        }
      } catch (error) {
        that.$message.error(error);
      }
    },
    /**  团队提成详情  */
    async channelEmployeeSalarySheet_teamCommission(row) {
      let that = this;
      try {
        let params = {
          ChannelEmployeeSalarySettlementIntervalID: row.ChannelEmployeeSalarySettlementIntervalID, //计算周期ID
          EmployeeID: row.EmployeeID, //员工编号
        };
        let res = await API.channelEmployeeSalarySheet_teamCommission(params);
        if (res.StateCode == 200) {
          that.teamCommission = res.Data;
        } else {
          that.$message.error(res.Message);
        }
      } catch (error) {
        that.$message.error(error);
      }
    },
    /**  结算周期设置  */
    async channelEmployeeSalarySheet_createSettlementInterval() {
      let that = this;
      try {
        let params = {
          SettlementMonth: that.createSettlementInterval.SettlementMonth, //结算月份
          StartDate: that.createSettlementInterval.QueryDate[0], //开始时间
          EndDate: that.createSettlementInterval.QueryDate[1], //结束时间
        };
        let res = await API.channelEmployeeSalarySheet_createSettlementInterval(params);
        if (res.StateCode == 200) {
          that.$message.success("保存成功");
          that.settlementPeriodDialogVisible = false;
          that.channelEmployeeSalarySheet_allSettlementInterval();
        } else {
          that.$message.error(res.Message);
        }
      } catch (error) {
        that.$message.error(error);
      }
    },
    /**  结算周期查询  */
    async channelEmployeeSalarySheet_allSettlementInterval() {
      let that = this;
      try {
        let params = {};
        let res = await API.channelEmployeeSalarySheet_allSettlementInterval(params);
        if (res.StateCode == 200) {
          that.allSettlementInterval = res.Data;
        } else {
          that.$message.error(res.Message);
        }
      } catch (error) {
        that.$message.error(error);
      }
    },
    /** 结算   */
    async channelEmployeeSalarySheet_settlement() {
      let that = this;
      try {
        let params = {
          ChannelEmployeeSalarySettlementIntervalID: that.settlementPerformanceData.ChannelEmployeeSalarySettlementIntervalID,
        };
        let res = await API.channelEmployeeSalarySheet_settlement(params);
        if (res.StateCode == 200) {
          that.$message.success("操作成功");
          that.settlementPerformanceDialogVisible = false;
          that.channelEmployeeSalarySheet_all();
        } else {
          that.$message.error(res.Message);
        }
      } catch (error) {
        that.$message.error(error);
      }
    },

    /* 初始化-职务 */
    jobTypeData: function () {
      var that = this;
      var params = {
        JobTypeName: "",
      };
      APIJob.getJobJobtypeAll(params).then((res) => {
        if (res.StateCode == 200) {
          that.jobTypeList = res.Data;
        } else {
          that.$message.error({
            message: res.Message,
            duration: 2000,
          });
        }
      });
    },
    /* 初始化-组织 */
    getEntityAllEntity() {
      var that = this;
      var params = {
        SearchKey: "",
      };
      APIEntity.getEntity(params).then((res) => {
        if (res.StateCode == 200) {
          this.EntityAllEntity = res.Data;
        } else {
          that.$message.error({
            message: res.Message,
            duration: 2000,
          });
        }
      });
    },
  },
  /** 创建实例之前执行函数   */
  beforeCreate() { },
  /**  实例创建完成之后  */
  created() { },
  /**  在挂载开始之前被调用  */
  beforeMount() { },
  /**  实例被挂载后调用  */
  mounted() {
    this.channelEmployeeSalarySheet_all();
    this.channelEmployeeSalarySheet_allSettlementInterval();
    this.jobTypeData();
    this.getEntityAllEntity();
  },
  /**  数据更新 之前 调用  */
  beforeUpdate() { },
  /** 数据更新 完成 调用   */
  updated() { },
  /**  实例销毁之前调用  */
  beforeDestroy() { },
  /**  实例销毁后调用  */
  destroyed() { },
};
</script>

<style lang="scss">
.ChannelEmployeeSalarySettlement {
  .vue-treeselect__control {
    width: 250px;
    margin-right: 10px;
    height: 32px;
  }

  .vue-treeselect__menu-container {
    width: 250px;
  }
}
</style>
