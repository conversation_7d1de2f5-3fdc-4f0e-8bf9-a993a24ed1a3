<template>
  <div class="height_fill_available" style="min-width: 1200px">
    <el-row class="height_fill_available">
      <el-col :span="15" class="height_fill_available">
        <div class="map_img">
          <el-image style="width: 100%; height: 100%" :src="DefaultLogoImg.BrandLoginImage" fit="cover" lazy>
            <div slot="error">
              <img :src="DefaultLogoImg.BrandLoginImage" />
            </div>
          </el-image>
        </div>
      </el-col>

      <el-col :span="9" class="height_fill_available dis_flex flex_y_center flex_x_center" style="position: relative">
        <div class="login_information">
          <el-form ref="ruleForm" :model="ruleForm" :rules="rules">
            <div class="login_logo">
              <el-image :src="DefaultLogoImg.BrandLoginLogo">
                <div slot="error">
                  <img :src="DefaultLogoImg.BrandLoginLogo" />
                </div>
              </el-image>
            </div>
            <el-form-item prop="EnterpriseCode">
              <el-input v-model="ruleForm.EnterpriseCode" placeholder="商户号" @keyup.enter.native="handleLogin" v-show="false"> </el-input>
            </el-form-item>
            <el-form-item prop="Username">
              <el-input v-model="ruleForm.Username" placeholder="账号" @keyup.enter.native="handleLogin"></el-input>
            </el-form-item>
            <el-form-item prop="Password">
              <el-input type="password" v-model="ruleForm.Password" placeholder="密码" @keyup.enter.native="handleLogin"> </el-input>
            </el-form-item>
            <el-form-item class="padtp_15">
              <el-button type="primary" style="width: 100%" @click.native.prevent="handleLogin" :loading="loading" round>登 录</el-button>
            </el-form-item>
            <div v-if="!isChrome()" class="dis_flex flex_y_center flex_x_center">
              <img src="https://www.google.cn/chrome/static/images/chrome-logo.svg" style="width: 20px" class="vertical-align:middle" />
              <div class="font_13 marlt_5 color_999 dis_flex flex_y_center">
                <span style="margin-right: 3px">推荐使用谷歌浏览器</span>
                <el-link class="chromSty" type="primary" :underline="false" href="https://www.google.cn/intl/zh-CN/chrome/" target="_blank">(点击下载)</el-link>
              </div>
            </div>
          </el-form>
          <div class="dis_flex flex_dir_row flex_x_between" style="margin-top: 50px">
            <el-popover placement="top-start" width="200" trigger="hover">
              <el-image
                style="width: 200px; height: 200px"
                :src="require('@/assets/img/erpMini.png')"
                fit="cover"
              >
              </el-image>
              <el-button class="btn-miniprogram" size="small" icon="el-iconfont icon-mini-program-fill" slot="reference" round>微信小程序</el-button>
              <!-- <el-button class="btn-miniprogram" size="small" icon="el-icon-upload" slot="reference" round>微信小程序</el-button> -->
            </el-popover>
            <div style="margin-left: 25px" class="dis_flex flex_dir_row flex_y_center flex_x_between">
              <img src="@/assets/img/beianIcon.png" alt="" />
              <el-link class="font_13 color_999 marlt_5" type="info" :underline="false" href="https://beian.miit.gov.cn" target="_blank"
                >苏ICP备**********号-5</el-link
              >
            </div>
          </div>
        </div>
      </el-col>
    </el-row>
    <div class="copyright">Copyright © 南京三七美品牌管理有限公司</div>
  </div>
</template>

<script>
import API from "@/api/account";
import md5 from "js-md5";

export default {
  data() {
    return {
      loading: false,
      // savePasswordState: false,                          // 记住密码
      ruleForm: {
        EnterpriseCode: "sanqimei",
        Username: "",
        Password: "",
      },
      rules: {
        EnterpriseCode: [{ required: true, message: "请输入商户号", trigger: "blur" }],
        Username: [{ required: true, message: "请输入账号", trigger: "blur" }],
        Password: [{ required: true, message: "请输入密码", trigger: "blur" }],
      },
      DefaultLogoImg: {
        BrandLoginImage: require("@/assets/img/login_des.png"),
        BrandLogo: "https://mfl-saas-data.oss-cn-shanghai.aliyuncs.com/web/logo/logo.png",
        BrandLogoIcon: "https://mfl-saas-data.oss-cn-shanghai.aliyuncs.com/web/logo/logo2.png",
        BrandLoginLogo: require("@/assets/img/login_logo.png"),
      },
    };
  },
  methods: {
    // 登录
    handleLogin() {
      var that = this;
      that.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.loading = true;
          let para = Object.assign({}, that.ruleForm);
          para.Password = md5(para.Password);
          API.getlogin(para)
            .then(function (res) {
              if (res.StateCode == 200) {
                localStorage.setItem("access-user", JSON.stringify(res.Data));
                that.routerData();
                // if (that.savePasswordState) {
                //   localStorage.setItem("savePasswordState", 1);
                //   localStorage.setItem("password", md5(that.ruleForm.Password));
                localStorage.setItem("EnterpriseCode", that.ruleForm.EnterpriseCode);
                localStorage.setItem("BrandLogin", false);
                //   localStorage.setItem("Username", that.ruleForm.Username);
                // }else{
                //   if(localStorage.getItem('password') != undefined || localStorage.getItem('password') != null){
                //     localStorage.setItem("savePasswordState", 0);
                //     localStorage.removeItem("password");
                //     localStorage.removeItem("EnterpriseCode");
                //     localStorage.removeItem("Username");
                //   }
                // }
              } else {
                that.$message.error({
                  message: res.Message,
                  duration: 2000,
                });
              }
            })
            .finally(function () {
              that.loading = false;
            });
        }
      });
    },
    routerData: function () {
      var that = this;
      API.getPCPermissionRouter().then((res) => {
        if (res.StateCode == 200) {
          var routerchildren = [];
          res.Data.forEach(function (item) {
            var routerChild = {};
            routerChild.path = item.RouterPath;
            routerChild.component = () => import(`@/${item.RouterComponentPath}`);
            routerChild.name = item.RouterName;
            var routerMeta = {};
            routerMeta.title = item.RouterMeta.Title;
            routerMeta.ExternalLinks = item.RouterMeta.ExternalLinks;
            routerMeta.IsVerifyStore = item.RouterMeta.IsVerifyStore;
            routerMeta.IsVerifyWarehouse = item.RouterMeta.IsVerifyWarehouse;
            routerMeta.Permission = item.RouterMeta.Permission;
            routerChild.meta = routerMeta;
            routerchildren.push(routerChild);
          });
          var routers = [];
          var router = {};
          router.path = "/";
          router.name = "UserRouter";
          router.component = () => import("@/components/common/Master");
          router.children = routerchildren;
          routers.push(router);
          var routerNotFound = {};
          routerNotFound.path = "*";
          routerNotFound.redirect = "/";
          routers.push(routerNotFound);
          that.$router.$addRoutes(routers);
          that.$router.push("/");
        } else {
          that.$message.error({
            message: res.Message,
            duration: 2000,
          });
        }
      });
    },
    // 重置密码
    resetPassword: function () {
      // var that = this;
      // that.$router.push("resetPassword")
    },
    // 记住密码
    selectRememberPassword() {},

    /* 判断当前浏览器是否为chrome */
    isChrome() {
      const isChromium = window.chrome;
      const winNav = window.navigator;
      const vendorName = winNav.vendor;
      const isOpera = typeof window.opr !== "undefined";
      const isIEedge = winNav.userAgent.indexOf("Edge") > -1;
      const isIOSChrome = winNav.userAgent.match("CriOS");

      return (
        isIOSChrome || (isChromium !== null && typeof isChromium !== "undefined" && vendorName === "Google Inc." && isOpera === false && isIEedge === false)
      );
    },
  },
  mounted() {
    // var that = this;
    // if(Number(localStorage.getItem('savePasswordState')) == 1){
    //   that.ruleForm.Password = localStorage.getItem('password');
    //   that.ruleForm.EnterpriseCode = localStorage.getItem('EnterpriseCode');
    //   that.ruleForm.Username = localStorage.getItem('Username');
    // }
  },
};
</script>

<style scoped lang="scss">
.logo {
  padding: 15px;
}

.map_img {
  height: 100%;

  img {
    width: 100%;
    height: 100%;
  }
}

.login_information {
  padding: 25px;
  width: 420px;
  border-radius: 5px;

  .login_logo {
    margin-bottom: 5px;
    width: 210px;
    height: 60px;

    .el-image {
      width: 100%;
      height: 100%;

      img {
        width: 100%;
        height: 100%;
        object-fit: contain;
      }
    }
  }

  .forget_password {
    color: #666;
  }

  .btn-miniprogram {
    color: rgb(157, 165, 179);
    background-color: rgb(248, 249, 252);
    border: none;
  }
}

.copyright {
  font-size: 14px;
  color: rgb(20, 20, 20);
  position: absolute;
  bottom: 1%;
  left: 0;
  right: 0;
  max-width: 100%;
  text-align: center;
  opacity: 0.6;
}

.chromSty {
  font-size: 13px;
}
</style>