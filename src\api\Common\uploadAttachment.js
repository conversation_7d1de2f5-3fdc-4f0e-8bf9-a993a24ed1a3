/**
 * Created by preference on 2022/05/11
 *  zmx 
 */

import * as API from '@/api/index'
export default {
  /* 上传图片、文件 */
  addAttachment: params => {
      return API.POST('api/upload/addAttachment', params) 
  },
  // 上传文件 
  uploadFile_network: params => {
    return API.importFile('api/customerFile/uploadFile',params)
  },
  // 上传图片
  upload_uploadFile: params => {
    return API.importFile('api/upload/uploadFile',params)
  },
}