<!-- /**  消耗订单内容 */ -->
<template>
  <section class="treatBillDetailContent">
    <div>
      <!-- 订单信息 -->
      <div style="margin-right: 10px">
        <div class="tip" style="margin-top: 0">订单信息</div>
        <el-form label-width="100px" class="treatInfoClass" size="small">
          <el-row>
            <el-col :span="8">
              <el-form-item label="订单编号:">{{ treatInfo.ID }}</el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="订单类型:">{{ treatInfo.BillType == "10" ? "消耗单" : "消耗退单" }}</el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="订单状态:">{{ getBillState(treatInfo.BillStatus) }} </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="8">
              <el-form-item label="顾客信息:"
                >{{ treatInfo.Name }} <span v-if="treatInfo.PhoneNumber != null">({{ treatInfo.PhoneNumber | hidephone }})</span>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="录单人:">{{ treatInfo.EmployeeName }}</el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="消耗金额:">￥{{ treatInfo.Amount | toFixed | NumFormat }}</el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="8">
              <el-form-item label="下单时间:">
                <span class="marrt_5">{{ treatInfo.BillDate | dateFormat("YYYY-MM-DD HH:mm") }}</span>
                <el-button v-if="isModifyBillDate && !limitSealingAccount(treatInfo.BillDate, ModifyBillDateRestriction)" type="text" @click="ModifyBillDateClick">修改</el-button>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="录单时间:">{{ treatInfo.CreatedOn | dateFormat("YYYY-MM-DD HH:mm") }}</el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="订单备注:">
                <span v-if="treatInfo.Remark" class="marrt_5">{{ treatInfo.Remark }} </span>
                <el-button type="text" @click="upRemarkDialog"> 修改备注</el-button>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row v-if="treatInfo.BillStatus == '30'">
            <el-col :span="8">
              <el-form-item label="取消时间:">{{ treatInfo.CancelCreatedOn | dateFormat("YYYY-MM-DD HH:mm") }}</el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="取消人:">{{ treatInfo.CancelCreatedBy }}</el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="取消原因:">
                <span v-if="treatInfo.CancelRemark" class="marrt_5">{{ treatInfo.CancelRemark }}</span>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row> </el-row>
          <el-row> </el-row>
        </el-form>
      </div>
      <div class="martp_10">
        <div class="tip">消耗信息</div>
        <!-- 项目 -->
        <div v-if="treatInfo.Project != undefined && treatInfo.Project.length > 0">
          <el-row class="tipback_col pad_10">
            <el-col :span="6">项目</el-col>
            <el-col :span="4">数量</el-col>
            <el-col v-show="treatInfo.BillType == '10'" :span="6">优惠金额</el-col>
            <el-col :span="8">小计</el-col>
          </el-row>
          <el-row v-for="(item, index) in treatInfo.Project" :key="index + 'x1'" class="text_left border_right border_left">
            <el-col :span="24" class="pad_10 border_bottom">
              <el-col :span="6">
                <div>
                  {{ item.ProjectName }}
                  <span v-if="item.Alias">({{ item.Alias }})</span>
                  <el-tag v-if="item.IsLargess" size="mini" type="danger" class="marlt_10">赠</el-tag>
                  <span v-if="isRemark" class="marlt_5 color_primary cursor_pointer">
                    <el-button icon="el-icon-edit-outline" type="text" style="padding: unset" @click="changeRemarkClick(item, 'Project')"></el-button>
                  </span>
                </div>
                <div class="color_red martp_5 font_12">￥{{ item.Price | toFixed | NumFormat }}</div>
              </el-col>
              <el-col :span="4">x {{ item.Quantity }}</el-col>
              <el-col :span="6" v-show="treatInfo.BillType == '10'">
                <div>
                  <span v-if="item.CardPreferentialAmount < 0">+ ¥ {{ Math.abs(item.CardPreferentialAmount) | toFixed | NumFormat }}</span>
                  <span v-else-if="item.CardPreferentialAmount > 0">- ¥ {{ Math.abs(item.CardPreferentialAmount) | toFixed | NumFormat }}</span>
                  <span v-else>¥ 0.00</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div>¥ {{ item.TotalAmount | toFixed | NumFormat }}</div>
                <div class="dis_flex martp_5 font_12">
                  <div class="color_green font_12" v-if="item.PayAmount > 0">现金金额：¥ {{ item.PayAmount | toFixed | NumFormat }}</div>

                  <div class="color_green font_12" v-if="item.CardDeductionAmount > 0" :class="item.PayAmount != 0 ? 'marlt_15' : ''">卡抵扣金额：¥ {{ item.CardDeductionAmount | toFixed | NumFormat }}</div>

                  <div class="color_red font_12" v-if="item.LargessCardDeductionAmount > 0" :class="item.PayAmount != 0 || item.CardDeductionAmount != 0 ? 'marlt_15' : ''">赠送卡扣金额：¥ {{ item.LargessCardDeductionAmount | toFixed | NumFormat }}</div>

                  <div class="color_red font_12" v-if="item.LargessAmount > 0">赠送金额：¥ {{ item.LargessAmount | toFixed | NumFormat }}</div>
                </div>
              </el-col>
            </el-col>
            <el-col v-if="item.Remark" :span="24" class="pad_10 border_bottom font_12 color_333">备注信息：{{ item.Remark }}</el-col>
            <el-col :span="24" v-if="item.TreatBillHandler.length > 0" class="pad_10 padtp_5 padbm_5 border_bottom">
              <el-row v-for="(handler, pIndex) in item.TreatBillHandler" :key="pIndex + 'h5'">
                <el-col :span="2">
                  <el-form @submit.native.prevent class="saleHandler" :inline="true" size="mini" label-position="left">
                    <el-form-item :label="`${handler.TreatHandlerName}`"></el-form-item>
                  </el-form>
                </el-col>
                <el-col :span="22">
                  <el-form @submit.native.prevent class="saleHandler" :inline="true" size="mini">
                    <el-form-item v-for="(employee, handleIndex) in handler.Employee" :key="handleIndex" :label="`${employee.EmployeeName}:`">{{ employee.Scale.toFixed(2) }}% </el-form-item>
                  </el-form>
                </el-col>
              </el-row>
            </el-col>
          </el-row>
        </div>
        <!-- 储值卡 -->
        <div v-if="treatInfo.SavingCard != undefined && treatInfo.SavingCard.length > 0">
          <el-row class="tipback_col pad_10">
            <el-col :span="6">储值卡</el-col>
            <el-col :span="4">数量</el-col>
            <el-col :span="6" v-show="treatInfo.BillType == '10'">优惠金额</el-col>
            <el-col :span="8">小计</el-col>
          </el-row>
          <el-row v-for="(item, index) in treatInfo.SavingCard" :key="index + 'x4'" class="border_right item border_left text_left">
            <el-row style="background-color: #f5f7fa" class="pad_10 border_top border_bottom">
              <div>
                {{ item.SavingCardName }} <span v-if="item.Alias">({{ item.Alias }})</span>
              </div>
            </el-row>
            <el-row v-for="(childItem, childIndex) in item.Project" :key="childIndex + 'c3'">
              <el-col :span="24" class="pad_10 border_bottom">
                <el-col :span="24">
                  <el-col :span="6">
                    <div>
                      {{ childItem.ProjectName }} <span v-if="childItem.Alias">({{ childItem.Alias }})</span>
                      <el-tag v-if="item.IsLargess" size="mini" type="danger" class="marlt_10">赠</el-tag>
                      <span v-if="isRemark" class="marlt_5 color_primary cursor_pointer">
                        <el-button icon="el-icon-edit-outline" type="text" style="padding: unset" @click="changeRemarkClick(childItem, 'SavingCard')"></el-button>
                      </span>
                    </div>
                  </el-col>
                  <el-col :span="4">x {{ childItem.Quantity }}</el-col>
                  <el-col :span="6" v-show="treatInfo.BillType == '10'">
                    <span v-if="childItem.PricePreferentialAmount + childItem.CardPreferentialAmount < 0">+ ¥ {{ Math.abs(childItem.PricePreferentialAmount + childItem.CardPreferentialAmount) | toFixed | NumFormat }}</span>
                    <span v-else-if="childItem.PricePreferentialAmount + childItem.CardPreferentialAmount > 0">- ¥ {{ Math.abs(childItem.PricePreferentialAmount + childItem.CardPreferentialAmount) | toFixed | NumFormat }}</span>
                    <span v-else>¥ 0.00</span>
                  </el-col>
                  <el-col :span="8">¥ {{ childItem.TotalAmount | toFixed | NumFormat }}</el-col>
                </el-col>
                <el-col :span="24" class="martp_5">
                  <el-col :span="10">
                    <div class="color_red font_12">¥ {{ childItem.Price | toFixed | NumFormat }}</div>
                  </el-col>
                  <el-col :span="6">
                    <div class="dis_flex">
                      <div v-if="childItem.PricePreferentialAmount == 0 && childItem.CardPreferentialAmount <= 0" style="opacity: 0; widht: 10px; height: 10px"></div>
                      <span class="color_gray font_12" v-if="childItem.PricePreferentialAmount != 0">
                        手动改价：
                        <span class="color_red" v-if="childItem.PricePreferentialAmount > 0">- ¥ {{ childItem.PricePreferentialAmount | toFixed | NumFormat }}</span>
                        <span class="color_green" v-else>+ ¥ {{ Math.abs(childItem.PricePreferentialAmount) | toFixed | NumFormat }}</span>
                      </span>
                      <span class="color_gray font_12" :class="childItem.PricePreferentialAmount != 0 ? 'marlt_15' : ''" v-if="childItem.CardPreferentialAmount > 0">
                        卡优惠：
                        <span class="color_red">- ¥ {{ parseFloat(childItem.CardPreferentialAmount) | toFixed | NumFormat }}</span>
                      </span>

                      
                      <span class="color_gray font_12" :class="childItem.PricePreferentialAmount != 0 || childItem.CardPreferentialAmount != 0 ? 'marlt_15' : ''" v-if="childItem.MemberPreferentialAmount > 0">
                        会员优惠：
                        <span class="color_red">- ¥ {{ parseFloat(childItem.MemberPreferentialAmount) | toFixed | NumFormat }}</span>
                      </span>
                    </div>
                  </el-col>
                  <el-col :span="8">
                    <div class="dis_flex">
                      <div class="color_green font_12" v-if="childItem.CardDeductionAmount > 0">卡抵扣金额： {{ childItem.CardDeductionAmount | toFixed | NumFormat }}</div>

                      <div class="color_red font_12" v-if="childItem.LargessCardDeductionAmount > 0" :class="childItem.CardDeductionAmount != 0 ? 'marlt_15' : ''">赠送卡抵扣金额： {{ childItem.LargessCardDeductionAmount | toFixed | NumFormat }}</div>
                      

                    </div>
                  </el-col>
                </el-col>
              </el-col>
              <el-col v-if="childItem.Remark" :span="24" class="pad_10 border_bottom font_12 color_333">备注信息：{{ childItem.Remark }}</el-col>
              <el-col :span="24" v-if="childItem.TreatBillHandler.length > 0" class="pad_10 padtp_5 padbm_5 border_bottom">
                <el-row v-for="(handler, pIndex) in childItem.TreatBillHandler" :key="pIndex + 'h5'">
                  <el-col :span="2">
                    <el-form @submit.native.prevent class="saleHandler" :inline="true" size="mini" label-position="left">
                      <el-form-item :label="`${handler.TreatHandlerName}`"></el-form-item>
                    </el-form>
                  </el-col>
                  <el-col :span="22">
                    <el-form @submit.native.prevent class="saleHandler" :inline="true" size="mini">
                      <el-form-item v-for="(employee, handleIndex) in handler.Employee" :key="handleIndex" :label="`${employee.EmployeeName}:`">{{ employee.Scale.toFixed(2) }}% </el-form-item>
                    </el-form>
                  </el-col>
                </el-row>
              </el-col>
            </el-row>
          </el-row>
        </div>
        <!-- 时效卡 -->
        <div v-if="treatInfo.TimeCard != undefined && treatInfo.TimeCard.length > 0">
          <el-row class="tipback_col pad_10">
            <el-col :span="6">时效卡</el-col>
            <el-col :span="4">数量</el-col>
            <el-col :span="6" v-show="treatInfo.BillType == '10'">优惠金额</el-col>
            <el-col :span="8">小计</el-col>
          </el-row>
          <el-row v-for="(item, index) in treatInfo.TimeCard" :key="index + 'x4'" class="border_right item border_left">
            <el-row style="background-color: #f5f7fa" class="pad_10 border_top border_bottom">
              <div>
                {{ item.TimeCardName }} <span v-if="item.Alias">({{ item.Alias }})</span>
                <el-tag v-if="item.IsLargess" size="mini" type="danger" class="marlt_10">赠</el-tag>
              </div>
            </el-row>
            <el-row v-for="(childItem, childIndex) in item.Project" :key="childIndex + 'c3'">
              <el-col :span="24" class="pad_10 border_bottom">
                <el-col :span="6">
                  <div>
                    {{ childItem.ProjectName }}
                    <span v-if="childItem.Alias">({{ childItem.Alias }})</span>
                    <el-tag v-if="item.IsLargess" size="mini" type="danger" class="marlt_10">赠</el-tag>
                    <span v-if="isRemark" class="marlt_5 color_primary cursor_pointer">
                      <el-button icon="el-icon-edit-outline" type="text" style="padding: unset" @click="changeRemarkClick(childItem, 'TimeCard')"></el-button>
                    </span>
                  </div>
                  <div class="color_red martp_5 font_12">¥ {{ childItem.Price | toFixed | NumFormat }}</div>
                </el-col>
                <el-col :span="4">x {{ childItem.Quantity }}</el-col>
                <el-col :span="6" v-show="treatInfo.BillType == '10'">
                  <div>
                    <span v-if="childItem.CardPreferentialAmount < 0">+ ¥ {{ Math.abs(childItem.CardPreferentialAmount) | toFixed | NumFormat }}</span>
                    <span v-else-if="childItem.CardPreferentialAmount > 0">- ¥ {{ Math.abs(childItem.CardPreferentialAmount) | toFixed | NumFormat }}</span>
                    <span v-else>¥ 0.00</span>
                  </div>
                  <div class="martp_5 font_12">
                    <span class="color_gray font_12" v-if="childItem.CardPreferentialAmount != 0">
                      卡优惠
                      <span class="color_red" v-if="childItem.CardPreferentialAmount > 0">- ¥ {{ childItem.CardPreferentialAmount | toFixed | NumFormat }}</span>
                      <span class="color_green" v-else>+ ¥ {{ Math.abs(childItem.CardPreferentialAmount) | toFixed | NumFormat }}</span>
                    </span>
                  </div>
                </el-col>
                <el-col :span="8">
                  <div>¥ {{ childItem.TotalAmount | toFixed | NumFormat }}</div>

                  <div class="dis_flex martp_5">
                    <div class="color_green font_12" v-if="childItem.PayAmount > 0">现金金额： {{ childItem.PayAmount | toFixed | NumFormat }}</div>
                    <div class="color_green font_12" v-if="childItem.CardDeductionAmount > 0" :class="childItem.PayAmount != 0 ? 'marlt_15' : ''">卡抵扣金额： {{ childItem.CardDeductionAmount | toFixed | NumFormat }}</div>

                    <div class="color_red font_12" v-if="childItem.LargessCardDeductionAmount > 0" :class="item.PayAmount != 0 || childItem.CardDeductionAmount != 0 ? 'marlt_15' : ''">赠送卡抵扣金额： {{ childItem.LargessCardDeductionAmount | toFixed | NumFormat }}</div>

                    <div class="color_red font_12" v-if="childItem.LargessAmount > 0">赠送金额：¥ {{ childItem.LargessAmount | toFixed | NumFormat }}</div>
                  </div>
                </el-col>
              </el-col>
              <el-col v-if="childItem.Remark" :span="24" class="pad_10 border_bottom font_12 color_333">备注信息：{{ childItem.Remark }}</el-col>
              <el-col :span="24" v-if="childItem.TreatBillHandler.length > 0" class="pad_10 padtp_5 padbm_5 border_bottom">
                <el-row v-for="(handler, pIndex) in childItem.TreatBillHandler" :key="pIndex + 'h5'">
                  <el-col :span="2">
                    <el-form @submit.native.prevent class="saleHandler" :inline="true" size="mini" label-position="left">
                      <el-form-item :label="`${handler.TreatHandlerName}`"></el-form-item>
                    </el-form>
                  </el-col>
                  <el-col :span="22">
                    <el-form @submit.native.prevent class="saleHandler" :inline="true" size="mini">
                      <el-form-item v-for="(employee, handleIndex) in handler.Employee" :key="handleIndex" :label="`${employee.EmployeeName}:`">{{ employee.Scale.toFixed(2) }}% </el-form-item>
                    </el-form>
                  </el-col>
                </el-row>
              </el-col>
            </el-row>
          </el-row>
        </div>
        <!-- 通用次卡 -->
        <div v-if="treatInfo.GeneralCard != undefined && treatInfo.GeneralCard.length > 0">
          <el-row class="tipback_col pad_10">
            <el-col :span="6">通用次卡</el-col>
            <el-col :span="4">数量</el-col>
            <el-col :span="6" v-show="treatInfo.BillType == '10'">优惠金额</el-col>
            <el-col :span="8">小计</el-col>
          </el-row>
          <el-row v-for="(item, index) in treatInfo.GeneralCard" :key="index + 'x3'" class="border_right item border_left">
            <el-row style="background-color: #f5f7fa" class="pad_10 border_top border_bottom">
              <div>
                {{ item.GeneralCardName }}<span v-if="item.Alias">({{ item.Alias }})</span>
                <el-tag v-if="item.IsLargess" size="mini" type="danger" class="marlt_10">赠 </el-tag>
              </div>
            </el-row>

            <el-row v-for="(childItem, childIndex) in item.Project" :key="childIndex + 'c3'">
              <el-col :span="24" class="pad_10 border_bottom">
                <el-col :span="6">
                  <div>
                    {{ childItem.ProjectName }}
                    <span v-if="childItem.Alias">({{ childItem.Alias }})</span>
                    <el-tag v-if="item.IsLargess" size="mini" type="danger" class="marlt_10">赠 </el-tag>
                    <span v-if="isRemark" class="marlt_5 color_primary cursor_pointer">
                      <el-button icon="el-icon-edit-outline" type="text" style="padding: unset" @click="changeRemarkClick(childItem, 'GeneralCard')"></el-button>
                    </span>
                  </div>
                  <div class="color_red martp_5 font_12">￥{{ childItem.Price | toFixed | NumFormat }}</div>
                </el-col>
                <el-col :span="4">x {{ childItem.Quantity }}</el-col>
                <el-col :span="6" v-show="treatInfo.BillType == '10'">
                  <div>
                    <span v-if="childItem.CardPreferentialAmount < 0">+ ¥ {{ Math.abs(childItem.CardPreferentialAmount) | toFixed | NumFormat }}</span>
                    <span v-else-if="childItem.CardPreferentialAmount > 0">- ¥ {{ Math.abs(childItem.CardPreferentialAmount) | toFixed | NumFormat }}</span>
                    <span v-else>¥ 0.00</span>
                  </div>
                  <div class="martp_5 font_12">
                    <span class="color_gray font_12" v-if="childItem.CardPreferentialAmount != 0">
                      卡优惠
                      <span class="color_red" v-if="childItem.CardPreferentialAmount > 0">- ¥ {{ childItem.CardPreferentialAmount | toFixed | NumFormat }}</span>
                      <span class="color_green" v-else>+ ¥ {{ Math.abs(childItem.CardPreferentialAmount) | toFixed | NumFormat }}</span>
                    </span>
                  </div>
                </el-col>
                <el-col :span="8">
                  <div>
                    <div>¥ {{ childItem.TotalAmount | toFixed | NumFormat }}</div>

                    <div class="dis_flex font_12 martp_5">
                      <div class="color_green font_12" v-if="childItem.PayAmount > 0">现金金额： {{ childItem.PayAmount | toFixed | NumFormat }}</div>
                      <div class="color_green font_12" v-if="childItem.CardDeductionAmount > 0" :class="childItem.PayAmount != 0 ? 'marlt_15' : ''">卡抵扣金额： {{ childItem.CardDeductionAmount | toFixed | NumFormat }}</div>

                      <div class="color_red font_12" v-if="childItem.LargessCardDeductionAmount > 0" :class="item.PayAmount != 0 || childItem.CardDeductionAmount != 0 ? 'marlt_15' : ''">赠送卡抵扣金额： {{ childItem.LargessCardDeductionAmount | toFixed | NumFormat }}</div>

                      <div class="color_red font_12" v-if="childItem.LargessAmount > 0">赠送金额：¥ {{ childItem.LargessAmount | toFixed | NumFormat }}</div>
                    </div>
                  </div>
                </el-col>
              </el-col>
              <el-col v-if="childItem.Remark" :span="24" class="pad_10 border_bottom font_12 color_333">备注信息：{{ childItem.Remark }}</el-col>
              <el-col :span="24" v-if="childItem.TreatBillHandler.length > 0" class="pad_10 padtp_5 padbm_5 border_bottom">
                <el-row v-for="(handler, pIndex) in childItem.TreatBillHandler" :key="pIndex + 'h5'">
                  <el-col :span="2">
                    <el-form @submit.native.prevent class="saleHandler" :inline="true" size="mini" label-position="left">
                      <el-form-item :label="`${handler.TreatHandlerName}`"></el-form-item>
                    </el-form>
                  </el-col>
                  <el-col :span="22">
                    <el-form @submit.native.prevent class="saleHandler" :inline="true" size="mini">
                      <el-form-item v-for="(employee, handleIndex) in handler.Employee" :key="handleIndex" :label="`${employee.EmployeeName}:`">{{ employee.Scale.toFixed(2) }}% </el-form-item>
                    </el-form>
                  </el-col>
                </el-row>
              </el-col>
            </el-row>
          </el-row>
        </div>
        <!-- 产品 -->
        <div v-if="treatInfo.Product != undefined && treatInfo.Product.length > 0">
          <el-row class="tipback_col pad_10">
            <el-col :span="6">产品</el-col>
            <el-col :span="4">数量</el-col>
            <el-col :span="6" v-show="treatInfo.BillType == '10'">优惠金额</el-col>
            <el-col :span="8">小计</el-col>
          </el-row>
          <el-row v-for="(item, index) in treatInfo.Product" :key="index + 'x1'" class="text_left border_right border_left">
            <el-col :span="24" class="pad_10 border_bottom">
              <el-col :span="6">
                <div>
                  {{ item.ProductName }}<span v-if="item.Alias">({{ item.Alias }})</span>
                  <el-tag v-if="item.IsLargess" size="mini" type="danger" class="marlt_10">赠 </el-tag>
                  <span v-if="isRemark" class="marlt_5 color_primary cursor_pointer">
                    <el-button icon="el-icon-edit-outline" type="text" style="padding: unset" @click="changeRemarkClick(item, 'Product')"></el-button>
                  </span>
                </div>
                <div class="color_red martp_5 font_12">￥{{ item.Price }}</div>
              </el-col>
              <el-col :span="4">x {{ item.Quantity }}</el-col>
              <el-col :span="6" v-show="treatInfo.BillType == '10'">
                <div>
                  <span v-if="item.CardPreferentialAmount < 0">+ ¥ {{ Math.abs(item.CardPreferentialAmount) | toFixed | NumFormat }}</span>
                  <span v-else-if="item.CardPreferentialAmount > 0">- ¥ {{ Math.abs(item.CardPreferentialAmount) | toFixed | NumFormat }}</span>
                  <span v-else>¥ 0.00</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div>¥ {{ item.TotalAmount | toFixed | NumFormat }}</div>
                <div class="dis_flex font_12 martp_5">
                  <div class="color_green font_12" v-if="item.PayAmount > 0">现金金额： {{ item.PayAmount | toFixed | NumFormat }}</div>
                  <div class="color_green font_12" v-if="item.CardDeductionAmount > 0" :class="item.PayAmount != 0 ? 'marlt_15' : ''">卡抵扣金额： {{ item.CardDeductionAmount | toFixed | NumFormat }}</div>

                  <div class="color_red font_12" v-if="item.LargessCardDeductionAmount > 0" :class="item.PayAmount != 0 || item.CardDeductionAmount != 0 ? 'marlt_15' : ''">赠送卡抵扣金额： {{ item.LargessCardDeductionAmount | toFixed | NumFormat }}</div>

                  <div class="color_red font_12" v-if="item.LargessAmount > 0">赠送金额：¥ {{ item.LargessAmount | toFixed | NumFormat }}</div>
                </div>
              </el-col>
            </el-col>
            <el-col v-if="item.Remark" :span="24" class="pad_10 border_bottom font_12 color_333">备注信息：{{ item.Remark }}</el-col>
            <el-col :span="24" v-if="item.TreatBillHandler.length > 0" class="pad_10 padtp_5 padbm_5 border_bottom">
              <el-row v-for="(handler, pIndex) in item.TreatBillHandler" :key="pIndex + 'h5'">
                <el-col :span="2">
                  <el-form @submit.native.prevent class="saleHandler" :inline="true" size="mini" label-position="left">
                    <el-form-item :label="`${handler.TreatHandlerName}`"></el-form-item>
                  </el-form>
                </el-col>
                <el-col :span="22">
                  <el-form @submit.native.prevent class="saleHandler" :inline="true" size="mini">
                    <el-form-item v-for="(employee, handleIndex) in handler.Employee" :key="handleIndex" :label="`${employee.EmployeeName}:`">{{ employee.Scale.toFixed(2) }}% </el-form-item>
                  </el-form>
                </el-col>
              </el-row>
            </el-col>
          </el-row>
        </div>
        <!-- 套餐卡 -->
        <div v-if="treatInfo.PackageCard != undefined && treatInfo.PackageCard.length > 0">
          <el-row class="tipback_col pad_10">
            <el-col :span="6">套餐卡</el-col>
            <el-col :span="4">数量</el-col>
            <el-col :span="6" v-show="treatInfo.BillType == '10'">优惠金额</el-col>
            <el-col :span="8">小计</el-col>
          </el-row>

          <el-row v-for="(item, index) in treatInfo.PackageCard" :key="index + 'x6'" class="text_left border_left border_right">
            <el-row style="background-color: #f5f7fa" class="pad_10 border_top border_bottom">
              {{ item.PackageCardName }}<span v-if="item.Alias">({{ item.Alias }})</span>
            </el-row>
            <!-- 项目 -->
            <el-row v-for="(childItem, childIndex) in item.Project" :key="childIndex + 'c6-1'">
              <el-col :span="24" class="border_bottom pad_10">
                <el-col :span="6">
                  <div>
                    {{ childItem.ProjectName }}<span v-if="childItem.Alias">({{ childItem.Alias }})</span>
                    <el-tag v-if="childItem.IsLargess" size="mini" type="danger" class="marlt_5">赠 </el-tag>
                    <el-tag size="mini marlt_5"> 项目 </el-tag>
                    <span v-if="isRemark" class="marlt_5 color_primary cursor_pointer">
                      <el-button icon="el-icon-edit-outline" type="text" style="padding: unset" @click="changeRemarkClick(childItem, 'PackageCardProject')"></el-button>
                    </span>
                  </div>
                  <div class="martp_5 color_red font_12">￥{{ childItem.Price | toFixed | NumFormat }}</div>
                </el-col>
                <el-col :span="4">x {{ childItem.Quantity }}</el-col>
                <el-col :span="6" v-show="treatInfo.BillType == '10'">
                  <div>
                    <span v-if="childItem.CardPreferentialAmount < 0">+ ¥ {{ Math.abs(childItem.CardPreferentialAmount) | toFixed | NumFormat }}</span>
                    <span v-else-if="childItem.CardPreferentialAmount > 0">- ¥ {{ Math.abs(childItem.CardPreferentialAmount) | toFixed | NumFormat }}</span>
                    <span v-else>¥ 0.00</span>
                  </div>
                </el-col>
                <el-col :span="8">
                  <div>¥ {{ childItem.TotalAmount | toFixed | NumFormat }}</div>
                  <div class="dis_flex martp_5">
                    <div class="color_green font_12" v-if="childItem.PayAmount > 0">现金金额： {{ childItem.PayAmount | toFixed | NumFormat }}</div>
                    <div class="color_green font_12" v-if="childItem.CardDeductionAmount > 0" :class="childItem.PayAmount != 0 ? 'marlt_15' : ''">卡抵扣金额： {{ childItem.CardDeductionAmount | toFixed | NumFormat }}</div>

                    <div class="color_red font_12" v-if="childItem.LargessCardDeductionAmount > 0" :class="childItem.CardDeductionAmount != 0 ? 'marlt_15' : ''">赠送卡抵扣金额： {{ childItem.LargessCardDeductionAmount | toFixed | NumFormat }}</div>

                    <div class="color_red font_12" v-if="childItem.LargessAmount > 0" :class="childItem.LargessCardDeductionAmount != 0 ? 'marlt_15' : ''">赠送金额：¥ {{ childItem.LargessAmount | toFixed | NumFormat }}</div>
                  </div>
                </el-col>
              </el-col>
              <el-col v-if="childItem.Remark" :span="24" class="pad_10 border_bottom font_12 color_333">备注信息：{{ childItem.Remark }}</el-col>
              <el-col :span="24" v-if="childItem.TreatBillHandler.length > 0" class="pad_10 padtp_5 padbm_5 border_bottom">
                <el-row v-for="(handler, pIndex) in childItem.TreatBillHandler" :key="pIndex + 'h5'">
                  <el-col :span="2">
                    <el-form class="saleHandler" :inline="true" size="mini" label-position="left" @submit.native.prevent>
                      <el-form-item :label="`${handler.TreatHandlerName}`"></el-form-item>
                    </el-form>
                  </el-col>
                  <el-col :span="22">
                    <el-form @submit.native.prevent class="saleHandler" :inline="true" size="mini">
                      <el-form-item v-for="(employee, handleIndex) in handler.Employee" :key="handleIndex" :label="`${employee.EmployeeName}:`">{{ employee.Scale.toFixed(2) }}% </el-form-item>
                    </el-form>
                  </el-col>
                </el-row>
              </el-col>
            </el-row>
            <!--  储值卡 -->
            <div class="border_right border_left" v-if="item.SavingCard.length > 0">
              <el-row v-for="(childItem, childIndex) in item.SavingCard" :key="childIndex + 'c2-3'">
                <el-row :span="24" style="background-color: #f5f7fa" class="pad_10 border_top border_bottom">
                  <div>
                    {{ childItem.SavingCardName }}<span v-if="childItem.Alias">({{ childItem.Alias }})</span>
                    <el-tag size="mini" class="marlt_5">储值卡</el-tag>
                  </div>
                </el-row>
                <el-row v-for="(childItem_1, childIndex_1) in childItem.Project" :key="childIndex_1 + 'c6-3-1'" class="item">
                  <el-col :span="24" class="border_bottom pad_10">
                    <el-col :span="6">
                      <div>
                        {{ childItem_1.ProjectName }}<span v-if="childItem_1.Alias">({{ childItem_1.Alias }})</span>
                        <span v-if="isRemark" class="marlt_5 color_primary cursor_pointer">
                          <el-button icon="el-icon-edit-outline" type="text" style="padding: unset" @click="changeRemarkClick(childItem_1, 'PackageCardSavingCard')"></el-button>
                        </span>
                      </div>
                      <div class="color_red martp_5 font_12">￥{{ childItem_1.Price | toFixed | NumFormat }}</div>
                    </el-col>
                    <el-col :span="4">x {{ childItem_1.Quantity }} </el-col>
                    <el-col :span="6" v-show="treatInfo.BillType == '10'">
                      <div>
                        <span v-if="childItem_1.PricePreferentialAmount + childItem_1.CardPreferentialAmount < 0">+ ¥ {{ Math.abs(childItem_1.PricePreferentialAmount + childItem_1.CardPreferentialAmount) | toFixed | NumFormat }}</span>
                        <span v-else-if="childItem_1.PricePreferentialAmount + childItem_1.CardPreferentialAmount > 0">- ¥ {{ Math.abs(childItem_1.PricePreferentialAmount + childItem_1.CardPreferentialAmount) | toFixed | NumFormat }}</span>
                        <span v-else>¥ 0.00</span>
                      </div>
                      <div class="martp_5">
                        <span class="color_gray font_12" v-if="childItem_1.PricePreferentialAmount != 0">
                          手动改价：
                          <span class="color_red" v-if="childItem_1.PricePreferentialAmount > 0">- ¥ {{ childItem_1.PricePreferentialAmount | toFixed | NumFormat }}</span>
                          <span class="color_green" v-else>+ ¥ {{ Math.abs(childItem_1.PricePreferentialAmount) | toFixed | NumFormat }}</span>
                        </span>
                        <span class="color_gray font_12" :class="childItem_1.PricePreferentialAmount != 0 ? 'marlt_15' : ''" v-if="childItem_1.CardPreferentialAmount > 0">
                          卡优惠：
                          <span class="color_red">- ¥ {{ parseFloat(childItem_1.CardPreferentialAmount) | toFixed | NumFormat }}</span>
                        </span>

                        <span class="color_gray font_12" :class="childItem_1.PricePreferentialAmount != 0 || childItem_1.CardPreferentialAmount != 0 ? 'marlt_15' : ''" v-if="childItem_1.MemberPreferentialAmount > 0">
                          会员优惠：
                          <span class="color_red">- ¥ {{ parseFloat(childItem_1.MemberPreferentialAmount) | toFixed | NumFormat }}</span>
                        </span>
                      </div>
                    </el-col>
                    <el-col :span="8">
                      <div>¥ {{ childItem_1.TotalAmount | toFixed | NumFormat }}</div>
                      <div class="dis_flex martp_5">
                        <div class="color_green font_12" v-if="childItem_1.PayAmount > 0">现金金额： {{ childItem_1.PayAmount | toFixed | NumFormat }}</div>
                        <div class="color_green font_12" v-if="childItem_1.CardDeductionAmount > 0" :class="childItem_1.PayAmount != 0 ? 'marlt_15' : ''">卡抵扣金额： {{ childItem_1.CardDeductionAmount | toFixed | NumFormat }}</div>

                        <div class="color_red font_12" v-if="childItem_1.LargessCardDeductionAmount > 0" :class="childItem_1.CardDeductionAmount != 0 ? 'marlt_15' : ''">赠送卡抵扣金额： {{ childItem_1.LargessCardDeductionAmount | toFixed | NumFormat }}</div>

                        <div class="color_red font_12" v-if="childItem_1.LargessAmount > 0" :class="childItem_1.LargessCardDeductionAmount != 0 ? 'marlt_15' : ''">赠送金额：¥ {{ childItem_1.LargessAmount | toFixed | NumFormat }}</div>
                      </div>
                    </el-col>
                  </el-col>
                  <el-col v-if="childItem_1.Remark" :span="24" class="pad_10 border_bottom font_12 color_333">备注信息：{{ childItem_1.Remark }}</el-col>
                  <el-col :span="24" v-if="childItem_1.TreatBillHandler.length > 0" class="pad_10 padtp_5 padbm_5 border_bottom">
                    <el-row v-for="(handler, pIndex) in childItem_1.TreatBillHandler" :key="pIndex + 'h5'">
                      <el-col :span="2">
                        <el-form @submit.native.prevent class="saleHandler" :inline="true" size="mini" label-position="left">
                          <el-form-item :label="`${handler.TreatHandlerName}`"></el-form-item>
                        </el-form>
                      </el-col>
                      <el-col :span="22">
                        <el-form class="saleHandler" :inline="true" size="mini">
                          <el-form-item @submit.native.prevent v-for="(employee, handleIndex) in handler.Employee" :key="handleIndex" :label="`${employee.EmployeeName}:`">{{ employee.Scale.toFixed(2) }}% </el-form-item>
                        </el-form>
                      </el-col>
                    </el-row>
                  </el-col>
                </el-row>
              </el-row>
            </div>
            <!-- 时效卡 -->
            <div class="border_right border_left" v-if="item.TimeCard.length > 0">
              <el-row v-for="(childItem, childIndex) in item.TimeCard" :key="childIndex + 'c1-3'">
                <el-row :span="24" style="background-color: #f5f7fa" class="pad_10 border_top border_bottom">
                  <div>
                    {{ childItem.TimeCardName }}<span v-if="childItem.Alias">({{ childItem.Alias }})</span>
                    <el-tag v-if="childItem.IsLargess" size="mini" type="danger" class="marlt_5">赠</el-tag>
                    <el-tag class="marlt_5" size="mini">时效卡</el-tag>
                  </div>
                </el-row>
                <el-row v-for="(childItem_1, childIndex_1) in childItem.Project" :key="childIndex_1 + 'c6-3-1'" class="item">
                  <el-col :span="24" class="border_bottom pad_10">
                    <el-col :span="6">
                      <div>
                        {{ childItem_1.ProjectName }}<span v-if="childItem_1.Alias">({{ childItem_1.Alias }})</span>
                        <el-tag v-if="childItem.IsLargess" size="mini" type="danger" class="marlt_5"> 赠 </el-tag>
                        <span v-if="isRemark" class="marlt_5 color_primary cursor_pointer">
                          <el-button icon="el-icon-edit-outline" type="text" style="padding: unset" @click="changeRemarkClick(childItem_1, 'PackageCardTimeCard')"></el-button>
                        </span>
                      </div>
                      <div class="color_red martp_5 font_12">￥{{ childItem_1.Price | toFixed | NumFormat }}</div>
                    </el-col>
                    <el-col :span="4">x {{ childItem_1.Quantity }} </el-col>
                    <el-col :span="6" v-show="treatInfo.BillType == '10'">
                      <div>
                        <span v-if="childItem_1.CardPreferentialAmount < 0">+ ¥ {{ Math.abs(childItem_1.CardPreferentialAmount) | toFixed | NumFormat }}</span>
                        <span v-else-if="childItem_1.CardPreferentialAmount > 0">- ¥ {{ Math.abs(childItem_1.CardPreferentialAmount) | toFixed | NumFormat }}</span>
                        <span v-else>¥ 0.00</span>
                      </div>
                      <div class="martp_5 font_12">
                        <span class="color_gray font_12" v-if="childItem_1.CardPreferentialAmount != 0">
                          卡优惠
                          <span class="color_red" v-if="childItem_1.CardPreferentialAmount > 0">- ¥ {{ childItem_1.CardPreferentialAmount | toFixed | NumFormat }}</span>
                          <span class="color_green" v-else>+ ¥ {{ Math.abs(childItem_1.CardPreferentialAmount) | toFixed | NumFormat }}</span>
                        </span>
                      </div>
                    </el-col>
                    <el-col :span="8">
                      <div>
                        <div>¥ {{ childItem_1.TotalAmount | toFixed | NumFormat }}</div>
                        <div class="dis_flex martp_5">
                          <div class="color_green font_12" v-if="childItem_1.PayAmount > 0">现金金额： {{ childItem_1.PayAmount | toFixed | NumFormat }}</div>
                          <div class="color_green font_12" v-if="childItem_1.CardDeductionAmount > 0" :class="childItem_1.PayAmount != 0 ? 'marlt_15' : ''">卡抵扣金额： {{ childItem_1.CardDeductionAmount | toFixed | NumFormat }}</div>

                          <div class="color_red font_12" v-if="childItem_1.LargessCardDeductionAmount > 0" :class="childItem_1.CardDeductionAmount != 0 ? 'marlt_15' : ''">赠送卡抵扣金额： {{ childItem_1.LargessCardDeductionAmount | toFixed | NumFormat }}</div>

                          <div class="color_red font_12" v-if="childItem_1.LargessAmount > 0" :class="childItem_1.LargessCardDeductionAmount != 0 ? 'marlt_15' : ''">赠送金额：¥ {{ childItem_1.LargessAmount | toFixed | NumFormat }}</div>
                        </div>
                      </div>
                    </el-col>
                  </el-col>
                  <el-col v-if="childItem_1.Remark" :span="24" class="pad_10 border_bottom font_12 color_333">备注信息：{{ childItem_1.Remark }}</el-col>
                  <el-col :span="24" v-if="childItem_1.TreatBillHandler.length > 0" class="pad_10 padtp_5 padbm_5 border_bottom">
                    <el-row v-for="(handler, pIndex) in childItem_1.TreatBillHandler" :key="pIndex + 'h5'">
                      <el-col :span="2">
                        <el-form @submit.native.prevent class="saleHandler" :inline="true" size="mini" label-position="left">
                          <el-form-item :label="`${handler.TreatHandlerName}`"></el-form-item>
                        </el-form>
                      </el-col>
                      <el-col :span="22">
                        <el-form @submit.native.prevent class="saleHandler" :inline="true" size="mini">
                          <el-form-item v-for="(employee, handleIndex) in handler.Employee" :key="handleIndex" :label="`${employee.EmployeeName}:`">{{ employee.Scale.toFixed(2) }}% </el-form-item>
                        </el-form>
                      </el-col>
                    </el-row>
                  </el-col>
                </el-row>
              </el-row>
            </div>
            <!-- 通用次卡 -->
            <div class="border_right border_left" v-if="item.GeneralCard.length > 0">
              <el-row v-for="(childItem, childIndex) in item.GeneralCard" :key="childIndex + 'c6-3'" class="item">
                <el-row :span="24" style="background-color: #f5f7fa" class="pad_10 border_top border_bottom">
                  <div>
                    {{ childItem.GeneralCardName }}<span v-if="childItem.Alias">({{ childItem.Alias }})</span>
                    <el-tag v-if="childItem.IsLargess" size="mini" type="danger" class="marlt_5"> 赠</el-tag>
                    <el-tag class="marlt_5" size="mini">通用次卡</el-tag>
                  </div>
                </el-row>
                <el-row v-for="(childItem_1, childIndex_1) in childItem.Project" :key="childIndex_1 + 'c6-3-1'">
                  <el-col :span="24" class="border_bottom pad_10">
                    <el-col :span="6">
                      <div>
                        {{ childItem_1.ProjectName }}<span v-if="childItem_1.Alias">({{ childItem_1.Alias }})</span>
                        <el-tag v-if="childItem.IsLargess" size="mini" type="danger" class="marlt_5"> 赠 </el-tag>
                        <span v-if="isRemark" class="marlt_5 color_primary cursor_pointer">
                          <el-button icon="el-icon-edit-outline" type="text" style="padding: unset" @click="changeRemarkClick(childItem_1, 'PackageCardGeneralCard')"></el-button>
                        </span>
                      </div>
                      <div class="color_red martp_5 font_12">￥{{ childItem_1.Price | toFixed | NumFormat }}</div>
                    </el-col>
                    <el-col :span="4">x {{ childItem_1.Quantity }} </el-col>
                    <el-col :span="6" v-show="treatInfo.BillType == '10'">
                      <div>
                        <span v-if="childItem_1.CardPreferentialAmount < 0">+ ¥ {{ Math.abs(childItem_1.CardPreferentialAmount) | toFixed | NumFormat }}</span>
                        <span v-else-if="childItem_1.CardPreferentialAmount > 0">- ¥ {{ Math.abs(childItem_1.CardPreferentialAmount) | toFixed | NumFormat }}</span>
                        <span v-else>¥ 0.00</span>
                      </div>
                      <div class="martp_5 font_12">
                        <span class="color_gray font_12" v-if="childItem_1.CardPreferentialAmount != 0">
                          卡优惠
                          <span class="color_red" v-if="childItem_1.CardPreferentialAmount > 0">- ¥ {{ childItem_1.CardPreferentialAmount | toFixed | NumFormat }}</span>
                          <span class="color_green" v-else>+ ¥ {{ Math.abs(childItem_1.CardPreferentialAmount) | toFixed | NumFormat }}</span>
                        </span>
                      </div>
                    </el-col>
                    <el-col :span="8">
                      <div>¥ {{ childItem_1.TotalAmount | toFixed | NumFormat }}</div>
                      <div class="dis_flex martp_5">
                        <div class="color_green font_12" v-if="childItem_1.PayAmount > 0">现金金额： {{ childItem_1.PayAmount | toFixed | NumFormat }}</div>
                        <div class="color_green font_12" v-if="childItem_1.CardDeductionAmount > 0" :class="childItem_1.PayAmount != 0 ? 'marlt_15' : ''">卡抵扣金额： {{ childItem_1.CardDeductionAmount | toFixed | NumFormat }}</div>

                        <div class="color_red font_12" v-if="childItem_1.LargessCardDeductionAmount > 0" :class="childItem_1.CardDeductionAmount != 0 ? 'marlt_15' : ''">赠送卡抵扣金额： {{ childItem_1.LargessCardDeductionAmount | toFixed | NumFormat }}</div>

                        <div class="color_red font_12" v-if="childItem_1.LargessAmount > 0" :class="childItem_1.LargessCardDeductionAmount != 0 ? 'marlt_15' : ''">赠送金额：¥ {{ childItem_1.LargessAmount | toFixed | NumFormat }}</div>
                      </div>
                    </el-col>
                  </el-col>
                  <el-col v-if="childItem_1.Remark" :span="24" class="pad_10 border_bottom font_12 color_333">备注信息：{{ childItem_1.Remark }}</el-col>
                  <el-col :span="24" v-if="childItem_1.TreatBillHandler.length > 0" class="pad_10 padtp_5 padbm_5 border_bottom">
                    <el-row v-for="(handler, pIndex) in childItem_1.TreatBillHandler" :key="pIndex + 'h5'">
                      <el-col :span="2">
                        <el-form @submit.native.prevent class="saleHandler" :inline="true" size="mini" label-position="left">
                          <el-form-item :label="`${handler.TreatHandlerName}`"></el-form-item>
                        </el-form>
                      </el-col>
                      <el-col :span="22">
                        <el-form @submit.native.prevent class="saleHandler" :inline="true" size="mini">
                          <el-form-item v-for="(employee, handleIndex) in handler.Employee" :key="handleIndex" :label="`${employee.EmployeeName}:`">{{ employee.Scale.toFixed(2) }}% </el-form-item>
                        </el-form>
                      </el-col>
                    </el-row>
                  </el-col>
                </el-row>
              </el-row>
            </div>
            <!-- 产品 -->
            <el-row v-for="(childItem, childIndex) in item.Product" :key="childIndex + 'c6-2'">
              <el-col :span="24" class="border_bottom pad_10">
                <el-col :span="6">
                  <div>
                    {{ childItem.ProductName }}<span v-if="childItem.Alias">({{ childItem.Alias }})</span>
                    <el-tag v-if="childItem.IsLargess" size="mini" type="danger" class="marlt_5">赠 </el-tag>
                    <el-tag class="marlt_5" size="mini"> 产品 </el-tag>
                    <span v-if="isRemark" class="marlt_5 color_primary cursor_pointer">
                      <el-button icon="el-icon-edit-outline" type="text" style="padding: unset" @click="changeRemarkClick(childItem, 'PackageCardProduct')"></el-button>
                    </span>
                  </div>
                  <div class="color_red martp_5 font_12">¥ {{ childItem.Price | toFixed | NumFormat }}</div>
                </el-col>
                <el-col :span="4">x {{ childItem.Quantity }}</el-col>
                <el-col :span="6" v-show="treatInfo.BillType == '10'">
                  <div>
                    <span v-if="childItem.CardPreferentialAmount < 0">+ ¥ {{ Math.abs(childItem.CardPreferentialAmount) | toFixed | NumFormat }}</span>
                    <span v-else-if="childItem.CardPreferentialAmount > 0">- ¥ {{ Math.abs(childItem.CardPreferentialAmount) | toFixed | NumFormat }}</span>
                    <span v-else>¥ 0.00</span>
                  </div>
                </el-col>
                <el-col :span="8">
                  <div>¥ {{ childItem.TotalAmount | toFixed | NumFormat }}</div>
                  <div class="dis_flex martp_5">
                    <div class="color_green font_12" v-if="childItem.PayAmount > 0">现金金额： {{ childItem.PayAmount | toFixed | NumFormat }}</div>
                    <div class="color_green font_12" v-if="childItem.CardDeductionAmount > 0" :class="childItem.PayAmount != 0 ? 'marlt_15' : ''">卡抵扣金额： {{ childItem.CardDeductionAmount | toFixed | NumFormat }}</div>

                    <div class="color_red font_12" v-if="childItem.LargessCardDeductionAmount > 0" :class="childItem.CardDeductionAmount != 0 ? 'marlt_15' : ''">赠送卡抵扣金额： {{ childItem.LargessCardDeductionAmount | toFixed | NumFormat }}</div>

                    <div class="color_red font_12" v-if="childItem.LargessAmount > 0" :class="childItem.LargessCardDeductionAmount != 0 ? 'marlt_15' : ''">赠送金额：¥ {{ childItem.LargessAmount | toFixed | NumFormat }}</div>
                  </div>
                </el-col>
              </el-col>
              <el-col v-if="childItem.Remark" :span="24" class="pad_10 border_bottom font_12 color_333">备注信息：{{ childItem.Remark }}</el-col>
              <el-col :span="24" v-if="childItem.TreatBillHandler.length > 0" class="pad_10 padtp_5 padbm_5 border_bottom">
                <el-row v-for="(handler, pIndex) in childItem.TreatBillHandler" :key="pIndex + 'h5'">
                  <el-col :span="2">
                    <el-form @submit.native.prevent class="saleHandler" :inline="true" size="mini" label-position="left">
                      <el-form-item :label="`${handler.TreatHandlerName}`"></el-form-item>
                    </el-form>
                  </el-col>
                  <el-col :span="22">
                    <el-form @submit.native.prevent class="saleHandler" :inline="true" size="mini">
                      <el-form-item v-for="(employee, handleIndex) in handler.Employee" :key="handleIndex" :label="`${employee.EmployeeName}:`">{{ employee.Scale.toFixed(2) }}% </el-form-item>
                    </el-form>
                  </el-col>
                </el-row>
              </el-col>
            </el-row>
          </el-row>
        </div>
      </div>
      <div class="border_left border_right border_bottom padtp_10 padbm_10" style="margin-bottom: 10px">
        <el-row>
          <el-col :span="6" :offset="17">
            <el-form class="saleInfo" size="mini">
              <el-form-item label="现金金额：" v-if="treatInfo.PayAmount">
                <div class="text_right">￥{{ treatInfo.PayAmount | toFixed | NumFormat }}</div>
              </el-form-item>

              <el-form-item label="卡抵扣金额：" v-if="treatInfo.CardDeductionAmount">
                <div class="text_right">￥{{ treatInfo.CardDeductionAmount | toFixed | NumFormat }}</div>
              </el-form-item>

              <el-form-item label="赠送卡扣金额：" v-if="treatInfo.LargessCardDeductionAmount">
                <div class="text_right">￥{{ treatInfo.LargessCardDeductionAmount | toFixed | NumFormat }}</div>
              </el-form-item>
              <el-form-item label="赠送金额：" v-if="treatInfo.LargessAmount">
                <div class="text_right">￥{{ treatInfo.LargessAmount | toFixed | NumFormat }}</div>
              </el-form-item>
              <el-form-item label="合计消耗金额：">
                <div class="text_right">￥{{ treatInfo.Amount | toFixed | NumFormat }}</div>
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>
      </div>
    </div>
    <!-- 修改备注弹框 -->
    <el-dialog width="30%" title="修改消耗单备注" :visible.sync="innerVisible" append-to-body>
      <el-input type="textarea" :rows="4" v-model="Remark"></el-input>
      <span slot="footer" class="dialog-footer">
        <el-button @click="innerVisible = false" v-prevent-click size="small">取消</el-button>
        <el-button type="primary" @click="updateRemarkClick" v-prevent-click size="small">保存</el-button>
      </span>
    </el-dialog>

    <!-- 修改 下单时间 -->
    <el-dialog width="30%" :visible.sync="ModifyBillDateVisible" append-to-body>
      <span slot="title" class="text_center">修改下单时间</span>

      <el-form :model="billDateForm" size="mini" ref="ModifyBillDateRef">
        <el-form-item label="原下单时间:">{{ treatInfo.BillDate | dateFormat("YYYY-MM-DD HH:mm") }}</el-form-item>
        <el-form-item label="新下单时间:" prop="BillDate" :rules="[{ required: true, message: '请选择下单时间', trigger: ['blur', 'change'] }]">
          <el-date-picker v-model="billDateForm.BillDate" :picker-options="pickerOptions" size="small" type="datetime" default-time="09:00" placeholder="选择日期" format="yyyy-MM-dd HH:mm" value-format="yyyy-MM-dd HH:mm"></el-date-picker>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="ModifyBillDateVisible = false" v-prevent-click size="small">取消</el-button>
        <el-button type="primary" @click="changeBillDate" v-prevent-click size="small">保存</el-button>
      </span>
    </el-dialog>

    <el-dialog :visible.sync="projectRemarkDialogVisible" title="备注" width="500px" append-to-body>
    <el-input type="textarea" :autosize="{ minRows: 2, maxRows: 4 }" maxlength="100" show-word-limit placeholder="请输入内容"
      v-model="projectRemark"></el-input>

    <div slot="footer" class="dialog-footer">
      <el-button @click="projectRemarkDialogVisible = false" v-prevent-click size="small" :disabled="RemarkLoading">取
        消</el-button>
      <el-button type="primary" @click="saveRemarkClick" :loading="RemarkLoading" v-prevent-click size="small">保
        存</el-button>
    </div>
  </el-dialog>
  </section>
</template>

<script>
import API from "@/api/iBeauty/Order/treatBill";

const dayjs = require("dayjs");
import "dayjs/locale/zh-cn"; // 导入本地化语言
dayjs.locale("zh-cn");
export default {
  name: "treatBillDetailContent",
  props: {
    treatInfo: Object,
    isModifyBillDate: {
      type: Boolean,
      default: false,
    },
    ModifyBillDateRestriction: {
      type: Object,
      default() {
        return {
          IsHaveRestriction: false, //为true有限制
          Deadline: "", //截止日期
        };
      },
    },
    
    isRemark: {
      type: Boolean,
      default: false,
    },
  },
  /** 监听数据变化   */
  watch: {
    treatInfo: {
      handler(newVal) {
        this.billDateForm.BillDate = newVal.BillDate;
      },
      deep: true,
    },
    ModifyBillDateRestriction: {
      handler(newVal) {
        if (newVal.IsHaveRestriction) {
          let DeadlineTime = dayjs(newVal.Deadline).valueOf();
          this.pickerOptions.disabledDate = (time) => {
            return time.getTime() > Date.now() || DeadlineTime > time.getTime();
          };
        } else {
          this.pickerOptions.disabledDate = (time) => {
            return time.getTime() > Date.now();
          };
        }
      },
      deep: true,
      immediate: true,
    },
  },
  /**  引入的组件  */
  components: {},
  /**  Vue 实例的数据对象**/
  data() {
    return {
      ModifyBillDateVisible: false,
      innerVisible: false,
      Remark: "",
      billDateForm: {
        BillDate: "",
      },
      payTypeForm: {
        payType: "",
      },

      pickerOptions: {
        disabledDate(time) {
          return (
            time.getTime() > Date.now()
            // ||
            // time.getTime() < Date.now() - 3600 * 1000 * 24 * 7
          );
        },
      },

      RemarkLoading: false,
      projectRemarkDialogVisible: false,
      currentItem: "",
      projectRemark: "",
      goodsTypeName: "",
    };
  },
  /**计算属性  */
  computed: {},
  /**  方法集合  */
  methods: {
    /**  修改备注  */
    saveRemarkClick() {
      let that = this;
      let params = {
        Remark: that.projectRemark,
        TreatBillGoodID: "",
      };
      switch (that.goodsTypeName) {
        case "Project":
          params.TreatBillGoodID = that.currentItem.TreatBillProjectID;
          that.treatBill_updateProjectRemark(params);
          break;
        case "SavingCard":
          params.TreatBillGoodID = that.currentItem.TreatBillSavingCardID;
          that.treatBill_updateSavingCardRemark(params);
          break;
        case "TimeCard":
          params.TreatBillGoodID = that.currentItem.TreatBillTimeCardID;
          that.treatBill_updateTimeCardRemark(params);
          break;
        case "GeneralCard":
          params.TreatBillGoodID = that.currentItem.TreatBillGeneralCardID;
          that.treatBill_updateGeneralCardRemark(params);
          break;
        case "Product":
          params.TreatBillGoodID = that.currentItem.TreatBillProductID;
          that.treatBill_updateProductRemark(params);
          break;
        case "PackageCardProject":
          params.TreatBillGoodID = that.currentItem.TreatBillProjectID;
          that.treatBill_updateProjectRemark(params);
          break;
        case "PackageCardSavingCard":
          params.TreatBillGoodID = that.currentItem.TreatBillSavingCardID;
          that.treatBill_updateSavingCardRemark(params);
          break;
        case "PackageCardTimeCard":
          params.TreatBillGoodID = that.currentItem.TreatBillTimeCardID;
          that.treatBill_updateTimeCardRemark(params);
          break;
        case "PackageCardGeneralCard":
          params.TreatBillGoodID = that.currentItem.TreatBillGeneralCardID;
          that.treatBill_updateGeneralCardRemark(params);
          break;
        case "PackageCardProduct":
          params.TreatBillGoodID = that.currentItem.TreatBillProductID;
          that.treatBill_updateProductRemark(params);
          break;
      }
    },
    /**    */
    changeRemarkClick(item, type) {
      let that = this;
      that.currentItem = item;
      that.goodsTypeName = type;
      that.projectRemark = item.Remark;
      that.projectRemarkDialogVisible = true;
    },
    /**    */
    limitSealingAccount(BillDate, limit) {
      let isBefore = dayjs(BillDate).isBefore(dayjs(limit.Deadline));
      if (isBefore && limit.IsHaveRestriction) {
        return true;
      }
      return false;
    },
    /**    */
    getBillState(BillStatus) {
      switch (BillStatus) {
        case "10":
          return "待结账";
        case "20":
          return "已完成";
        case "30":
          return "已取消";
        default:
          return "";
      }
    },

    /** 修改时间   */
    ModifyBillDateClick() {
      let that = this;
      that.ModifyBillDateVisible = true;
    },

    /** 修改下单时间   */
    changeBillDate() {
      let that = this;
      that.$refs.ModifyBillDateRef.validate((valid) => {
        if (valid) {
          that.$emit("ModifyBillDate", that.billDateForm.BillDate, () => {
            that.ModifyBillDateVisible = false;
            that.$message.success("修改成功");
          });
        }
      });
    },

    upRemarkDialog() {
      var that = this;
      that.Remark = that.treatInfo.Remark;
      that.innerVisible = true;
    },

    //修改备注
    updateRemarkClick() {
      var that = this;
      var params = {
        TreatBillID: that.treatInfo.ID,
        Remark: that.Remark,
      };
      API.updateRemark(params).then((res) => {
        if (res.StateCode == 200) {
          that.innerVisible = false;
          that.treatInfo.Remark = that.Remark;
        } else {
          that.$message.error({
            message: res.Message,
            duration: 2000,
          });
        }
      });
    },

    /**  修改产品备注  */
    treatBill_updateProductRemark(params) {
      let that = this;
      API.treatBill_updateProductRemark(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.currentItem.Remark = that.projectRemark;
            that.$message.success("修改成功");
            that.projectRemarkDialogVisible = false;
          } else {
            that.$message.error(res.Message);
          }
        })
        .catch((fail) => {
          that.$message.error(fail);
        });
    },
    /** 修改项目备注   */
    treatBill_updateProjectRemark(params) {
      let that = this;
      API.treatBill_updateProjectRemark(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.currentItem.Remark = that.projectRemark;
            that.$message.success("修改成功");
            that.projectRemarkDialogVisible = false;
          } else {
            that.$message.error(res.Message);
          }
        })
        .catch((fail) => {
          that.$message.error(fail);
        });
    },
    /**  修改通用次卡备注   */
    treatBill_updateGeneralCardRemark(params) {
      let that = this;
      API.treatBill_updateGeneralCardRemark(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.currentItem.Remark = that.projectRemark;
            that.$message.success("修改成功");
            that.projectRemarkDialogVisible = false;
          } else {
            that.$message.error(res.Message);
          }
        })
        .catch((fail) => {
          that.$message.error(fail);
        });
    },
    /**  修改时效卡备注  */
    treatBill_updateTimeCardRemark(params) {
      let that = this;
      API.treatBill_updateTimeCardRemark(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.currentItem.Remark = that.projectRemark;
            that.$message.success("修改成功");
            that.projectRemarkDialogVisible = false;
          } else {
            that.$message.error(res.Message);
          }
        })
        .catch((fail) => {
          that.$message.error(fail);
        });
    },
    /**  修改储值卡备注  */
    treatBill_updateSavingCardRemark(params) {
      let that = this;
      API.treatBill_updateSavingCardRemark(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.currentItem.Remark = that.projectRemark;
            that.$message.success("修改成功");
            that.projectRemarkDialogVisible = false;
          } else {
            that.$message.error(res.Message);
          }
        })
        .catch((fail) => {
          that.$message.error(fail);
        });
    },
  },
  /** 创建实例之前执行函数   */
  beforeCreate() {},
  /**  实例创建完成之后  */
  created() {},
  /**  在挂载开始之前被调用  */
  beforeMount() {},
  /**  实例被挂载后调用  */
  mounted() {
    let that = this;
    that.billDateForm.BillDate = that.treatInfo.BillDate;
  },
  /**  数据更新 之前 调用  */
  beforeUpdate() {},
  /** 数据更新 完成 调用   */
  updated() {},
  /**  实例销毁之前调用  */
  beforeDestroy() {},
  /**  实例销毁后调用  */
  destroyed() {},
};
</script>

<style lang="scss">
.treatBillDetailContent {
  .saleInfo {
    .el-form-item__label {
      font-size: 13px !important;
    }

    .el-form-item__content {
      font-size: 13px !important;
    }

    .el-form-item {
      margin-bottom: 0px;
    }
  }

  .saleHandler {
    .el-form-item__label {
      font-size: 12px !important;
      line-height: 18px;
      color: #c0c4cc;
    }

    .el-form-item__content {
      font-size: 12px !important;
      line-height: 20px;
      color: #c0c4cc;
    }

    .el-form-item {
      margin-bottom: 0px;
    }
  }
}
</style>
