/**
 * Created by preference on 2022/09/08
 *  zmx
 */

import * as API from "@/api/index";
export default {
  /**   */
  appointmentType_all: (params) => {
    return API.POST("api/appointmentType/all", params);
  },
  /**   */
  appointmentType_create: (params) => {
    return API.POST("api/appointmentType/create", params);
  },
  /**   */
  appointmentType_update: (params) => {
    return API.POST("api/appointmentType/update", params);
  },
  /**   */
  appointmentType_move: (params) => {
    return API.POST("api/appointmentType/move", params);
  },
};
