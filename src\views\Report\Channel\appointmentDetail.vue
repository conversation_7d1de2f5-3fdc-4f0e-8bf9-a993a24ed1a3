<template>
  <div class="ChannelAppointmentDetail content_body" :loading="loading">
    <div class="nav_header">
      <el-row>
        <el-col :span="24">
          <el-form :inline="true" size="small" @keyup.enter.native="handleSearch">
            <el-row>
              <el-form-item label="客户">
                <el-input v-model="searchForm.Name" placeholder="输入客户名称、手机号搜索" clearable @clear="handleSearch"> </el-input>
              </el-form-item>
              <el-form-item label="预约日期">
                <el-date-picker
                  v-model="searchForm.searchDate"
                  :picker-options="pickerOptions"
                  unlink-panels
                  type="daterange"
                  range-separator="至"
                  value-format="yyyy-MM-dd"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  @change="handleSearch"
                ></el-date-picker>
              </el-form-item>
              <el-form-item label="状态">
                <el-select placeholder="请选择预约状态" clearable v-model="searchForm.Status" @change="handleSearch">
                  <el-option label="未到店" value="10"></el-option>
                  <el-option label="已到店" value="20"></el-option>
                  <el-option label="已取消" value="30"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="预约来源">
                <el-select placeholder="请选择预约来源" clearable v-model="searchForm.Channel" @change="handleSearch">
                  <el-option label="PC" value="PC"></el-option>
                  <el-option label="小程序" value="Miniprogram"></el-option>
                  <el-option label="商城" value="MicroMall"></el-option>
                </el-select>
              </el-form-item>

              <el-form-item label="创建时间">
                <el-date-picker
                  v-model="searchForm.CreatedOnDate"
                  :picker-options="pickerOptions"
                  unlink-panels
                  type="daterange"
                  range-separator="至"
                  value-format="yyyy-MM-dd"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  @change="handleSearch"
                ></el-date-picker>
              </el-form-item>
            </el-row>
            <el-row>
              <el-form-item label="预约角色">
                <el-select placeholder="请选择" clearable v-model="searchForm.ServicerID" @change="handleSearch">
                  <el-option v-for="item in servicerList" :key="'servicer' + item.ServicerID" :label="item.ServicerName" :value="item.ServicerID"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="预约员工">
                <el-select placeholder="请选择" clearable filterable v-model="searchForm.ServicerEmployeeID" @change="handleSearch">
                  <el-option v-for="item in searchEmployeeAllList" :key="'searchEmployeeAllList_' + item.ID" :label="item.Name" :value="item.ID"></el-option>
                </el-select>
              </el-form-item>

              <el-form-item label="会员等级">
                <el-select v-model="searchForm.CustomerLevelID" placeholder="请选择会员等级" filterable size="small" clearable @change="handleSearch">
                  <el-option v-for="item in customerLevel" :key="item.ID" :label="item.Name" :value="item.ID"> </el-option>
                </el-select>
              </el-form-item>

              <el-form-item label="渠道">
                <el-input v-model="searchForm.CustomerChannel" placeholder="输入渠道名称" clearable @clear="handleSearch"> </el-input>
              </el-form-item>

              <el-form-item label="预约门店">
                <el-select v-model="searchForm.EntityID" placeholder="请选择预约门店" filterable size="small" clearable @change="handleSearch">
                  <el-option v-for="item in EntityList" :key="item.ID" :label="item.EntityName" :value="item.ID"> </el-option>
                </el-select>
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="handleSearch" v-prevent-click>搜索</el-button>
                <el-button type="primary" @click="channelAppointmentDetail_excel" v-prevent-click :loading="downloadLoading">导出</el-button>
              </el-form-item>
            </el-row>
          </el-form>
        </el-col>
      </el-row>
    </div>

    <el-table :data="tableData" size="small">
      <el-table-column prop="CustomerName" label="客户姓名">
        <template slot-scope="scope">
          <div>{{ scope.row.CustomerName }}</div>
          <div>手机号：{{ scope.row.PhoneNumber | hidephone }}</div>
          <!-- <div>编号：{{ scope.row.Code }}</div> -->
        </template>
      </el-table-column>
      <el-table-column prop="LevelName" label="会员等级"></el-table-column>
      <el-table-column prop="ChannelName" label="渠道"></el-table-column>
      <el-table-column prop="AppointmentTypeName" label="预约类型"> </el-table-column>
      <el-table-column prop="Servicer" label="接待人">
        <template slot-scope="scope">
          <el-popover placement="top-start" width="200" trigger="hover">
            <el-descriptions :column="1" size="small" border>
              <el-descriptions-item v-for="item in scope.row.Servicer" :key="'Servicer' + item.ServicerID" :label="item.ServicerName">{{
                servicerNames(item.Employee)
              }}</el-descriptions-item>
            </el-descriptions>
            <div slot="reference" style="text-overflow: ellipsis; white-space: nowrap; overflow: hidden">
              <span v-if="scope.row.Servicer.length > 0">
                <span v-if="scope.row.Servicer[0].Employee">{{ scope.row.Servicer[0].ServicerName }}：</span>
                <span>{{ servicerNames(scope.row.Servicer[0].Employee) }}</span>
              </span>
            </div>
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column prop="Status" label="预约状态">
        <template slot-scope="scope">
          {{ scope.row.Status == 10 ? '未到店' : scope.row.Status == 20 ? '已到店' : '已取消' }}
        </template>
      </el-table-column>
      <el-table-column prop="AppointmentDate" label="预约时间"></el-table-column>
      <el-table-column prop="Period" label="预约时长（分钟）"></el-table-column>
      <el-table-column prop="EntityName" label="预约门店"></el-table-column>
      <el-table-column prop="ArrivalDate" label="到店时间"></el-table-column>
      <el-table-column prop="CreatedBy" label="创建人"></el-table-column>
      <el-table-column prop="CreatedOn" label="创建时间"></el-table-column>
      <el-table-column prop="Channel" label="预约来源"></el-table-column>
    </el-table>
    <div class="text_right pad_15">
      <el-pagination
        background
        v-if="paginations.total > 0"
        @current-change="handleCurrentChange"
        :current-page.sync="paginations.page"
        :page-size="paginations.page_size"
        :layout="paginations.layout"
        :total="paginations.total"
      ></el-pagination>
    </div>
  </div>
</template>

<script>
import API from '@/api/Report/Channel/channelAppointmentDetail.js';

import API_ from '@/api/iBeauty/Appointment/appointmentView';
import APICustomerLevel from '@/api/CRM/Customer/customerLevel';
const dayjs = require('dayjs');
const isoWeek = require('dayjs/plugin/isoWeek');
dayjs.extend(isoWeek);
export default {
  name: 'ChannelAppointmentDetail',

  beforeRouteEnter(to, from, next) {
    next((vm) => {
      vm.isExport = vm.$permission.permission(to.meta.Permission, 'Report-Channel-AppointmentDetail-Export');
    });
  },
  props: {},
  /** 监听数据变化   */
  watch: {},
  /**  引入的组件  */
  components: {},
  /**  Vue 实例的数据对象**/
  data() {
    return {
      loading: false,
      downloadLoading: false,
      isExport: false,
      searchForm: {
        Name: '',
        AppointmentBillID: '',
        StartDate: '',
        EndDate: '',
        searchDate: [dayjs().format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')],
        Status: '',
        CreatedBy: '',
        CreatedOnStartDate: '',
        CreatedOnEndDate: '',
        CreatedOnDate: '',
        ServicerID: '',
        ServicerEmployeeID: '',
        Channel: '',
        CustomerLevelID: '', //顾客等级编号
        CustomerChannel: '', //顾客渠道搜索
        EntityID: '',
      },

      pickerOptions: {
        shortcuts: [
          {
            text: '今天',
            onClick: (picker) => {
              let end = new Date();
              let start = new Date();
              picker.$emit('pick', [start, end]);
            },
          },
          {
            text: '本周',
            onClick: (picker) => {
              let end = new Date();
              let weekDay = dayjs(end).isoWeekday();
              let start = dayjs(end)
                .subtract(weekDay - 1, 'day')
                .toDate();
              picker.$emit('pick', [start, end]);
            },
          },
          {
            text: '本月',
            onClick: (picker) => {
              let end = new Date();
              let start = dayjs(dayjs(end).format('YYYY-MM') + '01').toDate();
              picker.$emit('pick', [start, end]);
            },
          },
        ],
      },
      tableData: [],
      customerLevel: [],
      servicerList: [],
      searchEmployeeAllList: [],
      EntityList: [],

      paginations: {
        page: 1, // 当前位于哪页
        total: 0, // 总数
        page_size: 10, // 1页显示多少条
        layout: 'total, prev, pager, next,jumper', // 翻页属性
      },
    };
  },
  /**计算属性  */
  computed: {},
  /**  方法集合  */
  methods: {
    /**  搜索  */
    handleSearch() {
      let that = this;
      that.getAppointmentBill_list();
    },
    /**    */
    handleCurrentChange(page) {
      let that = this;
      that.paginations.page = page;
      that.getAppointmentBill_list();
    },
    /**    */
    servicerNames(employees) {
      return employees.map((i) => i.EmployeeName).join(',');
    },

    /**  获取列表  */
    getAppointmentBill_list() {
      let that = this;
      if (that.searchForm.CreatedOnDate) {
        if (dayjs(that.searchForm.CreatedOnDate[0]).add(366, 'day').valueOf() < dayjs(that.searchForm.CreatedOnDate[1]).valueOf()) {
          that.$message.error('时间筛选范围不能超366天');
          return;
        }
      }
      if (!that.searchForm.searchDate) {
        that.$message.error('请选择预约日期');
        return;
      }

      if (dayjs(that.searchForm.searchDate[0]).add(366, 'day').valueOf() < dayjs(that.searchForm.searchDate[1]).valueOf()) {
        that.$message.error('时间筛选范围不能超366天');
        return;
      }
      that.loading = true;
      let params = {
        PageNum: that.paginations.page,
        Name: that.searchForm.Name,
        AppointmentBillID: that.searchForm.AppointmentBillID,
        StartDate: that.searchForm.searchDate ? that.searchForm.searchDate[0] : '',
        EndDate: that.searchForm.searchDate ? that.searchForm.searchDate[1] : '',
        Status: that.searchForm.Status,
        CreatedOnStartDate: that.searchForm.CreatedOnDate ? that.searchForm.CreatedOnDate[0] : '',
        CreatedOnEndDate: that.searchForm.CreatedOnDate ? that.searchForm.CreatedOnDate[1] : '',
        ServicerID: that.searchForm.ServicerID,
        ServicerEmployeeID: that.searchForm.ServicerEmployeeID,
        Channel: that.searchForm.Channel,
        CustomerLevelID: that.searchForm.CustomerLevelID, //顾客等级编号
        CustomerChannel: that.searchForm.CustomerChannel, //顾客渠道搜索
        EntityID: that.searchForm.EntityID,
      };
      API.channelAppointmentDetail_list(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.tableData = res.List;
            that.paginations.total = res.Total;
            that.paginations.page_size = res.PageSize;
            that.loading = false;
          } else {
            that.loading = false;
            that.$message.error(res.Message);
          }
        })
        .catch((fail) => {
          that.loading = false;
          that.$message.error(fail);
        });
    },
    /**    */
    channelAppointmentDetail_excel() {
      let that = this;
      if (that.searchForm.CreatedOnDate) {
        if (dayjs(that.searchForm.CreatedOnDate[0]).add(366, 'day').valueOf() < dayjs(that.searchForm.CreatedOnDate[1]).valueOf()) {
          that.$message.error('时间筛选范围不能超366天');
          return;
        }
      }
      if (!that.searchForm.searchDate) {
        that.$message.error('请选择预约日期');
        return;
      }

      if (dayjs(that.searchForm.searchDate[0]).add(366, 'day').valueOf() < dayjs(that.searchForm.searchDate[1]).valueOf()) {
        that.$message.error('时间筛选范围不能超366天');
        return;
      }
      that.loading = true;
      let params = {
        PageNum: that.paginations.page,
        Name: that.searchForm.Name,
        AppointmentBillID: that.searchForm.AppointmentBillID,
        StartDate: that.searchForm.searchDate ? that.searchForm.searchDate[0] : '',
        EndDate: that.searchForm.searchDate ? that.searchForm.searchDate[1] : '',
        Status: that.searchForm.Status,
        CreatedOnStartDate: that.searchForm.CreatedOnDate ? that.searchForm.CreatedOnDate[0] : '',
        CreatedOnEndDate: that.searchForm.CreatedOnDate ? that.searchForm.CreatedOnDate[1] : '',
        ServicerID: that.searchForm.ServicerID,
        ServicerEmployeeID: that.searchForm.ServicerEmployeeID,
        Channel: that.searchForm.Channel,
        CustomerLevelID: that.searchForm.CustomerLevelID, //顾客等级编号
        CustomerChannel: that.searchForm.CustomerChannel, //顾客渠道搜索
        EntityID: that.searchForm.EntityID,
      };
      API.channelAppointmentDetail_excel(params)
        .then((res) => {
          this.$message.success({
            message: '正在导出',
            duration: '4000',
          });
          const link = document.createElement('a');
          let blob = new Blob([res], { type: 'application/octet-stream' });
          link.style.display = 'none';
          link.href = URL.createObjectURL(blob);
          link.download = '渠道预约明细.xlsx'; //下载的文件名
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
          that.downloadLoading = false;
        })
        .catch((fail) => {
          that.$message.error(fail);
          that.downloadLoading = false;
        });
    },

    /* 顾客等级 */
    CustomerLevelData() {
      var that = this;
      var params = {
        Name: '',
        Active: true,
      };
      APICustomerLevel.getCustomerLevel(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.customerLevel = res.Data;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },
    /**  预约看板-服务人员  */
    async appointmentBill_servicer() {
      let that = this;
      try {
        let params = {};
        let res = await API_.appointmentBill_servicer(params);
        if (res.StateCode == 200) {
          that.servicerList = res.Data;
        } else {
          that.$message.error(res.Message);
        }
      } catch (error) {
        that.$message.error(error);
      }
    },
    /**    */
    async employee_all() {
      let that = this;
      try {
        let params = {};
        let res = await API_.employee_all(params);
        if (res.StateCode == 200) {
          that.searchEmployeeAllList = res.Data;
        } else {
          that.$message.error(res.Message);
        }
      } catch (error) {
        that.$message.error(error);
      }
    },
    /**    */
    reportEntity_storeList() {
      let that = this;
      let params = {};
      API.reportEntity_storeList(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.EntityList = res.Data;
          } else {
            that.$message.error(res.Message);
          }
        })
        .catch((fail) => {
          that.$message.error(fail);
        });
    },
  },
  /** 创建实例之前执行函数   */
  beforeCreate() {},
  /**  实例创建完成之后  */
  created() {},
  /**  在挂载开始之前被调用  */
  beforeMount() {},
  /**  实例被挂载后调用  */
  mounted() {
    this.isExport = this.$permission.permission(this.$route.meta.Permission, 'Report-Channel-AppointmentDetail-Export');
    this.getAppointmentBill_list();
    this.CustomerLevelData();
    this.appointmentBill_servicer();
    this.employee_all();
    this.reportEntity_storeList();
  },
  /**  数据更新 之前 调用  */
  beforeUpdate() {},
  /** 数据更新 完成 调用   */
  updated() {},
  /**  实例销毁之前调用  */
  beforeDestroy() {},
  /**  实例销毁后调用  */
  destroyed() {},
};
</script>

<style lang="scss">
.ChannelAppointmentDetail {
}
</style>
