<template>
  <div class="CustomerDetail" v-loading.fullscreen="editLoading">
    <el-dialog title="客户详情" :visible.sync="customerDetailVisible" width="1280px" @close="customerDetailClose" append-to-body custom-class="CustomerDetail">
      <!-- 顾客信息 -->
      <div class="customer_information">
        <el-row type="flex" justify="space-between">
          <el-col :span="2">
            <el-upload :disabled="!customInfoEdit && !customerEntityBool" action="" :show-file-list="false" :before-upload="beforeAvatarUpload" :http-request="updateCustomerUploadImage">
              <el-avatar :size="50" :src="customerDetail.Avatar ? customerDetail.Avatar : circleUrl" fit="cover" class="custom-avatar"></el-avatar>
            </el-upload>
          </el-col>
          <el-col :span="12">
            <strong class="marrt_5 font_18">{{ customerDetail.Name }}</strong>
            <el-image v-if="customerDetail.Gender == 2" style="width: 8px; height: 12px" :src="require('@/assets/img/gender-female.png')"></el-image>
            <el-image v-if="customerDetail.Gender == 1" style="width: 8px; height: 12px" :src="require('@/assets/img/gender-male.png')"></el-image>
            <el-button class="padlt_10" size="mini" type="text" @click="showEditDialog(customerDetail, false)">查看/编辑全部信息</el-button>

            <el-row v-if="customerDetail.IsMember">
              <el-dropdown placement="bottom-start" :disabled="!isModifyCustomerLevel">
                <div>
                  <span class="cursor_pointer"><i class="el-icon-medal"></i> {{ customerDetail.CustomerLevelName }}</span>
                  <span v-if="customerDetail.IsShowGrowthValue">
                    (成长值：
                    <el-button type="text" size="small" @click="checkGrowthValue">{{ customerDetail.GrowthValue || 0 }} </el-button>
                    <span class="marlt_5" v-if="customerDetail.CustomerLevelValidityOn">| 有效期：{{ customerDetail.CustomerLevelValidityOn }}</span>

                    <span class="marlt_5" v-if="customerDetail.MemberOn">| 成为会员日期：{{ customerDetail.MemberOn }}</span>
                    )
                  </span>
                </div>

                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item @click.native="showCustomerLevelDialog(1)">设置会员等级</el-dropdown-item>
                  <el-dropdown-item @click.native="deleteCustomerLevel">移出等级会员</el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </el-row>
            <el-row v-else-if="isModifyCustomerLevel" class="LevelCustomer" @click.native="showCustomerLevelDialog(2)">升级等级会员</el-row>
          </el-col>

          <el-col :span="10" class="text_right">
            <el-button size="small" type="primary" @click="navCreateBill" plain v-if="openBillState">开单</el-button>
            <el-button size="small" type="primary" plain @click="clickAppointment" v-if="AppointmentState">预约 </el-button>
          </el-col>
        </el-row>

        <el-form size="small" label-width="90px" class="custom-customer-detail">
          <el-row class="martp_10">
            <el-col :span="6" class="color_999">
              <el-form-item label="手机号：">
                <span v-if="isCustomerPhoneNumberView" class="color_333">{{ customerDetail.PhoneNumber }}</span>
                <span v-else class="color_333">{{ customerDetail.PhoneNumber | hidephone }}</span>
              </el-form-item>
            </el-col>

            <el-col :span="6" class="color_999">
              <el-form-item label="服务人员：">
                <el-popover placement="top-start" width="200" trigger="hover" :disabled="getShowProductNameTooltip(customerDetail.ServicerEmployee)">
                  <div>
                    <el-descriptions size="mini" :column="1" border :colon="true" labelClassName="custom-customer-descLabel">
                      <el-descriptions-item v-for="(item, index) in customerDetail.ServicerEmployee && customerDetail.ServicerEmployee.filter((i) => i.ServicerEmpList && i.ServicerEmpList.length > 0)" :key="index">
                        <span slot="label" v-if="item.ServicerEmpList.length != 0">{{ item.Name + ":" }}</span>
                        {{ getServicerEmpNames(item.ServicerEmpList) }}
                      </el-descriptions-item>
                    </el-descriptions>
                  </div>
                  <div slot="reference" style="text-overflow: ellipsis; white-space: nowrap; overflow: hidden; padding-right: 5px" class="color_999">
                    <span class="color_333" v-if="customerDetail && customerDetail.ServicerEmployee && customerDetail.ServicerEmployee.length > 0">
                      <span v-if="customerDetail.ServicerEmployee && customerDetail.ServicerEmployee.filter((i) => i.ServicerEmpList.length).length > 0">{{ customerDetail.ServicerEmployee.filter((i) => i.ServicerEmpList.length)[0].Name }}： </span>
                      <span v-if="customerDetail.ServicerEmployee.filter((i) => i.ServicerEmpList.length).length > 0">
                        {{ getServicerEmpNames(customerDetail.ServicerEmployee.filter((i) => i.ServicerEmpList.length)[0].ServicerEmpList) }}
                      </span>
                    </span>
                  </div>
                </el-popover>
              </el-form-item>
            </el-col>

            <el-col :span="6" class="color_999">
              <el-form-item label="会员编号：">
                <span class="color_333">{{ customerDetail.Code ? customerDetail.Code : "-" }}</span>
              </el-form-item>
            </el-col>

            <el-col :span="6" class="color_999">
              <el-form-item label="公历生日：">
                <span slot="label"> {{ customerDetail.BirthdayType == 10 ? "公历生日" : "农历生日" }}： </span>
                <span class="color_333">{{ customerDetail.Birthday ? customerDetail.Birthday : "-" }}</span>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="6" class="color_999">
              <el-form-item label="归属门店：">
                <span class="color_333">{{ customerDetail.EntityName }}</span>
                <el-popover placement="bottom-start" width="280" trigger="click" ref="belongEntityRef">
                  <div class="dis_flex flex_x_between">
                    <el-select v-model="selectCustomerBelongEntity" placeholder="请选择" class="marrt_20">
                      <el-option v-for="item in customerBelongEntity" :key="item.ID" :label="item.EntityName" :value="item.ID"> </el-option>
                    </el-select>
                    <el-button @click="changeBelongEntityClick" size="mini" type="primary" v-prevent-click>确定</el-button>
                  </div>
                  <el-button v-if="isModifyBelongEntity" slot="reference" class="padlt_10" size="mini" type="text">修改</el-button>
                </el-popover>
              </el-form-item>
            </el-col>
            <el-col :span="6" class="color_999">
              <el-form-item label="信息来源：" class="color_333">{{ customerDetail.CustomerSourceName ? customerDetail.CustomerSourceName : "-" }}</el-form-item>
            </el-col>

            <el-col :span="6" class="color_999">
              <el-form-item label="注册门店：" class="color_333">{{ customerDetail.CreatedEntityName ? customerDetail.CreatedEntityName : "-" }}</el-form-item>
            </el-col>

            <el-col :span="6" class="color_999">
              <el-form-item label="注册时间：" class="color_333">{{ customerDetail.CreatedOn | dateFormat }}</el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <!-- 顾客存量余额信息 -->
      <div class="martp_5 customer_information">
        <el-row type="flex" align="middle" class="color_999">
          <el-col :span="5">
            <el-col :span="24">余额(元)/赠额</el-col>
          </el-col>
          <el-col :span="6">
            <el-col :span="24">累计消费金额(元)/次数/客单</el-col>
          </el-col>
          <el-col :span="6">
            <el-col :span="24">累计消耗金额(元)/次数/客单</el-col>
          </el-col>
          <el-col :span="3">
            <el-col :span="24">欠款金额(元)</el-col>
          </el-col>
          <el-col :span="2">
            <el-col :span="24">最近购买时间</el-col>
          </el-col>
          <el-col :span="2">
            <el-col :span="24">最近消耗时间</el-col>
          </el-col>
        </el-row>
        <el-row type="flex" align="middle">
          <el-col :span="5">
            <el-col :span="24" class="martp_5 color_333">
              <span>{{ accountInfo.TotalAmount | toFixed | NumFormat }}</span>
              <span> / {{ accountInfo.LargessTotalAmount | toFixed | NumFormat }}</span>
            </el-col>
          </el-col>
          <el-col :span="6">
            <el-col :span="24" class="martp_5">
              <span class="color_333">{{ accountInfo.SaleAmount | toFixed | NumFormat }}</span>
              <span>{{ " / " + accountInfo.SaleQuantity + "次 / " }}</span>
              <span class="color_main">{{ accountInfo.SalePrice | toFixed | NumFormat }}</span>
            </el-col>
          </el-col>
          <el-col :span="6">
            <el-col :span="24" class="martp_5">
              <span class="color_333">{{ accountInfo.ConsumeProjectAmount | toFixed | NumFormat }}</span>
              <span>{{ " / " + accountInfo.ConsumeProjectQuantity + "次 / " }}</span>
              <span class="color_main">{{ accountInfo.ConsumePrice | toFixed | NumFormat }}</span>
            </el-col>
          </el-col>
          <el-col :span="3">
            <el-col :span="24" class="martp_10 color_red"> {{ accountInfo.ArrearAmount | toFixed | NumFormat }}</el-col>
          </el-col>
          <el-col :span="2">
            <el-col :span="24" class="martp_10 color_333">{{ accountInfo.LastSaleBillDate }}</el-col>
          </el-col>
          <el-col :span="2">
            <el-col :span="24" class="martp_10 color_333">{{ accountInfo.LastConsumeProjectBillDate }}</el-col>
          </el-col>
        </el-row>
      </div>

      <div class="customer_content">
        <el-tabs v-model="tabPane" @tab-click="handleClick">
          <el-tab-pane label="基本档案" name="0">
            <div class="tip">
              <el-row type="flex" justify="space-between" align="middle">
                <el-col :span="12">标签</el-col>
                <el-col :span="12" class="text_right">
                  <el-button size="mini" type="text" style="padding: 0px" @click="tagClick">编辑</el-button>
                </el-col>
              </el-row>
            </div>
            <!-- 列表 -->
            <div class="martp_5" style="flex: 2">
              <el-tag v-for="(item, index) in customerTag" :key="index" effect="plain" class="mar_5">{{ item.Name }} </el-tag>
            </div>

            <!-- 添加 营销云 -->

            <div class="tip martp_5">
              <el-row type="flex" justify="space-between" align="middle">
                <el-col :span="12">营销云标签</el-col>
                <el-col :span="12" class="text_right">
                  <!-- <el-button size="mini" type="text" style="padding: 0px" @click="tagClick">编辑</el-button> -->
                </el-col>
              </el-row>
            </div>
            <!-- 营销云下面的列表 -->
            <div class="martp_5" style="flex: 2">
              <el-tag v-for="(item, index) in marketingCloud" :key="index" effect="plain" class="mar_5">{{ item }} </el-tag>
            </div>

            <!-- 顾客基本档案 -->
            <div class="tip martp_5" v-if="basicFileTerm.length > 0">
              <el-row type="flex" justify="space-between" align="middle">
                <el-col :span="12">基本档案 </el-col>
                <el-col :span="12" class="text_right">
                  <el-button size="mini" type="text" style="padding: 0px" @click="saveBasicFileClick">保存</el-button>
                </el-col>
              </el-row>
            </div>
            <div class="martp_10">
              <el-scrollbar style="height: 100%">
                <el-row>
                  <el-form ref="form" label-width="140px" size="small">
                    <el-col :span="8" v-for="(item, index) in basicFileTerm" :key="index">
                      <el-form-item v-if="item.Type == 10" :label="item.CustomerBasicFileName">
                        <el-input v-model="item.Value" placeholder="请输入文本内容" clearable size="small"></el-input>
                      </el-form-item>
                      <el-form-item v-if="item.Type == 20" :label="item.CustomerBasicFileName">
                        <el-date-picker v-model="item.Value" type="date" placeholder="请选择日期" clearable size="small"> </el-date-picker>
                      </el-form-item>
                      <el-form-item v-if="item.Type == 30" :label="item.CustomerBasicFileName">
                        <el-select v-model="item.Value" placeholder="请选择单选项" clearable size="small">
                          <el-option v-for="item in getComponentsProperty(item.ComponentsProperty)" :key="item.key" :label="item.value" :value="item.value"> </el-option>
                        </el-select>
                      </el-form-item>
                      <el-form-item v-if="item.Type == 40" :label="item.CustomerBasicFileName">
                        <el-select v-model="item.Value" multiple collapse-tags placeholder="请选择多选项" clearable size="small">
                          <el-option v-for="item in getComponentsProperty(item.ComponentsProperty)" :key="item.key" :label="item.value" :value="item.value"> </el-option>
                        </el-select>
                      </el-form-item>
                    </el-col>
                  </el-form>
                </el-row>
              </el-scrollbar>
            </div>
          </el-tab-pane>
          <el-tab-pane label="卡项信息" name="1">
            <customer-account :customerID="customerID" ref="customerAccount"></customer-account>
          </el-tab-pane>
          <el-tab-pane label="订单信息" name="2">
            <customerbill :customerID="customerID" ref="customerbill"></customerbill>
          </el-tab-pane>
          <el-tab-pane label="服务日志" name="5">
            <NursingLog :customerID="customerID" :isDeleteNursingLog="isDeleteNursingLog" ref="nursingLog"></NursingLog>
          </el-tab-pane>
          <el-tab-pane label="跟进记录" name="8">
            <followUpRecord :customerID="customerID" ref="followUpRecord"></followUpRecord>
          </el-tab-pane>
          <el-tab-pane label="预约记录" name="4">
            <appointmentRecord :customerID="customerID" ref="appointmentRecord"></appointmentRecord>
          </el-tab-pane>
          <el-tab-pane label="文件档案" name="6">
            <coustomerFileUpload :customerID="customerID" :isDeleteFile="isDeleteFile" ref="coustomerFileUpload"> </coustomerFileUpload>
          </el-tab-pane>
          <!-- <el-tab-pane v-if="isElectronicMedicalRecord" label="电子病历" name="7">
            <electronicMedicalRecord :CustomerID="customerID" :isDeleteFile="isDeleteFile" ref="electronicMedicalRecord"></electronicMedicalRecord>
          </el-tab-pane> -->
          <!-- <el-tab-pane v-if="isPhotoCompare" label="对比照片" name="9">
            <effectComparison :CustomerID="customerID" ref="effectComparison"></effectComparison>
          </el-tab-pane> -->
          <el-tab-pane label="企微会话" name="10">
            <customerDialogue :customerID="customerID" ref="customerDialogue"></customerDialogue>
          </el-tab-pane>
        </el-tabs>
      </div>
    </el-dialog>
    <!--标签弹窗-->
    <el-dialog v-if="dialogTag" title="编辑标签" :visible.sync="dialogTag" width="1000px" append-to-body>
      <el-row style="max-height: 130px; overflow-y: auto">
        <el-tag v-for="(item, index) in editCustomerTag" :key="index" closable @close="removeTag(index)" effect="plain" class="mar_5">{{ item.Name }} </el-tag>
      </el-row>
      <el-row class="pad_5" v-if="customTagLibrary">
        <el-col :span="10">
          <div class="el-form-item el-form-item--small" style="margin-bottom: 0px">
            <label class="el-form-item__label" style="width: 98px">自定义标签：</label>
            <div class="el-form-item__content" style="margin-left: 98px">
              <div class="el-input el-input--small">
                <el-input type="text" autocomplete="off" placeholder="标签名限8个字" v-model="tagName" maxlength="8" size="small" clearable>
                  <template slot="append">
                    <el-button size="small" @click="addTagClick" clearable v-prevent-click>添加</el-button>
                  </template>
                </el-input>
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
      <el-row class="pad_5">
        <div class="pad_5_0">选择已有标签</div>
        <el-col style="height: 180px; overflow-y: auto" class="border radius5 pad_10">
          <el-tag v-for="item in tagList" :key="item.ID" :type="getTagType(item.type)" effect="plain" @click="tagSelectClick(item)" class="cursor_pointer mar_5">{{ item.Name }} </el-tag>
        </el-col>
      </el-row>
      <div slot="footer">
        <el-button size="small" @click="dialogTag = false" v-prevent-click>取 消</el-button>
        <el-button type="primary" size="small" @click="tagSaveClick" :loading="modalLoading" v-prevent-click>保 存</el-button>
      </div>
    </el-dialog>
    <!--预约-->
    <el-dialog v-if="appointmentDialogShow" :visible.sync="appointmentDialogShow" width="900px" title="添加预约" append-to-body>
      <!-- 预约顾客信息  -->
      <el-row>
        <el-col :span="11" :offset="13" class="back_f8 dis_flex flex_y_center radius5 line_height_38 pad_0_10">
          <el-col :span="12" class="back_f8">预约项目</el-col>
          <el-col :span="12" class="text_right">
            <el-button type="text" size="small" @click="addAppointmentProject">添 加 </el-button>
          </el-col>
        </el-col>
      </el-row>
      <el-row class="martp_10">
        <el-col :span="11" class="left_item">
          <el-scrollbar class="back_f8 pad_10" style="height: 400px">
            <div class="martp_5">
              <el-form :model="appointmentRuleForm" :rules="appointmentRules" ref="appointmentRuleForm" label-width="100px" size="small">
                <el-form-item label="预约类型">
                  <el-col :span="20">
                    <el-select v-model="appointmentRuleForm.AppointmentTypeID" placeholder="请选择预约类型" clearable>
                      <el-option :label="item.Name" :value="item.ID" v-for="item in appointmentTypeList" :key="'appointmentType' + item.ID"></el-option>
                    </el-select>
                  </el-col>
                </el-form-item>

                <el-form-item prop="AppointmentDate" label="预约时间">
                  <el-date-picker v-model="appointmentRuleForm.AppointmentDate" value-format="yyyy-MM-dd" placeholder="选择日期时间" size="small" @change="handleAppointmentDateChange"> </el-date-picker>

                  <el-time-select
                    class="martp_15"
                    v-model="appointmentRuleForm.Time"
                    :picker-options="{
                      start: appointmentConfigInfo.StartTime,
                      step: '00:' + appointmentConfigInfo.Period,
                      end: appointmentConfigInfo.EndTime,
                    }"
                    placeholder="选择时间"
                  >
                  </el-time-select>
                </el-form-item>

                <el-form-item label="预约时长" prop="Period">
                  <el-col :span="20">
                    <el-select v-model="appointmentRuleForm.Period" placeholder="请选择预约时长" clearable>
                      <el-option :label="item.time" :value="item.value" v-for="(item, index) in timeArr" :key="index"> </el-option>
                    </el-select>
                  </el-col>
                </el-form-item>

                <el-form-item v-for="item in addServicerEmployeeList" :key="'addServicerID' + item.ServicerID" :label="item.ServicerName">
                  <el-col :span="20">
                    <el-select v-model="item.SelectEmployeeID" placeholder="请选择" clearable>
                      <el-option :label="item.EmployeeName" :value="item.EmployeeID" v-for="item in item.Employee" :key="'addEmployee' + item.EmployeeID"></el-option>
                    </el-select>
                  </el-col>
                </el-form-item>

                <el-form-item label="备注信息" prop="Remark">
                  <el-col :span="20">
                    <el-input type="textarea" :rows="4" v-model="appointmentRuleForm.Remark" placeholder="200字以内备注"> </el-input>
                  </el-col>
                </el-form-item>
              </el-form>
            </div>
          </el-scrollbar>
        </el-col>
        <el-col :span="11" :offset="2" class="right_item">
          <div style="height: 400px" class="back_f8 pad_10">
            <el-scrollbar class="el-scrollbar_height" style="height: 100%">
              <div class="dis_flex flex_x_between pad_10 flex_y_center" v-for="(item, index) in appointmentProjectList" :key="index">
                <span>{{ item.Name || item.ProjectName }}</span>
              </div>
            </el-scrollbar>
          </div>
        </el-col>
      </el-row>
      <span slot="footer">
        <el-button @click="appointmentDialogShow = false" size="small">取 消</el-button>
        <el-button type="primary" @click="saveAppointment" size="small" v-prevent-click :loading="saveALoading">保 存 </el-button>
      </span>
    </el-dialog>
    <!--选择项目-->
    <el-dialog v-if="selectProjectDialogState" :visible.sync="selectProjectDialogState" title="选择项目" width="900px" append-to-body>
      <template>
        <el-row>
          <el-col :span="8">
            <el-input placeholder="输入项目名称进行搜索" v-model="filterText" size="small" clearable></el-input>
            <el-scrollbar style="height: 460px" class="martp_5">
              <el-tree class="filter-tree" :data="projectList" show-checkbox node-key="PID" ref="treeRef" accordion highlight-current :props="defaultProps" :default-checked-keys="defaultCheckedKeysApplyApp" :filter-node-method="filterNode" @check="selectApplicableItems" default-expand-all>
                <span slot-scope="{ data }">
                  <span>{{ data.Name }}</span>
                  <el-tag plain class="marlt_5" size="mini" v-if="!data.IsProject">分类</el-tag>
                </span>
              </el-tree>
            </el-scrollbar>
          </el-col>
          <el-col :span="16" class="border_left">
            <el-table size="small" :data="selectedTableData.filter((data) => !filterText || data.Name.toLowerCase().includes(filterText.toLowerCase()))" max-height="500px">
              <el-table-column prop="Name" label="项目名称"></el-table-column>
              <el-table-column label="操作" width="80px">
                <template slot-scope="scope">
                  <el-button type="danger" size="small" @click="deleteSelectRow(scope.$index)">删除 </el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-col>
        </el-row>
      </template>
      <div slot="footer">
        <el-button size="small" @click="selectProjectDialogState = false" v-prevent-click>取 消</el-button>
        <el-button type="primary" size="small" @click="confirmProjectSelect" v-prevent-click>确 认</el-button>
      </div>
    </el-dialog>
    <!--编辑会员信息弹窗  -->
    <el-dialog v-if="dialogVisible" title="编辑客户信息" :visible.sync="dialogVisible" width="1000px" append-to-body custom-class="editCustomInfo">
      <el-scrollbar style="height: 60vh">
        <div>
          <el-row type="flex" justify="space-between" align="middle" class="tip">
            <el-col :span="12">
              <div class="dis_flex flex_x_between flex_y_center margin-bottom">基础信息</div>
            </el-col>
            <el-col v-show="!isCustomerBasicInformationModify || !isCustomerServicerModify" :span="12" class="text_right">
              <el-button v-if="isCustomerBasicInformationModify" size="mini" type="text" style="padding: 0px" @click="submitCustomer">保存</el-button>
            </el-col>
          </el-row>

          <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="110px" size="small" class="">
            <el-row>
              <el-col :span="12">
                <el-form-item label="客户姓名" prop="Name" :rules="getIsRequired('Name')">
                  <el-input v-model="ruleForm.Name" :disabled="infoDisabled || !isCustomerBasicInformationModify"></el-input>
                </el-form-item>
              </el-col>

              <el-col :span="12" v-if="isCustomerPhoneNumberView">
                <el-form-item label="手机号码" prop="PhoneNumber" :rules="getIsRequired('PhoneNumber')">
                  <el-input v-model="ruleForm.PhoneNumber" maxlength="11" :disabled="infoDisabled || !isCustomerPhoneNumberModify || !isCustomerBasicInformationModify"></el-input>
                </el-form-item>
              </el-col>

              <el-col :span="12">
                <el-form-item label="客户性别" class="custom-Gender-Class" prop="Gender" :rules="getIsRequired('Gender')">
                  <el-radio-group v-model="ruleForm.Gender" :disabled="infoDisabled || !isCustomerBasicInformationModify">
                    <el-radio label="2">女</el-radio>
                    <el-radio label="1">男</el-radio>
                    <el-radio label="0">未知</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>

              <el-col :span="12">
                <el-form-item label=" " label-width="1" prop="Birthday" :rules="getIsRequired('Birthday')">
                  <el-row :gutter="10">
                    <el-col :span="7">
                      <el-select v-model="ruleForm.BirthdayType" placeholder="请选择" :disabled="infoDisabled || !isCustomerBasicInformationModify">
                        <el-option label="公历生日" :value="10"></el-option>
                        <el-option label="农历生日" :value="20"></el-option>
                      </el-select>
                    </el-col>
                    <el-col :span="13">
                      <el-date-picker :disabled="infoDisabled || !isCustomerBasicInformationModify" v-model="ruleForm.Birthday" type="date" value-format="yyyy-MM-dd" placeholder="选择日期"> </el-date-picker>
                    </el-col>
                  </el-row>
                </el-form-item>
              </el-col>

              <el-col :span="12">
                <el-form-item label="信息来源" prop="CustomerSourceID" :rules="getIsRequired('CustomerSourceID')">
                  <el-cascader
                    :disabled="infoDisabled || !isCustomerBasicInformationModify"
                    v-model="ruleForm.CustomerSourceID"
                    :options="customerSource"
                    :props="{
                      checkStrictly: true,
                      children: 'Child',
                      value: 'ID',
                      label: 'Name',
                      emitPath: false,
                    }"
                    :show-all-levels="false"
                    filterable
                    clearable
                  ></el-cascader>
                </el-form-item>
              </el-col>

              <el-col :span="12" v-if="isShowChannel">
                <el-form-item label="渠道来源" size="small" prop="ChannelID" :rules="getIsRequired('ChannelID')">
                  <el-select
                    v-model="ruleForm.ChannelID"
                    placeholder="请输入渠道信息搜索客户渠道来源"
                    popper-class="custom_channelPopperClass"
                    filterable
                    remote
                    reserve-keyword
                    size="small"
                    clearable
                    :remote-method="searchChannelInfo"
                    @focus="focusChannel"
                    @clear="focusChannel"
                    :disabled="infoDisabled || !isCustomerBasicInformationModify"
                  >
                    <el-option v-for="item in channelList" :key="item.ID" :label="item.Name" :value="item.ID">
                      <div style="padding-bottom: 8px">
                        <div>{{ item.Name }}</div>
                        <div class="font_12 color_999">渠道类型：{{ item.ChannelType }}</div>
                        <div v-if="item.ParentName" class="font_12 color_999">上级渠道：{{ item.ParentName }}</div>
                        <div v-if="item.ContactPersonName" class="font_12 color_999">联系人名称：{{ item.ContactPersonName }}</div>
                        <div v-if="item.ContactPersonMobile" class="font_12 color_999">联系人手机号：{{ item.ContactPersonMobile }}</div>
                      </div>
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>

              <el-col :span="12">
                <el-form-item label="客户编号" prop="Code" :rules="getIsRequired('Code')">
                  <el-input v-model="ruleForm.Code" :disabled="infoDisabled || !isCustomerBasicInformationModify"></el-input>
                </el-form-item>
              </el-col>

              <el-col :span="12">
                <el-form-item label="身份证号" prop="IdentityCard" :rules="getIsRequired('IdentityCard')">
                  <el-input v-model="ruleForm.IdentityCard" :disabled="infoDisabled || !isCustomerBasicInformationModify"></el-input>
                </el-form-item>
              </el-col>

              <el-col :span="12">
                <el-form-item label="客户介绍人">
                  <el-select
                    v-model="ruleForm.Introducer"
                    placeholder="请选择客户介绍人"
                    filterable
                    remote
                    reserve-keyword
                    size="small"
                    default-first-option
                    v-loadmore="customerIntroducerLoadMore"
                    :remote-method="remoteCusMethod"
                    clearable
                    :disabled="infoDisabled || !isCustomerBasicInformationModify"
                  >
                    <el-option v-for="(item, index) in customerIntroducer" :key="'Introducer' + index + item.ID" :label="item.Name" :value="item.ID - 0">
                      <span style="float: left">{{ item.Name }}</span>
                      <span style="float: right; color: #8492a6; font-size: 13px" v-if="item.PhoneNumber.length == 11">{{ item.PhoneNumber | hidephone }}</span>
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>

              <el-col :span="12">
                <el-form-item label="职业" prop="Job" :rules="getIsRequired('Job')">
                  <el-input v-model="ruleForm.Job" :disabled="infoDisabled || !isCustomerBasicInformationModify"></el-input>
                </el-form-item>
              </el-col>

              <el-col :span="12">
                <el-form-item label="省市区" prop="ProvinceCityArea" :rules="getIsRequired('ProvinceCityArea')">
                  <el-cascader clearable placeholder="请选择省 / 市 / 区" size="small" :options="regionData" v-model="regionDataSelArr" :disabled="infoDisabled || !isCustomerBasicInformationModify" @change="changeProvinceCityArea"> </el-cascader>
                </el-form-item>
              </el-col>

              <el-col :span="12">
                <el-form-item label="详细地址" prop="Address" :rules="getIsRequired('Address')">
                  <el-input style="width: 100%; max-width: unset" v-model="ruleForm.Address" :disabled="infoDisabled || !isCustomerBasicInformationModify"> </el-input>
                </el-form-item>
              </el-col>

              <el-col :span="24">
                <el-form-item label="备注" prop="Remark" :rules="getIsRequired('Remark')">
                  <el-input type="textarea" rows="3" v-model="ruleForm.Remark" :disabled="infoDisabled || !isCustomerBasicInformationModify"></el-input>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row v-show="customerServicer.length != 0" type="flex" justify="space-between" align="middle" class="tip margin-bottom">
              <el-col :span="12">
                <div class="dis_flex flex_x_between flex_y_center margin-bottom">服务人员</div>
              </el-col>

              <el-col v-show="!isCustomerBasicInformationModify || !isCustomerServicerModify" :span="12" class="text_right">
                <el-button v-if="isCustomerServicerModify" size="mini" type="text" style="padding: 0px" @click="submitCustomer">保存</el-button>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12" v-for="item in customerServicer" :key="item.ID">
                <el-form-item :label="item.Name" label-width="110px">
                  <el-select v-model="item.ServicerListArr" placeholder="请选择服务人员" multiple collapse-tags reserve-keyword filterable size="small" default-first-option @change="change" :disabled="infoDisabled || !isCustomerServicerModify">
                    <el-option v-for="serervicer in item.ServicerEmpList" :key="'serve' + item.ID + serervicer.ID" :label="serervicer.Name" :value="serervicer.ID">
                      <span>{{ serervicer.Name }}</span>
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>
      </el-scrollbar>
      <div v-if="!infoDisabled" slot="footer">
        <el-button size="small" @click="dialogVisible = false" v-prevent-click>取 消</el-button>
        <el-button v-if="(customerEntityBool || customInfoEdit) && isCustomerServicerModify && isCustomerBasicInformationModify" type="primary" size="small" @click="submitCustomer" :loading="modalLoading" v-prevent-click>保存 </el-button>
      </div>
    </el-dialog>
    <!-- 设置会员等级 -->
    <el-dialog v-if="setCustomerLevelDialog" title="设置会员等级" :append-to-body="true" :visible.sync="setCustomerLevelDialog" width="500px">
      <el-form :model="customerForm" size="small" label-width="100px">
        <el-form-item label="会员等级：">
          <el-select v-model="customerForm.CustomerLevelID" placeholder="请选择" clearable>
            <el-option v-for="item in customerLevel" :key="item.ID" :label="item.Name" :value="item.ID"> </el-option>
          </el-select>
          <el-checkbox v-model="customerForm.IsLockMemberLevel">锁定会员等级，等级不随成长值而变化</el-checkbox>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="setCustomerLevelDialog = false" size="small">取 消</el-button>
        <el-button type="primary" @click="getCustomerLevel" size="small">保 存</el-button>
      </span>
    </el-dialog>
    <el-dialog v-if="growthValueVisible" title="成长值" append-to-body :visible.sync="growthValueVisible">
      <el-button type="primary" size="small" @click="increaseGrowthVisible = true">增减成长值</el-button>
      <el-table :data="growthData" :cell-style="setGrowthValueStyle" style="margin-top: 10px" size="small" max-height="500px">
        <el-table-column prop="GrowthValue" label="成长值"> </el-table-column>
        <el-table-column prop="CustomerGrowthTypeName" label="成长值变动原因"> </el-table-column>
        <el-table-column prop="CreatedOn" label="变动日期">
          <template slot-scope="scope">
            {{ scope.row.CreatedOn | dateFormat("YYYY-MM-DD HH:mm") }}
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
    <!-- 增减成长值 -->
    <el-dialog v-if="increaseGrowthVisible" title="增减成长值" width="500px" append-to-body :visible.sync="increaseGrowthVisible">
      <el-form size="small" :model="GrowthForm" ref="GrowthRef">
        <el-form-item
          label="增减成长值："
          prop="GrowthValue"
          :GrowthRules="[
            {
              required: true,
              message: '请输入成长值',
              trigger: 'blur',
            },
          ]"
        >
          <el-input type="number" @input="GrowthHandlerChange" class="custom-input-number" v-model="GrowthForm.GrowthValue" placeholder="请输入要加/减的成长值"></el-input>
        </el-form-item>
        <el-form-item label="备注：">
          <el-input style="width: 66%" type="textarea" :rows="2" v-model="GrowthForm.Remark" placeholder="请输入备注"> </el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="increaseGrowthVisible = false" size="small">取 消</el-button>
        <el-button type="primary" @click="updateGrowthClick" size="small">保 存</el-button>
      </span>
    </el-dialog>

    <!--预约提醒-->
    <el-dialog :visible.sync="customerAppointmentDialogVisible" title="提示" width="500px">
      <div>本客户今日已预约，是否继续添加预约</div>

      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="customerAppointmentDialogVisible = false" v-prevent-click>取 消</el-button>
        <el-button type="primary" size="small" @click="customerAppointmentAllDialogVisible = true" v-prevent-click>查看预约记录</el-button>
        <el-button type="primary" size="small" @click="confirmCustomerAppointment" v-prevent-click>确 认</el-button>
      </div>
    </el-dialog>
    <!--客户预约记录-->
    <el-dialog :visible.sync="customerAppointmentAllDialogVisible" title="预约记录" width="1200px">
      <el-table size="small" :data="customerAppointmentAll">
        <el-table-column prop="CustomerName" label="客户姓名"></el-table-column>
        <el-table-column prop="PhoneNumber" label="客户手机号">
          <template slot-scope="scope">
            {{ scope.row.PhoneNumber | hidephone }}
          </template>
        </el-table-column>
        <el-table-column prop="LevelName" label="会员等级"></el-table-column>
        <el-table-column prop="ChannelName" label="渠道"></el-table-column>
        <el-table-column v-if="appointmentTypeList.length > 0" prop="AppointmentTypeName" label="预约类型"> </el-table-column>
        <el-table-column prop="Servicer" label="接待人" width="150px">
          <template slot-scope="scope">
            <el-popover placement="top-start" width="200" trigger="hover">
              <el-descriptions :column="1" size="small" border>
                <el-descriptions-item v-for="item in scope.row.Servicer" :key="'Servicer' + item.ServicerID" :label="item.ServicerName">{{ servicerNames(item.Employee) }}</el-descriptions-item>
              </el-descriptions>
              <div slot="reference" style="text-overflow: ellipsis; white-space: nowrap; overflow: hidden">
                <span v-if="scope.row.Servicer.length > 0">
                  <span v-if="scope.row.Servicer[0].Employee">{{ scope.row.Servicer[0].ServicerName }}：</span>
                  <span>{{ servicerNames(scope.row.Servicer[0].Employee) }}</span>
                </span>
              </div>
            </el-popover>
          </template>
        </el-table-column>
        <el-table-column prop="Status" label="预约状态">
          <template slot-scope="scope">
            {{ scope.row.Status == 10 ? "未到店" : scope.row.Status == 20 ? "已到店" : "已取消" }}
          </template>
        </el-table-column>
        <el-table-column prop="AppointmentDate" label="预约时间"></el-table-column>
        <el-table-column prop="Period" label="预约时长（分钟）"></el-table-column>
        <el-table-column prop="ArrivalDate" label="到店时间"></el-table-column>
        <el-table-column prop="CreatedBy" label="创建人"></el-table-column>
        <el-table-column prop="CreatedOn" label="创建时间"></el-table-column>
        <el-table-column prop="Channel" label="预约来源"></el-table-column>
      </el-table>

      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="customerAppointmentAllDialogVisible = false" v-prevent-click>取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import permission from "@/components/js/permission.js";
import API from "@/api/iBeauty/Appointment/appointmentView";
import appointmentTypeAPI from "@/api/iBeauty/Appointment/appointmentType.js";
//api
import cusAPI from "@/api/CRM/Customer/customer";
import APITagLibrary from "@/api/CRM/Customer/customerTagLibrary";
import APIConfig from "@/api/iBeauty/Appointment/appointmentConfig";
import APIAppointment from "@/api/iBeauty/Appointment/appointmentView";
import APICustomerSource from "@/api/CRM/Customer/customerSource";
import APICustomerLevel from "@/api/CRM/Customer/customerLevel";
import basicFileAPI from "@/api/CRM/Customer/customerBasicFile.js";

import customerbill from "../Customer/customerBill";
import CustomerAccount from "../Customer/customerAccount";
import appointmentRecord from "../Customer/appointmentRecord";
import NursingLog from "../Customer/CustomerNursingLog";
import coustomerFileUpload from "../Customer/coustomerFileUpload";
import electronicMedicalRecord from "../Customer/electronicMedicalRecord";
import followUpRecord from "../Customer/coustomerFollowUpRecord.vue";
import effectComparison from "@/views/CRM/Customer/Components/Customer/effectComparison.vue";
import customerDialogue from "../Customer/customerDialogue";
import APIChannel from "@/api/CRM/Channel/channelInfo";
import APIScene from "@/api/CRM/Customer/customerFileApplicationScene.js";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";

var Enumerable = require("linq");
var dayjs = require("dayjs");
import { regionData } from "element-china-area-data";
import validate from "@/components/js/validate.js";
import date from "@/components/js/date";
export default {
  name: "CustomerDetail",
  props: {
    customerID: {
      type: Number,
      require: true,
    },
    visible: {
      type: Boolean,
      require: true,
    },
    isModifyBelongEntity: {
      type: Boolean,
      require: false,
    },
    isDeleteNursingLog: {
      type: Boolean,
      require: false,
    },
    // isCustomerServicerModify: {
    //   type: Boolean,
    //   require: false,
    // },
    // isCustomerBasicInformationModify: {
    //   type: Boolean,
    //   require: false,
    // },
    // isModifyCustomerLevel: {
    //   type: Boolean,
    //   require: false,
    // },
  },
  /** 监听数据变化   */
  watch: {
    visible: {
      immediate: true,
      handler: function (val) {
        if (val) {
          this.customerDetailVisible = val;
          this.getCustomerInfo();
        }
      },
    },
  },
  /**  引入的组件  */
  components: {
    customerbill,
    CustomerAccount,
    appointmentRecord,
    NursingLog,
    coustomerFileUpload,
    electronicMedicalRecord,
    // Treeselect,
    followUpRecord,
    effectComparison,
    customerDialogue,
  },
  /**  Vue 实例的数据对象**/
  data() {
    return {
      isCustomerServicerModify:false,
      isCustomerBasicInformationModify:false,
      isModifyCustomerLevel:false,
      customerAppointmentAllDialogVisible: false,
      customerAppointmentDialogVisible: false,
      customerAppointmentAll: [],
      customerAppointmentNumber: 0,
      saveALoading: false,
      isCustomerPhoneNumberView: false,
      isCustomerPhoneNumberModify: false,
      editLoading: false,
      infoDisabled: false,
      customerDetailVisible: false,
      growthValueVisible: false,
      increaseGrowthVisible: false,
      isPhotoCompare: false,
      isDeleteFile: false,
      isElectronicMedicalRecord: false,
      isShowChannel: false, //是否展示渠道
      loading: false,
      modalLoading: false,
      dialogDetail: false, //顾客详情弹层
      dialogTag: false, //标签弹层
      setCustomerLevelDialog: false, // 设置会员等级
      tabPane: "",
      customerEntityBool: "", //是否是归属门店
      circleUrl: "https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png", //默认头像
      regionData: regionData,
      customerDetail: {}, //顾客信息

      accountInfo: {}, //顾客存量信息

      customerTag: [], //顾客标签
      tagList: [], //全部标签
      editCustomerTag: [], //顾客修改标签
      tagName: "", //自定义标签输入框值

      appointmentDialogShow: false, //预约弹层
      StartTime: "",
      EndTime: "",
      IsMustEmployee: false,
      interval: "",
      Period: "", //时间间隔
      periodArr: "",
      timeArr: [],
      StartTimeData: "",
      EndTimeData: "",
      currentDate: "",
      employeeList: [], //可预约员工列表
      appointmentProjectList: [], //已选择项目列表
      customerServicer: [], // 服务人员
      channelList: [], // 渠道来源

      // 预约时间配置
      appointmentRuleForm: {
        ID: "", // 预约ID
        CustomerID: "", // 顾客ID
        EmployeeID: "", // 接待人ID
        AppointmentDate: dayjs().format("YYYY-MM-DD"), // 预约日期
        Time: "", // 预约时间
        Type: "", // 接待人状态
        Status: "10", // 审批状态
        Period: "", // 预约时长
        Remark: "", // 备注
      }, // 预约
      appointmentRules: {
        EmployeeID: [{ required: true, message: "请选择接待人", trigger: "change" }],
        AppointmentDate: [{ required: true, message: "请选择预约日期", trigger: "change" }],
        AppointmentTime: [{ required: true, message: "请选择预约时间", trigger: "change" }],
        Type: [{ required: true, message: "请选择接待人状态", trigger: "change" }],
      },
      selectedTableData: [],
      defaultCheckedKeysApplyApp: [],
      selectProjectDialogState: false,
      filterText: "", //过滤项目关键字
      projectList: [], //项目列表
      defaultProps: {
        children: "Child",
        label: "Name",
      },
      expireTimeOption: {
        disabledDate(date) {
          //disabledDate 文档上：设置禁用状态，参数为当前日期，要求返回 Boolean
          return date.getTime() < Date.now() - 24 * 60 * 60 * 1000;
        },
      },

      dialogVisible: false, //编辑会员信息
      customerSource: [], //信息来源
      customerLevel: [], //会员等级
      customerIntroducer: [], //会员介绍人
      employee: [], //营销顾问
      regionDataSelArr: [], //省市区
      CusTotal: 0,
      CusPageNum: 1,
      ruleForm: {
        Name: "",
        PhoneNumber: "",
        Gender: "2",
        CustomerSourceID: null,
        // EmployeeID: [],
        Introducer: "",
        CustomerLevelID: "",
        Code: "",
        BirthdayType: 10,
        Birthday: "",
        ProvinceCode: "",
        CityCode: "",
        AreaCode: "",
        Job: "",
        Address: "",
        IdentityCard: "",
        Remark: "",
        ChannelID: null,
        IsMember: false,
        IsLockMemberLevel: false, // 锁定会员等级
        ProvinceCityArea: [],
      },
      customerForm: {
        CustomerLevelID: "",
        IsLockMemberLevel: false,
      },
      GrowthForm: {
        GrowthValue: "",
        Remark: "",
      },

      rules: {
        Name: [{ required: true, message: "请输入会员名称", trigger: ["blur", "change"] }],
        PhoneNumber: [
          {
            validator: validate.validPhoneNumber,
            trigger: ["blur", "change"],
          },
        ],
        Gender: [{ message: "请选择会员性别", trigger: ["blur", "change"] }],
        Birthday: [{ message: "请选择会员生日", trigger: ["blur", "change"] }],
        CustomerSourceID: [{ message: "请选择会员信息来源", trigger: ["blur", "change"] }],
        ChannelID: [{ message: "请选择会员渠道来源", trigger: ["blur", "change"] }],
        Code: [{ message: "请输入会员编号", trigger: ["blur", "change"] }],
        IdentityCard: [
          {
            required: false,
            message: "请输入身份证号",
            trigger: ["blur", "change"],
          },
          {
            pattern: /(^[1-9]\d{5}(18|19|([23]\d))\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$)|(^[1-9]\d{5}\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{2}$)/,
            message: "请输入合法身份证号",
            trigger: "blur",
          },
        ],
        Job: [{ message: "请输入会员职业", trigger: ["blur", "change"] }],
        ProvinceCityArea: [{ message: "请选择会员地址", trigger: ["blur", "change"] }],
        Address: [{ message: "请输入会员详细地址", trigger: ["blur", "change"] }],
        Remark: [{ message: "请输入备注信息", trigger: ["blur", "change"] }],
      },

      customInfoEdit: "", //顾客信息编辑权限
      openBillState: true, //开单权限
      AppointmentState: true, //预约权限
      customTagLibrary: false, //顾客标签权限
      basicFileTerm: [], //
      serviceLists: [],
      growthData: [], // 成长值

      marketingCloud: [], //营销云的标签数组
      addServicerEmployeeList: [], // 预约服务人员
      appointmentTypeList: [], // 服务类型
      appointmentConfigInfo: {},
      sceneData: [],
      customerBelongEntity: [],
      selectCustomerBelongEntity: null,
    };
  },
  /**计算属性  */
  computed: {
    property() {
      const that = this;
      if (that.customerDetail.ProvinceCode && that.customerDetail.CityCode && that.customerDetail.AreaCode && that.dialogDetail) {
        const first = that.regionData.filter((item) => item.adcode == that.customerDetail.ProvinceCode);
        const second = first[0].districts.filter((item) => item.adcode == that.customerDetail.CityCode);
        const third = second[0].districts.filter((item) => item.adcode == that.customerDetail.AreaCode);
        if (first.length > 0 && second.length > 0 && third.length > 0) {
          return first[0].name + "/" + second[0].name + "/" + third[0].name;
        } else {
          return "";
        }
      } else {
        return "";
      }
    },
  },
  /**  方法集合  */
  methods: {
    /**    */
    handleAppointmentDateChange(){
      let that = this;
      that.appointmentBill_getCustomerAppointmentNumber();
    },
    /**    */
    servicerNames(employees) {
      return employees.map((i) => i.EmployeeName).join(",");
    },
    /**    */
    confirmCustomerAppointment() {
      let that = this;
      that.createAppointment();
    },
    /**   修改所属门店 */
    changeBelongEntityClick() {
      let that = this;
      that.customer_updateCustomerBelongEntity();
    },
    /**  修改所属组织请求  */
    async customer_updateCustomerBelongEntity() {
      let that = this;
      try {
        let params = {
          CustomerID: that.customerID,
          EntityID: that.selectCustomerBelongEntity,
        };
        let res = await cusAPI.customer_updateCustomerBelongEntity(params);
        if (res.StateCode == 200) {
          that.getCustomerDetail();
          that.$emit("refreshCustomerList");
          that.$message.success("修改成功");
          that.$refs.belongEntityRef.doClose();
        } else {
          that.$message.error(res.Message);
        }
      } catch (error) {
        that.$message.error(error);
      }
    },
    /**    */
    changeProvinceCityArea(val) {
      let that = this;
      that.ruleForm.ProvinceCityArea = val;
    },
    /**    */
    getIsRequired(code) {
      let that = this;
      let sceneCode = code;
      if (sceneCode == "CustomerSourceID") {
        sceneCode = "CustomerSource";
      }
      if (sceneCode == "ChannelID") {
        sceneCode = "Channel";
      }
      let item = that.sceneData.find((i) => i.Code == sceneCode);
      if (!item) return null;
      let rules = that.rules[code];
      if (!rules) return null;
      rules[0].required = item.IsRequired;
      return rules;
    },
    /**    */
    getTagType(type) {
      if (type) return type;
      return "info";
    },
    /**    */
    showCustomerInfoDialog() {
      // this.customerInfoVisible = true;
      // this.customerDetail.citySting = "";
      // if(this.customerDetail.ProvinceCode && this.customerDetail.CityCode && this.customerDetail.AreaCode){
      //  this.customerDetail.citySting =   CodeToText[this.customerDetail.ProvinceCode] + CodeToText[this.customerDetail.CityCode] + CodeToText[this.customerDetail.AreaCode]
      // }
    },
    /**  详情弹窗关闭  */
    customerDetailClose() {
      let that = this;
      that.customerDetail = {}; //顾客信息
      that.$emit("update:visible", false);
      this.$emit("close");
    },
    /**  弹窗请求顾客信息  */
    getCustomerInfo() {
      this.tabPane = "0";
      this.AccountInfo();
      this.getCustomerDetail();
      this.customerTagData();
      this.getBasicFile();
      this.getMarketingCloud();
      this.customer_customerBelongEntity();
    },
    /**  设置、升级会员等级   */
    showCustomerLevelDialog(type) {
      this.setCustomerLevelDialog = true;
      switch (type) {
        case 1:
          {
            this.customerForm = {
              CustomerLevelID: this.customerDetail.CustomerLevelID,
              IsLockMemberLevel: this.customerDetail.IsLockMemberLevel,
            };
          }
          break;
        case 2 /**  非会员设置会员  */:
          {
            this.customerForm = {
              CustomerLevelID: "",
              IsLockMemberLevel: false,
            };
          }
          break;
      }
    },
    /**  服务人员处理  */
    getServicerEmpNames(ServicerEmpList) {
      if (!ServicerEmpList) {
        return "";
      }
      return ServicerEmpList.map((val) => (val ? val.Name : "")).join(", ");
    },
    change() {
      this.$forceUpdate();
    },
    /** 顾客详情 弹窗界面关闭前调用   */
    clearCustomerDetailData() {
      let that = this;
      that.tabPane = "0";
      that.$refs.customerAccount.clearAccountData();
      that.$refs.customerbill.clearNetWorkData();
      that.$refs.appointmentRecord.clearAppoinmentRecordData();
      that.$refs.nursingLog.clearListData();
      that.$refs.coustomerFileUpload.claerCoustomerFileUploadData();
      if (this.isElectronicMedicalRecord) {
        that.$refs.electronicMedicalRecord.claerElectronicMedicalRecordData();
      }

      that.$refs.customerDialogue.clearSeachData();
    },
    // 获取顾客信息
    getCustomerDetail() {
      const that = this;
      cusAPI.getCustomerDetail({ CustomerID: that.customerID }).then((res) => {
        if (res.StateCode == 200) {
          that.customerDetail = res.Data;
          const EntityID = JSON.parse(localStorage.getItem("access-user")).EntityID;
          that.customerEntityBool = that.customerDetail.EntityID == EntityID ? true : false;
          // that.selectCustomerBelongEntity = that.customerDetail.EntityID;
        } else {
          that.$$message.error({
            message: res.Message,
            duration: 2000,
          });
        }
      });
    },
    // 编辑顾客
    async showEditDialog(row, disabled) {
      var that = this;
      that.editLoading = true;
      that.infoDisabled = disabled;
      // 获取渠道来源
      await that.getChannelList(row.ChannelName);
      // 获取顾客来源
      await that.CustomerSourceData();
      // 获取顾客等级
      await that.CustomerLevelData();
      // 获取顾客介绍人
      await that.CustomerIntroducer();
      // that.isAdd = false;
      row.EmployeeID = new Array();
      row.BirthdayType = row.BirthdayType - 0;
      row.BirthdayType ? "" : (row.BirthdayType = 10);
      row.IsMember = that.customerDetail.IsMember;
      row.IsLockMemberLevel = that.customerDetail.IsLockMemberLevel;
      that.customerServicer.forEach((servicer) => {
        servicer.ServicerListArr = [];
      });
      row.ServicerEmployee.forEach((item) => {
        that.customerServicer.forEach((servicer) => {
          if (item.ID == servicer.ID) {
            servicer.ServicerListArr = item.ServicerEmpList.map((val) => val.ID);
          }
        });
      });
      row.ProvinceCityArea = [];
      that.ruleForm = Object.assign({}, row);
      that.regionDataSelArr = [row.ProvinceCode, row.CityCode, row.AreaCode];
      that.ruleForm.ProvinceCityArea = that.regionDataSelArr;
      that.editLoading = false;
      that.dialogVisible = true;
    },
    // 顾客来源
    CustomerSourceData: function () {
      var that = this;
      var params = {
        Name: "",
        Active: true,
      };
      APICustomerSource.getCustomerSource(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.customerSource = res.Data;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },
    // 顾客等级
    CustomerLevelData: function () {
      var that = this;
      var params = {
        Name: "",
        Active: true,
      };
      APICustomerLevel.getCustomerLevel(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.customerLevel = res.Data;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },
    // 顾客介绍人
    CustomerIntroducer(value) {
      var that = this;
      var params = {
        Name: value,
        PageNum: that.CusPageNum,
        Active: true,
      };
      cusAPI
        .customerAll(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.customerIntroducer = [...that.customerIntroducer, ...res.List];
            that.CusTotal = res.Total;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },
    // 顾客介绍人加载更多
    customerIntroducerLoadMore() {
      const that = this;
      if (that.customerIntroducer.length < that.CusTotal) {
        that.CusPageNum++;
        that.CustomerIntroducer();
      }
    },
    // 顾客介绍人远程搜索
    remoteCusMethod(value) {
      const that = this;
      that.customerIntroducer = [];
      that.CusPageNum = 1;
      that.CustomerIntroducer(value);
    },
    // 服务人员
    getCustomerServicer() {
      let that = this;
      let params = {};
      cusAPI.getAllCustomerServicer(params).then((res) => {
        if (res.StateCode == 200) {
          that.customerServicer = res.Data;
          that.customerServicer.forEach((item) => {
            item.ServicerListArr = [];
          });
        } else {
          that.$message.error({
            message: res.Message,
            duration: 2000,
          });
        }
      });
    },
    // 编辑顾客保存
    submitCustomer: function () {
      var that = this;
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          that.modalLoading = true;
          let para = Object.assign({}, that.ruleForm);
          if (that.regionDataSelArr.length) {
            para.ProvinceCode = that.regionDataSelArr[0];
            para.CityCode = that.regionDataSelArr[1];
            para.AreaCode = that.regionDataSelArr[2];
          }
          let tempArr = [];
          that.customerServicer.forEach((val) => {
            val.ServicerListArr.forEach((em) => {
              tempArr.push({
                ServicerID: val.ID,
                EmployeeID: em,
              });
            });
          });
          para.ServicerList = tempArr;
          cusAPI
            .updateCustomer(para)
            .then(function (res) {
              if (res.StateCode === 200) {
                that.$message.success({
                  message: "编辑成功",
                  duration: 2000,
                });
                that.customerDetail = Object.assign({}, res.Data);
                that.$refs["ruleForm"].resetFields();
                that.getCustomerDetail();
                that.dialogVisible = false;
                that.$emit("refreshCustomerList");
              } else {
                that.$message.error({
                  message: res.Message,
                  duration: 2000,
                });
              }
            })
            .finally(function () {
              that.modalLoading = false;
            });
        }
      });
    },
    /**  切换  */
    handleClick() {
      var that = this;
      var tabPane = this.tabPane;
      switch (tabPane) {
        case "1":
          that.$refs.customerAccount.customerID = that.customerID;
          that.$refs.customerAccount.activeName = "0";
          that.$refs.customerAccount.handleClick();
          break;
        case "2":
          that.$refs.customerbill.customerID = that.customerID;
          that.$refs.customerbill.searchSaleBill();
          that.$refs.customerbill.clearSearchData();
          break;
        case "4":
          that.$refs.appointmentRecord.customerID = that.customerID;
          that.$refs.appointmentRecord.getAppointmentRecordList();
          break;
        case "5":
          that.$refs.nursingLog.customerID = that.customerID;
          that.$refs.nursingLog.clearListData();
          that.$refs.nursingLog.getNursingLogList();
          break;
        case "6":
          that.$refs.coustomerFileUpload.customerID = that.customerID;
          that.$refs.coustomerFileUpload.claerCoustomerFileUploadData();
          that.$refs.coustomerFileUpload.getCoustomerFileUploadData();
          break;
        case "7":
          that.$refs.electronicMedicalRecord.CustomerID = that.customerID;
          that.$refs.electronicMedicalRecord.claerElectronicMedicalRecordData();
          that.$refs.electronicMedicalRecord.getElectronicMedicalRecordData();
          break;
        case "8":
          that.$refs.followUpRecord.CustomerID = that.customerID;
          that.$refs.followUpRecord.getCustomerFollowUp();
          break;
        case "9":
          that.$refs.effectComparison.CustomerID = that.customerID;
          that.$refs.effectComparison.photoCompare();
          that.$refs.effectComparison.clearData();
          break;
        case "10":
          that.$refs.customerDialogue.CustomerID = that.customerID;
          that.$refs.customerDialogue.clearSeachData();
          that.$refs.customerDialogue.getCustomerRecord();
          break;
      }
    },

    /** 顾客 开单   */
    navCreateBill() {
      var that = this;
      that.customerDetailVisible = false;
      that.$emit("navigatorToSell");
      that.$router.push({
        path: "/Order/Bill",
        name: "Bill",
        params: { customerID: that.customerID },
      });
    },
    // 预约
    clickAppointment() {
      var that = this;
      that.appointmentRuleForm.ID = "";
      that.appointmentRuleForm.CustomerID = "";
      that.appointmentRuleForm.EmployeeID = "";
      that.appointmentRuleForm.AppointmentDate = dayjs().format("YYYY-MM-DD");
      that.appointmentRuleForm.Time = "";
      that.appointmentRuleForm.Type = "10";
      that.appointmentRuleForm.Status = "10";
      that.appointmentRuleForm.Period = that.timeArr[0].value;
      that.appointmentRuleForm.Remark = "";
      that.appointmentProjectList = [];
      
      that.appointmentBill_getCustomerAppointmentNumber();
      // 获取可预约员工列表
      that.appointmentBill_ervicerEmployee();
      // 获取项目列表
      that.getProjectList();
      that.appointmentType_all();

      that.appointmentDialogShow = true;
    },

    // 获取预约配置
    getAppointmentConfig() {
      var that = this;
      that.loading = true;
      var params = {};
      var period = 0;
      var dataTime = [];
      APIConfig.appointmentConfig(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.appointmentConfigInfo = res.Data;
            that.StartTime = res.Data.StartTime;
            that.EndTime = res.Data.EndTime;
            that.IsMustEmployee = res.Data.IsMustEmployee;
            that.interval = res.Data.Period;
            that.Period = "00:" + res.Data.Period;
            var number = parseInt(60 / res.Data.Period);
            for (let i = 0; i <= number - 1; i++) {
              period += res.Data.Period;
              dataTime.push(period);
            }
            that.periodArr = dataTime;

            that.getDateAppointmentIntervalList(res.Data.Period);
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },
    // 获取预约时长
    getDateAppointmentIntervalList(period) {
      var that = this;
      var startTimestamp = new Date(that.currentDate + " " + "00:" + period).getTime();
      var endTimestamp = new Date(that.currentDate + " " + "08:00").getTime();
      var periodTimestamp = Number(period) * 60 * 1000; // 间隔的毫秒
      var interValue = period;
      for (var index = startTimestamp; index <= endTimestamp; index += periodTimestamp) {
        if (interValue < 60) {
          that.timeArr.push({
            time: date.formatDate.format(new Date(index), "mm分钟"), // 时间轴显示字符串
            value: interValue,
          });
        } else {
          that.timeArr.push({
            time: date.formatDate.format(new Date(index), "h小时mm分钟"), // 时间轴显示字符串
            value: interValue,
          });
        }
        interValue += period;
      }
    },
    // 获取可预约员工列表
    // getEmployeeList() {
    //   var that = this;
    //   that.loading = true;
    //   var params = {};
    //   APIAppointment.getEmployeeList(params)
    //     .then((res) => {
    //       if (res.StateCode == 200) {
    //         that.employeeList = res.Data;
    //       } else {
    //         that.$message.error({
    //           message: res.Message,
    //           duration: 2000,
    //         });
    //       }
    //     })
    //     .finally(function () {
    //       that.loading = false;
    //     });
    // },
    // 获取项目列表
    getProjectList() {
      var that = this;
      that.loading = true;
      var params = {
        Name: "",
      };
      APIAppointment.getProjectList(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.setRecursion(res.Data);
            // that.projectList = res.Data;
            that.projectList = Enumerable.from(res.Data)
              .where((i) => {
                if (!i.IsProject) {
                  i.Child = Enumerable.from(i.Child)
                    .where((i) => {
                      return !i.IsProject && i.Child.length > 0;
                    })
                    .toArray();
                }
                return !i.IsProject && i.Child.length > 0;
              })
              .toArray();
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },
    // 递归
    setRecursion(data) {
      var that = this;
      for (let i = 0; i <= data.length - 1; i++) {
        if (data[i].IsProject) {
          data[i].PID = "1" + data[i].ID;
        } else {
          data[i].PID = "0" + data[i].ID;
        }
        if (data[i].Child) {
          that.setRecursion(data[i].Child);
        }
      }
    },
    // 选择时间
    getTimeArr() {
      var that = this;
      var currentDate = new Date();

      let selTime = new Date(date.formatDate.format(that.appointmentRuleForm.AppointmentDate, "YYYY-MM-DD")).getTime();

      let currentTime = new Date(date.formatDate.format(currentDate, "YYYY-MM-DD")).getTime();

      if (selTime == currentTime) {
        // 今天
        let minutes = parseInt(date.formatDate.format(currentDate, "mm"));
        var startIndex = parseInt(minutes / that.interval);
        var tempMinutes = that.periodArr[startIndex];

        if (tempMinutes == 60) {
          let hour = date.formatDate.format(new Date(new Date().setHours(new Date().getHours() + 1)), "hh");
          that.StartTimeData = hour + ":" + "00";
          that.EndTimeData = that.EndTime;
        } else {
          let hours = date.formatDate.format(currentDate, "hh");
          that.StartTimeData = hours + ":" + tempMinutes;
          that.EndTimeData = that.EndTime;
        }
      } else if (selTime > currentTime) {
        that.StartTimeData = that.StartTime;
        that.EndTimeData = that.EndTime;
      }
    },
    // 选择时间确认
    confirmTime(DateTime) {
      var that = this;
      var dateArr = DateTime.split(":");
      var currentDate = "";
      var dateTime = "";
      currentDate = Number(dateArr[0]) >= 10 ? Number(dateArr[0]) + ":" + (Number(dateArr[1]) >= 10 ? Number(dateArr[1]) : `0` + Number(dateArr[1])) : `0${Number(dateArr[0])}` + ":" + (Number(dateArr[1]) >= 10 ? Number(dateArr[1]) : `0` + Number(dateArr[1]));

      var AppointmentDate = date.formatDate.format(that.appointmentRuleForm.AppointmentDate, "YYYY-MM-DD").split(" ");
      dateTime = AppointmentDate[0] + " " + currentDate + ":00";
      that.appointmentRuleForm.AppointmentDate = dateTime;
    },
    // 添加预约项目
    addAppointmentProject() {
      var that = this;
      that.selectedTableData = Object.assign([], that.appointmentProjectList);
      that.defaultCheckedKeysApplyApp = [];
      that.selectProjectDialogState = true;
      that.$nextTick(() => {
        var defaultCheckedKeys = Enumerable.from(that.appointmentProjectList)
          .select((val) => "1" + val.ID)
          .toArray();
        that.$refs.treeRef.setCheckedKeys(defaultCheckedKeys);
      });
    },
    // 适用项目弹框搜索事件
    filterNode(value, data) {
      if (!value) return true;
      return data.Name.indexOf(value) !== -1;
    },
    // 选择适用项目事件
    selectApplicableItems(item, list) {
      var that = this;
      var timeCardProject = Enumerable.from(list.checkedNodes)
        .where(function (i) {
          return i.IsProject;
        })
        .select((item) => ({
          ID: item.ID,
          Name: item.Name,
          PID: item.PID,
          ParentID: item.ParentID,
          Price: item.Price,
          ProjectCategoryName: item.ProjectCategoryName,
          TreatTime: item.TreatTime,
        }))
        .toArray();
      that.selectedTableData = timeCardProject;
    },
    // 删除所选中的适用项目
    deleteSelectRow(index) {
      var that = this;
      // var ParentID = row.ParentID;
      that.selectedTableData.splice(index, 1);
      that.$nextTick(() => {
        var defaultCheckedKeys = Enumerable.from(that.selectedTableData)
          .select((val) => val.PID)
          .toArray();
        that.$refs.treeRef.setCheckedKeys(defaultCheckedKeys);
      });
    },
    // 选择项目确认
    confirmProjectSelect() {
      var that = this;
      // var appointmentProjectList = Object.assign([], that.appointmentProjectList);
      var selectedTableData = Object.assign([], that.selectedTableData);
      if (that.selectedTableData.length == 0) {
        that.$message.error("请选择项目");
        return false;
      } else {
        var totalPeriod = 0;
        that.appointmentProjectList = Object.assign([], selectedTableData);
        that.selectProjectDialogState = false;
        that.appointmentProjectList.forEach((val) => {
          totalPeriod += val.TreatTime;
        });
        for (let i = 0; i <= that.timeArr.length - 1; i++) {
          if (that.timeArr[i].value >= totalPeriod) {
            that.appointmentRuleForm.Period = that.timeArr[i].value;
            break;
          }
        }
      }
    },
    /**  获取产品是否显示提示  */
    getShowProductNameTooltip(row) {
      if (row && row.length >= 1) {
        return !row.some((i) => i.ServicerEmpList.length >= 1);
      } else {
        return false;
      }
    },
    // 保存预约
    saveAppointment() {
      var that = this;
      if (!that.appointmentRuleForm.Time) {
        that.$message.error("请选择预约时间");
        return;
      }
      that.$refs["appointmentRuleForm"].validate((valid) => {
        if (valid) {
          if (that.customerAppointmentNumber > 0) {
            that.customerAppointmentDialogVisible = true;
          } else {
            that.createAppointment();
          }
        }
      });
    },
    // 创建预约
    createAppointment() {
      var that = this;
      that.saveALoading = true;
      let params = {
        AppointmentDate: that.appointmentRuleForm.AppointmentDate + " " + that.appointmentRuleForm.Time, //预约时间
        CustomerID: that.customerID, //顾客iD
        Period: that.appointmentRuleForm.Period, //时长
        AppointmentTypeID: that.appointmentRuleForm.AppointmentTypeID, //预约类型
        Remark: that.appointmentRuleForm.Remark, //备注
        Servicer: that.addServicerEmployeeList
          .filter((i) => i.SelectEmployeeID)
          .map((val) => {
            return {
              ServicerID: val.ServicerID,
              EmployeeID: val.SelectEmployeeID,
            };
          }), //预约角色集合

        Project: that.appointmentProjectList.map((val) => {
          return {
            ProjectID: val.ID,
          };
        }), //项目集合
      };

      APIAppointment.appointmentBillCreate(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.$message.success({
              message: "成功创建预约",
              duration: 2000,
            });
            that.appointmentDialogShow = false;
            that.customerAppointmentAllDialogVisible = false;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.saveALoading = false;
        });
    },
    beforeAvatarUpload(file) {
      let that = this;
      let isFileType = false;
      if (file.type === "image/jpg" || file.type === "image/png" || file.type === "image/jpeg") {
        isFileType = true;
      }
      // const isLt2M = file.size / 1024 < 200;

      if (!isFileType) {
        this.$message.error("上传头像图片只能是 JPG 格式!");
        return false;
      }
      // if (!isLt2M) {
      //   that.$message.error("上传图片大小不能超过 200kb!");
      //   return false;
      // }

      let reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = function (evt) {
        let base64 = evt.target.result;
        that.updateCustomerUploadImage(base64);
      };
      return false;
    },
    /**  上传头像请求  */
    updateCustomerUploadImage(base64) {
      let that = this;
      let params = {
        CustomerImage: base64,
        CustomerID: that.customerID,
      };
      cusAPI
        .updateCustomerUploadImage(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.customerDetail.Avatar = res.Message;
          }
        })
        .finally(() => {});
    },
    /**  列表顾问格式化  */
    formatterEmployeeNames(row) {
      return Enumerable.from(row.service)
        .select((i) => i.service)
        .toArray()
        .join("、");
    },
    // 顾客存量余额信息
    AccountInfo() {
      const that = this;
      cusAPI.AccountInfo({ CustomerID: that.customerID }).then((res) => {
        if (res.StateCode == 200) {
          that.accountInfo = res.Data;
        } else {
          this.$message.error({
            message: res.Message,
            duration: 2000,
          });
        }
      });
    },
    // 顾客标签
    customerTagData: function () {
      var that = this;
      var params = {
        ID: that.customerID,
      };
      cusAPI
        .getCustomerTag(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.customerTag = res.Data;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },
    // 标签列表
    tagData: function () {
      var that = this;
      APITagLibrary.customerTagLibraryAll()
        .then((res) => {
          if (res.StateCode == 200) {
            that.tagList = res.Data;
            that.tagType();
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },
    // 标签
    tagClick: function () {
      var that = this;
      // 全部标签列表
      that.tagData();
      that.editCustomerTag = Object.assign([], that.customerTag);
      that.tagType();
      that.dialogTag = true;
    },
    tagType: function () {
      var that = this;
      that.tagList.forEach(function (item) {
        item.type = "info";
        that.editCustomerTag.forEach(function (tag) {
          if (item.ID == tag.ID) {
            item.type = "primary";
          }
        });
      });
    },
    // 删除标签
    removeTag: function (index) {
      var that = this;
      that.editCustomerTag.splice(index, 1);
      that.tagType();
    },
    // 添加标签
    addTagClick: function () {
      var that = this;
      var params = {
        Name: that.tagName,
      };
      APITagLibrary.customerTagLibraryCreate(params)
        .then(function (res) {
          if (res.StateCode === 200) {
            that.editCustomerTag.push(res.Data);
            that.tagList.push(res.Data);
            that.tagType();
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.modalLoading = false;
        });
    },
    // 选择标签
    tagSelectClick: function (row) {
      var that = this;
      if (row.type == "info") {
        that.editCustomerTag.push(row);
      }
      that.tagType();
    },
    // 标签保存
    tagSaveClick: function () {
      var that = this;
      that.modalLoading = true;
      var TagLibrary = Enumerable.from(that.editCustomerTag)
        .select((val) => val.ID)
        .toArray();
      var params = {
        ID: that.customerID,
        TagLibrary: TagLibrary,
      };
      cusAPI
        .updateCustomerTagLibrary(params)
        .then(function (res) {
          if (res.StateCode === 200) {
            that.dialogTag = false;
            that.customerTagData();
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.modalLoading = false;
        });
    },

    /**  获取选择项   */
    getComponentsProperty(property) {
      if (!property) return [];
      return JSON.parse(property);
    },
    /**  获取选择项value   */
    getPropertyValue(value) {
      if (!value) return [];
      return JSON.parse(value);
    },
    /**   获取基本信息档案项   */
    async getBasicFile() {
      let that = this;
      let params = {
        CustomerID: that.customerID, //顾客ID
      };
      let res = await basicFileAPI.getBasicFile(params);
      if (res.StateCode == 200) {
        that.basicFileTerm = res.Data.map((val) => {
          if (val.Type == 40 && typeof val.Value == "string") {
            val.Value = val.Value ? JSON.parse(val.Value) : [];
          }
          return val;
        });
      } else {
        that.$message.error(res.Message);
      }
    },
    /**   basicFileAPI   */
    async createBasicFile() {
      let that = this;
      let detail = that.basicFileTerm.map((val) => {
        let Value = "";
        if (val.Type == 40) {
          Value = JSON.stringify(val.Value);
        } else {
          Value = val.Value ? val.Value : "";
        }
        return {
          CustomerBasicFileID: val.CustomerBasicFileID,
          Value: Value,
        };
      });
      let params = {
        CustomerID: that.customerID,
        detail: detail,
      };
      let res = await basicFileAPI.createBasicFile(params);
      if (res.StateCode == 200) {
        that.$message.success("保存成功");
      } else {
        that.$message.error(res.Message);
      }
    },
    /**   保存档案信息 */
    saveBasicFileClick() {
      let that = this;
      that.createBasicFile();
    },
    /**  渠道来源获取焦点 清除数据  */
    focusChannel() {
      let that = this;
      that.channelList = [];
    },
    /**    */
    searchChannelInfo(value) {
      this.getChannelList(value);
    },
    /* 获取渠道来源 */
    getChannelList(value) {
      let that = this;
      let params = { Name: value, CustomerID: that.customerDetail.ID, Active: true };
      APIChannel.channel_customerInfo(params).then((res) => {
        if (res.StateCode == 200) {
          that.channelList = res.Data;
        }
      });
    },
    /* 树形结构数据转换 */
    normalizer(node) {
      return {
        id: node.ID,
        label: node.Name,
        children: node.Child,
      };
    },
    /* 设置会员等级*/
    async getCustomerLevel() {
      let params = {
        CustomerID: this.customerID,
        CustomerLevelID: this.customerForm.CustomerLevelID,
        IsLockMemberLevel: this.customerForm.IsLockMemberLevel,
      };
      let res = await cusAPI.setCustomerLevel(params);
      if (res.StateCode == 200) {
        this.$message.success("设置成功");
        this.setCustomerLevelDialog = false;
        this.getCustomerDetail();
        this.$emit("refreshCustomerList");
      } else {
        this.$message.error(res.Message);
      }
    },
    /* 移除会员等级*/
    deleteCustomerLevel() {
      var that = this;
      this.$confirm("确定移除会员等级吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          let params = {
            CustomerID: that.customerID,
          };
          cusAPI.deleteCustomerLevel(params).then((res) => {
            if (res.StateCode == 200) {
              this.$message.success("移除成功");
              this.getCustomerDetail();
              this.$emit("refreshCustomerList");
            } else {
              this.$message.error({
                message: res.Message,
                duration: 2000,
              });
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    /**  查看会员成长值  */
    async checkGrowthValue() {
      // checkGrowthValue
      let params = {
        CustomerID: this.customerID,
      };
      let res = await cusAPI.getGrowthData(params);
      if (res.StateCode == 200) {
        this.growthData = res.Data;
        this.growthValueVisible = true;
      } else {
        this.$message.error(res.Message);
      }
    },
    /* 增减成长值*/
    updateGrowthClick() {
      this.$refs.GrowthRef.validate(async (valid) => {
        if (!valid) return;
        let params = {
          CustomerID: this.customerID,
          GrowthValue: this.GrowthForm.GrowthValue,
          Remark: this.GrowthForm.Remark,
        };
        let res = await cusAPI.updateGrowth(params);
        if (res.StateCode == 200) {
          this.$message.success("保存成功");
          this.increaseGrowthVisible = false;
          this.GrowthForm.GrowthValue = "";
          this.GrowthForm.Remark = "";
          this.checkGrowthValue();
          this.getCustomerDetail();
        } else {
          this.$message.error(res.Message);
        }
      });
    },
    /* 设置成长值样式 */
    setGrowthValueStyle({ row, columnIndex }) {
      if (row.GrowthValue >= 0 && columnIndex == 0) {
        return "color:green";
      } else if (row.GrowthValue < 0 && columnIndex == 0) {
        return "color:red";
      }
    },
    /*成长值change*/
    GrowthHandlerChange(val) {
      this.GrowthForm.GrowthValue = val.replace(/\./g, "");
    },

    //获取营销云的下列标签
    getMarketingCloud() {
      //2063
      let params = { CustomerID: this.customerID };
      cusAPI.getMarketingCloud(params).then((res) => {
        if (res.StateCode == 200) {
          this.marketingCloud = res.Data;
        } else {
          this.$message.error({
            message: res.Message,
            duration: 2000,
          });
        }
      });
    },
    /** 服务人员员工 -新增修改时用  */
    async appointmentBill_ervicerEmployee() {
      let that = this;
      try {
        let params = {
          AppointmentDate: that.appointmentRuleForm.AppointmentDate,
        };
        let res = await API.appointmentBill_ervicerEmployee(params);
        if (res.StateCode == 200) {
          that.addServicerEmployeeList = res.Data;
        } else {
          that.$message.error(res.Message);
        }
      } catch (error) {
        that.$message.error(error);
      }
    },
    /**  获取预约类型  */
    async appointmentType_all() {
      let that = this;
      try {
        let params = {
          Name: "", //搜索名称
          Active: true, //有效性
        };
        let res = await appointmentTypeAPI.appointmentType_all(params);
        if (res.StateCode == 200) {
          that.appointmentTypeList = res.Data;
        } else {
          that.$message.error(res.Message);
        }
      } catch (error) {
        that.$message.error(error);
      }
    },

    /* 获取顾客档案配置 */
    async customerFileApplicationScene_all() {
      let that = this;
      that.loading = true;
      try {
        let params = {};
        let res = await APIScene.customerFileApplicationScene_all(params);
        if (res.StateCode == 200) {
          that.sceneData = res.Data;
        } else {
          that.$message.error(res.Message);
        }
        that.loading = false;
      } catch (error) {
        that.loading = false;
        that.$message.error(error);
      }
    },
    /**    */
    async customer_customerBelongEntity() {
      let that = this;
      try {
        let params = {
          CustomerID: this.customerID,
        };
        let res = await cusAPI.customer_customerBelongEntity(params);
        if (res.StateCode == 200) {
          that.customerBelongEntity = res.Data;
        } else {
          that.$message.error(res.Message);
        }
      } catch (error) {
        that.$message.error(error);
      }
    },
    /**    */
    appointmentBill_getCustomerAppointmentNumber() {
      let that = this;
      let params = {
        CustomerID: that.customerID,
        AppointmentDate:that.appointmentRuleForm.AppointmentDate,
      };
      API.appointmentBill_getCustomerAppointmentNumber(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.customerAppointmentNumber = res.Data.CustomerAppointmentNumber;
            if (that.customerAppointmentNumber > 0) {
              that.appointmentBill_getCustomerAppointmentAll();
            }
          } else {
            that.$message.error(res.Message);
          }
        })
        .catch((fail) => {
          that.$message.error(fail);
        });
    },
    /**    */
    appointmentBill_getCustomerAppointmentAll() {
      let that = this;
      let params = {
        CustomerID: that.customerID,
        AppointmentDate:that.appointmentRuleForm.AppointmentDate,
      };
      API.appointmentBill_getCustomerAppointmentAll(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.customerAppointmentAll = res.Data;
          } else {
            that.$message.error(res.Message);
          }
        })
        .catch((fail) => {
          that.$message.error(fail);
        });
    },
  },
  /** 创建实例之前执行函数   */
  beforeCreate() {},
  /**  实例创建完成之后  */
  created() {},
  /**  在挂载开始之前被调用  */
  beforeMount() {},
  /**  实例被挂载后调用  */
  mounted() {
    const that = this;
    // 开单权限
    that.openBillState = permission.routerPermission("/Order/Bill");
    // 预约权限
    that.AppointmentState = permission.routerPermission("/Appointment/AppointmentView");
    // 顾客信息编辑权限
    that.customInfoEdit = permission.getCustomerDetailPermission(this.$router, "iBeauty-Customer-Customer-ModifyNotBelongEntityCustomer");
    // 自定义标签权限
    that.customTagLibrary = permission.getCustomerDetailPermission(this.$router, "iBeauty-Customer-Customer-CustomTagLibrary");
    // 顾客详情渠道
    that.isShowChannel = that.$permission.getCustomerDetailPermission(this.$router, "iBeauty-Customer-Customer-Channel");
    /**  删除文件档案中文件权限  */
    that.isDeleteFile = permission.getCustomerDetailPermission(this.$router, "iBeauty-Customer-Customer-DeleteFile");
    /**  电子病例权限  */
    that.isElectronicMedicalRecord = that.$permission.getCustomerDetailPermission(this.$router, "iBeauty-Customer-Customer-ElectronicMedicalRecord");
    /* 是否可查看手机号 */
    that.isCustomerPhoneNumberView = that.$permission.getCustomerDetailPermission(this.$router, "iBeauty-Customer-Customer-CustomerPhoneNumberView");
    /* 是否修改手机号 */
    that.isCustomerPhoneNumberModify = that.$permission.getCustomerDetailPermission(this.$router, "iBeauty-Customer-Customer-CustomerPhoneNumberModify");

    that.isPhotoCompare = that.$permission.getCustomerDetailPermission(this.$router, "iBeauty-Customer-Customer-PhotoCompare");


    
    that.isModifyCustomerLevel = that.$permission.getCustomerDetailPermission(this.$router, "iBeauty-Customer-Customer-ModifyCustomerLevel");
    
    that.isCustomerBasicInformationModify = that.$permission.getCustomerDetailPermission(this.$router, "iBeauty-Customer-Customer-ModifyCustomerBasicInformation");
    
    that.isCustomerServicerModify = that.$permission.getCustomerDetailPermission(this.$router, "iBeauty-Customer-Customer-ModifyCustomerServicer");
    /*      */
    that.getChannelList();
    // 获取服务人员
    that.getCustomerServicer();
    // 获取预约配置
    that.getAppointmentConfig();
    // 获取顾客等级
    that.CustomerLevelData();
    that.customerFileApplicationScene_all();
    var time = new Date();
    that.currentDate = date.formatDate.format(new Date(time), "YYYY-MM-DD");
  },
  /**  数据更新 之前 调用  */
  beforeUpdate() {},
  /** 数据更新 完成 调用   */
  updated() {},
  /**  实例销毁之前调用  */
  beforeDestroy() {},
  /**  实例销毁后调用  */
  destroyed() {},
};
</script>

<style lang="scss">
.custom-customer-descLabel {
  min-width: 80px;
  text-align: right;
  padding-right: 10px;
}

.CustomerDetail {
  .customer_information {
    background-color: #f5f7fa;
    padding: 16px;
  }
  .custom-customer-detail {
    .el-form-item {
      margin-bottom: unset;
    }
    .el-form-item--small.el-form-item {
      margin-bottom: unset;
    }
  }
}

.vue-treeselect__control {
  height: 32px;

  .vue-treeselect__value-container {
    min-width: 215px;
  }
}

.vue-treeselect__placeholder,
.vue-treeselect__single-value {
  line-height: 32px;
}

.editCustomInfo {
  .el-scrollbar {
    .el-scrollbar__wrap {
      overflow-x: hidden;
    }

    .el-scrollbar__bar {
      opacity: 0;
    }

    .custom-Gender-Class {
      .el-form-item__content {
        line-height: 33px;
      }
    }
  }
}

.showCustomInfo {
  .el-scrollbar {
    .el-scrollbar__wrap {
      overflow-x: hidden;
    }

    .el-scrollbar__bar {
      opacity: 0;
    }
  }

  .el-form-item {
    margin-bottom: 8px;
  }
}

.LevelCustomer {
  background-color: #eee;
  border-radius: 10px;
  padding: 2px 10px;
  box-sizing: border-box;
  width: 104px;
  cursor: pointer;
}

.custom_channelPopperClass {
  .el-select-dropdown__item {
    line-height: normal;
    height: auto;
  }
}
.custom-avatar {
  img {
    width: 100%;
  }
}
</style>
