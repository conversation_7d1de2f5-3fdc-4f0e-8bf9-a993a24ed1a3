<template>
  <div class="customerCallBackLog">
    <el-table size="small" :data="tableData" height="40vh">
      <el-table-column prop="CallID" label="通话编号" width="150px"></el-table-column>
      <el-table-column prop="CustomerName" label="顾客名称"></el-table-column>
      <el-table-column prop="Caller" label="主叫手机号" width="150px"></el-table-column>
      <el-table-column prop="Callee" label="被叫手机号" width="150px"></el-table-column>

      <el-table-column prop="VideoID" label="接通状态">
        <template slot-scope="scope">
          {{ scope.row.IsConnected == "1" ? "已接通" : "未接通" }}
        </template>
      </el-table-column>

      <el-table-column prop="Duration" label="通话时长"></el-table-column>
      <el-table-column prop="Minutes" label="计费时长（分钟）"></el-table-column>
      <el-table-column prop="CreateTime" label="请求时间" width="150px"></el-table-column>
      <el-table-column prop="StartTime" label="话单开始时间" width="150px"></el-table-column>
      <el-table-column prop="EndTime" label="话单截止时间" width="150px"></el-table-column>
      <el-table-column prop="Record" label="录音地址" width="200px">
        <template slot-scope="scope">
          <el-popover placement="top-start" width="250" trigger="hover" :content="scope.row.Record">
            <div slot="reference" class="clamp1">{{ scope.row.Record }}</div>
          </el-popover>
        </template>
      </el-table-column>
    </el-table>

    <div class="text_right pad_15">
      <el-pagination background v-if="paginations.total > 0" @current-change="handleCurrentChange" :current-page.sync="paginations.page" :page-size="paginations.page_size" :layout="paginations.layout" :total="paginations.total"></el-pagination>
    </div>
  </div>
</template>

<script>
import API from "@/api/CRM/Customer/customer.js";
export default {
  name: "customerCallBackLog",
  props: {
    CustomerID: {
      type: Number,
      require: true,
    },
  },
  /** 监听数据变化   */
  watch: {},
  /**  引入的组件  */
  components: {},
  /**  Vue 实例的数据对象**/
  data() {
    return {
      tableData: [],
      paginations: {
        page: 1, // 当前位于哪页
        total: 0, // 总数
        page_size: 10, // 1页显示多少条
        layout: "total, prev, pager, next,jumper", // 翻页属性
      }, //需要给分页组件传的信息
    };
  },
  /**计算属性  */
  computed: {},
  /**  方法集合  */
  methods: {
    /**    */
    handleCurrentChange(page) {
      let that = this;
      that.paginations.page = page;
      that.phoneCallBack_callBackLog();
    },
    /**    */
    clearTableData() {
      let that = this;
      that.tableData = [];
    },
    /**    */
    phoneCallBack_callBackLog() {
      let that = this;
      let params = {
        PageNum: that.paginations.page, //页码
        CustomerID: that.CustomerID, //顾客编号
      };
      API.phoneCallBack_callBackLog(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.tableData = res.List;
            that.paginations.total = res.Total;
            that.paginations.page_size = res.PageSize;
          } else {
            that.$message.error(res.Message);
          }
        })
        .catch((fail) => {
          that.$message.error(fail);
        });
    },
  },
  /** 创建实例之前执行函数   */
  beforeCreate() {},
  /**  实例创建完成之后  */
  created() {},
  /**  在挂载开始之前被调用  */
  beforeMount() {},
  /**  实例被挂载后调用  */
  mounted() {},
  /**  数据更新 之前 调用  */
  beforeUpdate() {},
  /** 数据更新 完成 调用   */
  updated() {},
  /**  实例销毁之前调用  */
  beforeDestroy() {},
  /**  实例销毁后调用  */
  destroyed() {},
};
</script>

<style lang="scss">
.customerCallBackLog {
}
</style>
