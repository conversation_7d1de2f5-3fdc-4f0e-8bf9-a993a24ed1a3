<template>
  <div class="content_body" v-loading="loading">
    <div class="nav_header">
      <el-row>
        <el-col :span="20">
          <el-form :inline="true" size="small" @submit.native.prevent @keyup.enter.native="handleSearch">
            <el-form-item label="角色名称">
              <el-input v-model="name" size="small" placeholder="输入角色名称" clearable @clear="handleSearch"></el-input>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" size="small" @click="handleSearch" v-prevent-click>搜索</el-button>
            </el-form-item>
          </el-form>
        </el-col>
        <el-col :span="4" class="text_right">
          <el-button type="primary" size="small" @click="showAddDialog" v-prevent-click>新增</el-button>
        </el-col>
      </el-row>
    </div>

    <div>
      <el-table size="small" :data="tableData">
        <el-table-column prop="Name" label="角色名称"></el-table-column>
        <el-table-column label="操作" width="80">
          <template slot-scope="scope">
            <el-button type="primary" size="small" @click="showEditDialog(scope.row)" v-prevent-click>编辑</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="pad_15 text_right">
        <el-pagination
          background
          v-if="paginations.total > 0"
          @current-change="handleCurrentChange"
          :current-page.sync="paginations.page"
          :page-size="paginations.page_size"
          :layout="paginations.layout"
          :total="paginations.total"
        ></el-pagination>
      </div>
    </div>
    <!--增加、编辑弹出框-->
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="1300px" @closed="dialogClosed">
      <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="120px" @submit.native.prevent>
        <el-form-item label="角色名称" prop="name" label-width="80px">
          <el-input class="width_220" v-model="ruleForm.name" size="small"></el-input>
        </el-form-item>
      </el-form>

      <div class="position_relative">
        <el-input
          prefix-icon="el-icon-search"
          placeholder="请输入名称进行搜索"
          v-model="searchName"
          size="mini"
          clearable
          style="width: 30%; position: absolute; right: 10px; top: 5px; z-index: 2"
        ></el-input>
        <el-tabs v-model="flex_li">
          <el-tab-pane name="1" label="PC权限设置">
            <el-table
              size="small"
              :data="flex_li == '1' ? searchUpdateTableData(searchName, updateTableData) : updateTableData"
              max-height="480px"
              :border="true"
              :show-header="false"
            >
              <el-table-column prop="Name" width="100px"></el-table-column>
              <el-table-column prop="Child" style="padding: 0px" class-name="subMenuTable">
                <template slot-scope="scope">
                  <el-table size="small" :data="scope.row.Child" :show-header="false" style="cursor: pointer" empty-text=" " @cell-click="allSelectionChange">
                    <el-table-column prop="Name" width="110px"> </el-table-column>
                    <el-table-column class-name="subMenuTable">
                      <template slot-scope="scope">
                        <el-table size="small" :data="scope.row.Child" :show-header="false" empty-text=" ">
                          <el-table-column width="180px">
                            <template slot-scope="scope">
                              <el-checkbox
                                size="small"
                                v-model="scope.row.IsSelected"
                                :label="scope.row.Name"
                                @change="handleSelectionChange(scope.row)"
                              ></el-checkbox>
                            </template>
                          </el-table-column>
                          <el-table-column>
                            <template slot-scope="scope">
                              <el-row>
                                <el-col :span="8" v-for="p in scope.row.Permission" :key="p.Code">
                                  <el-checkbox size="small" v-model="p.IsSelected" class="padlt_10" :label="p.Name" @change="checkboxChange(p)"></el-checkbox>
                                </el-col>
                              </el-row>
                            </template>
                          </el-table-column>
                        </el-table>
                      </template>
                    </el-table-column>
                  </el-table>
                </template>
              </el-table-column>
            </el-table>
          </el-tab-pane>
          <el-tab-pane name="2" label="小程序权限设置">
            <el-table
              size="small"
              :data="flex_li == '2' ? searchUpdateTableData(searchName, miniMapAppTableData) : miniMapAppTableData"
              max-height="480px"
              :border="true"
              style="cursor: pointer"
              :show-header="false"
              @cell-click="allSelectionChange"
            >
              <el-table-column prop="Name" width="125px"></el-table-column>
              <el-table-column prop="Child" style="padding: 0px" class-name="subMenuTable">
                <template slot-scope="scope">
                  <el-table size="small" :data="scope.row.Child" :show-header="false" empty-text=" ">
                    <el-table-column width="210px">
                      <template slot-scope="scope">
                        <el-checkbox
                          size="small"
                          v-model="scope.row.IsSelected"
                          :label="scope.row.Name"
                          @change="handleSelectionChange(scope.row)"
                        ></el-checkbox>
                      </template>
                    </el-table-column>
                    <el-table-column>
                      <template slot-scope="scope">
                        <el-row>
                          <el-col :span="8" v-for="p in scope.row.Permission" :key="p.Code">
                            <el-checkbox size="small" v-model="p.IsSelected" class="padlt_10" :label="p.Name" @change="checkboxChange(p)"></el-checkbox>
                          </el-col>
                        </el-row>
                      </template>
                    </el-table-column>
                  </el-table>
                </template>
              </el-table-column>
            </el-table>
          </el-tab-pane>
        </el-tabs>
      </div>
      <div slot="footer">
        <el-button size="small" @click="cancelClick" v-prevent-click>取 消</el-button>
        <el-button size="small" type="primary" @click="addClick" :loading="modalLoading" v-prevent-click>保 存</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import API from "@/api/KHS/Role/role";
var Enumerable = require("linq");

export default {
  name: "RoleMenuPermission",
  watch: {},
  data() {
    return {
      searchName: "",
      loading: false,
      modalLoading: false, // 新增、编辑
      tableData: [],
      name: "",
      // 弹出框状态
      dialogVisible: false,
      flex_li: "1", //type
      type: 1, //编辑或新增 新增1 编辑 0
      ID: "", //角色ID
      title: "系统角色",
      updateTableData: [],
      miniMapAppTableData: [], //小程序权限
      MenuPermission: [], //选中的权限
      jurisdiction: [],
      ruleForm: {
        name: "",
      },
      rules: {
        name: [{ required: true, message: "请输入系统角色", trigger: "blur" }],
      },
      dialogTitle: "",
      //需要给分页组件传的信息
      paginations: {
        page: 1, // 当前位于哪页
        total: 0, // 总数
        page_size: 12, // 1页显示多少条
        layout: "total, prev, pager, next,jumper", // 翻页属性
      },
    };
  },
  methods: {
    searchUpdateTableData(searchName, source) {
      let that = this;
      let searchData = source.reduce((per, cur) => {
        if (cur.Child && cur.Child.length > 0) {
          let tmpChild = that.searchUpdateTableData(searchName, cur.Child);
          if (tmpChild && tmpChild.length > 0) {
            return [
              ...per,
              {
                ...cur,
                Child: tmpChild,
              },
            ];
          } else {
            return per;
          }
        } else {
          if (cur.Name.indexOf(searchName) > -1) {
            return [...per, cur];
          } else {
            return per;
          }
        }
      }, []);
      return searchData;
    },
    // 数据显示
    handleSearch: function () {
      let that = this;
      that.paginations.page = 1;
      that.search();
    },
    // 数据显示
    search: function () {
      let that = this;
      that.loading = true;
      var params = {
        RoleName: that.name,
        PageNum: that.paginations.page,
      };
      API.getRole(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.tableData = res.List;
            that.paginations.page_size = res.PageSize;
            that.paginations.total = res.Total;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },
    // 上下分页
    handleCurrentChange(page) {
      var that = this;
      that.paginations.page = page;
      that.search();
    },

    // 新增
    showAddDialog: function () {
      var that = this;
      that.type = 1;
      that.dialogVisible = true;
      that.ruleForm.name = "";
      that.flex_li = "1";
      that.updateTableData = [];
      that.miniMapAppTableData = [];
      that.dialogTitle = "新增角色";
      // PC
      that.permissionChange(1);
      // 小程序
      that.permissionChange(2);
    },
    // 编辑
    showEditDialog: function (row) {
      var that = this;
      that.type = 0;
      that.dialogVisible = true;
      var detail = row;
      that.ID = detail.ID;
      that.ruleForm.name = detail.Name;
      that.dialogTitle = "编辑角色";
      // PC
      that.rolePermission(1);
      // 小程序
      that.rolePermission(2);
    },

    //  新增、编辑部分
    // 返回
    cancelClick: function () {
      var that = this;
      that.dialogVisible = false;
    },
    //选择type
    // chooseClick: function () {

    //   var that = this;
    //   // that.flex_li = index;
    //   that.permissionChange();
    // },
    //菜单权限列表
    /**  type 1 PC 权限 2 小程序权限  */
    permissionChange: function (type) {
      var that = this;
      that.loading = true;
      that.MenuPermission = [];
      var params = {
        MenuPermissionType: type,
      };
      API.getMenuPermission(params)
        .then((res) => {
          if (res.StateCode == 200) {
            if (type == 1) {
              that.updateTableData = this.initRecursionPermission(res.Data);
              that.selectionData(that.updateTableData);
            }
            if (type == 2) {
              that.miniMapAppTableData = this.initRecursionPermission(res.Data);
              that.selectionData(that.miniMapAppTableData);
            }
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },
    // 递归遍历
    initRecursionPermission: function (menus, IsSelected) {
      var that = this;
      var newMenus = [];

      newMenus = menus.map(function (item) {
        item.IsSelected = IsSelected;
        if (item.Permission.length > 0) {
          item.Permission.forEach(function (p) {
            p.IsSelected = IsSelected;
          });
        }
        if (item.Child) {
          newMenus = newMenus.concat(that.initRecursionPermission(item.Child, IsSelected));
        }
        return item;
      });
      return newMenus;
    },

    selectionData: function (echoList) {
      var that = this;
      echoList.forEach(function (item) {
        item.IsSelected = false;
        if (item.Child) {
          that.selectionData(item.Child);
        }
      });
    },
    //角色菜单权限列表
    /**  type 1 PC 权限 2 小程序权限  */
    rolePermission: function (type) {
      var that = this;
      that.loading = true;
      that.MenuPermission = [];
      that.jurisdiction = [];
      that.updateTableData = [];
      that.miniMapAppTableData = [];
      var params = {
        ID: that.ID,
        MenuPermissionType: type,
      };
      API.getRoleGet(params)
        .then((res) => {
          if (res.StateCode == 200) {
            if (type == 1) {
              that.updateTableData = res.Data;
              that.echoData(that.updateTableData);
            }
            if (type == 2) {
              that.miniMapAppTableData = res.Data;
              that.echoData(that.miniMapAppTableData);
            }
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },
    // 回显数据
    echoData: function (echoList) {
      var that = this;
      var MenuPermission = that.MenuPermission;
      echoList.forEach(function (item) {
        // 默认选中 小程序 预约 和顾客管理二级权限
        // if (item.ParentCode =="MP-Customer" || item.ParentCode == "MP-Appointment") {
        // if (item.ParentCode == "MP-Customer") {
        //   item.IsSelected = true;
        // }
        if (item.IsSelected) {
          MenuPermission.push(item.Code);
        }
        if (item.Permission.length > 0) {
          item.Permission.forEach(function (p) {
            if (p.IsSelected) {
              that.jurisdiction.push(p.Code);
            }
          });
        }
        if (item.Child) {
          that.echoData(item.Child);
        }
      });
      that.MenuPermission = MenuPermission;
    },
    // 递归遍历
    recursionPermission: function (menus, IsSelected) {
      var that = this;
      var menuArr = [];
      var permissionArr = [];
      var obj = {};
      menus.forEach(function (item) {
        item.IsSelected = IsSelected;
        // if (item.IsSelected) {
        menuArr.push(item.Code);
        // }
        if (item.Permission.length > 0) {
          item.Permission.forEach(function (p) {
            p.IsSelected = IsSelected;
            // if (p.IsSelected) {
            permissionArr.push(p.Code);
            // }
          });
        }
        if (item.Child) {
          menuArr = menuArr.concat(that.recursionPermission(item.Child, item.IsSelected).MenuCodes);
          permissionArr = permissionArr.concat(that.recursionPermission(item.Child, item.IsSelected).permissionCodes);
        }
      });
      obj.MenuCodes = menuArr;
      obj.permissionCodes = permissionArr;
      return obj;
    },
    // 全选
    /**  选择目录权限  */
    allSelectionChange(row, column) {
      let that = this;
      if (column.property == "Name") {
        row.IsSelected = !row.IsSelected;
        let tempCodes = that.recursionPermission(row.Child, row.IsSelected);
        if (row.IsSelected) {
          that.MenuPermission = Enumerable.from(tempCodes.MenuCodes).union(that.MenuPermission).toArray();
          that.jurisdiction = Enumerable.from(tempCodes.permissionCodes).union(that.jurisdiction).toArray();
        } else {
          that.MenuPermission = Enumerable.from(that.MenuPermission).except(tempCodes.MenuCodes).toArray();
          that.jurisdiction = Enumerable.from(that.jurisdiction).except(tempCodes.permissionCodes).toArray();
        }
      }
    },

    // 选择
    handleSelectionChange: function (val) {
      var that = this;
      if (val.IsSelected) {
        that.MenuPermission.push(val.Code);
      } else {
        var index = that.MenuPermission.indexOf(val.Code);
        that.MenuPermission.splice(index, 1);
      }
    },
    checkboxChange: function (val) {
      var that = this;
      // var jurisdiction = that.jurisdiction;
      // val.IsSelected = !val.IsSelected;
      if (val.IsSelected) {
        that.jurisdiction.push(val.Code);
      } else {
        var index = that.jurisdiction.indexOf(val.Code);
        that.jurisdiction.splice(index, 1);
      }
      // that.jurisdiction = jurisdiction;
    },
    /**  权限弹窗关闭后调用  */
    dialogClosed() {
      this.MenuPermission = [];
      this.jurisdiction = [];
    },
    //  弹出框确认事件保存 新增、编辑
    addClick: function () {
      var that = this;
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          if (that.MenuPermission.length == 0) {
            that.$message.error({
              showClose: true,
              message: "请选择权限",
              duration: 2000,
            });
            return false;
          }
          var MenuPermission = that.MenuPermission.concat(that.jurisdiction);
          that.modalLoading = true;
          let para = Object.assign({}, that.ruleForm);
          if (that.type == 1) {
            var params = {
              Name: para.name,
              MenuPermission: MenuPermission,
            };
            API.createRole(params)
              .then(function (res) {
                if (res.StateCode === 200) {
                  that.$message.success({
                    showClose: true,
                    message: "新增成功",
                    duration: 2000,
                  });
                  that.$refs["ruleForm"].resetFields();
                  that.search();
                  that.dialogVisible = false;
                } else {
                  that.$message.error({
                    showClose: true,
                    message: res.Message,
                    duration: 2000,
                  });
                }
              })
              .finally(function () {
                that.modalLoading = false;
              });
          } else {
            var Params = {
              ID: that.ID,
              Name: para.name,
              MenuPermission: MenuPermission,
            };
            API.updateRole(Params)
              .then(function (res) {
                if (res.StateCode === 200) {
                  that.$message.success({
                    showClose: true,
                    message: "编辑成功",
                    duration: 2000,
                  });
                  that.$refs["ruleForm"].resetFields();
                  that.search();
                  that.dialogVisible = false;
                } else {
                  that.$message.error({
                    showClose: true,
                    message: res.Message,
                    duration: 2000,
                  });
                }
              })
              .finally(function () {
                that.modalLoading = false;
              });
          }
        }
      });
    },
  },
  mounted() {
    var that = this;
    that.handleSearch();
  },
};
</script>

<style lang="scss">
.subMenuTable {
  padding: 0px !important;
  .cell {
    padding: 0px;
  }
}
</style>
