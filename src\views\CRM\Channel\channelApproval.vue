<template>
  <div class="channelApproval content_body" v-loading="loading">
    <div class="nav_header">
      <el-row>
        <el-col :span="24">
          <el-form :inline="true" size="small" @submit.native.prevent @keyup.enter.native="handleSearch">
            <el-form-item label="渠道名称">
              <el-input v-model="searchRuleForm.Name" placeholder="请输入渠道名称" clearable @clear="handleSearch"></el-input>
            </el-form-item>
            <el-form-item label="渠道类型">
              <el-select v-model="searchRuleForm.ChannelTypeID" placeholder="请选择渠道类型" clearable @change="handleSearch">
                <el-option v-for="item in channelTypeList" :key="item.ID + 'type'" :label="item.Name" :value="item.ID">
                </el-option>
              </el-select>
            </el-form-item>

            <el-form-item label="上级渠道">
              <el-select v-model="searchRuleForm.ParentID" placeholder="请输入渠道信息搜索渠道"
                popper-class="custom_channelPopperClass" filterable remote reserve-keyword size="small" clearable
                :remote-method="searchChannelInfo" @focus="focusChannel" @clear="focusChannel" @change="handleSearch">
                <el-option v-for="item in allChannelList" :key="item.ID" :label="item.Name" :value="item.ID">
                  <div style="padding-bottom: 8px">
                    <div>{{ item.Name }}</div>
                    <div class="font_12 color_999">{{ item.ParentName }}</div>
                  </div>
                </el-option>
              </el-select>
            </el-form-item>

            <el-form-item label="开发人员">
              <el-select v-model="DeveloperID" placeholder="请选择开发人员" filterable clearable @change="handleSearch"
                @clear="handleSearch" size="small" popper-class="empPopper_custom">
                <el-option v-for="item in EmployeeList" :key="item.ID" :label="item.Name" :value="item.ID">
                  <div style="padding-bottom: 8px">
                    <div>{{ item.Name }}</div>
                    <div class="font_12 color_999">{{ item.EntityName }}</div>
                  </div>
                </el-option>
              </el-select>
            </el-form-item>

            <el-form-item label="市场咨询">
              <el-select v-model="ConsultantID" placeholder="请选择市场咨询" filterable clearable @change="handleSearch"
                @clear="handleSearch" size="small" popper-class="empPopper_custom">
                <el-option v-for="item in EmployeeList" :key="item.ID" :label="item.Name" :value="item.ID">
                  <div style="padding-bottom: 8px">
                    <div>{{ item.Name }}</div>
                    <div class="font_12 color_999">{{ item.EntityName }}</div>
                  </div>
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="审批状态">
              <el-select v-model="searchRuleForm.ApprovalStatus" placeholder="请选择审批状态" clearable @change="handleSearch">
                <el-option label="待审批" value="10"></el-option>
                <el-option label="已同意" value="20"></el-option>
                <el-option label="已拒绝" value="30"></el-option>
              </el-select>
            </el-form-item>

            <el-form-item label="渠道等级">
              <el-select v-model="searchRuleForm.ChannelLevelID" placeholder="请选择渠道等级" clearable @change="handleSearch">
                <el-option v-for="item in channelLevelList" :key="item.ID + 'type'" :label="item.Name" :value="item.ID">
                </el-option>
              </el-select>
            </el-form-item>

            <el-form-item>
              <el-button type="primary" size="small" @click="handleSearch" v-prevent-click>搜索</el-button>
            </el-form-item>
          </el-form>
        </el-col>
      </el-row>
    </div>

    <el-table size="small" :data="tableData">
      <el-table-column prop="Name" label="渠道名称"></el-table-column>
      <el-table-column prop="ChannelTypeName" label="渠道类型"></el-table-column>
      <el-table-column prop="ChannelLevelName" label="渠道等级"></el-table-column>
      <el-table-column prop="ParentName" label="上级渠道"></el-table-column>
      <el-table-column label="开发人员" :formatter="developerFormatter"></el-table-column>
      <el-table-column label="市场咨询" :formatter="consultantFormatter"></el-table-column>
      <el-table-column prop="ApprovalStatus" label="审批状态" :formatter="approvalStatusFormatter"></el-table-column>
      <el-table-column prop="CreatedBy" label="提报人"></el-table-column>
      <el-table-column prop="CreatedOn" label="提报时间">
        <template slot-scope="scope">
          {{ scope.row.CreatedOn | dateFormat("YYYY-MM-DD HH:mm") }}
        </template>
      </el-table-column>
      <el-table-column label="操作" width="80px">
        <template slot-scope="scope">
          <!-- <el-button type="primary" size="small" @click="showEditDialog(scope.row)" v-prevent-click>编辑</el-button> -->
          <el-button v-if="scope.row.ApprovalStatus == '10'" type="primary" size="small"
            @click="channelApproval(scope.row)" v-prevent-click>审批</el-button>
          <el-button v-else type="primary" size="small" @click="checkchannelApproval(scope.row)" v-prevent-click>查看
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <div class="pad_15 text_right">
      <el-pagination background v-if="paginations.total > 0" @current-change="handleCurrentChange"
        :current-page.sync="paginations.page" :page-size="paginations.page_size" :layout="paginations.layout"
        :total="paginations.total"></el-pagination>
    </div>

    <!-- 审批申请 -->
    <el-dialog title="审批" :visible.sync="dialogVisible" width="900px">
      <el-tabs v-if="channelInfo" v-model="activeName">
        <el-tab-pane label="渠道信息" name="1">
          <el-descriptions :column="3" :colon="false">
            <el-descriptions-item label="渠道名称：">{{ channelInfo.Name }}</el-descriptions-item>
            <el-descriptions-item label="渠道类型：">{{ channelInfo.ChannelTypeName }}</el-descriptions-item>
            <el-descriptions-item label="上级渠道：">{{ channelInfo.ParentName }}</el-descriptions-item>
            <el-descriptions-item label="联系人：">{{ channelInfo.ContactPersonName }}</el-descriptions-item>
            <el-descriptions-item label="手机号码：">{{ channelInfo.ContactPersonMobile }}</el-descriptions-item>
            <el-descriptions-item label="身份证号：">{{ channelInfo.ContactPersonIDNumber }}</el-descriptions-item>
            <el-descriptions-item label="提报人：">{{ channelInfo.ContactPersonName }}</el-descriptions-item>
            <el-descriptions-item label="提报时间：">{{ channelInfo.CreatedOn }}</el-descriptions-item>
            <el-descriptions-item label="地址：" :span="3">{{ channelInfo.AddressDetail }}</el-descriptions-item>
            <el-descriptions-item label="备注：" :span="3">{{ channelInfo.Remark }}</el-descriptions-item>
          </el-descriptions>
          <el-form ref="ruleForm" size="small" label-width="80px" :model="addruleform">
            <el-row>
              <el-col :span="24">
                <el-form-item label="审批说明" prop="remark"
                  :rules="[{ required: true, message: '请输入审批说明', trigger: ['blur', 'change'] }]">
                  <el-input type="textarea" :autosize="{ minRows: 2, maxRows: 4 }" placeholder="请输入审批内容"
                    v-model="addruleform.remark"> </el-input>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-tab-pane>
        <el-tab-pane label="开发人员" name="5">
          <el-table size="small" :data="channelInfo.Developer">
            <el-table-column prop="EmployeeName" label="开发人员"></el-table-column>
            <el-table-column prop="PerformanceRate" label="业绩比例">
              <template slot-scope="scope"> {{ scope.row.PerformanceRate }}% </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
        <el-tab-pane label="市场咨询" name="6">
          <el-table size="small" :data="channelInfo.Consultant">
            <el-table-column prop="EmployeeName" label="市场咨询"></el-table-column>
            <el-table-column prop="PerformanceRate" label="业绩比例">
              <template slot-scope="scope"> {{ scope.row.PerformanceRate }}% </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
        <el-tab-pane label="介绍人" name="2">
          <el-table size="small" :data="channelInfo.IntroducerList">
            <el-table-column prop="Name" label="渠道名称"></el-table-column>
            <el-table-column prop="ChannelTypeName" label="渠道类型"></el-table-column>
            <el-table-column prop="ParentName" label="上级渠道"></el-table-column>
          </el-table>
        </el-tab-pane>

        <el-tab-pane label="合同信息" name="3">
          <el-table size="small" :data="channelInfo.Contract">
            <el-table-column prop="Code" label="合同编号"></el-table-column>
            <el-table-column prop="BeginDate" label="合同开始日期"></el-table-column>
            <el-table-column prop="EndDate" label="合同结束日期"></el-table-column>
            <el-table-column label="操作" width="120px">
              <template slot-scope="scope">
                <el-button type="primary" size="small" @click="checkChannelContract(scope.row)" v-prevent-click>查看合同
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>

        <el-tab-pane label="法人信息" name="4">
          <el-table size="small" :data="channelInfo.Company">
            <el-table-column prop="Name" label="公司名称"></el-table-column>
            <el-table-column prop="Amount" label="公司额度"></el-table-column>
            <el-table-column prop="LegalPerson" label="法人姓名"></el-table-column>
            <el-table-column prop="PhoneNumber" label="法人手机"></el-table-column>
            <el-table-column prop="BankName" label="开户行"></el-table-column>
            <el-table-column prop="BankBranchName" label="开户支行"></el-table-column>
            <el-table-column prop="BankAccount" label="开户行账号"></el-table-column>
            <el-table-column prop="Remark" label="备注信息"></el-table-column>
          </el-table>
        </el-tab-pane>
      </el-tabs>

      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false" size="small">取 消</el-button>
        <el-button type="danger" :loading="rejectLoading" @click="approvalChannelRejectClick" v-prevent-click
          size="small">驳回</el-button>
        <el-button type="primary" :loading="approvalLoading" @click="approvalChannelClick" v-prevent-click size="small">
          通过</el-button>
      </span>
    </el-dialog>

    <!-- 渠道详情 -->
    <el-dialog title="渠道详情" :visible.sync="checkDialogVisible" width="900px">
      <el-tabs v-if="channelInfo" v-model="activeName">
        <el-tab-pane label="渠道信息" name="1">
          <el-descriptions :column="3" :colon="false">
            <el-descriptions-item label="渠道名称：">{{ channelInfo.Name }}</el-descriptions-item>
            <el-descriptions-item label="渠道类型：">{{ channelInfo.ChannelTypeName }}</el-descriptions-item>
            <el-descriptions-item label="上级渠道：">{{ channelInfo.ParentName }}</el-descriptions-item>

            <el-descriptions-item label="联系人：">{{ channelInfo.ContactPersonName }}</el-descriptions-item>
            <el-descriptions-item label="手机号码：">{{ channelInfo.ContactPersonMobile }}</el-descriptions-item>
            <el-descriptions-item label="身份证号：">{{ channelInfo.ContactPersonMobile }}</el-descriptions-item>

            <el-descriptions-item label="地址：" :span="3">{{ channelInfo.AddressDetail }}</el-descriptions-item>

            <el-descriptions-item label="提报人：">{{ channelInfo.ContactPersonIDNumber }}</el-descriptions-item>
            <el-descriptions-item label="提报日期：">{{ channelInfo.CreatedOn }}</el-descriptions-item>
            <el-descriptions-item label="备注：">{{ channelInfo.Remark }}</el-descriptions-item>

            <el-descriptions-item label="审批人：">{{ channelInfo.ApprovalBy }}</el-descriptions-item>
            <el-descriptions-item label="审批日期：">{{ channelInfo.ApprovalOn }}</el-descriptions-item>
            <el-descriptions-item label="审批说明：">{{ channelInfo.ApprovalRemark }}</el-descriptions-item>
          </el-descriptions>
        </el-tab-pane>
        <el-tab-pane label="开发人员" name="5">
          <el-table size="small" :data="channelInfo.Developer">
            <el-table-column prop="EmployeeName" label="开发人员"></el-table-column>
            <el-table-column prop="PerformanceRate" label="业绩比例">
              <template slot-scope="scope"> {{ scope.row.PerformanceRate }}% </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
        <el-tab-pane label="市场咨询" name="6">
          <el-table size="small" :data="channelInfo.Consultant">
            <el-table-column prop="EmployeeName" label="市场咨询"></el-table-column>
            <el-table-column prop="PerformanceRate" label="业绩比例">
              <template slot-scope="scope"> {{ scope.row.PerformanceRate }}% </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
        <el-tab-pane label="介绍人" name="2">
          <el-table size="small" :data="channelInfo.IntroducerList">
            <el-table-column prop="Name" label="渠道名称"></el-table-column>
            <el-table-column prop="ChannelTypeName" label="渠道类型"></el-table-column>
            <el-table-column prop="ParentName" label="上级渠道"></el-table-column>
          </el-table>
        </el-tab-pane>

        <el-tab-pane label="合同信息" name="3">
          <el-table size="small" :data="channelInfo.Contract">
            <el-table-column prop="Code" label="合同编号"></el-table-column>
            <el-table-column prop="BeginDate" label="合同开始日期"></el-table-column>
            <el-table-column prop="EndDate" label="合同结束日期"></el-table-column>
            <el-table-column label="操作" width="120px">
              <template slot-scope="scope">
                <el-button type="primary" size="small" @click="checkChannelContract(scope.row)" v-prevent-click>查看合同
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>

        <el-tab-pane label="法人信息" name="4">
          <el-table size="small" :data="channelInfo.Company">
            <el-table-column prop="Name" label="公司名称"></el-table-column>
            <el-table-column prop="Amount" label="公司额度"></el-table-column>
            <el-table-column prop="LegalPerson" label="法人姓名"></el-table-column>
            <el-table-column prop="PhoneNumber" label="法人手机"></el-table-column>
            <el-table-column prop="BankName" label="开户行"></el-table-column>
            <el-table-column prop="BankBranchName" label="开户支行"></el-table-column>
            <el-table-column prop="BankAccount" label="开户行账号"></el-table-column>
            <el-table-column prop="Remark" label="备注信息"></el-table-column>
          </el-table>
        </el-tab-pane>
      </el-tabs>

      <span slot="footer" class="dialog-footer">
        <el-button @click="checkDialogVisible = false" size="small">取 消</el-button>
      </span>
    </el-dialog>
    <el-dialog title="合同附件" :visible.sync="checkChannelContractVisible" width="600px">
      <div v-if="ContractAttachmentInfo" class="dis_flex flex_wrap">
        <el-image v-for="(item, index) in ContractAttachmentInfo" :key="index" style="width: 130px; height: 130px"
          class="marrt_10 marbm_10" :src="item.AttachmentURL" fit="cover"
          :preview-src-list="ContractAttachmentInfo.map((i) => i.AttachmentURL)"></el-image>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import API from "@/api/CRM/Channel/channelApproval.js";
import typeAPI from "@/api/CRM/Channel/channelType.js";
import levelAPI from "@/api/CRM/Channel/channelLevel.js";
import infoAPI from "@/api/CRM/Channel/channelInfo.js";

export default {
  name: "ChannelApproval",
  props: {},
  /** 监听数据变化   */
  watch: {},
  /**  引入的组件  */
  components: {},
  /**  Vue 实例的数据对象**/
  data() {
    return {
      loading: false,
      dialogVisible: false,
      approvalLoading: false,
      rejectLoading: false,
      checkDialogVisible: false,
      checkChannelContractVisible: false,
      activeName: "1",
      searchRuleForm: {
        Name: "", //模糊搜索
        ChannelTypeID: "", //类型
        ChannelLevelID: "", //等级
        ParentID: null, //上级渠道
        EmployeeID: "",
        ApprovalStatus: "10", //审批状态
        PageNum: 1,
      },
      paginations: {
        page: 1, // 当前位于哪页
        total: 0, // 总数
        page_size: 10, // 1页显示多少条
        layout: "total, prev, pager, next,jumper", // 翻页属性
      }, //需要给分页组件传的信息
      tableData: [],
      channelTypeList: [],
      channelLevelList: [],
      allChannelList: [],
      EmployeeList: [],
      addruleform: {
        remark: "",
      },
      channelInfo: null,

      DeveloperID: "", //开发人员ID
      ConsultantID: "", //咨询人员ID
      ContractAttachmentInfo: null,
    };
  },
  /**计算属性  */
  computed: {},
  /**  方法集合  */
  methods: {
    /**  查看 合同 附件  */
    checkChannelContract(row) {
      let that = this;
      if (!row.Attachment) {
        that.$message.error("暂未获取到合同附件");
        return;
      }
      that.ContractAttachmentInfo = row.Attachment;
      that.checkChannelContractVisible = true;
    },
    /**  格式华 开发人员  */
    developerFormatter(row) {
      return row.DeveloperList.reduce((perval, curval, index) => {
        if (index == row.DeveloperList.length - 1) {
          return perval + curval.EmployeeName;
        }
        return perval + curval.EmployeeName + ", ";
      }, "");
    },
    /**  格式华 市场咨询  */
    consultantFormatter(row) {
      return row.ConsultantList.reduce((perval, curval, index) => {
        if (index == row.DeveloperList.length - 1) {
          return perval + curval.EmployeeName;
        }
        return perval + curval.EmployeeName + ", ";
      }, "");
    },
    /**  搜索  */
    handleSearch() {
      this.paginations.page = 1;
      this.channel_approvalList();
    },
    /**    */
    handleCurrentChange(page) {
      this.paginations.page = page;
      this.channel_approvalList();
    },
    /**    */
    approvalStatusFormatter(row) {
      switch (row.ApprovalStatus) {
        case "10":
          return "待审批";
        case "20":
          return "已同意";
        case "30":
          return "已拒绝";
      }
    },
    /**   审批 */
    channelApproval(row) {
      let that = this;

      that.activeName = "1";
      that.channel_approvalDetail(row.ID);
      that.addruleform.remark = "";
    },
    /**  查看  */
    checkchannelApproval(row) {
      let that = this;
      that.activeName = "1";
      that.channel_approvalDetail(row.ID, "check");
    },
    /**  审批通过  */
    approvalChannelClick() {
      let that = this;
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          that.channel_approval(that.channelInfo.ID, "20");
        }
      });
    },
    /**  审批驳货  */
    approvalChannelRejectClick() {
      let that = this;
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          that.channel_approval(that.channelInfo.ID, "30");
        }
      });
    },
    /**  请求****************************  */
    /**  渠道审批列表  */
    async channel_approvalList() {
      let that = this;
      that.loading = true;
      let params = {
        Name: that.searchRuleForm.Name, //模糊搜索
        ChannelTypeID: that.searchRuleForm.ChannelTypeID, //类型
        ChannelLevelID: that.searchRuleForm.ChannelLevelID, //等级
        ParentID: that.searchRuleForm.ParentID, //上级渠道
        EmployeeID: that.searchRuleForm.EmployeeID,
        ApprovalStatus: that.searchRuleForm.ApprovalStatus, //审批状态
        PageNum: that.paginations.page,
        DeveloperID: that.DeveloperID, //开发人员ID
        ConsultantID: that.ConsultantID, //咨询人员ID
      };
      // let res = await API.channel_approvalList(params);
      // if (res.StateCode == 200) {
      //   that.tableData = res.List;
      //   that.paginations.total = res.Total;
      // } else {
      //   that.$message.error(res.Message);
      // }

      await API.channel_approvalList(params).then((res) => {
        if (res.StateCode == 200) {
          that.tableData = res.List;
          that.paginations.total = res.Total;
        } else {
          that.$message.error(res.Message);
        }
      }).finally(function () {
        that.loading = false;
      });

    },
    /**   渠道审批详情 */
    async channel_approvalDetail(ID, type) {
      let that = this;
      that.loading = true;
      let params = { ID: ID };
      await API.channel_approvalDetail(params).then((res) => {
        if (res.StateCode == 200) {
          that.channelInfo = res.Data;
          if (type == "check") {
            that.checkDialogVisible = true;
          } else {
            that.dialogVisible = true;
          }
        } else {
          that.$message.error(res.Message);
        }
      }).finally(function () {
        that.loading = false;
      });

    },
    /**  审批  */
    async channel_approval(ID, ApprovalStatus) {
      let that = this;
      let params = {
        ID: ID, //渠道id
        Remark: that.addruleform.remark, //备注
        ApprovalStatus: ApprovalStatus, //审批状态 20：同意  30 拒绝
      };
      let res = await API.channel_approval(params);
      if (res.StateCode == 200) {
        that.$message.success("操作成功");
        that.dialogVisible = false;
        that.channel_approvalList();
      } else {
        that.$message.error(res.Message);
      }
    },
    /**  获取渠道类型  */
    async getChannelTypeList() {
      let that = this;
      let params = { Active: true };
      let res = await typeAPI.getChannelTypeList(params);
      if (res.StateCode == 200) {
        that.channelTypeList = res.Data;
      } else {
        that.$message.error(res.Message);
      }
    },
    /**  获取等级列表  */
    async channelLevel_list() {
      let that = this;
      let params = {
        Active: true,
      };
      let res = await levelAPI.channelLevel_list(params);
      if (res.StateCode == 200) {
        that.channelLevelList = res.Data;
      } else {
        that.$message.error(res.Message);
      }
    },
    /**  渠道来源获取焦点 清除数据  */
    focusChannel() {
      let that = this;
      that.allChannelList = [];
    },
    /**    */
    searchChannelInfo(value) {
      this.getAllchannel(value);
    },
    /*  获取渠道 顶部筛选条件 */
    getAllchannel(value) {
      let that = this;
      let params = { Name: value,Active:true, };
      infoAPI.getChannelInfoList(params).then((res) => {
        if (res.StateCode == 200) {
          that.allChannelList = res.Data;
        } else {
          this.$message.error(res.Message);
        }
      });
    },
    /* 获取业务代表 */
    geTemployeeAll() {
      let that = this;
      let params = {};
      infoAPI.geTemployeeAll(params).then((res) => {
        if (res.StateCode == 200) {
          that.EmployeeList = res.Data;
        } else {
          this.$message.error(res.Message);
        }
      });
    },
  },
  /** 创建实例之前执行函数   */
  beforeCreate() { },
  /**  实例创建完成之后  */
  created() { },
  /**  在挂载开始之前被调用  */
  beforeMount() { },
  /**  实例被挂载后调用  */
  mounted() {
    this.channel_approvalList();
    this.getChannelTypeList();
    this.channelLevel_list();
    this.geTemployeeAll();
  },
  /**  数据更新 之前 调用  */
  beforeUpdate() { },
  /** 数据更新 完成 调用   */
  updated() { },
  /**  实例销毁之前调用  */
  beforeDestroy() { },
  /**  实例销毁后调用  */
  destroyed() { },
};
</script>

<style lang="scss">
.channelApproval {}

.custom_channelPopperClass {
  .el-select-dropdown__item {
    line-height: normal;
    height: auto;
  }
}
</style>
