<template>
  <div class="content_body" v-loading="loading">
    <div class="nav_header">
      <el-form :inline="true" size="small" :model="searchData" @submit.native.prevent>
        <el-form-item v-if="storeEntityList.length > 1" label="门店">
          <el-select v-model="searchData.EntityID" clearable filterable placeholder="请选择门店" :default-first-option="true" @change="handleSearch">
            <el-option v-for="item in storeEntityList" :key="item.ID" :label="item.EntityName" :value="item.ID"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="时间筛选">
          <el-date-picker v-model="searchData.QueryDate" unlink-panels :clearable="false" type="daterange" range-separator="至" value-format="yyyy-MM-dd" start-placeholder="开始日期" end-placeholder="结束日期" @change="handleSearch" :picker-options="pickerOptions"></el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" v-prevent-click @click="handleSearch">搜索</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="font_weight_600">实收统计</div>
    <el-table size="small" border show-summary :summary-method="getPayOverviewSummaries" class="martp_5" :data="entityMonthlyStatementPayOverview" v-loading="entityMonthlyStatementPayOverviewLoading">
      <el-table-column prop="Name" width="120px" label="收款方式"></el-table-column>
      <el-table-column align="right" prop="SalePayAmount" label="销售金额">
        <template slot-scope="scope">
          {{ scope.row.SalePayAmount | toFixed | NumFormat }}
        </template>
      </el-table-column>
      <el-table-column align="right" prop="ArrearPayAmount" label="补欠款金额">
        <template slot-scope="scope">
          {{ scope.row.ArrearPayAmount | toFixed | NumFormat }}
        </template>
      </el-table-column>
      <el-table-column prop="RefunAmount" align="right" label="退款金额">
        <template slot-scope="scope">
          <span v-if="scope.row.RefunAmount > 0" class="color_red"> -{{ scope.row.RefunAmount | toFixed | NumFormat }}</span>
          <span v-else>{{ scope.row.RefunAmount | toFixed | NumFormat }}</span>
        </template>
      </el-table-column>
      <el-table-column align="right" label="实收金额">
        <template slot-scope="scope">
          <span v-if="scope.row.SalePayAmount + scope.row.ArrearPayAmount - scope.row.RefunAmount < 0" class="color_red">{{ (scope.row.SalePayAmount + scope.row.ArrearPayAmount - scope.row.RefunAmount).toFixed(2) | NumFormat }}</span>
          <span v-else>{{ (scope.row.SalePayAmount + scope.row.ArrearPayAmount - scope.row.RefunAmount).toFixed(2) | NumFormat }}</span>
        </template>
      </el-table-column>
    </el-table>
    <div class="font_weight_600 martp_10">销售统计</div>
    <el-table size="small" show-summary :summary-method="getSaleOverviewSummaries" class="martp_5" :data="entityMonthlyStatementSaleOverview" v-loading="entityMonthlyStatementSaleOverviewLoading">
      <el-table-column width="120px" prop="GoodsType" label="商品类型"></el-table-column>
      <el-table-column align="center" label="销售信息">
        <el-table-column align="right" prop="SalePayAmount" label="实收金额">
          <template slot-scope="scope">
            {{ scope.row.SalePayAmount | toFixed | NumFormat }}
          </template>
        </el-table-column>
        <el-table-column align="right" prop="SaleSavingCardDeductionAmount" label="卡抵扣金额">
          <template slot-scope="scope">
            {{ scope.row.SaleSavingCardDeductionAmount | toFixed | NumFormat }}
          </template>
        </el-table-column>
        <el-table-column align="right" prop="SaleLargessSavingCardDeductionAmount" label="赠送卡抵扣金额">
          <template slot-scope="scope">
            {{ scope.row.SaleLargessSavingCardDeductionAmount | toFixed | NumFormat }}
          </template>
        </el-table-column>
        <el-table-column align="right" prop="SaleLargessAmount" label="赠送金额">
          <template slot-scope="scope">
            {{ scope.row.SaleLargessAmount | toFixed | NumFormat }}
          </template>
        </el-table-column>
      </el-table-column>
      <el-table-column align="center" label="补欠款信息">
        <el-table-column align="right" prop="ArrearPayAmount" label="实收金额">
          <template slot-scope="scope">
            {{ scope.row.ArrearPayAmount | toFixed | NumFormat }}
          </template>
        </el-table-column>
        <el-table-column align="right" prop="ArrearSavingCardDeductionAmount" label="卡抵扣金额">
          <template slot-scope="scope">
            {{ scope.row.ArrearSavingCardDeductionAmount | toFixed | NumFormat }}
          </template>
        </el-table-column>
        <el-table-column align="right" prop="ArrearLargessSavingCardDeductionAmount" label="赠送卡抵扣金额">
          <template slot-scope="scope">
            {{ scope.row.ArrearLargessSavingCardDeductionAmount | toFixed | NumFormat }}
          </template>
        </el-table-column>
      </el-table-column>
      <el-table-column align="center" label="退款信息">
        <el-table-column align="right" prop="RefunPayAmount" label="退款金额">
          <template slot-scope="scope">
            <span v-if="scope.row.RefunPayAmount > 0" class="color_red">-{{ scope.row.RefunPayAmount | toFixed | NumFormat }}</span>
            <span v-else>{{ scope.row.RefunPayAmount | toFixed | NumFormat }}</span>
          </template>
        </el-table-column>
        <el-table-column align="right" prop="RefundSavingCardDeductionAmount" label="退回卡金额">
          <template slot-scope="scope">
            <span v-if="scope.row.RefundSavingCardDeductionAmount > 0" class="color_red">-{{ scope.row.RefundSavingCardDeductionAmount | toFixed | NumFormat }}</span>
            <span v-else>{{ scope.row.RefundSavingCardDeductionAmount | toFixed | NumFormat }}</span>
          </template>
        </el-table-column>
        <el-table-column align="right" prop="RefundLargessSavingCardDeductionAmount" label="退回赠送卡金额">
          <template slot-scope="scope">
            <span v-if="scope.row.RefundLargessSavingCardDeductionAmount > 0" class="color_red">-{{ scope.row.RefundLargessSavingCardDeductionAmount | toFixed | NumFormat }}</span>
            <span v-else>{{ scope.row.RefundLargessSavingCardDeductionAmount | toFixed | NumFormat }}</span>
          </template>
        </el-table-column>
        <el-table-column align="right" prop="RefundLargessAmount" label="退回赠送金额">
          <template slot-scope="scope">
            <span v-if="scope.row.RefundLargessAmount > 0" class="color_red">-{{ scope.row.RefundLargessAmount | toFixed | NumFormat }}</span>
            <span v-else>{{ scope.row.RefundLargessAmount | toFixed | NumFormat }}</span>
          </template>
        </el-table-column>
      </el-table-column>
      <el-table-column align="center" label="合计">
        <el-table-column align="right" prop="PayTotalAmount" label="实收合计">
          <template slot-scope="scope">
            <span v-if="(scope.row.SalePayAmount + scope.row.ArrearPayAmount - scope.row.RefunPayAmount).toFixed(2) < 0" class="color_red">{{ (scope.row.SalePayAmount + scope.row.ArrearPayAmount - scope.row.RefunPayAmount).toFixed(2) | NumFormat }}</span>
            <span v-else>{{ (scope.row.SalePayAmount + scope.row.ArrearPayAmount - scope.row.RefunPayAmount).toFixed(2) | NumFormat }}</span>
          </template>
        </el-table-column>
        <el-table-column align="right" prop="SavingCardDeductionTotalAmount" label="卡抵扣合计">
          <template slot-scope="scope">
            <span v-if="(scope.row.SaleSavingCardDeductionAmount + scope.row.ArrearSavingCardDeductionAmount - scope.row.RefundSavingCardDeductionAmount).toFixed(2) < 0" class="color_red">{{ (scope.row.SaleSavingCardDeductionAmount  + scope.row.ArrearSavingCardDeductionAmount - scope.row.RefundSavingCardDeductionAmount).toFixed(2) | NumFormat }}</span>
            <span v-else>{{ (scope.row.SaleSavingCardDeductionAmount  + scope.row.ArrearSavingCardDeductionAmount - scope.row.RefundSavingCardDeductionAmount).toFixed(2) | NumFormat }}</span>
          </template>
        </el-table-column>
        <el-table-column align="right" prop="LargessSavingDeductionTotalAmount" label="赠送卡抵扣合计">
          <template slot-scope="scope">
            <span v-if="(scope.row.SaleLargessSavingCardDeductionAmount + scope.row.ArrearLargessSavingCardDeductionAmount - scope.row.RefundLargessSavingCardDeductionAmount).toFixed(2) < 0" class="color_red">{{ (scope.row.SaleLargessSavingCardDeductionAmount + scope.row.ArrearLargessSavingCardDeductionAmount - scope.row.RefundLargessSavingCardDeductionAmount).toFixed(2) | NumFormat }}</span>
            <span v-else>{{ (scope.row.SaleLargessSavingCardDeductionAmount + scope.row.ArrearLargessSavingCardDeductionAmount - scope.row.RefundLargessSavingCardDeductionAmount).toFixed(2) | NumFormat }}</span>
          </template>
        </el-table-column>
        <el-table-column align="right" prop="LargessTotalAmount" label="赠送金额合计">
          <template slot-scope="scope">
            <span v-if="(scope.row.SaleLargessAmount - scope.row.RefundLargessAmount).toFixed(2) < 0" class="color_red">{{ (scope.row.SaleLargessAmount - scope.row.RefundLargessAmount).toFixed(2) | NumFormat }}</span>
            <span v-else>{{ (scope.row.SaleLargessAmount - scope.row.RefundLargessAmount).toFixed(2) | NumFormat }}</span>
          </template>
        </el-table-column>
      </el-table-column>
    </el-table>
    <div class="font_weight_600 martp_10">消耗统计</div>
    <el-table size="small" show-summary :summary-method="getTreatOverviewSummaries" class="martp_5" :data="entityMonthlyStatementTreatOverview" v-loading="entityMonthlyStatementTreatOverviewLoading">
      <el-table-column prop="GoodsType" width="120px" label="消耗方式"></el-table-column>
      <el-table-column align="center" label="消耗信息">
        <el-table-column align="right" prop="TreatPayAmount" label="现金金额">
          <template slot-scope="scope">
            {{ scope.row.TreatPayAmount | toFixed | NumFormat }}
          </template>
        </el-table-column>

        <el-table-column align="right" prop="TreatCardAmount" label="卡抵扣金额">
          <template slot-scope="scope">
            {{ scope.row.TreatCardAmount | toFixed | NumFormat }}
          </template>
        </el-table-column>

        <el-table-column align="right" prop="TreatPayAmount" label="赠送卡抵扣金额">
          <template slot-scope="scope">
            {{ scope.row.TreatCardLargessAmount | toFixed | NumFormat }}
          </template>
        </el-table-column>

        <el-table-column align="right" prop="TreatLargessAmount" label="赠送金额">
          <template slot-scope="scope">
            {{ scope.row.TreatLargessAmount | toFixed | NumFormat }}
          </template>
        </el-table-column>
      </el-table-column>
      <el-table-column align="center" label="退消耗信息">
        <el-table-column align="right" prop="RefundTreatPayAmount" label="退本金金额">
          <template slot-scope="scope">
            <span v-if="scope.row.RefundTreatPayAmount > 0" class="color_red">-{{ scope.row.RefundTreatPayAmount | toFixed | NumFormat }}</span>
            <span v-else>{{ scope.row.RefundTreatPayAmount | toFixed | NumFormat }}</span>
          </template>
        </el-table-column>

        <el-table-column align="right" prop="RefundTreatCardAmount" label="退卡抵扣金额">
          <template slot-scope="scope">
            <span v-if="scope.row.RefundTreatCardAmount > 0" class="color_red">-{{ scope.row.RefundTreatCardAmount | toFixed | NumFormat }}</span>
            <span v-else>{{ scope.row.RefundTreatCardAmount | toFixed | NumFormat }}</span>
          </template>
        </el-table-column>

        <el-table-column align="right" prop="RefundTreatCardLargessAmount" label="退赠送卡抵扣金额">
          <template slot-scope="scope">
            <span v-if="scope.row.RefundTreatCardLargessAmount > 0" class="color_red">-{{ scope.row.RefundTreatCardLargessAmount | toFixed | NumFormat }}</span>
            <span v-else>{{ scope.row.RefundTreatCardLargessAmount | toFixed | NumFormat }}</span>
          </template>
        </el-table-column>

        <el-table-column align="right" prop="RefundTreatLargessAmount" label="退赠送金额">
          <template slot-scope="scope">
            <span v-if="scope.row.RefundTreatLargessAmount > 0" class="color_red">-{{ scope.row.RefundTreatLargessAmount | toFixed | NumFormat }}</span>
            <span v-else>{{ scope.row.RefundTreatLargessAmount | toFixed | NumFormat }}</span>
          </template>
        </el-table-column>
      </el-table-column>
      <el-table-column align="center" label="合计">
        <el-table-column align="right" label="现金合计">
          <template slot-scope="scope">
            <span v-if="(scope.row.TreatPayAmount - scope.row.RefundTreatPayAmount).toFixed(2) < 0" class="color_red">{{ (scope.row.TreatPayAmount - scope.row.RefundTreatPayAmount).toFixed(2) | NumFormat }}</span>
            <span v-else>{{ (scope.row.TreatPayAmount - scope.row.RefundTreatPayAmount).toFixed(2) | NumFormat }}</span>
          </template>
        </el-table-column>

        <el-table-column align="right" label="卡本金合计">
          <template slot-scope="scope">
            <span v-if="(scope.row.TreatCardAmount - scope.row.RefundTreatCardAmount).toFixed(2) < 0" class="color_red">{{ (scope.row.TreatCardAmount - scope.row.RefundTreatCardAmount).toFixed(2) | NumFormat }}</span>

            <span v-else>{{ (scope.row.TreatCardAmount - scope.row.RefundTreatCardAmount).toFixed(2) | NumFormat }}</span>
          </template>
        </el-table-column>

        <el-table-column align="right" label="卡赠金合计">
          <template slot-scope="scope">
            <span v-if="(scope.row.TreatCardLargessAmount - scope.row.RefundTreatCardLargessAmount).toFixed(2) < 0" class="color_red">{{ (scope.row.TreatCardLargessAmount - scope.row.RefundTreatCardLargessAmount).toFixed(2) | NumFormat }}</span>
            <span v-else>{{ (scope.row.TreatCardLargessAmount - scope.row.RefundTreatCardLargessAmount).toFixed(2) | NumFormat }}</span>
          </template>
        </el-table-column>

        <el-table-column align="right" label="赠金合计">
          <template slot-scope="scope">
            <span v-if="(scope.row.TreatLargessAmount - scope.row.RefundTreatLargessAmount).toFixed(2) < 0" class="color_red">{{ (scope.row.TreatLargessAmount - scope.row.RefundTreatLargessAmount).toFixed(2) | NumFormat }}</span>
            <span v-else>{{ (scope.row.TreatLargessAmount - scope.row.RefundTreatLargessAmount).toFixed(2) | NumFormat }}</span>
          </template>
        </el-table-column>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import EntityAPI from "@/api/Report/Common/entity";
import API from "@/api/Report/Entity/monthlyStatement";

var Enumerable = require("linq");
const dayjs = require("dayjs");
const isoWeek = require("dayjs/plugin/isoWeek");
dayjs.extend(isoWeek);

export default {
  name: "ReportEntityMonthlyStatement",
  data() {
    return {
      searchData: {
        EntityID: null,
        QueryDate: [new Date(), new Date()],
      },
      loading: false,
      entityMonthlyStatementPayOverviewLoading: false,
      entityMonthlyStatementPayOverview: [], //门店实收统计
      storeEntityList: [], //门店列表
      entityMonthlyStatementSaleOverviewLoading: false,
      entityMonthlyStatementSaleOverview: [], //门店销售统计
      entityMonthlyStatementTreatOverviewLoading: false,
      entityMonthlyStatementTreatOverview: [], //门店消耗统计
      pickerOptions: {
        shortcuts: [
          {
            text: "今天",
            onClick: (picker) => {
              let end = new Date();
              let start = new Date();
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "本周",
            onClick: (picker) => {
              let end = new Date();
              let weekDay = dayjs(end).isoWeekday();
              let start = dayjs(end)
                .subtract(weekDay - 1, "day")
                .toDate();
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "本月",
            onClick: (picker) => {
              let end = new Date();
              let start = dayjs(dayjs(end).format("YYYY-MM") + "01").toDate();
              picker.$emit("pick", [start, end]);
            },
          },
        ],
      },
    };
  },
  methods: {
    //获得当前用户下的权限门店
    getstoreEntityList() {
      var that = this;
      that.loading = true;
      EntityAPI.getStoreEntityList()
        .then((res) => {
          if (res.StateCode == 200) {
            that.storeEntityList = res.Data;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },

    //实收统计列表
    getEntityMonthlyStatementPayOverview(params) {
      var that = this;
      that.entityMonthlyStatementPayOverviewLoading = true;
      API.getEntityMonthlyStatementPayOverview(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.entityMonthlyStatementPayOverview = res.Data;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.entityMonthlyStatementPayOverviewLoading = false;
        });
    },

    //实收统计合计
    getPayOverviewSummaries(param) {
      const { data } = param;
      const sums = [];
      sums[0] = <span class="font_weight_600">合计</span>;
      sums[1] = Enumerable.from(data).sum((i) => {
        return i.SalePayAmount;
      });
      sums[2] = Enumerable.from(data).sum((i) => {
        return i.ArrearPayAmount;
      });
      sums[3] = Enumerable.from(data).sum((i) => {
        return i.RefunAmount;
      });

      var filter_NumFormat = this.$options.filters["NumFormat"];

      if ((parseFloat(sums[1]) + parseFloat(sums[2]) - parseFloat(sums[3])).toFixed(2) < 0) {
        sums[4] = <span class="font_weight_600 color_red">{filter_NumFormat((parseFloat(sums[1]) + parseFloat(sums[2]) - parseFloat(sums[3])).toFixed(2))}</span>;
      } else {
        sums[4] = <span class="font_weight_600">{filter_NumFormat((parseFloat(sums[1]) + parseFloat(sums[2]) - parseFloat(sums[3])).toFixed(2))}</span>;
      }
      sums[1] = <span class="font_weight_600">{filter_NumFormat(parseFloat(sums[1]).toFixed(2))}</span>;
      sums[2] = <span class="font_weight_600">{filter_NumFormat(parseFloat(sums[2]).toFixed(2))}</span>;
      if (parseFloat(sums[3]).toFixed(2) > 0) {
        sums[3] = <span class="font_weight_600 color_red">-{filter_NumFormat(parseFloat(sums[3]).toFixed(2))}</span>;
      } else {
        sums[3] = <span class="font_weight_600">{filter_NumFormat(parseFloat(sums[3]).toFixed(2))}</span>;
      }

      return sums;
    },

    //销售统计列表
    getEntityMonthlyStatementSaleOverview(params) {
      var that = this;
      that.entityMonthlyStatementSaleOverviewLoading = true;
      API.getEntityMonthlyStatementSaleOverview(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.entityMonthlyStatementSaleOverview = res.Data;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.entityMonthlyStatementSaleOverviewLoading = false;
        });
    },

    //销售统计合计
    getSaleOverviewSummaries(param) {
      const { data, columns } = param;
      const sums = [];
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = <span class="font_weight_600">合计</span>;
          return;
        }

        let filter_NumFormat = this.$options.filters["NumFormat"];

        let SalePayAmount = Enumerable.from(data).sum((i) => {
          return i.SalePayAmount;
        });

        let ArrearPayAmount = Enumerable.from(data).sum((i) => {
          return i.ArrearPayAmount;
        });

        let RefunPayAmount = Enumerable.from(data).sum((i) => {
          return i.RefunPayAmount;
        });

        let SaleSavingCardDeductionAmount = Enumerable.from(data).sum((i) => {
          return i.SaleSavingCardDeductionAmount;
        });

        let ArrearSavingCardDeductionAmount = Enumerable.from(data).sum((i) => {
          return i.ArrearSavingCardDeductionAmount;
        });

        let RefundSavingCardDeductionAmount = Enumerable.from(data).sum((i) => {
          return i.RefundSavingCardDeductionAmount;
        });

        let SaleLargessSavingCardDeductionAmount = Enumerable.from(data).sum((i) => {
          return i.SaleLargessSavingCardDeductionAmount;
        });

        let ArrearLargessSavingCardDeductionAmount = Enumerable.from(data).sum((i) => {
          return i.ArrearLargessSavingCardDeductionAmount;
        });
        let RefundLargessSavingCardDeductionAmount = Enumerable.from(data).sum((i) => {
          return i.RefundLargessSavingCardDeductionAmount;
        });

        let SaleLargessAmount = Enumerable.from(data).sum((i) => {
          return i.SaleLargessAmount;
        });
        let RefundLargessAmount = Enumerable.from(data).sum((i) => {
          return i.RefundLargessAmount;
        });

        switch (column.property) {
          case "SalePayAmount":
            {
              sums[index] = <span class="font_weight_600">{filter_NumFormat(parseFloat(SalePayAmount).toFixed(2))}</span>;
            }
            break;
          case "SaleSavingCardDeductionAmount":
            {
              sums[index] = <span class="font_weight_600">{filter_NumFormat(parseFloat(SaleSavingCardDeductionAmount).toFixed(2))}</span>;
            }
            break;
          case "SaleLargessSavingCardDeductionAmount":
            {
              sums[index] = <span class="font_weight_600">{filter_NumFormat(parseFloat(SaleLargessSavingCardDeductionAmount).toFixed(2))}</span>;
            }
            break;
          case "SaleLargessAmount":
            {
              sums[index] = <span class="font_weight_600">{filter_NumFormat(parseFloat(SaleLargessAmount).toFixed(2))}</span>;
            }
            break;
          case "ArrearPayAmount":
            {
              sums[index] = <span class="font_weight_600">{filter_NumFormat(parseFloat(ArrearPayAmount).toFixed(2))}</span>;
            }
            break;
          case "ArrearSavingCardDeductionAmount":
            {
              sums[index] = <span class="font_weight_600">{filter_NumFormat(parseFloat(ArrearSavingCardDeductionAmount).toFixed(2))}</span>;
            }
            break;
          case "ArrearLargessSavingCardDeductionAmount":
            {
              sums[index] = <span class="font_weight_600">{filter_NumFormat(parseFloat(ArrearLargessSavingCardDeductionAmount).toFixed(2))}</span>;
            }
            break;
          case "RefunPayAmount":
            {
              if (parseFloat(RefunPayAmount).toFixed(2) > 0) {
                sums[index] = <span class="font_weight_600 color_red">-{filter_NumFormat(parseFloat(RefunPayAmount).toFixed(2))}</span>;
              } else {
                sums[index] = <span class="font_weight_600">{filter_NumFormat(parseFloat(RefunPayAmount).toFixed(2))}</span>;
              }
            }
            break;
          case "RefundSavingCardDeductionAmount":
            {
              if (parseFloat(RefundSavingCardDeductionAmount).toFixed(2) > 0) {
                sums[index] = <span class="font_weight_600 color_red">-{filter_NumFormat(parseFloat(RefundSavingCardDeductionAmount).toFixed(2))}</span>;
              } else {
                sums[index] = <span class="font_weight_600">{filter_NumFormat(parseFloat(RefundSavingCardDeductionAmount).toFixed(2))}</span>;
              }
            }
            break;
          case "RefundLargessSavingCardDeductionAmount":
            {
              if (parseFloat(RefundLargessSavingCardDeductionAmount).toFixed(2) > 0) {
                sums[index] = <span class="font_weight_600 color_red">-{filter_NumFormat(parseFloat(RefundLargessSavingCardDeductionAmount).toFixed(2))}</span>;
              } else {
                sums[index] = <span class="font_weight_600">{filter_NumFormat(parseFloat(RefundLargessSavingCardDeductionAmount).toFixed(2))}</span>;
              }
            }
            break;
          case "RefundLargessAmount":
            {
              if (parseFloat(RefundLargessAmount).toFixed(2) > 0) {
                sums[index] = <span class="font_weight_600 color_red">-{filter_NumFormat(parseFloat(RefundLargessAmount).toFixed(2))}</span>;
              } else {
                sums[index] = <span class="font_weight_600">{filter_NumFormat(parseFloat(RefundLargessAmount).toFixed(2))}</span>;
              }
            }
            break;
          case "PayTotalAmount":
            {
              let PayTotalAmount = (parseFloat(SalePayAmount) + parseFloat(ArrearPayAmount) - parseFloat(RefunPayAmount)).toFixed(2);
              if (PayTotalAmount < 0) {
                sums[index] = <span class="font_weight_600 color_red">{filter_NumFormat(PayTotalAmount)}</span>;
              } else {
                sums[index] = <span class="font_weight_600">{filter_NumFormat(PayTotalAmount)}</span>;
              }
            }
            break;
          case "SavingCardDeductionTotalAmount":
            {
              let SavingCardDeductionTotalAmount = (parseFloat(SaleSavingCardDeductionAmount) + parseFloat(ArrearSavingCardDeductionAmount) - parseFloat(RefundSavingCardDeductionAmount)).toFixed(2);
              if (SavingCardDeductionTotalAmount < 0) {
                sums[index] = <span class="font_weight_600 color_red">{filter_NumFormat(SavingCardDeductionTotalAmount)}</span>;
              } else {
                sums[index] = <span class="font_weight_600">{filter_NumFormat(SavingCardDeductionTotalAmount)}</span>;
              }
            }
            break;
          case "LargessSavingDeductionTotalAmount":
            {
              let LargessSavingDeductionTotalAmount = (parseFloat(SaleLargessSavingCardDeductionAmount) + parseFloat(ArrearLargessSavingCardDeductionAmount) - parseFloat(RefundLargessSavingCardDeductionAmount)).toFixed(2);
              if (LargessSavingDeductionTotalAmount < 0) {
                sums[index] = <span class="font_weight_600 color_red">{filter_NumFormat(LargessSavingDeductionTotalAmount)}</span>;
              } else {
                sums[index] = <span class="font_weight_600">{filter_NumFormat(LargessSavingDeductionTotalAmount)}</span>;
              }
            }
            break;

          case "LargessTotalAmount":
            {
              let LargessTotalAmount = (parseFloat(SaleLargessAmount) - parseFloat(RefundLargessAmount)).toFixed(2);
              if (LargessTotalAmount < 0) {
                sums[index] = <span class="font_weight_600 color_red">{filter_NumFormat(LargessTotalAmount)}</span>;
              } else {
                sums[index] = <span class="font_weight_600">{filter_NumFormat(LargessTotalAmount)}</span>;
              }
            }
            break;

          // default:
          //   {
          //     if ((parseFloat(sums[1]) + parseFloat(sums[5]) - parseFloat(sums[8])).toFixed(2) < 0) {
          //       sums[12] = <span class="font_weight_600 color_red">{filter_NumFormat((parseFloat(sums[1]) + parseFloat(sums[5]) - parseFloat(sums[8])).toFixed(2))}</span>;
          //     } else {
          //       sums[12] = <span class="font_weight_600">{filter_NumFormat((parseFloat(sums[1]) + parseFloat(sums[5]) - parseFloat(sums[8])).toFixed(2))}</span>;
          //     }

          //     if ((parseFloat(sums[2]) - parseFloat(sums[9])).toFixed(2) < 0) {
          //       sums[13] = <span class="font_weight_600 color_red">{filter_NumFormat((parseFloat(sums[2]) - parseFloat(sums[9])).toFixed(2))}</span>;
          //     } else {
          //       sums[13] = <span class="font_weight_600">{filter_NumFormat((parseFloat(sums[2]) - parseFloat(sums[9])).toFixed(2))}</span>;
          //     }

          //     if ((parseFloat(sums[3]) - parseFloat(sums[10])).toFixed(2) < 0) {
          //       sums[14] = <span class="font_weight_600 color_red">{filter_NumFormat((parseFloat(sums[3]) - parseFloat(sums[10])).toFixed(2))}</span>;
          //     } else {
          //       sums[14] = <span class="font_weight_600">{filter_NumFormat((parseFloat(sums[3]) - parseFloat(sums[10])).toFixed(2))}</span>;
          //     }
          //     if ((parseFloat(sums[4]) - parseFloat(sums[11])).toFixed(2) < 0) {
          //       sums[15] = <span class="font_weight_600 color_red">{filter_NumFormat((parseFloat(sums[4]) - parseFloat(sums[11])).toFixed(2))}</span>;
          //     } else {
          //       sums[15] = <span class="font_weight_600">{filter_NumFormat((parseFloat(sums[4]) - parseFloat(sums[11])).toFixed(2))}</span>;
          //     }
          //   }
          //   break;
        }
      });
      // sums[0] = <span class="font_weight_600">合计</span>;
      // sums[1] = Enumerable.from(data).sum((i) => {
      //   return i.SalePayAmount;
      // });
      // sums[2] = Enumerable.from(data).sum((i) => {
      //   return i.SaleSavingCardDeductionAmount;
      // });
      // sums[3] = Enumerable.from(data).sum((i) => {
      //   return i.SaleLargessSavingCardDeductionAmount;
      // });
      // sums[4] = Enumerable.from(data).sum((i) => {
      //   return i.SaleLargessAmount;
      // });
      // sums[5] = Enumerable.from(data).sum((i) => {
      //   return i.ArrearPayAmount;
      // });

      // sums[6] = Enumerable.from(data).sum((i) => {
      //   return i.ArrearSavingCardDeductionAmount;
      // });
      // sums[7] = Enumerable.from(data).sum((i) => {
      //   return i.ArrearLargessSavingCardDeductionAmount;
      // });

      // sums[8] = Enumerable.from(data).sum((i) => {
      //   return i.RefunPayAmount;
      // });
      // sums[9] = Enumerable.from(data).sum((i) => {
      //   return i.RefundSavingCardDeductionAmount;
      // });
      // sums[10] = Enumerable.from(data).sum((i) => {
      //   return i.RefundLargessSavingCardDeductionAmount;
      // });
      // sums[11] = Enumerable.from(data).sum((i) => {
      //   return i.RefundLargessAmount;
      // });
      // var filter_NumFormat = this.$options.filters["NumFormat"];

      // if ((parseFloat(sums[1]) + parseFloat(sums[5]) - parseFloat(sums[8])).toFixed(2) < 0) {
      //   sums[12] = <span class="font_weight_600 color_red">{filter_NumFormat((parseFloat(sums[1]) + parseFloat(sums[5]) - parseFloat(sums[8])).toFixed(2))}</span>;
      // } else {
      //   sums[12] = <span class="font_weight_600">{filter_NumFormat((parseFloat(sums[1]) + parseFloat(sums[5]) - parseFloat(sums[8])).toFixed(2))}</span>;
      // }

      // if ((parseFloat(sums[2]) - parseFloat(sums[9])).toFixed(2) < 0) {
      //   sums[13] = <span class="font_weight_600 color_red">{filter_NumFormat((parseFloat(sums[2]) - parseFloat(sums[9])).toFixed(2))}</span>;
      // } else {
      //   sums[13] = <span class="font_weight_600">{filter_NumFormat((parseFloat(sums[2]) - parseFloat(sums[9])).toFixed(2))}</span>;
      // }

      // if ((parseFloat(sums[3]) - parseFloat(sums[10])).toFixed(2) < 0) {
      //   sums[14] = <span class="font_weight_600 color_red">{filter_NumFormat((parseFloat(sums[3]) - parseFloat(sums[10])).toFixed(2))}</span>;
      // } else {
      //   sums[14] = <span class="font_weight_600">{filter_NumFormat((parseFloat(sums[3]) - parseFloat(sums[10])).toFixed(2))}</span>;
      // }
      // if ((parseFloat(sums[4]) - parseFloat(sums[11])).toFixed(2) < 0) {
      //   sums[15] = <span class="font_weight_600 color_red">{filter_NumFormat((parseFloat(sums[4]) - parseFloat(sums[11])).toFixed(2))}</span>;
      // } else {
      //   sums[15] = <span class="font_weight_600">{filter_NumFormat((parseFloat(sums[4]) - parseFloat(sums[11])).toFixed(2))}</span>;
      // }

      // sums[1] = <span class="font_weight_600">{filter_NumFormat(parseFloat(sums[1]).toFixed(2))}</span>;
      // sums[2] = <span class="font_weight_600">{filter_NumFormat(parseFloat(sums[2]).toFixed(2))}</span>;
      // sums[3] = <span class="font_weight_600">{filter_NumFormat(parseFloat(sums[3]).toFixed(2))}</span>;
      // sums[4] = <span class="font_weight_600">{filter_NumFormat(parseFloat(sums[4]).toFixed(2))}</span>;
      // sums[5] = <span class="font_weight_600">{filter_NumFormat(parseFloat(sums[5]).toFixed(2))}</span>;

      // if (parseFloat(sums[8]).toFixed(2) > 0) {
      //   sums[8] = <span class="font_weight_600 color_red">-{filter_NumFormat(parseFloat(sums[8]).toFixed(2))}</span>;
      // } else {
      //   sums[8] = <span class="font_weight_600">{filter_NumFormat(parseFloat(sums[8]).toFixed(2))}</span>;
      // }
      // if (parseFloat(sums[9]).toFixed(2) > 0) {
      //   sums[9] = <span class="font_weight_600 color_red">-{filter_NumFormat(parseFloat(sums[9]).toFixed(2))}</span>;
      // } else {
      //   sums[9] = <span class="font_weight_600">{filter_NumFormat(parseFloat(sums[9]).toFixed(2))}</span>;
      // }
      // if (parseFloat(sums[10]).toFixed(2) > 0) {
      //   sums[10] = <span class="font_weight_600 color_red">-{filter_NumFormat(parseFloat(sums[10]).toFixed(2))}</span>;
      // } else {
      //   sums[10] = <span class="font_weight_600">{filter_NumFormat(parseFloat(sums[10]).toFixed(2))}</span>;
      // }
      // if (parseFloat(sums[11]).toFixed(2) > 0) {
      //   sums[11] = <span class="font_weight_600 color_red">-{filter_NumFormat(parseFloat(sums[11]).toFixed(2))}</span>;
      // } else {
      //   sums[11] = <span class="font_weight_600">{filter_NumFormat(parseFloat(sums[11]).toFixed(2))}</span>;
      // }

      return sums;
    },

    //消耗统计列表
    getEntityMonthlyStatementTreatOverview(params) {
      var that = this;
      that.entityMonthlyStatementTreatOverviewLoading = true;
      API.getEntityMonthlyStatementTreatOverview(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.entityMonthlyStatementTreatOverview = res.Data;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.entityMonthlyStatementTreatOverviewLoading = false;
        });
    },

    //消耗统计合计
    getTreatOverviewSummaries(param) {
      const { data } = param;
      const sums = [];
      sums[0] = <span class="font_weight_600">合计</span>;
      /**  消耗本金  */
      sums[1] = Enumerable.from(data).sum((i) => {
        return i.TreatPayAmount;
      });
      /**  消耗卡本金  */
      sums[2] = Enumerable.from(data).sum((i) => {
        return i.TreatCardAmount;
      });
      /**  消耗卡赠金  */
      sums[3] = Enumerable.from(data).sum((i) => {
        return i.TreatCardLargessAmount;
      });
      /**  消耗赠金  */
      sums[4] = Enumerable.from(data).sum((i) => {
        return i.TreatLargessAmount;
      });

      /**  退消耗本金  */
      sums[5] = Enumerable.from(data).sum((i) => {
        return i.RefundTreatPayAmount;
      });
      /**  退消耗卡本金  */
      sums[6] = Enumerable.from(data).sum((i) => {
        return i.RefundTreatCardAmount;
      });
      /**  退消耗卡赠金  */
      sums[7] = Enumerable.from(data).sum((i) => {
        return i.RefundTreatCardLargessAmount;
      });
      /**  退消耗赠金  */
      sums[8] = Enumerable.from(data).sum((i) => {
        return i.RefundTreatLargessAmount;
      });

      var filter_NumFormat = this.$options.filters["NumFormat"];
      /**  消耗现金合计  */
      if ((parseFloat(sums[1]) - parseFloat(sums[5])).toFixed(2) < 0) {
        sums[9] = <span class="font_weight_600 color_red">{filter_NumFormat((parseFloat(sums[1]) - parseFloat(sums[5])).toFixed(2))}</span>;
      } else {
        sums[9] = <span class="font_weight_600">{filter_NumFormat((parseFloat(sums[1]) - parseFloat(sums[5])).toFixed(2))}</span>;
      }
      /**  消耗卡本金合计  */
      if ((parseFloat(sums[2]) - parseFloat(sums[6])).toFixed(2) < 0) {
        sums[10] = <span class="font_weight_600 color_red">{filter_NumFormat((parseFloat(sums[2]) - parseFloat(sums[6])).toFixed(2))}</span>;
      } else {
        sums[10] = <span class="font_weight_600">{filter_NumFormat((parseFloat(sums[2]) - parseFloat(sums[6])).toFixed(2))}</span>;
      }

      /**  卡赠金合计  */
      if ((parseFloat(sums[3]) - parseFloat(sums[7])).toFixed(2) < 0) {
        sums[11] = <span class="font_weight_600 color_red">{filter_NumFormat((parseFloat(sums[3]) - parseFloat(sums[7])).toFixed(2))}</span>;
      } else {
        sums[11] = <span class="font_weight_600">{filter_NumFormat((parseFloat(sums[3]) - parseFloat(sums[7])).toFixed(2))}</span>;
      }

      /**  赠金合计  */
      if ((parseFloat(sums[4]) - parseFloat(sums[8])).toFixed(2) < 0) {
        sums[12] = <span class="font_weight_600 color_red">{filter_NumFormat((parseFloat(sums[4]) - parseFloat(sums[8])).toFixed(2))}</span>;
      } else {
        sums[12] = <span class="font_weight_600">{filter_NumFormat((parseFloat(sums[4]) - parseFloat(sums[8])).toFixed(2))}</span>;
      }

      /**  本金合计  */
      sums[1] = <span class="font_weight_600">{filter_NumFormat(parseFloat(sums[1]).toFixed(2))}</span>;
      /**  卡本金合计  */
      sums[2] = <span class="font_weight_600">{filter_NumFormat(parseFloat(sums[2]).toFixed(2))}</span>;
      /**  卡赠金合计  */
      sums[3] = <span class="font_weight_600">{filter_NumFormat(parseFloat(sums[3]).toFixed(2))}</span>;
      /**  赠金合计  */
      sums[4] = <span class="font_weight_600">{filter_NumFormat(parseFloat(sums[4]).toFixed(2))}</span>;
      /**  退本金合计  */
      if (parseFloat(sums[5]).toFixed(2) > 0) {
        sums[5] = <span class="font_weight_600 color_red">-{filter_NumFormat(parseFloat(sums[5]).toFixed(2))}</span>;
      } else {
        sums[5] = <span class="font_weight_600">{filter_NumFormat(parseFloat(sums[5]).toFixed(2))}</span>;
      }
      /**  退卡本金合计  */
      if (parseFloat(sums[6]).toFixed(2) > 0) {
        sums[6] = <span class="font_weight_600 color_red">-{filter_NumFormat(parseFloat(sums[6]).toFixed(2))}</span>;
      } else {
        sums[6] = <span class="font_weight_600">{filter_NumFormat(parseFloat(sums[6]).toFixed(2))}</span>;
      }
      /**  退卡赠金合计  */
      if (parseFloat(sums[7]).toFixed(2) > 0) {
        sums[7] = <span class="font_weight_600 color_red">-{filter_NumFormat(parseFloat(sums[7]).toFixed(2))}</span>;
      } else {
        sums[7] = <span class="font_weight_600">{filter_NumFormat(parseFloat(sums[7]).toFixed(2))}</span>;
      }
      /**  退赠金  */
      if (parseFloat(sums[8]).toFixed(2) > 0) {
        sums[8] = <span class="font_weight_600 color_red">-{filter_NumFormat(parseFloat(sums[8]).toFixed(2))}</span>;
      } else {
        sums[8] = <span class="font_weight_600">{filter_NumFormat(parseFloat(sums[8]).toFixed(2))}</span>;
      }
      return sums;
    },

    // 搜索
    handleSearch() {
      var that = this;
      if (that.searchData.QueryDate != null) {
        if (dayjs(that.searchData.QueryDate[0]).add(366, "day").valueOf() < dayjs(that.searchData.QueryDate[1]).valueOf()) {
          that.$message.error("时间筛选范围不能超366天");
          return;
        }
        var params = {
          EntityID: that.searchData.EntityID,
          StartDate: that.searchData.QueryDate[0],
          EndDate: that.searchData.QueryDate[1],
        };
        that.getEntityMonthlyStatementPayOverview(params);
        that.getEntityMonthlyStatementSaleOverview(params);
        that.getEntityMonthlyStatementTreatOverview(params);
      }
    },
  },
  mounted() {
    var that = this;
    that.getstoreEntityList();
    that.handleSearch();
  },
};
</script>

<style lang="scss"></style>
