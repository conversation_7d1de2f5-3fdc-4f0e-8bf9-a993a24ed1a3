/**
 * Created by preference on 2022/07/13
 *  zmx 
 */

import * as API from '@/api/index'
export default {
  /** 获取授权地址  */
  getAuthorizUrl: params => {
    return API.GET('api/open/offiaccount/getAuthorizUrl', params)
  },
  /**  获取openID */
  getUseropenid: params => {
    return API.POST('api/open/offiaccount/getUseropenid', params)
  },
   /**  获取验证码 */
   sendMicroMallVerificationCode: params => {
    return API.POST('api/open/offiaccount/sendMicroMallVerificationCode', params)
  },
   /**  获取openID */
   setUserBindNetwork: params => {
    return API.POST('api/open/offiaccount/userBind', params)
  },
}