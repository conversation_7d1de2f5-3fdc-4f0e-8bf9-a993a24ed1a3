<template>
  <div class="content_body InventoryProductInbound" v-loading="loading">
    <div class="nav_header">
      <el-row>
        <el-col :span="22">
          <el-form :inline="true" size="small" :model="searchForm" @keyup.enter.native="searchDataList">
            <el-form-item v-if="EntityList.length > 1" label="仓库/门店">
              <el-select v-model="searchForm.EntityID" clearable filterable placeholder="请选择仓库/门店" :default-first-option="true" @change="searchDataList">
                <el-option v-for="item in EntityList" :key="item.ID" :label="item.EntityName" :value="item.ID"> </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="产品">
              <el-input
                v-model="searchForm.ProductName"
                placeholder="请输入产品名称、别名"
                clearable
                @keyup.enter.native="searchDataList"
                @clear="searchDataList"
              ></el-input>
            </el-form-item>
            <el-form-item label="入库类型">
              <el-select
                v-model="searchForm.InventoryType"
                clearable
                filterable
                placeholder="请选择入库类型"
                :default-first-option="true"
                @change="searchDataList"
                @clear="searchDataList"
              >
                <el-option v-for="item in OutType" :key="item" :label="item" :value="item"> </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="制单日期">
              <el-date-picker
                v-model="searchForm.DateTime"
                unlink-panels
                type="daterange"
                range-separator="至"
                value-format="yyyy-MM-dd"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :picker-options="pickerOptions"
                clearable
                @change="searchDataList"
              ></el-date-picker>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="searchDataList" v-prevent-click>搜索</el-button>
            </el-form-item>
          </el-form>
        </el-col>

        <el-col :span="2" class="text_right">
          <el-button type="primary" @click="addInventoryProductInbound" size="small" v-prevent-click>新增</el-button>
        </el-col>
      </el-row>
    </div>

    <el-table size="small" :data="data_list" tooltip-effect="light">
      <el-table-column prop="ID" label="单据号"></el-table-column>
      <el-table-column prop="EntityName" label="仓库/门店"></el-table-column>
      <el-table-column prop="InventoryType" label="入库类型"></el-table-column>
      <el-table-column prop="CreatedOn" label="制单时间">
        <template slot-scope="scope">
          {{ scope.row.CreatedOn | dateFormat("YYYY-MM-DD HH:mm") }}
        </template>
      </el-table-column>
      <el-table-column prop="CreatedByName" label="操作人"></el-table-column>
      <el-table-column prop="Remark" label="备注" show-overflow-tooltip></el-table-column>
      <el-table-column label="操作" width="80px">
        <template slot-scope="scope">
          <el-button type="primary" size="small" @click="showInventoryProductInbound(scope.row)" v-prevent-click>详情</el-button>
        </template>
      </el-table-column>
    </el-table>

    <div class="pad_15 text_right">
      <el-pagination
        background
        v-if="paginations.total > 0"
        @current-change="InventoryProductInboundHandleCurrentChange"
        :current-page.sync="paginations.page"
        :page-size="paginations.page_size"
        :layout="paginations.layout"
        :total="paginations.total"
      ></el-pagination>
    </div>

    <!-- 新建产品入库  -->
    <el-dialog
      custom-class="addInboundDialogClass"
      title="产品入库 - 其他入库"
      :visible.sync="addInventoryProductInboundDialogVisible"
      width="1000px"
      @close="closeAddInventoryProductInbound"
    >
      <div class="tip">基本信息</div>
      <el-form
        class="addInboundDialogClassInfoFrom"
        :inline="true"
        :inline-message="true"
        label-width="100px"
        size="small"
        :model="InboundFrom"
        :rules="InboundFromRules"
        ref="InboundFromRef"
      >
        <el-row>
          <el-col :span="12">
            <el-form-item label="仓库/门店：" prop="EntityID">
              <el-select
                size="small"
                value-key="ID"
                v-model="InboundFrom.EntityName"
                filterable
                placeholder="请选择仓库/门店"
                @change="handleSelectEntity"
                class="ProductInBound_Entity-select-width"
              >
                <el-option value-key="ID" v-for="item in EntityList" :key="item.ID" :label="item.EntityName" :value="item"></el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="入库时间：" prop="InDate">
              <el-date-picker
                v-model="InboundFrom.InDate"
                placeholder="选择出库时间"
                type="datetime"
                value-format="yyyy-MM-dd HH:mm"
                :picker-options="pickerOptions"
                :default-time="InboundFrom.default_time"
                class="ProductInBound_Entity-select-width"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row class="martp_10">
          <el-col :span="12">
            <el-form-item label="备注信息：">
              <el-input
                v-model="InboundFrom.Remark"
                placeholder="请输入备注信息"
                size="small"
                class="ProductInBound_Entity-select-width"
                style="width: 300px"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <div class="tip marbm_10 martp_10">产品明细</div>
        <el-row>
          <el-col :span="24">
            <el-button type="primary" size="small" @click="addProducts">新增产品</el-button>
            <el-button type="danger" size="small" @click="removeMultipleProduct" :disabled="removeDisabled">删除产品</el-button>
          </el-col>
        </el-row>
        <el-table
          empty-text="暂无产品"
          size="small"
          class="martp_10"
          max-height="400px"
          :data="InboundFrom.Detail"
          @selection-change="removeHandleChangeSelectProduct"
        >
          <el-table-column type="selection" width="55"></el-table-column>
          <el-table-column label="产品名称" prop="ProductName" width="250">
            <template slot-scope="scope">
              <el-form-item :show-message="false" :prop="'Detail.' + scope.$index + '.ProductID'" :rules="InboundFromRules.ProductID">
                <el-tooltip
                  :disabled="getShowProductNameTooltip(scope.row.ProductName)"
                  class="item"
                  effect="dark"
                  :content="scope.row.ProductName"
                  placement="top"
                >
                  <el-select
                    v-model="scope.row.ProductID"
                    size="small"
                    filterable
                    remote
                    reserve-keyword
                    v-loadmore="loadMoreProduct"
                    placeholder="请选择产品"
                    :remote-method="searchProductListMethod"
                    :loading="productLoading"
                    @change="(val) => handleSelectProduct(val, scope.row)"
                    @focus="selectFocus"
                    @clear="clearSelectProduct(scope.row)"
                    :default-first-option="true"
                    popper-class="InventoryProductInbound_custom_popper_class"
                    class="ProductInBound_Entity-select-width"
                  >
                    <el-option v-for="item in ProductList" :key="item.ID" :label="formatProductName(item.ProductName)" :value="item.ID" :disabled="item.IsLock">
                      <div class="dis_flex flex_dir_column pad_5_0">
                        <div>
                          <span>{{ item.ProductName }}</span>
                          <span class="color_gray marlt_5 font_12" v-if="item.Alias">({{ item.Alias }})</span>
                          <el-tag v-if="item.IsLock" size="mini" type="warning" effect="dark">{{ "盘点锁定" }}</el-tag>
                        </div>
                        <div :class="item.ID == scope.row.ID ? 'font_12 dis_flex  flex_x_between' : 'color_gray font_12 dis_flex flex_x_between'">
                          <span class="">{{ item.PCategoryName }}</span>
                          <span v-if="item.Specification">{{ item.Specification }}</span>
                        </div>
                      </div>
                    </el-option>
                  </el-select>
                </el-tooltip>
              </el-form-item>
            </template>
          </el-table-column>
          <el-table-column prop="Specification" label="产品规格">
            <template slot-scope="scope">{{ scope.row.Specification }}</template>
          </el-table-column>
          <el-table-column label="入库单位" prop="UnitName">
            <template slot-scope="scope">
              <el-form-item :show-message="false" :prop="'Detail.' + scope.$index + '.UnitName'" :rules="InboundFromRules.UnitID">
                <el-select
                  value-key="UnitID"
                  v-model="scope.row.UnitName"
                  size="small"
                  filterable
                  placeholder="请选择单位"
                  @change="(val) => handleSelectProductUnit(val, scope.row)"
                  :default-first-option="true"
                >
                  <el-option v-for="item in scope.row.Units" :key="item.UnitID" :label="item.UnitName" :value="item"></el-option>
                </el-select>
              </el-form-item>
            </template>
          </el-table-column>
          <el-table-column label="入库数量" prop="Quantity">
            <template slot-scope="scope">
              <el-form-item :show-message="false" :prop="'Detail.' + scope.$index + '.Quantity'" :rules="InboundFromRules.Quantity">
                <el-input
                  class="input_type"
                  v-model="scope.row.Quantity"
                  size="small"
                  placeholder="请输入入库数量"
                  validate-event
                  v-enter-number2
                  v-enterInt
                  min="0"
                  type="number"
                  @input="changeInboundQuantity(scope.row)"
                ></el-input>
              </el-form-item>
            </template>
          </el-table-column>
          <el-table-column label="最小包装数量">
            <template slot-scope="scope" v-if="scope.row.Quantity">{{ scope.row.Quantity * scope.row.miniAmount }} {{ scope.row.miniUnitName }}</template>
          </el-table-column>
        </el-table>

        <!-- 附件上传 -->
        <div class="tip marbm_10 martp_20">附件上传</div>
        <div class="attachment-upload-area">
          <el-upload
            action="#"
            list-type="picture-card"
            :file-list="InboundFrom.AttachmentList"
            :before-upload="handleAttachmentUpload"
            :on-remove="removeAttachment"
            multiple
            accept="image/*,.pdf,.doc,.docx,.xls,.xlsx"
            :limit="10"
            :on-exceed="handleAttachmentExceed"
          >
            <!-- 加号图标 -->
            <i slot="default" class="el-icon-plus"></i>
            <div slot="file" slot-scope="{ file }">
              <el-image
                v-if="isImageFile(file)"
                :ref="`previewImg_${file.uid}`"
                fit="cover"
                class="el-upload-list__item-thumbnail"
                :src="file.AttachmentURL || file.url"
                :preview-src-list="getImagePreviewList()"
                style="height: 100%; width: 100%"
              />

              <div class="document_i" v-else>
                <div>
                  <i :class="getFileIcon(file)" style="font-size: 90px; color: #aaaaaa; margin-top: 15px"></i>
                </div>
                <div class="pad_0_10" style="width: 100%; box-sizing: border-box">{{ getFileName(file) }}</div>
              </div>

              <span class="el-upload-list__item-actions">
                <span v-if="isImageFile(file)" class="el-upload-list__item-preview" @click="handlePictureCardPreview(file)">
                  <i class="el-icon-zoom-in"></i>
                </span>
                <span class="el-upload-list__item-delete" @click="removeAttachment(file)">
                  <i class="el-icon-delete"></i>
                </span>
              </span>
            </div>
          </el-upload>
          <div class="upload-tip">
            <p>支持上传图片、PDF、Word、Excel等文件，单个文件不超过10MB，最多上传10个文件</p>
            <p>支持格式：jpg、png、gif、pdf、doc、docx、xls、xlsx</p>
          </div>
        </div>
      </el-form>

      <div slot="footer">
        <el-button @click="addInventoryProductInboundDialogVisible = false" size="small" v-prevent-click>取 消</el-button>
        <el-button type="primary" @click="saveInventoryProductInboundClick()" :loading="InboundLoading" size="small" v-prevent-click>确认入库</el-button>
      </div>
    </el-dialog>

    <!-- 详情 -->
    <el-dialog custom-class="addInboundDialogClass" title="入库详情" :visible.sync="InboundInfoDialogVisible" width="1000px">
      <div class="tip">基本信息</div>
      <el-form class="addInboundDialogClassInfoFrom" :inline="true" :inline-message="true" label-width="100px" size="small" :model="InboundInfo">
        <el-row>
          <el-col :span="8">
            <el-form-item label="单据号：">{{ InboundInfo.ID }}</el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="仓库/门店：">{{ InboundInfo.EntityName }}</el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="操作人：">{{ InboundInfo.CreatedByName }}</el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="入库类型：">{{ InboundInfo.InventoryType }}</el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="入库时间：">{{ InboundInfo.InDate | dateFormat("YYYY-MM-DD HH:mm") }}</el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="制单时间：">{{ InboundInfo.CreatedOn | dateFormat("YYYY-MM-DD HH:mm") }}</el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="备注信息：">{{ InboundInfo.Remark }}</el-form-item>
          </el-col>
        </el-row>

        <div class="tip marbm_10">产品明细</div>
        <el-table size="small" max-height="450px" :data="InboundInfo.Detail">
          <el-table-column label="产品" prop="ProductName">
            <template slot-scope="scope">
              <div>
                {{ scope.row.ProductName }}<span v-if="scope.row.Alias" class="font_12 color_999">({{ scope.row.Alias }})</span>
              </div>
              <div v-if="scope.row.Specification" class="font_12 color_999">规格：{{ scope.row.Specification }}</div>
            </template>
          </el-table-column>
          <el-table-column label="产品分类" prop="PCategoryName"> </el-table-column>
          <el-table-column label="入库数量" prop="Quantity">
            <template slot-scope="scope">
              <div>{{ scope.row.Quantity }} {{ scope.row.UnitName }}</div>
              <div class="color_gray font_12">最小包装数量：{{ scope.row.MinimumUnitQuantity }} {{ scope.row.MinimumUnitName }}</div>
            </template>
          </el-table-column>
        </el-table>

        <!-- 附件信息 -->
        <div v-if="InboundInfo.AttachmentList && InboundInfo.AttachmentList.length > 0" class="attachment-section martp_20">
          <div class="section-title">
            <i class="el-icon-paperclip"></i>
            <span>相关附件 ({{ InboundInfo.AttachmentList.length }})</span>
          </div>

          <div class="attachment-list-display">
            <div
              v-for="attachment in InboundInfo.AttachmentList"
              :key="attachment.ID"
              class="attachment-item-display"
            >
              <!-- 图片附件 -->
              <div v-if="attachment.AttachmentType === 10" class="attachment-image">
                <el-image
                  :src="attachment.AttachmentURL"
                  fit="cover"
                  class="attachment-thumbnail-display"
                  :preview-src-list="getDetailImagePreviewList()"
                >
                  <div slot="error" class="image-error">
                    <i class="el-icon-picture-outline"></i>
                    <div>图片加载失败</div>
                  </div>
                </el-image>
                <div class="attachment-info">
                  <div class="attachment-name" :title="attachment.AttachmentName">
                    {{ attachment.AttachmentName }}
                  </div>
                  <div class="attachment-meta">
                    <span class="attachment-size">{{ attachment.FileSizeFormatted || formatFileSize(attachment.FileSize) }}</span>
                    <span class="attachment-time">{{ attachment.CreatedOn | dateFormat('MM-DD HH:mm') }}</span>
                  </div>
                  <div v-if="attachment.Remark" class="attachment-remark">
                    {{ attachment.Remark }}
                  </div>
                </div>
              </div>

              <!-- 文档附件 -->
              <div v-else class="attachment-document">
                <div class="document-icon">
                  <i :class="getDetailFileIcon(attachment)" class="file-icon-large"></i>
                </div>
                <div class="attachment-info">
                  <div class="attachment-name" :title="attachment.AttachmentName">
                    {{ attachment.AttachmentName }}
                  </div>
                  <div class="attachment-meta">
                    <span class="attachment-type">{{ attachment.AttachmentTypeName || getAttachmentTypeName(attachment.AttachmentType) }}</span>
                    <span class="attachment-size">{{ attachment.FileSizeFormatted || formatFileSize(attachment.FileSize) }}</span>
                    <span class="attachment-time">{{ attachment.CreatedOn | dateFormat('MM-DD HH:mm') }}</span>
                  </div>
                  <div v-if="attachment.Remark" class="attachment-remark">
                    {{ attachment.Remark }}
                  </div>
                  <div class="attachment-actions">
                    <el-button
                      type="text"
                      size="mini"
                      @click="previewDetailAttachment(attachment)"
                      icon="el-icon-view"
                    >
                      预览
                    </el-button>
                    <el-button
                      type="text"
                      size="mini"
                      @click="downloadAttachment(attachment)"
                      icon="el-icon-download"
                    >
                      下载
                    </el-button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 无附件提示 -->
        <div v-else class="no-attachment-tip martp_20">
          <i class="el-icon-document"></i>
          <span>暂无相关附件</span>
        </div>
      </el-form>

      <div slot="footer">
        <el-button v-show="templateTypeList && templateTypeList.length == 0" type="primary" @click="printInfoTips" size="small" v-prevent-click>打印</el-button>
        <el-button
          v-show="templateTypeList && templateTypeList.length == 1"
          type="primary"
          v-print="'printContent'"
          @click="printInfo"
          size="small"
          v-prevent-click
          >打印</el-button
        >
        <el-button v-show="templateTypeList && templateTypeList.length > 1" type="primary" @click="printInfoSelectTemplate" size="small" v-prevent-click
          >打印</el-button
        >
      </div>
    </el-dialog>
    <!-- 打印模板选择 -->
    <el-dialog title="选择打印模板" :visible.sync="printTemplateVisible" width="400px">
      <el-select size="small" v-model="printTemplateID" @change="changePrintTemplate">
        <el-option v-for="item in templateTypeList" :key="item.ID" :label="item.Name" :value="item.ID"></el-option>
      </el-select>
      <div slot="footer">
        <el-button @click="printTemplateVisible = false" size="small" v-prevent-click>取消</el-button>
        <el-button v-print="'printContent'" type="primary" @click="confirmPrintTemplate" size="small" v-prevent-click>打印</el-button>
      </div>
    </el-dialog>
    <div style="display: none">
      <component id="printContent" :is="printComponentName"></component>
    </div>
  </div>
</template>

<script>
// import permission from "@/components/js/permission.js";

import API from "@/api/PSI/Inventory/inventoryProductInbound";
import APIStorage from "@/api/PSI/Purchase/storage";
import APIInventory from "@/api/PSI/Inventory/inventoryDetail";

import dateUtil from "@/components/js/date";
var Enumerable = require("linq");

import print from "vue-print-nb";
import Vue from "vue";

export default {
  name: "InventoryProductInbound",
  directives: {
    print,
  },
  /**  引入的组件  */
  components: {},
  /**  Vue 实例的数据对象**/

  data() {
    return {
      printComponentName: "",
      printContent: "",
      printTemplateVisible: false,
      printTemplateID: "",
      templateTypeList: [],

      loading: false,
      addInventoryProductInboundDialogVisible: false,
      selectProductDialogVisible: false,
      InboundLoading: false,
      InboundInfoDialogVisible: false,
      productLoading: false,

      // 列表筛选条件
      searchForm: {
        EntityID: "",
        ProductName: "",
        DateTime: "",
        InventoryType: "",
      },
      OutType: ["采购入库", "要货入库",  "退货入库", "要货退货入库", "消耗取消入库", "其他入库"],
      // "调拨入库",
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now() ? true : false;
        },
      },
      paginations: {
        page: 1, // 当前位于哪页
        total: 0, // 总数
        page_size: 10, // 1页显示多少条
        layout: "total, prev, pager, next, jumper", // 翻页属性
      }, //需要给分页组件传的信息
      InboundFrom: {
        EntityID: "",
        EntityName: "",
        InDate: dateUtil.formatDate.format(new Date(), "YYYY-MM-DD hh:mm:ss"),
        default_time: dateUtil.formatDate.format(new Date(), "hh:mm:ss"),
        Remark: "",
        Detail: [],
        AttachmentList: [],
      },
      InboundFromRules: {
        EntityID: [
          {
            required: true,
            message: "请选择出库仓库/门店",
            trigger: ["blur", "change"],
          },
        ],
        InDate: [
          {
            required: true,
            message: "请选择出库日期",
            trigger: ["blur", "change"],
          },
        ],
        Quantity: [{ required: true, trigger: ["blur", "change"] }],
        ProductID: [{ required: true, trigger: ["blur", "change"] }],
        UnitID: [{ required: true, trigger: ["blur", "change"] }],
      },

      EntityList: [], //仓库列表
      removeDisabled: true,
      ProductList: [],
      ProductListTotal: 0,
      productPageNum: 1,
      multipleProducts: [],
      selectProductList: [],
      data_list: [],
      InboundInfo: {
        ID: 111,
      },
    };
  },
  /**计算属性  */
  computed: {},
  /**  方法集合  */
  methods: {
    /** 确定选择打印模板，并打印   */
    confirmPrintTemplate() {
      let that = this;
      that.printTemplateVisible = false;
      that.$nextTick(() => {
        that.createPrintComponent(that.InboundInfo, that.printContent);
      });
    },
    /** 修改打印模板   */
    changePrintTemplate(val) {
      let that = this;
      let tempItem = that.templateTypeList.filter((item) => item.ID == val);
      that.printContent = tempItem[0].Template;
    },
    // 创建打印组件
    createPrintComponent(info, printContent) {
      let tempInfo = info; //传入打印数据源
      let templateStr = printContent; //传入打印模板
      var timestamp = new Date().valueOf();
      var componentName = "print" + timestamp;

      //创建组件
      Vue.component(componentName, {
        data: function () {
          return {
            info: tempInfo, //传入打印数据源
          };
        },
        template: "<div>" + templateStr + "</div>", //打印模板
      });
      this.printComponentName = componentName; //显示打印组件
    },

    /**  打印  */
    /**    */
    printInfoTips() {
      let that = this;
      that.$message.error("暂无打印模板，请添加打印模板");
    },
    printInfoSelectTemplate() {
      let that = this;
      that.printTemplateID = "";
      that.printTemplateVisible = true;
    },
    printInfo() {
      let that = this;
      let tempPrintTemplate = that.templateTypeList.length == 1 ? that.templateTypeList[0].Template : "";
      that.createPrintComponent(that.InboundInfo, tempPrintTemplate);
    },
    /**  搜索  */
    searchDataList() {
      let that = this;
      that.paginations.page = 1;
      that.get_list_inventoryProductInbound_netWork();
    },
    /**  分页切换  */
    InventoryProductInboundHandleCurrentChange(page) {
      let that = this;
      that.paginations.page = page;
      that.get_list_inventoryProductInbound_netWork();
    },
    /**  添加入库  */
    addInventoryProductInbound() {
      let that = this;
      that.InboundFrom = {
        EntityID: "",
        EntityName: "",
        InDate: dateUtil.formatDate.format(new Date(), "YYYY-MM-DD hh:mm:ss"),
        default_time: dateUtil.formatDate.format(new Date(), "hh:mm:ss"),
        Remark: "",
        Detail: [],
        AttachmentList: [],
      };

      if (that.EntityList.length == 1) {
        let entityItem = that.EntityList[0];
        that.InboundFrom.EntityID = entityItem.ID;
        that.InboundFrom.EntityName = entityItem.EntityName;
      }
      that.addInventoryProductInboundDialogVisible = true;
    },
    /** 查看详情   */
    showInventoryProductInbound(row) {
      let that = this;
      let parmas = {
        ID: row.ID,
        InventoryType: row.InventoryType,
      };
      that.get_info_inventoryProductInbound_netWork(parmas);
    },
    /**  关闭添加弹窗回调  */
    closeAddInventoryProductInbound() {
      let that = this;
      that.$refs["InboundFromRef"].clearValidate();
      if (that.$refs.multipleTable) {
        that.$refs.multipleTable.clearSelection();
      }
    },
    /**  选择入库仓库  */
    handleSelectEntity(row) {
      let that = this;
      that.InboundFrom.EntityID = row.ID;
      that.InboundFrom.Detail = [];
    },
    // /**   修改入库数量 */
    changeInboundQuantity(row) {
      row.Quantity = Math.floor(row.Quantity);
    },

    /**  新增产品  */
    addProducts() {
      let that = this;
      that.searchProductName = "";
      that.$refs["InboundFromRef"].validateField("EntityID", (valid) => {
        if (!valid) {
          that.InboundFrom.Detail.push({
            ProductID: "", // ID
            ProductName: "", // 名称
            Alias: "", // 别名
            Specification: "", // 规格
            UnitID: "", // 单位ID
            UnitName: "", // 单位名称
            Units: [], // 单位集合
            Quantity: "", // 申请数量
            StockQuantity: "", // 库存
          });
        }
      });
    },
    /**  选择将要删除的产品  */
    removeHandleChangeSelectProduct(selection) {
      this.multipleProducts = selection;
      if (this.multipleProducts.length > 0) {
        this.removeDisabled = false;
      } else {
        this.removeDisabled = true;
      }
    },
    /**  删除所选产品  */
    removeMultipleProduct() {
      var that = this;
      if (that.multipleProducts.length > 0) {
        for (var i = 0; i < that.InboundFrom.Detail.length; i++) {
          that.multipleProducts.forEach(function (item) {
            if (that.InboundFrom.Detail[i] == item) {
              that.InboundFrom.Detail.splice(i, 1);
            }
          });
        }
      }
    },

    /**  下拉选择产品  */
    handleSelectProduct(item_ID, row) {
      var that = this;
      // let item = Enumerable.from(that.ProductList).firstOrDefault(i=>{return item_ID == i.ProductID},{})

      // row.ProductID = item.ProductID;
      // row.Units = item.Unit;
      // row.Specification = item.Specification;

      row.Quantity = "";
      let item = Enumerable.from(that.ProductList).firstOrDefault((i) => {
        return item_ID == i.ID;
      }, {});
      row.ProductID = item.ID;
      row.ProductName = item.ProductName;
      row.Units = item.Unit;
      row.Specification = item.Specification;

      let defaultUnit = Enumerable.from(item.Unit).firstOrDefault((i) => {
        return i.IsDefautSendReceive;
      }, -1);
      let miniUnit = Enumerable.from(item.Unit).firstOrDefault((i) => {
        return i.IsMinimumUnit;
      }, -1);
      if (defaultUnit != -1) {
        row.UnitID = defaultUnit.UnitID;
        row.UnitName = defaultUnit.UnitName;
        row.miniAmount = defaultUnit.Amount;
      }
      if (miniUnit != -1) {
        row.miniUnitName = miniUnit.UnitName;
        row.MinimumUnitID = miniUnit.UnitID;
      }
    },
    /** 获取焦点   */
    selectFocus() {
      let that = this;
      that.ProductList = [];
      that.searchProductListMethod("");
    },
    /** 远程搜索产品   */
    searchProductListMethod(query) {
      var that = this;
      that.productPageNum = 1;
      that.ProductList = [];
      that.get_stock_list_entityProductListNetwork(query);
    },
    /**  加载更多产品  */
    loadMoreProduct() {
      var that = this;
      if (that.ProductListTotal > that.ProductList.length) {
        that.productPageNum++;
        that.get_stock_list_entityProductListNetwork();
      }
    },
    /**   选择单位 */
    handleSelectProductUnit(val, row) {
      row.Quantity = "";
      row.UnitID = val.UnitID;
      row.UnitName = val.UnitName;
      row.miniAmount = val.Amount;
    },

    /**  保存产品入库  */
    saveInventoryProductInboundClick() {
      let that = this;
      if (that.InboundFrom.Detail.length == 0) {
        that.$message.error({
          message: "请选择入库产品",
          duration: 2000,
        });
        return;
      }
      that.$refs["InboundFromRef"].validate((valid) => {
        if (valid) {
          let Products = Enumerable.from(that.InboundFrom.Detail)
            .select((i) => ({
              ProductID: i.ProductID,
              UnitID: i.UnitID,
              Quantity: i.Quantity,
              MinimumUnitID: i.MinimumUnitID,
              MinimumUnitQuantity: parseFloat(i.Quantity) * parseFloat(i.miniAmount),
            }))
            .toArray();
          // 构建附件数据
          const attachmentList = that.InboundFrom.AttachmentList.map(item => ({
            AttachmentName: item.AttachmentName,
            AttachmentURL: item.AttachmentURL,
            AttachmentType: item.AttachmentType,
            MimeType: item.MimeType,
            FileSize: item.FileSize,
            Remark: item.Remark || ''
          }));

          let params = {
            EntityID: that.InboundFrom.EntityID,
            InDate: that.InboundFrom.InDate,
            Remark: that.InboundFrom.Remark,
            Detail: Products,
            AttachmentList: attachmentList,
          };
          that.get_create_inventoryProductInbound_netWork(params);
        }
      });
    },

    /** ++++++++++++++ ============================================= +++++++++++++ */

    /**  仓库列表  */
    getStorageEntityNetwork: function () {
      var that = this;
      var params = {};
      APIStorage.getpurchaseStorageEntity(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.EntityList = res.Data;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {});
    },

    /** 查询 产品 库存列表 列表  */
    get_stock_list_entityProductListNetwork: function (searchProductName) {
      var that = this;
      var params = {
        PageNum: that.productPageNum,
        ProductName: searchProductName,
        EntityID: that.InboundFrom.EntityID,
      };
      APIInventory.get_stock_list_entityProductListAll(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.ProductListTotal = res.Total;
            that.ProductList.push.apply(that.ProductList, res.List);
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {});
    },

    /**   10.1.产品入库列表   */
    get_list_inventoryProductInbound_netWork: function () {
      var that = this;
      that.loading = true;
      var params = {
        PageNum: that.paginations.page,
        // ID: that.searchForm.ID,
        EntityID: that.searchForm.EntityID,
        ProductName: that.searchForm.ProductName,
        StartDate: that.searchForm.DateTime == null ? "" : that.searchForm.DateTime[0],
        EndDate: that.searchForm.DateTime == null ? "" : that.searchForm.DateTime[1],
        InventoryType: that.searchForm.InventoryType,
      };
      API.get_list_inventoryProductInbound(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.data_list = res.List;
            that.paginations.page_size = res.PageSize;
            that.paginations.total = res.Total;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },
    /**   10.2.产品入库详情    */
    get_info_inventoryProductInbound_netWork: function (params) {
      var that = this;
      that.loading = true;
      // var params = {
      //   ID: ID
      // };
      API.get_info_inventoryProductInbound(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.InboundInfo = res.Data;
            that.InboundInfoDialogVisible = true;
            that.printContent = "";
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },
    /**  10.3.产品入库   */
    get_create_inventoryProductInbound_netWork: function (params) {
      var that = this;
      API.get_create_inventoryProductInbound(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.addInventoryProductInboundDialogVisible = false;
            that.searchDataList();
            that.$message.success({
              message: "操作成功",
              duration: 2000,
            });
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {});
    },
    /** 获取模板列表   */
    async getPrintTemplate_list() {
      let that = this;
      let params = { TemplateType: "instock" };
      let res = await API.getPrintTemplate_list(params);
      if (res.StateCode == 200) {
        that.templateTypeList = res.Data;
      } else {
        that.$message.error(res.Message);
      }
    },

    // ========== 附件上传相关方法 ==========

    // 处理附件上传
    async handleAttachmentUpload(file) {
      // 检查文件类型
      const allowedTypes = [
        'image/jpeg', 'image/png', 'image/gif',
        'application/pdf',
        'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      ];

      if (!allowedTypes.includes(file.type)) {
        this.$message.error('不支持的文件格式，请上传图片、PDF、Word或Excel文件');
        return false;
      }

      // 检查文件大小（10MB）
      const isLt10M = file.size / 1024 / 1024 < 10;
      if (!isLt10M) {
        this.$message.error('文件大小不能超过10MB');
        return false;
      }

      // 检查文件数量限制
      if (this.InboundFrom.AttachmentList.length >= 10) {
        this.$message.error('最多只能上传10个文件');
        return false;
      }

      try {
        // 将文件转换为base64
        const base64 = await this.fileToBase64(file);

        // 确定附件类型
        let attachmentType = 30; // 默认其他类型
        if (file.type.startsWith('image/')) {
          attachmentType = 10; // 图片
        } else if (file.type === 'application/pdf' ||
                   file.type.includes('word') ||
                   file.type.includes('excel') ||
                   file.type.includes('sheet')) {
          attachmentType = 20; // 文档
        }

        // 生成唯一ID
        const uid = Date.now() + Math.random();

        // 添加到附件列表
        const attachment = {
          uid: uid,
          AttachmentName: file.name,
          AttachmentURL: base64,
          AttachmentType: attachmentType,
          MimeType: file.type,
          FileSize: file.size,
          Remark: '',
          // 用于显示的属性
          name: file.name,
          url: base64,
          status: 'success'
        };

        this.InboundFrom.AttachmentList.push(attachment);

        this.$message.success('文件上传成功');

      } catch (error) {
        console.error('文件上传失败:', error);
        this.$message.error('文件上传失败，请重试');
      }

      // 阻止默认上传行为
      return false;
    },

    // 将文件转换为base64
    fileToBase64(file) {
      return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.readAsDataURL(file);
        reader.onload = () => resolve(reader.result);
        reader.onerror = error => reject(error);
      });
    },

    // 移除附件
    removeAttachment(file) {
      const index = this.InboundFrom.AttachmentList.findIndex(item =>
        item.uid === file.uid || item.AttachmentName === file.name
      );
      if (index > -1) {
        this.InboundFrom.AttachmentList.splice(index, 1);
        this.$message.success('附件已删除');
      }
    },

    // 预览图片文件
    handlePictureCardPreview(file) {
      // 使用ref直接调用el-image的clickHandler方法
      this.$nextTick(() => {
        const refName = `previewImg_${file.uid}`;
        const imageRef = this.$refs[refName];
        if (imageRef && imageRef.length > 0) {
          imageRef[0].clickHandler();
        } else if (imageRef) {
          imageRef.clickHandler();
        }
      });
    },

    // 判断是否为图片文件
    isImageFile(file) {
      if (file.MimeType) {
        return file.MimeType.startsWith('image/');
      }
      if (file.type) {
        return file.type.startsWith('image/');
      }
      // 根据文件名判断
      const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'];
      const fileName = file.AttachmentName || file.name || '';
      return imageExtensions.some(ext => fileName.toLowerCase().endsWith(ext));
    },

    // 获取文件图标
    getFileIcon(file) {
      const mimeType = file.MimeType || file.type || '';

      if (mimeType.includes('pdf')) {
        return 'el-icon-document';
      } else if (mimeType.includes('word')) {
        return 'el-icon-document';
      } else if (mimeType.includes('excel') || mimeType.includes('sheet')) {
        return 'el-icon-s-grid';
      } else {
        return 'el-icon-document';
      }
    },

    // 获取文件名（截取显示）
    getFileName(file) {
      const name = file.AttachmentName || file.name || '未知文件';
      return name.length > 15 ? name.substring(0, 12) + '...' : name;
    },

    // 获取图片预览列表
    getImagePreviewList() {
      return this.InboundFrom.AttachmentList
        .filter(file => this.isImageFile(file))
        .map(file => file.AttachmentURL || file.url);
    },

    // 处理文件数量超出限制
    handleAttachmentExceed() {
      this.$message.warning('最多只能上传10个文件');
    },

    // ========== 详情附件展示相关方法 ==========

    // 获取详情页面图片预览列表
    getDetailImagePreviewList() {
      if (!this.InboundInfo.AttachmentList) return [];
      return this.InboundInfo.AttachmentList
        .filter(attachment => attachment.AttachmentType === 10)
        .map(attachment => attachment.AttachmentURL);
    },

    // 获取详情页面文件图标
    getDetailFileIcon(attachment) {
      const mimeType = attachment.MimeType || '';

      if (mimeType.includes('pdf')) {
        return 'el-icon-document';
      } else if (mimeType.includes('word')) {
        return 'el-icon-document';
      } else if (mimeType.includes('excel') || mimeType.includes('sheet')) {
        return 'el-icon-s-grid';
      } else {
        return 'el-icon-document';
      }
    },

    // 获取附件类型名称
    getAttachmentTypeName(type) {
      switch (type) {
        case 10: return '图片';
        case 20: return '文档';
        case 30: return '其他';
        default: return '未知';
      }
    },

    // 格式化文件大小
    formatFileSize(bytes) {
      if (!bytes) return '0 B';
      const k = 1024;
      const sizes = ['B', 'KB', 'MB', 'GB'];
      const i = Math.floor(Math.log(bytes) / Math.log(k));
      return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
    },

    // 预览详情附件
    previewDetailAttachment(attachment) {
      if (attachment.AttachmentType === 10) {
        // 图片预览由el-image组件自动处理
        return;
      } else {
        // 非图片文件，在新窗口打开
        if (attachment.AttachmentURL) {
          window.open(attachment.AttachmentURL, '_blank');
        } else {
          this.$message.info('该文件暂无预览地址');
        }
      }
    },

    // 下载附件
    downloadAttachment(attachment) {
      if (attachment.AttachmentURL) {
        // 创建一个临时的a标签来触发下载
        const link = document.createElement('a');
        link.href = attachment.AttachmentURL;
        link.download = attachment.AttachmentName;
        link.target = '_blank';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      } else {
        this.$message.error('附件下载地址不存在');
      }
    },
    /**  格式化明细商品名称 中间以... 代替  */
    formatProductName(name) {
      if (!name) return "";
      if (name.length > 20) {
        let frontStr = name.substr(0, 8);
        let afterStr = name.substr(name.length - 10, name.length);
        return frontStr + " ... " + afterStr;
      }
      return name;
    },
    /**  获取产品是否显示提示  */
    getShowProductNameTooltip(name) {
      if (!name || name == "") {
        return true;
      }
      if (name.length > 20) {
        return false;
      }
      return true;
    },
  },
  /**  实例被挂载后调用  */
  mounted() {
    var that = this;
    // var date = new Date(),
    // y = date.getFullYear(),
    // m = date.getMonth();
    // that.searchForm.DateTime = [dateUtil.formatDate.format(new Date(y, m, 1),"YYYY-MM-DD"),dateUtil.formatDate.format(new Date(),"YYYY-MM-DD")];
    that.searchDataList();
    that.getStorageEntityNetwork();
  },
  created() {
    let that = this;
    that.getPrintTemplate_list();
  },
};
</script>

<style lang="scss">
.InventoryProductInbound {
  .addInboundDialogClass {
    .addInboundDialogClassInfoFrom {
      .el-form-item__label {
        font-size: 13px !important;
      }
      .el-form-item__content {
        font-size: 13px !important;
      }
      .el-form-item {
        margin-bottom: 0px;
      }

      .el-input__inner {
        padding-right: 0;
      }
    }
  }
  .IsLockProduct_list_back {
    background-color: #edf2fc;
    cursor: not-allowed;
  }
  .IsLockProduct_list_back:hover > td {
    background-color: #edf2fc !important;
  }

  // 附件上传样式
  .attachment-upload-area {
    .document_i {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 100%;
      text-align: center;
    }

    .pad_0_10 {
      padding: 0 10px;
      font-size: 12px;
      color: #606266;
      word-break: break-all;
      line-height: 1.2;
    }

    .upload-tip {
      margin-top: 10px;
      color: #909399;
      font-size: 12px;
      line-height: 1.5;

      p {
        margin: 0;
      }
    }
  }

  // 详情附件展示样式
  .attachment-section {
    border-top: 1px solid #ebeef5;
    padding-top: 15px;

    .section-title {
      display: flex;
      align-items: center;
      margin-bottom: 15px;
      font-weight: 500;
      color: #303133;

      i {
        margin-right: 8px;
        color: #409eff;
      }
    }

    .attachment-list-display {
      display: flex;
      flex-wrap: wrap;
      gap: 15px;

      .attachment-item-display {
        border: 1px solid #ebeef5;
        border-radius: 6px;
        overflow: hidden;
        background: #fff;
        transition: all 0.3s;

        &:hover {
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        // 图片附件样式
        .attachment-image {
          width: 200px;

          .attachment-thumbnail-display {
            width: 100%;
            height: 120px;
            object-fit: cover;

            .image-error {
              display: flex;
              flex-direction: column;
              align-items: center;
              justify-content: center;
              height: 120px;
              background: #f5f7fa;
              color: #909399;

              i {
                font-size: 24px;
                margin-bottom: 5px;
              }

              div {
                font-size: 12px;
              }
            }
          }

          .attachment-info {
            padding: 10px;
          }
        }

        // 文档附件样式
        .attachment-document {
          width: 250px;
          display: flex;
          padding: 15px;

          .document-icon {
            margin-right: 15px;
            display: flex;
            align-items: center;

            .file-icon-large {
              font-size: 32px;
              color: #409eff;
            }
          }

          .attachment-info {
            flex: 1;
            min-width: 0;
          }
        }

        .attachment-info {
          .attachment-name {
            font-weight: 500;
            color: #303133;
            margin-bottom: 5px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }

          .attachment-meta {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-bottom: 5px;

            span {
              font-size: 12px;
              color: #909399;
              background: #f5f7fa;
              padding: 2px 6px;
              border-radius: 3px;
            }
          }

          .attachment-remark {
            font-size: 12px;
            color: #606266;
            margin-bottom: 8px;
            line-height: 1.4;
          }

          .attachment-actions {
            display: flex;
            gap: 5px;

            .el-button--text {
              padding: 0;
              font-size: 12px;
            }
          }
        }
      }
    }
  }

  .no-attachment-tip {
    text-align: center;
    color: #909399;
    padding: 20px;
    border-top: 1px solid #ebeef5;

    i {
      font-size: 24px;
      margin-right: 8px;
    }
  }

  .martp_20 {
    margin-top: 20px;
  }
}

.InventoryProductInbound_custom_popper_class {
  .el-select-dropdown__item {
    line-height: normal;
    height: auto;
  }
}
@media print {
  html,
  body {
    height: inherit;
  }
}
.ProductInBound_Entity-select-width {
  width: 230px;
}
</style>

