/**
 * Created by wsf on 2022/05/17
 *  门店交易报表 api
 */

import * as API from '@/api/index'

export default {
  /* 获取门店交易报表 */
  getEntityTradingList: params => {
    return API.POST('api/entityTrading/list', params)
  },
  // 导出
  entityTradingExcel: params => {
    return API.exportExcel('api/entityTrading/excel', params)
  },
  // 门店
  getStoreList: params => {
    return API.POST('api/reportEntity/storeList ', params)
  },
}
