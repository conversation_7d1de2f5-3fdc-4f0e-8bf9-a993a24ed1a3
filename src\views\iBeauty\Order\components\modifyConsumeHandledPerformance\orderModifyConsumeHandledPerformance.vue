<template>
  <div class="orderModifyConsumeHandledPerformance">
    <el-dialog :visible.sync="visible_" width="1380px" @close="closeModifyPerformance">
      <span slot="title">修改经手人业绩提成</span>
      <div style="height: 60vh" class="font_13">
        <el-scrollbar class="el-scrollbar_height">
          <!-- 修改详情项目 !-->
          <div v-if="orderDetail.Project.length > 0">
            <div v-for="(item, index) in orderDetail.Project" :key="index">
              <el-row class="border_bottom tipback_col pad_10">
                <el-col :span="6">项目</el-col>
                <el-col :span="4">数量</el-col>
                <el-col :span="6" v-show="treatInfo.BillType == '10'">优惠金额</el-col>
                <el-col :span="8">消耗金额</el-col>
              </el-row>
              <el-row class="pad_10 border_bottom border_left border_right">
                <el-col :span="6">
                  <div>
                    {{ item.ProjectName }}
                    <span v-if="item.Alias">({{ item.Alias }})</span>
                    <el-tag v-if="item.IsLargess" size="mini" type="danger" class="marlt_10">赠</el-tag>
                  </div>
                  <div class="color_red martp_5 font_12">¥ {{ item.Price | toFixed | NumFormat }}</div>
                </el-col>
                <el-col :span="4">x {{ item.Quantity }}</el-col>
                <el-col :span="6" v-show="treatInfo.BillType == '10'">
                  <div>
                    <span v-if="item.CardPreferentialAmount < 0">+ ¥ {{ Math.abs(item.CardPreferentialAmount) | toFixed | NumFormat }}</span>
                    <span v-else-if="item.CardPreferentialAmount > 0">- ¥ {{ Math.abs(item.CardPreferentialAmount) | toFixed | NumFormat }}</span>
                    <span v-else>¥ 0.00</span>
                  </div>
                </el-col>
                <el-col :span="8">
                  <div>¥ {{ (item.PayAmount + item.CardDeductionAmount + item.LargessCardDeductionAmount + item.LargessAmount) | toFixed | NumFormat }}</div>
                  <div class="martp_5 font_12">
                    <span class="color_green" v-if="item.PayAmount > 0">现金金额：¥ {{ item.PayAmount | toFixed | NumFormat }}</span>
                    <span class="color_green" v-if="item.CardDeductionAmount > 0" :class="item.PayAmount != 0 ? 'marlt_15' : ''"
                      >卡抵扣金额：¥ {{ item.CardDeductionAmount | toFixed | NumFormat }}</span
                    >
                    <span class="color_red" v-if="item.LargessCardDeductionAmount > 0" :class="item.CardDeductionAmount != 0 ? 'marlt_15' : ''"
                      >赠送卡扣金额：¥ {{ item.LargessCardDeductionAmount | toFixed | NumFormat }}</span
                    >
                    <span class="color_red" v-if="item.LargessAmount > 0" :class="item.LargessCardDeductionAmount != 0 ? 'marlt_15' : ''"
                      >赠送金额：¥ {{ item.LargessAmount | toFixed | NumFormat }}</span
                    >
                  </div>
                </el-col>
              </el-row>
              <el-row class="padlt_10 padrt_10 border_right border_left border_bottom font_12" v-for="(handler, index) in item.TreatBillHandler" :key="index">
                <el-col :span="2" class="padtp_10 padbm_10">
                  <el-link class="font_12 line_26" type="primary" :underline="false" @click="employeeHandleClick(1, item)">{{
                    handler.TreatHandlerName
                  }}</el-link>
                </el-col>
                <el-col :span="22" class="border_left">
                  <el-row v-for="(employee, index) in handler.Employee" :key="employee.EmployeeID">
                    <el-col :span="5" class="padtp_10 padrt_10 " :class="index != 0 ? 'border_top' : ''">
                      <el-form @submit.native.prevent class="sale-ModifyPerformanceCommission-Handler" label-position="right" size="mini" label-width="70px" :inline="true">
                        <el-form-item :label="`${employee.EmployeeName}`">
                          <el-input
                            type="number"
                            v-input-fixed
                            class="input_type"
                            style="width: 90px"
                            v-model="employee.Scale"
                            @input="changeSaleHandlerRate(item, employee)"
                          >
                            <template slot="append">%</template>
                          </el-input>
                        </el-form-item>
                        <el-form-item class="cursorclass">
                          <el-checkbox size="small" v-model="employee.IsCalculatePassengerFlow">客流</el-checkbox>
                        </el-form-item>
                      </el-form>
                    </el-col>
                    <el-col :span="15" class="border_right border_left">
                      <!-- 现金 -->
                      <el-row v-if="item.PayAmount > 0" class="padtp_10 padrt_10" :class="index != 0 ? 'border_top' : ''">
                        <el-col :span="8">
                          <el-form
                            @submit.native.prevent
                            class="sale-ModifyPerformanceCommission-Handler"
                            label-position="right"
                            size="mini"
                            label-width="120px"
                          >
                            <el-form-item label="现金业绩">
                              <span slot="label">
                                现金业绩
                                <el-popover placement="top-start" width="200" trigger="hover">
                                  <p>现金业绩 = 现金金额 x 员工现金业绩占比 x 业绩占比</p>
                                  <p>
                                    员工现金业绩占比参考值：
                                    <span v-if="employee.PerformancePayRate != null">{{ (employee.PerformancePayRate * 100) | toFixed | NumFormat }}%</span>
                                    <span v-else>无</span>
                                  </p>

                                  <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"> </el-button>
                                </el-popover>
                              </span>
                              <el-input type="number" v-input-fixed class="input_type" style="width: 120px" v-model="employee.PayPerformance">
                                <template slot="append">元</template>
                              </el-input>
                            </el-form-item>
                          </el-form>
                        </el-col>
                        <el-col :span="8">
                          <el-form
                            @submit.native.prevent
                            class="sale-ModifyPerformanceCommission-Handler"
                            label-position="right"
                            size="mini"
                            label-width="150px"
                          >
                            <el-form-item label="现金比例提成">
                              <span slot="label">
                                现金比例提成
                                <el-popover placement="top-start" width="200" trigger="hover">
                                  <p>现金比例提成 = 现金业绩 x 现金比例提成参考值</p>

                                  <p v-if="employee.PayRate != null">现金比例提成参考值：{{ employee.PayRate | toFixed | NumFormat }}%</p>
                                  <p v-else>现金比例提成参考值：无</p>
                                  <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"> </el-button>
                                </el-popover>
                              </span>
                              <el-input type="number" v-input-fixed class="input_type" style="width: 100px" v-model="employee.PayRateCommission">
                                <template slot="append">元</template>
                              </el-input>
                            </el-form-item>
                          </el-form>
                        </el-col>
                        <el-col :span="8">
                          <el-form
                            @submit.native.prevent
                            class="sale-ModifyPerformanceCommission-Handler"
                            label-position="right"
                            size="mini"
                            label-width="150px"
                          >
                            <el-form-item label="现金固定提成">
                              <span slot="label">
                                现金固定提成
                                <el-popover placement="top-start" width="200" trigger="hover">
                                  <p>现金固定提成 = （现金业绩 ÷ 消耗金额）x 现金固定提成参考值 x 数量</p>

                                  <p v-if="employee.PayFixed">
                                    现金固定提成参考值：¥
                                    {{ employee.PayFixed | toFixed | NumFormat }}
                                  </p>
                                  <p v-else>现金固定提成参考值：无</p>
                                  <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"> </el-button>
                                </el-popover>
                              </span>
                              <el-input type="number" v-input-fixed class="input_type" style="width: 100px" v-model="employee.PayFixedCommission">
                                <template slot="append">元</template>
                              </el-input>
                            </el-form-item>
                          </el-form>
                        </el-col>
                      </el-row>
                      <!-- 卡本金 -->
                      <el-row v-if="item.CardDeductionAmount > 0" class="padtp_10 padrt_10" :class="index != 0 ? 'border_top' : ''">
                        <el-col :span="8">
                          <el-form
                            @submit.native.prevent
                            class="sale-ModifyPerformanceCommission-Handler"
                            label-position="right"
                            size="mini"
                            label-width="120px"
                          >
                            <el-form-item label="卡抵扣业绩">
                              <span slot="label">
                                卡抵扣业绩
                                <el-popover placement="top-start" width="200" trigger="hover">
                                  <p>卡抵扣业绩 = 卡抵扣金额 x 员工卡抵扣业绩占比 x 业绩占比</p>
                                  <p>
                                    员工卡抵扣业绩占比参考值：
                                    <span v-if="employee.PerformanceCardRate != null">{{ (employee.PerformanceCardRate * 100) | toFixed | NumFormat }}%</span>
                                    <span v-else>无</span>
                                  </p>

                                  <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"> </el-button>
                                </el-popover>
                              </span>
                              <el-input type="number" v-input-fixed class="input_type" style="width: 120px" v-model="employee.CardPerformance">
                                <template slot="append">元</template>
                              </el-input>
                            </el-form-item>
                          </el-form>
                        </el-col>
                        <el-col :span="8">
                          <el-form
                            @submit.native.prevent
                            class="sale-ModifyPerformanceCommission-Handler"
                            label-position="right"
                            size="mini"
                            label-width="150px"
                          >
                            <el-form-item label="卡抵扣比例提成">
                              <span slot="label">
                                卡抵扣比例提成
                                <el-popover placement="top-start" width="200" trigger="hover">
                                  <p>卡抵扣比例提成 = 卡抵扣业绩 x 卡抵扣比例提成参考值</p>

                                  <p v-if="employee.CardRate != null">卡抵扣比例提成参考值：{{ employee.CardRate | toFixed | NumFormat }}%</p>
                                  <p v-else>卡抵扣比例提成参考值：无</p>
                                  <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"> </el-button>
                                </el-popover>
                              </span>
                              <el-input type="number" v-input-fixed class="input_type" style="width: 100px" v-model="employee.CardRateCommission">
                                <template slot="append">元</template>
                              </el-input>
                            </el-form-item>
                          </el-form>
                        </el-col>
                        <el-col :span="8">
                          <el-form
                            @submit.native.prevent
                            class="sale-ModifyPerformanceCommission-Handler"
                            label-position="right"
                            size="mini"
                            label-width="150px"
                          >
                            <el-form-item label="卡抵扣固定提成">
                              <span slot="label">
                                卡抵扣固定提成
                                <el-popover placement="top-start" width="200" trigger="hover">
                                  <p>卡抵扣固定提成 = （卡抵扣业绩 ÷ 消耗金额）x 卡抵扣固定提成参考值 x 数量</p>

                                  <p v-if="employee.CardFixed">
                                    卡抵扣固定提成参考值：¥
                                    {{ employee.CardFixed | toFixed | NumFormat }}
                                  </p>
                                  <p v-else>卡抵扣固定提成参考值：无</p>
                                  <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"> </el-button>
                                </el-popover>
                              </span>
                              <el-input type="number" v-input-fixed class="input_type" style="width: 100px" v-model="employee.CardFixedCommission">
                                <template slot="append">元</template>
                              </el-input>
                            </el-form-item>
                          </el-form>
                        </el-col>
                      </el-row>
                      <!-- 卡赠金 -->
                      <el-row v-if="item.LargessCardDeductionAmount > 0" class="padtp_10 padrt_10" :class="index != 0 ? 'border_top' : ''">
                        <el-col :span="8">
                          <el-form
                            @submit.native.prevent
                            class="sale-ModifyPerformanceCommission-Handler"
                            label-position="right"
                            size="mini"
                            label-width="120px"
                          >
                            <el-form-item label="赠送卡扣业绩">
                              <span slot="label">
                                赠送卡扣业绩
                                <el-popover placement="top-start" width="200" trigger="hover">
                                  <p>赠送卡抵扣业绩 = 赠送卡抵扣金额 x 赠送卡抵扣业绩占比 x 业绩占比</p>
                                  <p>
                                    员工赠送卡抵扣业绩占比参考值：
                                    <span v-if="employee.PerformanceCardLargessRate != null"
                                      >{{ (employee.PerformanceCardLargessRate * 100) | toFixed | NumFormat }}%</span
                                    >
                                    <span v-else>无</span>
                                  </p>

                                  <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"> </el-button>
                                </el-popover>
                              </span>
                              <el-input type="number" v-input-fixed class="input_type" style="width: 120px" v-model="employee.CardLargessPerformance">
                                <template slot="append">元</template>
                              </el-input>
                            </el-form-item>
                          </el-form>
                        </el-col>
                        <el-col :span="8">
                          <el-form
                            @submit.native.prevent
                            class="sale-ModifyPerformanceCommission-Handler"
                            label-position="right"
                            size="mini"
                            label-width="150px"
                          >
                            <el-form-item label="赠送卡扣比例提成">
                              <span slot="label">
                                赠送卡扣比例提成
                                <el-popover placement="top-start" width="200" trigger="hover">
                                  <p>赠送卡扣比例提成 = 赠送卡扣业绩 x 赠送卡扣比例提成参考值</p>

                                  <p v-if="employee.CardLargessRate != null">赠送卡扣比例提成参考值：{{ employee.CardLargessRate | toFixed | NumFormat }}%</p>
                                  <p v-else>赠送卡扣比例提成参考值：无</p>
                                  <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"> </el-button>
                                </el-popover>
                              </span>
                              <el-input type="number" v-input-fixed class="input_type" style="width: 100px" v-model="employee.CardLargessRateCommission">
                                <template slot="append">元</template>
                              </el-input>
                            </el-form-item>
                          </el-form>
                        </el-col>
                        <el-col :span="8">
                          <el-form
                            @submit.native.prevent
                            class="sale-ModifyPerformanceCommission-Handler"
                            label-position="right"
                            size="mini"
                            label-width="150px"
                          >
                            <el-form-item label="赠送卡扣固定提成">
                              <span slot="label">
                                赠送卡扣固定提成
                                <el-popover placement="top-start" width="200" trigger="hover">
                                  <p>赠送卡扣固定提成 = （赠送卡扣业绩 ÷ 消耗金额）x 赠送卡扣固定提成参考值 x 数量</p>

                                  <p v-if="employee.CardLargessFixed">
                                    赠送卡扣固定提成参考值：¥
                                    {{ employee.CardLargessFixed | toFixed | NumFormat }}
                                  </p>
                                  <p v-else>赠送卡扣固定提成参考值：无</p>
                                  <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"> </el-button>
                                </el-popover>
                              </span>
                              <el-input type="number" v-input-fixed class="input_type" style="width: 100px" v-model="employee.CardLargessFixedCommission">
                                <template slot="append">元</template>
                              </el-input>
                            </el-form-item>
                          </el-form>
                        </el-col>
                      </el-row>
                      <!-- 赠送 -->
                      <el-row v-if="item.LargessAmount > 0" class="padtp_10 padrt_10" :class="index != 0 ? 'border_top' : ''">
                        <el-col :span="8">
                          <el-form
                            @submit.native.prevent
                            class="sale-ModifyPerformanceCommission-Handler"
                            label-position="right"
                            size="mini"
                            label-width="120px"
                          >
                            <el-form-item label="赠送业绩">
                              <span slot="label">
                                赠送业绩
                                <el-popover placement="top-start" width="200" trigger="hover">
                                  <p>赠送业绩 = 赠送金额 x 赠送业绩占比 x 业绩占比</p>
                                  <p>
                                    员工赠送卡抵扣业绩占比参考值：
                                    <span v-if="employee.PerformanceLargessRate != null"
                                      >{{ (employee.PerformanceLargessRate * 100) | toFixed | NumFormat }}%</span
                                    >
                                    <span v-else>无</span>
                                  </p>

                                  <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"> </el-button>
                                </el-popover>
                              </span>
                              <el-input type="number" v-input-fixed class="input_type" style="width: 120px" v-model="employee.LargessPerformance">
                                <template slot="append">元</template>
                              </el-input>
                            </el-form-item>
                          </el-form>
                        </el-col>
                        <el-col :span="8">
                          <el-form
                            @submit.native.prevent
                            class="sale-ModifyPerformanceCommission-Handler"
                            label-position="right"
                            size="mini"
                            label-width="150px"
                          >
                            <el-form-item label="赠送比例提成">
                              <span slot="label">
                                赠送比例提成
                                <el-popover placement="top-start" width="200" trigger="hover">
                                  <p>赠送比例提成 = 赠送业绩 x 赠送比例提成参考值</p>
                                  <p v-if="employee.LargessRate != null">赠送比例提成参考值：{{ employee.LargessRate | toFixed | NumFormat }}%</p>
                                  <p v-else>赠送比例提成参考值：无</p>
                                  <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"> </el-button>
                                </el-popover>
                              </span>
                              <el-input type="number" v-input-fixed class="input_type" style="width: 100px" v-model="employee.LargessRateCommission">
                                <template slot="append">元</template>
                              </el-input>
                            </el-form-item>
                          </el-form>
                        </el-col>
                        <el-col :span="8">
                          <el-form
                            @submit.native.prevent
                            class="sale-ModifyPerformanceCommission-Handler"
                            label-position="right"
                            size="mini"
                            label-width="150px"
                          >
                            <el-form-item label="赠送固定提成">
                              <span slot="label">
                                赠送固定提成
                                <el-popover placement="top-start" width="200" trigger="hover">
                                  <p>赠送固定提成 = （赠送业绩 ÷ 消耗金额）x 赠送固定提成参考值 x 数量</p>
                                  <p v-if="employee.LargessFixed">
                                    赠送固定提成参考值：¥
                                    {{ employee.LargessFixed | toFixed | NumFormat }}
                                  </p>
                                  <p v-else>赠送固定提成参考值：无</p>
                                  <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"> </el-button>
                                </el-popover>
                              </span>
                              <el-input type="number" v-input-fixed class="input_type" style="width: 100px" v-model="employee.LargessFixedCommission">
                                <template slot="append">元</template>
                              </el-input>
                            </el-form-item>
                          </el-form>
                        </el-col>
                      </el-row>
                    </el-col>
                    <el-col :span="4" class="padtp_10" :class="index != 0 ? 'border_top' : ''">
                      <el-form @submit.native.prevent class="sale-ModifyPerformanceCommission-Handler" label-position="right" size="mini" label-width="90px">
                        <el-form-item label="无业绩奖励">
                          <span slot="label">
                            无业绩奖励
                            <el-popover placement="top-start" width="200" trigger="hover">
                              <p>无业绩奖励 = 无业绩奖励参考值 x 数量 x 经手人比例</p>
                              <p v-if="employee.SpecialBenefit != null">
                                无业绩奖励参考值：¥
                                {{ employee.SpecialBenefit | toFixed | NumFormat }}
                              </p>
                              <p v-else>无业绩奖励参考值：无</p>
                              <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"> </el-button>
                            </el-popover>
                          </span>
                          <el-input type="number" v-input-fixed class="input_type" style="width: 100px" v-model="employee.SpecialBenefitCommission">
                            <template slot="append">元</template>
                          </el-input>
                        </el-form-item>
                      </el-form>
                    </el-col>
                  </el-row>
                </el-col>
              </el-row>
            </div>
          </div>

          <!-- 修改员工业绩储值卡 !-->
          <div v-if="orderDetail.SavingCard.length > 0">
            <div v-for="(item, index) in orderDetail.SavingCard" :key="index">
              <el-row class="border_bottom tipback_col pad_10">
                <el-col :span="6">储值卡</el-col>
                <el-col :span="4">数量</el-col>
                <el-col :span="6" v-show="treatInfo.BillType == '10'">优惠金额</el-col>
                <el-col :span="8">消耗金额</el-col>
              </el-row>
              <el-row :span="24" style="background-color: #f5f7fa" class="pad_10 border_right border_left border_bottom">
                <div>
                  {{ item.SavingCardName }}
                  <span v-if="item.Alias">({{ item.Alias }})</span>
                </div>
              </el-row>
              <el-row v-for="(middItem, middIndex) in item.Project" :key="middIndex">
                <el-row class="pad_10 border_bottom border_left border_right">
                  <el-col :span="6">
                    <div>
                      {{ middItem.ProjectName }}
                      <span v-if="middItem.Alias">({{ middItem.Alias }})</span>
                    </div>
                    <div class="color_red martp_5 font_12">
                      ￥{{ middItem.Price | toFixed | NumFormat }}
                      <el-tag v-if="middItem.IsLargess" size="mini" class="marlt_10">赠</el-tag>
                    </div>
                  </el-col>
                  <el-col :span="4">x {{ middItem.Quantity }}</el-col>
                  <el-col :span="6" v-show="treatInfo.BillType == '10'">
                    <div>
                      <span v-if="middItem.PricePreferentialAmount + middItem.CardPreferentialAmount < 0"
                        >+ ¥ {{ Math.abs(middItem.PricePreferentialAmount + middItem.CardPreferentialAmount) | toFixed | NumFormat }}</span
                      >
                      <span v-else-if="middItem.PricePreferentialAmount + middItem.CardPreferentialAmount > 0"
                        >- ¥ {{ Math.abs(middItem.PricePreferentialAmount + middItem.CardPreferentialAmount) | toFixed | NumFormat }}</span
                      >
                      <span v-else>¥ 0.00</span>
                    </div>
                    <div class="martp_5 font_12">
                      <span class="color_gray font_12" v-if="middItem.PricePreferentialAmount != 0">
                        手动改价：
                        <span class="color_red" v-if="middItem.PricePreferentialAmount > 0">
                          - ¥
                          {{ middItem.PricePreferentialAmount | toFixed | NumFormat }}
                        </span>
                        <span class="color_green" v-else>
                          + ¥
                          {{ Math.abs(middItem.PricePreferentialAmount) | toFixed | NumFormat }}
                        </span>
                      </span>
                      <span
                        class="color_gray font_12"
                        :class="middItem.PricePreferentialAmount != 0 ? 'marlt_15' : ''"
                        v-if="middItem.CardPreferentialAmount > 0"
                      >
                        卡优惠：
                        <span class="color_red">
                          - ¥
                          {{ parseFloat(middItem.CardPreferentialAmount) | toFixed | NumFormat }}
                        </span>
                      </span>
                      <span class="color_gray font_12" :class="middItem.PricePreferentialAmount != 0 || middItem.CardPreferentialAmount != 0 ? 'marlt_15' : ''" v-if="middItem.MemberPreferentialAmount > 0">
                        会员优惠：
                        <span class="color_red">- ¥ {{ parseFloat(middItem.MemberPreferentialAmount) | toFixed | NumFormat }}</span>
                      </span>
                    </div>
                  </el-col>
                  <el-col :span="8">
                    <div>¥ {{ middItem.TotalAmount | toFixed | NumFormat }}</div>
                    <div class="martp_5 font_12">
                      <!-- <span class="color_green" v-if="middItem.PayAmount > 0">现金金额： {{ middItem.PayAmount | NumFormat }}</span> -->
                      <span class="color_green" v-if="middItem.CardDeductionAmount > 0"
                        >卡抵扣金额： {{ middItem.CardDeductionAmount | toFixed | NumFormat }}</span
                      >

                      <span class="color_red" v-if="middItem.LargessCardDeductionAmount > 0" :class="middItem.CardDeductionAmount != 0 ? 'marlt_15' : ''"
                        >赠送卡抵扣金额： {{ middItem.LargessCardDeductionAmount | toFixed | NumFormat }}</span
                      >

                      <!-- <span class="color_red" v-if="middItem.LargessAmount > 0" :class="middItem.LargessCardDeductionAmount != 0 ? 'marlt_15' : ''">赠送金额：¥ {{ middItem.LargessAmount | NumFormat }}</span> -->
                    </div>
                  </el-col>
                </el-row>

                <el-row
                  class="padlt_10 padrt_10 border_right border_left border_bottom font_12"
                  v-for="(handler, index) in middItem.TreatBillHandler"
                  :key="index"
                >
                  <el-col :span="2" class="padtp_10 padbm_10">
                    <el-link class="font_12 line_26" type="primary" :underline="false" @click="employeeHandleClick(3, middItem)">{{
                      handler.TreatHandlerName
                    }}</el-link>
                  </el-col>
                  <el-col :span="22" class="border_left">
                    <el-row v-for="(employee, index) in handler.Employee" :key="employee.EmployeeID">
                      <el-col :span="5" class="padtp_10 padrt_10 " :class="index != 0 ? 'border_top' : ''">
                        <el-form @submit.native.prevent class="sale-ModifyPerformanceCommission-Handler" label-position="right" size="mini" label-width="70px" :inline="true">
                          <el-form-item :label="`${employee.EmployeeName}`">
                            <el-input
                              type="number"
                              v-input-fixed
                              class="input_type"
                              style="width: 90px"
                              v-model="employee.Scale"
                              @input="changeSaleHandlerRate(middItem, employee)"
                            >
                              <template slot="append">%</template>
                            </el-input>
                          </el-form-item>
                          <el-form-item class="cursorclass">
                            <el-checkbox size="small" v-model="employee.IsCalculatePassengerFlow">客流</el-checkbox>
                          </el-form-item>
                        </el-form>
                      </el-col>

                      <el-col :span="15" class="border_right border_left">
                        <!-- 现金 -->
                        <el-row v-if="middItem.PayAmount > 0" class="padtp_10 padrt_10" :class="index != 0 ? 'border_top' : ''">
                          <el-col :span="8">
                            <el-form
                              @submit.native.prevent
                              class="sale-ModifyPerformanceCommission-Handler"
                              label-position="right"
                              size="mini"
                              label-width="120px"
                            >
                              <el-form-item label="现金业绩">
                                <span slot="label">
                                  现金业绩
                                  <el-popover placement="top-start" width="200" trigger="hover">
                                    <p>现金业绩 = 现金金额 x 员工现金业绩占比 x 业绩占比</p>
                                    <p>
                                      员工现金业绩占比参考值：
                                      <span v-if="employee.PerformancePayRate != null">{{ (employee.PerformancePayRate * 100) | toFixed | NumFormat }}%</span>
                                      <span v-else>无</span>
                                    </p>

                                    <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"> </el-button>
                                  </el-popover>
                                </span>
                                <el-input type="number" v-input-fixed class="input_type" style="width: 120px" v-model="employee.PayPerformance">
                                  <template slot="append">元</template>
                                </el-input>
                              </el-form-item>
                            </el-form>
                          </el-col>
                          <el-col :span="8">
                            <el-form
                              @submit.native.prevent
                              class="sale-ModifyPerformanceCommission-Handler"
                              label-position="right"
                              size="mini"
                              label-width="150px"
                            >
                              <el-form-item label="现金比例提成">
                                <span slot="label">
                                  现金比例提成
                                  <el-popover placement="top-start" width="200" trigger="hover">
                                    <p>现金比例提成 = 现金业绩 x 现金比例提成参考值</p>

                                    <p v-if="employee.PayRate != null">现金比例提成参考值：{{ employee.PayRate | toFixed | NumFormat }}%</p>
                                    <p v-else>现金比例提成参考值：无</p>
                                    <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"> </el-button>
                                  </el-popover>
                                </span>
                                <el-input type="number" v-input-fixed class="input_type" style="width: 100px" v-model="employee.PayRateCommission">
                                  <template slot="append">元</template>
                                </el-input>
                              </el-form-item>
                            </el-form>
                          </el-col>
                          <el-col :span="8">
                            <el-form
                              @submit.native.prevent
                              class="sale-ModifyPerformanceCommission-Handler"
                              label-position="right"
                              size="mini"
                              label-width="150px"
                            >
                              <el-form-item label="现金固定提成">
                                <span slot="label">
                                  现金固定提成
                                  <el-popover placement="top-start" width="200" trigger="hover">
                                    <p>现金固定提成 = （现金业绩 ÷ 消耗金额）x 现金固定提成参考值 x 数量</p>

                                    <p v-if="employee.PayFixed">
                                      现金固定提成参考值：¥
                                      {{ employee.PayFixed | toFixed | NumFormat }}
                                    </p>
                                    <p v-else>现金固定提成参考值：无</p>
                                    <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"> </el-button>
                                  </el-popover>
                                </span>
                                <el-input type="number" v-input-fixed class="input_type" style="width: 100px" v-model="employee.PayFixedCommission">
                                  <template slot="append">元</template>
                                </el-input>
                              </el-form-item>
                            </el-form>
                          </el-col>
                        </el-row>
                        <!-- 卡本金 -->
                        <el-row v-if="middItem.CardDeductionAmount > 0" class="padtp_10 padrt_10" :class="index != 0 ? 'border_top' : ''">
                          <el-col :span="8">
                            <el-form
                              @submit.native.prevent
                              class="sale-ModifyPerformanceCommission-Handler"
                              label-position="right"
                              size="mini"
                              label-width="120px"
                            >
                              <el-form-item label="卡抵扣业绩">
                                <span slot="label">
                                  卡抵扣业绩
                                  <el-popover placement="top-start" width="200" trigger="hover">
                                    <p>卡抵扣业绩 = 卡抵扣金额 x 员工卡抵扣业绩占比 x 业绩占比</p>
                                    <p>
                                      员工卡抵扣业绩占比参考值：
                                      <span v-if="employee.PerformanceCardRate != null">{{ (employee.PerformanceCardRate * 100) | toFixed | NumFormat }}%</span>
                                      <span v-else>无</span>
                                    </p>

                                    <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"> </el-button>
                                  </el-popover>
                                </span>
                                <el-input type="number" v-input-fixed class="input_type" style="width: 120px" v-model="employee.CardPerformance">
                                  <template slot="append">元</template>
                                </el-input>
                              </el-form-item>
                            </el-form>
                          </el-col>
                          <el-col :span="8">
                            <el-form
                              @submit.native.prevent
                              class="sale-ModifyPerformanceCommission-Handler"
                              label-position="right"
                              size="mini"
                              label-width="150px"
                            >
                              <el-form-item label="卡抵扣比例提成">
                                <span slot="label">
                                  卡抵扣比例提成
                                  <el-popover placement="top-start" width="200" trigger="hover">
                                    <p>卡抵扣比例提成 = 卡抵扣业绩 x 卡抵扣比例</p>

                                    <p v-if="employee.CardRate != null">卡抵扣比例参考值：{{ employee.CardRate | toFixed | NumFormat }}%</p>
                                    <p v-else>卡抵扣比例参考值：无</p>
                                    <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"> </el-button>
                                  </el-popover>
                                </span>
                                <el-input type="number" v-input-fixed class="input_type" style="width: 100px" v-model="employee.CardRateCommission">
                                  <template slot="append">元</template>
                                </el-input>
                              </el-form-item>
                            </el-form>
                          </el-col>
                          <el-col :span="8">
                            <el-form
                              @submit.native.prevent
                              class="sale-ModifyPerformanceCommission-Handler"
                              label-position="right"
                              size="mini"
                              label-width="150px"
                            >
                              <el-form-item label="卡抵扣固定提成">
                                <span slot="label">
                                  卡抵扣固定提成
                                  <el-popover placement="top-start" width="200" trigger="hover">
                                    <p>卡抵扣固定提成 = （卡抵扣业绩 ÷ 消耗金额）x 卡抵扣固定提成参考值 x 数量</p>

                                    <p v-if="employee.CardFixed">
                                      卡抵扣固定参考值：¥
                                      {{ employee.CardFixed | toFixed | NumFormat }}
                                    </p>
                                    <p v-else>卡抵扣固定参考值：无</p>
                                    <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"> </el-button>
                                  </el-popover>
                                </span>
                                <el-input type="number" v-input-fixed class="input_type" style="width: 100px" v-model="employee.CardFixedCommission">
                                  <template slot="append">元</template>
                                </el-input>
                              </el-form-item>
                            </el-form>
                          </el-col>
                        </el-row>
                        <!-- 卡赠金 -->
                        <el-row v-if="middItem.LargessCardDeductionAmount > 0" class="padtp_10 padrt_10" :class="index != 0 ? 'border_top' : ''">
                          <el-col :span="8">
                            <el-form
                              @submit.native.prevent
                              class="sale-ModifyPerformanceCommission-Handler"
                              label-position="right"
                              size="mini"
                              label-width="120px"
                            >
                              <el-form-item label="赠送卡扣业绩">
                                <span slot="label">
                                  赠送卡扣业绩
                                  <el-popover placement="top-start" width="200" trigger="hover">
                                    <p>赠送卡抵扣业绩 = 赠送卡抵扣金额 x 赠送卡抵扣业绩占比 x 业绩占比</p>
                                    <p>
                                      员工赠送卡抵扣业绩占比参考值：
                                      <span v-if="employee.PerformanceCardLargessRate != null"
                                        >{{ (employee.PerformanceCardLargessRate * 100) | toFixed | NumFormat }}%</span
                                      >
                                      <span v-else>无</span>
                                    </p>

                                    <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"> </el-button>
                                  </el-popover>
                                </span>
                                <el-input type="number" v-input-fixed class="input_type" style="width: 120px" v-model="employee.CardLargessPerformance">
                                  <template slot="append">元</template>
                                </el-input>
                              </el-form-item>
                            </el-form>
                          </el-col>
                          <el-col :span="8">
                            <el-form
                              @submit.native.prevent
                              class="sale-ModifyPerformanceCommission-Handler"
                              label-position="right"
                              size="mini"
                              label-width="150px"
                            >
                              <el-form-item label="赠送卡扣比例提成">
                                <span slot="label">
                                  赠送卡扣比例提成
                                  <el-popover placement="top-start" width="200" trigger="hover">
                                    <p>赠送卡扣比例提成 = 赠送卡扣业绩 x 赠送卡扣比例</p>
                                    <p v-if="employee.CardLargessRate != null">赠送卡扣比例参考值：{{ employee.CardLargessRate | toFixed | NumFormat }}%</p>
                                    <p v-else>赠送卡扣比例参考值：无</p>
                                    <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"> </el-button>
                                  </el-popover>
                                </span>
                                <el-input type="number" v-input-fixed class="input_type" style="width: 100px" v-model="employee.CardLargessRateCommission">
                                  <template slot="append">元</template>
                                </el-input>
                              </el-form-item>
                            </el-form>
                          </el-col>
                          <el-col :span="8">
                            <el-form
                              @submit.native.prevent
                              class="sale-ModifyPerformanceCommission-Handler"
                              label-position="right"
                              size="mini"
                              label-width="150px"
                            >
                              <el-form-item label="赠送卡扣固定提成">
                                <span slot="label">
                                  赠送卡扣固定提成
                                  <el-popover placement="top-start" width="200" trigger="hover">
                                    <p>赠送卡扣固定提成 = （赠送卡扣业绩 ÷ 消耗金额）x 赠送卡扣固定参考值 x 数量</p>

                                    <p v-if="employee.CardLargessFixed">
                                      赠送卡扣固定参考值：¥
                                      {{ employee.CardLargessFixed | toFixed | NumFormat }}
                                    </p>
                                    <p v-else>赠送卡扣金固定参考值：无</p>
                                    <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"> </el-button>
                                  </el-popover>
                                </span>
                                <el-input type="number" v-input-fixed class="input_type" style="width: 100px" v-model="employee.CardLargessFixedCommission">
                                  <template slot="append">元</template>
                                </el-input>
                              </el-form-item>
                            </el-form>
                          </el-col>
                        </el-row>
                        <!-- 赠送 -->
                        <el-row v-if="middItem.LargessAmount > 0" class="padtp_10 padrt_10" :class="index != 0 ? 'border_top' : ''">
                          <el-col :span="8">
                            <el-form
                              @submit.native.prevent
                              class="sale-ModifyPerformanceCommission-Handler"
                              label-position="right"
                              size="mini"
                              label-width="120px"
                            >
                              <el-form-item label="赠送业绩">
                                <span slot="label">
                                  赠送业绩
                                  <el-popover placement="top-start" width="200" trigger="hover">
                                    <p>赠送业绩 = 赠送金额 x 赠送业绩占比 x 业绩占比</p>
                                    <p>
                                      员工赠送业绩占比参考值：
                                      <span v-if="employee.PerformanceLargessRate != null"
                                        >{{ (employee.PerformanceLargessRate * 100) | toFixed | NumFormat }}%</span
                                      >
                                      <span v-else>无</span>
                                    </p>

                                    <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"> </el-button>
                                  </el-popover>
                                </span>
                                <el-input type="number" v-input-fixed class="input_type" style="width: 120px" v-model="employee.LargessPerformance">
                                  <template slot="append">元</template>
                                </el-input>
                              </el-form-item>
                            </el-form>
                          </el-col>
                          <el-col :span="8">
                            <el-form
                              @submit.native.prevent
                              class="sale-ModifyPerformanceCommission-Handler"
                              label-position="right"
                              size="mini"
                              label-width="150px"
                            >
                              <el-form-item label="赠送比例提成">
                                <span slot="label">
                                  赠送比例提成
                                  <el-popover placement="top-start" width="200" trigger="hover">
                                    <p>赠送比例提成 = 赠送业绩 x 赠送比例</p>
                                    <p v-if="employee.LargessRate != null">赠送比例参考值：{{ employee.LargessRate | toFixed | NumFormat }}%</p>
                                    <p v-else>赠送比例参考值：无</p>
                                    <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"> </el-button>
                                  </el-popover>
                                </span>
                                <el-input type="number" v-input-fixed class="input_type" style="width: 100px" v-model="employee.LargessRateCommission">
                                  <template slot="append">元</template>
                                </el-input>
                              </el-form-item>
                            </el-form>
                          </el-col>
                          <el-col :span="8">
                            <el-form
                              @submit.native.prevent
                              class="sale-ModifyPerformanceCommission-Handler"
                              label-position="right"
                              size="mini"
                              label-width="150px"
                            >
                              <el-form-item label="赠送固定提成">
                                <span slot="label">
                                  赠送固定提成
                                  <el-popover placement="top-start" width="200" trigger="hover">
                                    <p>赠送固定提成 = （赠送业绩 ÷ 消耗金额）x 赠送固定 x 数量</p>
                                    <p v-if="employee.LargessFixed">
                                      赠送固定参考值：¥
                                      {{ employee.LargessFixed | toFixed | NumFormat }}
                                    </p>
                                    <p v-else>赠送固定提成参考值：无</p>
                                    <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"> </el-button>
                                  </el-popover>
                                </span>
                                <el-input type="number" v-input-fixed class="input_type" style="width: 100px" v-model="employee.LargessFixedCommission">
                                  <template slot="append">元</template>
                                </el-input>
                              </el-form-item>
                            </el-form>
                          </el-col>
                        </el-row>
                      </el-col>

                      <el-col v-if="!item.IsLargess" :span="4" class="padtp_10" :class="index != 0 ? 'border_top' : ''">
                        <el-form @submit.native.prevent class="sale-ModifyPerformanceCommission-Handler" label-position="right" size="mini" label-width="90px">
                          <el-form-item label="无业绩奖励">
                            <span slot="label">
                              无业绩奖励
                              <el-popover placement="top-start" width="200" trigger="hover">
                                <p>无业绩奖励 = 无业绩奖励参考值 x 数量 x 经手人比例</p>
                                <p v-if="employee.SpecialBenefit != null">
                                  无业绩奖励参考值：¥
                                  {{ employee.SpecialBenefit | toFixed | NumFormat }}
                                </p>
                                <p v-else>无业绩奖励参考值：无</p>
                                <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"> </el-button>
                              </el-popover>
                            </span>
                            <el-input type="number" v-input-fixed class="input_type" style="width: 100px" v-model="employee.SpecialBenefitCommission">
                              <template slot="append">元</template>
                            </el-input>
                          </el-form-item>
                        </el-form>
                      </el-col>
                    </el-row>
                  </el-col>
                </el-row>
              </el-row>
            </div>
          </div>

          <!-- 修改详情时效卡 !-->
          <div v-if="orderDetail.TimeCard.length > 0">
            <div v-for="(item, index) in orderDetail.TimeCard" :key="index">
              <el-row class="border_bottom tipback_col pad_10">
                <el-col :span="6">时效卡</el-col>
                <el-col :span="4">数量</el-col>
                <el-col :span="6" v-show="treatInfo.BillType == '10'">优惠金额</el-col>
                <el-col :span="8">消耗金额</el-col>
              </el-row>
              <el-row :span="24" style="background-color: #f5f7fa" class="pad_10 border_right border_left border_bottom">
                <div>
                  {{ item.TimeCardName }}
                  <span v-if="item.Alias">({{ item.Alias }})</span>
                  <el-tag v-if="item.IsLargess" size="mini" class="marlt_5" type="danger">赠</el-tag>
                </div>
              </el-row>

              <el-row v-for="(middItem, middIndex) in item.Project" :key="middIndex">
                <el-row class="border_right border_left border_bottom pad_10">
                  <el-col :span="6">
                    <div>
                      {{ middItem.ProjectName }}
                      <span v-if="middItem.Alias">({{ middItem.Alias }})</span>
                      <el-tag v-if="item.IsLargess" size="mini" class="marlt_10" type="danger">赠</el-tag>
                    </div>
                    <div class="color_red martp_5 font_12">￥{{ middItem.Price | toFixed | NumFormat }}</div>
                  </el-col>
                  <el-col :span="4">x {{ middItem.Quantity }}</el-col>
                  <el-col :span="6" v-show="treatInfo.BillType == '10'">
                    <div>
                      <span v-if="middItem.CardPreferentialAmount < 0">+ ¥ {{ Math.abs(middItem.CardPreferentialAmount) | toFixed | NumFormat }}</span>
                      <span v-else-if="middItem.CardPreferentialAmount > 0">- ¥ {{ Math.abs(middItem.CardPreferentialAmount) | toFixed | NumFormat }}</span>
                      <span v-else>¥ 0.00</span>
                    </div>
                    <div class="martp_5 font_12">
                      <span class="color_gray font_12" v-if="middItem.CardPreferentialAmount != 0">
                        卡优惠
                        <span class="color_red" v-if="middItem.CardPreferentialAmount > 0">
                          - ¥
                          {{ middItem.CardPreferentialAmount | toFixed | NumFormat }}
                        </span>
                        <span class="color_green" v-else>
                          + ¥
                          {{ Math.abs(middItem.CardPreferentialAmount) | toFixed | NumFormat }}
                        </span>
                      </span>
                    </div>
                  </el-col>
                  <el-col :span="8">
                    <div>¥ {{ middItem.TotalAmount | toFixed | NumFormat }}</div>

                    <div class="martp_5 font_12">
                      <span class="color_green" v-if="middItem.PayAmount > 0">现金金额： {{ middItem.PayAmount | toFixed | NumFormat }}</span>
                      <span class="color_green" v-if="middItem.CardDeductionAmount > 0" :class="middItem.PayAmount != 0 ? 'marlt_15' : ''"
                        >卡抵扣金额： {{ middItem.CardDeductionAmount | toFixed | NumFormat }}</span
                      >

                      <span class="color_red" v-if="middItem.LargessCardDeductionAmount > 0" :class="middItem.CardDeductionAmount != 0 ? 'marlt_15' : ''"
                        >赠送卡抵扣金额： {{ middItem.LargessCardDeductionAmount | toFixed | NumFormat }}</span
                      >

                      <span class="color_red" v-if="middItem.LargessAmount > 0" :class="middItem.LargessCardDeductionAmount != 0 ? 'marlt_15' : ''"
                        >赠送金额：¥ {{ middItem.LargessAmount | toFixed | NumFormat }}</span
                      >
                    </div>
                  </el-col>
                </el-row>
                <el-row
                  class="padlt_10 padrt_10 border_right border_left border_bottom font_12"
                  v-for="(handler, index) in middItem.TreatBillHandler"
                  :key="index"
                >
                  <el-col :span="2" class="padtp_10 padbm_10">
                    <el-link class="font_12 line_26" type="primary" :underline="false" @click="employeeHandleClick(4, middItem)">{{
                      handler.TreatHandlerName
                    }}</el-link>
                  </el-col>
                  <el-col :span="22" class="border_left">
                    <el-row v-for="(employee, index) in handler.Employee" :key="employee.EmployeeID">
                      <el-col :span="5" class="padtp_10 padrt_10 " :class="index != 0 ? 'border_top' : ''">
                        <el-form @submit.native.prevent class="sale-ModifyPerformanceCommission-Handler" label-position="right" size="mini" label-width="70px" :inline="true">
                          <el-form-item :label="`${employee.EmployeeName}`">
                            <el-input
                              type="number"
                              v-input-fixed
                              class="input_type"
                              style="width: 90px"
                              v-model="employee.Scale"
                              @input="changeSaleHandlerRate(middItem, employee)"
                            >
                              <template slot="append">%</template>
                            </el-input>
                          </el-form-item>
                          <el-form-item class="cursorclass">
                            <el-checkbox size="small" v-model="employee.IsCalculatePassengerFlow">客流</el-checkbox>
                          </el-form-item>
                        </el-form>
                      </el-col>

                      <el-col :span="15" class="border_right border_left">
                        <!-- 现金 -->
                        <el-row v-if="middItem.PayAmount > 0" class="padtp_10 padrt_10" :class="index != 0 ? 'border_top' : ''">
                          <el-col :span="8">
                            <el-form
                              @submit.native.prevent
                              class="sale-ModifyPerformanceCommission-Handler"
                              label-position="right"
                              size="mini"
                              label-width="120px"
                            >
                              <el-form-item label="现金业绩">
                                <span slot="label">
                                  现金业绩
                                  <el-popover placement="top-start" width="200" trigger="hover">
                                    <p>现金业绩 = 现金金额 x 员工现金业绩占比 x 业绩占比</p>
                                    <p>
                                      员工现金业绩占比参考值：
                                      <span v-if="employee.PerformancePayRate != null">{{ (employee.PerformancePayRate * 100) | toFixed | NumFormat }}%</span>
                                      <span v-else>无</span>
                                    </p>

                                    <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"> </el-button>
                                  </el-popover>
                                </span>
                                <el-input type="number" v-input-fixed class="input_type" style="width: 120px" v-model="employee.PayPerformance">
                                  <template slot="append">元</template>
                                </el-input>
                              </el-form-item>
                            </el-form>
                          </el-col>
                          <el-col :span="8">
                            <el-form
                              @submit.native.prevent
                              class="sale-ModifyPerformanceCommission-Handler"
                              label-position="right"
                              size="mini"
                              label-width="150px"
                            >
                              <el-form-item label="现金比例提成">
                                <span slot="label">
                                  现金比例提成
                                  <el-popover placement="top-start" width="200" trigger="hover">
                                    <p>现金比例提成 = 现金业绩 x 现金比例提成参考值</p>

                                    <p v-if="employee.PayRate != null">现金比例提成参考值：{{ employee.PayRate | toFixed | NumFormat }}%</p>
                                    <p v-else>现金比例提成参考值：无</p>
                                    <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"> </el-button>
                                  </el-popover>
                                </span>
                                <el-input type="number" v-input-fixed class="input_type" style="width: 100px" v-model="employee.PayRateCommission">
                                  <template slot="append">元</template>
                                </el-input>
                              </el-form-item>
                            </el-form>
                          </el-col>
                          <el-col :span="8">
                            <el-form
                              @submit.native.prevent
                              class="sale-ModifyPerformanceCommission-Handler"
                              label-position="right"
                              size="mini"
                              label-width="150px"
                            >
                              <el-form-item label="现金固定提成">
                                <span slot="label">
                                  现金固定提成
                                  <el-popover placement="top-start" width="200" trigger="hover">
                                    <p>现金固定提成 = （现金业绩 ÷ 消耗金额）x 现金固定提成参考值 x 数量</p>

                                    <p v-if="employee.PayFixed">
                                      现金固定提成参考值：¥
                                      {{ employee.PayFixed | toFixed | NumFormat }}
                                    </p>
                                    <p v-else>现金固定提成参考值：无</p>
                                    <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"> </el-button>
                                  </el-popover>
                                </span>
                                <el-input type="number" v-model="employee.PayFixedCommission" v-input-fixed class="input_type" style="width: 100px">
                                  <template slot="append">元</template>
                                </el-input>
                              </el-form-item>
                            </el-form>
                          </el-col>
                        </el-row>
                        <!-- 卡抵扣 -->
                        <el-row v-if="middItem.CardDeductionAmount > 0" class="padtp_10 padrt_10" :class="index != 0 ? 'border_top' : ''">
                          <el-col :span="8">
                            <el-form
                              @submit.native.prevent
                              class="sale-ModifyPerformanceCommission-Handler"
                              label-position="right"
                              size="mini"
                              label-width="120px"
                            >
                              <el-form-item label="卡抵扣业绩">
                                <span slot="label">
                                  卡抵扣业绩
                                  <el-popover placement="top-start" width="200" trigger="hover">
                                    <p>卡抵扣业绩 = 卡抵扣金额 x 员工卡抵扣业绩占比 x 业绩占比</p>
                                    <p>
                                      员工卡抵扣业绩占比参考值：
                                      <span v-if="employee.PerformanceCardRate != null">{{ (employee.PerformanceCardRate * 100) | toFixed | NumFormat }}%</span>
                                      <span v-else>无</span>
                                    </p>

                                    <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"> </el-button>
                                  </el-popover>
                                </span>
                                <el-input type="number" v-input-fixed class="input_type" style="width: 120px" v-model="employee.CardPerformance">
                                  <template slot="append">元</template>
                                </el-input>
                              </el-form-item>
                            </el-form>
                          </el-col>
                          <el-col :span="8">
                            <el-form
                              @submit.native.prevent
                              class="sale-ModifyPerformanceCommission-Handler"
                              label-position="right"
                              size="mini"
                              label-width="150px"
                            >
                              <el-form-item label="卡抵扣比例提成">
                                <span slot="label">
                                  卡抵扣比例提成
                                  <el-popover placement="top-start" width="200" trigger="hover">
                                    <p>卡抵扣比例提成 = 卡抵扣业绩 x 卡抵扣比例</p>

                                    <p v-if="employee.CardRate != null">卡抵扣比例参考值：{{ employee.CardRate | toFixed | NumFormat }}%</p>
                                    <p v-else>卡抵扣比例参考值：无</p>
                                    <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"> </el-button>
                                  </el-popover>
                                </span>
                                <el-input type="number" v-input-fixed class="input_type" style="width: 100px" v-model="employee.CardRateCommission">
                                  <template slot="append">元</template>
                                </el-input>
                              </el-form-item>
                            </el-form>
                          </el-col>
                          <el-col :span="8">
                            <el-form
                              @submit.native.prevent
                              class="sale-ModifyPerformanceCommission-Handler"
                              label-position="right"
                              size="mini"
                              label-width="150px"
                            >
                              <el-form-item label="卡抵扣固定提成">
                                <span slot="label">
                                  卡抵扣固定提成
                                  <el-popover placement="top-start" width="200" trigger="hover">
                                    <p>卡抵扣固定提成 = （卡抵扣业绩 ÷ 消耗金额）x 卡抵扣固定提成参考值 x 数量</p>

                                    <p v-if="employee.CardFixed">
                                      卡抵扣固定参考值：¥
                                      {{ employee.CardFixed | toFixed | NumFormat }}
                                    </p>
                                    <p v-else>卡抵扣固定参考值：无</p>
                                    <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"> </el-button>
                                  </el-popover>
                                </span>
                                <el-input type="number" v-input-fixed class="input_type" style="width: 100px" v-model="employee.CardFixedCommission">
                                  <template slot="append">元</template>
                                </el-input>
                              </el-form-item>
                            </el-form>
                          </el-col>
                        </el-row>
                        <!-- 卡赠金 -->
                        <el-row v-if="middItem.LargessCardDeductionAmount > 0" class="padtp_10 padrt_10" :class="index != 0 ? 'border_top' : ''">
                          <el-col :span="8">
                            <el-form
                              @submit.native.prevent
                              class="sale-ModifyPerformanceCommission-Handler"
                              label-position="right"
                              size="mini"
                              label-width="120px"
                            >
                              <el-form-item label="赠送卡扣业绩">
                                <span slot="label">
                                  赠送卡扣业绩
                                  <el-popover placement="top-start" width="200" trigger="hover">
                                    <p>赠送卡抵扣业绩 = 赠送卡抵扣金额 x 赠送卡抵扣业绩占比 x 业绩占比</p>
                                    <p>
                                      员工赠送卡抵扣业绩占比参考值：
                                      <span v-if="employee.PerformanceCardLargessRate != null"
                                        >{{ (employee.PerformanceCardLargessRate * 100) | toFixed | NumFormat }}%</span
                                      >
                                      <span v-else>无</span>
                                    </p>

                                    <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"> </el-button>
                                  </el-popover>
                                </span>
                                <el-input type="number" v-input-fixed class="input_type" style="width: 120px" v-model="employee.CardLargessPerformance">
                                  <template slot="append">元</template>
                                </el-input>
                              </el-form-item>
                            </el-form>
                          </el-col>
                          <el-col :span="8">
                            <el-form
                              @submit.native.prevent
                              class="sale-ModifyPerformanceCommission-Handler"
                              label-position="right"
                              size="mini"
                              label-width="150px"
                            >
                              <el-form-item label="赠送卡扣比例提成">
                                <span slot="label">
                                  赠送卡扣比例提成
                                  <el-popover placement="top-start" width="200" trigger="hover">
                                    <p>赠送卡扣比例提成 = 赠送卡扣业绩 x 赠送卡扣比例</p>

                                    <p v-if="employee.CardLargessRate != null">赠送卡扣比例参考值：{{ employee.CardLargessRate | toFixed | NumFormat }}%</p>
                                    <p v-else>赠送卡扣比例参考值：无</p>
                                    <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"> </el-button>
                                  </el-popover>
                                </span>
                                <el-input type="number" v-input-fixed class="input_type" style="width: 100px" v-model="employee.CardLargessRateCommission">
                                  <template slot="append">元</template>
                                </el-input>
                              </el-form-item>
                            </el-form>
                          </el-col>
                          <el-col :span="8">
                            <el-form
                              @submit.native.prevent
                              class="sale-ModifyPerformanceCommission-Handler"
                              label-position="right"
                              size="mini"
                              label-width="150px"
                            >
                              <el-form-item label="赠送卡扣固定提成">
                                <span slot="label">
                                  赠送卡扣固定提成
                                  <el-popover placement="top-start" width="200" trigger="hover">
                                    <p>赠送卡扣固定提成 = （赠送卡扣业绩 ÷ 消耗金额）x 赠送卡扣固定参考值 x 数量</p>
                                    <p v-if="employee.CardLargessFixed">
                                      赠送卡扣固定参考值：¥
                                      {{ employee.CardLargessFixed | toFixed | NumFormat }}
                                    </p>
                                    <p v-else>赠送卡扣金固定参考值：无</p>
                                    <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"> </el-button>
                                  </el-popover>
                                </span>
                                <el-input type="number" v-input-fixed class="input_type" style="width: 100px" v-model="employee.CardLargessFixedCommission">
                                  <template slot="append">元</template>
                                </el-input>
                              </el-form-item>
                            </el-form>
                          </el-col>
                        </el-row>
                        <!-- 赠送 -->
                        <el-row v-if="middItem.LargessAmount > 0" class="padtp_10 padrt_10" :class="index != 0 ? 'border_top' : ''">
                          <el-col :span="8">
                            <el-form
                              @submit.native.prevent
                              class="sale-ModifyPerformanceCommission-Handler"
                              label-position="right"
                              size="mini"
                              label-width="120px"
                            >
                              <el-form-item label="赠送业绩">
                                <span slot="label">
                                  赠送业绩
                                  <el-popover placement="top-start" width="200" trigger="hover">
                                    <p>赠送业绩 = 赠送金额 x 赠送业绩占比 x 业绩占比</p>
                                    <p>
                                      员工赠送业绩占比参考值：
                                      <span v-if="employee.PerformanceLargessRate != null"
                                        >{{ (employee.PerformanceLargessRate * 100) | toFixed | NumFormat }}%</span
                                      >
                                      <span v-else>无</span>
                                    </p>

                                    <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"> </el-button>
                                  </el-popover>
                                </span>
                                <el-input type="number" v-input-fixed class="input_type" style="width: 120px" v-model="employee.LargessPerformance">
                                  <template slot="append">元</template>
                                </el-input>
                              </el-form-item>
                            </el-form>
                          </el-col>
                          <el-col :span="8">
                            <el-form
                              @submit.native.prevent
                              class="sale-ModifyPerformanceCommission-Handler"
                              label-position="right"
                              size="mini"
                              label-width="150px"
                            >
                              <el-form-item label="赠送比例提成">
                                <span slot="label">
                                  赠送比例提成
                                  <el-popover placement="top-start" width="200" trigger="hover">
                                    <p>赠送比例提成 = 赠送业绩 x 赠送比例</p>
                                    <p v-if="employee.LargessRate != null">赠送比例参考值：{{ employee.LargessRate | toFixed | NumFormat }}%</p>
                                    <p v-else>赠送比例参考值：无</p>
                                    <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"> </el-button>
                                  </el-popover>
                                </span>
                                <el-input type="number" v-input-fixed class="input_type" style="width: 100px" v-model="employee.LargessRateCommission">
                                  <template slot="append">元</template>
                                </el-input>
                              </el-form-item>
                            </el-form>
                          </el-col>
                          <el-col :span="8">
                            <el-form
                              @submit.native.prevent
                              class="sale-ModifyPerformanceCommission-Handler"
                              label-position="right"
                              size="mini"
                              label-width="150px"
                            >
                              <el-form-item label="赠送固定提成">
                                <span slot="label">
                                  赠送固定提成
                                  <el-popover placement="top-start" width="200" trigger="hover">
                                    <p>赠送固定提成 = （赠送业绩 ÷ 消耗金额）x 赠送固定 x 数量</p>
                                    <p v-if="employee.LargessFixed">
                                      赠送固定参考值：¥
                                      {{ employee.LargessFixed | toFixed | NumFormat }}
                                    </p>
                                    <p v-else>赠送固定提成参考值：无</p>
                                    <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"> </el-button>
                                  </el-popover>
                                </span>
                                <el-input type="number" v-input-fixed class="input_type" style="width: 100px" v-model="employee.LargessFixedCommission">
                                  <template slot="append">元</template>
                                </el-input>
                              </el-form-item>
                            </el-form>
                          </el-col>
                        </el-row>
                      </el-col>

                      <el-col :span="4" class="padtp_10" :class="index != 0 ? 'border_top' : ''">
                        <el-form @submit.native.prevent class="sale-ModifyPerformanceCommission-Handler" label-position="right" size="mini" label-width="90px">
                          <el-form-item label="无业绩奖励">
                            <span slot="label">
                              无业绩奖励
                              <el-popover placement="top-start" width="200" trigger="hover">
                                <p>无业绩奖励 = 无业绩奖励参考值 x 数量 x 经手人比例</p>
                                <p v-if="employee.SpecialBenefit != null">
                                  无业绩奖励参考值：¥
                                  {{ employee.SpecialBenefit | toFixed | NumFormat }}
                                </p>
                                <p v-else>无业绩奖励参考值：无</p>
                                <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"> </el-button>
                              </el-popover>
                            </span>
                            <el-input type="number" v-input-fixed class="input_type" style="width: 100px" v-model="employee.SpecialBenefitCommission">
                              <template slot="append">元</template>
                            </el-input>
                          </el-form-item>
                        </el-form>
                      </el-col>
                    </el-row>
                  </el-col>
                </el-row>
              </el-row>
            </div>
          </div>

          <!-- 修改员工业绩通用次卡 !-->
          <div v-if="orderDetail.GeneralCard.length > 0">
            <div v-for="(item, index) in orderDetail.GeneralCard" :key="index">
              <el-row class="border_bottom tipback_col pad_10">
                <el-col :span="6">通用次卡</el-col>
                <el-col :span="4">数量</el-col>
                <el-col :span="6" v-show="treatInfo.BillType == '10'">优惠金额</el-col>
                <el-col :span="8">消耗金额</el-col>
              </el-row>
              <el-row :span="24" style="background-color: #f5f7fa" class="pad_10 border_right border_left border_bottom">
                <div>
                  {{ item.GeneralCardName }}
                  <span v-if="item.Alias">({{ item.Alias }})</span>
                  <el-tag v-if="item.IsLargess" size="mini" class="marlt_10" type="danger">赠</el-tag>
                </div>
              </el-row>
              <el-row v-for="(middItem, middIndex) in item.Project" :key="middIndex">
                <el-row class="border_bottom border_left border_right pad_10">
                  <el-col :span="6">
                    <div>
                      {{ middItem.ProjectName }}
                      <span v-if="middItem.Alias">({{ middItem.Alias }})</span>
                      <el-tag v-if="item.IsLargess" size="mini" class="marlt_10" type="danger">赠</el-tag>
                    </div>
                    <div class="color_red martp_5 font_12">￥{{ middItem.Price }}</div>
                  </el-col>
                  <el-col :span="4">x {{ middItem.Quantity }}</el-col>
                  <el-col :span="6" v-show="treatInfo.BillType == '10'">
                    <div>
                      <span v-if="middItem.CardPreferentialAmount < 0">+ ¥ {{ Math.abs(middItem.CardPreferentialAmount) | toFixed | NumFormat }}</span>
                      <span v-else-if="middItem.CardPreferentialAmount > 0">- ¥ {{ Math.abs(middItem.CardPreferentialAmount) | toFixed | NumFormat }}</span>
                      <span v-else>¥ 0.00</span>
                    </div>
                    <div class="martp_5 font_12">
                      <span class="color_gray font_12" v-if="middItem.CardPreferentialAmount != 0">
                        卡优惠
                        <span class="color_red" v-if="middItem.CardPreferentialAmount > 0">
                          - ¥
                          {{ middItem.CardPreferentialAmount | toFixed | NumFormat }}
                        </span>
                        <span class="color_green" v-else>
                          + ¥
                          {{ Math.abs(middItem.CardPreferentialAmount) | toFixed | NumFormat }}
                        </span>
                      </span>
                    </div>
                  </el-col>
                  <el-col :span="8">
                    <div>¥ {{ middItem.TotalAmount | toFixed | NumFormat }}</div>

                    <div class="martp_5 font_12">
                      <span class="color_green" v-if="middItem.PayAmount > 0">现金金额： {{ middItem.PayAmount | toFixed | NumFormat }}</span>
                      <span class="color_green" v-if="middItem.CardDeductionAmount > 0" :class="middItem.PayAmount != 0 ? 'marlt_15' : ''"
                        >卡抵扣金额： {{ middItem.CardDeductionAmount | toFixed | NumFormat }}</span
                      >

                      <span class="color_red" v-if="middItem.LargessCardDeductionAmount > 0" :class="middItem.CardDeductionAmount != 0 ? 'marlt_15' : ''"
                        >赠送卡抵扣金额： {{ middItem.LargessCardDeductionAmount | toFixed | NumFormat }}</span
                      >

                      <span class="color_red" v-if="middItem.LargessAmount > 0" :class="middItem.LargessCardDeductionAmount != 0 ? 'marlt_15' : ''"
                        >赠送金额：¥ {{ middItem.LargessAmount | toFixed | NumFormat }}</span
                      >
                    </div>
                  </el-col>
                </el-row>
                <el-row
                  class="padlt_10 padrt_10 border_right border_left border_bottom font_12"
                  v-for="(handler, index) in middItem.TreatBillHandler"
                  :key="index"
                >
                  <el-col :span="2" class="padtp_10 padbm_10">
                    <el-link class="font_12 line_26" type="primary" :underline="false" @click="employeeHandleClick(5, middItem)">{{
                      handler.TreatHandlerName
                    }}</el-link>
                  </el-col>
                  <el-col :span="22" class="border_left">
                    <el-row v-for="(employee, index) in handler.Employee" :key="employee.EmployeeID">
                      <el-col :span="5" class="padtp_10 padrt_10 " :class="index != 0 ? 'border_top' : ''">
                        <el-form @submit.native.prevent class="sale-ModifyPerformanceCommission-Handler" label-position="right" size="mini" label-width="70px" :inline="true">
                          <el-form-item :label="`${employee.EmployeeName}`">
                            <el-input
                              type="number"
                              v-input-fixed
                              class="input_type"
                              style="width: 90px"
                              v-model="employee.Scale"
                              @input="changeSaleHandlerRate(middItem, employee)"
                            >
                              <template slot="append">%</template>
                            </el-input>
                          </el-form-item>
                          <el-form-item class="cursorclass">
                            <el-checkbox size="small" v-model="employee.IsCalculatePassengerFlow">客流</el-checkbox>
                          </el-form-item>
                        </el-form>
                      </el-col>

                      <el-col :span="15" class="border_right border_left">
                        <!-- 现金 -->
                        <el-row v-if="middItem.PayAmount > 0" class="padtp_10 padrt_10" :class="index != 0 ? 'border_top' : ''">
                          <el-col :span="8">
                            <el-form
                              @submit.native.prevent
                              class="sale-ModifyPerformanceCommission-Handler"
                              label-position="right"
                              size="mini"
                              label-width="120px"
                            >
                              <el-form-item label="现金业绩">
                                <span slot="label">
                                  现金业绩
                                  <el-popover placement="top-start" width="200" trigger="hover">
                                    <p>现金业绩 = 现金金额 x 员工现金业绩占比 x 业绩占比</p>
                                    <p>
                                      员工现金业绩占比参考值：
                                      <span v-if="employee.PerformancePayRate != null">{{ (employee.PerformancePayRate * 100) | toFixed | NumFormat }}%</span>
                                      <span v-else>无</span>
                                    </p>

                                    <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"> </el-button>
                                  </el-popover>
                                </span>
                                <el-input type="number" v-input-fixed class="input_type" style="width: 120px" v-model="employee.PayPerformance">
                                  <template slot="append">元</template>
                                </el-input>
                              </el-form-item>
                            </el-form>
                          </el-col>
                          <el-col :span="8">
                            <el-form
                              @submit.native.prevent
                              class="sale-ModifyPerformanceCommission-Handler"
                              label-position="right"
                              size="mini"
                              label-width="150px"
                            >
                              <el-form-item label="现金比例提成">
                                <span slot="label">
                                  现金比例提成
                                  <el-popover placement="top-start" width="200" trigger="hover">
                                    <p>现金比例提成 = 现金业绩 x 现金比例提成参考值</p>

                                    <p v-if="employee.PayRate != null">现金比例提成参考值：{{ employee.PayRate | toFixed | NumFormat }}%</p>
                                    <p v-else>现金比例提成参考值：无</p>
                                    <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"> </el-button>
                                  </el-popover>
                                </span>
                                <el-input type="number" v-input-fixed class="input_type" style="width: 100px" v-model="employee.PayRateCommission">
                                  <template slot="append">元</template>
                                </el-input>
                              </el-form-item>
                            </el-form>
                          </el-col>
                          <el-col :span="8">
                            <el-form
                              @submit.native.prevent
                              class="sale-ModifyPerformanceCommission-Handler"
                              label-position="right"
                              size="mini"
                              label-width="150px"
                            >
                              <el-form-item label="现金固定提成">
                                <span slot="label">
                                  现金固定提成
                                  <el-popover placement="top-start" width="200" trigger="hover">
                                    <p>现金固定提成 = （现金业绩 ÷ 消耗金额）x 现金固定提成参考值 x 数量</p>

                                    <p v-if="employee.PayFixed">
                                      现金固定提成参考值：¥
                                      {{ employee.PayFixed | toFixed | NumFormat }}
                                    </p>
                                    <p v-else>现金固定提成参考值：无</p>
                                    <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"> </el-button>
                                  </el-popover>
                                </span>
                                <el-input type="number" v-input-fixed class="input_type" style="width: 100px" v-model="employee.PayFixedCommission">
                                  <template slot="append">元</template>
                                </el-input>
                              </el-form-item>
                            </el-form>
                          </el-col>
                        </el-row>
                        <!-- 卡本金 -->
                        <el-row v-if="middItem.CardDeductionAmount > 0" class="padtp_10 padrt_10" :class="index != 0 ? 'border_top' : ''">
                          <el-col :span="8">
                            <el-form
                              @submit.native.prevent
                              class="sale-ModifyPerformanceCommission-Handler"
                              label-position="right"
                              size="mini"
                              label-width="120px"
                            >
                              <el-form-item label="卡抵扣业绩">
                                <span slot="label">
                                  卡抵扣业绩
                                  <el-popover placement="top-start" width="200" trigger="hover">
                                    <p>卡抵扣业绩 = 卡抵扣金额 x 员工卡抵扣业绩占比 x 业绩占比</p>
                                    <p>
                                      员工卡抵扣业绩占比参考值：
                                      <span v-if="employee.PerformanceCardRate != null">{{ (employee.PerformanceCardRate * 100) | toFixed | NumFormat }}%</span>
                                      <span v-else>无</span>
                                    </p>

                                    <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"> </el-button>
                                  </el-popover>
                                </span>
                                <el-input type="number" v-input-fixed class="input_type" style="width: 120px" v-model="employee.CardPerformance">
                                  <template slot="append">元</template>
                                </el-input>
                              </el-form-item>
                            </el-form>
                          </el-col>
                          <el-col :span="8">
                            <el-form
                              @submit.native.prevent
                              class="sale-ModifyPerformanceCommission-Handler"
                              label-position="right"
                              size="mini"
                              label-width="150px"
                            >
                              <el-form-item label="卡抵扣比例提成">
                                <span slot="label">
                                  卡抵扣比例提成
                                  <el-popover placement="top-start" width="200" trigger="hover">
                                    <p>卡抵扣比例提成 = 卡抵扣业绩 x 卡抵扣比例</p>

                                    <p v-if="employee.CardRate != null">卡抵扣比例参考值：{{ employee.CardRate | toFixed | NumFormat }}%</p>
                                    <p v-else>卡抵扣比例参考值：无</p>
                                    <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"> </el-button>
                                  </el-popover>
                                </span>
                                <el-input type="number" v-input-fixed class="input_type" style="width: 100px" v-model="employee.CardRateCommission">
                                  <template slot="append">元</template>
                                </el-input>
                              </el-form-item>
                            </el-form>
                          </el-col>
                          <el-col :span="8">
                            <el-form
                              @submit.native.prevent
                              class="sale-ModifyPerformanceCommission-Handler"
                              label-position="right"
                              size="mini"
                              label-width="150px"
                            >
                              <el-form-item label="卡抵扣固定提成">
                                <span slot="label">
                                  卡抵扣固定提成
                                  <el-popover placement="top-start" width="200" trigger="hover">
                                    <p>卡抵扣固定提成 = （卡抵扣业绩 ÷ 消耗金额）x 卡抵扣固定提成参考值 x 数量</p>

                                    <p v-if="employee.CardFixed">
                                      卡抵扣固定参考值：¥
                                      {{ employee.CardFixed | toFixed | NumFormat }}
                                    </p>
                                    <p v-else>卡抵扣固定参考值：无</p>
                                    <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"> </el-button>
                                  </el-popover>
                                </span>
                                <el-input type="number" v-input-fixed class="input_type" style="width: 100px" v-model="employee.CardFixedCommission">
                                  <template slot="append">元</template>
                                </el-input>
                              </el-form-item>
                            </el-form>
                          </el-col>
                        </el-row>
                        <!-- 卡赠金 -->
                        <el-row v-if="middItem.LargessCardDeductionAmount > 0" class="padtp_10 padrt_10" :class="index != 0 ? 'border_top' : ''">
                          <el-col :span="8">
                            <el-form
                              @submit.native.prevent
                              class="sale-ModifyPerformanceCommission-Handler"
                              label-position="right"
                              size="mini"
                              label-width="120px"
                            >
                              <el-form-item label="赠送卡扣业绩">
                                <span slot="label">
                                  赠送卡扣业绩
                                  <el-popover placement="top-start" width="200" trigger="hover">
                                    <p>赠送卡抵扣业绩 = 赠送卡抵扣金额 x 赠送卡抵扣业绩占比 x 业绩占比</p>
                                    <p>
                                      员工赠送卡抵扣业绩占比参考值：
                                      <span v-if="employee.PerformanceCardLargessRate != null"
                                        >{{ (employee.PerformanceCardLargessRate * 100) | toFixed | NumFormat }}%</span
                                      >
                                      <span v-else>无</span>
                                    </p>

                                    <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"> </el-button>
                                  </el-popover>
                                </span>
                                <el-input type="number" v-input-fixed class="input_type" style="width: 120px" v-model="employee.CardLargessPerformance">
                                  <template slot="append">元</template>
                                </el-input>
                              </el-form-item>
                            </el-form>
                          </el-col>
                          <el-col :span="8">
                            <el-form
                              @submit.native.prevent
                              class="sale-ModifyPerformanceCommission-Handler"
                              label-position="right"
                              size="mini"
                              label-width="150px"
                            >
                              <el-form-item label="赠送卡扣比例提成">
                                <span slot="label">
                                  赠送卡扣比例提成
                                  <el-popover placement="top-start" width="200" trigger="hover">
                                    <p>赠送卡扣比例提成 = 赠送卡扣业绩 x 赠送卡扣比例</p>

                                    <p v-if="employee.CardLargessRate != null">赠送卡扣比例参考值：{{ employee.CardLargessRate | toFixed | NumFormat }}%</p>
                                    <p v-else>赠送卡扣比例参考值：无</p>
                                    <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"> </el-button>
                                  </el-popover>
                                </span>
                                <el-input type="number" v-input-fixed class="input_type" style="width: 100px" v-model="employee.CardLargessRateCommission">
                                  <template slot="append">元</template>
                                </el-input>
                              </el-form-item>
                            </el-form>
                          </el-col>
                          <el-col :span="8">
                            <el-form
                              @submit.native.prevent
                              class="sale-ModifyPerformanceCommission-Handler"
                              label-position="right"
                              size="mini"
                              label-width="150px"
                            >
                              <el-form-item label="赠送卡扣固定提成">
                                <span slot="label">
                                  赠送卡扣固定提成
                                  <el-popover placement="top-start" width="200" trigger="hover">
                                    <p>赠送卡扣固定提成 = （赠送卡扣业绩 ÷ 消耗金额）x 赠送卡扣固定参考值 x 数量</p>

                                    <p v-if="employee.CardLargessFixed">
                                      赠送卡扣固定参考值：¥
                                      {{ employee.CardLargessFixed | toFixed | NumFormat }}
                                    </p>
                                    <p v-else>赠送卡扣金固定参考值：无</p>
                                    <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"> </el-button>
                                  </el-popover>
                                </span>
                                <el-input type="number" v-input-fixed class="input_type" style="width: 100px" v-model="employee.CardLargessFixedCommission">
                                  <template slot="append">元</template>
                                </el-input>
                              </el-form-item>
                            </el-form>
                          </el-col>
                        </el-row>
                        <!-- 赠送 -->
                        <el-row v-if="middItem.LargessAmount > 0" class="padtp_10 padrt_10" :class="index != 0 ? 'border_top' : ''">
                          <el-col :span="8">
                            <el-form
                              @submit.native.prevent
                              class="sale-ModifyPerformanceCommission-Handler"
                              label-position="right"
                              size="mini"
                              label-width="120px"
                            >
                              <el-form-item label="赠送业绩">
                                <span slot="label">
                                  赠送业绩
                                  <el-popover placement="top-start" width="200" trigger="hover">
                                    <p>赠送业绩 = 赠送金额 x 赠送业绩占比 x 业绩占比</p>
                                    <p>
                                      员工赠送业绩占比参考值：
                                      <span v-if="employee.PerformanceLargessRate != null"
                                        >{{ (employee.PerformanceLargessRate * 100) | toFixed | NumFormat }}%</span
                                      >
                                      <span v-else>无</span>
                                    </p>

                                    <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"> </el-button>
                                  </el-popover>
                                </span>
                                <el-input type="number" v-input-fixed class="input_type" style="width: 120px" v-model="employee.LargessPerformance">
                                  <template slot="append">元</template>
                                </el-input>
                              </el-form-item>
                            </el-form>
                          </el-col>
                          <el-col :span="8">
                            <el-form
                              @submit.native.prevent
                              class="sale-ModifyPerformanceCommission-Handler"
                              label-position="right"
                              size="mini"
                              label-width="150px"
                            >
                              <el-form-item label="赠送比例提成">
                                <span slot="label">
                                  赠送比例提成
                                  <el-popover placement="top-start" width="200" trigger="hover">
                                    <p>赠送比例提成 = 赠送业绩 x 赠送比例</p>
                                    <p v-if="employee.LargessRate != null">赠送比例参考值：{{ employee.LargessRate | toFixed | NumFormat }}%</p>
                                    <p v-else>赠送比例参考值：无</p>
                                    <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"> </el-button>
                                  </el-popover>
                                </span>
                                <el-input type="number" v-input-fixed class="input_type" style="width: 100px" v-model="employee.LargessRateCommission">
                                  <template slot="append">元</template>
                                </el-input>
                              </el-form-item>
                            </el-form>
                          </el-col>
                          <el-col :span="8">
                            <el-form
                              @submit.native.prevent
                              class="sale-ModifyPerformanceCommission-Handler"
                              label-position="right"
                              size="mini"
                              label-width="150px"
                            >
                              <el-form-item label="赠送固定提成">
                                <span slot="label">
                                  赠送固定提成
                                  <el-popover placement="top-start" width="200" trigger="hover">
                                    <p>赠送固定提成 = （赠送业绩 ÷ 消耗金额）x 赠送固定 x 数量</p>
                                    <p v-if="employee.LargessFixed">
                                      赠送固定参考值：¥
                                      {{ employee.LargessFixed | toFixed | NumFormat }}
                                    </p>
                                    <p v-else>赠送固定提成参考值：无</p>
                                    <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"> </el-button>
                                  </el-popover>
                                </span>
                                <el-input type="number" v-input-fixed class="input_type" style="width: 100px" v-model="employee.LargessFixedCommission">
                                  <template slot="append">元</template>
                                </el-input>
                              </el-form-item>
                            </el-form>
                          </el-col>
                        </el-row>
                      </el-col>

                      <el-col :span="4" class="padtp_10" :class="index != 0 ? 'border_top' : ''">
                        <el-form @submit.native.prevent class="sale-ModifyPerformanceCommission-Handler" label-position="right" size="mini" label-width="90px">
                          <el-form-item label="无业绩奖励">
                            <span slot="label">
                              无业绩奖励
                              <el-popover placement="top-start" width="200" trigger="hover">
                                <p>无业绩奖励 = 无业绩奖励参考值 x 数量 x 经手人比例</p>
                                <p v-if="employee.SpecialBenefit != null">
                                  无业绩奖励参考值：¥
                                  {{ employee.SpecialBenefit | toFixed | NumFormat }}
                                </p>
                                <p v-else>无业绩奖励参考值：无</p>
                                <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"> </el-button>
                              </el-popover>
                            </span>
                            <el-input type="number" v-input-fixed class="input_type" style="width: 100px" v-model="employee.SpecialBenefitCommission">
                              <template slot="append">元</template>
                            </el-input>
                          </el-form-item>
                        </el-form>
                      </el-col>
                    </el-row>
                  </el-col>
                </el-row>
              </el-row>
            </div>
          </div>

          <!-- 修改详情产品 !-->
          <div v-if="orderDetail.Product.length > 0">
            <div v-for="(item, index) in orderDetail.Product" :key="index">
              <el-row class="border_bottom tipback_col pad_10">
                <el-col :span="6">产品</el-col>
                <el-col :span="4">数量</el-col>
                <el-col :span="6" v-show="treatInfo.BillType == '10'">优惠金额</el-col>
                <el-col :span="8">消耗金额</el-col>
              </el-row>
              <el-row class="border_bottom pad_10 border_right border_left">
                <el-col :span="6">
                  <div>{{ item.ProductName }}({{ item.Alias }})</div>
                  <div class="color_red martp_5 font_12">
                    ￥{{ item.Price }}
                    <el-tag v-if="item.IsLargess" size="mini" class="marlt_10">赠</el-tag>
                  </div>
                </el-col>
                <el-col :span="4">x {{ item.Quantity }}</el-col>
                <el-col :span="6" v-show="treatInfo.BillType == '10'">
                  <div>
                    <span v-if="item.CardPreferentialAmount < 0">+ ¥ {{ Math.abs(item.CardPreferentialAmount) | toFixed | NumFormat }}</span>
                    <span v-else-if="item.CardPreferentialAmount > 0">- ¥ {{ Math.abs(item.CardPreferentialAmount) | toFixed | NumFormat }}</span>
                    <span v-else>¥ 0.00</span>
                  </div>
                </el-col>
                <el-col :span="8">
                  <div>¥ {{ (item.PayAmount + item.CardDeductionAmount + item.LargessCardDeductionAmount + item.LargessAmount) | toFixed | NumFormat }}</div>

                  <div class="martp_5 font_12">
                    <span class="color_green" v-if="item.PayAmount > 0">现金金额： {{ item.PayAmount | toFixed | NumFormat }}</span>
                    <span class="color_green" v-if="item.CardDeductionAmount > 0" :class="item.PayAmount != 0 ? 'marlt_15' : ''"
                      >卡抵扣金额： {{ item.CardDeductionAmount | toFixed | NumFormat }}</span
                    >

                    <span class="color_red" v-if="item.LargessCardDeductionAmount > 0" :class="item.CardDeductionAmount != 0 ? 'marlt_15' : ''"
                      >赠送卡抵扣金额： {{ item.LargessCardDeductionAmount | toFixed | NumFormat }}</span
                    >

                    <span class="color_red" v-if="item.LargessAmount > 0" :class="item.LargessCardDeductionAmount != 0 ? 'marlt_15' : ''"
                      >赠送金额：¥ {{ item.LargessAmount | toFixed | NumFormat }}</span
                    >
                  </div>
                </el-col>
              </el-row>
              <el-row class="padlt_10 padrt_10 border_right border_left border_bottom font_12" v-for="(handler, index) in item.TreatBillHandler" :key="index">
                <el-col :span="2" class="padtp_10 padbm_10">
                  <el-link class="font_12 line_26" type="primary" :underline="false" @click="employeeHandleClick(2, item)">{{
                    handler.TreatHandlerName
                  }}</el-link>
                </el-col>
                <el-col :span="22" class="border_left">
                  <el-row v-for="(employee, index) in handler.Employee" :key="employee.EmployeeID">
                    <el-col :span="5" class="padtp_10 padrt_10 " :class="index != 0 ? 'border_top' : ''">
                      <el-form @submit.native.prevent class="sale-ModifyPerformanceCommission-Handler" label-position="right" size="mini" label-width="70px" :inline="true">
                        <el-form-item :label="`${employee.EmployeeName}`">
                          <el-input
                            type="number"
                            v-input-fixed
                            class="input_type"
                            style="width: 90px"
                            v-model="employee.Scale"
                            @input="changeSaleHandlerRate(item, employee)"
                          >
                            <template slot="append">%</template>
                          </el-input>
                        </el-form-item>
                        <el-form-item class="cursorclass">
                          <el-checkbox size="small" v-model="employee.IsCalculatePassengerFlow">客流</el-checkbox>
                        </el-form-item>
                      </el-form>
                    </el-col>

                    <el-col :span="15" class="border_right border_left">
                      <!-- 现金 -->
                      <el-row v-if="item.PayAmount > 0" class="padtp_10 padrt_10" :class="index != 0 ? 'border_top' : ''">
                        <el-col :span="8">
                          <el-form
                            @submit.native.prevent
                            class="sale-ModifyPerformanceCommission-Handler"
                            label-position="right"
                            size="mini"
                            label-width="120px"
                          >
                            <el-form-item label="现金业绩">
                              <span slot="label">
                                现金业绩
                                <el-popover placement="top-start" width="200" trigger="hover">
                                  <p>现金业绩 = 现金金额 x 员工现金业绩占比 x 业绩占比</p>
                                  <p>
                                    员工现金业绩占比参考值：
                                    <span v-if="employee.PerformancePayRate != null">{{ (employee.PerformancePayRate * 100) | toFixed | NumFormat }}%</span>
                                    <span v-else>无</span>
                                  </p>

                                  <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"> </el-button>
                                </el-popover>
                              </span>
                              <el-input type="number" v-input-fixed class="input_type" style="width: 120px" v-model="employee.PayPerformance">
                                <template slot="append">元</template>
                              </el-input>
                            </el-form-item>
                          </el-form>
                        </el-col>
                        <el-col :span="8">
                          <el-form
                            @submit.native.prevent
                            class="sale-ModifyPerformanceCommission-Handler"
                            label-position="right"
                            size="mini"
                            label-width="150px"
                          >
                            <el-form-item label="现金比例提成">
                              <span slot="label">
                                现金比例提成
                                <el-popover placement="top-start" width="200" trigger="hover">
                                  <p>现金比例提成 = 现金业绩 x 现金比例提成参考值</p>

                                  <p v-if="employee.PayRate != null">现金比例提成参考值：{{ employee.PayRate | toFixed | NumFormat }}%</p>
                                  <p v-else>现金比例提成参考值：无</p>
                                  <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"> </el-button>
                                </el-popover>
                              </span>
                              <el-input type="number" v-input-fixed class="input_type" style="width: 100px" v-model="employee.PayRateCommission">
                                <template slot="append">元</template>
                              </el-input>
                            </el-form-item>
                          </el-form>
                        </el-col>
                        <el-col :span="8">
                          <el-form
                            @submit.native.prevent
                            class="sale-ModifyPerformanceCommission-Handler"
                            label-position="right"
                            size="mini"
                            label-width="150px"
                          >
                            <el-form-item label="现金固定提成">
                              <span slot="label">
                                现金固定提成
                                <el-popover placement="top-start" width="200" trigger="hover">
                                  <p>现金固定提成 = （现金业绩 ÷ 消耗金额）x 现金固定提成参考值 x 数量</p>

                                  <p v-if="employee.PayFixed">
                                    现金固定提成参考值：¥
                                    {{ employee.PayFixed | toFixed | NumFormat }}
                                  </p>
                                  <p v-else>现金固定提成参考值：无</p>
                                  <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"> </el-button>
                                </el-popover>
                              </span>
                              <el-input type="number" v-input-fixed class="input_type" style="width: 100px" v-model="employee.PayFixedCommission">
                                <template slot="append">元</template>
                              </el-input>
                            </el-form-item>
                          </el-form>
                        </el-col>
                      </el-row>
                      <!-- 卡本金 -->
                      <el-row v-if="item.CardDeductionAmount > 0" class="padtp_10 padrt_10" :class="index != 0 ? 'border_top' : ''">
                        <el-col :span="8">
                          <el-form
                            @submit.native.prevent
                            class="sale-ModifyPerformanceCommission-Handler"
                            label-position="right"
                            size="mini"
                            label-width="120px"
                          >
                            <el-form-item label="卡抵扣业绩">
                              <span slot="label">
                                卡抵扣业绩
                                <el-popover placement="top-start" width="200" trigger="hover">
                                  <p>卡抵扣业绩 = 卡抵扣金额 x 员工卡抵扣业绩占比 x 业绩占比</p>
                                  <p>
                                    员工卡抵扣业绩占比参考值：
                                    <span v-if="employee.PerformanceCardRate != null">{{ (employee.PerformanceCardRate * 100) | toFixed | NumFormat }}%</span>
                                    <span v-else>无</span>
                                  </p>

                                  <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"> </el-button>
                                </el-popover>
                              </span>
                              <el-input type="number" v-input-fixed class="input_type" style="width: 120px" v-model="employee.CardPerformance">
                                <template slot="append">元</template>
                              </el-input>
                            </el-form-item>
                          </el-form>
                        </el-col>
                        <el-col :span="8">
                          <el-form
                            @submit.native.prevent
                            class="sale-ModifyPerformanceCommission-Handler"
                            label-position="right"
                            size="mini"
                            label-width="150px"
                          >
                            <el-form-item label="卡抵扣比例提成">
                              <span slot="label">
                                卡抵扣比例提成
                                <el-popover placement="top-start" width="200" trigger="hover">
                                  <p>卡抵扣比例提成 = 卡抵扣业绩 x 卡抵扣比例</p>

                                  <p v-if="employee.CardRate != null">卡抵扣比例参考值：{{ employee.CardRate | toFixed | NumFormat }}%</p>
                                  <p v-else>卡抵扣比例参考值：无</p>
                                  <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"> </el-button>
                                </el-popover>
                              </span>
                              <el-input type="number" v-input-fixed class="input_type" style="width: 100px" v-model="employee.CardRateCommission">
                                <template slot="append">元</template>
                              </el-input>
                            </el-form-item>
                          </el-form>
                        </el-col>
                        <el-col :span="8">
                          <el-form
                            @submit.native.prevent
                            class="sale-ModifyPerformanceCommission-Handler"
                            label-position="right"
                            size="mini"
                            label-width="150px"
                          >
                            <el-form-item label="卡抵扣固定提成">
                              <span slot="label">
                                卡抵扣固定提成
                                <el-popover placement="top-start" width="200" trigger="hover">
                                  <p>卡抵扣固定提成 = （卡抵扣业绩 ÷ 消耗金额）x 卡抵扣固定提成参考值 x 数量</p>

                                  <p v-if="employee.CardFixed">
                                    卡抵扣固定参考值：¥
                                    {{ employee.CardFixed | toFixed | NumFormat }}
                                  </p>
                                  <p v-else>卡抵扣固定参考值：无</p>
                                  <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"> </el-button>
                                </el-popover>
                              </span>
                              <el-input type="number" v-input-fixed class="input_type" style="width: 100px" v-model="employee.CardFixedCommission">
                                <template slot="append">元</template>
                              </el-input>
                            </el-form-item>
                          </el-form>
                        </el-col>
                      </el-row>
                      <!-- 卡赠金 -->
                      <el-row v-if="item.LargessCardDeductionAmount > 0" class="padtp_10 padrt_10" :class="index != 0 ? 'border_top' : ''">
                        <el-col :span="8">
                          <el-form
                            @submit.native.prevent
                            class="sale-ModifyPerformanceCommission-Handler"
                            label-position="right"
                            size="mini"
                            label-width="120px"
                          >
                            <el-form-item label="赠送卡扣业绩">
                              <span slot="label">
                                赠送卡扣业绩
                                <el-popover placement="top-start" width="200" trigger="hover">
                                  <p>赠送卡抵扣业绩 = 赠送卡抵扣金额 x 赠送卡抵扣业绩占比 x 业绩占比</p>
                                  <p>
                                    员工赠送卡抵扣业绩占比参考值：
                                    <span v-if="employee.PerformanceCardLargessRate != null"
                                      >{{ (employee.PerformanceCardLargessRate * 100) | toFixed | NumFormat }}%</span
                                    >
                                    <span v-else>无</span>
                                  </p>

                                  <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"> </el-button>
                                </el-popover>
                              </span>
                              <el-input type="number" v-input-fixed class="input_type" style="width: 120px" v-model="employee.CardLargessPerformance">
                                <template slot="append">元</template>
                              </el-input>
                            </el-form-item>
                          </el-form>
                        </el-col>
                        <el-col :span="8">
                          <el-form
                            @submit.native.prevent
                            class="sale-ModifyPerformanceCommission-Handler"
                            label-position="right"
                            size="mini"
                            label-width="150px"
                          >
                            <el-form-item label="赠送卡扣比例提成">
                              <span slot="label">
                                赠送卡扣比例提成
                                <el-popover placement="top-start" width="200" trigger="hover">
                                  <p>赠送卡扣比例提成 = 赠送卡扣业绩 x 赠送卡扣比例</p>

                                  <p v-if="employee.CardLargessRate != null">赠送卡扣比例参考值：{{ employee.CardLargessRate | toFixed | NumFormat }}%</p>
                                  <p v-else>赠送卡扣比例参考值：无</p>
                                  <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"> </el-button>
                                </el-popover>
                              </span>
                              <el-input type="number" v-input-fixed class="input_type" style="width: 100px" v-model="employee.CardLargessRateCommission">
                                <template slot="append">元</template>
                              </el-input>
                            </el-form-item>
                          </el-form>
                        </el-col>
                        <el-col :span="8">
                          <el-form
                            @submit.native.prevent
                            class="sale-ModifyPerformanceCommission-Handler"
                            label-position="right"
                            size="mini"
                            label-width="150px"
                          >
                            <el-form-item label="赠送卡扣固定提成">
                              <span slot="label">
                                赠送卡扣固定提成
                                <el-popover placement="top-start" width="200" trigger="hover">
                                  <p>赠送卡扣固定提成 = （赠送卡扣业绩 ÷ 消耗金额）x 赠送卡扣固定参考值 x 数量</p>

                                  <p v-if="employee.CardLargessFixed">
                                    赠送卡扣固定参考值：¥
                                    {{ employee.CardLargessFixed | toFixed | NumFormat }}
                                  </p>
                                  <p v-else>赠送卡扣金固定参考值：无</p>
                                  <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"> </el-button>
                                </el-popover>
                              </span>
                              <el-input type="number" v-input-fixed class="input_type" style="width: 100px" v-model="employee.CardLargessFixedCommission">
                                <template slot="append">元</template>
                              </el-input>
                            </el-form-item>
                          </el-form>
                        </el-col>
                      </el-row>
                      <!-- 赠送 -->
                      <el-row v-if="item.LargessAmount > 0" class="padtp_10 padrt_10" :class="index != 0 ? 'border_top' : ''">
                        <el-col :span="8">
                          <el-form
                            @submit.native.prevent
                            class="sale-ModifyPerformanceCommission-Handler"
                            label-position="right"
                            size="mini"
                            label-width="120px"
                          >
                            <el-form-item label="赠送业绩">
                              <span slot="label">
                                赠送业绩
                                <el-popover placement="top-start" width="200" trigger="hover">
                                  <p>赠送业绩 = 赠送金额 x 赠送业绩占比 x 业绩占比</p>
                                  <p>
                                    员工赠送业绩占比参考值：
                                    <span v-if="employee.PerformanceLargessRate != null"
                                      >{{ (employee.PerformanceLargessRate * 100) | toFixed | NumFormat }}%</span
                                    >
                                    <span v-else>无</span>
                                  </p>

                                  <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"> </el-button>
                                </el-popover>
                              </span>
                              <el-input type="number" v-input-fixed class="input_type" style="width: 120px" v-model="employee.LargessPerformance">
                                <template slot="append">元</template>
                              </el-input>
                            </el-form-item>
                          </el-form>
                        </el-col>
                        <el-col :span="8">
                          <el-form
                            @submit.native.prevent
                            class="sale-ModifyPerformanceCommission-Handler"
                            label-position="right"
                            size="mini"
                            label-width="150px"
                          >
                            <el-form-item label="赠送比例提成">
                              <span slot="label">
                                赠送比例提成
                                <el-popover placement="top-start" width="200" trigger="hover">
                                  <p>赠送比例提成 = 赠送业绩 x 赠送比例</p>
                                  <p v-if="employee.LargessRate != null">赠送比例参考值：{{ employee.LargessRate | toFixed | NumFormat }}%</p>
                                  <p v-else>赠送比例参考值：无</p>
                                  <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"> </el-button>
                                </el-popover>
                              </span>
                              <el-input type="number" v-input-fixed class="input_type" style="width: 100px" v-model="employee.LargessRateCommission">
                                <template slot="append">元</template>
                              </el-input>
                            </el-form-item>
                          </el-form>
                        </el-col>
                        <el-col :span="8">
                          <el-form
                            @submit.native.prevent
                            class="sale-ModifyPerformanceCommission-Handler"
                            label-position="right"
                            size="mini"
                            label-width="150px"
                          >
                            <el-form-item label="赠送固定提成">
                              <span slot="label">
                                赠送固定提成
                                <el-popover placement="top-start" width="200" trigger="hover">
                                  <p>赠送固定提成 = （赠送业绩 ÷ 消耗金额）x 赠送固定 x 数量</p>
                                  <p v-if="employee.LargessFixed">
                                    赠送固定参考值：¥
                                    {{ employee.LargessFixed | toFixed | NumFormat }}
                                  </p>
                                  <p v-else>赠送固定提成参考值：无</p>
                                  <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"> </el-button>
                                </el-popover>
                              </span>
                              <el-input type="number" v-input-fixed class="input_type" style="width: 100px" v-model="employee.LargessFixedCommission">
                                <template slot="append">元</template>
                              </el-input>
                            </el-form-item>
                          </el-form>
                        </el-col>
                      </el-row>
                    </el-col>

                    <el-col :span="4" class="padtp_10" :class="index != 0 ? 'border_top' : ''">
                      <el-form @submit.native.prevent class="sale-ModifyPerformanceCommission-Handler" label-position="right" size="mini" label-width="90px">
                        <el-form-item label="无业绩奖励">
                          <span slot="label">
                            无业绩奖励
                            <el-popover placement="top-start" width="200" trigger="hover">
                              <p>无业绩奖励 = 无业绩奖励参考值 x 数量 x 经手人比例</p>
                              <p v-if="employee.SpecialBenefit != null">
                                无业绩奖励参考值：¥
                                {{ employee.SpecialBenefit | toFixed | NumFormat }}
                              </p>
                              <p v-else>无业绩奖励参考值：无</p>
                              <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"> </el-button>
                            </el-popover>
                          </span>
                          <el-input type="number" v-input-fixed class="input_type" style="width: 100px" v-model="employee.SpecialBenefitCommission">
                            <template slot="append">元</template>
                          </el-input>
                        </el-form-item>
                      </el-form>
                    </el-col>
                  </el-row>
                </el-col>
              </el-row>
            </div>
          </div>

          <!-- 修改员工业绩套餐卡 !-->
          <div v-if="orderDetail && orderDetail.PackageCard.length > 0">
            <div v-for="(item, index) in orderDetail.PackageCard" :key="index + 'a1'">
              <el-row class="border_bottom tipback_col pad_10">
                <el-col :span="6">套餐卡</el-col>
                <el-col :span="4">数量</el-col>
                <el-col :span="6" v-show="treatInfo.BillType == '10'">优惠金额</el-col>
                <el-col :span="8">消耗金额</el-col>
              </el-row>
              <el-row :span="24" style="background-color: #f5f7fa" class="pad_10 border_right border_left border_bottom">
                <div>
                  {{ item.PackageCardName }}
                  <span v-if="item.Alias">({{ item.Alias }})</span>
                  <el-tag v-if="item.IsLargess" size="mini" class="marlt_5" type="danger">赠</el-tag>
                </div>
              </el-row>
              <!--项目-->
              <div v-if="item.Project.length > 0">
                <el-row v-for="(middItem, middIndex) in item.Project" :key="middIndex">
                  <el-row class="pad_10 border_bottom border_left border_right">
                    <el-col :span="6">
                      <div>
                        {{ middItem.ProjectName }}
                        <span v-if="middItem.Alias"></span>
                        ({{ middItem.Alias }})
                        <el-tag v-if="middItem.IsLargess" size="mini" type="danger" class="marlt_5">赠</el-tag>
                        <el-tag class="marlt_5" size="mini">项目</el-tag>
                      </div>
                      <div class="color_red font_12 martp_5">¥ {{ middItem.Price | toFixed | NumFormat }}</div>
                    </el-col>
                    <el-col :span="4">x {{ middItem.Quantity }}</el-col>
                    <el-col :span="6" v-show="treatInfo.BillType == '10'">
                      <div>
                        <span v-if="middItem.CardPreferentialAmount < 0">+ ¥ {{ Math.abs(middItem.CardPreferentialAmount) | toFixed | NumFormat }}</span>
                        <span v-else-if="middItem.CardPreferentialAmount > 0">- ¥ {{ Math.abs(middItem.CardPreferentialAmount) | toFixed | NumFormat }}</span>
                        <span v-else>¥ 0.00</span>
                      </div>
                    </el-col>
                    <el-col :span="8">
                      <div>¥ {{ middItem.TotalAmount | toFixed | NumFormat }}</div>
                      <div class="martp_5 font_12">
                        <span class="color_green" v-if="middItem.PayAmount > 0">现金金额： {{ middItem.PayAmount | toFixed | NumFormat }}</span>
                        <span class="color_green" v-if="middItem.CardDeductionAmount > 0" :class="middItem.PayAmount != 0 ? 'marlt_15' : ''"
                          >卡抵扣金额： {{ middItem.CardDeductionAmount | toFixed | NumFormat }}</span
                        >

                        <span class="color_red" v-if="middItem.LargessCardDeductionAmount > 0" :class="middItem.CardDeductionAmount != 0 ? 'marlt_15' : ''"
                          >赠送卡抵扣金额： {{ middItem.LargessCardDeductionAmount | toFixed | NumFormat }}</span
                        >

                        <span class="color_red" v-if="middItem.LargessAmount > 0" :class="middItem.LargessCardDeductionAmount != 0 ? 'marlt_15' : ''"
                          >赠送金额：¥ {{ middItem.LargessAmount | toFixed | NumFormat }}</span
                        >
                      </div>
                    </el-col>
                  </el-row>

                  <el-row
                    class="padlt_10 padrt_10 border_right border_left border_bottom font_12"
                    v-for="(handler, index) in middItem.TreatBillHandler"
                    :key="index"
                  >
                    <el-col :span="2" class="padtp_10 padbm_10">
                      <el-link class="font_12 line_26" type="primary" :underline="false" @click="employeeHandleClick(6, middItem, '项目')">{{
                        handler.TreatHandlerName
                      }}</el-link>
                    </el-col>
                    <el-col :span="22" class="border_left">
                      <el-row v-for="(employee, index) in handler.Employee" :key="employee.EmployeeID">
                        <el-col :span="5" class="padtp_10 padrt_10 " :class="index != 0 ? 'border_top' : ''">
                          <el-form
                            @submit.native.prevent
                            class="sale-ModifyPerformanceCommission-Handler"
                            label-position="right"
                            size="mini"
                            label-width="70px"
                            :inline="true"
                          >
                            <el-form-item :label="`${employee.EmployeeName}`">
                              <el-input
                                type="number"
                                v-input-fixed
                                class="input_type"
                                style="width: 90px"
                                v-model="employee.Scale"
                                @input="changeSaleHandlerRate(middItem, employee)"
                              >
                                <template slot="append">%</template>
                              </el-input>
                            </el-form-item>
                            <el-form-item class="cursorclass">
                             <el-checkbox size="small" v-model="employee.IsCalculatePassengerFlow">客流</el-checkbox>
                            </el-form-item>
                          </el-form>
                        </el-col>

                        <el-col :span="15" class="border_right border_left">
                          <!-- 现金 -->
                          <el-row v-if="middItem.PayAmount > 0" class="padtp_10 padrt_10" :class="index != 0 ? 'border_top' : ''">
                            <el-col :span="8">
                              <el-form
                                @submit.native.prevent
                                class="sale-ModifyPerformanceCommission-Handler"
                                label-position="right"
                                size="mini"
                                label-width="120px"
                              >
                                <el-form-item label="现金业绩">
                                  <span slot="label">
                                    现金业绩
                                    <el-popover placement="top-start" width="200" trigger="hover">
                                      <p>现金业绩 = 现金金额 x 员工现金业绩占比 x 业绩占比</p>
                                      <p>
                                        员工现金业绩占比参考值：
                                        <span v-if="employee.PerformancePayRate != null">{{ (employee.PerformancePayRate * 100) | toFixed | NumFormat }}%</span>
                                        <span v-else>无</span>
                                      </p>

                                      <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                    </el-popover>
                                  </span>
                                  <el-input type="number" v-input-fixed class="input_type" style="width: 120px" v-model="employee.PayPerformance">
                                    <template slot="append">元</template>
                                  </el-input>
                                </el-form-item>
                              </el-form>
                            </el-col>
                            <el-col :span="8">
                              <el-form
                                @submit.native.prevent
                                class="sale-ModifyPerformanceCommission-Handler"
                                label-position="right"
                                size="mini"
                                label-width="150px"
                              >
                                <el-form-item label="现金比例提成">
                                  <span slot="label">
                                    现金比例提成
                                    <el-popover placement="top-start" width="200" trigger="hover">
                                      <p>现金比例提成 = 现金业绩 x 现金比例提成参考值</p>

                                      <p v-if="employee.PayRate != null">现金比例提成参考值：{{ employee.PayRate | toFixed | NumFormat }}%</p>
                                      <p v-else>现金比例提成参考值：无</p>
                                      <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                    </el-popover>
                                  </span>
                                  <el-input type="number" v-input-fixed class="input_type" style="width: 100px" v-model="employee.PayRateCommission">
                                    <template slot="append">元</template>
                                  </el-input>
                                </el-form-item>
                              </el-form>
                            </el-col>
                            <el-col :span="8">
                              <el-form
                                @submit.native.prevent
                                class="sale-ModifyPerformanceCommission-Handler"
                                label-position="right"
                                size="mini"
                                label-width="150px"
                              >
                                <el-form-item label="现金固定提成">
                                  <span slot="label">
                                    现金固定提成
                                    <el-popover placement="top-start" width="200" trigger="hover">
                                      <p>现金固定提成 = （现金业绩 ÷ 消耗金额）x 现金固定提成参考值 x 数量</p>

                                      <p v-if="employee.PayFixed">
                                        现金固定提成参考值：¥
                                        {{ employee.PayFixed | toFixed | NumFormat }}
                                      </p>
                                      <p v-else>现金固定提成参考值：无</p>
                                      <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                    </el-popover>
                                  </span>
                                  <el-input type="number" v-input-fixed class="input_type" style="width: 100px" v-model="employee.PayFixedCommission">
                                    <template slot="append">元</template>
                                  </el-input>
                                </el-form-item>
                              </el-form>
                            </el-col>
                          </el-row>
                          <!-- 卡本金 -->
                          <el-row v-if="middItem.CardDeductionAmount > 0" class="padtp_10 padrt_10" :class="index != 0 ? 'border_top' : ''">
                            <el-col :span="8">
                              <el-form
                                @submit.native.prevent
                                class="sale-ModifyPerformanceCommission-Handler"
                                label-position="right"
                                size="mini"
                                label-width="120px"
                              >
                                <el-form-item label="卡抵扣业绩">
                                  <span slot="label">
                                    卡抵扣业绩
                                    <el-popover placement="top-start" width="200" trigger="hover">
                                      <p>卡抵扣业绩 = 卡抵扣金额 x 员工卡抵扣业绩占比 x 业绩占比</p>
                                      <p>
                                        员工卡抵扣业绩占比参考值：
                                        <span v-if="employee.PerformanceCardRate != null"
                                          >{{ (employee.PerformanceCardRate * 100) | toFixed | NumFormat }}%</span
                                        >
                                        <span v-else>无</span>
                                      </p>

                                      <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                    </el-popover>
                                  </span>
                                  <el-input type="number" v-input-fixed class="input_type" style="width: 120px" v-model="employee.CardPerformance">
                                    <template slot="append">元</template>
                                  </el-input>
                                </el-form-item>
                              </el-form>
                            </el-col>
                            <el-col :span="8">
                              <el-form
                                @submit.native.prevent
                                class="sale-ModifyPerformanceCommission-Handler"
                                label-position="right"
                                size="mini"
                                label-width="150px"
                              >
                                <el-form-item label="卡抵扣比例提成">
                                  <span slot="label">
                                    卡抵扣比例提成
                                    <el-popover placement="top-start" width="200" trigger="hover">
                                      <p>卡抵扣比例提成 = 卡抵扣业绩 x 卡抵扣比例</p>

                                      <p v-if="employee.CardRate != null">卡抵扣比例参考值：{{ employee.CardRate | toFixed | NumFormat }}%</p>
                                      <p v-else>卡抵扣比例参考值：无</p>
                                      <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                    </el-popover>
                                  </span>
                                  <el-input type="number" v-input-fixed class="input_type" style="width: 100px" v-model="employee.CardRateCommission">
                                    <template slot="append">元</template>
                                  </el-input>
                                </el-form-item>
                              </el-form>
                            </el-col>
                            <el-col :span="8">
                              <el-form
                                @submit.native.prevent
                                class="sale-ModifyPerformanceCommission-Handler"
                                label-position="right"
                                size="mini"
                                label-width="150px"
                              >
                                <el-form-item label="卡抵扣固定提成">
                                  <span slot="label">
                                    卡抵扣固定提成
                                    <el-popover placement="top-start" width="200" trigger="hover">
                                      <p>卡抵扣固定提成 = （卡抵扣业绩 ÷ 消耗金额）x 卡抵扣固定提成参考值 x 数量</p>

                                      <p v-if="employee.CardFixed">
                                        卡抵扣固定参考值：¥
                                        {{ employee.CardFixed | toFixed | NumFormat }}
                                      </p>
                                      <p v-else>卡抵扣固定参考值：无</p>
                                      <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                    </el-popover>
                                  </span>
                                  <el-input type="number" v-input-fixed class="input_type" style="width: 100px" v-model="employee.CardFixedCommission">
                                    <template slot="append">元</template>
                                  </el-input>
                                </el-form-item>
                              </el-form>
                            </el-col>
                          </el-row>
                          <!-- 卡赠金 -->
                          <el-row v-if="middItem.LargessCardDeductionAmount > 0" class="padtp_10 padrt_10" :class="index != 0 ? 'border_top' : ''">
                            <el-col :span="8">
                              <el-form
                                @submit.native.prevent
                                class="sale-ModifyPerformanceCommission-Handler"
                                label-position="right"
                                size="mini"
                                label-width="120px"
                              >
                                <el-form-item label="赠送卡扣业绩">
                                  <span slot="label">
                                    赠送卡扣业绩
                                    <el-popover placement="top-start" width="200" trigger="hover">
                                      <p>赠送卡抵扣业绩 = 赠送卡抵扣金额 x 赠送卡抵扣业绩占比 x 业绩占比</p>
                                      <p>
                                        员工赠送卡抵扣业绩占比参考值：
                                        <span v-if="employee.PerformanceCardLargessRate != null"
                                          >{{ (employee.PerformanceCardLargessRate * 100) | toFixed | NumFormat }}%</span
                                        >
                                        <span v-else>无</span>
                                      </p>

                                      <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                    </el-popover>
                                  </span>
                                  <el-input type="number" v-input-fixed class="input_type" style="width: 120px" v-model="employee.CardLargessPerformance">
                                    <template slot="append">元</template>
                                  </el-input>
                                </el-form-item>
                              </el-form>
                            </el-col>
                            <el-col :span="8">
                              <el-form
                                @submit.native.prevent
                                class="sale-ModifyPerformanceCommission-Handler"
                                label-position="right"
                                size="mini"
                                label-width="150px"
                              >
                                <el-form-item label="赠送卡扣比例提成">
                                  <span slot="label">
                                    赠送卡扣比例提成
                                    <el-popover placement="top-start" width="200" trigger="hover">
                                      <p>赠送卡扣比例提成 = 赠送卡扣业绩 x 赠送卡扣比例</p>

                                      <p v-if="employee.CardLargessRate != null">赠送卡扣比例参考值：{{ employee.CardLargessRate | toFixed | NumFormat }}%</p>
                                      <p v-else>赠送卡扣比例参考值：无</p>
                                      <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                    </el-popover>
                                  </span>
                                  <el-input type="number" v-input-fixed class="input_type" style="width: 100px" v-model="employee.CardLargessRateCommission">
                                    <template slot="append">元</template>
                                  </el-input>
                                </el-form-item>
                              </el-form>
                            </el-col>
                            <el-col :span="8">
                              <el-form
                                @submit.native.prevent
                                class="sale-ModifyPerformanceCommission-Handler"
                                label-position="right"
                                size="mini"
                                label-width="150px"
                              >
                                <el-form-item label="赠送卡扣固定提成">
                                  <span slot="label">
                                    赠送卡扣固定提成
                                    <el-popover placement="top-start" width="200" trigger="hover">
                                      <p>赠送卡扣固定提成 = （赠送卡扣业绩 ÷ 消耗金额）x 赠送卡扣固定参考值 x 数量</p>

                                      <p v-if="employee.CardLargessFixed">
                                        赠送卡扣固定参考值：¥
                                        {{ employee.CardLargessFixed | toFixed | NumFormat }}
                                      </p>
                                      <p v-else>赠送卡扣金固定参考值：无</p>
                                      <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                    </el-popover>
                                  </span>
                                  <el-input type="number" v-input-fixed class="input_type" style="width: 100px" v-model="employee.CardLargessFixedCommission">
                                    <template slot="append">元</template>
                                  </el-input>
                                </el-form-item>
                              </el-form>
                            </el-col>
                          </el-row>
                          <!-- 赠送 -->
                          <el-row v-if="middItem.LargessAmount > 0" class="padtp_10 padrt_10" :class="index != 0 ? 'border_top' : ''">
                            <el-col :span="8">
                              <el-form
                                @submit.native.prevent
                                class="sale-ModifyPerformanceCommission-Handler"
                                label-position="right"
                                size="mini"
                                label-width="120px"
                              >
                                <el-form-item label="赠送业绩">
                                  <span slot="label">
                                    赠送业绩
                                    <el-popover placement="top-start" width="200" trigger="hover">
                                      <p>赠送业绩 = 赠送金额 x 赠送业绩占比 x 业绩占比</p>
                                      <p>
                                        员工赠送业绩占比参考值：
                                        <span v-if="employee.PerformanceLargessRate != null"
                                          >{{ (employee.PerformanceLargessRate * 100) | toFixed | NumFormat }}%</span
                                        >
                                        <span v-else>无</span>
                                      </p>

                                      <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                    </el-popover>
                                  </span>
                                  <el-input type="number" v-input-fixed class="input_type" style="width: 120px" v-model="employee.LargessPerformance">
                                    <template slot="append">元</template>
                                  </el-input>
                                </el-form-item>
                              </el-form>
                            </el-col>
                            <el-col :span="8">
                              <el-form
                                @submit.native.prevent
                                class="sale-ModifyPerformanceCommission-Handler"
                                label-position="right"
                                size="mini"
                                label-width="150px"
                              >
                                <el-form-item label="赠送比例提成">
                                  <span slot="label">
                                    赠送比例提成
                                    <el-popover placement="top-start" width="200" trigger="hover">
                                      <p>赠送比例提成 = 赠送业绩 x 赠送比例</p>
                                      <p v-if="employee.LargessRate != null">赠送比例参考值：{{ employee.LargessRate | toFixed | NumFormat }}%</p>
                                      <p v-else>赠送比例参考值：无</p>
                                      <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                    </el-popover>
                                  </span>
                                  <el-input type="number" v-input-fixed class="input_type" style="width: 100px" v-model="employee.LargessRateCommission">
                                    <template slot="append">元</template>
                                  </el-input>
                                </el-form-item>
                              </el-form>
                            </el-col>
                            <el-col :span="8">
                              <el-form
                                @submit.native.prevent
                                class="sale-ModifyPerformanceCommission-Handler"
                                label-position="right"
                                size="mini"
                                label-width="150px"
                              >
                                <el-form-item label="赠送固定提成">
                                  <span slot="label">
                                    赠送固定提成
                                    <el-popover placement="top-start" width="200" trigger="hover">
                                      <p>赠送固定提成 = （赠送业绩 ÷ 消耗金额）x 赠送固定 x 数量</p>
                                      <p v-if="employee.LargessFixed">
                                        赠送固定参考值：¥
                                        {{ employee.LargessFixed | toFixed | NumFormat }}
                                      </p>
                                      <p v-else>赠送固定提成参考值：无</p>
                                      <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                    </el-popover>
                                  </span>
                                  <el-input type="number" v-input-fixed class="input_type" style="width: 100px" v-model="employee.LargessFixedCommission">
                                    <template slot="append">元</template>
                                  </el-input>
                                </el-form-item>
                              </el-form>
                            </el-col>
                          </el-row>
                        </el-col>

                        <el-col v-if="!item.IsLargess" :span="4" class="padtp_10" :class="index != 0 ? 'border_top' : ''">
                          <el-form
                            @submit.native.prevent
                            class="sale-ModifyPerformanceCommission-Handler"
                            label-position="right"
                            size="mini"
                            label-width="90px"
                          >
                            <el-form-item label="无业绩奖励">
                              <span slot="label">
                                无业绩奖励
                                <el-popover placement="top-start" width="200" trigger="hover">
                                  <p>无业绩奖励 = 无业绩奖励参考值 x 数量 x 经手人比例</p>
                                  <p v-if="employee.SpecialBenefit != null">
                                    无业绩奖励参考值：¥
                                    {{ employee.SpecialBenefit | toFixed | NumFormat }}
                                  </p>
                                  <p v-else>无业绩奖励参考值：无</p>
                                  <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"> </el-button>
                                </el-popover>
                              </span>
                              <el-input type="number" v-input-fixed class="input_type" style="width: 100px" v-model="employee.SpecialBenefitCommission">
                                <template slot="append">元</template>
                              </el-input>
                            </el-form-item>
                          </el-form>
                        </el-col>
                      </el-row>
                    </el-col>
                  </el-row>
                </el-row>
              </div>

              <!--储值卡-->
              <div v-if="item.SavingCard.length > 0">
                <el-row v-for="(middItem, middIndex) in item.SavingCard" :key="middIndex">
                  <el-row :span="24" style="background-color: #f5f7fa" class="pad_10 border_right border_left border_bottom">
                    <div>
                      {{ middItem.SavingCardName }}
                      <span v-if="middItem.Alias">({{ middItem.Alias }})</span>
                      <el-tag class="marlt_5" size="mini">储值卡</el-tag>
                    </div>
                  </el-row>
                  <el-row v-for="(chlidItem, chlidIndex) in middItem.Project" :key="chlidIndex">
                    <el-row class="border_right border_left border_bottom pad_10">
                      <el-col :span="6">
                        <div>
                          {{ chlidItem.ProjectName }}
                          <span v-if="chlidItem.Alias">({{ chlidItem.Alias }})</span>
                        </div>
                        <div class="color_red font_12 martp_5">
                          ¥ {{ chlidItem.Price | toFixed | NumFormat }}
                          <el-tag v-if="chlidItem.IsLargess" size="mini" class="marlt_10">赠</el-tag>
                        </div>
                      </el-col>
                      <el-col :span="4">x {{ chlidItem.Quantity }}</el-col>

                      <el-col :span="6" v-show="treatInfo.BillType == '10'">
                        <div>
                          <span v-if="chlidItem.PricePreferentialAmount + chlidItem.CardPreferentialAmount < 0"
                            >+ ¥ {{ Math.abs(chlidItem.PricePreferentialAmount + chlidItem.CardPreferentialAmount) | toFixed | NumFormat }}</span
                          >
                          <span v-else-if="chlidItem.PricePreferentialAmount + chlidItem.CardPreferentialAmount > 0"
                            >- ¥ {{ Math.abs(chlidItem.PricePreferentialAmount + chlidItem.CardPreferentialAmount) | toFixed | NumFormat }}</span
                          >
                          <span v-else>¥ 0.00</span>
                        </div>
                        <div class="martp_5 font_12">
                          <span class="color_gray font_12" v-if="chlidItem.PricePreferentialAmount != 0">
                            手动改价：
                            <span class="color_red" v-if="chlidItem.PricePreferentialAmount > 0">
                              - ¥
                              {{ chlidItem.PricePreferentialAmount | toFixed | NumFormat }}
                            </span>
                            <span class="color_green" v-else>
                              + ¥
                              {{ Math.abs(chlidItem.PricePreferentialAmount) | toFixed | NumFormat }}
                            </span>
                          </span>
                          <span
                            class="color_gray font_12"
                            :class="chlidItem.PricePreferentialAmount != 0 ? 'marlt_15' : ''"
                            v-if="chlidItem.CardPreferentialAmount > 0"
                          >
                            卡优惠：
                            <span class="color_red">
                              - ¥
                              {{ parseFloat(chlidItem.CardPreferentialAmount) | toFixed | NumFormat }}
                            </span>
                          </span>
                          <span class="color_gray font_12" :class="chlidItem.PricePreferentialAmount != 0 || chlidItem.CardPreferentialAmount != 0 ? 'marlt_15' : ''" v-if="chlidItem.MemberPreferentialAmount > 0">
                        会员优惠：
                        <span class="color_red">- ¥ {{ parseFloat(chlidItem.MemberPreferentialAmount) | toFixed | NumFormat }}</span>
                      </span>
                        </div>
                      </el-col>

                      <el-col :span="8">
                        <div>¥ {{ chlidItem.TotalAmount | toFixed | NumFormat }}</div>

                        <div class="martp_5 font_12">
                          <!-- <span class="color_green" v-if="chlidItem.PayAmount > 0">现金金额： {{ chlidItem.PayAmount | NumFormat }}</span> -->
                          <span class="color_green" v-if="chlidItem.CardDeductionAmount > 0"
                            >卡抵扣金额： {{ chlidItem.CardDeductionAmount | toFixed | NumFormat }}</span
                          >

                          <span class="color_red" v-if="chlidItem.LargessCardDeductionAmount > 0" :class="chlidItem.CardDeductionAmount != 0 ? 'marlt_15' : ''"
                            >赠送卡抵扣金额： {{ chlidItem.LargessCardDeductionAmount | toFixed | NumFormat }}</span
                          >

                          <!-- <span class="color_red" v-if="chlidItem.LargessAmount > 0" :class="chlidItem.LargessCardDeductionAmount != 0 ? 'marlt_15' : ''">赠送金额：¥ {{ chlidItem.LargessAmount | NumFormat }}</span> -->
                        </div>
                      </el-col>
                    </el-row>

                    <el-row
                      class="padlt_10 padrt_10 border_right border_left border_bottom font_12"
                      v-for="(handler, index) in chlidItem.TreatBillHandler"
                      :key="index"
                    >
                      <el-col :span="2" class="padtp_10 padbm_10">
                        <el-link class="font_12 line_26" type="primary" :underline="false" @click="employeeHandleClick(6, chlidItem, '储值卡')">{{
                          handler.TreatHandlerName
                        }}</el-link>
                      </el-col>
                      <el-col :span="22" class="border_left">
                        <el-row v-for="(employee, index) in handler.Employee" :key="employee.EmployeeID">
                          <el-col :span="5" class="padtp_10 padrt_10 " :class="index != 0 ? 'border_top' : ''">
                            <el-form
                              @submit.native.prevent
                              class="sale-ModifyPerformanceCommission-Handler"
                              label-position="right"
                              size="mini"
                              label-width="70px"
                              :inline="true"
                            >
                              <el-form-item :label="`${employee.EmployeeName}`">
                                <el-input
                                  type="number"
                                  v-input-fixed
                                  class="input_type"
                                  style="width: 90px"
                                  v-model="employee.Scale"
                                  @input="changeSaleHandlerRate(chlidItem, employee)"
                                >
                                  <template slot="append">%</template>
                                </el-input>
                              </el-form-item>
                              <el-form-item class="cursorclass">
                                <el-checkbox size="small" v-model="employee.IsCalculatePassengerFlow">客流</el-checkbox>
                              </el-form-item>
                            </el-form>
                          </el-col>

                          <el-col :span="15" class="border_right border_left">
                            <!-- 现金 -->
                            <el-row v-if="chlidItem.PayAmount > 0" class="padtp_10 padrt_10" :class="index != 0 ? 'border_top' : ''">
                              <el-col :span="8">
                                <el-form
                                  @submit.native.prevent
                                  class="sale-ModifyPerformanceCommission-Handler"
                                  label-position="right"
                                  size="mini"
                                  label-width="120px"
                                >
                                  <el-form-item label="现金业绩">
                                    <span slot="label">
                                      现金业绩
                                      <el-popover placement="top-start" width="200" trigger="hover">
                                        <p>现金业绩 = 现金金额 x 员工现金业绩占比 x 业绩占比</p>
                                        <p>
                                          员工现金业绩占比参考值：
                                          <span v-if="employee.PerformancePayRate != null"
                                            >{{ (employee.PerformancePayRate * 100) | toFixed | NumFormat }}%</span
                                          >
                                          <span v-else>无</span>
                                        </p>

                                        <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                      </el-popover>
                                    </span>
                                    <el-input type="number" v-input-fixed class="input_type" style="width: 120px" v-model="employee.PayPerformance">
                                      <template slot="append">元</template>
                                    </el-input>
                                  </el-form-item>
                                </el-form>
                              </el-col>
                              <el-col :span="8">
                                <el-form
                                  @submit.native.prevent
                                  class="sale-ModifyPerformanceCommission-Handler"
                                  label-position="right"
                                  size="mini"
                                  label-width="150px"
                                >
                                  <el-form-item label="现金比例提成">
                                    <span slot="label">
                                      现金比例提成
                                      <el-popover placement="top-start" width="200" trigger="hover">
                                        <p>现金比例提成 = 现金业绩 x 现金比例提成参考值</p>
                                        <p v-if="employee.PayRate != null">现金比例提成参考值：{{ employee.PayRate | toFixed | NumFormat }}%</p>
                                        <p v-else>现金比例提成参考值：无</p>
                                        <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                      </el-popover>
                                    </span>
                                    <el-input type="number" v-input-fixed class="input_type" style="width: 100px" v-model="employee.PayRateCommission">
                                      <template slot="append">元</template>
                                    </el-input>
                                  </el-form-item>
                                </el-form>
                              </el-col>
                              <el-col :span="8">
                                <el-form
                                  @submit.native.prevent
                                  class="sale-ModifyPerformanceCommission-Handler"
                                  label-position="right"
                                  size="mini"
                                  label-width="150px"
                                >
                                  <el-form-item label="现金固定提成">
                                    <span slot="label">
                                      现金固定提成
                                      <el-popover placement="top-start" width="200" trigger="hover">
                                        <p>现金固定提成 = （现金业绩 ÷ 消耗金额）x 现金固定提成参考值 x 数量</p>

                                        <p v-if="employee.PayFixed">
                                          现金固定提成参考值：¥
                                          {{ employee.PayFixed | toFixed | NumFormat }}
                                        </p>
                                        <p v-else>现金固定提成参考值：无</p>
                                        <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                      </el-popover>
                                    </span>
                                    <el-input type="number" v-input-fixed class="input_type" style="width: 100px" v-model="employee.PayFixedCommission">
                                      <template slot="append">元</template>
                                    </el-input>
                                  </el-form-item>
                                </el-form>
                              </el-col>
                            </el-row>
                            <!-- 卡本金 -->
                            <el-row v-if="chlidItem.CardDeductionAmount > 0" class="padtp_10 padrt_10" :class="index != 0 ? 'border_top' : ''">
                              <el-col :span="8">
                                <el-form
                                  @submit.native.prevent
                                  class="sale-ModifyPerformanceCommission-Handler"
                                  label-position="right"
                                  size="mini"
                                  label-width="120px"
                                >
                                  <el-form-item label="卡抵扣业绩">
                                    <span slot="label">
                                      卡抵扣业绩
                                      <el-popover placement="top-start" width="200" trigger="hover">
                                        <p>卡抵扣业绩 = 卡抵扣金额 x 员工卡抵扣业绩占比 x 业绩占比</p>
                                        <p>
                                          员工卡抵扣业绩占比参考值：
                                          <span v-if="employee.PerformanceCardRate != null"
                                            >{{ (employee.PerformanceCardRate * 100) | toFixed | NumFormat }}%</span
                                          >
                                          <span v-else>无</span>
                                        </p>

                                        <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                      </el-popover>
                                    </span>
                                    <el-input type="number" v-input-fixed class="input_type" style="width: 120px" v-model="employee.CardPerformance">
                                      <template slot="append">元</template>
                                    </el-input>
                                  </el-form-item>
                                </el-form>
                              </el-col>
                              <el-col :span="8">
                                <el-form
                                  @submit.native.prevent
                                  class="sale-ModifyPerformanceCommission-Handler"
                                  label-position="right"
                                  size="mini"
                                  label-width="150px"
                                >
                                  <el-form-item label="卡抵扣比例提成">
                                    <span slot="label">
                                      卡抵扣比例提成
                                      <el-popover placement="top-start" width="200" trigger="hover">
                                        <p>卡抵扣比例提成 = 卡抵扣业绩 x 卡抵扣比例</p>
                                        <p v-if="employee.CardRate != null">卡抵扣比例参考值：{{ employee.CardRate | toFixed | NumFormat }}%</p>
                                        <p v-else>卡抵扣比例参考值：无</p>
                                        <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                      </el-popover>
                                    </span>
                                    <el-input type="number" v-input-fixed class="input_type" style="width: 100px" v-model="employee.CardRateCommission">
                                      <template slot="append">元</template>
                                    </el-input>
                                  </el-form-item>
                                </el-form>
                              </el-col>
                              <el-col :span="8">
                                <el-form
                                  @submit.native.prevent
                                  class="sale-ModifyPerformanceCommission-Handler"
                                  label-position="right"
                                  size="mini"
                                  label-width="150px"
                                >
                                  <el-form-item label="卡抵扣固定提成">
                                    <span slot="label">
                                      卡抵扣固定提成
                                      <el-popover placement="top-start" width="200" trigger="hover">
                                        <p>卡抵扣固定提成 = （卡抵扣业绩 ÷ 消耗金额）x 卡抵扣固定提成参考值 x 数量</p>
                                        <p v-if="employee.CardFixed">
                                          卡抵扣固定参考值：¥
                                          {{ employee.CardFixed | toFixed | NumFormat }}
                                        </p>
                                        <p v-else>卡抵扣固定参考值：无</p>
                                        <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                      </el-popover>
                                    </span>
                                    <el-input type="number" v-input-fixed class="input_type" style="width: 100px" v-model="employee.CardFixedCommission">
                                      <template slot="append">元</template>
                                    </el-input>
                                  </el-form-item>
                                </el-form>
                              </el-col>
                            </el-row>
                            <!-- 卡赠金 -->
                            <el-row v-if="chlidItem.LargessCardDeductionAmount > 0" class="padtp_10 padrt_10" :class="index != 0 ? 'border_top' : ''">
                              <el-col :span="8">
                                <el-form
                                  @submit.native.prevent
                                  class="sale-ModifyPerformanceCommission-Handler"
                                  label-position="right"
                                  size="mini"
                                  label-width="120px"
                                >
                                  <el-form-item label="赠送卡扣业绩">
                                    <span slot="label">
                                      赠送卡扣业绩
                                      <el-popover placement="top-start" width="200" trigger="hover">
                                        <p>赠送卡抵扣业绩 = 赠送卡抵扣金额 x 赠送卡抵扣业绩占比 x 业绩占比</p>
                                        <p>
                                          员工赠送卡抵扣业绩占比参考值：
                                          <span v-if="employee.PerformanceCardLargessRate != null"
                                            >{{ (employee.PerformanceCardLargessRate * 100) | toFixed | NumFormat }}%</span
                                          >
                                          <span v-else>无</span>
                                        </p>

                                        <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                      </el-popover>
                                    </span>
                                    <el-input type="number" v-input-fixed class="input_type" style="width: 120px" v-model="employee.CardLargessPerformance">
                                      <template slot="append">元</template>
                                    </el-input>
                                  </el-form-item>
                                </el-form>
                              </el-col>
                              <el-col :span="8">
                                <el-form
                                  @submit.native.prevent
                                  class="sale-ModifyPerformanceCommission-Handler"
                                  label-position="right"
                                  size="mini"
                                  label-width="150px"
                                >
                                  <el-form-item label="赠送卡扣比例提成">
                                    <span slot="label">
                                      赠送卡扣比例提成
                                      <el-popover placement="top-start" width="200" trigger="hover">
                                        <p>赠送卡扣比例提成 = 赠送卡扣业绩 x 赠送卡扣比例</p>
                                        <p v-if="employee.CardLargessRate != null">赠送卡扣比例参考值：{{ employee.CardLargessRate | toFixed | NumFormat }}%</p>
                                        <p v-else>赠送卡扣比例参考值：无</p>
                                        <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                      </el-popover>
                                    </span>
                                    <el-input type="number" v-input-fixed class="input_type" style="width: 100px" v-model="employee.CardLargessRateCommission">
                                      <template slot="append">元</template>
                                    </el-input>
                                  </el-form-item>
                                </el-form>
                              </el-col>
                              <el-col :span="8">
                                <el-form
                                  @submit.native.prevent
                                  class="sale-ModifyPerformanceCommission-Handler"
                                  label-position="right"
                                  size="mini"
                                  label-width="150px"
                                >
                                  <el-form-item label="赠送卡扣固定提成">
                                    <span slot="label">
                                      赠送卡扣固定提成
                                      <el-popover placement="top-start" width="200" trigger="hover">
                                        <p>赠送卡扣固定提成 = （赠送卡扣业绩 ÷ 消耗金额）x 赠送卡扣固定参考值 x 数量</p>
                                        <p v-if="employee.CardLargessFixed">
                                          赠送卡扣固定参考值：¥
                                          {{ employee.CardLargessFixed | toFixed | NumFormat }}
                                        </p>
                                        <p v-else>赠送卡扣金固定参考值：无</p>
                                        <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                      </el-popover>
                                    </span>
                                    <el-input type="number" v-input-fixed class="input_type" style="width: 100px" v-model="employee.CardLargessFixedCommission">
                                      <template slot="append">元</template>
                                    </el-input>
                                  </el-form-item>
                                </el-form>
                              </el-col>
                            </el-row>
                            <!-- 赠送 -->
                            <el-row v-if="chlidItem.LargessAmount > 0" class="padtp_10 padrt_10" :class="index != 0 ? 'border_top' : ''">
                              <el-col :span="8">
                                <el-form
                                  @submit.native.prevent
                                  class="sale-ModifyPerformanceCommission-Handler"
                                  label-position="right"
                                  size="mini"
                                  label-width="120px"
                                >
                                  <el-form-item label="赠送业绩">
                                    <span slot="label">
                                      赠送业绩
                                      <el-popover placement="top-start" width="200" trigger="hover">
                                        <p>赠送业绩 = 赠送金额 x 赠送业绩占比 x 业绩占比</p>
                                        <p>
                                          员工赠送业绩占比参考值：
                                          <span v-if="employee.PerformanceLargessRate != null"
                                            >{{ (employee.PerformanceLargessRate * 100) | toFixed | NumFormat }}%</span
                                          >
                                          <span v-else>无</span>
                                        </p>

                                        <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                      </el-popover>
                                    </span>
                                    <el-input type="number" v-input-fixed class="input_type" style="width: 120px" v-model="employee.LargessPerformance">
                                      <template slot="append">元</template>
                                    </el-input>
                                  </el-form-item>
                                </el-form>
                              </el-col>
                              <el-col :span="8">
                                <el-form
                                  @submit.native.prevent
                                  class="sale-ModifyPerformanceCommission-Handler"
                                  label-position="right"
                                  size="mini"
                                  label-width="150px"
                                >
                                  <el-form-item label="赠送比例提成">
                                    <span slot="label">
                                      赠送比例提成
                                      <el-popover placement="top-start" width="200" trigger="hover">
                                        <p>赠送比例提成 = 赠送业绩 x 赠送比例</p>
                                        <p v-if="employee.LargessRate != null">赠送比例参考值：{{ employee.LargessRate | toFixed | NumFormat }}%</p>
                                        <p v-else>赠送比例参考值：无</p>
                                        <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                      </el-popover>
                                    </span>
                                    <el-input type="number" v-input-fixed class="input_type" style="width: 100px" v-model="employee.LargessRateCommission">
                                      <template slot="append">元</template>
                                    </el-input>
                                  </el-form-item>
                                </el-form>
                              </el-col>
                              <el-col :span="8">
                                <el-form
                                  @submit.native.prevent
                                  class="sale-ModifyPerformanceCommission-Handler"
                                  label-position="right"
                                  size="mini"
                                  label-width="150px"
                                >
                                  <el-form-item label="赠送固定提成">
                                    <span slot="label">
                                      赠送固定提成
                                      <el-popover placement="top-start" width="200" trigger="hover">
                                        <p>赠送固定提成 = （赠送业绩 ÷ 消耗金额）x 赠送固定 x 数量</p>
                                        <p v-if="employee.LargessFixed">
                                          赠送固定参考值：¥
                                          {{ employee.LargessFixed | toFixed | NumFormat }}
                                        </p>
                                        <p v-else>赠送固定提成参考值：无</p>
                                        <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                      </el-popover>
                                    </span>
                                    <el-input type="number" v-input-fixed class="input_type" style="width: 100px" v-model="employee.LargessFixedCommission">
                                      <template slot="append">元</template>
                                    </el-input>
                                  </el-form-item>
                                </el-form>
                              </el-col>
                            </el-row>
                          </el-col>

                          <el-col v-if="!item.IsLargess" :span="4" class="padtp_10" :class="index != 0 ? 'border_top' : ''">
                            <el-form
                              @submit.native.prevent
                              class="sale-ModifyPerformanceCommission-Handler"
                              label-position="right"
                              size="mini"
                              label-width="90px"
                            >
                              <el-form-item label="无业绩奖励">
                                <span slot="label">
                                  无业绩奖励
                                  <el-popover placement="top-start" width="200" trigger="hover">
                                    <p>无业绩奖励 = 无业绩奖励参考值 x 数量 x 经手人比例</p>
                                    <p v-if="employee.SpecialBenefit != null">
                                      无业绩奖励参考值：¥
                                      {{ employee.SpecialBenefit | toFixed | NumFormat }}
                                    </p>
                                    <p v-else>无业绩奖励参考值：无</p>
                                    <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"> </el-button>
                                  </el-popover>
                                </span>
                                <el-input type="number" v-input-fixed class="input_type" style="width: 100px" v-model="employee.SpecialBenefitCommission">
                                  <template slot="append">元</template>
                                </el-input>
                              </el-form-item>
                            </el-form>
                          </el-col>
                        </el-row>
                      </el-col>
                    </el-row>
                  </el-row>
                </el-row>
              </div>

              <!--时效卡-->
              <div v-if="item.TimeCard.length > 0">
                <el-row v-for="(middItem, middIndex) in item.TimeCard" :key="middIndex">
                  <el-row :span="24" style="background-color: #f5f7fa" class="pad_10 border_right border_left border_bottom">
                    <div>
                      {{ middItem.TimeCardName }}
                      <span v-if="middItem.Alias">({{ middItem.Alias }})</span>
                      <el-tag v-if="middItem.IsLargess" size="mini" type="danger" class="marlt_5">赠</el-tag>
                      <el-tag class="marlt_5" size="mini">时效卡</el-tag>
                    </div>
                  </el-row>
                  <el-row v-for="(chlidItem, chlidIndex) in middItem.Project" :key="chlidIndex">
                    <el-row class="border_left border_bottom border_right pad_10">
                      <el-col :span="6">
                        <div>
                          {{ chlidItem.ProjectName }}
                          <span v-if="chlidItem.Alias">({{ chlidItem.Alias }})</span>
                          <el-tag v-if="middItem.IsLargess" size="mini" type="danger" class="marlt_5">赠</el-tag>
                        </div>
                        <div class="color_red font_12 martp_5">¥ {{ chlidItem.Price | toFixed | NumFormat }}</div>
                      </el-col>
                      <el-col :span="4">x {{ chlidItem.Quantity }}</el-col>
                      <el-col :span="6" v-show="treatInfo.BillType == '10'">
                        <div>
                          <span v-if="chlidItem.CardPreferentialAmount < 0">+ ¥ {{ Math.abs(chlidItem.CardPreferentialAmount) | toFixed | NumFormat }}</span>
                          <span v-else-if="chlidItem.CardPreferentialAmount > 0"
                            >- ¥ {{ Math.abs(chlidItem.CardPreferentialAmount) | toFixed | NumFormat }}</span
                          >
                          <span v-else>¥ 0.00</span>
                        </div>
                        <div class="martp_5 font_12">
                          <span class="color_gray font_12" v-if="chlidItem.CardPreferentialAmount != 0">
                            卡优惠
                            <span class="color_red" v-if="chlidItem.CardPreferentialAmount > 0">
                              - ¥
                              {{ chlidItem.CardPreferentialAmount | toFixed | NumFormat }}
                            </span>
                            <span class="color_green" v-else>
                              + ¥
                              {{ Math.abs(chlidItem.CardPreferentialAmount) | toFixed | NumFormat }}
                            </span>
                          </span>
                        </div>
                      </el-col>
                      <el-col :span="8">
                        <div>¥ {{ chlidItem.TotalAmount | toFixed | NumFormat }}</div>
                        <div class="martp_5 font_12">
                          <span class="color_green" v-if="chlidItem.PayAmount > 0">现金金额： {{ chlidItem.PayAmount | toFixed | NumFormat }}</span>
                          <span class="color_green" v-if="chlidItem.CardDeductionAmount > 0" :class="chlidItem.PayAmount != 0 ? 'marlt_15' : ''"
                            >卡抵扣金额： {{ chlidItem.CardDeductionAmount | toFixed | NumFormat }}</span
                          >

                          <span class="color_red" v-if="chlidItem.LargessCardDeductionAmount > 0" :class="chlidItem.CardDeductionAmount != 0 ? 'marlt_15' : ''"
                            >赠送卡抵扣金额： {{ chlidItem.LargessCardDeductionAmount | toFixed | NumFormat }}</span
                          >

                          <span class="color_red" v-if="chlidItem.LargessAmount > 0" :class="chlidItem.LargessCardDeductionAmount != 0 ? 'marlt_15' : ''"
                            >赠送金额：¥ {{ chlidItem.LargessAmount | toFixed | NumFormat }}</span
                          >
                        </div>
                      </el-col>
                    </el-row>

                    <el-row
                      class="padlt_10 padrt_10 border_right border_left border_bottom font_12"
                      v-for="(handler, index) in chlidItem.TreatBillHandler"
                      :key="index"
                    >
                      <el-col :span="2" class="padtp_10 padbm_10">
                        <el-link class="font_12 line_26" type="primary" :underline="false" @click="employeeHandleClick(6, chlidItem, '时效卡')">{{
                          handler.TreatHandlerName
                        }}</el-link>
                      </el-col>
                      <el-col :span="22" class="border_left">
                        <el-row v-for="(employee, index) in handler.Employee" :key="employee.EmployeeID">
                          <el-col :span="5" class="padtp_10 padrt_10 " :class="index != 0 ? 'border_top' : ''">
                            <el-form
                              @submit.native.prevent
                              class="sale-ModifyPerformanceCommission-Handler"
                              label-position="right"
                              size="mini"
                              label-width="70px"
                              :inline="true"
                            >
                              <el-form-item :label="`${employee.EmployeeName}`">
                                <el-input
                                  type="number"
                                  v-input-fixed
                                  class="input_type"
                                  style="width: 90px"
                                  v-model="employee.Scale"
                                  @input="changeSaleHandlerRate(chlidItem, employee)"
                                >
                                  <template slot="append">%</template>
                                </el-input>
                              </el-form-item>
                              <el-form-item class="cursorclass">
                                <el-checkbox size="small" v-model="employee.IsCalculatePassengerFlow">客流</el-checkbox>
                              </el-form-item>
                            </el-form>
                          </el-col>

                          <el-col :span="15" class="border_right border_left">
                            <!-- 现金 -->
                            <el-row v-if="chlidItem.PayAmount > 0" class="padtp_10 padrt_10" :class="index != 0 ? 'border_top' : ''">
                              <el-col :span="8">
                                <el-form
                                  @submit.native.prevent
                                  class="sale-ModifyPerformanceCommission-Handler"
                                  label-position="right"
                                  size="mini"
                                  label-width="120px"
                                >
                                  <el-form-item label="现金业绩">
                                    <span slot="label">
                                      现金业绩
                                      <el-popover placement="top-start" width="200" trigger="hover">
                                        <p>现金业绩 = 现金金额 x 员工现金业绩占比 x 业绩占比</p>
                                        <p>
                                          员工现金业绩占比参考值：
                                          <span v-if="employee.PerformancePayRate != null"
                                            >{{ (employee.PerformancePayRate * 100) | toFixed | NumFormat }}%</span
                                          >
                                          <span v-else>无</span>
                                        </p>

                                        <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                      </el-popover>
                                    </span>
                                    <el-input type="number" v-input-fixed class="input_type" style="width: 120px" v-model="employee.PayPerformance">
                                      <template slot="append">元</template>
                                    </el-input>
                                  </el-form-item>
                                </el-form>
                              </el-col>
                              <el-col :span="8">
                                <el-form
                                  @submit.native.prevent
                                  class="sale-ModifyPerformanceCommission-Handler"
                                  label-position="right"
                                  size="mini"
                                  label-width="150px"
                                >
                                  <el-form-item label="现金比例提成">
                                    <span slot="label">
                                      现金比例提成
                                      <el-popover placement="top-start" width="200" trigger="hover">
                                        <p>现金比例提成 = 现金业绩 x 现金比例提成参考值</p>
                                        <p v-if="employee.PayRate != null">现金比例提成参考值：{{ employee.PayRate | toFixed | NumFormat }}%</p>
                                        <p v-else>现金比例提成参考值：无</p>
                                        <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                      </el-popover>
                                    </span>
                                    <el-input type="number" v-input-fixed class="input_type" style="width: 100px" v-model="employee.PayRateCommission">
                                      <template slot="append">元</template>
                                    </el-input>
                                  </el-form-item>
                                </el-form>
                              </el-col>
                              <el-col :span="8">
                                <el-form
                                  @submit.native.prevent
                                  class="sale-ModifyPerformanceCommission-Handler"
                                  label-position="right"
                                  size="mini"
                                  label-width="150px"
                                >
                                  <el-form-item label="现金固定提成">
                                    <span slot="label">
                                      现金固定提成
                                      <el-popover placement="top-start" width="200" trigger="hover">
                                        <p>现金固定提成 = （现金业绩 ÷ 消耗金额）x 现金固定提成参考值 x 数量</p>
                                        <p v-if="employee.PayFixed">
                                          现金固定提成参考值：¥
                                          {{ employee.PayFixed | toFixed | NumFormat }}
                                        </p>
                                        <p v-else>现金固定提成参考值：无</p>
                                        <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                      </el-popover>
                                    </span>
                                    <el-input type="number" v-input-fixed class="input_type" style="width: 100px" v-model="employee.PayFixedCommission">
                                      <template slot="append">元</template>
                                    </el-input>
                                  </el-form-item>
                                </el-form>
                              </el-col>
                            </el-row>
                            <!-- 卡本金 -->
                            <el-row v-if="chlidItem.CardDeductionAmount > 0" class="padtp_10 padrt_10" :class="index != 0 ? 'border_top' : ''">
                              <el-col :span="8">
                                <el-form
                                  @submit.native.prevent
                                  class="sale-ModifyPerformanceCommission-Handler"
                                  label-position="right"
                                  size="mini"
                                  label-width="120px"
                                >
                                  <el-form-item label="卡抵扣业绩">
                                    <span slot="label">
                                      卡抵扣业绩
                                      <el-popover placement="top-start" width="200" trigger="hover">
                                        <p>卡抵扣业绩 = 卡抵扣金额 x 员工卡抵扣业绩占比 x 业绩占比</p>
                                        <p>
                                          员工卡抵扣业绩占比参考值：
                                          <span v-if="employee.PerformanceCardRate != null"
                                            >{{ (employee.PerformanceCardRate * 100) | toFixed | NumFormat }}%</span
                                          >
                                          <span v-else>无</span>
                                        </p>

                                        <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                      </el-popover>
                                    </span>
                                    <el-input type="number" v-input-fixed class="input_type" style="width: 120px" v-model="employee.CardPerformance">
                                      <template slot="append">元</template>
                                    </el-input>
                                  </el-form-item>
                                </el-form>
                              </el-col>
                              <el-col :span="8">
                                <el-form
                                  @submit.native.prevent
                                  class="sale-ModifyPerformanceCommission-Handler"
                                  label-position="right"
                                  size="mini"
                                  label-width="150px"
                                >
                                  <el-form-item label="卡抵扣比例提成">
                                    <span slot="label">
                                      卡抵扣比例提成
                                      <el-popover placement="top-start" width="200" trigger="hover">
                                        <p>卡抵扣比例提成 = 卡抵扣业绩 x 卡抵扣比例</p>

                                        <p v-if="employee.CardRate != null">卡抵扣比例参考值：{{ employee.CardRate | toFixed | NumFormat }}%</p>
                                        <p v-else>卡抵扣比例参考值：无</p>
                                        <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                      </el-popover>
                                    </span>
                                    <el-input type="number" v-input-fixed class="input_type" style="width: 100px" v-model="employee.CardRateCommission">
                                      <template slot="append">元</template>
                                    </el-input>
                                  </el-form-item>
                                </el-form>
                              </el-col>
                              <el-col :span="8">
                                <el-form
                                  @submit.native.prevent
                                  class="sale-ModifyPerformanceCommission-Handler"
                                  label-position="right"
                                  size="mini"
                                  label-width="150px"
                                >
                                  <el-form-item label="卡抵扣固定提成">
                                    <span slot="label">
                                      卡抵扣固定提成
                                      <el-popover placement="top-start" width="200" trigger="hover">
                                        <p>卡抵扣固定提成 = （卡抵扣业绩 ÷ 消耗金额）x 卡抵扣固定提成参考值 x 数量</p>

                                        <p v-if="employee.CardFixed">
                                          卡抵扣固定参考值：¥
                                          {{ employee.CardFixed | toFixed | NumFormat }}
                                        </p>
                                        <p v-else>卡抵扣固定参考值：无</p>
                                        <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                      </el-popover>
                                    </span>
                                    <el-input type="number" v-input-fixed class="input_type" style="width: 100px" v-model="employee.CardFixedCommission">
                                      <template slot="append">元</template>
                                    </el-input>
                                  </el-form-item>
                                </el-form>
                              </el-col>
                            </el-row>
                            <!-- 卡赠金 -->
                            <el-row v-if="chlidItem.LargessCardDeductionAmount > 0" class="padtp_10 padrt_10" :class="index != 0 ? 'border_top' : ''">
                              <el-col :span="8">
                                <el-form
                                  @submit.native.prevent
                                  class="sale-ModifyPerformanceCommission-Handler"
                                  label-position="right"
                                  size="mini"
                                  label-width="120px"
                                >
                                  <el-form-item label="赠送卡扣业绩">
                                    <span slot="label">
                                      赠送卡扣业绩
                                      <el-popover placement="top-start" width="200" trigger="hover">
                                        <p>赠送卡抵扣业绩 = 赠送卡抵扣金额 x 赠送卡抵扣业绩占比 x 业绩占比</p>
                                        <p>
                                          员工赠送卡抵扣业绩占比参考值：
                                          <span v-if="employee.PerformanceCardLargessRate != null"
                                            >{{ (employee.PerformanceCardLargessRate * 100) | toFixed | NumFormat }}%</span
                                          >
                                          <span v-else>无</span>
                                        </p>

                                        <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                      </el-popover>
                                    </span>
                                    <el-input type="number" v-input-fixed class="input_type" style="width: 120px" v-model="employee.CardLargessPerformance">
                                      <template slot="append">元</template>
                                    </el-input>
                                  </el-form-item>
                                </el-form>
                              </el-col>
                              <el-col :span="8">
                                <el-form
                                  @submit.native.prevent
                                  class="sale-ModifyPerformanceCommission-Handler"
                                  label-position="right"
                                  size="mini"
                                  label-width="150px"
                                >
                                  <el-form-item label="赠送卡扣比例提成">
                                    <span slot="label">
                                      赠送卡扣比例提成
                                      <el-popover placement="top-start" width="200" trigger="hover">
                                        <p>赠送卡扣比例提成 = 赠送卡扣业绩 x 赠送卡扣比例</p>

                                        <p v-if="employee.CardLargessRate != null">赠送卡扣比例参考值：{{ employee.CardLargessRate | toFixed | NumFormat }}%</p>
                                        <p v-else>赠送卡扣比例参考值：无</p>
                                        <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                      </el-popover>
                                    </span>
                                    <el-input type="number" v-input-fixed class="input_type" style="width: 100px" v-model="employee.CardLargessRateCommission">
                                      <template slot="append">元</template>
                                    </el-input>
                                  </el-form-item>
                                </el-form>
                              </el-col>
                              <el-col :span="8">
                                <el-form
                                  @submit.native.prevent
                                  class="sale-ModifyPerformanceCommission-Handler"
                                  label-position="right"
                                  size="mini"
                                  label-width="150px"
                                >
                                  <el-form-item label="赠送卡扣固定提成">
                                    <span slot="label">
                                      赠送卡扣固定提成
                                      <el-popover placement="top-start" width="200" trigger="hover">
                                        <p>赠送卡扣固定提成 = （赠送卡扣业绩 ÷ 消耗金额）x 赠送卡扣固定参考值 x 数量</p>

                                        <p v-if="employee.CardLargessFixed">
                                          赠送卡扣固定参考值：¥
                                          {{ employee.CardLargessFixed | toFixed | NumFormat }}
                                        </p>
                                        <p v-else>赠送卡扣金固定参考值：无</p>
                                        <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                      </el-popover>
                                    </span>
                                    <el-input type="number" v-input-fixed class="input_type" style="width: 100px" v-model="employee.CardLargessFixedCommission">
                                      <template slot="append">元</template>
                                    </el-input>
                                  </el-form-item>
                                </el-form>
                              </el-col>
                            </el-row>
                            <!-- 赠送 -->
                            <el-row v-if="chlidItem.LargessAmount > 0" class="padtp_10 padrt_10" :class="index != 0 ? 'border_top' : ''">
                              <el-col :span="8">
                                <el-form
                                  @submit.native.prevent
                                  class="sale-ModifyPerformanceCommission-Handler"
                                  label-position="right"
                                  size="mini"
                                  label-width="120px"
                                >
                                  <el-form-item label="赠送业绩">
                                    <span slot="label">
                                      赠送业绩
                                      <el-popover placement="top-start" width="200" trigger="hover">
                                        <p>赠送业绩 = 赠送金额 x 赠送业绩占比 x 业绩占比</p>
                                        <p>
                                          员工赠送业绩占比参考值：
                                          <span v-if="employee.PerformanceLargessRate != null"
                                            >{{ (employee.PerformanceLargessRate * 100) | toFixed | NumFormat }}%</span
                                          >
                                          <span v-else>无</span>
                                        </p>

                                        <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                      </el-popover>
                                    </span>
                                    <el-input type="number" v-input-fixed class="input_type" style="width: 120px" v-model="employee.LargessPerformance">
                                      <template slot="append">元</template>
                                    </el-input>
                                  </el-form-item>
                                </el-form>
                              </el-col>
                              <el-col :span="8">
                                <el-form
                                  @submit.native.prevent
                                  class="sale-ModifyPerformanceCommission-Handler"
                                  label-position="right"
                                  size="mini"
                                  label-width="150px"
                                >
                                  <el-form-item label="赠送比例提成">
                                    <span slot="label">
                                      赠送比例提成
                                      <el-popover placement="top-start" width="200" trigger="hover">
                                        <p>赠送比例提成 = 赠送业绩 x 赠送比例</p>
                                        <p v-if="employee.LargessRate != null">赠送比例参考值：{{ employee.LargessRate | toFixed | NumFormat }}%</p>
                                        <p v-else>赠送比例参考值：无</p>
                                        <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                      </el-popover>
                                    </span>
                                    <el-input type="number" v-input-fixed class="input_type" style="width: 100px" v-model="employee.LargessRateCommission">
                                      <template slot="append">元</template>
                                    </el-input>
                                  </el-form-item>
                                </el-form>
                              </el-col>
                              <el-col :span="8">
                                <el-form
                                  @submit.native.prevent
                                  class="sale-ModifyPerformanceCommission-Handler"
                                  label-position="right"
                                  size="mini"
                                  label-width="150px"
                                >
                                  <el-form-item label="赠送固定提成">
                                    <span slot="label">
                                      赠送固定提成
                                      <el-popover placement="top-start" width="200" trigger="hover">
                                        <p>赠送固定提成 = （赠送业绩 ÷ 消耗金额）x 赠送固定 x 数量</p>
                                        <p v-if="employee.LargessFixed">
                                          赠送固定参考值：¥
                                          {{ employee.LargessFixed | toFixed | NumFormat }}
                                        </p>
                                        <p v-else>赠送固定提成参考值：无</p>
                                        <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                      </el-popover>
                                    </span>
                                    <el-input type="number" v-input-fixed class="input_type" style="width: 100px" v-model="employee.LargessFixedCommission">
                                      <template slot="append">元</template>
                                    </el-input>
                                  </el-form-item>
                                </el-form>
                              </el-col>
                            </el-row>
                          </el-col>

                          <el-col v-if="!item.IsLargess" :span="4" class="padtp_10" :class="index != 0 ? 'border_top' : ''">
                            <el-form
                              @submit.native.prevent
                              class="sale-ModifyPerformanceCommission-Handler"
                              label-position="right"
                              size="mini"
                              label-width="90px"
                            >
                              <el-form-item label="无业绩奖励">
                                <span slot="label">
                                  无业绩奖励
                                  <el-popover placement="top-start" width="200" trigger="hover">
                                    <p>无业绩奖励 = 无业绩奖励参考值 x 数量 x 经手人比例</p>
                                    <p v-if="employee.SpecialBenefit != null">
                                      无业绩奖励参考值：¥
                                      {{ employee.SpecialBenefit | toFixed | NumFormat }}
                                    </p>
                                    <p v-else>无业绩奖励参考值：无</p>
                                    <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"> </el-button>
                                  </el-popover>
                                </span>
                                <el-input type="number" v-input-fixed class="input_type" style="width: 100px" v-model="employee.SpecialBenefitCommission">
                                  <template slot="append">元</template>
                                </el-input>
                              </el-form-item>
                            </el-form>
                          </el-col>
                        </el-row>
                      </el-col>
                    </el-row>
                  </el-row>
                </el-row>
              </div>

              <!--通用次卡-->
              <div v-if="item.GeneralCard.length > 0">
                <el-row v-for="(middItem, middIndex) in item.GeneralCard" :key="middIndex">
                  <el-row :span="24" style="background-color: #f5f7fa" class="pad_10 border_right border_left border_bottom">
                    <div>
                      {{ middItem.GeneralCardName }}
                      <span v-if="middItem.Alias">({{ middItem.Alias }})</span>
                      <el-tag v-if="middItem.IsLargess" size="mini" class="marlt_5" type="danger">赠</el-tag>
                      <el-tag class="marlt_5" size="mini">通用次卡</el-tag>
                    </div>
                  </el-row>
                  <el-row v-for="(chlidItem, chlidIndex) in middItem.Project" :key="chlidIndex">
                    <el-row class="border_left border_bottom border_left pad_10">
                      <el-col :span="6">
                        <div>
                          {{ chlidItem.ProjectName }}
                          <span v-if="chlidItem.Alias">({{ chlidItem.Alias }})</span>
                          <el-tag v-if="middItem.IsLargess" size="mini" class="marlt_5" type="danger">赠</el-tag>
                        </div>
                        <div class="color_red font_12 martp_5">¥ {{ chlidItem.Price | toFixed | NumFormat }}</div>
                      </el-col>
                      <el-col :span="4">x {{ chlidItem.Quantity }}</el-col>
                      <el-col :span="6" v-show="treatInfo.BillType == '10'">
                        <div>
                          <span v-if="chlidItem.CardPreferentialAmount < 0">+ ¥ {{ Math.abs(chlidItem.CardPreferentialAmount) | toFixed | NumFormat }}</span>
                          <span v-else-if="chlidItem.CardPreferentialAmount > 0"
                            >- ¥ {{ Math.abs(chlidItem.CardPreferentialAmount) | toFixed | NumFormat }}</span
                          >
                          <span v-else>¥ 0.00</span>
                        </div>
                        <div class="martp_5 font_12">
                          <span class="color_gray font_12" v-if="chlidItem.CardPreferentialAmount != 0">
                            卡优惠
                            <span class="color_red" v-if="chlidItem.CardPreferentialAmount > 0">
                              - ¥
                              {{ chlidItem.CardPreferentialAmount | toFixed | NumFormat }}
                            </span>
                            <span class="color_green" v-else>
                              + ¥
                              {{ Math.abs(chlidItem.CardPreferentialAmount) | toFixed | NumFormat }}
                            </span>
                          </span>
                        </div>
                      </el-col>
                      <el-col :span="8">
                        <div>¥ {{ chlidItem.TotalAmount | toFixed | NumFormat }}</div>
                        <div class="martp_5 font_12">
                          <span class="color_green" v-if="chlidItem.PayAmount > 0">现金金额： {{ chlidItem.PayAmount | toFixed | NumFormat }}</span>
                          <span class="color_green" v-if="chlidItem.CardDeductionAmount > 0" :class="chlidItem.PayAmount != 0 ? 'marlt_15' : ''"
                            >卡抵扣金额： {{ chlidItem.CardDeductionAmount | toFixed | NumFormat }}</span
                          >

                          <span class="color_red" v-if="chlidItem.LargessCardDeductionAmount > 0" :class="chlidItem.CardDeductionAmount != 0 ? 'marlt_15' : ''"
                            >赠送卡抵扣金额： {{ chlidItem.LargessCardDeductionAmount | toFixed | NumFormat }}</span
                          >

                          <span class="color_red" v-if="chlidItem.LargessAmount > 0" :class="chlidItem.LargessCardDeductionAmount != 0 ? 'marlt_15' : ''"
                            >赠送金额：¥ {{ chlidItem.LargessAmount | toFixed | NumFormat }}</span
                          >
                        </div>
                      </el-col>
                    </el-row>

                    <el-row
                      class="padlt_10 padrt_10 border_right border_left border_bottom font_12"
                      v-for="(handler, index) in chlidItem.TreatBillHandler"
                      :key="index"
                    >
                      <el-col :span="2" class="padtp_10 padbm_10">
                        <el-link class="font_12 line_26" type="primary" :underline="false" @click="employeeHandleClick(6, chlidItem, '通用次卡')">{{
                          handler.TreatHandlerName
                        }}</el-link>
                      </el-col>
                      <el-col :span="22" class="border_left">
                        <el-row v-for="(employee, index) in handler.Employee" :key="employee.EmployeeID">
                          <el-col :span="5" class="padtp_10 padrt_10 " :class="index != 0 ? 'border_top' : ''">
                            <el-form
                              @submit.native.prevent
                              class="sale-ModifyPerformanceCommission-Handler"
                              label-position="right"
                              size="mini"
                              label-width="70px"
                              :inline="true"
                            >
                              <el-form-item :label="`${employee.EmployeeName}`">
                                <el-input
                                  type="number"
                                  v-input-fixed
                                  class="input_type"
                                  style="width: 90px"
                                  v-model="employee.Scale"
                                  @input="changeSaleHandlerRate(chlidItem, employee)"
                                >
                                  <template slot="append">%</template>
                                </el-input>
                              </el-form-item>
                              <el-form-item class="cursorclass">
                                <el-checkbox size="small" v-model="employee.IsCalculatePassengerFlow">客流</el-checkbox>
                              </el-form-item>
                            </el-form>
                          </el-col>

                          <el-col :span="15" class="border_right border_left">
                            <!-- 现金 -->
                            <el-row v-if="chlidItem.PayAmount > 0" class="padtp_10 padrt_10" :class="index != 0 ? 'border_top' : ''">
                              <el-col :span="8">
                                <el-form
                                  @submit.native.prevent
                                  class="sale-ModifyPerformanceCommission-Handler"
                                  label-position="right"
                                  size="mini"
                                  label-width="120px"
                                >
                                  <el-form-item label="现金业绩">
                                    <span slot="label">
                                      现金业绩
                                      <el-popover placement="top-start" width="200" trigger="hover">
                                        <p>现金业绩 = 现金金额 x 员工现金业绩占比 x 业绩占比</p>
                                        <p>
                                          员工现金业绩占比参考值：
                                          <span v-if="employee.PerformancePayRate != null"
                                            >{{ (employee.PerformancePayRate * 100) | toFixed | NumFormat }}%</span
                                          >
                                          <span v-else>无</span>
                                        </p>

                                        <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                      </el-popover>
                                    </span>
                                    <el-input type="number" v-input-fixed class="input_type" style="width: 120px" v-model="employee.PayPerformance">
                                      <template slot="append">元</template>
                                    </el-input>
                                  </el-form-item>
                                </el-form>
                              </el-col>
                              <el-col :span="8">
                                <el-form
                                  @submit.native.prevent
                                  class="sale-ModifyPerformanceCommission-Handler"
                                  label-position="right"
                                  size="mini"
                                  label-width="150px"
                                >
                                  <el-form-item label="现金比例提成">
                                    <span slot="label">
                                      现金比例提成
                                      <el-popover placement="top-start" width="200" trigger="hover">
                                        <p>现金比例提成 = 现金业绩 x 现金比例提成参考值</p>
                                        <p v-if="employee.PayRate != null">现金比例提成参考值：{{ employee.PayRate | toFixed | NumFormat }}%</p>
                                        <p v-else>现金比例提成参考值：无</p>
                                        <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                      </el-popover>
                                    </span>
                                    <el-input type="number" v-input-fixed class="input_type" style="width: 100px" v-model="employee.PayRateCommission">
                                      <template slot="append">元</template>
                                    </el-input>
                                  </el-form-item>
                                </el-form>
                              </el-col>
                              <el-col :span="8">
                                <el-form
                                  @submit.native.prevent
                                  class="sale-ModifyPerformanceCommission-Handler"
                                  label-position="right"
                                  size="mini"
                                  label-width="150px"
                                >
                                  <el-form-item label="现金固定提成">
                                    <span slot="label">
                                      现金固定提成
                                      <el-popover placement="top-start" width="200" trigger="hover">
                                        <p>现金固定提成 = （现金业绩 ÷ 消耗金额）x 现金固定提成参考值 x 数量</p>

                                        <p v-if="employee.PayFixed">
                                          现金固定提成参考值：¥
                                          {{ employee.PayFixed | toFixed | NumFormat }}
                                        </p>
                                        <p v-else>现金固定提成参考值：无</p>
                                        <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                      </el-popover>
                                    </span>
                                    <el-input type="number" v-input-fixed class="input_type" style="width: 100px" v-model="employee.PayFixedCommission">
                                      <template slot="append">元</template>
                                    </el-input>
                                  </el-form-item>
                                </el-form>
                              </el-col>
                            </el-row>
                            <!-- 卡本金 -->
                            <el-row v-if="chlidItem.CardDeductionAmount > 0" class="padtp_10 padrt_10" :class="index != 0 ? 'border_top' : ''">
                              <el-col :span="8">
                                <el-form
                                  @submit.native.prevent
                                  class="sale-ModifyPerformanceCommission-Handler"
                                  label-position="right"
                                  size="mini"
                                  label-width="120px"
                                >
                                  <el-form-item label="卡抵扣业绩">
                                    <span slot="label">
                                      卡抵扣业绩
                                      <el-popover placement="top-start" width="200" trigger="hover">
                                        <p>卡抵扣业绩 = 卡抵扣金额 x 员工卡抵扣业绩占比 x 业绩占比</p>
                                        <p>
                                          员工卡抵扣业绩占比参考值：
                                          <span v-if="employee.PerformanceCardRate != null"
                                            >{{ (employee.PerformanceCardRate * 100) | toFixed | NumFormat }}%</span
                                          >
                                          <span v-else>无</span>
                                        </p>

                                        <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                      </el-popover>
                                    </span>
                                    <el-input type="number" v-input-fixed class="input_type" style="width: 120px" v-model="employee.CardPerformance">
                                      <template slot="append">元</template>
                                    </el-input>
                                  </el-form-item>
                                </el-form>
                              </el-col>
                              <el-col :span="8">
                                <el-form
                                  @submit.native.prevent
                                  class="sale-ModifyPerformanceCommission-Handler"
                                  label-position="right"
                                  size="mini"
                                  label-width="150px"
                                >
                                  <el-form-item label="卡抵扣比例提成">
                                    <span slot="label">
                                      卡抵扣比例提成
                                      <el-popover placement="top-start" width="200" trigger="hover">
                                        <p>卡抵扣比例提成 = 卡抵扣业绩 x 卡抵扣比例</p>

                                        <p v-if="employee.CardRate != null">卡抵扣比例参考值：{{ employee.CardRate | toFixed | NumFormat }}%</p>
                                        <p v-else>卡抵扣比例参考值：无</p>
                                        <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                      </el-popover>
                                    </span>
                                    <el-input type="number" v-input-fixed class="input_type" style="width: 100px" v-model="employee.CardRateCommission">
                                      <template slot="append">元</template>
                                    </el-input>
                                  </el-form-item>
                                </el-form>
                              </el-col>
                              <el-col :span="8">
                                <el-form
                                  @submit.native.prevent
                                  class="sale-ModifyPerformanceCommission-Handler"
                                  label-position="right"
                                  size="mini"
                                  label-width="150px"
                                >
                                  <el-form-item label="卡抵扣固定提成">
                                    <span slot="label">
                                      卡抵扣固定提成
                                      <el-popover placement="top-start" width="200" trigger="hover">
                                        <p>卡抵扣固定提成 = （卡抵扣业绩 ÷ 消耗金额）x 卡抵扣固定提成参考值 x 数量</p>

                                        <p v-if="employee.CardFixed">
                                          卡抵扣固定参考值：¥
                                          {{ employee.CardFixed | toFixed | NumFormat }}
                                        </p>
                                        <p v-else>卡抵扣固定参考值：无</p>
                                        <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                      </el-popover>
                                    </span>
                                    <el-input type="number" v-input-fixed class="input_type" style="width: 100px" v-model="employee.CardFixedCommission">
                                      <template slot="append">元</template>
                                    </el-input>
                                  </el-form-item>
                                </el-form>
                              </el-col>
                            </el-row>
                            <!-- 卡赠金 -->
                            <el-row v-if="chlidItem.LargessCardDeductionAmount > 0" class="padtp_10 padrt_10" :class="index != 0 ? 'border_top' : ''">
                              <el-col :span="8">
                                <el-form
                                  @submit.native.prevent
                                  class="sale-ModifyPerformanceCommission-Handler"
                                  label-position="right"
                                  size="mini"
                                  label-width="120px"
                                >
                                  <el-form-item label="赠送卡扣业绩">
                                    <span slot="label">
                                      赠送卡扣业绩
                                      <el-popover placement="top-start" width="200" trigger="hover">
                                        <p>赠送卡抵扣业绩 = 赠送卡抵扣金额 x 赠送卡抵扣业绩占比 x 业绩占比</p>
                                        <p>
                                          员工赠送卡抵扣业绩占比参考值：
                                          <span v-if="employee.PerformanceCardLargessRate != null"
                                            >{{ (employee.PerformanceCardLargessRate * 100) | toFixed | NumFormat }}%</span
                                          >
                                          <span v-else>无</span>
                                        </p>

                                        <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                      </el-popover>
                                    </span>
                                    <el-input type="number" v-input-fixed class="input_type" style="width: 120px" v-model="employee.CardLargessPerformance">
                                      <template slot="append">元</template>
                                    </el-input>
                                  </el-form-item>
                                </el-form>
                              </el-col>
                              <el-col :span="8">
                                <el-form
                                  @submit.native.prevent
                                  class="sale-ModifyPerformanceCommission-Handler"
                                  label-position="right"
                                  size="mini"
                                  label-width="150px"
                                >
                                  <el-form-item label="赠送卡扣比例提成">
                                    <span slot="label">
                                      赠送卡扣比例提成
                                      <el-popover placement="top-start" width="200" trigger="hover">
                                        <p>赠送卡扣比例提成 = 赠送卡扣业绩 x 赠送卡扣比例</p>

                                        <p v-if="employee.CardLargessRate != null">赠送卡扣比例参考值：{{ employee.CardLargessRate | toFixed | NumFormat }}%</p>
                                        <p v-else>赠送卡扣比例参考值：无</p>
                                        <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                      </el-popover>
                                    </span>
                                    <el-input type="number" v-input-fixed class="input_type" style="width: 100px" v-model="employee.CardLargessRateCommission">
                                      <template slot="append">元</template>
                                    </el-input>
                                  </el-form-item>
                                </el-form>
                              </el-col>
                              <el-col :span="8">
                                <el-form
                                  @submit.native.prevent
                                  class="sale-ModifyPerformanceCommission-Handler"
                                  label-position="right"
                                  size="mini"
                                  label-width="150px"
                                >
                                  <el-form-item label="赠送卡扣固定提成">
                                    <span slot="label">
                                      赠送卡扣固定提成
                                      <el-popover placement="top-start" width="200" trigger="hover">
                                        <p>赠送卡扣固定提成 = （赠送卡扣业绩 ÷ 消耗金额）x 赠送卡扣固定参考值 x 数量</p>

                                        <p v-if="employee.CardLargessFixed">
                                          赠送卡扣固定参考值：¥
                                          {{ employee.CardLargessFixed | toFixed | NumFormat }}
                                        </p>
                                        <p v-else>赠送卡扣金固定参考值：无</p>
                                        <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                      </el-popover>
                                    </span>
                                    <el-input type="number" v-input-fixed class="input_type" style="width: 100px" v-model="employee.CardLargessFixedCommission">
                                      <template slot="append">元</template>
                                    </el-input>
                                  </el-form-item>
                                </el-form>
                              </el-col>
                            </el-row>
                            <!-- 赠送 -->
                            <el-row v-if="chlidItem.LargessAmount > 0" class="padtp_10 padrt_10" :class="index != 0 ? 'border_top' : ''">
                              <el-col :span="8">
                                <el-form
                                  @submit.native.prevent
                                  class="sale-ModifyPerformanceCommission-Handler"
                                  label-position="right"
                                  size="mini"
                                  label-width="120px"
                                >
                                  <el-form-item label="赠送业绩">
                                    <span slot="label">
                                      赠送业绩
                                      <el-popover placement="top-start" width="200" trigger="hover">
                                        <p>赠送业绩 = 赠送金额 x 赠送业绩占比 x 业绩占比</p>
                                        <p>
                                          员工赠送业绩占比参考值：
                                          <span v-if="employee.PerformanceLargessRate != null"
                                            >{{ (employee.PerformanceLargessRate * 100) | toFixed | NumFormat }}%</span
                                          >
                                          <span v-else>无</span>
                                        </p>

                                        <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                      </el-popover>
                                    </span>
                                    <el-input type="number" v-input-fixed class="input_type" style="width: 120px" v-model="employee.LargessPerformance">
                                      <template slot="append">元</template>
                                    </el-input>
                                  </el-form-item>
                                </el-form>
                              </el-col>
                              <el-col :span="8">
                                <el-form
                                  @submit.native.prevent
                                  class="sale-ModifyPerformanceCommission-Handler"
                                  label-position="right"
                                  size="mini"
                                  label-width="150px"
                                >
                                  <el-form-item label="赠送比例提成">
                                    <span slot="label">
                                      赠送比例提成
                                      <el-popover placement="top-start" width="200" trigger="hover">
                                        <p>赠送比例提成 = 赠送业绩 x 赠送比例</p>
                                        <p v-if="employee.LargessRate != null">赠送比例参考值：{{ employee.LargessRate | toFixed | NumFormat }}%</p>
                                        <p v-else>赠送比例参考值：无</p>
                                        <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                      </el-popover>
                                    </span>
                                    <el-input type="number" v-input-fixed class="input_type" style="width: 100px" v-model="employee.LargessRateCommission">
                                      <template slot="append">元</template>
                                    </el-input>
                                  </el-form-item>
                                </el-form>
                              </el-col>
                              <el-col :span="8">
                                <el-form
                                  @submit.native.prevent
                                  class="sale-ModifyPerformanceCommission-Handler"
                                  label-position="right"
                                  size="mini"
                                  label-width="150px"
                                >
                                  <el-form-item label="赠送固定提成">
                                    <span slot="label">
                                      赠送固定提成
                                      <el-popover placement="top-start" width="200" trigger="hover">
                                        <p>赠送固定提成 = （赠送业绩 ÷ 消耗金额）x 赠送固定 x 数量</p>
                                        <p v-if="employee.LargessFixed">
                                          赠送固定参考值：¥
                                          {{ employee.LargessFixed | toFixed | NumFormat }}
                                        </p>
                                        <p v-else>赠送固定提成参考值：无</p>
                                        <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                      </el-popover>
                                    </span>
                                    <el-input type="number" v-input-fixed class="input_type" style="width: 100px" v-model="employee.LargessFixedCommission">
                                      <template slot="append">元</template>
                                    </el-input>
                                  </el-form-item>
                                </el-form>
                              </el-col>
                            </el-row>
                          </el-col>

                          <el-col v-if="!item.IsLargess" :span="4" class="padtp_10" :class="index != 0 ? 'border_top' : ''">
                            <el-form
                              @submit.native.prevent
                              class="sale-ModifyPerformanceCommission-Handler"
                              label-position="right"
                              size="mini"
                              label-width="90px"
                            >
                              <el-form-item label="无业绩奖励">
                                <span slot="label">
                                  无业绩奖励
                                  <el-popover placement="top-start" width="200" trigger="hover">
                                    <p>无业绩奖励 = 无业绩奖励参考值 x 数量 x 经手人比例</p>
                                    <p v-if="employee.SpecialBenefit != null">
                                      无业绩奖励参考值：¥
                                      {{ employee.SpecialBenefit | toFixed | NumFormat }}
                                    </p>
                                    <p v-else>无业绩奖励参考值：无</p>
                                    <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"> </el-button>
                                  </el-popover>
                                </span>
                                <el-input type="number" v-input-fixed class="input_type" style="width: 100px" v-model="employee.SpecialBenefitCommission">
                                  <template slot="append">元</template>
                                </el-input>
                              </el-form-item>
                            </el-form>
                          </el-col>
                        </el-row>
                      </el-col>
                    </el-row>
                  </el-row>
                </el-row>
              </div>

              <!--产品-->
              <div v-if="item.Product.length > 0">
                <el-row v-for="(middItem, middIndex) in item.Product" :key="middIndex">
                  <el-row class="border_right border_left border_bottom pad_10">
                    <el-col :span="6">
                      <div>
                        {{ middItem.ProductName }}
                        <span v-if="middItem.Alias">({{ middItem.Alias }})</span>
                        <el-tag v-if="middItem.IsLargess" size="mini" class="marlt_5" type="danger">赠</el-tag>
                        <el-tag class="marlt_5" size="mini">产品</el-tag>
                      </div>
                      <div class="color_red font_12 martp_5">¥ {{ middItem.Price | toFixed | NumFormat }}</div>
                    </el-col>
                    <el-col :span="4">x {{ middItem.Quantity }}</el-col>
                    <el-col :span="6" v-show="treatInfo.BillType == '10'">
                      <div>
                        <span v-if="middItem.CardPreferentialAmount < 0">+ ¥ {{ Math.abs(middItem.CardPreferentialAmount) | toFixed | NumFormat }}</span>
                        <span v-else-if="middItem.CardPreferentialAmount > 0">- ¥ {{ Math.abs(middItem.CardPreferentialAmount) | toFixed | NumFormat }}</span>
                        <span v-else>¥ 0.00</span>
                      </div>
                    </el-col>
                    <el-col :span="8">
                      <div>¥ {{ middItem.TotalAmount | toFixed | NumFormat }}</div>
                      <div class="martp_5 font_12">
                        <span class="color_green" v-if="middItem.PayAmount > 0">现金金额： {{ middItem.PayAmount | toFixed | NumFormat }}</span>
                        <span class="color_green" v-if="middItem.CardDeductionAmount > 0" :class="middItem.PayAmount != 0 ? 'marlt_15' : ''"
                          >卡抵扣金额： {{ middItem.CardDeductionAmount | toFixed | NumFormat }}</span
                        >

                        <span class="color_red" v-if="middItem.LargessCardDeductionAmount > 0" :class="middItem.CardDeductionAmount != 0 ? 'marlt_15' : ''"
                          >赠送卡抵扣金额： {{ middItem.LargessCardDeductionAmount | toFixed | NumFormat }}</span
                        >

                        <span class="color_red" v-if="middItem.LargessAmount > 0" :class="middItem.LargessCardDeductionAmount != 0 ? 'marlt_15' : ''"
                          >赠送金额：¥ {{ middItem.LargessAmount | toFixed | NumFormat }}</span
                        >
                      </div>
                    </el-col>
                  </el-row>

                  <el-row
                    class="padlt_10 padrt_10 border_right border_left border_bottom font_12"
                    v-for="(handler, index) in middItem.TreatBillHandler"
                    :key="index"
                  >
                    <el-col :span="2" class="padtp_10 padbm_10">
                      <el-link class="font_12 line_26" type="primary" :underline="false" @click="employeeHandleClick(6, middItem, '产品')">{{
                        handler.TreatHandlerName
                      }}</el-link>
                    </el-col>
                    <el-col :span="22" class="border_left">
                      <el-row v-for="(employee, index) in handler.Employee" :key="employee.EmployeeID">
                        <el-col :span="5" class="padtp_10 padrt_10 " :class="index != 0 ? 'border_top' : ''">
                          <el-form
                            @submit.native.prevent
                            class="sale-ModifyPerformanceCommission-Handler"
                            label-position="right"
                            size="mini"
                            label-width="70px"
                            :inline="true"
                          >
                            <el-form-item :label="`${employee.EmployeeName}`">
                              <el-input
                                type="number"
                                v-input-fixed
                                class="input_type"
                                style="width: 90px"
                                v-model="employee.Scale"
                                @input="changeSaleHandlerRate(middItem, employee)"
                              >
                                <template slot="append">%</template>
                              </el-input>
                            </el-form-item>
                            <el-form-item class="cursorclass">
                              <el-checkbox size="small" v-model="employee.IsCalculatePassengerFlow">客流</el-checkbox>
                            </el-form-item>
                          </el-form>
                        </el-col>

                        <el-col :span="15" class=" border_right border_left">
                          <!-- 现金 -->
                          <el-row v-if="middItem.PayAmount > 0" class="padtp_10 padrt_10" :class="index != 0 ? 'border_top' : ''">
                            <el-col :span="8">
                              <el-form
                                @submit.native.prevent
                                class="sale-ModifyPerformanceCommission-Handler"
                                label-position="right"
                                size="mini"
                                label-width="120px"
                              >
                                <el-form-item label="现金业绩">
                                  <span slot="label">
                                    现金业绩
                                    <el-popover placement="top-start" width="200" trigger="hover">
                                      <p>现金业绩 = 现金金额 x 员工现金业绩占比 x 业绩占比</p>
                                      <p>
                                        员工现金业绩占比参考值：
                                        <span v-if="employee.PerformancePayRate != null">{{ (employee.PerformancePayRate * 100) | toFixed | NumFormat }}%</span>
                                        <span v-else>无</span>
                                      </p>

                                      <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                    </el-popover>
                                  </span>
                                  <el-input type="number" v-input-fixed class="input_type" style="width: 120px" v-model="employee.PayPerformance">
                                    <template slot="append">元</template>
                                  </el-input>
                                </el-form-item>
                              </el-form>
                            </el-col>
                            <el-col :span="8">
                              <el-form
                                @submit.native.prevent
                                class="sale-ModifyPerformanceCommission-Handler"
                                label-position="right"
                                size="mini"
                                label-width="150px"
                              >
                                <el-form-item label="现金比例提成">
                                  <span slot="label">
                                    现金比例提成
                                    <el-popover placement="top-start" width="200" trigger="hover">
                                      <p>现金比例提成 = 现金业绩 x 现金比例提成参考值</p>

                                      <p v-if="employee.PayRate != null">现金比例提成参考值：{{ employee.PayRate | toFixed | NumFormat }}%</p>
                                      <p v-else>现金比例提成参考值：无</p>
                                      <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                    </el-popover>
                                  </span>
                                  <el-input type="number" v-input-fixed class="input_type" style="width: 100px" v-model="employee.PayRateCommission">
                                    <template slot="append">元</template>
                                  </el-input>
                                </el-form-item>
                              </el-form>
                            </el-col>
                            <el-col :span="8">
                              <el-form
                                @submit.native.prevent
                                class="sale-ModifyPerformanceCommission-Handler"
                                label-position="right"
                                size="mini"
                                label-width="150px"
                              >
                                <el-form-item label="现金固定提成">
                                  <span slot="label">
                                    现金固定提成
                                    <el-popover placement="top-start" width="200" trigger="hover">
                                      <p>现金固定提成 = （现金业绩 ÷ 消耗金额）x 现金固定提成参考值 x 数量</p>

                                      <p v-if="employee.PayFixed">
                                        现金固定提成参考值：¥
                                        {{ employee.PayFixed | toFixed | NumFormat }}
                                      </p>
                                      <p v-else>现金固定提成参考值：无</p>
                                      <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                    </el-popover>
                                  </span>
                                  <el-input type="number" v-input-fixed class="input_type" style="width: 100px" v-model="employee.PayFixedCommission">
                                    <template slot="append">元</template>
                                  </el-input>
                                </el-form-item>
                              </el-form>
                            </el-col>
                          </el-row>
                          <!-- 卡本金 -->
                          <el-row v-if="middItem.CardDeductionAmount > 0" class="padtp_10 padrt_10" :class="index != 0 ? 'border_top' : ''">
                            <el-col :span="8">
                              <el-form
                                @submit.native.prevent
                                class="sale-ModifyPerformanceCommission-Handler"
                                label-position="right"
                                size="mini"
                                label-width="120px"
                              >
                                <el-form-item label="卡抵扣业绩">
                                  <span slot="label">
                                    卡抵扣业绩
                                    <el-popover placement="top-start" width="200" trigger="hover">
                                      <p>卡抵扣业绩 = 卡抵扣金额 x 员工卡抵扣业绩占比 x 业绩占比</p>
                                      <p>
                                        员工卡抵扣业绩占比参考值：
                                        <span v-if="employee.PerformanceCardRate != null"
                                          >{{ (employee.PerformanceCardRate * 100) | toFixed | NumFormat }}%</span
                                        >
                                        <span v-else>无</span>
                                      </p>

                                      <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                    </el-popover>
                                  </span>
                                  <el-input type="number" v-input-fixed class="input_type" style="width: 120px" v-model="employee.CardPerformance">
                                    <template slot="append">元</template>
                                  </el-input>
                                </el-form-item>
                              </el-form>
                            </el-col>
                            <el-col :span="8">
                              <el-form
                                @submit.native.prevent
                                class="sale-ModifyPerformanceCommission-Handler"
                                label-position="right"
                                size="mini"
                                label-width="150px"
                              >
                                <el-form-item label="卡抵扣比例提成">
                                  <span slot="label">
                                    卡抵扣比例提成
                                    <el-popover placement="top-start" width="200" trigger="hover">
                                      <p>卡抵扣比例提成 = 卡抵扣业绩 x 卡抵扣比例</p>

                                      <p v-if="employee.CardRate != null">卡抵扣比例参考值：{{ employee.CardRate | toFixed | NumFormat }}%</p>
                                      <p v-else>卡抵扣比例参考值：无</p>
                                      <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                    </el-popover>
                                  </span>
                                  <el-input type="number" v-input-fixed class="input_type" style="width: 100px" v-model="employee.CardRateCommission">
                                    <template slot="append">元</template>
                                  </el-input>
                                </el-form-item>
                              </el-form>
                            </el-col>
                            <el-col :span="8">
                              <el-form
                                @submit.native.prevent
                                class="sale-ModifyPerformanceCommission-Handler"
                                label-position="right"
                                size="mini"
                                label-width="150px"
                              >
                                <el-form-item label="卡抵扣固定提成">
                                  <span slot="label">
                                    卡抵扣固定提成
                                    <el-popover placement="top-start" width="200" trigger="hover">
                                      <p>卡抵扣固定提成 = （卡抵扣业绩 ÷ 消耗金额）x 卡抵扣固定提成参考值 x 数量</p>

                                      <p v-if="employee.CardFixed">
                                        卡抵扣固定参考值：¥
                                        {{ employee.CardFixed | toFixed | NumFormat }}
                                      </p>
                                      <p v-else>卡抵扣固定参考值：无</p>
                                      <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                    </el-popover>
                                  </span>
                                  <el-input type="number" v-input-fixed class="input_type" style="width: 100px" v-model="employee.CardFixedCommission">
                                    <template slot="append">元</template>
                                  </el-input>
                                </el-form-item>
                              </el-form>
                            </el-col>
                          </el-row>
                          <!-- 卡赠金 -->
                          <el-row v-if="middItem.LargessCardDeductionAmount > 0" class="padtp_10 padrt_10" :class="index != 0 ? 'border_top' : ''">
                            <el-col :span="8">
                              <el-form
                                @submit.native.prevent
                                class="sale-ModifyPerformanceCommission-Handler"
                                label-position="right"
                                size="mini"
                                label-width="120px"
                              >
                                <el-form-item label="赠送卡扣业绩">
                                  <span slot="label">
                                    赠送卡扣业绩
                                    <el-popover placement="top-start" width="200" trigger="hover">
                                      <p>赠送卡抵扣业绩 = 赠送卡抵扣金额 x 赠送卡抵扣业绩占比 x 业绩占比</p>
                                      <p>
                                        员工赠送卡抵扣业绩占比参考值：
                                        <span v-if="employee.PerformanceCardLargessRate != null"
                                          >{{ (employee.PerformanceCardLargessRate * 100) | toFixed | NumFormat }}%</span
                                        >
                                        <span v-else>无</span>
                                      </p>

                                      <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                    </el-popover>
                                  </span>
                                  <el-input type="number" v-input-fixed class="input_type" style="width: 120px" v-model="employee.CardLargessPerformance">
                                    <template slot="append">元</template>
                                  </el-input>
                                </el-form-item>
                              </el-form>
                            </el-col>
                            <el-col :span="8">
                              <el-form
                                @submit.native.prevent
                                class="sale-ModifyPerformanceCommission-Handler"
                                label-position="right"
                                size="mini"
                                label-width="150px"
                              >
                                <el-form-item label="赠送卡扣比例提成">
                                  <span slot="label">
                                    赠送卡扣比例提成
                                    <el-popover placement="top-start" width="200" trigger="hover">
                                      <p>赠送卡扣比例提成 = 赠送卡扣业绩 x 赠送卡扣比例</p>

                                      <p v-if="employee.CardLargessRate != null">赠送卡扣比例参考值：{{ employee.CardLargessRate | toFixed | NumFormat }}%</p>
                                      <p v-else>赠送卡扣比例参考值：无</p>
                                      <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                    </el-popover>
                                  </span>
                                  <el-input type="number" v-input-fixed class="input_type" style="width: 100px" v-model="employee.CardLargessRateCommission">
                                    <template slot="append">元</template>
                                  </el-input>
                                </el-form-item>
                              </el-form>
                            </el-col>
                            <el-col :span="8">
                              <el-form
                                @submit.native.prevent
                                class="sale-ModifyPerformanceCommission-Handler"
                                label-position="right"
                                size="mini"
                                label-width="150px"
                              >
                                <el-form-item label="赠送卡扣固定提成">
                                  <span slot="label">
                                    赠送卡扣固定提成
                                    <el-popover placement="top-start" width="200" trigger="hover">
                                      <p>赠送卡扣固定提成 = （赠送卡扣业绩 ÷ 消耗金额）x 赠送卡扣固定参考值 x 数量</p>

                                      <p v-if="employee.CardLargessFixed">
                                        赠送卡扣固定参考值：¥
                                        {{ employee.CardLargessFixed | toFixed | NumFormat }}
                                      </p>
                                      <p v-else>赠送卡扣金固定参考值：无</p>
                                      <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                    </el-popover>
                                  </span>
                                  <el-input type="number" v-input-fixed class="input_type" style="width: 100px" v-model="employee.CardLargessFixedCommission">
                                    <template slot="append">元</template>
                                  </el-input>
                                </el-form-item>
                              </el-form>
                            </el-col>
                          </el-row>
                          <!-- 赠送 -->
                          <el-row v-if="middItem.LargessAmount > 0" class="padtp_10 padrt_10" :class="index != 0 ? 'border_top' : ''">
                            <el-col :span="8">
                              <el-form
                                @submit.native.prevent
                                class="sale-ModifyPerformanceCommission-Handler"
                                label-position="right"
                                size="mini"
                                label-width="120px"
                              >
                                <el-form-item label="赠送业绩">
                                  <span slot="label">
                                    赠送业绩
                                    <el-popover placement="top-start" width="200" trigger="hover">
                                      <p>赠送业绩 = 赠送金额 x 赠送业绩占比 x 业绩占比</p>
                                      <p>
                                        员工赠送业绩占比参考值：
                                        <span v-if="employee.PerformanceLargessRate != null"
                                          >{{ (employee.PerformanceLargessRate * 100) | toFixed | NumFormat }}%</span
                                        >
                                        <span v-else>无</span>
                                      </p>

                                      <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                    </el-popover>
                                  </span>
                                  <el-input type="number" v-input-fixed class="input_type" style="width: 120px" v-model="employee.LargessPerformance">
                                    <template slot="append">元</template>
                                  </el-input>
                                </el-form-item>
                              </el-form>
                            </el-col>
                            <el-col :span="8">
                              <el-form
                                @submit.native.prevent
                                class="sale-ModifyPerformanceCommission-Handler"
                                label-position="right"
                                size="mini"
                                label-width="150px"
                              >
                                <el-form-item label="赠送比例提成">
                                  <span slot="label">
                                    赠送比例提成
                                    <el-popover placement="top-start" width="200" trigger="hover">
                                      <p>赠送比例提成 = 赠送业绩 x 赠送比例</p>
                                      <p v-if="employee.LargessRate != null">赠送比例参考值：{{ employee.LargessRate | toFixed | NumFormat }}%</p>
                                      <p v-else>赠送比例参考值：无</p>
                                      <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                    </el-popover>
                                  </span>
                                  <el-input type="number" v-input-fixed class="input_type" style="width: 100px" v-model="employee.LargessRateCommission">
                                    <template slot="append">元</template>
                                  </el-input>
                                </el-form-item>
                              </el-form>
                            </el-col>
                            <el-col :span="8">
                              <el-form
                                @submit.native.prevent
                                class="sale-ModifyPerformanceCommission-Handler"
                                label-position="right"
                                size="mini"
                                label-width="150px"
                              >
                                <el-form-item label="赠送固定提成">
                                  <span slot="label">
                                    赠送固定提成
                                    <el-popover placement="top-start" width="200" trigger="hover">
                                      <p>赠送固定提成 = （赠送业绩 ÷ 消耗金额）x 赠送固定 x 数量</p>
                                      <p v-if="employee.LargessFixed">
                                        赠送固定参考值：¥
                                        {{ employee.LargessFixed | toFixed | NumFormat }}
                                      </p>
                                      <p v-else>赠送固定提成参考值：无</p>
                                      <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                                    </el-popover>
                                  </span>
                                  <el-input type="number" v-input-fixed class="input_type" style="width: 100px" v-model="employee.LargessFixedCommission">
                                    <template slot="append">元</template>
                                  </el-input>
                                </el-form-item>
                              </el-form>
                            </el-col>
                          </el-row>
                        </el-col>

                        <el-col v-if="!item.IsLargess" :span="4" class="padtp_10" :class="index != 0 ? 'border_top' : ''">
                          <el-form
                            @submit.native.prevent
                            class="sale-ModifyPerformanceCommission-Handler"
                            label-position="right"
                            size="mini"
                            label-width="90px"
                          >
                            <el-form-item label="无业绩奖励">
                              <span slot="label">
                                无业绩奖励
                                <el-popover placement="top-start" width="200" trigger="hover">
                                  <p>无业绩奖励 = 无业绩奖励参考值 x 数量 x 经手人比例</p>
                                  <p v-if="employee.SpecialBenefit != null">
                                    无业绩奖励参考值：¥
                                    {{ employee.SpecialBenefit | toFixed | NumFormat }}
                                  </p>
                                  <p v-else>无业绩奖励参考值：无</p>
                                  <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"> </el-button>
                                </el-popover>
                              </span>
                              <el-input type="number" v-input-fixed class="input_type" style="width: 100px" v-model="employee.SpecialBenefitCommission">
                                <template slot="append">元</template>
                              </el-input>
                            </el-form-item>
                          </el-form>
                        </el-col>
                      </el-row>
                    </el-col>
                  </el-row>
                </el-row>
              </div>
            </div>
          </div>
        </el-scrollbar>
      </div>

      <span slot="footer" class="dialog-footer">
        <el-button @click="visible_ = false" v-prevent-click size="small">取消</el-button>
        <el-button type="primary" @click="saveSaleBill" :loading="loading" v-prevent-click size="small">保存 </el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: "orderModifyConsumeHandledPerformance",

  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    orderDetail: {
      type: Object,
      default() {
        return {
          Project: [],
          Product: [],
          PackageCard: [],
          GeneralCard: [],
          SavingCard: [],
          TimeCard: [],
        };
      },
    },
    loading: {
      type: Boolean,
      default: false,
    },
    treatInfo: {
      type: Object,
    },
  },
  /** 监听数据变化   */
  watch: {
    visible: {
      immediate: true,
      handler(val) {
        this.visible_ = val;
      },
    },
  },
  /**  引入的组件  */
  components: {},
  /**  Vue 实例的数据对象**/
  data() {
    return {
      visible_: false,
      changeLoading: false,
      saveTreatDialog: false,
    };
  },
  /**计算属性  */
  computed: {},
  /**  方法集合  */
  methods: {
    /**    */
    changeIsCalculatePassengerFlowClick(emp) {
      emp.IsCalculatePassengerFlow = !emp.IsCalculatePassengerFlow;
    },
    /**    */
    closeModifyPerformance() {
      let that = this;
      that.$emit("update:visible", false);
    },
    /*  */
    saveSaleBill() {
      let that = this;
      that.$emit("saveSaleBill");
    },
    employeeHandleClick(type, item, type_name) {
      let that = this;
      that.$emit("employeeHandleClick", type, item, type_name);
    },
    changeSaleHandlerRate(item, employee) {
      let that = this;
      that.$emit("changeSaleHandlerRate", item, employee);
    },
  },
  /** 创建实例之前执行函数   */
  beforeCreate() {},
  /**  实例创建完成之后  */
  created() {},
  /**  在挂载开始之前被调用  */
  beforeMount() {},
  /**  实例被挂载后调用  */
  mounted() {},
  /**  数据更新 之前 调用  */
  beforeUpdate() {},
  /** 数据更新 完成 调用   */
  updated() {},
  /**  实例销毁之前调用  */
  beforeDestroy() {},
  /**  实例销毁后调用  */
  destroyed() {},
};
</script>

<style lang="scss">
.orderModifyConsumeHandledPerformance {
  .cursorclass{
    cursor:pointer;
  }
}
</style>
