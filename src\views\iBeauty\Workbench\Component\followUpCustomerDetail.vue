<template>
  <div class="workbenchCustomerDetail">
    <el-drawer v-drawerDrag:changeMinWidth="changeMinWidth" :visible.sync="show" @close="closeDialog" :wrapperClosable="false" custom-class="custom-class-drawer" :size="drawerSize" :show-close="false">
      <div slot="title" class="dis_flex flex_x_between">
        <div></div>
        <div class="text_right" style="width: 100px">
          <i @click="changeDrawerSize" class="el-icon-rank font_24 marrt_15"></i>
          <i @click="closeDrawerClick" class="el-icon-close font_24"></i>
        </div>
      </div>
      <el-tabs class="custom-tabs-class" v-model="tabPane" @tab-click="handleClick">
        <el-tab-pane label="基本档案" name="0">
          <workbench-customer-basic-files v-if="customerID" :customerID="customerID" :isCustomerPhoneNumberView="isCustomerPhoneNumberView" :isCustomerPhoneNumberModify="isCustomerPhoneNumberModify" :isCustomerBasicInformationModify="isCustomerBasicInformationModify" :isCustomerServicerModify="isCustomerServicerModify" :isCustomerBasicFileModify="isCustomerBasicFileModify" ref="customerbasicfiles" @customerInfoUpdated="handleCustomerInfoUpdated"></workbench-customer-basic-files>
        </el-tab-pane>
        <el-tab-pane label="跟进记录" name="1">
          <workbench-followUpRecord
            v-if="customerID"
            :customerID="customerID"
            :customerName="customerName"
            :isCallBack="isCallBack"
            :hasAppointment="hasAppointment"
            :appointmentStatus="appointmentStatus"
            :appointmentID="appointmentID"
            :leadStatus="leadStatus"
            :leadID="leadID"
            :layoutType="followUpLayoutType"
            ref="followUpRecord"
            @openAppointment="handleOpenAppointment"
            @cancelAppointment="handleCancelAppointment"
            @openFollowUp="handleOpenFollowUp">
          </workbench-followUpRecord>
        </el-tab-pane>
        <!-- <el-tab-pane label="企微会话" name="8">
          <workbenchDialogue v-if="customerID" :customerID="customerID" ref="workbenchDialogue"></workbenchDialogue>
        </el-tab-pane> -->
         <!-- <el-tab-pane label="卡项信息" name="2">
          <workbench-customer-account v-if="customerID" :customerID="customerID" ref="customerAccount"></workbench-customer-account>
        </el-tab-pane> -->
        <el-tab-pane label="订单信息" name="3">
          <workbench-customer-bill v-if="customerID" :customerID="String(customerID)" ref="customerBill"></workbench-customer-bill>
        </el-tab-pane>
        <el-tab-pane label="预约记录" name="4">
          <workbench-appointment-record
            v-if="customerID"
            :customerID="customerID"
            :customerName="customerName"
            ref="customerAppointmentRecord"
            @openAppointment="handleOpenAppointment"
            @appointmentCancelled="handleAppointmentCancelled">
          </workbench-appointment-record>
        </el-tab-pane>
        <!-- <el-tab-pane label="服务日志" name="5">
          <workbench-customer-nursing-log v-if="customerID" :customerID="customerID" ref="customerNursingLog"></workbench-customer-nursing-log>
        </el-tab-pane> -->
        <!-- <el-tab-pane label="文件档案" name="6">
          <workbench-customer-file-upload v-if="customerID" :customerID="customerID" :isDeleteFile="isDeleteFile" ref="coustomerFileUpload"></workbench-customer-file-upload>
        </el-tab-pane> -->
        <!-- <el-tab-pane label="历史病历" name="8">
          <workbench-medical-record v-if="customerID && tabPane == 8" :CustomerID="customerID" :CustomerName="customerName" :CustomerCases="CustomerCases" ref="workbenchMedicalRecord"></workbench-medical-record>
        </el-tab-pane>
        <el-tab-pane label="客户处方" name="9">
          <workbench-prescription v-if="customerID" :CustomerID="customerID" :CustomerName="customerName" :CustomerCases="CustomerCases" ref="workbenchOrescriptionRef"></workbench-prescription>
        </el-tab-pane> -->

        <!-- <el-tab-pane label="对比照" name="10">
          <workbench-effect-comparison v-if="customerID" :CustomerID="customerID" :CustomerName="customerName" :CustomerCases="CustomerCases" ref="effectComparison"></workbench-effect-comparison>
        </el-tab-pane> -->

        <!-- <el-tab-pane label="皮肤数据预览" name="11" style="height: 100%"  v-if="yjEntity">
            <iframe :src="'https://yanjia.xiaofutech.com/access_detail?scope=OPEN:basic&channel_id=' + yjEntity.YJInstitutionID + '&store_id=' + yjEntity.YJEntityID + '&openid=' + customerID" frameborder="0" style="width: 100%; height: 500px" id="pIframe"></iframe>
        </el-tab-pane> -->
        <el-tab-pane label="通话记录" name="14">
          <workbenchCallBackLog :CustomerID="customerID" ref="customerCallBackLogRef"></workbenchCallBackLog>
        </el-tab-pane>
      </el-tabs>
    </el-drawer>
  </div>
</template>

<script>
import workbenchFollowUpRecord from "@/views/iBeauty/Workbench/Component/workbenchFollowUpRecord";
import workbenchAppointmentRecord from "@/views/iBeauty/Workbench/Component/workbenchAppointmentRecord";
import workbenchCustomerFileUpload from "@/views/iBeauty/Workbench/Component/workbenchCustomerFileUpload";
import workbenchCustomerAccount from "@/views/iBeauty/Workbench/Component/workbenchCustomerAccount";
import workbenchCustomerBasicFiles from "@/views/iBeauty/Workbench/Component/workbenchCustomerBasicFiles.vue";
import workbenchCustomerBill from "@/views/iBeauty/Workbench/Component/clueFollowUpCustomerBill";
import workbenchCustomerNursingLog from "@/views/iBeauty/Workbench/Component/workbenchCustomerNursingLog";
import workbenchMedicalRecord from "@/views/iBeauty/Workbench/Component/workbenchMedicalRecord";
import workbenchPrescription from "@/views/iBeauty/Workbench/Component/workbenchPrescription";
import workbenchEffectComparison from "@/views/iBeauty/Workbench/Component/workbenchEffectComparison";
import workbenchCallBackLog from "@/views/iBeauty/Workbench/Component/workbenchCallBackLog.vue";
import cusAPI from "@/api/CRM/Customer/customer";

export default {
  name: "workbenchCustomerDetail",
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    customerID: {
      type: Number,
      require: true,
    },
    customerName: {
      type: String,
      require: true,
    },
    isCallBack: {
      type: Boolean,
    },
    isCustomerPhoneNumberView: {
      type: Boolean,
      default: false,
    },
    isCustomerPhoneNumberModify: {
      type: Boolean,
      default: false,
    },
    isCustomerBasicInformationModify: {
      type: Boolean,
      default: false,
    },
    isCustomerServicerModify: {
      type: Boolean,
      default: false,
    },
    isCustomerBasicFileModify: {
      type: Boolean,
      default: false,
    },
    CustomerCases: {
      Type: Boolean,
      default() {
        return {
          Add: false,
          Update: false,
          Delete: false,
          SelectStencil: false,
          SaveStencil: false,
          DeleteStencil: false,
          PrescriptionAdd: false,
          PrescriptionUpdate: false,
        };
      },
    },
    // 预约相关状态
    hasAppointment: {
      type: Boolean,
      default: false,
    },
    appointmentStatus: {
      type: [Number, String],
      default: null,
    },
    appointmentID: {
      type: String,
      default: null,
    },
    leadStatus: {
      type: [Number, String],
      default: 0,
    },
    leadID: {
      type: [String, Number],
      default: null,
    },
    // 跟进记录布局类型
    followUpLayoutType: {
      type: String,
      default: 'default',
    },
  },
  /** 监听数据变化   */
  watch: {
    visible: {
      immediate: true,
      handler(val) {
        this.show = val;
        this.tabPane = "0";
        this.isFullscreen = false;
        this.drawerSize = "60%";
      },
    },
    customerID: {
      immediate: true,
      handler(val) {
        if (val) {
          this.$nextTick(() => {
            this.handleClick();
          });
        }
      },
    },
  },
  /**  引入的组件  */
  components: {
    workbenchFollowUpRecord,
    workbenchAppointmentRecord,
    workbenchCustomerFileUpload,
    workbenchCustomerAccount,
    workbenchCustomerBasicFiles,
    workbenchCustomerBill,
    workbenchCustomerNursingLog,
    workbenchMedicalRecord,
    workbenchPrescription,
    workbenchEffectComparison,
    workbenchCallBackLog,
  },
  /**  Vue 实例的数据对象**/
  data() {
    return {
      show: false,
      moreShow: true,
      isDeleteFile: false,
      tabPane: "0",
      circleUrl: "https://cube.elemecdn.com/3/7c/********************************.png", //默认头像
      drawerSize: "60%",
      isFullscreen: false,
      // yjEntity: "",
    };
  },
  /**计算属性  */
  computed: {},
  /**  方法集合  */
  methods: {
    /**    */
    changeMinWidth() {
      // let that = this;
      // console.log("==========");
    },
    /**  全品按钮  */
    changeDrawerSize() {
      let that = this;
      that.isFullscreen = !that.isFullscreen;
      if (that.isFullscreen) {
        that.drawerSize = "100%";
      } else {
        that.drawerSize = "60%";
      }
    },
    /**    */
    closeDrawerClick() {
      let that = this;
      that.show = false;
    },
    /**    */
    closeDialog() {
      let that = this;
      that.$emit("update:visible", false);
    },
    showConsumeInfo() {
      this.moreShow = !this.moreShow;
    },
    /* 切换 */
    handleClick() {
      let that = this;
      let tabPane = this.tabPane;
      if (that.customerID) {
        switch (tabPane) {
          case "0":
            {
              if (that.$refs.customerbasicfiles && that.$refs.customerbasicfiles.getCustomerInfoData) {
                that.$refs.customerbasicfiles.getCustomerInfoData();
              }
            }
            break;

          case "1":
            {
              if (that.$refs.followUpRecord && that.$refs.followUpRecord.getCustomerFollowUp) {
                that.$refs.followUpRecord.getCustomerFollowUp();
              }
            }
            break;

          case "2":
            {
              if (that.$refs.customerAccount && that.$refs.customerAccount.handleClick) {
                that.$refs.customerAccount.activeName = "0";
                that.$refs.customerAccount.handleClick();
              }
            }
            break;

          case "3":
            {
              if (that.$refs.customerBill && that.$refs.customerBill.searchSaleBill) {
                that.$refs.customerBill.searchSaleBill();
              }
            }
            break;

          case "4":
            {
              if (that.$refs.customerAppointmentRecord && that.$refs.customerAppointmentRecord.getAppointmentRecordList) {
                that.$refs.customerAppointmentRecord.getAppointmentRecordList();
              }
            }
            break;

          case "5":
            {
              if (that.$refs.customerNursingLog && that.$refs.customerNursingLog.clearListData && that.$refs.customerNursingLog.getNursingLogList) {
                that.$refs.customerNursingLog.clearListData();
                that.$refs.customerNursingLog.getNursingLogList();
              }
            }
            break;

          case "6":
            {
              if (that.$refs.coustomerFileUpload && that.$refs.coustomerFileUpload.claerCoustomerFileUploadData && that.$refs.coustomerFileUpload.getCoustomerFileUploadData) {
                that.$refs.coustomerFileUpload.claerCoustomerFileUploadData();
                that.$refs.coustomerFileUpload.getCoustomerFileUploadData();
              }
            }
            break;

          case "7":
            {
              if (that.$refs.customerElectronicMedicalRecord && that.$refs.customerElectronicMedicalRecord.claerElectronicMedicalRecordData && that.$refs.customerElectronicMedicalRecord.getElectronicMedicalRecordData) {
                that.$refs.customerElectronicMedicalRecord.claerElectronicMedicalRecordData();
                that.$refs.customerElectronicMedicalRecord.getElectronicMedicalRecordData();
              }
            }
            break;
          case "8":
            that.$nextTick(() => {
              if (that.$refs.workbenchMedicalRecord && that.$refs.workbenchMedicalRecord.initWorkbenchMedicakRecordData) {
                that.$refs.workbenchMedicalRecord.initWorkbenchMedicakRecordData();
              }
            });
            break;
          case "9":
            that.$nextTick(() => {
              if (that.$refs.workbenchOrescriptionRef && that.$refs.workbenchOrescriptionRef.initWorkbenchPrescriptionData) {
                that.$refs.workbenchOrescriptionRef.initWorkbenchPrescriptionData();
              }
            });

            break;
          case "10":
            that.$nextTick(() => {
              if (that.$refs.effectComparison && that.$refs.effectComparison.photoCompare && that.$refs.effectComparison.clearData) {
                that.$refs.effectComparison.CustomerID = that.customerID;
                that.$refs.effectComparison.photoCompare();
                that.$refs.effectComparison.clearData();
              }
            });
            break;

          case "14":
            if (that.$refs.customerCallBackLogRef && that.$refs.customerCallBackLogRef.phoneCallBack_callBackLog) {
              that.$refs.customerCallBackLogRef.phoneCallBack_callBackLog();
            }
            break;
        }
      }
    },    /**    */
    // customer_yjEntity() {
    //   let that = this;
    //   let params = {};
    //   cusAPI
    //     .customer_yjEntity(params)
    //     .then((res) => {
    //       if (res.StateCode == 200) {
    //         that.yjEntity = res.Data;
    //       } else {
    //         that.$message.error(res.Message);
    //       }
    //     })
    //     .catch((fail) => {
    //       that.$message.error(fail);
    //     });
    // },

    // 处理跟进记录组件发出的预约事件
    handleOpenAppointment(appointmentData) {
      // 将预约事件传递给父组件（线索跟进页面）
      this.$emit('openAppointment', appointmentData);
    },

    // 处理预约记录组件发出的取消预约事件
    handleAppointmentCancelled(cancelData) {
      // 将取消预约事件传递给父组件
      this.$emit('appointmentCancelled', cancelData);
    },

    // 处理跟进记录组件发出的取消预约事件
    handleCancelAppointment(cancelData) {
      // 将取消预约事件传递给父组件
      this.$emit('cancelAppointment', cancelData);
    },

    // 处理跟进记录组件发出的跟进事件
    handleOpenFollowUp(followUpData) {
      // 将跟进事件传递给父组件（线索跟进页面）
      this.$emit('openFollowUp', followUpData);
    },

    // 处理客户基本信息更新事件
    handleCustomerInfoUpdated() {
      console.log('客户基本信息已更新，通知父组件刷新列表');
      // 通知父组件刷新列表数据
      this.$emit('refreshList');
    },
  },
  /** 创建实例之前执行函数   */
  beforeCreate() {},
  /**  实例创建完成之后  */
  created() {},
  /**  在挂载开始之前被调用  */
  beforeMount() {},
  /**  实例被挂载后调用  */
  mounted() {
    // this.customer_yjEntity();
    /**  删除文件档案中文件权限  */
    this.isDeleteFile = this.$permission.getCustomerDetailPermission(this.$router, "iBeauty-Customer-Customer-DeleteFile");
  },
  /**  数据更新 之前 调用  */
  beforeUpdate() {},
  /** 数据更新 完成 调用   */
  updated() {},
  /**  实例销毁之前调用  */
  beforeDestroy() {},
  /**  实例销毁后调用  */
  destroyed() {},
};
</script>

<style lang="scss">
.workbenchCustomerDetail {
  min-width: 600px;
  height: 100%;
  max-width: 100vw;
  .custom-class-drawer {
    .el-drawer__header {
      margin-bottom: unset;
      padding-top: 5px;
      padding-bottom: 5px;
      background: #f5f7fa;
    }
    .el-drawer__body {
      padding: 8px;
    }
  }
  .custom-tabs-class {
    display: flex;
    flex-direction: column;
    height: 100%;
    .el-tabs__content {
      /*height: calc(100vh - 110px);*/
      // height: 100%;
      overflow-y: auto;
      flex: 1;
      .el-tab-pane {
        // height: 100%;
      }
    }
  }
  .el-tabs__header {
    margin: 0 0 5px;
  }
  .el-icon-rank {
    transform: rotate(45deg);
  }
}
</style>
