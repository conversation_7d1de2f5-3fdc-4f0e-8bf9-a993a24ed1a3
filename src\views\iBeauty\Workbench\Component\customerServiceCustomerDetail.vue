<template>
  <div class="customerServiceCustomerDetail">
    <el-drawer :visible.sync="show" @close="closeDialog" :wrapperClosable="false" custom-class="custom-class-drawer" :size="drawerSize" :show-close="false">
      <div slot="title" class="dis_flex flex_x_between">
        <div></div>
        <div class="text_right" style="width: 100px">
          <i @click="changeDrawerSize" class="el-icon-rank font_24 marrt_15"></i>
          <i @click="closeDrawerClick" class="el-icon-close font_24"></i>
        </div>
      </div>
      <el-tabs class="custom-tabs-class" v-model="tabPane" @tab-click="handleClick">
        <el-tab-pane label="基本档案" name="0">
          <workbench-customer-basic-files
            v-if="customerID"
            :customerID="customerID"
            :isCustomerPhoneNumberView="isCustomerPhoneNumberView"
            :isCustomerPhoneNumberModify="isCustomerPhoneNumberModify"
            :isCustomerBasicInformationModify="isCustomerBasicInformationModify"
            :isCustomerServicerModify="isCustomerServicerModify"
            :isCustomerBasicFileModify="isCustomerBasicFileModify"
            ref="customerbasicfiles"
            @customerInfoUpdated="handleCustomerInfoUpdated">
          </workbench-customer-basic-files>
        </el-tab-pane>
        <el-tab-pane label="跟进记录" name="1">
          <workbench-followUpRecord
            v-if="customerID"
            :customerID="customerID"
            :customerName="customerName"
            :isCallBack="isCallBack"
            :hasAppointment="hasAppointment"
            :appointmentStatus="appointmentStatus"
            :appointmentID="appointmentID"
            :leadStatus="leadStatus"
            :leadID="leadID"
            :layoutType="followUpLayoutType"
            ref="followUpRecord"
            @openAppointment="handleOpenAppointment"
            @cancelAppointment="handleCancelAppointment"
            @openFollowUp="handleOpenFollowUp">
          </workbench-followUpRecord>
        </el-tab-pane>
        <el-tab-pane label="订单信息" name="3">
          <workbench-customer-bill
            v-if="customerID"
            :customerID="parseInt(customerID)"
            ref="customerBill">
          </workbench-customer-bill>
        </el-tab-pane>
        <el-tab-pane label="预约记录" name="4">
          <workbench-appointment-record
            v-if="customerID"
            :customerID="customerID"
            :customerName="customerName"
            ref="customerAppointmentRecord"
            @openAppointment="handleOpenAppointment"
            @appointmentCancelled="handleAppointmentCancelled">
          </workbench-appointment-record>
        </el-tab-pane>
      </el-tabs>
    </el-drawer>

    <!-- 客服预约表专用预约弹框 -->
    <el-dialog :visible.sync="appointmentDialogShow" v-if="appointmentDialogShow" width="960px">
      <span slot="title" class="font_18">{{ isAdd ? '添加预约' : '修改预约' }}</span>
      <div>
        <!-- 预约顾客信息  -->
        <el-row>
          <el-col :span="15">
            <el-input
              prefix-icon="el-icon-user-solid"
              v-model="customerName"
              style="width: 100%"
              placeholder="客户信息"
              readonly
              size="small"
            ></el-input>
          </el-col>
          <el-col :span="8" :offset="1" class="back_f8 dis_flex flex_y_center radius5 line_height_38 pad_0_10">
            <el-col :span="12" class="back_f8">预约项目</el-col>
            <el-col :span="12" class="text_right">
              <el-button
                type="text"
                size="small"
                @click="addAppointmentProject"
                >添 加
              </el-button>
            </el-col>
          </el-col>
        </el-row>

        <el-row class="martp_10">
          <el-col :span="15">
            <div style="height: 420px" class="back_f8 radius5">
              <el-scrollbar class="el-scrollbar_height" style="height: 100%">
                <el-row class="padtp_15">
                  <el-form :model="AppointmentInfoRuleForm" :rules="AppointmentInfoRules" ref="AppointmentInfoRuleForm" label-width="120px" size="small">
                    <el-form-item v-if="appointmentTypeList.length > 0" label="预约类型">
                      <el-col :span="20">
                        <el-select v-model="AppointmentInfoRuleForm.AppointmentTypeID" placeholder="请选择预约类型" clearable>
                          <el-option
                            :label="item.Name"
                            :value="item.ID"
                            v-for="item in appointmentTypeList"
                            :key="'appointmentType' + item.ID"
                          >
                          </el-option>
                        </el-select>
                      </el-col>
                    </el-form-item>
                    <el-form-item label="预约门店" prop="EntityID">
                      <el-col :span="20">
                        <el-select v-model="AppointmentInfoRuleForm.EntityID" placeholder="请选择预约门店" clearable>
                          <el-option
                            :label="item.Name"
                            :value="item.ID"
                            v-for="item in entityList"
                            :key="'entity' + item.ID"
                          >
                          </el-option>
                        </el-select>
                      </el-col>
                    </el-form-item>
                    <el-form-item label="预约时间" style="margin-bottom: 0px" required>
                      <el-col :span="12">
                        <el-form-item prop="AppointmentDate">
                          <el-date-picker
                            v-model="AppointmentInfoRuleForm.AppointmentDate"
                            value-format="yyyy-MM-dd"
                            placeholder="选择日期时间"
                            size="small"
                            @change="changeHandleAppointmentDate"
                          >
                          </el-date-picker>
                        </el-form-item>
                      </el-col>

                      <el-col :span="12">
                        <el-form-item prop="Time">
                          <el-time-select
                            v-model="AppointmentInfoRuleForm.Time"
                            :picker-options="{
                              start: appointmentConfigInfo.StartTime,
                              step: '00:' + appointmentConfigInfo.Period,
                              end: appointmentConfigInfo.EndTime,
                            }"
                            placeholder="选择时间"
                          >
                          </el-time-select>
                        </el-form-item>
                      </el-col>
                    </el-form-item>
                    <el-form-item label="预约时长" prop="Period">
                      <el-col :span="20">
                        <el-select
                          v-model="AppointmentInfoRuleForm.Period"
                          placeholder="请选择预约时长"
                          clearable
                        >
                          <el-option :label="item.time" :value="item.value" v-for="(item, index) in timeArr" :key="index"> </el-option>
                        </el-select>
                      </el-col>
                    </el-form-item>
                    <el-form-item v-for="item in addServicerEmployeeList" :key="'addServicerID' + item.ServicerID" :label="item.ServicerName">
                      <el-col :span="20">
                        <el-select v-model="item.SelectEmployeeID" placeholder="请选择员工" clearable @change="changeAppointmentServicer">
                          <el-option
                            :label="item.EmployeeName"
                            :value="item.EmployeeID"
                            v-for="item in item.Employee"
                            :key="'addEmployee' + item.EmployeeID"
                          >
                          </el-option>
                        </el-select>
                      </el-col>
                    </el-form-item>
                    <el-form-item label="商家备注" prop="Remark">
                      <el-col :span="20">
                        <el-input
                          type="textarea"
                          :rows="4"
                          v-model="AppointmentInfoRuleForm.Remark"
                          placeholder="商家备注不超过200个字"
                        >
                        </el-input>
                      </el-col>
                    </el-form-item>
                  </el-form>
                </el-row>
              </el-scrollbar>
            </div>
          </el-col>
          <el-col :span="8" :offset="1">
            <div style="height: 420px" class="back_f8 radius5">
              <el-scrollbar class="el-scrollbar_height" style="height: 100%">
                <div class="pad_10">
                  <el-tag v-for="(item, index) in appointmentProjectList" :key="'appointmentProject' + index" type="primary" class="marlt_5 martp_5" closable @close="deleteAppointmentProject(index)">
                    {{ item.Name || item.ProjectName }}
                  </el-tag>
                </div>
              </el-scrollbar>
            </div>
          </el-col>
        </el-row>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="appointmentDialogShow = false" v-prevent-click size="small">取 消</el-button>
        <el-button type="primary" size="small" :loading="saveLoading" v-prevent-click @click="saveAppointment">保 存</el-button>
      </span>
    </el-dialog>

    <!--选择项目弹框-->
    <el-dialog :visible.sync="selectProjectDialogState" title="选择预约项目" width="900px">
      <template>
        <el-row>
          <el-col :span="8">
            <el-input placeholder="输入项目名称进行搜索" v-model="filterText" size="small" clearable></el-input>
            <el-scrollbar class="el-scrollbar_height martp_5">
              <el-tree
                class="filter-tree"
                :data="projectList"
                show-checkbox
                node-key="PID"
                ref="treeRef"
                :filter-node-method="filterNode"
                :default-checked-keys="defaultCheckedKeysApplyApp"
                :props="defaultProps"
              >
              </el-tree>
            </el-scrollbar>
          </el-col>
          <el-col :span="16">
            <div class="marlt_10">
              <div class="dis_flex flex_x_between">
                <div class="font_16 font_weight_600">已选择项目</div>
                <div>
                  <el-button type="primary" size="small" @click="selectApplicableItems">确 定</el-button>
                </div>
              </div>
              <div class="martp_10">
                <el-table :data="clueSelectedTableData" border size="small" max-height="400">
                  <el-table-column prop="Name" label="项目名称" align="center"></el-table-column>
                  <el-table-column prop="TreatTime" label="治疗时长(分钟)" align="center" width="120"></el-table-column>
                  <el-table-column label="操作" align="center" width="80">
                    <template slot-scope="scope">
                      <el-button type="text" size="small" @click="deleteSelectRow(scope.row, scope.$index)">删除</el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </div>
          </el-col>
        </el-row>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import workbenchCustomerBasicFiles from "@/views/iBeauty/Workbench/Component/workbenchCustomerBasicFiles";
import workbenchFollowUpRecord from "@/views/iBeauty/Workbench/Component/workbenchFollowUpRecord";
import workbenchCustomerBill from "@/views/iBeauty/Workbench/Component/workbenchCustomerBill";
import workbenchAppointmentRecord from "@/views/iBeauty/Workbench/Component/workbenchAppointmentRecord";
import followUpAPI from "@/api/iBeauty/Workbench/followUp";
import * as CommonAPI from "@/api/index";
var Enumerable = require('linq');

export default {
  name: "customerServiceCustomerDetail",
  components: {
    workbenchCustomerBasicFiles,
    workbenchFollowUpRecord,
    workbenchCustomerBill,
    workbenchAppointmentRecord,
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    customerID: {
      type: Number,
      default: null,
    },
    customerName: {
      type: String,
      default: "",
    },
    isCustomerPhoneNumberView: {
      type: Boolean,
      default: false,
    },
    isCustomerPhoneNumberModify: {
      type: Boolean,
      default: false,
    },
    isCustomerBasicInformationModify: {
      type: Boolean,
      default: false,
    },
    isCustomerServicerModify: {
      type: Boolean,
      default: false,
    },
    isCustomerBasicFileModify: {
      type: Boolean,
      default: false,
    },
    appointmentID: {
      type: [String, Number],
      default: null,
    },
    appointmentStatus: {
      type: [String, Number],
      default: null,
    },
    leadStatus: {
      type: [String, Number],
      default: null,
    },
    leadID: {
      type: [String, Number],
      default: null,
    },
    followUpLayoutType: {
      type: String,
      default: "appointment",
    },
  },
  data() {
    return {
      show: false,
      tabPane: "0",
      drawerSize: "60%",
      isCallBack: false,
      hasAppointment: false,
      
      // 预约弹框相关
      appointmentDialogShow: false,
      isAdd: true,
      saveLoading: false,
      currentAppointmentID: null,
      CustomerID: null,
      
      // 预约表单
      AppointmentInfoRuleForm: {
        AppointmentTypeID: "",
        EntityID: "",
        AppointmentDate: "",
        Time: "",
        Period: 30,
        Remark: "",
      },

      // 预约表单验证规则
      AppointmentInfoRules: {
        AppointmentDate: [{ required: true, message: "请选择预约日期", trigger: "change" }],
        Time: [{ required: true, message: "请选择预约时间", trigger: "change" }],
        Period: [{ required: true, message: "请选择预约时长", trigger: "change" }],
        EntityID: [{ required: true, message: "请选择预约门店", trigger: "change" }],
      },

      // 下拉选项和配置
      appointmentTypeList: [],
      entityList: [],
      appointmentProjectList: [],
      addServicerEmployeeList: [],
      appointmentConfigInfo: {
        StartTime: "08:00",
        EndTime: "20:00",
        Period: "30",
        AppointmentServicerIsRequired: false,
      },
      timeArr: [
        { time: "30分钟", value: 30 },
        { time: "60分钟", value: 60 },
        { time: "90分钟", value: 90 },
        { time: "120分钟", value: 120 },
      ],

      // 项目选择相关
      selectProjectDialogState: false,
      projectList: [],
      filterText: '',
      defaultCheckedKeysApplyApp: [],
      clueSelectedTableData: [],
      defaultProps: {
        children: 'Child',
        label: 'Name',
      },
    };
  },
  watch: {
    visible: {
      handler(newVal) {
        console.log('客户详情组件 visible 变化:', newVal);
        console.log('当前 customerID:', this.customerID);
        console.log('当前 customerName:', this.customerName);
        this.show = newVal;
        if (newVal && this.customerID) {
          console.log('开始加载客户数据...');
          // 重置为基本档案标签页
          this.tabPane = "0";
          // 当弹框打开时，自动加载当前标签页的数据
          this.$nextTick(() => {
            this.handleClick();
          });
        }
      },
      immediate: true,
    },
    show: {
      handler(newVal) {
        this.$emit("update:visible", newVal);
        if (newVal && this.customerID) {
          // 重置为基本档案标签页
          this.tabPane = "0";
          // 当弹框显示时，确保加载当前标签页的数据
          this.$nextTick(() => {
            this.handleClick();
          });
        }
      },
    },
    customerID: {
      immediate: true,
      handler(val) {
        console.log('客户详情组件 customerID 变化:', val);
        if (val && this.show) {
          // 重置为基本档案标签页
          this.tabPane = "0";
          this.$nextTick(() => {
            this.handleClick();
          });
        }
      },
    },
  },
  methods: {
    changeMinWidth() {
      // 改变最小宽度
    },

    testAppointmentDialog() {
      console.log('测试预约弹框');
      this.appointmentDialogShow = true;
    },
    
    closeDialog() {
      this.show = false;
      // 通知父组件刷新列表数据
      this.$emit('refreshList');
    },

    closeDrawerClick() {
      this.show = false;
      // 通知父组件刷新列表数据
      this.$emit('refreshList');
    },
    
    changeDrawerSize() {
      this.drawerSize = this.drawerSize === "60%" ? "80%" : "60%";
    },
    
    handleClick(tab) {
      // 标签页点击事件
      let that = this;
      let tabPane = tab ? tab.name : this.tabPane;

      if (that.customerID) {
        switch (tabPane) {
          case "0":
            {
              if (that.$refs.customerbasicfiles && that.$refs.customerbasicfiles.getCustomerInfoData) {
                that.$refs.customerbasicfiles.getCustomerInfoData();
              }
            }
            break;

          case "1":
            {
              if (that.$refs.followUpRecord && that.$refs.followUpRecord.getCustomerFollowUp) {
                that.$refs.followUpRecord.getCustomerFollowUp();
              }
            }
            break;

          case "3":
            {
              if (that.$refs.customerBill && that.$refs.customerBill.searchSaleBill) {
                that.$refs.customerBill.searchSaleBill();
              }
            }
            break;

          case "4":
            {
              if (that.$refs.customerAppointmentRecord && that.$refs.customerAppointmentRecord.getAppointmentRecordList) {
                that.$refs.customerAppointmentRecord.getAppointmentRecordList();
              }
            }
            break;
        }
      }
    },
    
    // 处理跟进记录组件发出的预约事件
    handleOpenAppointment(appointmentData) {
      console.log('客服预约表 - 处理预约事件:', appointmentData);
      
      if (appointmentData.action === 'edit') {
        // 修改预约
        this.CustomerID = parseInt(appointmentData.CustomerID); // 确保是 Number 类型
        this.isAdd = false;
        this.currentAppointmentID = appointmentData.AppointmentID;
        
        // 加载预约数据
        this.loadAppointmentData(appointmentData.AppointmentID);
      } else if (appointmentData.action === 'create') {
        // 新建预约
        this.CustomerID = parseInt(appointmentData.CustomerID); // 确保是 Number 类型
        this.isAdd = true;
        this.currentAppointmentID = null;
        this.appointmentDialogShow = true;
        
        // 重置表单
        this.AppointmentInfoRuleForm = {
          AppointmentTypeID: "",
          EntityID: "",
          AppointmentDate: this.$formatDate(new Date(), "YYYY-MM-DD"),
          Time: "",
          Period: 30,
          Remark: "",
        };

        this.appointmentProjectList = [];
        this.clueSelectedTableData = [];

        // 获取基础数据
        this.getEntityList();
        this.getAppointmentType();
        this.getAppointmentConfig();
        this.getServicerEmployee();
      }
    },
    
    // 处理跟进记录组件发出的取消预约事件
    handleCancelAppointment(cancelData) {
      // 这里可以添加取消预约的处理逻辑
      console.log('取消预约:', cancelData);
    },
    
    // 处理预约记录组件发出的取消预约事件
    handleAppointmentCancelled(cancelData) {
      // 刷新预约记录
      if (this.$refs.customerAppointmentRecord) {
        this.$refs.customerAppointmentRecord.getAppointmentRecordList();
      }
      // 刷新跟进记录
      if (this.$refs.followUpRecord) {
        this.$refs.followUpRecord.getCustomerFollowUp();
      }
    },
    
    // 处理跟进记录组件发出的跟进事件
    handleOpenFollowUp(followUpData) {
      // 将跟进事件传递给父组件
      this.$emit('openFollowUp', followUpData);
    },

    // 处理客户基本信息更新事件
    handleCustomerInfoUpdated() {
      console.log('客户基本信息已更新，通知父组件刷新列表');
      // 通知父组件刷新列表数据
      this.$emit('refreshList');
    },

    // 获取门店列表
    getEntityList() {
      let that = this;
      CommonAPI.POST("api/entity/allEntity", {})
        .then((res) => {
          if (res.StateCode == 200) {
            that.entityList = (res.Data || []).map(item => ({
              ...item,
              ID: String(item.ID)
            }));
          } else {
            that.$message.error(res.Message || '获取门店列表失败');
          }
        })
        .catch((error) => {
          console.error('获取门店列表失败:', error);
          that.$message.error('获取门店列表失败，请稍后重试');
        });
    },

    // 获取预约类型
    getAppointmentType() {
      let that = this;
      let params = {
        Name: '', //搜索名称
        Active: true, //有效性
      };
      followUpAPI.getAppointmentType(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.appointmentTypeList = res.Data || [];
          } else {
            that.$message.error(res.Message || '获取预约类型失败');
          }
        })
        .catch((error) => {
          console.error('获取预约类型失败:', error);
          that.$message.error('获取预约类型失败，请稍后重试');
        });
    },

    // 加载预约数据（用于编辑）
    loadAppointmentData(appointmentID) {
      let that = this;
      let params = { ID: appointmentID };

      followUpAPI.appointmentBillDetail(params)
        .then((res) => {
          if (res.StateCode == 200) {
            let data = res.Data;

            // 处理预约日期和时间
            let appointmentDate = "";
            let appointmentTime = "";

            if (data.AppointmentDate) {
              if (data.AppointmentDate.includes(' ')) {
                let dateTimeParts = data.AppointmentDate.split(' ');
                appointmentDate = dateTimeParts[0];
                appointmentTime = dateTimeParts[1] ? dateTimeParts[1].substring(0, 5) : "";
              } else {
                appointmentDate = data.AppointmentDate;
              }
            }

            if (data.Time) {
              appointmentTime = data.Time.length > 5 ? data.Time.substring(0, 5) : data.Time;
            }

            that.AppointmentInfoRuleForm = {
              ID: data.ID,
              AppointmentTypeID: data.AppointmentTypeID,
              EntityID: data.EntityID ? String(data.EntityID) : "",
              AppointmentDate: appointmentDate,
              Time: appointmentTime,
              Period: data.Period,
              Remark: data.Remark,
            };

            that.appointmentProjectList = data.Project || [];
            that.addServicerEmployeeList = data.Servicer || [];

            // 获取基础数据并打开弹框
            that.getEntityList();
            that.getAppointmentType();
            that.getAppointmentConfig();
            that.getServicerEmployee();
            that.appointmentDialogShow = true;
          } else {
            that.$message.error(res.Message || '加载预约数据失败');
          }
        })
        .catch((error) => {
          console.error('加载预约数据失败:', error);
          that.$message.error('加载预约数据失败，请稍后重试');
        });
    },

    // 保存预约
    saveAppointment() {
      let that = this;

      that.$refs['AppointmentInfoRuleForm'].validate((valid) => {
        if (valid) {
          that.saveLoading = true;

          if (that.isAdd) {
            // 新建预约
            that.createAppointment();
          } else {
            // 修改预约
            that.updateAppointment();
          }
        }
      });
    },

    // 创建预约
    createAppointment() {
      let that = this;
      let params = {
        CustomerID: parseInt(that.CustomerID),
        AppointmentTypeID: that.AppointmentInfoRuleForm.AppointmentTypeID,
        EntityID: that.AppointmentInfoRuleForm.EntityID,
        AppointmentDate: that.AppointmentInfoRuleForm.AppointmentDate + ' ' + that.AppointmentInfoRuleForm.Time,
        Period: that.AppointmentInfoRuleForm.Period,
        Remark: that.AppointmentInfoRuleForm.Remark,
        AppointmentCategory: 2,
        Status: 10,
        Servicer: that.addServicerEmployeeList
          .filter((i) => i.SelectEmployeeID)
          .map((val) => {
            return {
              ServicerID: val.ServicerID,
              EmployeeID: val.SelectEmployeeID,
            };
          }),
        Project: that.appointmentProjectList.map((val) => {
          return {
            ProjectID: val.ID,
          };
        }),
      };

      followUpAPI.appointmentBillCreate(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.$message.success('预约创建成功');
            that.appointmentDialogShow = false;

            // 刷新预约记录
            that.refreshAppointmentRecords();
          } else {
            that.$message.error(res.Message || '创建预约失败');
          }
        })
        .catch((error) => {
          console.error('创建预约失败:', error);
          that.$message.error('创建预约失败，请稍后重试');
        })
        .finally(() => {
          that.saveLoading = false;
        });
    },

    // 修改预约
    updateAppointment() {
      let that = this;
      let params = {
        ID: that.currentAppointmentID,
        CustomerID: parseInt(that.CustomerID),
        AppointmentTypeID: that.AppointmentInfoRuleForm.AppointmentTypeID,
        EntityID: that.AppointmentInfoRuleForm.EntityID,
        AppointmentDate: that.AppointmentInfoRuleForm.AppointmentDate + ' ' + that.AppointmentInfoRuleForm.Time,
        Period: that.AppointmentInfoRuleForm.Period,
        Remark: that.AppointmentInfoRuleForm.Remark,
        AppointmentCategory: 2,
        Status: 10,
        Servicer: that.addServicerEmployeeList
          .filter((i) => i.SelectEmployeeID)
          .map((val) => {
            return {
              ServicerID: val.ServicerID,
              EmployeeID: val.SelectEmployeeID,
            };
          }),
        Project: that.appointmentProjectList.map((i) => {
          return {
            ProjectID: i.ID,
          };
        }),
      };

      followUpAPI.appointmentBillUpdate(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.$message.success('预约修改成功');
            that.appointmentDialogShow = false;

            // 刷新预约记录
            that.refreshAppointmentRecords();
          } else {
            that.$message.error(res.Message || '修改预约失败');
          }
        })
        .catch((error) => {
          console.error('修改预约失败:', error);
          that.$message.error('修改预约失败，请稍后重试');
        })
        .finally(() => {
          that.saveLoading = false;
        });
    },

    // 刷新预约记录
    refreshAppointmentRecords() {
      let that = this;

      // 刷新预约记录组件
      if (that.$refs.customerAppointmentRecord) {
        that.$refs.customerAppointmentRecord.getAppointmentRecordList();
      }

      // 刷新跟进记录组件
      if (that.$refs.followUpRecord) {
        that.$refs.followUpRecord.getCustomerFollowUp();
      }

      // 通知父组件刷新
      that.$emit('appointmentUpdated');
    },

    // 获取预约配置
    getAppointmentConfig() {
      let that = this;
      followUpAPI.getAppointmentConfig()
        .then((res) => {
          if (res.StateCode == 200) {
            that.appointmentConfigInfo = res.Data || that.appointmentConfigInfo;
          }
        })
        .catch((error) => {
          console.error('获取预约配置失败:', error);
        });
    },

    // 获取服务人员
    getServicerEmployee() {
      // 这里可以调用API获取服务人员列表，暂时使用空数组
      this.addServicerEmployeeList = [];
    },

    // 预约日期变化处理
    changeHandleAppointmentDate() {
      // 可以在这里处理日期变化逻辑
    },

    // 预约服务人员变化处理
    changeAppointmentServicer() {
      // 可以在这里处理服务人员变化逻辑
    },

    // 添加预约项目
    addAppointmentProject() {
      var that = this;
      that.clueSelectedTableData = Object.assign([], that.appointmentProjectList);
      that.defaultCheckedKeysApplyApp = [];

      // 如果项目列表为空，先加载项目列表
      if (that.projectList.length === 0) {
        that.getProjectList();
      }

      that.selectProjectDialogState = true;
      that.$nextTick(() => {
        var defaultCheckedKeys = that.appointmentProjectList.map(item => '1' + item.ID);
        if (that.$refs.treeRef) {
          that.$refs.treeRef.setCheckedKeys(defaultCheckedKeys);
        }
      });
    },

    // 删除预约项目
    deleteAppointmentProject(index) {
      this.appointmentProjectList.splice(index, 1);
    },

    // 获取项目列表
    getProjectList() {
      let that = this;
      // 这里应该调用获取项目列表的API
      // 暂时使用空数组
      that.projectList = [];
    },

    // 项目搜索过滤
    filterNode(value, data) {
      if (!value) return true;
      return data.Name.indexOf(value) !== -1;
    },

    // 选择适用项目事件
    selectApplicableItems() {
      var that = this;
      var checkedNodes = that.$refs.treeRef.getCheckedNodes();
      var selectedTableData = checkedNodes.filter(node => node.PID && node.PID.startsWith('1'));

      if (selectedTableData.length == 0) {
        that.$message.error('请选择项目');
        return false;
      } else {
        var totalPeriod = 0;
        that.appointmentProjectList = Object.assign([], selectedTableData);
        that.selectProjectDialogState = false;
        that.appointmentProjectList.forEach((val) => {
          totalPeriod += val.TreatTime || 30;
        });
        for (let i = 0; i <= that.timeArr.length - 1; i++) {
          if (that.timeArr[i].value >= totalPeriod) {
            that.AppointmentInfoRuleForm.Period = that.timeArr[i].value;
            break;
          }
        }
      }
    },

    // 删除所选中的适用项目
    deleteSelectRow(row, index) {
      var that = this;
      that.clueSelectedTableData.splice(index, 1);
      that.$nextTick(() => {
        var defaultCheckedKeys = that.clueSelectedTableData.map(item => '1' + item.ID);
        if (that.$refs.treeRef) {
          that.$refs.treeRef.setCheckedKeys(defaultCheckedKeys);
        }
      });
    },

    // 手动显示客户详情弹框
    showCustomerDetail(customerID, customerName) {
      console.log('手动显示客户详情弹框:', { customerID, customerName });
      // 重置为基本档案标签页
      this.tabPane = "0";
      this.show = true;
      if (customerID) {
        // 当手动显示弹框时，自动加载当前标签页的数据
        this.$nextTick(() => {
          this.handleClick();
        });
      }
    },
  },

  watch: {
    filterText(val) {
      if (this.$refs.treeRef) {
        this.$refs.treeRef.filter(val);
      }
    },
  },

  mounted() {
    console.log('customerServiceCustomerDetail 组件已挂载');
    console.log('初始 props:', {
      visible: this.visible,
      customerID: this.customerID,
      customerName: this.customerName
    });

    // 强制检查一次 visible 状态
    this.$nextTick(() => {
      console.log('nextTick 中的 props:', {
        visible: this.visible,
        customerID: this.customerID,
        customerName: this.customerName
      });
      if (this.visible) {
        console.log('mounted 中发现 visible 为 true，设置 show');
        this.show = true;
      }
    });
  },
};
</script>

<style scoped>
.customerServiceCustomerDetail {
  /* 样式 */
}

.back_f8 {
  background-color: #f8f8f8;
}

.radius5 {
  border-radius: 5px;
}

.dis_flex {
  display: flex;
}

.flex_y_center {
  align-items: center;
}

.flex_x_between {
  justify-content: space-between;
}

.line_height_38 {
  line-height: 38px;
}

.pad_0_10 {
  padding: 0 10px;
}

.padtp_15 {
  padding-top: 15px;
}

.martp_10 {
  margin-top: 10px;
}

.martp_5 {
  margin-top: 5px;
}

.marlt_5 {
  margin-left: 5px;
}

.marlt_10 {
  margin-left: 10px;
}

.pad_10 {
  padding: 10px;
}

.text_right {
  text-align: right;
}

.font_16 {
  font-size: 16px;
}

.font_weight_600 {
  font-weight: 600;
}

.el-scrollbar_height {
  height: 380px;
}

.filter-tree {
  max-height: 380px;
  overflow: auto;
}
</style>
