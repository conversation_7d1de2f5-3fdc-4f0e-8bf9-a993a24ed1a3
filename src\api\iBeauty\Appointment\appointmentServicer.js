/**
 * Created by preference on 2022/09/08
 *  zmx
 */

import * as API from "@/api/index";
export default {
  /** 接待角色列表  */
  appointmentServicer_all: (params) => {
    return API.POST("api/appointmentServicer/all", params);
  },
  /**  接待角色详细 */
  appointmentServicer_detail: (params) => {
    return API.POST("api/appointmentServicer/detail", params);
  },
  /**  新建接待角色 */
  appointmentServicer_create: (params) => {
    return API.POST("api/appointmentServicer/create", params);
  },
  /** 更新接待角色  */
  appointmentServicer_update: (params) => {
    return API.POST("api/appointmentServicer/update", params);
  },
  /** 移动  */
  appointmentServicer_move: (params) => {
    return API.POST("api/appointmentServicer/move", params);
  },
  /**  组织列表 */
  entity_list: (params) => {
    return API.POST("api/entity/list", params);
  },
  /** 职务列表  */
  jobtype_all: (params) => {
    return API.POST("api/jobtype/all", params);
  },
};
