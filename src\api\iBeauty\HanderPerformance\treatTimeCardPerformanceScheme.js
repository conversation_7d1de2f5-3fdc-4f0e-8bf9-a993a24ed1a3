/**
 * Created by JoJO on 2022/01/10
 *
 */

import * as API from '@/api/index'
export default {

  /** 业绩方案列表  */
  treatTimeCardPerformanceScheme_list: params => {
    return API.POST('api/treatTimeCardPerformanceScheme/list', params)
  },
  /**  业绩方案保存 */
  treatTimeCardPerformanceScheme_create: params => {
    return API.POST('api/treatTimeCardPerformanceScheme/create', params)
  },
  /**  业绩方案删除 */
  treatTimeCardPerformanceScheme_delete: params => {
    return API.POST('api/treatTimeCardPerformanceScheme/delete', params)
  },
  /**  时效卡分类业绩  */
  treatTimeCardProjectCategoryPerformance_all: params => {
    return API.POST('api/treatTimeCardCategoryPerformance/all', params)
  },
  /** 时效卡分类业绩保存*/
  treatTimeCardProjectCategoryPerformance_update: params => {
    return API.POST('api/treatTimeCardCategoryPerformance/update', params)
  },
  /**  所有时效卡经手人业绩 */
  treatTimeCardSchemeHandlerPerformance_all: params => {
    return API.POST('api/treatTimeCardSchemeHandlerPerformance/all', params)
  },
  /** 所有时效卡经手人业绩保存   */
  treatTimeCardSchemeHandlerPerformance_update: params => {
    return API.POST('api/treatTimeCardSchemeHandlerPerformance/update', params)
  },
  /**  分类时效卡经手人业绩 */
  treatTimeCardCategoryHandlerPerformance_all: params => {
    return API.POST('api/treatTimeCardCategoryHandlerPerformance/all', params)
  },
  /** 分类时效卡经手人业绩保存   */
  treatTimeCardCategoryHandlerPerformance_update: params => {
    return API.POST('api/treatTimeCardCategoryHandlerPerformance/update', params)
  },
  /**  时效卡业绩 */
  treatTimeCardPerformance_all: params => {
    return API.POST('api/treatTimeCardPerformance/all', params)
  },
  /** 时效卡业绩保存   */
  treatTimeCardPerformance_update: params => {
    return API.POST('api/treatTimeCardPerformance/update', params)
  },
  /**  时效卡经手人业绩 */
  treatTimeCardHandlerPerformance_all: params => {
    return API.POST('api/treatTimeCardHandlerPerformance/all', params)
  },
  /** 时效卡经手人业绩保存   */
  treatTimeCardHandlerPerformance_update: params => {
    return API.POST('api/treatTimeCardHandlerPerformance/update', params)
  },
  /**获取  时效卡业绩 项目业绩 */
  treatTimeCardProjectPerformance_all: params => {
    return API.POST('api/treatTimeCardProjectPerformance/all', params)
  },
  /**保存 时效卡业绩 项目业绩 */
  treatTimeCardProjectPerformance_update: params => {
    return API.POST('api/treatTimeCardProjectPerformance/update', params)
  },

  /**获取  时效卡业绩 项目业绩  经手人业绩 */
  treatTimeCardProjectHandlerPerformance_all: params => {
    return API.POST('api/treatTimeCardProjectHandlerPerformance/all', params)
  },
  /**保存 时效卡业绩 项目业绩  经手人业绩 */
  treatTimeCardProjectHandlerPerformance_update: params => {
    return API.POST('api/treatTimeCardProjectHandlerPerformance/update', params)
  },

}
