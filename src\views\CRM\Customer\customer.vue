<template>
  <div  class="content_body Customer" v-loading="loading">
  
    <div class="nav_header">
      <el-row>
        <el-col :span="22">
          <el-form :inline="true" size="small" @keyup.enter.native="handleSearch">
            <el-form-item label="客户名称">
              <el-input v-model="name" placeholder="请输入姓名/手机号/编码搜索" clearable @clear="handleSearch"></el-input>
            </el-form-item>
            <el-form-item label="会员等级">
              <el-select v-model="customerLevelID" placeholder="请选择会员等级" clearable @change="handleSearch">
                <el-option v-for="item in customerLevel" :key="item.ID" :label="item.Name" :value="item.ID"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="成为会员日期">
              <el-date-picker v-model="MemberOnDate" type="daterange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" value-format="yyyy-MM-dd" clearable :picker-options="pickerOptionsMemberOnDate" popper-class="customDateClass" @change="handleSearch"></el-date-picker>
            </el-form-item>
            <el-form-item label="客户生日">
              <el-date-picker
                v-model="Birthday"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                format="MM 月 dd 日"
                value-format="yyyy-MM-dd"
                clearable
                :picker-options="pickerOptions"
                popper-class="customDateClass"
                @change="handleSearch"
              ></el-date-picker>
            </el-form-item>

            <el-form-item label="久未到店">
              <el-date-picker v-model="toShopDate" type="daterange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" value-format="yyyy-MM-dd" clearable popper-class="customDateClass" @change="handleSearch" :picker-options="toShopPickerOptions"></el-date-picker>
            </el-form-item>

            <el-form-item label="信息来源">
              <el-cascader
                v-model="customerSourceID"
                placeholder="请选择信息来源"
                :options="customerSource"
                :props="{
                  checkStrictly: true,
                  children: 'Child',
                  value: 'ID',
                  label: 'Name',
                  emitPath: false,
                }"
                :show-all-levels="false"
                clearable
                filterable
                @change="handleSearch"
              ></el-cascader>
            </el-form-item>
            <el-form-item label="渠道来源">
              <el-input v-model="searchChannelName" clearable @keyup.enter.native="handleSearch" @clear="handleSearch" placeholder="请输入渠道来源"></el-input>
            </el-form-item>

            <el-form-item label="介绍人">
              <el-input v-model="searchIntroducerName" clearable @keyup.enter.native="handleSearch" @clear="handleSearch" placeholder="请输入介绍人"></el-input>
            </el-form-item>

            <el-form-item v-if="allCustomerServicer.length != 0" label="服务人员">
              <el-select v-model="ServicerID" filterable placeholder="请选择服务人员类别" clearable @change="handleSearch">
                <el-option v-for="item in allCustomerServicer" :key="item.ID" :label="item.Name" :value="item.ID"> </el-option>
              </el-select>
            </el-form-item>
            <el-form-item v-if="allCustomerServicer.length != 0" label="服务员工">
              <el-select v-model="ServicerEmployeeID" filterable placeholder="请选择服务人员" clearable @change="handleSearch">
                <el-option v-for="item in ServicerList" :key="item.ID" :label="item.Name" :value="item.ID"></el-option>
              </el-select>
            </el-form-item>
            
            <el-form-item label="注册日期">
              <el-date-picker
                v-model="createdOnDate"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                clearable
                :picker-options="pickerOptions_"
                popper-class="customDateClass"
                @change="handleSearch"
              ></el-date-picker>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleSearch" v-prevent-click>搜索</el-button>
            </el-form-item>
            <el-form-item>
              <el-dropdown @command="customer_Export" v-if="isCustomerExport && isCustomerPhoneNumberExport" :loading="downloadLoading">
                <el-button type="primary"> 导出<i class="el-icon-arrow-down el-icon--right"></i> </el-button>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item command="excelNoDisPlayPhone">导出</el-dropdown-item>
                  <el-dropdown-item command="excelDisPlayPhone">导出(手机号)</el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
              <el-button @click="customer_Export('excelNoDisPlayPhone')" v-else-if="isCustomerExport" type="primary" v-prevent-click :loading="downloadLoading"> 导出 </el-button>
              <el-button @click="customer_Export('excelDisPlayPhone')" v-else-if="isCustomerPhoneNumberExport" type="primary" v-prevent-click :loading="downloadLoading"> 导出(手机号） </el-button>
            </el-form-item>
          </el-form>
        </el-col>
        <el-col :span="2" class="text_right">
          <el-button type="primary" size="small" @click="showAddDialog" v-prevent-click>新增</el-button>
        </el-col>
      </el-row>
    </div>

    <div>
      <el-table size="small" :data="tableData" tooltip-effect="light">
        <el-table-column label="操作" :width="openBillState ? '240' : '160'" fixed="left">
          <template slot-scope="scope">
            <el-button type="primary" size="small" @click="customerDetailClick(scope.row)" v-prevent-click>详情 </el-button>
            <el-button type="primary" size="small" @click="openConsultationForm(scope.row)" v-prevent-click>开面诊单</el-button>
            <el-button v-if="openBillState" type="primary" size="small" @click="gotoOrderCreated(scope.row)" v-prevent-click>开单</el-button>
          </template>
        </el-table-column>
        <el-table-column prop="Name" label="客户姓名">
          <template slot-scope="scope">
            {{ scope.row.Name }}
            <a v-if="scope.row.skinReport" v-bind:href="scope.row.skinReport.ViewUrl" target="_blank" title="美际3D皮肤报告">
              <img v-bind:src="skinReportImg" style="width: 20px; height: 20px;"/>
            </a>
          </template>
        </el-table-column>
        <!-- <el-table-column prop="Code" label="客户编号"></el-table-column> -->
        <el-table-column prop="EntityName" label="所属组织"></el-table-column>
        <el-table-column prop="PhoneNumber" label="手机号码">
          <template slot-scope="scope">
            {{ scope.row.PhoneNumber | hidephone }}
          </template>
        </el-table-column>
        <el-table-column prop="Gender" label="性别" :formatter="formatGender"></el-table-column>
        <el-table-column prop="Birthday" label="生日">
          <template slot-scope="scope">
            <span v-if="scope.row.Birthday">{{ scope.row.BirthdayType == 10 ? "公历 " + scope.row.Birthday : "农历 " + scope.row.Birthday }}</span>
          </template>
        </el-table-column>
        <el-table-column label="服务人员">
          <template slot-scope="scope">
            <el-popover placement="top-start" width="200" trigger="hover">
              <el-descriptions size="mini" :column="1" border :colon="false" labelClassName="custom-customer-descLabel">
                <el-descriptions-item v-for="(item, index) in scope.row.ServicerEmployee" :key="index" :label="item.Name + '：'">{{ getServicerEmpNames(item.ServicerEmpList) }}</el-descriptions-item>
              </el-descriptions>
              <div slot="reference" style="text-overflow: ellipsis; white-space: nowrap; overflow: hidden">
                <span v-if="scope.row.ServicerEmployee.length > 0">
                  <span v-if="scope.row.ServicerEmployee[0].ServicerEmpList">{{ scope.row.ServicerEmployee[0].Name }}：</span>
                  <span>{{ getServicerEmpNames(scope.row.ServicerEmployee[0].ServicerEmpList) }}</span>
                </span>
              </div>
            </el-popover>
          </template>
        </el-table-column>
        <el-table-column prop="CustomerSourceName" label="信息来源"></el-table-column>
        <el-table-column prop="ChannelName" label="渠道来源"></el-table-column>
        <el-table-column prop="IntroducerName" label="介绍人"></el-table-column>
        <el-table-column prop="CustomerLevelName" label="会员等级"></el-table-column>
        <el-table-column prop="CreatedOn" label="成为会员日期">
          <template slot-scope="scope">{{ scope.row.MemberOn | dateFormat("YYYY-MM-DD HH:mm") }}</template>
        </el-table-column>
        <el-table-column prop="LastBillDate" label="上次消费时间">
          <template slot-scope="scope">
            <span v-if="scope.row.LastBillDate">{{ scope.row.LastBillDate | dateFormat("YYYY-MM-DD") }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="CreatedOn" label="注册日期">
          <template slot-scope="scope">{{ scope.row.CreatedOn | dateFormat("YYYY-MM-DD") }}</template>
        </el-table-column>
      </el-table>
      <div class="pad_15 text_right">
        <el-pagination background v-if="paginations.total > 0" @current-change="handleCurrentChange" :current-page.sync="paginations.page" :page-size="paginations.page_size" :layout="paginations.layout" :total="paginations.total"></el-pagination>
      </div>
    </div>

    <!--新增弹窗  -->
    <add-customer :title="isAdd ? '新增客户' : '编辑基本信息'" :visible.sync="dialogVisible" @addCustomerSuccess="addCustomerSuccess" :isCreateMember="isCreateMember"></add-customer>

    <!--标签弹窗-->
    <el-dialog title="编辑标签" :visible.sync="dialogTag" width="1000px">
      <el-row style="max-height: 130px; overflow-y: auto">
        <el-tag v-for="(item, index) in editCustomerTag" :key="index" closable @close="removeTag(index)" effect="plain" class="mar_5">{{ item.Name }}</el-tag>
      </el-row>
      <el-row class="pad_5" v-if="customTagLibrary">
        <el-col :span="10">
          <div class="el-form-item el-form-item--small" style="margin-bottom: 0px">
            <label class="el-form-item__label" style="width: 98px">自定义标签：</label>
            <div class="el-form-item__content" style="margin-left: 98px">
              <div class="el-input el-input--small">
                <el-input type="text" autocomplete="off" placeholder="标签名限8个字" v-model="tagName" maxlength="8" size="small" clearable>
                  <template slot="append">
                    <el-button size="small" @click="addTagClick" clearable v-prevent-click>添加</el-button>
                  </template>
                </el-input>
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
      <el-row class="pad_5">
        <div class="pad_5_0">选择已有标签</div>
        <el-col style="height: 180px; overflow-y: auto" class="border radius5 pad_10">
          <el-tag v-for="item in tagList" :key="item.ID" :type="item.type" effect="plain" @click="tagSelectClick(item)" class="cursor_pointer mar_5">{{ item.Name }}</el-tag>
        </el-col>
      </el-row>
      <div slot="footer">
        <el-button size="small" @click="dialogTag = false" v-prevent-click>取 消</el-button>
        <el-button type="primary" size="small" @click="tagSaveClick" :loading="modalLoading" v-prevent-click>保存 </el-button>
      </div>
    </el-dialog>
    <!--修改跟进-->
    <el-dialog title="修改跟进" :visible.sync="editFollowVisble" width="800px">
      <el-row>
        <el-col :span="24" class="backColor_f8 border_bottom pad_10 font_16">客户信息：{{ customerDetail.Name }} 【{{ customerDetail.Code }}】</el-col>
      </el-row>

      <el-form label-width="85px" size="mini">
        <el-row class="backColor_f8 padtp_10">
          <el-col :span="24" class="font_14">
            <el-form-item label="关联订单">
              <el-button type="text" size="small">重新选择</el-button>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="订单时间">2020-09-29 :15:45</el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="下单门店">苏州中心店</el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="订单金额">5000.00</el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="项目名称">2如今看来是对方过后就</el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-form-item label="跟进类型" required>
            <el-radio-group>
              <el-radio label="回访"></el-radio>
              <el-radio label="邀约"></el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item label="回访记录" required>
            <el-input type="textarea" placeholder="请输入内容"></el-input>
          </el-form-item>

          <el-form-item label="添加图片">
            <el-upload ref="upload" action="/" list-type="picture-card" :limit="5" :on-exceed="exceed" :file-list="fileList1" :http-request="auxiliaryUpload" :on-remove="handleRemove" :before-upload="onBeforeUploadImage" multiple>
              <i class="el-icon-plus"></i>
            </el-upload>
          </el-form-item>
        </el-row>
      </el-form>

      <div slot="footer">
        <el-button size="small" @click="editFollowVisble = false" v-prevent-click>取 消</el-button>
        <el-button type="primary" size="small" @click="followSaveClick" :loading="modalLoading" v-prevent-click>保存 </el-button>
      </div>
    </el-dialog>
    <!-- 顾客详情 -->
    <customer-detail 
      ref="customerDetail" 
      @refreshCustomerList="search" 
      v-if="customerDetailVisible" 
      :customerID="customerID" 
      :isModifyBelongEntity="isModifyBelongEntity" 
      :visible.sync="customerDetailVisible" 
      :isDeleteNursingLog="isDeleteNursingLog" 
      :isCustomerBasicInformationModify="isCustomerBasicInformationModify"
      :isCustomerServicerModify="isCustomerServicerModify" 
      :isModifyCustomerLevel="isModifyCustomerLevel">
    </customer-detail>
    <div v-show="false" style="height: 10px;width: 100%;">
      <medicalEditor ref="hiddenMedicalEditor"></medicalEditor>
    </div>
  
  </div>
</template>

<script>
import medicalEditor from "@/components/medicalEditor/medicalEditor.vue";
import API from "@/api/CRM/Customer/customer";
import APICustomerLevel from "@/api/CRM/Customer/customerLevel";
import APICustomerSource from "@/api/CRM/Customer/customerSource";
import APITagLibrary from "@/api/CRM/Customer/customerTagLibrary";
import APIAppointment from "@/api/iBeauty/Appointment/appointmentView";
import date from "@/components/js/date";
import addCustomer from "@/views/CRM/Customer/Components/CustomerDetail/addCustomer.vue";

var Enumerable = require("linq");
const dayjs = require("dayjs");
// import customerDetail from "@/views/CRM/Customer/Components/CustomerDetail/CustomerDetail";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";

export default {
  name: "Customer",
  // Treeselect  /**  引入的组件  */
  // 
  components: { 
    customerDetail:()=>import("@/views/CRM/Customer/Components/CustomerDetail/CustomerDetail"), 
    addCustomer,
    medicalEditor
  },
  data() {
    return {
      createdOnDate:"",
      isModifyCustomerLevel:false,
      isCustomerServicerModify:false,
      isCustomerBasicInformationModify:false,

      isDeleteNursingLog: false,
      searchChannelName: "",
      searchIntroducerName: "",
      isCreateMember: false,
      isModifyBelongEntity: false,
      downloadLoading: false,
      isCustomerExport: false,
      isCustomerPhoneNumberExport: false,
      isThreshold: true /**  是否有入会门槛 true 为没有 false 为有  */,
      isShowChannel: false, //是否展示渠道
      isElectronicMedicalRecord: false, // 电子病例
      isDeleteFile: false,
      customerDetailVisible: false,
      regionDataSelArr: [], //已选择省市区
      avatarcircleUrl: "",
      customTagLibrary: false, //自定义标签权限
      loading: false,
      modalLoading: false,
      dialogVisible: false,
      dialogDetail: false,
      dialogTag: false,
      editFollowVisble: false,
      isAdd: true,
      tabPane: "0",
      name: null,
      ServicerID: null,
      ServicerEmployeeID: null,
      // employeeID: null,
      customerLevelID: null,
      customerSourceID: null,
      AppointmentState: false, // 是否可预约
      openBillState: false, // 是否可开单
      appointmentDialogShow: false, // 预约弹框
      IsMustEmployee: true, // 是否必选接待人
      selectProjectDialogState: false, // 选择项目弹框展示状态
      toShopDate: null,
      filterText: "",
      timeArr: [], // 预约时长数据
      appointmentProjectList: [], // 项目列表
      projectList: [], // 项目列表数据
      StartTime: "",
      EndTime: "",
      StartTimeData: "", // 开始时间(选择时间)
      EndTimeData: "", // 结束时间(选择时间)
      employeeList: [], // 可预约员工列表
      Period: "", // 预约间隔
      defaultCheckedKeysApplyApp: [], // 默认选中的适用项目节点
      selectedTableData: [], // 选中项目
      interval: 0,
      periodArr: [],
      tableData: [],
      fileList1: [],
      fit: "",
      // channelList: [],
      /* employee: [], //营销顾问 */
      customerLevel: [], //顾客等级
      customerSource: [], //顾客来源
      // customerIntroducer: [], //顾客介绍人
      customerServicer: [], //服务人员
      allCustomerServicer: [],
      ServicerEmployeeList: [],
      allServicerEmployeeList: [],
      ServicerList: [],
      CusPageNum: 1,
      CusTotal: "",
      customerTag: [], //顾客标签
      editCustomerTag: [],
      tagList: [], //所有标签
      customerID: null,
      circleUrl: "https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png",
      skinReportImg: require("@/assets/img/beauty-3D.png"),
      customerDetail: "",
      tagName: "",
      defaultProps: {
        children: "Child",
        label: "Name",
      },
      expireTimeOption: {
        disabledDate(date) {
          //disabledDate 文档上：设置禁用状态，参数为当前日期，要求返回 Boolean
          return date.getTime() < Date.now() - 24 * 60 * 60 * 1000;
        },
      }, // 预约时间配置
      // appointmentRuleForm: {
      //   ID: "", // 预约ID
      //   CustomerID: "", // 顾客ID
      //   EmployeeID: "", // 接待人ID
      //   AppointmentDate: new Date(), // 预约日期
      //   AppointmentTime: "", // 预约时间
      //   Type: "", // 接待人状态
      //   Status: "10", // 审批状态
      //   Period: "", // 预约时长
      //   Remark: "", // 备注
      // }, // 预约
      // appointmentRules: {
      //   EmployeeID: [{ required: true, message: "请选择接待人", trigger: "change" }],
      //   AppointmentDate: [{ required: true, message: "请选择预约日期", trigger: "change" }],
      //   AppointmentTime: [{ required: true, message: "请选择预约时间", trigger: "change" }],
      //   Type: [{ required: true, message: "请选择接待人状态", trigger: "change" }],
      // },
      //需要给分页组件传的信息
      paginations: {
        page: 1, // 当前位于哪页
        total: 0, // 总数
        page_size: 10, // 1页显示多少条
        layout: "total, prev, pager, next,jumper", // 翻页属性
      },
      Birthday: "",
      pickerOptions: {
        shortcuts: [
          {
            text: "今天生日",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "最近一周",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              end.setTime(start.getTime() + 3600 * 1000 * 24 * 7);
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "最近一个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              end.setTime(start.getTime() + 3600 * 1000 * 24 * 30);
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "最近三个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              end.setTime(start.getTime() + 3600 * 1000 * 24 * 90);
              picker.$emit("pick", [start, end]);
            },
          },
        ],
      },
      
      pickerOptions_: {
        shortcuts: [
          {
            text: "最近7天",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(end.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "最近30天",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(end.getTime() - 3600 * 1000 * 24 * 30);
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "最近90天",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(end.getTime() - 3600 * 1000 * 24 * 90);
              picker.$emit("pick", [start, end]);
            },
          },
        ],
      },
      toShopPickerOptions: {
        shortcuts: [
          {
            text: "一个月",
            onClick(picker) {
              picker.$emit("pick", [dayjs().subtract(1, "month").toDate(), dayjs().toDate()]);
            },
          },
          {
            text: "三个月",
            onClick(picker) {
              picker.$emit("pick", [dayjs().subtract(3, "month").toDate(), dayjs().toDate()]);
            },
          },
          {
            text: "半年",
            onClick(picker) {
              picker.$emit("pick", [dayjs().subtract(6, "month").toDate(), dayjs().toDate()]);
            },
          },

          {
            text: "一年",
            onClick(picker) {
              picker.$emit("pick", [dayjs().subtract(12, "month").toDate(), dayjs().toDate()]);
            },
          },
        ],
      },
      pickerOptionsMemberOnDate: {
        shortcuts: [
          {
            text: "今天",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "最近一周",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              end.setTime(start.getTime() + 3600 * 1000 * 24 * 7);
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "最近一个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              end.setTime(start.getTime() + 3600 * 1000 * 24 * 30);
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "最近三个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              end.setTime(start.getTime() + 3600 * 1000 * 24 * 90);
              picker.$emit("pick", [start, end]);
            },
          },
        ],
      },
      customInfoEdit: "", //顾客信息编辑权限
      customerEntityBool: "", //是否是归属门店
      accountInfo: {}, //顾客存量余额信息
      itemID: [],
      MemberOnDate: [], //成为会员日期
    };
  },
  computed: {
  },
  watch: {
    filterText(val) {
      var that = this;
      that.$refs.treeRef.filter(val);
    },
  },

  methods: {
    /**  服务人员处理  */
    getServicerEmpNames(ServicerEmpList) {
      if (!ServicerEmpList) {
        return "";
      }
      return ServicerEmpList.map((val) => (val ? val.Name : "")).join(", ");
    },
    change() {
      this.$forceUpdate();
    },
    handleClick() {
      var that = this;
      var tabPane = this.tabPane;
      switch (tabPane) {
        case "1":
          that.$refs.customerAccount.customerID = that.customerID;
          that.$refs.customerAccount.activeName = "0";
          that.$refs.customerAccount.handleClick();
          break;
        case "2":
          that.$refs.customerbill.customerID = that.customerID;
          that.$refs.customerbill.searchSaleBill();
          that.$refs.customerbill.clearSearchData();
          break;
        case "4":
          that.$refs.appointmentRecord.customerID = that.customerID;
          that.$refs.appointmentRecord.getAppointmentRecordList();
          break;
        case "5":
          that.$refs.nursingLog.customerID = that.customerID;
          that.$refs.nursingLog.clearListData();
          that.$refs.nursingLog.getNursingLogList();
          break;
      }
    },
    // //状态显示转换
    formatGender: function (row) {
      switch (row.Gender) {
        case "1":
          return "男";
        case "2":
          return "女";
        case "0":
          return "未知";
      }
    },
    handleRemove() {},
    auxiliaryUpload() {},
    onBeforeUploadImage() {},
    exceed() {},

    // 顾客来源
    CustomerSourceData: function () {
      var that = this;
      var params = {
        Name: "",
        Active: true,
      };
      APICustomerSource.getCustomerSource(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.customerSource = res.Data;
            that.customerSource.unshift({
              ID: 0,
              Name: "无来源",
            });
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },
    // 顾客等级
    CustomerLevelData: function () {
      var that = this;
      var params = {
        Name: "",
        Active: true,
      };
      APICustomerLevel.getCustomerLevel(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.customerLevel = res.Data;
            that.customerLevel.unshift({
              ID: 0,
              Name: "无等级",
            });
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },
    // 新增获取服务人员
    getCustomerServicer() {
      let that = this;
      let params = {};
      API.getCustomerServicer(params).then((res) => {
        if (res.StateCode == 200) {
          that.customerServicer = res.Data;
        } else {
          that.$message.error({
            message: res.Message,
            duration: 2000,
          });
        }
      });
    },
    /* 获取所有服务人员 */
    getAllCustomerServicer() {
      let that = this;
      let params = {};
      let array = [];
      let arr = [];
      API.getAllCustomerServicer(params).then((res) => {
        if (res.StateCode == 200) {
          that.allCustomerServicer = res.Data;
          that.allCustomerServicer.forEach((item) => {
            that.ServicerEmployeeList.push(...item.ServicerEmpList);
            that.allServicerEmployeeList = that.ServicerEmployeeList;
            item.ServicerListArr = [];
          });
          for (let i = 0; i < that.ServicerEmployeeList.length; i++) {
            if (array.indexOf(that.ServicerEmployeeList[i].ID) === -1) {
              array.push(that.ServicerEmployeeList[i].ID);
              arr.push(that.ServicerEmployeeList[i]);
            }
          }
          that.ServicerEmployeeList = arr;
          that.ServicerList = that.ServicerEmployeeList;
        } else {
          that.$message.error({
            message: res.Message,
            duration: 2000,
          });
        }
      });
    },

    // 顾客标签
    customerTagData: function () {
      var that = this;
      var params = {
        ID: that.customerDetail.ID,
      };
      API.getCustomerTag(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.customerTag = res.Data;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },
    // 标签列表
    tagData: function () {
      var that = this;
      APITagLibrary.customerTagLibraryAll()
        .then((res) => {
          if (res.StateCode == 200) {
            that.tagList = res.Data;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },
    // 数据显示
    handleSearch: function () {
      let that = this;
      that.paginations.page = 1;
      that.ServicerList = that.allServicerEmployeeList.filter((item) => {
        return item.ServicerID == that.ServicerID;
      });
      if (!that.ServicerID) {
        that.ServicerList = [];
        that.ServicerList = that.ServicerEmployeeList;
      }
      that.search();
    },
    // 数据显示
    search: function () {
      let that = this;
      that.loading = true;
      var params = {
        Name: that.name,
        CustomerLevelID: that.customerLevelID,
        CustomerSourceID: that.customerSourceID,
        PageNum: that.paginations.page,
        StartDate: that.Birthday ? that.Birthday[0] : "",
        EndDate: that.Birthday ? that.Birthday[1] : "",
        ServicerID: that.ServicerID,
        ServicerEmployeeID: that.ServicerEmployeeID,
        ToShopStartDate: that.toShopDate ? that.toShopDate[0] : null,
        ToShopEndDate: that.toShopDate ? that.toShopDate[1] : null,
        MemberOnStartDate: that.MemberOnDate && that.MemberOnDate.length ? that.MemberOnDate[0] : null,
        MemberOnEndDate: that.MemberOnDate && that.MemberOnDate.length > 1 ? that.MemberOnDate[1] : null,
        IntroducerName: that.searchIntroducerName, //介绍人
        ChannelName: that.searchChannelName, //渠道

        CreatedOnStartDate: that.createdOnDate && that.createdOnDate.length ? that.createdOnDate[0] : null,
        CreatedOnEndDate: that.createdOnDate && that.createdOnDate.length > 1 ? that.createdOnDate[1] : null,
      };
      API.getCustomer(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.tableData = res.List;
            that.paginations.total = res.Total;
            that.paginations.page_size = res.PageSize;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },
    // 上下分页
    handleCurrentChange(page) {
      var that = this;
      that.paginations.page = page;
      that.search();
    },
    // 新增顾客
    showAddDialog: function () {
      var that = this;

      that.dialogVisible = true;
    },
    // 顾客详情
    customerDetailClick: function (row) {
      this.customerID = row.ID;
      this.customerDetailVisible = true;
    },
    /**   新增客户成功 */
    addCustomerSuccess() {
      let that = this;
      that.search();
    },

    // 标签
    tagClick: function () {
      var that = this;
      that.editCustomerTag = Object.assign([], that.customerTag);
      that.tagType();
      that.dialogTag = true;
    },
    tagType: function () {
      var that = this;
      that.tagList.forEach(function (item) {
        item.type = "info";
        that.editCustomerTag.forEach(function (tag) {
          if (item.ID == tag.ID) {
            item.type = "primary";
          }
        });
      });
    },
    // 删除标签
    removeTag: function (index) {
      var that = this;
      that.editCustomerTag.splice(index, 1);
      that.tagType();
    },
    // 选择标签
    tagSelectClick: function (row) {
      var that = this;
      if (row.type == "info") {
        that.editCustomerTag.push(row);
      }
      that.tagType();
    },
    // 添加标签
    addTagClick: function () {
      var that = this;
      var params = {
        Name: that.tagName,
      };
      APITagLibrary.customerTagLibraryCreate(params)
        .then(function (res) {
          if (res.StateCode === 200) {
            that.editCustomerTag.push(res.Data);
            that.tagList.push(res.Data);
            that.tagType();
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.modalLoading = false;
        });
    },

    // 标签保存
    tagSaveClick: function () {
      var that = this;
      that.modalLoading = true;
      var TagLibrary = Enumerable.from(that.editCustomerTag)
        .select((val) => val.ID)
        .toArray();
      var params = {
        ID: that.customerDetail.ID,
        TagLibrary: TagLibrary,
      };
      API.updateCustomerTagLibrary(params)
        .then(function (res) {
          if (res.StateCode === 200) {
            that.dialogTag = false;
            that.customerTagData();
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.modalLoading = false;
        });
    },
    /**  跟进编辑  */
    editFollowUp() {
      var that = this;
      that.editFollowVisble = true;
    },
    followSaveClick() {
      var that = this;
      that.editFollowVisble = false;
    },
    /** 顾客 开单   */
    navCreateBill() {
      var that = this;
      that.dialogDetail = false;
      that.$router.push({
        path: "/Order/Bill",
        name: "Bill",
        params: { customerID: that.customerID },
      });
    },
    /**    */
    gotoOrderCreated(row) {
      var that = this;
      that.dialogDetail = false;
      that.$router.push({
        path: "/Order/Bill",
        name: "Bill",
        params: { customerID: row.ID },
      });
    },
    
    /** 打开面诊单页面 */
    openConsultationForm(row) {
      const routeData = this.$router.resolve({
        path: "/Customer/ConsultationForm",
        query: { customerID: row.ID }
      });
      window.open(routeData.href, '_blank');
    },

    // 预约
    // clickAppointment() {
    //   var that = this;
    //   console.log("000000");
    //   // that.dialogDetail = false;
    //   that.appointmentRuleForm.ID = "";
    //   that.appointmentRuleForm.CustomerID = "";
    //   that.appointmentRuleForm.EmployeeID = "";
    //   that.appointmentRuleForm.AppointmentDate = new Date();
    //   that.appointmentRuleForm.AppointmentTime = "";
    //   that.appointmentRuleForm.Type = "10";
    //   that.appointmentRuleForm.Status = "10";
    //   that.appointmentRuleForm.Period = that.timeArr[0].value;
    //   that.appointmentRuleForm.Remark = "";
    //   that.appointmentProjectList = [];

    //   that.appointmentDialogShow = true;
    // },
    // 获取预约时长
    // getDateAppointmentIntervalList(period) {
    //   var that = this;
    //   var startTimestamp = new Date(that.currentDate + " " + "00:" + period).getTime();
    //   var endTimestamp = new Date(that.currentDate + " " + "08:00").getTime();
    //   var periodTimestamp = Number(period) * 60 * 1000; // 间隔的毫秒
    //   var interValue = period;
    //   for (var index = startTimestamp; index <= endTimestamp; index += periodTimestamp) {
    //     if (interValue < 60) {
    //       that.timeArr.push({
    //         time: date.formatDate.format(new Date(index), "mm分钟"), // 时间轴显示字符串
    //         value: interValue,
    //       });
    //     } else {
    //       that.timeArr.push({
    //         time: date.formatDate.format(new Date(index), "h小时mm分钟"), // 时间轴显示字符串
    //         value: interValue,
    //       });
    //     }
    //     interValue += period;
    //   }
    // },
    // 获取预约配置
    // getAppointmentConfig() {
    //   var that = this;
    //   that.loading = true;
    //   var params = {};
    //   var period = 0;
    //   var dataTime = [];
    //   APIConfig.appointmentConfig(params)
    //     .then((res) => {
    //       if (res.StateCode == 200) {
    //         that.StartTime = res.Data.StartTime;
    //         that.EndTime = res.Data.EndTime;
    //         that.IsMustEmployee = res.Data.IsMustEmployee;
    //         that.interval = res.Data.Period;
    //         that.Period = "00:" + res.Data.Period;
    //         var number = parseInt(60 / res.Data.Period);
    //         for (let i = 0; i <= number - 1; i++) {
    //           period += res.Data.Period;
    //           dataTime.push(period);
    //         }
    //         that.periodArr = dataTime;

    //         that.getDateAppointmentIntervalList(res.Data.Period);
    //       } else {
    //         that.$message.error({
    //           message: res.Message,
    //           duration: 2000,
    //         });
    //       }
    //     })
    //     .finally(function () {
    //       that.loading = false;
    //     });
    // },
    // 获取可预约员工列表
    // getEmployeeList() {
    //   var that = this;
    //   that.loading = true;
    //   var params = {};
    //   APIAppointment.getEmployeeList(params)
    //     .then((res) => {
    //       if (res.StateCode == 200) {
    //         that.employeeList = res.Data;
    //       } else {
    //         that.$message.error({
    //           message: res.Message,
    //           duration: 2000,
    //         });
    //       }
    //     })
    //     .finally(function () {
    //       that.loading = false;
    //     });
    // },
    // 递归
    setRecursion(data) {
      var that = this;
      for (let i = 0; i <= data.length - 1; i++) {
        if (data[i].IsProject) {
          data[i].PID = "1" + data[i].ID;
        } else {
          data[i].PID = "0" + data[i].ID;
        }
        if (data[i].Child) {
          that.setRecursion(data[i].Child);
        }
      }
    },
    // 获取项目列表
    getProjectList() {
      var that = this;
      that.loading = true;
      var params = {
        Name: "",
      };
      APIAppointment.getProjectList(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.setRecursion(res.Data);
            // that.projectList = res.Data;
            that.projectList = Enumerable.from(res.Data)
              .where((i) => {
                if (!i.IsProject) {
                  i.Child = Enumerable.from(i.Child)
                    .where((i) => {
                      return !i.IsProject && i.Child.length > 0;
                    })
                    .toArray();
                }
                return !i.IsProject && i.Child.length > 0;
              })
              .toArray();
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },
    // 添加预约项目
    addAppointmentProject() {
      var that = this;
      that.selectedTableData = Object.assign([], that.appointmentProjectList);
      that.defaultCheckedKeysApplyApp = [];
      that.selectProjectDialogState = true;
      that.$nextTick(() => {
        var defaultCheckedKeys = Enumerable.from(that.appointmentProjectList)
          .select((val) => "1" + val.ID)
          .toArray();
        that.$refs.treeRef.setCheckedKeys(defaultCheckedKeys);
      });
    },
    // 选择适用项目事件
    selectApplicableItems(item, list) {
      var that = this;
      var timeCardProject = Enumerable.from(list.checkedNodes)
        .where(function (i) {
          return i.IsProject;
        })
        .select((item) => ({
          ID: item.ID,
          Name: item.Name,
          PID: item.PID,
          ParentID: item.ParentID,
          Price: item.Price,
          ProjectCategoryName: item.ProjectCategoryName,
          TreatTime: item.TreatTime,
        }))
        .toArray();
      that.selectedTableData = timeCardProject;
    },
    // 选择项目确认
    confirmProjectSelect() {
      var that = this;
      // var appointmentProjectList = Object.assign([], that.appointmentProjectList);
      var selectedTableData = Object.assign([], that.selectedTableData);
      if (that.selectedTableData.length == 0) {
        that.$message.error("请选择项目");
        return false;
      } else {
        var totalPeriod = 0;
        that.appointmentProjectList = Object.assign([], selectedTableData);
        that.selectProjectDialogState = false;
        that.appointmentProjectList.forEach((val) => {
          totalPeriod += val.TreatTime;
        });
        for (let i = 0; i <= that.timeArr.length - 1; i++) {
          if (that.timeArr[i].value >= totalPeriod) {
            that.appointmentRuleForm.Period = that.timeArr[i].value;
            break;
          }
        }
      }
    },
    // 删除所选中的适用项目
    deleteSelectRow(index) {
      var that = this;
      that.selectedTableData.splice(index, 1);
      that.$nextTick(() => {
        var defaultCheckedKeys = Enumerable.from(that.selectedTableData)
          .select((val) => val.PID)
          .toArray();
        that.$refs.treeRef.setCheckedKeys(defaultCheckedKeys);
      });
    },
    // 选择时间
    getTimeArr() {
      var that = this;
      var currentDate = new Date();

      let selTime = new Date(date.formatDate.format(that.appointmentRuleForm.AppointmentDate, "YYYY-MM-DD")).getTime();

      let currentTime = new Date(date.formatDate.format(currentDate, "YYYY-MM-DD")).getTime();

      if (selTime == currentTime) {
        // 今天
        let minutes = parseInt(date.formatDate.format(currentDate, "mm"));
        var startIndex = parseInt(minutes / that.interval);
        var tempMinutes = that.periodArr[startIndex];

        if (tempMinutes == 60) {
          let hour = date.formatDate.format(new Date(new Date().setHours(new Date().getHours() + 1)), "hh");
          that.StartTimeData = hour + ":" + "00";
          that.EndTimeData = that.EndTime;
        } else {
          let hours = date.formatDate.format(currentDate, "hh");
          that.StartTimeData = hours + ":" + tempMinutes;
          that.EndTimeData = that.EndTime;
        }
      } else if (selTime > currentTime) {
        that.StartTimeData = that.StartTime;
        that.EndTimeData = that.EndTime;
      }
    },

    // 选择时间
    // getTimeArr() {
    //   var that = this;
    //   var interval = that.interval;
    //   var currentDate = new Date();
    //   var StartTimeData = '';
    //   var EndTimeData = '';
    //   var currentHours = currentDate.getHours();
    //   var currentMinutes = currentDate.getMinutes();
    //   var startIndex = parseInt(currentMinutes / interval);
    //   var startminutes = that.periodArr[startIndex];

    //   var currentYear = currentDate.getFullYear();
    //   var currentMonth = currentDate.getMonth() + 1;
    //   var currentDay = currentDate.getDate();
    //   var todayDate = currentYear + '-' + (currentMonth >= 10 ? currentMonth : '0' + currentMonth) + '-' + (currentDay >= 10 ? currentDay : '0' + currentDay);

    //   var beforeTime = new Date(that.currentDate).getTime();
    //   var currentTime = new Date(todayDate).getTime();

    //   if (beforeTime == currentTime) {
    //     if (startminutes == 60) {
    //       currentHours += 1;
    //       startminutes = Number('00');
    //     }
    //     StartTimeData = currentHours >= 10 ? currentHours + ':' + (startminutes >= 10 ? startminutes : `0` + startminutes) : `0${currentHours}` + ':' + (startminutes >= 10 ? startminutes : `0` + startminutes);
    //     that.StartTimeData = StartTimeData;
    //     that.EndTimeData = that.EndTime;
    //   } else if (beforeTime > currentTime) {
    //     that.StartTimeData = that.StartTime;
    //     that.EndTimeData = that.EndTime;
    //   }
    // },
    // 选择时间确认
    confirmTime(date) {
      var that = this;
      var dateArr = date.split(":");
      var currentDate = "";
      var dateTime = "";
      currentDate = Number(dateArr[0]) >= 10 ? Number(dateArr[0]) + ":" + (Number(dateArr[1]) >= 10 ? Number(dateArr[1]) : `0` + Number(dateArr[1])) : `0${Number(dateArr[0])}` + ":" + (Number(dateArr[1]) >= 10 ? Number(dateArr[1]) : `0` + Number(dateArr[1]));
      var AppointmentDate = that.appointmentRuleForm.AppointmentDate.split(" ");
      dateTime = AppointmentDate[0] + " " + currentDate + ":00";
      that.appointmentRuleForm.AppointmentDate = dateTime;
    },
    // 保存预约
    // saveAppointment() {
    //   var that = this;
    //   var AppointmentTime = that.appointmentRuleForm.AppointmentTime;
    //   that.$refs["appointmentRuleForm"].validate((valid) => {
    //     if (valid) {
    //       if (AppointmentTime == undefined || AppointmentTime == null || AppointmentTime == "") {
    //         this.$message({
    //           type: "error",
    //           message: "请选择预约时间",
    //         });
    //       } else {
    //         that.createAppointment();
    //       }
    //     }
    //   });
    // },
    // 创建预约
    // createAppointment() {
    //   var that = this;
    //   var appointmentProjectList = [];
    //   var obj = {};
    //   var params = {};

    //   var appointmentDate = date.formatDate.format(new Date(that.appointmentRuleForm.AppointmentDate), "YYYY-MM-DD");
    //   var AppointmentTime = that.appointmentRuleForm.AppointmentTime;
    //   var AppointmentDate = appointmentDate + " " + AppointmentTime;

    //   that.loading = true;

    //   obj.EmployeeID = that.appointmentRuleForm.EmployeeID;
    //   obj.CustomerID = that.customerID;
    //   obj.AppointmentDate = AppointmentDate;
    //   obj.Period = that.appointmentRuleForm.Period;
    //   obj.Remark = that.appointmentRuleForm.Remark;
    //   obj.Type = that.appointmentRuleForm.Type;

    //   that.appointmentProjectList.forEach((val) => {
    //     var obj = {};
    //     obj.ProjectID = val.ID;
    //     appointmentProjectList.push(obj);
    //   });
    //   params = Object.assign({}, obj);
    //   params.Project = appointmentProjectList;

    //   APIAppointment.appointmentBillCreate(params)
    //     .then((res) => {
    //       if (res.StateCode == 200) {
    //         that.$message.success({
    //           message: "成功创建预约",
    //           duration: 2000,
    //         });
    //         that.appointmentDialogShow = false;
    //       } else {
    //         that.$message.error({
    //           message: res.Message,
    //           duration: 2000,
    //         });
    //       }
    //     })
    //     .finally(function () {
    //       that.loading = false;
    //     });
    // },
    // 适用项目弹框搜索事件
    // filterNode(value, data) {
    //   if (!value) return true;
    //   return data.Name.indexOf(value) !== -1;
    // },

    /**  上传头像请求  */
    updateCustomerUploadImage(base64) {
      let that = this;
      let params = {
        CustomerImage: base64,
        CustomerID: that.customerDetail.ID,
      };
      API.updateCustomerUploadImage(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.customerDetail.Avatar = res.Message;
            that.search();
          }
        })
        .finally(() => {});
    },
    // 顾客存量余额信息
    AccountInfo() {
      const that = this;
      API.AccountInfo({ CustomerID: that.customerDetail.ID }).then((res) => {
        if (res.StateCode == 200) {
          that.accountInfo = res.Data;
        } else {
          this.$message.error({
            message: res.Message,
            duration: 2000,
          });
        }
      });
    },
    //
    beforeAvatarUpload(file) {
      let that = this;
      let isFileType = false;
      if (file.type === "image/jpg" || file.type === "image/png" || file.type === "image/jpeg") {
        isFileType = true;
      }
      // const isLt2M = file.size / 1024 < 200;

      if (!isFileType) {
        this.$message.error("上传头像图片只能是 JPG 格式!");
        return false;
      }
      // if (!isLt2M) {
      //   that.$message.error("上传图片大小不能超过 200kb!");
      //   return false;
      // }

      let reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = function (evt) {
        let base64 = evt.target.result;
        that.updateCustomerUploadImage(base64);
      };
      return false;
    },

    /* 导出 - 显示手机号 */
    customer_Export(type) {
      let that = this;
      let params = {
        Name: that.name,
        CustomerLevelID: that.customerLevelID,
        CustomerSourceID: that.customerSourceID,
        PageNum: that.paginations.page,
        StartDate: that.Birthday ? that.Birthday[0] : "",
        EndDate: that.Birthday ? that.Birthday[1] : "",
        ServicerID: that.ServicerID,
        ServicerEmployeeID: that.ServicerEmployeeID,
        ToShopStartDate: that.toShopDate ? that.toShopDate[0] : null,
        ToShopEndDate: that.toShopDate ? that.toShopDate[1] : null,
        MemberOnStartDate: that.MemberOnDate && that.MemberOnDate.length ? that.MemberOnDate[0] : null,
        MemberOnEndDate: that.MemberOnDate && that.MemberOnDate.length > 1 ? that.MemberOnDate[1] : null,
        IntroducerName: that.searchIntroducerName, //介绍人
        ChannelName: that.searchChannelName, //渠道
      };
      that.downloadLoading = true;
      API[type](params)
        .then((res) => {
          this.$message.success({
            message: "正在导出",
            duration: "4000",
          });
          const link = document.createElement("a");
          let blob = new Blob([res], { type: "application/octet-stream" });
          link.style.display = "none";
          link.href = URL.createObjectURL(blob);
          link.download = "客户列表.xlsx"; //下载的文件名
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
        })
        .finally(function () {
          that.downloadLoading = false;
        });
    },
  },
  beforeRouteEnter(to, from, next) {
    next((vm) => {
      vm.AppointmentState = vm.$permission.routerPermission("/Appointment/AppointmentView");
      vm.openBillState = vm.$permission.routerPermission("/Order/Bill");
      vm.customInfoEdit = vm.$permission.permission(to.meta.Permission, "iBeauty-Customer-Customer-ModifyNotBelongEntityCustomer");
      vm.customTagLibrary = vm.$permission.permission(to.meta.Permission, "iBeauty-Customer-Customer-CustomTagLibrary");
      vm.isDeleteFile = vm.$permission.permission(to.meta.Permission, "iBeauty-Customer-Customer-DeleteFile");
      vm.isElectronicMedicalRecord = vm.$permission.permission(to.meta.Permission, "iBeauty-Customer-Customer-ElectronicMedicalRecord");
      vm.isShowChannel = vm.$permission.permission(to.meta.Permission, "iBeauty-Customer-Customer-Channel");
      vm.isCustomerExport = vm.$permission.permission(to.meta.Permission, "iBeauty-Customer-Customer-Export");
      vm.isCustomerPhoneNumberExport = vm.$permission.permission(to.meta.Permission, "iBeauty-Customer-Customer-PhoneNumberExport");
      vm.isModifyBelongEntity = vm.$permission.permission(to.meta.Permission, "iBeauty-Customer-Customer-ModifyBelongEntity");
      vm.isDeleteNursingLog = vm.$permission.permission(to.meta.Permission, "iBeauty-Customer-Customer-DeleteNursingLog");

      vm.isModifyCustomerLevel = vm.$permission.permission(to.meta.Permission, "iBeauty-Customer-Customer-ModifyCustomerLevel");
      vm.isCustomerBasicInformationModify = vm.$permission.permission(to.meta.Permission, "iBeauty-Customer-Customer-ModifyCustomerBasicInformation");
      vm.isCustomerServicerModify = vm.$permission.permission(to.meta.Permission, "iBeauty-Customer-Customer-ModifyCustomerServicer");


    });
  },
  mounted() {
    var that = this;

    that.AppointmentState = that.$permission.routerPermission("/Appointment/AppointmentView");
    that.openBillState = that.$permission.routerPermission("/Order/Bill");
    that.customTagLibrary = that.$permission.permission(that.$route.meta.Permission, "iBeauty-Customer-Customer-CustomTagLibrary");
    that.customInfoEdit = that.$permission.permission(that.$route.meta.Permission, "iBeauty-Customer-Customer-ModifyNotBelongEntityCustomer");
    that.isDeleteFile = that.$permission.permission(that.$route.meta.Permission, "iBeauty-Customer-Customer-DeleteFile");
    that.isShowChannel = that.$permission.permission(that.$route.meta.Permission, "iBeauty-Customer-Customer-Channel");
    that.isElectronicMedicalRecord = that.$permission.permission(that.$route.meta.Permission, "iBeauty-Customer-Customer-ElectronicMedicalRecord");
    that.isCustomerExport = that.$permission.permission(that.$route.meta.Permission, "iBeauty-Customer-Customer-Export");
    that.isCustomerPhoneNumberExport = that.$permission.permission(that.$route.meta.Permission, "iBeauty-Customer-Customer-PhoneNumberExport");
    that.isModifyBelongEntity = that.$permission.permission(that.$route.meta.Permission, "iBeauty-Customer-Customer-ModifyBelongEntity");
    that.isDeleteNursingLog = that.$permission.permission(that.$route.meta.Permission, "iBeauty-Customer-Customer-DeleteNursingLog");
    
    that.isModifyCustomerLevel = that.$permission.permission(that.$route.meta.Permission, "iBeauty-Customer-Customer-ModifyCustomerLevel");
    // that.isCreateMember = that.$permission.permission(that.$route.meta.Permission, "iBeauty-Customer-Customer-CreateMember");

    that.isCustomerBasicInformationModify = that.$permission.permission(that.$route.meta.Permission, "iBeauty-Customer-Customer-ModifyCustomerBasicInformation");
    that.isCustomerServicerModify = that.$permission.permission(that.$route.meta.Permission, "iBeauty-Customer-Customer-ModifyCustomerServicer");

    var time = new Date();
    that.currentDate = date.formatDate.format(new Date(time), "YYYY-MM-DD");

    that.CustomerSourceData();
    that.CustomerLevelData();
    that.tagData();
    that.handleSearch();
    that.getAllCustomerServicer();
  },
};
</script>

<style lang="scss">
.Customer {
  .right_item {
    background: #f8f8f8;
  }

  .margin-bottom {
    margin-bottom: 10px;
  }

  .ui-sex-female {
    background: "url(" + require("@/assets/img/logo2.png") + ")";
    background-size: 8px 12px;
  }

  .backColor_f8 {
    background-color: #f8f8f8;
  }

  .clamp1 {
    display: inline-block;
    white-space: nowrap;
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

.vue-treeselect__control {
  height: 32px;

  .vue-treeselect__value-container {
    min-width: 215px;
  }
}

.vue-treeselect__placeholder,
.vue-treeselect__single-value {
  line-height: 32px;
}

.custom-add-customer-class {
  .customer-add-scrollbar {
    .el-scrollbar__wrap {
      overflow-x: hidden;
    }

    .el-scrollbar__bar {
      opacity: 0;
    }
  }
}
</style>
