.plaintext[data-v-698b7bac] {
    opacity: .8;
    cursor: not-allowed;
    min-width: 3em;
    max-width: 100%
}

.plaintext.editable[data-v-698b7bac] {
    opacity: 1;
    cursor: unset;
    -moz-user-modify: read-write-plaintext-only;
    -webkit-user-modify: read-write-plaintext-only
}

.plaintext[data-v-698b7bac]:empty:before {
    opacity: .5;
    pointer-events: none;
    content: attr(placeholder)
}

.mktageditor .tag-form input {
    padding: 6px !important;
    margin: 6px 0
}

.mktageditor label {
    cursor: pointer;
    display: flex;
    align-items: center;
    font-size: 14px
}

    .mktageditor label input {
        margin: 0 .2em 0 0
    }

.tag-config {
    display: grid;
    grid-template-columns: 1fr 1fr;
    display: flex;
    align-items: center
}

    .tag-config label {
        margin-right: 15px
    }

    .tag-config .tag-btn {
        color: red;
        cursor: pointer;
        font-size: 12px
    }

.mkb-tag {
    padding: 5px 0
}

    .mkb-tag .tag-name {
        padding: 4px 10px
    }

.form[data-v-9f9c2948] {
    padding: 20px 20px 40px;
    width: 450px;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none
}

.form button[data-v-9f9c2948] {
    padding: 7px
}

.form button input[data-v-9f9c2948] {
    padding: 0;
    margin: 0
}

.form label[data-v-9f9c2948] {
    cursor: pointer
}

.form button[data-v-9f9c2948] {
    margin-top: 30px;
    width: 100%
}

.mktageditor {
    display: flex;
    flex-direction: column
}

    .mktageditor .tag-form {
        display: flex;
        flex: 1
    }

        .mktageditor .tag-form input {
            flex: 1;
            padding: 3px 6px !important;
            margin: 3px 0;
            border-radius: var(--small-radius)
        }

    .mktageditor .tags {
        max-height: 400px;
        overflow: auto;
        display: flex;
        flex-wrap: wrap;
        font-size: 14px;
        align-items: center
    }

    .mktageditor .tag-btn {
        font-size: 12px;
        margin-left: .5em;
        color: #ccc;
        transition: all .3s;
        cursor: pointer
    }

        .mktageditor .tag-btn:before {
            display: inline-block;
            transform: scale(.8)
        }

        .mktageditor .tag-btn:hover {
            color: var(--danger-color)
        }

.mkb-tag {
    margin: 3px 5px 3px 0;
    padding: 6px 10px;
    position: relative;
    z-index: 1;
    line-height: 1.2;
    white-space: nowrap;
    display: flex;
    align-items: center;
    border-radius: var(--small-radius);
    justify-content: space-between;
    background: #eeedf0
}

    .mkb-tag .tag-name, .mkb-tag:hover {
        background: var(--hoverBg)
    }

    .mkb-tag .tag-name {
        border: 1px solid var(--theme-bg);
        border-radius: 3px;
        min-width: 3em;
        font-size: 14px;
        white-space: normal;
        margin-right: 20px
    }

.form[data-v-0e86f8b8] {
    padding: 20px 20px 40px;
    width: 450px;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none
}

.form button[data-v-0e86f8b8] {
    padding: 7px
}

.form button input[data-v-0e86f8b8] {
    padding: 0;
    margin: 0
}

.form label[data-v-0e86f8b8] {
    cursor: pointer
}

.form button[data-v-0e86f8b8] {
    margin-top: 30px;
    width: 100%
}

.form[data-v-112c29ed] {
    padding: 20px 20px;
    width: 430px;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none
}

.form input[type=radio][data-v-112c29ed] {
    padding: 0;
    margin: 0
}

.form label[data-v-112c29ed] {
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    font-size: 14px
}

    .form label[data-v-112c29ed]:not(:last-child) {
        margin-right: 15px
    }

    .form label[data-v-112c29ed] > :not(:last-child) {
        margin-right: 5px
    }

.form label.margin[data-v-112c29ed] {
    border: 1px solid #ddd;
    border-radius: 3px
}

.form label.margin span[data-v-112c29ed] {
    padding: 3px 5px;
    margin: 0
}

.form label.margin input[data-v-112c29ed] {
    outline: none;
    margin: 0;
    text-align: center;
    width: 2.2em;
    padding: 5px 3px;
    border-radius: 0;
    border-top: 0;
    border-bottom: 0;
    border-left: 1px solid #ddd;
    border-right: 1px solid #ddd
}

.form button[data-v-112c29ed] {
    padding: 7px;
    margin-top: 10px;
    width: 100%
}

@font-face {
    font-family: hms-fonticon;
    src: url(fonts/hms-fonticon.3bf76059.ttf) format("truetype"),url(fonts/hms-fonticon.37c7d0dc.woff) format("woff"),url(img/hms-fonticon.503969e1.svg#hms-fonticon) format("svg");
    font-weight: 400;
    font-style: normal;
    font-display: block
}

[class*=" hm-icon-"], [class^=hm-icon-] {
    font-family: hms-fonticon !important;
    speak: never;
    font-style: normal;
    font-weight: 400;
    font-variant: normal;
    text-transform: none;
    line-height: 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale
}

.hm-icon-dropdown:before {
    content: "\e929"
}

.hm-icon-sup:before {
    content: "\e928"
}

.hm-icon-pageheader:before {
    content: "\e927"
}

.hm-icon-design:before {
    content: "\e926"
}

.hm-icon-division:before {
    content: "\e925"
}

.hm-icon-group:before {
    content: "\e923"
}

.hm-icon-check:before {
    content: "\e922";
    color: #38a83d
}

.hm-icon-more-arrow:before {
    content: "\e921"
}

.hm-icon-teeth:before {
    content: "\e920"
}

.hm-icon-clear:before {
    content: "\e91f"
}

.hm-icon-sign1:before {
    content: "\e919"
}

.hm-icon-sign:before {
    content: "\e91a"
}

.hm-icon-sign2:before {
    content: "\e91b"
}

.hm-icon-paper:before {
    content: "\e912"
}

.hm-icon-symbol:before {
    content: "\e90e"
}

.hm-icon-edit-data:before {
    content: "\e90d"
}

.hm-icon-mark-range:before {
    content: "\e90c"
}

.hm-icon-human-body:before {
    content: "\e90b"
}

.hm-icon-chart:before {
    content: "\e90a"
}

.hm-icon-checkbox:before {
    content: "\e909"
}

.hm-icon-print:before {
    content: "\e908"
}

.hm-icon-hr:before {
    content: "\e907"
}

.hm-icon-img:before {
    content: "\e906"
}

.hm-icon-table:before {
    content: "\e916"
}

.hm-icon-align-left:before {
    content: "\ea77"
}

.hm-icon-align-center:before {
    content: "\ea78"
}

.hm-icon-align-right:before {
    content: "\ea79"
}

.hm-icon-align-justify:before {
    content: "\ea7a"
}

.hm-icon-pagebreak:before {
    content: "\e904"
}

.hm-icon-pagebreak1:before {
    content: "\e905"
}

.hm-icon-datetime:before {
    content: "\e901"
}

.hm-icon-input:before {
    content: "\e902"
}

.hm-icon-field:before {
    content: "\e903"
}

.hm-icon-style:before {
    content: "\e900"
}

.hm-icon-medical:before {
    content: "\e911"
}

.hm-icon-patient:before {
    content: "\e910"
}

.hm-icon-doctor:before {
    content: "\e90f"
}

.hm-icon-loading:before {
    content: "\e97f"
}

.hm-icon-close:before {
    content: "\e924"
}

.hm-icon-heading1:before {
    content: "\e913"
}

.hm-icon-heading2:before {
    content: "\e914"
}

.hm-icon-heading3:before {
    content: "\e915"
}

.hm-icon-heading4:before {
    content: "\e917"
}

.hm-icon-heading5:before {
    content: "\e918"
}

.hm-icon-polygon:before {
    content: "\e91c"
}

.hm-icon-fill:before {
    content: "\e91d"
}

.hm-icon-delete:before {
    content: "\e91e";
    color: #e52c2c
}

.hm-icon-bold:before {
    content: "\ea62"
}

.hm-icon-down:before {
    content: "\e985"
}

:root {
    --dialog-bg: #fff;
    --main-color: #000;
    --theme-bg: #0096fd;
    --hoverBg: rgba(0,151,255,0.1);
    --focusBg: rgba(0,151,255,0.25);
    --activeColor: #0096fd;
    --selectedBg: #0096fd;
    --successBg: #67c23a;
    --smallShadow: 0 0 4px 1px rgba(9,105,218,0.6);
    --focusShadow: 0 0 4px 1px rgba(9,105,218,0.6);
    --activedOutlin: 1px dashed #0096fd;
    --activedBorder: 1px dashed #0096fd;
    --main-border: 1px solid #e0e6ed;
    --small-padding: 4px;
    --small-radius: 2px;
    --danger-color: #f18985;
    --baseline-color: #bbb
}

.group {
    height: 100px;
    text-align: center;
    width: 210mm;
    display: flex;
    flex-direction: column;
    margin: 10px 0
}

    .group .group-item {
        -webkit-user-select: none;
        -moz-user-select: none;
        user-select: none;
        cursor: default;
        padding: 2px 4px 2px 6px;
        border: 1px solid #eee;
        border-radius: 3px;
        display: inline-flex;
        align-items: center
    }

        .group .group-item button {
            padding: 3px;
            border: none;
            cursor: pointer;
            background: none;
            margin-left: 10px
        }

            .group .group-item button:hover {
                background: #fff
            }

        .group .group-item:not(:last-child) {
            margin-right: 10px
        }

        .group .group-item:hover {
            background: rgba(18,165,18,.32941176470588235)
        }

    .group .group-content {
        padding: 10px;
        border: 1px dashed #ccc;
        flex: 1
    }

        .group .group-content.itemdrag {
            background: #d2efd2
        }

    .group .group-btn {
        padding: 5px;
        border: 1px solid #ccc;
        border-top: none;
        display: flex;
        align-items: center
    }

        .group .group-btn button {
            padding: 5px 10px;
            border: none;
            background: none;
            color: #fff;
            border-radius: 4px;
            cursor: pointer
        }

            .group .group-btn button:disabled {
                opacity: .4;
                cursor: not-allowed
            }

        .group .group-btn .group-name {
            display: inline-flex;
            align-items: stretch;
            flex: 1;
            justify-content: flex-end
        }

            .group .group-btn .group-name div {
                height: 1em;
                line-height: 1;
                align-self: center;
                font-size: 14px;
                color: #666
            }

            .group .group-btn .group-name select {
                min-width: 10em;
                color: #495057;
                background-color: #fff;
                border: 1px solid #ced4da;
                border-radius: 0
            }

                .group .group-btn .group-name select option {
                    padding: 6px
                }

        .group .group-btn input {
            padding: 5px 6px;
            border-radius: 5px 0 0 5px;
            border: 1px solid #ccc;
            border-right: 0
        }

        .group .group-btn .cancalgroup {
            background: red;
            margin-right: 20px
        }

        .group .group-btn .addgroup {
            background: green;
            border-top-left-radius: 0;
            border-bottom-left-radius: 0
        }

input::-webkit-inner-spin-button, input::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0
}

input[type=number] {
    -webkit-appearance: textfield;
    -moz-appearance: textfield;
    appearance: textfield
}

.mkb-block-wrapper[align=center] {
    margin-top: 10px !important;
    margin-bottom: 10px !important
}

.hms-editor-tool-btn {
    border: none;
    padding: 4px;
    font-size: 16px;
    cursor: pointer;
    border-radius: 3px;
    background: none
}

    .hms-editor-tool-btn:hover {
        background: #eee
    }

    .hms-editor-tool-btn select {
        border: none;
        background: none
    }

.hms-editor-wrapper {
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center
}

    .hms-editor-wrapper * {
        box-sizing: border-box;
        outline: none
    }

    .hms-editor-wrapper.show {
        display: flex
    }

    .hms-editor-wrapper .hms-editor-header {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        flex-direction: column;
        justify-content: space-around;
        background: #fff;
        position: relative;
        z-index: 1;
        margin-bottom: 15px;
        border-width: 0 1px 1px;
        border-style: solid;
        border-color: #eee
    }

    .hms-editor-wrapper .hms-btn-group {
        padding: 6px 8px;
        border-radius: 4px;
        display: flex;
        align-items: center
    }

        .hms-editor-wrapper .hms-btn-group .hms-text-icon {
            display: inline-flex;
            flex-direction: column;
            align-items: center;
            cursor: pointer;
            margin: 0 9px
        }

            .hms-editor-wrapper .hms-btn-group .hms-text-icon span {
                margin-top: 5px
            }

        .hms-editor-wrapper .hms-btn-group button {
            white-space: nowrap;
            line-height: unset;
            border: none;
            padding: 4px;
            font-size: 16px;
            cursor: pointer;
            border-radius: 3px;
            background: none
        }

            .hms-editor-wrapper .hms-btn-group button:hover {
                background: #eee
            }

            .hms-editor-wrapper .hms-btn-group button select {
                border: none;
                background: none
            }

            .hms-editor-wrapper .hms-btn-group button div {
                margin-bottom: 5px
            }

            .hms-editor-wrapper .hms-btn-group button span {
                color: #888;
                font-size: 12px
            }

            .hms-editor-wrapper .hms-btn-group button:disabled {
                cursor: not-allowed
            }

            .hms-editor-wrapper .hms-btn-group button:focus {
                outline: none
            }

        .hms-editor-wrapper .hms-btn-group .hms-editor-tool-btn-split {
            border-right: 1px solid #e8e8e8;
            height: 2.2em;
            margin: 0 5px
        }

    .hms-editor-wrapper .hms-editor-core {
        overflow-x: hidden;
        width: 100%;
        height: 100%;
        align-self: center;
        width: 210mm
    }

        .hms-editor-wrapper .hms-editor-core.A4, .hms-editor-wrapper .hms-editor-core.A4T {
            box-shadow: 0 0 5px 2px rgba(64,60,67,.16)
        }

        .hms-editor-wrapper .hms-editor-core.A5, .hms-editor-wrapper .hms-editor-core.A5T {
            box-shadow: none
        }

        .hms-editor-wrapper .hms-editor-core.A4T-MAX {
            width: 297mm !important
        }

        .hms-editor-wrapper .hms-editor-core.A5T-MAX {
            width: 210mm !important
        }

.dialog {
    padding: 20px
}

.app-dialog-wraper .dialog-body .dialog-header {
    padding: 0 10px 0 20px;
    height: 46px;
    line-height: 42px;
    border-bottom: 1px solid #eee;
    font-size: 14px;
    color: #404341;
    overflow: hidden;
    background-color: #f6f9fc;
    border-radius: 2px 2px 0 0
}

    .app-dialog-wraper .dialog-body .dialog-header .dialog-title {
        opacity: .8
    }

    .app-dialog-wraper .dialog-body .dialog-header .dialog-close {
        font-size: .8em;
        opacity: .8;
        color: #2d2c3b
    }

        .app-dialog-wraper .dialog-body .dialog-header .dialog-close:hover {
            opacity: 1
        }

.app-dialog-wraper .dialog-body .mktageditor .tag-form input {
    line-height: 1.4rem;
    color: #495057;
    background-color: #fff;
    background-clip: padding-box;
    border: 1px solid #ced4da;
    font-size: 14px
}

.app-dialog-wraper .dialog-body .form button {
    border: 1px solid #dedede;
    width: 100%;
    border-radius: var(--main-radius)
}

.app-dialog-wraper.show {
    background-color: rgba(0,0,0,.3) !important
}

.app-dialog-wraper {
    position: fixed;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    z-index: 10000;
    background-color: transparent;
    font-size: 0;
    text-align: center;
    transition: all .3s ease-in-out;
    perspective: 1300px
}

    .app-dialog-wraper:not(:last-child) {
        background: none !important
    }

    .app-dialog-wraper:after {
        content: "";
        display: block;
        width: 0;
        height: 100%;
        display: inline-block;
        vertical-align: middle
    }

    .app-dialog-wraper .dialog-header {
        display: flex;
        align-items: center;
        padding: 10px 8px 10px 15px;
        position: relative;
        z-index: 10;
        border-radius: 2px 2px 0 0
    }

        .app-dialog-wraper .dialog-header .dialog-title {
            flex: 1;
            height: 1.4em;
            line-height: 1.4em
        }

        .app-dialog-wraper .dialog-header .dialog-close {
            opacity: .3;
            cursor: pointer;
            font-size: 1.2em;
            padding: 8px;
            border-radius: 5px;
            background-color: #e0eff9
        }

    .app-dialog-wraper .dialog-body {
        font-size: medium;
        border-radius: 5px;
        max-width: 80%;
        display: inline-block;
        text-align: left;
        overflow: hidden;
        min-width: 100px;
        min-height: 60px;
        transition: all .3s ease;
        transform-origin: 50% 0;
        vertical-align: middle;
        transform: scale(.6);
        opacity: 0
    }

        .app-dialog-wraper .dialog-body:not(.noframe) {
            box-shadow: 1px 1px 10px rgba(0,0,0,.1);
            background-color: var(--dialog-bg)
        }

    .app-dialog-wraper.show {
        background-color: rgba(0,0,0,.7)
    }

        .app-dialog-wraper.show .dialog-body {
            opacity: 1;
            transform: scale(1)
        }

    .app-dialog-wraper.close {
        background-color: transparent
    }

        .app-dialog-wraper.close .dialog-body {
            opacity: 0;
            transform: scale(.6)
        }

.mint-dialog-notify-container {
    top: 0;
    right: 0;
    width: 400px;
    bottom: 25px;
    position: fixed;
    z-index: 10002;
    pointer-events: none;
    display: flex;
    flex-direction: column;
    justify-content: flex-end
}

    .mint-dialog-notify-container .mint-dialog-notify {
        margin: 10px;
        pointer-events: all;
        font-size: 14px;
        border-radius: var(--middle-radius);
        overflow: hidden;
        transform: translateX(100%);
        transition: all .5s ease-in-out
    }

        .mint-dialog-notify-container .mint-dialog-notify.show {
            transform: translateX(0)
        }

    .mint-dialog-notify-container .notify-header {
        padding: 10px;
        background: var(--workbenchbar-header-bg)
    }

    .mint-dialog-notify-container .notify-body {
        padding: 10px;
        line-height: 1.4em;
        background: var(--workbenchbar-bg)
    }

@font-face {
    font-family: dialogicon;
    src: url(data:font/ttf;base64,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) format("truetype"),url(data:font/woff;base64,d09GRgABAAAAAAesAAsAAAAAB2AAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAABPUy8yAAABCAAAAGAAAABgDxIGK2NtYXAAAAFoAAAAVAAAAFQXVtKLZ2FzcAAAAbwAAAAIAAAACAAAABBnbHlmAAABxAAAA2gAAANoFOyj8WhlYWQAAAUsAAAANgAAADYecKlpaGhlYQAABWQAAAAkAAAAJAfAA8lobXR4AAAFiAAAACQAAAAkGAAAJWxvY2EAAAWsAAAAFAAAABQCVAMybWF4cAAABcAAAAAgAAAAIAANAEJuYW1lAAAF4AAAAaoAAAGqvWxQynBvc3QAAAeMAAAAIAAAACAAAwAAAAMEAAGQAAUAAAKZAswAAACPApkCzAAAAesAMwEJAAAAAAAAAAAAAAAAAAAAARAAAAAAAAAAAAAAAAAAAAAAQAAA6QQDvv+/AEEDvgBBAAAAAQAAAAAAAAAAAAAAIAAAAAAAAwAAAAMAAAAcAAEAAwAAABwAAwABAAAAHAAEADgAAAAKAAgAAgACAAEAIOkE//3//wAAAAAAIOkA//3//wAB/+MXBAADAAEAAAAAAAAAAAAAAAEAAf//AA8AAQAAAAAAAAAAAAIAADc5AQAAAAABAAAAAAAAAAAAAgAANzkBAAAAAAEAAAAAAAAAAAACAAA3OQEAAAAAAwAVAA0D6wNJAA0AGQAoAAAlASYiBzEBBhYzITI2JwUiJjU0NjMyFhUUBjcUBiMiJj0BNDYzMTIWFQPr/lQaShr+VCMsQgNAQiwj/hUQGBgQERcXFxcRERcYEBEXlAK1Kir9SzhPTzgQFxEQFxcQERfGEBcXEO8QGBcRAAMAAP/ABAADwAAbACIAPwAAASIHDgEHBhUUFx4BFxYzMjc+ATc2NTQnLgEnJgMiJjUzFAYnIicmNjc2PQE0Njc0NjMyFgceAR0BFBceAQcGIwIAal1eiygoKCiLXl1qal1eiygoKCiLXl1mGCV1ICCiLi4UIyRQOxIODRIDO1AiIhEwMKQDwCgoi15dampdXosoKCgoi15dampdXosoKPzyIRgYIVUKCigeHiiERUcFDhISDgVESIQoHh4oCgoAAAMAAP/ABAADwAAbACcANQAAASIHDgEHBhUUFx4BFxYzMjc+ATc2NTQnLgEnJgMiJjU0NjMyFhUUBhMUBiMiJjURNDYzMhYVAgBqXV6LKCgoKIteXWpqXV6LKCgoKIteXWoYIyMYGCMjIyMYGCMjGBgjA8AoKIteXWpqXV6LKCgoKIteXWpqXV6LKCj83iIZGCMjGBkiAQAZIyMZARMZIiIZAAAAAgAA/8AEAAPAACMAPwAACQEOAS8BLgEnLgE1NDYzMhYXMDIxFzAyMRcWMjcBNjIXHgEHASIHDgEHBhUUFx4BFxYzMjc+ATc2NTQnLgEnJgMp/rULIQ3IAgMBAwMUDwQJBAEBAbACBAIBOwkaCQkBCP7Xal1eiygoKCiLXl1qal1eiygoKCiLXl0Cb/53DQMLvQIDAgQLBg4VAgMBcwEBATYJCQkZCQFRKCiLXl1qal1eiygoKCiLXl1qal1eiygoAAAAAAEAEP/QA/ADsAAkAAAJATY0LwEmIgcJASYiDwEGFBcJAQYUHwEWMjcJARYyPwE2NCcBAk8BoRAQAhAtEP5f/l8QLRACEBABof5fEBACEC0QAaEBoRAtEAIQEP5fAcABoRAtEAIQEP5eAaIQEAIQLRD+X/5fEC0QAhAQAaH+XxAQAhAtEAGhAAABAAAAAQAAy4kK118PPPUACwQAAAAAAN2osnYAAAAA3aiydgAA/8AEAAPAAAAACAACAAAAAAAAAAEAAAO+/78AAAQAAAAAAAQAAAEAAAAAAAAAAAAAAAAAAAAJBAAAAAAAAAAAAAAAAAAAAAQAABUEAAAABAAAAAQAAAAEAAAQAAAAAAAKABQAHgBcALwBDgFwAbQAAQAAAAkAQAADAAAAAAACAAAAAAAAAAAAAAAAAAAAAAAAAA4ArgABAAAAAAABAAoAAAABAAAAAAACAAcAewABAAAAAAADAAoAPwABAAAAAAAEAAoAkAABAAAAAAAFAAsAHgABAAAAAAAGAAoAXQABAAAAAAAKABoArgADAAEECQABABQACgADAAEECQACAA4AggADAAEECQADABQASQADAAEECQAEABQAmgADAAEECQAFABYAKQADAAEECQAGABQAZwADAAEECQAKADQAyGRpYWxvZ2ljb24AZABpAGEAbABvAGcAaQBjAG8AblZlcnNpb24gMS4wAFYAZQByAHMAaQBvAG4AIAAxAC4AMGRpYWxvZ2ljb24AZABpAGEAbABvAGcAaQBjAG8AbmRpYWxvZ2ljb24AZABpAGEAbABvAGcAaQBjAG8AblJlZ3VsYXIAUgBlAGcAdQBsAGEAcmRpYWxvZ2ljb24AZABpAGEAbABvAGcAaQBjAG8AbkZvbnQgZ2VuZXJhdGVkIGJ5IEljb01vb24uAEYAbwBuAHQAIABnAGUAbgBlAHIAYQB0AGUAZAAgAGIAeQAgAEkAYwBvAE0AbwBvAG4ALgAAAAMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA=) format("woff"),url(img/dialogicon.f1a3f505.svg#dialogicon) format("svg");
    font-weight: 400;
    font-style: normal;
    font-display: block
}

[class*=" dialog-icon-"], [class^=dialog-icon-] {
    font-family: dialogicon !important;
    speak: never;
    font-style: normal;
    font-weight: 400;
    font-variant: normal;
    text-transform: none;
    line-height: 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale
}

.dialog-icon-close:before {
    content: "\e904"
}

.dialog-icon-error:before {
    content: "\e900"
}

.dialog-icon-tip:before {
    content: "\e901"
}

.dialog-icon-warning:before {
    content: "\e902"
}

.dialog-icon-success:before {
    content: "\e903"
}

.mint-dialog-tip-container {
    top: 0;
    right: 0;
    padding: 10px;
    bottom: 25px;
    height: 20vh;
    position: fixed;
    z-index: 10003;
    pointer-events: none
}

    .mint-dialog-tip-container .mint-dialog-tip {
        margin: 10px;
        pointer-events: all;
        font-size: 14px;
        border-radius: var(--middle-radius);
        overflow: hidden;
        transform: translateY(-30%);
        opacity: 0;
        transition: all .5s ease-in-out;
        display: flex;
        align-items: center;
        text-align: right;
        padding: 10px;
        line-height: 1.2em
    }

        .mint-dialog-tip-container .mint-dialog-tip.warning {
            color: #df760c;
            background: #ffd5a6
        }

        .mint-dialog-tip-container .mint-dialog-tip.error {
            color: #ee0a24;
            background: #f8a6af
        }

        .mint-dialog-tip-container .mint-dialog-tip.success {
            color: #07c160;
            background: #fff
        }

        .mint-dialog-tip-container .mint-dialog-tip.tip {
            color: #06f;
            background: #fff
        }

        .mint-dialog-tip-container .mint-dialog-tip.show {
            transform: translateX(0);
            opacity: 1
        }

        .mint-dialog-tip-container .mint-dialog-tip .tip-icon {
            width: 1em;
            height: 1em;
            margin-right: 10px;
            font-size: 1.2em
        }

.mint-dialog-alert, .mint-dialog-confirm {
    padding: 20px 20px 20px;
    min-width: 300px;
    max-width: 400px;
    display: flex;
    flex-direction: column;
    min-height: 160px
}

.mint-dialog-title {
    font-size: 22px
}

.mint-dialog-msg {
    flex: 1;
    margin: 15px 0 30px;
    line-height: 1.6em
}

.mint-dialog-btns {
    text-align: center;
    display: flex;
    justify-content: flex-end
}

    .mint-dialog-btns button {
        padding: 8px 24px;
        margin-left: 20px
    }

.mint-dialog-alert .mint-cancel-btn {
    display: none
}

.mint-dialog-loading-container {
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    position: fixed;
    max-width: 100%;
    width: 100%;
    z-index: 10001;
    padding-bottom: 20vh;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    background: rgba(0,0,0,.5);
    display: flex;
    letter-spacing: .05em
}

    .mint-dialog-loading-container.empty {
        display: none
    }

    .mint-dialog-loading-container .mint-loading-text {
        display: flex;
        align-items: center;
        justify-content: center
    }

    .mint-dialog-loading-container .mint-loading-icon {
        width: 50px;
        height: 50px;
        background-size: contain;
        background-position: 50%;
        margin: 12px auto 15px;
        position: relative
    }

        .mint-dialog-loading-container .mint-loading-icon:not(.show-progress) {
            background-image: url(img/loading-progress.bdb0ab3e.svg);
            animation: loading_spin 1s linear infinite
        }

        .mint-dialog-loading-container .mint-loading-icon .loading-progress {
            position: absolute;
            font-size: 2em;
            top: 50%;
            left: 50%;
            transform: translate(-50%,-50%);
            white-space: nowrap;
            text-align: center;
            line-height: 1
        }

            .mint-dialog-loading-container .mint-loading-icon .loading-progress .percent {
                font-size: 12px;
                padding-left: 2px;
                position: absolute;
                left: 100%;
                bottom: 3px
            }

@keyframes loading_spin {
    0% {
        transform: rotate(0deg)
    }

    to {
        transform: rotate(1turn)
    }
}

.mint-dialog-loading {
    min-width: 6em;
    background: rgba(0,0,0,.7);
    pointer-events: all;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    border-radius: 8px;
    color: #ddd;
    text-align: center;
    box-shadow: 1px 1px 5px rgba(0,0,0,.5);
    margin: 10px;
    font-size: .9em;
    opacity: 0;
    transform: scale(.6);
    transition: all .3s ease
}

    .mint-dialog-loading.large {
        width: 130px;
        height: 130px
    }

        .mint-dialog-loading.large .mint-loading-text {
            position: relative;
            bottom: 2px
        }

    .mint-dialog-loading:not(.large) {
        padding: .8em 1.2em
    }

    .mint-dialog-loading.show {
        opacity: 1;
        transform: scale(1)
    }

    .mint-dialog-loading.close {
        opacity: 0;
        transform: scale(.6)
    }

    .mint-dialog-loading .tail {
        height: 1em;
        width: 3em;
        background-image: url(img/loading-tail.737b9a28.svg);
        background-position: 50%;
        background-repeat: no-repeat;
        background-size: contain;
        font-size: 12px
    }

        .mint-dialog-loading .tail:not(:first-child) {
            margin-left: .6em
        }

.__printerEditor-print-helper[data-v-174f5f29] {
    width: 210mm;
    align-self: center
}
