<template>
  <div class="followUp content_body">
    <div class="nav_header">
      <el-form :inline="true" size="small" @keyup.enter.native="handleSearch">
        <el-form-item label="客户信息">
          <el-input size="small" v-model="search.Name" @clear="handleSearch" clearable placeholder="输入姓名、手机号或客户编号"></el-input>
        </el-form-item>
        
          <el-form-item label="线索状态">
          <el-select placeholder="请选择线索状态" clearable v-model="search.Status" @change="handleSearch" size="small">
            <el-option label="未知线索" :value="0"></el-option>
            <el-option label="新线索" :value="1"></el-option>
            <el-option label="跟进中" :value="2"></el-option>
            <el-option label="已成交" :value="3"></el-option>
          </el-select>
        </el-form-item>

         <!--<el-form-item label="预约时间">
          <el-date-picker v-model="search.AppointmentDate" unlink-panels type="daterange" range-separator="至" value-format="yyyy-MM-dd" start-placeholder="开始日期" end-placeholder="结束日期" @change="handleSearch"> </el-date-picker>
        </el-form-item> -->

        <el-form-item label="成交会员">
          <el-select placeholder="请选择成交会员" clearable v-model="search.IsDeal" @change="handleSearch" size="small">
            <el-option label="已成交" :value="true"></el-option>
            <el-option label="未成交" :value="false"></el-option>
          </el-select>
        </el-form-item>


        <el-form-item label="线索来源">
          <el-select placeholder="请选择线索来源" clearable v-model="search.LeadSource" @change="handleSearch" size="small">
            <el-option label="抖音信息流" value="DOUYIN_XINXILIU"></el-option>
            <el-option label="微信朋友圈" value="WEIXIN_PENGYOUQUAN"></el-option>
            <el-option label="抖音来客-团购支付" value="DOUYIN_TUANGOU"></el-option>
            <el-option label="抖音来客-自然线索" value="DOUYIN_ZIRANXIANSOU"></el-option>
            <el-option label="美团团购" value="MEITUAN_TUANGOU"></el-option>
          </el-select>
        </el-form-item>

        

        <el-form-item label="预约状态">
          <el-select placeholder="请选择预约状态" clearable v-model="search.AppointmentStatus" @change="handleSearch" size="small">
            <el-option label="未预约" :value="0"></el-option>
            <el-option label="已预约" :value="10"></el-option>
            <el-option label="已到店" :value="20"></el-option>
            <el-option label="已取消" :value="30"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="线索创建时间">
          <el-date-picker v-model="search.CustomerCreatedDate" unlink-panels type="daterange" range-separator="至" value-format="yyyy-MM-dd" start-placeholder="开始日期" end-placeholder="结束日期" @change="handleSearch"> </el-date-picker>
        </el-form-item>
        <!-- <el-form-item label="跟进方式">
          <el-select placeholder="请选择跟进方式" filterable clearable v-model="search.FollowUpMethodID" @change="handleSearch" size="small">
            <el-option v-for="item in clueTableDataMethod" :key="item.ID" :label="item.Name" :value="item.ID"></el-option>
          </el-select>
        </el-form-item> -->

        <!-- <el-form-item label="跟进状态">
          <el-select placeholder="请选择跟进状态" filterable clearable v-model="search.FollowUpStatusID" @change="handleSearch" size="small">
            <el-option v-for="item in clueTableDataStatus" :key="item.ID" :label="item.Name" :value="item.ID"></el-option>
          </el-select>
        </el-form-item> -->



        <el-form-item label="计划跟进时间">
          <el-date-picker v-model="search.QueryDate" unlink-panels type="daterange" range-separator="至" value-format="yyyy-MM-dd" start-placeholder="开始日期" end-placeholder="结束日期" @change="handleSearch"> </el-date-picker>
        </el-form-item>

        <el-form-item label="跟进人员" v-if="isShowFollowUp">
          <el-select placeholder="请选择跟进人员" filterable clearable v-model="search.FollowUpBy" @change="handleSearch" size="small">
            <el-option v-for="item in followUpEmployeeData" :key="item.ID" :label="item.Name" :value="item.ID"></el-option>
          </el-select>
        </el-form-item>

        <!-- <el-form-item label="跟进部门" v-if="isShowFollowUp">
          <el-select placeholder="请选择跟进部门" filterable clearable v-model="search.FollowUpEntity" @change="handleSearch" size="small">
            <el-option v-for="item in followUpEntityData" :key="item.ID" :label="item.Name" :value="item.ID"></el-option>
          </el-select>
        </el-form-item> -->

        <el-form-item v-if="isShowFollowUp">
          <el-checkbox v-model="search.IsShowOwnFollowUp" @change="IsShowOwnFollowUpChange()">显示自己的跟进</el-checkbox>
        </el-form-item>
        <el-form-item>
          <el-button size="small" type="primary" @click="handleSearch">搜索</el-button>
        </el-form-item>
        <el-form-item>
          <el-button size="small" type="primary" @click="addFollowUp">新增</el-button>
        </el-form-item>
      </el-form>
    </div>
    <!-- 表格 -->
    <el-table size="small" highlight-current-row :data="clueTableData" tooltip-effect="light" @row-dblclick="handleRowDblClick">
      <el-table-column fixed label="操作" :width="isAnewAssign ? '300px' : '240px'">
        <template slot-scope="scope">
          <div class="operation-buttons" @dblclick.stop>
            <el-button type="primary" size="small" @click.stop="followUpClick(scope.row)" v-if="scope.row.Status === 0 || scope.row.Status === '0' || scope.row.Status === 1 || scope.row.Status === '1' || scope.row.Status === 2 || scope.row.Status === '2'">跟进</el-button>
            <el-button type="primary" size="small" @click.stop="anewAssignFollowUpClick(scope.row)" v-if="(scope.row.Status === 0 || scope.row.Status === 1) && isAnewAssign">重新指派</el-button>

            <!-- 预约相关按钮 -->
            <!-- 未预约状态 -->
            <el-button
              v-if="((scope.row.Status === 0 || scope.row.Status === 1) && !scope.row.HasAppointment) || (scope.row.Status === 2 && !scope.row.HasAppointment && canShowAppointmentAndCallButtons(scope.row))"
              type="primary"
              size="small"
              @click.stop="addAppointmentClick(scope.row)"
            >
              预约
            </el-button>

            <!-- 已预约状态 - 使用下拉菜单 -->
            <el-dropdown
              v-if="(((scope.row.Status === 0 || scope.row.Status === 1) && scope.row.HasAppointment && (scope.row.AppointmentStatus === 10 || scope.row.AppointmentStatus === '10')) || (scope.row.Status === 2 && scope.row.HasAppointment && (scope.row.AppointmentStatus === 10 || scope.row.AppointmentStatus === '10') && canShowAppointmentAndCallButtons(scope.row)))"
              @command="handleAppointmentCommand"
              trigger="click"
              size="small"
            >
              <el-button type="success" size="small" class="appointment-btn">
                预约管理<i class="el-icon-arrow-down el-icon--right"></i>
              </el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item :command="{action: 'edit', row: scope.row}">
                  <i class="el-icon-edit"></i> 修改预约
                </el-dropdown-item>
                <el-dropdown-item :command="{action: 'cancel', row: scope.row}">
                  <i class="el-icon-close"></i> 取消预约
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>

            <!-- 已到店状态 - 可以继续预约 -->
            <el-button
              v-if="scope.row.HasAppointment && (scope.row.AppointmentStatus === 20 || scope.row.AppointmentStatus === '20')"
              type="primary"
              size="small"
              class="appointment-btn"
              @click.stop="addAppointmentClick(scope.row)"
            >
              继续预约
            </el-button>

            <!-- 已取消状态，可以重新预约 -->
            <el-button
              v-if="(((scope.row.Status === 0 || scope.row.Status === 1) && scope.row.HasAppointment && (scope.row.AppointmentStatus === 30 || scope.row.AppointmentStatus === '30')) || (scope.row.Status === 2 && scope.row.HasAppointment && (scope.row.AppointmentStatus === 30 || scope.row.AppointmentStatus === '30')))"
              type="primary"
              size="small"
              class="appointment-btn"
              @click.stop="addAppointmentClick(scope.row)"
            >
              重新预约
            </el-button>

            <el-button
              type="primary"
              size="small"
              @click.stop="phoneCallBack_callBack(scope.row)"
              v-if="(scope.row.Status === 0 || scope.row.Status === 1) || ((scope.row.Status === 2 || scope.row.Status === 3) && canShowAppointmentAndCallButtons(scope.row))"
            >
              呼叫
            </el-button>

            <!-- 手动标记成交按钮：只有跟进中状态且有客户等级的数据才能标记成交 -->
            <el-button
              v-if="(scope.row.Status === 2 || scope.row.Status === '2') && scope.row.CustomerLevelName"
              type="success"
              size="small"
              @click.stop="markConverted(scope.row)"
            >
              标记成交
            </el-button>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="客户" prop="CustomerName" width="150px">
        <template slot-scope="scope">
          <div class="customer-info" style="display: flex; align-items: center; flex-wrap: nowrap; white-space: nowrap;">
            <span
              class="text-bold customer-name clickable-customer-name"
              style="flex-shrink: 0; color: #409EFF; cursor: pointer;"
              @click="handleCustomerNameClick(scope.row)"
            >
              {{ scope.row.CustomerName }}
            </span>
            <el-tag
              v-if="scope.row.Status === 0 || scope.row.Status === 1"
              size="mini"
              type="danger"
              class="new-tag"
              style="margin-left: 6px; flex-shrink: 0;"
            >
              NEW
            </el-tag>
            <el-tag
              v-if="scope.row.CustomerLevelName"
              size="mini"
              type="warning"
              class="customer-level-tag"
              style="margin-left: 6px; flex-shrink: 0;"
            >
              {{ scope.row.CustomerLevelName }}
            </el-tag>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="线索来源" prop="LeadSourceName" width="150px">
        <template slot-scope="scope">
          <span style="white-space: nowrap;">{{ scope.row.LeadSourceName }}</span>
        </template>
      </el-table-column>
      <el-table-column label="性别" prop="Gender" :formatter="formatGender"></el-table-column>
      <el-table-column label="手机" prop="PhoneNumber" width="100px">
        <template slot-scope="scope">
          {{ scope.row.PhoneNumber | hidephone }}
        </template>
      </el-table-column>
      <el-table-column label="备注" prop="Remark" width="140px" show-overflow-tooltip>
        <template slot-scope="scope">
          <div class="remark-cell">
            <span v-if="scope.row.Remark && scope.row.Remark.trim()"
                  class="remark-content">
              {{ scope.row.Remark }}
            </span>
            <span v-else class="remark-empty">暂无</span>
          </div>
        </template>
      </el-table-column>
      <!-- <el-table-column label="编号" prop="Code"></el-table-column> -->
      <el-table-column label="预约信息" width="150px">
        <template slot-scope="scope">
          <div v-if="scope.row.HasAppointment">
            <div class="font_12 color_666">
              {{ scope.row.AppointmentDate | dateFormat('YYYY-MM-DD HH:mm') }}
            </div>
            <el-tag
              :type="getAppointmentTagType(scope.row.AppointmentStatus)"
              size="mini"
            >
              {{ getAppointmentStatusText(scope.row.AppointmentStatus) }}
            </el-tag>
          </div>
          <span v-else class="color_999">未预约</span>
        </template>
      </el-table-column>
      <el-table-column label="预约门店" width="120px">
        <template slot-scope="scope">
          <span v-if="scope.row.AppointmentEntityName" class="color_333">
            {{ scope.row.AppointmentEntityName }}
          </span>
          <span v-else class="color_999">-</span>
        </template>
      </el-table-column>
      <!-- 意向门店列 -->
      <el-table-column label="意向门店" width="140px">
        <template slot-scope="scope">
          <!-- 编辑状态 -->
          <div v-if="scope.row.editingIntentionEntity" class="intention-entity-edit">
            <el-select
              v-model="scope.row.IntentionEntityID"
              placeholder="请选择意向门店"
              size="mini"
              clearable
              filterable
              @change="handleIntentionEntityChange(scope.row)"
              ref="intentionEntitySelect"
              style="width: 120px;"
            >
              <el-option
                v-for="item in entityList"
                :key="item.ID"
                :label="item.Name || item.EntityName"
                :value="item.ID"
              >
              </el-option>
            </el-select>
            <div class="edit-buttons" style="margin-top: 4px;">
              <el-button
                type="primary"
                size="mini"
                @click="handleIntentionEntitySave(scope.row)"
                :loading="scope.row.savingIntentionEntity"
                style="padding: 2px 6px; font-size: 11px;"
              >
                保存
              </el-button>
              <el-button
                size="mini"
                @click="handleIntentionEntityCancel(scope.row)"
                style="padding: 2px 6px; font-size: 11px;"
              >
                取消
              </el-button>
            </div>
          </div>
          <!-- 显示状态 -->
          <div v-else class="intention-entity-display" @click="handleIntentionEntityEdit(scope.row)">
            <span v-if="scope.row.IntentionEntityName" class="color_333 clickable-text">
              {{ scope.row.IntentionEntityName }}
            </span>
            <span v-else class="color_999 clickable-text">点击设置</span>
            <i class="el-icon-edit edit-icon"></i>
          </div>
        </template>
      </el-table-column>
      <!-- 线索状态列 -->
      <el-table-column label="线索状态" prop="StatusName" width="100px">
        <template slot-scope="scope">
          <el-tag 
            :type="getLeadStatusTagType(scope.row.Status)" 
            size="mini"
          >
            {{ scope.row.StatusName }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column label="线索创建时间" prop="LeadCreatedOn" width="140px">
        <template slot-scope="scope">
          {{ scope.row.LeadCreatedOn | dateFormat("YYYY-MM-DD HH:mm") }}
        </template>
      </el-table-column>
      
      <el-table-column label="分配时间" prop="AssignedOn" width="140px">
        <template slot-scope="scope">
          {{ scope.row.AssignedOn | dateFormat("YYYY-MM-DD HH:mm") }}
        </template>
      </el-table-column>
      
      <el-table-column label="分配给" prop="AssignedToName" width="100px">
        <template slot-scope="scope">
          {{ scope.row.AssignedToName }}
        </template>
      </el-table-column>
      <!-- <el-table-column label="指派/创建时间" prop="CreatedOn" width="140px">
        <template slot-scope="scope">
          {{ scope.row.CreatedOn | dateFormat("YYYY-MM-DD HH:mm") }}
        </template>
      </el-table-column> -->
      <!-- <el-table-column label="计划跟进时间" prop="PlannedOn" width="140px">
        <template slot-scope="scope">
          {{ scope.row.PlannedOn | dateFormat("YYYY-MM-DD HH:mm") }}
        </template>
      </el-table-column> -->
      <!-- <el-table-column label="实际跟进时间" prop="FollowUpOn" width="140px">
        <template slot-scope="scope">
          {{ scope.row.FollowUpOn | dateFormat("YYYY-MM-DD HH:mm") }}
        </template>
      </el-table-column> -->
      <el-table-column label="指派人" prop="AssignedByName"></el-table-column>
      <el-table-column label="跟进人员" prop="FollowUpByName"></el-table-column>
      <!-- <el-table-column label="跟进方式" prop="MethodName"></el-table-column> -->
      <!-- <el-table-column label="跟进状态" prop="Status" width="100"></el-table-column> -->
      <el-table-column label="服务人员" width="180px">
        <template slot-scope="scope">
          <div v-if="scope.row.ServicerEmployee && scope.row.ServicerEmployee.length > 0">
            <el-tooltip placement="top" effect="light" :disabled="!shouldShowTooltip(scope.row.ServicerEmployee)">
              <div slot="content">
                <el-descriptions size="mini" :column="1" border :colon="false" labelClassName="custom-customer-descLabel">
                  <el-descriptions-item v-for="(item, index) in scope.row.ServicerEmployee" :key="index" :label="item.Name + '：'">
                    {{ getServicerEmpNames(item.ServicerEmpList) }}
                  </el-descriptions-item>
                </el-descriptions>
              </div>
              <div style="line-height: 1.4; word-break: break-all; max-height: 60px; overflow: hidden;">
                <div v-for="(item, index) in scope.row.ServicerEmployee" :key="index" style="margin-bottom: 2px;">
                  <span style="font-weight: 500; color: #606266;">{{ item.Name }}：</span>
                  <span style="color: #909399;">{{ getServicerEmpNames(item.ServicerEmpList) }}</span>
                </div>
              </div>
            </el-tooltip>
          </div>
          <span v-else style="color: #C0C4CC;">暂无</span>
        </template>
      </el-table-column>
      <el-table-column label="城市" prop="City" >
        <template slot-scope='scope'>
          <span>{{ scope.row.CityName }}</span>
        </template>
      </el-table-column>
      <el-table-column label="所属门店" prop="EntityName" width="120"></el-table-column>
    </el-table>
    <!-- 分页 -->
    <div class="pad_15 text_right">
      <el-pagination
        background
        v-if="paginations.total > 0"
        @current-change="handleCurrentChange"
        @size-change="handleSizeChange"
        :current-page.sync="paginations.page"
        :page-size="paginations.page_size"
        :page-sizes="paginations.page_sizes"
        :layout="paginations.layout"
        :total="paginations.total">
      </el-pagination>
    </div>

    <work-customer-detail
      :visible.sync="customerDetailVisible"
      :customerID="CustomerID"
      :customerName="customerInfo.Name"
      ref="customerDetail"
      :isCustomerPhoneNumberView="isCustomerPhoneNumberView"
      :isCustomerPhoneNumberModify="isCustomerPhoneNumberModify"
      :isCustomerBasicInformationModify="isCustomerBasicInformationModify"
      :isCustomerServicerModify="isCustomerServicerModify"
      :isCustomerBasicFileModify="isCustomerBasicFileModify"
      :CustomerCases="CustomerCases"
      :hasAppointment="customerInfo.HasAppointment || false"
      :appointmentStatus="customerInfo.AppointmentStatus"
      :appointmentID="customerInfo.AppointmentID"
      :leadStatus="customerInfo.Status"
      :leadID="customerInfo.LeadID"
      :followUpLayoutType="'inline'"
      @openAppointment="handleCustomerDetailAppointment"
      @cancelAppointment="handleCustomerDetailCancelAppointment"
      @appointmentCancelled="handleAppointmentCancelled"
      @appointmentUpdated="handleAppointmentUpdated"
      @openFollowUp="handleCustomerDetailFollowUp"
      @refreshList="handleRefreshList"
    ></work-customer-detail>

    <!-- 添加预约弹框 -->
    <el-dialog :visible.sync="appointmentDialogShow" v-if="appointmentDialogShow" width="960px">
      <span slot="title" class="font_18">{{ isAdd ? '添加预约' : '修改预约' }}</span>
      <div>
        <!-- 预约顾客信息  -->
        <el-row>
          <el-col :span="15">
            <el-input
              prefix-icon="el-icon-user-solid"
              v-model="customerName"
              style="width: 100%"
              placeholder="客户信息"
              readonly
              size="small"
            ></el-input>
          </el-col>
          <el-col :span="8" :offset="1" class="back_f8 dis_flex flex_y_center radius5 line_height_38 pad_0_10">
            <el-col :span="12" class="back_f8">预约项目</el-col>
            <el-col :span="12" class="text_right">
              <el-button
                type="text"
                size="small"
                @click="addAppointmentProject"
                >添 加
              </el-button>
            </el-col>
          </el-col>
        </el-row>

        <el-row class="martp_10">
          <el-col :span="15">
            <div style="height: 420px" class="back_f8 radius5">
              <el-scrollbar class="el-scrollbar_height" style="height: 100%">
                <el-row class="padtp_15">
                  <el-form :model="AppointmentInfoRuleForm" :rules="AppointmentInfoRules" ref="AppointmentInfoRuleForm" label-width="120px" size="small">
                    <el-form-item v-if="appointmentTypeList.length > 0" label="预约类型">
                      <el-col :span="20">
                        <el-select v-model="AppointmentInfoRuleForm.AppointmentTypeID" placeholder="请选择预约类型" clearable>
                          <el-option
                            :label="item.Name"
                            :value="item.ID"
                            v-for="item in appointmentTypeList"
                            :key="'appointmentType' + item.ID"
                          >
                          </el-option>
                        </el-select>
                      </el-col>
                    </el-form-item>
                    <el-form-item label="预约门店" prop="EntityID">
                      <el-col :span="20">
                        <el-select v-model="AppointmentInfoRuleForm.EntityID" placeholder="请选择预约门店" clearable>
                          <el-option
                            :label="item.Name"
                            :value="item.ID"
                            v-for="item in entityList"
                            :key="'entity' + item.ID"
                          >
                          </el-option>
                        </el-select>
                      </el-col>
                    </el-form-item>
                    <el-form-item label="预约时间" style="margin-bottom: 0px" required>
                      <el-col :span="12">
                        <el-form-item prop="AppointmentDate">
                          <el-date-picker
                            v-model="AppointmentInfoRuleForm.AppointmentDate"
                            value-format="yyyy-MM-dd"
                            placeholder="选择日期时间"
                            size="small"
                            @change="changeHandleAppointmentDate"
                          >
                          </el-date-picker>
                        </el-form-item>
                      </el-col>

                      <el-col :span="12">
                        <el-form-item prop="Time">
                          <el-time-select
                            v-model="AppointmentInfoRuleForm.Time"
                            :picker-options="{
                              start: appointmentConfigInfo.StartTime,
                              step: '00:' + appointmentConfigInfo.Period,
                              end: appointmentConfigInfo.EndTime,
                            }"
                            placeholder="选择时间"
                          >
                          </el-time-select>
                        </el-form-item>
                      </el-col>
                    </el-form-item>
                    <el-form-item label="预约时长" prop="Period">
                      <el-col :span="20">
                        <el-select
                          v-model="AppointmentInfoRuleForm.Period"
                          placeholder="请选择预约时长"
                          clearable
                        >
                          <el-option :label="item.time" :value="item.value" v-for="(item, index) in timeArr" :key="index"> </el-option>
                        </el-select>
                      </el-col>
                    </el-form-item>
                    <el-form-item v-for="item in addServicerEmployeeList" :key="'addServicerID' + item.ServicerID" :label="item.ServicerName">
                      <el-col :span="20">
                        <el-select v-model="item.SelectEmployeeID" placeholder="请选择员工" clearable @change="changeAppointmentServicer">
                          <el-option
                            :label="item.EmployeeName"
                            :value="item.EmployeeID"
                            v-for="item in item.Employee"
                            :key="'addEmployee' + item.EmployeeID"
                          >
                          </el-option>
                        </el-select>
                      </el-col>
                    </el-form-item>
                    <el-form-item label="商家备注" prop="Remark">
                      <el-col :span="20">
                        <el-input
                          type="textarea"
                          :rows="4"
                          v-model="AppointmentInfoRuleForm.Remark"
                          placeholder="商家备注不超过200个字"
                        >
                        </el-input>
                      </el-col>
                    </el-form-item>
                  </el-form>
                </el-row>
              </el-scrollbar>
            </div>
          </el-col>
          <el-col :span="8" :offset="1">
            <div style="height: 420px" class="back_f8 radius5">
              <el-scrollbar class="el-scrollbar_height" style="height: 100%">
                <div class="pad_10">
                  <el-tag v-for="(item, index) in appointmentProjectList" :key="'appointmentProject' + index" type="primary" class="marlt_5 martp_5" closable @close="deleteAppointmentProject(index)">
                    {{ item.Name || item.ProjectName }}
                  </el-tag>
                </div>
              </el-scrollbar>
            </div>
          </el-col>
        </el-row>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="appointmentDialogShow = false" v-prevent-click size="small">取 消</el-button>
        <el-button type="primary" size="small" :loading="saveLoading" v-prevent-click @click="saveAppointment">保 存</el-button>
      </span>
    </el-dialog>

    <!--选择项目弹框-->
    <el-dialog :visible.sync="selectProjectDialogState" title="选择预约项目" width="900px">
      <template>
        <el-row>
          <el-col :span="8">
            <el-input placeholder="输入项目名称进行搜索" v-model="filterText" size="small" clearable></el-input>
            <el-scrollbar class="el-scrollbar_height martp_5">
              <el-tree
                class="filter-tree"
                :data="projectList"
                show-checkbox
                node-key="PID"
                ref="treeRef"
                accordion
                highlight-current
                :props="defaultProps"
                :default-checked-keys="defaultCheckedKeysApplyApp"
                :filter-node-method="filterNode"
                @check="selectApplicableItems"
              >
                <span slot-scope="{ data }">
                  <span>{{ data.Name }}</span>
                  <el-tag plain class="marlt_5" size="mini" v-if="!data.IsProject">分类</el-tag>
                </span>
              </el-tree>
            </el-scrollbar>
          </el-col>
          <el-col :span="15" :offset="1" class="border_left">
            <el-table
              size="small"
              :data="clueSelectedTableData.filter((data) => !filterText || data.Name.toLowerCase().includes(filterText.toLowerCase()))"
              max-height="500px"
            >
              <el-table-column prop="Name" label="项目名称"></el-table-column>
              <el-table-column label="操作" width="80px">
                <template slot-scope="scope">
                  <el-button type="danger" size="small" @click="deleteSelectRow(scope.row, scope.$index)">删除 </el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-col>
        </el-row>
      </template>
      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="selectProjectDialogState = false" v-prevent-click>取 消</el-button>
        <el-button type="primary" size="small" @click="confirmProjectSelect" v-prevent-click>确 认</el-button>
      </div>
    </el-dialog>
    <!-- 新建、处理跟进弹出框 -->
    <el-dialog :title="isAdd ? '新建跟进' : '处理跟进'" :visible.sync="dialogVisible" width="980px" custom-class="custom-dialog" @close="closeAddFollowUpDialog" :close-on-click-modal="false">
      <el-scrollbar class="el_scrollbar_height_followup">
        <el-autocomplete
          popper-class="customer-autocomplete"
          prefix-icon="el-icon-user-solid"
          v-model="customerName"
          style="width: 480px; margin-left: 30px; margin-bottom: 10px"
          size="small"
          placeholder="请输入客户姓名、手机号、编号查找，无匹配按回车新增"
          :fetch-suggestions="saleCustomerData"
          @select="handleCustomerSelect"
          :popper-append-to-body="false"
          :disabled="customerID != null"
          :trigger-on-focus="false"
          :hide-loading="false"
          :highlight-first-item="true"
          :select-when-unmatched="true"
          v-if="isAdd"
        >
          <template slot="append">
            <el-button icon="el-icon-delete" @click="removeCustomer"></el-button>
          </template>
          <template slot-scope="{ item }">
            <div class="name">
              {{ item.Name }}
              <el-tag size="mini" v-if="item.CustomerLevelName">{{ item.CustomerLevelName }} </el-tag>
            </div>
            <div class="info">手机号：{{ item.PhoneNumber | hidephone }}</div>
            <span class="info" v-if="item.Code">客户编号：{{ item.Code }}</span>
            <span class="info" v-if="item.EntityName">所属组织：{{ item.EntityName }}</span>
            <div class="info" v-if="item.ChannelDeveloperList && item.ChannelDeveloperList.length > 0">
              开发人员：
              <el-tooltip placement="top">
                <div slot="content">{{ getChannelNames(item.ChannelDeveloperList) }}</div>
                <span>{{ getChannelNames(item.ChannelDeveloperList) }}</span>
              </el-tooltip>
            </div>
            <div class="info" v-if="item.ChannelConsultantList && item.ChannelConsultantList.length > 0">
              市场咨询：
              <el-tooltip placement="top">
                <div slot="content">{{ getChannelNames(item.ChannelConsultantList) }}</div>
                <span>{{ getChannelNames(item.ChannelConsultantList) }}</span>
              </el-tooltip>
            </div>
          </template>
        </el-autocomplete>
        <div class="information" v-if="!isAdd">
          <el-row type="flex" align="" style="border-bottom: 1px solid #cfcfcf; padding-bottom: 5px">
            <el-col :span="2">
              <el-avatar :size="50" :src="circleUrl"></el-avatar>
            </el-col>
            <el-col :span="22">
              <el-row type="flex" justify="space-between">
                <el-col :span="24">
                  <strong class="marrt_5 font_18">{{ customerDetail.Name }}</strong>
                  <el-image v-if="customerDetail.Gender == 2" style="width: 8px; height: 12px" :src="require('@/assets//img//gender-female.png')"></el-image>
                  <el-image v-if="customerDetail.Gender == 1" style="width: 8px; height: 12px" :src="require('@/assets/img/gender-male.png')"></el-image>
                </el-col>
              </el-row>
              <el-col justify="space-between">
                <el-col :span="8" class="color_999 martp_10"
                  >手机号：<span class="color_333">{{ customerDetail.PhoneNumber | hidephone }}</span></el-col
                >
                <el-col :span="8" class="color_999 martp_10"
                  >会员编号：<span class="color_333">{{ customerDetail.Code }}</span></el-col
                >
                <el-col :span="8" class="color_999 martp_10"
                  >注册时间：<span class="color_333">{{ customerDetail.CreatedOn | dateFormat("YYYY-MM-DD HH:mm") }}</span></el-col
                >
                <el-col :span="8" class="color_999 martp_10"
                  >介绍人：<span class="color_333">{{ customerDetail.IntroducerName }}</span></el-col
                >
                <el-col :span="8" class="color_999 martp_10"
                  >渠道来源：<span class="color_333">{{ customerDetail.ChannelName }}</span></el-col
                >
                <el-col :span="8" class="color_999 martp_10"
                  >线索来源：<span class="color_333">{{ selectFollowUpRow.LeadSourceName }}</span></el-col
                >
              </el-col>
            </el-col>
          </el-row>
          <!-- <el-form size="small"> -->
          <el-row class="martp_5">
            <el-col :span="24">
              <!-- <el-form-item style="margin-bottom: 0"> -->
              <el-col :span="3">指派人员：</el-col>
              <el-col :span="21">{{ selectFollowUpRow.CreatedByName }}</el-col>
              <!-- </el-form-item> -->
            </el-col>
            <el-col :span="24" v-if="PlannedRemark">
              <!-- <el-form-item style="margin-bottom: 0"> -->
              <el-col :span="3">任务备注：</el-col>
              <el-col :span="21"> {{ selectFollowUpRow.PlannedRemark }}</el-col>
              <!-- </el-form-item> -->
            </el-col>
          </el-row>
          <!-- </el-form> -->
        </div>
        <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="110px" size="small">
          <el-form-item label="跟进方式" prop="FollowUpMethodID">
            <el-radio-group v-model="ruleForm.FollowUpMethodID">
              <el-radio v-for="item in clueTableDataMethod" :key="item.ID" :label="item.ID">{{ item.Name }}</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="跟进状态" prop="FollowUpStatusID">
            <el-radio-group v-model="ruleForm.FollowUpStatusID">
              <el-radio v-for="item in clueTableDataStatus" :key="item.ID" :label="item.ID">{{ item.Name }}</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="跟进记录" prop="FollowUpContent">
            <el-input rows="4" type="textarea" v-model="ruleForm.FollowUpContent"></el-input>
          </el-form-item>
          <el-form-item label="">
            <el-upload :limit="9" class="avatar-uploader" list-type="picture-card" action="#" :file-list="ruleForm.Attachment" :before-upload="commodityMainbeforeUpload" :on-remove="commodityMainRemove" accept="image/*" multiple>
              <i class="el-icon-plus avatar-uploader-icon"></i>
              <div slot="file" slot-scope="{ file }" style="height: 100px; width: 100px">
                <el-image :id="file.uid" :src="file.AttachmentURL" :preview-src-list="preview_src_list" :z-index="9999" fit="cover" style="height: 100px; width: 100px"></el-image>
                <span class="el-upload-list__item-actions">
                  <span class="el-upload-list__item-preview" @click="DialogPreview(file)">
                    <i class="el-icon-zoom-in"></i>
                  </span>
                  <span class="el-upload-list__item-preview" @click="commodityMainRemove(file)">
                    <i class="el-icon-delete"></i>
                  </span>
                </span>
              </div>
            </el-upload>
          </el-form-item>
          <el-form-item label="下次跟进计划" prop="IsNextFollowUp">
            <el-radio-group v-model="ruleForm.IsNextFollowUp">
              <el-radio :label="true">是</el-radio>
              <el-radio :label="false">否</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item
            v-if="ruleForm.IsNextFollowUp"
            prop="PlannedOn"
            label="下次跟进时间"
            :rules="[
              {
                required: ruleForm.IsNextFollowUp,
                message: '请选择下次跟进时间',
                trigger: 'change',
              },
            ]"
          >
            <el-date-picker v-model="ruleForm.PlannedOn" type="datetime" format="yyyy-MM-dd HH:mm" :default-time="nextDateTime" placeholder="请选择下次跟进日期"> </el-date-picker>
          </el-form-item>
          <el-form-item v-if="ruleForm.IsNextFollowUp" label="计划备注">
            <el-input type="textarea" rows="3" v-model="ruleForm.PlannedRemark"></el-input>
          </el-form-item>
        </el-form>
      </el-scrollbar>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false" v-prevent-click size="small">取 消</el-button>
        <el-button :loading="modalLoading" type="primary" v-prevent-click size="small" @click="submitFollowUp">保 存</el-button>
      </span>
    </el-dialog>
    <!-- 新建会员弹出框 -->
    <!--新增 客户-->
    <add-customer title="新增客户" :visible.sync="isAddCustom" @addCustomerSuccess="addCustomerSuccess"> </add-customer>

    <!-- 创建线索弹框 -->
    <el-dialog title="新增线索" :visible.sync="showCreateLeadDialog" width="600px" @close="resetCreateLeadForm">
      <el-form :model="createLeadForm" :rules="createLeadRules" ref="createLeadFormRef" label-width="100px" size="small">
        <el-form-item label="客户姓名" prop="Name">
          <el-input v-model="createLeadForm.Name" placeholder="请输入客户姓名"></el-input>
        </el-form-item>

        <el-form-item label="手机号" prop="PhoneNumber">
          <el-input v-model="createLeadForm.PhoneNumber" placeholder="请输入手机号" maxlength="11"></el-input>
        </el-form-item>

        <el-form-item label="性别" prop="Gender">
          <el-radio-group v-model="createLeadForm.Gender">
            <el-radio label="1">男</el-radio>
            <el-radio label="2">女</el-radio>
            <el-radio label="0">保密</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="线索来源" prop="LeadSource">
          <el-select v-model="createLeadForm.LeadSource" placeholder="请选择线索来源" clearable>
            <el-option label="抖音信息流" value="DOUYIN_XINXILIU"></el-option>
            <el-option label="微信朋友圈" value="WEIXIN_PENGYOUQUAN"></el-option>
            <el-option label="抖音来客-团购支付" value="DOUYIN_TUANGOU"></el-option>
            <el-option label="抖音来客-自然线索" value="DOUYIN_ZIRANXIANSOU"></el-option>
            <el-option label="美团团购" value="MEITUAN_TUANGOU"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="省市区">
          <el-cascader
            clearable
            placeholder="请选择省 / 市 / 区"
            :options="regionData"
            v-model="regionDataSelArr"
            @change="changeProvinceCityArea">
          </el-cascader>
        </el-form-item>

        <el-form-item label="意向门店">
          <el-select v-model="createLeadForm.IntentionEntityID" placeholder="请选择意向门店" clearable>
            <el-option
              v-for="item in entityList"
              :key="item.ID"
              :label="item.Name"
              :value="item.ID">
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="备注">
          <el-input
            type="textarea"
            v-model="createLeadForm.Remark"
            placeholder="请输入备注信息"
            :rows="3">
          </el-input>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="showCreateLeadDialog = false">取 消</el-button>
        <el-button type="primary" @click="submitCreateLead" :loading="createLeadLoading">确 定</el-button>
      </div>
    </el-dialog>

    <!--预约提醒弹框-->
    <el-dialog :visible.sync="customerAppointmentDialogVisible" title="提示" width="500px">
      <div>本客户今日已预约，是否继续添加预约</div>
      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="customerAppointmentDialogVisible = false" v-prevent-click>取 消</el-button>
        <el-button type="primary" size="small" @click="confirmCustomerAppointment" v-prevent-click>确 认</el-button>
      </div>
    </el-dialog>

    <!-- 配置默认员工 -->
    <el-dialog title="重新指派" :visible.sync="dialogVisibleAnewAssign" width="550px">
      <el-form :model="FollowUpByFrom" :rules="FollowUpRules" ref="FollowUpByRef" label-width="100px" size="small" @submit.native.prevent>
        <el-form-item label="跟进人员" prop="FollowUpBy">
          <el-select :popper-append-to-body="false" popper-class="custom-el-select" v-model="FollowUpByFrom.FollowUpBy" filterable remote :remote-method="searchEmpRemote" placeholder="请选择默认负责员工" clearable>
            <el-option v-for="item in searchData" :key="item.ID" :label="item.Name" :value="item.ID">
              <div class="dis_flex flex_dir_column pad_5_0">
                <div style="line-height: 25px">
                  <span style="float: left">{{ item.Name }}</span>
                  <span style="float: right; color: #8492a6; font-size: 13px">{{ item.ID }}</span>
                </div>
                <div style="line-height: 20px; color: #8492a6">
                  <span style="float: left">{{ item.JobName }}</span>
                  <span style="float: right; font-size: 13px" class="marlt_5">{{ item.JobName }}</span>
                </div>
              </div>
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="dialogVisibleAnewAssign = false" v-prevent-click>取消</el-button>
        <el-button type="primary" size="small" @click="onAddSubmitAnewAssign" :loading="defaultLoading" v-prevent-click>保存</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import API from "@/api/iBeauty/Workbench/followUp";
import CustomerAPI from "@/api/iBeauty/Order/saleGoods";
import cusAPI from "@/api/CRM/Customer/customer";
import APIFollowUp from "@/api/KHS/Setting/followUpConfig.js";
import FollowUpAPI from "@/api/iBeauty/Workbench/followUp";
import workCustomerDetail from "@/views/iBeauty/Workbench/Component/followUpCustomerDetail";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import addCustomer from "@/views/CRM/Customer/Components/CustomerDetail/addCustomer.vue";
import APIUpload from "@/api/Common/uploadAttachment.js";
import utils from "@/components/js/utils.js";
import Enumerable from "linq";
// 预约相关API
import appointmentTypeAPI from "@/api/iBeauty/Appointment/appointmentType";
import * as CommonAPI from "@/api/index";
// 省市区数据
import { regionData } from "element-china-area-data";

export default {
  name: "ClueFollowUp",

  props: {},
  /**  引入的组件  */
  components: {
    workCustomerDetail,
    addCustomer,
  },
  /**  Vue 实例的数据对象**/
  data() {
    return {
      isAnewAssign: false,
      defaultLoading: false,
      dialogVisibleAnewAssign: false,
      customerDetailVisible: false,
      isCustomerPhoneNumberView: false,
      isCustomerPhoneNumberModify: false,
      isCustomerBasicInformationModify: false,
      isCustomerServicerModify: false,
      isCustomerBasicFileModify: false,
      loading: false,
      modalLoading: false,
      isAdd: true,
      show: true,
      dialogVisible: false,
      isAddCustom: false,
      showCreateLeadDialog: false, // 显示创建线索弹框
      createLeadLoading: false, // 创建线索加载状态
      isShowChannel: false, // 是否展示渠道
      isShowFollowUp: false, // 跟进工作台权限
      search: {
        Name: "", // 用户名称搜索

        FollowUpMethodID: "", // 跟进方式搜索
        FollowUpStatusID: "", // 跟进状态搜索
        FollowUpBy: "", // 跟进人搜索
        FollowUpEntity: "", // 跟进部门搜索
        QueryDate: [], // 计划跟进时间搜索（去掉默认当天时间）
        AppointmentDate: [], // 预约时间搜索
        IsShowOwnFollowUp: true, //是否显示自己的跟进
        AppointmentStatus: "", // 预约状态搜索
        LeadSource: "", // 线索来源搜索
        Status: "", // 线索状态搜索
        CustomerCreatedDate: [this.$formatDate(new Date(), "YYYY-MM-DD"), this.$formatDate(new Date(), "YYYY-MM-DD")], // 线索创建时间搜索（加上默认当天时间）
        IsDeal: "", // 成交会员搜索
      }, // 搜索条件
      customerName: "", // 新建跟进任务搜索
      channelList: [], // 渠道来源
      // customerServicer: [], // 服务人员
      clueTableData: [],
      customerDetail: {}, // 顾客信息
      CustomerID: null, // 跟进顾客ID
      customerInfo: {
        Name: "",
        ID: "",
      },
      ID: "", //跟进记录ID
      employee: [], //营销顾问
      followUpCustomerID: "", //顾客ID
      leadID: "", //线索ID
      IntroducerPageNum: "",
      IntroducerTotal: "",
      addCustomerPhoneNumber: "", // 新增时 顾客的手机号
      regionDataSelArr: [], //城市已选择
      clueTableDataMethod: [], // 跟进方式
      clueTableDataStatus: [], // 跟进状态
      followUpEntityData: [], // 跟进部门
      followUpEmployeeData: [], // 跟进人员
      CreatedByName: "", // 指派人员
      PlannedRemark: "", // 任务备注
      selectFollowUpRow: {}, // 处理跟进任务时使用
      customerID: null,
      customerFullName: "",
      // 预约相关
      appointmentDialogShow: false,
      appointmentTypeList: [], // 预约类型列表
      appointmentProjectList: [], // 预约项目列表
      appointmentConfigInfo: {
        StartTime: "08:00",
        EndTime: "20:00",
        Period: "30",
        AppointmentServicerIsRequired: false,
      },
      timeArr: [
        { time: "30分钟", value: 30 },
        { time: "60分钟", value: 60 },
        { time: "90分钟", value: 90 },
        { time: "120分钟", value: 120 },
      ],
      addServicerEmployeeList: [], // 预约服务人员列表
      // 项目选择相关
      selectProjectDialogState: false, // 选择项目弹框展示状态
      projectList: [], // 项目列表数据
      filterText: '', // 项目搜索文本
      defaultCheckedKeysApplyApp: [], // 默认选中的适用项目节点
      clueSelectedTableData: [], // 已选择的项目数据
      defaultProps: {
        children: 'Child',
        label: 'Name',
      },
      AppointmentInfoRuleForm: {
        AppointmentTypeID: "", // 预约类型
        AppointmentDate: "",
        Time: "",
        Period: 30,
        Remark: "",
      },
      AppointmentInfoRules: {
        AppointmentDate: [{ required: true, message: "请选择预约日期", trigger: "change" }],
        Time: [{ required: true, message: "请选择预约时间", trigger: "change" }],
        Period: [{ required: true, message: "请选择预约时长", trigger: "change" }],
        EntityID: [{ required: true, message: "请选择预约门店", trigger: "change" }],
      },
      customerPhoneNumber: "",
      nextDateTime: "",
      circleUrl: "https://cube.elemecdn.com/3/7c/********************************.png", //默认头像
      ruleForm: {
        FollowUpMethodID: "", // 跟进方式
        FollowUpStatusID: "", // 跟进状态
        FollowUpContent: "", // 跟进记录
        PlannedOn: "", // 计划跟进时间
        IsNextFollowUp: true, // 下次是否跟进
        PlannedRemark: "", // 计划跟进备注
        Attachment: [],
      },
      rules: {
        FollowUpMethodID: [{ required: true, message: "请选择跟进方式", trigger: "change" }],
        FollowUpStatusID: [{ required: false, message: "请选择跟进状态", trigger: "change" }], // 改为非必填
        FollowUpContent: [{ required: true, message: "请填写跟进记录", trigger: "blur" }],
        IsNextFollowUp: [{ required: true, message: "请选择下次是否跟进", trigger: "change" }],
      },
      //需要给分页组件传的信息
      paginations: {
        page: 1, // 当前位于哪页
        total: 0, // 总数
        page_size: 10, // 1页显示多少条
        page_sizes: [10, 15, 30, 45, 60], // 每页显示条数选项
        layout: "total, sizes, prev, pager, next, jumper", // 翻页属性
      },
      preview_src_list: [],
      CustomerCases: {
        Add: false,
        Update: false,
        Delete: false,
        SelectStencil: false,
        SaveStencil: false,
        DeleteStencil: false,
        PrescriptionAdd: false,
        PrescriptionUpdate: false,
      },

      FollowUpByFrom: {
        ID: "",
        FollowUpBy: "",
      },
      FollowUpRules: {
        FollowUpBy: [{ required: true, message: "请选择跟进人员", trigger: "change" }],
      },
      searchData: [],
      // 预约保存相关
      saveLoading: false,
      editLoading: false,
      confirmLoading: false,
      customerAppointmentNumber: 0,
      customerAppointmentDialogVisible: false,
      customerAppointmentAll: [],
      cancelState: false,
      currentAppointmentID: null, // 当前编辑的预约ID
      entityList: [], // 门店列表
      // 创建线索表单
      createLeadForm: {
        Name: '',
        PhoneNumber: '',
        Gender: '1',
        LeadSource: '',
        AssignedTo: '',
        ProvinceCode: '',
        CityCode: '',
        AreaCode: '',
        Address: '',
        Remark: '',
        IntentionEntityID: null // 意向门店ID
      },
      // 创建线索表单验证规则
      createLeadRules: {
        Name: [{ required: true, message: '请输入客户姓名', trigger: 'blur' }],
        PhoneNumber: [
          { required: true, message: '请输入手机号', trigger: 'blur' },
          { pattern: /^1[3456789]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
        ],
        Gender: [{ required: true, message: '请选择性别', trigger: 'change' }],
        LeadSource: [{ required: true, message: '请选择线索来源', trigger: 'change' }]
      },
      // 省市区数据
      regionData: [], // 省市区选项数据，在mounted中初始化
      regionDataSelArr: [], // 已选择省市区
      // 线索来源选项
      leadSourceOptions: [
        { label: '自然到店', value: 'ZIRAN_DAODIAN' },
        { label: '抖音信息流', value: 'DOUYIN_XINXILIU' },
        { label: '微信朋友圈', value: 'WEIXIN_PENGYOUQUAN' },
        { label: '老带新', value: 'LAO_DAI_XIN' },
        { label: '美团点评', value: 'MEITUAN_DIANPING' }
      ],
      // 路由参数处理标志
      routeQueryProcessed: false,
      // 意向门店编辑相关
      intentionEntityBackup: {} // 用于存储编辑前的原始值
    };
  },
  beforeRouteEnter(to, from, next) {
    next((vm) => {
      vm.isShowChannel = vm.$permission.permission(to.meta.Permission, "iBeauty-Workbench-ClueFollowUp-Channel");
      vm.isShowFollowUp = vm.$permission.permission(to.meta.Permission, "iBeauty-Workbench-ClueFollowUp-EntityRang");
      vm.isCustomerPhoneNumberView = vm.$permission.permission(to.meta.Permission, "iBeauty-Workbench-ClueFollowUp-CustomerPhoneNumberView");
      vm.isCustomerPhoneNumberModify = vm.$permission.permission(to.meta.Permission, "iBeauty-Workbench-ClueFollowUp-CustomerPhoneNumberModify");

      vm.isCustomerBasicInformationModify = vm.$permission.permission(to.meta.Permission, "iBeauty-Workbench-ClueFollowUp-CustomerBasicInformationModify");
      vm.isCustomerServicerModify = vm.$permission.permission(to.meta.Permission, "iBeauty-Workbench-ClueFollowUp-CustomerServicerModify");
      vm.isCustomerBasicFileModify = vm.$permission.permission(to.meta.Permission, "iBeauty-Workbench-ClueFollowUp-CustomerBasicFileModify");

      // vm.CustomerCases.Add = vm.$permission.permission(to.meta.Permission, "iBeauty-Workbench-FollowUp-Cases-Add");
      // vm.CustomerCases.Update = vm.$permission.permission(to.meta.Permission, "iBeauty-Workbench-FollowUp-Cases-Update");
      // vm.CustomerCases.Delete = vm.$permission.permission(to.meta.Permission, "iBeauty-Workbench-FollowUp-Cases-Delete");
      // vm.CustomerCases.SelectStencil = vm.$permission.permission(to.meta.Permission, "iBeauty-Workbench-FollowUp-Cases-SelectStencil");
      // vm.CustomerCases.SaveStencil = vm.$permission.permission(to.meta.Permission, "iBeauty-Workbench-FollowUp-Cases-SaveStencil");
      // vm.CustomerCases.DeleteStencil = vm.$permission.permission(to.meta.Permission, "iBeauty-Workbench-FollowUp-Cases-DeleteStencil");
      // vm.CustomerCases.PrescriptionAdd = vm.$permission.permission(to.meta.Permission, "iBeauty-Workbench-FollowUp-Prescription-Add");
      // vm.CustomerCases.PrescriptionUpdate = vm.$permission.permission(to.meta.Permission, "iBeauty-Workbench-FollowUp-Prescription-Update");
      vm.isAnewAssign = vm.$permission.permission(to.meta.Permission, "iBeauty-Workbench-FollowUp-AnewAssign");
      if (to.params.customerID != undefined) {
        CustomerAPI.getCustomerInfo({ ID: to.params.customerID }).then(function (res) {
          if (res.StateCode == 200) {
            vm.customerID = res.Data.ID;
            vm.customerFullName = res.Data.Name;
            vm.customerPhoneNumber = res.Data.PhoneNumber;
            vm.customerName = res.Data.Name + "【" + res.Data.PhoneNumber + "】";
          } else {
            this.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        });
      }
    });
  },
  watch: {
    "ruleForm.Attachment": {
      deep: true,
      immediate: true,
      handler(val) {
        this.preview_src_list = [];
        this.preview_src_list = val.map((i) => i.AttachmentURL);
      },
    },
    filterText(val) {
      var that = this;
      if (that.$refs.treeRef) {
        that.$refs.treeRef.filter(val);
      }
    },

    // 监听路由变化 - 简化处理
    '$route'(to, from) {
      console.log('路由变化:', { to: to.path, from: from.path, query: to.query });

      // 如果是从其他页面跳转过来的，且有预约参数，则处理
      if (to.path !== from.path && to.query.action && to.query.customerID) {
        console.log('检测到跨页面跳转，处理预约参数');
        // 重置标志并处理
        this.routeQueryProcessed = false;
        setTimeout(() => {
          this.handleRouteQuery();
        }, 1000);
      }
    },

    // 监听客户详情弹框的显示状态
    customerDetailVisible(newVal, oldVal) {
      // 当弹框从显示状态变为隐藏状态时，刷新列表数据
      if (oldVal === true && newVal === false) {
        console.log('客户详情弹框已关闭，刷新线索列表');
        this.getFollowUp();
      }
    },

    // 监听意向门店编辑状态变化，确保正确回填门店名称
    'tabData': {
      deep: true,
      handler(newTabData) {
        if (newTabData && newTabData.length > 0) {
          newTabData.forEach(row => {
            // 当行进入编辑状态时，确保下拉框显示正确的门店名称
            if (row.editingIntentionEntity && row.IntentionEntityID) {
              this.$nextTick(() => {
                // 根据ID找到对应的门店名称
                const selectedEntity = this.entityList.find(entity =>
                  String(entity.ID) === String(row.IntentionEntityID)
                );
                if (selectedEntity) {
                  // 确保下拉框的值是门店名称而不是ID
                  console.log('回填意向门店:', {
                    id: row.IntentionEntityID,
                    name: selectedEntity.EntityName
                  });
                  // 这里不需要修改 IntentionEntityID，因为 el-select 的 v-model 绑定的是 ID
                  // 但是要确保 entityList 中有对应的选项
                }
              });
            }
          });
        }
      }
    },
  },
  /**计算属性  */
  computed: {
    /*     tableHeight: function() {
	     return (window.innerHeight-200) + 'px';
	   } */
  },
  /**  方法集合  */
  methods: {
    /**  保存指派  */
    onAddSubmitAnewAssign() {
      let that = this;
      that.$refs.FollowUpByRef.validate((valid) => {
        if (valid) {
          that.followUp_anewAssign();
        }
      });
    },
    /**    */
    anewAssignFollowUpClick(row) {
      let that = this;
      that.FollowUpByFrom = {
        ID: row.ID,
        FollowUpBy: "",
      };
      that.dialogVisibleAnewAssign = true;
    },
    /**  搜索其他人员  */
    searchEmpRemote(query) {
      this.getSearch(query);
    },
    /**    */
    getChannelNames(items) {
      if (!items) {
        return "";
      }
      return items.reduce((per, cur, index) => {
        if (items.length - 1 == index) {
          return per + cur.EmployeeName;
        } else {
          return per + cur.EmployeeName + ",";
        }
      }, "");
    },
    /* 性别状态显示转换 */
    formatGender: function (row) {
      switch (row.Gender) {
        case "1":
          return "男";
        case "2":
          return "女";
        case "0":
          return "未知";
      }
    },
    /* 搜索 */
    handleSearch() {
      let that = this;
      that.paginations.page = 1;
      that.CustomerID = null;
      that.getFollowUp();
    },
    /* 分页 */
    handleCurrentChange(page) {
      let that = this;
      that.paginations.page = page;
      that.CustomerID = null;
      that.getFollowUp();
    },
    /* 每页条数变化 */
    handleSizeChange(size) {
      let that = this;
      that.paginations.page_size = size;
      that.paginations.page = 1; // 重置到第一页
      that.CustomerID = null;
      that.getFollowUp();
    },
    /* 新建线索 */
    addFollowUp() {
      let that = this;
      // 直接打开创建线索弹框
      that.showCreateLeadDialog = true;
      that.resetCreateLeadForm();
    },
    /* 处理跟进 */
    followUpClick(row) {
      let that = this;
      that.isAdd = false;
      that.followUpCustomerID = row.CustomerID;
      that.leadID = row.ID; // 设置线索ID
      that.CreatedByName = row.CreatedByName;
      that.PlannedRemark = row.PlannedRemark;
      that.selectFollowUpRow = row;
      that.ID = row.ID;
      that.nextDateTime = this.$formatDate(new Date(), "hh:mm:ss");
      that.ruleForm = {
        FollowUpMethodID: "", // 跟进方式
        FollowUpStatusID: "", // 跟进状态
        FollowUpContent: "", // 跟进记录
        PlannedOn: "", // 计划跟进时间
        IsNextFollowUp: true, // 下次是否跟进
        PlannedRemark: "", // 计划跟进备注
        Attachment: [],
      };
      if (this.$refs.ruleForm) {
        this.$refs["ruleForm"].resetFields();
      }
      that.getCustomerDetail();
    },
    /**  跟进弹窗关闭  */
    closeAddFollowUpDialog() {
      this.ruleForm.Attachment = [];
    },
    /* 跟进列表  只能看自己/权限下 */
    getFollowUp() {
      let that = this;
      let params = {
        PageNum: that.paginations.page,
        PageSize: that.paginations.page_size, //每页显示条数
        Name: that.search.Name, //名称

        FollowUpMethodID: that.search.FollowUpMethodID, //跟进方式
        FollowUpStatusID: that.search.FollowUpStatusID, //跟进状态
        FollowUpBy: that.search.FollowUpBy, //跟进人
        FollowUpEntity: that.search.FollowUpEntity, //跟进部门
        PlannedOnStart: that.search.QueryDate ? this.search.QueryDate[0] : "", //计划跟进开始时间
        PlannedOnEnd: that.search.QueryDate ? this.search.QueryDate[1] : "", //计划跟进结束时间
        AppointmentStartDate: that.search.AppointmentDate ? this.search.AppointmentDate[0] : "", //预约开始时间
        AppointmentEndDate: that.search.AppointmentDate ? this.search.AppointmentDate[1] : "", //预约结束时间
        CustomerCreatedStartDate: that.search.CustomerCreatedDate ? this.search.CustomerCreatedDate[0] : "", //线索创建开始时间
        CustomerCreatedEndDate: that.search.CustomerCreatedDate ? this.search.CustomerCreatedDate[1] : "", //线索创建结束时间
        IsShowOwnFollowUp: that.search.IsShowOwnFollowUp, //是否显示自己的跟进
        AppointmentStatus: that.search.AppointmentStatus, //预约状态
        LeadSource: that.search.LeadSource, //线索来源
        Status: that.search.Status, //线索状态
        IsDeal: that.search.IsDeal, //成交会员
      };
      if (that.isShowFollowUp) {
        this.getFollowUpAll(params);
      } else {
        this.getFollowUpList(params);
      }
    },
    /** 权限下的跟进 - 切换为线索列表接口 */
    async getFollowUpAll(params) {
      let that = this;
      // 转换参数格式为新的线索列表接口格式
      let leadParams = {
        PageNum: params.PageNum || 1,
        PageSize: params.PageSize || 10,
        CustomerName: params.Name || "", // 客户姓名搜索
        LeadSource: params.LeadSource || "", // 线索来源
        Status: params.Status !== "" ? params.Status : undefined, // 线索状态
        StartDate: params.CustomerCreatedStartDate || "", // 线索创建开始时间
        EndDate: params.CustomerCreatedEndDate || "", // 线索创建结束时间
        PlannedOnStart: params.PlannedOnStart || "", // 计划跟进开始时间
        PlannedOnEnd: params.PlannedOnEnd || "", // 计划跟进结束时间
        IsOwnOnly: params.IsShowOwnFollowUp || false, // 只显示自己的线索
        AssignedTo: params.FollowUpBy || "", // 分配给谁（跟进人员）
        IsDeal: params.IsDeal, // 成交会员
        AppointmentStatus: params.AppointmentStatus, // 预约状态
      };
      
      // 调用新的线索列表接口
      let res = await API.getLeadList(leadParams);
      if (res.StateCode == 200) {
        // 处理线索数据，转换为兼容原有格式的数据结构
        that.clueTableData = res.List.map(item => ({
          ...item,
          // 保持原有字段兼容性
          ID: item.ID, // 线索ID
          CustomerID: item.CustomerID,
          CustomerName: item.CustomerName,
          PhoneNumber: item.PhoneNumber,
          LeadSource: item.LeadSource,
          LeadSourceName: item.LeadSourceName,
          Status: item.Status, // 线索状态
          StatusName: item.StatusName,
          // IsNew字段已移除，直接使用Status判断NEW标识
          CreatedOn: item.LeadCreatedOn, // 线索创建时间
          AssignedTo: item.AssignedTo,
          AssignedToName: item.AssignedToName,
          Address: item.Address,
          Remark: item.Remark,
          // 客户等级相关字段
          CustomerLevelName: item.CustomerLevelName || null,
          // 预约相关字段保持兼容
          HasAppointment: item.HasAppointment || (item.AppointmentStatus && item.AppointmentStatus !== 0) || false,
          AppointmentStatus: item.AppointmentStatus || null,
          AppointmentDate: item.AppointmentDate || null,
          AppointmentID: item.AppointmentID || null,
          AppointmentEntityName: item.AppointmentEntityName || null,
          // 意向门店相关字段 - 只保留ID，名称通过匹配获取
          IntentionEntityID: item.IntentionEntityID ? parseInt(item.IntentionEntityID) || item.IntentionEntityID : null,
          // 跟进相关字段已移除，直接使用Status字段
        }));

        // 设置意向门店名称
        that.setIntentionEntityNames();

        that.paginations.total = res.Total;
        that.paginations.page_size = res.PageSize;
      } else {
        that.$message.error({
          message: res.Message || "获取线索列表失败",
          duration: 2000,
        });
      }
    },
    /** 自己的跟进 - 切换为线索列表接口 */
    async getFollowUpList(params) {
      let that = this;
      that.loading = true;
      
      // 转换参数格式为新的线索列表接口格式
      let leadParams = {
        PageNum: params.PageNum || 1,
        PageSize: params.PageSize || 10,
        CustomerName: params.Name || "", // 客户姓名搜索
        LeadSource: params.LeadSource || "", // 线索来源
        Status: params.Status !== "" ? params.Status : undefined, // 线索状态
        StartDate: params.CustomerCreatedStartDate || "", // 线索创建开始时间
        EndDate: params.CustomerCreatedEndDate || "", // 线索创建结束时间
        PlannedOnStart: params.PlannedOnStart || "", // 计划跟进开始时间
        PlannedOnEnd: params.PlannedOnEnd || "", // 计划跟进结束时间
        IsOwnOnly: true, // 只显示自己的线索
        AssignedTo: "", // 当前用户的线索
        IsDeal: params.IsDeal, // 成交会员
        AppointmentStatus: params.AppointmentStatus, // 预约状态
      };
      
      // 调用新的线索列表接口
      let res = await API.getLeadList(leadParams);
      if (res.StateCode == 200) {
        // 处理线索数据，转换为兼容原有格式的数据结构
        that.clueTableData = res.List.map(item => ({
          ...item,
          // 保持原有字段兼容性
          ID: item.ID, // 线索ID
          CustomerID: item.CustomerID,
          CustomerName: item.CustomerName,
          PhoneNumber: item.PhoneNumber,
          LeadSource: item.LeadSource,
          LeadSourceName: item.LeadSourceName,
          Status: item.Status, // 线索状态
          StatusName: item.StatusName,
          // IsNew字段已移除，直接使用Status判断NEW标识
          CreatedOn: item.LeadCreatedOn, // 线索创建时间
          AssignedTo: item.AssignedTo,
          AssignedToName: item.AssignedToName,
          Address: item.Address,
          Remark: item.Remark,
          // 客户等级相关字段
          CustomerLevelName: item.CustomerLevelName || null,
          // 预约相关字段保持兼容
          HasAppointment: item.HasAppointment || false,
          AppointmentStatus: item.AppointmentStatus || null,
          AppointmentDate: item.AppointmentDate || null,
          AppointmentID: item.AppointmentID || null,
          AppointmentEntityName: item.AppointmentEntityName || null, // 预约门店名称
          // 意向门店相关字段 - 只保留ID，名称通过匹配获取
          IntentionEntityID: item.IntentionEntityID ? parseInt(item.IntentionEntityID) || item.IntentionEntityID : null,
          // 跟进相关字段已移除，直接使用Status字段
        }));

        // 设置意向门店名称
        that.setIntentionEntityNames();

        that.paginations.total = res.Total;
        that.paginations.page_size = res.PageSize;
      } else {
        that.$message.error({
          message: res.Message || "获取线索列表失败",
          duration: 2000,
        });
      }

      that.loading = false;
    },
    /* 列表行双击事件处理 */
    handleRowDblClick(row, column, event) {
      // 检查是否点击的是操作列，如果是则不触发详情
      if (column && column.label === '操作') {
        return;
      }
      this.rowClick(row);
    },

    /* 列表行点击事件 */
    rowClick(row) {
      let that = this;
      that.customerDetailVisible = true;
      that.CustomerID = row.CustomerID;
      this.customerInfo = {
        Name: row.CustomerName,
        ID: row.CustomerID,
        // 传递预约状态信息
        HasAppointment: row.HasAppointment,
        AppointmentStatus: row.AppointmentStatus,
        AppointmentID: row.AppointmentID,
        Status: row.Status,
        // 传递线索ID
        LeadID: row.ID,
      };
    },

    // 客户名称点击事件，效果和双击行一样
    handleCustomerNameClick(row) {
      // 调用双击行的处理方法
      this.handleRowDblClick(row, null, null);
    },

    // 获取山东省数据
    getShandongData() {
      // 从完整的省市区数据中筛选出山东省的数据
      const shandongProvince = regionData.find(province => province.label === '山东省');
      return shandongProvince ? [shandongProvince] : [];
    },
    /* 是否只显示自己的跟进 */
    IsShowOwnFollowUpChange() {
      let that = this;
      that.handleSearch();
    },
    /* 保存 */
    submitFollowUp() {
      let that = this;
      if (that.isAdd) {
        if (that.customerID) {
          this.$refs.ruleForm.validate((valid) => {
            if (valid) {
              that.modalLoading = true;
              let params = that.ruleForm;
              params.CustomerID = that.customerID;
              params.LeadID = that.leadID; // 添加线索ID
              params.PlannedOn = this.ruleForm.PlannedOn ? this.$formatDate(this.ruleForm.PlannedOn, "YYYY-MM-DD hh:mm") : "";
              API.followUpByLead(params)
                .then(function (res) {
                  if (res.StateCode === 200) {
                    that.$message.success({
                      message: "新建成功",
                      duration: 2000,
                    });
                    that.dialogVisible = false;
                    that.getFollowUp();

                    // 如果客户详情弹框是打开的，刷新跟进记录页签
                    if (that.customerDetailVisible && that.$refs.customerDetail) {
                      that.$nextTick(() => {
                        // 刷新客户详情弹框中的跟进记录
                        if (that.$refs.customerDetail.$refs.followUpRecord) {
                          that.$refs.customerDetail.$refs.followUpRecord.getCustomerFollowUp();
                        }
                      });
                    }
                  } else {
                    that.$message.error({
                      message: res.Message,
                      duration: 2000,
                    });
                  }
                })
                .finally(function () {
                  that.modalLoading = false;
                });
            }
          });
        } else {
          this.$message("请先选择顾客");
        }
      } else {
        this.$refs.ruleForm.validate((valid) => {
          if (valid) {
            that.modalLoading = true;
            let params = that.ruleForm;
            params.CustomerID = that.followUpCustomerID;
            params.LeadID = that.leadID; // 添加线索ID
            (params.PlannedOn = this.ruleForm.PlannedOn ? this.$formatDate(this.ruleForm.PlannedOn, "YYYY-MM-DD hh:mm") : ""), (params.ID = that.ID);
            API.followUpByLead(params)
              .then(function (res) {
                if (res.StateCode === 200) {
                  that.$message.success({
                    message: "跟进更新成功",
                    duration: 2000,
                  });
                  that.dialogVisible = false;
                  that.getFollowUp();

                  // 如果客户详情弹框是打开的，刷新跟进记录页签
                  if (that.customerDetailVisible && that.$refs.customerDetail) {
                    that.$nextTick(() => {
                      // 刷新客户详情弹框中的跟进记录
                      if (that.$refs.customerDetail.$refs.followUpRecord) {
                        that.$refs.customerDetail.$refs.followUpRecord.getCustomerFollowUp();
                      }
                    });
                  }
                } else {
                  that.$message.error({
                    message: res.Message,
                    duration: 2000,
                  });
                }
              })
              .finally(function () {
                that.modalLoading = false;
              });
          }
        });
      }
    },
    /* 上传图片 */
    commodityMainbeforeUpload(file) {
      let that = this;
      const isSize200kb = file.size / 1024 < 200;
      if (!isSize200kb) {
        that.$message.error("上传图片大小不能超过 200kb!");
        return false;
      }
      utils.getImageBase64(file).then((base64) => {
        this.addAttachment(base64).then((AttachmentURL) => {
          that.$nextTick(() => {
            that.ruleForm.Attachment.push({
              AttachmentType: "10",
              AttachmentURL: AttachmentURL,
            });
          });
        });
      });
      // let reader = new FileReader();
      // reader.readAsDataURL(file);
      // reader.onload = function (evt) {
      //   let base64 = evt.target.result;
      //   that.$nextTick(() => {
      //     that.ruleForm.Attachment.push({
      //       AttachmentType: 10,
      //       AttachmentURL: base64,
      //     });
      //   });
      // };
      return false;
    },
    /* 查看大图 */
    DialogPreview(file) {
      document.getElementById(file.uid).click();
    },
    /* 删除图片 */
    commodityMainRemove(file) {
      if (file && file.status !== "success") return;
      let that = this;
      let index = that.ruleForm.Attachment.findIndex((item) => item.AttachmentURL == file.AttachmentURL);
      that.ruleForm.Attachment.splice(index, 1);
    },
    /* 顾客 */
    saleCustomerData: function (queryString, cb) {
      var that = this;
      that.loading = true;
      var params = {
        Name: queryString ? queryString : "",
      };
      CustomerAPI.getSaleCustomer(params)
        .then((res) => {
          if (res.StateCode == 200) {
            cb(res.Data);
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },
    handleCustomerSelect(item) {
      var filter_hidephone = this.$options.filters["hidephone"];
      if (item.ID != undefined) {
        this.customerID = item.ID;
        this.customerFullName = item.Name;
        this.customerPhoneNumber = item.PhoneNumber;
        this.customerName = item.Name + "【" + filter_hidephone(item.PhoneNumber) + "】";
      } else {
        if (/^1[3456789]\d{9}$/.test(this.customerName)) {
          this.addCustomerPhoneNumber = this.customerName;
        }
        this.addNewCustomer();
      }
    },
    removeCustomer() {
      this.customerID = null;
      this.customerFullName = "";
      this.customerPhoneNumber = "";
      this.customerName = "";
    },
    /* 新增线索 */
    addNewCustomer: function () {
      var that = this;
      // 确保门店列表已加载
      if (!that.entityList || that.entityList.length === 0) {
        that.getEntityList();
      }
      // 使用新的简化线索创建流程
      that.showCreateLeadDialog = true;
      // 如果输入的是手机号，预填充到表单中
      if (/^1[3456789]\d{9}$/.test(that.customerName)) {
        that.createLeadForm.PhoneNumber = that.customerName;
      }
    },
    /**  新增会员成功  */
    addCustomerSuccess(info) {
      var filter_hidephone = this.$options.filters["hidephone"];
      let that = this;
      that.customerID = info.ID;
      that.customerFullName = info.Name;
      that.customerPhoneNumber = info.PhoneNumber;
      that.customerName = info.Name + "【" + filter_hidephone(info.PhoneNumber) + "】";
    },

    // 省市区选择变化
    changeProvinceCityArea(value) {
      if (value && value.length >= 3) {
        this.createLeadForm.ProvinceCode = value[0];
        this.createLeadForm.CityCode = value[1];
        this.createLeadForm.AreaCode = value[2];
      } else {
        this.createLeadForm.ProvinceCode = '';
        this.createLeadForm.CityCode = '';
        this.createLeadForm.AreaCode = '';
      }
    },

    // 重置创建线索表单
    resetCreateLeadForm() {
      this.createLeadForm = {
        Name: '',
        PhoneNumber: '',
        Gender: '1',
        LeadSource: '',
        AssignedTo: '',
        ProvinceCode: '',
        CityCode: '',
        AreaCode: '',
        Address: '',
        Remark: '',
        IntentionEntityID: null // 意向门店ID
      };
      this.regionDataSelArr = [];
      if (this.$refs.createLeadFormRef) {
        this.$refs.createLeadFormRef.resetFields();
      }
    },

    // 提交创建线索
    async submitCreateLead() {
      let that = this;

      // 表单验证
      try {
        await that.$refs.createLeadFormRef.validate();
      } catch (error) {
        return;
      }

      that.createLeadLoading = true;

      try {
        const params = {
          Name: that.createLeadForm.Name,
          PhoneNumber: that.createLeadForm.PhoneNumber,
          Gender: that.createLeadForm.Gender,
          LeadSource: that.createLeadForm.LeadSource,
          AssignedTo: that.createLeadForm.AssignedTo || undefined,
          ProvinceCode: that.createLeadForm.ProvinceCode || undefined,
          CityCode: that.createLeadForm.CityCode || undefined,
          AreaCode: that.createLeadForm.AreaCode || undefined,
          Address: that.createLeadForm.Address || undefined,
          Remark: that.createLeadForm.Remark || undefined,
          IntentionEntityID: that.createLeadForm.IntentionEntityID || undefined
        };

        const res = await API.createLead(params);

        if (res.StateCode === 200) {
          that.$message.success('线索创建成功！');

          // 关闭创建线索弹框
          that.showCreateLeadDialog = false;
          that.resetCreateLeadForm();

          // 刷新列表
          that.getFollowUp();
        } else {
          that.$message.error(res.Message || '创建线索失败');
        }
      } catch (error) {
        console.error('创建线索失败:', error);
        that.$message.error('创建线索失败，请稍后重试');
      } finally {
        that.createLeadLoading = false;
      }
    },

    /**  获取列表服务人员信息  */
    getFirstServiceEmp(ServicerEmployee) {
      if (!ServicerEmployee || ServicerEmployee.length == 0) {
        return "";
      }
      let firstItem = ServicerEmployee[0];
      return firstItem.Name + ":" + this.getServicerEmpNames(firstItem.ServicerEmpList);
    },
    /* 服务人员处理  */
    getServicerEmpNames(ServicerEmpList) {
      if (!ServicerEmpList) {
        return "";
      }
      return ServicerEmpList.map((val) => (val ? val.Name : "")).join(", ");
    },
    /* 判断是否需要显示tooltip */
    shouldShowTooltip(ServicerEmployee) {
      if (!ServicerEmployee || ServicerEmployee.length === 0) {
        return false;
      }
      // 如果有多个服务类型或者单个服务类型的员工名称过长，则显示tooltip
      if (ServicerEmployee.length > 1) {
        return true;
      }
      const firstItem = ServicerEmployee[0];
      const empNames = this.getServicerEmpNames(firstItem.ServicerEmpList);
      const fullText = firstItem.Name + "：" + empNames;
      return fullText.length > 20; // 超过20个字符显示tooltip
    },

    /* 判断是否可以显示预约和呼叫按钮 */
    canShowAppointmentAndCallButtons(currentRow) {
      // 如果不是已跟进状态，按原逻辑显示
      if (currentRow.IsFollowUp != 1) {
        return true;
      }

      // 获取相同手机号的所有已跟进记录（不再限制预约状态）
      const samePhoneRecords = this.clueTableData.filter(row =>
        row.PhoneNumber === currentRow.PhoneNumber &&
        row.IsFollowUp == 1 &&
        row.PhoneNumber // 确保手机号不为空
      );

      // 如果只有一条记录，可以显示按钮
      if (samePhoneRecords.length <= 1) {
        return true;
      }

      // 找到实际跟进时间最近的记录
      let latestRecord = null;
      let latestTime = null;

      // 先尝试根据实际跟进时间判断
      for (let record of samePhoneRecords) {
        if (record.FollowUpOn) {
          const recordTime = new Date(record.FollowUpOn).getTime();
          if (!latestTime || recordTime > latestTime) {
            latestTime = recordTime;
            latestRecord = record;
          }
        }
      }

      // 如果没有找到有实际跟进时间的记录，则根据ID最大的记录来判断（假设ID越大越新）
      if (!latestRecord) {
        latestRecord = samePhoneRecords.reduce((latest, current) => {
          return current.ID > latest.ID ? current : latest;
        });
      }

      // 只有最新的记录才能显示按钮
      return latestRecord && latestRecord.ID === currentRow.ID;
    },
    /* 获取顾客信息 */
    getCustomerDetail() {
      const that = this;
      // 确保在打开弹框时重置loading状态
      that.modalLoading = false;

      // 根据isAdd状态选择正确的CustomerID
      const customerID = that.isAdd ? that.customerID : that.followUpCustomerID;

      cusAPI.getCustomerDetail({ CustomerID: customerID }).then((res) => {
        if (res.StateCode == 200) {
          that.customerDetail = res.Data;
          that.dialogVisible = true;
        } else {
          that.$message.error({
            message: res.Message,
            duration: 2000,
          });
        }
      });
    },
    /* 获取跟进方式列表 */
    getFollowUpMethod() {
      var that = this;
      var params = {
        Name: "",
        Active: true,
      };
      APIFollowUp.getFollowUpMethod(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.clueTableDataMethod = res.Data;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },
    /* 获取跟进类型列表 */
    getFollowUpStatus() {
      var that = this;
      var params = {
        Name: "",
        Active: true,
      };
      APIFollowUp.getFollowUpStatus(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.clueTableDataStatus = res.Data;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },
    /* 获取跟进部门 */
    getFollowUpEntity() {
      let that = this;
      let params = {};
      API.getFollowUpEntity(params).then((res) => {
        if (res.StateCode == 200) {
          that.followUpEntityData = res.Data;
        }
      });
    },
    /* 获取跟进人员 */
    getFollowUpEmployee() {
      let that = this;
      let params = {};
      API.getFollowUpEmployee(params).then((res) => {
        if (res.StateCode == 200) {
          that.followUpEmployeeData = res.Data;
        }
      });
    },
    /** 图片上传   */
    async addAttachment(base64) {
      let that = this;
      let params = { AttachmentURL: base64 };
      let res = await APIUpload.addAttachment(params);
      if (res.StateCode == 200) {
        return res.Data.AttachmentURL;
      } else {
        that.$message.error(res.Message);
      }
    },
    /* 获取指派-其他人员 */
    getSearch(SearchKey) {
      let that = this;
      let params = {
        SearchKey: SearchKey,
      };
      FollowUpAPI.getSearch(params).then((res) => {
        if (res.StateCode == 200) {
          that.searchData = res.Data;
        } else {
          that.$message.error({
            message: res.Message,
            duration: 2000,
          });
        }
      });
    },
    /**    */
    followUp_anewAssign() {
      let that = this;
      that.defaultLoading = true;
      let params = {
        leadId: parseInt(that.FollowUpByFrom.ID), // 线索ID（Integer）
        assignedTo: that.FollowUpByFrom.FollowUpBy, // 分配给谁（员工ID）
      };
      API.followUp_anewAssign(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.dialogVisibleAnewAssign = false;
            that.defaultLoading = false;
            that.$message.success("操作成功");
            that.getFollowUp();
          } else {
            that.defaultLoading = false;
            that.$message.error(res.Message);
          }
        })
        .catch((fail) => {
          that.defaultLoading = false;
          that.$message.error(fail);
        });
    },
    /**    */
    phoneCallBack_callBack(row) {
      let that = this;
      let params = {
        callee: row.PhoneNumber,
      };
      API.phoneCallBack_callBack(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.$message.success(res.Message);
          } else {
            that.$message.error(res.Message);
          }
        })
        .catch((fail) => {
          that.$message.error(fail);
        });
    },

    // 获取预约状态标签类型
    getAppointmentTagType(status) {
      const statusMap = {
        10: 'warning', // 未到店 - 橙色
        '10': 'warning',
        20: 'success', // 已到店 - 绿色
        '20': 'success',
        30: 'info',     // 已取消 - 灰色
        '30': 'info'
      };
      return statusMap[status] || 'info';
    },

    // 获取线索状态标签类型
    getLeadStatusTagType(status) {
      const statusMap = {
        0: 'info',      // 未知线索 - 灰色
        1: 'primary',   // 新线索 - 蓝色
        2: 'warning',   // 跟进中 - 橙色
        3: 'success'    // 已成交 - 绿色
      };
      return statusMap[status] || 'info';
    },

    // 获取预约状态文本
    getAppointmentStatusText(status) {
      const statusMap = {
        10: '已预约',
        '10': '已预约',
        20: '已到店',
        '20': '已到店',
        30: '已取消',
        '30': '已取消'
      };
      return statusMap[status] || '未知';
    },

    // 设置意向门店名称
    setIntentionEntityNames() {
      let that = this;
      if (that.tabData && that.tabData.length > 0 && that.entityList && that.entityList.length > 0) {
        that.tabData.forEach(row => {
          if (row.IntentionEntityID) {
            const entity = that.entityList.find(e => String(e.ID) === String(row.IntentionEntityID));
            if (entity) {
              that.$set(row, 'IntentionEntityName', entity.Name);
            }
          }
        });
      }
    },

    // 获取门店列表
    getEntityList() {
      var that = this;
      var params = {};
      return CommonAPI.POST("api/entity/allEntity", params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.entityList = (res.Data || []).map(item => ({
              ID: parseInt(item.ID) || item.ID,
              Name: item.Name || item.EntityName || '未知门店'
            }));
            // 门店列表加载完成后，设置意向门店名称
            that.setIntentionEntityNames();
            return Promise.resolve();
          } else {
            that.$message.error({
              message: res.Message || '获取门店列表失败',
              duration: 2000,
            });
            return Promise.reject(new Error(res.Message || '获取门店列表失败'));
          }
        })
        .catch((error) => {
          console.error('获取门店列表失败:', error);
          that.$message.error({
            message: '获取门店列表失败，请稍后重试',
            duration: 2000,
          });
          return Promise.reject(error);
        });
    },



    // ========== 意向门店编辑相关方法 ==========

    // 开始编辑意向门店
    handleIntentionEntityEdit(row) {
      let that = this;

      // 如果门店列表为空，先获取门店列表
      if (!that.entityList || that.entityList.length === 0) {
        that.getEntityList();
        // 等待门店列表加载完成后再开始编辑
        setTimeout(() => {
          that.startIntentionEntityEdit(row);
        }, 500);
      } else {
        that.startIntentionEntityEdit(row);
      }
    },

    // 开始编辑意向门店的具体逻辑
    startIntentionEntityEdit(row) {
      let that = this;

      // 备份原始值
      that.intentionEntityBackup[row.ID] = {
        IntentionEntityID: row.IntentionEntityID,
        IntentionEntityName: row.IntentionEntityName
      };

      // 如果有门店名称但没有门店ID，尝试根据名称找到ID
      if (row.IntentionEntityName && !row.IntentionEntityID) {
        const matchedEntity = that.entityList.find(entity =>
          entity.Name === row.IntentionEntityName
        );
        if (matchedEntity) {
          that.$set(row, 'IntentionEntityID', matchedEntity.ID);
        }
      }

      // 设置编辑状态
      that.$set(row, 'editingIntentionEntity', true);
      that.$set(row, 'savingIntentionEntity', false);

      // 下一帧聚焦到选择框
      that.$nextTick(() => {
        const selectRefs = that.$refs.intentionEntitySelect;
        if (selectRefs) {
          const selectRef = Array.isArray(selectRefs) ? selectRefs.find(ref => ref) : selectRefs;
          if (selectRef) {
            selectRef.focus();
          }
        }
      });
    },

    // 意向门店选择变化
    handleIntentionEntityChange(row) {
      // 根据选择的门店ID找到门店名称
      const selectedEntity = this.entityList.find(entity => String(entity.ID) === String(row.IntentionEntityID));
      if (selectedEntity) {
        row.IntentionEntityName = selectedEntity.Name || selectedEntity.EntityName || '';
      } else {
        row.IntentionEntityName = '';
      }
    },

    // 保存意向门店
    async handleIntentionEntitySave(row) {
      let that = this;

      // 防止重复保存
      if (row.savingIntentionEntity) {
        return;
      }

      that.$set(row, 'savingIntentionEntity', true);

      try {
        // 确保门店ID和名称的一致性
        if (row.IntentionEntityID) {
          const selectedEntity = that.entityList.find(entity => String(entity.ID) === String(row.IntentionEntityID));
          if (selectedEntity) {
            row.IntentionEntityName = selectedEntity.Name;
          }
        } else {
          row.IntentionEntityName = '';
        }

        // 调用API保存意向门店
        const params = {
          LeadID: row.ID,
          IntentionEntityID: row.IntentionEntityID ? String(row.IntentionEntityID) : null
        };

        console.log('保存意向门店参数:', params); // 调试日志

        const res = await API.updateLeadIntentionEntity(params);

        if (res.StateCode === 200) {
          // 保存成功
          that.$message.success('意向门店设置成功');

          // 清除编辑状态
          that.$set(row, 'editingIntentionEntity', false);
          that.$set(row, 'savingIntentionEntity', false);

          // 清除备份
          delete that.intentionEntityBackup[row.ID];

        } else {
          throw new Error(res.Message || '保存失败');
        }

      } catch (error) {
        console.error('保存意向门店失败:', error);
        that.$message.error(error.message || '保存失败，请重试');

        // 恢复原始值
        that.handleIntentionEntityCancel(row);
      }
    },

    // 取消编辑意向门店
    handleIntentionEntityCancel(row) {
      let that = this;

      // 恢复原始值
      const backup = that.intentionEntityBackup[row.ID];
      if (backup) {
        row.IntentionEntityID = backup.IntentionEntityID;
        row.IntentionEntityName = backup.IntentionEntityName;
        delete that.intentionEntityBackup[row.ID];
      }

      // 清除编辑状态
      that.$set(row, 'editingIntentionEntity', false);
      that.$set(row, 'savingIntentionEntity', false);
    },

    // 获取门店列表并加载预约数据（用于编辑）
    getEntityListForEdit(appointmentID) {
      var that = this;
      var params = {};
      CommonAPI.POST("api/entity/allEntity", params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.entityList = (res.Data || []).map(item => ({
              ID: parseInt(item.ID) || item.ID,
              Name: item.Name || item.EntityName || '未知门店'
            }));
            // 门店列表加载完成后，再加载预约数据
            that.loadAppointmentData(appointmentID);
          } else {
            that.$message.error({
              message: res.Message || '获取门店列表失败',
              duration: 2000,
            });
          }
        })
        .catch((error) => {
          console.error('获取门店列表失败:', error);
          that.$message.error({
            message: '获取门店列表失败，请稍后重试',
            duration: 2000,
          });
        });
    },

    // 预约相关方法
    addAppointmentClick(row) {
      let that = this;
      that.CustomerID = row.CustomerID;
      that.leadID = row.ID; // 设置线索ID
      that.customerName = row.CustomerName;
      that.isAdd = true; // 设置为新增模式
      that.currentAppointmentID = null; // 清空当前预约ID
      that.appointmentDialogShow = true;
      that.getEntityList(); // 获取门店列表
      that.AppointmentInfoRuleForm = {
        AppointmentTypeID: "",
        EntityID: "",
        AppointmentDate: that.$formatDate(new Date(), "YYYY-MM-DD"),
        Time: "",
        Period: 30,
        Remark: "",
      };
      that.appointmentProjectList = []; // 清空项目列表
      that.clueSelectedTableData = []; // 清空已选择的项目
      that.getAppointmentConfig();
      that.getAppointmentType();
      that.getServicerEmployee();
      // 获取客户预约数量
      if (that.AppointmentInfoRuleForm.AppointmentDate) {
        that.appointmentBill_getCustomerAppointmentNumber();
      }
    },

    // 处理客户详情中跟进记录的预约事件
    handleCustomerDetailAppointment(appointmentData) {
      let that = this;

      if (appointmentData.action === 'edit') {
        // 修改预约
        that.CustomerID = appointmentData.CustomerID;
        that.customerName = appointmentData.CustomerName;
        that.isAdd = false; // 设置为编辑模式
        that.currentAppointmentID = appointmentData.AppointmentID;

        // 先获取门店列表，然后加载预约数据
        that.getEntityListForEdit(appointmentData.AppointmentID);
      } else {
        // 新建预约 - 使用与外层列表预约按钮相同的逻辑
        that.CustomerID = appointmentData.CustomerID;
        that.leadID = appointmentData.LeadID; // 可能为null，这是正常的
        that.customerName = appointmentData.CustomerName;
        that.isAdd = true; // 设置为新增模式
        that.currentAppointmentID = null; // 清空当前预约ID
        that.appointmentDialogShow = true;
        that.getEntityList(); // 获取门店列表
        that.AppointmentInfoRuleForm = {
          AppointmentTypeID: "",
          EntityID: "",
          AppointmentDate: that.$formatDate(new Date(), "YYYY-MM-DD"),
          Time: "",
          Period: 30,
          Remark: "",
        };
        that.appointmentProjectList = []; // 清空项目列表
        that.clueSelectedTableData = []; // 清空已选择的项目
        that.getAppointmentConfig();
        that.getAppointmentType();
        that.getServicerEmployee();
      }
    },

    // 处理客户详情中跟进记录的取消预约事件
    handleCustomerDetailCancelAppointment(cancelData) {
      let that = this;
      that.$confirm('确定要取消这个预约吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        that.cancelAppointment(cancelData.AppointmentID, null);
      }).catch(() => {
        // 用户取消操作
      });
    },

    // 处理客户详情中跟进记录的跟进事件
    handleCustomerDetailFollowUp(followUpData) {
      let that = this;
      // 使用与外层列表跟进按钮相同的逻辑
      that.isAdd = true; // 修正：这是新建跟进，应该设置为true
      that.customerID = followUpData.CustomerID; // 修正：应该设置customerID而不是followUpCustomerID
      that.leadID = followUpData.LeadID; // 线索ID
      that.customerName = followUpData.CustomerName;
      that.CreatedByName = ''; // 这个在客户详情中可能没有
      that.PlannedRemark = '';
      that.selectFollowUpRow = {
        CustomerID: followUpData.CustomerID,
        ID: followUpData.LeadID
      };
      that.ID = followUpData.LeadID;
      that.nextDateTime = this.$formatDate(new Date(), "hh:mm:ss");

      // 重置loading状态，确保按钮可以正常使用
      that.modalLoading = false;

      that.ruleForm = {
        FollowUpMethodID: "", // 跟进方式
        FollowUpStatusID: "", // 跟进状态
        FollowUpContent: "", // 跟进记录
        PlannedOn: "", // 计划跟进时间
        IsNextFollowUp: true, // 下次是否跟进
        PlannedRemark: "", // 计划跟进备注
        Attachment: [],
      };
      if (this.$refs.ruleForm) {
        this.$refs["ruleForm"].resetFields();
      }
      that.getCustomerDetail();
    },

    // 处理预约记录组件的取消预约事件
    handleAppointmentCancelled(cancelData) {
      let that = this;

      // 刷新跟进列表
      that.getFollowUp();

      // 刷新客户详情中的预约记录
      that.refreshCustomerDetailAppointments();
    },

    // 处理预约更新事件
    handleAppointmentUpdated() {
      let that = this;
      console.log('预约信息已更新，刷新线索列表');

      // 刷新线索列表数据
      that.getFollowUp();
    },

    // 处理客户详情关闭时的刷新列表事件
    handleRefreshList() {
      let that = this;
      console.log('客户基本信息已更新，刷新线索列表');

      // 刷新线索列表数据
      that.getFollowUp();
    },

    // 修改预约
    editAppointmentClick(row) {
      let that = this;
      that.CustomerID = row.CustomerID;
      that.customerName = row.CustomerName;
      that.isAdd = false; // 设置为编辑模式
      that.currentAppointmentID = row.AppointmentID;

      // 先获取门店列表，然后加载预约数据
      that.getEntityListForEdit(row.AppointmentID);
    },

    // 取消预约
    cancelAppointmentClick(row) {
      let that = this;
      that.$confirm('确定要取消这个预约吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        that.cancelAppointment(row.AppointmentID, row);
      });
    },

    // 处理预约下拉菜单命令
    handleAppointmentCommand(command) {
      let that = this;
      const { action, row } = command;

      if (action === 'edit') {
        that.editAppointmentClick(row);
      } else if (action === 'cancel') {
        that.cancelAppointmentClick(row);
      }
    },

    // 测试预约状态方法
    testAppointmentStatus(row) {
      let that = this;
      // 模拟设置预约状态
      row.HasAppointment = true;
      row.AppointmentStatus = 10;
      row.AppointmentDate = '2024-01-15 10:00:00';
      row.AppointmentID = 123;

      that.$message.success('已设置为预约状态，现在应该显示预约管理按钮');

      // 强制更新视图
      that.$forceUpdate();
    },

    // 获取预约配置
    getAppointmentConfig() {
      // 这里可以调用API获取预约配置，暂时使用默认值
      this.appointmentConfigInfo = {
        StartTime: "08:00",
        EndTime: "20:00",
        Period: "30",
        AppointmentServicerIsRequired: false,
      };
    },

    // 获取预约类型
    async getAppointmentType() {
      let that = this;
      try {
        let params = {
          Name: '', //搜索名称
          Active: true, //有效性
        };
        let res = await appointmentTypeAPI.appointmentType_all(params);
        if (res.StateCode == 200) {
          that.appointmentTypeList = res.Data;
        } else {
          that.$message.error(res.Message);
        }
      } catch (error) {
        that.$message.error(error);
      }
    },

    // 获取服务人员
    getServicerEmployee() {
      // 这里可以调用API获取服务人员列表，暂时使用空数组
      this.addServicerEmployeeList = [];
    },

    // 添加预约项目
    addAppointmentProject() {
      var that = this;
      that.clueSelectedTableData = Object.assign([], that.appointmentProjectList);
      that.defaultCheckedKeysApplyApp = [];

      // 如果项目列表为空，先加载项目列表
      if (that.projectList.length === 0) {
        that.getProjectList();
      }

      that.selectProjectDialogState = true;
      that.$nextTick(() => {
        var defaultCheckedKeys = Enumerable.from(that.appointmentProjectList)
          .select((val) => '1' + val.ID)
          .toArray();
        if (that.$refs.treeRef) {
          that.$refs.treeRef.setCheckedKeys(defaultCheckedKeys);
        }
      });
    },

    // 删除预约项目
    deleteAppointmentProject(index) {
      this.appointmentProjectList.splice(index, 1);
    },

    // 适用项目弹框搜索事件
    filterNode(value, data) {
      if (!value) return true;
      return data.Name.indexOf(value) !== -1;
    },

    // 选择适用项目事件
    selectApplicableItems(item, list) {
      var that = this;
      that.clueSelectedTableData = Enumerable.from(list.checkedNodes)
        .where(function (i) {
          return i.IsProject;
        })
        .select((item) => ({
          ID: item.ID,
          Name: item.Name,
          PID: item.PID,
          ParentID: item.ParentID,
          Price: item.Price,
          ProjectCategoryName: item.ProjectCategoryName,
          TreatTime: item.TreatTime,
        }))
        .toArray();
    },

    // 选择项目确认
    confirmProjectSelect() {
      var that = this;
      var selectedTableData = Object.assign([], that.clueSelectedTableData);
      if (that.clueSelectedTableData.length == 0) {
        that.$message.error('请选择项目');
        return false;
      } else {
        var totalPeriod = 0;
        that.appointmentProjectList = Object.assign([], selectedTableData);
        that.selectProjectDialogState = false;
        that.appointmentProjectList.forEach((val) => {
          totalPeriod += val.TreatTime;
        });
        for (let i = 0; i <= that.timeArr.length - 1; i++) {
          if (that.timeArr[i].value >= totalPeriod) {
            that.AppointmentInfoRuleForm.Period = that.timeArr[i].value;
            break;
          }
        }
      }
    },

    // 删除所选中的适用项目
    deleteSelectRow(row, index) {
      var that = this;
      that.clueSelectedTableData.splice(index, 1);
      that.$nextTick(() => {
        var defaultCheckedKeys = Enumerable.from(that.clueSelectedTableData)
          .select((val) => val.PID)
          .toArray();
        if (that.$refs.treeRef) {
          that.$refs.treeRef.setCheckedKeys(defaultCheckedKeys);
        }
      });
    },

    // 递归设置项目数据
    setRecursion(data) {
      var that = this;
      for (let i = 0; i <= data.length - 1; i++) {
        if (data[i].IsProject) {
          data[i].PID = '1' + data[i].ID;
        } else {
          data[i].PID = '0' + data[i].ID;
        }
        if (data[i].Child) {
          that.setRecursion(data[i].Child);
        }
      }
    },

    // 获取项目列表
    getProjectList() {
      var that = this;
      var params = {};
      API.getProjectList(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.setRecursion(res.Data);
            that.projectList = Enumerable.from(res.Data)
              .where((i) => {
                if (!i.IsProject) {
                  i.Child = Enumerable.from(i.Child)
                    .where((i) => {
                      return !i.IsProject && i.Child.length > 0;
                    })
                    .toArray();
                }
                return !i.IsProject && i.Child.length > 0;
              })
              .toArray();
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .catch((error) => {
          console.error('获取项目列表失败:', error);
          that.$message.error('获取项目列表失败: ' + (error.message || error));
        });
    },

    // 预约日期改变
    changeHandleAppointmentDate() {
      let that = this;
      if (that.AppointmentInfoRuleForm.AppointmentDate) {
        that.appointmentBill_getCustomerAppointmentNumber();
      }
    },

    // 预约服务人员改变
    changeAppointmentServicer() {
      // 可以在这里处理服务人员改变的逻辑
    },

    // 保存预约
    saveAppointment(type) {
      var that = this;
      if (!that.CustomerID) {
        that.$message.error('请选择客户');
        return;
      }
      if (that.appointmentConfigInfo.AppointmentServicerIsRequired && that.addServicerEmployeeList.every((i) => !i.SelectEmployeeID)) {
        that.$message.error('请选择预约接待人');
        return;
      }

      if (that.appointmentDialogShow) {
        that.$refs['AppointmentInfoRuleForm'].validate((valid) => {
          if (valid) {
            if (!that.isAdd) {
              that.appointmentBillUpdate(type);
            } else {
              if (that.customerAppointmentNumber > 0) {
                that.customerAppointmentDialogVisible = true;
              } else {
                that.createAppointment();
              }
            }
          }
        });
      } else {
        if (!that.isAdd) {
          that.appointmentBillUpdate(type);
        } else {
          if (that.customerAppointmentNumber > 0) {
            that.customerAppointmentDialogVisible = true;
          } else {
            that.createAppointment();
          }
        }
      }
    },

    // 创建预约
    createAppointment() {
      var that = this;
      let params = {
        AppointmentDate: that.AppointmentInfoRuleForm.AppointmentDate + ' ' + that.AppointmentInfoRuleForm.Time, //预约时间
        CustomerID: that.CustomerID, //顾客iD
        LeadID: that.leadID, //线索ID
        Period: that.AppointmentInfoRuleForm.Period, //时长
        AppointmentTypeID: that.AppointmentInfoRuleForm.AppointmentTypeID, //预约类型
        EntityID: that.AppointmentInfoRuleForm.EntityID, //预约门店
        Remark: that.AppointmentInfoRuleForm.Remark, //备注
        AppointmentServicerIsRequired: false, //预约服务人员是否必填
        AppointmentCategory: 2, //预约分类
        Servicer: that.addServicerEmployeeList
          .filter((i) => i.SelectEmployeeID)
          .map((val) => {
            return {
              ServicerID: val.ServicerID,
              EmployeeID: val.SelectEmployeeID,
            };
          }), //预约角色集合

        Project: that.appointmentProjectList.map((val) => {
          return {
            ProjectID: val.ID,
          };
        }), //项目集合
      };
      that.saveLoading = true;
      API.appointmentBillCreate(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.$message.success({
              message: '成功创建预约',
              duration: 2000,
            });
            that.appointmentDialogShow = false;
            that.customerAppointmentDialogVisible = false;

            // 更新当前行的预约状态
            that.updateFollowUpAppointmentStatus(that.CustomerID, res.Data);

            // 刷新客户详情中的预约记录
            that.refreshCustomerDetailAppointments();

            // 可选：如果需要完整刷新可以启用下面这行
            // that.getFollowUp();
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.saveLoading = false;
        });
    },

    // 修改预约
    appointmentBillUpdate(type) {
      let that = this;
      let params = Object.assign({}, that.AppointmentInfoRuleForm);
      params.ID = that.currentAppointmentID; //预约ID
      params.CustomerID = that.CustomerID; //顾客iD
      params.AppointmentDate = that.AppointmentInfoRuleForm.AppointmentDate + ' ' + that.AppointmentInfoRuleForm.Time; //预约时间
      params.AppointmentCategory = 2; //预约分类
      params.Status = 10; //预约状态：10表示已预约
      params.Servicer = that.addServicerEmployeeList
        .filter((i) => i.SelectEmployeeID)
        .map((val) => {
          return {
            ServicerID: val.ServicerID,
            EmployeeID: val.SelectEmployeeID,
          };
        }); //预约角色集合
      params.Project = that.appointmentProjectList.map((i) => {
        return {
          ProjectID: i.ID,
        };
      });
      that[type] = true;
      API.appointmentBillUpdate(params)
        .then((res) => {
          if (res.StateCode == 200) {
            if (that.cancelState) {
              this.$message({
                type: 'success',
                message: '取消成功',
              });
              that.cancelState = false;
            } else {
              this.$message({
                type: 'success',
                message: '修改成功',
              });
            }
            that.appointmentDialogShow = false;
            // 刷新跟进列表
            that.getFollowUp();

            // 刷新客户详情中的预约记录
            that.refreshCustomerDetailAppointments();
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that[type] = false;
        });
    },

    // 确认客户预约
    confirmCustomerAppointment() {
      let that = this;
      that.createAppointment();
    },

    // 获取客户预约数量
    appointmentBill_getCustomerAppointmentNumber() {
      let that = this;
      if (!that.CustomerID) {
        return;
      }
      let params = {
        CustomerID: that.CustomerID,
        AppointmentDate: that.AppointmentInfoRuleForm.AppointmentDate,
      };
      API.appointmentBill_getCustomerAppointmentNumber(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.customerAppointmentNumber = res.Data.CustomerAppointmentNumber;
            if (that.customerAppointmentNumber > 0) {
              that.appointmentBill_getCustomerAppointmentAll();
            }
          } else {
            that.$message.error(res.Message);
          }
        })
        .catch((fail) => {
          that.$message.error(fail);
        });
    },

    // 获取客户所有预约
    appointmentBill_getCustomerAppointmentAll() {
      let that = this;
      if (!that.CustomerID) {
        return;
      }
      let params = {
        CustomerID: that.CustomerID,
        AppointmentDate: that.AppointmentInfoRuleForm.AppointmentDate,
      };
      API.appointmentBill_getCustomerAppointmentAll(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.customerAppointmentAll = res.Data;
          } else {
            that.$message.error(res.Message);
          }
        })
        .catch((fail) => {
          that.$message.error(fail);
        });
    },

    // 加载预约数据（用于编辑）
    loadAppointmentData(appointmentID) {
      let that = this;
      let params = {
        ID: appointmentID
      };
      API.appointmentBillDetail(params)
        .then((res) => {
          if (res.StateCode == 200) {
            let data = res.Data;
            
            // 处理预约日期和时间
            let appointmentDate = "";
            let appointmentTime = "";
            
            if (data.AppointmentDate) {
              // 如果AppointmentDate包含完整的日期时间
              if (data.AppointmentDate.includes(' ')) {
                let dateTimeParts = data.AppointmentDate.split(' ');
                appointmentDate = dateTimeParts[0];
                appointmentTime = dateTimeParts[1] ? dateTimeParts[1].substring(0, 5) : "";
              } else {
                // 如果只是日期
                appointmentDate = data.AppointmentDate;
              }
            }
            
            // 如果有单独的Time字段，优先使用
            if (data.Time) {
              appointmentTime = data.Time.length > 5 ? data.Time.substring(0, 5) : data.Time;
            }

            that.AppointmentInfoRuleForm = {
              ID: data.ID,
              AppointmentTypeID: data.AppointmentTypeID,
              EntityID: data.EntityID ? String(data.EntityID) : "",
              AppointmentDate: appointmentDate,
              Time: appointmentTime,
              Period: data.Period,
              Remark: data.Remark,
            };
            that.appointmentProjectList = data.Project || [];
            that.addServicerEmployeeList = data.Servicer || [];
            that.clueSelectedTableData = Object.assign([], that.appointmentProjectList);
            
            // 打开预约弹框
            that.appointmentDialogShow = true;
            // 获取预约配置和类型
            that.getAppointmentConfig();
            that.getAppointmentType();
            that.getServicerEmployee();
          } else {
            that.$message.error(res.Message);
          }
        })
        .catch((error) => {
          that.$message.error('加载预约数据失败');
        });
    },

    // 取消预约
    cancelAppointment(appointmentID, row) {
      let that = this;
      let params = {
        ID: appointmentID,
        Status: 30 // 30表示已取消
      };
      that.saveLoading = true;
      API.appointmentBillCancel(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.$message.success('预约已取消');
            // 更新当前行的预约状态
            row.AppointmentStatus = 30;
            // 可选：刷新整个列表
            // that.getFollowUp();
          } else {
            that.$message.error(res.Message);
          }
        })
        .catch((error) => {
          that.$message.error('取消预约失败');
        })
        .finally(() => {
          that.saveLoading = false;
        });
    },

    // 预约成功后更新列表数据
    updateFollowUpAppointmentStatus(customerID, appointmentData) {
      let that = this;
      let targetRow = that.clueTableData.find(item => item.CustomerID === customerID);
      if (targetRow) {
        targetRow.HasAppointment = true;
        targetRow.AppointmentStatus = 10; // 未到店
        targetRow.AppointmentDate = appointmentData.AppointmentDate;
        targetRow.AppointmentID = appointmentData.ID;
      }
    },

    // 手动标记成交
    markConverted(lead) {
      let that = this;
      that.$prompt('请输入成交备注（如：客户已成交办卡）', '标记成交', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputType: 'textarea',
        inputPlaceholder: '请输入成交备注...'
      }).then(({ value }) => {
        if (!value || value.trim() === '') {
          that.$message.warning('请输入成交备注');
          return;
        }

        that.saveLoading = true;
        // 调用标记转化接口
        API.markConverted({
          leadId: parseInt(lead.ID), // 确保传递为integer类型
          remark: value.trim()
        }).then(res => {
          if (res.StateCode === 200) {
            that.$message.success('线索已标记为成交');
            // 更新当前行的状态
            lead.Status = 3;
            lead.StatusName = '已成交';
            // 可选：刷新整个列表
            // that.getFollowUp();
          } else {
            that.$message.error(res.Message || '标记成交失败');
          }
        }).catch(error => {
          console.error('标记成交失败:', error);
          that.$message.error('标记成交失败');
        }).finally(() => {
          that.saveLoading = false;
        });
      }).catch(() => {
        // 用户取消操作
      });
    },

    // 处理路由参数
    handleRouteQuery() {
      let that = this;
      const query = that.$route.query;

      console.log('=== 开始处理路由参数 ===');
      console.log('当前路由:', that.$route.path);
      console.log('路由参数:', query);
      console.log('处理标志:', that.routeQueryProcessed);

      // 避免重复处理
      if (that.routeQueryProcessed) {
        console.log('路由参数已处理，跳过');
        return;
      }

      if (query.action && query.customerID) {
        console.log('检测到有效的路由参数，准备处理预约操作');
        that.routeQueryProcessed = true; // 标记为已处理

        // 延迟执行，确保页面初始化完成
        that.$nextTick(() => {
          console.log('开始执行预约操作处理');
          if (query.action === 'editAppointment' && query.appointmentID) {
            console.log('处理修改预约:', {
              customerID: query.customerID,
              customerName: query.customerName,
              appointmentID: query.appointmentID
            });

            // 修改预约
            that.CustomerID = parseInt(query.customerID);
            that.customerName = query.customerName || '';
            that.isAdd = false; // 设置为编辑模式
            that.currentAppointmentID = query.appointmentID;

            // 先获取门店列表，然后加载预约数据
            that.getEntityListForEdit(query.appointmentID);
          } else if (query.action === 'createAppointment') {
            // 新建预约
            that.CustomerID = parseInt(query.customerID);
            that.customerName = query.customerName || '';
            that.isAdd = true; // 设置为新增模式
            that.currentAppointmentID = null;
            that.appointmentDialogShow = true;
            that.getEntityList(); // 获取门店列表
            that.AppointmentInfoRuleForm = {
              AppointmentTypeID: "",
              EntityID: "",
              AppointmentDate: that.$formatDate(new Date(), "YYYY-MM-DD"),
              Time: "10:00",
              Period: 30,
              Remark: "",
            };
            that.getAppointmentConfig();
            that.getAppointmentType();
            that.getServicerEmployee();
          }
        });
      }
    },

    // 刷新客户详情中的预约记录
    refreshCustomerDetailAppointments() {
      let that = this;

      // 如果客户详情弹框是打开的，刷新其中的预约记录
      if (that.$refs.customerDetail && that.$refs.customerDetail.visible) {
        that.$nextTick(() => {
          // 刷新预约记录
          if (that.$refs.customerDetail.$refs.customerAppointmentRecord) {
            that.$refs.customerDetail.$refs.customerAppointmentRecord.getAppointmentRecordList();
          }
          // 刷新跟进记录
          if (that.$refs.customerDetail.$refs.followUpRecord) {
            that.$refs.customerDetail.$refs.followUpRecord.getCustomerFollowUp();
          }
        });
      }
    },
  },
  /** 创建实例之前执行函数   */
  beforeCreate() {},
  /**  实例创建完成之后  */
  created() {
    let that = this;
    that.preview_src_list = Enumerable.from(that.ruleForm.Attachment)
      .select((val) => val.AttachmentURL)
      .toArray();
  },
  /**  在挂载开始之前被调用  */
  beforeMount() {},
  /**  实例被挂载后调用  */
  mounted() {
    let that = this;
    that.isShowChannel = that.$permission.permission(that.$route.meta.Permission, "iBeauty-Workbench-ClueFollowUp-Channel");
    that.isShowFollowUp = that.$permission.permission(that.$route.meta.Permission, "iBeauty-Workbench-ClueFollowUp-EntityRang");

    that.isCustomerPhoneNumberView = that.$permission.permission(that.$route.meta.Permission, "iBeauty-Workbench-ClueFollowUp-CustomerPhoneNumberView");

    // 初始化山东省数据
    that.regionData = that.getShandongData();
    that.isCustomerPhoneNumberModify = that.$permission.permission(that.$route.meta.Permission, "iBeauty-Workbench-ClueFollowUp-CustomerPhoneNumberModify");

    // that.CustomerCases.Add = that.$permission.permission(that.$route.meta.Permission, "iBeauty-Workbench-FollowUp-Cases-Add");
    // that.CustomerCases.Update = that.$permission.permission(that.$route.meta.Permission, "iBeauty-Workbench-FollowUp-Cases-Update");
    // that.CustomerCases.Delete = that.$permission.permission(that.$route.meta.Permission, "iBeauty-Workbench-FollowUp-Cases-Delete");
    // that.CustomerCases.SelectStencil = that.$permission.permission(that.$route.meta.Permission, "iBeauty-Workbench-FollowUp-Cases-SelectStencil");
    // that.CustomerCases.SaveStencil = that.$permission.permission(that.$route.meta.Permission, "iBeauty-Workbench-FollowUp-Cases-SaveStencil");
    // that.CustomerCases.DeleteStencil = that.$permission.permission(that.$route.meta.Permission, "iBeauty-Workbench-FollowUp-Cases-DeleteStencil");
    // that.CustomerCases.PrescriptionAdd = that.$permission.permission(that.$route.meta.Permission, "iBeauty-Workbench-FollowUp-Prescription-Add");
    // that.CustomerCases.PrescriptionUpdate = that.$permission.permission(that.$route.meta.Permission, "iBeauty-Workbench-FollowUp-Prescription-Update");

    that.isAnewAssign = that.$permission.permission(that.$route.meta.Permission, "iBeauty-Workbench-FollowUp-AnewAssign");

    that.getFollowUp();
    that.getFollowUpStatus();
    that.getFollowUpMethod();
    that.getFollowUpEmployee();
    that.getFollowUpEntity();
    that.getEntityList(); // 加载门店列表，用于意向门店编辑
    // 项目列表延迟加载，在需要时再获取

    // 延迟处理路由参数，确保页面完全初始化
    setTimeout(() => {
      that.handleRouteQuery();
    }, 1000); // 增加延迟时间，确保页面完全加载
    that.$bus.$on(that.$bus.RefreshClueFollowUpList, () => {
      that.getFollowUp();
    });
  },
  /**  数据更新 之前 调用  */
  beforeUpdate() {},
  /** 数据更新 完成 调用   */
  updated() {},
  /**  实例销毁之前调用  */
  beforeDestroy() {
    this.$bus.$off(this.$bus.RefreshClueFollowUpList);
  },
  /**  实例销毁后调用  */
  destroyed() {},
};
</script>

<style lang="scss">
.followUp {
  .el_scrollbar_height_followup {
    height: 65vh;
    .el-scrollbar__wrap {
      overflow-x: hidden;
    }
  }
  .custom-customer-descLabel {
    min-width: 80px;
    text-align: right;
    padding-right: 10px;
  }
  .customer-detail {
    background-color: #ffffff;
    padding: 15px;
    height: 100%;
    box-sizing: border-box;
  }
  .information {
    background-color: #f7f8fa;
    padding: 8px 8px 8px 8px;
    // margin-bottom: 5px;
  }
  .customer-autocomplete {
    li {
      line-height: normal;
      padding: 7px;

      .name {
        text-overflow: ellipsis;
        overflow: hidden;
      }
      .info {
        font-size: 12px;
        color: #b4b4b4;
      }
      .highlighted .info {
        color: #ddd;
      }
    }
    .tip {
      margin: 0px;
      background-color: #f7f8fa;
    }
    .margin-bottom {
      margin-bottom: 10px;
    }
  }
  .el-upload--picture-card {
    width: 100px;
    height: 100px;
    font-size: 16px !important;
  }
  .el-upload {
    width: 100px;
    height: 100px;
    line-height: 100px;
    font-size: 16px;
  }
  .el-upload-list--picture-card .el-upload-list__item {
    width: 100px;
    height: 100px;
    line-height: 100px;
    font-size: 16px;
  }
  .el-autocomplete-suggestion__wrap {
    margin-bottom: 0px !important;
  }
  .custom-el-select {
    li {
      line-height: normal;
      height: auto;
    }
  }

  // 预约弹框样式
  .customer-autocomplete {
    .el-autocomplete-suggestion__list {
      .el-autocomplete-suggestion__item {
        padding: 10px;
        border-bottom: 1px solid #f0f0f0;

        .name {
          font-weight: bold;
          margin-bottom: 5px;
        }

        .info {
          font-size: 12px;
          color: #999;
          margin-bottom: 2px;
        }
      }
    }
  }

  // 项目选择弹框样式
  .filter-tree {
    .el-tree-node__content {
      height: auto;
      padding: 5px 0;
    }
  }

  .border_left {
    border-left: 1px solid #e6e6e6;
    padding-left: 15px;
  }

  .el-scrollbar_height {
    height: 400px;
  }

  // 操作按钮样式优化
  .operation-buttons {
    display: flex;
    flex-wrap: nowrap;
    gap: 5px;
    align-items: center;
    justify-content: flex-start;
    width: 100%;

    .el-button--small {
      padding: 7px 12px;
      font-size: 12px;
      margin: 0;
      border-radius: 4px;
      white-space: nowrap;
      flex-shrink: 0; // 防止按钮被压缩
    }

    // 预约相关按钮统一样式
    .appointment-btn {
      width: 80px !important;
      padding: 7px 8px !important;
      text-align: center;

      // 下拉箭头图标样式调整
      .el-icon--right {
        margin-left: 2px;
      }
    }

    // 下拉菜单按钮样式
    .el-dropdown {
      .el-button--small {
        padding: 7px 8px;
      }
    }

    // 确保按钮高度一致
    .el-button {
      height: 32px;
      line-height: 1.2;
      min-width: auto;
    }

    // 客户信息样式
    .customer-info {
      display: flex;
      align-items: center;
      gap: 8px;
      flex-wrap: wrap;
    }

    .customer-name {
      font-weight: 500;
      color: #303133;
      font-size: 14px;
    }

    // 客户等级标签样式
    .customer-level-tag {
      background: linear-gradient(135deg, #ff9800 0%, #ff5722 100%) !important;
      border: none !important;
      color: #fff !important;
      font-weight: 500 !important;
      border-radius: 12px !important;
      padding: 2px 8px !important;
    }

    // NEW标识样式
    .new-tag {
      background: linear-gradient(135deg, #ff4d4f 0%, #ff7875 100%) !important;
      border: none !important;
      color: #fff !important;
      font-weight: bold !important;
      border-radius: 8px !important;
      padding: 3px 8px !important;
      font-size: 11px !important;
      letter-spacing: 0.5px !important;
      box-shadow: 0 2px 4px rgba(255, 77, 79, 0.3) !important;
      animation: pulse 2s infinite !important;
    }

    // NEW标识闪烁动画
    @keyframes pulse {
      0% {
        box-shadow: 0 2px 4px rgba(255, 77, 79, 0.3);
      }
      50% {
        box-shadow: 0 2px 8px rgba(255, 77, 79, 0.6);
      }
      100% {
        box-shadow: 0 2px 4px rgba(255, 77, 79, 0.3);
      }
    }

    // 客户名称点击样式
    .clickable-customer-name {
      transition: all 0.3s ease;

      &:hover {
        color: #66b1ff !important;
        text-decoration: none !important;
        font-weight: bold;
        transform: scale(1.05);
      }

      &:active {
        color: #3a8ee6 !important;
        transform: scale(0.98);
      }
    }

    // 备注列样式
    .remark-cell {
      position: relative;
      width: 100%;

      .remark-content {
        display: block;
        width: 100%;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        color: #606266;
        font-size: 13px;
        line-height: 1.4;

        // 添加渐变遮罩效果，让截断更明显
        &::after {
          content: '';
          position: absolute;
          right: 0;
          top: 0;
          width: 20px;
          height: 100%;
          background: linear-gradient(to right, transparent, #fff);
          pointer-events: none;
        }
      }

      .remark-empty {
        color: #C0C4CC;
        font-style: italic;
        font-size: 12px;
      }
    }

    // 当表格行悬停时，备注列的特殊效果
    .el-table__row:hover .remark-cell .remark-content {
      color: #409EFF;
      cursor: pointer;
    }

    // 意向门店编辑样式
    .intention-entity-display {
      cursor: pointer;
      position: relative;
      padding: 4px 8px;
      border-radius: 4px;
      transition: all 0.3s ease;
      min-height: 24px;
      display: flex;
      align-items: center;
      justify-content: space-between;

      &:hover {
        background-color: #f5f7fa;

        .edit-icon {
          opacity: 1;
        }
      }

      .clickable-text {
        flex: 1;
        font-size: 13px;

        &.color_999 {
          font-style: italic;
        }
      }

      .edit-icon {
        opacity: 0;
        font-size: 12px;
        color: #909399;
        margin-left: 4px;
        transition: opacity 0.3s ease;
      }
    }

    .intention-entity-edit {
      .edit-buttons {
        display: flex;
        gap: 4px;
        justify-content: flex-start;

        .el-button--mini {
          height: 24px;
          line-height: 1;
          border-radius: 3px;
        }
      }
    }
  }
}
</style>
