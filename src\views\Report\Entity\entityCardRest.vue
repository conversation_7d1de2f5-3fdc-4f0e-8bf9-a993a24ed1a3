<template>
  <div class="entityCardRest content_body" v-loading="loading">
    <!-- 搜索 -->
    <div class="nav_header">
      <el-form :inline="true" size="small" @submit.native.prevent>
        <el-form-item label="门店">
          <el-select v-model="EntityID" clearable filterable placeholder="请选择门店" :default-first-option="true" @change="handleSearch">
            <el-option v-for="item in EntityList" :key="item.ID" :label="item.EntityName" :value="item.ID"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" v-prevent-click @click="handleSearch">搜索</el-button>
        </el-form-item>
        <el-form-item>
          <el-button v-if="isExport" type="primary" size="small" v-prevent-click :loading="downloadLoading" @click="downloadSalePayExcel">导出</el-button>
        </el-form-item>
      </el-form>
    </div>
    <!-- 表格 -->
    <el-table :data="tableData" size="small" show-summary :summary-method="getSummary" >
      <el-table-column label="门店" prop="EntityName" fixed width="150"></el-table-column>
      <el-table-column label="项目统计" align="center">
        <el-table-column label="非赠送" align="center">
          <el-table-column label="有效张数" prop="ProjectValidSheet" align="right"></el-table-column>
          <el-table-column label="剩余次数" prop="ProjectBalance" align="right"></el-table-column>
          <el-table-column label="本金剩余金" align="right" width="90" prop="ProjectBalanceAmount">
            <template slot-scope="scope">
              {{ scope.row.ProjectBalanceAmount | toFixed | NumFormat }}
            </template>
          </el-table-column>
          <el-table-column label="赠金剩余金" align="right" width="90" prop="ProjectBalanceLargessAmount">
            <template slot-scope="scope">
              {{ scope.row.ProjectBalanceLargessAmount | toFixed | NumFormat }}
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="赠送" align="center">
          <el-table-column label="有效张数" prop="LargessProjectValidSheet" align="right"></el-table-column>
          <el-table-column label="剩余次数" prop="LargessProjectBalance" align="right"></el-table-column>
          <el-table-column label="剩余赠额" align="right" prop="LargessProjectBalanceLargessAmount">
            <template slot-scope="scope">
              {{ scope.row.LargessProjectBalanceLargessAmount | toFixed | NumFormat }}
            </template>
          </el-table-column>
        </el-table-column>
      </el-table-column>
      <el-table-column label="产品统计" align="center">
        <el-table-column label="非赠送" align="center">
          <el-table-column label="有效张数" prop="ProductValidSheet" align="right"></el-table-column>
          <el-table-column label="剩余次数" prop="ProductBalance" align="right"></el-table-column>
          <el-table-column label="本金剩余金" align="right" width="90" prop="ProductBalanceAmount">
            <template slot-scope="scope">
              {{ scope.row.ProductBalanceAmount | toFixed | NumFormat }}
            </template>
          </el-table-column>
          <el-table-column label="赠金剩余金" align="right" width="90" prop="ProductBalanceLargessAmount">
            <template slot-scope="scope">
              {{ scope.row.ProductBalanceLargessAmount | toFixed | NumFormat }}
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="赠送" align="center">
          <el-table-column label="有效张数" prop="LargessProductValidSheet" align="right"></el-table-column>
          <el-table-column label="剩余次数" prop="LargessProductBalance" align="right"></el-table-column>
          <el-table-column label="剩余赠额" align="right" prop="LargessProductBalanceLargessAmount">
            <template slot-scope="scope">
              {{ scope.row.LargessProductBalanceLargessAmount | toFixed | NumFormat }}
            </template>
          </el-table-column>
        </el-table-column>
      </el-table-column>
      <el-table-column label="通用次卡统计" align="center">
        <el-table-column label="非赠送" align="center">
          <el-table-column label="有效张数" prop="GeneralCardValidSheet" align="right"></el-table-column>
          <el-table-column label="剩余次数" prop="GeneralCardBalance" align="right"></el-table-column>
          <el-table-column label="本金剩余金" align="right" width="90" prop="GeneralCardBalanceAmount">
            <template slot-scope="scope">
              {{ scope.row.GeneralCardBalanceAmount | toFixed | NumFormat }}
            </template>
          </el-table-column>
          <el-table-column label="赠金剩余金" align="right" width="90" prop="GeneralCardBalanceLargessAmount">
            <template slot-scope="scope">
              {{ scope.row.GeneralCardBalanceLargessAmount | toFixed | NumFormat }}
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="赠送" align="center">
          <el-table-column label="有效张数" prop="LargessGeneralCardValidSheet" align="right"></el-table-column>
          <el-table-column label="剩余次数" prop="LargessGeneralCardBalance" align="right"></el-table-column>
          <el-table-column label="剩余赠额" align="right" prop="LargessGeneralCardBalanceLargessAmount">
            <template slot-scope="scope">
              {{ scope.row.LargessGeneralCardBalanceLargessAmount | toFixed | NumFormat }}
            </template>
          </el-table-column>
        </el-table-column>
      </el-table-column>
      <el-table-column label="时效卡统计" align="center">
        <el-table-column label="非赠送有效张数" align="right" width="110" prop="TimeCardValidSheet"></el-table-column>
        <el-table-column label="赠送有效张数" align="right" width="100" prop="TimeCardLargessValidSheet"></el-table-column>
      </el-table-column>
      <el-table-column label="储值卡统计" align="center">
        <el-table-column label="有效张数" align="right" prop="SavingCardValidSheet"></el-table-column>
        <el-table-column label="本金剩余金额" align="right" width="100" prop="SavingCardBalanceAmount">
          <template slot-scope="scope">
            {{ scope.row.SavingCardBalanceAmount | toFixed | NumFormat }}
          </template>
        </el-table-column>
        <el-table-column label="赠送剩余金额" width="100" align="right" prop="SavingCardBalanceLargessAmount">
          <template slot-scope="scope">
            {{ scope.row.SavingCardBalanceLargessAmount | toFixed | NumFormat }}
          </template>
        </el-table-column>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <div class="pad_15 text_right">
      <el-pagination
        background
        v-if="paginations.total > 0"
        @current-change="handlePageChange"
        :current-page.sync="paginations.page"
        :page-size="paginations.page_size"
        :layout="paginations.layout"
        :total="paginations.total"
      ></el-pagination>
    </div>
  </div>
</template>

<script>
import API from "@/api/Report/Entity/entityCardRest";
import APIStore from "@/api/Report/Entity/entityTrade";
export default {
  name: "ReportEntityCardRest",

  beforeRouteEnter(to, from, next) {
    next((vm) => {
      vm.isExport = vm.$permission.permission(to.meta.Permission, "Report-Entity-EntityCardRest-Export");
    });
  },
  props: {},
  /** 监听数据变化   */
  watch: {},
  /**  引入的组件  */
  components: {},
  /**  Vue 实例的数据对象**/
  data() {
    return {
      isExport: false,
      loading: false,
      downloadLoading: false,
      EntityList: [], // 门店数据
      tableData: [], // 表格数据
      SumOutputForm: {}, // 合计数据
      EntityID: null,
      paginations: {
        page: 1, // 当前位于哪页
        total: 0, // 总数
        page_size: 10, // 1页显示多少条
        layout: "total, prev, pager, next,jumper", // 翻页属性
      },
    };
  },
  /**计算属性  */
  computed: {},
  /**  方法集合  */
  methods: {
    /* 搜索 */
    handleSearch() {
      let that = this;
      that.paginations.page = 1;
      that.getEntityCustomerBalancList();
    },
    /* 分页 */
    handlePageChange(page) {
      let that = this;
      that.paginations.page = page;
      that.getEntityCustomerBalancList();
    },
    /* 导出 */
    downloadSalePayExcel() {
      let that = this;
      let params = {
        EntityID: that.EntityID,
      };
      that.downloadLoading = true;
      API.entityCustomerBalancExcel(params)
        .then((res) => {
          this.$message.success({
            message: "正在导出",
            duration: "4000",
          });
          const link = document.createElement("a");
          let blob = new Blob([res], { type: "application/octet-stream" });
          link.style.display = "none";
          link.href = URL.createObjectURL(blob);
          link.download = "门店余量报表.xlsx"; //下载的文件名
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
        })
        .finally(function () {
          that.downloadLoading = false;
        });
    },
    /* 获取表格数据 */
    getEntityCustomerBalancList() {
      let that = this;
      that.loading = true;
      let params = {
        EntityID: that.EntityID,
        PageNum: that.paginations.page,
      };
      API.getEntityCustomerBalancList(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.tableData = res.Data.Detail.List;
            that.paginations.page_size = res.Data.Detail.PageSize;
            that.paginations.total = res.Data.Detail.Total;
            that.SumOutputForm = res.Data.SumOutputForm;
          } else {
            this.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },
    /* 表格合计 */
    getSummary({ columns }) {
      const sums = [];
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = <span class="font_weight_600">合计</span>;
          return;
        }
        var filter_NumFormat = this.$options.filters["NumFormat"];
        switch (column.property) {
          case "ProjectValidSheet":
            sums[index] = <span class="font_weight_600">{this.SumOutputForm ? this.SumOutputForm.ProjectValidSheet : 0}</span>;
            break;
          case "ProjectBalance":
            sums[index] = <span class="font_weight_600">{this.SumOutputForm ? this.SumOutputForm.ProjectBalance : 0}</span>;
            break;
          case "ProjectBalanceAmount":
            sums[index] = <span class="font_weight_600">{filter_NumFormat(this.SumOutputForm ? this.SumOutputForm.ProjectBalanceAmount:0)}</span>;
            break;
          case "ProjectBalanceLargessAmount":
            sums[index] = <span class="font_weight_600">{filter_NumFormat(this.SumOutputForm ? this.SumOutputForm.ProjectBalanceLargessAmount : 0)}</span>;
            break;
          case "LargessProjectValidSheet":
            sums[index] = <span class="font_weight_600">{this.SumOutputForm ? this.SumOutputForm.LargessProjectValidSheet : 0}</span>;
            break;
          case "LargessProjectBalance":
            sums[index] = <span class="font_weight_600">{this.SumOutputForm ? this.SumOutputForm.LargessProjectBalance : 0}</span>;
            break;
          case "LargessProjectBalanceLargessAmount":
            sums[index] = <span class="font_weight_600">{filter_NumFormat(this.SumOutputForm ? this.SumOutputForm.LargessProjectBalanceLargessAmount : 0)}</span>;
            break;
          case "ProductValidSheet":
            sums[index] = <span class="font_weight_600">{this.SumOutputForm ? this.SumOutputForm.ProductValidSheet : 0}</span>;
            break;
          case "ProductBalance":
            sums[index] = <span class="font_weight_600">{this.SumOutputForm ? this.SumOutputForm.ProductBalance : 0}</span>;
            break;
          case "ProductBalanceAmount":
            sums[index] = <span class="font_weight_600">{filter_NumFormat(this.SumOutputForm ? this.SumOutputForm.ProductBalanceAmount : 0)}</span>;
            break;
          case "ProductBalanceLargessAmount":
            sums[index] = <span class="font_weight_600">{filter_NumFormat(this.SumOutputForm ? this.SumOutputForm.ProductBalanceLargessAmount : 0)}</span>;
            break;
          case "LargessProductValidSheet":
            sums[index] = <span class="font_weight_600">{this.SumOutputForm ? this.SumOutputForm.LargessProductValidSheet : 0}</span>;
            break;
          case "LargessProductBalance":
            sums[index] = <span class="font_weight_600">{this.SumOutputForm ? this.SumOutputForm.LargessProductBalance : 0}</span>;
            break;
          case "LargessProductBalanceLargessAmount":
            sums[index] = <span class="font_weight_600">{filter_NumFormat(this.SumOutputForm ? this.SumOutputForm.LargessProductBalanceLargessAmount : 0)}</span>;
            break;
          case "GeneralCardValidSheet":
            sums[index] = <span class="font_weight_600">{this.SumOutputForm ? this.SumOutputForm.GeneralCardValidSheet : 0}</span>;
            break;
          case "GeneralCardBalance":
            sums[index] = <span class="font_weight_600">{this.SumOutputForm ? this.SumOutputForm.GeneralCardBalance : 0}</span>;
            break;
          case "GeneralCardBalanceAmount":
            sums[index] = <span class="font_weight_600">{filter_NumFormat(this.SumOutputForm ? this.SumOutputForm.GeneralCardBalanceAmount : 0)}</span>;
            break;
          case "GeneralCardBalanceLargessAmount":
            sums[index] = <span class="font_weight_600">{filter_NumFormat(this.SumOutputForm ? this.SumOutputForm.GeneralCardBalanceLargessAmount : 0)}</span>;
            break;
          case "LargessGeneralCardValidSheet":
            sums[index] = <span class="font_weight_600">{this.SumOutputForm ? this.SumOutputForm.LargessGeneralCardValidSheet : 0}</span>;
            break;
          case "LargessGeneralCardBalance":
            sums[index] = <span class="font_weight_600">{this.SumOutputForm ? this.SumOutputForm.LargessGeneralCardBalance : 0}</span>;
            break;
          case "LargessGeneralCardBalanceLargessAmount":
            sums[index] = <span class="font_weight_600">{filter_NumFormat(this.SumOutputForm ? this.SumOutputForm.LargessGeneralCardBalanceLargessAmount : 0)}</span>;
            break;
          case "TimeCardValidSheet":
            sums[index] = <span class="font_weight_600">{this.SumOutputForm ? this.SumOutputForm.TimeCardValidSheet : 0}</span>;
            break;
          case "TimeCardLargessValidSheet":
            sums[index] = <span class="font_weight_600">{this.SumOutputForm ? this.SumOutputForm.TimeCardLargessValidSheet : 0}</span>;
            break;
          case "SavingCardValidSheet":
            sums[index] = <span class="font_weight_600">{this.SumOutputForm ? this.SumOutputForm.SavingCardValidSheet : 0}</span>;
            break;
          case "SavingCardBalanceAmount":
            sums[index] = <span class="font_weight_600">{filter_NumFormat(this.SumOutputForm ? this.SumOutputForm.SavingCardBalanceAmount : 0)}</span>;
            break;
          case "SavingCardBalanceLargessAmount":
            sums[index] = <span class="font_weight_600">{filter_NumFormat(this.SumOutputForm ? this.SumOutputForm.SavingCardBalanceLargessAmount : 0)}</span>;
            break;
        }
      });
      return sums;
    },
    /* 获取门店 */
    async getStoreList() {
      var that = this;
      let res = await APIStore.getStoreList();
      if (res.StateCode == 200) {
        that.EntityList = res.Data;
      } else {
        that.$message.error({
          message: res.Message,
          duration: 2000,
        });
      }
    },
  },
  /** 创建实例之前执行函数   */
  beforeCreate() {},
  /**  实例创建完成之后  */
  created() {},
  /**  在挂载开始之前被调用  */
  beforeMount() {},
  /**  实例被挂载后调用  */
  mounted() {
    let that = this;
    this.isExport = this.$permission.permission(this.$route.meta.Permission, "Report-Entity-EntityCardRest-Export");
    that.getStoreList();
    that.getEntityCustomerBalancList();
  },
  /**  数据更新 之前 调用  */
  beforeUpdate() {},
  /** 数据更新 完成 调用   */
  updated() {},
  /**  实例销毁之前调用  */
  beforeDestroy() {},
  /**  实例销毁后调用  */
  destroyed() {},
};
</script>

<style lang="scss">
.entityCardRest {
}
</style>
