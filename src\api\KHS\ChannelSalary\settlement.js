/**
 * Created by wsf on 2022/02/23.
 * 渠道业绩结算 api
 */

 import * as API from '@/api/index'

 export default {
  /* 结算周期查询  */
  getAllSettlementInterval: params => {
      return API.POST('api/channelSalarySheet/allSettlementInterval', params)
  },
  /* 结算周期设置 */
  createSettlementInterval: params => {
      return API.POST('api/channelSalarySheet/createSettlementInterval', params)
  },
  /* 结算数据查询 */
  getAllchannelSalarySheet: params => {
    return API.POST('api/channelSalarySheet/all', params)   
  },
  /* 结算详情 */
  getChannelSalarySheetDetail: params => {
    return API.POST('api/channelSalarySheet/detail', params)
  },
  /* 结算数据导出 */
  exportChannelSalarySheetExcek: params => {
    return API.exportExcel('api/channelSalarySheet/excel', params)
  },
  /* 业绩提成结算 */
  settlement: params => {
    return API.POST('api/channelSalarySheet/settlement', params)
  }
 }