/**
 * Created by wsf on 2022/05/25
 *  门店消耗业绩明细报表 api
 */

import * as API from "@/api/index";

export default {
  /* 获取门店消耗业绩明细报表 */
  getEntityTreatPerformanceList: (params) => {
    return API.POST("api/entityTreatPerformance/list", params);
  },
  // 导出
  entityTreatPerformanceExcel: (params) => {
    return API.exportExcel("api/entityTreatPerformance/excel", params);
  },
  /* 获取门店消耗业绩明细报表 */
  entitySaleGoodsDetailStatement_productAndProjectCategory: (params) => {
    return API.POST("api/entitySaleGoodsDetailStatement/productAndProjectCategory", params);
  },

  /* 查询客户等级 */
  customerLevel_all: (params) => {
    return API.POST("api/customerLevel/all", params);
  },
};
