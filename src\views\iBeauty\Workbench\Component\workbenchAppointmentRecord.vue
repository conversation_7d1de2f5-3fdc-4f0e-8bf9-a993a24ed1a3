<!--  顾客详情 预约记录 -->
<template>
  <div class="appointmentRecord">
    <el-scrollbar style="height: 100%" class="custom-scrollbar_hidden-x">
      <!-- 注释掉新建预约按钮 -->
      <!-- <div v-if="customerID" class="button-box">
        <div>
          <el-button size="small" type="primary" plain @click="addAppointmentClick">新建预约</el-button>
        </div>
      </div> -->
      <el-row class="custom-header">
        <el-col :offset="3" :span="24">
          <el-checkbox-group v-model="typeList" @change="changeTypeListClick">
            <el-checkbox label="1">门店预约</el-checkbox>
            <el-checkbox label="2">线索跟进客服预约</el-checkbox>
            <el-checkbox label="3">其他预约</el-checkbox>
          </el-checkbox-group>
        </el-col>
      </el-row>
      <el-row class="martp_15">
        <el-col :offset="3" :span="21">
          <div v-if="activities.length === 0 && !loading" class="no-data" style="text-align: center; padding: 40px; color: #999;">
            暂无预约记录
          </div>
          <el-timeline v-else class="csutom-timeline-conten">
            <el-timeline-item v-for="yearItem in activities" :key="'year-' + yearItem.Year" class="position_relative" color="var(--zl-color-orange-primary)" size="large">
              <!-- 年份 -->
              <div class="position_absolute" style="top: 0; left: -75px">
                <span @click="expandTimelineClick(yearItem)" class="bold">
                  <span>{{ yearItem.Year }}</span>
                  <i :class="[yearItem.isYearExpand ? 'el-icon-caret-bottom' : 'el-icon-caret-right', 'color_999', 'marlt_5']"></i>
                </span>
              </div>
              <el-timeline v-show="yearItem.isYearExpand" class="custom-moth-timeline">
                <el-timeline-item v-for="monthItem in yearItem.Child" :key="'month-' + monthItem.Month" color="var(--zl-color-orange-primary)">
                  <!-- 月 -->
                  <span class="bold" @click="showDetailsClick(monthItem)"
                    >{{ monthItem.Month }}月
                    <i :class="[monthItem.isMonthExpand ? 'el-icon-caret-bottom' : 'el-icon-caret-right', 'color_999']"></i>
                  </span>

                  <el-timeline v-show="monthItem.isMonthExpand" class="custom-day-timeline">
                    <el-timeline-item v-for="(dayItem, index) in monthItem.Log" :key="getID(dayItem, index)" color="var(--zl-color-orange-primary)">
                      <el-row type="flex" justify="space-between">
                        <el-col :span="3">
                          <p class="bold">
                            {{ dayItem.AppointmentDate | formatDateStr("date") }}
                          </p>
                          <span class="font_12 color_999">{{ dayItem.AppointmentDate | formatDateStr("time") }}</span>
                        </el-col>
                        <el-col :span="18">
                          <p class="color_333">
                            <span>{{ dayItem.CreatedByName }}</span>
                            <span v-show="dayItem.JobName">[{{ dayItem.JobName }}]</span>
                            <el-tag :type="getCategoryTagType(dayItem.AppointmentCategory)" class="marlt_10" size="small">{{ dayItem.AppointmentCategoryName || getCategoryName(dayItem.AppointmentCategory) }}</el-tag>
                            <span style="margin-left: 10px" v-if="dayItem.AppointmentStatus == '10'" class="color_main">未到店</span>
                            <span style="margin-left: 10px" v-if="dayItem.AppointmentStatus == '20'" class="color_success">已到店</span>
                            <span style="margin-left: 10px" v-if="dayItem.AppointmentStatus == '30'" class="color_danger">已取消</span>
                          </p>
                          <span class="color_999 font_13">{{ dayItem.EntityName }}</span>
                        </el-col>
                        <!-- 预约管理按钮 -->
                        <el-col :span="3" style="text-align: right;">
                          <div v-if="dayItem.AppointmentStatus == '10'" class="appointment-actions">
                            <el-dropdown
                              @command="(command) => handleAppointmentCommand(command, dayItem)"
                              trigger="click"
                              size="small"
                            >
                              <el-button type="success" size="small" class="appointment-btn">
                                预约管理<i class="el-icon-arrow-down el-icon--right"></i>
                              </el-button>
                              <el-dropdown-menu slot="dropdown">
                                <el-dropdown-item :command="{action: 'edit'}">
                                  <i class="el-icon-edit"></i> 修改预约
                                </el-dropdown-item>
                                <el-dropdown-item :command="{action: 'cancel'}">
                                  <i class="el-icon-close"></i> 取消预约
                                </el-dropdown-item>
                              </el-dropdown-menu>
                            </el-dropdown>
                          </div>
                        </el-col>
                      </el-row>
                      <el-row>
                        <el-card class="martp_5" shadow="always">
                          <el-row v-if="dayItem.AppointmentTypeName" style="line-height: 1.5">
                            <el-col :span="3" style="color: #909399">预约类型：</el-col>
                            <el-col :span="21" style="color: #606266">{{ dayItem.AppointmentTypeName }}</el-col>
                          </el-row>
                          <el-row v-if="dayItem.AppointmentDate" style="line-height: 1.5">
                            <el-col :span="3" style="color: #909399">预约时间：</el-col>
                            <el-col :span="21" style="color: #606266">{{ dayItem.AppointmentDate | dateFormat("YYYY-MM-DD HH:mm") }}</el-col>
                          </el-row>
                          <el-row v-if="dayItem.Period" style="line-height: 1.5">
                            <el-col :span="3" style="color: #909399">预约时长：</el-col>
                            <el-col :span="21" style="color: #606266">{{ dayItem.Period }}分钟</el-col>
                          </el-row>
                          <el-row v-if="dayItem.AppointmentStatusName" style="line-height: 1.5">
                            <el-col :span="3" style="color: #909399">预约状态：</el-col>
                            <el-col :span="21" style="color: #606266">
                              <span style="margin-right: 10px">{{ dayItem.AppointmentStatusName }}</span>
                            </el-col>
                          </el-row>
                          <el-row v-if="dayItem.Content" style="line-height: 1.5">
                            <el-col :span="3" style="color: #909399; white-space: pre">预约备注：</el-col>
                            <el-col :span="21" style="color: #606266; white-space: pre-wrap">{{ dayItem.Content }}</el-col>
                          </el-row>
                          <el-row v-if="dayItem.CreatedOn" style="line-height: 1.5">
                            <el-col :span="3" style="color: #909399">创建时间：</el-col>
                            <el-col :span="21" style="color: #606266">{{ dayItem.CreatedOn | dateFormat("YYYY-MM-DD HH:mm") }}</el-col>
                          </el-row>
                        </el-card>
                      </el-row>
                    </el-timeline-item>
                  </el-timeline>
                </el-timeline-item>
              </el-timeline>
            </el-timeline-item>
          </el-timeline>
        </el-col>
      </el-row>
    </el-scrollbar>
  </div>
</template>

<!-- 注释掉不再需要的按钮样式 -->
<!-- <style scoped>
.appointment-actions { ... }
.appointment-btn { ... }
.el-dropdown { ... }
.el-dropdown-menu__item { ... }
.el-button { ... }
</style> -->

<script>
import followUpAPI from "@/api/iBeauty/Workbench/followUp";
export default {
  props: {
    customerID: {
      type: Number,
      require: true,
    },
    customerName: {
      type: String,
      default: '',
    },
  },
  name: "workbenchAppointmentRecord",
  data() {
    return {
      loading: false,
      activities: [], // 时间轴数据
      typeList: ["1", "2", "3"], // 默认显示所有类型：1-门店预约，2-线索跟进客服预约，3-其他预约
      AppointmentRecordDetail: {},
      dialogVisible: false,
      employeeID: null, // 当前用户ID，在mounted中获取
    };
  },
  methods: {
    // 获取预约记录列表
    async getAppointmentRecordList() {
      let that = this;
      that.loading = true;

      try {
        console.log('获取预约记录，CustomerID:', that.customerID);
        // 使用API文件中的预约记录接口
        const res = await followUpAPI.getAppointmentRecords(that.customerID);
        console.log('预约记录API响应:', res);

        if (res.StateCode === 200) {
          console.log('预约记录数据:', res.Data);
          that.processTimelineData(res.Data || []);
        } else {
          console.error('获取预约记录失败:', res.Message);
          that.$message.error(res.Message || '获取预约记录失败');
        }
      } catch (error) {
        console.error('获取预约记录异常:', error);
        that.$message.error('获取预约记录失败: ' + (error.message || error));
      } finally {
        that.loading = false;
      }
    },

    // 处理时间轴数据
    processTimelineData(data) {
      let that = this;

      // 按类型筛选数据
      let filteredData = data.filter(item => {
        return that.typeList.includes(String(item.AppointmentCategory || 1));
      });

      // 按年月分组
      let groupedData = {};

      filteredData.forEach(item => {
        let date = new Date(item.AppointmentDate);
        let year = date.getFullYear();
        let month = date.getMonth() + 1;

        if (!groupedData[year]) {
          groupedData[year] = {};
        }
        if (!groupedData[year][month]) {
          groupedData[year][month] = [];
        }

        groupedData[year][month].push(item);
      });

      // 转换为时间轴格式
      that.activities = Object.keys(groupedData).map(year => {
        return {
          Year: year,
          isYearExpand: true,
          Child: Object.keys(groupedData[year]).map(month => {
            return {
              Month: month,
              isMonthExpand: true,
              Log: groupedData[year][month].sort((a, b) => new Date(b.AppointmentDate) - new Date(a.AppointmentDate))
            };
          }).sort((a, b) => b.Month - a.Month)
        };
      }).sort((a, b) => b.Year - a.Year);
    },

    // 类型筛选变化
    changeTypeListClick() {
      this.getAppointmentRecordList();
    },

    // 展开/收起年份
    expandTimelineClick(yearItem) {
      yearItem.isYearExpand = !yearItem.isYearExpand;
    },

    // 展开/收起月份
    showDetailsClick(monthItem) {
      monthItem.isMonthExpand = !monthItem.isMonthExpand;
    },

    // 获取ID（用于key）
    getID(item, index) {
      return item.ID || `appointment-${index}`;
    },

    // 获取预约类别标签类型
    getCategoryTagType(category) {
      const typeMap = {
        1: 'info',    // 门店预约
        2: 'primary', // 线索跟进客服预约
        3: 'warning'  // 其他预约
      };
      return typeMap[category] || 'info';
    },

    // 获取预约类别名称
    getCategoryName(category) {
      const categoryMap = {
        1: '门店预约',
        2: '线索跟进客服预约',
        3: '其他预约类型'
      };
      return categoryMap[category] || '门店预约';
    },

    // 处理预约管理下拉菜单命令
    handleAppointmentCommand(command, dayItem) {
      let that = this;

      if (command.action === 'edit') {
        // 修改预约 - 通过事件通知父组件
        that.$emit('openAppointment', {
          CustomerID: that.customerID,
          CustomerName: that.customerName, // 传递客户名称
          AppointmentID: dayItem.ID,
          action: 'edit'
        });
      } else if (command.action === 'cancel') {
        // 取消预约
        that.cancelClick(dayItem);
      }
    },

    // 取消预约
    async cancelClick(item) {
      let that = this;

      try {
        const confirmResult = await that.$confirm('确定要取消这个预约吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        });

        if (confirmResult) {
          console.log('取消预约，预约ID:', item.ID);

          // 使用API文件中的取消预约接口，传递ID和状态参数
          const res = await followUpAPI.appointmentBillCancel({
            ID: item.ID,
            Status: 30 // 30表示已取消
          });
          console.log('取消预约API响应:', res);

          if (res.StateCode === 200) {
            that.$message.success('预约已取消');
            that.getAppointmentRecordList(); // 刷新列表

            // 通知父组件刷新
            that.$emit('appointmentCancelled', {
              appointmentID: item.ID,
              customerID: that.customerID
            });
          } else {
            console.error('取消预约失败:', res);
            that.$message.error(res.Message || '取消预约失败');
          }
        }
      } catch (error) {
        console.error('取消预约异常:', error);
        if (error !== 'cancel') {
          that.$message.error('取消预约失败: ' + (error.message || error));
        }
      }
    },
  },

  watch: {
    customerID: {
      handler(newVal) {
        if (newVal) {
          this.getAppointmentRecordList();
        }
      },
      immediate: true
    }
  },

  filters: {
    formatDateStr(value, type) {
      if (!value) return '';
      const date = new Date(value);
      if (type === 'date') {
        return `${date.getMonth() + 1}/${date.getDate()}`;
      } else if (type === 'time') {
        return `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
      }
      return value;
    },
    dateFormat(value, format) {
      if (!value) return '';
      const date = new Date(value);
      return format.replace('YYYY', date.getFullYear())
                  .replace('MM', (date.getMonth() + 1).toString().padStart(2, '0'))
                  .replace('DD', date.getDate().toString().padStart(2, '0'))
                  .replace('HH', date.getHours().toString().padStart(2, '0'))
                  .replace('mm', date.getMinutes().toString().padStart(2, '0'));
    }
  },

  mounted() {
    // 获取当前用户ID
    if (this.$store.state.user && this.$store.state.user.userInfo) {
      this.employeeID = this.$store.state.user.userInfo.EmployeeID;
    }

    if (this.customerID) {
      this.getAppointmentRecordList();
    }
  },
};
</script>


<style lang="scss">
.appointmentRecord {
  height: calc(100% - 4px);

  .custom-scrollbar_hidden-x {
    .el-scrollbar__wrap {
      overflow-x: hidden;
    }
  }

  .button-box {
    padding: 10px 0;
    border-bottom: 1px solid #ebeef5;
    margin-bottom: 15px;
  }

  .custom-header {
    padding: 10px 0;
    border-bottom: 1px solid #ebeef5;
    margin-bottom: 15px;
  }

  .csutom-timeline-conten {
    .el-timeline-item__wrapper {
      padding-top: 48px;
      padding-left: 0px;
      .custom-moth-timeline {
        .el-timeline-item__wrapper {
          padding-top: 0px;
          padding-left: 20px;
          .custom-day-timeline {
            margin-top: 20px;
            margin-left: -20px;

            font-size: 13px !important;
            .el-timeline-item__tail {
              display: none !important;
            }
            .el-timeline-item__node--normal {
              width: 10px;
              height: 10px;
            }
            .el-card-form .el-form-item {
              margin-bottom: 5px;
            }
            .el-form-item__label {
              line-height: 18px;
            }
            .el-form-item__content {
              line-height: 18px;
            }
          }
        }
      }
    }
    .el-timeline-item__tail {
      display: block !important;
    }
  }

  .color_main {
    color: #409EFF;
  }

  .color_success {
    color: #67C23A;
  }

  .color_danger {
    color: #F56C6C;
  }

  .color_333 {
    color: #333;
  }

  .color_999 {
    color: #999;
  }

  .font_12 {
    font-size: 12px;
  }

  .font_13 {
    font-size: 13px;
  }

  .bold {
    font-weight: bold;
  }

  .marlt_5 {
    margin-left: 5px;
  }

  .marlt_10 {
    margin-left: 10px;
  }

  .martp_5 {
    margin-top: 5px;
  }

  .martp_15 {
    margin-top: 15px;
  }

  .position_relative {
    position: relative;
  }

  .position_absolute {
    position: absolute;
  }

  // 预约弹框样式
  .font_18 {
    font-size: 18px;
    font-weight: bold;
  }

  .back_f8 {
    background-color: #f8f8f8;
  }

  .dis_flex {
    display: flex;
  }

  .flex_y_center {
    align-items: center;
  }

  .radius5 {
    border-radius: 5px;
  }

  .line_height_38 {
    line-height: 38px;
  }

  .pad_0_10 {
    padding: 0 10px;
  }

  .martp_10 {
    margin-top: 10px;
  }

  .pad_10 {
    padding: 10px;
  }

  .font_14 {
    font-size: 14px;
  }

  .font_weight_bold {
    font-weight: bold;
  }

  .marb_10 {
    margin-bottom: 10px;
  }

  .text_center {
    text-align: center;
  }

  .pad_20 {
    padding: 20px;
  }

  .flex_1 {
    flex: 1;
  }

  .marb_5 {
    margin-bottom: 5px;
  }

  // 预约管理按钮样式
  .appointment-btn {
    background-color: #67c23a;
    border-color: #67c23a;
    color: #fff;

    &:hover {
      background-color: #85ce61;
      border-color: #85ce61;
      color: #fff;
    }

    &:focus {
      background-color: #67c23a;
      border-color: #67c23a;
      color: #fff;
    }
  }

  .appointment-actions {
    display: flex;
    justify-content: flex-end;
    align-items: center;
  }
}
</style>