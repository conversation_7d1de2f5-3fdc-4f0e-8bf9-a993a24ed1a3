<template>
  <div class="diagnosis content_body">
    <div class="nav_header" style="background-color: #ffffff">
      <el-form :inline="true" size="small" v-model="searchRuleForm"  @keyup.enter.native="handleSearch">
        <el-form-item label="会员信息">
          <el-input v-model="searchRuleForm.Name" size="small" @clear="handleSearch" clearable placeholder="输入会员信息搜索"></el-input>
        </el-form-item>

        <el-form-item label="指派日期">
          <el-date-picker
            v-model="searchRuleForm.DesignDate"
            unlink-panels
            type="daterange"
            range-separator="至"
            value-format="yyyy-MM-dd"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            @change="handleSearch"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="渠道信息">
          <el-input size="small" v-model="searchRuleForm.ChannelName" @clear="handleSearch" clearable placeholder="输入渠道名称"></el-input>
        </el-form-item>
        <el-form-item label="会员等级" >
          <el-select placeholder="请选择会员等级" filterable clearable v-model="searchRuleForm.CustomerLevelID" @change="handleSearch" size="small">
            <el-option v-for="item in customerLevelList" :key="item.ID" :label="item.Name" :value="item.ID"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="是否接诊">
          <el-select v-model="searchRuleForm.IsDiagnosis" placeholder="请选择是否接诊" clearable @change="handleSearch" @clear="handleSearch" size="small">
            <el-option label="待接诊" :value="false"> </el-option>
            <el-option label="已接诊" :value="true"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button size="small" type="primary" @click="handleSearch">搜索</el-button>
        </el-form-item>

        <el-form-item>
        <el-dropdown @command="customer_Export" :loading="downloadLoading">
          <el-button type="primary"> 导出<i class="el-icon-arrow-down el-icon--right"></i> </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item command="excelNoDisPlayPhone">导出</el-dropdown-item>
            <el-dropdown-item command="excelDisPlayPhone">导出(手机号)</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
          <!-- <el-button @click="customer_Export('excelNoDisPlayPhone')"  type="primary" v-prevent-click :loading="downloadLoading"> 导出 </el-button>
          <el-button @click="customer_Export('excelDisPlayPhone')"  type="primary" v-prevent-click :loading="downloadLoading"> 导出(手机号）</el-button> -->
        </el-form-item>
        <!-- <el-form-item>
          <el-button size="small" type="primary" @click="diagnosis_excelDisPlayPhone" :loading="downloadLoading">导出</el-button>
        </el-form-item>

        <el-form-item>
          <el-button size="small" type="primary" @click="diagnosis_excelNoDisPlayPhone" :loading="downloadNoDisPlayLoading">隐藏手机号导出</el-button>
        </el-form-item> -->
      </el-form>
    </div>

    <el-table size="small" :data="tableData" tooltip-effect="light" @row-click="clickDiagnosisRowData" highlight-current-row>
      <el-table-column fixed label="操作" :width="openBillState ? '145' : '80'">
        <template slot-scope="scope">
          <el-button v-if="scope.row.IsDiagnosis" type="primary" size="small" @click.stop="transferDiagnosisClick(scope.row)">转诊</el-button>
          <el-button v-else type="primary" size="small" @click.stop="diagnosisClick(scope.row)">接诊</el-button>

          <el-button v-if="openBillState" type="primary" size="small" @click.stop="gotoOrderCreated(scope.row)">开单</el-button>
        </template>
      </el-table-column>
      <el-table-column fixed label="接诊状态">
        <template slot-scope="scope">
          <span v-if="scope.row.IsDiagnosis" style="color: #00db93">已接诊</span>
          <span v-else style="color: var(--zl-color-orange-primary)">待接诊</span>
        </template>
      </el-table-column>
      <el-table-column label="指派时间" prop="AgnosisDate" width="150">
        <template slot-scope="scope">
          {{ scope.row.AgnosisDate | dateFormat('YYYY-MM-DD HH:mm') }}
        </template>
      </el-table-column>
      <el-table-column label="导诊备注" show-overflow-tooltip prop="Guidance"></el-table-column>
      <el-table-column label="指派人员" prop="assignByName"></el-table-column>
      <el-table-column label="接诊人员" prop="DiagnosisByName"></el-table-column>
      <el-table-column label="接诊时间" prop="DiagnosisOn" width="150"></el-table-column>
      <el-table-column label="接诊院部" prop="AppointmentEntityName" width="130"></el-table-column>
      <el-table-column label="客户" prop="CustomerName" width="150px"> 
        <template slot-scope="scope">
          <div>{{ scope.row.CustomerName }} <span v-if="scope.row.Code">({{ scope.row.Code }})</span> </div>
          <div>{{ scope.row.PhoneNumber | hidephone }}</div>
        </template>
      </el-table-column>
      <!-- <el-table-column label="客户名称" prop="CustomerName" width="110">
        <template slot-scope="scope">
          {{ scope.row.CustomerName }}
          <span class="marlt_5">({{ scope.row.Code }})</span>
        </template>
      </el-table-column> -->
      <!-- <el-table-column label="客户手机号" prop="PhoneNumber" width="110">
        <template slot-scope="scope">
          {{ scope.row.PhoneNumber | hidephone }}
        </template>
      </el-table-column> -->
      

      <el-table-column label="渠道" prop="ChannelName" width="100px"></el-table-column>
      <el-table-column label="会员等级" prop="CustomerLevelName"></el-table-column>

      <el-table-column label="来源" prop="CustomerSourceName"></el-table-column>
      <el-table-column label="服务人员" prop="ServicerEmployee" width="150">
        <template slot-scope="scope">
          <el-tooltip placement="top" effect="light">
            <div slot="content">
              <el-descriptions size="mini" :column="1" border :colon="false" labelClassName="custom-customer-descLabel">
                <el-descriptions-item v-for="(item, index) in scope.row.ServicerEmployee" :key="index" :label="item.Name + '：'">
                  {{ getServicerEmpNames(item.ServicerEmpList) }}
                </el-descriptions-item>
              </el-descriptions>
            </div>
            <div style="text-overflow: ellipsis; white-space: nowrap; overflow: hidden">
              {{ getFirstServiceEmp(scope.row.ServicerEmployee) }}
            </div>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column label="会员性别" prop="Gender">
        <template slot-scope="scope">
          <span v-if="scope.row.Gender == 1">男</span>
          <span v-else-if="scope.row.Gender == 2">女</span>
          <span v-else>未知</span>
        </template>
      </el-table-column>
      <el-table-column label="生日" prop="Birthday"></el-table-column>
      <el-table-column label="所属门店" prop="EntityName" width="120"></el-table-column>
      <el-table-column label="注册日期" prop="CustomerCreatedOn" width="150">
        <template slot-scope="scope">
          {{ scope.row.CustomerCreatedOn | dateFormat('YYYY-MM-DD HH:mm') }}
        </template>
      </el-table-column>
    </el-table>
    <div class="pad_15 text_right">
      <el-pagination
        background
        v-if="paginations.total > 0"
        @current-change="handleCurrentChange"
        :current-page.sync="paginations.page"
        :page-size="paginations.page_size"
        :layout="paginations.layout"
        :total="paginations.total"
      ></el-pagination>
    </div>
    <!-- </el-main>
      </el-container>

      <el-aside :width="'680px'" class="customer-detail">
        <workbench-customer-detail
          ref="customerDetail"
          :customerID="customerID"
          :isCustomerPhoneNumberView="isCustomerPhoneNumberView"
          :isCustomerPhoneNumberModify="isCustomerPhoneNumberModify"
        ></workbench-customer-detail>
      </el-aside>
    </el-container> -->

    <workbench-customer-detail
      v-if="customerDetailVisible"
      :visible.sync="customerDetailVisible"
      ref="customerDetail"
      :customerID="customerID"
      :isCustomerPhoneNumberView="isCustomerPhoneNumberView"
      :isCustomerPhoneNumberModify="isCustomerPhoneNumberModify"
      :isCustomerBasicInformationModify="isCustomerBasicInformationModify"
      :isCustomerServicerModify="isCustomerServicerModify"
      :isCustomerBasicFileModify="isCustomerBasicFileModify"
    ></workbench-customer-detail>

    <!-- 接诊 -->
    <el-dialog title="接诊填写" :visible.sync="diagnosisVisible" width="980px" :close-on-click-modal="false">
      <el-scrollbar class="el_scrollbar_height_followup">
        <!-- 顾客信息 -->
        <el-row
          type="flex"
          align=""
          class="pad_10 radius5"
          style="background-color: #f4f6fe"
          :style="ruleForm.Guidance ? 'border-bottom: 1px solid #E4E4E4;' : ''"
        >
          <el-col :span="2">
            <!--  -->
            <el-avatar :size="50" :src="ruleForm.Avatar ? ruleForm.Avatar : 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png'"></el-avatar>
          </el-col>
          <el-col :span="22">
            <el-row type="flex" justify="space-between">
              <el-col :span="24">
                <strong class="marrt_5 font_18">{{ ruleForm.CustomerName }}</strong>
                <el-image v-if="ruleForm.Gender == 1" style="width: 8px; height: 12px" :src="require('@/assets/img/gender-male.png')"></el-image>
                <el-image v-if="ruleForm.Gender == 2" style="width: 8px; height: 12px" :src="require('@/assets/img//gender-female.png')"></el-image>
              </el-col>
            </el-row>
            <el-col justify="space-between">
              <el-col :span="8" class="color_999 martp_10"
                >手机号：<span class="color_333">{{ ruleForm.PhoneNumber | hidephone }}</span></el-col
              >
              <el-col :span="8" class="color_999 martp_10"
                >客户编号：<span class="color_333">{{ ruleForm.Code }}</span></el-col
              >
              <el-col :span="8" class="color_999 martp_10"
                >注册时间：<span class="color_333">{{ ruleForm.CustomerCreatedOn }}</span></el-col
              >
              <el-col :span="8" class="color_999 martp_10"
                >介绍人：<span class="color_333">{{ ruleForm.CustomerName }}</span></el-col
              >
              <el-col :span="8" class="color_999 martp_10"
                >渠道来源：<span class="color_333">{{ ruleForm.CustomerName }}</span></el-col
              >
              <el-col :span="8" class="color_999 martp_10"
                >信息来源：<span class="color_333">{{ ruleForm.CustomerSourceName }}</span></el-col
              >
            </el-col>
          </el-col>
        </el-row>
        <!-- 导诊备注 -->
        <el-row v-if="ruleForm.Guidance" class="pad_10 radius5" style="background-color: #f4f6fe">
          <el-col :span="2">导诊备注:</el-col>
          <el-col :span="22">{{ ruleForm.Guidance }}</el-col>
        </el-row>
        <el-form :model="ruleForm" size="small" class="martp_15" ref="diagnosisForm" label-width="110px">
          <el-form-item
            label="接诊记录"
            prop="DiagnosisContent"
            :rules="[
              {
                required: true,
                message: '请输入接诊记录',
                trigger: ['blur', 'change'],
              },
            ]"
          >
            <el-input v-model="ruleForm.DiagnosisContent" type="textarea" :rows="5"></el-input>
          </el-form-item>
          <el-form-item>
            <el-upload
              :limit="9"
              class="avatar-uploader"
              list-type="picture-card"
              action="#"
              accept="image/*"
              :file-list="ruleForm.Attachment"
              :before-upload="beforeUpload"
              multiple
            >
              <i class="el-icon-plus avatar-uploader-icon"></i>
              <div slot="file" slot-scope="{ file }" style="height: 100px; widht: 100px">
                <el-image
                  style="height: 100%; width: 100%"
                  :id="file.uid"
                  :src="file.AttachmentURL"
                  :preview-src-list="previewImgSrcs"
                  :z-index="9999"
                  fit="cover"
                ></el-image>
                <span class="el-upload-list__item-actions">
                  <span class="el-upload-list__item-preview" @click="checkPreview(file)">
                    <i class="el-icon-zoom-in"></i>
                  </span>
                  <span class="el-upload-list__item-preview" @click="removeDiagnosisImage(file)">
                    <i class="el-icon-delete"></i>
                  </span>
                </span>
              </div>
            </el-upload>
          </el-form-item>
          <el-form-item label="下次跟进">
            <el-radio-group v-model="ruleForm.IsNextFollowUp">
              <el-radio :label="true">是</el-radio>
              <el-radio :label="false">否</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item
            prop="PlannedOn"
            v-if="ruleForm.IsNextFollowUp"
            label="计划跟进时间"
            :rules="[
              {
                required: ruleForm.IsNextFollowUp,
                message: '请选择下次跟进时间',
                trigger: ['blur', 'change'],
              },
            ]"
          >
            <el-date-picker
              v-model="ruleForm.PlannedOn"
              type="datetime"
              format="yyyy-MM-dd HH:mm"
              :default-time="nextDateTime"
              placeholder="请选择下次跟进日期"
            ></el-date-picker>
          </el-form-item>
          <el-form-item label="备注" v-if="ruleForm.IsNextFollowUp">
            <el-input v-model="ruleForm.PlannedRemark" type="textarea" :rows="3"></el-input>
          </el-form-item>
        </el-form>
      </el-scrollbar>
      <div slot="footer">
        <el-button @click="diagnosisVisible = false" size="small" v-prevent-click>取 消</el-button>
        <el-button type="primary" @click="saveDiagnosisClick" :loading="addLoading" size="small" v-prevent-click>保存</el-button>
      </div>
    </el-dialog>

    <!-- 转诊 -->
    <el-dialog title="转诊" :visible.sync="transferDiagnosisVisible" width="800px" :close-on-click-modal="false">
      <div>
        <el-row type="flex" align="" class="pad_10 radius5" style="background-color: #f7f8fa">
          <el-col :span="2">
            <el-avatar
              :size="50"
              :src="serviceListServicer.Avatar ? serviceListServicer.Avatar : 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png'"
            >
            </el-avatar>
          </el-col>
          <el-col :span="22">
            <el-row type="flex" justify="space-between">
              <el-col :span="12">
                <strong class="marrt_5 font_18">{{ serviceListServicer.Name }}</strong>
                <el-image v-if="serviceListServicer.Gender == 2" style="width: 8px; height: 12px" :src="require('@/assets//img//gender-female.png')"></el-image>
                <el-image v-if="serviceListServicer.Gender == 1" style="width: 8px; height: 12px" :src="require('@/assets/img/gender-male.png')"></el-image>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="8" class="color_999 over-flow martp_5" v-for="item in serviceListServicer.ServicerEmployee" :key="item.ID">
                {{ item.Name }}：
                <el-tooltip class="item" effect="light" :content="getEmpNames(item)" placement="top">
                  <span class="color_333">{{ getEmpNames(item) }}</span>
                </el-tooltip>
              </el-col>
            </el-row>
          </el-col>
        </el-row>
        <el-tabs class="custom-tabs-class" v-model="transferActive">
          <el-tab-pane label="服务人员" name="1">
            <div>
              <el-radio-group v-model="transferRuleForm.Status">
                <el-radio label="10">不替换不追加</el-radio>
                <el-radio label="20">替换服务人员</el-radio>
                <el-radio label="30">追加服务人员</el-radio>
              </el-radio-group>
            </div>

            <div class="martp_15 border_top border_right">
              <el-scrollbar class="custom-scrollbar-class">
                <div class="dis_flex">
                  <div class="border_bottom border_left" style="width: 250px" v-for="item in serviceList" :key="item.ID">
                    <div class="dis_flex flex_dir_column border_bottom pad_10">
                      <strong class="color_333">{{ item.Name }}</strong>
                      <el-input
                        v-model="item.searchKey"
                        placeholder="请输入编号、姓名"
                        prefix-icon="el-icon-search"
                        size="small"
                        class="martp_5"
                        clearable
                      ></el-input>
                    </div>

                    <el-scrollbar style="height: 300px" class="serviceTypeClass">
                      <div style="width: 250px; padding-bottom: 30px">
                        <div
                          @click="selectServiceEmpClick(item, emp)"
                          v-for="emp in item.Detail.filter(
                            (val) =>
                              !item.searchKey ||
                              val.EmployeeID.toLowerCase().includes(item.searchKey.toLowerCase()) ||
                              val.EmployeeName.toLowerCase().includes(item.searchKey.toLowerCase())
                          )"
                          :key="'emp' + emp.EmployeeID"
                          class="empItem border pad_10 font_13 position_relative border_box"
                          :class="setSelectServiceClass(item, emp) ? 'selectServiceEmp' : ''"
                        >
                          <div v-if="emp.IsBelong" class="color_main position_absolute" style="top: 5px; right: 5px">所属</div>
                          <div class="overflow_hidden">
                            <strong class="color_333 font_13">{{ emp.EmployeeName }}</strong>
                            <span class="font_13 color_666 marlt_10">[{{ emp.JobName }}]</span>
                          </div>

                          <div class="martp_5">当天接诊{{ emp.todayDiagnosisCount }}人</div>
                          <div class="martp_5">昨天接诊{{ emp.yesterdayDiagnosisCount }}人</div>
                          <el-image
                            v-if="setSelectServiceClass(item, emp)"
                            :src="require('@/assets/img/select-servicer.png')"
                            style="height: 20px; width: 20px; bottom: 0; right: 0; position: absolute"
                          ></el-image>
                        </div>
                      </div>
                    </el-scrollbar>
                  </div>
                </div>
              </el-scrollbar>
            </div>

            <div class="martp_15">
              <el-row type="flex" align="">
                <el-col :span="2">导诊备注:</el-col>
                <el-col :span="22">
                  <el-input v-model="transferRuleForm.Guidance" placeholder="请输入内容" type="textarea" :rows="3"></el-input>
                </el-col>
              </el-row>
            </div>
          </el-tab-pane>
          <el-tab-pane label="其他人员" name="2">
            <el-form label-width="75px" size="small">
              <el-form-item label="指派人员">
                <el-select
                  size="small"
                  popper-class="custom-el-select"
                  v-model="transferRuleForm.otherEmployeeID"
                  filterable
                  remote
                  :remote-method="searchEmpRemote"
                  placeholder="请选择"
                  @change="selectOtherEmp"
                >
                  <el-option v-for="item in otherServiceList" :key="item.ID" :label="item.Name" :value="item.ID">
                    <div class="dis_flex flex_dir_column pad_5_0">
                      <div style="line-height: 25px">
                        <span style="float: left">{{ item.Name }}</span>
                        <span style="float: right; color: #8492a6; font-size: 13px">{{ item.ID }}</span>
                      </div>
                      <div style="line-height: 20px; color: #8492a6">
                        <span style="float: left">{{ item.JobName }}</span>
                        <span style="float: right; font-size: 13px" class="marlt_5">{{ item.JobName }}</span>
                      </div>
                    </div>
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="导诊备注:">
                <el-input v-model="transferRuleForm.Guidance" placeholder="请输入内容" type="textarea" :rows="3"></el-input>
              </el-form-item>
            </el-form>
          </el-tab-pane>
        </el-tabs>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="transferDiagnosisVisible = false" v-prevent-click size="small">取 消</el-button>
        <el-button type="primary" @click="confirmArrange" v-prevent-click :loading="transferLoading" size="small">确认指派</el-button>
      </span>
    </el-dialog>

    <div v-show="false" style="height: 10px; width: 100%">
      <medicalEditor ref="hiddenMedicalEditor"></medicalEditor>
    </div>
  </div>
</template>

<script>
import API from '@/api/iBeauty/Workbench/diagnosis.js';
// import workbenchCustomerDetail from "@/views/iBeauty/Workbench/Component/workbenchCustomerDetail";
import APIUpload from '@/api/Common/uploadAttachment.js';
import utils from '@/components/js/utils.js';
import medicalEditor from '@/components/medicalEditor/medicalEditor.vue';

export default {
  name: 'Diagnosis',
  props: {},
  /**  引入的组件  */
  components: {
    workbenchCustomerDetail: () => import('@/views/iBeauty/Workbench/Component/workbenchCustomerDetail'),
    medicalEditor,
  },

  /** 监听数据变化   */
  watch: {
    'ruleForm.Attachment': {
      immediate: true,
      handler(val) {
        if (val) {
          this.previewImgSrcs = val.map((val) => val.AttachmentURL);
        }
      },
    },
  },

  /**  Vue 实例的数据对象**/
  data() {
    return {
      customerLevelList:[],
      downloadLoading: false,
      downloadNoDisPlayLoading: false,
      isCustomerBasicFileModify: false,
      isCustomerServicerModify: false,
      isCustomerBasicInformationModify: false,

      customerDetailVisible: false,
      isCustomerPhoneNumberModify: false,
      isCustomerPhoneNumberView: false,
      openBillState: false,
      isEntityRang: false,
      diagnosisVisible: false,
      transferDiagnosisVisible: false,
      transferLoading: false,
      addLoading: false,
      customerID: null,
      transferActive: '1',
      searchRuleForm: {
        Name: '',
        DesignDate: [this.$formatDate(new Date(), 'YYYY-MM-DD'), this.$formatDate(new Date(), 'YYYY-MM-DD')],
        IsDiagnosis: false,
        ChannelName:"",
        CustomerLevelID:"",
      },
      ruleForm: {
        DiagnosisID: null, //接待ID
        DiagnosisContent: '', //接诊记录
        IsNextFollowUp: true, //是否下次跟进
        PlannedOn: '', //计划跟进时间
        PlannedRemark: '', //计划跟进备注
        Attachment: [], // {{"AttachmentType": 10,"AttachmentURL": }}
      },
      nextDateTime: '',
      transferRuleForm: {
        DiagnosisID: null, //接待ID
        Guidance: '', //导诊备注
        DiagnosisBy: '', //接诊人
        IsServicer: false, //是否是服务人员
        Status: '10', //10 不替换不追加  20 替换服务人员  30  追加服务人员
        ServicerID: null, //服务人员ID˝
        otherEmployeeID: null,
      },

      paginations: {
        page: 1, // 当前位于哪页
        total: 0, // 总数
        page_size: 10, // 1页显示多少条
        layout: 'total, prev, pager, next,jumper', // 翻页属性
      },
      tableData: [],
      previewImgSrcs: [],
      serviceList: [], //获取服务人员列表
      otherServiceList: [],

      cascaderProps: {
        checkStrictly: true,
        label: 'Name',
        value: 'ID',
        children: 'Detail',
      },
      serviceListServicer: {},
    };
  },

  /**计算属性  */
  computed: {},
  /**  方法集合  */
  methods: {
       /**    */
       customer_Export(type){
      let that = this;
      if (type == "excelNoDisPlayPhone") {
        that.diagnosis_excelNoDisPlayPhone();
      }

      
      if (type == "excelDisPlayPhone") {
        that.diagnosis_excelDisPlayPhone();
      }
    
    },
    /**    */
    gotoOrderCreated(row) {
      var that = this;
      that.$router.push({
        path: '/Order/Bill',
        name: 'Bill',
        params: { customerID: row.CustomerID },
      });
    },
    /**  搜索  */
    handleSearch() {
      this.paginations.page = 1;
      this.customerID = null;
      if (this.isEntityRang) {
        this.diagnosis_all();
      } else {
        this.diagnosis_list();
      }
    },
    /**    */
    handleCurrentChange(page) {
      this.paginations.page = page;
      this.customerID = null;
      if (this.isEntityRang) {
        this.diagnosis_all();
      } else {
        this.diagnosis_list();
      }
    },
    /**  获取列表服务人员信息  */
    getFirstServiceEmp(ServicerEmployee) {
      if (!ServicerEmployee || ServicerEmployee.length == 0) {
        return '';
      }
      let firstItem = ServicerEmployee[0];
      return firstItem.Name + ':' + this.getServicerEmpNames(firstItem.ServicerEmpList);
    },
    /**  服务人员处理  */
    getServicerEmpNames(ServicerEmpList) {
      if (!ServicerEmpList) {
        return '';
      }
      return ServicerEmpList.map((val) => (val ? val.Name : '')).join(', ');
    },
    /**  接诊操作  */
    diagnosisClick(row) {
      this.ruleForm = {
        DiagnosisID: null, //接待ID
        DiagnosisContent: '', //接诊记录
        IsNextFollowUp: true, //是否下次跟进
        PlannedOn: '', //计划跟进时间
        PlannedRemark: '', //计划跟进备注
        Attachment: [], // {{"AttachmentType": 10,"AttachmentURL": }}
      };
      if (this.$refs.diagnosisForm) {
        this.$refs['diagnosisForm'].resetFields();
      }
      this.ruleForm.DiagnosisID = row.DiagnosisRecordID;
      this.ruleForm.DiagnosisContent = row.Guidance;
      this.nextDateTime = this.$formatDate(new Date(), 'hh:mm:ss');
      this.ruleForm = Object.assign(this.ruleForm, row);
      this.diagnosisVisible = true;
    },
    /**  转诊  */
    transferDiagnosisClick(row) {
      this.transferRuleForm.ServicerID = '';
      this.transferRuleForm.DiagnosisBy = '';
      this.transferRuleForm.DiagnosisID = row.DiagnosisRecordID;
      this.transferRuleForm.DiagnosisContent = row.Guidance;
      this.transferRuleForm.IsServicer = false;
      this.transferRuleForm = Object.assign(this.transferRuleForm, row);
      this.getServiceList(row.CustomerID);
      this.customerDetailServicer(row.CustomerID);
      this.transferDiagnosisVisible = true;
    },
    /**  点击行  刷新右侧顾客详情数据 */
    clickDiagnosisRowData(row) {
      this.customerID = row.CustomerID;
      this.customerDetailVisible = true;
    },
    /**   图片上传前 回调   */
    beforeUpload(file) {
      let that = this;
      // const isSize3M = file.size / 1024 < 200;
      // if (!isSize3M) {
      //   that.$message.error("上传图片大小不能超过 200kb!");
      //   return false;
      // }
      utils.getImageBase64(file).then((base64) => {
        this.addAttachment(base64).then((AttachmentURL) => {
          that.$nextTick(() => {
            that.ruleForm.Attachment.push({
              AttachmentType: '10',
              AttachmentURL: AttachmentURL,
            });
          });
        });
      });

      // let reader = new FileReader();
      // reader.readAsDataURL(file);
      // reader.onload = function (evt) {
      //   let base64 = evt.target.result;
      //   that.$nextTick(() => {
      //     that.ruleForm.Attachment.push({
      //       AttachmentType: '10',
      //       AttachmentURL: base64
      //     });
      //   });
      // };
      return false;
    },
    /**  预览图片  */
    checkPreview(file) {
      document.getElementById(file.uid).click();
    },
    /**  删除图片  */
    removeDiagnosisImage(file) {
      let that = this;
      if (file && file.status !== 'success') return;
      let index = that.ruleForm.Attachment.findIndex((item) => item.AttachmentURL == file.AttachmentURL);
      that.ruleForm.Attachment.splice(index, 1);
    },
    /**  保存接诊信息  */
    saveDiagnosisClick() {
      this.$refs.diagnosisForm.validate((valid) => {
        if (valid) {
          this.diagnosis_diagnosis();
        }
      });
    },
    /**  获取服务人员 集合名字  */
    getEmpNames(item) {
      if (!item || !item.ServicerEmpList) {
        return '';
      }
      return item.ServicerEmpList.map((val) => (val ? val.Name : '')).join(', ');
    },
    /**  搜索其他人员  */
    searchEmpRemote(query) {
      this.employee_search(query);
    },
    /**    */
    setSelectServiceClass(item, emp) {
      if (this.transferRuleForm.IsServicer && this.transferRuleForm.ServicerID == item.ServicerID && this.transferRuleForm.DiagnosisBy == emp.EmployeeID) {
        return true;
      }
      return false;
    },
    /**  选择服务人员  */
    selectServiceEmpClick(item, emp) {
      this.transferRuleForm.IsServicer = true;
      this.transferRuleForm.ServicerID = item.ServicerID;
      this.transferRuleForm.DiagnosisBy = emp.EmployeeID;
      this.transferRuleForm.otherEmployeeID = null;
    },
    /**   选择其他服务人员 */
    selectOtherEmp() {
      this.transferRuleForm.IsServicer = false;
    },
    /**  确认指派  */
    confirmArrange() {
      if (this.transferActive == '2' && !this.transferRuleForm.IsServicer && this.transferRuleForm.ServicerID == '') {
        this.$message.error('请选择其他人员');
        return;
      }
      this.diagnosis_transferDiagnosis();
    },

    /** ******* ******* ******* ******* ******* ******* */

    /** 接诊列表   */
    async diagnosis_list() {
      let that = this;
      let params = {
        PageNum: this.paginations.page,
        Name: this.searchRuleForm.Name, //名称模糊搜索
        IsDiagnosis: this.searchRuleForm.IsDiagnosis, //是否接诊
        StartDate: this.searchRuleForm.DesignDate ? this.searchRuleForm.DesignDate[0] : '', //开始时间
        EndDate: this.searchRuleForm.DesignDate ? this.searchRuleForm.DesignDate[1] : '', //结束时间
        ChannelName: that.searchRuleForm.ChannelName, //渠道
        CustomerLevelID: that.searchRuleForm.CustomerLevelID, //会员等级
      };
      let res = await API.diagnosis_list(params);
      if (res.StateCode == 200) {
        that.tableData = res.List;
        that.paginations.total = res.Total;
        that.paginations.page_size = res.PageSize;
      } else {
        that.$message.error(res.Message);
      }
    },
    /** 接诊列表 权限下的   */
    async diagnosis_all() {
      let that = this;
      let params = {
        PageNum: this.paginations.page,
        Name: this.searchRuleForm.Name, //名称模糊搜索
        IsDiagnosis: this.searchRuleForm.IsDiagnosis, //是否接诊
        StartDate: this.searchRuleForm.DesignDate ? this.searchRuleForm.DesignDate[0] : '', //开始时间
        EndDate: this.searchRuleForm.DesignDate ? this.searchRuleForm.DesignDate[1] : '', //结束时间
        
        
        ChannelName: that.searchRuleForm.ChannelName, //渠道
        CustomerLevelID: that.searchRuleForm.CustomerLevelID, //会员等级
      };
      let res = await API.diagnosis_all(params);
      if (res.StateCode == 200) {
        that.tableData = res.List;
        that.paginations.total = res.Total;
        that.paginations.page_size = res.PageSize;
      } else {
        that.$message.error(res.Message);
      }
    },
    /**  员工接诊列表  */
    async customer_diagnosisRecord() {
      let that = this;
      let params = {
        PageNum: this.paginations.page,
        Name: this.searchRuleForm.Name, //名称模糊搜索
        IsDiagnosis: this.searchRuleForm.IsDiagnosis, //是否接诊
        StartDate: this.searchRuleForm.DesignDate ? this.searchRuleForm.DesignDate[0] : this.$formatDate(new Date(), 'YYYY-MM-DD'), //开始时间
        EndDate: this.searchRuleForm.DesignDate ? this.searchRuleForm.DesignDate[1] : this.$formatDate(new Date(), 'YYYY-MM-DD'), //结束时间
      };
      let res = await API.customer_diagnosisRecord(params);
      if (res.StateCode == 200) {
        that.tableData = res.List;
        that.paginations.total = res.Total;
        that.paginations.page_size = res.PageSize;
      } else {
        that.$message.error(res.Message);
      }
    },
    /**  接诊  */
    async diagnosis_diagnosis() {
      let that = this;
      let params = {
        DiagnosisID: this.ruleForm.DiagnosisID, //接待ID
        DiagnosisContent: this.ruleForm.DiagnosisContent, //接诊记录
        IsNextFollowUp: this.ruleForm.IsNextFollowUp, //是否下次跟进
        PlannedOn: this.ruleForm.PlannedOn ? this.$formatDate(this.ruleForm.PlannedOn, 'YYYY-MM-DD hh:mm:ss') : '', //计划跟进时间
        PlannedRemark: this.ruleForm.PlannedRemark, //计划跟进备注
        Attachment: this.ruleForm.Attachment, // {{"AttachmentType": 10,"AttachmentURL": }}
      };
      that.addLoading = true;
      let res = await API.diagnosis_diagnosis(params);
      if (res.StateCode == 200) {
        that.$message.success('操作成功');
        that.diagnosisVisible = false;
        that.handleSearch();
      } else {
        that.$message.error(res.Message);
      }
      that.addLoading = false;
    },
    /**  转诊  */
    async diagnosis_transferDiagnosis() {
      let that = this;
      let params = {
        DiagnosisID: this.transferRuleForm.DiagnosisID, //接待ID
        Guidance: this.transferRuleForm.Guidance, //导诊备注
        DiagnosisBy: this.transferRuleForm.IsServicer ? this.transferRuleForm.DiagnosisBy : this.transferRuleForm.otherEmployeeID, //接诊人
        IsServicer: this.transferRuleForm.IsServicer, //是否是服务人员
        Status: this.transferRuleForm.Status, //10 未到店  20 已完成  30  已取消
        ServicerID: this.transferRuleForm.IsServicer ? this.transferRuleForm.ServicerID : '', //服务人员ID
      };
      that.transferLoading = true;
      let res = await API.diagnosis_transferDiagnosis(params);
      if (res.StateCode == 200) {
        that.$message.success('操作成功');
        that.transferDiagnosisVisible = false;
        that.handleSearch();
      } else {
        that.$message.error(res.Message);
      }
      that.transferLoading = false;
    },
    /* 获取服务人员列表 */
    async getServiceList(customerID) {
      let that = this;
      that.loading = true;
      let params = { CustomerID: customerID };
      let res = await API.getServiceList(params);
      if (res.StateCode == 200) {
        that.serviceList = res.Data.map((val) => {
          val.searchKey = '';
          return val;
        });
      } else {
        that.$message.error({
          message: res.Message,
          duration: 2000,
        });
      }
    },
    /* 获取服务人员列表 */
    async customerDetailServicer(customerID) {
      let that = this;
      that.loading = true;
      let params = { CustomerID: customerID };
      let res = await API.customerDetailServicer(params);
      if (res.StateCode == 200) {
        that.serviceListServicer = res.Data;
      } else {
        that.$message.error({
          message: res.Message,
          duration: 2000,
        });
      }
    },
    /**  获取其他服务人员  */
    async employee_search(SearchKey) {
      let that = this;
      let params = { SearchKey: SearchKey };
      let res = await API.employee_search(params);
      if (res.StateCode == 200) {
        that.otherServiceList = res.Data;
      } else {
        that.$message.error(res.Message);
      }
    },

    /** 图片上传   */
    async addAttachment(base64) {
      let that = this;
      let params = { AttachmentURL: base64 };
      let res = await APIUpload.addAttachment(params);
      if (res.StateCode == 200) {
        return res.Data.AttachmentURL;
      } else {
        that.$message.error(res.Message);
      }
    },

    /* 导出 */
    diagnosis_excelNoDisPlayPhone() {
      let that = this;
      let params = {
        Name: this.searchRuleForm.Name, //名称模糊搜索
        IsDiagnosis: this.searchRuleForm.IsDiagnosis, //是否接诊
        StartDate: this.searchRuleForm.DesignDate ? this.searchRuleForm.DesignDate[0] : '', //开始时间
        EndDate: this.searchRuleForm.DesignDate ? this.searchRuleForm.DesignDate[1] : '', //结束时间
        ChannelName: that.searchRuleForm.ChannelName, //渠道
        CustomerLevelID: that.searchRuleForm.CustomerLevelID, //会员等级
      };
      that.downloadNoDisPlayLoading = true;
      API.diagnosis_excelNoDisPlayPhone(params)
        .then((res) => {
          this.$message.success({
            message: '正在导出',
            duration: '4000',
          });
          const link = document.createElement('a');
          let blob = new Blob([res], { type: 'application/octet-stream' });
          link.style.display = 'none';
          link.href = URL.createObjectURL(blob);
          link.download = '接诊工作台隐藏手机号表.xlsx'; //下载的文件名
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
        })
        .finally(function () {
          that.downloadNoDisPlayLoading = false;
        });
    },
    /* 导出 */
    diagnosis_excelDisPlayPhone() {
      let that = this;
      let params = {
        Name: this.searchRuleForm.Name, //名称模糊搜索
        IsDiagnosis: this.searchRuleForm.IsDiagnosis, //是否接诊
        StartDate: this.searchRuleForm.DesignDate ? this.searchRuleForm.DesignDate[0] : '', //开始时间
        EndDate: this.searchRuleForm.DesignDate ? this.searchRuleForm.DesignDate[1] : '', //结束时间
        ChannelName: that.searchRuleForm.ChannelName, //渠道
        CustomerLevelID: that.searchRuleForm.CustomerLevelID, //会员等级
      };
      that.downloadLoading = true;
      API.diagnosis_excelDisPlayPhone(params)
        .then((res) => {
          this.$message.success({
            message: '正在导出',
            duration: '4000',
          });
          const link = document.createElement('a');
          let blob = new Blob([res], { type: 'application/octet-stream' });
          link.style.display = 'none';
          link.href = URL.createObjectURL(blob);
          link.download = '接诊工作台表.xlsx'; //下载的文件名
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
        })
        .finally(function () {
          that.downloadLoading = false;
        });
    },
    
    /** 查询会员等级   */
    customerLevel_all(){
      let that = this;
      let params = {};
      API.customerLevel_all(params)
       .then((res) => {
         if(res.StateCode == 200){
          that.customerLevelList = res.Data;
          }
          else{
             that.$message.error(res.Message);
           }
         })
         .catch((fail) => {
           that.$message.error(fail);
         });
    },
  },
  /** 创建实例之前执行函数   */
  beforeCreate() {},
  /**  实例创建完成之后  */
  created() {
    // this.diagnosis_list();
    // this.getServiceList();
  },
  /**  在挂载开始之前被调用  */
  beforeMount() {},
  /**  实例被挂载后调用  */
  mounted() {
    let that = this;
    this.isEntityRang = this.$permission.permission(this.$route.meta.Permission, 'iBeauty-Workbench-Diagnosis-EntityRang');
    that.isCustomerPhoneNumberView = that.$permission.permission(that.$route.meta.Permission, 'iBeauty-Workbench-Diagnosis-CustomerPhoneNumberView');
    that.isCustomerPhoneNumberModify = that.$permission.permission(that.$route.meta.Permission, 'iBeauty-Workbench-Diagnosis-CustomerPhoneNumberModify');

    that.isCustomerBasicInformationModify = that.$permission.permission(
      that.$route.meta.Permission,
      'iBeauty-Workbench-Diagnosis-CustomerBasicInformationModify'
    );
    that.isCustomerServicerModify = that.$permission.permission(that.$route.meta.Permission, 'iBeauty-Workbench-Diagnosis-CustomerServicerModify');
    that.isCustomerBasicFileModify = that.$permission.permission(that.$route.meta.Permission, 'iBeauty-Workbench-Diagnosis-CustomerBasicFileModify');

    that.openBillState = that.$permission.routerPermission('/Order/Bill');
    that.customerLevel_all();
    if (this.isEntityRang) {
      this.diagnosis_all();
    } else {
      this.diagnosis_list();
    }
  },

  beforeRouteEnter(to, from, next) {
    next((vm) => {
      vm.openBillState = vm.$permission.routerPermission('/Order/Bill');
      vm.isEntityRang = vm.$permission.permission(to.meta.Permission, 'iBeauty-Workbench-Diagnosis-EntityRang');
      vm.isCustomerPhoneNumberView = vm.$permission.permission(to.meta.Permission, 'iBeauty-Workbench-Diagnosis-CustomerPhoneNumberView');
      vm.isCustomerPhoneNumberModify = vm.$permission.permission(to.meta.Permission, 'iBeauty-Workbench-Diagnosis-CustomerPhoneNumberModify');

      vm.isCustomerBasicInformationModify = vm.$permission.permission(to.meta.Permission, 'iBeauty-Workbench-Diagnosis-CustomerBasicInformationModify');
      vm.isCustomerServicerModify = vm.$permission.permission(to.meta.Permission, 'iBeauty-Workbench-Diagnosis-CustomerServicerModify');
      vm.isCustomerBasicFileModify = vm.$permission.permission(to.meta.Permission, 'iBeauty-Workbench-Diagnosis-CustomerBasicFileModify');
    });
  },
  /**  数据更新 之前 调用  */
  beforeUpdate() {},
  /** 数据更新 完成 调用   */
  updated() {},
  /**  实例销毁之前调用  */
  beforeDestroy() {},
  /**  实例销毁后调用  */
  destroyed() {},
};
</script>

<style lang="scss">
.diagnosis {
  // background-color: #f0f0f0;
  // padding: unset;
  // display: flex;
  // height: 100%;
  // max-height: 100%;
  // box-sizing: border-box;
  .el_scrollbar_height_followup {
    height: 65vh;
    .el-scrollbar__wrap {
      overflow-x: hidden;
    }
  }
  .workbench {
    background-color: #ffffff;
    box-sizing: border-box;
    padding: 15px;
    min-height: 100%;
    border-right: 8px solid #f0f0f0;
  }
  .custom-scrollbar-class {
    .el-scrollbar__wrap {
      overflow-x: auto;
      height: calc(100% + 20px); //多出来的20px是横向滚动条默认的样式
    }
    .el-scrollbar__wrap .el-scrollbar__view {
      white-space: nowrap;
      display: inline-block;
    }
  }
  .serviceTypeClass {
    .el-scrollbar__wrap {
      overflow-x: hidden;
    }
    .selectServiceEmp {
      border: solid 2px var(--zl-color-orange-primary);
      box-sizing: border-box;
    }
  }
  .empItem {
    margin-top: 10px;
    margin-left: 10px;
    margin-right: 10px;
    box-sizing: border-box;
  }
  .customer-detail {
    background-color: #ffffff;
    padding: 15px;
    height: 100%;
    box-sizing: border-box;
  }
  .el-upload--picture-card {
    width: 100px;
    height: 100px;
    font-size: 16px !important;
  }
  .el-upload {
    width: 100px;
    height: 100px;
    line-height: 100px;
    font-size: 16px;
  }
  .el-upload-list--picture-card {
    .el-upload-list__item {
      width: 100px;
      height: 100px;
      line-height: 100px;
      font-size: 16px;
    }
  }
}

.custom-el-select {
  li {
    line-height: normal;
    height: auto;
  }
}
</style>
