/**
 * 销售明细报表
 */
 import * as API from '@/api/index'

 export default {
    // 销售报表-销售
    getSaleDetailStatement: params => {
        return API.POST('api/entitySaleDetailStatement/list', params)
    },
    // 销售报表-销售欠款
    getSaleArrearDetailStatement: params => {
        return API.POST('api/entitySaleArrearDetailStatement/list', params)
    },
    // 营业报表-消耗统计
    getSaleRefundDetailStatement: params => {
        return API.POST('api/entitySaleRefundDetailStatement/list', params)
    },

    // 营业报表-消耗统计
    allEntity: params => {
      return API.POST('api/entity/allEntity', params)
    },


  // 销售报表-销售
  exportSaleDetailStatement: params => {
    return API.exportExcel('api/entitySaleDetailStatement/excel', params)
  },
  // 销售报表-销售欠款
  exportSaleArrearDetailStatement: params => {
      return API.exportExcel('api/entitySaleArrearDetailStatement/excel', params)
  },
  // 营业报表-消耗统计
  exportSaleRefundDetailStatement: params => {
      return API.exportExcel('api/entitySaleRefundDetailStatement/excel', params)
  },



  

  // 销售报表-销售
  entitySaleDetailStatement_excelDisPlayPhone: params => {
    return API.exportExcel('api/entitySaleDetailStatement/excelDisPlayPhone', params)
  },
  // 销售报表-销售欠款
  entitySaleArrearDetailStatement_excelDisPlayPhone: params => {
      return API.exportExcel('api/entitySaleArrearDetailStatement/excelDisPlayPhone', params)
  },
  // 营业报表-消耗统计
  entitySaleRefundDetailStatement_excelDisPlayPhone: params => {
      return API.exportExcel('api/entitySaleRefundDetailStatement/excelDisPlayPhone', params)
  },


  
 }