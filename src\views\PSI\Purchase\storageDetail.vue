<template>
  <div class="PurchaseStorageDetail content_body" v-loading="loading">

    <div class="nav_header">
      <el-form :inline="true" size="small" :model="searchFrom" @submit.native.prevent>
        <el-form-item v-if="EntityList.length>1" label="仓库/门店">
          <el-select v-model="searchFrom.EntityID" clearable filterable placeholder="请选择门店" :default-first-option="true" @change="handleSearchClick">
            <el-option v-for="item in EntityList" :key="item.ID" :label="item.EntityName" :value="item.ID">
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item v-if="SupplierList.length>1" label="供应商名称">
          <el-select v-model="searchFrom.SupplierID" clearable filterable placeholder="请选择供应商" :default-first-option="true" @change="handleSearchClick">
            <el-option v-for="item in SupplierList" :key="item.ID" :label="item.Name" :value="item.ID">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="操作人">
          <el-input v-model="searchFrom.CreatedBy" clearable @keyup.enter.native="handleSearchClick" @clear="handleSearchClick" placeholder="请输入操作人姓名"></el-input>
        </el-form-item>

        <el-form-item label="产品分类">
          <el-cascader @change="handleSearchClick" @clear="handleSearchClick" :options="classifyList" :show-all-levels="true" clearable filterable :props="cascaderProps" v-model="searchFrom.CategoryID" placeholder="请选择产品分类"></el-cascader>
        </el-form-item>

        <el-form-item label="产品名称">
          <el-input v-model="searchFrom.ProductName" clearable @keyup.enter.native="handleSearchClick" @clear="handleSearchClick" placeholder="请输入产品名称"></el-input>
        </el-form-item>

        <el-form-item label="制单时间">
          <el-date-picker v-model="searchFrom.QueryDate" unlink-panels type="daterange" range-separator="至" value-format="yyyy-MM-dd" start-placeholder="开始日期" end-placeholder="结束日期" @change="handleSearchClick"></el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" v-prevent-click @click="handleSearchClick">搜索</el-button>
        </el-form-item>
        <el-form-item>
          <el-button v-if="isExport" type="primary" size="small" :loading="downloadLoading" @click="downloadExcel">导出</el-button>
        </el-form-item>
      </el-form>
    </div>
    <el-table size="small" show-summary :summary-method="getSummary" :data="tableData">
      <el-table-column prop="ID" label="单据号"></el-table-column>
      <el-table-column prop="EntityName" label="仓库/门店"></el-table-column>
      <el-table-column prop="InventoryType" label="入库类型"></el-table-column>
      <el-table-column prop="SupplierName" label="供应商"></el-table-column>
      <el-table-column prop="PCategoryName" label="产品分类"></el-table-column>
      <el-table-column prop="BrandName" label="产品品牌"></el-table-column>
      <el-table-column prop="ProductName" label="产品名称"></el-table-column>
      <el-table-column prop="UnitPrice" label="采购单价"></el-table-column>
      <el-table-column prop="Quantity" label="采购数量"></el-table-column>
      <el-table-column prop="UnitName" label="采购单位"></el-table-column>
      <el-table-column prop="Amount" label="合计金额">
        <template slot-scope='scope'>
          {{scope.row.Amount | toFixed | NumFormat}}
        </template>
      </el-table-column>
      <el-table-column prop="Specification" label="产品规格"></el-table-column>
      <el-table-column prop="MinimumUnitQuantity" label="最小包装数量"></el-table-column>
      <el-table-column prop="ActualQuantity" label="实际入库数量">
        <template slot-scope="scope">
          {{ scope.row.ActualQuantity || 0 }}
        </template>
      </el-table-column>
      <el-table-column prop="RemainingQuantity" label="剩余待入库数量">
        <template slot-scope="scope">
          {{ scope.row.RemainingQuantity || 0 }}
        </template>
      </el-table-column>
      <el-table-column prop="RemainingMinimumUnitQuantity" label="剩余最小单位待入库数量">
        <template slot-scope="scope">
          {{ scope.row.RemainingMinimumUnitQuantity || 0 }}
        </template>
      </el-table-column>
      <el-table-column prop="InDate" label="入库时间">
        <template slot-scope="scope">
           {{scope.row.InDate | dateFormat("YYYY-MM-DD HH:mm") }}
        </template>
      </el-table-column>
      <el-table-column prop="CreatedOn" label="制单时间">
          <template slot-scope="scope">
           {{scope.row.CreatedOn | dateFormat("YYYY-MM-DD HH:mm") }}
        </template>
      </el-table-column>
      <el-table-column prop="EmployeeName" label="操作人"></el-table-column>
      <el-table-column prop="Remark" label="备注信息" show-overflow-tooltip></el-table-column>

    </el-table>
    <div class="pad_15 text_right">
      <el-pagination background v-if="paginations.total > 0" @current-change="handlePageChange" :current-page.sync="paginations.page" :page-size="paginations.page_size" :layout="paginations.layout" :total="paginations.total"></el-pagination>
    </div>

  </div>
</template>

<script>
import API from "@/api/PSI/Purchase/storageDetail.js";
import APIStorage from "@/api/PSI/Purchase/storage";
import permission from "@/components/js/permission.js";

export default {
  name: "PurchaseStorageDetail",
  beforeRouteEnter(to, from, next) {
    next((vm) => {
      vm.isExport = permission.permission(
        to.meta.Permission,
        "PSI-Purchase-StorageDetail-Export"
      );
    });
  },
  props: {},
  /**  引入的组件  */
  components: {},
  /**  Vue 实例的数据对象**/
  data() {
    return {
      isExport: false,
      loading: false,
      downloadLoading: false,
      tableData: [],
      tableDataSum: {},
      searchFrom: {
        PageNum: 1,
        EntityID: "", //门店
        SupplierID: "",
        CategoryID: "", //分类ID
        QueryDate: "",
        StartTime: "", //开始时间
        EndTime: "", //结束时间
        ProductName: "", //产品名称
        CreatedBy: "", //申请人
      },
      EntityList: [], //门店列表
      classifyList: [],
      SupplierList: [],

      cascaderProps: {
        checkStrictly: true,
        value: "ID",
        label: "Name",
        children: "Child",
        emitPath: false,
        expandTrigger: "hover",
      }, // 级联选择器配置项

      //需要给分页组件传的信息
      paginations: {
        page: 1, // 当前位于哪页
        total: 0, // 总数
        page_size: 10, // 1页显示多少条
        layout: "total, prev, pager, next,jumper", // 翻页属性
      },
    };
  },
  /**计算属性  */
  computed: {},
  /**  方法集合  */
  methods: {
    /**  搜索  */
    handleSearchClick() {
      let that = this;
      that.paginations.page = 1;
      that.purchaseStorage_detail();
    },
    /**  修改分页  */
    handlePageChange(page) {
      let that = this;
      that.paginations.page = page;
      that.purchaseStorage_detail();
    },

    /**    */
    getSummary({ columns }) {
      let that = this;
      // const { columns } = param;
      const sums = [];
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = <span class="font_weight_600">合计</span>;
          return;
        }
        var filter_NumFormat = this.$options.filters["NumFormat"];
        switch (column.property) {
          case "Quantity":
            sums[index] = (
              <span class="font_weight_600">
                {that.tableDataSum ? that.tableDataSum.Quantity : 0}
              </span>
            );
            break;
          case "Amount":
            sums[index] = (
              <span class="font_weight_600">
                ¥{" "}
                {filter_NumFormat(
                  that.tableDataSum ? that.tableDataSum.Amount : 0
                )}
              </span>
            );
            break;
          default:
            sums[index] = <span class="font_weight_600"></span>;
        }
      });
      return sums;
    },

    /**   导出 */
    downloadExcel() {
      let that = this;
      that.purchaseStorage_excel();
    },
    /**  **********************************************  */
    /**    */
    async purchaseStorage_detail() {
      let that = this;

      let params = Object.assign({}, that.searchFrom);
      params.PageNum = that.paginations.page;
      params.StartTime = params.QueryDate ? params.QueryDate[0] : "";
      params.EndTime = params.QueryDate ? params.QueryDate[1] : "";
      that.loading = true;
      let res = await API.purchaseStorage_detail(params);
      if (res.StateCode == 200) {
        that.tableData = res.Data.detail.List;
        that.tableDataSum =
          res.Data.purchaseStorageDetailSumStatementOutputForm;
        that.paginations.total = res.Data.detail.Total;
      } else {
        that.$message.error(res.Message);
      }
      that.loading = false;
    },

    /**  4.4.仓库列表  */
    async getStorageEntityNetwork() {
      var that = this;
      let params = Object.assign({}, that.searchFrom);
      params.PageNum = that.paginations.page;
      params.StartTime = params.QueryDate ? params.QueryDate[0] : "";
      params.EndTime = params.QueryDate ? params.QueryDate[1] : "";
      let res = await APIStorage.getpurchaseStorageEntity(params);
      if (res.StateCode == 200) {
        that.EntityList = res.Data;
      } else {
        that.$message.error({
          message: res.Message,
          duration: 2000,
        });
      }
    },
    /** 4.5.供应商列表  */
    async getStorageStorageNetwork() {
      var that = this;
      var params = {};
      let res = await APIStorage.getpurchaseStorageSupplier(params);
      if (res.StateCode == 200) {
        that.SupplierList = res.Data;
      } else {
        that.$message.error({
          message: res.Message,
          duration: 2000,
        });
      }
    },

    // 获取产品分类列表
    async getProductCategory() {
      var that = this;
      let res = await API.getValidProductCategory();
      if (res.StateCode == 200) {
        that.classifyList = res.Data;
      } else {
        that.$message.error({
          message: res.Message,
          duration: 2000,
        });
      }
    },

    /**  导出  */
    async purchaseStorage_excel() {
      let that = this;
      that.downloadLoading = true;
      let params = Object.assign({}, that.searchFrom);
      params.PageNum = that.paginations.page;
      params.StartTime = params.QueryDate ? params.QueryDate[0] : "";
      params.EndTime = params.QueryDate ? params.QueryDate[1] : "";
      let res = await API.purchaseStorage_excel(params);
      // if(res.StateCode == 200){
      //   that.$message.success("导出成功");

      this.$message.success({
        message: "正在导出",
        duration: "4000",
      });
      const link = document.createElement("a");
      let blob = new Blob([res], { type: "application/octet-stream" });
      link.style.display = "none";
      link.href = URL.createObjectURL(blob);
      link.download = "采购入库明细.xlsx"; //下载的文件名
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      // }
      // else{
      //   // that.$message.error(res.Message);
      // }
      that.downloadLoading = false;
    },
  },
  /** 监听数据变化   */
  watch: {},
  /** 创建实例之前执行函数   */
  beforeCreate() {},
  /**  实例创建完成之后  */
  created() {},
  /**  在挂载开始之前被调用  */
  beforeMount() {},
  /**  实例被挂载后调用  */
  mounted() {
    let that = this;
    that.purchaseStorage_detail();
    that.getStorageEntityNetwork();
    that.getStorageStorageNetwork();
    that.getProductCategory();
  },
  /**  数据更新 之前 调用  */
  beforeUpdate() {},
  /** 数据更新 完成 调用   */
  updated() {},
  /**  实例销毁之前调用  */
  beforeDestroy() {},
  /**  实例销毁后调用  */
  destroyed() {},
};
</script>

<style lang="scss">
.PurchaseStorageDetail {
}
</style>
