<template>
  <div class="followSelect">
    <!-- 标签 -->
    <el-card class="box-card" shadow="never">
      <div class="dis_flex flex_x_between flex_y_center marbm_20">
        <div>
          <span style="margin-right: 48px">{{ title }}</span>
          <span class="color_999">{{ subTitle }}</span>
        </div>
        <i class="el-icon-close" @click="handlerClose(Code)"></i>
      </div>
      <div class="dis_flex flex_wrap">
        <div class="marrt_15">{{ contentTitle }}</div>
        <template v-if="contentValues_ && contentValues_.length > 0">
          <el-tag v-for="(tag, index) in contentValues_tags" :key="tag.ID + tag.Name" closable @close="tagsChangeClear(index)" type="warning" class="marrt_10" size="small">
            {{ tag.Name }}
          </el-tag>
        </template>
        <el-select v-model="contentValues_" filterable multiple collapse-tags style="margin-left: 20px" placeholder="请选择" size="small" @change="selectTagsChange">
          <el-option v-for="item in tags" :key="getOptionKey(item)" :label="getOptionLabel(item)" :value="getOptionKey(item)"> </el-option>
        </el-select>
      </div>
    </el-card>
  </div>
</template>

<script>
import API_LABLE from "@/api/CRM/Customer/customerTagLibrary";
export default {
  name: "followSelectTag",
  props: {
    title: {
      type: String,
      default: "",
    },
    subTitle: {
      type: String,
      default: null,
    },
    contentTitle: {
      type: String,
      default: null,
    },
    Code: {
      type: String,
      default: null,
    },
    contentValues: {
      type: Array,
      default: () => [],
    },
    options: {
      type: Object,
      default: () => {
        return {
          ID: "ID",
          Name: "Name",
        };
      },
    },
    metadata: {
      type: Array,
      default: () => [],
    },
  },
  /** 监听数据变化   */
  watch: {
    contentValues: {
      handler(val) {
        this.contentValues_ = val;
      },
      immediate: true,
    },
    contentValues_: {
      handler(val) {
        if (this.tags.length > 0) {
          this.contentValues_tags = val.map((i) => {
            let tempItem = this.tags.find(fi=>fi.ID == i);
            return tempItem;
          });
        }
      },
      immediate: true,
    },
  },
  /**  引入的组件  */
  components: {},
  /**  Vue 实例的数据对象**/
  data() {
    return {
      contentValues_: [],
      contentValues_tags: [],
      tags: [],
    };
  },
  /**计算属性  */
  computed: {

  },
  /**  方法集合  */
  methods: {
    getOptionKey(item) {
      return item[this.options.ID];
    },

    getOptionLabel(item) {
      return item[this.options.Name];
    },
    /**    */
    handlerClose() {
      this.$emit("handlerChildClone");
    },

    // 选择标签
    selectTagsChange() {
      this.$emit("handlerChildChange", this.contentValues_);
    },

    // 标签
    async getcustomerTagLibraryList() {
      let params = {
        Name: "",
        PageNum: this.PageNum,
      };
      let res = await API_LABLE.customerTagLibraryAll(params);
      if (res.StateCode == 200) {
        this.tags = res.Data;
         this.contentValues_tags = this.contentValues_.map((i) => {
            let tempItem = this.tags.find(fi=>fi.ID == i);
            return tempItem;
          });
      } else {
        this.$message.error({
          message: res.Message,
          duration: 2000,
        });
      }
    },
    // 删除tags标签
    tagsChangeClear(index) {
      this.triggerCondition.selectTags.splice(index, 1);
      this.tags.unshift(this.triggerCondition.selectTags[index]);
    },
  },
  /** 创建实例之前执行函数   */
  beforeCreate() {},
  /**  实例创建完成之后  */
  created() {},
  /**  在挂载开始之前被调用  */
  beforeMount() {},
  /**  实例被挂载后调用  */
  mounted() {
    this.getcustomerTagLibraryList();
  },
  /**  数据更新 之前 调用  */
  beforeUpdate() {},
  /** 数据更新 完成 调用   */
  updated() {},
  /**  实例销毁之前调用  */
  beforeDestroy() {},
  /**  实例销毁后调用  */
  destroyed() {},
};
</script>

<style lang="scss">
.followSelect {
}
</style>
