import * as API from '@/api/index';

export default {
  /* 接待工作台列表 */
  getReception: (params) => {
    return API.POST('api/reception/list', params);
  },
  /* 确认到访 */
  confirmCime: (params) => {
    return API.POST('api/reception/confirm', params);
  },
  // 服务人员
  servicerStaff: (params) => {
    return API.POST('api/servicer/diagnosisServicer', params);
  },
  // 其他人员
  otherStaff: (params) => {
    return API.POST('api/employee/search', params);
  },
  // 确认并指派
  confirmAssign: (params) => {
    return API.POST('api/reception/confirmAssign', params);
  },
  // 确认指派
  confirmArrange: (params) => {
    return API.POST('api/reception/assign', params);
  },
  // 重新指派
  afreshArrange: (params) => {
    return API.POST('api/reception/againAssign', params);
  },
  // 到访登记上的确认到访
  confirmVist: (params) => {
    return API.POST('api/reception/confirmVist', params);
  },
  // 选择会员时展示的其服务人员
  custDetailServicer: (params) => {
    return API.POST('api/servicer/customerDetailServicer', params);
  },

  // 导出 隐藏手机号
  reception_excelNoDisPlayPhone: (params) => {
    return API.exportExcel('api/reception/excelNoDisPlayPhone', params);
  },
  // 导出 显示手机号
  reception_excelDisPlayPhone: (params) => {
    return API.exportExcel('api/reception/excelDisPlayPhone', params);
  },
  /* 查询会员等级 */
  customerLevel_all: (params) => {
    return API.POST('api/customerLevel/all', params);
  },
};
