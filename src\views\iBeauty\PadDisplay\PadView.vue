<template>
  <div class="Groupon content_body padView" v-loading="isVClaassLoading">
    <el-container style="height: 100%; background: #ccc">
      <el-aside width="17vw" class="bgcol-5B70FF">
        <el-container class="onwAside">
          <el-main>
            <ul class="infinite-list son" style="height: 100%">
              <li v-for="(i, index) in GoodsDisplayList" :key="index" class="infinite-list-item marbm_10" @click="oneClick(index)">
                <div class="dis_flex flex_y_center padlt_10 item" :class="oneActiveIndex == index ? 'oneClass' : 'color_fff'">
                  <el-image style="width: 46px; height: 46px; margin: 0 10px 0 12px; border-radius: 5px" :src="i.ImageURL" fit="cover"> </el-image>
                  <div class="clamp1" style="width: calc(100% - 70px); font-size: 16px">
                    {{ i.Name }}
                  </div>
                </div>
              </li>
            </ul>
          </el-main>
        </el-container>
      </el-aside>
      <el-main class="Content">
        <el-container style="height: 100%">
          <el-aside width="16vw" class="bgcol-4860fd" v-if="TwoCategories.length">
            <el-container class="twoAside">
              <el-main>
                <ul class="infinite-list son">
                  <li
                    v-for="(i, index) in GoodsDisplayList[oneActiveIndex].Child.length ? GoodsDisplayList[oneActiveIndex].Child : ''"
                    :key="index"
                    @click="twoClick(index)"
                    class="infinite-list-item dis_flex flex_y_center flex_dir_column marbm_10 border_box"
                  >
                    <el-image class="marbm_10" style="width: " :class="twoActiveIndex == index ? 'border' : ''" :src="i.ImageURL" fit="cover"> </el-image>
                    <div class="clamp2" style="font-size: 14px" :style="twoActiveIndex == index ? 'color:#6e7eeb' : ''">
                      {{ i.Name }}
                    </div>
                  </li>
                </ul>
              </el-main>
            </el-container>
          </el-aside>
          <el-main class="mainBox">
            <el-container class="bgcol-f3f4ff" style="height: 100%">
              <el-main class="main" v-loading="isLoading">
                <div class="grid" v-if="TwoCategories.length">
                  <div v-for="(i, index) in TwoCategories[twoActiveIndex].Goods" :key="index" @click="showDetail(index)">
                    <el-image style="width: 160px; height: 160px; margin: 0 10px 0 12px" :src="i.GoodsImageURL" fit="cover">
                      <div
                        slot="error"
                        class="image-slot dis_flex flex_x_center flex_y_center"
                        style="height: 100%; background: linear-gradient(90deg, #a2aeff, #4961fd)"
                      >
                        <span class="font_13 color_fff">暂未上传图片</span>
                      </div>
                    </el-image>
                    <div class="padbm_5 font_14">
                      <div class="pad_0_10 box_border clamp2 font_14" style="color: #333">{{ i.GoodsName }}</div>
                      <div class="pad_0_10 box_border clamp2 font_14 martp_5" style="color: #ff2727" v-if="i.Price">{{ i.Price | NumFormat }}</div>
                    </div>
                  </div>
                </div>
                <div v-else>
                  <div
                    style="width: 100%"
                    class="Memo"
                    v-if="GoodsDisplayList[oneActiveIndex] && GoodsDisplayList[oneActiveIndex].Memo"
                    v-html="GoodsDisplayList[oneActiveIndex].Memo"
                  ></div>
                </div>
              </el-main>
            </el-container>
          </el-main>
        </el-container>
      </el-main>
    </el-container>
    <el-drawer size="65%" show-close :with-header="false" :visible.sync="detailBool" @close="drawerClose">
      <div style="width: 100%; height: 100%; padding: 1% 1% 0">
        <div class="position_absolute detailClose dis_flex flex_x_center flex_y_center" style="top: 4%; right: 3%" @click="drawerClose">
          <i class="el-icon-close"></i>
        </div>
        <div style="width: 100%; height: 100%" v-loading="isDetailLoading" class="Memo" v-html="GoodsDisplayMemo"></div>
        <div class="position_absolute nextBtn" style="left: calc(1% + 10px)" v-if="detailIndex != 0" @click="showDetail(detailIndex - 1)">上一个</div>
        <div class="position_absolute nextBtn" style="right: calc(1% + 10px)" v-if="detailIndex < getGoodsLength()" @click="showDetail(detailIndex + 1)">
          下一个
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import API from "@/api/iBeauty/PadDisplay/PadView";
export default {
  name: "ProjectSeckill",

  data() {
    return {
      isVClaassLoading: false,
      oneActiveIndex: 0,
      twoActiveIndex: 0,
      GoodsDisplayList: [], //分类列表
      isLoading: false,
      isDetailLoading: false,
      oneActiveMemo: [], //一级分类简介
      GoodsDisplayMemo: [], //二级分类简介
      TwoCategories: [],
      detailBool: false, //详情弹层
      title: "", //弹层标题
      detailIndex: 0,
    };
  },
  components: {},
  mounted() {
    const bool = this.GetQueryString("token");
    if (bool) {
      window.localStorage.setItem("access-user", JSON.stringify({ AuthToken: bool }));
    }
    const that = this;
    that.getGoodsDisplayList();
  },

  methods: {
    getGoodsLength() {
      if (
        this.GoodsDisplayList[this.oneActiveIndex] &&
        this.GoodsDisplayList[this.oneActiveIndex].Child &&
        this.GoodsDisplayList[this.oneActiveIndex].Child.length &&
        this.GoodsDisplayList[this.oneActiveIndex].Child[this.twoActiveIndex].Goods
      ) {
        return this.GoodsDisplayList[this.oneActiveIndex].Child[this.twoActiveIndex].Goods.length - 1;
      }
      return 0;
    },
    GetQueryString(name) {
      var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
      var r = window.location.search.substr(1).match(reg);
      if (r != null) return unescape(r[2]);
      return null;
    },
    // 获取一级分类简介
    getOneClassMemo(ID, index) {
      this.isLoading = true;
      API.Memo({ ID })
        .then((res) => {
          if (res.StateCode == 200) {
            this.GoodsDisplayList[index].Memo = res.Message;
          }
        })
        .finally(() => {
          this.isLoading = false;
        });
    },
    // 获取商品分类
    getGoodsDisplayList() {
      const that = this;
      that.isVClaassLoading = true;
      API.GoodsDisplayList()
        .then((res) => {
          if (res.StateCode == 200) {
            if (res.Data.length && !res.Data[0].Child.length) {
              that.getOneClassMemo(res.Data[0].ID, 0);
              that.TwoCategories = res.Data[0].Child;
            } else if (res.Data.length && res.Data[0].Child.length) {
              that.TwoCategories = res.Data[0].Child;
            }

            that.GoodsDisplayList = res.Data;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(() => {
          that.isVClaassLoading = false;
        });
    },
    // 点击一级分类
    oneClick(index) {
      this.oneActiveIndex = index;
      this.isLoading = false;
      this.twoActiveIndex = 0;
      if (!this.GoodsDisplayList[index].Child.length && !this.GoodsDisplayList[index].Memo) {
        this.getOneClassMemo(this.GoodsDisplayList[index].ID, index);
      }
      if (this.GoodsDisplayList[index].Child.length) {
        this.TwoCategories = this.GoodsDisplayList[index].Child;
      } else {
        this.TwoCategories = [];
      }
    },
    // 点击二级分类
    twoClick(index) {
      this.twoActiveIndex = index;
    },
    // getImgSrc(richtext) {
    //   let imgList = [];
    //   richtext.replace(
    //     /<img [^>]*src=['"]([^'"]+)[^>]*>/g,
    //     (match, capture) => {
    //       imgList.push(capture);
    //     }
    //   );
    //   return imgList;
    // },
    // 查看详情
    showDetail(index) {
      this.detailBool = true;
      this.detailIndex = index;
      let item = this.TwoCategories[this.twoActiveIndex].Goods[index];
      if (this.GoodsDisplayList[this.oneActiveIndex].Child[this.twoActiveIndex].Goods[index].Memo) {
        this.GoodsDisplayMemo = this.GoodsDisplayList[this.oneActiveIndex].Child[this.twoActiveIndex].Goods[index].Memo;
        return;
      }
      this.title = item.GoodsName;
      this.isDetailLoading = true;
      API.PadGoodsDisplayMemo({ ID: item.GoodsDisplayID })
        .then((res) => {
          if (res.StateCode == 200) {
            this.GoodsDisplayList[this.oneActiveIndex].Child[this.twoActiveIndex].Goods[index].Memo = res.Message;
            this.GoodsDisplayMemo = res.Message;
          } else {
            this.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(() => {
          this.isDetailLoading = false;
        });
    },

    drawerClose() {
      this.GoodsDisplayMemo = "";
      this.detailBool = false;
      this.isDetailLoading = false;
    },
  },
};
</script>

<style lang="scss" >
.padView.content_body {
  padding: unset;
}
.padView {
  height: 100%;
  .onwAside {
    .element::-webkit-scrollbar {
      width: 0 !important;
    }
    height: 100%;
    // .el-header {
    //   height: 85px !important;
    // }
    .el-main {
      height: 100%;
      padding: 10px 0 0 0;
      overflow-y: scroll;
      -webkit-overflow-scrolling: touch !important; //该属性随着手指离开还会保持滚动
      scrollbar-width: none; /* firefox */
      -ms-overflow-style: none; /* IE 10+ */
      .item {
        height: 65px;
        width: calc(100% - 20px);
      }
      .oneClass {
        color: #4860fd;
        background: #fff;
        border-top-right-radius: 8px;
        border-bottom-right-radius: 8px;
      }
    }

    .el-main::-webkit-scrollbar {
      width: 0 !important;
    }
  }
  .Content {
    height: 100%;
    padding: 0;
  }
  .twoAside {
    height: 100%;
    background-color: #fff;
    border-top-left-radius: 10px;
    border-bottom-left-radius: 10px;
    // .el-header {
    //   height: 85px !important;
    // }
    .el-main {
      padding: 10px 10px 0;
      overflow-y: scroll;
      -webkit-overflow-scrolling: touch !important; //该属性随着手指离开还会保持滚动
      scrollbar-width: none; /* firefox */
      -ms-overflow-style: none; /* IE 10+ */
      .son {
        li {
          // height: ;
          .el-image {
            width: 100%;
            height: calc(9vw - 20px);
            border-radius: 5px;
          }
          .border {
            border: 2px solid #6e7eeb;
          }
        }
      }
    }
    .el-main::-webkit-scrollbar {
      width: 0 !important;
    }
  }
  .mainBox {
    padding: 0;
    .el-container {
      flex-direction: column;
    }

    // .el-header {
    //   height: 85px !important;
    // }
    .main {
      padding: 10px 20px 0;
      .grid {
        display: grid;
        justify-content: space-between;
        grid-template-columns: repeat(auto-fill, 160px);
        grid-gap: 20px;
        > div {
          width: 160px;
          background-color: #fff;
          border-radius: 5px;
          overflow: hidden;
          .el-image {
            margin: unset !important;
          }
        }
      }
      .Memo {
        img {
          width: 100%;
          overflow-x: hidden;
        }
      }
    }
  }
  .el-drawer__body {
    overflow-y: auto;
    overflow-x: hidden;
    -webkit-overflow-scrolling: touch !important; //该属性随着手指离开还会保持滚动
    .Memo {
      img {
        width: 100%;
        overflow-x: hidden;
      }
    }
    .detailTitle {
      min-width: 200px;
      max-width: 400px;
      height: 20px;
      line-height: 20px;
      padding: 10px;
      background-color: #fff;
      border-radius: 10px;
    }
    .detailClose {
      width: 40px;
      height: 40px;
      border-radius: 20px;
      background-color: #fff;
    }
  }

  .clamp2 {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
  }

  .clamp1 {
    display: inline-block;
    white-space: nowrap;
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .nextBtn {
    top: 50%;
    background-color: rgba(0, 0, 0, 0.6);
    color: #ffffff;
    border-radius: 50%;
    height: 60px;
    width: 60px;
    line-height: 60px;
    text-align: center;
  }
}
</style>