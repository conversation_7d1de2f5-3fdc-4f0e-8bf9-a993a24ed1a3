<template>
  <div class="businessBills content_body" v-loading="loading">
    <!-- 搜索 -->
    <div class="nav_header" style="padding:15px 0 0 0">
      <el-form :inline="true" size="small" :model="mfSearchFrom" @submit.native.prevent>
        <el-form-item v-if="storeEntityList.length>1" label="门店">
          <el-select v-model="mfSearchFrom.EntityID" clearable filterable placeholder="请选择门店" :default-first-option="true" @change="mfHandleSaleSearch">
            <el-option v-for="item in storeEntityList" :key="item.ID" :label="item.EntityName" :value="item.ID">
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="下单日期">
          <el-date-picker v-model="mfSearchFrom.QueryDate" unlink-panels type="daterange" range-separator="至" value-format="yyyy-MM-dd" start-placeholder="开始日期" end-placeholder="结束日期" @change="mfHandleSaleSearch">
          </el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" v-prevent-click @click="mfHandleSaleSearch">搜索</el-button>
        </el-form-item>
      </el-form>
    </div>
    <el-row class="content">
      <el-form v-model="businessBillSumStatementForm">
        <el-col :span="24">
          <el-col :span="4">
            <el-form-item label="营业天数:">{{businessBillSumStatementForm.Days}}</el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item label="总客数:">{{businessBillSumStatementForm.TotalCustomer}}</el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item label="到店人头:">{{businessBillSumStatementForm.TotalCustomerHead}}</el-form-item>
          </el-col>
        </el-col>
        <el-col :span="24">
          <el-col :span="4">
            <el-form-item label="营业总收入:">{{businessBillSumStatementForm.Amount}}</el-form-item>
          </el-col>
          <el-col :span="4" v-for="(item,index) in businessBillSumStatementForm.PayMethod" :key="index">
            <el-form-item>
              <span style="color:#666">{{item.Name}}: </span>
              <span>{{item.PayAmount}}</span>
            </el-form-item>
          </el-col>
        </el-col>
        <el-col :span="24">
          <el-col :span="4">
            <el-form-item label="项目总消耗:">
              {{businessBillSumStatementForm.TreatAmount}}
            </el-form-item>
          </el-col>
        </el-col>
      </el-form>
    </el-row>

    <el-row class="content back_f8" style="border-top:1px solid #eeeeee;">
      <el-col :span="12">
        <el-row class=" marrt_5 backfff">
          <el-form label-width="auto" v-model="beautySale">
            <el-col style="border-bottom:1px solid #eeeeee; padding:10px">
              <el-col :span="24" class="font_16 color_333 text_center">美容</el-col>
              <el-col :span="12">
                <el-form-item label="总客数" v-form-tooltip="'总客数：查询当前门店美容/美发客数，一个顾客一天内多次发生销售/消耗算1，来几天算几次，每天重复计算'" v-model="beautyPassengerFlow">{{beautyPassengerFlow.TotalCustomer}}
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="到店人头" v-form-tooltip="'到店人头：查询当前门店美容/美发人头，在查询周期内发生销售/消耗的顾客人数，一个人只算一次'" v-model="beautyPassengerFlow">{{beautyPassengerFlow.TotalCustomerHead}} </el-form-item>
              </el-col>
            </el-col>

            <el-col style="border-bottom:1px solid #eeeeee; padding:10px">
              <el-col :span="24" class="font_16 color_333 text_center">销售数据</el-col>
              <el-form-item label="销售客数" v-form-tooltip="'销售客数：只查购买实付金额大于0的顾客数，一个顾客一天内多次算1，多天算多个'">{{beautySale.SaleCustomer}}
              </el-form-item>
              <el-form-item label="销售人头" v-form-tooltip="'销售人头：只查销售，且购买实付金额大于0的顾客，且查询期间内一个顾客多次只算1'">{{beautySale.SaleCustomerHead}}
              </el-form-item>
              <el-form-item label="人均销售客单计" v-form-tooltip="'人均销售客单：营业收入 ÷ 销售客数'">{{beautySale.Price}}
              </el-form-item>
              <el-form-item label="营业收入" v-form-tooltip="'分别查询美容/美发的实际支付金额'">{{beautySale.Amount}}
              </el-form-item>
                  <el-form-item label="支付方式" v-form-tooltip="'分别查出美容/美发的各个支付方式支付的金额'">
                    <el-col :span="8" v-for="(item,index) in beautySale.PayMethod" :key="index">{{item.Name}}:
                      {{item.PayAmount}}</el-col>
                  </el-form-item>
              <el-form-item label="充卡总金额" v-form-tooltip="'充卡总金额：购买储值卡+项目（不含快速开单的项目）+通用次卡+时效卡+套餐卡（不含产品）的实收金额'">
                {{beautySale.RechargeAmount}}
                <span style="color:#FA0000">(储值卡+疗程)</span>
              </el-form-item>
              <el-row>
                <el-col :span="8">
                  <el-form-item label="新开卡" v-form-tooltip="'新开卡：顾客首次购买美容/美发储值卡，实付金额，不含赠送'"> {{beautySale.NewRechargeAmount}}
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="老客充值" v-form-tooltip="'老客充卡：顾客第二次及以后购买/充值美容/美发储值卡，实付金额，不含赠送'"> {{beautySale.OldRechargeAmount}}
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="疗程销售金额" v-form-tooltip="'疗程销售金额：顾客在查询期间内购买项目（不含快速开单项目）+通用次卡+时效卡+套餐卡（不含产品）的实付金额'"> {{beautySale.SaleProjectAmount}}
                  </el-form-item>
                </el-col>
              </el-row>
              <el-form-item label="产品销售金额" v-form-tooltip="'产品销售金额：产品销售实收金额+套餐卡里的产品实收部分+快速开单里的产品实收'"> {{beautySale.SaleProductAmount}}
              </el-form-item>
              <el-form-item label="单次项目销售金额" v-form-tooltip="'单次项目销售金额：快速开单里的项目销售实收金额'"> {{beautySale.SingelSaleProjectAmount}}
              </el-form-item>
              <el-form-item label="本期赠送储值卡金额" v-form-tooltip="'本期赠送储值卡金额：查询周期内，赠送的美容/美发储值卡的总金额'" style="font-weight: 700"> {{beautySale.SavingCardLargessAmount}}
              </el-form-item>
            </el-col>
          </el-form>
          <el-form label-width="auto" v-model="beautyTreat">
            <el-col style="border-bottom:1px solid #eeeeee; padding:10px">
              <el-col :span="24" class="font_16 color_333 text_center">消耗划卡数据</el-col>
              <el-col :span="12">
                <el-form-item label="消耗客数" v-form-tooltip="'消耗客数：查询消耗项目的顾客人次，一个人一天内多次消耗项目算1，来几天算几次'">{{beautyTreat.TreatCustomer}}</el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="消耗人头" v-form-tooltip="'消耗人头：查询消耗项目的顾客人数，一个人在查询周期内只算1'">{{beautyTreat.TreatCustomerHead}}</el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="总划卡金额" v-form-tooltip="'总划卡金额：产品划卡+疗程划卡+项目储值卡划卡'">{{beautyTreat.Amount}}</el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="总划卡金额(赠送)" v-form-tooltip="'总划卡金额（赠送）：产品划卡（赠送储值卡）+项目划卡（赠送储值卡）+项目储值卡划卡（赠送储值卡）'">{{beautyTreat.LargessAmount}}</el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="产品划卡(储值卡)" v-form-tooltip="'产品划卡（储值卡）：非赠送储值卡抵扣的产品部分'">{{beautyTreat.ProductAmount}}</el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="产品划卡(赠送储值卡)" v-form-tooltip="'产品划卡（赠送储值卡）：赠送储值卡抵扣的产品部分'">{{beautyTreat.ProductLargessAmount}}</el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="疗程划卡" v-form-tooltip="'疗程划卡：不含快速开单的项目消耗，不含单独赠送项目消耗，项目+通用次卡+时效卡的消耗金额（赠金，不含本金）'">{{beautyTreat.ProjectAmount}}</el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="疗程划卡(赠送储值卡)" v-form-tooltip="'疗程划卡（赠送储值卡）：赠送储值卡抵扣的疗程部分'">{{beautyTreat.ProjectLargessAmount}}</el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="项目储值划卡" v-form-tooltip="'项目储值卡划卡：储值卡直接消耗项目的金额（不含赠送）'">{{beautyTreat.ProjectSavingCardAmount}}<span style="color:#FA0000">(储值卡消耗)</span></el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="项目储值划卡(赠送储值卡)" v-form-tooltip="'项目储值卡划卡（赠送储值卡）：赠送储值卡直接消耗项目的金额（赠金）'">{{beautyTreat.ProjectSavingCardLargessAmount}}</el-form-item>
              </el-col>
            </el-col>
          </el-form>
        </el-row>
      </el-col>
      <el-col :span="12">
        <el-row class=" marrt_5 backfff">
          <el-form label-width="auto" v-model="hairSale">
            <el-col style="border-bottom:1px solid #eeeeee; padding:10px">
              <el-col :span="24" class="font_16 color_333 text_center">美发</el-col>
              <el-col :span="12">
                <el-form-item label="总客数" v-form-tooltip="'总客数：查询当前门店美容/美发客数，一个顾客一天内多次发生销售/消耗算1，来几天算几次，每天重复计算'" v-model="hairPassengerFlow">{{hairPassengerFlow.TotalCustomer}}
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="到店人头" v-form-tooltip="'到店人头：查询当前门店美容/美发人头，在查询周期内发生销售/消耗的顾客人数，一个人只算一次'" v-model="hairPassengerFlow">{{hairPassengerFlow.TotalCustomerHead}} </el-form-item>
              </el-col>
            </el-col>

            <el-col style="border-bottom:1px solid #eeeeee; padding:10px">
              <el-col :span="24" class="font_16 color_333 text_center">销售数据</el-col>
              <el-form-item label="销售客数" v-form-tooltip="'销售客数：只查购买实付金额大于0的顾客数，一个顾客一天内多次算1，多天算多个'">{{hairSale.SaleCustomer}}
              </el-form-item>
              <el-form-item label="销售人头" v-form-tooltip="'销售人头：只查销售，且购买实付金额大于0的顾客，且查询期间内一个顾客多次只算1'">{{hairSale.SaleCustomerHead}}
              </el-form-item>
              <el-form-item label="人均销售客单价" v-form-tooltip="'人均销售客单：营业收入 ÷ 销售客数'">{{hairSale.Price}}
              </el-form-item>
              <el-form-item label="营业收入" v-form-tooltip="'分别查询美容/美发的实际支付金额'">{{hairSale.Amount}}
              </el-form-item>
               <el-form-item label="支付方式" v-form-tooltip="'分别查出美容/美发的各个支付方式支付的金额'">
                    <el-col :span="8" v-for="(item,index) in hairSale.PayMethod" :key="index">{{item.Name}}:
                      {{item.PayAmount}}</el-col>
                  </el-form-item>
              <el-form-item label="充卡总金额" v-form-tooltip="'充卡总金额：购买储值卡+项目（不含快速开单的项目）+通用次卡+时效卡+套餐卡（不含产品）的实收金额'">{{hairSale.RechargeAmount}}
                <span style="color:#FA0000">(储值卡+疗程)</span>
              </el-form-item>
              <el-col :span="8">
                <el-form-item label="新开卡" v-form-tooltip="'新开卡：顾客首次购买美容/美发储值卡，实付金额，不含赠送'">{{hairSale.NewRechargeAmount}}
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="老客充值" v-form-tooltip="'老客充卡：顾客第二次及以后购买/充值美容/美发储值卡，实付金额，不含赠送'">{{hairSale.OldRechargeAmount}}
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="疗程销售金额" v-form-tooltip="'疗程销售金额：顾客在查询期间内购买项目（不含快速开单项目）+通用次卡+时效卡+套餐卡（不含产品）的实付金额'">{{hairSale.SaleProjectAmount}}
                </el-form-item>
              </el-col>
              <el-form-item label="产品销售金额" v-form-tooltip="'产品销售金额：产品销售实收金额+套餐卡里的产品实收部分+快速开单里的产品实收'">{{hairSale.SaleProductAmount}}
              </el-form-item>
              <el-form-item label="单次项目销售金额" v-form-tooltip="'单次项目销售金额：快速开单里的项目销售实收金额'">{{hairSale.SingelSaleProjectAmount}}
              </el-form-item>
              <el-form-item label="本期赠送储值卡金额" v-form-tooltip="'本期赠送储值卡金额：查询周期内，赠送的美容/美发储值卡的总金额'" style="font-weight: 700">{{hairSale.SavingCardLargessAmount}}
              </el-form-item>
            </el-col>
          </el-form>
          <el-form label-width="auto" v-model="hairTreat">
            <el-col style="border-bottom:1px solid #eeeeee; padding:10px">
              <el-col :span="24" class="font_16 color_333 text_center">消耗划卡数据</el-col>
              <el-col :span="12">
                <el-form-item label="消耗客数" v-form-tooltip="'消耗客数：查询消耗项目的顾客人次，一个人一天内多次消耗项目算1，来几天算几次'">{{hairTreat.TreatCustomer}}</el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="消耗人头" v-form-tooltip="'消耗人头：查询消耗项目的顾客人数，一个人在查询周期内只算1'">{{hairTreat.TreatCustomerHead}}</el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="总划卡金额" v-form-tooltip="'总划卡金额：产品划卡+疗程划卡+项目储值卡划卡'">{{hairTreat.Amount}}</el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="总划卡金额(赠送)" v-form-tooltip="'总划卡金额（赠送）：产品划卡（赠送储值卡）+项目划卡（赠送储值卡）+项目储值卡划卡（赠送储值卡）'">{{hairTreat.LargessAmount}}</el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="产品划卡(储值卡)" v-form-tooltip="'产品划卡（储值卡）：非赠送储值卡抵扣的产品部分'">{{hairTreat.ProductAmount}}</el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="产品划卡(赠送储值卡)" v-form-tooltip="'产品划卡（赠送储值卡）：赠送储值卡抵扣的产品部分'">{{hairTreat.ProductLargessAmount}}</el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="疗程划卡" v-form-tooltip="'疗程划卡：不含快速开单的项目消耗，不含单独赠送项目消耗，项目+通用次卡+时效卡的消耗金额（赠金，不含本金）'">{{hairTreat.ProjectAmount}}</el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="疗程划卡(赠送储值卡)" v-form-tooltip="'疗程划卡（赠送储值卡）：赠送储值卡抵扣的疗程部分'">{{hairTreat.ProjectLargessAmount}}</el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="项目储值划卡" v-form-tooltip="'项目储值卡划卡：储值卡直接消耗项目的金额（不含赠送）;'">{{hairTreat.ProjectSavingCardAmount}}<span style="color:#FA0000">(储值卡消耗)</span></el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="项目储值划卡(赠送储值卡)" v-form-tooltip="'项目储值卡划卡（赠送储值卡）：赠送储值卡直接消耗项目的金额（赠金）'">{{hairTreat.ProjectSavingCardLargessAmount}}</el-form-item>
              </el-col>
            </el-col>
          </el-form>
        </el-row>

      </el-col>
    </el-row>

  </div>
</template>

<script>

import EntityAPI from "@/api/Report/Common/entity";
import API from '@/api/Report/YZH/Entity/businessBills'

export default {
  name: 'ReportYZHEntityBusinessBills',
  props: {},
  /**  引入的组件  */
  components: {},
  /**  Vue 实例的数据对象**/
  data() {
    return {
      hairTreat:{},
      hairSale:{},
      hairPassengerFlow:{},
      beautyTreat:{},
      beautySale: {},
      beautyPassengerFlow: {},
      businessBillSumStatementForm: {},
      Tooltip: "999jjjj",
      loading: false,
      activeName: "0",
      storeEntityList: [],
      categoryList: [],
      mfSearchFrom: {
        EntityID: "",
        QueryDate: [new Date(), new Date()],
      },
      thableData: [],

      mrpaginations: {
        page: 1, // 当前位于哪页
        total: 0, // 总数
        page_size: 10, // 1页显示多少条
        layout: "total, prev, pager, next,jumper", // 翻页属性
      },

      mfpaginations: {
        page: 1, // 当前位于哪页
        total: 0, // 总数
        page_size: 10, // 1页显示多少条
        layout: "total, prev, pager, next,jumper", // 翻页属性
      },

    }
  },
  /**计算属性  */
  computed: {
  },
  /**  方法集合  */
  methods: {
    /**    */
    mfHandleSaleSearch() {
      let that = this
      that.list_entityBusinessBillStatement()
    },

    // 数据
    async list_entityBusinessBillStatement() {
      let that = this
      let params = {
        EntityID: that.mfSearchFrom.EntityID,
        StartDate: that.mfSearchFrom.QueryDate === null ? "" : that.mfSearchFrom.QueryDate[0],
        EndDate: that.mfSearchFrom.QueryDate === null ? "" : that.mfSearchFrom.QueryDate[1],
      }
      let res = await API.list_entityBusinessBillStatement(params)
      if (res.StateCode == 200) {
        that.businessBillSumStatementForm = res.Data.businessBillSumStatementForm
        that.beautyPassengerFlow = res.Data.beautyPassengerFlow
        that.beautySale = res.Data.beautySale
        that.beautyTreat = res.Data.beautyTreat
        that.hairPassengerFlow = res.Data.hairPassengerFlow
        that.hairSale = res.Data.hairSale
        that.hairTreat = res.Data.hairTreat
      }
    },
    //获得当前用户下的权限门店
    getstoreEntityList() {
      var that = this;
      that.loading = true;
      EntityAPI.getStoreEntityList()
        .then((res) => {
          if (res.StateCode == 200) {
            that.storeEntityList = res.Data;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },
  },
  /** 监听数据变化   */
  watch: {},
  /** 创建实例之前执行函数   */
  beforeCreate() { },
  /**  实例创建完成之后  */
  created() { },
  /**  在挂载开始之前被调用  */
  beforeMount() { },
  /**  实例被挂载后调用  */
  mounted() {
    let that = this
    that.getstoreEntityList()
    that.list_entityBusinessBillStatement()
  },
  /**  数据更新 之前 调用  */
  beforeUpdate() { },
  /** 数据更新 完成 调用   */
  updated() { },
  /**  实例销毁之前调用  */
  beforeDestroy() { },
  /**  实例销毁后调用  */
  destroyed() { },


}
</script>

<style lang="scss">
.businessBills {
  .content {
    .el-form-item {
      margin-bottom: 0px;
      .el-form-item__label-wrap {
        margin-left: 0px !important;
      }
    }
  }
}
</style>
