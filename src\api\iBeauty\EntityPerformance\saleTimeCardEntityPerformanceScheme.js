/**
 * Created by Jo<PERSON><PERSON> on 2022/01/11.
 * 门店业绩-时效卡销售门店业绩
 */
import * as API from '@/api/index'

export default {
  // 获取门店时效卡销售业绩方案列表
  getTimeCardEntityPerformanceScheme: params => {
    return API.POST('api/saleTimeCardEntityPerformanceScheme/list', params)
  },

  // 创建门店时效卡销售业绩方案
  createTimeCardEntityPerformanceScheme: params => {
    return API.POST('api/saleTimeCardEntityPerformanceScheme/create', params)
  },

  // 删除门店时效卡销售业绩方案
  deleteTimeCardEntityPerformanceScheme: params => {
    return API.POST('api/saleTimeCardEntityPerformanceScheme/delete', params)
  },

  // 编辑的点击
  getTimeCardCategoryEntityPerformance: params => {
    return API.POST('api/saleTimeCardCategoryEntityPerformance/all', params)
  },

  // 编辑的保存
  updateTimeCardCategoryEntityPerformance: params => {
    return API.POST('api/saleTimeCardCategoryEntityPerformance/update', params)
  },

  // 时效卡业绩的点击
  getTimeCardEntityPerformance: params => {
    return API.POST('api/saleTimeCardEntityPerformance/all', params)
  },

  // 时效卡业绩的保存
  updateTimeCardEntityPerformance: params => {
    return API.POST('api/saleTimeCardEntityPerformance/update', params)
  },

}
