<template>
  <!--    门店业绩-储值卡消耗门店业绩-->
  <div class="treatSavingCardEntityPerformanceScheme content_body">
    <div class="nav_header">
      <el-row>
        <el-col :span="20">
          <el-form :inline="true" size="small" @submit.native.prevent>
            <el-form-item label="组织单位">
              <el-input
                v-model="name"
                placeholder="输入组织单位名称搜索"
                clearable
                @clear="handleSearch"
                @keyup.enter.native="handleSearch"
              ></el-input>
            </el-form-item>
            <el-form-item>
              <el-button
                type="primary"
                size="small"
                @click="handleSearch"
                v-prevent-click
              >搜索</el-button
              >
            </el-form-item>
          </el-form>
        </el-col>
        <el-col :span="4" class="text_right">
          <el-button
            type="primary"
            size="small"
            @click="showAddDialog"
            v-prevent-click
          >新增</el-button
          >
        </el-col>
      </el-row>
    </div>
    <div>
      <el-table size="small" :data="tableData">
        <el-table-column prop="EntityName" label="组织单位"></el-table-column>
        <el-table-column label="操作" width="145px">
          <template slot-scope="scope">
            <el-button
              type="primary"
              size="small"
              @click="showEditDialog(scope.row)"
              v-prevent-click
            >编辑</el-button
            >
            <el-button
              type="danger"
              size="small"
              @click="removeEntityClick(scope.row)"
              v-prevent-click
              v-if="isDelete"
            >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <div class="page pad_10">
        <div class="text_right" v-if="paginations.total > 0">
          <el-pagination
            background
            @current-change="handleCurrentChange"
            :current-page.sync="paginations.page"
            :page-size="paginations.page_size"
            :layout="paginations.layout"
            :total="paginations.total"
          ></el-pagination>
        </div>
      </div>
    </div>
    <!--新增弹窗-->
    <el-dialog
      title="新增储值卡消耗门店业绩方案"
      :visible.sync="dialogVisible"
      width="30%"
      custom-class="custom-dialog-add"
    >
      <div>
        <el-form
          :model="ruleForm"
          :rules="rules"
          ref="ruleForm"
          label-width="120px"
          size="small"
        >
          <el-form-item label="适用组织" prop="EntityID">
            <span slot="label">
              适用组织
              <el-popover placement="top-start" width="200" trigger="hover">
                <p>适用于同级所有节点，则只需选择父节点。</p>
                <p>比如：适用于所有节点，只需选择“顶级/第一个”节点。</p>
                <el-button
                  type="text"
                  style="color: #dcdfe6"
                  icon="el-icon-info"
                  slot="reference"
                ></el-button>
              </el-popover>
            </span>
            <treeselect
              v-model="ruleForm.EntityID"
              :options="entityList"
              :normalizer="normalizer"
              clearValueText
              noResultsText="无匹配数据"
              placeholder="选择适用组织"
            />
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="dialogVisible = false" v-prevent-click
        >取 消</el-button
        >
        <el-button
          type="primary"
          size="small"
          :loading="modalLoading"
          v-prevent-click
          @click="submitSavingCardEntityPerformanceScheme"
        >保存</el-button
        >
      </div>
    </el-dialog>

    <!--编辑弹窗-->
    <el-dialog :visible.sync="dialogEdit" custom-class="custom-dialog-edit" width="50%">
      <div slot="title">{{ entityName }} - 储值卡分类消耗门店业绩方案</div>
      <el-table
        :data="savingCardEntityPerformance"
        size="small"
        max-height="500px"
        :row-class-name="tableRowClassName"
        row-key="CategoryID"
        :tree-props="{ children: 'Child', hasChildren: 'hasChild' }"
      >
        <el-table-column
          prop="CategoryName"
          label="储值卡分类"
          min-width="100px"
          fixed
        ></el-table-column>

        <el-table-column
          prop="CardRate"
          label="卡抵扣比例"
          min-width="105px"
        >
          <template slot-scope="scope">
            <el-input
              type="number"
              size="mini"
              v-input-fixed="2"
              v-model="scope.row.CardRate"
              class="input_type"
              @input="royaltyRateChange(0, scope.row)"
            >
              <template slot="append">%</template>
            </el-input>
          </template>
        </el-table-column>
        <el-table-column
          prop="CardLargessRate"
          label="赠送卡抵扣比例"
          min-width="105px"
        >
          <template slot-scope="scope">
            <el-input
              type="number"
              size="mini"
              v-input-fixed="2"
              v-model="scope.row.CardLargessRate"
              class="input_type"
              @input="royaltyRateChange(1, scope.row)"
            >
              <template slot="append">%</template>
            </el-input>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="115px">
          <template slot-scope="scope">
            <el-button
              type="primary"
              size="mini"
              @click="savingCardPerformanceClick(scope.row)"
              v-if="!scope.row.isEntity"
            >储值卡业绩</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="dialogEdit = false" v-prevent-click
        >取 消</el-button
        >
        <el-button
          type="primary"
          size="small"
          :loading="modalLoading"
          v-prevent-click
          @click="submitSavingCardEntityPerformance"
        >保 存</el-button
        >
      </div>
    </el-dialog>

    <!--储值卡弹窗-->
    <el-dialog :visible.sync="dialogSavingCardEntity" width="40%" custom-class="custom-dialog-edit_product">
      <div slot="title">
        {{ entityName }} - {{ categoryName }} - 储值卡消耗门店业绩方案
      </div>
      <div>
        <el-form :inline="true" size="small" @submit.native.prevent>
          <el-form-item>
            <el-input
              v-model="searchSavingCardName"
              placeholder="输入储值卡名称搜索"
              prefix-icon="el-icon-search"
              clearable
            ></el-input>
          </el-form-item>
        </el-form>
        <el-table
          :data="
            savingCardPerformance.filter(
              (data) =>
                !searchSavingCardName ||
                data.GoodName.toLowerCase().includes(
                  searchSavingCardName.toLowerCase()
                )
            )
          "
          max-height="500px"
          size="small"
        >
          <el-table-column
            prop="GoodName"
            label="储值卡名称"
            min-width="150px"
            fixed
          ></el-table-column>
          <el-table-column
            prop="CardRate"
            label="卡抵扣比例"
            min-width="105px"
          >
            <template slot-scope="scope">
              <el-input
                type="number"
                v-model="scope.row.CardRate"
                v-input-fixed="2"
                class="input_type"
                @input="royaltyRateChange(0, scope.row)"
                size="mini"
              >
                <template slot="append">%</template>
              </el-input>
            </template>
          </el-table-column>
          <el-table-column
            prop="CardLargessRate"
            label="赠送卡抵扣比例"
            min-width="105px"
          >
            <template slot-scope="scope">
              <el-input
                type="number"
                v-model="scope.row.CardLargessRate"
                v-input-fixed="2"
                class="input_type"
                @input="royaltyRateChange(1, scope.row)"
                size="mini"
              >
                <template slot="append">%</template>
              </el-input>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="dialogSavingCardEntity = false" v-prevent-click
        >取 消</el-button
        >
        <el-button
          type="primary"
          size="small"
          :loading="modalLoading"
          v-prevent-click
          @click="submitSavingCardEntityCategoryPerformance"
        >保存</el-button
        >
      </div>
    </el-dialog>

  </div>
</template>

<script>
  import APIEntity from "@/api/KHS/Entity/entity";
  import API from "@/api/iBeauty/EntityPerformance/treatSavingCardEntityPerformanceScheme";
  var Enumerable = require("linq");
  import "@riophae/vue-treeselect/dist/vue-treeselect.css";
  import Treeselect from "@riophae/vue-treeselect";
export default {
 name: 'treatSavingCardEntityPerformanceScheme',

beforeRouteEnter(to, from, next) {
 next((vm) => {
     vm.isDelete = vm.$permission.permission(
      to.meta.Permission,
      "KHS-EntityPerformance-TreatSavingCardEntityPerformanceScheme-Delete"
    );
  });
},
  props:{},
  /**  引入的组件  */
  components: {
    Treeselect
  },
  /**  Vue 实例的数据对象**/
  data() {
    return {
      loading: false,
      modalLoading: false,
      dialogVisible: false,
      dialogEdit: false,
      dialogSavingCardEntity: false,
      isDelete: false,
      name: "",
      entityList: [], //选择组织
      tableData: [], //组织单位列表
      searchSavingCardName: "", //储值卡业绩弹框搜索框
      entityID: "",
      categoryID: "",
      entityName: "", //门店名称
      categoryName: "",
      savingCardEntityPerformance: [],
      savingCardPerformance: [],
      ruleForm: {
        EntityID: null,
      },
      rules: {
        EntityID: [
          { required: true, message: "请选择组织", trigger: "change" },
        ],
      },
      //需要给分页组件传的信息
      paginations: {
        page: 1, // 当前位于哪页
        total: 0, // 总数
        page_size: 10, // 1页显示多少条
        layout: "total, prev, pager, next,jumper", // 翻页属性
      },
    };
  },
   /**计算属性  */
  computed: {
  },
  /**  方法集合  */
  methods: {
    normalizer(node) {
      return {
        id: node.ID,
        label: node.EntityName,
        children: node.Child,
      };
    },
    tableRowClassName({ rowIndex }) {
      if (rowIndex === 0) {
        return "info-row";
      }
      return "";
    },
    // 所属单位
    entityData: function () {
      var that = this;
      APIEntity.getEntityAll()
        .then((res) => {
          if (res.StateCode == 200) {
            that.entityList = res.Data;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },
    // 数据显示
    handleSearch: function () {
      let that = this;
      that.paginations.page = 1;
      that.search();
    },
    // 数据显示
    search: function () {
      let that = this;
      that.loading = true;
      var params = {
        Name: that.name,
        PageNum: that.paginations.page,
      };
      API.getSavingCardEntityPerformanceScheme(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.tableData = res.List;
            that.paginations.total = res.Total;
            that.paginations.page_size = res.PageSize;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },
    // 上下分页
    handleCurrentChange(page) {
      var that = this;
      that.paginations.page = page;
      that.search();
    },
    // 编辑
    showEditDialog: function (row) {
      var that = this;
      that.entityName = row.EntityName;
      that.entityID = row.EntityID;
      that.getSavingCardCategoryEntityPerformance();
    },
    // 编辑 获取储值卡消耗门店业绩
    getSavingCardCategoryEntityPerformance: function () {
      var that = this;
      that.loading = true;
      var params = {
        EntityID: that.entityID,
      };
      API.getSavingCardCategoryEntityPerformance(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.dialogEdit = true;
            var data = {
              CategoryID: "0" + res.Data.EntityID,
              CategoryName: "所有储值卡",
              CardRate: res.Data.CardRate, //赠卡比例业绩
              CardLargessRate: res.Data.CardLargessRate,
              isEntity: true,
            };
            var Category = Enumerable.from(res.Data.Category)
              .select((val) => ({
                CategoryID: val.CategoryID,
                CategoryName: val.CategoryName,
                CardRate: val.CardRate, //赠卡比例业绩
                CardLargessRate: val.CardLargessRate,
              }))
              .toArray();
            that.savingCardEntityPerformance = Object.assign([], Category);
            that.savingCardEntityPerformance.unshift(data);
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },
    // 点击储值卡业绩 获取储值卡业绩列表
    getSavingCardEntityPerformance: function () {
      var that = this;
      // that.loading = true;
      var params = {
        EntityID: that.entityID,
        CategoryID: that.categoryID,
      };
      API.getSavingCardEntityPerformance(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.dialogSavingCardEntity = true;
            that.savingCardPerformance = res.Data;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },
    // 储值卡业绩的点击
    savingCardPerformanceClick: function (row) {
      var that = this;
      that.categoryID = row.CategoryID;
      that.categoryName = row.CategoryName;
      that.getSavingCardEntityPerformance();

    },
    // 保存 储值卡业绩
    submitSavingCardEntityCategoryPerformance: function () {
      var that = this;
      var savingCardPerformance = Enumerable.from(that.savingCardPerformance)
        .where(function (i) {
          return (
            (i.CardRate !== "" && i.CardRate !== null) ||
            (i.CardLargessRate !== "" && i.CardLargessRate !== null)
          );
        })
        .select((val)=>({
          GoodID: val.GoodID,
          CardRate: val.CardRate,
          CardLargessRate: val.CardLargessRate,
        }))
        .toArray();
      that.modalLoading = true;
      var params = {
        EntityID: that.entityID,
        Good: savingCardPerformance,
        CategoryID: that.categoryID,
      };
      API.updateSavingCardEntityPerformance(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.$message.success("业绩设置成功");
            that.dialogSavingCardEntity = false;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.modalLoading = false;
        });
    },
    // 保存 编辑
    submitSavingCardEntityPerformance: function () {
      var that = this;
      that.modalLoading = true;
      var params = {
        EntityID: that.savingCardEntityPerformance[0].CategoryID,
        CardRate: that.savingCardEntityPerformance[0].CardRate,
        CardLargessRate: that.savingCardEntityPerformance[0].CardLargessRate,
      };

      var Category = that.savingCardEntityPerformance;
      Category = Enumerable.from(Category)
        .where(function (i) {
          return !i.isEntity;
        })
        .toArray();

      Category = Enumerable.from(Category)
        .where(function (i) {
          return (
            (i.CardRate !== "" && i.CardRate !== null) ||
            (i.CardLargessRate !== "" && i.CardLargessRate !== null)
          );
        })
        .select((val)=>({
          CategoryID: val.CategoryID,
          CardRate: val.CardRate, //赠卡比例业绩
          CardLargessRate: val.CardLargessRate,
        }))
        .toArray();
      params.Category = Category;
      API.updateSavingCardCategoryEntityPerformance(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.$message.success("业绩设置成功");
            that.dialogEdit = false;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.modalLoading = false;
        });
    },
    // 新增 组织单位
    showAddDialog: function () {
      var that = this;
      that.ruleForm = {
        EntityID: null,
      };
      that.dialogVisible = true;
    },
    // 创建 组织单位
    submitSavingCardEntityPerformanceScheme: function () {
      var that = this;
      that.$refs.ruleForm.validate((valid) => {
        if (valid) {
          that.modalLoading = true;
          let para = Object.assign({}, that.ruleForm);
          API.createSavingCardEntityPerformanceScheme(para)
            .then(function (res) {
              if (res.StateCode === 200) {
                that.$message.success({
                  message: "新增成功",
                  duration: 2000,
                });
                that.search();
                that.$refs["ruleForm"].resetFields();
                that.dialogVisible = false;
              } else {
                that.$message.error({
                  message: res.Message,
                  duration: 2000,
                });
              }
            })
            .finally(function () {
              that.modalLoading = false;
            });
        }
      });
    },
    // 删除 组织单位
    removeEntityClick: function (row) {
      var that = this;
      that
        .$confirm("此操作将永久删除该记录, 是否继续?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
        .then(() => {
          that.loading = false;
          var params = {
            EntityID: row.EntityID,
          };
          API.deleteSavingCardEntityPerformanceScheme(params)
            .then((res) => {
              if (res.StateCode == 200) {
                that.$message.success("删除成功");
                that.search();
              } else {
                that.$message.error({
                  message: res.Message,
                  duration: 2000,
                });
              }
            })
            .finally(function () {
              that.loading = false;
            });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    // 提成比例
    royaltyRateChange: function (index, row) {
      if (index == 0) {
        if (row.CardRate > 100) {
          row.CardRate = 100;
        }
      } else if (index == 1) {
        if (row.CardLargessRate > 100) {
          row.CardLargessRate = 100;
        }
      }
    },
  },
  /** 监听数据变化   */
  watch: {},
  /** 创建实例之前执行函数   */
  beforeCreate() {},
  /**  实例创建完成之后  */
  created() {},
  /**  在挂载开始之前被调用  */
  beforeMount() {},
  /**  实例被挂载后调用  */
  mounted() {
    this.isDelete = this.$permission.permission(
      this.$route.meta.Permission,
       "KHS-EntityPerformance-TreatSavingCardEntityPerformanceScheme-Delete"
    );
    this.handleSearch();
    this.entityData();
  },
  /**  数据更新 之前 调用  */
  beforeUpdate() {},
  /** 数据更新 完成 调用   */
  updated() {},
  /**  实例销毁之前调用  */
  beforeDestroy() {},
  /**  实例销毁后调用  */
  destroyed() {},
}
</script>

<style lang="scss">

.treatSavingCardEntityPerformanceScheme{
  .input_type {
    .el-input-group__append {
      padding: 0 10px;
    }
  }
  .el-input__inner {
    padding-right: 0;
  }

  .el-table .info-row {
    background: #c0c4cc;
  }
  .custom-dialog-add{
    min-width: 500px;
  }
  .custom-dialog-edit{
    min-width: 950px;
  }
  .custom-dialog-edit_product{
    min-width: 850px;
  }
 }
</style>
