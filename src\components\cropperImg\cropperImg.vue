<template>
  <div class="cropperImg">
    <el-dialog
      custom-class="custom-cropperImg"
      title="图片剪裁"
      :visible.sync="dialogVisible"
      append-to-body
      @close="cropperImgClose"
      :close-on-click-modal="false"
      width="1200px"
    >
      <el-row>
        <el-col :span="12">
          <div class="cropper" style="text-align: center">
            <vue-cropper
              ref="cropper"
              :img="option.img"
              :output-size="option.size"
              :output-type="option.outputType"
              :info="true"
              :full="option.full"
              :can-move="option.canMove"
              :autoCropWidth="option.autoCropWidth"
              :autoCropHeight="option.autoCropHeight"
              :can-move-box="option.canMoveBox"
              :original="option.original"
              :auto-crop="option.autoCrop"
              :fixed="option.fixed"
              :fixed-number="option.fixedNumber"
              :center-box="option.centerBox"
              :info-true="option.infoTrue"
              :fixed-box="option.fixedBox"
              :high="option.high"
              :enlarge="option.enlarge"
              @realTime="realTimePreviewImg"
            ></vue-cropper>
          </div>
        </el-col>
        <el-col :span="12" class="dis_flex flex_x_center">
          <div v-if="previewImg" :style="previewStyle">
            <div :style="previewImg.div">
              <img :src="previewImg.url" :style="previewImg.img" />
            </div>
          </div>
        </el-col>
      </el-row>
      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="dialogVisible = false">取 消</el-button>
        <el-button size="small" type="primary" @click="confirmCropperImg" :loading="loading">确认</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { VueCropper } from "vue-cropper";
export default {
  name: "cropperImg",
  props: {
    /**  弹窗  */
    cropperImgDialog: {
      type: Boolean,
      default: false,
    },
    /**  图片源  */
    img: {
      default: "",
    },
    /**  是否固定图片比例  */
    fixed: {
      Type: Boolean,
      default: false,
    },
    /**  图片比例  */
    fixedNumber: {
      Type: Array,
      default() {
        return [1, 1];
      },
    },
    autoCropWidth: {
      Type: Number,
    },
    autoCropHeight: {
      Type: Number,
    },
  },
  /** 监听数据变化   */
  watch: {
    /**  弹窗  */
    cropperImgDialog: {
      immediate: true,
      handler(val) {
        this.dialogVisible = val;
      },
    },
    /**  图片地址  */
    img: {
      immediateL: true,
      handler(val) {
        this.option.img = val;
      },
    },
    /**  是否开启截图框宽高固定比例  */
    fixed: {
      immediate: true,
      handler(val) {
        this.option.fixed = val;
      },
    },
    fixedNumber: {
      immediate: true,
      handler(val) {
        if (val) {
          this.option.fixedNumber = val;
        } else {
          this.option.fixedNumber = null;
        }
      },
    },
    autoCropWidth: {
      immediate: true,
      handler(val) {
        if (val) {
          this.option.autoCropWidth = val;
        }
      },
      default:"800px",
    },
    autoCropHeight: {
      immediate: true,
      handler(val) {
        if (val) {
          this.option.autoCropHeight = val;
        }
      },
      default:"800px",
    },
  },
  /**  引入的组件  */
  components: { VueCropper },
  /**  Vue 实例的数据对象**/
  data() {
    return {
      dialogVisible: false,
      option: {
        img: "", // 裁剪图片的地址
        info: false, // 裁剪框的大小信息
        outputSize: 1, // 裁剪生成图片的质量
        outputType: "png", // 裁剪生成图片的格式
        canScale: false, // 图片是否允许滚轮缩放
        autoCrop: true, // 是否默认生成截图框
        autoCropWidth: "800px", // 默认生成截图框宽度
        autoCropHeight: "800px", // 默认生成截图框高度
        fixedBox: false, // 固定截图框大小 不允许改变
        fixed: false, // 是否开启截图框宽高固定比例
        // fixedNumber:[1,1], // 截图框的宽高比例
        full: false, // 是否输出原图比例的截图
        canMoveBox: true, // 截图框能否拖动
        original: false, // 上传图片按照原始比例渲染
        centerBox: true, // 截图框是否被限制在图片里面
        infoTrue: true, // true 为展示真实输出图片宽高 false 展示看到的截图框宽高
        high:false,
        enlarge:2,
      },
      picsList: [], //页面显示的数组
      loading: false, // 防止重复提交
      previewStyle: "",
      previewImg: null,
    };
  },
  /**计算属性  */
  computed: {},
  /**  方法集合  */
  methods: {
    /**  关闭弹窗  */
    cropperImgClose() {
      let that = this;
      that.$emit("update:cropperImgDialog", false);
    },
    /**  切图实时预览  */
    realTimePreviewImg(data) {
      let z = 0.8;
      this.previewStyle = {
        width: data.w + "px",
        height: data.h + "px",
        overflow: "hidden",
        margin: "0",
        zoom: z,
      };
      this.previewImg = data;
    },

    // 限制图片大小
    changeImage(file) {
      this.loading = false;
      const isLt5M = file.size / 1024 / 1024 < 5;
      if (!isLt5M) {
        this.$message.error("上传文件大小不能超过 5MB!");
        return false;
      } // 上传成功后将图片地址赋值给裁剪框显示图片
      this.$nextTick(() => {
        this.option.img = file.url;
        this.dialogVisible = true;
      });
    },
    /**  点击裁剪，这一步是可以拿到处理后的地址  */
    confirmCropperImg() {
      this.$refs.cropper.getCropData((data) => {
        this.$emit("getCropperImgBase64Data", data);
      });
    }, // blob 装 base64
    blobToDataURI(blob, callback) {
      const reader = new FileReader();
      reader.readAsDataURL(blob);
      reader.onload = function (e) {
        callback(e.target.result);
      };
    },
  },
  /** 创建实例之前执行函数   */
  beforeCreate() {},
  /**  实例创建完成之后  */
  created() {},
  /**  在挂载开始之前被调用  */
  beforeMount() {},
  /**  实例被挂载后调用  */
  mounted() {},
  /**  数据更新 之前 调用  */
  beforeUpdate() {},
  /** 数据更新 完成 调用   */
  updated() {},
  /**  实例销毁之前调用  */
  beforeDestroy() {},
  /**  实例销毁后调用  */
  destroyed() {},
};
</script>

<style lang="scss">
.cropper {
  width: auto;
  height: 400px;
}
.cropperImg {
  .custom-cropperImg {
  }
}
</style>
