/**
 * Created by wsf on 2022/01/27.
 * 跟进工作台 api
 */

import * as API from '@/api/index';

export default {
  /* 跟进列表-只能看自己 */
  getFollowUpList: (params) => {
    return API.POST('api/followUp/list', params);
  },
  /* 跟进列表-权限下 */
  getFollowUpAll: (params) => {
    return API.POST('api/followUp/all', params);
  },
  /* 线索列表查询 - 新增接口 */
  getLeadList: (params) => {
    return API.POST('api/lead/list', params);
  },
  /* 顾客跟进列表 */
  getCustomerFollowUp: (params) => {
    return API.POST('api/customer/followUp', params);
  },
  /* 新建跟进 */
  createFollowUp: (params) => {
    return API.POST('api/followUp/create', params);
  },
  /* 跟进 */
  FollowUp: (params) => {
    return API.POST('api/followUp/followUp', params);
  },
  /* 线索跟进 - 新接口 */
  followUpByLead: (params) => {
    return API.POST('api/followUp/followUpByLead', params);
  },
  /* 更新跟进 */
  updateFollowUp: (params) => {
    return API.POST('api/followUp/update', params);
  },
  /* 删除跟进 */
  deleteFollowUp: (params) => {
    return API.POST('api/followUp/delete', params);
  },
  /* 指派 */
  assignFollowUp: (params) => {
    return API.POST('api/followUp/assign', params);
  },
  /* 指派-服务人员 */
  getServicer: (params) => {
    return API.POST('api/servicer/servicer', params);
  },
  /* 指派-其他人员 */
  getSearch: (params) => {
    return API.POST('api/employee/search', params);
  },
  /* 跟进部门 */
  getFollowUpEntity: (params) => {
    return API.POST('api/followUp/followUpEntity', params);
  },
  /* 跟进人员 */
  getFollowUpEmployee: (params) => {
    return API.POST('api/followUp/followUpEmployee', params);
  },
  /* 跟进预约 */
  createAppointmentBill: (params) => {
    return API.POST('api/followUp/createAppointmentBill', params);
  },
  /* 跟进预约选择门店 */
  getallEntity: (params) => {
    return API.POST('api/entity/allEntity', params);
  },
  /* 获取预约类型 */
  getAppointmentType: (params) => {
    return API.POST('api/appointmentType/all', params);
  },
  /* 获取预约配置 */
  getAppointmentConfig: (params) => {
    return API.POST('api/appointment/config', params);
  },
  /* 更新 */
  consult_update: (params) => {
    return API.POST("api/consult/update", params);
  },
  /* 更新接诊信息 */
  diagnosis_update: (params) => {
    return API.POST('api/diagnosis/update', params);
  },
  // 导出 隐藏手机号
  followUp_excelNoDisPlayPhone: (params) => {
    return API.exportExcel('api/followUp/excelNoDisPlayPhone', params);
  },
  // 导出 显示手机号
  followUp_excelDisPlayPhone: (params) => {
    return API.exportExcel('api/followUp/excelDisPlayPhone', params);
  },

  /* 查询会员等级 */
  customerLevel_all: (params) => {
    return API.POST('api/customerLevel/all', params);
  },
   /* 重新指派 */
  followUp_anewAssign: (params) => {
    return API.POST("api/lead/assign", params);
  },
  /* 呼叫 */
  phoneCallBack_callBack: (params) => {
    return API.POST("api/phoneCallBack/clickCall", params);
  },
  /* 获取项目列表 */
  getProjectList: (params) => {
    return API.POST("api/appointment/findCategoryAndProject", params);
  },
  /* 创建预约 */
  appointmentBillCreate: (params) => {
    return API.POST("api/appointmentBill/create", params);
  },
  /* 修改预约 */
  appointmentBillUpdate: (params) => {
    return API.POST("api/appointmentBill/update", params);
  },
  /* 获取客户预约数量 */
  appointmentBill_getCustomerAppointmentNumber: (params) => {
    return API.POST("api/appointmentBill/getCustomerAppointmentNumber", params);
  },
  /* 获取客户所有预约 */
  appointmentBill_getCustomerAppointmentAll: (params) => {
    return API.POST("api/appointmentBill/getCustomerAppointmentAll", params);
  },
  /* 获取预约详情 */
  appointmentBillDetail: (params) => {
    return API.POST("api/appointmentBill/detail", params);
  },
  /* 取消预约 */
  appointmentBillCancel: (params) => {
    return API.POST("api/appointmentBill/cancel", params);
  },
  /* 获取客户预约记录 */
  getAppointmentRecords: (customerId) => {
    return API.GET(`api/lead/appointmentRecords?customerId=${customerId}`);
  },
  /* 手动标记成交 */
  markConverted: (params) => {
    return API.POST("api/lead/markConverted", params);
  },
  /* 新增线索 */
  createLead: (params) => {
    return API.POST("api/lead/create", params);
  },
  /* 更新线索意向门店 */
  updateLeadIntentionEntity: (params) => {
    return API.POST("api/lead/updateIntentionEntity", params);
  },
};
