<template>
  <div class="ChannelEmployeeCommissionScheme content_body">
    <div class="nav_header">
      <el-row>
        <el-col :span="20">
          <el-form :inline="true" size="small" @submit.native.prevent>
            <el-form-item label="市场提成方案">
              <el-input
                @clear="handleSearch"
                v-model="searchData.Name"
                placeholder="输入市场提成方案搜索"
                clearable
                @keyup.enter.native="handleSearch"
              ></el-input>
            </el-form-item>
            <el-form-item label="市场业绩取值方案" prop="PerformanceSchemeID">
              <el-select v-model="searchData.PerformanceSchemeID" placeholder="请选择市场业绩取值方案" filterable clearable size="small" @change="handleSearch">
                <el-option v-for="item in performanceSchemeAllList" :key="item.ID" :label="item.Name" :value="item.ID"> </el-option>
              </el-select>
            </el-form-item>

            <el-form-item label="有效性">
              <el-select v-model="searchData.Active" placeholder="选择有效性" clearable size="small" @change="handleSearch">
                <el-option label="有效" :value="true"></el-option>
                <el-option label="无效" :value="false"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" size="small" @click="handleSearch" v-prevent-click>搜索</el-button>
            </el-form-item>
          </el-form>
        </el-col>
        <el-col :span="4" class="text_right">
          <el-button type="primary" size="small" @click="showAddDialog" v-prevent-click>新增</el-button>
        </el-col>
      </el-row>
    </div>
    <el-table size="small" :data="tableData">
      <el-table-column prop="Name" label="名称"></el-table-column>
      <el-table-column prop="PerformanceSchemeName" label="业绩取值方案"></el-table-column>
      <el-table-column prop="Calculation" label="计算方式">
        <template slot-scope="scope">
          <span>{{ scope.row.Calculation == 10 ? "阶梯式计算" : "阶段式计算" }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="Active" label="有效性">
        <template slot-scope="scope">
          {{ scope.row.Active ? "有效" : "无效" }}
        </template>
      </el-table-column>
      <el-table-column label="操作" width="80px">
        <template slot-scope="scope">
          <el-button type="primary" size="small" @click="showEditDialog(scope.row)" v-prevent-click>编辑</el-button>
        </template>
      </el-table-column>
    </el-table>
    <div class="page pad_10">
      <div class="text_right" v-if="paginations.total > 0">
        <el-pagination
          background
          @current-change="handleCurrentChange"
          :current-page.sync="paginations.page"
          :page-size="paginations.page_size"
          :layout="paginations.layout"
          :total="paginations.total"
        ></el-pagination>
      </div>
    </div>
    <el-dialog :title="isAdd ? '新增市场业绩取值方案' : '编辑市场业绩取值方案'" :visible.sync="dialogVisible">
      <el-tabs v-model="activeName">
        <el-tab-pane label="基本信息" name="Info">
          <el-scrollbar class="el-scrollbar_height">
            <el-form size="small" ref="addRuleFormRef" :model="addRuleForm" :rules="addRules" label-width="150px">
              <el-form-item label="提成方案名称" prop="Name">
                <el-input v-model="addRuleForm.Name" placeholder="请输入提成方案名称"></el-input>
              </el-form-item>

              <el-form-item label="市场业绩取值方案" prop="PerformanceSchemeID">
                <el-select v-model="addRuleForm.PerformanceSchemeID" placeholder="请选择市场业绩取值方案" filterable clearable size="small">
                  <el-option v-for="item in performanceSchemeAllList" :key="item.ID" :label="item.Name" :value="item.ID"> </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="计算方式">
                <span slot="label">
                  计算方式
                  <el-popover placement="top-start" width="850px" trigger="hover">
                    <p>按阶梯式计算：设置后呈阶梯式增长，按总业绩计算提成</p>
                    <p>例：1-10000时6%，10000-15000时10%，员工业绩13000，提成为：13000*10%</p>
                    <p>按阶段式计算：设置后分阶段式计算，根据区间计算提成</p>
                    <p>例：1-10000时6%，10000-15000时10%，员工业绩13000，提成为：10000*6%+3000*10%</p>
                    <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                  </el-popover>
                </span>
                <el-radio v-model="addRuleForm.Calculation" label="10">阶梯式计算</el-radio>
                <el-radio v-model="addRuleForm.Calculation" label="20">阶段式计算</el-radio>
              </el-form-item>
              <el-form-item v-if="!isAdd" label="有效性">
                <el-radio-group v-model="addRuleForm.Active">
                  <el-radio :label="true">有效</el-radio>
                  <el-radio :label="false">无效</el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="提成方案" prop="Commission" :rules="[{ required: true, message: '请设置提成方案' }]">
                <span slot="label">
                  提成方案
                  <el-popover placement="top-start" width="850px" trigger="hover">
                    <p>提成方案的区间值，最小值包含该区间内，最大值不包含在该区间内。</p>
                    <p>比如：区间设置为1000～2000，计算提成时，如业绩值为1000，则符合该区间规则；如业绩值为2000时，则不符合该区间规则。</p>
                    <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                  </el-popover>
                </span>
                <el-button type="primary" size="small" @click="addSchemeClick()">新增提成方案 </el-button>
              </el-form-item>
            </el-form>
            <el-table size="small" :data="addRuleForm.Commission" style="width: calc(100% - 110px); margin-left: 110px">
              <el-table-column prop="BeginPerformance" label="开始业绩(大于等于)">
                <template slot-scope="scope">{{ scope.row.BeginPerformance | toFixed | NumFormat }}</template>
              </el-table-column>
              <el-table-column prop="EndPerformance" label="结束业绩(小于)">
                <template slot-scope="scope">{{ scope.row.EndPerformance | toFixed | NumFormat }}</template>
              </el-table-column>
              <el-table-column prop="Rate" label="比例提成">
                <template slot-scope="scope">{{ scope.row.Rate | toFixed }}%</template>
              </el-table-column>
              <el-table-column prop="Fixed" label="固定提成(元)">
                <template slot-scope="scope">￥{{ scope.row.Fixed | toFixed | NumFormat }}</template>
              </el-table-column>
              <el-table-column label="操作" width="145px">
                <template slot-scope="scope">
                  <el-button type="primary" size="small" @click="editCommissionClick(scope.row, scope.$index)"> 编辑</el-button>
                  <el-button type="danger" size="small" @click="removeCommissionClick(scope.$index)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-scrollbar>
        </el-tab-pane>
        <el-tab-pane label="适用职务" name="JobType">
          <el-row :gutter="20" class="pad_10_0">
            <el-col :span="10">
              <el-input placeholder="输入职务名称搜索" size="small" v-model="filterJobName" clearable></el-input>
            </el-col>
            <el-col :span="14">
              <el-button type="primary" @click="clickAddApplicableDuty" size="small">配置适用职务 </el-button>
            </el-col>
          </el-row>
          <el-table
            size="small"
            :data="JobTypeList.filter((data) => !filterJobName || data.JobTypeName.toLowerCase().includes(filterJobName.toLowerCase()))"
            max-height="450"
          >
            <el-table-column label="职务名称" prop="JobName" sortable>
              <template slot-scope="scope">
                <span>{{ scope.row.JobName }}</span>
              </template>
            </el-table-column>
            <el-table-column label="职务描述" prop="JobDescription">
              <template slot-scope="scope">
                <span>{{ scope.row.JobDescription }}</span>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="80">
              <template slot-scope="scope">
                <el-button type="danger" size="mini" @click="deleteSelectedJobType(scope.row)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
      </el-tabs>
      <span slot="footer" class="dialog-footer">
        <el-button size="small" @click="dialogVisible = false" v-prevent-click>取 消</el-button>
        <el-button size="small" type="primary" @click="handleSaveCommissionScheme" v-prevent-click>保 存</el-button>
      </span>
    </el-dialog>

    <!-- 设置条件 -->
    <el-dialog width="30%" :title="isAddCommission ? '新增提成方案' : '编辑提成方案'" :visible.sync="commissionDialogVisible" @close="commissionClose">
      <el-form size="small" ref="commissionFormData" :model="commissionFormData" label-width="110px">
        <el-form-item label="条件">
          <span slot="label"><span style="margin-right: 4px; color: #f67979">*</span><span>业绩范围</span></span>
          <el-col :span="8">
            <el-form-item
              class="custom-input"
              label-width="0"
              style="margin-bottom: 0px !important"
              prop="BeginPerformance"
              :rules="[{ required: true, message: '请输入开始业绩' }]"
            >
              <el-input v-model="commissionFormData.BeginPerformance" type="number" v-input-fixed placeholder="请输入开始业绩"> </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="2" class="dis_flex flex_x_center">至</el-col>
          <el-col :span="8">
            <el-form-item label-width="0" style="margin-bottom: 0px !important" prop="EndPerformance" :rules="[{ required: true, message: '请输入截止业绩' }]">
              <el-input v-model="commissionFormData.EndPerformance" type="number" v-input-fixed placeholder="请输入截止业绩"> </el-input>
            </el-form-item>
          </el-col>
        </el-form-item>
        <el-form-item label="比例提成" prop="Rate" :rules="[{ required: true, message: '请输入比例提成' }]">
          <el-input v-model="commissionFormData.Rate" @input="setCriteriaRate" v-input-fixed="2" type="number">
            <template slot="append">%</template>
          </el-input>
        </el-form-item>
        <el-form-item label="固定提成" prop="Fixed" :rules="[{ required: true, message: '请输入固定提成' }]">
          <el-input v-model="commissionFormData.Fixed" v-input-fixed="2">
            <template slot="append">元</template>
          </el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button size="small" @click="commissionDialogVisible = false" v-prevent-click>取 消</el-button>
        <el-button size="small" type="primary" @click="handleSaveCriteria" v-prevent-click>保 存</el-button>
      </span>
    </el-dialog>

    <!--添加适用职务弹出框-->
    <el-dialog :visible.sync="addApplicableDutyDialog" width="700px">
      <div slot="title">
        <span>配置适用职务</span>
      </div>
      <el-row>
        <el-col :span="10" class="pad_10_0">
          <el-input size="small" v-model="JobTypeName" clearable placeholder="输入职务名称搜索"></el-input>
        </el-col>
      </el-row>
      <el-table
        size="small"
        :data="applicableDutyList.filter((data) => !JobTypeName || data.JobName.toLowerCase().includes(JobTypeName.toLowerCase()))"
        max-height="480px"
        @selection-change="getSelectedJobType"
        @row-click="jobTypeRowClick"
        ref="multipleTable"
      >
        <el-table-column type="selection" width="55"></el-table-column>
        <el-table-column prop="JobName" label="职务名称" sortable column-key="JobName"></el-table-column>
        <el-table-column prop="JobDescription" label="职务描述"></el-table-column>
      </el-table>
      <span slot="footer" class="dialog-footer">
        <el-button @click="addApplicableDutyDialog = false" v-prevent-click size="small">取 消</el-button>
        <el-button type="primary" size="small" @click="submitFormApplicableDuty" v-prevent-click>确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import API from "@/api/KHS/ChannelEmployeeSalary/commissionScheme.js";
export default {
  name: "ChannelEmployeeCommissionScheme",
  props: {},
  /** 监听数据变化   */
  JobTypeName() {
    var that = this;
    that.$nextTick(() => {
      that.applicableDutyList.forEach((val) => {
        that.$refs.multipleTable.toggleRowSelection(val, false);
      });
    });
    if (that.selectedJobTypeList.length > 0) {
      that.selectedJobTypeList.forEach((item) => {
        that.applicableDutyList.forEach((val) => {
          if (item.ID == val.ID) {
            that.$nextTick(() => {
              that.$refs.multipleTable.toggleRowSelection(val);
            });
          }
        });
      });
    }
  },
  /**  引入的组件  */
  components: {},
  /**  Vue 实例的数据对象**/
  data() {
    return {
      addApplicableDutyDialog: false,
      searchData: {
        Name: "", //模糊搜索
        PerformanceSchemeID: null, //业绩取值方案编号
        Active: true, //有效性
      },
      tableData: [],
      paginations: {
        page: 1, // 当前位于哪页
        total: 0, // 总数
        page_size: 10, // 1页显示多少条
        layout: "total, prev, pager, next,jumper", // 翻页属性
      },
      dialogVisible: false,
      isAdd: false,
      activeName: "Info",
      addRuleForm: {
        ID: "",
        Active: true,
        Name: "",
        PerformanceSchemeID: "", //业绩取值方案编号
        Calculation: "10", //计算方式
        Commission: [],
        Range: [],
      },
      addRules: {
        Name: [{ required: true, message: "请输入提成方案名称", trigger: "blur" }],
        PerformanceSchemeID: [{ required: true, message: "请选择取值方案名称", trigger: "blur" }],
        Commission: [{ required: true, message: "请输入提成方案名称", trigger: ["blur", "change"] }],
        Active: [{ required: true, message: "请选择有效性", trigger: ["blur", "change"] }],
      },
      employeeTableData: [],
      isAddCommission: false,
      commissionDialogVisible: false,
      commissionFormData: {
        BeginPerformance: "",
        EndPerformance: "",
        Rate: "",
        Fixed: "",
      },
      commissionEditIndex: 0,
      JobTypeName: "",
      filterJobName: "",
      performanceSchemeAllList: [],
      applicableDutyList: [],
      JobTypeList: [],
    };
  },
  /**计算属性  */
  computed: {},
  /**  方法集合  */
  methods: {
    /**  搜索  */
    handleSearch() {
      let that = this;
      that.paginations.page = 1;
      that.channelEmployeeCommissionScheme_all();
    },
    /**   修改分页 */
    handleCurrentChange(page) {
      let that = this;
      that.paginations.page = page;
      that.channelEmployeeCommissionScheme_all();
    },
    /**  新增  */
    showAddDialog() {
      let that = this;
      that.isAdd = true;
      that.activeName = "Info";
      that.addRuleForm = {
        Name: "",
        PerformanceSchemeID: "", //业绩取值方案编号
        Calculation: "10", //计算方式
        Commission: [],
        Range: [],
      };
      that.dialogVisible = true;
    },
    /**  编辑  */
    async showEditDialog(row) {
      let that = this;
      that.activeName = "Info";
      that.addRuleForm = Object.assign(that.addRuleForm, row);
      that.isAdd = false;
      await that.channelEmployeeCommissionScheme_commission(row.ID);
      that.channelEmployeeCommissionScheme_range(row.ID);

      that.dialogVisible = true;
    },
    /**  删除业绩方案  */
    removeCommissionClick(index) {
      let that = this;
      that.addRuleForm.Commission.splice(index, 1);
    },

    /**  保存业绩方案  */
    handleSaveCommissionScheme() {
      let that = this;
      that.$refs.addRuleFormRef.validate((valid) => {
        if (valid) {
          if (that.isAdd) {
            that.channelEmployeeCommissionScheme_create();
          } else {
            that.channelEmployeeCommissionScheme_update();
          }
        }
      });
    },
    /**   新增提成方案 */
    addSchemeClick() {
      let that = this;
      that.isAddCommission = true;

      that.activeName = "Info";
      that.commissionFormData = {
        BeginPerformance: "",
        EndPerformance: "",
        Rate: "",
        Fixed: "",
      };
      that.commissionDialogVisible = true;
    },
    /** 编辑   */
    editCommissionClick(row, index) {
      let that = this;
      that.activeName = "Info";
      that.isAddCommission = false;
      that.commissionEditIndex = index;
      that.commissionFormData = Object.assign(that.commissionFormData, row);
      that.commissionDialogVisible = true;
    },
    /**    */
    commissionClose() {
      let that = this;
      that.$refs.commissionFormData.resetFields();
    },
    /**  比例提成输入  */
    setCriteriaRate(val) {
      let that = this;
      if (Number(val) > 100) {
        that.commissionFormData.Rate = 100;
      }
    },
    // 添加适用职务点击事件
    clickAddApplicableDuty() {
      var that = this;
      that.JobTypeName = "";
      that.addApplicableDutyDialog = true;
      that.$nextTick(() => {
        that.$refs.multipleTable.clearSelection();
        if (that.JobTypeList.length > 0) {
          // var defaultCheckedKeys = Array.from(that.JobTypeList).map((i) => i.JobTypeID);

          that.JobTypeList.forEach((item) => {
            that.applicableDutyList.forEach((val) => {
              if (item.ID == val.ID) {
                that.$nextTick(() => {
                  that.$refs.multipleTable.toggleRowSelection(val, true);
                });
              }
            });
          });
        }
      });
    },
    // 获取所选中的职务列表
    getSelectedJobType(list) {
      var that = this;
      that.selectedJobTypeList = list;
    },
    /**    */
    jobTypeRowClick(row) {
      let that = this;
      that.$refs.multipleTable.toggleRowSelection(row);
    },
    // 添加适用职务表单保存事件
    submitFormApplicableDuty() {
      var that = this;
      that.JobTypeList = Object.assign([], that.selectedJobTypeList);
      that.addApplicableDutyDialog = false;
    },
    // 删除所选职务
    deleteSelectedJobType(val) {
      var that = this;
      that.JobTypeList.splice(
        that.JobTypeList.findIndex((p) => p.JobTypeID == val.JobTypeID),
        1
      );
    },
    /**   保存条件 */
    handleSaveCriteria() {
      let that = this;
      that.$refs.commissionFormData.validate((valid) => {
        if (valid) {
          // that.addRuleForm.Commission = [];

          if (that.commissionFormData.BeginPerformance - that.commissionFormData.EndPerformance > 0) {
            that.$message.error({
              message: "截止业绩数额不能小于开始业绩数额",
              duration: 2000,
            });
            return;
          }
          // 判断是否有相同的提成方案
          let commission = JSON.parse(JSON.stringify(that.addRuleForm.Commission));
          if (!that.isAddCommission) {
            let index = that.commissionEditIndex;
            commission.splice(index, 1);
          }
          let num = commission.every((item) => {
            const num1 = Number(item.BeginPerformance);
            const num2 = Number(item.EndPerformance);
            const num3 = Number(that.commissionFormData.BeginPerformance);
            const num4 = Number(that.commissionFormData.EndPerformance);
            if (num3 >= num2) return true;
            if (num4 <= num1) return true;
            return false;
          });
          if (!num) {
            that.$message.error({
              message: "条件设置存在重复数额",
              duration: 2000,
            });
            return;
          }

          if (that.isAddCommission) {
            that.addRuleForm.Commission.push({ ...that.commissionFormData });
          } else {
            let index = that.commissionEditIndex;
            that.addRuleForm.Commission.splice(index, 1, { ...that.commissionFormData });
          }
          that.commissionDialogVisible = false;
        }
      });
    },

    /**••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••*/
    /**  提成查询  */
    async channelEmployeeCommissionScheme_all() {
      let that = this;
      try {
        let params = {
          PageNum: that.paginations.page,
          Name: that.searchData.Name, //模糊搜索
          PerformanceSchemeID: that.searchData.PerformanceSchemeID, //业绩取值方案编号
          Active: that.searchData.Active, //有效性
        };
        let res = await API.channelEmployeeCommissionScheme_all(params);
        if (res.StateCode == 200) {
          that.tableData = res.List;
          that.paginations.total = res.Total;
        } else {
          that.$message.error(res.Message);
        }
      } catch (error) {
        that.$message.error(error);
      }
    },
    /** 提成添加   */
    async channelEmployeeCommissionScheme_create() {
      let that = this;
      try {
        let params = {
          Name: that.addRuleForm.Name, //模糊搜索
          PerformanceSchemeID: that.addRuleForm.PerformanceSchemeID, //业绩取值方案编号
          Calculation: that.addRuleForm.Calculation, //计算方式
          Active: true,
          Commission: that.addRuleForm.Commission.map((i) => {
            return {
              BeginPerformance: i.BeginPerformance, //开始数额
              EndPerformance: i.EndPerformance, //结束数额
              Rate: i.Rate, //比例提成
              Fixed: i.Fixed, //固定提成
            };
          }),
          JobType: that.JobTypeList.map((i) => i.ID), //适用市场
        };
        let res = await API.channelEmployeeCommissionScheme_create(params);
        if (res.StateCode == 200) {
          that.$message.success("操作成功");
          that.dialogVisible = false;
          that.channelEmployeeCommissionScheme_all();
        } else {
          that.$message.error(res.Message);
        }
      } catch (error) {
        that.$message.error(error);
      }
    },
    /**  提成详情-条件  */
    async channelEmployeeCommissionScheme_commission(ID) {
      let that = this;
      try {
        let params = {
          ID: ID, //个人业绩提成编号
        };
        let res = await API.channelEmployeeCommissionScheme_commission(params);
        if (res.StateCode == 200) {
          that.addRuleForm.Commission = res.Data;
        } else {
          that.$message.error(res.Message);
        }
      } catch (error) {
        that.$message.error(error);
      }
    },
    /**  提成详情-市场范围  */
    async channelEmployeeCommissionScheme_range(ID) {
      let that = this;
      try {
        let params = {
          ID: ID, //个人业绩提成编号
        };
        let res = await API.channelEmployeeCommissionScheme_range(params);
        if (res.StateCode == 200) {
          that.JobTypeList = res.Data.map((i) => {
            return {
              ID: i.JobTypeID,
              JobDescription: i.JobDescription,
              JobName: i.JobTypeName,
            };
          });
        } else {
          that.$message.error(res.Message);
        }
      } catch (error) {
        that.$message.error(error);
      }
    },
    /**  提成修改  */
    async channelEmployeeCommissionScheme_update() {
      let that = this;
      try {
        let params = {
          ID: that.addRuleForm.ID,
          Active: that.addRuleForm.Active,
          Name: that.addRuleForm.Name, //模糊搜索
          PerformanceSchemeID: that.addRuleForm.PerformanceSchemeID, //业绩取值方案编号
          Calculation: that.addRuleForm.Calculation, //计算方式
          Commission: that.addRuleForm.Commission.map((i) => {
            return {
              BeginPerformance: i.BeginPerformance, //开始数额
              EndPerformance: i.EndPerformance, //结束数额
              Rate: i.Rate, //比例提成
              Fixed: i.Fixed, //固定提成
            };
          }),
          JobType: that.JobTypeList.map((i) => i.ID), //适用市场
        };
        let res = await API.channelEmployeeCommissionScheme_update(params);
        if (res.StateCode == 200) {
          that.$message.success("操作成功");
          that.dialogVisible = false;
          that.channelEmployeeCommissionScheme_all();
        } else {
          that.$message.error(res.Message);
        }
      } catch (error) {
        that.$message.error(error);
      }
    },
    /**    */
    async channelPerformanceScheme_valid() {
      let that = this;
      try {
        let params = {};
        let res = await API.channelPerformanceScheme_valid(params);
        if (res.StateCode == 200) {
          that.performanceSchemeAllList = res.Data;
        } else {
          that.$message.error(res.Message);
        }
      } catch (error) {
        that.$message.error(error);
      }
    },
    // 获取职务列表
    getJobTypeAll() {
      var that = this;
      that.loading = true;
      var params = {
        JobTypeName: that.JobTypeName,
      };
      API.getJobTypeAll(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.applicableDutyList = res.Data;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },
  },
  /** 创建实例之前执行函数   */
  beforeCreate() {},
  /**  实例创建完成之后  */
  created() {},
  /**  在挂载开始之前被调用  */
  beforeMount() {},
  /**  实例被挂载后调用  */
  mounted() {
    this.channelEmployeeCommissionScheme_all();
    setTimeout(() => {
      this.channelPerformanceScheme_valid();
      this.getJobTypeAll();
    }, 500);
  },
  /**  数据更新 之前 调用  */
  beforeUpdate() {},
  /** 数据更新 完成 调用   */
  updated() {},
  /**  实例销毁之前调用  */
  beforeDestroy() {},
  /**  实例销毁后调用  */
  destroyed() {},
};
</script>

<style lang="scss">
.ChannelEmployeeCommissionScheme {
  .el-scrollbar_height {
    height: 55vh;
    .el-scrollbar__wrap {
      overflow-x: hidden;
    }
  }
  .input_type {
    .el-input-group__append {
      padding: 0 10px;
    }
  }
  .el-input__inner {
    padding-right: 0;
  }
}
</style>
