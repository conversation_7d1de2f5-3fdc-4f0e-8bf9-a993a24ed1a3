/**
 * Created by wsf on 2022/01/11.
 * 门店业绩 门店项目销售业绩api
 */
import * as API  from '@/api/index'

export default {
  // 获取门店项目销售业绩方案列表
  getProjectEntityPerformanceScheme: params => {
      return API.POST('api/saleProjectEntityPerformanceScheme/list', params)
  },
  // 保存门店项目销售业绩方案
  createProjectEntityPerformanceScheme: params => {
      return API.POST('api/saleProjectEntityPerformanceScheme/create', params)
  },
  // 删除门店项目业绩方案
  deleteProjectEntityPerformanceScheme: params => {
      return API.POST('api/saleProjectEntityPerformanceScheme/delete', params)
  },
  // 获取分类项目业绩
  getProjectCategoryEntityPerformance: params => {
      return API.POST('api/saleProjectCategoryEntityPerformance/all', params)
  },
  // 保存分类项目业绩
  updateProjectCategoryEntityPerformance: params => {
      return API.POST('api/saleProjectCategoryEntityPerformance/update', params)
  },
  // 获取项目业绩
  getProjectEntityPerformance: params => {
      return API.POST('api/saleProjectEntityPerformance/all', params)
  },
  // 保存项目业绩
  updateProjectEntityPerformance: params => {
      return API.POST('api/saleProjectEntityPerformance/update', params)
  }
}