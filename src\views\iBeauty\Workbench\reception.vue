<template>
  <div class="reception content_body" v-loading="loading">
    <!-- 搜索 -->
    <div class="nav_header" style="background-color: #ffffff">
      <el-form :inline="true" size="small" v-model="search" @keyup.enter.native="getSearch">
        <el-form-item label="客户信息">
          <el-input size="small" @clear="getSearch" clearable placeholder="输入姓名、手机号或客户编号" v-model="search.Name"></el-input>
        </el-form-item>
        <el-form-item label="预约日期">
          <el-date-picker
            unlink-panels
            type="daterange"
            range-separator="至"
            value-format="yyyy-MM-dd"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            @change="getSearch"
            v-model="searchDate"
            clearable
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="预约状态">
          <el-select placeholder="请选择预约状态" clearable @change="getSearch" size="small" v-model="search.Status">
            <el-option label="未到店" :value="10"></el-option>
            <el-option label="已到店" :value="20"></el-option>
            <el-option label="已取消" :value="30"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="渠道信息">
          <el-input size="small" v-model="search.ChannelName" @clear="getSearch" clearable placeholder="输入渠道名称"></el-input>
        </el-form-item>
        <el-form-item label="会员等级">
          <el-select placeholder="请选择会员等级" filterable clearable v-model="search.CustomerLevelID" @change="getSearch" size="small">
            <el-option v-for="item in customerLevelList" :key="item.ID" :label="item.Name" :value="item.ID"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item>
          <el-button size="small" type="primary" @click="getSearch">搜索</el-button>
        </el-form-item>
        <el-form-item>
          <el-button size="small" type="primary" @click="addReception">到访登记</el-button>
        </el-form-item>

        <el-form-item>
          <el-dropdown @command="customer_Export" :loading="downloadLoading">
            <el-button type="primary"> 导出<i class="el-icon-arrow-down el-icon--right"></i> </el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item command="excelNoDisPlayPhone">导出</el-dropdown-item>
              <el-dropdown-item command="excelDisPlayPhone">导出(手机号)</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
          <!-- <el-button @click="customer_Export('excelNoDisPlayPhone')"  type="primary" v-prevent-click :loading="downloadLoading"> 导出 </el-button>
          <el-button @click="customer_Export('excelDisPlayPhone')"  type="primary" v-prevent-click :loading="downloadLoading"> 导出(手机号）</el-button> -->
        </el-form-item>

        <!-- <el-form-item>
          <el-button size="small" type="primary" @click="reception_excelDisPlayPhone" :loading="downloadLoading">导出</el-button>
        </el-form-item>

        <el-form-item>
          <el-button size="small" type="primary" @click="reception_excelNoDisPlayPhone" :loading="downloadNoDisPlayLoading">隐藏手机号导出</el-button>
        </el-form-item> -->
      </el-form>
    </div>

    <!-- 表格 -->
    <el-table size="small" :data="tableData" tooltip-effect="light" @row-click="rowClick" highlight-current-row>
      <el-table-column fixed label="操作" width="170px">
        <template slot-scope="scope">
          <el-button type="primary" size="small" v-if="scope.row.DiagnosisRecordID !== null && scope.row.Status == 20" @click.stop="openArrange(3, scope.row)"
            >重新指派</el-button
          >
          <el-button type="primary" size="small" v-if="scope.row.Status == 10" @click.stop="confirmReception(scope.row)">确认到访</el-button>
          <el-button
            type="primary"
            size="small"
            v-if="scope.row.Status == 10 || (scope.row.DiagnosisRecordID == null && scope.row.Status == 20)"
            @click.stop="openArrange(2, scope.row)"
            >指派</el-button
          >
        </template>
      </el-table-column>
      <el-table-column label="预约状态" prop="Status">
        <template slot-scope="{ row }">
          <div v-if="row.Status == 10" class="color_orange">未到店</div>
          <div v-if="row.Status == 20" class="color_green">已到店</div>
          <div v-if="row.Status == 30" class="color_999">已取消</div>
        </template>
      </el-table-column>

      <el-table-column label="预约时间" prop="AppointmentDate" width="140px">
        <template slot-scope="scope">
          {{ scope.row.AppointmentDate | dateFormat('YYYY-MM-DD HH:mm') }}
        </template>
      </el-table-column>
      <el-table-column label="预约备注" show-overflow-tooltip prop="Remark"></el-table-column>
      <!-- <el-table-column show-overflow-tooltip label="预约项目" prop="ProjectName"> </el-table-column> -->
      <el-table-column label="导诊备注" show-overflow-tooltip prop="Guidance"></el-table-column>
      <!-- <el-table-column label="接诊人员" prop="EmployeeName"></el-table-column> -->
      <!-- <el-table-column label="预约院部" prop="AppointmentEntityName"></el-table-column> -->
      <!-- <el-table-column label="客户名称" width="80px" prop="CustomerName"></el-table-column>
      <el-table-column label="客户手机号" width="100px" prop="PhoneNumber">
        <template slot-scope="scope">
          {{ scope.row.PhoneNumber | hidephone }}
        </template>
      </el-table-column> -->

      <el-table-column label="客户" prop="CustomerName" width="150px">
        <template slot-scope="scope">
          <div>
            {{ scope.row.CustomerName }} <span v-if="scope.row.Code">({{ scope.row.Code }})</span>
          </div>
          <div>{{ scope.row.PhoneNumber | hidephone }}</div>
        </template>
      </el-table-column>

      <el-table-column label="渠道" prop="ChannelName" width="100px"></el-table-column>
      <el-table-column label="会员等级" prop="CustomerLevelName"></el-table-column>

      <el-table-column label="服务人员">
        <template slot-scope="scope">
          <el-tooltip placement="top" effect="light">
            <div slot="content">
              <el-descriptions size="mini" :column="1" border :colon="false" labelClassName="custom-customer-descLabel">
                <el-descriptions-item v-for="(item, index) in scope.row.ServicerEmployee" :key="index" :label="item.Name + '：'">
                  {{ getServicerEmpNames(item.ServicerEmpList) }}
                </el-descriptions-item>
              </el-descriptions>
            </div>
            <div style="text-overflow: ellipsis; white-space: nowrap; overflow: hidden">
              {{ getFirstServiceEmp(scope.row.ServicerEmployee) }}
            </div>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column label="来源" prop="CustomerSourceName"></el-table-column>
      <el-table-column label="会员性别" prop="Gender">
        <template slot-scope="scope">{{ formatGender(scope.row.Gender) }}</template>
      </el-table-column>
      <!-- <el-table-column label="生日" prop="Birthday" width="100"></el-table-column> -->
      <el-table-column label="所属组织" prop="EntityName" width="120px"></el-table-column>
      <el-table-column label="注册日期" prop="CustomerCreatedOn" width="140px">
        <template slot-scope="scope">
          {{ scope.row.CustomerCreatedOn | dateFormat('YYYY-MM-DD HH:mm') }}
        </template>
      </el-table-column>
    </el-table>

    <div class="pad_15 text_right">
      <el-pagination
        background
        @current-change="handleCurrentChange"
        :current-page.sync="paginations.page"
        :page-size="paginations.page_size"
        :layout="paginations.layout"
        :total="paginations.total"
      ></el-pagination>
    </div>

    <work-customer-detail
      v-if="customerDetailVisible"
      :visible.sync="customerDetailVisible"
      ref="customerDetail"
      :customerID="CustomerID"
      :isCustomerPhoneNumberView="isCustomerPhoneNumberView"
      :isCustomerPhoneNumberModify="isCustomerPhoneNumberModify"
      :isCustomerBasicInformationModify="isCustomerBasicInformationModify"
      :isCustomerServicerModify="isCustomerServicerModify"
      :isCustomerBasicFileModify="isCustomerBasicFileModify"
    ></work-customer-detail>
    <!-- 到访登记 -->
    <el-dialog title="到访登记" :visible.sync="dialogVisible" width="980px" :close-on-click-modal="false">
      <!-- <div class="tip marbm_10" style="margin-top: 0">预约信息</div> -->
      <el-autocomplete
        popper-class="customer-autocomplete"
        prefix-icon="el-icon-user-solid"
        v-model="customerName"
        style="width: 430px"
        size="small"
        placeholder="请输入客户姓名、手机号、编号查找，无匹配按回车新增"
        :fetch-suggestions="saleCustomerData"
        @select="handleCustomerSelect"
        :disabled="customerID != ''"
        :trigger-on-focus="false"
        :hide-loading="false"
        :highlight-first-item="true"
        :select-when-unmatched="true"
        :popper-append-to-body="false"
      >
        <template slot="append">
          <el-button icon="el-icon-delete" @click="removeCustomer"></el-button>
        </template>
        <template slot-scope="{ item }">
          <div class="name">
            {{ item.Name }}
            <el-tag size="mini" v-if="item.CustomerLevelName">{{ item.CustomerLevelName }}</el-tag>
          </div>
          <div class="info">手机号：{{ item.PhoneNumber | hidephone }}</div>
          <div class="info" v-if="item.Code">客户编号：{{ item.Code }}</div>
          <div class="info" v-if="item.EntityName">所属组织：{{ item.EntityName }}</div>
          <div class="info" v-if="item.ChannelName">渠道信息：{{ item.ChannelName }}</div>
        </template>
      </el-autocomplete>

      <div>
        <el-row type="flex" align="" v-if="itemInfo.itemName" class="martp_5" style="background-color: #f7f8fa; padding: 8px">
          <el-col :span="2">
            <el-avatar :size="50" :src="circleUrl"></el-avatar>
          </el-col>
          <el-col :span="22">
            <el-row type="flex" justify="space-between">
              <el-col :span="12">
                <strong class="marrt_5 font_18">{{ itemInfo.itemName }}</strong>
                <el-image v-if="itemInfo.Gender == 2" style="width: 8px; height: 12px" :src="require('@/assets//img//gender-female.png')"></el-image>
                <el-image v-if="itemInfo.Gender == 1" style="width: 8px; height: 12px" :src="require('@/assets/img/gender-male.png')"></el-image>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="8" class="color_999 over-flow martp_5" v-for="item in itemInfo.itemServicerList" :key="item.ServicerID">
                {{ item.ServicerName }}：
                <el-tooltip class="item" effect="light" :content="item.nameShow" placement="top">
                  <span class="color_333">{{ item.nameShow }}</span>
                </el-tooltip>
              </el-col>
            </el-row>
          </el-col>
        </el-row>
        <el-tabs v-model="registerName">
          <el-tab-pane label="确认到访" name="1">
            <div class="message el-message--info">
              <i class="el-message__icon el-icon-info"></i>
              <p class="el-message__content">不需要指派服务人员接诊，只做到店登记。</p>
            </div>
          </el-tab-pane>
          <el-tab-pane label="确认到访并指派服务人员" name="2">
            <div>
              <el-radio-group v-model="confirmVistInfo.Status">
                <el-radio label="10">不替换不追加</el-radio>
                <el-radio label="20">替换服务人员</el-radio>
                <el-radio label="30">追加服务人员</el-radio>
              </el-radio-group>
            </div>

            <div class="martp_10 border_top border_right">
              <el-scrollbar class="custom-scrollbar-class">
                <div class="dis_flex">
                  <div class="border_bottom border_left" style="width: 210px" v-for="item in servicerStaffInfo" :key="item.ServicerID">
                    <div class="dis_flex flex_dir_column border_bottom pad_5">
                      <strong class="color_333 font_12">{{ item.ServicerName }}</strong>
                      <el-input placeholder="请输入员工编号、姓名" prefix-icon="el-icon-search" size="mini" class="martp_5" v-model="item.searchKey" clearable>
                      </el-input>
                    </div>

                    <el-scrollbar style="height: 270px" class="serviceTypeClass">
                      <div style="width: 210px; cursor: pointer">
                        <div
                          @click="confirmVistClick(item.ServicerID, itemx.EmployeeID)"
                          v-for="itemx in item.Detail.filter(
                            (val) =>
                              !item.searchKey ||
                              val.EmployeeID.toLowerCase().includes(item.searchKey.toLowerCase()) ||
                              val.EmployeeName.toLowerCase().includes(item.searchKey.toLowerCase())
                          )"
                          :key="'itemx' + itemx.EmployeeID"
                          class="empItem border pad_5 font_13 position_relative border_box"
                          :class="setSelectClass(item, itemx) ? 'selectServiceEmp' : ''"
                        >
                          <!-- <div class="color_main">所属</div> -->
                          <div>
                            <strong class="color_333">{{ itemx.EmployeeName }}</strong>
                            <span class="font_13 color_666 marlt_10">[{{ itemx.JobName }}]</span>
                          </div>

                          <div class="martp_5 font_13">当天接诊{{ itemx.todayDiagnosisCount }}人</div>
                          <div class="martp_5 font_13">昨天接诊{{ itemx.yesterdayDiagnosisCount }}人</div>
                          <el-image
                            v-if="setSelectClass(item, itemx)"
                            :src="require('@/assets/img/select-servicer.png')"
                            style="height: 20px; width: 20px; bottom: 0; right: 0; position: absolute"
                          ></el-image>
                        </div>
                      </div>
                    </el-scrollbar>
                  </div>
                </div>
              </el-scrollbar>
            </div>
            <div class="martp_15">
              <el-row type="flex">
                <el-col :span="2">导诊备注</el-col>
                <el-col :span="22">
                  <el-input placeholder="请输入导诊备注" type="textarea" :rows="3" v-model="confirmVistInfo.Guidance"></el-input>
                </el-col>
              </el-row>
            </div>
          </el-tab-pane>
          <el-tab-pane label="确认到访并指派其他人员" name="3">
            <el-form label-width="70px" size="small">
              <el-form-item label="指派人员">
                <el-select
                  popper-class="custom-el-select"
                  v-model="confirmVistInfo.Diagnosis"
                  filterable
                  remote
                  :remote-method="searchEmpRemote"
                  placeholder="请选择其他人员"
                  @change="selectOtherEmp"
                  size="small"
                >
                  <el-option v-for="item in otherStaffInfo" :key="item.ID" :label="item.Name" :value="item.ID">
                    <div class="dis_flex flex_dir_column pad_5_0">
                      <div style="line-height: 25px">
                        <span style="float: left">{{ item.Name }}</span>
                        <span style="float: right; color: #8492a6; font-size: 13px">{{ item.ID }}</span>
                      </div>
                      <div style="line-height: 20px; color: #8492a6">
                        <span style="float: left">{{ item.JobName }}</span>
                        <span style="float: right; font-size: 13px" class="marlt_5">{{ item.JobName }}</span>
                      </div>
                    </div>
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="导诊备注">
                <el-input placeholder="请输入导诊备注" type="textarea" :rows="3" v-model="confirmVistInfo.Guidance"></el-input>
              </el-form-item>
            </el-form>
          </el-tab-pane>
        </el-tabs>
      </div>

      <span slot="footer" class="dialog-footer">
        <el-button @click="cancelSave" size="small" v-prevent-click>取 消</el-button>
        <el-button type="primary" @click="save" v-prevent-click size="small">保 存</el-button>
      </span>
    </el-dialog>

    <!-- 指派 -->
    <el-dialog title="指派接诊人员" :visible.sync="confirmStaffVisible" width="800px" :close-on-click-modal="false">
      <div>
        <el-row type="flex" align="" v-if="itemInfo.itemName" style="background-color: #f7f8fa; padding: 8px">
          <el-col :span="2">
            <el-avatar :size="50" :src="circleUrl"></el-avatar>
          </el-col>
          <el-col :span="22">
            <el-row type="flex" justify="space-between">
              <el-col :span="12">
                <strong class="marrt_5 font_18">{{ itemInfo.itemName }}</strong>
                <el-image v-if="itemInfo.Gender == 2" style="width: 8px; height: 12px" :src="require('@/assets//img//gender-female.png')"></el-image>
                <el-image v-if="itemInfo.Gender == 1" style="width: 8px; height: 12px" :src="require('@/assets/img/gender-male.png')"></el-image>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="8" class="color_999 over-flow martp_5" v-for="item in itemInfo.itemServicerList" :key="item.ServicerID">
                {{ item.ServicerName }}：
                <el-tooltip class="item" effect="light" :content="item.nameShow" placement="top">
                  <span class="color_333">{{ item.nameShow }}</span>
                </el-tooltip>
              </el-col>
            </el-row>
          </el-col>
        </el-row>
        <el-tabs v-model="arrangeInfo.IsServicer">
          <el-tab-pane label="服务人员" name="1">
            <div>
              <el-radio-group v-model="arrangeInfo.Status">
                <el-radio :label="10">不替换不追加</el-radio>
                <el-radio :label="20">替换服务人员</el-radio>
                <el-radio :label="30">追加服务人员</el-radio>
              </el-radio-group>
            </div>

            <div class="martp_10 border_top border_right">
              <el-scrollbar class="custom-scrollbar-class">
                <div class="dis_flex">
                  <div class="border_bottom border_left" style="width: 210px" v-for="item in servicerStaffInfo" :key="item.ServicerID">
                    <div class="dis_flex flex_dir_column border_bottom pad_5">
                      <strong class="color_333 font_12">{{ item.ServicerName }}</strong>
                      <el-input placeholder="请输入员工编号、姓名" prefix-icon="el-icon-search" size="mini" class="martp_5" v-model="item.searchKey" clearable>
                      </el-input>
                    </div>

                    <el-scrollbar style="height: 300px" class="serviceTypeClass">
                      <div style="width: 210px; cursor: pointer; font-size: 16px">
                        <div
                          @click="servicerClick(item.ServicerID, itemx.EmployeeID)"
                          v-for="itemx in item.Detail.filter(
                            (val) =>
                              !item.searchKey ||
                              val.EmployeeID.toLowerCase().includes(item.searchKey.toLowerCase()) ||
                              val.EmployeeName.toLowerCase().includes(item.searchKey.toLowerCase())
                          )"
                          :key="'itemx' + itemx.EmployeeID"
                          class="empItem border pad_5 font_13 position_relative border_box"
                          :class="setSelectServiceClass(item, itemx) ? 'selectServiceEmp' : ''"
                        >
                          <!-- <div class="color_main">所属</div> -->
                          <div class="overflow_hidden">
                            <strong class="color_333">{{ itemx.EmployeeName }}</strong>
                            <span class="color_666 font_13 marlt_10">[{{ itemx.JobName }}]</span>
                          </div>

                          <div class="martp_5 font_13">当天接诊{{ itemx.todayDiagnosisCount }}人</div>
                          <div class="martp_5 font_13">昨天接诊{{ itemx.yesterdayDiagnosisCount }}人</div>
                          <el-image
                            v-if="setSelectServiceClass(item, itemx)"
                            :src="require('@/assets/img/select-servicer.png')"
                            style="height: 20px; width: 20px; bottom: 0; right: 0; position: absolute"
                          ></el-image>
                        </div>
                      </div>
                    </el-scrollbar>
                  </div>
                </div>
              </el-scrollbar>
            </div>
            <el-row class="martp_10">
              <el-col :span="2"> 导诊备注 </el-col>
              <el-col :span="22">
                <el-input type="textarea" :rows="3" placeholder="请输入导诊备注" v-model="arrangeInfo.Guidance"></el-input>
              </el-col>
            </el-row>
          </el-tab-pane>
          <el-tab-pane label="其他人员" name="2">
            <el-form v-model="arrangeInfo" label-width="70px" size="small">
              <el-form-item label="指派人员">
                <el-select
                  size="small"
                  popper-class="custom-el-select"
                  v-model="arrangeInfo.Diagnosis"
                  filterable
                  remote
                  :remote-method="searchEmpRemote"
                  placeholder="请选择其他人员"
                  @change="selectOtherEmp"
                >
                  <el-option v-for="item in otherStaffInfo" :key="item.ID" :label="item.Name" :value="item.ID">
                    <div class="dis_flex flex_dir_column pad_5_0">
                      <div style="line-height: 25px">
                        <span style="float: left">{{ item.Name }}</span>
                        <span style="float: right; color: #8492a6; font-size: 13px">{{ item.ID }}</span>
                      </div>
                      <div style="line-height: 20px; color: #8492a6">
                        <span style="float: left">{{ item.JobName }}</span>
                        <span style="float: right; font-size: 13px" class="marlt_5">{{ item.JobName }}</span>
                      </div>
                    </div>
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="导诊备注">
                <el-input type="textarea" :rows="3" placeholder="请输入导诊备注" v-model="arrangeInfo.Guidance"></el-input>
              </el-form-item>
            </el-form>
          </el-tab-pane>
        </el-tabs>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="confirmStaffVisible = false" v-prevent-click size="small">取 消</el-button>
        <el-button type="primary" @click="confirmArrange" v-prevent-click size="small">确认指派</el-button>
      </span>
    </el-dialog>

    <!-- 确认 -->
    <el-dialog title="到访登记" custom-class="" :visible.sync="confirmVisible" width="30%" :close-on-click-modal="false">
      <div>确认当前客户已经到院了吗？确认后状态不可修改</div>

      <span slot="footer" class="dialog-footer">
        <el-button @click="confirmVisible = false" size="small">取 消</el-button>
        <el-button type="primary" @click="confirmCime" v-prevent-click size="small">确认到访</el-button>
        <el-button type="primary" @click="confirmCimeArrange(1)" v-prevent-click size="small">确认到访并指派</el-button>
      </span>
    </el-dialog>
    <!--新增 客户-->
    <add-customer title="新增客户" :visible.sync="isAddCustom" @addCustomerSuccess="addCustomerSuccess"> </add-customer>
    <div v-show="false" style="height: 10px; width: 100%">
      <medicalEditor ref="hiddenMedicalEditor"></medicalEditor>
    </div>
  </div>
</template>

<script>
import medicalEditor from '@/components/medicalEditor/medicalEditor.vue';
import CustomerAPI from '@/api/iBeauty/Order/saleGoods';
// import workCustomerDetail from "@/views/iBeauty/Workbench/Component/workbenchCustomerDetail";
import APIReception from '@/api/iBeauty/Workbench/reception.js';
import cusAPI from '@/api/CRM/Customer/customer';
import validate from '@/components/js/validate.js';

import addCustomer from '@/views/CRM/Customer/Components/CustomerDetail/addCustomer.vue';
export default {
  name: 'Reception',

  props: {},
  /**  引入的组件  */
  components: {
    workCustomerDetail: () => import('@/views/iBeauty/Workbench/Component/workbenchCustomerDetail'),
    addCustomer,
    medicalEditor,
  },
  /**  Vue 实例的数据对象**/
  data() {
    return {
      customerLevelList: [],
      downloadLoading: false,
      downloadNoDisPlayLoading: false,
      isCustomerBasicFileModify: false,
      isCustomerServicerModify: false,
      isCustomerBasicInformationModify: false,
      customerDetailVisible: false,
      isCustomerPhoneNumberView: false,
      isCustomerPhoneNumberModify: false,
      loading: false, // 加载状态
      dialogVisible: false, //新增类型
      confirmVisible: false, //确认到访
      modalLoading: false,
      confirmStaffVisible: false, // 确认指派人员
      isAddCustom: false,
      circleUrl: 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png', //默认头像
      searchDate: [this.$formatDate(new Date(), 'YYYY-MM-DD'), this.$formatDate(new Date(), 'YYYY-MM-DD')], // 预约到院时间
      paginations: {
        page: 1, // 当前位于哪页
        total: 0, // 总数
        page_size: 10, // 1页显示多少条
        layout: 'total, prev, pager, next, jumper', // 翻页属性
      },
      search: {
        //搜索条件
        PageNum: 1,
        Name: '',
        Status: 10,
        StartDate: '',
        EndDate: '',
        ChannelName: '', //渠道来源
        CustomerLevelID: '', //会员等级
      },
      customerID: '',
      customerName: '',
      tableData: [], // 列表数据
      name: '',
      radioValue: '1',
      AppointmentBillID: '', //确认到访BillID
      // 到访登记的保存
      confirmVistInfo: {
        CustomerID: '',
        Guidance: '',
        DiagnosisBy: '',
        IsServicer: '1',
        IsAssign: false,
        Status: '10',
        ServicerID: '',
        Diagnosis: '',
      },

      //指派
      arrangeInfo: {
        AppointmentBillID: '',
        DiagnosisRecordID: '',
        IsServicer: '1',
        CustomerID: '',
        Guidance: '',
        DiagnosisBy: '',
        Status: 10,
        ServicerID: '',
        Diagnosis: '',
      },
      servicerStaffInfo: [],
      otherStaffInfo: [],
      isShowChannel: false,
      customerServicer: [], //新增时使用的服务人员
      regionDataSelArr: [], //城市已选择
      customer: {
        Name: '',
        PhoneNumber: '',
        Gender: '2',
        CustomerSourceID: null,
        CustomerLevelID: '',
        Introducer: '',
        Code: '',
        BirthdayType: 10,
        Birthday: '',
        ProvinceCode: '',
        CityCode: '',
        AreaCode: '',
        Job: '',
        Address: '',
        IdentityCard: '',
        Remark: '',
        ChannelID: null,
        IsMember: false,
        IsLockMemberLevel: false,
      },
      customerRoules: {
        Name: [{ required: true, message: '请输入客户名称', trigger: 'blur' }],
        PhoneNumber: [
          {
            required: true,
            validator: validate.validPhoneNumber,
            trigger: 'blur',
          },
        ],
        Gender: [{ required: true, message: '请选择客户性别', trigger: 'change' }],
        CustomerSourceID: [{ required: true, message: '请选择客户来源', trigger: 'change' }],
        CustomerLevelID: [{ required: true, message: '请选择客户等级', trigger: 'change' }],
        Code: [{ required: true, message: '请输入客户编号', trigger: 'blur' }],
      },

      customerDetail: {}, // 到访等级客户信息
      itemRowCustomerDetail: {}, // 选择的到访客户信息
      itemRow: {}, // 选中的接待客户信息
      registerName: '1',
      CustomerID: null, //选中的客户ID 显示基本档案
      TagEntry: '',

      itemInfo: {
        itemName: '',
        Gender: null,
        itemServicerList: [],
      },
    };
  },
  beforeRouteEnter(to, from, next) {
    next((vm) => {
      vm.isShowChannel = vm.$permission.permission(to.meta.Permission, 'iBeauty-Customer-Customer-Channel');
      vm.isCustomerPhoneNumberView = vm.$permission.permission(to.meta.Permission, 'iBeauty-Workbench-Reception-CustomerPhoneNumberView');
      vm.isCustomerPhoneNumberModify = vm.$permission.permission(to.meta.Permission, 'iBeauty-Workbench-Reception-CustomerPhoneNumberModify');

      vm.isCustomerBasicInformationModify = vm.$permission.permission(to.meta.Permission, 'iBeauty-Workbench-Reception-CustomerBasicInformationModify');
      vm.isCustomerServicerModify = vm.$permission.permission(to.meta.Permission, 'iBeauty-Workbench-Reception-CustomerServicerModify');
      vm.isCustomerBasicFileModify = vm.$permission.permission(to.meta.Permission, 'iBeauty-Workbench-Reception-CustomerBasicFileModify');
    });
  },
  /**计算属性  */
  computed: {},
  /**  方法集合  */
  methods: {
    /**    */
    customer_Export(type) {
      let that = this;
      if (type == 'excelNoDisPlayPhone') {
        that.reception_excelNoDisPlayPhone();
      }

      if (type == 'excelDisPlayPhone') {
        that.reception_excelDisPlayPhone();
      }
    },
    /* 性别状态显示转换 */
    formatGender: function (row) {
      switch (row.Gender) {
        case '1':
          return '男';
        case '2':
          return '女';
        case '0':
          return '未知';
      }
    },
    // 显示到访登记弹框
    addReception() {
      var that = this;
      that.customerID = '';
      that.clearVistReception();
      that.dialogVisible = true;
    },

    // 操作+确认到访
    confirmReception(row) {
      this.itemRow = row;
      this.confirmVisible = true;
    },

    // 显示指派接待人员弹框
    openArrange(index, row) {
      // index == 2 操作 指派 index == 3 重新指派
      this.itemRow = row;
      this.TagEntry = index;
      this.cancelArrange();
      this.itemInfo.itemName = row.CustomerName;
      this.itemInfo.Gender = row.Gender;
      this.getServicerStaff(row.CustomerID); // 获取该客户的服务人员
      this.getCustomerDetail(row.CustomerID); // 获取该客户的信息-展示
      this.arrangeInfo.ServicerID = '';
      this.confirmVistInfo.ServicerID = '';
      this.confirmStaffVisible = true;
    },

    // 确认到访
    async confirmCime() {
      var that = this;
      var params = {
        AppointmentBillID: that.itemRow.AppointmentBillID,
      };
      let res = await APIReception.confirmCime(params);
      if (res.StateCode == 200) {
        this.confirmVisible = false;
        that.$message.success('确认到访成功');
        that.getSearch();
      } else {
        that.$message.error({
          message: res.Message,
          duration: 2000,
        });
      }
    },

    // 确认到访并指派 显示指派接待人员弹框
    confirmCimeArrange() {
      this.TagEntry = '1';
      this.confirmVisible = false;
      this.cancelArrange();
      this.itemInfo.itemName = this.itemRow.CustomerName;
      this.itemInfo.Gender = this.itemRow.Gender;
      this.getServicerStaff(this.itemRow.CustomerID);
      this.confirmStaffVisible = true;
    },

    // 到访登记上的确认到访
    save() {
      var that = this;
      if (!that.customerID) {
        that.$message.error('请选择顾客');
        return;
      }
      var params = {
        CustomerID: that.customerID,
        Guidance: that.confirmVistInfo.Guidance,
        DiagnosisBy: that.registerName == '2' ? that.confirmVistInfo.DiagnosisBy : that.confirmVistInfo.Diagnosis,
        IsServicer: that.registerName == '2' ? true : false,
        IsAssign: that.registerName == '1' ? false : true,
        Status: that.registerName !== '2' ? '10' : that.confirmVistInfo.Status,
        ServicerID: that.confirmVistInfo.ServicerID,
      };
      if (that.registerName == '2' && !params.DiagnosisBy) {
        that.$message.error('请指派服务人员');
        return;
      }
      if (that.registerName == '3' && !params.DiagnosisBy) {
        that.$message.error('请指派其他人员');
        return;
      }
      APIReception.confirmVist(params).then((res) => {
        if (res.StateCode == 200) {
          that.dialogVisible = false;
          that.paginations.page = 1;
          that.removeCustomer();
          that.getSearch();
          that.$message.success('保存成功');
        } else {
          that.$message.error({
            message: res.Message,
            duration: 2000,
          });
        }
      });
    },

    /*
     选择会员 新增会员
    */
    saleCustomerData: function (queryString, cb) {
      var that = this;
      that.loading = true;
      var params = {
        Name: queryString ? queryString : '',
      };
      CustomerAPI.getSaleCustomer(params)
        .then((res) => {
          if (res.StateCode == 200) {
            cb(res.Data);
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },

    handleCustomerSelect(item) {
      if (item.ID != undefined) {
        var filter_hidephone = this.$options.filters['hidephone'];
        this.itemInfo.itemName = item.Name;
        this.itemInfo.Gender = item.Gender;
        this.getCustomerDetail(item.ID);
        this.getServicerStaff(item.ID);
        this.customerID = item.ID;
        if (item.PhoneNumber) {
          this.customerName = item.Name + '【' + filter_hidephone(item.PhoneNumber) + '】';
        } else {
          this.customerName = item.Name;
        }
      } else {
        if (/^1[3456789]\d{9}$/.test(this.customerName)) {
          this.addCustomerPhoneNumber = this.customerName;
        }
        this.addNewCustomer();
      }
    },

    removeCustomer() {
      this.customerID = '';
      this.customerName = '';
      this.customerDetail = {};
      this.servicerStaffInfo = [];
      this.otherStaffInfo = [];
      this.registerName = '1';
      this.itemInfo.itemName = '';
    },

    /* 新增会员 */
    addNewCustomer: function () {
      var that = this;
      that.isAddCustom = true;
    },
    /**  新增客户成功 */
    addCustomerSuccess(info) {
      let that = this;
      that.customerID = info.ID;
      that.getCustomerDetail(info.ID);
      that.getServicerStaff(info.ID);
      // that.customerName = info.Name + "【" + info.PhoneNumber + "】";
      var filter_hidephone = this.$options.filters['hidephone'];
      if (info.PhoneNumber) {
        that.customerName = info.Name + '【' + filter_hidephone(info.PhoneNumber) + '】';
      } else {
        that.customerName = info.Name;
      }
    },
    servicerClick(ServicerID, DiagnosisBy) {
      if (this.arrangeInfo.IsServicer == '1') {
        this.arrangeInfo.ServicerID = ServicerID;
        this.arrangeInfo.DiagnosisBy = DiagnosisBy;
        this.arrangeInfo.Diagnosis = '';
      }
    },

    confirmVistClick(ServicerID, EmployeeID) {
      this.confirmVistInfo.ServicerID = ServicerID;
      this.confirmVistInfo.DiagnosisBy = EmployeeID;
      this.confirmVistInfo.Diagnosis = '';
    },

    // 选择其他服务人员
    selectOtherEmp() {
      this.arrangeInfo.IsServicer = '2';
      this.arrangeInfo.ServicerID = '';
      this.arrangeInfo.DiagnosisBy = '';
      this.confirmVistInfo.IsServicer = '2';
      this.confirmVistInfo.ServicerID = '';
      this.confirmVistInfo.DiagnosisBy = '';
    },

    confirmArrange() {
      var that = this;
      var params = {};
      'IsServicer,CustomerID,Guidance,DiagnosisBy,Status,ServicerID'.split(',').forEach((key) => {
        if (key == 'IsServicer') {
          params[key] = that.arrangeInfo.IsServicer == '1' ? true : false;
        } else if (key == 'CustomerID') {
          params[key] = that.itemRow.CustomerID;
        } else if (key == 'DiagnosisBy') {
          params[key] = that.arrangeInfo.IsServicer == '1' ? that.arrangeInfo.DiagnosisBy : that.arrangeInfo.Diagnosis;
        } else {
          params[key] = that.arrangeInfo[key];
        }
      });

      if (that.TagEntry == '3') {
        params.DiagnosisRecordID = that.itemRow.DiagnosisRecordID;
      } else {
        params.AppointmentBillID = that.itemRow.AppointmentBillID;
      }
      if (that.TagEntry == '1') {
        that.confirmCimeArrangeApi(params);
      } else if (that.TagEntry == '2') {
        that.comfirmReceptionApi(params);
      } else if (that.TagEntry == '3') {
        that.afreshReceptionApi(params);
      }
    },
    // 确认到访并指派的保存
    confirmCimeArrangeApi(params) {
      var that = this;
      if (!params.DiagnosisBy) {
        that.$message.error('请指派接诊人员');
        return;
      }
      APIReception.confirmAssign(params).then((res) => {
        if (res.StateCode == 200) {
          that.confirmStaffVisible = false;
          that.$message.success('确认指派成功');
          that.paginations.page = 1;
          that.getSearch();
        } else {
          that.$message.error({
            message: res.Message,
            duration: 2000,
          });
        }
      });
    },
    // 指派的保存
    comfirmReceptionApi(params) {
      var that = this;
      if (!params.DiagnosisBy) {
        that.$message.error('请指派接诊人员');
        return;
      }
      APIReception.confirmArrange(params).then((res) => {
        if (res.StateCode == 200) {
          that.servicerStaffInfo = [];
          that.confirmStaffVisible = false;
          that.$message.success('确认指派成功');
          that.paginations.page = 1;
          that.getSearch();
        } else {
          that.$message.error({
            message: res.Message,
            duration: 2000,
          });
        }
      });
    },
    // 重新指派的保存
    afreshReceptionApi(params) {
      var that = this;
      if (!params.DiagnosisBy) {
        that.$message.error('请指派接诊人员');
        return;
      }
      APIReception.afreshArrange(params).then((res) => {
        if (res.StateCode == 200) {
          that.servicerStaffInfo = [];
          that.confirmStaffVisible = false;
          that.$message.success('重新指派成功');
          that.paginations.page = 1;
          that.getSearch();
        } else {
          that.$message.error({
            message: res.Message,
            duration: 2000,
          });
        }
      });
    },

    // 搜索条件
    getSearch() {
      var that = this;
      if (!that.searchDate) {
        that.search.StartDate = '';
        that.search.EndDate = '';
      } else {
        that.search.StartDate = that.searchDate[0];
        that.search.EndDate = that.searchDate[1];
      }
      that.search.PageNum = that.paginations.page;
      that.CustomerID = null;
      that.getReception(that.search);
    },

    /* 列表行点击事件 */
    rowClick(row) {
      let that = this;
      that.CustomerID = row.CustomerID;
      that.customerDetailVisible = true;
    },

    /**  修改页码  */
    handleCurrentChange(page) {
      var that = this;
      that.paginations.page = page;
      that.CustomerID = null;
      that.getSearch();
    },

    /**  搜索其他人员  */
    searchEmpRemote(query) {
      this.getOtherStaff(query);
    },

    /**
     *  接待列表
     */
    getReception(params) {
      var that = this;
      that.loading = true;
      APIReception.getReception(params)
        .then((res) => {
          if (res.StateCode == 200) {
            res.List.forEach((item) => {
              item.ProjectName = '';
              if (item.Project.length > 0) {
                item.ProjectName = item.Project.map((val) => {
                  return val.ProjectName;
                }).join(',');
              }
            });
            that.tableData = res.List;
            that.paginations.total = res.Total;
            that.paginations.page_size = res.PageSize;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },

    /* 获取顾客信息 */
    getCustomerDetail(id) {
      const that = this;
      APIReception.custDetailServicer({ CustomerID: id }).then((res) => {
        if (res.StateCode == 200) {
          res.Data.forEach((element) => {
            element.nameShow = '';
            if (element.Employee.length > 0) {
              var t = [];
              element.Employee.forEach((i) => {
                t.push(i.EmployeeName);
              });
              element.nameShow = t.join(',');
            }
          });
          that.itemInfo.itemServicerList = res.Data;
        } else {
          that.$$message.error({
            message: res.Message,
            duration: 2000,
          });
        }
      });
    },

    /**  获取服务人员  */
    getServicerStaff(id) {
      var that = this;
      var params = {
        CustomerID: id,
      };
      APIReception.servicerStaff(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.servicerStaffInfo = res.Data.map((val) => {
              val.searchKey = '';
              return val;
            });
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {});
    },

    /**  获取其他服务人员  */
    async getOtherStaff(SearchKey) {
      let that = this;
      let params = { SearchKey: SearchKey };
      let res = await APIReception.otherStaff(params);
      if (res.StateCode == 200) {
        that.otherStaffInfo = res.Data;
      } else {
        that.$message.error(res.Message);
      }
    },

    /* 服务人员 */
    getCustomerServicer() {
      let that = this;
      let params = {};
      cusAPI.getCustomerServicer(params).then((res) => {
        if (res.StateCode == 200) {
          that.customerServicer = res.Data;
          that.customerServicer.forEach((item) => {
            item.ServicerListArr = [];
          });
        } else {
          that.$message.error({
            message: res.Message,
            duration: 2000,
          });
        }
      });
    },

    /**
     * 清空到访登记数据
     */
    clearVistReception() {
      var that = this;
      that.confirmVistInfo = {
        CustomerID: '',
        Guidance: '',
        DiagnosisBy: '',
        IsServicer: '1',
        IsAssign: false,
        Status: '10',
        ServicerID: '',
        Diagnosis: '',
      };
      that.itemInfo.itemName = '';
      that.customerName = '';
      that.servicerStaffInfo = [];
      that.otherStaffInfo = [];
    },

    // 到访登记的取消
    cancelSave() {
      var that = this;
      that.dialogVisible = false;
      that.removeCustomer();
    },

    /**
     * 清空指派人员
     */
    cancelArrange() {
      this.arrangeInfo = {
        AppointmentBillID: '',
        DiagnosisRecordID: '',
        IsServicer: '1',
        CustomerID: '',
        Guidance: '',
        DiagnosisBy: '',
        Status: 10,
        ServicerID: '',
        Diagnosis: '',
      };
    },

    /**  显示提示  */
    getShowProductNameTooltip(name) {
      if (!name || name == '') {
        return true;
      }
      if (name.length > 10) {
        return false;
      }
      return true;
    },

    /**  获取列表服务人员信息  */
    getFirstServiceEmp(ServicerEmployee) {
      if (!ServicerEmployee || ServicerEmployee.length == 0) {
        return '';
      }
      let firstItem = ServicerEmployee[0];
      return firstItem.Name + ':' + this.getServicerEmpNames(firstItem.ServicerEmpList);
    },
    /* 服务人员处理  */
    getServicerEmpNames(ServicerEmpList) {
      if (!ServicerEmpList) {
        return '';
      }
      return ServicerEmpList.map((val) => (val ? val.Name : '')).join(', ');
    },
    setSelectServiceClass(item, emp) {
      if (this.arrangeInfo.IsServicer && this.arrangeInfo.ServicerID == item.ServicerID && this.arrangeInfo.DiagnosisBy == emp.EmployeeID) {
        return true;
      }
      return false;
    },
    setSelectClass(item, emp) {
      if (this.confirmVistInfo.IsServicer && this.confirmVistInfo.ServicerID == item.ServicerID && this.confirmVistInfo.DiagnosisBy == emp.EmployeeID) {
        return true;
      }
      return false;
    },

    /* 导出 */
    reception_excelNoDisPlayPhone() {
      let that = this;
      let params = that.search;
      that.downloadNoDisPlayLoading = true;
      APIReception.reception_excelNoDisPlayPhone(params)
        .then((res) => {
          this.$message.success({
            message: '正在导出',
            duration: '4000',
          });
          const link = document.createElement('a');
          let blob = new Blob([res], { type: 'application/octet-stream' });
          link.style.display = 'none';
          link.href = URL.createObjectURL(blob);
          link.download = '接待工作台隐藏手机号表.xlsx'; //下载的文件名
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
        })
        .finally(function () {
          that.downloadNoDisPlayLoading = false;
        });
    },
    /* 导出 */
    reception_excelDisPlayPhone() {
      let that = this;
      let params = that.search;
      that.downloadLoading = true;
      APIReception.reception_excelDisPlayPhone(params)
        .then((res) => {
          this.$message.success({
            message: '正在导出',
            duration: '4000',
          });
          const link = document.createElement('a');
          let blob = new Blob([res], { type: 'application/octet-stream' });
          link.style.display = 'none';
          link.href = URL.createObjectURL(blob);
          link.download = '接待工作台表.xlsx'; //下载的文件名
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
        })
        .finally(function () {
          that.downloadLoading = false;
        });
    },

    /** 查询会员等级   */
    customerLevel_all() {
      let that = this;
      let params = {};
      APIReception.customerLevel_all(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.customerLevelList = res.Data;
          } else {
            that.$message.error(res.Message);
          }
        })
        .catch((fail) => {
          that.$message.error(fail);
        });
    },
  },
  /** 监听数据变化   */
  watch: {},
  /** 创建实例之前执行函数   */
  beforeCreate() {},
  /**  实例创建完成之后  */
  created() {},
  /**  在挂载开始之前被调用  */
  beforeMount() {},
  /**  实例被挂载后调用  */
  mounted() {
    var that = this;
    that.isShowChannel = that.$permission.permission(that.$route.meta.Permission, 'iBeauty-Customer-Customer-Channel');
    that.isCustomerPhoneNumberView = that.$permission.permission(that.$route.meta.Permission, 'iBeauty-Workbench-Reception-CustomerPhoneNumberView');
    that.isCustomerPhoneNumberModify = that.$permission.permission(that.$route.meta.Permission, 'iBeauty-Workbench-Reception-CustomerPhoneNumberModify');

    that.isCustomerBasicInformationModify = that.$permission.permission(
      that.$route.meta.Permission,
      'iBeauty-Workbench-Reception-CustomerBasicInformationModify'
    );
    that.isCustomerServicerModify = that.$permission.permission(that.$route.meta.Permission, 'iBeauty-Workbench-Reception-CustomerServicerModify');
    that.isCustomerBasicFileModify = that.$permission.permission(that.$route.meta.Permission, 'iBeauty-Workbench-Reception-CustomerBasicFileModify');
    that.getSearch();
    that.customerLevel_all();
  },

  /**  数据更新 之前 调用  */
  beforeUpdate() {},
  /** 数据更新 完成 调用   */
  updated() {},
  /**  实例销毁之前调用  */
  beforeDestroy() {},
  /**  实例销毁后调用  */
  destroyed() {},
};
</script>

<style lang="scss">
.reception {
  // background-color: #f0f0f0;
  // padding: unset;
  // display: flex;
  // height: 100%;
  // max-height: 100%;
  // box-sizing: border-box;

  .workbench {
    background-color: #ffffff;
    box-sizing: border-box;
    padding: 15px;
    min-height: 100%;
    border-right: 8px solid #f0f0f0;
  }

  .customer-detail {
    background-color: #ffffff;
    padding: 15px;
    height: 100%;
    box-sizing: border-box;
  }
  .customer-autocomplete {
    li {
      line-height: normal;
      padding: 7px;

      .name {
        text-overflow: ellipsis;
        overflow: hidden;
      }
      .info {
        font-size: 12px;
        color: #b4b4b4;
      }
      .highlighted .info {
        color: #ddd;
      }
    }
    .tip {
      margin: 0px;
      background-color: #f7f8fa;
    }
    .margin-bottom {
      margin-bottom: 10px;
    }
    .el-scrollbar_height {
      height: 100%;
      .el-scrollbar__wrap {
        overflow-x: hidden;
      }
    }
  }
  .serviceTypeClass {
    .el-scrollbar__wrap {
      overflow-x: hidden;
    }
    .selectServiceEmp {
      border: solid 2px var(--zl-color-orange-primary);
      box-sizing: border-box;
    }
  }
  .empItem {
    margin-top: 5px;
    margin-left: 5px;
    margin-right: 5px;
  }
  .custom-el-select {
    li {
      line-height: normal;
      height: auto;
    }
  }
  .over-flow {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .custom-customer-descLabel {
    min-width: 80px;
    text-align: right;
    padding-right: 10px;
  }
}
</style>
