/**
 * Created by preference on 2023/12/05
 *  zmx
 */

import * as API from "@/api/index";
export default {
  /** 列表  */
  phoneCallBack_list: (params) => {
    return API.POST("api/phoneCallBack/list", params);
  },
  /** 新增  */
  phoneCallBack_add: (params) => {
    return API.POST("api/phoneCallBack/add", params);
  },
  /**  删除 */
  phoneCallBack_delete: (params) => {
    return API.POST("api/phoneCallBack/delete", params);
  },
  /** 查询通话记录  */
  phoneCallBack_callBackLog: (params) => {
    return API.POST("api/phoneCallBack/callBackLog", params);
  },
};
