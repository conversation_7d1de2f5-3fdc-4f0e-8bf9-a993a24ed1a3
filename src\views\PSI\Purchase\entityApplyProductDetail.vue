<template>
  <!-- 搜索 -->
  <div class="content_body PurchaseEntityApplyProductDetail" v-loading="loading">
    <div class="nav_header">
      <el-form :inline="true" size="small" :model="searchInventoryApplyData" @submit.native.prevent>
        
        <el-form-item label="单据号">
          <el-input
            v-model="searchInventoryApplyData.BillID"
            clearable
            @keyup.enter.native="handleInventoryApplySearch"
            @clear="handleInventoryApplySearch"
            placeholder="请输入单据号"
          >
          </el-input>
        </el-form-item>
        <el-form-item v-if="purchaseStorage.length > 1" label="仓库/门店">
          <el-select
            v-model="searchInventoryApplyData.EntityID"
            :default-first-option="true"
            @change="handleInventoryApplySearch"
            @clear="handleInventoryApplySearch"
            clearable
            filterable
            placeholder="请选择仓库/门店"
          >
            <el-option v-for="item in purchaseStorage" :key="item.ID" :label="item.EntityName" :value="item.ID"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="申请人">
          <el-input
            v-model="searchInventoryApplyData.CreatedBy"
            clearable
            @keyup.enter.native="handleInventoryApplySearch"
            @clear="handleInventoryApplySearch"
            placeholder="请输入申请人"
          >
          </el-input>
        </el-form-item>
        <el-form-item label="产品名称">
          <el-input
            v-model="searchInventoryApplyData.ProductName"
            clearable
            @keyup.enter.native="handleInventoryApplySearch"
            @clear="handleInventoryApplySearch"
            placeholder="请输入产品名称"
          >
          </el-input>
        </el-form-item>
        <el-form-item label="单据状态">
          <el-select
            v-model="searchInventoryApplyData.BillStatus"
            clearable
            filterable
            placeholder="请选择商品类型"
            :default-first-option="true"
            @change="handleInventoryApplySearch"
            multiple
          >
            <el-option label="待审核" value="10"></el-option>
            <el-option label="待配送" value="20"></el-option>
            <el-option label="待入库" value="30"></el-option>
            <el-option label="已驳回" value="40"></el-option>
            <el-option label="已完成" value="50"></el-option>
            <el-option label="已取消" value="60"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="产品分类">
          <el-cascader
            @change="handleInventoryApplySearch"
            @clear="handleInventoryApplySearch"
            :options="classifyList"
            :show-all-levels="true"
            clearable
            filterable
            :props="cascaderProps"
            v-model="searchInventoryApplyData.CategoryID"
            placeholder="请选择产品分类"
          ></el-cascader>
        </el-form-item>
        
        <el-form-item label="配送时间">
          <el-date-picker
            v-model="searchInventoryApplyData.DelivererTimes"
            unlink-panels
            type="daterange"
            range-separator="至"
            value-format="yyyy-MM-dd"
            start-placeholder="下始日期"
            end-placeholder="结束日期"
            @change="handleInventoryApplySearch"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="制单时间">
          <el-date-picker
            v-model="searchInventoryApplyData.QueryDate"
            unlink-panels
            type="daterange"
            range-separator="至"
            value-format="yyyy-MM-dd"
            start-placeholder="下始日期"
            end-placeholder="结束日期"
            @change="handleInventoryApplySearch"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" v-prevent-click @click="handleInventoryApplySearch">搜索</el-button>
        </el-form-item>
        <el-form-item>
          <el-button v-if="InventoryApplyDetailExport" type="primary" size="small" :loading="downloadLoading" @click="downloadInventoryApplyExcel"
            >导出</el-button
          >
        </el-form-item>
      </el-form>
    </div>
    <!-- 表格 -->
    <el-table size="small" show-summary :summary-method="getInventoryApplySummaries" :data="InventoryApplyDetailList" v-loading="InventoryApplyLoading">
      <el-table-column prop="ID" label="单据号"></el-table-column>
      <el-table-column prop="EntityName" label="申请仓库/门店"></el-table-column>
      <el-table-column prop="BillStatus" label="单据状态">
        <template v-slot="scope">
          <span v-if="scope.row.BillStatus == 10">待审核</span>
          <span v-else-if="scope.row.BillStatus == 15">待付款</span>
          <span v-else-if="scope.row.BillStatus == 20">待配送</span>
          <span v-else-if="scope.row.BillStatus == 30">待入库</span>
          <span v-else-if="scope.row.BillStatus == 40">已驳回</span>
          <span v-else-if="scope.row.BillStatus == 50">已完成</span>
          <span v-else-if="scope.row.BillStatus == 60">已取消</span>
        </template>
      </el-table-column>
      <el-table-column prop="PCategoryName" label="产品分类"></el-table-column>
      <el-table-column prop="ProductName" label="产品名称"></el-table-column>
      <el-table-column prop="OutboundPrice" label="配送单价">
        <template v-slot="scope">
          {{ scope.row.OutboundPrice | toFixed | NumFormat }}
        </template>
      </el-table-column>
      <el-table-column align="right" prop="DelivererOn" label="配送时间">
        <template slot-scope="scope">
          {{scope.row.DelivererOn | dateFormat("YYYY-MM-DD HH:mm") }}
        </template>
      </el-table-column>
      <el-table-column align="right" prop="OutboundEntityName" label="发货仓库"></el-table-column>
      <el-table-column align="right" prop="ApplyQuantity" label="要货数量"></el-table-column>
      <el-table-column align="right" prop="OutboundQuantity" label="实发数量"></el-table-column>
      <el-table-column align="right" prop="InboundQuantity" label="实收数量"> </el-table-column>
      <el-table-column align="right" prop="UnitName" label="单位"> </el-table-column>
      <el-table-column align="right" prop="ApprovedTotalAmount" label="合计金额">
        <template v-slot="scope">
          {{ scope.row.ApprovedTotalAmount | toFixed | NumFormat }}
        </template>
      </el-table-column>
      <el-table-column prop="Specification" width="150px" label="产品规格"> </el-table-column>
      <el-table-column prop="MinimumUnitAmount" label="最小包装数量"></el-table-column>
      <el-table-column prop="CreatedOn" label="制单时间">
        <template slot-scope="scope">
          {{scope.row.CreatedOn | dateFormat("YYYY-MM-DD HH:mm") }}
        </template>
      </el-table-column>
      <el-table-column prop="CreatedByName" label="申请人"></el-table-column>
      <el-table-column prop="ApplyRemark" label="申请备注"></el-table-column>
      <el-table-column prop="ApprovalRemark" label="审批备注"></el-table-column>
      <el-table-column prop="Remark" label="明细备注"></el-table-column>
    </el-table>
    <!-- 分页 -->
    <div class="pad_15 text_right">
      <el-pagination
        background
        v-if="InventoryApplyPaginations.total > 0"
        @current-change="handleInventoryApplyDetailPageChange"
        :current-page.sync="InventoryApplyPaginations.page"
        :page-size="InventoryApplyPaginations.page_size"
        :layout="InventoryApplyPaginations.layout"
        :total="InventoryApplyPaginations.total"
      ></el-pagination>
    </div>
  </div>
</template>

<script>
import APIStorage from "@/api/PSI/Purchase/storage";
import APICategory from "@/api/PSI/Product/productCategory";
import API from "@/api/PSI/Purchase/entityApplyProductDetail";
import dateTime from "@/components/js/date";
import permission from "@/components/js/permission.js";
export default {
  name: "PurchaseEntityApplyProductDetail",

  components: {},

  directives: {},

  data() {
    return {
      loading: false,
      downloadLoading: false,
      InventoryApplyLoading: false,
      purchaseStorage: [], //仓库列表
      classifyList: [], //产品分类列表
      searchInventoryApplyData: {
        EntityID: "",
        BillStatus: "",
        ProductName: "",
        CategoryID: "",
        CreatedBy: "",
        BillID:"",
        DelivererTimes:[],
        QueryDate: [],
      },
      cascaderProps: {
        checkStrictly: true,
        value: "ID",
        label: "Name",
        children: "Child",
        emitPath: false,
        expandTrigger: "hover",
      }, // 级联选择器配置项
      InventoryApplyPaginations: {
        page: 1, // 当前位于哪页
        total: 0, // 总数
        page_size: 10, // 1页显示多少条
        layout: "total, prev, pager, next,jumper", // 翻页属性
      },
      InventoryApplyDetailExport: false,
      InventoryApplyDetailList: [],
      inventoryApplySumStatementOutputForm: {},
    };
  },
  created() {
    this.searchInventoryApplyData.QueryDate = [dateTime.formatDate.format(new Date(), "YYYY-MM-DD"), dateTime.formatDate.format(new Date(), "YYYY-MM-DD")];
  },
  mounted() {
    const that = this;
    that.InventoryApplyDetailExport = permission.permission(that.$route.meta.Permission, "PSI-SupplyChain-EntityApplyProductDetail-Export");
    that.getStorageEntityNetwork();
    that.getProductCategory();
    that.getOrderList();
  },

  methods: {
    /**仓库列表  */
    getStorageEntityNetwork: function () {
      var that = this;
      var params = {};
      APIStorage.getpurchaseStorageEntity(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.purchaseStorage = res.Data;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {});
    },
    // 获取产品分类列表
    getProductCategory: function () {
      var that = this;
      // var params = {
      //   Name: "",
      //   Active: true,
      // };
      APICategory.getValidProductCategory()
        .then((res) => {
          if (res.StateCode == 200) {
            that.classifyList = res.Data;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },
    // 获取订单列表
    getOrderList() {
      const that = this;
      // if (!that.searchInventoryApplyData.QueryDate) {
      //   return;
      // }
      that.InventoryApplyLoading = true;
      const params = {
        PageNum: that.InventoryApplyPaginations.page,
        EntityID: that.searchInventoryApplyData.EntityID,
        BillStatus: that.searchInventoryApplyData.BillStatus,
        StartTime: that.searchInventoryApplyData.QueryDate ? that.searchInventoryApplyData.QueryDate[0] : "",
        EndTime: that.searchInventoryApplyData.QueryDate ? that.searchInventoryApplyData.QueryDate[1] : "",
        DelivererStartTime: that.searchInventoryApplyData.DelivererTimes ? that.searchInventoryApplyData.DelivererTimes[0]:"",
        DelivererEndTime:  that.searchInventoryApplyData.DelivererTimes ? that.searchInventoryApplyData.DelivererTimes[1]:"",
        ProductName: that.searchInventoryApplyData.ProductName,
        CategoryID: that.searchInventoryApplyData.CategoryID,
        CreatedBy: that.searchInventoryApplyData.CreatedBy,
        BillID: that.searchInventoryApplyData.BillID
      };
      API.getInventoryApplyDetail(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.InventoryApplyDetailList = res.Data.detail.List;
            that.inventoryApplySumStatementOutputForm = res.Data.inventoryApplySumStatementOutputForm;
            that.InventoryApplyPaginations.total = res.Data.detail.Total;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(() => {
          that.InventoryApplyLoading = false;
        });
    },
    handleInventoryApplySearch() {
      const that = this;
      that.InventoryApplyPaginations.page = 1;
      that.getOrderList();
    },
    downloadInventoryApplyExcel() {
      const that = this;
      // if (!that.searchInventoryApplyData.QueryDate) {
      //   return;
      // }
      that.downloadLoading = true;
      const data = that.searchInventoryApplyData;
      const params = {
        EntityID: data.EntityID,
        BillStatus: data.BillStatus,
        CategoryID: data.CategoryID,
        StartTime: that.searchInventoryApplyData.QueryDate ? that.searchInventoryApplyData.QueryDate[0] : "",
        EndTime: that.searchInventoryApplyData.QueryDate ? that.searchInventoryApplyData.QueryDate[1] : "",
        DelivererStartTime: that.searchInventoryApplyData.DelivererTimes ? that.searchInventoryApplyData.DelivererTimes[0]:"",
        DelivererEndTime:  that.searchInventoryApplyData.DelivererTimes ? that.searchInventoryApplyData.DelivererTimes[1]:"",
        ProductName: data.ProductName,
        CreatedBy: data.CreatedBy,
        BillID: data.BillID
      };
      API.InventoryApplyExcel(params)
        .then((res) => {
          this.$message.success({
            message: "正在导出",
            duration: "4000",
          });
          const link = document.createElement("a");
          let blob = new Blob([res], { type: "application/octet-stream" });
          link.style.display = "none";
          link.href = URL.createObjectURL(blob);
          link.download = "门店要货明细.xlsx"; //下载的文件名
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
        })
        .finally(() => {
          that.downloadLoading = false;
        });
    },
    // 分页
    handleInventoryApplyDetailPageChange() {
      this.getOrderList();
    },
    // 合计
    getInventoryApplySummaries(param) {
      const { columns } = param;
      const sums = [];
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = <span class="font_weight_600">合计</span>;
          return;
        }
        var filter_NumFormat = this.$options.filters["NumFormat"];
        switch (column.property) {
          case "ApprovedTotalAmount":
            sums[index] = (
              <span class="font_weight_600">
                {filter_NumFormat(this.inventoryApplySumStatementOutputForm ? this.inventoryApplySumStatementOutputForm.ApprovedTotalAmount : 0)}
              </span>
            );
            break;
          case "OutboundQuantity":
            sums[index] = (
              <span class="font_weight_600">{this.inventoryApplySumStatementOutputForm ? this.inventoryApplySumStatementOutputForm.OutboundQuantity : 0}</span>
            );
            break;
          case "InboundQuantity":
            sums[index] = (
              <span class="font_weight_600">{this.inventoryApplySumStatementOutputForm ? this.inventoryApplySumStatementOutputForm.InboundQuantity : 0}</span>
            );
            break;
          case "ApplyQuantity":
            sums[index] = (
              <span class="font_weight_600">{this.inventoryApplySumStatementOutputForm ? this.inventoryApplySumStatementOutputForm.ApplyQuantity : 0}</span>
            );
            break;
          default:
            sums[index] = <span class="font_weight_600"></span>;
        }
      });
      return sums;
    },
  },
};
</script>

<style lang="scss" >
.PurchaseEntityApplyProductDetail {
}
</style>