<template>
  <div class="saleSavingCardEntityPerformanceScheme content_body">
    <!-- 搜索 -->
    <div class="nav_header">
      <el-row>
        <el-col :span="20">
          <el-form :inline="true" size="small" @submit.native.prevent>
            <el-form-item label="组织单位">
              <el-input
                @clear="handleSearch"
                v-model="Name"
                placeholder="输入组织单位名称搜索"
                clearable
                @keyup.enter.native="handleSearch"
              ></el-input>
            </el-form-item>
            <el-form-item>
              <el-button
                type="primary"
                size="small"
                @click="handleSearch"
                v-prevent-click
                >搜索</el-button
              >
            </el-form-item>
          </el-form>
        </el-col>
        <el-col :span="4" class="text_right">
          <el-button
            type="primary"
            size="small"
            @click="showAddDialog"
            v-prevent-click
            >新增</el-button
          >
        </el-col>
      </el-row>
    </div>
    <!-- 表格 -->
    <div>
      <el-table size="small" :data="saleSavingCardEntityTableData">
        <el-table-column  prop="EntityName"  label="组织单位"></el-table-column>
        <el-table-column label="操作" width="145px">
          <template slot-scope="scope">
            <el-button
              type="primary"
              size="small"
              @click="showEditDialog(scope.row)"
              v-prevent-click
              >编辑</el-button
            >
            <el-button
              type="danger"
              size="small"
              @click="removeEntityClick(scope.row)"
              v-prevent-click
              v-if="isDelete"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <div class="page pad_10 text_right">
        <div class="text_right" v-if="paginations.total > 0">
          <el-pagination
            background
            @current-change="handleCurrentChange"
            :current-page.sync="paginations.page"
            :page-size="paginations.page_size"
            :layout="paginations.layout"
            :total="paginations.total"
          ></el-pagination>
        </div>
      </div>
   </div>
   <!-- 新增弹窗 -->
   <el-dialog
      title="新增储值卡销售门店业绩方案"
      :visible.sync="dialogVisible"
      width="30%"
      custom-class="custom-dialog-add"
    >
      <div>
        <el-form
          :model="ruleForm"
          :rules="rules"
          ref="ruleForm"
          label-width="120px"
          size="small"
        >
          <el-form-item label="组织单位" prop="EntityID">
            <span slot="label">
              适用组织
              <el-popover placement="top-start" width="200" trigger="hover">
                <p>适用于同级所有节点，则只需选择父节点。</p>
                <p>比如：适用于所有节点，只需选择“顶级/第一个”节点。</p>
                <el-button
                  type="text"
                  style="color: #dcdfe6"
                  icon="el-icon-info"
                  slot="reference"
                ></el-button>
              </el-popover>
            </span>
            <treeselect
              v-model="ruleForm.EntityID"
              :options="entityList"
              :normalizer="normalizer"
              clearValueText
              noResultsText="无匹配数据"
              placeholder="选择所属部门"
            />
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="dialogVisible = false" v-prevent-click
          >取 消</el-button
        >
        <el-button
          type="primary"
          size="small"
          :loading="modalLoading"
          v-prevent-click
          @click="submitSavingCardEntityPerformanceClick"
          >保存</el-button
        >
      </div>
   </el-dialog>
   <!-- 编辑弹窗 -->
   <el-dialog :visible.sync="dialogEdit" width="40%" custom-class="custom-dialog-edit" >
      <div slot="title">{{ entityName }} - 储值卡分类销售门店业绩方案</div>
      <el-table
        size="small"
        :data="saleSavingCardEntityCategoryTableData"
        row-key="CategoryID"
        :tree-props="{ children: 'Child', hasChildren: 'hasChild' }"
        :row-class-name="tableRowClassName"
        max-height="500px"
      >
        <el-table-column
          prop="CategoryName"
          label="储值卡名称"
          min-width="120px"
          fixed
        ></el-table-column>
        <el-table-column label="现金比例" min-width="70px">
          <template slot-scope="scope">
            <el-input
              size="mini"
              v-model="scope.row.PayRate"
              v-input-fixed="2"
              type="number"
              class="input_type"
              @input="royaltyRateChange(1, scope.row)"
            >
              <template slot="append">%</template>
            </el-input>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="115px">
          <template slot-scope="scope">
            <el-button
              type="primary"
              size="mini"
              @click="savingCardEntityPerformance(scope.row)"
              v-if="!scope.row.isEntity"
              >储值卡业绩</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="dialogEdit = false" v-prevent-click
          >取 消</el-button
        >
        <el-button
          type="primary"
          size="small"
          :loading="modalLoading"
          v-prevent-click
          @click="submitSavingCardEntityCategoryClick"
          >保存</el-button
        >
      </div>
   </el-dialog>
    <!--储值卡业绩弹窗-->
   <el-dialog :visible.sync="dialogSavingCardEntity" width="40%" custom-class="custom-dialog-edit_SavingCard">
      <div slot="title">
        {{ entityName }} - {{ categoryName }} - 储值卡销售门店业绩方案
      </div>
      <div>
        <el-form :inline="true" size="small" @submit.native.prevent>
          <el-form-item>
            <el-input
              v-model="SearchKey"
              placeholder="输入储值卡名称搜索"
              prefix-icon="el-icon-search"
              clearable
            ></el-input>
          </el-form-item>
        </el-form>
        <el-table
          :data="
            savingCardEntityRoyaltyList.filter(
              (data) =>
                !SearchKey ||
                data.GoodName.toLowerCase().includes(SearchKey.toLowerCase())
            )
          "
          row-key="GoodID"
          size="small"
          max-height="500px"
        >
          <el-table-column
            prop="GoodName"
            label="储值卡名称"
            min-width="130px"
            fixed
          ></el-table-column>
          <el-table-column label="现金比例" min-width="70px">
            <template slot-scope="scope">
              <el-input
                size="mini"
                v-model="scope.row.PayRate"
                type="number"
                v-input-fixed="2"
                class="input_type"
                @input="royaltyRateChange(1, scope.row)"
              >
                <template slot="append">%</template>
              </el-input>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="dialogSavingCardEntity = false" v-prevent-click
          >取 消</el-button
        >
        <el-button
          type="primary"
          size="small"
          :loading="modalLoading"
          v-prevent-click
          @click="updateSavingCardEntityPerformance"
          >保存</el-button
        >
      </div>
   </el-dialog>

  </div>
</template>

<script>

import API from "@/api/iBeauty/EntityPerformance/saleSavingCardEntityPerformanceScheme"
import APIEntity from "@/api/KHS/Entity/entity";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
var Enumerable = require("linq");

export default {
 name: 'saleSavingCardEntityPerformanceScheme',

beforeRouteEnter(to, from, next) {
 next((vm) => {
     vm.isDelete = vm.$permission.permission(
      to.meta.Permission,
      "KHS-EntityPerformance-SaleSavingCardEntityPerformanceScheme-Delete"
    );
  });
},
  props:{},
  /**  引入的组件  */
  components: {Treeselect},
  /**  Vue 实例的数据对象**/
  data() {
    return {
      isDelete: false,
     modalLoading: false,
     loading: false,
     dialogVisible: false,
     dialogEdit: false,
     dialogSavingCardEntity: false,
     Name: '', // 搜索条件
     EntityID: "", //  当前的门店ID
     entityName: "",
     categoryName: "",
     savingCardCategoryID: "",
     SearchKey: "",  // 储值卡搜索
     saleSavingCardEntityTableData: [], //表格数据
     saleSavingCardEntityCategoryTableData: [], //编辑弹窗表格数据
     savingCardEntityRoyaltyList: [], //储值卡弹窗表格数据
     entityList: [], //门店数据
     ruleForm: {
        EntityID: null,
        },
        rules: {
        EntityID: [
          { required: true, message: "请选择组织", trigger: "change" },
         ],
        },
     //需要给分页组件传的信息
        paginations: {
        page: 1, // 当前位于哪页
        total: 0, // 总数
        page_size: 10, // 1页显示多少条
        layout: "total, prev, pager, next,jumper", // 翻页属性
        },
    }
  },
   /**计算属性  */
  computed: {
  },
  /**  方法集合  */
  methods: {
    /* 数据显示 */
    handleSearch(){
     let that = this;
     that.paginations.page = 1
     that.getSavingCardEntityPerformanceScheme();
    },
    /* 上下分页 */
    handleCurrentChange(page) {
      let that = this;
      that.paginations.page = page;
      that.getSavingCardEntityPerformanceScheme()
    },
    /* 获取门店储值卡销售业绩列表 */
    getSavingCardEntityPerformanceScheme: function () {
      let that = this;
      that.loading = true;
      var params = {
        Name: that.name,
        PageNum: that.paginations.page,
      };
      API.getSavingCardEntityPerformanceScheme(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.saleSavingCardEntityTableData = res.List;
            that.paginations.total = res.Total;
            that.paginations.page_size = res.PageSize;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },
    /* 新增 */
    showAddDialog(){
      let that = this
      that.ruleForm = {
        entity: null,
      };
      that.dialogVisible = true;
    },
    /* 编辑 */
    showEditDialog(row){
      let that = this
      that.EntityID = row.EntityID
      that.entityName = row.EntityName;
      that.getSavingCardCategoryEntityPerformance()
    },
    /* 获取分类储值卡业绩 编辑*/
    getSavingCardCategoryEntityPerformance: function () {
      var that = this;
      that.loading = true;
      var params = {
        EntityID: that.EntityID,
      };
      API.getSavingCardCategoryEntityPerformance(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.dialogEdit = true;
            var data = {
              CategoryID: res.Data.EntityID,
              CategoryName: "所有储值卡",
              PayRate: res.Data.PayRate,
              isEntity: true,
            };
            var Category = Enumerable.from(res.Data.Category)
              .select((val) => ({
                CategoryID: val.CategoryID,
                CategoryName: val.CategoryName,
                PayRate: val.PayRate,
                isEntity: false,
              }))
              .toArray();
            that.saleSavingCardEntityCategoryTableData = Category;
            that.saleSavingCardEntityCategoryTableData.unshift(data);
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },

    /* 保存分类储值卡业绩 */
    submitSavingCardEntityCategoryClick(){
      var that = this;
      var params = {
        EntityID: that.saleSavingCardEntityCategoryTableData[0].CategoryID,
        PayRate: that.saleSavingCardEntityCategoryTableData[0].PayRate,
      };
      var saleSavingCardEntityCategoryTableData = JSON.parse(JSON.stringify(that.saleSavingCardEntityCategoryTableData))
      saleSavingCardEntityCategoryTableData = Enumerable.from(
        saleSavingCardEntityCategoryTableData
      )
        .where(function (i) {
          return (
            (i.PayRate !== "" && i.PayRate !== null)
          );
        })
        .toArray();
      that.modalLoading = true;
      var Category = Enumerable.from(saleSavingCardEntityCategoryTableData)
        .where(function (i) {
          return !i.isEntity;
        })
        .select((val) => ({
          CategoryID: val.CategoryID,
          PayRate: val.PayRate,
        }))
        .toArray();
      params.Category = Category;
      API.updateSavingCardCategoryEntityPerformance(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.$message.success("业绩设置成功");
            that.dialogEdit = false;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.modalLoading = false;
        });
    },


    /* 获取储值卡业绩 */
    savingCardEntityPerformance: function (row) {
      let that = this;
      that.savingCardCategoryID = row.CategoryID;
      that.categoryName = row.CategoryName;
      let params = {
        EntityID: that.EntityID,
        CategoryID: that.savingCardCategoryID,
      };
      API.getSavingCardEntityPerformance(params).then((res) => {
        if (res.StateCode == 200) {
          that.dialogSavingCardEntity = true;
          that.savingCardEntityRoyaltyList = res.Data;
        } else {
          that.$message.error({
            message: res.Message,
            duration: 2000,
          });
        }
      });
    },
    /* 储值卡业绩保存 */
    updateSavingCardEntityPerformance: function (){
      let that = this
      that.modalLoading = true;
      let SavingCardList = [];
      SavingCardList = Enumerable.from(that.savingCardEntityRoyaltyList)
        .where(function (i) {
          return (
            (i.PayRate !== "" && i.PayRate !== null)
          );
        })
        .select((val) => ({
          GoodID: val.GoodID,
          PayRate: val.PayRate,
        }))
        .toArray();
      let params = {
        EntityID: that.EntityID,
        Good: SavingCardList,
        CategoryID: that.savingCardCategoryID,
      };
      API.updateSavingCardEntityPerformance(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.$message.success({
              message: res.Message,
              duration: 2000,
            });
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
          this.dialogSavingCardEntity = false;
        })
        .finally(() => {
          that.modalLoading = false;
        });
    },
    /* 删除储值卡销售业绩方案 */
    removeEntityClick: function (row) {
      var that = this;
      that
        .$confirm("此操作将永久删除该记录, 是否继续?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
        .then(() => {
            that.loading = true;
          var params = {
            EntityID: row.EntityID,
          };
          API.deleteSavingCardEntityPerformanceScheme(params)
            .then((res) => {
              if (res.StateCode == 200) {
                that.$message.success("删除成功");
                that.getSavingCardEntityPerformanceScheme();
              } else {
                that.$message.error({
                  message: res.Message,
                  duration: 2000,
                });
              }
            })
            .finally(function () {
              that.loading = false;
            });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    /* 所属单位 */
    entityData: function () {
      var that = this;
      APIEntity.getEntityAll()
        .then((res) => {
          if (res.StateCode == 200) {
            that.entityList = res.Data;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },
    /* 新增保存 */
    submitSavingCardEntityPerformanceClick(){
      var that = this;
      that.$refs.ruleForm.validate((valid) => {
        if (valid) {
          that.modalLoading = true;
          let para = Object.assign({}, that.ruleForm);
          API.createSavingCardEntityPerformanceScheme(para)
            .then(function (res) {
              if (res.StateCode === 200) {
                that.$message.success({
                  message: "新增成功",
                  duration: 2000,
                });
                that.getSavingCardEntityPerformanceScheme();
                that.$refs["ruleForm"].resetFields();
                that.dialogVisible = false;
              } else {
                that.$message.error({
                  message: res.Message,
                  duration: 2000,
                });
              }
            })
            .finally(function () {
              that.modalLoading = false;
            });
        }
      });
    },
    /* 约束业绩比例 */
    royaltyRateChange: function (index, row) {
      if (index == 1) {
        if (row.PayRate > 100) {
          row.PayRate = 100;
        }
      }
    },
    /* 高亮第一级表格 */
    tableRowClassName({ rowIndex }) {
      if (rowIndex === 0) {
        return "info-row";
      }
      return "";
    },
    /* 树形结构数据转换 */
    normalizer(node) {
      return {
        id: node.ID,
        label: node.EntityName,
        children: node.Child,
      };
    },
  },
  /** 监听数据变化   */
  watch: {},
  /** 创建实例之前执行函数   */
  beforeCreate() {},
  /**  实例创建完成之后  */
  created() {},
  /**  在挂载开始之前被调用  */
  beforeMount() {},
  /**  实例被挂载后调用  */
  mounted() {
    let that = this
    this.isDelete = this.$permission.permission(
      this.$route.meta.Permission,
       "KHS-EntityPerformance-SaleSavingCardEntityPerformanceScheme-Delete"
    );
    that.handleSearch();
    that.entityData();
  },
  /**  数据更新 之前 调用  */
  beforeUpdate() {},
  /** 数据更新 完成 调用   */
  updated() {},
  /**  实例销毁之前调用  */
  beforeDestroy() {},
  /**  实例销毁后调用  */
  destroyed() {},
}
</script>

<style lang="scss">

.saleSavingCardEntityPerformanceScheme{
  .input_type {
    .el-input-group__append {
      padding: 0 10px;
    }
  }
  .el-table .info-row {
    background: #c0c4cc;
  }
  .el-input__inner {
    padding-right: 0;
  }
  .custom-dialog-add{
    min-width: 500px;
  }
  .custom-dialog-edit{
    min-width: 850px;
  }
  .custom-dialog-edit_SavingCard{
    min-width: 850px;
  }
 }
</style>
