<template>
  <div class="workbenchCustomerDetail">
    <el-drawer v-drawerDrag:changeMinWidth="changeMinWidth" :visible.sync="show" @close="closeDialog" :wrapperClosable="false" custom-class="custom-class-drawer" :size="drawerSize" :show-close="false">
      <div slot="title" class="dis_flex flex_x_between">
        <div></div>
        <div class="text_right" style="width: 100px">
          <i @click="changeDrawerSize" class="el-icon-rank font_24 marrt_15"></i>
          <i @click="closeDrawerClick" class="el-icon-close font_24"></i>
        </div>
      </div>
      <el-tabs class="custom-tabs-class" v-model="tabPane" @tab-click="handleClick">
        <el-tab-pane label="基本档案" name="0">
          <workbench-customer-basic-files
            v-if="customerID"
            :customerID="customerID"
            :isCustomerPhoneNumberView="isCustomerPhoneNumberView"
            :isCustomerPhoneNumberModify="isCustomerPhoneNumberModify"
            :isCustomerBasicInformationModify="isCustomerBasicInformationModify"
            :isCustomerServicerModify="isCustomerServicerModify"
            :isCustomerBasicFileModify="isCustomerBasicFileModify"
            ref="customerbasicfiles"
          ></workbench-customer-basic-files>
        </el-tab-pane>
        <el-tab-pane label="卡项信息" name="2">
          <workbench-customer-account v-if="customerID" :customerID="customerID" ref="customerAccount"></workbench-customer-account>
        </el-tab-pane>
        <el-tab-pane label="订单信息" name="3">
          <workbench-customer-bill v-if="customerID" :customerID="customerID" ref="customerBill"></workbench-customer-bill>
        </el-tab-pane>
        <el-tab-pane label="服务日志" name="5">
          <workbench-customer-nursing-log v-if="customerID" :customerID="customerID" ref="customerNursingLog"></workbench-customer-nursing-log>
        </el-tab-pane>

        <el-tab-pane label="跟进记录" name="1">
          <workbench-followUpRecord v-if="customerID" :customerID="customerID" :isCallBack="isCallBack" ref="followUpRecord"> </workbench-followUpRecord>
        </el-tab-pane>
        <el-tab-pane label="预约记录" name="4">
          <workbench-appointment-record
            v-if="customerID"
            :customerID="customerID"
            ref="customerAppointmentRecord"
            @addAppointment="handleAddAppointment"
            @modifyAppointment="handleModifyAppointment"
          ></workbench-appointment-record>
        </el-tab-pane>
        <el-tab-pane label="文件档案" name="6">
          <workbench-customer-file-upload v-if="customerID" :customerID="customerID" :isDeleteFile="isDeleteFile" ref="coustomerFileUpload"></workbench-customer-file-upload>
        </el-tab-pane>
        <!-- <el-tab-pane label="电子病历" name="7" v-if="isElectronicMedicalRecord">
          <workbench-electronic-medical-record v-if="customerID" :CustomerID="customerID" :isDeleteFile="isDeleteFile" ref="customerElectronicMedicalRecord"></workbench-electronic-medical-record>
        </el-tab-pane>
        <el-tab-pane label="对比照" name="9">
          <workbenchEffectComparison :CustomerID="customerID" ref="workbenchEffectComparisonRef"></workbenchEffectComparison>
        </el-tab-pane> -->
        <el-tab-pane label="企微会话" name="8">
          <workbenchDialogue v-if="customerID" :customerID="customerID" ref="workbenchDialogue"></workbenchDialogue>
        </el-tab-pane>
      </el-tabs>
    </el-drawer>
  </div>
</template>

<script>
import workbenchFollowUpRecord from "@/views/iBeauty/Workbench/Component/workbenchFollowUpRecord";
import workbenchAppointmentRecord from "@/views/iBeauty/Workbench/Component/workbenchAppointmentRecord";
import workbenchCustomerFileUpload from "@/views/iBeauty/Workbench/Component/workbenchCustomerFileUpload";
import workbenchCustomerAccount from "@/views/iBeauty/Workbench/Component/workbenchCustomerAccount";
import workbenchCustomerBasicFiles from "@/views/iBeauty/Workbench/Component/workbenchCustomerBasicFiles.vue";
import workbenchCustomerBill from "@/views/iBeauty/Workbench/Component/workbenchCustomerBill";
import workbenchCustomerNursingLog from "@/views/iBeauty/Workbench/Component/workbenchCustomerNursingLog";
import workbenchElectronicMedicalRecord from "@/views/iBeauty/Workbench/Component/workbenchElectronicMedicalRecord";
import workbenchDialogue from "@/views/iBeauty/Workbench/Component/workbenchDialogue";
import workbenchEffectComparison from "@/views/iBeauty/Workbench/Component/workbenchEffectComparison.vue";

export default {
  name: "workbenchCustomerDetail",
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    customerID: {
      type: Number,
      require: true,
    },
    customerName: {
      type: String,
      require: true,
    },
    isCallBack: {
      type: Boolean,
    },
    isCustomerPhoneNumberView: {
      type: Boolean,
      default: false,
    },
    isCustomerPhoneNumberModify: {
      type: Boolean,
      default: false,
    },
    isCustomerBasicFileModify: {
      type: Boolean,
      default: false,
    },
    isCustomerServicerModify: {
      type: Boolean,
      default: false,
    },
    isCustomerBasicInformationModify: {
      type: Boolean,
      default: false,
    },
  },
  /** 监听数据变化   */
  watch: {
    visible: {
      immediate: true,
      handler(val) {
        this.show = val;
        this.tabPane = "0";
        this.isFullscreen = false;
        this.drawerSize = "60%";
      },
    },
    customerID: {
      immediate: true,
      handler(val) {
        if (val) {
          this.$nextTick(() => {
            this.handleClick();
          });
        }
      },
    },
    show: {
      handler(val) {
        if (this.customerID && val) {
          this.$nextTick(() => {
            this.handleClick();
          });
        }
      },
    },
  },
  /**  引入的组件  */
  components: {
    workbenchFollowUpRecord,
    workbenchAppointmentRecord,
    workbenchCustomerFileUpload,
    workbenchCustomerAccount,
    workbenchCustomerBasicFiles,
    workbenchCustomerBill,
    workbenchCustomerNursingLog,
    workbenchElectronicMedicalRecord,
    workbenchDialogue,
    workbenchEffectComparison,
  },
  /**  Vue 实例的数据对象**/
  data() {
    return {
      show: false,
      moreShow: true,
      isDeleteFile: false,
      isElectronicMedicalRecord: false,
      tabPane: "0",
      circleUrl: "https://cube.elemecdn.com/3/7c/********************************.png", //默认头像
      drawerSize: "60%",
      isFullscreen: false,
    };
  },
  /**计算属性  */
  computed: {},
  /**  方法集合  */
  methods: {
    /**    */
    changeMinWidth() {
      // console.log("==========");
    } /**  全品按钮  */,
    changeDrawerSize() {
      let that = this;
      that.isFullscreen = !that.isFullscreen;
      if (that.isFullscreen) {
        that.drawerSize = "100%";
      } else {
        that.drawerSize = "60%";
      }
    },
    /**    */
    closeDrawerClick() {
      let that = this;
      that.show = false;
    },
    /**    */
    closeDialog() {
      let that = this;
      that.$emit("update:visible", false);
    },
    showConsumeInfo() {
      this.moreShow = !this.moreShow;
    },
    /* 切换 */
    handleClick() {
      let that = this;
      let tabPane = this.tabPane;
      if (that.customerID) {
        switch (tabPane) {
          case "0":
            {
              that.$refs.customerbasicfiles.getCustomerInfoData();
            }
            break;

          case "1":
            {
              that.$refs.followUpRecord.getCustomerFollowUp();
            }
            break;

          case "2":
            {
              that.$refs.customerAccount.activeName = "0";
              that.$refs.customerAccount.handleClick();
            }
            break;

          case "3":
            {
              that.$refs.customerBill.searchSaleBill();
            }
            break;

          case "4":
            {
              that.$refs.customerAppointmentRecord.getAppointmentRecordList();
            }
            break;

          case "5":
            {
              that.$refs.customerNursingLog.clearListData();
              that.$refs.customerNursingLog.getNursingLogList();
            }
            break;

          case "6":
            {
              that.$refs.coustomerFileUpload.claerCoustomerFileUploadData();
              that.$refs.coustomerFileUpload.getCoustomerFileUploadData();
            }
            break;

          case "7":
            {
              that.$refs.customerElectronicMedicalRecord.claerElectronicMedicalRecordData();
              that.$refs.customerElectronicMedicalRecord.getElectronicMedicalRecordData();
            }
            break;
          case "8":
            that.$refs.workbenchDialogue.CustomerID = that.customerID;
            that.$refs.workbenchDialogue.clearSeachData();
            that.$refs.workbenchDialogue.getCustomerRecord();
            break;

          case "9":
            that.$refs.workbenchEffectComparisonRef.CustomerID = that.customerID;
            that.$refs.workbenchEffectComparisonRef.photoCompare();
            that.$refs.workbenchEffectComparisonRef.clearData();
            break;
        }
      }
    },

    // 处理新建预约事件
    handleAddAppointment(data) {
      console.log('父组件收到新建预约事件:', data);
      // 预约记录组件已经有自己的弹框了，不需要父组件处理
      // 这个事件现在由预约记录组件内部直接处理
    },

    // 处理修改预约事件
    handleModifyAppointment(appointmentData) {
      console.log('父组件收到修改预约事件:', appointmentData);
      // 暂时显示提示，因为修改预约需要更复杂的实现
      this.$message.info('修改预约功能正在开发中');
    },
  },
  /** 创建实例之前执行函数   */
  beforeCreate() {},
  /**  实例创建完成之后  */
  created() {},
  /**  在挂载开始之前被调用  */
  beforeMount() {},
  /**  实例被挂载后调用  */
  mounted() {
    /**  删除文件档案中文件权限  */
    this.isDeleteFile = this.$permission.getCustomerDetailPermission(this.$router, "iBeauty-Customer-Customer-DeleteFile");
    /**  电子病例权限  */
    this.isElectronicMedicalRecord = this.$permission.getCustomerDetailPermission(this.$router, "iBeauty-Customer-Customer-CustomTagLibrary");
  },
  /**  数据更新 之前 调用  */
  beforeUpdate() {},
  /** 数据更新 完成 调用   */
  updated() {},
  /**  实例销毁之前调用  */
  beforeDestroy() {},
  /**  实例销毁后调用  */
  destroyed() {},
};
</script>

<style lang="scss">
.workbenchCustomerDetail {
  min-width: 600px;
  height: 100%;
  max-width: 100vw;
  .custom-class-drawer {
    .el-drawer__header {
      margin-bottom: unset;
      padding-top: 5px;
      padding-bottom: 5px;
      background: #f5f7fa;
    }
    .el-drawer__body {
      padding: 8px;
    }
  }
  .custom-tabs-class {
    display: flex;
    flex-direction: column;
    height: 100%;
    .el-tabs__content {
      /*height: calc(100vh - 110px);*/
      height: 100%;
      overflow-y: auto;
      flex: 1;
      .el-tab-pane {
        height: 100%;
      }
    }
  }
  .el-tabs__header {
    margin: 0 0 5px;
  }

  .el-icon-rank {
    transform: rotate(45deg);
  }
}
</style>
