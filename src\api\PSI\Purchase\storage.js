/**
 * Created by preference on 2020/09/08
 *  zmx 
 */

import * as API from '@/api/index'
export default {
  /** 4.1.采购入库列表  */
  getpurchaseStorageList: params => {
    return API.POST('api/purchaseStorage/list', params)
  },
  /** 4.2.创建采购入库单  */
  getpurchaseStorageCreate: params => {
    return API.POST('api/purchaseStorage/create', params)
  },
  /**  4.3.采购入库单详情 */
  getpurchaseStorageInfo: params => {
    return API.POST('api/purchaseStorage/info', params)
  },
  /**  4.4.仓库列表 */
  getpurchaseStorageEntity: params => {
    return API.POST('api/purchaseStorage/entity', params)
  },
  /**  4.5.供应商列表 */
  getpurchaseStorageSupplier: params => {
    return API.POST('api/purchaseStorage/supplier', params)
  },
  /**  4.6.采购入库产品列表 */
  getpurchaseStorageProduct: params => {
    return API.POST('api/purchaseStorage/product', params)
  },
  /**  4.4.仓库列表 无权限限制 */
  getInventoryAllocationApplyOutEntity: params => {
    return API.POST('api/inventoryAllocationApply/outEntity', params)
  },
  // /* 打印小票内容 */
  // print: params => {
  //   return API.POST('api/purchaseStorage/print', params)
  // },
  //  /**  获取小票打印配置 */
  //  getReceiptConfigBill: params => {
  //   return API.POST('api/receiptConfig/bill', params)
  // },

  /**  模板列表 */
  getPrintTemplate_list: params => {
    return API.POST('api/template/list', params)
  },

  // ========== 采购入库流程优化新增接口 ==========
  /** 创建待确认入库单 */
  createDraft: params => {
    return API.POST('api/purchaseStorage/createDraft', params)
  },

  /** 获取待确认入库单详情 */
  pendingInfo: params => {
    return API.POST('api/purchaseStorage/pendingInfo', params)
  },

  /** 获取入库单完整详情 */
  fullInfo: params => {
    return API.POST('api/purchaseStorage/fullInfo', params)
  },

  /** 确认入库 */
  confirm: params => {
    return API.POST('api/purchaseStorage/confirm', params)
  },

  /** 删除附件 */
  deleteAttachment: params => {
    return API.POST('api/purchaseStorage/attachment/delete', params)
  },

}