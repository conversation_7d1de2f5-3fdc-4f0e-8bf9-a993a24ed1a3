<template>
  <div>
    <!-- 成为客户天数 -->
    <el-card v-show="triggerShow.CustomerShow.Code == 'CreatedOn_Day'" class="box-card" shadow="never">
      <div class="dis_flex flex_x_between flex_y_center marbm_20">
        <div>
          <span style="margin-right: 10px">成为客户天数</span>
          <span class="color_999">成为客户的日期在指定范围的人群</span>
        </div>
        <i class="el-icon-close" @click="triggerDeleteClick(triggerShow.CustomerShow.Code)"></i>
      </div>
      <div class="dis_flex flex_y_center">
        <span style="margin-right: 40px">选择时间</span>
        <el-input v-model="sectionData.customerStartTime" @blur="handlerAgeInput" style="width: 140px; height: 34px" size="small" min="0" type="number" placeholder="请输入"></el-input>
        <span style="margin: 0 15px">-</span>
        <el-input v-model="sectionData.customerEndTime" @blur="handlerAgeInput" style="width: 140px; height: 34px" size="small" min="0" type="number" placeholder="请输入"></el-input>
        <span class="marlt_10">天</span>
      </div>
    </el-card>

    <!-- 销售金额 -->
    <el-card v-show="triggerShow.SaleStartMoneyShow.Code == 'Sale_Amount'" class="box-card" shadow="never">
      <div class="dis_flex flex_x_between flex_y_center marbm_20">
        <div>
          <span style="margin-right: 40px">销售金额</span>
          <span class="color_999">在店铺内成功销售总金额达到指定范围的客户</span>
        </div>
        <i class="el-icon-close" @click="triggerDeleteClick(triggerShow.SaleStartMoneyShow.Code)"></i>
      </div>
      <div class="dis_flex flex_y_center">
        <span style="margin-right: 40px">选择金额</span>
        <el-input v-model="sectionData.SaleStartMoney" @blur="handlerAgeInput" style="width: 140px; height: 34px" size="small" min="0" type="number" placeholder="请输入"></el-input>
        <span style="margin: 0 15px">-</span>
        <el-input v-model="sectionData.SaleEndMoney" @blur="handlerAgeInput" style="width: 140px; height: 34px" size="small" min="0" type="number" placeholder="请输入"></el-input>
        <span class="marlt_10">元</span>
      </div>
    </el-card>
    <!-- 销售次数 -->
    <el-card v-show="triggerShow.SaleStartSecondShow.Code == 'Sale_Count'" class="box-card" shadow="never">
      <div class="dis_flex flex_x_between flex_y_center marbm_20">
        <div>
          <span style="margin-right: 40px">销售次数</span>
          <span class="color_999">在店铺内成功销售总次数达到指定范围的客户</span>
        </div>
        <i class="el-icon-close" @click="triggerDeleteClick(triggerShow.SaleStartSecondShow.Code)"></i>
      </div>
      <div class="dis_flex flex_y_center">
        <span style="margin-right: 40px">选择次数</span>
        <el-input v-model="sectionData.SaleStartSecond" @blur="handlerAgeInput" style="width: 140px; height: 34px" size="small" min="0" type="number" placeholder="请输入"></el-input>
        <span style="margin: 0 15px">-</span>
        <el-input v-model="sectionData.SaleEndSecond" @blur="handlerAgeInput" style="width: 140px; height: 34px" size="small" min="0" type="number" placeholder="请输入"></el-input>
        <span class="marlt_10">次</span>
      </div>
    </el-card>
    <!-- 销售客单价 -->
    <el-card v-show="triggerShow.SalePassengerStartPriceShow.Code == 'Sale_Average'" class="box-card" shadow="never">
      <div class="dis_flex flex_x_between flex_y_center marbm_20">
        <div>
          <span style="margin-right: 26px">销售客单价</span>
          <span class="color_999">在店铺内平均每次销售金额达到指定范围的客户</span>
        </div>
        <i class="el-icon-close" @click="triggerDeleteClick(triggerShow.SalePassengerStartPriceShow.Code)"></i>
      </div>
      <div class="dis_flex flex_y_center">
        <span style="margin-right: 40px">选择金额</span>
        <el-input
          v-model="sectionData.SalePassengerStartPrice"
          style="width: 140px; height: 34px"
          size="small"
          min="0"
          @blur="handlerAgeInput"
          type="number"
          placeholder="请输入"
        ></el-input>
        <span style="margin: 0 15px">-</span>
        <el-input
          v-model="sectionData.SalePassengerEndPrice"
          style="width: 140px; height: 34px"
          size="small"
          min="0"
          @blur="handlerAgeInput"
          type="number"
          placeholder="请输入"
        ></el-input>
        <span class="marlt_10">次</span>
      </div>
    </el-card>

    <!-- 消耗金额 -->
    <el-card v-show="triggerShow.ConsumeStartMoneyShow.Code == 'Treat_Amount'" class="box-card" shadow="never">
      <div class="dis_flex flex_x_between flex_y_center marbm_20">
        <div>
          <span style="margin-right: 40px">消耗金额</span>
          <span class="color_999">在店铺内成功消耗总金额达到指定范围的客户</span>
        </div>
        <i class="el-icon-close" @click="triggerDeleteClick(triggerShow.ConsumeStartMoneyShow.Code)"></i>
      </div>
      <div class="dis_flex flex_y_center">
        <span style="margin-right: 40px">选择金额</span>
        <el-input v-model="sectionData.ConsumeStartMoney" @blur="handlerAgeInput" style="width: 140px; height: 34px" size="small" min="0" type="number" placeholder="请输入"></el-input>
        <span style="margin: 0 15px">-</span>
        <el-input v-model="sectionData.ConsumeEndMoney" @blur="handlerAgeInput" style="width: 140px; height: 34px" size="small" min="0" type="number" placeholder="请输入"></el-input>
        <span class="marlt_10">元</span>
      </div>
    </el-card>
    <!-- 消耗次数 -->
    <el-card v-show="triggerShow.ConsumeStartSecondShow.Code == 'Treat_Count'" class="box-card" shadow="never">
      <div class="dis_flex flex_x_between flex_y_center marbm_20">
        <div>
          <span style="margin-right: 40px">消耗次数</span>
          <span class="color_999">在店铺内成功消耗总次数达到指定范围的客户</span>
        </div>
        <i class="el-icon-close" @click="triggerDeleteClick(triggerShow.ConsumeStartSecondShow.Code)"></i>
      </div>
      <div class="dis_flex flex_y_center">
        <span style="margin-right: 40px">选择次数</span>
        <el-input
          v-model="sectionData.ConsumeStartSecond"
          style="width: 140px; height: 34px"
          size="small"
          min="0"
          @blur="handlerAgeInput"
          type="number"
          placeholder="请输入"
        ></el-input>
        <span style="margin: 0 15px">-</span>
        <el-input v-model="sectionData.ConsumeEndSecond" @blur="handlerAgeInput" style="width: 140px; height: 34px" size="small" min="0" type="number" placeholder="请输入"></el-input>
        <span class="marlt_10">次</span>
      </div>
    </el-card>
    <!-- 消耗客单价 -->
    <el-card v-show="triggerShow.ConsumePassengerStartPriceShow.Code == 'Treat_Average'" class="box-card" shadow="never">
      <div class="dis_flex flex_x_between flex_y_center marbm_20">
        <div>
          <span style="margin-right: 26px">消耗客单价</span>
          <span class="color_999">在店铺内平均每次消耗次数达到指定范围的客户</span>
        </div>
        <i class="el-icon-close" @click="triggerDeleteClick(triggerShow.ConsumePassengerStartPriceShow.Code)"></i>
      </div>
      <div class="dis_flex flex_y_center">
        <span style="margin-right: 40px">选择金额</span>
        <el-input
          v-model="sectionData.ConsumePassengerStartPrice"
          style="width: 140px; height: 34px"
          size="small"
          min="0"
          @blur="handlerAgeInput"
          type="number"
          placeholder="请输入"
        ></el-input>
        <span style="margin: 0 15px">-</span>
        <el-input
          v-model="sectionData.ConsumePassengerEndPrice"
          style="width: 140px; height: 34px"
          size="small"
          min="0"
          @blur="handlerAgeInput"
          type="number"
          placeholder="请输入"
        ></el-input>
        <span class="marlt_10">元</span>
      </div>
    </el-card>

    <!-- 余额 -->
    <el-card v-show="triggerShow.BalanceShow.Code == 'Card_Balance_Amount'" class="box-card" shadow="never">
      <div class="dis_flex flex_x_between flex_y_center marbm_20">
        <div>
          <span style="margin-right: 67px">余额</span>
          <span class="color_999">客户的账户余额在指定范围的客户</span>
        </div>
        <i class="el-icon-close" @click="triggerDeleteClick(triggerShow.BalanceShow.Code)"></i>
      </div>
      <div class="dis_flex flex_y_center">
        <span style="margin-right: 40px">选择金额</span>
        <el-input v-model="sectionData.StartMoney" @blur="handlerAgeInput" style="width: 140px; height: 34px" size="small" min="0" type="number" placeholder="请输入"></el-input>
        <span style="margin: 0 15px">-</span>
        <el-input v-model="sectionData.EndMoney" @blur="handlerAgeInput" style="width: 140px; height: 34px" size="small" min="0" type="number" placeholder="请输入"></el-input>
        <span class="marlt_10">元</span>
      </div>
    </el-card>
    <!-- 剩余有效性 -->
    <el-card v-show="triggerShow.EffectiveShow.Code == 'Card_Expired_Day'" class="box-card" shadow="never">
      <div class="dis_flex flex_x_between flex_y_center marbm_20">
        <div>
          <span style="margin-right: 22px">剩余有效期</span>
          <span class="color_999">卡项(通用次卡，时效卡，储值卡，套餐卡)剩余有效期在指定范围的客户</span>
        </div>
        <i class="el-icon-close" @click="triggerDeleteClick(triggerShow.EffectiveShow.Code)"></i>
      </div>
      <div class="dis_flex flex_y_center">
        <span style="margin-right: 40px">选择时间</span>
        <el-input v-model="sectionData.effectiveStart" @blur="handlerAgeInput" style="width: 140px; height: 34px" size="small" min="0" type="number" placeholder="请输入"></el-input>
        <span style="margin: 0 15px">-</span>
        <el-input v-model="sectionData.effectiveEnd" @blur="handlerAgeInput" style="width: 140px; height: 34px" size="small" min="0" type="number" placeholder="请输入"></el-input>
        <span class="marlt_10">天</span>
      </div>
    </el-card>
    <!-- 剩余次数 -->
    <el-card v-show="triggerShow.FrequencyShow.Code == 'Card_Balance_Times'" class="box-card" shadow="never">
      <div class="dis_flex flex_x_between flex_y_center marbm_20">
        <div>
          <span style="margin-right: 22px">剩余次数</span>
          <span class="color_999">卡项(项目，通用次卡)剩余次数在指定范围的客户</span>
        </div>
        <i class="el-icon-close" @click="triggerDeleteClick(triggerShow.FrequencyShow.Code)"></i>
      </div>
      <div class="dis_flex flex_y_center">
        <span style="margin-right: 40px">选择次数</span>
        <el-input v-model="sectionData.frequencyStart" @blur="handlerAgeInput" style="width: 140px; height: 34px" size="small" min="0" type="number" placeholder="请输入"></el-input>
        <span style="margin: 0 15px">-</span>
        <el-input v-model="sectionData.frequencyEnd" @blur="handlerAgeInput" style="width: 140px; height: 34px" size="small" min="0" type="number" placeholder="请输入"></el-input>
        <span class="marlt_10">次</span>
      </div>
    </el-card>
  </div>
</template>

<script>
export default {
  name: "",
  components: {},
  props: {
    triggerShow: Object,
  },
  data() {
    return {
      sectionData: {
        customerStartTime: "", // 成为客户天数开始时间
        customerEndTime: "", // 成为客户天数结束
        SaleStartMoney: "", // 销售金额开始
        SaleEndMoney: "", // 销售金额结束
        SaleStartSecond: "", // 销售金额开始
        SaleEndSecond: "", // 销售金额结束
        SalePassengerStartPrice: "", // 销售客单价金额开始
        SalePassengerEndPrice: "", // 销售客单价金额结束

        ConsumeStartMoney: "", // 消耗金额开始
        ConsumeEndMoney: "", // 消耗金额结束
        ConsumeStartSecond: "", // 消耗次数开始
        ConsumeEndSecond: "", // 消耗次数结束
        ConsumePassengerStartPrice: "", // 消耗客单价开始
        ConsumePassengerEndPrice: "", // 消耗客单价结束

        StartMoney: "", // 余额开始
        EndMoney: "", // 余额结束
        effectiveStart: "", // 剩余有效期开始
        effectiveEnd: "", // 剩余有效期结束
        frequencyStart: "", // 剩余次数开始
        frequencyEnd: "", // 剩余次数结束
      },
    };
  },
  computed: {},
  watch: {},
  created() {},
  mounted() {},
  methods: {
    // 删除
    triggerDeleteClick(Code) {
      this.$emit("getChildChange", Code);
    },
    handlerAgeInput(){

      if(this.sectionData.customerStartTime !== "" && this.sectionData.customerEndTime !== ""){
        if(Number(this.sectionData.customerStartTime) > Number(this.sectionData.customerEndTime)){
          this.sectionData.customerStartTime = this.sectionData.customerEndTime
        }
      }

      if(this.sectionData.SaleStartMoney !== "" && this.sectionData.SaleEndMoney !== ""){
        if(Number(this.sectionData.SaleStartMoney) > Number(this.sectionData.SaleEndMoney)){
          this.sectionData.SaleStartMoney = this.sectionData.SaleEndMoney
        }
      }
       if(this.sectionData.SaleStartSecond !== "" && this.sectionData.SaleEndSecond !== ""){
        if(Number(this.sectionData.SaleStartSecond) > Number(this.sectionData.SaleEndSecond)){
          this.sectionData.SaleStartSecond = this.sectionData.SaleEndSecond
        }
      }
      if(this.sectionData.SalePassengerStartPrice !== "" && this.sectionData.SalePassengerEndPrice !== ""){
        if(Number(this.sectionData.SalePassengerStartPrice) > Number(this.sectionData.SalePassengerEndPrice)){
          this.sectionData.SalePassengerStartPrice = this.sectionData.SalePassengerEndPrice
        }
      }
      if(this.sectionData.ConsumeStartMoney !== "" && this.sectionData.ConsumeEndMoney !== ""){
        if(Number(this.sectionData.ConsumeStartMoney) > Number(this.sectionData.ConsumeEndMoney)){
          this.sectionData.ConsumeStartMoney = this.sectionData.ConsumeEndMoney
        }
      }
      if(this.sectionData.ConsumeStartSecond !== "" && this.sectionData.ConsumeEndSecond !== ""){
        if(Number(this.sectionData.ConsumeStartSecond) > Number(this.sectionData.ConsumeEndSecond)){
          this.sectionData.ConsumeStartSecond = this.sectionData.ConsumeEndSecond
        }
      }
      if(this.sectionData.ConsumePassengerStartPrice !== "" && this.sectionData.ConsumePassengerEndPrice !== ""){
        if(Number(this.sectionData.ConsumePassengerStartPrice) > Number(this.sectionData.ConsumePassengerEndPrice)){
          this.sectionData.ConsumePassengerStartPrice = this.sectionData.ConsumePassengerEndPrice
        }
      }
      if(this.sectionData.StartMoney !== "" && this.sectionData.EndMoney !== ""){
        if(Number(this.sectionData.StartMoney) > Number(this.sectionData.EndMoney)){
          this.sectionData.StartMoney = this.sectionData.EndMoney
        }
      }
       if(this.sectionData.effectiveStart !== "" && this.sectionData.effectiveEnd !== ""){
        if(Number(this.sectionData.effectiveStart) > Number(this.sectionData.effectiveEnd)){
          this.sectionData.effectiveStart = this.sectionData.effectiveEnd
        }
      }
       if(this.sectionData.frequencyStart !== "" && this.sectionData.frequencyEnd !== ""){
        if(Number(this.sectionData.frequencyStart) > Number(this.sectionData.frequencyEnd)){
          this.sectionData.frequencyStart = this.sectionData.frequencyEnd
        }
      }
    },
  },
};
</script>

<style scoped lang="less">

</style>
