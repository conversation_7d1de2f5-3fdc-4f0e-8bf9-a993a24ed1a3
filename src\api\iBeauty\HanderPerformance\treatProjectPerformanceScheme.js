/**
 * Created by wsf on 2022/01/07.
 * 项目消耗业绩 api
 */
 import * as API from '@/api/index'

 export default {
    // 获取员工项目消耗业绩方案列表
    getTreatProjectPerformanceScheme: params => {
        return API.POST('api/treatProjectPerformanceScheme/list', params)
    },
    // 保存员工项目消耗业绩方案
    createTreatProjectPerformanceScheme: params => {
        return API.POST('api/treatProjectPerformanceScheme/create', params)
    },
    // 删除员工项目消耗业绩方案
    deleteTreatProjectPerformanceScheme: params => {
        return API.POST('api/treatProjectPerformanceScheme/delete', params)
    },
    // 获取项目分类业绩 
    getTreatProjectCategoryPerformance: params => {
        return API.POST('api/treatProjectCategoryPerformance/all', params)
    },
    // 保存项目分类业绩
    updateTreatProjectCategoryPerformance: params => {
        return API.POST('api/treatProjectCategoryPerformance/update', params)
    },
    // 获取所有项目经手人业绩
    getTreatProjectSchemeHandlerPerformance: params => {
        return API.POST('api/treatProjectSchemeHandlerPerformance/all', params)
    },
    // 保存所有项目经手人业绩
    updateTreatProjectSchemeHandlerPerformance:  params => {
        return API.POST('api/treatProjectSchemeHandlerPerformance/update', params)
    },
    // 获取分类项目经手人业绩 
    getTreatProjectCategoryHandlerPerformance: params => {
        return API.POST('api/treatProjectCategoryHandlerPerformance/all', params)
    },
    // 保存分类项目经手人业绩
    updateTreatProjectCategoryHandlerPerformance: params => {
        return API.POST('api/treatProjectCategoryHandlerPerformance/update', params)
    },
    // 获取项目业绩
    getTreatProjectPerformance: params => {
        return API.POST('api/treatProjectPerformance/all', params)
    },
    // 保存项目业绩
    updateTreatProjectPerformance: params => {
        return API.POST('api/treatProjectPerformance/update', params)
    },
    // 获取项目经手人业绩
    getTreatProjectHandlerPerformance: params => {
        return API.POST('api/treatProjectHandlerPerformance/all', params)
    },
    // 保存项目经手人业绩
    updateTreatProjectHandlerPerformance: params => {
        return API.POST('api/treatProjectHandlerPerformance/update', params)
    },
 }