<template>
  <div class="EntityInfo">
    <div class="entity-info-box">
      <el-carousel height="200px" arrow="never">
        <el-carousel-item v-for="(item, index) in onlineEntityInfo.PhotoURLList" :key="index">
          <el-image class="" style="width: 100%; height: 100%" :src="item.PhotoURL" fit="cover"></el-image>
        </el-carousel-item>
      </el-carousel>
      <div class="info-box">
        <div class="image-box">
          <el-image class="logo" fit="cover" :src="onlineEntityInfo.EntityLogoURL"></el-image>
        </div>
        <div class="info">
          <div
            v-if="
              ConfigProperty.some((val) => {
                return val == 'Name';
              })
            "
            class="info-content"
            style="font-size: 18px"
          >
            {{ onlineEntityInfo.EntityName }}
          </div>

          <div class="info-content">
            <div>
              <el-image style="width: 11px; height: 11px" :src="require('../../../../assets/img/store/<EMAIL>')"></el-image>
              <span class="font_14 color_666 marlt_5"
                >营业时间： {{ formatBusinessDate() }} {{ onlineEntityInfo.BusinessStartTime }} -- {{ onlineEntityInfo.BusinessEndTime }}
              </span>
            </div>
          </div>

          <div
            v-if="
              ConfigProperty.some((val) => {
                return val == 'Phone';
              })
            "
            class="info-content"
          >
            <div>
              <el-image style="width: 11px; height: 11px" :src="require('../../../../assets/img/store/<EMAIL>')"></el-image>
              <span class="font_14 color_666 marlt_5">服务电话：{{ onlineEntityInfo.ServiceTelephoneNumber }}</span>
            </div>
            <div class="line dis_flex flex_y_center pad_0_10">
              <el-image style="width: 11px; height: 15px" :src="require('../../../../assets/img/store/phone.png')"></el-image>
            </div>
          </div>

          <div
            v-if="
              ConfigProperty.some((val) => {
                return val == 'Address';
              })
            "
            class="info-content"
          >
            <div>
              <el-image style="width: 9px; height: 12px" :src="require('../../../../assets/img/store/<EMAIL>')"></el-image>
              <span class="font_14 color_666 marlt_5">门店地址：{{ onlineEntityInfo.AddressDetail }}</span>
            </div>
            <div class="line dis_flex flex_y_center pad_0_10">
              <el-image style="width: 11px; height: 16px" :src="require('../../../../assets/img/store/<EMAIL>')"></el-image>
            </div>
          </div>

          <div
            v-if="
              ConfigProperty.some((val) => {
                return val == 'Describe';
              })
            "
            class="info-content"
          >
            简介:{{ onlineEntityInfo.Description }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import API from "@/api/Marketing/EShopManage/EShopInfo";
export default {
  name: "EntityInfo",
  props: {
    ConfigProperty: {
      type: Array,
      default: () => {
        return ["Name", "Address", "Phone", "Describe"];
      },
    },
  },
  /**  引入的组件  */
  components: {},
  /**  Vue 实例的数据对象**/
  data() {
    return {
      onlineEntityInfo: "",
    };
  },
  /**计算属性  */
  computed: {},
  /**  方法集合  */
  methods: {
    /**  获取网店信息  */
    getOnlineEntityInfo() {
      var that = this;
      that.loading = true;
      API.getOnlineEntityInfo()
        .then((res) => {
          if (res.StateCode == 200) {
            that.onlineEntityInfo = res.Data;
            that.$emit("getEntityName", that.onlineEntityInfo.EntityName);

            // this.formatBusinessDate();
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },

    /**  处理营业时间  */
    formatBusinessDate() {
      let that = this;
      let businessWeekStr = "";
      var arr = that.onlineEntityInfo ? that.onlineEntityInfo.EntityBusinessDate.map((val) => val.Data) : [];
      var result = [];
      var tmp;
      while ((tmp = arr.shift())) {
        if (result.length == 0) {
          result.push([tmp]);
          continue;
        }
        var e = result[result.length - 1];
        if (tmp == e[e.length - 1] + 1) {
          e.push(tmp);
        } else {
          result.push([tmp]);
        }
      }

      for (let index = 0; index < result.length; index++) {
        const element = result[index];
        if (element.length >= 2) {
          businessWeekStr = businessWeekStr + that.toWeekName(element[0] - 1) + "至" + that.toWeekName(element[element.length - 1] - 1) + "、";
        }
        if (element.length == 1) {
          businessWeekStr = businessWeekStr + that.toWeekName(element[0] - 1) + "、";
        }
      }
      return businessWeekStr.slice(0, businessWeekStr.length - 1);
    },
    /**  将数字转换成周几  */
    toWeekName(num) {
      let weekday = new Array(7);
      weekday[0] = "周一";
      weekday[1] = "周二";
      weekday[2] = "周三";
      weekday[3] = "周四";
      weekday[4] = "周五";
      weekday[5] = "周六";
      weekday[6] = "周日";
      return weekday[num];
    },
  },
  /** 监听数据变化   */
  watch: {},
  /** 创建实例之前执行函数   */
  beforeCreate() {},
  /**  实例创建完成之后  */
  created() {
    this.getOnlineEntityInfo();
  },
  /**  在挂载开始之前被调用  */
  beforeMount() {},
  /**  实例被挂载后调用  */
  mounted() {},
  /**  数据更新 之前 调用  */
  beforeUpdate() {},
  /** 数据更新 完成 调用   */
  updated() {},
  /**  实例销毁之前调用  */
  beforeDestroy() {},
  /**  实例销毁后调用  */
  destroyed() {},
};
</script>

<style lang="scss">
.EntityInfo {
  .entity-info-box {
    .info-box {
      background: #ffffff;
      .image-box {
        position: relative;
        height: 25px;
        .logo {
          width: 50px;
          height: 50px;
          z-index: 9;
          top: -25px;
          right: 24px;
          position: absolute;
          border-radius: 6px;
          border: 1px solid #eeeeee;
        }
      }
      .info {
        position: relative;
        // top: 25px;
        .info-content {
          font-size: 14px;
          color: #333333;
          padding: 10px;
          border-bottom: 1px solid #eeeeee;
          display: flex;
          justify-content: space-between;
        }
      }
    }
  }
  .el-carousel__indicator--horizontal {
    display: inline-block;
    padding: 12px 4px;
    .el-carousel__button {
      width: 6px;
      height: 6px;
      border-radius: 6px;
    }
  }
}
</style>
