<template>
  <div class="content_body StorageConfirmDetail">
    <el-card>
      <div slot="header" class="clearfix">
        <span>采购入库确认 - {{ storageInfo.ID }}</span>
        <el-button style="float: right; padding: 3px 0" type="text" @click="goBack">返回列表</el-button>
      </div>

      <!-- 基本信息展示 -->
      <div class="info-section">
        <h3>基本信息</h3>
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="info-item">
              <label>入库日期：</label>
              <span>{{ storageInfo.InDate }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>供应商：</label>
              <span>{{ storageInfo.SupplierName }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>仓库/门店：</label>
              <span>{{ storageInfo.EntityName }}</span>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="info-item">
              <label>制单人：</label>
              <span>{{ storageInfo.EmployeeName }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>制单时间：</label>
              <span>{{ storageInfo.CreatedOn }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>单据状态：</label>
              <el-tag :type="getStatusTagType(storageInfo.BillStatus)">{{ storageInfo.BillStatusName }}</el-tag>
            </div>
          </el-col>
        </el-row>
        <el-row v-if="storageInfo.Remark">
          <el-col :span="24">
            <div class="info-item">
              <label>备注：</label>
              <span>{{ storageInfo.Remark }}</span>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 产品明细确认表格 -->
      <div class="product-section">
        <h3>产品明细确认</h3>
        <div class="table-toolbar">
          <el-button size="small" @click="selectAll">全选</el-button>
          <el-button size="small" @click="selectNone">取消全选</el-button>
          <el-button size="small" @click="batchSetQuantity" :disabled="selectedProducts.length === 0">批量设置数量</el-button>
        </div>
        
        <el-table
          ref="productTable"
          :data="productList"
          border
          @selection-change="handleSelectionChange"
          v-loading="loading">
          <el-table-column type="selection" width="55" />
          <el-table-column prop="ProductName" label="产品名称" min-width="120" />
          <el-table-column prop="Specification" label="规格" width="100" />
          <el-table-column prop="UnitName" label="单位" width="80" />
          <el-table-column prop="UnitPrice" label="单价" width="100">
            <template slot-scope="scope">
              ¥{{ scope.row.UnitPrice }}
            </template>
          </el-table-column>
          <el-table-column prop="Quantity" label="计划数量" width="100" />
          <el-table-column prop="ActualQuantity" label="已入库数量" width="120" />
          <el-table-column prop="RemainingQuantity" label="剩余数量" width="100" />
          <el-table-column label="本次入库数量" width="150">
            <template slot-scope="scope">
              <el-input-number
                v-model="scope.row.ThisTimeQuantity"
                :min="0"
                :max="scope.row.RemainingQuantity"
                :precision="0"
                size="small"
                @change="calculateThisTimeAmount(scope.row)"
                :class="{ 'input-error': scope.row.quantityError }"
              />
              <div v-if="scope.row.quantityError" class="error-message">{{ scope.row.quantityError }}</div>
            </template>
          </el-table-column>
          <el-table-column prop="Amount" label="计划金额" width="120">
            <template slot-scope="scope">
              ¥{{ scope.row.Amount }}
            </template>
          </el-table-column>
          <el-table-column label="本次入库金额" width="120">
            <template slot-scope="scope">
              ¥{{ scope.row.ThisTimeAmount || 0 }}
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 确认备注 -->
      <div class="remark-section">
        <h3>确认备注</h3>
        <el-input
          v-model="confirmForm.Remark"
          type="textarea"
          :rows="3"
          placeholder="请输入确认备注（可选）"
          maxlength="500"
          show-word-limit
        />
      </div>

      <!-- 操作按钮 -->
      <div class="button-section">
        <el-button @click="goBack">返回</el-button>
        <el-button type="primary" @click="confirmStorage" :loading="confirmLoading">确认入库</el-button>
      </div>
    </el-card>

    <!-- 批量设置数量对话框 -->
    <el-dialog title="批量设置数量" :visible.sync="batchDialogVisible" width="400px">
      <el-form :model="batchForm" label-width="120px">
        <el-form-item label="本次入库数量">
          <el-input-number
            v-model="batchForm.quantity"
            :min="0"
            :precision="0"
            placeholder="请输入数量"
          />
        </el-form-item>
        <el-form-item>
          <el-checkbox v-model="batchForm.useRemaining">使用剩余数量</el-checkbox>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="batchDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmBatchSet">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import APIStorage from "@/api/PSI/Purchase/storage";

export default {
  name: 'StorageConfirmDetail',
  data() {
    return {
      loading: false,
      confirmLoading: false,
      storageInfo: {},
      productList: [],
      selectedProducts: [],
      confirmForm: {
        PurchaseStorageID: '',
        Remark: '',
        ProductList: []
      },
      batchDialogVisible: false,
      batchForm: {
        quantity: 0,
        useRemaining: false
      }
    }
  },
  computed: {
    storageId() {
      return this.$route.query.id;
    }
  },
  methods: {
    // 获取待确认入库单详情
    async getPendingInfo() {
      if (!this.storageId) {
        this.$message.error('缺少入库单号参数');
        this.goBack();
        return;
      }

      this.loading = true;
      try {
        const response = await APIStorage.pendingInfo({ ID: this.storageId });
        if (response.StateCode === 200) {
          this.storageInfo = response.Data;
          this.productList = (response.Data.Detail || []).map(item => ({
            ...item,
            ThisTimeQuantity: 0,
            ThisTimeMinimumUnitQuantity: 0,
            ThisTimeAmount: 0,
            IsSelected: false,
            quantityError: ''
          }));
          this.confirmForm.PurchaseStorageID = response.Data.ID;
        } else {
          this.$message.error(response.Message || '获取入库单详情失败');
          this.goBack();
        }
      } catch (error) {
        console.error('获取入库单详情失败:', error);
        this.$message.error('获取入库单详情失败，请稍后重试');
        this.goBack();
      } finally {
        this.loading = false;
      }
    },

    // 处理表格选择变化
    handleSelectionChange(selection) {
      this.selectedProducts = selection;
      // 更新选中状态
      this.productList.forEach(item => {
        item.IsSelected = selection.some(selected => selected.ID === item.ID);
      });
    },

    // 全选
    selectAll() {
      this.$refs.productTable && this.$refs.productTable.toggleAllSelection();
    },

    // 取消全选
    selectNone() {
      this.$refs.productTable && this.$refs.productTable.clearSelection();
    },

    // 批量设置数量
    batchSetQuantity() {
      if (this.selectedProducts.length === 0) {
        this.$message.warning('请先选择要设置的产品');
        return;
      }
      this.batchForm.quantity = 0;
      this.batchForm.useRemaining = false;
      this.batchDialogVisible = true;
    },

    // 确认批量设置
    confirmBatchSet() {
      const quantity = this.batchForm.useRemaining ? null : this.batchForm.quantity;
      
      this.selectedProducts.forEach(product => {
        const targetQuantity = this.batchForm.useRemaining ? product.RemainingQuantity : quantity;
        if (targetQuantity >= 0 && targetQuantity <= product.RemainingQuantity) {
          product.ThisTimeQuantity = targetQuantity;
          this.calculateThisTimeAmount(product);
        }
      });
      
      this.batchDialogVisible = false;
      this.$message.success('批量设置完成');
    },

    // 计算本次入库金额
    calculateThisTimeAmount(row) {
      // 清除之前的错误信息
      row.quantityError = '';
      
      // 验证数量
      if (row.ThisTimeQuantity < 0) {
        row.quantityError = '数量不能小于0';
        return;
      }
      
      if (row.ThisTimeQuantity > row.RemainingQuantity) {
        row.quantityError = '不能超过剩余数量';
        return;
      }

      // 计算金额和最小单位数量
      row.ThisTimeAmount = (row.ThisTimeQuantity * row.UnitPrice).toFixed(2);
      row.ThisTimeMinimumUnitQuantity = row.ThisTimeQuantity * (row.ConversionRate || 1);
    },

    // 获取状态标签类型
    getStatusTagType(status) {
      const statusMap = {
        '10': 'warning',  // 待确认 - 橙色
        '15': 'primary',  // 部分确认 - 蓝色
        '20': 'success'   // 已确认 - 绿色
      };
      return statusMap[status] || 'info';
    },

    // 确认入库
    async confirmStorage() {
      // 验证是否有选中的产品
      if (this.selectedProducts.length === 0) {
        this.$message.warning('请至少选择一个产品进行确认');
        return;
      }

      // 验证选中产品的本次入库数量
      let hasError = false;
      for (let product of this.selectedProducts) {
        if (!product.ThisTimeQuantity || product.ThisTimeQuantity <= 0) {
          this.$message.error(`产品 ${product.ProductName} 的本次入库数量必须大于0`);
          hasError = true;
          break;
        }
        if (product.ThisTimeQuantity > product.RemainingQuantity) {
          this.$message.error(`产品 ${product.ProductName} 的本次入库数量不能超过剩余数量`);
          hasError = true;
          break;
        }
      }

      if (hasError) return;

      // 构建确认数据
      this.confirmForm.ProductList = this.productList.map(item => ({
        ID: item.ID,
        IsSelected: item.IsSelected,
        ThisTimeQuantity: item.ThisTimeQuantity || 0,
        ThisTimeMinimumUnitQuantity: item.ThisTimeMinimumUnitQuantity || 0,
        ThisTimeAmount: parseFloat(item.ThisTimeAmount || 0)
      }));

      this.confirmLoading = true;
      try {
        const response = await APIStorage.confirm(this.confirmForm);
        if (response.StateCode === 200) {
          this.$message.success('入库确认成功');
          this.goBack();
        } else {
          this.$message.error(response.Message || '入库确认失败');
        }
      } catch (error) {
        console.error('入库确认失败:', error);
        this.$message.error('入库确认失败，请稍后重试');
      } finally {
        this.confirmLoading = false;
      }
    },

    // 返回列表
    goBack() {
      // 返回到入库确认列表页面
      this.$router.go(-1);
    }
  },

  mounted() {
    this.getPendingInfo();
  }
}
</script>

<style lang="scss" scoped>
.StorageConfirmDetail {
  .info-section, .product-section, .remark-section {
    margin-bottom: 20px;

    h3 {
      margin-bottom: 15px;
      color: #303133;
      font-size: 16px;
      font-weight: 600;
      border-bottom: 2px solid #409EFF;
      padding-bottom: 8px;
    }
  }

  .info-item {
    margin-bottom: 10px;

    label {
      font-weight: 600;
      color: #606266;
      margin-right: 8px;
    }

    span {
      color: #303133;
    }
  }

  .table-toolbar {
    margin-bottom: 10px;

    .el-button {
      margin-right: 10px;
    }
  }

  .button-section {
    text-align: center;
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #EBEEF5;

    .el-button {
      margin: 0 10px;
      min-width: 100px;
    }
  }

  .input-error {
    .el-input__inner {
      border-color: #F56C6C;
    }
  }

  .error-message {
    color: #F56C6C;
    font-size: 12px;
    margin-top: 2px;
    line-height: 1;
  }

  .el-table {
    .el-input-number {
      width: 120px;
    }
  }

  .dialog-footer {
    text-align: right;
  }
}
</style>
