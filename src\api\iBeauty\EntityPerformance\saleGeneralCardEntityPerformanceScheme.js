/**
 * Created by wsf on 2022/01/11.
 * 门店业绩 门店通用次卡销售业绩api
 */
 import * as API  from '@/api/index'

 export default {
  // 获取门店通用次卡销售业绩方案列表
  getGeneralCardEntityPerformanceScheme: params => {
     return API.POST('api/saleGeneralCardEntityPerformanceScheme/list', params)
  },
  // 保存门店通用次卡销售业绩方案
  createGeneralCardEntityPerformanceScheme: params => {
      return API.POST('api/saleGeneralCardEntityPerformanceScheme/create', params)
  },
  // 删除门店通用次卡销售业绩方案
  deleteGeneralCardEntityPerformanceScheme: params => {
      return API.POST('api/saleGeneralCardEntityPerformanceScheme/delete', params)
  },
  // 获取分类通用次卡业绩
  getGeneralCardCategoryEntityPerformance: params => {
      return API.POST('api/saleGeneralCardCategoryEntityPerformance/all', params)
  },
  // 保存分类通用次卡业绩
  updateGeneralCardCategoryEntityPerformance: params => {
      return API.POST('api/saleGeneralCardCategoryEntityPerformance/update', params)
  },
  // 获取通用次卡业绩
  getGeneralCardEntityPerformance: params => {
      return API.POST('api/saleGeneralCardEntityPerformance/all', params)
  },
  // 保存通用次卡业绩
  updateGeneralCardEntityPerformance: params => {
      return API.POST('api/saleGeneralCardEntityPerformance/update', params)
  }
 }