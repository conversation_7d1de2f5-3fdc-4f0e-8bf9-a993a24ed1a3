<template>
  <div class="projectConsumable content_body">
    <el-container style="height: 100%">
      <el-aside width:280px>
        <el-scrollbar class="categoryScrollbar">
          <div class="dis_flex flex_y_center pad_10" style="border-bottom: 1px solid #e4e6e9">
            <el-input
              class="marrt_10"
              size="small"
              placeholder="搜索耗材模板分类"
              prefix-icon="el-icon-search"
              v-model.trim="CategorySearchKey"
              clearable
              @keyup.enter.native="handleCategoryQuery"
              @clear="handleCategoryQuery"
            >
            </el-input>
            <i @click="addConsumableCategoryModal" class="el-icon-plus"></i>
          </div>
          <el-tree
            class="padtp_10"
            :expand-on-click-node="false"
            node-key="ID"
            :data="ConsumableCategory"
            :auto-expand-parent="true"
            :props="defaultProps"
            :default-expanded-keys="ConsumableCategoryDefaultExpandedKeys"
            @node-click="handleNodeClick"
          >
            <div slot-scope="{ data }" class="dis_flex flex_x_between pad_5_10 consumableeditItem" style="width: 100%">
              <div class="font_14 flex_box">{{ data.Name }}</div>
              <i class="el-icon-edit consumableedit" @click="editConsumableCategoryModal(data)"></i>
            </div>
          </el-tree>
        </el-scrollbar>
      </el-aside>
      <el-container>
        <el-header style="height: auto; padding: 0px">
          <div class="nav_header martp_15">
            <el-row>
              <el-col :span="22">
                <el-form inline size="small" label-width="80px">
                  <el-form-item label="模板名称" prop="Name">
                    <el-input
                      size="small"
                      v-model="SearchConsumable.Name"
                      placeholder="输入模板名称"
                      @change="handleConsumableTemplate"
                      clearable=""
                    ></el-input>
                  </el-form-item>
                  <el-form-item label="有效性" prop="Active">
                    <el-select
                      v-model="SearchConsumable.Active"
                      placeholder="请选择有效性"
                      size="small"
                      filterable
                      clearable
                      @change="handleConsumableTemplate"
                    >
                      <el-option label="有效" :value="true"> </el-option>
                      <el-option label="无效" :value="false"> </el-option>
                    </el-select>
                  </el-form-item>
                  <el-form-item>
                    <el-button @click="handleConsumableTemplate" type="primary" size="small" v-prevent-click>搜索</el-button>
                  </el-form-item>
                </el-form>
              </el-col>
              <el-col :span="2" class="text_right">
                <el-button @click="addConsumableTemplate" type="primary" size="small" v-prevent-click>新增</el-button>
              </el-col>
            </el-row>
          </div>
        </el-header>
        <el-main style="padding: 0px">
          <div>
            <el-table size="small" :data="tableData" tooltip-effect="light" v-loading="detailLoading">
              <el-table-column prop="Name" label="模板名称"></el-table-column>
              <el-table-column prop="ProjectConsumableCategoryName" label="模板分类"></el-table-column>
              <el-table-column prop="Active" label="有效性">
                <template slot-scope="scope">
                  {{ scope.row.Active ? "有效" : "无效" }}
                </template>
              </el-table-column>
              <el-table-column label="操作" width="80px">
                <template slot-scope="scope">
                  <el-button type="primary" size="small" @click="editConsumableTemplate(scope.row)" v-prevent-click>编辑</el-button>
                  <!-- <el-button type="danger" size="small" @click="removeConsumableTemplate(scope.row)" v-prevent-click>删除</el-button> -->
                </template>
              </el-table-column>
            </el-table>
          </div>
          <div class="text_right" v-if="paginations.total > 0">
            <el-pagination
              background
              @current-change="handleCurrentChange"
              :current-page.sync="paginations.page"
              :page-size="paginations.page_size"
              :page-sizes="[10, 15, 20, 30, 40, 50]"
              :layout="paginations.layout"
              :total="paginations.total"
            ></el-pagination>
          </div>
        </el-main>
      </el-container>
    </el-container>
    <!-- 新增模板 -->
    <el-dialog :title="isAdd ? '新增模板' : '编辑模板'" :visible.sync="consumableVisible" width="800px">
      <el-tabs v-model="activeName">
        <el-tab-pane name="Info" label="基本信息">
          <el-form size="small" :model="addRuleForm" :rules="addRules" ref="addRuleFormRef">
            <el-form-item label="模板名称" prop="Name">
              <el-input v-model="addRuleForm.Name" placeholder="请输入" clearable></el-input>
            </el-form-item>
            <el-form-item label="模板分类" prop="ProjectConsumableCategoryID">
              <el-select v-model="addRuleForm.ProjectConsumableCategoryID" clearable>
                <el-option v-for="item in CategoryList" :key="item.ID" :label="item.Name" :value="item.ID"></el-option>
              </el-select>
            </el-form-item>

            <el-form-item v-if="!isAdd" label="有效性" required>
              <el-radio-group v-model="addRuleForm.Active">
                <el-radio :label="true">有效</el-radio>
                <el-radio :label="false">无效</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-form>
        </el-tab-pane>
        <el-tab-pane name="ConsumableProject" label="适用项目">
          <el-row :gutter="20" class="marbm_10">
            <el-col :span="6">
              <el-input v-model="selctProjectName" size="small" placeholder="输入项目名/分类搜索" clearable></el-input>
            </el-col>
            <el-col :span="18">
              <el-button size="small" type="primary" @click="selectConsumableProject" v-prevent-click>添加项目</el-button>
            </el-col>
          </el-row>
          <el-table
            size="small"
            :data="addRuleForm.ProjectListAll.filter((data) => !selctProjectName || data.Name.toLowerCase().includes(selctProjectName.toLowerCase()))"
            max-height="500px"
          >
            <el-table-column label="项目名称/分类名称">
              <template slot-scope="scope">
                {{ scope.row.Name }}
                <el-tag type="info" v-if="!scope.row.IsProject" size="mini">分类</el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="80">
              <template slot-scope="scope">
                <el-button type="danger" size="small" @click="removeConsumableTemplateProject(scope.row, scope.$index)" v-prevent-click>删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
        <el-tab-pane name="ConsumableProduct" label="使用耗材">
          <el-row :gutter="20" class="marbm_10">
            <el-col :span="6">
              <el-input v-model="selectProductName" clearable size="small" placeholder="请输入耗材名称搜索"></el-input>
            </el-col>
            <el-col :span="18">
              <el-button size="small" type="primary" @click="selectConsumableProduct" v-prevent-click>配置耗材</el-button>
            </el-col>
          </el-row>
          <el-table
            size="small"
            :data="addRuleForm.ProductList.filter((i) => !selectProductName || i.ProductName.toLowerCase().includes(selectProductName.toLowerCase()))"
            max-height="500px"
          >
            <el-table-column label="产品" prop="ProductName"></el-table-column>
            <el-table-column label="品牌" prop="BrandName"></el-table-column>
            <el-table-column label="规格" prop="Specification"></el-table-column>
            <el-table-column label="最小包装单位" prop="MinimumUnitName"></el-table-column>
            <el-table-column label="标准量">
              <template slot-scope="scope">
                <el-input v-model="scope.row.MinimumUnitQuantity" size="small" v-input-fixed="0"></el-input>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="80">
              <template slot-scope="scope">
                <el-button type="danger" size="small" @click="removeConsumableTemplateProduct(scope.row, scope.$index)" v-prevent-click>删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
      </el-tabs>
      <span slot="footer">
        <el-button size="small" @click="consumableVisible = false" v-prevent-click>取 消</el-button>
        <el-button size="small" type="primary" @click="saveConsumableTempplate" v-prevent-click :loading="addLoading">确 定</el-button>
      </span>
    </el-dialog>
    <!-- 新增分类 -->
    <el-dialog :title="isAddCategory ? '新增分类' : '编辑分类'" :visible.sync="addCategoryVisible" width="500px">
      <el-form :model="addCategoryRuleForm" :rules="addCategoryRules" size="small" ref="addCategoryRef" label-width="90px">
        <el-form-item label="分类名称" prop="Name">
          <el-input v-model="addCategoryRuleForm.Name"></el-input>
        </el-form-item>
        <el-form-item label="上级分类" prop="ParentID">
          <el-select v-model="addCategoryRuleForm.ParentID" clearable>
            <el-option v-for="item in CategoryList" :key="item.ID" :label="item.Name" :value="item.ID"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item v-if="!isAddCategory" label="有效性" required>
          <el-radio-group v-model="addCategoryRuleForm.Active">
            <el-radio :label="true">有效</el-radio>
            <el-radio :label="false">无效</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="addCategoryVisible = false" v-prevent-click size="small">取 消</el-button>
        <el-button type="primary" size="small" @click="submitCreateCategory" v-prevent-click :loading="addCategoryLoading">确 定</el-button>
      </span>
    </el-dialog>

    <!--适用项目-->
    <el-dialog title="配置适用项目" :visible.sync="projectVisible" width="900px">
      <div>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-input v-model="projectName" placeholder="输入关键字进行搜索" clearable size="small"></el-input>
            <el-scrollbar class="el-scrollbar_height">
              <el-tree
                ref="projectTree"
                :expand-on-click-node="false"
                :check-on-click-node="true"
                :check-strictly="true"
                :data="projectCategorys"
                show-checkbox
                accordion
                node-key="PID"
                :default-checked-keys="defaultProjectCheckedKeys"
                :props="{ children: 'Child', label: 'Name' }"
                :filter-node-method="filterNode"
                @check="changeProjectCheckedData"
              >
                <span slot-scope="{ data }">
                  <span>{{ data.Name }}</span>
                  <el-tag class="marlt_5" type="info" size="mini" v-if="!data.IsProject">分类</el-tag>
                </span>
              </el-tree>
            </el-scrollbar>
          </el-col>
          <el-col :span="16">
            <el-table
              size="small"
              :data="defaultProjectCheckedKeys.filter((data) => !projectName || data.Name.toLowerCase().includes(projectName.toLowerCase()))"
              max-height="500px"
            >
              <el-table-column prop="Name" label="项目名称/分类名称">
                <template slot-scope="scope">
                  {{ scope.row.Name }}
                  <el-tag type="info" v-if="!scope.row.IsProject" size="mini">分类</el-tag>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="80px">
                <template slot-scope="scope">
                  <el-button type="danger" size="small" @click="removetConsumableProject(scope.row, scope.$index)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-col>
        </el-row>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="projectVisible = false" v-prevent-click>取 消</el-button>
        <el-button size="small" type="primary" @click="addProjectSelect" v-prevent-click>确 定</el-button>
      </div>
    </el-dialog>

    <!-- 选择商品弹窗 -->
    <el-dialog title="选择商品" :visible.sync="showProductVisible" :close-on-click-modal="false">
      <el-row>
        <el-col :span="8" class="pad_10_0">
          <el-input
            size="small"
            v-model="ProductName"
            clearable
            @clear="handleSearchProductClick"
            @keyup.enter.native="handleSearchProductClick"
            placeholder="输入商品名称搜索"
          ></el-input>
        </el-col>
        <el-col :span="2" class="pad_10_0 marlt_10">
          <el-button @click="handleSearchProductClick" size="small" type="primary" v-prevent-click>搜索</el-button>
        </el-col>
      </el-row>
      <div>
        <el-table
          size="small"
          :data="ProductList"
          max-height="480px"
          :row-key="(row) => row.ProductID"
          @selection-change="changeSelectProduct"
          ref="productTable"
        >
          <el-table-column type="selection" width="55" :reserve-selection="true"></el-table-column>
          <el-table-column prop="ProductName" label="商品名称">
            <template slot-scope="scope">
              {{ scope.row.ProductName }}
              <el-tag v-if="scope.row.IsLock" type="warning" size="small">库存盘点中</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="Specification" label="产品规格"></el-table-column>
          <el-table-column prop="PCategoryName" label="品牌名称"></el-table-column>
          <!-- <el-table-column prop="Quantity" label="可用库存"></el-table-column> -->
          <el-table-column prop="MinimumUnitName" label="最小包装单位"></el-table-column>
        </el-table>
        <div class="pad_15 text_right">
          <el-pagination
            background
            v-if="ProductPaginations.total > 0"
            @current-change="handleProductChange"
            :current-page.sync="ProductPaginations.page"
            :page-size="ProductPaginations.page_size"
            :layout="ProductPaginations.layout"
            :total="ProductPaginations.total"
          ></el-pagination>
        </div>
      </div>

      <span slot="footer" class="dialog-footer">
        <el-button @click="showProductVisible = false" v-prevent-click size="small">取 消</el-button>
        <el-button type="primary" size="small" @click="confirmSelectProductClick" v-prevent-click>确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import API from "@/api/iBeauty/Goods/projectConsumable.js";
export default {
  name: "ProjectConsumable",
  props: {},
  /** 监听数据变化   */
  watch: {
    projectName(val) {
      this.$refs.projectTree.filter(val);
    },
  },
  /**  引入的组件  */
  components: {},
  /**  Vue 实例的数据对象**/
  data() {
    return {
      detailLoading: false,
      addCategoryLoading: false,
      addLoading: false,
      addCategoryVisible: false,
      consumableVisible: false,
      CategorySearchKey: "",
      addCategoryRuleForm: {
        ID: null, //分类ID
        Name: "", //分类名
        ParentID: null, //上级分类ID，不填时为顶级分类，不传该参数或者参数值为null/0
        Active: true, //是否有效
      },
      isAddCategory: false,
      addCategoryRules: {
        Name: [{ required: true, message: "请输入分类名称", trigger: ["blur", "change"] }],
      },
      ConsumableCategory: [], //分类列表
      CategoryList: [],
      ConsumableCategoryDefaultExpandedKeys: [1],
      defaultProps: {
        children: "Child",
        label: "Name",
      },
      SearchConsumable: {
        Name: "",
        Active: true,
        ProjectConsumableCategoryID: "",
      },
      tableData: [],
      paginations: {
        page: 1, // 当前位于哪页
        total: 1, // 总数
        page_size: 15, // 1页显示多少条
        layout: "total, prev, pager, next,jumper", // 翻页属性
      },
      activeName: "Info",
      isAdd: false,
      addRuleForm: {
        Active: true,
        Name: "",
        ProjectConsumableCategoryID: "",
        ProductList: [], // 产品列表
        ProjectList: [], // 项目列表
        ProjectListAll: [], //包含项目分类的列表
        ProjectCategoryList: [], //项目分类列表
      },
      addRules: {
        Name: [{ required: true, message: "请输入模板名称", trigger: "blur" }],
        ProjectConsumableCategoryID: [{ required: true, message: "请选择模板分类", trigger: "blur" }],
      },
      projectVisible: false,

      projectName: "",
      selctProjectName: "",
      projectCategorys: [], //分类及项目
      selectProject: [], //选中适用项目
      defaultProjectCheckedKeys: [], //回显

      showProductVisible: false,
      ProductName: "",
      selectProductName: "",
      ProductList: [],
      ProductPaginations: {
        page: 1, // 当前位于哪页
        total: 0, // 总数
        page_size: 10, // 1页显示多少条
        layout: "total, prev, pager, next, jumper", // 翻页属性
      },
      tmpSelectProductList: [],
    };
  },
  /**计算属性  */
  computed: {},
  /**  方法集合  */
  methods: {
    /**   添加分类 */
    addConsumableCategoryModal() {
      let that = this;
      that.addCategoryRuleForm = {
        ID: null, //分类ID
        Name: "", //分类名
        ParentID: null, //上级分类ID，不填时为顶级分类，不传该参数或者参数值为null/0
        Active: true, //是否有效
      };
      that.isAddCategory = true;
      that.addCategoryVisible = true;
    },
    /**   编辑分类 */
    editConsumableCategoryModal(data) {
      let that = this;
      that.addCategoryRuleForm = Object.assign(that.addCategoryRuleForm, data);
      if (that.addCategoryRuleForm.ParentID == 0) {
        that.addCategoryRuleForm.ParentID = null;
      }
      that.isAddCategory = false;
      that.addCategoryVisible = true;
    },
    /** 保存分类   */
    submitCreateCategory() {
      let that = this;
      that.$refs.addCategoryRef.validate((valid) => {
        if (valid) {
          if (that.isAddCategory) {
            that.ProjectConsumableTemplate_createCategory();
          } else {
            that.ProjectConsumableTemplate_updateCategory();
          }
        }
      });
    },
    /**  查询分类  */
    handleCategoryQuery() {
      this.ProjectConsumableTemplate_allCategory();
    },
    /**    */
    handleNodeClick(data) {
      let that = this;
      that.SearchConsumable.ProjectConsumableCategoryID = data.ID;
      that.ProjectConsumableTemplate_all();
    },
    // /**    */
    // handleDrop() {
    //   // let that = this;
    // },
    // allowDrag(node) {
    //   return node.data.ID != 1;
    // },
    // allowDrop(draggingNode, dropNode, type) {
    //   return !((type == "prev" || type == "next") && dropNode.data.ID == 1);
    // },
    /**  修改列表分页  */
    handleCurrentChange() {
      // let that = this;
    },
    /**  搜索列表  */
    handleConsumableTemplate() {
      let that = this;
      that.paginations.page = 1;
      that.ProjectConsumableTemplate_all();
    },
    /**   添加 消耗模板 */
    addConsumableTemplate() {
      let that = this;
      that.addRuleForm = {
        Active: true,
        Name: "",
        ProjectConsumableCategoryID: that.SearchConsumable.ProjectConsumableCategoryID,
        ProductList: [], // 产品列表
        ProjectList: [], // 项目列表
        ProjectListAll: [], //包含项目分类的列表
        ProjectCategoryList: [], //项目分类列表
      };
      that.activeName = "Info";
      that.isAdd = true;
      that.consumableVisible = true;
    },
    /**  编辑模板  */
    editConsumableTemplate(row) {
      let that = this;
      that.activeName = "Info";
      that.isAdd = false;
      that.ProjectConsumableTemplate_detail(row.ID);
    },
    /**   删除模板 */
    removeConsumableTemplate() {
      // let that = this;
    },
    /**  保存模板  */
    saveConsumableTempplate() {
      let that = this;
      that.$refs.addRuleFormRef.validate((valid) => {
        if (valid) {
          if (that.isAdd) {
            that.ProjectConsumableTemplate_create();
          } else {
            that.ProjectConsumableTemplate_update();
          }
        }
      });
    },

    // 适用项目
    selectConsumableProject() {
      var that = this;
      that.projectName = "";
      that.defaultProjectCheckedKeys = that.addRuleForm.ProjectListAll.map((i) => {
        let PID = i.IsProject ? "1" + i.ID : "0" + i.ID;
        return {
          ID: i.ID,
          IsProject: true,
          Name: i.Name,
          PID: PID,
        };
      });
      that.projectVisible = true;
      that.$nextTick(() => {
        let tmpkey = that.defaultProjectCheckedKeys.map((i) => i.PID);
        that.$refs.projectTree.setCheckedKeys(tmpkey);
      });
    },
    filterNode(value, data) {
      if (!value) return true;
      return data.Name.indexOf(value) !== -1;
    },
    // 递归
    setRecursion(data) {
      var that = this;
      for (let i = 0; i <= data.length - 1; i++) {
        if (data[i].IsProject) {
          data[i].PID = "1" + data[i].ID;
        } else {
          data[i].PID = "0" + data[i].ID;
        }
        if (data[i].Child) {
          that.setRecursion(data[i].Child);
        }
      }
    },
    /* 修改适用项目选中装填 */
    changeProjectCheckedData(data, checkData) {
      var that = this;
      that.defaultProjectCheckedKeys = Array.from(checkData.checkedNodes).map((val) => ({
        PID: val.PID,
        ID: val.ID,
        Name: val.Name,
        IsProject: val.IsProject,
      }));
    },
    /**  删除选中项目  */
    removetConsumableProject(row, index) {
      let that = this;
      that.defaultProjectCheckedKeys.splice(index, 1);
      let tmpkey = that.defaultProjectCheckedKeys.map((i) => i.PID);
      that.$refs.projectTree.setCheckedKeys(tmpkey);
    },
    /* 确认选中适用项目 */
    addProjectSelect() {
      var that = this;
      that.addRuleForm.ProjectListAll = that.defaultProjectCheckedKeys;
      that.projectVisible = false;
    },
    /**  删除选中后的适用项目  */
    removeConsumableTemplateProject(row, index) {
      let that = this;
      that.addRuleForm.ProjectListAll.splice(index, 1);
    },

    /* •••••••••••••••••••••••••••••••••••••••••••••••••••••••••• */
    /**  配置耗材  */
    selectConsumableProduct() {
      let that = this;

      that.showProductVisible = true;
      that.$nextTick(() => {
        that.ProductList.forEach((i) => {
          that.addRuleForm.ProductList.forEach((j) => {
            if (i.ProductID == j.ProductID) {
              that.$refs.productTable.toggleRowSelection(i);
            }
          });
        });
      });
    },
    /**  修改产品分页  */
    handleProductChange(page) {
      let that = this;
      that.ProductPaginations.page = page;
      that.treatBill_product();
    },
    /**    */
    handleSearchProductClick() {
      let that = this;
      that.ProductPaginations.page = 1;
      that.treatBill_product();
    },
    /* 选择商品 */
    changeSelectProduct(selection) {
      this.tmpSelectProductList = selection;
    },
    /* 确认商品选择 */
    confirmSelectProductClick() {
      let that = this;
      let temp = that.tmpSelectProductList.filter((i) => {
        return !that.addRuleForm.ProductList.some((j) => j.ProductID == i.ProductID);
      });
      that.addRuleForm.ProductList = [...that.addRuleForm.ProductList, ...temp];
      that.showProductVisible = false;
    },
    /**    */
    removeConsumableTemplateProduct(row, index) {
      let that = this;
      that.addRuleForm.ProductList.splice(index, 1);
    },

    /* •••• 请求  •••••••••••••••••••••••••••••••••••••••••••••••••••••• */
    /**  耗材分类  */
    async ProjectConsumableTemplate_allCategory() {
      let that = this;
      let params = {
        Name: that.CategorySearchKey,
      };
      let res = await API.ProjectConsumableTemplate_allCategory(params);
      if (res.StateCode == 200) {
        that.ConsumableCategory = res.Data;
        if (that.ConsumableCategory.length > 0) {
          that.ProjectConsumableCategoryID = that.ConsumableCategory[0].ID;
          that.ProjectConsumableTemplate_all();
        }
      } else {
        that.$message.error(res.Message);
      }
    },
    /**  平铺列表 */
    async ProjectConsumableTemplate_listCategory(Name) {
      let that = this;
      let params = {
        Name: Name,
      };
      let res = await API.ProjectConsumableTemplate_listCategory(params);
      if (res.StateCode == 200) {
        that.CategoryList = res.Data;
      } else {
        that.$message.error(res.Message);
      }
    },
    /**  创建分类  */
    async ProjectConsumableTemplate_createCategory() {
      let that = this;
      let params = Object.assign({}, that.addCategoryRuleForm);
      that.addCategoryLoading = true;
      let res = await API.ProjectConsumableTemplate_createCategory(params);
      if (res.StateCode == 200) {
        that.$message.success("操作成功");
        that.addCategoryVisible = false;
        that.ProjectConsumableTemplate_allCategory();
        that.ProjectConsumableTemplate_listCategory();
      } else {
        that.$message.error(res.Message);
      }
      that.addCategoryLoading = false;
    },
    /**  更新分类  */
    async ProjectConsumableTemplate_updateCategory() {
      let that = this;
      let params = Object.assign({}, that.addCategoryRuleForm);
      that.addCategoryLoading = true;
      let res = await API.ProjectConsumableTemplate_updateCategory(params);
      if (res.StateCode == 200) {
        that.$message.success("操作成功");
        that.addCategoryVisible = false;
        that.ProjectConsumableTemplate_allCategory();
        that.ProjectConsumableTemplate_listCategory();
      } else {
        that.$message.error(res.Message);
      }
      that.addCategoryLoading = false;
    },

    /**  耗材模板  */
    async ProjectConsumableTemplate_all() {
      let that = this;
      let params = {
        PageNum: that.paginations.page,
        Name: that.SearchConsumable.Name, //搜索分类名字段
        Active: that.SearchConsumable.Active, //是否有效
        ProjectConsumableCategoryID: that.SearchConsumable.ProjectConsumableCategoryID, //左侧分类列表ID
      };
      let res = await API.ProjectConsumableTemplate_all(params);
      if (res.StateCode == 200) {
        that.tableData = res.List;
        that.paginations.total = res.Total;
      } else {
        that.$message.error(res.Message);
      }
    },

    /**  新建耗材模板  */
    async ProjectConsumableTemplate_create() {
      let that = this;
      if (!that.addRuleForm.ProjectListAll || that.addRuleForm.ProjectListAll.length == 0) {
        that.$message.error("请选择适用项目");
        return;
      }
      if (!that.addRuleForm.ProductList || that.addRuleForm.ProductList.length == 0) {
        that.$message.error("请选择耗材");
        return;
      }
      if (that.addRuleForm.ProductList.some((i) => !i.MinimumUnitQuantity)) {
        that.$message.error("请填写耗材标准量");
        return;
      }
      that.addRuleForm.ProjectListAll.forEach((i) => {
        if (i.IsProject) {
          that.addRuleForm.ProjectList.push({
            ProjectID: i.ID,
          });
        } else {
          that.addRuleForm.ProjectCategoryList.push({
            ProjectCategoryID: i.ID,
          });
        }
      });

      let params = Object.assign({}, that.addRuleForm);
      that.addLoading = true;
      let res = await API.ProjectConsumableTemplate_create(params);
      if (res.StateCode == 200) {
        that.$message.success("操作成功");
        that.ProjectConsumableTemplate_all();
        that.consumableVisible = false;
      } else {
        that.$message.error(res.Message);
      }
      that.addLoading = false;
    },

    /**  更新耗材模板  */
    async ProjectConsumableTemplate_update() {
      let that = this;
      if (!that.addRuleForm.ProjectListAll || that.addRuleForm.ProjectListAll.length == 0) {
        that.$message.error("请选择适用项目");
        return;
      }
      if (!that.addRuleForm.ProductList || that.addRuleForm.ProductList.length == 0) {
        that.$message.error("请选择耗材");
        return;
      }
      if (that.addRuleForm.ProductList.some((i) => !i.MinimumUnitQuantity)) {
        that.$message.error("请填写耗材标准量");
        return;
      }
      that.addRuleForm.ProjectListAll.forEach((i) => {
        if (i.IsProject) {
          that.addRuleForm.ProjectList.push({
            ProjectID: i.ID,
          });
        } else {
          that.addRuleForm.ProjectCategoryList.push({
            ProjectCategoryID: i.ID,
          });
        }
      });

      let params = Object.assign({}, that.addRuleForm);
      that.addLoading = true;
      let res = await API.ProjectConsumableTemplate_update(params);
      if (res.StateCode == 200) {
        that.$message.success("操作成功");
        that.ProjectConsumableTemplate_all();
        that.consumableVisible = false;
      } else {
        that.$message.error(res.Message);
      }
      that.addLoading = false;
    },
    /**  获取详情  */
    async ProjectConsumableTemplate_detail(ID) {
      let that = this;
      let params = { ID: ID };
      that.detailLoading = true;
      let res = await API.ProjectConsumableTemplate_detail(params);
      if (res.StateCode == 200) {
        let tmp = res.Data;
        tmp.ProjectListAll = [
          ...tmp.ProjectList.map((i) => {
            return {
              PID: "1" + i.ProjectID,
              ID: i.ProjectID,
              Name: i.ProjectName,
              IsProject: true,
            };
          }),
          ...tmp.ProjectCategoryList.map((i) => {
            return {
              PID: "0" + i.ProjectCategoryID,
              ID: i.ProjectCategoryID,
              Name: i.ProjectCategoryName,
              IsProject: false,
            };
          }),
        ];
        tmp.ProjectCategoryList = [];
        tmp.ProjectList = [];
        that.addRuleForm = Object.assign(that.addRuleForm, tmp);
        that.consumableVisible = true;
      } else {
        that.$message.error(res.Message);
      }
      that.detailLoading = false;
    },
    /**    */
    async ProjectConsumableTemplate_findCategoryAndProject() {
      let that = this;
      let params = {};
      let res = await API.ProjectConsumableTemplate_findCategoryAndProject(params);
      if (res.StateCode == 200) {
        that.setRecursion(res.Data);
        that.projectCategorys = res.Data;
      } else {
        that.$message.error(res.Message);
      }
    },

    /**  商品查询  */
    async treatBill_product() {
      let that = this;
      let params = {
        PageNum: that.ProductPaginations.page,
        Name: that.ProductName, //模糊搜索
      };
      let res = await API.treatBill_product(params);
      if (res.StateCode == 200) {
        that.ProductPaginations.total = res.Total;
        that.ProductList = res.List;

        that.$nextTick(() => {
          that.ProductList.forEach((i) => {
            that.addRuleForm.ProductList.forEach((j) => {
              if (i.ProductID == j.ProductID) {
                that.$refs.productTable.toggleRowSelection(i);
              }
            });
          });
        });
      } else {
        that.$message.error(res.Message);
      }
    },
  },
  /** 创建实例之前执行函数   */
  beforeCreate() {},
  /**  实例创建完成之后  */
  created() {},
  /**  在挂载开始之前被调用  */
  beforeMount() {},
  /**  实例被挂载后调用  */
  mounted() {
    this.ProjectConsumableTemplate_listCategory();
    this.ProjectConsumableTemplate_allCategory();
    this.ProjectConsumableTemplate_all();

    this.ProjectConsumableTemplate_findCategoryAndProject();

    this.treatBill_product();
  },
  /**  数据更新 之前 调用  */
  beforeUpdate() {},
  /** 数据更新 完成 调用   */
  updated() {},
  /**  实例销毁之前调用  */
  beforeDestroy() {},
  /**  实例销毁后调用  */
  destroyed() {},
};
</script>

<style lang="scss">
.projectConsumable {
  padding: 0px;
  height: 100%;
  .categoryScrollbar {
    height: 100%;
    border-right: 1px solid #ddd;
    .el-scrollbar__wrap {
      overflow-x: hidden;
    }

    .consumableeditItem i {
      display: none;
    }

    .consumableeditItem:hover i {
      display: inline-block;
    }
  }
  .el-scrollbar_height {
    height: 65vh;
    .el-scrollbar__wrap {
      overflow-x: hidden;
    }
  }
}
</style>
