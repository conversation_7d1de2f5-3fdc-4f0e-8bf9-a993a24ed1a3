<template>
  <!--    门店业绩-时效卡销售门店业绩-->
  <div class="saleTimeCardEntityPerformanceScheme content_body">
    <div class="nav_header">
      <el-row>
        <el-col :span="20">
          <el-form :inline="true" size="small" @submit.native.prevent>
            <el-form-item label="组织单位">
              <el-input
                v-model="name"
                placeholder="输入组织单位名称搜索"
                clearable
                @clear="handleSearch"
                @keyup.enter.native="handleSearch"
              ></el-input>
            </el-form-item>
            <el-form-item>
              <el-button
                type="primary"
                size="small"
                @click="handleSearch"
                v-prevent-click
                >搜索</el-button
              >
            </el-form-item>
          </el-form>
        </el-col>
        <el-col :span="4" class="text_right">
          <el-button
            type="primary"
            size="small"
            @click="showAddDialog"
            v-prevent-click
            >新增</el-button
          >
        </el-col>
      </el-row>
    </div>

    <div>
      <el-table size="small" :data="tableData">
        <el-table-column prop="EntityName" label="组织单位"></el-table-column>
        <el-table-column label="操作" width="145px">
          <template slot-scope="scope">
            <el-button
              type="primary"
              size="small"
              @click="showEditDialog(scope.row)"
              v-prevent-click
              >编辑</el-button
            >
            <el-button
              type="danger"
              size="small"
              @click="removeEntityClick(scope.row)"
              v-prevent-click
              v-if="isDelete"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <div class="page pad_10">
        <div class="text_right" v-if="paginations.total > 0">
          <el-pagination
            background
            @current-change="handleCurrentChange"
            :current-page.sync="paginations.page"
            :page-size="paginations.page_size"
            :layout="paginations.layout"
            :total="paginations.total"
          ></el-pagination>
        </div>
      </div>
    </div>

    <!--新增弹窗-->
    <el-dialog
      title="新增时效卡销售门店业绩方案"
      :visible.sync="dialogVisible"
      width="30%"
      custom-class="custom-dialog-add"
    >
      <div>
        <el-form
          :model="ruleForm"
          :rules="rules"
          ref="ruleForm"
          label-width="120px"
          size="small"
        >
          <el-form-item label="适用组织" prop="EntityID">
            <span slot="label">
              适用组织
              <el-popover placement="top-start" width="200" trigger="hover">
                <p>适用于同级所有节点，则只需选择父节点。</p>
                <p>比如：适用于所有节点，只需选择“顶级/第一个”节点。</p>
                <el-button
                  type="text"
                  style="color: #dcdfe6"
                  icon="el-icon-info"
                  slot="reference"
                ></el-button>
              </el-popover>
            </span>
            <treeselect
              v-model="ruleForm.EntityID"
              :options="entityList"
              :normalizer="normalizer"
              clearValueText
              noResultsText="无匹配数据"
              placeholder="选择适用组织"
            />
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="dialogVisible = false" v-prevent-click
          >取 消</el-button
        >
        <el-button
          type="primary"
          size="small"
          :loading="modalLoading"
          v-prevent-click
          @click="submitTimeCardEntityPerformanceScheme"
          >保存</el-button
        >
      </div>
    </el-dialog>

    <!--编辑弹窗-->
    <el-dialog :visible.sync="dialogEdit" custom-class="custom-dialog-edit" width="60%">
      <div slot="title">{{ entityName }} - 时效卡分类销售门店业绩方案</div>
      <el-table
        :data="timeCardEntityPerformance"
        size="small"
        max-height="500px"
        :row-class-name="tableRowClassName"
        row-key="CategoryID"
        :tree-props="{ children: 'Child', hasChildren: 'hasChild' }"
      >
        <el-table-column
          prop="CategoryName"
          label="时效卡分类"
          min-width="100px"
          fixed
        ></el-table-column>
        <el-table-column prop="PayRate" label="现金比例" min-width="105px">
          <template slot-scope="scope">
            <el-input
              type="number"
              size="mini"
              v-input-fixed="2"
              v-model="scope.row.PayRate"
              class="input_type"
              @input="royaltyRateChange(1, scope.row)"
            >
              <template slot="append">%</template>
            </el-input>
          </template>
        </el-table-column>
        <el-table-column
          prop="SavingCardRate"
          label="卡抵扣比例"
          min-width="105px"
        >
          <template slot-scope="scope">
            <el-input
              type="number"
              size="mini"
              v-input-fixed="2"
              v-model="scope.row.SavingCardRate"
              class="input_type"
              @input="royaltyRateChange(2, scope.row)"
            >
              <template slot="append">%</template>
            </el-input>
          </template>
        </el-table-column>
        <el-table-column
          prop="SavingCardLargessRate"
          label="赠送卡抵扣比例"
          min-width="105px"
        >
          <template slot-scope="scope">
            <el-input
              type="number"
              size="mini"
              v-input-fixed="2"
              v-model="scope.row.SavingCardLargessRate"
              class="input_type"
              @input="royaltyRateChange(3, scope.row)"
            >
              <template slot="append">%</template>
            </el-input>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="115px">
          <template slot-scope="scope">
            <el-button
              type="primary"
              size="mini"
              @click="timeCardPerformanceClick(scope.row)"
              v-if="!scope.row.isEntity"
              >时效卡业绩</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="dialogEdit = false" v-prevent-click
          >取 消</el-button
        >
        <el-button
          type="primary"
          size="small"
          :loading="modalLoading"
          v-prevent-click
          @click="submitTimeCardEntityPerformance"
          >保 存</el-button
        >
      </div>
    </el-dialog>

    <!--时效卡弹窗-->
    <el-dialog :visible.sync="dialogTimeCardEntity" custom-class="custom-dialog-edit_TimeCard">
      <div slot="title">
        {{ entityName }} - {{ categoryName }} - 时效卡销售业绩方案
      </div>
      <div>
        <el-form :inline="true" size="small" @submit.native.prevent>
          <el-form-item>
            <el-input
              v-model="searchTimeCardName"
              placeholder="输入时效卡名称搜索"
              prefix-icon="el-icon-search"
              clearable
            ></el-input>
          </el-form-item>
        </el-form>
        <el-table
          :data="
            timeCardPerformance.filter(
              (data) =>
                !searchTimeCardName ||
                data.GoodName.toLowerCase().includes(
                  searchTimeCardName.toLowerCase()
                )
            )
          "
          max-height="500px"
          size="small"
        >
          <el-table-column
            prop="GoodName"
            label="时效卡名称"
            min-width="150px"
            fixed
          ></el-table-column>
          <el-table-column prop="PayRate" label="现金比例" min-width="105px">
            <template slot-scope="scope">
              <el-input
                type="number"
                v-model="scope.row.PayRate"
                v-input-fixed="2"
                class="input_type"
                @input="royaltyRateChange(1, scope.row)"
                size="mini"
              >
                <template slot="append">%</template>
              </el-input>
            </template>
          </el-table-column>
          <el-table-column
            prop="SavingCardRate"
            label="卡抵扣比例"
            min-width="105px"
          >
            <template slot-scope="scope">
              <el-input
                type="number"
                v-model="scope.row.SavingCardRate"
                v-input-fixed="2"
                class="input_type"
                @input="royaltyRateChange(2, scope.row)"
                size="mini"
              >
                <template slot="append">%</template>
              </el-input>
            </template>
          </el-table-column>
          <el-table-column
            prop="SavingCardLargessRate"
            label="赠送卡扣比例"
            min-width="105px"
          >
            <template slot-scope="scope">
              <el-input
                type="number"
                v-model="scope.row.SavingCardLargessRate"
                v-input-fixed="2"
                class="input_type"
                @input="royaltyRateChange(3, scope.row)"
                size="mini"
              >
                <template slot="append">%</template>
              </el-input>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="dialogTimeCardEntity = false" v-prevent-click
          >取 消</el-button
        >
        <el-button
          type="primary"
          size="small"
          :loading="modalLoading"
          v-prevent-click
          @click="updateTimeCardEntityPerformance"
          >保存</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
import APIEntity from "@/api/KHS/Entity/entity";
import API from "@/api/iBeauty/EntityPerformance/saleTimeCardEntityPerformanceScheme";
var Enumerable = require("linq");
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import Treeselect from "@riophae/vue-treeselect";

export default {
  name: "saleTimeCardEntityPerformanceScheme",

  beforeRouteEnter(to, from, next) {
    next((vm) => {
      vm.isDelete = vm.$permission.permission(
        to.meta.Permission,
        "KHS-EntityPerformance-SaleTimeCardEntityPerformanceScheme-Delete"
      );
    });
  },
  props: {},
  /**  引入的组件  */
  components: {
    Treeselect
  },
  /**  Vue 实例的数据对象**/
  data() {
    return {
      loading: false,
      modalLoading: false,
      dialogVisible: false,
      dialogEdit: false,
      dialogTimeCardEntity: false,
      isDelete: false,
      name: "",
      entityList: [], //选择组织
      tableData: [], //组织单位列表
      searchTimeCardName: "", //时效卡业绩弹框搜索框
      entityID: "",
      categoryID: "",
      entityName: "", //门店名称
      categoryName: "",
      timeCardEntityPerformance: [],
      timeCardPerformance: [],
      ruleForm: {
        EntityID: null,
      },
      rules: {
        EntityID: [
          { required: true, message: "请选择组织", trigger: "change" },
        ],
      },
      //需要给分页组件传的信息
      paginations: {
        page: 1, // 当前位于哪页
        total: 0, // 总数
        page_size: 10, // 1页显示多少条
        layout: "total, prev, pager, next,jumper", // 翻页属性
      },
    };
  },
  /**计算属性  */
  computed: {},
  /**  方法集合  */
  methods: {
    normalizer(node) {
      return {
        id: node.ID,
        label: node.EntityName,
        children: node.Child,
      };
    },
    tableRowClassName({ rowIndex }) {
      if (rowIndex === 0) {
        return "info-row";
      }
      return "";
    },
    // 所属单位
    entityData: function () {
      var that = this;
      APIEntity.getEntityAll()
        .then((res) => {
          if (res.StateCode == 200) {
            that.entityList = res.Data;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },
    // 数据显示
    handleSearch: function () {
      let that = this;
      that.paginations.page = 1;
      that.search();
    },
    // 数据显示
    search: function () {
      let that = this;
      that.loading = true;
      var params = {
        Name: that.name,
        PageNum: that.paginations.page,
      };
      API.getTimeCardEntityPerformanceScheme(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.tableData = res.List;
            that.paginations.total = res.Total;
            that.paginations.page_size = res.PageSize;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },
    // 上下分页
    handleCurrentChange(page) {
      var that = this;
      that.paginations.page = page;
      that.search();
    },
    // 编辑
    showEditDialog: function (row) {
      var that = this;
      that.entityName = row.EntityName;
      that.entityID = row.EntityID;
      that.getTimeCardCategoryEntityPerformance();

    },
    // 编辑 获取时效卡销售门店业绩
    getTimeCardCategoryEntityPerformance: function () {
      var that = this;
      that.loading = true;
      var params = {
        EntityID: that.entityID,
      };
      API.getTimeCardCategoryEntityPerformance(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.dialogEdit = true;
            var data = {
              CategoryID: "0" + res.Data.EntityID,
              CategoryName: "所有时效卡",
              PayRate: res.Data.PayRate,
              SavingCardLargessRate: res.Data.SavingCardLargessRate,
              SavingCardRate: res.Data.SavingCardRate,
              isEntity: true,
            };
            var Category = Enumerable.from(res.Data.Category)
              .select((val) => ({
                CategoryID: val.CategoryID,
                CategoryName: val.CategoryName,
                PayRate: val.PayRate,
                SavingCardLargessRate: val.SavingCardLargessRate,
                SavingCardRate: val.SavingCardRate,
              }))
              .toArray();
            that.timeCardEntityPerformance = Object.assign([], Category);
            that.timeCardEntityPerformance.unshift(data);
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },

    // 时效卡业绩的点击
    timeCardPerformanceClick: function (row) {
      var that = this;
      that.categoryID = row.CategoryID;
      that.categoryName = row.CategoryName;
      that.getTimeCardEntityPerformance();
    },

    // 获取时效卡业绩列表
    getTimeCardEntityPerformance: function () {
      var that = this;
      // that.loading = true;
      var params = {
        EntityID: that.entityID,
        CategoryID: that.categoryID,
      };
      API.getTimeCardEntityPerformance(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.dialogTimeCardEntity = true;
            that.timeCardPerformance = res.Data;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },

    // 保存 时效卡业绩
    updateTimeCardEntityPerformance: function () {
      var that = this;
      var timeCardPerformance = Enumerable.from(that.timeCardPerformance)
        .where(function (i) {
          return (
            (i.PayRate !== "" && i.PayRate !== null) ||
            (i.SavingCardRate !== "" && i.SavingCardRate !== null) ||
            (i.SavingCardLargessRate !== "" && i.SavingCardLargessRate !== null)
          );
        })
        .toArray();
      that.modalLoading = true;

      var timeCardPerformances = timeCardPerformance.map((item)=>{
        return {
          GoodID:item.GoodID,
          PayRate:item.PayRate,
          SavingCardRate:item.SavingCardRate,
          SavingCardLargessRate:item.SavingCardLargessRate,
        }
      })
      var params = {
        EntityID: that.entityID,
        Good: timeCardPerformances,
        CategoryID: that.categoryID,
      };
      API.updateTimeCardEntityPerformance(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.$message.success("业绩设置成功");
            that.dialogTimeCardEntity = false;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.modalLoading = false;
        });
    },
    // 保存 编辑
    submitTimeCardEntityPerformance: function () {
      var that = this;
      that.modalLoading = true;
      var params = {
        EntityID: that.timeCardEntityPerformance[0].CategoryID,
        PayRate: that.timeCardEntityPerformance[0].PayRate,
        SavingCardRate: that.timeCardEntityPerformance[0].SavingCardRate,
        SavingCardLargessRate: that.timeCardEntityPerformance[0].SavingCardLargessRate,
      };

      var Category = that.timeCardEntityPerformance;
      Category = Enumerable.from(Category)
        .where(function (i) {
          return !i.isEntity;
        })
        .toArray();

      Category = Enumerable.from(Category)
        .where(function (i) {
          return (
            (i.PayRate !== "" && i.PayRate !== null) ||
            (i.SavingCardRate !== "" && i.SavingCardRate !== null) ||
            (i.SavingCardLargessRate !== "" && i.SavingCardLargessRate !== null)
          );
        })
        .toArray();
      var Categorys = Category.map((item)=>{
        return {
          CategoryID:item.CategoryID,
          PayRate:item.PayRate,
          SavingCardRate:item.SavingCardRate,
          SavingCardLargessRate:item.SavingCardLargessRate,
        }
      })
      params.Category = Categorys;
      API.updateTimeCardCategoryEntityPerformance(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.$message.success("业绩设置成功");
            that.dialogEdit = false;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.modalLoading = false;
        });
    },
    // 新增 组织单位
    showAddDialog: function () {
      var that = this;
      that.ruleForm = {
        EntityID: null,
      };
      that.dialogVisible = true;
    },
    // 创建 组织单位
    submitTimeCardEntityPerformanceScheme: function () {
      var that = this;
      that.$refs.ruleForm.validate((valid) => {
        if (valid) {
          that.modalLoading = true;
          let para = Object.assign({}, that.ruleForm);
          API.createTimeCardEntityPerformanceScheme(para)
            .then(function (res) {
              if (res.StateCode === 200) {
                that.$message.success({
                  message: "新增成功",
                  duration: 2000,
                });
                that.search();
                that.$refs["ruleForm"].resetFields();
                that.dialogVisible = false;
              } else {
                that.$message.error({
                  message: res.Message,
                  duration: 2000,
                });
              }
            })
            .finally(function () {
              that.modalLoading = false;
            });
        }
      });
    },
    // 删除 组织单位
    removeEntityClick: function (row) {
      var that = this;
      that
        .$confirm("此操作将永久删除该记录, 是否继续?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
        .then(() => {
          that.loading = false;
          var params = {
            EntityID: row.EntityID,
          };
          API.deleteTimeCardEntityPerformanceScheme(params)
            .then((res) => {
              if (res.StateCode == 200) {
                that.$message.success("删除成功");
                that.search();
              } else {
                that.$message.error({
                  message: res.Message,
                  duration: 2000,
                });
              }
            })
            .finally(function () {
              that.loading = false;
            });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    // 提成比例
    royaltyRateChange: function (index, row) {
      if (index == 1) {
        if (row.PayRate > 100) {
          row.PayRate = 100;
        }
      } else if (index == 2) {
        if (row.SavingCardRate > 100) {
          row.SavingCardRate = 100;
        }
      } else if (index == 3) {
        if (row.SavingCardLargessRate > 100) {
          row.SavingCardLargessRate = 100;
        }
      }
    },
  },
  /** 监听数据变化   */
  watch: {},
  /** 创建实例之前执行函数   */
  beforeCreate() {},
  /**  实例创建完成之后  */
  created() {},
  /**  在挂载开始之前被调用  */
  beforeMount() {},
  /**  实例被挂载后调用  */
  mounted() {
    this.isDelete = this.$permission.permission(
      this.$route.meta.Permission,
      "KHS-EntityPerformance-SaleTimeCardEntityPerformanceScheme-Delete"
    );
    this.handleSearch();
    this.entityData();
  },
  /**  数据更新 之前 调用  */
  beforeUpdate() {},
  /** 数据更新 完成 调用   */
  updated() {},
  /**  实例销毁之前调用  */
  beforeDestroy() {},
  /**  实例销毁后调用  */
  destroyed() {},
};
</script>

<style lang="scss">
.saleTimeCardEntityPerformanceScheme {
  .input_type {
    .el-input-group__append {
      padding: 0 10px;
    }
  }
  .el-input__inner {
    padding-right: 0;
  }

  .el-table .info-row {
    background: #c0c4cc;
  }
  .custom-dialog-add{
    min-width: 500px;
  }
  .custom-dialog-edit{
    min-width: 950px;
  }
  .custom-dialog-edit_TimeCard{
    min-width: 850px;
  }
}
</style>
