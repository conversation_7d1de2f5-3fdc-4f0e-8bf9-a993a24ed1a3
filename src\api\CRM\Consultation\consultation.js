import { POST, GET } from "@/api/index";

const BASE_URL = "api/consultation";

export default {
  // 创建面诊单
  create(params) {
    return POST(`${BASE_URL}/create`, params);
  },

  // 删除面诊单
  delete(params) {
    return POST(`${BASE_URL}/delete`, params);
  },

  // 更新面诊单
  update(params) {
    return POST(`${BASE_URL}/update`, params);
  },

  // 获取面诊单详情
  view(id) {
    return GET(`${BASE_URL}/view/${id}`);
  },

  // 查询面诊单列表
  query(params) {
    return POST(`${BASE_URL}/query`, params);
  },
};
