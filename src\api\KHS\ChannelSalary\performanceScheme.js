// 渠道业绩取值方案
import * as API from "@/api/index";

export default {
  // 渠道业绩取值方案查询
  queryPerformanceData: (params) => {
    return API.POST("api/channelPerformanceScheme/all", params);
  },

  // 渠道业绩取值方案新增
  createdPerformanceData: (params) => {
    return API.POST("api/channelPerformanceScheme/create", params);
  },

  // 渠道业绩取值方案修改
  uploadPerformanceData: (params) => {
    return API.POST("api/channelPerformanceScheme/update", params);
  },

  // 渠道业绩取值方案详情
  detailPerformanceData: (params) => {
    return API.POST("api/channelPerformanceScheme/detail", params);
  },

  // 渠道业绩取值方案产品详情
  PerformanceDataProductDetail: (params) => {
    return API.POST("api/channelPerformanceSchemeProduct/all", params);
  },

  // 渠道业绩取值方案产品保存
  PerformanceDataProductCreate: (params) => {
    return API.POST("api/channelPerformanceSchemeProduct/create", params);
  },

  // 渠道业绩取值方案项目详情
  PerformanceDataProjectDetail: (params) => {
    return API.POST("api/channelPerformanceSchemeProject/all", params);
  },

  // 渠道业绩取值方案项目保存
  PerformanceDataProjectCreate: (params) => {
    return API.POST("api/channelPerformanceSchemeProject/create", params);
  },

  // 渠道业绩取值方案通用次卡详情
  PerformanceDataGeneralCardDetail: (params) => {
    return API.POST("api/channelPerformanceSchemeGeneralCard/all", params);
  },

  // 渠道业绩取值方案通用次卡保存
  PerformanceDataGeneralCardCreate: (params) => {
    return API.POST("api/channelPerformanceSchemeGeneralCard/create", params);
  },

  // 渠道业绩取值方案时效卡详情
  PerformanceDataTimeCardDetail: (params) => {
    return API.POST("api/channelPerformanceSchemeTimeCard/all", params);
  },

  // 渠道业绩取值方案时效卡保存
  PerformanceDataTimeCardCreate: (params) => {
    return API.POST("api/channelPerformanceSchemeTimeCard/create", params);
  },

  // 渠道业绩取值方案储值卡详情
  PerformanceDataSavingCardDetail: (params) => {
    return API.POST("api/channelPerformanceSchemeSavingCard/all", params);
  },

  // 渠道业绩取值方案储值卡保存
  PerformanceDataSavingCardCreate: (params) => {
    return API.POST("api/channelPerformanceSchemeSavingCard/create", params);
  },

  // 渠道业绩取值方案消耗产品详情
  PerformanceConsumeProductDetail: (params) => {
    return API.POST("api/channelPerformanceSchemeTreatProduct/all", params);
  },

  // 渠道业绩取值方案消耗产品保存
  PerformanceConsumeProductCreate: (params) => {
    return API.POST("api/channelPerformanceSchemeTreatProduct/create", params);
  },

  // 渠道业绩取值方案消耗项目详情
  PerformanceConsumeProjectDetail: (params) => {
    return API.POST("api/channelPerformanceSchemeTreatProject/all", params);
  },

  // 渠道业绩取值方案消耗项目保存
  PerformanceConsumeProjectCreate: (params) => {
    return API.POST("api/channelPerformanceSchemeTreatProject/create", params);
  },

  // 渠道业绩取值方案消耗   通用次卡保存
  channelPerformanceSchemeTreatGeneralCard_all: (params) => {
    return API.POST("api/channelPerformanceSchemeTreatGeneralCard/all", params);
  },

  // 渠道业绩取值方案消耗   通用次卡保存
  channelPerformanceSchemeTreatGeneralCard_create: (params) => {
    return API.POST(
      "api/channelPerformanceSchemeTreatGeneralCard/create",
      params
    );
  },

  // 渠道业绩取值方案消耗   时效卡保存
  channelPerformanceSchemeTreatTimeCard_all: (params) => {
    return API.POST("api/channelPerformanceSchemeTreatTimeCard/all", params);
  },

  // 渠道业绩取值方案消耗   时效卡保存
  channelPerformanceSchemeTreatTimeCard_create: (params) => {
    return API.POST("api/channelPerformanceSchemeTreatTimeCard/create", params);
  },

  // 渠道业绩取值方案消耗   储值卡保存
  channelPerformanceSchemeTreatSavingCard_all: (params) => {
    return API.POST("api/channelPerformanceSchemeTreatSavingCard/all", params);
  },

  // 渠道业绩取值方案消耗   储值卡保存
  channelPerformanceSchemeTreatSavingCard_create: (params) => {
    return API.POST("api/channelPerformanceSchemeTreatSavingCard/create", params);
  },
};
