/**
 * Created by JoJO on 2022/01/05.
 * 员工业绩 产品销售业绩
 */
import * as API from '@/api/index'

export default {
    // 获取产品销售_组织单位业绩方案列表
    getProductPerformanceScheme: params => {
        return API.POST('api/saleProductPerformanceScheme/list', params)
    },

    // 创建产品销售_组织单位业绩方案
    createProductPerformanceScheme: params => {
        return API.POST('api/saleProductPerformanceScheme/create', params)
    },

    // 删除产品销售_组织单位业绩方案
    deleteProductPerformanceScheme: params => {
        return API.POST('api/saleProductPerformanceScheme/delete', params)
    },

    // 获取产品销售_分类业绩方案 即编辑
    getProductCategoryPerformance: params => {
        return API.POST('api/saleProductCategoryPerformance/all', params)
    },

    // 获取 所有产品经手人/职务业绩-方案(产品)
    getProductSchemeHanderPerformance: params => {
        return API.POST('api/saleProductSchemeHandlerPerformance/all', params)
    },

    // 获取 所有产品经手人/职务业绩-方案(套餐卡-产品)
    getPackageCardProductSchemeHanderPerformance: params => {
        return API.POST('api/saleProductSchemeHandlerPerformance/packageCard', params)
    },


    // 保存 所有产品经手人/职务业绩方案
    updateProductSchemeHanderPerformance: params => {
        return API.POST('api/saleProductSchemeHandlerPerformance/update', params)
    },

    // 获取 分类经手人/职务业绩方案(产品)
    getProductCategoryHandlerPerformance: params => {
        return API.POST('api/saleProductCategoryHandlerPerformance/all', params)
    },

    // 获取 分类经手人/职务业绩方案(套餐卡-产品)
    getPackageCardSaleProductCategoryHandlerPerformance: params => {
        return API.POST('api/saleProductCategoryHandlerPerformance/packageCard', params)
    },


    // 保存 分类经手人/职务业绩方案
    updateProductCategoryHandlerPerformance: params => {
        return API.POST('api/saleProductCategoryHandlerPerformance/update', params)
    },


    // 获取 产品业绩方案 即时产品业绩 列表
    getProductPerformance: params => {
        return API.POST('api/saleProductPerformance/all', params)
    },


    // 获取 产品业绩下 经手人业绩方案(产品)
    getProductHanderPerformance: params => {
        return API.POST('api/saleProductHandlerPerformance/all', params)
    },


    // 获取 产品业绩下 经手人业绩方案(套餐卡-产品)
    getPackageCardSaleProductHandlerPerformance: params => {
        return API.POST('api/saleProductHandlerPerformance/packageCard', params)
    },


    // 保存 产品业绩下 经手人业绩方案
    updateProductHanderPerformance: params => {
        return API.POST('api/saleProductHandlerPerformance/update', params)
    },


    // 保存 产品业绩
    updateProductPerformance: params => {
        return API.POST('api/saleProductPerformance/update', params)
    },


    // 保存 编辑
    updateProductCategoryPerformance: params => {
        return API.POST('api/saleProductCategoryPerformance/update', params)
    },

}
