/**
 * Created by wsf on 2022/05/20
 *  门店客流报表 api
 */

import * as API from "@/api/index";

export default {
  /* 获取门店客流报表 */
  getEntityPassengerFlowList: (params) => {
    return API.POST("api/entityPassengerFlow/list", params);
  },
  // 导出
  entityPassengerFlowExcel: (params) => {
    return API.exportExcel("api/entityPassengerFlow/excel", params);
  },
  /* 获取门店客流报表 */
  entityPassengerFlow_saleList: (params) => {
    return API.POST("api/entityPassengerFlow/saleList", params);
  },

  // 导出
  entityPassengerFlow_saleExcel: (params) => {
    return API.exportExcel("api/entityPassengerFlow/saleExcel", params);
  },
  /* 获取门店客流报表 */
  entityPassengerFlow_treatList: (params) => {
    return API.POST("api/entityPassengerFlow/treatList", params);
  },

  // 导出
  entityPassengerFlow_treatExcel: (params) => {
    return API.exportExcel("api/entityPassengerFlow/treatExcel", params);
  },
};
