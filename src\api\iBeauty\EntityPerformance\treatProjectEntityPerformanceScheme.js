/**
 * Created by wsf on 2022/01/11.
 * 门店业绩 门店项目消耗业绩api
 */
 import * as API  from '@/api/index'

 export default {
   // 获取门店项目消耗业绩方案列表
   getTreatProjectEntityPerformanceScheme: params => {
       return API.POST('api/treatProjectEntityPerformanceScheme/list', params)
   },
   // 保存门店项目消耗业绩方案
   createTreatProjectEntityPerformanceScheme: params => {
       return API.POST('api/treatProjectEntityPerformanceScheme/create', params)
   },
   // 删除门店项目消耗业绩方案
   deleteTreatProjectEntityPerformanceScheme: params => {
       return API.POST('api/treatProjectEntityPerformanceScheme/delete', params)
   },
   // 获取分类项目消耗业绩
   getTreatProjectCategoryEntityPerformance: params => {
       return API.POST('api/treatProjectCategoryEntityPerformance/all', params)
   },
   // 保存分类项目消耗业绩
   updateTreatProjectCategoryEntityPerformance: params => {
       return API.POST('api/treatProjectCategoryEntityPerformance/update', params)
   },
   // 获取项目消耗业绩
   getTreatProjectEntityPerformance: params => {
       return API.POST('api/treatProjectEntityPerformance/all', params)
   },
   // 保存项目消耗业绩
   updateTreatProjectEntityPerformance: params => {
       return API.POST('api/treatProjectEntityPerformance/update', params)
   }
 }