/**
 * Created by wsf on 2022/01/11.
 * 门店业绩 门店储值卡销售业绩api
 */
 import * as API  from '@/api/index'

 export default {
    // 获取门店储值卡销售业绩列表
    getSavingCardEntityPerformanceScheme: params => {
        return API.POST('api/saleSavingCardEntityPerformanceScheme/list', params)
    },
    // 保存门店储值卡销售业绩方案
    createSavingCardEntityPerformanceScheme: params => {
        return API.POST('api/saleSavingCardEntityPerformanceScheme/create', params)
    },
    // 删除门店储值卡销售业绩方案
    deleteSavingCardEntityPerformanceScheme: params => {
        return API.POST('api/saleSavingCardEntityPerformanceScheme/delete', params)
    },
    // 获取分类储值卡业绩
    getSavingCardCategoryEntityPerformance: params => {
        return API.POST('api/saleSavingCardCategoryEntityPerformance/all', params)
    },
    // 保存分类储值卡业绩
    updateSavingCardCategoryEntityPerformance: params => {
        return API.POST('api/saleSavingCardCategoryEntityPerformance/update', params)
    },
    // 获取储值卡业绩
    getSavingCardEntityPerformance: params => {
        return API.POST('api/saleSavingCardEntityPerformance/all', params)
    },
    // 保存储值卡业绩
    updateSavingCardEntityPerformance: params => {
        return API.POST('api/saleSavingCardEntityPerformance/update', params)
    }
 }