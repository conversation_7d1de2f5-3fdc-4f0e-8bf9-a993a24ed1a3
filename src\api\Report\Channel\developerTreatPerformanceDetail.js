/**
 * Created by preference on 2022/08/11
 *  zmx
 */

import * as API from "@/api/index";
export default {
  // 获取门店
  getAllEntityApi: (params) => {
    return API.POST("api/entity/allEntity", params);
  },
  /**   */
  developerTreatPerformanceDetailStatement_list: (params) => {
    return API.POST(
      "api/developerTreatPerformanceDetailStatement/list",
      params
    );
  },
  /**   */
  developerTreatPerformanceDetailStatement_excel: (params) => {
    return API.exportExcel(
      "api/developerTreatPerformanceDetailStatement/excel",
      params
    );
  },
  /* 业务代表 */
  geTemployeeAll: (params) => {
    return API.POST("api/channel/employeeAll", params);
  },
};
