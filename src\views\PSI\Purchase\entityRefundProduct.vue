<template>
  <div class="content_body EntityApplyProduct" v-loading="loading">
    <div class="nav_header">
      <el-row>
        <el-col :span="22">
          <el-form :inline="true" size="small" :model="searchForm" @keyup.enter.native="handleSearchEntityApplyProductClick">
            <el-form-item v-if="purchaseStorage.length > 1" label="仓库/门店">
              <el-select
                v-model="searchForm.EntityID"
                :default-first-option="true"
                @change="handleSearchEntityApplyProductClick"
                @clear="handleSearchEntityApplyProductClick"
                clearable
                filterable
                placeholder="请选择仓库"
              >
                <el-option v-for="item in purchaseStorage" :key="item.ID" :label="item.EntityName" :value="item.ID"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="产品名称">
              <el-input
                v-model="searchForm.Name"
                placeholder="输入产品名称、别名搜索"
                clearable
                @clear="handleSearchEntityApplyProductClick"
                @keyup.enter.native="handleSearchEntityApplyProductClick"
              ></el-input>
            </el-form-item>
            <el-form-item label="制单日期">
              <el-date-picker
                v-model="searchForm.DateTime"
                unlink-panels
                type="daterange"
                range-separator="至"
                value-format="yyyy-MM-dd"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :picker-options="pickerOptions"
                clearable
                @change="searchDateChange"
              ></el-date-picker>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleSearchEntityApplyProductClick" v-prevent-click>搜索</el-button>
            </el-form-item>
          </el-form>
        </el-col>
        <el-col :span="2" class="text_right">
          <el-button v-if="isAdd" type="primary" @click="addEntityApplyProductClick" size="small" v-prevent-click>新增</el-button>
        </el-col>
      </el-row>
    </div>

    <!-- 单据状态，05：草稿、 10：待审核、20：待配送、30：待入库、40：已驳回、50：已完成、60：已取消 -->
    <el-tabs v-model="searchForm.BillStatus" type="border-card" @tab-click="tabsHandleClick">
      <el-tab-pane label="全部" name="0"></el-tab-pane>
      <el-tab-pane name="05">
        <span slot="label"
          >草稿
          <el-badge v-if="StatusNumberInfo && StatusNumberInfo.BillStatus05 != 0" is-dot />
        </span>
      </el-tab-pane>
      <el-tab-pane name="10">
        <span slot="label"
          >待审核
          <el-badge v-if="StatusNumberInfo && StatusNumberInfo.BillStatus10 != 0" is-dot />
        </span>
      </el-tab-pane>
      <el-tab-pane name="20">
        <span slot="label"
          >待退货
          <el-badge v-if="StatusNumberInfo && StatusNumberInfo.BillStatus20 != 0" is-dot />
        </span>
      </el-tab-pane>
      <el-tab-pane name="30">
        <span slot="label"
          >待入库
          <el-badge v-if="StatusNumberInfo && StatusNumberInfo.BillStatus30 != 0" is-dot />
        </span>
      </el-tab-pane>
      <el-tab-pane name="40">
        <span slot="label"
          >待付款
          <el-badge v-if="StatusNumberInfo && StatusNumberInfo.BillStatus40 != 0" is-dot />
        </span>
      </el-tab-pane>

      <el-tab-pane name="50">
        <span slot="label"
          >已驳回
          <el-badge v-if="StatusNumberInfo && StatusNumberInfo.BillStatus50 != 0" is-dot />
        </span>
      </el-tab-pane>
      <el-tab-pane label="已完成" name="60"> </el-tab-pane>
      <el-tab-pane label="已关闭" name="70"> </el-tab-pane>
    </el-tabs>

    <el-table size="small" class="martp_10" :data="inventoryApplyList">
      <el-table-column prop="ID" label="单据号"></el-table-column>
      <el-table-column prop="RefundOutboundEntityName" label="申请仓库/门店"></el-table-column>
      <el-table-column prop="BillStatus" label="单据状态" :formatter="ApplyOrderBillStatusFormatter"></el-table-column>
      <el-table-column prop="TotalAmount" label="单据总额（元）">
        <template slot-scope="scope">￥{{ scope.row.TotalAmount | toFixed | NumFormat }}</template>
      </el-table-column>
      <el-table-column prop="CreatedOn" label="制单时间">
        <template slot-scope="scope">
          {{ scope.row.CreatedOn | dateFormat("YYYY-MM-DD HH:mm") }}
        </template>
      </el-table-column>
      <el-table-column prop="CreatedByName" label="申请人"></el-table-column>
      <el-table-column prop="Remark" label="备注信息" show-overflow-tooltip></el-table-column>
      <el-table-column label="操作" width="240">
        <template slot-scope="scope">
          <!-- 权限 -->
          <!-- 待审核 主动关闭 -->
          <el-button
            v-if="(scope.row.BillStatus == '10' && isClose) || (scope.row.BillStatus == '05' && isClose)"
            type="danger"
            size="small"
            @click="closeEntityApplyProductClick(scope.row)"
            v-prevent-click
            >关 闭</el-button
          >
          <!-- 驳回的编辑 -->
          <el-button v-if="scope.row.BillStatus == '50'" type="primary" size="small" @click="updateEntityProductInfo(scope.row)" v-prevent-click
            >编 辑</el-button
          >
          <!-- 草稿的编辑 -->
          <el-button v-if="scope.row.BillStatus == '05'" type="primary" size="small" @click="editDraft(scope.row)" v-prevent-click>编 辑</el-button>
          <!-- 审核驳回关闭 -->
          <el-button
            v-if="scope.row.BillStatus == '50' && isTurnClose"
            type="danger"
            size="small"
            @click="turnCloseEntityApplyProductClick(scope.row)"
            v-prevent-click
            >关 闭</el-button
          >
          <!-- 待审批 -->
          <el-button
            v-if="scope.row.BillStatus == '10' && isCheck"
            class="martp_5"
            type="primary"
            size="small"
            @click="approvalEntityApplyProductDetail(scope.row)"
            v-prevent-click
            >审 批</el-button
          >
          <!-- 待入库 -->
          <el-button
            v-if="scope.row.BillStatus == '30' && isStorage && scope.row.IsHavePermission"
            type="primary"
            size="small"
            @click="inboundEntityApplyProductClick(scope.row)"
            v-prevent-click
            >入 库</el-button
          >
          <!-- 待退货 -->
          <el-button
            v-if="scope.row.BillStatus == '20' && isDelivery && scope.row.IsHavePermission"
            type="primary"
            size="small"
            @click="outboundEntityApplyProductClick(scope.row)"
            v-prevent-click
            >退 货</el-button
          >
          <!-- 确认收款 -->
          <el-button
            v-if="scope.row.BillStatus == '40' && isPaymentConfirm && scope.row.IsHavePermission"
            type="primary"
            size="small"
            @click="confirmPaymentClick(scope.row)"
            v-prevent-click
            >确认付款</el-button
          >
          <el-button
            v-if="scope.row.BillStatus != '20' || scope.row.BillStatus != '30' || scope.row.BillStatus != '50'"
            type="primary"
            size="small"
            @click="checkEntityApplyProductDetail(scope.row)"
            v-prevent-click
            >详 情</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <div class="pad_15 text_right">
      <el-pagination
        background
        v-if="paginations.total > 0"
        @current-change="EntityApplyProductListHandleCurrentChange"
        :current-page.sync="paginations.page"
        :page-size="paginations.page_size"
        :layout="paginations.layout"
        :total="paginations.total"
      ></el-pagination>
    </div>

    <!-- 新建退货申请-->
    <el-dialog
      custom-class="entityApplyProductDialogClass"
      :title="isAddOrEdit ? '新建申请' : '编辑申请'"
      :visible.sync="dialogVisible"
      width="1200px"
      @close="closeAddApplyProduct"
      :close-on-click-modal="false"
    >
      <div class="tip marbm_10" style="margin-top: 0">退货信息</div>
      <el-form
        class="entityApplyProductInfoFrom"
        :inline="true"
        :inline-message="true"
        label-width="130px"
        size="small"
        :model="entityApplyProduct"
        :rules="entityApplyProductRules"
        ref="entityApplyProductRef"
      >
        <el-row>
          <el-col :span="12">
            <el-form-item label="申请仓库/门店：" prop="EntityID">
              <el-select
                size="small"
                value-key="ID"
                v-model="entityApplyProduct.EntityName"
                filterable
                placeholder="请选择仓库/门店"
                @change="handleSelectProductEntity"
                class="zl-custom-select-width"
              >
                <el-option value-key="ID" v-for="item in purchaseStorage" :key="item.ID" :label="item.EntityName" :value="item"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="备注信息：">
              <el-input
                type="textarea"
                style="width: 300px"
                :autosize="{ minRows: 1, maxRows: 3 }"
                v-model="entityApplyProduct.Remark"
                placeholder="请输入备注信息"
                size="small"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <!-- 余额 -->
        <el-row v-if="entityApplyProduct.TotalBalance">
          <el-col :span="12">
            <el-form-item label="账户余额：">¥ {{ entityApplyProduct.Balance | toFixed | NumFormat }}</el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="赠送余额：">¥ {{ entityApplyProduct.LargessBalance | toFixed | NumFormat }}</el-form-item>
          </el-col>
        </el-row>

        <div class="tip marbm_10 martp_10">产品明细</div>
        <el-row>
          <el-col :span="4">
            <el-button type="primary" size="small" @click="addProducts">添加产品</el-button>
            <el-button type="danger" size="small" @click="removeMultipleProduct" :disabled="removeDisabled">删除产品</el-button>
          </el-col>
        </el-row>

        <el-table
          empty-text="暂无产品"
          size="small"
          class="martp_15"
          :data="entityApplyProduct.Product"
          @selection-change="handleChangeSelectProduct"
          show-summary
          :summary-method="entityApplyProductDetailedSummary"
        >
          <el-table-column type="index" width="50"></el-table-column>
          <el-table-column type="selection" width="55"></el-table-column>
          <el-table-column label="产品名称" prop="ProductName" width="250"> </el-table-column>
          <el-table-column prop="Specification" label="产品规格">
            <template slot-scope="scope">{{ scope.row.Specification }}</template>
          </el-table-column>
          <el-table-column prop="Quantity" label="可退库存">
            <template slot-scope="scope">
              <span class="marlt_5">{{ scope.row.Quantity }}{{ scope.row.miniUnitName }}</span>
            </template>
          </el-table-column>
          <el-table-column label="退货单位" prop="UnitName">
            <template slot-scope="scope">
              <el-form-item :show-message="false" :prop="'Product.' + scope.$index + '.UnitName'" :rules="entityApplyProductRules.UnitID">
                <el-select
                  value-key="UnitID"
                  v-model="scope.row.UnitName"
                  size="small"
                  filterable
                  placeholder="请选择单位"
                  @change="(val) => handleSelectProductUnit(val, scope.row)"
                  :default-first-option="true"
                >
                  <el-option v-for="item in scope.row.Unit" :key="item.UnitID" :label="item.UnitName" :value="item"></el-option>
                </el-select>
              </el-form-item>
            </template>
          </el-table-column>
          <el-table-column label="退货单位数量">
            <template slot-scope="scope">
              <el-form-item
                v-if="entityApplyProduct.Product.length > 0"
                :show-message="false"
                :prop="'Product.' + scope.$index + '.ApplyQuantity'"
                :rules="entityApplyProductRules.ApplyQuantity"
              >
                <!-- 改数量 -->
                <el-input
                  v-model="scope.row.ApplyQuantity"
                  size="small"
                  placeholder="请输入退货数量"
                  @input="changeApplyQuantity(scope.row)"
                  validate-event
                  v-enter-number2
                  v-enterInt
                  min="0"
                  type="number"
                ></el-input>
              </el-form-item>
            </template>
          </el-table-column>
          <el-table-column label="最小包装单位数量" prop="miniUnitQuantity">
            <template slot-scope="scope">
              {{ scope.row.miniUnitQuantity }}
              <span v-if="scope.row.miniUnitQuantity">{{ scope.row.miniUnitName }}</span>
            </template>
          </el-table-column>

          <el-table-column prop="DeliveryPrice" label="配销价格（元）">
            <template slot-scope="scope">
              <el-form-item :show-message="false" :prop="'Product.' + scope.$index + '.DeliveryPrice'" :rules="entityApplyProductRules.DeliveryPrice">
                <el-input
                  v-model="scope.row.DeliveryPrice"
                  size="small"
                  placeholder="请输入配销价格"
                  @input="changeDeliveryPrice(scope.row)"
                  validate-event
                  v-enter-number2
                  min="0"
                  type="number"
                ></el-input>
              </el-form-item>
            </template>
          </el-table-column>
          <el-table-column prop="DeliveryTotalPrice" label="配销价小计（元）">
            <template slot-scope="scope">{{ scope.row.DeliveryTotalPrice | toFixed | NumFormat }}</template>
          </el-table-column>
        </el-table>
      </el-form>

      <div slot="footer">
        <el-button @click="dialogVisible = false" size="small" v-prevent-click>取 消</el-button>
        <el-button type="primary" @click="saveDraftClick" :loading="saveDraftLoading" size="small" v-prevent-click>保存草稿</el-button>
        <el-button type="primary" @click="saveEntityApplyProductClick" :loading="modalLoading" size="small" v-prevent-click>提交申请</el-button>
      </div>
    </el-dialog>

    <!-- 门店退货详情  -->
    <el-dialog custom-class="entityApplyProductDialogClass" title="退货详情" :visible.sync="applyDetaildialogVisible" width="1100px">
      <div v-if="applyDetailInfo" class="tip">
        退货信息 -
        <span class="font_weight_600">{{ ApplyOrderBillStatusFormatter(applyDetailInfo) }}（{{ applyDetailInfo.ID }}）</span>
      </div>
      <el-form v-if="applyDetailInfo" class="entityApplyProductInfoFrom" label-width="110px" size="small" :model="applyDetailInfo">
        <el-row>
          <el-col :span="8">
            <el-form-item label="申请仓库/门店：">{{ applyDetailInfo.RefundOutboundEntityName }} </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="申请人：">{{ applyDetailInfo.CreatedByName }}</el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="申请时间：">{{ applyDetailInfo.CreatedOn | dateFormat("YYYY-MM-DD HH:mm") }}</el-form-item>
          </el-col>
        </el-row>
        <!-- 审批信息 -->
        <template v-if="applyDetailInfo.ApprovedByName">
          <!-- <div class="tip marbm_10">审批信息</div> -->
          <el-row>
            <el-col :span="8" v-if="!applyDetailInfo.RejectReason">
              <el-form-item label="收货仓库：">
                {{ applyDetailInfo.RefundInboundEntityName }}
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="审批人：">
                {{ applyDetailInfo.ApprovedByName }}
              </el-form-item>
            </el-col>

            <el-col :span="8" v-if="applyDetailInfo.BillStatus == '20' || applyDetailInfo.BillStatus == '30'">
              <el-form-item label="审批时间：">
                {{ applyDetailInfo.ApprovedOn | dateFormat("YYYY-MM-DD HH:mm") }}
              </el-form-item>
            </el-col>
            <el-col :span="8" v-if="applyDetailInfo.BillStatus == '40' || applyDetailInfo.BillStatus == '60'">
              <el-form-item label="出库时间：">
                {{ applyDetailInfo.RefundOutboundOn | dateFormat("YYYY-MM-DD HH:mm") }}
              </el-form-item>
            </el-col>
            <el-col :span="24" v-if="applyDetailInfo.BillStatus == '50'">
              <el-form-item label="驳回原因：">
                {{ applyDetailInfo.RejectReason }}
              </el-form-item>
            </el-col>
          </el-row>
        </template>
        <el-row>
          <el-col :span="24">
            <el-form-item label="备注信息：">
              <div>{{ applyDetailInfo.Remark }}</div>
            </el-form-item>
          </el-col>
          <el-col v-if="applyDetailInfo.RefundOutboundBillID && isViewDeliveryBill" :span="8">
            <el-form-item label="要货退货出库单号：" label-width="129px">
              {{ applyDetailInfo.RefundOutboundBillID }}
              <el-button type="text" @click="checkOutboundBillInfo">查看</el-button>
            </el-form-item>
          </el-col>
          <el-col :span="8" v-if="applyDetailInfo.RefundInboundBillID && isViewStorageyBill">
            <el-form-item label="要货退货入库单号：" label-width="129px">
              {{ applyDetailInfo.RefundInboundBillID }}
              <el-button type="text" @click="checkInbounBillInfo">查看</el-button>
            </el-form-item>
          </el-col>
        </el-row>

        <div class="tip">产品明细</div>
        <el-table size="small" :data="applyDetailInfo.Detail" :summary-method="getSumm" show-summary>
          <el-table-column type="index" width="50"></el-table-column>
          <el-table-column label="产品名称" prop="ProductName" key="1">
            <template slot-scope="scope">
              <div>
                {{ scope.row.ProductName }}<span v-if="scope.row.Alias" class="color_gray font_12"> ({{ scope.row.Alias }})</span>
              </div>
              <div v-if="scope.row.Specification" class="color_gray font_12">规格：{{ scope.row.Specification }}</div>
            </template>
          </el-table-column>
          <el-table-column
            prop="RefundQuantity"
            label="申请退货数量"
            v-if="
              applyDetailInfo.BillStatus == '10' ||
              applyDetailInfo.BillStatus == '50' ||
              applyDetailInfo.BillStatus == '40' ||
              applyDetailInfo.BillStatus == '60' ||
              applyDetailInfo.BillStatus == '70'
            "
            key="2"
          >
            <template slot-scope="scope">
              <div>{{ scope.row.RefundQuantity }} {{ scope.row.UnitName }}</div>
              <div class="color_gray font_12">
                最小包装数量：{{ scope.row.RefundMinimumUnitQuantity }}
                {{ scope.row.MinimumUnitName }}
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="ApproveQuantity" label="申请退货数量" v-if="applyDetailInfo.BillStatus == '20' || applyDetailInfo.BillStatus == '30'" key="3">
            <template slot-scope="scope">
              <div>{{ scope.row.ApproveQuantity }} {{ scope.row.UnitName }}</div>
              <div class="color_gray font_12">
                最小包装数量：{{ scope.row.ApproveMinimumUnitQuantity }}
                {{ scope.row.MinimumUnitName }}
              </div>
            </template>
          </el-table-column>
          <el-table-column
            prop="RefundOutboundQuantity"
            label="实际退货数量"
            v-if="applyDetailInfo.BillStatus == '30' || applyDetailInfo.BillStatus == '40' || applyDetailInfo.BillStatus == '60'"
            key="4"
          >
            <template slot-scope="scope">
              <div>{{ scope.row.RefundOutboundQuantity }} {{ scope.row.UnitName }}</div>
              <div class="color_gray font_12">
                最小包装数量：{{ scope.row.RefundOutboundMinimumUnitQuantity }}
                {{ scope.row.MinimumUnitName }}
              </div>
            </template>
          </el-table-column>

          <el-table-column
            prop="RefundInboundQuantity"
            label="退货数量"
            v-if="applyDetailInfo.BillStatus == '40' || applyDetailInfo.BillStatus == '60'"
            key="5"
          >
            <template slot-scope="scope">
              <div>{{ scope.row.RefundInboundQuantity }} {{ scope.row.UnitName }}</div>
              <div class="color_gray font_12">
                最小包装数量：{{ scope.row.RefundInboundMinimumUnitQuantity }}
                {{ scope.row.MinimumUnitName }}
              </div>
            </template>
          </el-table-column>

          <el-table-column prop="ApprovedPrice" label="退货单价(元)" key="6" v-if="applyDetailInfo.BillStatus !== '70' && applyDetailInfo.BillStatus !== '50'">
            <template slot-scope="scope">
              <div>
                {{ scope.row.ApprovedPrice | toFixed | NumFormat }}
              </div>
            </template>
          </el-table-column>

          <el-table-column v-if="applyDetailInfo.BillStatus == '10' || applyDetailInfo.BillStatus == '20'" prop="ApprovedTotalAmount" label="小计(元)" key="7">
            <template slot-scope="scope">
              {{ scope.row.ApprovedTotalAmount | toFixed | NumFormat }}
            </template>
          </el-table-column>

          <el-table-column v-if="applyDetailInfo.BillStatus == '30'" prop="RefundOutboundTotalAmount" label="小计(元)" key="8">
            <template slot-scope="scope">
              {{ scope.row.RefundOutboundTotalAmount | toFixed | NumFormat }}
            </template>
          </el-table-column>

          <el-table-column
            v-if="applyDetailInfo.BillStatus == '40' || applyDetailInfo.BillStatus == '60'"
            prop="RefundInboundTotalAmount"
            label="小计(元)"
            key="9"
          >
            <template slot-scope="scope">
              {{ scope.row.RefundInboundTotalAmount | toFixed | NumFormat }}
            </template>
          </el-table-column>
        </el-table>
      </el-form>

      <div slot="footer">
        <el-button v-show="templateTypeList && templateTypeList.length == 0" type="primary" @click="printInfoTips" size="small" v-prevent-click>打印</el-button>
        <el-button
          v-show="templateTypeList && templateTypeList.length == 1"
          type="primary"
          v-print="'printContent'"
          @click="printInfo"
          size="small"
          v-prevent-click
          >打印</el-button
        >
        <el-button v-show="templateTypeList && templateTypeList.length > 1" type="primary" @click="printInfoSelectTemplate" size="small" v-prevent-click
          >打印</el-button
        >
      </div>
    </el-dialog>

    <!-- 退货审批  审批弹框 -->
    <el-dialog
      v-if="approvalDetaildialogVisible"
      custom-class="entityApplyProductDialogClass"
      title="退货审批"
      :visible.sync="approvalDetaildialogVisible"
      width="1100px"
      :close-on-click-modal="false"
    >
      <div class="tip" style="margin-top: 0">退货信息</div>
      <el-form
        v-if="applyDetailInfo"
        class="entityApplyProductInfoFrom"
        label-width="110px"
        size="small"
        :model="applyDetailInfo"
        :rules="approveEntityRules"
        ref="approvalDetailRef"
      >
        <el-row>
          <el-col :span="8">
            <el-form-item label="申请仓库/门店：" prop="OutboundEntityName">
              {{ applyDetailInfo.RefundOutboundEntityName }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="出货状态：" prop="BillStatus">
              {{ ApplyOrderBillStatusFormatter(applyDetailInfo) }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="申请人："> {{ applyDetailInfo.CreatedByName }}</el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="申请日期：">
              {{ applyDetailInfo.CreatedOn | dateFormat("YYYY-MM-DD HH:mm") }}
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="备注信息：">
              <div>{{ applyDetailInfo.Remark }}</div>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="收货仓库：" prop="EntityID">
              <el-select
                size="small"
                value-key="ID"
                v-model="applyDetailInfo.EntityItem"
                filterable
                placeholder="请选择收货仓库"
                @change="deliveryHandleSelectProductEntity"
              >
                <el-option value-key="ID" v-for="item in OutboundEntitys" :key="item.ID" :label="item.EntityName" :value="item"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="结算方式:" prop="SettlementWay" :inline-message="true">
              <el-select size="small" value-key="ID" v-model="applyDetailInfo.SettlementWay" placeholder="请选择结算方式">
                <el-option label="先货后款" value="10"></el-option>
                <el-option label="先款后货" value="20"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <div class="tip martp_10">产品明细</div>
        <el-table size="small" :data="applyDetailInfo.Detail" :summary-method="getSumm" :show-summary="true">
          <el-table-column type="index" width="50"></el-table-column>
          <el-table-column label="产品名称" prop="ProductName">
            <template slot-scope="scope">
              <div>
                {{ scope.row.ProductName }}<span v-if="scope.row.Alias" class="color_gray font_12"> ({{ scope.row.Alias }})</span>
              </div>
              <!-- <div v-if="scope.row.Specification" class="color_gray font_12">规格：{{ scope.row.Specification }}</div> -->
            </template>
          </el-table-column>
          <el-table-column prop="StockQuantity" label="产品规格">
            <template slot-scope="scope">{{ scope.row.Specification }}</template>
          </el-table-column>
          <el-table-column prop="RefundQuantity" label="申请退货数量">
            <template slot-scope="scope">
              <div>{{ scope.row.RefundQuantity }} {{ scope.row.UnitName }}</div>
              <div class="color_gray font_12">
                最小包装数量：{{ scope.row.RefundMinimumUnitQuantity }}
                {{ scope.row.MinimumUnitName }}
              </div>
            </template>
          </el-table-column>

          <el-table-column prop="ApprovedPrice" label="配销价格(元)">
            <template slot-scope="scope">
              <el-form-item
                label-width="0px"
                :show-message="false"
                :prop="'Detail.' + scope.$index + '.ApprovedPrice'"
                :rules="approveEntityRules.ApprovedPrice"
              >
                <el-input
                  v-model="scope.row.ApprovedPrice"
                  class="input_type"
                  size="small"
                  @input="changeApprovalPrice(scope.row)"
                  validate-event
                  v-input-fixed="2"
                  min="0"
                  type="number"
                >
                </el-input>
              </el-form-item>
            </template>
          </el-table-column>
          <el-table-column prop="ApprovedTotalAmount" label="小计(元)">
            <template slot-scope="scope">
              {{ scope.row.ApprovedTotalAmount | toFixed | NumFormat }}
            </template>
          </el-table-column>
        </el-table>
      </el-form>

      <div slot="footer">
        <el-button @click="refundDetaildialog" size="small" v-prevent-click>取 消</el-button>
        <el-button type="danger" @click="approvalEntityApplyProductClick(false)" size="small" v-prevent-click plain>审核驳回</el-button>
        <el-button type="primary" @click="approvalEntityApplyProductClick(true)" :loading="approvedPaasLoading" size="small" v-prevent-click
          >审核通过</el-button
        >
      </div>
    </el-dialog>

    <!-- 确认付款  -->
    <el-dialog
      v-if="confirmPaymentDialogVisible"
      custom-class="entityApplyProductDialogClass"
      title="确认付款"
      :visible.sync="confirmPaymentDialogVisible"
      width="1100px"
      :close-on-click-modal="false"
    >
      <div class="tip" style="margin-top: 0">退货信息</div>
      <!-- size="small" -->
      <el-form
        v-if="applyDetailInfo"
        size="small"
        class="PaymententityApplyProductInfoFrom"
        label-width="110px"
        :model="applyDetailInfo"
        :rules="approveEntityRules"
        ref="PaymentWayapprovalDetailRef"
      >
        <el-row>
          <el-col :span="8">
            <el-form-item label="申请仓库/门店：" prop="OutboundEntityName">
              {{ applyDetailInfo.RefundOutboundEntityName }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="退货状态：" prop="BillStatus">
              {{ ApplyOrderBillStatusFormatter(applyDetailInfo) }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="申请人："> {{ applyDetailInfo.CreatedByName }}</el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="8">
            <el-form-item label="申请日期：">
              {{ applyDetailInfo.CreatedOn | dateFormat("YYYY-MM-DD HH:mm") }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="收货仓库：">{{ applyDetailInfo.RefundInboundEntityName }}</el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="结算方式：">{{ settlementWayTitleFormat(applyDetailInfo.SettlementWay) }}</el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="备注信息：">
              <div>{{ applyDetailInfo.Remark }}</div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="付款方式：" prop="PaymentWay">
              <el-select size="small" v-model="applyDetailInfo.PaymentWay" @change="changePaymentWayClick" placeholder="请选择">
                <el-option label="离线转账" value="10"></el-option>
                <el-option label="退回余额" value="20"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <!-- <el-col :span="8">
            <el-form-item label="剩余本金：">{{
              applyDetailInfo.Balance | NumFormat
            }}</el-form-item>
          </el-col> -->
          <!-- <el-col :span="8">
            <el-form-item label="剩余赠金：">{{
              applyDetailInfo.LargessBalance | NumFormat
            }}</el-form-item>
          </el-col> -->
          <el-col :span="8" v-show="applyDetailInfo.PaymentWay == 10">
            <el-form-item label="回单号码：" prop="ReceiptNumber">
              <el-input v-model="applyDetailInfo.ReceiptNumber" placeholder="请输入回单号码" size="small"></el-input>
            </el-form-item>
          </el-col>

          <el-col :span="8" v-show="applyDetailInfo.PaymentWay == 10">
            <el-form-item label="付款户名：" prop="PaymentAccountName">
              <el-input v-model="applyDetailInfo.PaymentAccountName" placeholder="请输入付款户名" size="small"></el-input>
            </el-form-item>
          </el-col>

          <el-col :span="8" v-show="applyDetailInfo.PaymentWay == 10">
            <el-form-item label="付款备注：" prop="Remark">
              <el-input v-model="applyDetailInfo.RemarkPayment" placeholder="请输入付款备注信息" size="small"></el-input>
            </el-form-item>
          </el-col>

          <el-col :span="8" v-show="applyDetailInfo.PaymentWay == 20">
            <el-form-item label="退回本金：" prop="Balance_payment">
              <el-input
                v-model="applyDetailInfo.Balance_payment"
                @input="changPaymentBalance"
                placeholder="请输入付款本金金额"
                type="number"
                class="custom_input"
                size="small"
              ></el-input>
            </el-form-item>
          </el-col>

          <el-col :span="8" v-show="applyDetailInfo.PaymentWay == 20">
            <el-form-item label="退回赠金：" prop="LargessBalance_payment">
              <el-input
                v-model="applyDetailInfo.LargessBalance_payment"
                @input="changePaymentLargessBalance"
                placeholder="请输入付款赠金金额"
                type="number"
                class="custom_input"
                size="small"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <div class="tip martp_10">产品明细</div>

        <el-table size="small" :data="applyDetailInfo.Detail" :show-summary="true" :summary-method="getSummaries">
          <el-table-column type="index" width="50"></el-table-column>
          <el-table-column label="产品名称" prop="ProductName">
            <template slot-scope="scope">
              <div>
                {{ scope.row.ProductName }}<span v-if="scope.row.Alias" class="color_gray font_12"> ({{ scope.row.Alias }})</span>
              </div>
              <div v-if="scope.row.Specification" class="color_gray font_12">规格：{{ scope.row.Specification }}</div>
            </template>
          </el-table-column>
          <el-table-column prop="ApplyQuantity" label="申请退货数量">
            <template slot-scope="scope">
              <div>{{ scope.row.ApproveQuantity }} {{ scope.row.UnitName }}</div>
              <div class="color_gray font_12">
                最小包装数量：{{ scope.row.ApproveMinimumUnitQuantity }}
                {{ scope.row.MinimumUnitName }}
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="ApplyQuantity" label="实发数量">
            <template slot-scope="scope">
              <div>{{ scope.row.RefundOutboundQuantity }} {{ scope.row.UnitName }}</div>
              <div class="color_gray font_12">
                最小包装数量：{{ scope.row.RefundOutboundMinimumUnitQuantity }}
                {{ scope.row.MinimumUnitName }}
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="ApplyQuantity" label="实入数量">
            <template slot-scope="scope">
              <div>{{ scope.row.RefundInboundQuantity }} {{ scope.row.UnitName }}</div>
              <div class="color_gray font_12">
                最小包装数量：{{ scope.row.RefundInboundMinimumUnitQuantity }}
                {{ scope.row.MinimumUnitName }}
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="ApprovedPrice" label="审核单价(元)">
            <template slot-scope="scope">
              <div>
                {{ scope.row.ApprovedPrice }}
              </div>
            </template>
          </el-table-column>

          <el-table-column prop="ApprovedTotalAmount" label="小计(元)">
            <template slot-scope="scope">
              {{ scope.row.RefundInboundTotalAmount | toFixed | NumFormat }}
            </template>
          </el-table-column>
        </el-table>
      </el-form>

      <div slot="footer">
        <el-button @click="confirmPaymentDialogVisible = false" size="small" v-prevent-click>取 消</el-button>
        <el-button type="primary" @click="saveConfirmPaymentClick(true)" :loading="approvedPaasLoading" size="small" v-prevent-click>确认付款</el-button>
      </div>
    </el-dialog>

    <!-- 驳回审核 -->
    <el-dialog width="600px" :visible.sync="finalRejectionDialogVisible" title="审核驳回">
      <el-input type="textarea" :rows="4" v-model="finalRejection" placeholder="请输入备注内容"></el-input>
      <span slot="footer" class="dialog-footer">
        <el-button @click="finalRejectionDialogVisible = false" v-prevent-click size="small">取消</el-button>
        <el-button type="danger" @click="finalRejectApprovedClick" :loading="approvedRefuseLoading" v-prevent-click size="small">驳回审核</el-button>
      </span>
    </el-dialog>

    <!-- 待退货弹框-->
    <el-dialog
      custom-class="entityApplyProductDialogClass"
      title="配送出库"
      :visible.sync="outboundDetaildialogVisible"
      width="1100px"
      :close-on-click-modal="false"
    >
      <div class="tip">退货信息</div>
      <el-form
        v-if="applyDetailInfo"
        :inline="true"
        :inline-message="true"
        class="entityApplyProductInfoFrom"
        label-width="110px"
        size="small"
        :model="applyDetailInfo"
        :rules="approveEntityRules"
        ref="outboundDetailRef"
      >
        <el-row>
          <el-col :span="8">
            <el-form-item label="申请仓库/门店：" prop="InboundEntityName">
              {{ applyDetailInfo.RefundOutboundEntityName }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="申请人："> {{ applyDetailInfo.CreatedByName }}</el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="申请日期：">
              {{ applyDetailInfo.CreatedOn | dateFormat("YYYY-MM-DD HH:mm") }}
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="8">
            <el-form-item label="收货仓库：" prop="OutboundEntityName">
              {{ applyDetailInfo.RefundInboundEntityName }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="审批人：" prop="ApprovedByName">
              {{ applyDetailInfo.ApprovedByName }}
            </el-form-item>
          </el-col>

          <el-col :span="8">
            <el-form-item label="审批日期：" prop="InDate">
              {{ applyDetailInfo.ApprovedOn | dateFormat("YYYY-MM-DD HH:mm") }}
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="备注信息：" class="padbm_10 padtp_5">
              <el-input
                type="textarea"
                style="width: 280px"
                :autosize="{ minRows: 1, maxRows: 3 }"
                v-model="applyDetailInfo.outRemark"
                placeholder="请输入备注信息"
                size="small"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <div class="tip">产品信息</div>
        <el-table size="small" :data="applyDetailInfo.Detail">
          <el-table-column type="index" width="50"></el-table-column>
          <el-table-column label="产品名称" prop="ProductName">
            <template slot-scope="scope">
              <div>
                {{ scope.row.ProductName }}<span v-if="scope.row.Alias" class="color_gray font_12"> ({{ scope.row.Alias }})</span>
              </div>
              <div v-if="scope.row.Specification" class="color_gray font_12">规格：{{ scope.row.Specification }}</div>
            </template>
          </el-table-column>
          <el-table-column prop="StockQuantity" label="可退库存">
            <template slot-scope="scope">
              {{ scope.row.StockQuantity }}
              <span>{{ scope.row.MinimumUnitName }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="ApplyQuantity" label="申请退货数量">
            <template slot-scope="scope">
              <!-- <div>{{ scope.row.ApplyQuantity }} {{ scope.row.UnitName }}</div> -->
              <div>{{ scope.row.ApproveQuantity }} {{ scope.row.UnitName }}</div>
              <div class="color_gray font_12">
                最小包装数量：{{ scope.row.RefundMinimumUnitQuantity }}
                {{ scope.row.MinimumUnitName }}
              </div>
            </template>
          </el-table-column>
          <el-table-column label="实际退货数量">
            <template slot-scope="scope">
              <el-form-item
                :show-message="false"
                :prop="'Detail.' + scope.$index + '.RefundOutboundQuantity'"
                :rules="approveEntityRules.RefundOutboundQuantity"
              >
                <el-input
                  v-model="scope.row.RefundOutboundQuantity"
                  size="small"
                  style="min-width: 130px"
                  class="input_type"
                  placeholder="请输入实发数量"
                  @input="changeOutboundQuantity(scope.row)"
                  validate-event
                  v-enter-number2
                  v-enterInt
                  min="0"
                  :max="scope.row.ApproveQuantity"
                  type="number"
                  :disabled="scope.row.OutboundIsLock"
                >
                  <template slot="append">{{ scope.row.UnitName }}</template>
                </el-input>
              </el-form-item>
            </template>
          </el-table-column>
          <el-table-column label="实际退货最小包装数量">
            <template slot-scope="scope">
              <div v-if="!scope.row.OutboundIsLock">
                {{ scope.row.ApproveMinimumUnitQuantity }}
                {{ scope.row.MinimumUnitName }}
              </div>
              <el-tag v-else size="mini" type="warning">{{ "盘点锁定" }}</el-tag>
            </template>
          </el-table-column>
        </el-table>
      </el-form>
      <div slot="footer">
        <el-button @click="outboundDetaildialogVisible = false" size="small" v-prevent-click>取 消</el-button>
        <el-button type="primary" @click="saveOutboundEntityApplyProductClick" :loading="outboundLoading" size="small" v-prevent-click>退货出库</el-button>
      </div>
    </el-dialog>

    <!-- 待入库弹框 -->
    <el-dialog custom-class="entityApplyProductDialogClass" title="退货入库" :visible.sync="inboundDetaildialogVisible" width="1100px">
      <div class="tip">退货信息</div>
      <el-form
        v-if="applyDetailInfo"
        :inline="true"
        :inline-message="true"
        class="entityApplyProductInfoFrom"
        label-width="110px"
        size="small"
        :model="applyDetailInfo"
        :rules="approveEntityRules"
        ref="InboundDetailRef"
      >
        <!-- 申请信息 -->
        <el-row>
          <el-col :span="8">
            <el-form-item label="申请仓库/门店：" prop="InboundEntityName">
              {{ applyDetailInfo.RefundOutboundEntityName }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="申请人："> {{ applyDetailInfo.CreatedByName }}</el-form-item>
          </el-col>

          <el-col :span="8">
            <el-form-item label="入库备注信息：">
              <el-input
                type="textarea"
                :autosize="{ minRows: 1, maxRows: 3 }"
                v-model="applyDetailInfo.inRemark"
                placeholder="请输入备注信息"
                size="small"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <div class="tip martp_10">产品信息</div>
        <el-table class="martp_10" size="small" :data="applyDetailInfo.Detail">
          <el-table-column type="index" width="50"></el-table-column>
          <el-table-column label="产品名称" prop="ProductName">
            <template slot-scope="scope">
              <div>
                {{ scope.row.ProductName }}<span v-if="scope.row.Alias" class="color_gray font_12"> ({{ scope.row.Alias }})</span>
              </div>
              <div v-if="scope.row.Specification" class="color_gray font_12">规格：{{ scope.row.Specification }}</div>
            </template>
          </el-table-column>
          <el-table-column prop="ApplyQuantity" label="申请退货数量">
            <template slot-scope="scope">
              <div>{{ scope.row.ApproveQuantity }} {{ scope.row.UnitName }}</div>
              <div class="color_gray font_12">
                最小包装数量：{{ scope.row.ApproveMinimumUnitQuantity }}
                {{ scope.row.MinimumUnitName }}
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="OutboundQuantity" label="实际退货数量">
            <template slot-scope="scope">
              <div>{{ scope.row.RefundOutboundQuantity }} {{ scope.row.UnitName }}</div>
              <div class="color_gray font_12">
                最小包装数量：{{ scope.row.RefundOutboundMinimumUnitQuantity }}
                {{ scope.row.MinimumUnitName }}
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="RefundInboundQuantity" label="实收数量">
            <template slot-scope="scope">
              <el-form-item :show-message="false" :prop="'Detail.' + scope.$index + '.RefundInboundQuantity'" :rules="approveEntityRules.RefundInboundQuantity">
                <el-input
                  v-model="scope.row.RefundInboundQuantity"
                  class="input_type"
                  size="small"
                  placeholder="请输入入库数量"
                  @input="changeInboundQuantity(scope.row)"
                  validate-event
                  v-enter-number2
                  v-enterInt
                  min="0"
                  :max="scope.row.RefundOutboundQuantity"
                  type="number"
                  :disabled="scope.row.InboundIsLock"
                >
                  <template slot="append">{{ scope.row.UnitName }}</template>
                </el-input>
              </el-form-item>
            </template>
          </el-table-column>
          <el-table-column prop="stockoutQuantity" label="实收最小包装数量">
            <template slot-scope="scope">
              <div v-if="!scope.row.InboundIsLock">
                {{ scope.row.RefundInboundMinimumUnitQuantity }}
                <!-- {{
                  parseFloat(scope.row.MinimumUnitAmount || 0) *
                  parseFloat(scope.row.RefundInboundQuantity || 0)
                }} -->
                {{ scope.row.MinimumUnitName }}
              </div>
              <el-tag v-else size="mini" type="warning">{{ "盘点锁定" }}</el-tag>
            </template>
          </el-table-column>
        </el-table>
      </el-form>

      <div slot="footer">
        <el-button @click="inboundDetaildialogVisible = false" size="small" v-prevent-click>取 消</el-button>
        <el-button type="primary" @click="saveInboundEntityApplyProductClick" :loading="outboundLoading" size="small" v-prevent-click>确认入库</el-button>
      </div>
    </el-dialog>

    <!-- 入库 详情 -->
    <el-dialog custom-class="entityApplyProductDialogClass" title="要货入库详情" :visible.sync="InboundInfoDialogVisible" width="1000px">
      <div class="tip">退货信息</div>
      <el-form class="entityApplyProductInfoFrom" :inline="true" :inline-message="true" label-width="100px" size="small" :model="InboundInfo">
        <el-row>
          <el-col :span="6">
            <el-form-item label="入库仓库/门店：" label-width="110px">{{ InboundInfo.EntityName }}</el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item label="操作人：">{{ InboundInfo.CreatedByName }}</el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="入库时间：">{{ InboundInfo.InDate | dateFormat("YYYY-MM-DD HH:mm") }}</el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form-item label="备注信息：">{{ InboundInfo.Remark }}</el-form-item>
          </el-col>
        </el-row>

        <div class="tip">产品明细</div>
        <el-table size="small" :data="InboundInfo.Detail">
          <el-table-column type="index" width="50"></el-table-column>
          <el-table-column label="产品" prop="ProductName">
            <template slot-scope="scope">
              <div>
                {{ scope.row.ProductName }}<span v-if="scope.row.Alias" class="color_gray font_12"> ({{ scope.row.Alias }})</span>
              </div>
              <div v-if="scope.row.Specification" class="color_gray font_12">规格：{{ scope.row.Specification }}</div>
            </template>
          </el-table-column>
          <el-table-column label="入库数量" prop="Quantity">
            <template slot-scope="scope">
              <div>{{ scope.row.Quantity || 0 }} {{ scope.row.UnitName }}</div>
              <div class="color_gray font_12">
                最小包装数量：{{ scope.row.MinimumUnitQuantity || 0 }}
                {{ scope.row.MinimumUnitName }}
              </div>
            </template>
          </el-table-column>
        </el-table>
      </el-form>

      <div slot="footer">
        <el-button v-show="templateTypeList && templateTypeList.length == 0" type="primary" @click="printInfoTips" size="small" v-prevent-click>打印</el-button>
        <el-button
          v-show="templateTypeList && templateTypeList.length == 1"
          type="primary"
          v-print="'printContent'"
          @click="printInfo"
          size="small"
          v-prevent-click
          >打印</el-button
        >
        <el-button v-show="templateTypeList && templateTypeList.length > 1" type="primary" @click="printInfoSelectTemplate" size="small" v-prevent-click
          >打印</el-button
        >
      </div>
    </el-dialog>

    <!-- 详情-->
    <el-dialog custom-class="entityApplyProductDialogClass" title="要货退货出库详情" :visible.sync="OutboundInfoDialogVisible" width="1000px">
      <div class="tip">退货信息</div>
      <el-form class="entityApplyProductInfoFrom" :inline="true" :inline-message="true" label-width="100px" size="small" :model="OutboundInfo">
        <el-row>
          <el-col :span="7">
            <el-form-item label="出库仓库/门店：" label-width="110px">{{ OutboundInfo.EntityName }}</el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item label="操作人：">{{ OutboundInfo.CreatedByName }}</el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="出库时间：" prop="OutDate">{{ OutboundInfo.OutDate | dateFormat("YYYY-MM-DD HH:mm") }}</el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form-item label="备注信息：">{{ OutboundInfo.Remark }}</el-form-item>
          </el-col>
        </el-row>
        <div class="tip">产品明细</div>
        <el-table size="small" :data="OutboundInfo.Detail">
          <el-table-column type="index" width="50"></el-table-column>
          <el-table-column label="产品" prop="ProductName">
            <template slot-scope="scope">
              <div>
                {{ scope.row.ProductName }}<span v-if="scope.row.Alias" class="color_gray font_12"> ({{ scope.row.Alias }})</span>
              </div>
              <div v-if="scope.row.Specification" class="color_gray font_12">规格：{{ scope.row.Specification }}</div>
            </template>
          </el-table-column>
          <el-table-column prop="Quantity" label="出库数量">
            <template slot-scope="scope">
              <div>{{ scope.row.Quantity || 0 }} {{ scope.row.UnitName }}</div>
              <div class="color_gray font_12">
                最小包装数量：{{ scope.row.MinimumUnitQuantity || 0 }}
                {{ scope.row.MinimumUnitName }}
              </div>
            </template>
          </el-table-column>
        </el-table>
      </el-form>

      <div slot="footer">
        <el-button v-show="templateTypeList && templateTypeList.length == 0" type="primary" @click="printInfoTips" size="small" v-prevent-click>打印</el-button>
        <el-button
          v-show="templateTypeList && templateTypeList.length == 1"
          type="primary"
          v-print="'printContent'"
          @click="printInfo"
          size="small"
          v-prevent-click
          >打印</el-button
        >
        <el-button v-show="templateTypeList && templateTypeList.length > 1" type="primary" @click="printInfoSelectTemplate" size="small" v-prevent-click
          >打印</el-button
        >
      </div>
    </el-dialog>

    <!-- 打印 -->
    <el-dialog title="选择打印模板" :visible.sync="printTemplateVisible" width="400px">
      <el-select size="small" v-model="printTemplateID" @change="changePrintTemplate">
        <el-option v-for="item in templateTypeList" :key="item.ID" :label="item.Name" :value="item.ID"></el-option>
      </el-select>
      <div slot="footer">
        <el-button @click="printTemplateVisible = false" size="small" v-prevent-click>取消</el-button>
        <el-button v-print="'printContent'" type="primary" @click="confirmPrintTemplate" size="small" v-prevent-click>打印</el-button>
      </div>
    </el-dialog>
    <div style="display: none">
      <component id="printContent" :is="printComponentName"></component>
    </div>

    <!-- 选择商品弹窗 -->
    <el-dialog title="选择商品" :visible.sync="showProductVisible" :close-on-click-modal="false">
      <el-row>
        <el-col :span="8" class="pad_10_0">
          <el-input
            size="small"
            v-model="ProductName"
            clearable
            @clear="handleSearchProductClick"
            @keyup.enter.native="handleSearchProductClick"
            placeholder="输入商品名称搜索"
          ></el-input>
        </el-col>
        <el-col :span="2" class="pad_10_0 marlt_10">
          <el-button @click="handleSearchProductClick" size="small" type="primary" v-prevent-click>搜索</el-button>
        </el-col>
      </el-row>
      <div>
        <el-table size="small" :data="ProductList" max-height="480px" :row-key="(row) => row.ID" @selection-change="getSelectProduct" ref="productTable">
          <el-table-column type="selection" width="55" :selectable="checkboxSelect" :reserve-selection="true"></el-table-column>
          <el-table-column prop="ProductName" label="商品名称">
            <template slot-scope='scope'>
              {{scope.row.ProductName}}
            <el-tag v-if="scope.row.IsLock" type="warning" size="small">库存盘点中</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="Specification" label="产品规格"></el-table-column>
          <el-table-column prop="PCategoryName" label="品牌名称"></el-table-column>
          <el-table-column prop="Quantity" label="可退库存"></el-table-column>
          <el-table-column prop="DeliveryPrice" label="配销价格(元)">
            <template slot-scope="scope">
              {{ scope.row.DeliveryPrice | toFixed | NumFormat }}
            </template>
          </el-table-column>
          <el-table-column label="最小包装单位数量">
            <template slot-scope="scope">
              <span v-for="(item, index) in scope.row.Unit" :key="index">
                <span v-if="item.UnitName">{{ item.Amount }}/{{ item.UnitName }}</span>
              </span>
            </template>
          </el-table-column>
        </el-table>
        <div class="pad_15 text_right">
          <el-pagination
            background
            v-if="ProductPaginations.total > 0"
            @current-change="ProductChange"
            :current-page.sync="ProductPaginations.page"
            :page-size="ProductPaginations.page_size"
            :layout="ProductPaginations.layout"
            :total="ProductPaginations.total"
          ></el-pagination>
        </div>
      </div>

      <span slot="footer" class="dialog-footer">
        <el-button @click="showProductVisible = false" v-prevent-click size="small">取 消</el-button>
        <el-button type="primary" size="small" @click="submitFormApplicableDuty" v-prevent-click>确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import permission from "@/components/js/permission.js";
import APIStorage from "@/api/PSI/Purchase/storage";
// import APIInquire from  "@/api/PSI/Inventory/Inquire";
// import APIInventory from "@/api/PSI/Inventory/inventoryDetail";
import APIPSIApplyProduct from "@/api/PSI/Purchase/entityApplyProduct";
import APIPSIRefundProduct from "@/api/PSI/Purchase/entityRefundProduct";
import APIInbound from "@/api/PSI/Inventory/inventoryProductInbound";
import APIOutbound from "@/api/PSI/Inventory/inventoryProductOutbound";

import dateUtil from "@/components/js/date";

var Enumerable = require("linq");
import print from "vue-print-nb";
import Vue from "vue";

export default {
  name: "PurchaseEntityRefundProduct",
  directives: {
    print,
  },
  /**  Vue 实例的数据对象**/
  data() {
    return {
      selectProductList: [],
      ProductName: "",
      showProductVisible: false,
      saveDraftLoading: false,
      printComponentName: "",
      printContent: "",
      printTemplateVisible: false,
      printTemplateID: "",
      templateTypeList: [],
      TemplateType: "entityrefundstock",
      clickdelite: true,
      clickout: true,

      /**  权限功能按钮   */
      isAdd: false, // 新建退货
      isDelivery: false, // 待退货
      isCheck: false, // 审核单据
      isClose: false, // 关闭待审核单据
      isTurnClose: false, // 关闭已驳回单据单据
      isStorage: false, // 要货入库
      isPaymentConfirm: false, // 确认收款

      // 一下两个功能权限暂时不写 【功能模块没有写】
      isViewDeliveryBill: false, // 查看配送出库单
      isViewStorageyBill: false, // 查看要货入库单

      loading: false,
      approvedLoading: false,
      outboundLoading: false,
      modalLoading: false,

      approvedRefuseLoading: false, //审批驳货
      approvedPaasLoading: false, // 审批通过

      dialogVisible: false,
      applyDetaildialogVisible: false, //要货详情
      approvalDetaildialogVisible: false, // 审批
      finalRejectionDialogVisible: false,
      outboundDetaildialogVisible: false, // 审批出库
      inboundDetaildialogVisible: false, //入库

      selectProductDialogVisible: false,
      isAddOrEdit: false, // 是否新增
      InboundInfoDialogVisible: false, //入库详情
      OutboundInfoDialogVisible: false, // 出库
      productLoading: false,

      removeDisabled: true, //  选中产品删除按钮是否禁用
      multipleProducts: [], // 已选中的将要删除的产品

      confirmPaymentDialogVisible: false,
      confirmPaymentInfo: "", // 确认付款信息
      // 列表筛选条件
      searchForm: {
        ID: "",
        Name: "", //产品名称
        DateTime: "",
        BillStatus: "0", //单据状态，10：待审核、20：待配送、30：待入库、40：已驳回、50：已完成、60：已取消
        EntityID: "",
      },
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now() ? true : false;
        },
      },
      searchProductName: "",
      //创建要货 验证规则
      entityApplyProduct: {
        InDate: dateUtil.formatDate.format(new Date(), "YYYY-MM-DD hh:mm"), //入库时间
        EntityID: "", //仓库ID
        EntityName: "",
        LargessBalance: "",
        TotalBalance: "",
        Remark: "", // 备注
        Product: [], // 产品列表
      },
      entityApplyProductRules: {
        EntityID: [
          {
            required: true,
            message: "请选择仓库/门店",
            trigger: ["blur", "change"],
          },
        ],
        ApplyQuantity: [{ required: true, trigger: ["blur", "change"] }],
        ProductID: [{ required: true, trigger: ["blur", "change"] }],
        UnitID: [{ required: true, trigger: ["blur", "change"] }],
        DeliveryPrice: [{ required: true, trigger: ["blur", "change"] }],
      }, //产品验证规则

      // 要货列表
      inventoryApplyList: [],
      paginations: {
        page: 1, // 当前位于哪页
        total: 0, // 总数
        page_size: 10, // 1页显示多少条
        layout: "total, prev, pager, next, jumper", // 翻页属性
      }, //需要给分页组件传的信息
      purchaseStorage: [], //仓库列表
      OutboundEntitys: [], // 审批要货仓库 排除入库仓
      // 要货产品信息
      ProductList: [],
      ProductListTotal: 0,
      productPageNum: 1,
      applyDetailInfo: null,
      approveEntityRules: "",
      finalRejection: "", //驳回原因

      StatusNumberInfo: "", // 个状态数量
      InboundInfo: "",
      OutboundInfo: "",
      editProductName: [],
      ProductPaginations: {
        page: 1, // 当前位于哪页
        total: 0, // 总数
        page_size: 10, // 1页显示多少条
        layout: "total, prev, pager, next, jumper", // 翻页属性
      }, //需要给分页组件传的信息
    };
  },
  /**  路由  */
  beforeRouteEnter(to, from, next) {
    next((vm) => {
      /**  新建退货  */
      vm.isAdd = permission.permission(to.meta.Permission, "PSI-SupplyChain-EntityRefundProduct-Add");
      /**  等待退货 */
      vm.isDelivery = permission.permission(to.meta.Permission, "PSI-SupplyChain-EntityRefundProduct-Delivery");
      /**  审核单据  */
      vm.isCheck = permission.permission(to.meta.Permission, "PSI-SupplyChain-EntityRefundProduct-Check");
      /** 关闭待审核单据 */
      vm.isClose = permission.permission(to.meta.Permission, "PSI-SupplyChain-EntityRefundProduct-Close");
      /** 关闭已驳回单据 */
      vm.isTurnClose = permission.permission(to.meta.Permission, "PSI-SupplyChain-EntityRefundProduct-RejectClose");

      /**  入库 */
      vm.isStorage = permission.permission(to.meta.Permission, "PSI-SupplyChain-EntityRefundProduct-Storage");
      // /**  查看配送出库单 */
      vm.isViewDeliveryBill = permission.permission(to.meta.Permission, "PSI-SupplyChain-EntityRefundProduct-ViewDeliveryBill");
      // /**  查看要货入库单  */
      vm.isViewStorageyBill = permission.permission(to.meta.Permission, "PSI-SupplyChain-EntityRefundProduct-ViewStorageyBill");
      /** 确认付款审核单据 */
      vm.isPaymentConfirm = permission.permission(to.meta.Permission, "PSI-SupplyChain-EntityRefundProduct-PaymentConfirm");
    });
  },
  /**  方法集合  */
  methods: {
    /**  申请合计   */
    entityApplyProductDetailedSummary({ columns }) {
      const sums = [];
      columns.forEach((column, index) => {
        if (index == 0) {
          sums[index] = "总计";
        } else if (index == columns.length - 1) {
          let total = parseFloat(
            this.entityApplyProduct.Product &&
              this.entityApplyProduct.Product.reduce((perVal, nexVal) => {
                return perVal + parseFloat(nexVal.DeliveryTotalPrice || 0);
              }, 0)
          ).toFixed(2);
          let filterNumFormat = this.$options.filters["NumFormat"];

          sums[index] = <span class="font_weight_600">{filterNumFormat(total)}</span>;
        } else if (index == columns.length - 4) {
          let applyQuantityTotal = parseInt(
            this.entityApplyProduct.Product &&
              this.entityApplyProduct.Product.reduce((perVal, nexVal) => {
                return perVal + parseInt(nexVal.ApplyQuantity || 0);
              }, 0)
          );
          sums[index] = <span class="font_weight_600">{applyQuantityTotal}</span>;
        } else {
          sums[index] = "";
        }
      });
      return sums;
    },

    /**  格式化明细商品名称 中间以... 代替  */
    formatProductName(name) {
      if (!name) return "";
      if (name.length > 20) {
        let frontStr = name.substr(0, 8);
        let afterStr = name.substr(name.length - 10, name.length);
        return frontStr + " ... " + afterStr;
      }
      return name;
    },
    /**  获取产品是否显示提示  */
    getShowProductNameTooltip(name) {
      if (!name || name == "") {
        return true;
      }
      if (name.length > 20) {
        return false;
      }
      return true;
    },
    /** 确定选择打印模板，并打印   */
    confirmPrintTemplate() {
      let that = this;
      that.printTemplateVisible = false;

      that.$nextTick(() => {
        /* that.clickdelite?that.applyDetailInfo:that.clickout?that.OutboundInfo:that.InboundInfo */
        that.createPrintComponent(that.clickdelite ? that.applyDetailInfo : that.clickout ? that.OutboundInfo : that.InboundInfo, that.printContent);
      });
    },
    /** 修改打印模板   */
    changePrintTemplate(val) {
      let that = this;
      let tempItem = that.templateTypeList.filter((item) => item.ID == val);
      that.printContent = tempItem[0].Template;
    },
    // 创建打印组件
    createPrintComponent(info, printContent) {
      let tempInfo = info; //传入打印数据源
      let templateStr = printContent; //传入打印模板
      var timestamp = new Date().valueOf();
      var componentName = "print" + timestamp;

      //创建组件
      Vue.component(componentName, {
        data: function () {
          return {
            info: tempInfo, //传入打印数据源
          };
        },
        template: "<div>" + templateStr + "</div>", //打印模板
      });
      this.printComponentName = componentName; //显示打印组件
    },

    /**  打印  */
    /**    */
    printInfoTips() {
      let that = this;
      that.$message.error("暂无打印模板，请添加打印模板");
    },
    printInfoSelectTemplate() {
      let that = this;
      that.printTemplateID = "";
      that.printTemplateVisible = true;
    },
    printInfo() {
      let that = this;
      let tempPrintTemplate = that.templateTypeList.length == 1 ? that.templateTypeList[0].Template : "";
      /* that.clickdelite?that.applyDetailInfo:that.clickout?that.OutboundInfo:that.InboundInfo */
      that.createPrintComponent(that.clickdelite ? that.applyDetailInfo : that.clickout ? that.OutboundInfo : that.InboundInfo, tempPrintTemplate);
    },
    closeOutboundInfoDialog() {
      let that = this;
      that.clickdelite = true;
      that.TemplateType = "entityapplystock";
      that.getPrintTemplate_list();
    },
    closeInboundInfoDialog() {
      let that = this;
      that.clickdelite = true;
      that.TemplateType = "entityapplystock";
      that.getPrintTemplate_list();
    },
    /**    */
    settlementWayTitleFormat(type) {
      switch (type) {
        case "10":
          return "先货后款";
        case "20":
          return "先款后货";
        default:
          return "";
      }
    },
    /**  点击搜索  */
    handleSearchEntityApplyProductClick() {
      let that = this;
      // that.searchForm.BillStatus = "0";
      that.paginations.page = 1;
      // that.getInventoryApplyListNetwork();
      that.getInventoryRefundListNetwork();
    },
    /**  时间修改  */
    searchDateChange() {
      let that = this;
      that.handleSearchEntityApplyProductClick();
    },

    /** 要货列表 分页切换  */
    EntityApplyProductListHandleCurrentChange(page) {
      let that = this;
      that.paginations.paginations = page;
      // that.getInventoryApplyListNetwork();
      that.getInventoryRefundListNetwork();
    },
    /**  订单状态数据格式化  */
    ApplyOrderBillStatusFormatter(row) {
      // 05：草稿、 10：待审核、20：待配送、30：待入库、40：已驳回、50：已完成、60：已取消
      if (!row) {
        return "";
      }
      switch (row.BillStatus) {
        case "05":
          return "草稿";
        case "10":
          return "待审核";
        case "20":
          return "待退货";
        case "30":
          return "待入库";
        case "40":
          return "待付款";
        case "50":
          return "已驳回";
        case "60":
          return "已完成";
        case "70":
          return "已取消";
      }
    },
    /**  点击tabs 切换  */
    tabsHandleClick() {
      var that = this;
      that.paginations.page = 1;
      // that.getInventoryApplyListNetwork();
      that.getInventoryRefundListNetwork();
    },

    /**  添加要货申请  */
    addEntityApplyProductClick() {
      var that = this;
      that.entityApplyProduct = {
        InDate: dateUtil.formatDate.format(new Date(), "YYYY-MM-DD hh:mm"), //入库时间
        EntityID: "", //仓库ID
        EntityName: "",
        Remark: "", // 备注
        Product: [], // 产品列表
      };
      if (that.purchaseStorage.length == 1) {
        that.entityApplyProduct.EntityID = that.purchaseStorage[0].ID;
        that.entityApplyProduct.EntityName = that.purchaseStorage[0].EntityName;
      }
      that.dialogVisible = true;
      that.isAddOrEdit = true;
    },
    // 驳回的编辑
    updateEntityProductInfo(row) {
      let that = this;
      that.getinventoryApplyInfoNetwork(row.ID, 5);
    },
    editDraft(row) {
      let that = this;
      that.getinventoryApplyInfoNetwork(row.ID, 6);
    },
    /** 关闭新增弹窗的回调   */
    closeAddApplyProduct() {
      let that = this;
      that.$refs["entityApplyProductRef"].clearValidate();
      if (that.$refs.multipleTable) {
        that.$refs.multipleTable.clearSelection();
      }
    },
    /**  选择 要货仓库  */
    handleSelectProductEntity(row) {
      let that = this;
      that.entityApplyProduct.EntityID = row.ID;
      that.entityApplyProduct.LargessBalance = row.LargessBalance;
      that.entityApplyProduct.TotalBalance = row.TotalBalance;
      that.entityApplyProduct.Balance = row.Balance;

      that.entityApplyProduct.Product = [];
    },
    /**  要货添加产品 -- 新增 */
    addProducts() {
      let that = this;
      that.ProductPaginations.page = 1;

      that.$refs["entityApplyProductRef"].validateField("EntityID", (valid) => {
        if (!valid) {
          that.ProductName = "";
          that.get_stock_list_entityProductListNetwork();
          that.showProductVisible = true;
          that.selectProductList = [];
          that.$nextTick(() => {
            this.$refs.productTable.clearSelection();
          });
        }
      });
    },
    /* 产品搜索 */
    handleSearchProductClick() {
      let that = this;
      that.ProductPaginations.page = 1;
      that.get_stock_list_entityProductListNetwork();
    },
    /**   选择单位 */
    handleSelectProductUnit(val, row) {
      row.ApplyQuantity = "";
      row.miniUnitQuantity = "";
      row.UnitID = val.UnitID;
      row.UnitName = val.UnitName;
      row.miniAmount = val.Amount;
    },

    /**  修改要货数量  计算*/
    changeApplyQuantity(row) {
      // 退货单位数量
      row.ApplyQuantity = Math.floor(row.ApplyQuantity);
      // 最小包装单位数量
      row.miniUnitQuantity = parseFloat(row.ApplyQuantity) * parseFloat(row.miniAmount);

      if (this.entityApplyProduct.Product.length > 1) {
        var total = this.entityApplyProduct.Product.reduce((prev, cur) => {
          if (cur.ProductID !== row.ProductID) {
            return prev + 0;
          }
          return prev + cur.miniUnitQuantity;
        }, 0);
        if (total > row.Quantity) {
          this.$message.error({
            message: "退货总量不能大于可退库存",
            duration: 2000,
          });
          row.ApplyQuantity = "";
          row.miniUnitQuantity = "";
          row.DeliveryTotalPrice = "";
          return;
        }
      }
      // 配销价小计
      if (row.miniUnitQuantity > row.Quantity) {
        this.$message.error({
          message: "退货数量不能大于可退库存",
          duration: 2000,
        });
        row.ApplyQuantity = "";
        row.miniUnitQuantity = "";
        return;
      }
      row.DeliveryTotalPrice = parseFloat(row.miniUnitQuantity) * parseFloat(row.DeliveryPrice);
    },

    /**  修改配销价格  */
    changeDeliveryPrice(row) {
      if (row.ApplyQuantity) {
        row.DeliveryTotalPrice = parseFloat(parseFloat(row.miniUnitQuantity) * parseFloat(row.DeliveryPrice))
          .toFixed(3)
          .slice(0, -1);
      }
    },
    /**  产品 --- 列表切换分页 */
    ProductChange(page) {
      let that = this;
      that.ProductPaginations.page = page;
      that.get_stock_list_entityProductListNetwork();
    },

    /**  批量删除  */
    removeMultipleProduct() {
      var that = this;
      if (that.multipleProducts.length > 0) {
        for (var i = 0; i < that.entityApplyProduct.Product.length; i++) {
          that.multipleProducts.forEach(function (item) {
            if (that.entityApplyProduct.Product[i] == item) {
              that.entityApplyProduct.Product.splice(i, 1);
              i--;
            }
          });
        }
      }
    },

    /** 选中 产品 执行删除   */
    handleChangeSelectProduct(selection) {
      this.multipleProducts = selection;
      if (this.multipleProducts.length > 0) {
        this.removeDisabled = false;
      } else {
        this.removeDisabled = true;
      }
    },

    /** 保存要货申请信息   */
    saveEntityApplyProductClick() {
      let that = this;
      if (that.entityApplyProduct.Product.length == 0) {
        that.$message.error({
          message: "请选择要货产品",
          duration: 2000,
        });
        return;
      }
      that.$refs["entityApplyProductRef"].validate((valid) => {
        if (valid) {
          if (that.isAddOrEdit) {
            that.setInventoryApplyCreateNetwork();
          } else {
            that.entityRefundApply();
          }
        }
      });
    },
    /* 保存草稿 */
    saveDraftClick() {
      let that = this;
      if (that.entityApplyProduct.Product.length == 0) {
        that.$message.error({
          message: "请选择要货产品",
          duration: 2000,
        });
        return;
      }
      that.$refs["entityApplyProductRef"].validate((valid) => {
        if (valid) {
          that.saveDraft();
        }
      });
    },
    /**  关闭要货   */
    closeEntityApplyProductClick(row) {
      var that = this;
      that
        .$confirm("关闭退货后将无法继续退货, 确定要关闭吗?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
        .then(() => {
          that.setInventoryProductCancelNetwork(row.ID);
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消操作",
          });
        });
    },
    /**  审核驳回关闭  */
    turnCloseEntityApplyProductClick(row) {
      var that = this;
      that
        .$confirm("确定要关闭吗?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
        .then(() => {
          that.setinventoryProductCancelRejectApplylNetwork(row.ID);
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消操作",
          });
        });
    },

    /**  查看详情  */
    checkEntityApplyProductDetail(row) {
      let that = this;
      that.getinventoryApplyInfoNetwork(row.ID, 0);
    },

    /**  单价 标题动态修改  */
    formatApprovedPriceTitle(row, isPrice) {
      var that = this;
      switch (that.applyDetailInfo.BillStatus) {
        case "20":
          return isPrice ? "审核单价" : "小计(元)";
        case "30":
          return isPrice ? "配送单价" : "小计(元)";
        case "50":
          return isPrice ? "配送单价" : "小计(元)";
        default:
          return "";
      }
    },
    /**  要货金额统计  */
    formatterTotalAmount(row) {
      let that = this;
      let TotalAmount = "";
      switch (that.applyDetailInfo.BillStatus) {
        case "20":
          TotalAmount = row.ApprovedTotalAmount;
          break;

        case "30":
          TotalAmount = row.OutboundTotalAmount;
          break;

        case "40":
        case "50":
          TotalAmount = row.InboundTotalAmount;
          break;

        default:
          TotalAmount = "";
          break;
      }
      // NumFormat
      return TotalAmount;
    },
    /** 要货 审批 详情  */
    approvalEntityApplyProductDetail(row) {
      let that = this;
      that.getinventoryApplyInfoNetwork(row.ID, 1);
    },
    /**  审批 发货仓库选择  */
    deliveryHandleSelectProductEntity(row) {
      let that = this;
      let ProductIDs = Enumerable.from(that.applyDetailInfo.Detail)
        .select((i) => i.ProductID)
        .toArray();
      that.applyDetailInfo.EntityID = row.ID;
      that.getinventoryProductStockNetwork(ProductIDs, that.applyDetailInfo.EntityID);
    },
    /**  修改审批通过数量  */
    changeApprovalQuantity(row) {
      let that = this;
      row.ApproveQuantity = Math.floor(row.ApproveQuantity);
      // 下标
      let indexOf = that.applyDetailInfo.Detail.indexOf(row);
      // 排除 当前项的  已审批数量
      let totalQuantity = Enumerable.from(that.applyDetailInfo.Detail)
        .where((i, index) => {
          return i.ProductID == row.ProductID && index != indexOf;
        })
        .sum((i) => Number(i.ApproveQuantity) * Number(i.MinimumUnitAmount));
      // 剩余的最小单位数量
      let balanceMiniQuantity = parseFloat(row.StockQuantity) - parseFloat(totalQuantity);
      // 转换大单位数量
      let balanceQuantity = Math.floor(balanceMiniQuantity / row.MinimumUnitAmount);
      // 临时值  申请数量是否大于当前库存数量  单位按照申请单位计算
      let tempQuantity = row.ApplyQuantity > balanceQuantity ? balanceQuantity : row.ApplyQuantity;

      if (row.ApproveQuantity > tempQuantity) {
        row.ApproveQuantity = tempQuantity;
        that.$message.error({
          message: "预配数量超出实物库存数量",
          duration: 2000,
        });
      }

      if (row.ApprovedPrice) {
        row.ApprovedTotalAmount = parseFloat(parseFloat(row.ApprovedPrice) * parseFloat(row.ApproveQuantity)).toFixed(2);
      }
    },
    /**  修改审核 配送单价 */
    changeApprovalPrice(row) {
      if (row.ApprovedPrice && row.MinimumUnitID) {
        row.ApprovedTotalAmount = parseFloat(parseFloat(row.ApprovedPrice) * parseFloat(row.RefundMinimumUnitQuantity)).toFixed(2);
      }
    },

    /**  审核通过 驳回  */
    approvalEntityApplyProductClick(isPass) {
      let that = this;
      if (isPass) {
        // 通过
        that.$refs["approvalDetailRef"].validate((valid) => {
          if (valid) {
            let ApprovedTotalAmount = Enumerable.from(that.applyDetailInfo.Detail).sum((i) => Number(i.ApprovedTotalAmount));

            let InventoryApplyDetail = Enumerable.from(that.applyDetailInfo.Detail)
              .select((i) => ({
                ID: i.ID,
                ApproveQuantity: i.RefundQuantity, // 改  ApproveQuantity
                ApprovedPrice: i.ApprovedPrice,
                ApprovedTotalAmount: i.ApprovedTotalAmount || 0,
                // ApproveMinimumUnitQuantity: parseFloat(i.ApproveQuantity || 0) * parseFloat(i.MinimumUnitAmount || 0), // 最小单位数量
                ApproveMinimumUnitQuantity: i.RefundMinimumUnitQuantity,
              }))
              .toArray();

            var approvedParams = {
              ID: that.applyDetailInfo.ID,
              BillStatus: "20",
              ApprovedTotalAmount: ApprovedTotalAmount,
              InboundEntityID: that.applyDetailInfo.EntityID,
              InventoryRefundApplyDetail: InventoryApplyDetail,
              SettlementWay: that.applyDetailInfo.SettlementWay,
            };
            that.approvedPaasLoading = true;
            that.setinventoryApplyApprovedNetwork(approvedParams);
          }
        });
      } else {
        // 驳回
        that.finalRejectionDialogVisible = true;
      }
    },

    /**  确认付款  */
    confirmPaymentClick(row) {
      let that = this;
      that.getinventoryApplyInfoNetwork(row.ID, 4);
    },
    /**  修改支付方式  */
    changePaymentWayClick(val) {
      let that = this;
      if (val == 10) {
        that.$set(that.approveEntityRules, "PaymentWay", [
          {
            required: true,
            message: "请选择付款方式",
            trigger: ["blur", "change"],
          },
        ]);
        that.$set(that.approveEntityRules, "ReceiptNumber", [
          {
            required: true,
            message: "请输入回单号码",
            trigger: ["blur", "change"],
          },
        ]);
        that.$set(that.approveEntityRules, "PaymentAccountName", [
          {
            required: true,
            message: "请输入付款户名",
            trigger: ["blur", "change"],
          },
        ]);

        delete that.approveEntityRules.Balance_payment;
      }
      if (val == 20) {
        // if (that.applyDetailInfo.PaymentTotalAmout > parseFloat(that.applyDetailInfo.Balance || 0) + parseFloat(that.applyDetailInfo.LargessBalance || 0)) {
        //   that.$message.error("账户余额不足！");
        // }
        // that.$set(that.approveEntityRules, "Balance_payment", [
        //   {
        //     required: true,
        //     message: "请输入付款本金金额",
        //     trigger: ["blur", "change"]
        //   }
        // ]);

        delete that.approveEntityRules.PaymentWay;
        delete that.approveEntityRules.ReceiptNumber;
        delete that.approveEntityRules.PaymentAccountName;
      }
      // that.approveEntityRules.
    },
    /**  修改支付本金  */
    changPaymentBalance(val) {
      let that = this;
      if (val > that.applyDetailInfo.Balance) {
        // that.applyDetailInfo.Balance_payment = that.applyDetailInfo.Balance;
      }
    },
    /**  修改支付赠金  */
    changePaymentLargessBalance(val) {
      let that = this;
      if (val > that.applyDetailInfo.LargessBalance) {
        // that.applyDetailInfo.LargessBalance_payment = that.applyDetailInfo.LargessBalance;
      }
    },
    /**  保存确认付款  */
    saveConfirmPaymentClick() {
      let that = this;
      that.$refs.PaymentWayapprovalDetailRef.validate((valid) => {
        if (valid) {
          // that.applyDetailInfo.RefundInboundTotalAmount !==
          if (
            that.applyDetailInfo.PaymentWay == "20" &&
            parseFloat(that.applyDetailInfo.Balance_payment || 0) + parseFloat(that.applyDetailInfo.LargessBalance_payment || 0) == 0
          ) {
            that.$message.error({
              message: "请输入退款金额",
              duration: 2000,
            });
            return;
          }
          that.inventoryApply_pay();
          // else {
          // }
        }
      });
    },
    // 合计
    getSummaries(param) {
      let that = this;
      const { columns } = param;
      const sums = [];
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = "合计";
          return;
        }
        if (index == columns.length - 1) {
          var filter_NumFormat = this.$options.filters["NumFormat"];
          sums[index] = <span class="font_weight_600">¥{filter_NumFormat(that.applyDetailInfo.RefundInboundTotalAmount)}</span>;
        } else {
          sums[index] = "";
        }
      });
      return sums;
    },
    getSumm(param) {
      const { columns } = param;
      const sums = [];
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = "合计";
          return;
        }
        if (index == columns.length - 1) {
          let total = parseFloat(
            this.applyDetailInfo.Detail &&
              this.applyDetailInfo.Detail.reduce((perVal, nexVal) => {
                return perVal + parseFloat(nexVal["ApprovedTotalAmount"] || 0);
              }, 0)
          ).toFixed(2);
          var filter_NumFormat = this.$options.filters["NumFormat"];
          sums[index] = <span class="font_weight_600">¥{filter_NumFormat(total)}</span>;
        } else {
          sums[index] = "";
        }
      });
      return sums;
    },
    /**  审核驳回*/
    finalRejectApprovedClick() {
      let that = this;
      var approvedParams = {
        ID: that.applyDetailInfo.ID,
        BillStatus: "50",
        RejectReason: that.finalRejection,
        ApprovedTotalAmount: "",
        OutboundEntityID: "",
        InventoryRefundApplyDetail: [],
      };
      that.approvedRefuseLoading = true;
      that.setinventoryApplyApprovedNetwork(approvedParams);
    },

    /**  点击 退货 详情  */
    outboundEntityApplyProductClick(row) {
      let that = this;
      that.getinventoryApplyInfoNetwork(row.ID, 2);
    },

    /** 配送修改数量 */
    changeOutboundQuantity(row) {
      row.RefundOutboundQuantity = Math.floor(row.RefundOutboundQuantity);
      if (row.RefundOutboundQuantity > row.ApproveQuantity) {
        this.$message.error({
          message: "实际退货数量不能超过申请退货数量",
          duration: 2000,
        });
        row.RefundOutboundQuantity = "";
        row.ApproveMinimumUnitQuantity = 0;
        return;
      }
      row.ApproveMinimumUnitQuantity = parseFloat(row.RefundOutboundQuantity) * parseFloat(row.MinimumUnitAmount);
      row.ApprovedTotalAmount = parseFloat(row.ApproveMinimumUnitQuantity) * parseFloat(row.ApprovedPrice);
    },
    /** 创建退货出库   */
    saveOutboundEntityApplyProductClick() {
      let that = this;
      let isLock = Enumerable.from(that.applyDetailInfo.Detail).contains(true, (val) => {
        return val.OutboundIsLock;
      });
      if (isLock) {
        that.$message.error({
          message: "存在盘点锁定的产品",
          duration: 2000,
        });
        return;
      }
      that.$refs["outboundDetailRef"].validate((valid) => {
        if (valid) {
          let InventoryApplyDetail = Enumerable.from(that.applyDetailInfo.Detail)
            .select((i) => ({
              ID: i.ID,
              OutboundQuantity: i.RefundOutboundQuantity,
              OutboundPrice: i.OutboundPrice,
              OutboundTotalAmount: i.ApprovedTotalAmount,
              OutboundMinimumUnitQuantity: i.ApproveMinimumUnitQuantity,
            }))
            .toArray();

          let OutboundTotalAmount = Enumerable.from(that.applyDetailInfo.Detail).sum((i) => Number(i.ApprovedTotalAmount));
          let params = {
            ID: that.applyDetailInfo.ID,
            OutboundTotalAmount: OutboundTotalAmount,
            InventoryRefundApplyDetail: InventoryApplyDetail,
            Remark: that.applyDetailInfo.outRemark == undefined ? "" : that.applyDetailInfo.outRemark,
          };
          that.setinventoryApplyOutboundNetwork(params);
        }
      });
    },

    /**  入库  */
    inboundEntityApplyProductClick(row) {
      let that = this;
      that.getinventoryApplyInfoNetwork(row.ID, 3);
    },
    /**  修改入库数量  RefundInboundQuantity RefundOutboundQuantity*/
    changeInboundQuantity(row) {
      row.RefundInboundQuantity = Math.floor(row.RefundInboundQuantity);

      if (row.RefundInboundQuantity > row.RefundOutboundQuantity) {
        this.$message.error({
          message: "入库数量不能超过实际退货数量",
          duration: 2000,
        });
        row.RefundInboundQuantity = "";
        row.RefundInboundMinimumUnitQuantity = "";
        return;
      }
      row.RefundInboundMinimumUnitQuantity = parseFloat(row.RefundInboundQuantity) * parseFloat(row.MinimumUnitAmount);
      row.RefundInboundTotalAmount = parseFloat(row.RefundInboundMinimumUnitQuantity) * parseFloat(row.ApprovedPrice);
    },
    /**  保存入库  */
    saveInboundEntityApplyProductClick() {
      let that = this;
      let isLock = Enumerable.from(that.applyDetailInfo.Detail).contains(true, (val) => {
        return val.InboundIsLock;
      });
      if (isLock) {
        that.$message.error({
          message: "存在盘点锁定的产品",
          duration: 2000,
        });
        return;
      }
      that.$refs["InboundDetailRef"].validate((valid) => {
        if (valid) {
          let InventoryApplyDetail = Enumerable.from(that.applyDetailInfo.Detail)
            .select((i) => ({
              //   ID: i.ID,
              //   InboundQuantity: i.InboundQuantity,
              //   InboundPrice: i.InboundPrice,
              //   InboundTotalAmount: i.InboundTotalAmount,
              //   InboundMinimumUnitQuantity:
              //     parseFloat(i.InboundQuantity) * parseFloat(i.MinimumUnitAmount),
              ID: i.ID,
              InboundQuantity: i.RefundInboundQuantity,
              InboundPrice: i.ApprovedPrice,
              InboundTotalAmount: i.RefundInboundTotalAmount,
              InboundMinimumUnitQuantity: i.RefundInboundMinimumUnitQuantity,
              // parseFloat(i.InboundQuantity) * parseFloat(i.MinimumUnitAmount),
            }))
            .toArray();

          let InboundTotalAmount = Enumerable.from(that.applyDetailInfo.Detail).sum((i) => Number(i.RefundInboundTotalAmount));

          let params = {
            ID: that.applyDetailInfo.ID,
            InboundTotalAmount: InboundTotalAmount,
            InventoryRefundApplyDetail: InventoryApplyDetail,
            Remark: that.applyDetailInfo.inRemark,
          };
          that.setinventoryApplyInboundNetwork(params);
        }
      });
    },
    refundDetaildialog() {
      // this.$refs["approvalDetailRef"].clearValidate();
      this.approvalDetaildialogVisible = false;
    },
    /** 查看出库明细   */
    checkOutboundBillInfo() {
      let that = this;
      let parmas = {
        ID: that.applyDetailInfo.RefundOutboundBillID,
        InventoryType: "要货退货出库",
      };
      that.get_info_ProductInventoryOutbound_netWork(parmas);
      that.clickout = true;
      that.clickdelite = false;
      that.TemplateType = "outstock";
      that.getPrintTemplate_list();
    },
    /**  查看入库明细  */
    checkInbounBillInfo() {
      let that = this;
      let parmas = {
        ID: that.applyDetailInfo.RefundInboundBillID,
        InventoryType: "要货退货入库",
      };
      that.get_info_inventoryProductInbound_netWork(parmas);
      that.clickout = false;
      that.clickdelite = false;
      that.TemplateType = "instock";
      that.getPrintTemplate_list();
      // that.fileType.typeName = 'that.InboundInfo';
      that.clickout = false;
      that.clickdelite = false;
    },

    /**  ==========================================================================================  */

    /**  6.1.门店要货申请列表*/
    // getInventoryApplyListNetwork: function() {
    // 	var that = this;
    // 	that.loading = true;
    // 	var params = {
    // 		PageNum: that.paginations.page,
    // 		ID: that.searchForm.ID,
    // 		ProductName: that.searchForm.Name,
    // 		EntityID: that.searchForm.EntityID,
    // 		StartDate: that.searchForm.DateTime == null ? "" : that.searchForm.DateTime[0],
    // 		EndDate: that.searchForm.DateTime == null ? "" : that.searchForm.DateTime[1],
    // 		BillStatus: that.searchForm.BillStatus == "0" ? "" : that.searchForm.BillStatus,
    // 	};

    // 	APIPSIApplyProduct.inventoryApplyList(params)
    // 		.then((res) => {
    // 			if (res.StateCode == 200) {
    // 				that.inventoryApplyList = res.List;
    // 				that.paginations.page_size = res.PageSize;
    // 				that.paginations.total = res.Total;
    // 				that.getInventoryProductBillStatusNumberNetwork();
    // 			} else {
    // 				that.$message.error({
    // 					message: res.Message,
    // 					duration: 2000,
    // 				});
    // 			}
    // 		})
    // 		.finally(function() {
    // 			that.loading = false;
    // 		});
    // },
    getInventoryRefundListNetwork: function () {
      var that = this;
      that.loading = true;
      var params = {
        PageNum: that.paginations.page,
        ID: that.searchForm.ID,
        ProductName: that.searchForm.Name,
        EntityID: that.searchForm.EntityID,
        StartDate: that.searchForm.DateTime == null ? "" : that.searchForm.DateTime[0],
        EndDate: that.searchForm.DateTime == null ? "" : that.searchForm.DateTime[1],
        BillStatus: that.searchForm.BillStatus == "0" ? "" : that.searchForm.BillStatus,
      };

      APIPSIRefundProduct.inventoryRefundList(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.inventoryApplyList = res.List;
            that.paginations.page_size = res.PageSize;
            that.paginations.total = res.Total;
            that.getInventoryProductBillStatusNumberNetwork();
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },

    /**  4.4.仓库列表  */
    getStorageEntityNetwork: function () {
      var that = this;
      var params = {};
      APIStorage.getpurchaseStorageEntity(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.purchaseStorage = res.Data;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {});
    },

    /**  6.3.门店要货单申请  */
    setInventoryApplyCreateNetwork: function () {
      let that = this;
      let Products = Enumerable.from(that.entityApplyProduct.Product)
        .select((i) => ({
          ProductID: i.ProductID,
          UnitID: i.UnitID,
          ApplyQuantity: i.ApplyQuantity,
          MinimumUnitID: i.miniUnitID,
          ApplyMinimumUnitQuantity: Number(i.ApplyQuantity) * Number(i.miniAmount),
          ApprovedPrice: parseFloat(i.DeliveryPrice).toFixed(3).slice(0, -1),
          ApprovedTotalAmount: i.DeliveryTotalPrice,
        }))
        .toArray();
      let Amount = Products.reduce((perVal, nextVal) => {
        return perVal + parseFloat(nextVal.ApprovedTotalAmount);
      }, 0)
        .toFixed(2)
        .slice(0, -1);
      var params = {
        OutboundEntityID: that.entityApplyProduct.EntityID,
        Remark: that.entityApplyProduct.Remark,
        InventoryRefundApplyDetail: Products,
        Amount: Amount,
        BillStatus: "10",
      };
      if (that.entityApplyProduct.ID) {
        params.ID = that.entityApplyProduct.ID;
      }
      APIPSIRefundProduct.inventoryRefundCreate(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.$message.success({
              message: "申请成功",
              duration: 2000,
            });
            that.dialogVisible = false;
            that.handleSearchEntityApplyProductClick();
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {});
    },
    saveDraft() {
      let that = this;
      that.saveDraftLoading = true;
      let Products = Enumerable.from(that.entityApplyProduct.Product)
        .select((i) => ({
          ProductID: i.ProductID,
          UnitID: i.UnitID,
          ApplyQuantity: i.ApplyQuantity,
          MinimumUnitID: i.miniUnitID,
          ApplyMinimumUnitQuantity: Number(i.ApplyQuantity) * Number(i.miniAmount),
          ApprovedPrice: parseFloat(i.DeliveryPrice).toFixed(3).slice(0, -1),
          ApprovedTotalAmount: i.DeliveryTotalPrice,
        }))
        .toArray();
      let Amount = Products.reduce((perVal, nextVal) => {
        return perVal + parseFloat(nextVal.ApprovedTotalAmount);
      }, 0)
        .toFixed(2)
        .slice(0, -1);
      var params = {
        ID: that.entityApplyProduct.ID,
        OutboundEntityID: that.entityApplyProduct.EntityID,
        Remark: that.entityApplyProduct.Remark,
        InventoryRefundApplyDetail: Products,
        Amount: Amount,
        BillStatus: "05",
      };
      APIPSIRefundProduct.inventoryRefundCreate(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.$message.success({
              message: "退货草稿保存成功",
              duration: 2000,
            });
            that.dialogVisible = false;
            that.handleSearchEntityApplyProductClick();
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.saveDraftLoading = false;
        });
    },

    /**  确认付款  */
    async inventoryApply_pay() {
      let that = this;
      let params = {
        // ID: that.applyDetailInfo.ID, //订单号
        // BillStatus: "40",
        // PaymentWay: that.applyDetailInfo.PaymentWay, //付款方式（10：离线转账，20：余额支付）
        // ReceiptNumber: that.applyDetailInfo.ReceiptNumber, //回单号码（离线转账填写）
        // PaymentAccountName: that.applyDetailInfo.PaymentAccountName, //付款户名（离线转账填写）
        // Balance: that.applyDetailInfo.Balance_payment, //本金
        // LargessBalance: that.applyDetailInfo.LargessBalance_payment, //赠额
        // Remark: that.applyDetailInfo.Remark, //备注

        ID: that.applyDetailInfo.ID, //订单号
        BillStatus: "60",
        PaymentWay: that.applyDetailInfo.PaymentWay, //付款方式（10：离线转账，20：余额支付）
        ReceiptNumber: that.applyDetailInfo.ReceiptNumber, //回单号码（离线转账填写）
        PaymentAccountName: that.applyDetailInfo.PaymentAccountName, //付款户名（离线转账填写）
        Balance: that.applyDetailInfo.Balance_payment == "0" ? "" : that.applyDetailInfo.Balance_payment, //本金
        LargessBalance: that.applyDetailInfo.LargessBalance_payment == "0" ? "" : that.applyDetailInfo.LargessBalance_payment, //赠额
        Remark: that.applyDetailInfo.Remark, //备注
      };
      let res = await APIPSIRefundProduct.inventoryRefundProductStock(params);
      if (res.StateCode == 200) {
        that.confirmPaymentDialogVisible = false;
        that.$message.success({
          message: "操作成功",
          duration: 2000,
        });
        that.handleSearchEntityApplyProductClick();
      } else {
        that.$message.error(res.Message);
      }
    },
    /**  6.2.门店要货单详情 */
    getinventoryApplyInfoNetwork(ID, type) {
      var that = this;
      var params = {
        ID: ID,
      };
      that.loading = true;
      APIPSIRefundProduct.inventoryRefundInfo(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.applyDetailInfo = res.Data;
            that.approveEntityRules = {
              EntityID: [
                {
                  required: true,
                  message: "请选择收货仓库",
                  trigger: ["blur", "change"],
                },
              ],
              ApproveQuantity: [{ required: true, trigger: ["blur", "change"] }],
              ApprovedPrice: [{ required: true, trigger: ["blur", "change"] }],
              RefundOutboundQuantity: [{ required: true, trigger: ["blur", "change"] }],
              InboundQuantity: [{ required: true, trigger: ["blur", "change"] }],
              SettlementWay: [
                {
                  required: true,
                  message: "请选择结算方式",
                  trigger: ["blur", "change"],
                },
              ],
              RefundInboundQuantity: [{ required: true, trigger: ["blur", "change"] }],
            };
            switch (type) {
              case 0: // 查看详情
                that.applyDetaildialogVisible = true;
                that.approveEntityRules = {};
                break;
              case 1: // 审批详情
                for (let i = 0; i < that.purchaseStorage.length; i++) {
                  if (that.purchaseStorage[i].ID == that.applyDetailInfo.RefundOutboundEntityID) {
                    that.purchaseStorage.splice(i, 1);
                    // return;
                  }
                }
                that.OutboundEntitys = Enumerable.from(that.purchaseStorage)
                  .where((i) => i.ID != that.applyDetailInfo.InboundEntityID)
                  .toArray();
                that.applyDetailInfo.EntityID = "";
                that.applyDetailInfo.EntityName = "";
                that.approvalDetaildialogVisible = true;
                that.applyDetailInfo.InDate = dateUtil.formatDate.format(new Date(), "YYYY-MM-DD hh:mm:ss");
                break;
              case 2: // 退货
                that.outboundDetaildialogVisible = true;
                Enumerable.from(that.applyDetailInfo.Detail).forEach((i) => {
                  if (!i.OutboundIsLock) {
                    i.OutboundQuantity = i.StockQuantity > i.ApproveQuantity ? i.ApproveQuantity : i.StockQuantity;
                    i.OutboundPrice = i.ApprovedPrice;
                    i.OutboundTotalAmount = parseFloat(i.OutboundQuantity) * parseFloat(i.OutboundPrice);
                  }
                });
                break;
              case 3: // 入库
                that.inboundDetaildialogVisible = true;
                Enumerable.from(that.applyDetailInfo.Detail).forEach((i) => {
                  if (!i.InboundIsLock) {
                    i.InboundQuantity = i.OutboundQuantity;
                    i.InboundPrice = i.OutboundPrice;
                    i.InboundTotalAmount = parseFloat(i.InboundQuantity) * parseFloat(i.InboundPrice);
                  }
                });
                break;
              case 4: // 付款
                that.confirmPaymentDialogVisible = true;
                that.applyDetailInfo.PaymentTotalAmout = that.applyDetailInfo.Detail.reduce((perVal, curVal) => {
                  return perVal + curVal.ApprovedTotalAmount;
                }, 0);
                that.$set(that.applyDetailInfo, "PaymentWay", "10");
                that.$set(that.applyDetailInfo, "Balance_payment", "");
                that.$set(that.applyDetailInfo, "LargessBalance_payment", "");

                that.$set(that.applyDetailInfo, "ReceiptNumber", "");
                that.$set(that.applyDetailInfo, "PaymentAccountName", "");
                that.$set(that.applyDetailInfo, "RemarkPayment", "");

                that.approveEntityRules.PaymentWay = [
                  {
                    required: true,
                    message: "请选择付款方式",
                    trigger: ["blur", "change"],
                  },
                ];
                that.approveEntityRules.ReceiptNumber = [
                  {
                    required: true,
                    message: "请输入回单号码",
                    trigger: ["blur", "change"],
                  },
                ];
                that.approveEntityRules.PaymentAccountName = [
                  {
                    required: true,
                    message: "请输入付款户名",
                    trigger: ["blur", "change"],
                  },
                ];
                break;
              case 5: // 驳回的编辑
                that.dialogVisible = true;
                that.isAddOrEdit = false;
                this.editProductName = that.applyDetailInfo.Detail.map((cur) => {
                  return {
                    ID: cur.ProductID,
                    ProductName: cur.ProductName,
                    PCategoryName: cur.PCategoryName,
                    IsLock: cur.InboundIsLock,
                    Alias: cur.Alias,
                    DeliveryPrice: cur.ApprovedPrice, // 配销价格
                    // Price: cur.Price,
                    Specification: cur.Specification, // 规格
                    Unit: cur.Unit,
                  };
                });
                var entityApplyProduct = {
                  ID: that.applyDetailInfo.ID,
                  EntityID: that.applyDetailInfo.RefundOutboundEntityID,
                  EntityName: that.applyDetailInfo.RefundOutboundEntityName,
                  Remark: that.applyDetailInfo.Remark,
                  TotalBalance: that.applyDetailInfo.Balance + that.applyDetailInfo.LargessBalance,
                  Balance: that.applyDetailInfo.Balance,
                  LargessBalance: that.applyDetailInfo.LargessBalance,
                  Product: that.applyDetailInfo.Detail.map((val) => {
                    return {
                      Alias: val.Alias,
                      ProductName: val.ProductName,
                      ProductID: val.ProductID,
                      Specification: val.Specification, // 规格
                      Quantity: val.StockQuantity, //可退库存
                      UnitName: val.UnitName, // 退货单位
                      ApplyQuantity: val.RefundQuantity, // 退货单位数量
                      miniUnitQuantity: val.RefundMinimumUnitQuantity, // 最小包装单位数量
                      DeliveryPrice: val.ApprovedPrice, // 配销价格
                      DeliveryTotalPrice: val.ApprovedTotalAmount, // 配销价格小计
                      miniAmount: val.MinimumUnitAmount,
                      miniUnitID: val.MinimumUnitID,
                      Unit: val.Unit,
                      UnitID: val.UnitID,
                      miniUnitName: val.MinimumUnitName,
                    };
                  }),
                };
                that.entityApplyProduct.EntityID;
                that.entityApplyProduct = entityApplyProduct;
                break;
              case 6:
                that.dialogVisible = true;
                that.isAddOrEdit = true;
                this.editProductName = that.applyDetailInfo.Detail.map((cur) => {
                  return {
                    ID: cur.ProductID,
                    ProductName: cur.ProductName,
                    PCategoryName: cur.PCategoryName,
                    IsLock: cur.InboundIsLock,
                    Alias: cur.Alias,
                    DeliveryPrice: cur.ApprovedPrice, // 配销价格
                    // Price: cur.Price,
                    Specification: cur.Specification, // 规格
                    Unit: cur.Unit,
                  };
                });
                var entityProduct = {
                  ID: that.applyDetailInfo.ID,
                  EntityID: that.applyDetailInfo.RefundOutboundEntityID,
                  EntityName: that.applyDetailInfo.RefundOutboundEntityName,
                  Remark: that.applyDetailInfo.Remark,
                  TotalBalance: that.applyDetailInfo.Balance + that.applyDetailInfo.LargessBalance,
                  Balance: that.applyDetailInfo.Balance,
                  LargessBalance: that.applyDetailInfo.LargessBalance,
                  Product: that.applyDetailInfo.Detail.map((val) => {
                    return {
                      Alias: val.Alias,
                      ProductName: val.ProductName,
                      ProductID: val.ProductID,
                      Specification: val.Specification, // 规格
                      Quantity: val.StockQuantity, //可退库存
                      UnitName: val.UnitName, // 退货单位
                      ApplyQuantity: val.RefundQuantity, // 退货单位数量
                      miniUnitQuantity: val.RefundMinimumUnitQuantity, // 最小包装单位数量
                      DeliveryPrice: val.ApprovedPrice, // 配销价格
                      DeliveryTotalPrice: val.ApprovedTotalAmount, // 配销价格小计
                      miniAmount: val.MinimumUnitAmount,
                      miniUnitID: val.MinimumUnitID,
                      Unit: val.Unit,
                      UnitID: val.UnitID,
                      miniUnitName: val.MinimumUnitName,
                    };
                  }),
                };
                that.entityApplyProduct.EntityID;
                that.entityApplyProduct = entityProduct;
                break;
            }
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },
    /**  6.2.驳回编辑保存  */
    entityRefundApply: function () {
      let that = this;
      let Products = Enumerable.from(that.entityApplyProduct.Product)
        .select((i) => ({
          ProductID: i.ProductID,
          UnitID: i.UnitID,
          ApplyQuantity: i.ApplyQuantity,
          MinimumUnitID: i.miniUnitID,
          ApplyMinimumUnitQuantity: Number(i.ApplyQuantity) * Number(i.miniAmount),
          ApprovedPrice: parseFloat(i.DeliveryPrice).toFixed(3).slice(0, -1),
          ApprovedTotalAmount: i.DeliveryTotalPrice,
        }))
        .toArray();
      let Amount = Products.reduce((perVal, nextVal) => {
        return perVal + parseFloat(nextVal.ApprovedTotalAmount);
      }, 0)
        .toFixed(2)
        .slice(0, -1);
      var params = {
        ID: that.entityApplyProduct.ID, //退货订单编号
        Remark: that.entityApplyProduct.Remark,
        InventoryRefundApplyDetail: Products,
        Amount: Amount,
      };
      APIPSIRefundProduct.entityRefundApply(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.$message.success({
              message: "申请成功",
              duration: 2000,
            });
            that.dialogVisible = false;
            that.handleSearchEntityApplyProductClick();
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {});
    },
    /**  修改编辑状态下 明细   */
    editAndProductList(originalArray) {
      if (this.isAddOrEdit) {
        return originalArray;
      } else {
        let tempUnion = Enumerable.from(originalArray)
          .union(this.editProductName, (i) => i.ID)
          .toArray();
        return tempUnion;
      }
    },
    /** 查询 产品 库存列表 列表  */
    get_stock_list_entityProductListNetwork: function () {
      var that = this;
      var params = {
        PageNum: that.ProductPaginations.page,
        ProductName: that.ProductName,
        EntityID: that.entityApplyProduct.EntityID,
      };
      APIPSIApplyProduct.getEntityProductDeliveryPrice(params)
        .then((res) => {
          if (res.StateCode == 200) {
            res.List.forEach((item) => {
              (item.ProductID = item.ID),
                (item.Units = item.Unit),
                (item.ApplyQuantity = ""),
                (item.DeliveryTotalPrice = ""),
                item.Unit.forEach((unit) => {
                  if (unit.IsMinimumUnit) {
                    (item.miniUnitQuantity = unit.Amount),
                      (item.miniAmount = unit.Amount),
                      (item.miniUnitName = unit.UnitName),
                      (item.miniUnitID = unit.UnitID);
                  }
                });
            });
            that.ProductPaginations.total = res.Total;
            that.ProductList = res.List;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {});
    },
    /**  6.4.门店要货单审批*/
    setinventoryApplyApprovedNetwork: function (params) {
      var that = this;
      APIPSIRefundProduct.inventoryRefundApproved(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.$message.success({
              message: "审核成功",
              duration: 2000,
            });
            that.finalRejection = "";
            that.handleSearchEntityApplyProductClick();
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.approvedPaasLoading = false;
          that.approvedRefuseLoading = false;
          that.approvalDetaildialogVisible = false;
          that.finalRejectionDialogVisible = false;
        });
    },
    /**  6.5.门店退货单配送出库 */
    setinventoryApplyOutboundNetwork: function (params) {
      var that = this;
      APIPSIRefundProduct.inventoryProductOutbound(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.$message.success({
              message: "审核成功",
              duration: 2000,
            });
            that.outboundDetaildialogVisible = false;
            that.handleSearchEntityApplyProductClick();
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.finalRejectionDialogVisible = false;
        });
    },

    /**  6.6.门店要货单入库  */
    setinventoryApplyInboundNetwork: function (params) {
      var that = this;
      APIPSIRefundProduct.inventoryProductInbound(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.$message.success({
              message: "入库成功",
              duration: 2000,
            });
            that.inboundDetaildialogVisible = false;
            that.handleSearchEntityApplyProductClick();
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {});
    },

    /**  6.7.门店要货单取消 */
    setInventoryProductCancelNetwork: function (ID) {
      var that = this;
      that.approvedLoading = true;
      var params = {
        ID: ID,
      };
      APIPSIRefundProduct.inventoryRefundProductCancel(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.$message({
              message: "取消成功",
              duration: 2000,
            });
            that.handleSearchEntityApplyProductClick();
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {});
    },
    /**  6.7.门店要货单取消 驳回*/
    setinventoryProductCancelRejectApplylNetwork: function (ID) {
      var that = this;
      that.approvedLoading = true;
      var params = {
        ID: ID,
      };
      APIPSIRefundProduct.inventoryRefundProductStockBack(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.$message({
              message: "取消成功",
              duration: 2000,
            });
            that.handleSearchEntityApplyProductClick();
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {});
    },
    /** 6.8.门店出货单状态统计 */
    getInventoryProductBillStatusNumberNetwork: function () {
      var that = this;
      that.approvedLoading = true;
      var params = {
        ID: that.searchForm.ID,
        ProductName: that.searchForm.Name,
        EntityID: that.searchForm.EntityID,
        StartDate: that.searchForm.DateTime == null ? "" : that.searchForm.DateTime[0],
        EndDate: that.searchForm.DateTime == null ? "" : that.searchForm.DateTime[1],
        // BillStatus:
        //   that.searchForm.BillStatus == "0" ? "" : that.searchForm.BillStatus,
      };

      APIPSIRefundProduct.inventoryProductBillStatusNumber(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.StatusNumberInfo = res.Data;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {});
    },
    /** 门店要货单审批 产品实时库存  */
    getinventoryProductStockNetwork: function (ProductIDs, EntityID) {
      var params = {
        ProductID: ProductIDs,
        EntityID: EntityID,
      };
      APIPSIApplyProduct.inventoryProductStock(params)
        .then((res) => {
          if (res.StateCode == 200) {
            // let Products = res.Data;
            // that.applyDetailInfo.Detail = Enumerable.from(that.applyDetailInfo.Detail)
            // 	.select((val) => {
            // 		val.ApproveQuantity = "";
            // 		return val;
            // 	})
            // 	.toArray();
            // Enumerable.from(that.applyDetailInfo.Detail).forEach((i) => {
            // 	i.ApproveQuantity = "";
            // 	let tempProduct = Enumerable.from(Products).singleOrDefault((j) => {
            // 		return j.ProductID == i.ProductID;
            // 	}, -1);
            // 	if (tempProduct != -1) {
            // 		i.StockQuantity = tempProduct.Quantity; // 可用库存  最小单位数量
            // 		i.miniUnitName = tempProduct.UnitName; // 最小单位名称
            // 		i.miniUnitID = tempProduct.UnitID; // 最小单位 ID
            // 		let totalQuantity = Enumerable.from(that.applyDetailInfo.Detail)
            // 			.where((val) => {
            // 				return val.ProductID == i.ProductID;
            // 			})
            // 			.sum((val) => Number(val.ApproveQuantity || 0) * Number(val.MinimumUnitAmount));
            // 		// 最小单位剩余数量
            // 		tempProduct.balanceQuantity = parseFloat(tempProduct.Quantity) - parseFloat(totalQuantity || 0);
            // 		// 当前大单位的数量
            // 		let tempApproveQuantity = Math.floor(parseFloat(tempProduct.balanceQuantity) / parseFloat(i.MinimumUnitAmount));
            // 		i.ApproveQuantity = i.ApplyQuantity > tempApproveQuantity ? tempApproveQuantity : i.ApplyQuantity;
            // 		// 计算价格
            // 		if (tempProduct.DeliveryPrice) {
            // 			i.ApprovedPrice = tempProduct.DeliveryPrice;
            // 		}
            // 		if (i.ApprovedPrice) {
            // 			i.ApprovedTotalAmount = parseFloat(i.ApproveQuantity) * parseFloat(i.MinimumUnitAmount) * parseFloat(i.ApprovedPrice);
            // 		}
            // 		}
            // 	});
            // } else {
            // 	that.$message.error({
            // 		message: res.Message,
            // 		duration: 2000,
            // 	});
          }
        })
        .finally(function () {});
    },

    /**   10.2.产品入库详情    */
    get_info_inventoryProductInbound_netWork: function (params) {
      var that = this;
      that.loading = true;
      APIInbound.get_info_inventoryProductInbound(params)
        .then((res) => {
          if (res.StateCode == 200) {
            if (!res.Data) {
              that.$message.error("暂未获取到数据");
            } else {
              that.InboundInfo = res.Data;
              that.InboundInfoDialogVisible = true;
            }
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },

    /**  8.2.产品出库详情   */
    get_info_ProductInventoryOutbound_netWork: function (params) {
      var that = this;
      that.loading = true;
      APIOutbound.getProductInventoryOutbound_info(params)
        .then((res) => {
          if (res.StateCode == 200) {
            if (!res.Data) {
              that.$message.error("暂未获取到数据");
            } else {
              that.OutboundInfoDialogVisible = true;
              that.OutboundInfo = res.Data;
            }
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },

    /**    */
    async getProductDispatchPrice(ProductID, EntityID) {
      let that = this;
      let params = {
        ProductID: ProductID, //产品编号
        EntityID: EntityID, //门店编号
      };
      let res = await APIPSIApplyProduct.getProductDispatchPrice(params);
      if (res.StateCode == 200) {
        return res.Data;
      } else {
        that.$message.error(res.Message);
      }
    },
    /** 获取模板列表   */
    async getPrintTemplate_list() {
      let that = this;
      let params = { TemplateType: that.TemplateType };
      let res = await APIPSIApplyProduct.getPrintTemplate_list(params);
      if (res.StateCode == 200) {
        that.templateTypeList = res.Data;
      } else {
        that.$message.error(res.Message);
      }
    },
    /* 选择商品 */
    getSelectProduct(list) {
      let that = this;
      list.forEach((item) => {
        let defaultUnit = Enumerable.from(item.Unit).firstOrDefault((i) => {
          return i.IsDefautSendReceive;
        }, -1);

        item.UnitID = defaultUnit.UnitID;
        item.UnitName = defaultUnit.UnitName;
        item.miniAmount = defaultUnit.Amount;
      });
      that.selectProductList = list;
    },
    /* 确认商品选择 */
    submitFormApplicableDuty() {
      let that = this;
      that.entityApplyProduct.Product.push(...that.selectProductList);
      that.showProductVisible = false;
    },
    /* 商品选择选择框是否禁用 */
    checkboxSelect(row) {
      return !row.IsLock && row.Quantity != 0;
    },
  },
  /**  实例被挂载后调用  */
  mounted() {
    var that = this;
    /**  新建退货  */
    that.isAdd = permission.permission(that.$route.meta.Permission, "PSI-SupplyChain-EntityRefundProduct-Add");
    /**  审核单据  */
    that.isCheck = permission.permission(that.$route.meta.Permission, "PSI-SupplyChain-EntityRefundProduct-Check");
    /** 关闭待审核单据 */
    that.isClose = permission.permission(that.$route.meta.Permission, "PSI-SupplyChain-EntityRefundProduct-Close");
    // 		/** 关闭已驳回单据 */
    that.isTurnClose = permission.permission(that.$route.meta.Permission, "PSI-SupplyChain-EntityRefundProduct-RejectClose");
    // 		/**  配送出库 */
    that.isDelivery = permission.permission(that.$route.meta.Permission, "PSI-SupplyChain-EntityRefundProduct-Delivery");
    // /**  要货入库 */
    that.isStorage = permission.permission(that.$route.meta.Permission, "PSI-SupplyChain-EntityRefundProduct-Storage");
    // 		/**  查看配送出库单 */
    that.isViewDeliveryBill = permission.permission(that.$route.meta.Permission, "PSI-SupplyChain-EntityRefundProduct-ViewDeliveryBill");
    // 		/**  查看要货入库单  */
    that.isViewStorageyBill = permission.permission(that.$route.meta.Permission, "PSI-SupplyChain-EntityRefundProduct-ViewStorageyBill");
    that.isPaymentConfirm = permission.permission(that.$route.meta.Permission, "PSI-SupplyChain-EntityRefundProduct-PaymentConfirm");
    // var date = new Date(),
    //   y = date.getFullYear(),
    //   m = date.getMonth();
    // that.searchForm.DateTime = [
    //   dateUtil.formatDate.format(new Date(y, m, 1), "YYYY-MM-DD"),
    //   dateUtil.formatDate.format(new Date(), "YYYY-MM-DD"),
    // ];
    that.handleSearchEntityApplyProductClick();
    that.getStorageEntityNetwork();
    that.getPrintTemplate_list();
    // that.get_stock_list_entityProductListNetwork();
  },
};
</script>

<style lang="scss">
.EntityApplyProduct {
  .entityApplyProductDialogClass {
    .entityApplyProductInfoFrom {
      .el-form-item__label {
        font-size: 13px !important;
      }
      .el-form-item__content {
        font-size: 13px !important;
        line-height: 33px;
      }
      .el-form-item {
        margin-bottom: 0px;
      }
      .el-input__inner {
        padding-right: 0;
      }
    }
    .productFormInforClass {
      .el-form-item {
        margin-bottom: 0px;
      }
    }
    .PaymententityApplyProductInfoFrom {
      .el-form-item__label {
        font-size: 13px !important;
      }
      .el-form-item__content {
        font-size: 13px !important;
        line-height: 33px;
      }
      .el-form-item {
        margin-bottom: 6px;
      }
      .el-input__inner {
        padding-right: 0;
      }
    }
  }

  .IsLockProduct_list_back {
    background-color: #edf2fc;
    cursor: not-allowed;
  }
  .IsLockProduct_list_back:hover > td {
    background-color: #edf2fc !important;
  }
  .input_type {
    .el-input-group__append {
      padding: 0 10px;
    }
  }
  .el-tabs--border-card {
    // border: 0px,0px,0px,0px !important;
    border-bottom: 0px;
    box-shadow: 0 0px 0px 0 rgba(0, 0, 0, 0), 0 0 0px 0 rgba(0, 0, 0, 0);
    .el-tabs__content {
      padding: 0px !important;
    }
  }

  .custom_input {
    .el-input__inner {
      padding: 0 0 0 10px;
    }
  }

  .zl-custom-select-width {
    width: 230px;
  }
}
.EntityApplyProduct_custom_popper_class {
  .el-select-dropdown__item {
    line-height: normal;
    height: auto;
  }
}
@media print {
  html,
  body {
    height: inherit;
  }
}
</style>
