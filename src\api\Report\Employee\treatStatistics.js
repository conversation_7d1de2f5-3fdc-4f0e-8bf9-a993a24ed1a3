/**
 * Created by preference on 2023/01/13
 *  zmx
 */

import * as API from "@/api/index";
export default {
  /**   */
  employeeTreatStatement_goodStatistics: (params) => {
    return API.POST("api/employeeTreatStatement/goodStatistics", params);
  },
  /**   */
  employeeTreatStatement_goodStatisticsExcel: (params) => {
    return API.exportExcel(
      "api/employeeTreatStatement/goodStatisticsExcel",
      params
    );
  },

  /**   */
  employeeTreatStatement_projectStatistics: (params) => {
    return API.POST("api/employeeTreatStatement/projectStatistics", params);
  },
  /**   */
  employeeTreatStatement_projectStatisticsExcel: (params) => {
    return API.exportExcel(
      "api/employeeTreatStatement/projectStatisticsExcel",
      params
    );
  },

  /**   */
  employeeTreatStatement_productStatistics: (params) => {
    return API.POST("api/employeeTreatStatement/productStatistics", params);
  },
  /**   */
  employeeTreatStatement_productStatisticsExcel: (params) => {
    return API.exportExcel(
      "api/employeeTreatStatement/productStatisticsExcel",
      params
    );
  },

  /**   */
  employeeTreatStatement_cardStatistics: (params) => {
    return API.POST("api/employeeTreatStatement/cardStatistics", params);
  },
  /**   */
  employeeTreatStatement_cardStatisticsExcel: (params) => {
    return API.exportExcel(
      "api/employeeTreatStatement/cardStatisticsExcel",
      params
    );
  },
};
