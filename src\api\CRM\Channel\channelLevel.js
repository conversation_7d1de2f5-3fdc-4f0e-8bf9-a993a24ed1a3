/**
 * Created by preference on 2022/07/19
 *  zmx 
 */

import * as API from '@/api/index'
export default {
  /** 渠道等级  */
  channelLevel_list: params => {
    return API.POST('api/channelLevel/list', params)
  },
  /**  渠道等级添加 */
  channelLevel_create: params => {
    return API.POST('api/channelLevel/create', params)
  },
  /**  更新 */
  channelLevel_update: params => {
    return API.POST('api/channelLevel/update', params)
  },
  /** 渠道等级添移动  */
  channelLevel_move: params => {
    return API.POST('api/channelLevel/move', params)
  },
}