<template>
  <div class="brandLogin" style="background-color: #f8f8f8">
    <el-row style="height: 100%; position: relative" type="flex" align="middle">
      <el-col :offset="8" :span="8" class="dis_flex flex_x_center">
        <div v-show="showLogin" class="login_information">
          <el-form ref="ruleForm" :model="ruleForm" :rules="rules">
            <div class="login_logo">
              <el-image :src="LogoImg.BrandLogoIcon" style="width: 65px; height: 60px">
                <div slot="error">
                  <img src="https://mfl-saas-data.oss-cn-shanghai.aliyuncs.com/web/logo/logo2.png" />
                </div>
              </el-image>
            </div>
            <div class="font_18 font_weight_600 color_333 text_center" style="margin-top:5px;margin-bottom:50px">{{ LogoImg.BrandName }}</div>

            <el-form-item prop="Username">
              <el-input v-model="ruleForm.Username" placeholder="账号" @keyup.enter.native="handleLogin"></el-input>
            </el-form-item>
            <el-form-item prop="Password">
              <el-input type="password" v-model="ruleForm.Password" placeholder="密码" @keyup.enter.native="handleLogin"></el-input>
            </el-form-item>
            <el-form-item class="pad_15_0">
              <el-button :style="{ 'background-color': LogoImg.FrontColor, 'border-color': LogoImg.FrontColor }" type="primary" style="width: 100%" @click.native.prevent="handleLogin" :loading="loading" round>登 录</el-button>
            </el-form-item>
          </el-form>
          <div class="dis_flex flex_dir_row flex_x_between" style="margin-top: 60px">
            <el-popover placement="top-start" width="200" trigger="hover">
              <el-image style="width: 200px; height: 200px" :src="require('@/assets/img/erpMini.png')" fit="cover"></el-image>
              <el-button class="btn-miniprogram" size="small" icon="el-iconfont icon-mini-program-fill" slot="reference" round>微信小程序</el-button>
              <!-- <el-button class="btn-miniprogram" size="small" icon="el-icon-upload" slot="reference" round>微信小程序</el-button> -->
            </el-popover>
            <div style="margin-left: 25px" class="dis_flex flex_dir_row flex_y_center flex_x_between">
              <img src="@/assets/img/beianIcon.png" alt="" />
              <el-link class="font_13 color_999 marlt_5" type="info" :underline="false" href="https://beian.miit.gov.cn" target="_blank">苏ICP备**********号-1</el-link>
            </div>
          </div>
        </div>
      </el-col>
      <div class="copyright">Copyright © 南京三七美品牌管理有限公司</div>
    </el-row>
  </div>
</template>

<script>
import API from "@/api/account";
import md5 from "js-md5";

export default {
  name: "LoginBrandLoginCopy",
  data() {
    return {
      showLogin: false,
      loading: false,
      ruleForm: {
        EnterpriseCode: "",
        Username: "",
        Password: "",
      },
      rules: {
        EnterpriseCode: [
          { required: true, message: "请输入商户号", trigger: "blur" },
        ],
        Username: [
          { required: true, message: "请输入账号", trigger: "blur" },
        ],
        Password: [{ required: true, message: "请输入密码", trigger: "blur" }],
      },
      LogoImg: {
        BrandLogo: "",
        BrandLogoIcon: "",
        BackgroundColor: "f7f8f8",
        BrandName: "三七美",
        FrontColor: "#ff8646",
      },
    };
  },
  methods: {
    // 登录
    handleLogin() {
      var that = this;
      that.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.loading = true;
          let para = Object.assign({}, that.ruleForm);
          para.Password = md5(para.Password);
          API.getlogin(para)
            .then(function (res) {
              if (res.StateCode == 200) {
                localStorage.setItem("access-user", JSON.stringify(res.Data));
                that.routerData();
                localStorage.setItem(
                  "EnterpriseCode",
                  that.ruleForm.EnterpriseCode
                );
                localStorage.setItem("BrandLogin", true);
              } else {
                that.$message.error({
                  message: res.Message,
                  duration: 2000,
                });
              }
            })
            .finally(function () {
              that.loading = false;
            });
        }
      });
    },
    routerData: function () {
      var that = this;
      API.getPCPermissionRouter().then((res) => {
        if (res.StateCode == 200) {
          var routerchildren = [];
          res.Data.forEach(function (item) {
            var routerChild = {};
            routerChild.path = item.RouterPath;
            routerChild.component = () =>
              import(`@/${item.RouterComponentPath}`);
            routerChild.name = item.RouterName;
            var routerMeta = {};
            routerMeta.title = item.RouterMeta.Title;
            routerMeta.ExternalLinks = item.RouterMeta.ExternalLinks;
            routerMeta.IsVerifyStore = item.RouterMeta.IsVerifyStore;
            routerMeta.IsVerifyWarehouse = item.RouterMeta.IsVerifyWarehouse;
            routerMeta.Permission = item.RouterMeta.Permission;
            routerChild.meta = routerMeta;
            routerchildren.push(routerChild);
          });
          var routers = [];
          var router = {};
          router.path = "/";
          router.name = "UserRouter";
          router.component = () => import("@/components/common/Master");
          router.children = routerchildren;
          routers.push(router);
          var routerNotFound = {};
          routerNotFound.path = "*";
          routerNotFound.redirect = "/";
          routers.push(routerNotFound);
          that.$router.$addRoutes(routers);
          that.$router.push("/");
        } else {
          that.$message.error({
            message: res.Message,
            duration: 2000,
          });
        }
      });
    },
    // 重置密码
    resetPassword: function () {
      // var that = this;
      // that.$router.push("resetPassword")
    },
    // 记住密码
    selectRememberPassword() {},

    /**  获取商户logo  */
    async getBrandLogoInfo(EnterpriseCode) {
      let that = this;
      let params = { EnterpriseCode: EnterpriseCode };
      let res = await API.getBrandLogoInfo(params);
      if (res.StateCode == 200) {
        if (res.Data != null) {
          that.LogoImg = Object.assign(that.LogoImg, res.Data);
        }
        this.showLogin = true;
      } else {
        this.$router.push("/login");
      }
    },
  },
  created() {
    // this.LogoImg.BrandLogoIcon =
    //   "https://mfl-saas-data.oss-cn-shanghai.aliyuncs.com/20220223183226355";
    this.getBrandLogoInfo(this.$route.params.EnterpriseCode);
  },
  mounted() {
    this.ruleForm.EnterpriseCode = this.$route.params.EnterpriseCode;
  },
  beforeDestroy() {
    this.showLogin = false;
  },
};
</script>

<style lang="scss">
.brandLogin {
  height: 100%;
  .logo {
    padding: 15px;
  }

  .map_img {
    height: 100%;
    img {
      width: 100%;
      height: 100%;
    }
  }

  .login_information {
    padding: 40px;
    height: 460px;
    width: 360px;
    border-radius: 8px;
    background-color: #ffffff;
    box-shadow: 10px 10px 10px #f5f0ee;
    display: flex;
    flex-direction: column;
    justify-content: center;
    .login_logo {
      // margin-bottom: 15px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .el-input__inner {
      border: none !important;
      border-bottom: 1px solid #eeeeee !important;
      padding: 15px;
    }
    .forget_password {
      color: #666;
    }
    .btn-miniprogram {
      color: rgb(157, 165, 179);
      background-color: rgb(248, 249, 252);
      border: none;
    }
  }

  .copyright {
    font-size: 14px;
    color: rgb(20, 20, 20);
    position: absolute;
    bottom: 1%;
    left: 0;
    right: 0;
    max-width: 100%;
    text-align: center;
    opacity: 0.6;
  }
}
</style>