{"name": "project", "version": "1.0.0", "private": true, "scripts": {"serve:dev": "vue-cli-service serve --mode dev --open", "serve:prod": "vue-cli-service serve --mode prod --open", "serve:test": "node --max_old_space_size=4096 node_modules/@vue/cli-service/bin/vue-cli-service.js serve --mode test --open", "build:develop": "vue-cli-service build --mode develop", "build:prod": "vue-cli-service build --mode prod", "lint": "vue-cli-service lint"}, "dependencies": {"@amap/amap-jsapi-loader": "^1.0.1", "@progress/kendo-editor-vue-wrapper": "^2023.1.314", "@progress/kendo-theme-default": "^4.38.1", "@progress/kendo-ui": "^2023.3.1010", "@riophae/vue-treeselect": "^0.4.0", "axios": "^0.21.1", "cache-loader": "^4.1.0", "core-js": "^3.15.2", "dayjs": "^1.11.5", "el-tree-transfer": "^2.4.7", "element-china-area-data": "^5.0.2", "element-ui": "^2.15.6", "js-cookie": "^2.2.1", "js-md5": "^0.7.3", "linq": "^3.2.4", "vue": "^2.6.14", "vue-cropper": "^0.5.8", "vue-i18n": "^8.22.4", "vue-print-nb": "^1.7.5", "vue-qr": "^2.5.0", "vue-router": "^3.5.2", "vuedraggable": "^2.24.3", "vuex": "^3.6.2", "webpack": "^4.46.0"}, "devDependencies": {"@riophae/vue-treeselect": "^0.4.0", "@vue/cli-plugin-babel": "^4.5.13", "@vue/cli-plugin-eslint": "^4.5.13", "@vue/cli-service": "^4.5.13", "babel-eslint": "^10.0.3", "babel-plugin-syntax-dynamic-import": "^6.18.0", "eslint": "^7.30.0", "eslint-plugin-vue": "^7.13.0", "less": "^4.1.1", "less-loader": "^10.0.1", "sass": "^1.77.8", "sass-loader": "^10.4.1", "stylus": "^0.54.8", "stylus-loader": "^6.1.0", "vue-quill-editor": "^3.0.6", "vue-template-compiler": "^2.6.14", "vue-video-player": "^5.0.2"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "eslint:recommended"], "rules": {"no-console": "off"}, "parserOptions": {"parser": "babel-es<PERSON>"}, "globals": {"AMap": "true"}}, "browserslist": ["> 1%", "last 2 versions"], "overrides": {"@progress/kendo-ui": "^2023.3.1010"}}