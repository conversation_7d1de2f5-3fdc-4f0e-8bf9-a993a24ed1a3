<template>
  <div class="batchSaleEntityRange">
    <!--批量设置弹出框-->
    <el-dialog :title="title" :visible.sync="visible_" width="800px" @close="dialogClose">
      <div class="message el-message--info marbm_10">
        <i class="el-message__icon el-icon-info"></i>
        <p class="el-message__content">适用于同级所有节点，则只需勾选父节点。比如：适用于所有节点，只需勾选“顶级/第一个”节点。</p>
      </div>
      <el-scrollbar class="el-scrollbar_height">
        <el-tree
          ref="treeSale"
          :expand-on-click-node="false"
          :check-on-click-node="true"
          :check-strictly="true"
          :data="data"
          show-checkbox
          node-key="ID"
          :default-checked-keys="defaultCheckedKeys"
          :default-expanded-keys="defaultExpandedKeys"
          :props="defaultProps"
        ></el-tree>
      </el-scrollbar>
      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="visible_ = false" v-prevent-click>取 消</el-button>
        <el-button type="primary" size="small" @click="saveClick" v-prevent-click :loading="loading">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: "batchSaleEntityRange",
  props: {
    data: {
      type: Array,
      default() {
        return [];
      },
    },
    visible: {
      type: Boolean,
      default: false,
    },
    title: {
      type: String,
      default: "批量设置",
    },
    loading: {
      type: Boolean,
      default: false,
    },
    defaultCheckedKeys: {
      type: Array,
      default() {
        return [];
      },
    },
    defaultExpandedKeys: {
      type: Array,
      default() {
        return [];
      },
    },
  },
  /** 监听数据变化   */
  watch: {
    visible: {
      immediate: true,
      handler(val) {
        this.visible_ = val;
      },
    },
  },
  /**  引入的组件  */
  components: {},
  /**  Vue 实例的数据对象**/
  data() {
    return {
      visible_: false,
      defaultProps: {
        children: "Child",
        label: "EntityName",
      }, // 销售范围选择配置项
    };
  },
  /**计算属性  */
  computed: {},
  /**  方法集合  */
  methods: {
    /**    */
    dialogClose() {
      let that = this;
      that.$emit("update:visible", false);
    },
    /**  保存点击  */
    saveClick() {
      let that = this;
      let entityIds = that.$refs.treeSale.getCheckedKeys();
      that.$emit("saveClick", entityIds);
    },
  },
  /** 创建实例之前执行函数   */
  beforeCreate() {},
  /**  实例创建完成之后  */
  created() {},
  /**  在挂载开始之前被调用  */
  beforeMount() {},
  /**  实例被挂载后调用  */
  mounted() {},
  /**  数据更新 之前 调用  */
  beforeUpdate() {},
  /** 数据更新 完成 调用   */
  updated() {},
  /**  实例销毁之前调用  */
  beforeDestroy() {},
  /**  实例销毁后调用  */
  destroyed() {},
};
</script>

<style lang="scss">
.batchSaleEntityRange {
}
</style>
