/**
 * Created by preference on 2024/03/12
 *  zmx
 */

import * as API from "@/api/index";
export default {
  /**  查询科室 */
  department_all: (params) => {
    return API.POST("api/department/all", params);
  },
  /** 创建科室  */
  department_create: (params) => {
    return API.POST("api/department/create", params);
  },
  /** 更新科室  */
  department_update: (params) => {
    return API.POST("api/department/update", params);
  },
  /** 移动科室 */
  department_move: (params) => {
    return API.POST("api/department/move", params);
  },
};
