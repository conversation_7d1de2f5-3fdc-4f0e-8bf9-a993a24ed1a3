<template>
  <div class="Groupon content_body padCategory">
    <div class="dis_flex flex_y_center flex_dir_column padtp_20">
      <vue-qr :logoSrc="imageUrl" :text="url" :size="200"></vue-qr>
      <span class="martp_25 marbm_20">扫码进入PAD端</span>
      <el-button type="primary" size="small" @click="toPad">进入PAD端</el-button>
    </div>
  </div>
</template>

<script>
import vueQr from "vue-qr";
export default {
  name: "ProjectSeckill",

  data() {
    return {
      imageUrl: "",
      url: "",
    };
  },
  mounted() {
    this.url = window.location.host + "/iBeauty/PadView";
    const token = JSON.parse(window.localStorage.getItem("access-user")).AuthToken;
    this.url = this.url + "?token=" + token;
  },
  components: {
    vueQr,
  },
  methods: {
    toPad() {
      const url = "/iBeauty/PadView";
      var winRef = window.open("");
      winRef.location = url;
    },
  },
};
</script>

<style lang="scss" >
.padCategory {
}
</style>