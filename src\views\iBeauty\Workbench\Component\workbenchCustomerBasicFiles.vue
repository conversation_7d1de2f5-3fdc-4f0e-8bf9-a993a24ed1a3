<template>
  <div class="workbenchCustomerBasicFiles" v-loading="loading">
    <el-scrollbar v-show="customerID">
      <div>
        <div class="tip">
          <el-row type="flex" justify="space-between" align="middle">
            <el-col :span="12">标签</el-col>
            <el-col :span="12" class="text_right">
              <el-button size="mini" type="text" style="padding: 0px" @click="tagClick">编辑</el-button>
            </el-col>
          </el-row>
        </div>
        <div class="martp_5 marbm_5" style="flex: 2">
          <el-tag v-for="(item, index) in customerTag" :key="index" effect="plain" class="mar_5">{{ item.Name }} </el-tag>
        </div>
        <!-- 添加 营销云 -->
        <div class="tip martp_5">
          <el-row type="flex" justify="space-between" align="middle">
            <el-col :span="12">营销云标签</el-col>
            <el-col :span="12" class="text_right"> </el-col>
          </el-row>
        </div>
        <!-- 营销云下面的列表 -->
        <div class="martp_5" style="flex: 2">
          <el-tag v-for="(item, index) in marketingCloud" :key="index" effect="plain" class="mar_5">{{ item }} </el-tag>
        </div>
        <el-form label-width="110px" size="small" :inline="true" ref="ruleFormRef" :model="ruleForm" :rules="rules">
          <div class="tip dis_flex flex_x_between flex_y_center">
            <div>基本信息</div>
            <el-button v-if="isCustomerBasicInformationModify" size="mini" type="text" style="padding: 0px" @click="saveInfo(1)" :loading="modalLoading">保存</el-button>
          </div>

          <el-form-item label="会员姓名" prop="Name" :rules="getIsRequired('Name')">
            <el-input v-model="ruleForm.Name" style="width: 100%" :disabled="!isCustomerBasicInformationModify"></el-input>
          </el-form-item>

          <el-form-item label="手机号" prop="PhoneNumber" v-if="isCustomerPhoneNumberView" :rules="getIsRequired('PhoneNumber')">
            <el-input v-model="ruleForm.PhoneNumber" :disabled="!isCustomerPhoneNumberModify || !isCustomerBasicInformationModify"></el-input>
          </el-form-item>

          <el-form-item label="客户编号" prop="Code">
            <el-input v-model="ruleForm.Code" style="width: 100%" :disabled="!isCustomerBasicInformationModify"></el-input>
          </el-form-item>

          <el-form-item label="会员性别" :rules="getIsRequired('Gender')">
            <el-radio-group v-model="ruleForm.Gender" :disabled="!isCustomerBasicInformationModify">
              <el-radio label="2">女</el-radio>
              <el-radio label="1">男</el-radio>
              <el-radio label="0">未知</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item label=" " label-width="1" prop="Birthday" :rules="getIsRequired('Birthday')">
            <div slot="label">
              <el-select placeholder="请选择" v-model="ruleForm.BirthdayType" style="width: 100%" :disabled="!isCustomerBasicInformationModify">
                <el-option label="公历生日" value="10"></el-option>
                <el-option label="农历生日" value="20"></el-option>
              </el-select>
            </div>
            <el-date-picker v-model="ruleForm.Birthday" type="date" value-format="yyyy-MM-dd" placeholder="选择日期" :disabled="!isCustomerBasicInformationModify"> </el-date-picker>
          </el-form-item>

          <el-form-item label="信息来源" prop="CustomerSourceID" :rules="getIsRequired('CustomerSourceID')">
            <el-cascader
              v-model="ruleForm.CustomerSourceID"
              :options="customerSource"
              :props="{
                checkStrictly: true,
                children: 'Child',
                value: 'ID',
                label: 'Name',
                emitPath: false,
              }"
              :show-all-levels="false"
              filterable
              clearable
              :disabled="!isCustomerBasicInformationModify"
            ></el-cascader>
          </el-form-item>
          <!-- <el-col :span="12" v-if="isShowChannel"> -->
          <el-form-item label="渠道来源" size="small" prop="ChannelID" :rules="getIsRequired('ChannelID')">
            <el-select
              v-model="ruleForm.ChannelID"
              placeholder="请输入渠道信息搜索客户渠道来源"
              popper-class="custom_channelPopperClass"
              filterable
              remote
              reserve-keyword
              size="small"
              clearable
              :remote-method="searchChannelInfo"
              @focus="focusChannel"
              @clear="focusChannel"
              :disabled="!isCustomerBasicInformationModify"
            >
              <el-option v-for="item in channelList" :key="item.ID" :label="item.Name" :value="item.ID">
                <div style="padding-bottom: 8px">
                  <div>{{ item.Name }}</div>
                  <div class="font_12 color_999">{{ item.ParentName }}</div>
                </div>
              </el-option>
            </el-select>
            <!-- <treeselect
                
                v-model="ruleForm.ChannelID"
                :normalizer="normalizer"
                clearValueText
                :options="channelList"
                noResultsText="无匹配数据"
                placeholder="请选择渠道来源"
                :max-height="200"
              >
              </treeselect> -->
          </el-form-item>
          <!-- </el-col> -->

          <el-form-item label="会员介绍人">
            <el-select v-model="ruleForm.Introducer" placeholder="请选择会员" filterable remote reserve-keyword size="small" default-first-option :remote-method="remoteCusMethod" :disabled="!isCustomerBasicInformationModify">
              <el-option v-for="item in customerIntroducer" :key="item.ID" :label="item.Name" :value="item.ID - 0">
                <span style="float: left">{{ item.Name }}</span>
                <span style="float: right; color: #8492a6; font-size: 13px" v-if="item.PhoneNumber.length == 11">{{ item.PhoneNumber | hidephone }}</span>
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="身份证号" prop="IdentityCard" :rules="getIsRequired('IdentityCard')">
            <el-input v-model="ruleForm.IdentityCard" :disabled="!isCustomerBasicInformationModify"></el-input>
          </el-form-item>

          <el-form-item label="职业" prop="Job" :rules="getIsRequired('Job')">
            <el-input v-model="ruleForm.Job" :disabled="!isCustomerBasicInformationModify"></el-input>
          </el-form-item>

          <el-form-item label="省市区" prop="ProvinceCityArea" :rules="getIsRequired('ProvinceCityArea')">
            <el-cascader clearable placeholder="请选择省 / 市 / 区" size="small" :options="regionData" v-model="regionDataSelArr" :disabled="!isCustomerBasicInformationModify"> </el-cascader>
          </el-form-item>

          <el-form-item label="详细地址" prop="Address" :rules="getIsRequired('Address')">
            <el-input style="width: 100%; max-width: unset" v-model="ruleForm.Address" :disabled="!isCustomerBasicInformationModify"></el-input>
          </el-form-item>

          <el-form-item label="备注信息" :rules="getIsRequired('Remark')">
            <el-input v-model="ruleForm.Remark" :disabled="!isCustomerBasicInformationModify"></el-input>
          </el-form-item>

          <div v-if="customerServicer && customerServicer.length > 0">
            <div class="tip dis_flex flex_x_between flex_y_center">
              <div>服务人员</div>
              <el-button v-if="isCustomerServicerModify" size="mini" type="text" style="padding: 0px" @click="saveInfo(2)" :loading="ServeModalLoading">保存</el-button>
            </div>
            <el-row>
              <el-col :span="12" v-for="item in customerServicer" :key="item.ID">
                <el-form-item :label="item.Name">
                  <el-select v-model="item.ServicerListArr" placeholder="请选择服务人员" multiple collapse-tags reserve-keyword filterable size="small" default-first-option @change="change" :disabled="!isCustomerServicerModify">
                    <el-option v-for="serervicer in item.ServicerEmpList" :key="serervicer.ID" :label="serervicer.Name" :value="serervicer.ID">
                      <span>{{ serervicer.Name }}</span>
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
          </div>

          <div v-if="basicFileTerm && basicFileTerm.length > 0" class="tip dis_flex flex_x_between flex_y_center">
            <div>基本档案</div>
            <el-button v-if="isCustomerBasicFileModify" size="mini" type="text" style="padding: 0px" @click="saveBasicFile" :loading="baseModalLoading">保存</el-button>
          </div>
          <el-row v-if="basicFileTerm && basicFileTerm.length > 0" style="display: flex;flex-wrap: wrap;">
            <el-col :span="12" v-for="(item, index) in basicFileTerm" :key="index">
              <el-form-item v-if="item.Type == 10" :label="item.CustomerBasicFileName">
                <el-input v-model="item.Value" placeholder="请输入文本内容" clearable size="small" sty="width:60px" :disabled="!isCustomerBasicFileModify"></el-input>
              </el-form-item>
              <el-form-item v-if="item.Type == 20" :label="item.CustomerBasicFileName">
                <el-date-picker v-model="item.Value" type="date" placeholder="请选择日期" clearable size="small" :disabled="!isCustomerBasicFileModify"> </el-date-picker>
              </el-form-item>
              <el-form-item v-if="item.Type == 30" :label="item.CustomerBasicFileName">
                <el-select v-model="item.Value" placeholder="请选择单选项" clearable size="small" :disabled="!isCustomerBasicFileModify">
                  <el-option v-for="item in getComponentsProperty(item.ComponentsProperty)" :key="item.key" :label="item.value" :value="item.value"> </el-option>
                </el-select>
              </el-form-item>
              <el-form-item v-if="item.Type == 40" :label="item.CustomerBasicFileName">
                <el-select v-model="item.Value" multiple collapse-tags placeholder="请选择多选项" clearable size="small" :disabled="!isCustomerBasicFileModify">
                  <el-option v-for="item in getComponentsProperty(item.ComponentsProperty)" :key="item.key" :label="item.value" :value="item.value"> </el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </el-scrollbar>

    <!--标签弹窗-->
    <el-dialog v-if="dialogTag" title="编辑标签" :visible.sync="dialogTag" width="1000px" append-to-body>
      <el-row style="max-height: 130px; overflow-y: auto">
        <el-tag v-for="(item, index) in editCustomerTag" :key="index" closable @close="removeTag(index)" effect="plain" class="mar_5">{{ item.Name }} </el-tag>
      </el-row>
      <el-row class="pad_5" v-if="isTagEdit">
        <el-col :span="10">
          <div class="el-form-item el-form-item--small" style="margin-bottom: 0px">
            <label class="el-form-item__label" style="width: 98px">自定义标签：</label>
            <div class="el-form-item__content" style="margin-left: 98px">
              <div class="el-input el-input--small">
                <el-input type="text" autocomplete="off" placeholder="标签名限8个字" v-model="tagName" maxlength="8" size="small" clearable>
                  <template slot="append">
                    <el-button size="small" @click="addTagClick" clearable v-prevent-click>添加</el-button>
                  </template>
                </el-input>
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
      <el-row class="pad_5">
        <div class="pad_5_0">选择已有标签</div>
        <el-col style="height: 180px; overflow-y: auto" class="border radius5 pad_10">
          <el-tag v-for="item in tagList" :key="item.ID" :type="item.type" effect="plain" @click="tagSelectClick(item)" class="cursor_pointer mar_5">{{ item.Name }} </el-tag>
        </el-col>
      </el-row>
      <div slot="footer">
        <el-button size="small" @click="dialogTag = false" v-prevent-click>取 消</el-button>
        <el-button type="primary" size="small" @click="tagSaveClick" :loading="modalLoading" v-prevent-click>保存 </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { regionData } from "element-china-area-data";
import API from "@/api/CRM/Customer/customer";
import APICustomerSource from "@/api/CRM/Customer/customerSource";
import basicFileAPI from "@/api/CRM/Customer/customerBasicFile.js";
import APICustomerLevel from "@/api/CRM/Customer/customerLevel";
import APITagLibrary from "@/api/CRM/Customer/customerTagLibrary";
import APIChannel from "@/api/CRM/Channel/channelInfo";
import APIScene from "@/api/CRM/Customer/customerFileApplicationScene.js";
import validate from "@/components/js/validate.js";
// import Treeselect from "@riophae/vue-treeselect";

var Enumerable = require("linq");

export default {
  name: "workbenchCustomerBasicFiles",
  props: {
    customerID: {
      type: Number,
      require: true,
    },
    isCustomerPhoneNumberView: {
      type: Boolean,
      default: false,
    },
    isCustomerPhoneNumberModify: {
      type: Boolean,
      default: false,
    },
    isCustomerBasicFileModify: {
      type: Boolean,
      default: false,
    },
    isCustomerServicerModify: {
      type: Boolean,
      default: false,
    },
    isCustomerBasicInformationModify: {
      type: Boolean,
      default: false,
    },
  },
  /** 监听数据变化   */
  watch: {},
  /**  引入的组件  */
  components: {
    // Treeselect,
  },
  /**  Vue 实例的数据对象**/
  data() {
    return {
      modalLoading: false,
      baseModalLoading: false,
      ServeModalLoading: false,
      dialogTag: false,
      loading: false,

      isTagEdit: false, //顾客标签权限
      isShowChannel: false, //是否展示渠道

      customerLevel: [],
      regionData: regionData, //省市区
      customerDetail: {},
      customerIntroducer: [],
      regionDataSelArr: [],
      customerSource: [],
      customerServicer: [], // 服务人员
      basicFileTerm: [], // 基本档案数组

      customerTag: [], //顾客标签
      tagList: [], //全部标签
      editCustomerTag: [], //顾客修改标签
      tagName: "", //自定义标签输入框值

      ruleForm: {
        Name: "",
        PhoneNumber: "",
        Gender: "2",
        CustomerSourceID: null,
        // EmployeeID: [],
        Introducer: "",
        CustomerLevelID: "",
        Code: "",
        BirthdayType: "10",
        Birthday: "",
        ProvinceCode: "",
        CityCode: "",
        AreaCode: "",
        Job: "",
        Address: "",
        IdentityCard: "",
        Remark: "",
        ChannelID: null,
      },

      rules: {
        Name: [{ required: true, message: "请输入会员名称", trigger: ["blur", "change"] }],
        PhoneNumber: [
          {
            validator: validate.validPhoneNumber,
            trigger: ["blur", "change"],
          },
        ],
        Gender: [{ message: "请选择会员性别", trigger: ["blur", "change"] }],
        Birthday: [{ message: "请选择会员生日", trigger: ["blur", "change"] }],
        CustomerSourceID: [{ message: "请选择会员信息来源", trigger: ["blur", "change"] }],
        ChannelID: [{ message: "请选择会员渠道来源", trigger: ["blur", "change"] }],
        Code: [{ message: "请输入会员编号", trigger: ["blur", "change"] }],
        IdentityCard: [
          {
            required: false,
            message: "请输入身份证号",
            trigger: ["blur", "change"],
          },
          {
            pattern: /(^[1-9]\d{5}(18|19|([23]\d))\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$)|(^[1-9]\d{5}\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{2}$)/,
            message: "请输入合法身份证号",
            trigger: "blur",
          },
        ],
        Job: [{ message: "请输入会员职业", trigger: ["blur", "change"] }],
        ProvinceCityArea: [{ message: "请选择会员地址", trigger: ["blur", "change"] }],
        Address: [{ message: "请输入会员详细地址", trigger: ["blur", "change"] }],
        Remark: [{ message: "请输入备注信息", trigger: ["blur", "change"] }],
      },
      marketingCloud: [], //营销云的标签数组
      channelList: [],
      sceneData: [],
    };
  },
  /**计算属性  */
  computed: {
    property() {
      const that = this;
      if (that.customerDetail.ProvinceCode && that.customerDetail.CityCode && that.customerDetail.AreaCode && that.dialogDetail) {
        const first = that.regionData.filter((item) => item.value == that.customerDetail.ProvinceCode);
        const second = first[0].children.filter((item) => item.value == that.customerDetail.CityCode);
        const third = second[0].children.filter((item) => item.value == that.customerDetail.AreaCode);
        if (first.length > 0 && second.length > 0 && third.length > 0) {
          return first[0].label + "/" + second[0].label + "/" + third[0].label;
        } else {
          return "";
        }
      } else {
        return "";
      }
    },
  },
  /**  方法集合  */
  methods: {
    /**    */
    getIsRequired(code) {
      let that = this;
      let sceneCode = code;
      if (sceneCode == "CustomerSourceID") {
        sceneCode = "CustomerSource";
      }
      if (sceneCode == "ChannelID") {
        sceneCode = "Channel";
      }
      let item = that.sceneData.find((i) => i.Code == sceneCode);
      if (!item) return null;
      let rules = that.rules[code];
      if (!rules) return null;
      rules[0].required = item.IsRequired;
      return rules;
    },

    /* 树形结构数据转换 */
    normalizer(node) {
      return {
        id: node.ID,
        label: node.Name,
        children: node.Child,
      };
    },
    // 基本信息 服务人员的保存
    saveInfo(e) {
      var that = this;
      that.$refs.ruleFormRef.validate((valid) => {
        if (valid) {
          if (e == 1) {
            that.modalLoading = true;
          } else {
            that.ServeModalLoading = true;
          }
          let para = Object.assign({}, that.ruleForm);
          if (that.regionDataSelArr.length) {
            para.ProvinceCode = that.regionDataSelArr[0];
            para.CityCode = that.regionDataSelArr[1];
            para.AreaCode = that.regionDataSelArr[2];
          }
          let tempArr = [];
          that.customerServicer.forEach((val) => {
            val.ServicerListArr.forEach((em) => {
              tempArr.push({
                ServicerID: val.ID,
                EmployeeID: em,
              });
            });
          });
          para.ServicerList = tempArr;
          API.updateCustomer(para)
            .then(function (res) {
              if (res.StateCode === 200) {
                that.$message.success({
                  message: "编辑成功",
                  duration: 2000,
                });
                that.customerDetail = Object.assign({}, res.Data);
                that.getCustomerDetail();
                // 发出客户信息更新事件
                that.$emit('customerInfoUpdated');
              } else {
                that.$message.error({
                  message: res.Message,
                  duration: 2000,
                });
              }
            })
            .finally(function () {
              if (e == 1) {
                that.modalLoading = false;
              } else {
                that.ServeModalLoading = false;
              }
            });
        }
      });
    },

    // 基本档案的保存
    async saveBasicFile() {
      let that = this;
      let detail = that.basicFileTerm.map((val) => {
        let Value = "";
        if (val.Type == 40) {
          Value = JSON.stringify(val.Value);
        } else {
          Value = val.Value ? val.Value : "";
        }
        return {
          CustomerBasicFileID: val.CustomerBasicFileID,
          Value: Value,
        };
      });
      let params = {
        CustomerID: that.customerID,
        detail: detail,
      };
      that.baseModalLoading = true;
      let res = await basicFileAPI.createBasicFile(params);
      if (res.StateCode == 200) {
        that.baseModalLoading = false;
        that.$message.success("保存成功");
        // 发出客户信息更新事件
        that.$emit('customerInfoUpdated');
      } else {
        that.baseModalLoading = false;
        that.$message.error(res.Message);
      }
    },

    /* 顾客介绍人加载更多 */
    customerIntroducerLoadMore() {
      const that = this;
      if (that.customerIntroducer.length < that.CusTotal) {
        that.CusPageNum++;
        that.getCustomerIntroducer();
      }
    },
    /* 顾客介绍人远程搜索 */
    remoteCusMethod(value) {
      const that = this;
      that.customerIntroducer = [];
      that.CusPageNum = 1;
      that.getCustomerIntroducer(value);
    },
    /**  *******************************  */
    /**    */
    getCustomerInfoData() {
      let that = this;
      that.loading = true;
      that.getCustomerServicer().then(() => {
        let promiseArray = [that.getCustomerDetail(), that.customerTagData(), that.getBasicFile(), that.getMarketingCloud(), that.getChannelList()];
        Promise.all(promiseArray).then(() => {
          that.loading = false;
        });
      });
    },
    /**  获取顾客详情  */
    async getCustomerDetail() {
      let that = this;
      let params = { CustomerID: that.customerID };
      let res = await API.getCustomerDetail(params);
      if (res.StateCode == 200) {
        that.customerDetail = res.Data;
        that.ruleForm = Object.assign({}, res.Data);
        that.regionDataSelArr = [res.Data.ProvinceCode, res.Data.CityCode, res.Data.AreaCode];

        this.customerDetail.ServicerEmployee.forEach((item) => {
          that.customerServicer.forEach((servicer) => {
            if (item.ID == servicer.ID) {
              servicer.ServicerListArr = item.ServicerEmpList.map((val) => val.ID);
            }
          });
        });
        if (that.customerDetail.ChannelName) {
          that.getChannelList(that.customerDetail.ChannelName);
        }
      } else {
        that.$message.error(res.Message);
      }
    },
    /*  顾客来源 */
    async getCustomerSourceData() {
      var that = this;
      var params = {
        Name: "",
        Active: true,
      };
      let res = await APICustomerSource.getCustomerSource(params);
      if (res.StateCode == 200) {
        that.customerSource = res.Data;
      } else {
        that.$message.error({
          message: res.Message,
          duration: 2000,
        });
      }
    },
    /* 顾客等级 */
    async getCustomerLevelData() {
      var that = this;
      var params = {
        Name: "",
        Active: true,
      };
      let res = await APICustomerLevel.getCustomerLevel(params);
      if (res.StateCode == 200) {
        that.customerLevel = res.Data;
      } else {
        that.$message.error({
          message: res.Message,
          duration: 2000,
        });
      }
    },
    /* 顾客介绍人 */
    async getCustomerIntroducer(value) {
      var that = this;
      var params = {
        Name: value,
        PageNum: that.CusPageNum,
        Active: true,
      };
      let res = await API.customerAll(params);
      if (res.StateCode == 200) {
        that.customerIntroducer = [...that.customerIntroducer, ...res.List];
        that.CusTotal = res.Total;
      } else {
        that.$message.error({
          message: res.Message,
          duration: 2000,
        });
      }
    },
    /**   服务人员   */
    async getCustomerServicer() {
      let that = this;
      let params = {};
      let res = await API.getAllCustomerServicer(params);
      if (res.StateCode == 200) {
        that.customerServicer = res.Data;
        that.customerServicer.forEach((item) => {
          item.ServicerListArr = [];
        });
      } else {
        that.$message.error({
          message: res.Message,
          duration: 2000,
        });
      }
    },
    /**   获取基本信息档案项   */
    async getBasicFile() {
      let that = this;
      let params = {
        CustomerID: that.customerID, //顾客ID
      };
      let res = await basicFileAPI.getBasicFile(params);
      if (res.StateCode == 200) {
        that.basicFileTerm = res.Data.map((val) => {
          if (val.Type == 40 && typeof val.Value == "string") {
            val.Value = val.Value ? JSON.parse(val.Value) : [];
          }
          return val;
        });
      } else {
        that.$message.error(res.Message);
      }
    },

    change() {
      this.$forceUpdate();
    },
    /**  获取选择项   */
    getComponentsProperty(property) {
      if (!property) return [];
      return JSON.parse(property);
    },
    // 顾客标签
    async customerTagData() {
      var that = this;
      var params = {
        ID: that.customerID,
      };
      let res = await API.getCustomerTag(params);
      // .then((res) => {
      if (res.StateCode == 200) {
        that.customerTag = res.Data;
      } else {
        that.$message.error({
          message: res.Message,
          duration: 2000,
        });
      }
      // })
      // .finally(function () {
      //   that.loading = false;
      // });
    },
    // 标签列表
    tagData: function () {
      var that = this;
      APITagLibrary.customerTagLibraryAll()
        .then((res) => {
          if (res.StateCode == 200) {
            that.tagList = res.Data;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {});
    },
    // 标签
    tagClick: function () {
      var that = this;
      that.editCustomerTag = Object.assign([], that.customerTag);
      that.tagType();
      that.dialogTag = true;
    },
    tagType: function () {
      var that = this;
      that.tagList.forEach(function (item) {
        item.type = "info";
        that.editCustomerTag.forEach(function (tag) {
          if (item.ID == tag.ID) {
            item.type = "primary";
          }
        });
      });
    },
    // 删除标签
    removeTag: function (index) {
      var that = this;
      that.editCustomerTag.splice(index, 1);
      that.tagType();
    },
    // 添加标签
    addTagClick: function () {
      var that = this;
      var params = {
        Name: that.tagName,
      };
      APITagLibrary.customerTagLibraryCreate(params)
        .then(function (res) {
          if (res.StateCode === 200) {
            that.editCustomerTag.push(res.Data);
            that.tagList.push(res.Data);
            that.tagType();
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.modalLoading = false;
        });
    },
    // 选择标签
    tagSelectClick: function (row) {
      var that = this;
      if (row.type == "info") {
        that.editCustomerTag.push(row);
      }
      that.tagType();
    },
    // 标签保存
    tagSaveClick: function () {
      var that = this;
      var TagLibrary = Enumerable.from(that.editCustomerTag)
        .select((val) => val.ID)
        .toArray();
      var params = {
        ID: that.customerID,
        TagLibrary: TagLibrary,
      };
      API.updateCustomerTagLibrary(params)
        .then(function (res) {
          if (res.StateCode === 200) {
            that.dialogTag = false;
            that.customerTagData();
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {});
    },
    //获取营销云的下列标签
    async getMarketingCloud() {
      let params = { CustomerID: this.customerID };
      let res = await API.getMarketingCloud(params);
      // .then((res) => {
      if (res.StateCode == 200) {
        this.marketingCloud = res.Data;
      } else {
        this.$message.error({
          message: res.Message,
          duration: 2000,
        });
      }
      // });
    },
    /**  渠道来源获取焦点 清除数据  */
    focusChannel() {
      let that = this;
      that.channelList = [];
    },
    /**    */
    searchChannelInfo(value) {
      this.getChannelList(value);
    },
    /* 
    /* 获取渠道来源 */
    async getChannelList(value) {
      let that = this;
      let params = { Name: value, CustomerID: that.customerDetail.ID, Active: true };
      let res = await APIChannel.channel_customerInfo(params);
      // .then((res) => {
      if (res.StateCode == 200) {
        that.channelList = res.Data;
      }
      // });
    },
    /* 获取顾客档案配置 */
    async customerFileApplicationScene_all() {
      let that = this;
      try {
        let params = {};
        let res = await APIScene.customerFileApplicationScene_all(params);
        if (res.StateCode == 200) {
          that.sceneData = res.Data;
        } else {
          that.$message.error(res.Message);
        }
      } catch (error) {
        that.$message.error(error);
      }
    },
  },

  /** 创建实例之前执行函数   */
  beforeCreate() {},
  /**  实例创建完成之后  */
  created() {
    let that = this;
    // 顾客详情渠道
    that.isShowChannel = that.$permission.getCustomerDetailPermission(that.$router, "iBeauty-Customer-Customer-Channel");
    // 顾客信息编辑权限
    that.isTagEdit = that.$permission.getCustomerDetailPermission(that.$router, "iBeauty-Customer-Customer-CustomTagLibrary");

    that.getCustomerSourceData();
    that.getCustomerLevelData();
    that.getCustomerIntroducer();
    that.getBasicFile();
    // 全部标签列表ƒ
    that.tagData();
    that.getMarketingCloud();
    that.customerFileApplicationScene_all();
  },
  /**  在挂载开始之前被调用  */
  beforeMount() {},
  /**  实例被挂载后调用  */
  mounted() {},
  /**  数据更新 之前 调用  */
  beforeUpdate() {},
  /** 数据更新 完成 调用   */
  updated() {},
  /**  实例销毁之前调用  */
  beforeDestroy() {},
  /**  实例销毁后调用  */
  destroyed() {},
};
</script>

<style lang="scss">
.workbenchCustomerBasicFiles {
  overflow: hidden !important;
  font-size: 14px;
  color: #666666;
  /*height: calc(100vh - 23vh);*/
  height: 100%;
  .el-scrollbar {
    height: 100%;
    .el-scrollbar__wrap {
      overflow-x: hidden !important;
    }
  }
  .el-form-item__content {
    width: 200px;
    .el-select,
    .el-cascader,
    .el-date-editor.el-input {
      width: 100%;
    }
  }
}
.custom_channelPopperClass {
  .el-select-dropdown__item {
    line-height: normal;
    height: auto;
  }
}
</style>
