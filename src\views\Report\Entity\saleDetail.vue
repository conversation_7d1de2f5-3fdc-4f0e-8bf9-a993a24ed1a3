<template>
  <div class="content_body_nopadding ReportEntitySaleDetail" v-loading="loading">
    <el-tabs type="border-card">
      <el-tab-pane label="销售明细">
        <span slot="label">
          销售明细
          <el-popover placement="top-start" width="200" trigger="hover">
            <p>包含：开单、开卡、充值订单</p>
            <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
          </el-popover>
        </span>
        <div class="nav_header" style="padding: 0px">
          <el-form :inline="true" size="small" :model="searchSaleData" @submit.native.prevent>
            <el-row>
              <el-form-item v-if="storeEntityList.length > 1" label="开单门店">
                <el-select v-model="searchSaleData.EntityID" clearable filterable placeholder="请选择开单门店" :default-first-option="true" @change="handleSaleSearch">
                  <el-option v-for="item in storeEntityList" :key="item.ID" :label="item.EntityName" :value="item.ID"> </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="时间筛选">
                <el-date-picker v-model="searchSaleData.QueryDate" :picker-options="pickerOptions" :clearable="false" unlink-panels type="daterange" range-separator="至" value-format="yyyy-MM-dd" start-placeholder="开始日期" end-placeholder="结束日期" @change="handleSaleSearch"></el-date-picker>
              </el-form-item>
              <el-form-item label="订单来源">
                <el-select v-model="searchSaleData.Channel" clearable filterable placeholder="请选择订单来源" :default-first-option="true" @change="handleSaleSearch">
                  <el-option label="PC" value="PC"> </el-option>
                  <el-option label="小程序" value="Miniprogram"> </el-option>
                  <el-option label="商城" value="MicroMall"> </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="商品分类">
                <el-cascader v-model="searchSaleData.CategoryID" :options="categoryList" :props="cascaderProps" @change="handleSaleSearch" clearable></el-cascader>
              </el-form-item>
              <el-form-item label="商品名称">
                <el-input v-model="searchSaleData.GoodName" clearable @keyup.enter.native="handleSaleSearch" @clear="handleSaleSearch" placeholder="请输入商品名称"></el-input>
              </el-form-item>
            </el-row>
            <el-form-item label="客户信息">
              <el-input v-model="searchSaleData.CustomerName" clearable @keyup.enter.native="handleSaleSearch" @clear="handleSaleSearch" placeholder="请输入客户姓名、手机号"></el-input>
            </el-form-item>

            <el-form-item v-if="BelongEntityList.length > 1" label="所属组织">
              <el-select v-model="searchSaleData.BelongEntityID" clearable filterable placeholder="请选择客户所属组织" :default-first-option="true" @change="handleSaleSearch">
                <el-option v-for="item in BelongEntityList" :key="item.ID" :label="item.Name" :value="item.ID"> </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="会员等级">
              <el-select v-model="searchSaleData.CustomerLevelID" placeholder="请选择会员等级" filterable size="small" clearable @change="handleSaleSearch">
                <el-option v-for="item in customerLevel" :key="item.ID" :label="item.Name" :value="item.ID"> </el-option>
              </el-select>
            </el-form-item>

            <el-form-item label="信息来源">
              <el-cascader
                v-model="searchSaleData.CustomerSourceID"
                placeholder="请选择客户信息来源"
                :options="customerSource"
                :props="{
                  checkStrictly: true,
                  children: 'Child',
                  value: 'ID',
                  label: 'Name',
                  emitPath: false,
                }"
                :show-all-levels="false"
                filterable
                clearable
                @change="handleSaleSearch"
              ></el-cascader>
            </el-form-item>

            <el-form-item label="渠道来源">
              <el-input v-model="searchSaleData.ChannelName" clearable @keyup.enter.native="handleSaleSearch" @clear="handleSaleSearch" placeholder="请输入渠道来源"></el-input>
            </el-form-item>

            <el-form-item label="介绍人">
              <el-input v-model="searchSaleData.IntroducerName" clearable @keyup.enter.native="handleSaleSearch" @clear="handleSaleSearch" placeholder="请输入介绍人"></el-input>
            </el-form-item>

            <el-form-item>
              <el-button type="primary" v-prevent-click @click="handleSaleSearch">搜索</el-button>
            </el-form-item>
            <!-- <el-form-item>
              <el-button v-if="SaleDetailExport" type="primary" size="small" :loading="downloadLoading" @click="downloadSaleExcel">导出</el-button>
            </el-form-item> -->
            
            <el-form-item>
              <el-dropdown @command="downloadSaleExcel_command" v-if="SaleDetailExport && SaleDetailExportDisPlayPhone" :loading="downloadLoading">
                <el-button type="primary"> 导出<i class="el-icon-arrow-down el-icon--right"></i> </el-button>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item command="excelNoDisPlayPhone">导出</el-dropdown-item>
                  <el-dropdown-item command="excelDisPlayPhone">导出(手机号)</el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
              <el-button @click="downloadSaleExcel_command('excelNoDisPlayPhone')" v-else-if="SaleDetailExport" type="primary" v-prevent-click :loading="downloadLoading"> 导出 </el-button>
              <el-button @click="downloadSaleExcel_command('excelDisPlayPhone')" v-else-if="SaleDetailExportDisPlayPhone" type="primary" v-prevent-click :loading="downloadLoading"> 导出(手机号） </el-button>
            </el-form-item>
          </el-form>
        </div>
        <el-table size="small" show-summary :summary-method="getsaleDetailListSummaries" :data="saleDetailList">
          <el-table-column prop="BillID" label="订单编号" min-width="90px"></el-table-column>
          <el-table-column prop="BillDate" label="下单日期">
            <template slot-scope="scope">
              {{ scope.row.BillDate | dateFormat("YYYY-MM-DD HH:mm") }}
            </template>
          </el-table-column>
          <el-table-column prop="EntityName" label="下单门店"></el-table-column>
          <el-table-column prop="EmployeeName" label="开单人"></el-table-column>
          <el-table-column prop="Channel" label="订单来源"></el-table-column>
          <el-table-column label="客户信息">
            <el-table-column prop="CustomerName" width="148px" label="客户">
              <template slot-scope="scope">
                <div>{{ scope.row.CustomerName }}</div>
                <div v-if="scope.row.CustomerPhoneNumber">手机号：{{ scope.row.CustomerPhoneNumber | hidephone }}</div>
              </template>
            </el-table-column>
            <el-table-column prop="CustomerEntityName" label="所属组织"></el-table-column>
            <el-table-column prop="CustomerLevelName" label="会员等级"></el-table-column>
            <el-table-column prop="CustomerSourceName" label="信息来源"></el-table-column>
            <el-table-column prop="ChannelName" label="渠道来源"></el-table-column>
            <el-table-column prop="IntroducerName" label="介绍人"></el-table-column>
            <el-table-column prop="CreatedOn" label="注册日期">
              <template slot-scope="scope">
                {{ scope.row.CreatedOn | dateFormat("YYYY-MM-DD HH:mm") }}
              </template>
            </el-table-column>
          </el-table-column>
          <el-table-column label="商品信息">
            <el-table-column prop="GoodName" label="商品名称"></el-table-column>
            <el-table-column prop="CategoryName" label="商品分类"></el-table-column>
            <el-table-column prop="GoodsTypeName" label="商品类型"></el-table-column>
            <el-table-column prop="IsLargess" label="是否赠送">
              <template slot-scope="scope">
                <div v-if="scope.row.IsLargess">是</div>
                <div v-else>否</div>
              </template>
            </el-table-column>
            <el-table-column prop="DetailsRemark" label="明细备注"></el-table-column>
            <el-table-column prop="Remark" label="单据备注"></el-table-column>


            <el-table-column align="right" prop="Price" label="单价">
              <template slot-scope="scope">
                {{ scope.row.Price | toFixed | NumFormat }}
              </template>
            </el-table-column>
            <el-table-column align="right" prop="Quantity" label="数量"></el-table-column>
            <el-table-column align="right" prop="PreferentialAmount" label="优惠金额">
              <template align="right" slot-scope="scope">
                <div v-if="scope.row.PreferentialAmount < 0" class="color_red">
                  {{ scope.row.PreferentialAmount | toFixed | NumFormat }}
                </div>
                <div v-else-if="scope.row.PreferentialAmount > 0" class="color_green">+{{ scope.row.PreferentialAmount | toFixed | NumFormat }}</div>
                <div v-else>0.00</div>
              </template>
            </el-table-column>
            <el-table-column align="right" prop="TotalAmount" label="合计金额">
              <template slot-scope="scope">
                <span class="font_weight_600">{{ scope.row.TotalAmount | toFixed | NumFormat }}</span>
              </template>
            </el-table-column>
            <el-table-column align="right" prop="ArrearAmount" label="欠款金额">
              <template slot-scope="scope">
                {{ scope.row.ArrearAmount | toFixed | NumFormat }}
              </template>
            </el-table-column>
          </el-table-column>
          <el-table-column label="支付信息">
            <el-table-column align="right" prop="PayAmount" label="实收金额">
              <template slot-scope="scope">
                {{ scope.row.PayAmount | toFixed | NumFormat }}
              </template>
            </el-table-column>
            <el-table-column align="right" prop="SavingCardDeductionAmount" label="卡抵扣">
              <template slot-scope="scope">
                {{ scope.row.SavingCardDeductionAmount | toFixed | NumFormat }}
              </template>
            </el-table-column>
            <el-table-column align="right" prop="LargessSavingCardDeductionAmount" label="赠卡抵扣">
              <template slot-scope="scope">
                {{ scope.row.LargessSavingCardDeductionAmount | toFixed | NumFormat }}
              </template>
            </el-table-column>
            <el-table-column align="right" prop="LargessAmount" label="赠送金额">
              <template slot-scope="scope">
                {{ scope.row.LargessAmount | toFixed | NumFormat }}
              </template>
            </el-table-column>
          </el-table-column>
        </el-table>
        <div class="pad_15 text_right">
          <el-pagination background v-if="salePaginations.total > 0" @current-change="handleSaleDetailPageChange" :current-page.sync="salePaginations.page" :page-size="salePaginations.page_size" :layout="salePaginations.layout" :total="salePaginations.total"></el-pagination>
        </div>
      </el-tab-pane>
      <el-tab-pane label="补欠款明细">
        <div class="nav_header" style="padding: 0px">
          <el-form :inline="true" size="small" :model="searchSaleArrearData" @submit.native.prevent>
            <el-row>
              <el-form-item v-if="storeEntityList.length > 1" label="开单门店">
                <el-select v-model="searchSaleArrearData.EntityID" clearable filterable placeholder="请选择开单门店" :default-first-option="true" @change="handleSaleArrearSearch">
                  <el-option v-for="item in storeEntityList" :key="item.ID" :label="item.EntityName" :value="item.ID"> </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="时间筛选">
                <el-date-picker
                  v-model="searchSaleArrearData.QueryDate"
                  :picker-options="saleArrearpickerOptions"
                  :clearable="false"
                  unlink-panels
                  type="daterange"
                  range-separator="至"
                  value-format="yyyy-MM-dd"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  @change="handleSaleArrearSearch"
                ></el-date-picker>
              </el-form-item>

              <el-form-item label="订单来源">
                <el-select v-model="searchSaleArrearData.Channel" clearable filterable placeholder="请选择订单来源" :default-first-option="true" @change="handleSaleSearch">
                  <el-option label="PC" value="PC"> </el-option>
                  <el-option label="小程序" value="Miniprogram"> </el-option>
                  <el-option label="商城" value="MicroMall"> </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="商品分类">
                <el-cascader v-model="searchSaleArrearData.CategoryID" :options="categoryList" :props="cascaderProps" @change="handleSaleArrearSearch" clearable></el-cascader>
              </el-form-item>
              <el-form-item label="商品名称">
                <el-input v-model="searchSaleArrearData.GoodName" clearable @keyup.enter.native="handleSaleArrearSearch" @clear="handleSaleArrearSearch" placeholder="请输入商品名称"></el-input>
              </el-form-item>
            </el-row>
            <el-form-item label="客户信息">
              <el-input v-model="searchSaleArrearData.CustomerName" clearable @keyup.enter.native="handleSaleArrearSearch" @clear="handleSaleArrearSearch" placeholder="请输入客户姓名、手机号"> </el-input>
            </el-form-item>
            <el-form-item v-if="BelongEntityList.length > 1" label="所属组织">
              <el-select v-model="searchSaleArrearData.BelongEntityID" clearable filterable placeholder="请选择客户所属组织" :default-first-option="true" @change="handleSaleArrearSearch">
                <el-option v-for="item in BelongEntityList" :key="item.ID" :label="item.Name" :value="item.ID"> </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="会员等级">
              <el-select v-model="searchSaleArrearData.CustomerLevelID" placeholder="请选择会员等级" filterable size="small" clearable @change="handleSaleArrearSearch">
                <el-option v-for="item in customerLevel" :key="item.ID" :label="item.Name" :value="item.ID"> </el-option>
              </el-select>
            </el-form-item>

            <el-form-item label="信息来源">
              <el-cascader
                v-model="searchSaleArrearData.CustomerSourceID"
                placeholder="请选择客户信息来源"
                :options="customerSource"
                :props="{
                  checkStrictly: true,
                  children: 'Child',
                  value: 'ID',
                  label: 'Name',
                  emitPath: false,
                }"
                :show-all-levels="false"
                filterable
                clearable
                @change="handleSaleArrearSearch"
              ></el-cascader>
            </el-form-item>

            <el-form-item label="渠道来源">
              <el-input v-model="searchSaleArrearData.ChannelName" clearable @keyup.enter.native="handleSaleSearch" @clear="handleSaleSearch" placeholder="请输入渠道来源"></el-input>
            </el-form-item>

            <el-form-item label="介绍人">
              <el-input v-model="searchSaleArrearData.IntroducerName" clearable @keyup.enter.native="handleSaleSearch" @clear="handleSaleSearch" placeholder="请输入介绍人"></el-input>
            </el-form-item>

            <el-form-item>
              <el-button type="primary" v-prevent-click @click="handleSaleArrearSearch">搜索</el-button>
            </el-form-item>
            <!-- <el-form-item>
              <el-button v-if="ArrearSaleDetailExport" type="primary" size="small" :loading="downloadLoading" @click="downloadSaleArrearExcel">导出</el-button>
            </el-form-item> -->

                  
            <el-form-item>
              <el-dropdown @command="downloadSaleArrearExcel_command" v-if="ArrearSaleDetailExport && ArrearSaleDetailExportDisPlayPhone" :loading="downloadLoading">
                <el-button type="primary"> 导出<i class="el-icon-arrow-down el-icon--right"></i> </el-button>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item command="excelNoDisPlayPhone">导出</el-dropdown-item>
                  <el-dropdown-item command="excelDisPlayPhone">导出(手机号)</el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
              <el-button @click="downloadSaleArrearExcel_command('excelNoDisPlayPhone')" v-else-if="ArrearSaleDetailExport" type="primary" v-prevent-click :loading="downloadLoading"> 导出 </el-button>
              <el-button @click="downloadSaleArrearExcel_command('excelDisPlayPhone')" v-else-if="ArrearSaleDetailExportDisPlayPhone" type="primary" v-prevent-click :loading="downloadLoading"> 导出(手机号） </el-button>
            </el-form-item>
          </el-form>
        </div>
        <el-table size="small" show-summary :summary-method="getsaleArrearDetailListSummaries" :data="saleArrearDetailList">
          <el-table-column prop="BillID" label="订单编号"></el-table-column>
          <el-table-column prop="BillDate" label="下单日期">
            <template slot-scope="scope">
              {{ scope.row.BillDate | dateFormat("YYYY-MM-DD HH:mm") }}
            </template>
          </el-table-column>
          <el-table-column prop="EntityName" label="下单门店"></el-table-column>
          <el-table-column prop="EmployeeName" label="开单人"></el-table-column>
          <el-table-column prop="Channel" label="订单来源"></el-table-column>
          <el-table-column label="客户信息">
            <el-table-column prop="CustomerName" width="150px" label="客户">
              <template slot-scope="scope">
                <div>
                  <span class="marrt_10">{{ scope.row.CustomerName }}</span>
                  <!-- <span v-if="scope.row.CustomerCode">({{ scope.row.CustomerCode }})</span> -->
                </div>
                <div v-if="scope.row.CustomerPhoneNumber">手机号：{{ scope.row.CustomerPhoneNumber | hidephone }}</div>
              </template>
            </el-table-column>
            <el-table-column prop="CustomerEntityName" label="所属组织"></el-table-column>
            <el-table-column prop="CustomerLevelName" label="会员等级"></el-table-column>
            <el-table-column prop="CustomerSourceName" label="信息来源"></el-table-column>
            <el-table-column prop="ChannelName" label="渠道来源"></el-table-column>
            <el-table-column prop="IntroducerName" label="介绍人"></el-table-column>
            <el-table-column prop="CreatedOn" label="注册日期">
              <template slot-scope="scope">
                {{ scope.row.CreatedOn | dateFormat("YYYY-MM-DD HH:mm") }}
              </template>
            </el-table-column>
          </el-table-column>
          <el-table-column label="商品信息">
            <el-table-column prop="GoodName" label="商品名称"></el-table-column>
            <el-table-column prop="CategoryName" label="商品分类"></el-table-column>
            <el-table-column prop="GoodsTypeName" label="商品类型"></el-table-column>
            
            <el-table-column prop="DetailsRemark" label="明细备注"></el-table-column>
            <el-table-column prop="Remark" label="单据备注"></el-table-column>
            <el-table-column align="right" prop="BuyAmount" label="购买金额">
              <template slot-scope="scope">
                {{ scope.row.BuyAmount | toFixed | NumFormat }}
              </template>
            </el-table-column>
          </el-table-column>
          <el-table-column prop="Channel" label="支付信息">
            <el-table-column prop="PayAmount" label="实收金额">
              <template slot-scope="scope">
                <span class="font_weight_600">{{ scope.row.PayAmount | toFixed | NumFormat }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="SavingCardDeductionAmount" label="卡抵扣金">
              <template slot-scope="scope">
                <span class="font_weight_600">{{ scope.row.SavingCardDeductionAmount | toFixed | NumFormat }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="LargessSavingCardDeductionAmount" label="赠卡抵扣">
              <template slot-scope="scope">
                <span class="font_weight_600">{{ scope.row.LargessSavingCardDeductionAmount | toFixed | NumFormat }}</span>
              </template>
            </el-table-column>
          </el-table-column>
        </el-table>
        <div class="pad_15 text_right">
          <el-pagination
            background
            v-if="saleArrearPaginations.total > 0"
            @current-change="handleSaleArrearDetailPageChange"
            :current-page.sync="saleArrearPaginations.page"
            :page-size="saleArrearPaginations.page_size"
            :layout="saleArrearPaginations.layout"
            :total="saleArrearPaginations.total"
          ></el-pagination>
        </div>
      </el-tab-pane>
      <el-tab-pane label="退款明细">
        <div class="nav_header" style="padding: 0px">
          <el-form :inline="true" size="small" :model="searchSaleRefundData" @submit.native.prevent>
            <el-row>
              <el-form-item v-if="storeEntityList.length > 1" label="开单门店">
                <el-select v-model="searchSaleRefundData.EntityID" clearable filterable placeholder="请选择开单门店" :default-first-option="true" @change="handleSaleRefundSearch">
                  <el-option v-for="item in storeEntityList" :key="item.ID" :label="item.EntityName" :value="item.ID"> </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="时间筛选">
                <el-date-picker
                  v-model="searchSaleRefundData.QueryDate"
                  :picker-options="saleRefundpickerOptions"
                  :clearable="false"
                  unlink-panels
                  type="daterange"
                  range-separator="至"
                  value-format="yyyy-MM-dd"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  @change="handleSaleRefundSearch"
                ></el-date-picker>
              </el-form-item>
              <el-form-item label="订单来源">
                <el-select v-model="searchSaleRefundData.Channel" clearable filterable placeholder="请选择订单来源" :default-first-option="true" @change="handleSaleSearch">
                  <el-option label="PC" value="PC"> </el-option>
                  <el-option label="小程序" value="Miniprogram"> </el-option>
                  <el-option label="商城" value="MicroMall"> </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="商品分类">
                <el-cascader v-model="searchSaleRefundData.CategoryID" :options="categoryList" :props="cascaderProps" @change="handleSaleRefundSearch" clearable></el-cascader>
              </el-form-item>
              <el-form-item label="商品名称">
                <el-input v-model="searchSaleRefundData.GoodName" clearable @keyup.enter.native="handleSaleRefundSearch" @clear="handleSaleRefundSearch" placeholder="请输入商品名称"></el-input>
              </el-form-item>
            </el-row>
            <el-form-item label="客户信息">
              <el-input v-model="searchSaleRefundData.CustomerName" clearable @keyup.enter.native="handleSaleRefundSearch" @clear="handleSaleRefundSearch" placeholder="请输入客户姓名、手机号"> </el-input>
            </el-form-item>
            <el-form-item v-if="BelongEntityList.length > 1" label="所属组织">
              <el-select v-model="searchSaleRefundData.BelongEntityID" clearable filterable placeholder="请选择客户所属组织" :default-first-option="true" @change="handleSaleRefundSearch">
                <el-option v-for="item in BelongEntityList" :key="item.ID" :label="item.Name" :value="item.ID"> </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="会员等级">
              <el-select v-model="searchSaleRefundData.CustomerLevelID" placeholder="请选择会员等级" filterable size="small" clearable @change="handleSaleRefundSearch">
                <el-option v-for="item in customerLevel" :key="item.ID" :label="item.Name" :value="item.ID"> </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="信息来源">
              <el-cascader
                v-model="searchSaleRefundData.CustomerSourceID"
                placeholder="请选择客户信息来源"
                :options="customerSource"
                :props="{
                  checkStrictly: true,
                  children: 'Child',
                  value: 'ID',
                  label: 'Name',
                  emitPath: false,
                }"
                :show-all-levels="false"
                filterable
                clearable
                @change="handleSaleRefundSearch"
              ></el-cascader>
            </el-form-item>
            <el-form-item label="渠道来源">
              <el-input v-model="searchSaleRefundData.ChannelName" clearable @keyup.enter.native="handleSaleSearch" @clear="handleSaleSearch" placeholder="请输入渠道来源"></el-input>
            </el-form-item>

            <el-form-item label="介绍人">
              <el-input v-model="searchSaleRefundData.IntroducerName" clearable @keyup.enter.native="handleSaleSearch" @clear="handleSaleSearch" placeholder="请输入介绍人"></el-input>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" v-prevent-click @click="handleSaleRefundSearch">搜索</el-button>
            </el-form-item>

            <!-- <el-form-item>
              <el-button v-if="RefundSaleDetailExport" type="primary" size="small" :loading="downloadLoading" @click="downloadSaleRefundExcel">导出</el-button>
            </el-form-item> -->
            <el-form-item>
              <el-dropdown @command="downloadSaleRefundExcel_command" v-if="RefundSaleDetailExport && RefundSaleExportDisPlayPhone" :loading="downloadLoading">
                <el-button type="primary"> 导出<i class="el-icon-arrow-down el-icon--right"></i> </el-button>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item command="excelNoDisPlayPhone">导出</el-dropdown-item>
                  <el-dropdown-item command="excelDisPlayPhone">导出(手机号)</el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
              <el-button @click="downloadSaleRefundExcel_command('excelNoDisPlayPhone')" v-else-if="RefundSaleDetailExport" type="primary" v-prevent-click :loading="downloadLoading"> 导出 </el-button>
              <el-button @click="downloadSaleRefundExcel_command('excelDisPlayPhone')" v-else-if="RefundSaleExportDisPlayPhone" type="primary" v-prevent-click :loading="downloadLoading"> 导出(手机号） </el-button>
            </el-form-item>

          </el-form>
        </div>
        <el-table size="small" show-summary :summary-method="getsaleRefundDetailListSummaries" :data="saleRefundDetailList">
          <el-table-column prop="BillID" label="订单编号"></el-table-column>
          <el-table-column prop="BillDate" label="下单日期">
            <template slot-scope="scope">
              {{ scope.row.BillDate | dateFormat("YYYY-MM-DD HH:mm") }}
            </template>
          </el-table-column>
          <el-table-column prop="EntityName" label="下单门店"></el-table-column>
          <el-table-column prop="EmployeeName" label="开单人"></el-table-column>
          <el-table-column prop="Channel" label="订单来源"></el-table-column>
          <el-table-column label="客户信息">
            <el-table-column prop="CustomerName" width="150px" label="客户">
              <template slot-scope="scope">
                <div>{{ scope.row.CustomerName }}</div>
                <div v-if="scope.row.CustomerPhoneNumber">手机号：{{ scope.row.CustomerPhoneNumber | hidephone }}</div>
              </template>
            </el-table-column>
            <el-table-column prop="CustomerEntityName" label="所属组织"></el-table-column>
            <el-table-column prop="CustomerLevelName" label="会员等级"></el-table-column>
            <el-table-column prop="CustomerSourceName" label="信息来源"></el-table-column>
            <el-table-column prop="ChannelName" label="渠道来源"></el-table-column>
            <el-table-column prop="IntroducerName" label="介绍人"></el-table-column>
            <el-table-column prop="CreatedOn" label="注册日期">
              <template slot-scope="scope">
                {{ scope.row.CreatedOn | dateFormat("YYYY-MM-DD HH:mm") }}
              </template>
            </el-table-column>
          </el-table-column>
          <el-table-column label="商品信息">
            <el-table-column prop="GoodName" label="商品名称"></el-table-column>
            <el-table-column prop="CategoryName" label="商品分类"></el-table-column>
            <el-table-column prop="GoodsTypeName" label="商品类型"></el-table-column>
            <el-table-column prop="IsLargess" label="是否赠送">
              <template slot-scope="scope">
                <div v-if="scope.row.IsLargess">是</div>
                <div v-else>否</div>
              </template>
            </el-table-column>
            
            <el-table-column prop="DetailsRemark" label="明细备注"></el-table-column>
            <el-table-column prop="Remark" label="单据备注"></el-table-column>
            <el-table-column align="right" prop="Quantity" label="退款数量"></el-table-column>
            <el-table-column align="right" prop="OriginAmount" label="可退金额">
              <template slot-scope="scope">
                {{ scope.row.OriginAmount | toFixed | NumFormat }}
              </template>
            </el-table-column>
            <el-table-column align="right" prop="TotalAmount" label="实际退金额">
              <template slot-scope="scope">
                <span class="font_weight_600">{{ scope.row.TotalAmount | toFixed | NumFormat }}</span>
              </template>
            </el-table-column>
          </el-table-column>
          <el-table-column label="退款信息">
            <el-table-column align="right" prop="PayAmount" label="退款金额">
              <template slot-scope="scope">
                {{ scope.row.PayAmount | toFixed | NumFormat }}
              </template>
            </el-table-column>
            <el-table-column align="right" prop="SavingCardDeductionAmount" label="退卡金额">
              <template slot-scope="scope">
                {{ scope.row.SavingCardDeductionAmount | toFixed | NumFormat }}
              </template>
            </el-table-column>
            <el-table-column align="right" prop="LargessSavingCardDeductionAmount" label="退赠卡金额">
              <template slot-scope="scope">
                {{ scope.row.LargessSavingCardDeductionAmount | toFixed | NumFormat }}
              </template>
            </el-table-column>
            <el-table-column align="right" prop="LargessAmount" label="赠送金额">
              <template slot-scope="scope">
                {{ scope.row.LargessAmount | toFixed | NumFormat }}
              </template>
            </el-table-column>
          </el-table-column>
        </el-table>
        <div class="pad_15 text_right">
          <el-pagination
            background
            v-if="saleRefundPaginations.total > 0"
            @current-change="handleSaleRefundDetailPageChange"
            :current-page.sync="saleRefundPaginations.page"
            :page-size="saleRefundPaginations.page_size"
            :layout="saleRefundPaginations.layout"
            :total="saleRefundPaginations.total"
          ></el-pagination>
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import EntityAPI from "@/api/Report/Common/entity";
import APICustomerLevel from "@/api/CRM/Customer/customerLevel";
import APICustomerSource from "@/api/CRM/Customer/customerSource";
import API from "@/api/Report/Entity/saleDetailStatement";
import permission from "@/components/js/permission.js";
import APICategory from "@/api/Report/Goods/SaleStatistics.js";
const dayjs = require("dayjs");
const isoWeek = require("dayjs/plugin/isoWeek");
dayjs.extend(isoWeek);

export default {
  name: "ReportEntitySaleDetail",
  data() {
    return {
      loading: false,
      downloadLoading: false,
      searchSaleData: {
        EntityID: null,
        QueryDate: [dayjs(new Date()).format("YYYY-MM-DD"), dayjs(new Date()).format("YYYY-MM-DD")],
        RegisterQueryDate: null,
        GoodsTypeName: "",
        GoodName: "",
        IsLargess: null,
        CustomerName: null,
        CustomerLevelID: "",
        CustomerSourceID: "",
        CategoryID: "",
        Channel: "", //订单渠道：PC: PC，小程序：Miniprogram，商城：MicroMall
        IntroducerName: "", //介绍人
        ChannelName: "", //渠道
      },
      searchSaleArrearData: {
        EntityID: null,
        QueryDate: [dayjs(new Date()).format("YYYY-MM-DD"), dayjs(new Date()).format("YYYY-MM-DD")],
        RegisterQueryDate: null,
        GoodsTypeName: "",
        GoodName: "",
        IsLargess: null,
        CustomerName: null,
        CustomerLevelID: "",
        CustomerSourceID: "",
        CategoryID: "", //分类编号
        Channel: "", //订单渠道：PC: PC，小程序：Miniprogram，商城：MicroMall
        IntroducerName: "", //介绍人
        ChannelName: "", //渠道
      },
      searchSaleRefundData: {
        EntityID: null,
        QueryDate: [dayjs(new Date()).format("YYYY-MM-DD"), dayjs(new Date()).format("YYYY-MM-DD")],
        RegisterQueryDate: null,
        GoodsTypeName: "",
        GoodName: "",
        IsLargess: null,
        CustomerName: null,
        CustomerLevelID: "",
        CustomerSourceID: "",
        CategoryID: "", //分类编号
        Channel: "", //订单渠道：PC: PC，小程序：Miniprogram，商城：MicroMall
        IntroducerName: "", //介绍人
        ChannelName: "", //渠道
      },
      storeEntityList: [], //门店列表
      saleDetailList: [], //销售明细
      saleDetailSum: {},
      saleArrearDetailList: [], //销售不欠款明细
      saleArrearDetailSum: {},
      saleRefundDetailList: [], //销售不欠款明细
      saleRefundDetailSum: {},
      //需要给分页组件传的信息
      salePaginations: {
        page: 1, // 当前位于哪页
        total: 0, // 总数
        page_size: 10, // 1页显示多少条
        layout: "total, prev, pager, next,jumper", // 翻页属性
      },
      saleArrearPaginations: {
        page: 1, // 当前位于哪页
        total: 0, // 总数
        page_size: 10, // 1页显示多少条
        layout: "total, prev, pager, next,jumper", // 翻页属性
      },
      saleRefundPaginations: {
        page: 1, // 当前位于哪页
        total: 0, // 总数
        page_size: 10, // 1页显示多少条
        layout: "total, prev, pager, next,jumper", // 翻页属性
      },
      ArrearSaleDetailExport: false,
      RefundSaleDetailExport: false,
      SaleDetailExport: false,
      SaleDetailExportDisPlayPhone: false,
      ArrearSaleDetailExportDisPlayPhone: false,
      RefundSaleExportDisPlayPhone:false,
      BelongEntityList: [],

      pickerOptions: {
        shortcuts: [
          {
            text: "今天",
            onClick: (picker) => {
              let end = new Date();
              let start = new Date();
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "本周",
            onClick: (picker) => {
              let end = new Date();
              let weekDay = dayjs(end).isoWeekday();
              let start = dayjs(end)
                .subtract(weekDay - 1, "day")
                .toDate();
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "本月",
            onClick: (picker) => {
              let end = new Date();
              let start = dayjs(dayjs(end).format("YYYY-MM") + "01").toDate();
              picker.$emit("pick", [start, end]);
            },
          },
        ],
      },

      saleArrearpickerOptions: {
        shortcuts: [
          {
            text: "今天",
            onClick: (picker) => {
              let end = new Date();
              let start = new Date();
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "本周",
            onClick: (picker) => {
              let end = new Date();
              let weekDay = dayjs(end).isoWeekday();
              let start = dayjs(end)
                .subtract(weekDay - 1, "day")
                .toDate();
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "本月",
            onClick: (picker) => {
              let end = new Date();
              let start = dayjs(dayjs(end).format("YYYY-MM") + "01").toDate();
              picker.$emit("pick", [start, end]);
            },
          },
        ],
      },

      saleRefundpickerOptions: {
        shortcuts: [
          {
            text: "今天",
            onClick: (picker) => {
              let end = new Date();
              let start = new Date();
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "本周",
            onClick: (picker) => {
              let end = new Date();
              let weekDay = dayjs(end).isoWeekday();
              let start = dayjs(end)
                .subtract(weekDay - 1, "day")
                .toDate();
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "本月",
            onClick: (picker) => {
              let end = new Date();
              let start = dayjs(dayjs(end).format("YYYY-MM") + "01").toDate();
              picker.$emit("pick", [start, end]);
            },
          },
        ],
      },

      /**  注册日期时间快捷选择  */
      pickerOptionsCustomer: {
        shortcuts: [
          {
            text: "今天",
            onClick: (picker) => {
              let end = new Date();
              let start = new Date();
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "近七天",
            onClick: (picker) => {
              let end = new Date();
              let start = dayjs(end).subtract(7, "day").toDate();
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "近一个月",
            onClick: (picker) => {
              let end = new Date();
              let start = dayjs(end).subtract(1, "month").toDate();
              picker.$emit("pick", [start, end]);
            },
          },
        ],
      },
      customerLevel: [],
      customerSource: [],
      categoryList: [],
      cascaderProps: {
        checkStrictly: true,
        label: "Name",
        value: "ID",
        children: "Child",
      },
    };
  },
  
  beforeRouteEnter(to, from, next) {
      next((vm) => {
        vm.ArrearSaleDetailExport = permission.permission(to.meta.Permission, "Report-Entity-ArrearSaleDetail-Export");
        vm.RefundSaleDetailExport = permission.permission(to.meta.Permission, "Report-Entity-RefundSaleDetail-Export");
        vm.SaleDetailExport = permission.permission(to.meta.Permission, "Report-Entity-SaleDetail-Export");


        vm.SaleDetailExportDisPlayPhone = permission.permission(to.meta.Permission, "Report-Trade-SaleDetail-ExportDisPlayPhone");
        vm.ArrearSaleDetailExportDisPlayPhone = permission.permission(to.meta.Permission, "Report-Trade-ArrearSaleDetail-ExportDisPlayPhone");
        vm.RefundSaleExportDisPlayPhone = permission.permission(to.meta.Permission, "Report-Trade-RefundSaleDetail-ExportDisPlayPhone");
      });
    },
  methods: {
    //获得当前用户下的权限门店
    getstoreEntityList() {
      var that = this;
      that.loading = true;
      EntityAPI.getStoreEntityList()
        .then((res) => {
          if (res.StateCode == 200) {
            that.storeEntityList = res.Data;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },
    handleSaleSearch() {
      var that = this;
      that.salePaginations.page = 1;
      that.SaleSearch();
    },
    handleSaleDetailPageChange(page) {
      this.salePaginations.page = page;
      this.SaleSearch();
    },
    // 销售搜索
    SaleSearch() {
      var that = this;
      if (that.searchSaleData.QueryDate != null) {
        if (dayjs(that.searchSaleData.QueryDate[0]).add(366, "day").valueOf() < dayjs(that.searchSaleData.QueryDate[1]).valueOf()) {
          that.$message.error("时间筛选范围不能超366天");
          return;
        }
        let CategoryID = "";
        if (that.searchSaleData.CategoryID.length == 2) {
          CategoryID = that.searchSaleData.CategoryID[1];
        }

        if (that.searchSaleData.CategoryID.length == 3) {
          CategoryID = that.searchSaleData.CategoryID[2];
        }
        var params = {
          EntityID: that.searchSaleData.EntityID,
          StartDate: that.searchSaleData.QueryDate ? that.searchSaleData.QueryDate[0] : null,
          EndDate: that.searchSaleData.QueryDate ? that.searchSaleData.QueryDate[1] : null,
          CreateStartDate: that.searchSaleData.RegisterQueryDate ? that.searchSaleData.RegisterQueryDate[0] : null,
          CreateEndDate: that.searchSaleData.RegisterQueryDate ? that.searchSaleData.RegisterQueryDate[1] : null,

          GoodName: that.searchSaleData.GoodName.trim(),
          IsLargess: that.searchSaleData.IsLargess,
          PageNum: that.salePaginations.page,
          CustomerName: that.searchSaleData.CustomerName,
          BelongEntityID: that.searchSaleData.BelongEntityID,
          CustomerLevelID: that.searchSaleData.CustomerLevelID, //顾客等级
          CustomerSourceID: that.searchSaleData.CustomerSourceID, //顾客来源
          GoodsTypeName: that.searchSaleData.CategoryID.length ? that.getGoodsTypeID(that.searchSaleData.CategoryID[0]) : "",
          CategoryID: CategoryID,

          Channel: that.searchSaleData.Channel, //订单渠道：PC: PC，小程序：Miniprogram，商城：MicroMall
          IntroducerName: that.searchSaleData.IntroducerName, //介绍人
          ChannelName: that.searchSaleData.ChannelName, //渠道
        };
        that.loading = true;
        API.getSaleDetailStatement(params)
          .then((res) => {
            if (res.StateCode == 200) {
              that.saleDetailSum = res.Data.entitySaleDetailSumStatementForm;
              that.saleDetailList = res.Data.entitySaleDetailStatementForms.List;
              that.salePaginations.total = res.Data.entitySaleDetailStatementForms.Total;
              that.salePaginations.page_size = res.Data.entitySaleDetailStatementForms.PageSize;
            } else {
              that.$message.error({
                message: res.Message,
                duration: 2000,
              });
            }
          })
          .finally(function () {
            that.loading = false;
          });
      } else {
        that.$message.error({
          message: "请选择查询时间",
          duration: 2000,
        });
      }
    },
    /**    */
    getGoodsTypeID(type) {
      switch (type) {
        case 10:
          return "产品";
        case 20:
          return "项目";
        case 30:
          return "通用次卡";
        case 40:
          return "时效卡";
        case 50:
          return "储值卡";
        case 60:
          return "套餐卡";
      }
    },
    getsaleDetailListSummaries({ columns }) {
      let that = this;
      const sums = [];
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = <span class="font_weight_600">合计</span>;
          return;
        }
        let filter_NumFormat = this.$options.filters["NumFormat"];

        switch (column.property) {
          case "ArrearAmount":
          case "LargessSavingCardDeductionAmount":
          case "PayAmount":
          case "PreferentialAmount":
          case "SavingCardDeductionAmount":
          case "TotalAmount":
          case "LargessAmount":
            {
              if (that.saleDetailSum) {
                let value = that.saleDetailSum[column.property];
                let val = that.saleDetailSum ? value : 0;
                sums[index] = <span class="font_weight_600">{filter_NumFormat(val)}</span>;
              }
            }
            break;

          default:
            sums[index] = <span class="font_weight_600"></span>;
            break;
        }
      });
      return sums;
    },
    handleSaleArrearSearch() {
      var that = this;
      that.saleArrearPaginations.page = 1;
      that.SaleArrearSearch();
    },
    handleSaleArrearDetailPageChange(page) {
      this.saleArrearPaginations.page = page;
      this.SaleArrearSearch();
    },
    // 销售欠款搜索
    SaleArrearSearch() {
      var that = this;
      if (that.searchSaleArrearData.QueryDate != null) {
        if (dayjs(that.searchSaleArrearData.QueryDate[0]).add(366, "day").valueOf() < dayjs(that.searchSaleArrearData.QueryDate[1]).valueOf()) {
          that.$message.error("时间筛选范围不能超366天");
          return;
        }
        let CategoryID = "";
        if (that.searchSaleArrearData.CategoryID.length == 2) {
          CategoryID = that.searchSaleArrearData.CategoryID[1];
        }

        if (that.searchSaleArrearData.CategoryID.length == 3) {
          CategoryID = that.searchSaleArrearData.CategoryID[2];
        }
        var params = {
          EntityID: that.searchSaleArrearData.EntityID,
          StartDate: that.searchSaleArrearData.QueryDate[0],
          EndDate: that.searchSaleArrearData.QueryDate[1],
          CreateStartDate: that.searchSaleArrearData.RegisterQueryDate ? that.searchSaleArrearData.RegisterQueryDate[0] : null,
          CreateEndDate: that.searchSaleArrearData.RegisterQueryDate ? that.searchSaleArrearData.RegisterQueryDate[1] : null,
          GoodName: that.searchSaleArrearData.GoodName.trim(),
          IsLargess: that.searchSaleArrearData.IsLargess,
          PageNum: that.saleArrearPaginations.page,
          CustomerName: that.searchSaleArrearData.CustomerName,
          BelongEntityID: that.searchSaleArrearData.BelongEntityID,
          CustomerLevelID: that.searchSaleArrearData.CustomerLevelID, //顾客等级
          CustomerSourceID: that.searchSaleArrearData.CustomerSourceID, //顾客来源
          GoodsTypeName: that.searchSaleArrearData.CategoryID.length ? that.getGoodsTypeID(that.searchSaleArrearData.CategoryID[0]) : "",
          CategoryID: CategoryID,

          Channel: that.searchSaleArrearData.Channel, //订单渠道：PC: PC，小程序：Miniprogram，商城：MicroMall
          IntroducerName: that.searchSaleArrearData.IntroducerName, //介绍人
          ChannelName: that.searchSaleArrearData.ChannelName, //渠道
        };
        that.loading = true;
        API.getSaleArrearDetailStatement(params)
          .then((res) => {
            if (res.StateCode == 200) {
              that.saleArrearDetailSum = res.Data.entitySaleArrearDetailSumStatementForm;
              that.saleArrearDetailList = res.Data.entitySaleArrearDetailStatementForm.List;
              that.saleArrearPaginations.total = res.Data.entitySaleArrearDetailStatementForm.Total;
              that.saleArrearPaginations.page_size = res.Data.entitySaleArrearDetailStatementForm.PageSize;
            } else {
              that.$message.error({
                message: res.Message,
                duration: 2000,
              });
            }
          })
          .finally(function () {
            that.loading = false;
          });
      } else {
        that.$message.error({
          message: "请选择查询时间",
          duration: 2000,
        });
      }
    },
    getsaleArrearDetailListSummaries({ columns }) {
      let that = this;
      const sums = [];
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = <span class="font_weight_600">合计</span>;
          return;
        }
        var filter_NumFormat = this.$options.filters["NumFormat"];

        switch (column.property) {
          case "PayAmount":
          case "SavingCardDeductionAmount":
          case "LargessSavingCardDeductionAmount":
            {
              if (that.saleArrearDetailSum) {
                let value = that.saleArrearDetailSum[column.property];
                let val = that.saleArrearDetailSum ? value : 0;
                sums[index] = <span class="font_weight_600">{filter_NumFormat(val)}</span>;
              }
            }

            break;
          default:
            sums[index] = <span class="font_weight_600"></span>;
            break;
        }
      });
      return sums;
    },
    handleSaleRefundSearch() {
      var that = this;
      that.saleRefundPaginations.page = 1;
      that.SaleRefundSearch();
    },
    handleSaleRefundDetailPageChange(page) {
      this.saleRefundPaginations.page = page;
      this.SaleRefundSearch();
    },
    // 销售退款搜索
    SaleRefundSearch() {
      var that = this;
      if (that.searchSaleRefundData.QueryDate != null) {
        if (dayjs(that.searchSaleRefundData.QueryDate[0]).add(366, "day").valueOf() < dayjs(that.searchSaleRefundData.QueryDate[1]).valueOf()) {
          that.$message.error("时间筛选范围不能超366天");
          return;
        }
        let CategoryID = "";
        if (that.searchSaleRefundData.CategoryID.length == 2) {
          CategoryID = that.searchSaleRefundData.CategoryID[1];
        }

        if (that.searchSaleRefundData.CategoryID.length == 3) {
          CategoryID = that.searchSaleRefundData.CategoryID[2];
        }
        var params = {
          EntityID: that.searchSaleRefundData.EntityID,
          StartDate: that.searchSaleRefundData.QueryDate[0],
          EndDate: that.searchSaleRefundData.QueryDate[1],
          CreateStartDate: that.searchSaleRefundData.RegisterQueryDate ? that.searchSaleRefundData.RegisterQueryDate[0] : null,
          CreateEndDate: that.searchSaleRefundData.RegisterQueryDate ? that.searchSaleRefundData.RegisterQueryDate[1] : null,
          GoodName: that.searchSaleRefundData.GoodName.trim(),
          IsLargess: that.searchSaleRefundData.IsLargess,
          PageNum: that.saleRefundPaginations.page,
          CustomerName: that.searchSaleRefundData.CustomerName,
          BelongEntityID: that.searchSaleRefundData.BelongEntityID,
          CustomerLevelID: that.searchSaleRefundData.CustomerLevelID, //顾客等级
          CustomerSourceID: that.searchSaleRefundData.CustomerSourceID, //顾客来源
          GoodsTypeName: that.searchSaleRefundData.CategoryID.length ? that.getGoodsTypeID(that.searchSaleRefundData.CategoryID[0]) : "",
          CategoryID: CategoryID,

          Channel: that.searchSaleRefundData.Channel, //订单渠道：PC: PC，小程序：Miniprogram，商城：MicroMall
          IntroducerName: that.searchSaleRefundData.IntroducerName, //介绍人
          ChannelName: that.searchSaleRefundData.ChannelName, //渠道
        };
        that.loading = true;
        API.getSaleRefundDetailStatement(params)
          .then((res) => {
            if (res.StateCode == 200) {
              that.saleRefundDetailList = res.List;
              that.saleRefundPaginations.total = res.Total;
              that.saleRefundPaginations.page_size = res.PageSize;

              that.saleRefundDetailSum = res.Data.entitySaleRefundDetailSumStatementForm;
              that.saleRefundDetailList = res.Data.entitySaleRefundDetailStatementForms.List;
              that.saleRefundPaginations.total = res.Data.entitySaleRefundDetailStatementForms.Total;
              that.saleRefundPaginations.page_size = res.Data.entitySaleRefundDetailStatementForms.PageSize;
            } else {
              that.$message.error({
                message: res.Message,
                duration: 2000,
              });
            }
          })
          .finally(function () {
            that.loading = false;
          });
      } else {
        that.$message.error({
          message: "请选择查询时间",
          duration: 2000,
        });
      }
    },
    getsaleRefundDetailListSummaries({ columns }) {
      let that = this;
      const sums = [];
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = <span class="font_weight_600">合计</span>;
          return;
        }
        var filter_NumFormat = this.$options.filters["NumFormat"];
        switch (column.property) {
          case "OriginAmount":
          case "TotalAmount":
          case "PayAmount":
          case "SavingCardDeductionAmount":
          case "LargessSavingCardDeductionAmount":
          case "LargessAmount":
            {
              if (that.saleRefundDetailSum) {
                let value = that.saleRefundDetailSum[column.property];
                let val = that.saleRefundDetailSum ? value : 0;
                sums[index] = <span class="font_weight_600">{filter_NumFormat(val)}</span>;
              }
            }
            break;
          default:
            sums[index] = <span class="font_weight_600"></span>;
            break;
        }
      });
      return sums;
    },

    /** 数据导出 销售 */
    downloadSaleExcel_command (type) {
      if (type == "excelNoDisPlayPhone") {
        this.downloadSaleExcel();
      }
      if (type == "excelDisPlayPhone") {
        this.entitySaleDetailStatement_excelDisPlayPhone();
      }
      
    },
    downloadSaleExcel() {
      var that = this;
      if (that.searchSaleData.QueryDate != null) {
        if (dayjs(that.searchSaleData.QueryDate[0]).add(366, "day").valueOf() < dayjs(that.searchSaleData.QueryDate[1]).valueOf()) {
          that.$message.error("时间筛选范围不能超366天");
          return;
        }

        let CategoryID = "";
        if (that.searchSaleData.CategoryID.length == 2) {
          CategoryID = that.searchSaleData.CategoryID[1];
        }

        if (that.searchSaleData.CategoryID.length == 3) {
          CategoryID = that.searchSaleData.CategoryID[2];
        }
        let params = {
          EntityID: that.searchSaleData.EntityID,
          StartDate: that.searchSaleData.QueryDate[0],
          EndDate: that.searchSaleData.QueryDate[1],
          CreateStartDate: that.searchSaleData.RegisterQueryDate ? that.searchSaleData.RegisterQueryDate[0] : null,
          CreateEndDate: that.searchSaleData.RegisterQueryDate ? that.searchSaleData.RegisterQueryDate[1] : null,
          GoodName: that.searchSaleData.GoodName.trim(),
          IsLargess: that.searchSaleData.IsLargess,
          BelongEntityID: that.searchSaleData.BelongEntityID,
          CustomerLevelID: that.searchSaleData.CustomerLevelID, //顾客等级
          CustomerSourceID: that.searchSaleData.CustomerSourceID, //顾客来源
          GoodsTypeName: that.searchSaleData.CategoryID.length ? that.getGoodsTypeID(that.searchSaleData.CategoryID[0]) : "",
          CategoryID: CategoryID,
          CustomerName: that.searchSaleData.CustomerName,
          Channel: that.searchSaleData.Channel, //订单渠道：PC: PC，小程序：Miniprogram，商城：MicroMall
          IntroducerName: that.searchSaleData.IntroducerName, //介绍人
          ChannelName: that.searchSaleData.ChannelName, //渠道
        };
        that.downloadLoading = true;
        API.exportSaleDetailStatement(params)
          .then((res) => {
            this.$message.success({
              message: "正在导出",
              duration: "4000",
            });
            const link = document.createElement("a");
            let blob = new Blob([res], { type: "application/octet-stream" });
            link.style.display = "none";
            link.href = URL.createObjectURL(blob);
            link.download = "销售明细.xlsx"; //下载的文件名
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
          })
          .finally(function () {
            that.downloadLoading = false;
          });
      } else {
        that.$message.error({
          message: "请选择查询时间",
          duration: 2000,
        });
      }
    },
    entitySaleDetailStatement_excelDisPlayPhone() {
      var that = this;
      if (that.searchSaleData.QueryDate != null) {
        if (dayjs(that.searchSaleData.QueryDate[0]).add(366, "day").valueOf() < dayjs(that.searchSaleData.QueryDate[1]).valueOf()) {
          that.$message.error("时间筛选范围不能超366天");
          return;
        }

        let CategoryID = "";
        if (that.searchSaleData.CategoryID.length == 2) {
          CategoryID = that.searchSaleData.CategoryID[1];
        }

        if (that.searchSaleData.CategoryID.length == 3) {
          CategoryID = that.searchSaleData.CategoryID[2];
        }
        let params = {
          EntityID: that.searchSaleData.EntityID,
          StartDate: that.searchSaleData.QueryDate[0],
          EndDate: that.searchSaleData.QueryDate[1],
          CreateStartDate: that.searchSaleData.RegisterQueryDate ? that.searchSaleData.RegisterQueryDate[0] : null,
          CreateEndDate: that.searchSaleData.RegisterQueryDate ? that.searchSaleData.RegisterQueryDate[1] : null,
          GoodName: that.searchSaleData.GoodName.trim(),
          IsLargess: that.searchSaleData.IsLargess,
          BelongEntityID: that.searchSaleData.BelongEntityID,
          CustomerLevelID: that.searchSaleData.CustomerLevelID, //顾客等级
          CustomerSourceID: that.searchSaleData.CustomerSourceID, //顾客来源
          GoodsTypeName: that.searchSaleData.CategoryID.length ? that.getGoodsTypeID(that.searchSaleData.CategoryID[0]) : "",
          CategoryID: CategoryID,
          CustomerName: that.searchSaleData.CustomerName,
          Channel: that.searchSaleData.Channel, //订单渠道：PC: PC，小程序：Miniprogram，商城：MicroMall
          IntroducerName: that.searchSaleData.IntroducerName, //介绍人
          ChannelName: that.searchSaleData.ChannelName, //渠道
        };
        that.downloadLoading = true;
        API.entitySaleDetailStatement_excelDisPlayPhone(params)
          .then((res) => {
            this.$message.success({
              message: "正在导出",
              duration: "4000",
            });
            const link = document.createElement("a");
            let blob = new Blob([res], { type: "application/octet-stream" });
            link.style.display = "none";
            link.href = URL.createObjectURL(blob);
            link.download = "销售明细(显示手机号).xlsx"; //下载的文件名
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
          })
          .finally(function () {
            that.downloadLoading = false;
          });
      } else {
        that.$message.error({
          message: "请选择查询时间",
          duration: 2000,
        });
      }
    },
    /** 数据导出 欠尾款 */
    downloadSaleArrearExcel_command (type) {
      
      if (type == "excelNoDisPlayPhone") {
        this.downloadSaleArrearExcel();
      }
      if (type == "excelDisPlayPhone") {
        this.entitySaleArrearDetailStatement_excelDisPlayPhone();
      }
    },
    
    downloadSaleArrearExcel() {
      var that = this;
      if (that.searchSaleArrearData.QueryDate != null) {
        if (dayjs(that.searchSaleArrearData.QueryDate[0]).add(366, "day").valueOf() < dayjs(that.searchSaleArrearData.QueryDate[1]).valueOf()) {
          that.$message.error("时间筛选范围不能超366天");
          return;
        }
        let CategoryID = "";
        if (that.searchSaleArrearData.CategoryID.length == 2) {
          CategoryID = that.searchSaleArrearData.CategoryID[1];
        }

        if (that.searchSaleArrearData.CategoryID.length == 3) {
          CategoryID = that.searchSaleArrearData.CategoryID[2];
        }
        let params = {
          EntityID: that.searchSaleArrearData.EntityID,
          StartDate: that.searchSaleArrearData.QueryDate[0],
          EndDate: that.searchSaleArrearData.QueryDate[1],
          CreateStartDate: that.searchSaleArrearData.RegisterQueryDate ? that.searchSaleArrearData.RegisterQueryDate[0] : null,
          CreateEndDate: that.searchSaleArrearData.RegisterQueryDate ? that.searchSaleArrearData.RegisterQueryDate[1] : null,
          GoodName: that.searchSaleArrearData.GoodName.trim(),
          IsLargess: that.searchSaleArrearData.IsLargess,
          BelongEntityID: that.searchSaleArrearData.BelongEntityID,
          CustomerLevelID: that.searchSaleArrearData.CustomerLevelID, //顾客等级
          CustomerSourceID: that.searchSaleArrearData.CustomerSourceID, //顾客来源
          GoodsTypeName: that.searchSaleArrearData.CategoryID.length ? that.getGoodsTypeID(that.searchSaleArrearData.CategoryID[0]) : "",
          CategoryID: CategoryID,
          CustomerName: that.searchSaleArrearData.CustomerName,
          Channel: that.searchSaleArrearData.Channel, //订单渠道：PC: PC，小程序：Miniprogram，商城：MicroMall
          IntroducerName: that.searchSaleArrearData.IntroducerName, //介绍人
          ChannelName: that.searchSaleArrearData.ChannelName, //渠道
        };
        that.downloadLoading = true;
        API.exportSaleArrearDetailStatement(params)
          .then((res) => {
            this.$message.success({
              message: "正在导出",
              duration: "4000",
            });
            const link = document.createElement("a");
            let blob = new Blob([res], { type: "application/octet-stream" });
            link.style.display = "none";
            link.href = URL.createObjectURL(blob);
            link.download = "补尾款明细.xlsx"; //下载的文件名
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
          })
          .finally(function () {
            that.downloadLoading = false;
          });
      } else {
        that.$message.error({
          message: "请选择查询时间",
          duration: 2000,
        });
      }
    },
       
    entitySaleArrearDetailStatement_excelDisPlayPhone() {
      var that = this;
      if (that.searchSaleArrearData.QueryDate != null) {
        if (dayjs(that.searchSaleArrearData.QueryDate[0]).add(366, "day").valueOf() < dayjs(that.searchSaleArrearData.QueryDate[1]).valueOf()) {
          that.$message.error("时间筛选范围不能超366天");
          return;
        }
        let CategoryID = "";
        if (that.searchSaleArrearData.CategoryID.length == 2) {
          CategoryID = that.searchSaleArrearData.CategoryID[1];
        }

        if (that.searchSaleArrearData.CategoryID.length == 3) {
          CategoryID = that.searchSaleArrearData.CategoryID[2];
        }
        let params = {
          EntityID: that.searchSaleArrearData.EntityID,
          StartDate: that.searchSaleArrearData.QueryDate[0],
          EndDate: that.searchSaleArrearData.QueryDate[1],
          CreateStartDate: that.searchSaleArrearData.RegisterQueryDate ? that.searchSaleArrearData.RegisterQueryDate[0] : null,
          CreateEndDate: that.searchSaleArrearData.RegisterQueryDate ? that.searchSaleArrearData.RegisterQueryDate[1] : null,
          GoodName: that.searchSaleArrearData.GoodName.trim(),
          IsLargess: that.searchSaleArrearData.IsLargess,
          BelongEntityID: that.searchSaleArrearData.BelongEntityID,
          CustomerLevelID: that.searchSaleArrearData.CustomerLevelID, //顾客等级
          CustomerSourceID: that.searchSaleArrearData.CustomerSourceID, //顾客来源
          GoodsTypeName: that.searchSaleArrearData.CategoryID.length ? that.getGoodsTypeID(that.searchSaleArrearData.CategoryID[0]) : "",
          CategoryID: CategoryID,
          CustomerName: that.searchSaleArrearData.CustomerName,
          Channel: that.searchSaleArrearData.Channel, //订单渠道：PC: PC，小程序：Miniprogram，商城：MicroMall
          IntroducerName: that.searchSaleArrearData.IntroducerName, //介绍人
          ChannelName: that.searchSaleArrearData.ChannelName, //渠道
        };
        that.downloadLoading = true;
        API.entitySaleArrearDetailStatement_excelDisPlayPhone(params)
          .then((res) => {
            this.$message.success({
              message: "正在导出",
              duration: "4000",
            });
            const link = document.createElement("a");
            let blob = new Blob([res], { type: "application/octet-stream" });
            link.style.display = "none";
            link.href = URL.createObjectURL(blob);
            link.download = "补尾款明细(显示手机号).xlsx"; //下载的文件名
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
          })
          .finally(function () {
            that.downloadLoading = false;
          });
      } else {
        that.$message.error({
          message: "请选择查询时间",
          duration: 2000,
        });
      }
    },
    /** 数据导出 退款 */
    downloadSaleRefundExcel_command (type) {
      if (type == "downloadSaleRefundExcel") {
        this.downloadSaleArrearExcel();
      }
      if (type == "excelDisPlayPhone") {
        this.entitySaleArrearDetailStatement_excelDisPlayPhone();
      }
    },
    
    downloadSaleRefundExcel() {
      var that = this;
      if (that.searchSaleRefundData.QueryDate != null) {
        if (dayjs(that.searchSaleRefundData.QueryDate[0]).add(366, "day").valueOf() < dayjs(that.searchSaleRefundData.QueryDate[1]).valueOf()) {
          that.$message.error("时间筛选范围不能超366天");
          return;
        }
        let CategoryID = "";
        if (that.searchSaleRefundData.CategoryID.length == 2) {
          CategoryID = that.searchSaleRefundData.CategoryID[1];
        }
        if (that.searchSaleRefundData.CategoryID.length == 3) {
          CategoryID = that.searchSaleRefundData.CategoryID[2];
        }
        let params = {
          EntityID: that.searchSaleRefundData.EntityID,
          StartDate: that.searchSaleRefundData.QueryDate[0],
          EndDate: that.searchSaleRefundData.QueryDate[1],
          CreateStartDate: that.searchSaleRefundData.RegisterQueryDate ? that.searchSaleRefundData.RegisterQueryDate[0] : null,
          CreateEndDate: that.searchSaleRefundData.RegisterQueryDate ? that.searchSaleRefundData.RegisterQueryDate[1] : null,
          GoodName: that.searchSaleRefundData.GoodName.trim(),
          IsLargess: that.searchSaleRefundData.IsLargess,
          BelongEntityID: that.searchSaleRefundData.BelongEntityID,
          CustomerLevelID: that.searchSaleRefundData.CustomerLevelID, //顾客等级
          CustomerSourceID: that.searchSaleRefundData.CustomerSourceID, //顾客来源
          GoodsTypeName: that.searchSaleRefundData.CategoryID.length ? that.getGoodsTypeID(that.searchSaleRefundData.CategoryID[0]) : "",
          CategoryID: CategoryID,
          CustomerName: that.searchSaleRefundData.CustomerName,

          Channel: that.searchSaleRefundData.Channel, //订单渠道：PC: PC，小程序：Miniprogram，商城：MicroMall
          IntroducerName: that.searchSaleRefundData.IntroducerName, //介绍人
          ChannelName: that.searchSaleRefundData.ChannelName, //渠道
        };
        that.downloadLoading = true;
        API.exportSaleRefundDetailStatement(params)
          .then((res) => {
            this.$message.success({
              message: "正在导出",
              duration: "4000",
            });
            const link = document.createElement("a");
            let blob = new Blob([res], { type: "application/octet-stream" });
            link.style.display = "none";
            link.href = URL.createObjectURL(blob);
            link.download = "退款明细.xlsx"; //下载的文件名
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
          })
          .finally(function () {
            that.downloadLoading = false;
          });
      } else {
        that.$message.error({
          message: "请选择查询时间",
          duration: 2000,
        });
      }
    },
    
    entitySaleRefundDetailStatement_excelDisPlayPhone() {
      var that = this;
      if (that.searchSaleRefundData.QueryDate != null) {
        if (dayjs(that.searchSaleRefundData.QueryDate[0]).add(366, "day").valueOf() < dayjs(that.searchSaleRefundData.QueryDate[1]).valueOf()) {
          that.$message.error("时间筛选范围不能超366天");
          return;
        }
        let CategoryID = "";
        if (that.searchSaleRefundData.CategoryID.length == 2) {
          CategoryID = that.searchSaleRefundData.CategoryID[1];
        }
        if (that.searchSaleRefundData.CategoryID.length == 3) {
          CategoryID = that.searchSaleRefundData.CategoryID[2];
        }
        let params = {
          EntityID: that.searchSaleRefundData.EntityID,
          StartDate: that.searchSaleRefundData.QueryDate[0],
          EndDate: that.searchSaleRefundData.QueryDate[1],
          CreateStartDate: that.searchSaleRefundData.RegisterQueryDate ? that.searchSaleRefundData.RegisterQueryDate[0] : null,
          CreateEndDate: that.searchSaleRefundData.RegisterQueryDate ? that.searchSaleRefundData.RegisterQueryDate[1] : null,
          GoodName: that.searchSaleRefundData.GoodName.trim(),
          IsLargess: that.searchSaleRefundData.IsLargess,
          BelongEntityID: that.searchSaleRefundData.BelongEntityID,
          CustomerLevelID: that.searchSaleRefundData.CustomerLevelID, //顾客等级
          CustomerSourceID: that.searchSaleRefundData.CustomerSourceID, //顾客来源
          GoodsTypeName: that.searchSaleRefundData.CategoryID.length ? that.getGoodsTypeID(that.searchSaleRefundData.CategoryID[0]) : "",
          CategoryID: CategoryID,
          CustomerName: that.searchSaleRefundData.CustomerName,

          Channel: that.searchSaleRefundData.Channel, //订单渠道：PC: PC，小程序：Miniprogram，商城：MicroMall
          IntroducerName: that.searchSaleRefundData.IntroducerName, //介绍人
          ChannelName: that.searchSaleRefundData.ChannelName, //渠道
        };
        that.downloadLoading = true;
        API.entitySaleRefundDetailStatement_excelDisPlayPhone(params)
          .then((res) => {
            this.$message.success({
              message: "正在导出",
              duration: "4000",
            });
            const link = document.createElement("a");
            let blob = new Blob([res], { type: "application/octet-stream" });
            link.style.display = "none";
            link.href = URL.createObjectURL(blob);
            link.download = "退款明细(显示手机号).xlsx"; //下载的文件名
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
          })
          .finally(function () {
            that.downloadLoading = false;
          });
      } else {
        that.$message.error({
          message: "请选择查询时间",
          duration: 2000,
        });
      }
    },
    /**    */
    async getAllEntity() {
      let that = this;
      let res = await API.allEntity();
      if (res.StateCode == 200) {
        that.BelongEntityList = res.Data;
      } else {
        that.$message.error(res.Message);
      }
    },
    /* 顾客等级 */
    CustomerLevelData() {
      var that = this;
      var params = {
        Name: "",
        Active: true,
      };
      APICustomerLevel.getCustomerLevel(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.customerLevel = res.Data;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },

    /* 顾客来源 */
    CustomerSourceData: function () {
      var that = this;
      var params = {
        Name: "",
        Active: true,
      };
      APICustomerSource.getCustomerSource(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.customerSource = res.Data;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },
    /**  分类  */
    async entitySaleGoodsDetailStatement_category() {
      let that = this;
      let res = await APICategory.entitySaleGoodsDetailStatement_category();
      if (res.StateCode == 200) {
        that.categoryList = res.Data;
      } else {
        that.$message.error(res.Message);
      }
    },
  },
  mounted() {
    var that = this;
    that.ArrearSaleDetailExport = permission.permission(that.$route.meta.Permission, "Report-Trade-ArrearSaleDetail-Export");
    that.RefundSaleDetailExport = permission.permission(that.$route.meta.Permission, "Report-Trade-RefundSaleDetail-Export");
    that.SaleDetailExport = permission.permission(that.$route.meta.Permission, "Report-Trade-SaleDetail-Export");

    that.SaleDetailExportDisPlayPhone = permission.permission(that.$route.meta.Permission, "Report-Trade-SaleDetail-ExportDisPlayPhone");
    that.ArrearSaleDetailExportDisPlayPhone = permission.permission(that.$route.meta.Permission, "Report-Trade-ArrearSaleDetail-ExportDisPlayPhone");
    that.RefundSaleExportDisPlayPhone = permission.permission(that.$route.meta.Permission, "Report-Trade-RefundSaleDetail-ExportDisPlayPhone");

    that.getstoreEntityList();
    that.handleSaleSearch();
    that.handleSaleArrearSearch();
    that.handleSaleRefundSearch();
    that.getAllEntity();
    that.CustomerLevelData();
    that.CustomerSourceData();
    that.entitySaleGoodsDetailStatement_category();
  },
};
</script>

<style lang="scss">
.ReportEntitySaleDetail {
  .el-tabs--border-card {
    border: 0px !important;
    box-shadow: 0 0px 0px 0 rgba(0, 0, 0, 0), 0 0 0px 0 rgba(0, 0, 0, 0);
  }

  .el-form-item--mini.el-form-item,
  .el-form-item--small.el-form-item {
    margin-bottom: 15px;
  }
}
</style>
