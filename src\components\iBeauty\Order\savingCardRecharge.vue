<template>
  <div class="savingCardRecharge">
    <el-row class="savingCardRecharge_content">
      <el-col :span="9" class="project_left">
        <div class="pad_15">
          <el-input prefix-icon="el-icon-search" placeholder="请输入商品名称、别名关键字，按回车搜索" v-model="searchName" size="small" clearable @keyup.enter.native="getRechargeSavingCardAccount" @clear="getRechargeSavingCardAccount"> </el-input>
        </div>

        <el-scrollbar class="el-scrollbar_height el_scrollbar_project">
          <el-card class="marbm_10 marlt_10 marrt_10 cursor_pointer" :class="index == 0 ? 'martp_10' : ''" :body-style="{ padding: '0px' }" shadow="hover" v-for="(item, index) in savingCards" :key="index + 'cs'" @click.native="savingCardRechargeAccountClick(item)">
            <div slot="header" class="font_14 color_333">
              <span>{{ item.Name }}</span>
              <span v-if="item.Alias">({{ item.Alias }})</span>
              <el-tag v-if="item.IsLargess" size="mini" class="marlt_5" type="danger">赠</el-tag>
            </div>
            <el-row class="border_bottom">
              <el-col :span="8" class="border_right">
                <div class="goods-item">
                  <span class="goods-lable">有效余额：</span>
                  <span>¥ {{ item.ValidBalance | toFixed | NumFormat }}</span>
                  <el-popover class="marlt_5" placement="top-start" width="240" trigger="hover">
                    <p>1.有效金额= 剩余数量-退款中金额-欠款金额；</p>
                    <p>2.注意：如果有赠额的情况，赠额可用金额也跟退款金额和欠款金额有关；</p>
                    <p>3.比如：充值100送100，欠款50，则可用本金为50，可用赠额为50，可用余额为100。</p>
                    <el-button type="text" style="color: #dcdfe6" class="font_12 el-popover-botton-tip" icon="el-icon-info" slot="reference"></el-button>
                  </el-popover>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="goods-item">
                  <span class="goods-lable">剩余金额：</span>
                  <span>¥ {{ item.TotalBalance | toFixed | NumFormat }}</span>
                  <el-popover class="marlt_5" placement="top-start" width="240" trigger="hover">
                    <p>剩余金额= 剩余本金+剩余赠额</p>
                    <el-button type="text" style="color: #dcdfe6" class="font_12 el-popover-botton-tip" icon="el-icon-info" slot="reference"></el-button>
                  </el-popover>
                </div>
              </el-col>
              <el-col :span="8" class="border_left">
                <div class="goods-item">
                  <span class="goods-lable">退款中金额：</span>
                  <span>¥ {{ item.RefundAmount | toFixed | NumFormat }}</span>
                </div>
              </el-col>
            </el-row>
            <el-col :span="8" class="border_right">
              <div class="goods-item">
                <span class="goods-lable">购买金额：</span>
                <span>¥ {{ item.TotalAmount | toFixed | NumFormat }}</span>
              </div>
            </el-col>
            <el-row class="border_bottom">
              <el-col :span="8">
                <div class="goods-item">
                  <span class="goods-lable">剩余本金：</span>
                  <span>¥ {{ item.Balance | toFixed | NumFormat }}</span>
                </div>
              </el-col>
              <el-col :span="8" class="border_left">
                <div class="goods-item">
                  <span class="goods-lable">剩余赠额：</span>
                  <span>¥ {{ item.LargessBalance | toFixed | NumFormat }}</span>
                </div>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="8" class="border_right">
                <div class="goods-item">
                  <span class="goods-lable">欠款金额：</span>
                  <span>¥ {{ item.ArrearAmount | toFixed | NumFormat }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="goods-item">
                  <span class="goods-lable">购买日期：</span>
                  <span>{{ item.BuyDate }}</span>
                </div>
              </el-col>
              <el-col :span="8" class="border_left">
                <div class="goods-item">
                  <span class="goods-lable">有效期：</span>
                  <span>{{ item.ValidDayName }}</span>
                </div>
              </el-col>
            </el-row>
          </el-card>
        </el-scrollbar>
      </el-col>
      <el-col :span="15" class="project_right position_relative">
        <el-container style="height: 100%">
          <el-main>
            <el-scrollbar class="el-scrollbar_height color_333">
              <!--储值卡-->
              <div>
                <el-row v-if="selectSavingCards.length > 0" class="row_header border_bottom font_14">
                  <el-col :span="24">
                    <el-col :span="11">储值卡</el-col>
                    <el-col :span="11">充值金额/赠额</el-col>
                    <el-col :span="2"></el-col>
                  </el-col>
                </el-row>
                <el-row v-for="(item, index) in selectSavingCards" :key="index">
                  <el-col :span="24" class="pad_10 border_bottom">
                    <el-col :span="11">
                      <div class="font_14 color_333">
                        {{ item.Name }}
                        <span v-if="item.Alias">({{ item.Alias }})</span>
                      </div>
                    </el-col>

                    <el-col :span="11">
                      <el-col :span="7">
                        <!-- @input="savingAmountChange(item)" -->
                        <el-input v-model="item.Amount" placeholder="金额" :min="0" class="savingCardAmount" type="number" v-input-fixed size="small"></el-input>
                      </el-col>
                      <el-col :span="7">
                        <el-input v-model="item.LargessAmount" placeholder="赠额" :min="0" class="savingCardLargessAmount" type="number" v-input-fixed size="small"></el-input>
                      </el-col>
                    </el-col>

                    <el-col :offset="1" :span="1" class="text_right">
                      <el-button type="danger" icon="el-icon-delete" circle size="mini" @click="removeSavingCardSelectItemClick(index)"></el-button>
                    </el-col>
                  </el-col>
                  <el-col :span="24" v-if="item.SavingCardRechargeRules.length > 0" class="dis_flex flex_dir_row flex_wrap">
                    <!-- never always -->
                    <el-card
                      shadow="never"
                      v-for="(RecharPrice, Rulesindex) in item.SavingCardRechargeRules"
                      :key="Rulesindex"
                      :body-style="{
                        padding: '0px',
                        padding: '10px',
                        'min-width': '100px',
                      }"
                      class="marrt_10 martp_10"
                      :class="RechargeRulesClass(item, Rulesindex)"
                      style="cursor: pointer"
                      @click.native="selectRechargeRules(item, RecharPrice, Rulesindex)"
                    >
                      <div class="font_15 font_weight_600">
                        {{ RecharPrice.Price | toFixed | NumFormat }}
                      </div>
                      <div class="font_13 color_666">赠额：{{ RecharPrice.LargessPrice | toFixed | NumFormat }}</div>
                    </el-card>
                  </el-col>
                  <el-col :span="24" class="pad_10 padbm_0 border_bottom">
                    <el-row class="cursor_pointer" @click.native="employeeHandleClick(item, index)" v-for="(handleItem, handleIndex) in item.HandleTypeList" :key="handleIndex">
                      <el-col :span="4">
                        <el-form :inline="true" size="mini" label-position="left">
                          <el-form-item style="margin-bottom: 10px" :label="`${handleItem.Name}：`"></el-form-item>
                        </el-form>
                      </el-col>
                      <el-col :span="20">
                        <el-form :inline="true" size="mini">
                          <el-form-item style="margin-bottom: 10px" v-for="(empItem, empIndex) in handleItem.Employee" :key="empIndex" :label="`${empItem.EmployeeName} `">
                            <el-input class="employee_num" v-model="empItem.Discount" size="mini" :min="0" :max="100" type="number" v-on:click.native.stop v-input-fixed @input="handlerPercentChange(handleItem.Employee, empItem)">
                              <template slot="append">%</template>
                            </el-input>
                            <i class="el-icon-error marlt_5 font_16" style="color: #909399; vertical-align: middle" v-on:click.stop="removeHandleClick(handleItem, empIndex)"></i>
                          </el-form-item>
                        </el-form>
                      </el-col>
                    </el-row>
                  </el-col>
                </el-row>
              </div>
            </el-scrollbar>
          </el-main>
          <transition>
            <div v-show="showRemark" class="orderInfoRemark" @click="showRemark = false">
              <div @click.stop class="infoRemarContent">
                <el-row @click.native="hiddenInfoRemarkClick" style="height: 40px" class="dis_flex flex_y_center">
                  <el-col :span="12">订单信息</el-col>
                  <el-col :span="12" class="text_right">
                    <el-button @click="hiddenInfoRemarkClick" type="text">收起</el-button>
                    <i class="el-icon-arrow-down color_main font_16 text-bold"></i>
                  </el-col>
                </el-row>
                <div class="back_f7f8fa" style="padding: 20px 16px 2px 16px">
                  <el-form label-width="80px" size="small">
                    <el-form-item v-show="selectSavingCards && selectSavingCards.length > 0" label="批量添加：">
                      <el-button @click="showSelectAllHandlerClick" icon="el-icon-plus">经手人</el-button>
                    </el-form-item>
                    <el-form-item label="订单备注：">
                      <el-input type="textarea" :rows="3" placeholder="请输入备注信息" v-model="Remark"></el-input>
                    </el-form-item>
                  </el-form>
                </div>
              </div>
            </div>
          </transition>
          <el-row @click.native="showRemarkClick" style="height: 40px" class="dis_flex flex_y_center pad_0_10 back_f8 font_13">
            <el-col :span="12" class="color_666">订单信息<span class="font_12 color_999">(已折叠)</span></el-col>
            <el-col :span="12" class="text_right">
              <el-button type="text">展开</el-button>
              <i class="el-icon-arrow-up color_main font_16 text-bold"></i>
            </el-col>
          </el-row>
          <el-footer class="border_top">
            <el-row class="color_333" type="flex" align="middle">
              <el-col :span="20">
                <span>
                  <span class="font_14 color_maroon">待收款金额：¥ {{ Number(TotalAmount).toFixed(2) | NumFormat }}</span>
                </span>
              </el-col>
              <!-- <el-col :span="14">
                <el-input type="textarea" :rows="1" placeholder="请输入备注信息" v-model="Remark"></el-input>
              </el-col> -->
              <el-col :span="2" class="text_right">
                <el-button v-if="SellPermission.isSaleBilling_SalePending || SellPermission.isSaleSettle" type="primary" size="small" @click="confrimRechargeTreatSettleBillClick">提交</el-button>
              </el-col>
              <el-col :span="2" class="text_right">
                <el-button v-if="SellPermission.isSaleSettle || SellPermission.isSaleBilling_SaleSettle" type="primary" size="small" @click="rechargeBillClick">结账</el-button>
              </el-col>
            </el-row>
          </el-footer>
        </el-container>
      </el-col>
    </el-row>

    <!--经手人-->
    <el-dialog title="经手人" :visible.sync="handelDialogVisible" width="800px" append-to-body>
      <div>
        <el-row class="padbm_10">
          <el-col :span="8">
            <el-input placeholder="请输入员工编号、姓名" prefix-icon="el-icon-search" v-model="handlerName" size="small" clearable></el-input>
          </el-col>
        </el-row>
        <el-tabs v-model="tabHandle">
          <el-tab-pane :label="handler.Name" :name="`${index}`" v-for="(handler, index) in savingCardHandlerList" :key="index">
            <el-row style="max-height: 300px; overflow-y: auto">
              <el-col :span="12" v-for="item in handler.Employee.filter((item) => !handlerName || item.EmployeeName.toLowerCase().includes(handlerName.toLowerCase()) || item.EmployeeID.toLowerCase().includes(handlerName.toLowerCase()))" :key="item.EmployeeID" class="marbm_10 dis_flex flex_y_center">
                <el-checkbox v-model="item.Checked" @change="handlerCheckedChange(handler.Employee, item)">
                  <span class="marrt_10">{{ item.EmployeeName }} [{{ item.EmployeeID }}]</span>
                </el-checkbox>
                <el-input placeholder v-model="item.Discount" style="width: 120px" type="number" size="mini" min="0" max="100" v-input-fixed @input="handlerPercentChange(handler.Employee, item, 'dialog')">
                  <template slot="append">%</template>
                </el-input>
              </el-col>
            </el-row>
          </el-tab-pane>
        </el-tabs>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="handelDialogVisible = false" size="small">取 消</el-button>
        <el-button type="primary" size="small" v-prevent-click @click="submitHandleClick">确 定</el-button>
      </div>
    </el-dialog>

    <!--结账-->
    <el-dialog title="收银台" :visible.sync="dialogBill" :close-on-click-modal="false" :close-on-press-escape="false" :show-close="false" width="900px" append-to-body>
      <div>
        <el-row>
          <el-col :span="8">
            <el-scrollbar class="el-scrollbar_height" style="height: 500px">
              <div class="marrt_10">
                <div class="dis_flex">
                  <span class="flex_box text_center font_16" style="line-height: 32px">{{ entityName }}</span>
                </div>
                <el-divider>
                  <span class="font_12 color_gray">订单信息</span>
                </el-divider>
                <div class="dis_flex">
                  <span class="flex_box font_12 color_gray text-left line_height_24">下单时间</span>
                  <span class="font_12 text_right line_height_24" style="flex: 3">{{ getBillDate() | dateFormat("YYYY-MM-DD HH:mm") }}</span>
                </div>
                <div class="dis_flex">
                  <span class="flex_box font_12 color_gray text-left line_height_24">会员姓名</span>
                  <span class="flex_box font_12 text_right line_height_24">{{ customerFullName }}</span>
                </div>
                <div class="dis_flex">
                  <span class="flex_box font_12 color_gray text-left line_height_24">会员手机号</span>
                  <span class="flex_box font_12 text_right line_height_24">{{ customerPhoneNumber | hidephone }}</span>
                </div>
                <div class="dis_flex">
                  <span class="flex_box font_12 color_gray text-left line_height_24">开单人</span>
                  <span class="flex_box font_12 text_right line_height_24">{{ userName }}</span>
                </div>
                <el-divider>
                  <span class="font_12 color_gray">消费明细</span>
                </el-divider>
                <template v-for="(item, index) in selectSavingCards">
                  <div :key="index">
                    <div class="dis_flex">
                      <span class="font_12 color_gray text-left line_height_24" style="flex: 2">{{ item.Name }}</span>
                      <span class="font_12 text_right line_height_24" style="flex: 1">￥{{ (item.Amount || 0) | toFixed | NumFormat }}</span>
                    </div>
                    <div class="dis_flex" v-if="item.LargessAmount > 0">
                      <span class="font_12 color_gray text-left line_height_24 marlt_10" style="flex: 2">充值赠送</span>
                      <span class="font_12 text_right line_height_24" style="flex: 1">￥{{ (item.LargessAmount || 0) | toFixed | NumFormat }}</span>
                    </div>
                    <!-- <div class="dis_flex">
                      <span class="font_12 color_gray text-left line_height_24 marlt_10" style="flex: 2">数量</span>
                      <span class="font_12 text_right line_height_24" style="flex: 1">{{ item.Quantity }}</span>
                    </div> -->
                    <div class="dis_flex">
                      <span class="font_12 color_gray text-left line_height_24 marlt_10" style="flex: 2">小计</span>
                      <span class="font_12 text_right line_height_24" style="flex: 1">￥{{ item.Amount | toFixed | NumFormat }}</span>
                    </div>
                  </div>
                </template>
                <el-divider class="sell-el-divider"></el-divider>
                <div class="dis_flex">
                  <span class="flex_box font_12 color_gray text-left line_height_24">合计</span>
                  <span class="flex_box font_12 text_right line_height_24">￥{{ parseFloat(TotalAmount).toFixed(2) | NumFormat }}</span>
                </div>
              </div>
            </el-scrollbar>
          </el-col>
          <el-col :span="16">
            <el-row type="flex" align="middle" class="dialog_bill_detail">
              <el-col :span="24">
                <div class="marbm_10">
                  <span class="font_20">待收款：</span>
                  <span class="font_20">¥{{ TotalAmount | toFixed | NumFormat }}</span>
                </div>
                <div>
                  <span>订单金额：¥{{ TotalAmount | toFixed | NumFormat }}</span>

                  <span v-if="PayCashAmount > 0" class="color_gray font_12 marlt_10">
                    付款：
                    <span class="color_red">-¥{{ PayCashAmount | toFixed | NumFormat }}</span>
                  </span>
                </div>
              </el-col>
            </el-row>
            <el-scrollbar class="el-scrollbar_height" style="height: 415px">
              <el-table :data="payList" size="small" class="padtp_15" :show-header="false">
                <el-table-column prop="payName" label="选择收款方式">
                  <template slot-scope="scope">
                    <el-select v-model="scope.row.PayMethodID" placeholder="选择收款方式" size="small" clearable filterable @change="payMethodChange(scope.row)">
                      <el-option v-for="item in payTypeList" :key="item.ID" :label="item.Name" :value="item.ID"> </el-option>
                    </el-select>
                  </template>
                </el-table-column>
                <el-table-column prop="price" label="支付金额">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.Amount" size="small" type="number" v-input-fixed placeholder="请输入收款金额" :disabled="scope.row.PayMethodID == ''" @input="payPriceChange(scope.row)"></el-input>
                  </template>
                </el-table-column>
                <el-table-column prop="address" label="操作" width="100">
                  <template slot-scope="scope">
                    <el-button type="danger" icon="el-icon-close" circle size="mini" @click="removePayClick(scope.$index)" v-if="scope.$index + 1 != 1"></el-button>
                    <el-button type="primary" icon="el-icon-plus" circle size="mini" @click="addPayclick" v-if="scope.$index + 1 == payList.length"></el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-scrollbar>
          </el-col>
        </el-row>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogBill = false" size="small" :disabled="modalLoading">取 消</el-button>
        <el-button type="primary" @click="saleBillRecharge" :loading="modalLoading" v-prevent-click size="small">确定收款 </el-button>
      </div>
    </el-dialog>

    <!--结账成功-->
    <el-dialog :visible.sync="dialogPay" width="450px" @close="closeSucceedDialog" append-to-body>
      <div class="text_center pad_15">
        <i class="el-icon-document" style="font-size: 80px; color: #999"></i>
        <div class="pad_15 color_primary font_weight_600 font_18">订单已结账成功</div>
      </div>
      <el-row class="pad_15 border_bottom">
        <el-col :span="12">销售订单号：</el-col>
        <el-col :span="12" class="text_right">{{ orderNumber }}</el-col>
      </el-row>
      <el-row class="pad_15 border_bottom">
        <el-col :span="12">订单金额：</el-col>
        <el-col :span="12" class="color_red text_right">¥{{ totalAmount_tmp }}</el-col>
      </el-row>
      <el-row class="pad_15 border_bottom">
        <el-col :span="5">订单备注：</el-col>
        <el-col :span="19">{{ Remark }}</el-col>
      </el-row>

      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="rechargeSavingContinueClick" size="small">继续开单</el-button>
        <el-button type="primary" @click="printNoteOfSmallDenomination" v-prevent-click :loading="printLoading" size="small">打印小票 </el-button>
        
        <el-button type="primary" @click="confrimPrintClick"  v-prevent-click size="small">打印单据</el-button>
      </div>
    </el-dialog>

    <!-- 打印小票 -->
    <el-dialog :visible.sync="cashierReceiptDialogVisible" width="300px" append-to-body>
      <span slot="title" class="font_14 color_333">打印小票</span>
      <div v-if="saleOrderDetail && entityName">
        <el-row>
          <el-col :span="24">
            <el-scrollbar class="el-scrollbar_height" style="height: 500px">
              <div class="marrt_10">
                <div class="dis_flex">
                  <span class="flex_box text_center font_16" style="line-height: 32px">{{ entityName }}</span>
                </div>
                <el-divider>
                  <span class="font_12 color_gray">订单信息</span>
                </el-divider>

                <div class="dis_flex">
                  <span class="flex_box font_12 color_gray text-left line_height_24">订单编号</span>
                  <span class="font_12 text_right line_height_24" style="flex: 3">{{ saleOrderDetail.ID }}</span>
                </div>
                <div class="dis_flex">
                  <span class="flex_box font_12 color_gray text-left line_height_24">下单时间</span>
                  <span class="font_12 text_right line_height_24" style="flex: 3">{{ saleOrderDetail.BillDate | dateFormat("YYYY-MM-DD HH:mm") }}</span>
                </div>
                <div class="dis_flex">
                  <span class="flex_box font_12 color_gray text-left line_height_24">会员姓名</span>
                  <span class="flex_box font_12 text_right line_height_24">{{ cashierReceipt.NameEncrypt ? formatName(saleOrderDetail.Name) : saleOrderDetail.Name }}</span>
                </div>
                <div class="dis_flex">
                  <span class="flex_box font_12 color_gray text-left line_height_24">会员手机号</span>
                  <span class="flex_box font_12 text_right line_height_24">{{ cashierReceipt.MobileEncrypt ? formatPhone(saleOrderDetail.PhoneNumber) : saleOrderDetail.PhoneNumber }}</span>
                </div>
                <div class="dis_flex">
                  <span class="flex_box font_12 color_gray text-left line_height_24">开单人</span>
                  <span class="flex_box font_12 text_right line_height_24">{{ saleOrderDetail.EmployeeName }}</span>
                </div>

                <div v-if="cashierReceipt.EntityAddress" class="dis_flex">
                  <span class="flex_box6 color_gray text-left line_height_24">地址：</span>
                  <span class="flex_box font_12 text_right line_height_24">{{ saleOrderDetail.AddressDetail }}</span>
                </div>
                <el-divider>
                  <span class="font_12 color_gray">消费明细</span>
                </el-divider>
                <template v-for="(item, index) in saleOrderDetail.SavingCard">
                  <div :key="index + 'SavingCard' + item.SavingCardID">
                    <div class="dis_flex">
                      <span class="font_12 color_gray text-left line_height_24" style="flex: 2">{{ index + 1 + saleOrderDetail.Project.length }} {{ item.SavingCardName }}</span>
                      <span v-if="cashierReceipt.SaleGoodsOriginPrice" class="font_12 text_right line_height_24" style="flex: 1">￥{{ (item.Amount / item.Quantity || 0).toFixed(2) | NumFormat }}</span>
                    </div>
                    <div class="dis_flex" v-if="item.LargessPrice > 0">
                      <span class="font_12 color_gray text-left line_height_24 marlt_10" style="flex: 2">充值赠送</span>
                      <span class="font_12 text_right line_height_24" style="flex: 1">￥{{ (item.LargessPrice / item.Quantity || 0).toFixed(2) | NumFormat }}</span>
                    </div>
                    <div class="dis_flex">
                      <span class="font_12 color_gray text-left line_height_24 marlt_10" style="flex: 2">数量</span>
                      <span class="font_12 text_right line_height_24" style="flex: 1">{{ item.Quantity }}</span>
                    </div>
                    <div v-if="cashierReceipt.SaleGoodsOriginPrice" class="dis_flex">
                      <span class="font_12 color_gray text-left line_height_24 marlt_10" style="flex: 2">小计</span>
                      <span class="font_12 text_right line_height_24" style="flex: 1">￥{{ item.Amount | toFixed | NumFormat }}</span>
                    </div>
                  </div>
                </template>

                <el-divider class="sell-el-divider"></el-divider>
                <div class="dis_flex">
                  <span class="flex_box font_12 color_gray text-left line_height_24">合计</span>
                  <span class="flex_box font_12 text_right line_height_24">￥{{ saleOrderDetail.TotalAmount | toFixed | NumFormat }}</span>
                </div>
                <div v-if="saleOrderDetail.SaleBillPay && saleOrderDetail.SaleBillPay.length > 0" class="dis_flex font_12">
                  <span class="flex_box6 color_gray text-left line_height_24">付款：</span>
                  <div class="flex_box">
                    <div class="dis_flex flex_box" v-for="pay in saleOrderDetail.SaleBillPay" :key="pay.ID + 'pay'">
                      <span class="flex_box color_gray line_height_24">{{ pay.Name }}</span>
                      <span class="flex_box text_right line_height_24">¥ {{ pay.Amount | toFixed | NumFormat }}</span>
                    </div>
                  </div>
                </div>
                <!--  -->
                <div v-if="saleOrderDetail.SaleBillPaySavingCardDeduction && saleOrderDetail.SaleBillPaySavingCardDeduction.length > 0" class="dis_flex font_12">
                  <span class="flex_box6 color_gray text-left line_height_24">卡抵扣：</span>
                  <div class="flex_box">
                    <div class="dis_flex flex_box" v-for="cardPay in saleOrderDetail.SaleBillPaySavingCardDeduction" :key="cardPay.ID + 'cardPay'">
                      <span class="flex_box color_gray line_height_24">{{ cardPay.Name }}</span>
                      <span class="flex_box text_right line_height_24">¥ {{ cardPay.TotalAmount | toFixed | NumFormat }}</span>
                    </div>
                  </div>
                </div>
                <div class="dis_flex" v-if="saleOrderDetail.PricePreferentialAmount != 0 && cashierReceipt.SalePromotions">
                  <span class="flex_box font_12 color_gray text-left line_height_24">手动改价</span>
                  <span class="flex_box font_12 text_right line_height_24" v-if="saleOrderDetail.PricePreferentialAmount > 0">-￥{{ saleOrderDetail.PricePreferentialAmount | toFixed | NumFormat }}</span>
                  <span class="flex_box font_12 text_right line_height_24" v-else>+￥{{ mathAbsData(saleOrderDetail.PricePreferentialAmount) | toFixed | NumFormat }}</span>
                </div>
                <div class="dis_flex" v-if="saleOrderDetail.CardPreferentialAmount > 0 && cashierReceipt.SalePromotions">
                  <span class="flex_box font_12 color_gray text-left line_height_24">卡优惠</span>
                  <span class="flex_box font_12 text_right line_height_24">-￥{{ saleOrderDetail.CardPreferentialAmount | toFixed | NumFormat }}</span>
                </div>
                <el-divider class="sell-el-divider"></el-divider>
                <div class="dis_flex flex_dir_column font_14 font_weight_600 flex_y_center color_999 padbm_10">
                  <span>{{ cashierReceipt.WriteTextFirst }}</span>
                  <span>{{ cashierReceipt.WriteTextSecond }}</span>
                </div>

                <div class="dis_flex font_12">
                  <span class="flex_box6 color_gray text-left line_height_24">签字：</span>
                </div>
              </div>
            </el-scrollbar>
          </el-col>
        </el-row>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cashierReceiptDialogVisible = false" size="small" :disabled="modalLoading">取 消</el-button>
        <el-button type="primary" @click="getprintSaleBillContent" :loading="modalLoading" v-prevent-click size="small"> 打印</el-button>
      </div>
    </el-dialog>

    <!--经手人-->
    <el-dialog title="经手人" :visible.sync="dialogVisibleAllHandler" width="800px" append-to-body>
      <div>
        <el-row class="padbm_10">
          <el-col :span="8">
            <el-input placeholder="请输入员工编号、姓名" prefix-icon="el-icon-search" v-model="handlerAllName" size="small" clearable></el-input>
          </el-col>
        </el-row>
        <el-tabs v-model="tabAllHandle">
          <el-tab-pane :label="handler.Name" :name="`${index}`" v-for="(handler, index) in getAllHandlerList()" :key="index">
            <el-row style="max-height: 300px; overflow-y: auto">
              <el-col
                :span="12"
                v-for="item in handler.Employee.filter((item) => !handlerAllName || item.EmployeeName.toLowerCase().includes(handlerAllName.toLowerCase()) || item.EmployeeID.toLowerCase().includes(handlerAllName.toLowerCase()))"
                :key="item.EmployeeID"
                class="marbm_10 dis_flex flex_y_center"
              >
                <el-checkbox v-model="item.Checked" @change="handlerCheckedChange(handler.Employee, item)">
                  <span class="marrt_10">{{ item.EmployeeName }} [{{ item.EmployeeID }}]</span>
                </el-checkbox>
                <el-input placeholder v-model="item.Discount" style="width: 120px" type="number" size="mini" v-input-fixed min="0" max="100" @input="handlerPercentChange(handler.Employee, item, 'dialog')">
                  <template slot="append">%</template>
                </el-input>
              </el-col>
            </el-row>
          </el-tab-pane>
        </el-tabs>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisibleAllHandler = false" size="small">取 消</el-button>
        <el-button type="primary" size="small" @click="confirmAllHandlerClick" v-prevent-click>确 定</el-button>
      </div>
    </el-dialog>

    <el-button v-show="false" ref="printButton" v-print="'rechargePrintContent'">打印</el-button>
    <!-- 打印 -->
    <el-dialog title="选择打印模板" :visible.sync="printTemplateVisible" width="400px">
      <el-select size="small" v-model="printTemplateID" >
        <el-option v-for="item in templateTypeList" :key="item.ID" :label="item.Name" :value="item.ID"></el-option>
      </el-select>
      <div slot="footer">
        <el-button @click="printTemplateVisible = false" size="small" v-prevent-click>取消</el-button>
        <el-button  type="primary" @click="confirmSelectPrintTemplate" size="small" v-prevent-click>打印 </el-button>
      </div>
    </el-dialog>
    <div style="display: none">
      <div id="rechargePrintContent">
        <component :is="printComponentName"></component>
      </div>
    </div>
  </div>
</template>

<script>
import API from "@/api/iBeauty/Order/savingCardRecharge";
import SellAPI from "@/api/iBeauty/Order/saleGoods";
import date from "@/components/js/date";
import cashierAPI from "@/api/iBeauty/Order/cashierReceipt";
import orderAPI from "@/api/iBeauty/Order/saleBill";
import printReceipt from "@/components/js/print";
import printComponent from "@/views/iBeauty/Order/components/zl-print.js";
import print from "vue-print-nb";

var socket;
var Enumerable = require("linq");

export default {
  name: "savingCardRecharge",
    
  directives: {
    print,
  },
  props: {
    billDate: String,
    isSupplement: Boolean,
    customerID: [Number, String],
    customerFullName: String,
    customerPhoneNumber: String,
    SellPermission: Object,
  },
  /**  引入的组件  */
  components: {},
  /**  Vue 实例的数据对象**/
  data() {
    return {
            
      templateTypeList: [],
      printComponentName: "",
      printTemplateVisible: false,
      printTemplateID: null,

      dialogVisibleAllHandler: false,
      showRemark: false,
      printLoading: false,
      dialogPay: false,
      dialogBill: false,
      modalLoading: false,
      handelDialogVisible: false,
      cashierReceiptDialogVisible: false,
      searchName: "",
      orderNumber: "",
      savingCards: [],
      selectSavingCards: [],
      savingCardHandlerList: [], //储值卡销售经手人
      Remark: "",
      goodsIndex: "",
      payTypeList: [], //支付方式
      handlerName: "",
      payList: [{ PayMethodID: "", Amount: "", price: 0 }],
      userName: "",
      entityName: "",
      PayCashAmount: "",
      tabHandle: "",

      PayAmount: "",
      saleOrderDetail: "",
      cashierReceipt: "", // 小票配置信息
      // rechargeTakeOrderData: null,
      billID: null,
      totalAmount_tmp: "",
      tabAllHandlePosition: "saleHandler",
      tabAllHandle: "0",
      saleAllHandlerList: [],
      treatAllHandlerList: [],
      handlerAllName: "",
    };
  },
  /**计算属性  */
  computed: {
    TotalAmount: function () {
      let that = this;
      return that.selectSavingCards.reduce((preVal, curVal) => {
        return preVal + parseFloat(curVal.Amount || 0);
      }, 0);
    },
  },
  /**  方法集合  */
  methods: {
     /**  确定打印   */
     confirmSelectPrintTemplate() {
      let that = this;
      that.printTemplateVisible = false;
      that.$nextTick(() => {
        let temp = that.templateTypeList.find((i) => {
          return i.ID == that.printTemplateID;
        });
        that.printComponentName = printComponent.getPrintComponent(that.saleOrderDetail, temp.Template);
        let buttonElement = that.$refs.printButton.$el;
        let clickEvent = new MouseEvent('click');
        buttonElement.dispatchEvent(clickEvent);
      });
    },
    /**  确认打印  */
    confrimPrintClick() {
      let that = this;
      if (that.templateTypeList.length == 1) {
        let temp = that.templateTypeList[0].Template;
        that.$nextTick(() => {
          that.printComponentName = printComponent.getPrintComponent(that.saleOrderDetail, temp);
          let buttonElement = that.$refs.printButton.$el;
          let clickEvent = new MouseEvent("click");
          buttonElement.dispatchEvent(clickEvent);
        });
      } else {
        if (!that.templateTypeList || !that.templateTypeList.length) {
          that.$message.error("暂无打印模板，请添加打印模板");
          return;
        }
        that.printTemplateID = that.templateTypeList[0].ID;
        that.printTemplateVisible = true;
      }
    },
    /**    */
    printNoteOfSmallDenomination(){
      let that = this;
      that.cashierReceiptDialogVisible = true;
    },
    /* 清楚已选择的储值卡 */
    clearSelectSavingCard() {
      let that = this;
      that.selectSavingCards = [];
    },
    /**  确认全部修改经手人  */
    confirmAllHandlerClick() {
      let that = this;

      /* 储值卡 */
      that.selectSavingCards.forEach((j) => {
        j.HandleTypeList.forEach((n) => {
          that.saleAllHandlerList
            .filter((i) => i.Name == n.Name)
            .forEach((e) => {
              n.Employee = e.Employee.filter((em) => em.Checked).map((emp) => {
                emp.ID = `${n.ID}-${emp.EmployeeID}`;
                emp.SaleHandlerID = n.ID;
                return Object.assign({}, emp);
              });
            });
        });
      });
      that.dialogVisibleAllHandler = false;
      that.showRemark = false;
      that.saleAllHandlerList = [];
      that.treatAllHandlerList = [];
    },

    /** 获取经手人   */
    getAllHandlerList() {
      var that = this;
      if (that.tabAllHandlePosition == "saleHandler") {
        return that.saleAllHandlerList;
      } else {
        return that.treatAllHandlerList;
      }
    },

    /**  修改经手人类型 销售、消耗  */
    hangeAllHandleType() {
      var that = this;
      that.tabAllHandle = "0";
    },
    /**    */
    showSelectAllHandlerClick() {
      let that = this;
      let GoodTypes = [];
      if (that.selectSavingCards && that.selectSavingCards.length > 0) {
        GoodTypes.push("50");
      }
      that.tabAllHandle = "0";
      that.saleHandler_allHandler(GoodTypes);
      that.dialogVisibleAllHandler = true;
    },
    /**    */
    hiddenInfoRemarkClick() {
      let that = this;
      that.showRemark = false;
    },
    /**    */
    showRemarkClick() {
      let that = this;
      that.showRemark = true;
    },

    /**  继续开单点击事件  */
    rechargeSavingContinueClick() {
      let that = this;
      that.dialogPay = false;
      that.$emit("rechargeSavingContinue");
    },
    /**    */
    rechargeSavingTakeOrder(data) {
      let that = this;
      that.Remark = data.Remark;
      // that.rechargeTakeOrderData = data;
      if (data) {
        that.billID = data.ID;
        let tmp = JSON.parse(data.Content);
        that.selectSavingCards = Array.from(tmp).map((val) => val);
      } else {
        that.$message.error("单据信息获取失败");
      }
    },
    /**  清除数据  */
    clearRechargeSavingCardAccountData() {
      let that = this;
      that.savingCards = [];
      that.selectSavingCards = [];
    },
    //获取开单时间
    getBillDate: function () {
      var that = this;
      return that.isSupplement ? that.billDate : date.formatDate.format(new Date(), "YYYY-MM-DD hh:mm:ss");
    },
    /**  点击储值卡  */
    savingCardRechargeAccountClick(item) {
      let that = this;
      let temp = that.selectSavingCards.findIndex((val) => {
        return val.AccountID == item.AccountID;
      });
      if (temp != -1) {
        that.$message.error({
          message: "该储值卡已经在列表中。",
        });
        return;
      }

      var tempHandle = [];
      that.savingCardHandlerList.forEach((val) => {
        tempHandle.push({
          Name: val.Name,
          ID: val.ID,
          Employee: [],
        });
      });

      that.selectSavingCards.push({
        Name: item.Name,
        Alias: item.Alias,
        AccountID: item.AccountID,
        SavingCardID: item.SavingCardID,
        Amount: "",
        LargessAmount: "",
        SavingCardSaleHandler: [],
        HandleTypeList: tempHandle,
        SavingCardRechargeRules: item.SavingCardRechargeRules,
      });
    },

    /** 阶梯价格选中项 样式   */
    RechargeRulesClass(item, index) {
      // let mar = index == 0?"":""
      let style = item.RechargeRulesSelectIndex == index ? "selectRules" : "";
      return " " + style;
    },
    /**  选择 阶梯价格规则  */
    selectRechargeRules(item, RechargeItem, index) {
      item.RechargeRulesSelectIndex = index;

      item.number = 1;
      item.Amount = RechargeItem.Price;
      item.LargessAmount = RechargeItem.LargessPrice;
      // that.getTotalAmount();
    },

    /**  删除已选储值卡  */
    removeSavingCardSelectItemClick(index) {
      let that = this;
      that.selectSavingCards.splice(index, 1);
    },

    // 经手人
    employeeHandleClick: function (item, index) {
      var that = this;
      var emplayee = [];

      item.HandleTypeList.forEach(function (hand) {
        hand.Employee.forEach(function (emp) {
          emplayee.push({ ID: emp.ID, Discount: emp.Discount });
        });
      });
      that.savingCardHandlerList.forEach(function (handler) {
        handler.Employee.forEach(function (emp) {
          emp.Checked = false;
          emp.Discount = "";
          emplayee.forEach(function (i) {
            if (emp.ID == i.ID) {
              emp.Checked = true;
              emp.Discount = i.Discount;
            }
          });
        });
      });

      that.goodsIndex = index;
      that.handelDialogVisible = true;
    },

    // 经手人选择
    handlerCheckedChange: function (row, item) {
      let checkedArr = row.filter((i) => {
        return i.Checked;
      });
      row.forEach((val) => {
        if (val.Checked) {
          val.Discount = Math.floor(100 / checkedArr.length);
          if (val.EmployeeID == checkedArr[checkedArr.length - 1].EmployeeID) {
            val.Discount = Math.ceil(100 / checkedArr.length);
          }
        }
      });
      var discount = 0;
      var employee = Enumerable.from(row)
        .where(function (i) {
          return i.Checked;
        })
        .toArray();
      employee.forEach(function (emp) {
        var Discount = emp.Discount;
        if (Discount == "") {
          Discount = 0;
        }
        discount = parseFloat(discount) + parseFloat(Discount);
      });
      if (!item.Checked) {
        item.Discount = "";
      } else {
        if (item.Discount == "") {
          if (discount > 100) {
            item.Discount = 0;
          } else {
            item.Discount = 100 - discount;
          }
        }
      }
    },
    // 百分比
    handlerPercentChange: function (row, item, type) {
      var that = this;
      var discount = 0;
      if (item.Discount != "") {
        item.Discount = parseFloat(item.Discount);
      }
      if (type !== "dialog") {
        if (item.Discount > 100) {
          item.Discount = 100;
        }
      }
      var employee = Enumerable.from(row)
        .where(function (i) {
          return i.Checked;
        })
        .toArray();
      employee.forEach(function (emp) {
        var Discount = emp.Discount;
        if (Discount == "") {
          Discount = 0;
        }
        discount = parseFloat(discount) + parseFloat(Discount);
      });
      if (type !== "dialog") {
        if (parseFloat(discount) > 100) {
          item.Discount = 100 - (discount - item.Discount);
          that.$message.error("比例总和不能超过100%");
        }
      }
    },

    // 经手人确认选择
    submitHandleClick: function () {
      var that = this;
      var goodsHandlerList = JSON.parse(JSON.stringify(that.savingCardHandlerList));
      if (
        goodsHandlerList.some((item) => {
          return (
            item.Employee.reduce((pre, pri) => {
              return pre + Number(pri.Discount);
            }, 0) > 100
          );
        })
      ) {
        that.$message.error("比例总和不能超过100%");
        return;
      }

      goodsHandlerList.forEach(function (item) {
        item.Employee = Enumerable.from(item.Employee)
          .where(function (i) {
            return i.Checked;
          })
          .toArray();
      });

      that.selectSavingCards[that.goodsIndex].HandleTypeList = goodsHandlerList;

      that.handelDialogVisible = false;
    },

    // 删除支付
    removePayClick: function (index) {
      var that = this;
      that.payList.splice(index, 1);
      that.paymentAmountData();
    },
    // 添加支付
    addPayclick: function () {
      var that = this;
      that.payList.push({ PayMethodID: "", Amount: "", price: 0 });
    },
    // 支付方式支付金额变化
    payPriceChange: function (item) {
      var that = this;
      var payAmount = (parseFloat(that.PayAmount || 0) + parseFloat(item.price || 0)).toFixed(2);
      if (parseFloat(item.Amount) > parseFloat(payAmount)) {
        item.Amount = payAmount;
      }
      item.price = item.Amount;
      that.paymentAmountData();
    },
    payMethodChange: function (item) {
      var that = this;
      if (item.PayMethodID == "") {
        item.Amount = "";
      } else {
        if (item.Amount == "") {
          item.Amount = parseFloat(that.PayAmount || 0).toFixed(2);
        }
      }
      that.payPriceChange(item);
    },

    // 支付方式金额总计
    paymentAmountData: function () {
      var that = this;
      let amount = that.payList.reduce((preVal, curVal) => {
        return preVal + parseFloat(curVal.price || 0);
      }, 0);
      that.PayCashAmount = parseFloat(amount || 0).toFixed(2);
      that.PayAmount = (that.TotalAmount - amount).toFixed(2);
    },

    /*  */
    reviewHandledScale() {
      const that = this;
      const isProjectHandleScale = that.selectSavingCards.some((i) => {
        return (
          i.HandleTypeList &&
          i.HandleTypeList.some((h) => {
            return h.Employee.some((e) => {
              return !e.Discount && e.Discount !== 0;
            });
          })
        );
      });
      return isProjectHandleScale;
    },

    /**  结账  */
    rechargeBillClick() {
      let that = this;

      if (!that.customerID) {
        that.$message.error("请填写客户信息");
        return;
      }
      if (that.selectSavingCards.length == 0) {
        that.$message.error("请选择充值的储值卡");
        return;
      }
      if (that.getBillDate() == null) {
        that.$message.error("请输入补录日期");
        return;
      }
      if (that.reviewHandledScale()) {
        that.$message.error("请输入经手人比例");
        return;
      }
      if (that.customerID == null) {
        that.$message.error("请填写客户信息");
        return;
      }
      if (
        that.selectSavingCards.some((val) => {
          return val.Amount == null || val.Amount === "";
        })
      ) {
        that.$message.error("请填写充值金额");
        return;
      }

      if (
        that.selectSavingCards.some((i) => {
          return !i.HandleTypeList || !i.HandleTypeList.some((j) => j.Employee && j.Employee.length);
        })
      ) {
        that
          .$confirm("存在未添加经手人的商品，是否继续收款", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          })
          .then(() => {
            that.PayAmount = that.TotalAmount;
            that.PayCashAmount = "";
            that.payList = [{ PayMethodID: "", Amount: "", price: "" }];
            that.dialogBill = true;
          })
          .catch(() => {
            that.$message({
              type: "info",
              message: "已取消操作",
            });
          });
      } else {
        that.PayAmount = that.TotalAmount;
        that.PayCashAmount = "";
        that.payList = [{ PayMethodID: "", Amount: "", price: "" }];
        that.dialogBill = true;
      }
    },
    /**  挂单 -提交  */
    confrimRechargeTreatSettleBillClick() {
      let that = this;
      if (that.selectSavingCards.length == 0) {
        that.$message.error("请选择充值的储值卡");
        return;
      }
      if (that.getBillDate() == null) {
        that.$message.error("请输入补录日期");
        return;
      }
      if (that.customerID == null) {
        that.$message.error("请填写客户信息");
        return;
      }
      if (
        that.selectSavingCards.some((val) => {
          return val.Amount == null || val.Amount === "";
        })
      ) {
        that.$message.error("请填写充值金额");
        return;
      }

      that.PayAmount = that.TotalAmount;
      that.PayCashAmount = "";

      that
        .$confirm("是否确充值订单信息？", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
        .then(() => {
          that.createRechargePendingOrder();
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消操作",
          });
        });
    },

    /**   关闭结账成功 */
    closeSucceedDialog() {
      let that = this;
      that.Remark = "";
      that.totalAmount_tmp = "";
    },

    /**  打印小票  */
    printOrderReceipt() {
      let that = this;

      that.getOrderDetail();
      that.getReceiptConfig();
    },

    /** 获取订单详情 */
    getOrderDetail() {
      var that = this;
      that.printLoading = true;

      var params = {
        SaleBillID: that.orderNumber,
      };

      orderAPI
        .getOrderDetail(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.saleOrderDetail = res.Data;
            
            that.getPrintTemplate_list();
          }
        })
        .finally(function () {
          that.printLoading = false;
        });
    },
    /**  获取打印内容  */
    getprintSaleBillContent() {
      let that = this;
      let params = {
        SaleBillID: that.saleOrderDetail.ID,
      };
      cashierAPI
        .printSaleBillContent(params)
        .then((res) => {
          if (res.StateCode == 200) {
            for (let index = 0; index < that.cashierReceipt.PrintQuantity; index++) {
              printReceipt.doActionPrint(res.Data.printDocuments, (request) => {
                socket.send(JSON.stringify(request));
              });
            }
          }
        })
        .finally(() => {});
    },

    /**  获取小票配置信息  */
    getReceiptConfig() {
      var that = this;
      cashierAPI
        .getReceiptConfigBill()
        .then((res) => {
          if (res.StateCode == 200) {
            that.cashierReceipt = res.Data;
          }
        })
        .finally(() => {});
    },

    // 姓名隐藏
    formatName(name) {
      return printReceipt.hiddenName(name);
    },
    // 手机号隐藏
    formatPhone(phone) {
      return printReceipt.hiddenPhone(phone);
    },

    /********************************************************************************************    */
    /**  获取顾客存量  */
    async getRechargeSavingCardAccount() {
      let that = this;
      try {
        let params = {
          CustomerID: that.customerID,
          BillDate: that.getBillDate(),
          Name: that.searchName,
        };
        let res = await API.getRechargeSavingCardAccount(params);
        if (res.StateCode == 200) {
          that.savingCards = res.Data;
        } else {
          that.$message.error({
            message: res.Message,
          });
        }
      } catch {
        that.$message.error({
          message: "错误",
        });
      }
    },
    /**  获取充值结账的 参数  */
    getRechargeSavingCardAccountParams() {
      let that = this;
      let savingCard = that.selectSavingCards.map((val) => {
        val.HandleTypeList.forEach(function (handler) {
          handler.Employee.forEach(function (employee) {
            val.SavingCardSaleHandler.push({
              SaleHandlerID: employee.SaleHandlerID,
              EmployeeID: employee.EmployeeID,
              Scale: employee.Discount,
            });
          });
        });

        let temp = {
          AccountID: val.AccountID,
          SavingCardID: val.SavingCardID,
          Amount: val.Amount,
          LargessAmount: val.LargessAmount,
          SavingCardSaleHandler: val.SavingCardSaleHandler,
        };

        return temp;
      });

      let payList = Enumerable.from(that.payList)
        .where(function (i) {
          return i.PayMethodID != "" && i.Amount != "";
        })
        .select((val) => ({
          PayMethodID: val.PayMethodID,
          Amount: val.Amount,
        }))
        .toArray();

      let params = {
        CustomerID: that.customerID,
        BillDate: that.getBillDate(),
        Amount: that.TotalAmount,
        Remark: that.Remark,
        SavingCard: savingCard,
        PayMethod: payList,
      };
      return params;
    },
    /**  充值结账  */
    async saleBillRecharge() {
      let that = this;
      if (that.PayAmount != 0) {
        that.$message.error({
          message: "请填写收款金额。",
          duration: 2000,
        });
        that.modalLoading = false;
        return;
      }
      that.modalLoading = true;
      try {
        let params = that.getRechargeSavingCardAccountParams();
        if (that.billID) {
          params.BillID = that.billID;
        }
        let res = await API.saleBillRecharge(params);
        if (res.StateCode == 200) {
          that.totalAmount_tmp = that.TotalAmount;
          that.selectSavingCards = [];
          // that.Remark = "";
          that.dialogBill = false;
          that.payList = [{ PayMethodID: "", Amount: "", price: 0 }];
          that.PayAmount = "";
          that.PayCashAmount = "";
          // that.$message.success("充值成功！");
          that.dialogPay = true;
          that.orderNumber = res.Message;
          that.billID = null;
          that.getRechargeSavingCardAccount();
          that.getOrderDetail();
        } else {
          that.$message.error({
            message: res.Message,
          });
        }

        that.modalLoading = false;
      } catch {
        that.modalLoading = false;
        // that.$message.error({
        //   message: "获取失败",
        // });
      }
    },
    /**  储值卡挂单  */
    async createRechargePendingOrder() {
      let that = this;
      let params = that.getRechargeSavingCardAccountParams();
      params.Content = JSON.stringify(that.selectSavingCards);
      let res = await API.createRechargePendingOrder(params);
      if (res.StateCode == 200) {
        that.selectSavingCards = [];
        that.Remark = "";
        that.billID = null;
        that.$message.success("充值单挂单成功！");
      } else {
        that.$message.error(res.Message);
      }
    },

    // 储值卡经手人
    savingCardHandlerData: function () {
      var that = this;
      that.loading = true;
      SellAPI.getSavingCardHandler()
        .then((res) => {
          if (res.StateCode == 200) {
            that.savingCardHandlerList = res.Data;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },

    // 支付方式
    salePayMethodData: function () {
      var that = this;
      that.loading = true;
      SellAPI.getSalePayMethod()
        .then((res) => {
          if (res.StateCode == 200) {
            that.payTypeList = res.Data;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },

    /** 销售经手人   */
    async saleHandler_allHandler(GoodTypes) {
      let that = this;
      try {
        let params = {
          GoodTypes: GoodTypes,
        };
        let res = await SellAPI.saleHandler_allHandler(params);
        if (res.StateCode == 200) {
          that.saleAllHandlerList = res.Data.map((i) => {
            return {
              Name: i.Name,
              Employee: i.Employee.map((j) => {
                j.Checked = false;
                return Object.assign({}, j);
              }),
            };
          });
        } else {
          that.$message.error(res.Message);
        }
      } catch (error) {
        //that.$message.error(error);
      }
    },

     /** 获取模板列表   */
     async getPrintTemplate_list() {
      let that = this;
      let params = { TemplateType: "rechargebill" };
      let res = await orderAPI.getPrintTemplate_list(params);
      if (res.StateCode == 200) {
          that.templateTypeList = res.Data;  
        
      } else {
        that.$message.error(res.Message);
      }
      return res;
    },
  },
  /** 监听数据变化   */
  watch: {},
  /** 创建实例之前执行函数   */
  beforeCreate() {},
  /**  实例创建完成之后  */
  created() {},
  /**  在挂载开始之前被调用  */
  beforeMount() {},
  /**  实例被挂载后调用  */
  mounted() {
    let that = this;
    socket = printReceipt.getSocket((res) => {
      if (res.status == "success") {
        that.$message.success({
          message: "打印成功",
          duration: 2000,
        });
        that.cashierReceiptDialogVisible = false;
      }
    });

    if (localStorage.getItem("access-user")) {
      that.userName = JSON.parse(localStorage.getItem("access-user")).EmployeeName;
      that.entityName = JSON.parse(localStorage.getItem("access-user")).EntityName;
    }
    that.savingCardHandlerData();
    that.salePayMethodData();
    if (that.customerID != null) {
      that.getRechargeSavingCardAccount();
    }

    // that.$bus.$on(that.$bus.rechargTakeOrder, (data) => {
    // });
  },

  beforeDestroy() {
    // let that = this;
    // that.$bus.$off(that.$bus.rechargTakeOrder);
  },
};
</script>

<style lang="scss">
.savingCardRecharge {
  height: calc(100% - 63px);

  .savingCardRecharge_content {
    height: 100%;

    .project_left {
      height: 100%;
      border-right: 1px solid #eee;

      .el-scrollbar_height {
        height: calc(100% - 63px);

        .goods-item {
          line-height: 36px;
          font-size: 12px;
          margin-left: 20px;
          margin-right: 5px;
        }

        .goods-lable {
          color: #606266;
        }

        .el-card__header {
          padding: 10px 20px;
          background-color: #f5f7fa;
        }
      }
    }

    .project_right {
      height: 100%;

      .el-main {
        padding: 0;

        .el-scrollbar_height {
          height: calc(100% - 63px);

          .row_header {
            background-color: #fff7f3;
            padding: 10px;
          }

          .employee_num {
            width: 90px;

            .el-input-group__append {
              padding: 0 10px;
            }
          }

          .el-form-item__label {
            font-size: 13px !important;
          }
        }
      }
    }
  }

  .el-footer {
    height: initial !important;
    padding: 10px;
  }
}

.orderInfoRemark {
  position: absolute;
  background-color: rgba($color: #333, $alpha: 0.3);
  top: 0;
  right: 0;
  width: 100%;
  height: calc(100% - 54px);
  z-index: 1;
  .infoRemarContent {
    padding: 0px 10px 10px;
    background-color: #fff;
    position: absolute;
    bottom: 0;
    right: 0;
    left: 0;
    // width: 100%;
    font-size: 13px;
    color: #666;
    .el-form {
      .el-form-item {
        .el-form-item__label {
          font-size: 13px;
        }
      }
    }
  }

  .v-enter-active,
  .v-leave-active {
    transition: all 0.2s ease;
  }
  .v-enter,
  .v-leave-to {
    transform: opacity 0.5s;
    opacity: 0;
  }
}
</style>
