import Vue from 'vue';

// 使用 Event Bus
const bus = new Vue({
  data:{
    treatTakeOrder:"treatTakeOrder_key", // 消耗挂单 取单的key
    saleTakeOrder:"saleTakeOrder_key", // 销售挂单详情 key
    rechargTakeOrder:"rechargTakeOrder_key", // 充值挂单详情 key
    arrearTakeOrder:"arrearTakeOrder_key", // 补欠款挂单详情 key
    RefreshFollowUpList:"RefreshFollowUpList_key",//工作台刷新跟进列表的数据
    ChangeLogoImage:"ChangeLogoImage_bus_key",//修改logo 及时修改左上角 图标
    
    LogoImg:"LogoImageInfo_key"
  }
});

export default bus;