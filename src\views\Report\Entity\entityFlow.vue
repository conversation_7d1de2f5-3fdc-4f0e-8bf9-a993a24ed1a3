<template>
  <div class="entityFlow content_body_nopadding" v-loading="loading">
    <el-tabs v-model="activeName" type="border-card">
      <el-tab-pane label="门店客流" name="1">
        <!-- 搜索 -->
        <div class="nav_header">
          <el-form :inline="true" size="small" @submit.native.prevent>
            <el-form-item v-if="EntityList.length > 1" label="门店">
              <el-select v-model="searchData.EntityID" clearable filterable placeholder="请选择门店" :default-first-option="true" @change="handleSearch">
                <el-option v-for="item in EntityList" :key="item.ID" :label="item.EntityName" :value="item.ID"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="时间筛选">
              <el-date-picker v-model="searchData.QueryDate" unlink-panels type="daterange" range-separator="至" value-format="yyyy-MM-dd" start-placeholder="开始日期" end-placeholder="结束日期" @change="handleSearch" :picker-options="pickerOptions"></el-date-picker>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" v-prevent-click @click="handleSearch">搜索</el-button>
            </el-form-item>
            <el-form-item>
              <el-button v-if="isExport" type="primary" size="small" v-prevent-click :loading="downloadLoading" @click="downloadSalePayExcel">导出</el-button>
            </el-form-item>
          </el-form>
        </div>
        <!-- 表格 -->
        <el-table :data="tableData" size="small" show-summary :summary-method="getSummary">
          <el-table-column label="门店" prop="EntityName" fixed width="150"></el-table-column>
          <el-table-column label="客户数" align="center">
            <el-table-column label="客户数" prop="CustomerCount" align="right">
              <template slot="header">
                客户数
                <el-popover placement="top-start" trigger="hover">
                  <p>1.统计时间内包含销售、消耗订单的客户数；</p>
                  <p>2.散客订单按订单数统计，同一客户在统计时间内去重</p>
                  <el-button type="text" style="color: #dcdfe6; padding: 0" icon="el-icon-info" slot="reference"></el-button>
                </el-popover>
              </template>
            </el-table-column>
            <el-table-column label="销售" prop="CustomerSaleCount" align="right">
              <template slot="header">
                销售
                <el-popover placement="top-start" trigger="hover">
                  <p>1.统计时间内包含销售订单的客户数；</p>
                  <p>2.散客订单按订单数统计，同一客户在统计时间内去重</p>
                  <el-button type="text" style="color: #dcdfe6; padding: 0" icon="el-icon-info" slot="reference"></el-button>
                </el-popover>
              </template>
            </el-table-column>
            <el-table-column label="消耗" prop="CustomerTreatCount" align="right">
              <template slot="header">
                消耗
                <el-popover placement="top-start" trigger="hover">
                  <p>1.统计时间内包含消耗订单的客户数</p>
                  <p>2.散客订单按订单数统计，同一客户当天去重，隔天累加</p>
                  <el-button type="text" style="color: #dcdfe6; padding: 0" icon="el-icon-info" slot="reference"></el-button>
                </el-popover>
              </template>
            </el-table-column>
          </el-table-column>
          <el-table-column label="客流" align="center">
            <el-table-column label="客流数" prop="PassengerFlowCount" align="right">
              <template slot="header">
                客流数
                <el-popover placement="top-start" trigger="hover">
                  <p>1. 统计时间内包含销售、消耗订单的客户数;</p>
                  <p>2. 散客订单按订单数统计，同一客户当天去重，隔天累加</p>
                  <el-button type="text" style="color: #dcdfe6; padding: 0" icon="el-icon-info" slot="reference"></el-button>
                </el-popover>
              </template>
            </el-table-column>
            <el-table-column label="销售" prop="PassengerFlowSaleCount" align="right">
              <template slot="header">
                销售
                <el-popover placement="top-start" trigger="hover">
                  <p>1. 统计时间内包含销售订单的客户数;</p>
                  <p>2. 散客订单按订单数统计，同一客户当天去重，隔天累加</p>
                  <el-button type="text" style="color: #dcdfe6; padding: 0" icon="el-icon-info" slot="reference"></el-button>
                </el-popover>
              </template>
            </el-table-column>
            <el-table-column label="消耗" prop="PassengerFlowTreatCount" align="right">
              <template slot="header">
                消耗
                <el-popover placement="top-start" trigger="hover">
                  <p>1. 统计时间内包含消耗订单的客户数;</p>
                  <p>2. 散客订单按订单数统计，同一客户当天去重，隔天累加</p>
                  <el-button type="text" style="color: #dcdfe6; padding: 0" icon="el-icon-info" slot="reference"></el-button>
                </el-popover>
              </template>
            </el-table-column>
          </el-table-column>
          <el-table-column label="新增" align="center">
            <el-table-column label="新增客户数" prop="NewCustomerCount" align="right" width="90"></el-table-column>
            <el-table-column label="新增会员数" prop="NewCustomerMemberCount" align="right" width="90"></el-table-column>
          </el-table-column>
          <el-table-column label="新老客" align="center">
            <el-table-column label="成交新客数" prop="DealNewCount" align="right" width="110">
              <template slot="header">
                成交新客数
                <el-popover placement="top-start" trigger="hover">
                  <p>在统计时间内，历史未发生过成交，首次在店铺完成付款的客户数</p>
                  <el-button type="text" style="color: #dcdfe6; padding: 0" icon="el-icon-info" slot="reference"></el-button>
                </el-popover>
              </template>
            </el-table-column>
            <el-table-column label="成交老客数" prop="DealOldCount" align="right" width="110">
              <template slot="header">
                成交老客数
                <el-popover placement="top-start" trigger="hover">
                  <p>1.销售订单时间在统计时间内，付款的老客户数;</p>
                  <p>2.同一客户当天去重，隔天累加，散客不计入统计</p>
                  <el-button type="text" style="color: #dcdfe6; padding: 0" icon="el-icon-info" slot="reference"></el-button>
                </el-popover>
              </template>
            </el-table-column>
          </el-table-column>
          <el-table-column label="性别" align="center">
            <template slot="header">
              性别
              <el-popover placement="top-start" trigger="hover">
                <p>1.在统计时间内，按客户性别人数进行统计;</p>
                <p>2.散客按未知性别，数量为1进行统计</p>
                <el-button type="text" style="color: #dcdfe6; padding: 0" icon="el-icon-info" slot="reference"></el-button>
              </el-popover>
            </template>
            <el-table-column label="女客" prop="WomanCount" align="right"></el-table-column>
            <el-table-column label="男客" prop="ManCount" align="right"></el-table-column>
            <el-table-column label="未知" prop="UnknownCount" align="right"></el-table-column>
          </el-table-column>
          <el-table-column label="客户构成" align="center">
            <template slot="header">
              客户构成
              <el-popover placement="top-start" trigger="hover">
                <p>在统计时间内，按顾客的身份信息(散客/非会员/会员)进行统计</p>
                <el-button type="text" style="color: #dcdfe6; padding: 0" icon="el-icon-info" slot="reference"></el-button>
              </el-popover>
            </template>
            <el-table-column label="散客" prop="IndividualTravelerCount" align="right"></el-table-column>
            <el-table-column label="散客占比" prop="IndividualTravelerRate" align="right">
              <template slot-scope="scope">
                <span v-if="scope.row.IndividualTravelerRate > 0">{{ scope.row.IndividualTravelerRate | toFixed | NumFormat }}%</span>
                <span v-else>{{ scope.row.IndividualTravelerRate }}</span>
              </template>
            </el-table-column>
            <el-table-column label="会员" prop="MemberCount" align="right"></el-table-column>
            <el-table-column label="会员占比" prop="MemberRate" align="right">
              <template slot-scope="scope">
                <span v-if="scope.row.MemberRate > 0">{{ scope.row.MemberRate | toFixed | NumFormat }}%</span>
                <span v-else>{{ scope.row.MemberRate }}</span>
              </template>
            </el-table-column>
            <el-table-column label="非会员" prop="UnMemberCount" align="right"></el-table-column>
            <el-table-column label="非会员占比" prop="UnMemberRate" align="right" width="90">
              <template slot-scope="scope">
                <span v-if="scope.row.UnMemberRate > 0">{{ scope.row.UnMemberRate | toFixed | NumFormat }}%</span>
                <span v-else>{{ scope.row.UnMemberRate }}</span>
              </template>
            </el-table-column>
          </el-table-column>
        </el-table>
        <!-- 分页 -->
        <div class="pad_15 text_right">
          <el-pagination background v-if="paginations.total > 0" @current-change="handlePageChange" :current-page.sync="paginations.page" :page-size="paginations.page_size" :layout="paginations.layout" :total="paginations.total"></el-pagination>
        </div>
      </el-tab-pane>
      <el-tab-pane label="销售客流" name="2">
        <!-- 搜索 -->
        <div class="nav_header">
          <el-form :inline="true" size="small" @submit.native.prevent>
            <el-form-item v-if="EntityList.length > 1" label="门店">
              <el-select v-model="searchData_sale.EntityID" clearable filterable placeholder="请选择门店" :default-first-option="true" @change="handleSearch_sale">
                <el-option v-for="item in EntityList" :key="item.ID" :label="item.EntityName" :value="item.ID"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="时间筛选">
              <el-date-picker v-model="searchData_sale.QueryDate" unlink-panels type="daterange" range-separator="至" value-format="yyyy-MM-dd" start-placeholder="开始日期" end-placeholder="结束日期" @change="handleSearch_sale" :picker-options="pickerOptions"></el-date-picker>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" v-prevent-click @click="handleSearch_sale">搜索</el-button>
            </el-form-item>
            <el-form-item>
              <el-button v-if="isExport" type="primary" size="small" v-prevent-click :loading="downloadLoading" @click="entityPassengerFlow_saleExcel">导出</el-button>
            </el-form-item>
          </el-form>
        </div>
        <!-- 表格 -->
        <el-table :data="tableData_sale" size="small" show-summary :summary-method="getSummarySale">
          <el-table-column label="门店" prop="EntityName" fixed width="150"></el-table-column>

          <el-table-column label="客户数" prop="CustomerCount" align="right">
            <template slot="header">
              客户数
              <el-popover placement="top-start" trigger="hover">
                <p>1.统计时间内包含销售、消耗订单的客户数；</p>
                <p>2.散客订单按订单数统计，同一客户在统计时间内去重</p>
                <el-button type="text" style="color: #dcdfe6; padding: 0" icon="el-icon-info" slot="reference"></el-button>
              </el-popover>
            </template>
          </el-table-column>
          <el-table-column label="客流数" prop="PassengerFlowCount" align="right">
            <template slot="header">
              客流数
              <el-popover placement="top-start" trigger="hover">
                <p>1. 统计时间内包含销售、消耗订单的客户数;</p>
                <p>2. 散客订单按订单数统计，同一客户当天去重，隔天累加</p>
                <el-button type="text" style="color: #dcdfe6; padding: 0" icon="el-icon-info" slot="reference"></el-button>
              </el-popover>
            </template>
          </el-table-column>
          <el-table-column label="新老客" align="center">
            <el-table-column label="成交新客数" prop="DealNewCount" align="right" width="110">
              <template slot="header">
                成交新客数
                <el-popover placement="top-start" trigger="hover">
                  <p>在统计时间内，历史未发生过成交，首次在店铺完成付款的客户数</p>
                  <el-button type="text" style="color: #dcdfe6; padding: 0" icon="el-icon-info" slot="reference"></el-button>
                </el-popover>
              </template>
            </el-table-column>
            <el-table-column label="成交老客数" prop="DealOldCount" align="right" width="110">
              <template slot="header">
                成交老客数
                <el-popover placement="top-start" trigger="hover">
                  <p>1.销售订单时间在统计时间内，付款的老客户数;</p>
                  <p>2.同一客户当天去重，隔天累加，散客不计入统计</p>
                  <el-button type="text" style="color: #dcdfe6; padding: 0" icon="el-icon-info" slot="reference"></el-button>
                </el-popover>
              </template>
            </el-table-column>
          </el-table-column>
          <el-table-column label="性别" align="center">
            <template slot="header">
              性别
              <el-popover placement="top-start" trigger="hover">
                <p>1.在统计时间内，按客户性别人数进行统计;</p>
                <p>2.散客按未知性别，数量为1进行统计</p>
                <el-button type="text" style="color: #dcdfe6; padding: 0" icon="el-icon-info" slot="reference"></el-button>
              </el-popover>
            </template>
            <el-table-column label="女客" prop="WomanCount" align="right"></el-table-column>
            <el-table-column label="男客" prop="ManCount" align="right"></el-table-column>
            <el-table-column label="未知" prop="UnknownCount" align="right"></el-table-column>
          </el-table-column>
          <el-table-column label="客户构成" align="center">
            <template slot="header">
              客户构成
              <el-popover placement="top-start" trigger="hover">
                <p>在统计时间内，按顾客的身份信息(散客/非会员/会员)进行统计</p>
                <el-button type="text" style="color: #dcdfe6; padding: 0" icon="el-icon-info" slot="reference"></el-button>
              </el-popover>
            </template>
            <el-table-column label="散客" prop="IndividualTravelerCount" align="right"></el-table-column>
            <el-table-column label="散客占比" prop="IndividualTravelerRate" align="right">
              <template slot-scope="scope">
                <span v-if="scope.row.IndividualTravelerRate > 0">{{ scope.row.IndividualTravelerRate | toFixed | NumFormat }}%</span>
                <span v-else>{{ scope.row.IndividualTravelerRate }}</span>
              </template>
            </el-table-column>
            <el-table-column label="会员" prop="MemberCount" align="right"></el-table-column>
            <el-table-column label="会员占比" prop="MemberRate" align="right">
              <template slot-scope="scope">
                <span v-if="scope.row.MemberRate > 0">{{ scope.row.MemberRate | toFixed | NumFormat }}%</span>
                <span v-else>{{ scope.row.MemberRate }}</span>
              </template>
            </el-table-column>
            <el-table-column label="非会员" prop="UnMemberCount" align="right"></el-table-column>
            <el-table-column label="非会员占比" prop="UnMemberRate" align="right" width="90">
              <template slot-scope="scope">
                <span v-if="scope.row.UnMemberRate > 0">{{ scope.row.UnMemberRate | toFixed | NumFormat }}%</span>
                <span v-else>{{ scope.row.UnMemberRate }}</span>
              </template>
            </el-table-column>
          </el-table-column>
        </el-table>
        <!-- 分页 -->
        <div class="pad_15 text_right">
          <el-pagination background v-if="paginations_sale.total > 0" @current-change="handleSalePageChange" :current-page.sync="paginations_sale.page" :page-size="paginations_sale.page_size" :layout="paginations_sale.layout" :total="paginations_sale.total"></el-pagination>
        </div>
      </el-tab-pane>
      <el-tab-pane label="消耗客流" name="3">
        <!-- 搜索 -->
        <div class="nav_header">
          <el-form :inline="true" size="small" @submit.native.prevent>
            <el-form-item v-if="EntityList.length > 1" label="门店">
              <el-select v-model="searchData_treat.EntityID" clearable filterable placeholder="请选择门店" :default-first-option="true" @change="handleSearch_treat">
                <el-option v-for="item in EntityList" :key="item.ID" :label="item.EntityName" :value="item.ID"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="时间筛选">
              <el-date-picker v-model="searchData_treat.QueryDate" unlink-panels type="daterange" range-separator="至" value-format="yyyy-MM-dd" start-placeholder="开始日期" end-placeholder="结束日期" @change="handleSearch_treat" :picker-options="pickerOptions"></el-date-picker>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" v-prevent-click @click="handleSearch_treat">搜索</el-button>
            </el-form-item>
            <el-form-item>
              <el-button v-if="isExport" type="primary" size="small" v-prevent-click :loading="downloadLoading" @click="downloadSalePayExcel">导出</el-button>
            </el-form-item>
          </el-form>
        </div>
        <!-- 表格 -->
        <el-table :data="tableData_treat" size="small" show-summary :summary-method="getTreatSummary">
          <el-table-column label="门店" prop="EntityName" fixed width="150"></el-table-column>

          <el-table-column label="客户数" prop="CustomerCount" align="right">
            <template slot="header">
              客户数
              <el-popover placement="top-start" trigger="hover">
                <p>1.统计时间内包含销售、消耗订单的客户数；</p>
                <p>2.散客订单按订单数统计，同一客户在统计时间内去重</p>
                <el-button type="text" style="color: #dcdfe6; padding: 0" icon="el-icon-info" slot="reference"></el-button>
              </el-popover>
            </template>
          </el-table-column>
          <el-table-column label="客流数" prop="PassengerFlowCount" align="right">
            <template slot="header">
              客流数
              <el-popover placement="top-start" trigger="hover">
                <p>1. 统计时间内包含销售、消耗订单的客户数;</p>
                <p>2. 散客订单按订单数统计，同一客户当天去重，隔天累加</p>
                <el-button type="text" style="color: #dcdfe6; padding: 0" icon="el-icon-info" slot="reference"></el-button>
              </el-popover>
            </template>
          </el-table-column>

          <el-table-column label="性别" align="center">
            <template slot="header">
              性别
              <el-popover placement="top-start" trigger="hover">
                <p>1.在统计时间内，按客户性别人数进行统计;</p>
                <p>2.散客按未知性别，数量为1进行统计</p>
                <el-button type="text" style="color: #dcdfe6; padding: 0" icon="el-icon-info" slot="reference"></el-button>
              </el-popover>
            </template>
            <el-table-column label="女客" prop="WomanCount" align="right"></el-table-column>
            <el-table-column label="男客" prop="ManCount" align="right"></el-table-column>
            <el-table-column label="未知" prop="UnknownCount" align="right"></el-table-column>
          </el-table-column>
          <el-table-column label="客户构成" align="center">
            <template slot="header">
              客户构成
              <el-popover placement="top-start" trigger="hover">
                <p>在统计时间内，按顾客的身份信息(散客/非会员/会员)进行统计</p>
                <el-button type="text" style="color: #dcdfe6; padding: 0" icon="el-icon-info" slot="reference"></el-button>
              </el-popover>
            </template>
            <el-table-column label="散客" prop="IndividualTravelerCount" align="right"></el-table-column>
            <el-table-column label="散客占比" prop="IndividualTravelerRate" align="right">
              <template slot-scope="scope">
                <span v-if="scope.row.IndividualTravelerRate > 0">{{ scope.row.IndividualTravelerRate | toFixed | NumFormat }}%</span>
                <span v-else>{{ scope.row.IndividualTravelerRate }}</span>
              </template>
            </el-table-column>
            <el-table-column label="会员" prop="MemberCount" align="right"></el-table-column>
            <el-table-column label="会员占比" prop="MemberRate" align="right">
              <template slot-scope="scope">
                <span v-if="scope.row.MemberRate > 0">{{ scope.row.MemberRate | toFixed | NumFormat }}%</span>
                <span v-else>{{ scope.row.MemberRate }}</span>
              </template>
            </el-table-column>
            <el-table-column label="非会员" prop="UnMemberCount" align="right"></el-table-column>
            <el-table-column label="非会员占比" prop="UnMemberRate" align="right" width="90">
              <template slot-scope="scope">
                <span v-if="scope.row.UnMemberRate > 0">{{ scope.row.UnMemberRate | toFixed | NumFormat }}%</span>
                <span v-else>{{ scope.row.UnMemberRate }}</span>
              </template>
            </el-table-column>
          </el-table-column>
        </el-table>
        <!-- 分页 -->
        <div class="pad_15 text_right">
          <el-pagination background v-if="paginations_treat.total > 0" @current-change="handleTreatPageChange" :current-page.sync="paginations_treat.page" :page-size="paginations_treat.page_size" :layout="paginations_treat.layout" :total="paginations_treat.total"></el-pagination>
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import API from "@/api/Report/Entity/entityFlow";
import APIStorage from "@/api/PSI/Purchase/storage";
const dayjs = require("dayjs");
const isoWeek = require("dayjs/plugin/isoWeek");
dayjs.extend(isoWeek);

export default {
  name: "ReportEntityFlow",

  beforeRouteEnter(to, from, next) {
    next((vm) => {
      vm.isExport = vm.$permission.permission(to.meta.Permission, "Report-Entity-EntityFlow-Export");
    });
  },
  props: {},
  /** 监听数据变化   */
  watch: {},
  /**  引入的组件  */
  components: {},
  /**  Vue 实例的数据对象**/
  data() {
    return {
      activeName: "1",
      isExport: false,
      loading: false,
      downloadLoading: false,
      EntityList: [], //门店数据
      tableData: [], // 表格数据
      SumOutputForm: {}, // 合计数据
      searchData: {
        EntityID: null,
        QueryDate: [this.$formatDate(new Date(), "YYYY-MM-DD"), this.$formatDate(new Date(), "YYYY-MM-DD")],
      },
      paginations: {
        page: 1, // 当前位于哪页
        total: 0, // 总数
        page_size: 10, // 1页显示多少条
        layout: "total, prev, pager, next,jumper", // 翻页属性
      },
      pickerOptions: {
        shortcuts: [
          {
            text: "今天",
            onClick: (picker) => {
              let end = new Date();
              let start = new Date();
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "本周",
            onClick: (picker) => {
              let end = new Date();
              let weekDay = dayjs(end).isoWeekday();
              let start = dayjs(end)
                .subtract(weekDay - 1, "day")
                .toDate();
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "本月",
            onClick: (picker) => {
              let end = new Date();
              let start = dayjs(dayjs(end).format("YYYY-MM") + "01").toDate();
              picker.$emit("pick", [start, end]);
            },
          },
        ],
      },

      tableData_sale: [], // 表格数据
      SumOutputForm_slae: {}, // 合计数据
      searchData_sale: {
        EntityID: null,
        QueryDate: [this.$formatDate(new Date(), "YYYY-MM-DD"), this.$formatDate(new Date(), "YYYY-MM-DD")],
      },
      paginations_sale: {
        page: 1, // 当前位于哪页
        total: 0, // 总数
        page_size: 10, // 1页显示多少条
        layout: "total, prev, pager, next,jumper", // 翻页属性
      },

      tableData_treat: [], // 表格数据
      SumOutputForm_treat: {}, // 合计数据
      searchData_treat: {
        EntityID: null,
        QueryDate: [this.$formatDate(new Date(), "YYYY-MM-DD"), this.$formatDate(new Date(), "YYYY-MM-DD")],
      },
      paginations_treat: {
        page: 1, // 当前位于哪页
        total: 0, // 总数
        page_size: 10, // 1页显示多少条
        layout: "total, prev, pager, next,jumper", // 翻页属性
      },
    };
  },
  /**计算属性  */
  computed: {},
  /**  方法集合  */
  methods: {
    /**    */
    handleTreatPageChange(page) {
      let that = this;
      that.paginations_sale.page = page;
      that.entityPassengerFlow_treatList();
    },
    /* 分页 */
    handleSearch_treat() {
      let that = this;
      that.paginations.page = 1;
      that.entityPassengerFlow_treatList();
    },

    /**    */
    getTreatSummary({ columns }) {
      let sums = [];
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = <span class="font_weight_600">合计</span>;
          return;
        }
        switch (column.property) {
          case "CustomerCount":
            sums[index] = <span class="font_weight_600">{this.SumOutputForm_treat ? this.SumOutputForm_treat.CustomerCount : 0}</span>;
            break;
          case "PassengerFlowCount":
            sums[index] = <span class="font_weight_600">{this.SumOutputForm_treat ? this.SumOutputForm_treat.PassengerFlowCount : 0}</span>;
            break;
          case "ManCount":
            sums[index] = <span class="font_weight_600">{this.SumOutputForm_treat ? this.SumOutputForm_treat.ManCount : 0}</span>;
            break;
          case "WomanCount":
            sums[index] = <span class="font_weight_600">{this.SumOutputForm_treat ? this.SumOutputForm_treat.WomanCount : 0}</span>;
            break;
          case "UnknownCount":
            sums[index] = <span class="font_weight_600">{this.SumOutputForm_treat ? this.SumOutputForm_treat.UnknownCount : 0}</span>;
            break;
          case "IndividualTravelerCount":
            sums[index] = <span class="font_weight_600">{this.SumOutputForm_treat ? this.SumOutputForm_treat.IndividualTravelerCount : 0}</span>;
            break;
          case "IndividualTravelerRate":
            sums[index] = <span class="font_weight_600">{this.SumOutputForm_treat ? this.SumOutputForm_treat.IndividualTravelerRate : 0}</span>;
            break;
          case "MemberCount":
            sums[index] = <span class="font_weight_600">{this.SumOutputForm_treat ? this.SumOutputForm_treat.MemberCount : 0}</span>;
            break;
          case "MemberRate":
            sums[index] = <span class="font_weight_600">{this.SumOutputForm_treat ? this.SumOutputForm_treat.MemberRate : 0}</span>;
            break;
          case "UnMemberCount":
            sums[index] = <span class="font_weight_600">{this.SumOutputForm_treat ? this.SumOutputForm_treat.UnMemberCount : 0}</span>;
            break;
          case "UnMemberRate":
            sums[index] = <span class="font_weight_600">{this.SumOutputForm_treat ? this.SumOutputForm_treat.UnMemberRate : 0}</span>;
            break;
        }
      });
      return sums;
    },
    /**    */
    handleSalePageChange(page) {
      let that = this;
      that.paginations_sale.page = page;
      that.entityPassengerFlow_saleList();
    },
    /* 分页 */
    handleSearch_sale() {
      let that = this;
      that.paginations.page = 1;
      that.entityPassengerFlow_saleList();
    },
    /**    */
    getSummarySale({ columns }) {
      let sums = [];
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = <span class="font_weight_600">合计</span>;
          return;
        }
        switch (column.property) {
          case "CustomerCount":
            sums[index] = <span class="font_weight_600">{this.SumOutputForm_slae ? this.SumOutputForm_slae.CustomerCount : 0}</span>;
            break;
          case "PassengerFlowCount":
            sums[index] = <span class="font_weight_600">{this.SumOutputForm_slae ? this.SumOutputForm_slae.PassengerFlowCount : 0}</span>;
            break;
          case "DealNewCount":
            sums[index] = <span class="font_weight_600">{this.SumOutputForm_slae ? this.SumOutputForm_slae.DealNewCount : 0}</span>;
            break;
          case "DealOldCount":
            sums[index] = <span class="font_weight_600">{this.SumOutputForm_slae ? this.SumOutputForm_slae.DealOldCount : 0}</span>;
            break;
          case "ManCount":
            sums[index] = <span class="font_weight_600">{this.SumOutputForm_slae ? this.SumOutputForm_slae.ManCount : 0}</span>;
            break;
          case "WomanCount":
            sums[index] = <span class="font_weight_600">{this.SumOutputForm_slae ? this.SumOutputForm_slae.WomanCount : 0}</span>;
            break;
          case "NewCustomerCount":
            sums[index] = <span class="font_weight_600">{this.SumOutputForm_slae ? this.SumOutputForm_slae.NewCustomerCount : 0}</span>;
            break;
          case "UnknownCount":
            sums[index] = <span class="font_weight_600">{this.SumOutputForm_slae ? this.SumOutputForm_slae.UnknownCount : 0}</span>;
            break;
          case "IndividualTravelerCount":
            sums[index] = <span class="font_weight_600">{this.SumOutputForm_slae ? this.SumOutputForm_slae.IndividualTravelerCount : 0}</span>;
            break;
          case "IndividualTravelerRate":
            sums[index] = <span class="font_weight_600">{this.SumOutputForm_slae ? this.SumOutputForm_slae.IndividualTravelerRate : 0}</span>;
            break;
          case "MemberCount":
            sums[index] = <span class="font_weight_600">{this.SumOutputForm_slae ? this.SumOutputForm_slae.MemberCount : 0}</span>;
            break;
          case "MemberRate":
            sums[index] = <span class="font_weight_600">{this.SumOutputForm_slae ? this.SumOutputForm_slae.MemberRate : 0}</span>;
            break;
          case "UnMemberCount":
            sums[index] = <span class="font_weight_600">{this.SumOutputForm_slae ? this.SumOutputForm_slae.UnMemberCount : 0}</span>;
            break;
          case "UnMemberRate":
            sums[index] = <span class="font_weight_600">{this.SumOutputForm_slae ? this.SumOutputForm_slae.UnMemberRate : 0}</span>;
            break;
        }
      });
      return sums;
    },

    /* 搜索 */
    handleSearch() {
      let that = this;
      that.paginations.page = 1;
      that.getEntityPassengerFlowList();
    },
    /* 分页 */
    handlePageChange(page) {
      let that = this;
      that.paginations.page = page;
      that.getEntityPassengerFlowList();
    },
    /* 导出 */
    downloadSalePayExcel() {
      let that = this;
      if (that.searchData.QueryDate != null) {
        if (dayjs(that.searchData.QueryDate[0]).add(366, "day").valueOf() < dayjs(that.searchData.QueryDate[1]).valueOf()) {
          that.$message.error("时间筛选范围不能超366天");
          return;
        }
        let params = {
          EntityID: that.searchData.EntityID,
          StartDate: that.searchData.QueryDate[0],
          EndDate: that.searchData.QueryDate[1],
        };
        that.downloadLoading = true;
        API.entityPassengerFlowExcel(params)
          .then((res) => {
            this.$message.success({
              message: "正在导出",
              duration: "4000",
            });
            const link = document.createElement("a");
            let blob = new Blob([res], { type: "application/octet-stream" });
            link.style.display = "none";
            link.href = URL.createObjectURL(blob);
            link.download = "门店客流报表.xlsx"; //下载的文件名
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
          })
          .finally(function () {
            that.downloadLoading = false;
          });
      } else {
        this.$message.error({
          message: "请选择筛选时间",
          duration: 2000,
        });
      }
    },
    /* 获取表格数据 */
    getEntityPassengerFlowList() {
      let that = this;
      if (that.searchData.QueryDate != null) {
        if (dayjs(that.searchData.QueryDate[0]).add(366, "day").valueOf() < dayjs(that.searchData.QueryDate[1]).valueOf()) {
          that.$message.error("时间筛选范围不能超366天");
          return;
        }
        that.loading = true;
        let params = {
          EntityID: that.searchData.EntityID,
          StartDate: that.searchData.QueryDate[0],
          EndDate: that.searchData.QueryDate[1],
          PageNum: that.paginations.page,
        };
        API.getEntityPassengerFlowList(params)
          .then((res) => {
            if (res.StateCode == 200) {
              that.tableData = res.Data.Detail.List;
              that.paginations.page_size = res.Data.Detail.PageSize;
              that.paginations.total = res.Data.Detail.Total;
              that.SumOutputForm = res.Data.SumOutputForm;
            } else {
              this.$message.error({
                message: res.Message,
                duration: 2000,
              });
            }
          })
          .finally(function () {
            that.loading = false;
          });
      } else {
        this.$message.error({
          message: "请选择筛选时间",
          duration: 2000,
        });
      }
    },
    /* 合计 */
    getSummary({ columns }) {
      let sums = [];
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = <span class="font_weight_600">合计</span>;
          return;
        }
        switch (column.property) {
          case "CustomerCount":
            sums[index] = <span class="font_weight_600">{this.SumOutputForm ? this.SumOutputForm.CustomerCount : 0}</span>;
            break;
          case "CustomerSaleCount":
            sums[index] = <span class="font_weight_600">{this.SumOutputForm ? this.SumOutputForm.CustomerSaleCount : 0}</span>;
            break;
          case "CustomerTreatCount":
            sums[index] = <span class="font_weight_600">{this.SumOutputForm ? this.SumOutputForm.CustomerTreatCount : 0}</span>;
            break;
          case "PassengerFlowCount":
            sums[index] = <span class="font_weight_600">{this.SumOutputForm ? this.SumOutputForm.PassengerFlowCount : 0}</span>;
            break;
          case "PassengerFlowSaleCount":
            sums[index] = <span class="font_weight_600">{this.SumOutputForm ? this.SumOutputForm.PassengerFlowSaleCount : 0}</span>;
            break;
          case "PassengerFlowTreatCount":
            sums[index] = <span class="font_weight_600">{this.SumOutputForm ? this.SumOutputForm.PassengerFlowTreatCount : 0}</span>;
            break;
          case "NewCustomerCount":
            sums[index] = <span class="font_weight_600">{this.SumOutputForm ? this.SumOutputForm.NewCustomerCount : 0}</span>;
            break;
          case "NewCustomerMemberCount":
            sums[index] = <span class="font_weight_600">{this.SumOutputForm ? this.SumOutputForm.NewCustomerMemberCount : 0}</span>;
            break;
          case "DealNewCount":
            sums[index] = <span class="font_weight_600">{this.SumOutputForm ? this.SumOutputForm.DealNewCount : 0}</span>;
            break;
          case "DealOldCount":
            sums[index] = <span class="font_weight_600">{this.SumOutputForm ? this.SumOutputForm.DealOldCount : 0}</span>;
            break;
          case "ManCount":
            sums[index] = <span class="font_weight_600">{this.SumOutputForm ? this.SumOutputForm.ManCount : 0}</span>;
            break;
          case "WomanCount":
            sums[index] = <span class="font_weight_600">{this.SumOutputForm ? this.SumOutputForm.WomanCount : 0}</span>;
            break;
          case "UnknownCount":
            sums[index] = <span class="font_weight_600">{this.SumOutputForm ? this.SumOutputForm.UnknownCount : 0}</span>;
            break;
          case "IndividualTravelerCount":
            sums[index] = <span class="font_weight_600">{this.SumOutputForm ? this.SumOutputForm.IndividualTravelerCount : 0}</span>;
            break;
          case "IndividualTravelerRate":
            sums[index] = <span class="font_weight_600">{this.SumOutputForm && this.SumOutputForm.IndividualTravelerRate > 0 ? this.SumOutputForm.IndividualTravelerRate + "%" : 0}</span>;
            break;
          case "MemberCount":
            sums[index] = <span class="font_weight_600">{this.SumOutputForm ? this.SumOutputForm.MemberCount : 0}</span>;
            break;
          case "MemberRate":
            sums[index] = <span class="font_weight_600">{this.SumOutputForm && this.SumOutputForm.MemberRate > 0 ? this.SumOutputForm.MemberRate + "%" : 0}</span>;
            break;
          case "UnMemberCount":
            sums[index] = <span class="font_weight_600">{this.SumOutputForm ? this.SumOutputForm.UnMemberCount : 0}</span>;
            break;
          case "UnMemberRate":
            sums[index] = <span class="font_weight_600">{this.SumOutputForm && this.SumOutputForm.UnMemberRate > 0 ? this.SumOutputForm.UnMemberRate + "%" : 0}</span>;
            break;
        }
      });
      return sums;
    },
    /* 获取门店 */
    async getStorageEntityNetwork() {
      var that = this;
      let res = await APIStorage.getpurchaseStorageEntity();
      if (res.StateCode == 200) {
        that.EntityList = res.Data;
      } else {
        that.$message.error({
          message: res.Message,
          duration: 2000,
        });
      }
    },

    /* 导出 */
    entityPassengerFlow_saleExcel() {
      let that = this;
      if (that.searchData_sale.QueryDate != null) {
        if (dayjs(that.searchData_sale.QueryDate[0]).add(366, "day").valueOf() < dayjs(that.searchData_sale.QueryDate[1]).valueOf()) {
          that.$message.error("时间筛选范围不能超366天");
          return;
        }
        let params = {
          EntityID: that.searchData_sale.EntityID,
          StartDate: that.searchData_sale.QueryDate[0],
          EndDate: that.searchData_sale.QueryDate[1],
        };
        that.downloadLoading = true;
        API.entityPassengerFlow_saleExcel(params)
          .then((res) => {
            this.$message.success({
              message: "正在导出",
              duration: "4000",
            });
            const link = document.createElement("a");
            let blob = new Blob([res], { type: "application/octet-stream" });
            link.style.display = "none";
            link.href = URL.createObjectURL(blob);
            link.download = "门店客流报表.xlsx"; //下载的文件名
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
          })
          .finally(function () {
            that.downloadLoading = false;
          });
      } else {
        this.$message.error({
          message: "请选择筛选时间",
          duration: 2000,
        });
      }
    },
    /* 销售 */
    entityPassengerFlow_saleList() {
      let that = this;
      if (that.searchData_sale.QueryDate != null) {
        if (dayjs(that.searchData_sale.QueryDate[0]).add(366, "day").valueOf() < dayjs(that.searchData_sale.QueryDate[1]).valueOf()) {
          that.$message.error("时间筛选范围不能超366天");
          return;
        }
        that.loading = true;
        let params = {
          EntityID: that.searchData_sale.EntityID,
          StartDate: that.searchData_sale.QueryDate[0],
          EndDate: that.searchData_sale.QueryDate[1],
          PageNum: that.paginations_sale.page,
        };
        API.entityPassengerFlow_saleList(params)
          .then((res) => {
            if (res.StateCode == 200) {
              that.tableData_sale = res.Data.Detail.List;
              that.paginations_sale.page_size = res.Data.Detail.PageSize;
              that.paginations_sale.total = res.Data.Detail.Total;
              that.SumOutputForm_slae = res.Data.SumOutputForm;
            } else {
              this.$message.error({
                message: res.Message,
                duration: 2000,
              });
            }
          })
          .finally(function () {
            that.loading = false;
          });
      } else {
        this.$message.error({
          message: "请选择筛选时间",
          duration: 2000,
        });
      }
    },

    /* 导出 */
    entityPassengerFlow_treatExcel() {
      let that = this;
      if (that.searchData_treat.QueryDate != null) {
        if (dayjs(that.searchData_treat.QueryDate[0]).add(366, "day").valueOf() < dayjs(that.searchData_treat.QueryDate[1]).valueOf()) {
          that.$message.error("时间筛选范围不能超366天");
          return;
        }
        let params = {
          EntityID: that.searchData_treat.EntityID,
          StartDate: that.searchData_treat.QueryDate[0],
          EndDate: that.searchData_treat.QueryDate[1],
        };
        that.downloadLoading = true;
        API.entityPassengerFlow_treatExcel(params)
          .then((res) => {
            this.$message.success({
              message: "正在导出",
              duration: "4000",
            });
            const link = document.createElement("a");
            let blob = new Blob([res], { type: "application/octet-stream" });
            link.style.display = "none";
            link.href = URL.createObjectURL(blob);
            link.download = "门店客流报表.xlsx"; //下载的文件名
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
          })
          .finally(function () {
            that.downloadLoading = false;
          });
      } else {
        this.$message.error({
          message: "请选择筛选时间",
          duration: 2000,
        });
      }
    },
    /* 消耗 */
    entityPassengerFlow_treatList() {
      let that = this;
      if (that.searchData_treat.QueryDate != null) {
        if (dayjs(that.searchData_treat.QueryDate[0]).add(366, "day").valueOf() < dayjs(that.searchData_treat.QueryDate[1]).valueOf()) {
          that.$message.error("时间筛选范围不能超366天");
          return;
        }
        that.loading = true;
        let params = {
          EntityID: that.searchData_treat.EntityID,
          StartDate: that.searchData_treat.QueryDate[0],
          EndDate: that.searchData_treat.QueryDate[1],
          PageNum: that.paginations_treat.page,
        };
        API.entityPassengerFlow_treatList(params)
          .then((res) => {
            if (res.StateCode == 200) {
              that.tableData_treat = res.Data.Detail.List;
              that.paginations_treat.page_size = res.Data.Detail.PageSize;
              that.paginations_treat.total = res.Data.Detail.Total;
              that.SumOutputForm_treat = res.Data.SumOutputForm;
            } else {
              this.$message.error({
                message: res.Message,
                duration: 2000,
              });
            }
          })
          .finally(function () {
            that.loading = false;
          });
      } else {
        this.$message.error({
          message: "请选择筛选时间",
          duration: 2000,
        });
      }
    },
  },
  /** 创建实例之前执行函数   */
  beforeCreate() {},
  /**  实例创建完成之后  */
  created() {},
  /**  在挂载开始之前被调用  */
  beforeMount() {},
  /**  实例被挂载后调用  */
  mounted() {
    let that = this;
    this.isExport = this.$permission.permission(this.$route.meta.Permission, "Report-Entity-EntityFlow-Export");
    that.getStorageEntityNetwork();
    that.getEntityPassengerFlowList();
    that.entityPassengerFlow_saleList();
    that.entityPassengerFlow_treatList();
  },
  /**  数据更新 之前 调用  */
  beforeUpdate() {},
  /** 数据更新 完成 调用   */
  updated() {},
  /**  实例销毁之前调用  */
  beforeDestroy() {},
  /**  实例销毁后调用  */
  destroyed() {},
};
</script>

<style lang="scss">
.entityFlow {
  .el-tabs--border-card {
    border: 0px !important;
    box-shadow: 0 0px 0px 0 rgba(0, 0, 0, 0), 0 0 0px 0 rgba(0, 0, 0, 0);
  }
}
</style>
