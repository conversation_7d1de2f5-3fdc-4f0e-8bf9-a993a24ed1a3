/**
 * Created by preference on 2021/11/27
 *  zmx 
 */

import * as API from '@/api/index'
export default {
  /** 列表  */
  customerFileList: params => {
    return API.POST('api/customer/file', params)
  },
  /** 视频上传  */
  uploadVideoAndAudio: params => {
    return API.importFile('api/customerFile/uploadVideoAndAudio', params)
  },
  /** 上传 图片 文件 以 base64 形式  */
  customerFile_create: params => {
    return API.POST('api/customerFile/create', params)
  },
  // 获取类型列表
  getCustomerFileCategory: params => {
    return API.POST('api/customerFileCategory/all',params)
  },
  // 删除
  customerFile_deleteFile: params => {
    return API.POST('api/customer/deleteFile',params)
  },
  // 上传文件 
  customerFile_uploadFile: params => {
    return API.importFile('api/customerFile/uploadFile ',params)
  },
  // 下载文件down
  customerFile_downFile: params => {

    return API.exportExcel('api/customer/getFile',params)
  },
  
  
}