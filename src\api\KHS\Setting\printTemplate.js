/**
 * Created by preference on 2021/12/03
 *  zmx 
 */

import * as API from '@/api/index'
  export default {
  /**  模板列表 */
  template_list: params => {
    return API.POST('api/template/list', params)
  },
  /** 删除模板  */
  template_delete: params => {
    return API.POST('api/template/delete', params)
  },
  /**  修改模板  */
  template_updatet: params => {
    return API.POST('api/template/update', params)
  },
  /** 新增模板  */
  template_create: params => {
    return API.POST('api/template/create', params)
  },
  /** 获取默认模板  */
  template_defaultTemplate: params => {
    return API.POST('api/template/defaultTemplate', params)
  },
  /** 移动  */
  template_move: params => {
    return API.POST('api/template/move', params)
  },
}