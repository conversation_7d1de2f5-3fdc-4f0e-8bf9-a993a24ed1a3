/**
 * Created by preference on 2022/08/23
 *  zmx
 */

import * as API from "@/api/index";
export default {
  /** 耗材分  */
  ProjectConsumableTemplate_allCategory: (params) => {
    return API.POST("api/ProjectConsumableTemplate/allCategory", params);
  },
  /** 耗材分类平铺列表  */
  ProjectConsumableTemplate_listCategory: (params) => {
    return API.POST("api/ProjectConsumableTemplate/listCategory", params);
  },
  /** 创建分类  */
  ProjectConsumableTemplate_createCategory: (params) => {
    return API.POST("api/ProjectConsumableTemplate/createCategory", params);
  },
  /** 更新分类  */
  ProjectConsumableTemplate_updateCategory: (params) => {
    return API.POST("api/ProjectConsumableTemplate/updateCategory", params);
  },
  /**  耗材模板 */
  ProjectConsumableTemplate_all: (params) => {
    return API.POST("api/ProjectConsumableTemplate/all", params);
  },
  /**  新建耗材模板  */
  ProjectConsumableTemplate_create: (params) => {
    return API.POST("api/ProjectConsumableTemplate/create", params);
  },
  /** 更新耗材模板  */
  ProjectConsumableTemplate_update: (params) => {
    return API.POST("api/ProjectConsumableTemplate/update", params);
  },
  /** 耗材模板详细  */
  ProjectConsumableTemplate_detail: (params) => {
    return API.POST("api/ProjectConsumableTemplate/detail", params);
  },
  /** 配置项目  */
  ProjectConsumableTemplate_findCategoryAndProject: (params) => {
    return API.POST(
      "api/ProjectConsumableTemplate/findCategoryAndProject",
      params
    );
  },

  /** 配置产品  */
  treatBill_product: (params) => {
    return API.POST("api/treatBill/product", params);
  },
};
