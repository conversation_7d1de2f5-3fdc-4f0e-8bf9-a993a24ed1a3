<template>
  <div class="channelType content_body" v-loading="loading">
    <div class="nav_header">
      <el-row>
        <el-col :span="20">
          <el-form :model="searchForm" size="small" @keyup.enter.native="handleSearch" :inline="true">
            <el-form-item label="渠道类型">
              <el-input size="small" v-model="searchForm.name" placeholder="输入渠道类型搜索" clearable @clear="handleSearch"></el-input>
            </el-form-item>
            <el-form-item label="有效性">
              <el-select size="small" v-model="searchForm.active" placeholder="请选择" clearable @change="handleSearch" @clear="handleSearch">
                <el-option :value="true" label="有效"></el-option>
                <el-option :value="false" label="无效"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleSearch" size="small">搜索</el-button>
            </el-form-item>
          </el-form>
        </el-col>
        <el-col :span="4" class="text_right">
          <el-button type="primary" @click="addChannelTypeVisible" size="small" v-prevent-click>新增</el-button>
        </el-col>
      </el-row>
    </div>

    <div>
      <el-table :data="tableData" size="small">
        <el-table-column prop="Name" label="渠道类型"> </el-table-column>
        <el-table-column label="移动" min-width="150px">
          <template slot-scope="scope">
            <el-button
              size="small"
              type="primary"
              circle
              icon="el-icon-upload2"
              @click="upOneClick(scope.row, scope.$index)"
              :disabled="scope.$index == 0"
            ></el-button>
            <el-button
              size="small"
              type="primary"
              circle
              icon="el-icon-top"
              @click="upClick(scope.row, scope.$index)"
              :disabled="scope.$index == 0"
            ></el-button>
            <el-button
              size="small"
              type="primary"
              circle
              icon="el-icon-bottom"
              @click="downClick(scope.row, scope.$index)"
              :disabled="scope.$index == tableData.length - 1"
            ></el-button>
            <el-button
              size="small"
              type="primary"
              circle
              icon="el-icon-download"
              @click="downOneClick(scope.row, scope.$index)"
              :disabled="scope.$index == tableData.length - 1"
            ></el-button>
          </template>
        </el-table-column>
        <el-table-column prop="Active" label="有效性">
          <template slot-scope="scope">
            {{ scope.row.Active == true ? "有效" : "无效" }}
          </template>
        </el-table-column>
        <el-table-column prop="date" label="操作" width="80">
          <template slot-scope="scope">
            <el-button type="primary" size="small" @click="showEditDialog(scope.row)" v-prevent-click>编辑</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <el-dialog :title="isEdit ? '编辑渠道类型' : '新增渠道类型'" :visible.sync="dialogVisible" width="450px">
      <div>
        <el-form ref="ruleForm" :rules="rules" :model="addChannel" label-width="auto" size="small" @submit.native.prevent>
          <el-form-item label="渠道类型" prop="Name">
            <el-input v-model="addChannel.Name" placeholder="请输入渠道类型"></el-input>
          </el-form-item>
          <el-form-item label="有效性" prop="Active" v-if="isEdit">
            <el-radio-group v-model="addChannel.Active">
              <el-radio :label="true">有效</el-radio>
              <el-radio :label="false">无效</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false" size="small">取 消</el-button>
        <el-button type="primary" :loading="modalLoading" @click="addChannelType" v-prevent-click size="small">保 存</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import API from "@/api/CRM/Channel/channelType";
export default {
  name: "channelType",
  props: {},
  /**  引入的组件  */
  components: {},
  /**  Vue 实例的数据对象**/
  data() {
    return {
      isEdit: false,
      loading: false,
      modalLoading: false,
      //搜索条件
      searchForm: {
        name: "",
        active: true,
      },
      tableData: [], //列表数据
      dialogVisible: false,
      addChannel: {
        Name: "", //新增的类型
        Active: "",
      },

      ruleForm: {
        name: "",
      },
      rules: {
        Name: [
          { required: true, message: "请输入渠道类型", trigger: "blur" },
          { min: 1, max: 10, message: "长度在 1 到 10 个字符", trigger: "blur" },
        ],
        Active: [{ required: true, message: "请选择有效性", trigger: "change" }],
      },
    };
  },
  /**计算属性  */
  computed: {},
  /**  方法集合  */
  methods: {
    // 数据显示
    handleSearch: function () {
      let that = this;
      that.search();
    },
    search() {
      var that = this;
      var params = {
        Name: that.searchForm.name,
        Active: that.searchForm.active,
      };
      API.getChannelTypeList(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.tableData = res.Data;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },
    //打开新增弹框
    addChannelTypeVisible() {
      var that = this;
      (that.addChannel = {
        Name: "",
        Active: "",
      }),
        (that.isEdit = false);
      if (this.$refs.ruleForm) {
        this.$refs.ruleForm.clearValidate();
      }
      that.dialogVisible = true;
    },
    //验证新增
    addChannelType() {
      var that = this;
      that.$refs.ruleForm.validate((valid) => {
        if (!valid) return;
        if (that.isEdit) {
          that.updateChannelType();
        } else {
          that.createChannelType();
        }
      });
    },
    //新增
    createChannelType() {
      var that = this;
      that.modalLoading = true;
      let params = that.addChannel;
      params.Active = true;
      API.createChannelType(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.$message.success("新增成功");
            that.dialogVisible = false;
            that.search();
          }
          else{
            that.$message.error(res.Message);
          }
        })
        .finally(function () {
          that.modalLoading = false;
        });
    },
    // 编辑
    showEditDialog(row) {
      let that = this;
      that.addChannel = Object.assign({}, row);
      that.isEdit = true;
      if (this.$refs.ruleForm) {
        this.$refs.ruleForm.clearValidate();
      }
      that.dialogVisible = true;
    },
    // 编辑保存
    updateChannelType() {
      let that = this;
      that.modalLoading = true;
      let params = that.addChannel;
      API.updateChannelType(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.$message.success("更新成功");
            that.dialogVisible = false;
            that.search();
          }
          else{
            that.$message.error(res.Message);
          }
        })
        .finally(function () {
          that.modalLoading = false;
        });
    },
    /* 移动首部 */
    upOneClick: function (row) {
      let that = this;
      that.moveClick(row.ID, "");
    },
    /* 移动尾部 */
    downOneClick: function (row, index) {
      let that = this;
      let beforeId = "";
      let tableLength = that.tableData.length;
      if (index < tableLength - 1) {
        beforeId = that.tableData[tableLength - 1].ID;
      }
      that.moveClick(row.ID, beforeId);
    },
    /* 向上 */
    upClick: function (row, index) {
      let that = this;
      let beforeId = "";
      if (index > 1) {
        beforeId = that.tableData[index - 2].ID;
      }
      that.moveClick(row.ID, beforeId);
    },
    /* 向下 */
    downClick: function (row, index) {
      let that = this;
      let beforeId = "";
      if (index + 1 != that.tableData.length) {
        beforeId = that.tableData[index + 1].ID;
      }
      that.moveClick(row.ID, beforeId);
    },
    /* 移动顺序 */
    moveClick: function (moveId, beforeId) {
      var that = this;
      that.loading = true;
      var params = {
        MoveID: moveId,
        BeforeID: beforeId,
      };
      API.moveChannelType(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.$message.success("移动成功");
            that.search();
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },
  },
  /** 监听数据变化   */
  watch: {},
  /** 创建实例之前执行函数   */
  beforeCreate() {},
  /**  实例创建完成之后  */
  created() {},
  /**  在挂载开始之前被调用  */
  beforeMount() {},
  /**  实例被挂载后调用  */
  mounted() {
    this.handleSearch();
  },
  /**  数据更新 之前 调用  */
  beforeUpdate() {},
  /** 数据更新 完成 调用   */
  updated() {},
  /**  实例销毁之前调用  */
  beforeDestroy() {},
  /**  实例销毁后调用  */
  destroyed() {},
};
</script>

<style lang="scss">
.channelType {
}
</style>
