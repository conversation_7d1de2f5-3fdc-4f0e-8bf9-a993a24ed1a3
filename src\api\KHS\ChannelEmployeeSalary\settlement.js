/**
 * Created by preference on 2022/08/31
 *  zmx
 */
import * as API from "@/api/index";
export default {
  /** 提成结算列表  */
  channelEmployeeSalarySheet_all: (params) => {
    return API.POST("api/channelEmployeeSalarySheet/all", params);
  },
  /**  提成结算导出 */
  channelEmployeeSalarySheet_excel: (params) => {
    return API.exportExcel("api/channelEmployeeSalarySheet/excel", params);
  },
  /**  个人提成详情 */
  channelEmployeeSalarySheet_employeeCommission: (params) => {
    return API.POST(
      "api/channelEmployeeSalarySheet/employeeCommission",
      params
    );
  },
  /** 团队提成详情  */
  channelEmployeeSalarySheet_teamCommission: (params) => {
    return API.POST("api/channelEmployeeSalarySheet/teamCommission", params);
  },
  /** 结算周期设置  */
  channelEmployeeSalarySheet_createSettlementInterval: (params) => {
    return API.POST(
      "api/channelEmployeeSalarySheet/createSettlementInterval",
      params
    );
  },
  /**  结算周期查询 */
  channelEmployeeSalarySheet_allSettlementInterval: (params) => {
    return API.POST(
      "api/channelEmployeeSalarySheet/allSettlementInterval",
      params
    );
  },
  /**  结算 */
  channelEmployeeSalarySheet_settlement: (params) => {
    return API.POST("api/channelEmployeeSalarySheet/settlement", params);
  },
};
