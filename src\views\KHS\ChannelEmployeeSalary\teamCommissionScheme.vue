<template>
  <div class="ChannelEmployeeTeamCommissionScheme content_body">
    <div class="nav_header">
      <el-row>
        <el-col :span="20">
          <el-form :inline="true" size="small" @submit.native.prevent>
            <el-form-item label="市场团队提成方案">
              <el-input
                @clear="handleSearch"
                v-model="searchData.Name"
                placeholder="输入市场团队提成方案搜索"
                clearable
                @keyup.enter.native="handleSearch"
              ></el-input>
            </el-form-item>

            <el-form-item label="市场业绩取值方案" prop="PerformanceSchemeID">
              <el-select v-model="searchData.PerformanceSchemeID" placeholder="请选择市场业绩取值方案" filterable clearable size="small" @change="handleSearch">
                <el-option v-for="item in performanceSchemeAllList" :key="item.ID" :label="item.Name" :value="item.ID"> </el-option>
              </el-select>
            </el-form-item>

            <el-form-item label="有效性">
              <el-select v-model="searchData.Active" placeholder="选择有效性" clearable size="small" @change="handleSearch">
                <el-option label="有效" :value="true"></el-option>
                <el-option label="无效" :value="false"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" size="small" @click="handleSearch" v-prevent-click>搜索</el-button>
            </el-form-item>
          </el-form>
        </el-col>
        <el-col :span="4" class="text_right">
          <el-button type="primary" size="small" @click="showAddDialog" v-prevent-click>新增</el-button>
        </el-col>
      </el-row>
    </div>
    <el-table size="small" :data="tableData">
      <el-table-column prop="Name" label="市场团队提成方案名称"></el-table-column>
      <el-table-column prop="PerformanceSchemeName" label="市场团队业绩取值方案"></el-table-column>
      <el-table-column prop="Calculation" label="计算方式">
        <template slot-scope="scope">
          <span>{{ scope.row.Calculation == 10 ? "阶梯式计算" : "阶段式计算" }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="Active" label="有效性">
        <template slot-scope="scope">
          {{ scope.row.Active ? "有效" : "无效" }}
        </template>
      </el-table-column>
      <el-table-column label="操作" width="80px">
        <template slot-scope="scope">
          <el-button type="primary" size="small" @click="showEditDialog(scope.row)" v-prevent-click>编辑</el-button>
        </template>
      </el-table-column>
    </el-table>
    <div class="page pad_10">
      <div class="text_right" v-if="paginations.total > 0">
        <el-pagination
          background
          @current-change="handleCurrentChange"
          :current-page.sync="paginations.page"
          :page-size="paginations.page_size"
          :layout="paginations.layout"
          :total="paginations.total"
        ></el-pagination>
      </div>
    </div>

    <el-dialog :title="isAdd ? '新增市场团队业绩取值方案' : '编辑市场团队业绩取值方案'" :visible.sync="dialogVisible" width="1000px">
      <el-tabs v-model="activeName">
        <el-tab-pane label="基本信息" name="Info">
          <el-scrollbar class="el-scrollbar_height">
            <el-form size="small" ref="addRuleFormRef" :model="addRuleForm" :rules="addRules" label-width="150px">
              <el-form-item label="提成方案名称" prop="Name">
                <el-input v-model="addRuleForm.Name" placeholder="请输入提成方案名称"></el-input>
              </el-form-item>

              <el-form-item label="市场业绩取值方案" prop="PerformanceSchemeID">
                <el-select v-model="addRuleForm.PerformanceSchemeID" placeholder="请选择市场业绩取值方案" filterable clearable size="small">
                  <el-option v-for="item in performanceSchemeAllList" :key="item.ID" :label="item.Name" :value="item.ID"> </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="计算方式">
                <span slot="label">
                  计算方式
                  <el-popover placement="top-start" width="850px" trigger="hover">
                    <p>按阶梯式计算：设置后呈阶梯式增长，按总业绩计算提成</p>
                    <p>例：1-10000时6%，10000-15000时10%，员工业绩13000，提成为：13000*10%</p>
                    <p>按阶段式计算：设置后分阶段式计算，根据区间计算提成</p>
                    <p>例：1-10000时6%，10000-15000时10%，员工业绩13000，提成为：10000*6%+3000*10%</p>
                    <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                  </el-popover>
                </span>
                <el-radio v-model="addRuleForm.Calculation" label="10">阶梯式计算</el-radio>
                <el-radio v-model="addRuleForm.Calculation" label="20">阶段式计算</el-radio>
              </el-form-item>
              <el-form-item v-if="!isAdd" label="有效性" prop="Active">
                <el-radio-group v-model="addRuleForm.Active">
                  <el-radio :label="true">有效</el-radio>
                  <el-radio :label="false">无效</el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="提成方案" prop="Commission" :rules="[{ required: true, message: '请设置提成方案' }]">
                <span slot="label">
                  提成方案
                  <el-popover placement="top-start" width="850px" trigger="hover">
                    <p>提成方案的区间值，最小值包含该区间内，最大值不包含在该区间内。</p>
                    <p>比如：区间设置为1000～2000，计算提成时，如业绩值为1000，则符合该区间规则；如业绩值为2000时，则不符合该区间规则。</p>
                    <el-button type="text" style="color: #dcdfe6" icon="el-icon-info" slot="reference"></el-button>
                  </el-popover>
                </span>
                <el-button type="primary" size="small" @click="addSchemeClick()">新增提成方案 </el-button>
              </el-form-item>
            </el-form>
            <el-table size="small" :data="addRuleForm.Commission" style="width: calc(100% - 110px); margin-left: 110px">
              <el-table-column prop="BeginPerformance" label="开始业绩(大于等于)">
                <template slot-scope="scope">{{ scope.row.BeginPerformance | toFixed | NumFormat }}</template>
              </el-table-column>
              <el-table-column prop="EndPerformance" label="结束业绩(小于)">
                <template slot-scope="scope">{{ scope.row.EndPerformance | toFixed | NumFormat }}</template>
              </el-table-column>
              <el-table-column prop="Rate" label="比例提成">
                <template slot-scope="scope">{{ scope.row.Rate | toFixed }}%</template>
              </el-table-column>
              <el-table-column prop="Fixed" label="固定提成(元)">
                <template slot-scope="scope">￥{{ scope.row.Fixed | toFixed | NumFormat }}</template>
              </el-table-column>
              <el-table-column prop="address" label="操作" width="145px">
                <template slot-scope="scope">
                  <el-button type="primary" size="small" @click="editCommissionClick(scope.row, scope.$index)"> 编辑</el-button>
                  <el-button type="danger" size="small" @click="removeCommissionClick(scope.$index)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-scrollbar>
        </el-tab-pane>
        <el-tab-pane label="提成员工" name="commissionEmployee">
          <el-row>
            <el-col :span="20">
              <el-form :inline="true" size="small" @submit.native.prevent>
                <el-form-item label="员工" label-width="40px">
                  <el-input v-model="searchCommissionEmps.Name" size="small" placeholder="输入员工姓名/编号搜索" clearable> </el-input>
                </el-form-item>
                <el-form-item label="职务" prop="JobID" label-width="60px">
                  <el-select v-model="searchCommissionEmps.JobID" placeholder="请选择" size="small" filterable clearable>
                    <el-option v-for="item in jobTypeData" :label="item.JobName" :value="item.ID" :key="item.ID"> </el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="在职状态" label-width="80px">
                  <el-select v-model="searchCommissionEmps.State" placeholder="请选择" size="small" filterable clearable>
                    <el-option label="在职" :value="true"></el-option>
                    <el-option label="离职" :value="false"> </el-option>
                  </el-select>
                </el-form-item>
              </el-form>
            </el-col>
            <el-col :span="4" class="text_right">
              <el-button type="primary" size="small" @click="addEmployee(true)" v-prevent-click>添加员工</el-button>
            </el-col>
          </el-row>
          <el-table
            size="small"
            :data="filterEmplyoee(commissionEmployee, 'searchCommissionEmps')"
            style="width: 100%"
            max-height="450px"
            tooltip-effect="light"
          >
            <el-table-column prop="Name" label="员工姓名"></el-table-column>
            <el-table-column show-overflow-tooltip label="员工所属单位">
              <template slot-scope="scope">
                {{ getEntityNames(scope.row.BelongEntity) }}
              </template>
            </el-table-column>
            <el-table-column prop="EmployeeID" label="员工编号"></el-table-column>
            <el-table-column prop="JobName" label="职务"></el-table-column>
            <el-table-column :formatter="(row) => (row.State ? '在职' : '离职')" label="在职状态"></el-table-column>
            <el-table-column label="操作" width="80">
              <template slot-scope="scope">
                <el-button size="small" type="danger" @click="handleDeleteApplyEmployee(scope.$index, true)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
        <el-tab-pane label="团队成员" name="employee">
          <el-row>
            <el-col :span="20">
              <el-form :inline="true" size="small" @submit.native.prevent>
                <el-form-item label="员工" label-width="40px">
                  <el-input v-model="searchTempEmps.Name" size="small" placeholder="输入员工姓名/编号搜索" clearable> </el-input>
                </el-form-item>
                <el-form-item label="职务" prop="JobID" label-width="60px">
                  <el-select v-model="searchTempEmps.JobID" placeholder="请选择" size="small" filterable clearable>
                    <el-option v-for="item in jobTypeData" :label="item.JobName" :value="item.ID" :key="item.ID"> </el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="在职状态" label-width="80px">
                  <el-select v-model="searchTempEmps.State" placeholder="请选择" size="small" filterable clearable>
                    <el-option label="在职" :value="true"></el-option>
                    <el-option label="离职" :value="false"> </el-option>
                  </el-select>
                </el-form-item>
              </el-form>
            </el-col>
            <el-col :span="4" class="text_right">
              <el-button type="primary" size="small" @click="addEmployee(false)" v-prevent-click>添加员工</el-button>
            </el-col>
          </el-row>
          <el-table size="small" :data="filterEmplyoee(teamEmployee, 'searchTempEmps')" style="width: 100%" max-height="450px" tooltip-effect="light">
            <el-table-column prop="Name" label="员工姓名"></el-table-column>
            <el-table-column show-overflow-tooltip label="员工所属单位">
              <template slot-scope="scope">{{ getEntityNames(scope.row.BelongEntity) }}</template>
            </el-table-column>
            <el-table-column prop="EmployeeID" label="员工编号"></el-table-column>
            <el-table-column prop="JobName" label="职务"></el-table-column>
            <el-table-column :formatter="(row) => (row.State ? '在职' : '离职')" label="在职状态"></el-table-column>
            <el-table-column label="操作" width="80">
              <template slot-scope="scope">
                <el-button size="small" type="danger" @click="handleDeleteApplyEmployee(scope.$index, false)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
      </el-tabs>

      <span slot="footer" class="dialog-footer">
        <el-button size="small" @click="dialogVisible = false" v-prevent-click>取 消</el-button>
        <el-button size="small" type="primary" @click="saveTeamCommissionSchemeCreate" v-prevent-click :loading="confirmLoading">保 存</el-button>
      </span>
    </el-dialog>

    <!-- 设置条件 -->
    <el-dialog width="30%" :title="isAddCommission ? '新增提成方案' : '编辑提成方案'" :visible.sync="commissionDialogVisible" @close="commissionClose">
      <el-form size="small" ref="commissionFormRef" :model="commissionFormData" label-width="110px">
        <el-form-item label="条件">
          <span slot="label"><span style="margin-right: 4px; color: #f67979">*</span><span>业绩范围</span></span>
          <el-col :span="8">
            <el-form-item
              label-width="0"
              style="margin-bottom: 0px !important"
              prop="BeginPerformance"
              :rules="[{ required: true, message: '请输入开始业绩' }]"
            >
              <el-input class="custom-input-number" v-model="commissionFormData.BeginPerformance" type="number" v-input-fixed="2" placeholder="请输入开始业绩">
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="2" class="dis_flex flex_x_center">至</el-col>
          <el-col :span="8">
            <el-form-item label-width="0" style="margin-bottom: 0px !important" prop="EndPerformance" :rules="[{ required: true, message: '请输入截止业绩' }]">
              <el-input class="custom-input-number" v-model="commissionFormData.EndPerformance" type="number" v-input-fixed="2" placeholder="请输入截止业绩">
              </el-input>
            </el-form-item>
          </el-col>
        </el-form-item>
        <el-form-item label="比例提成" prop="Rate" :rules="[{ required: true, message: '请输入比例提成' }]">
          <el-input v-model="commissionFormData.Rate" @input="setCriteriaRate" v-input-fixed="2" type="number" class="custom-input-number">
            <template slot="append">%</template>
          </el-input>
        </el-form-item>
        <el-form-item label="固定提成" prop="Fixed" :rules="[{ required: true, message: '请输入固定提成' }]">
          <el-input v-model="commissionFormData.Fixed" v-input-fixed="2" class="custom-input-number">
            <template slot="append">元</template>
          </el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button size="small" @click="commissionDialogVisible = false" v-prevent-click>取 消</el-button>
        <el-button size="small" type="primary" @click="handleSaveCommissin" v-prevent-click>保 存</el-button>
      </span>
    </el-dialog>

    <!-- 员工列表 -->
    <el-dialog title="添加员工" :visible.sync="commissionEmployeeDialogVisible" width="1000px" @close="emplyoeeBeforeClose">
      <el-row>
        <el-col :span="24">
          <el-form :inline="true" size="small" @submit.native.prevent @keyup.enter.native="handleEmployeeSearch">
            <el-form-item label="搜索" label-width="40px">
              <el-input v-model="searchEmployeeData.Name" size="small" placeholder="输入员工名称/编号搜索" clearable @clear="handleEmployeeSearch"> </el-input>
            </el-form-item>
            <el-form-item label="职务" prop="JobID" label-width="60px">
              <el-select v-model="searchEmployeeData.JobID" placeholder="请选择" size="small" filterable clearable @change="handleEmployeeSearch">
                <el-option v-for="item in jobTypeData" :label="item.JobName" :value="item.ID" :key="item.ID"> </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="在职状态" label-width="80px">
              <el-select v-model="searchEmployeeData.State" placeholder="请选择" size="small" filterable clearable @change="handleEmployeeSearch">
                <el-option label="在职" :value="true"></el-option>
                <el-option label="离职" :value="false"> </el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" size="small" @click="handleEmployeeSearch" v-prevent-click>搜索</el-button>
            </el-form-item>
          </el-form>
        </el-col>
      </el-row>
      <el-table
        size="small"
        :data="emplyoeeList"
        ref="emplyoeeList"
        style="width: 100%"
        max-height="450px"
        :row-key="(row) => row.EmployeeID"
        @selection-change="handleSelectionChange"
        @row-click="EmployeeRowClick"
        tooltip-effect="light"
      >
        <el-table-column type="selection" width="55" :reserve-selection="true"></el-table-column>
        <el-table-column prop="Name" label="员工姓名"></el-table-column>
        <el-table-column prop="EmployeeID" label="员工编号"></el-table-column>
        <el-table-column prop="JobName" label="职务"></el-table-column>
        <el-table-column :formatter="(row) => (row.State ? '在职' : '离职')" label="在职状态"></el-table-column>
        <el-table-column
          show-overflow-tooltip
          :formatter="
            (row) => {
              let str = '';
              row.Entity && row.Entity.length && row.Entity.forEach((item) => (str += item.EntityName + ';'));
              return str;
            }
          "
          label="所属单位"
        ></el-table-column>
      </el-table>
      <div class="pad_15 text_right">
        <el-pagination
          background
          v-if="EmployeePaginations.total > 0"
          @current-change="handleEmployeeCurrentChange"
          :current-page.sync="EmployeePaginations.page"
          :page-size="EmployeePaginations.page_size"
          :layout="EmployeePaginations.layout"
          :total="EmployeePaginations.total"
        ></el-pagination>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button size="small" @click="commissionEmployeeDialogVisible = false" v-prevent-click>取 消</el-button>
        <el-button size="small" type="primary" @click="handleSaveEmployee" v-prevent-click>保 存</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import API from "@/api/KHS/ChannelEmployeeSalary/teamCommissionScheme.js";
import APIJob from "@/api/KHS/Entity/jobtype";
export default {
  name: "ChannelEmployeeTeamCommissionScheme",
  props: {},
  /** 监听数据变化   */
  watch: {},
  /**  引入的组件  */
  components: {},
  /**  Vue 实例的数据对象**/
  data() {
    return {
      searchData: {
        Name: "",
        Active: true,
      },
      tableData: [],
      paginations: {
        page: 1, // 当前位于哪页
        total: 0, // 总数
        page_size: 10, // 1页显示多少条
        layout: "total, prev, pager, next,jumper", // 翻页属性
      },
      dialogVisible: false,
      isAdd: false,
      activeName: "Info",
      addRuleForm: {
        Active: true,
        Name: "", //模糊搜索
        PerformanceSchemeID: "", //业绩取值方案编号
        Calculation: "10", //计算方式
        Commission: [], //条件设置集合
        CommissionEmployee: [],
        Employee: [],
      },
      addRules: {
        Name: [{ required: true, message: "请输入提成方案名称", trigger: "blur" }],
        Active: [{ required: true, message: "请选择有效性", trigger: "blur" }],
        PerformanceSchemeID: [{ required: true, message: "请选择取值方案名称", trigger: "blur" }],
        Commission: [{ required: true, message: "请填写提成方案", trigger: "blur" }],
      },
      employeeTableData: [],

      isAddCommission: false,
      commissionDialogVisible: false,
      commissionFormData: {
        BeginPerformance: "",
        EndPerformance: "",
        Rate: "",
        Fixed: "",
      },
      commissionEditIndex: 0,

      commissionEmployeeDialogVisible: false,
      emplyoeeList: [],
      searchEmployee: "",
      EmployeePaginations: {
        page: 1, // 当前位于哪页
        total: 0, // 总数
        page_size: 10, // 1页显示多少条
        layout: "total, prev, pager, next,jumper", // 翻页属性
      },
      searchEmployeeData: {
        Name: "",
        JobID: "",
        State: true,
      },
      commissionEmployee: [] /**  提成成员  */,
      teamEmployee: [] /**  团队成员  */,
      performanceScheme: [],
      confirmLoading: false,
      searchCommissionEmps: {
        Name: "",
        JobID: "",
        State: "",
      },
      searchTempEmps: {
        Name: "",
        JobID: "",
        State: "",
      },
      jobTypeData: [],
      selectionEmp: [] /**  员工弹窗选中临时数据  */,
      performanceSchemeAllList: [], //市场业绩取值方案
    };
  },
  /**计算属性  */
  computed: {},
  /**  方法集合  */
  methods: {
    /**  搜索  */
    handleSearch() {
      let that = this;
      that.paginations.page = 1;
      that.channelEmployeeTeamCommissionScheme();
    },
    /**   修改分页 */
    handleCurrentChange(page) {
      let that = this;
      that.paginations.page = page;
      that.channelEmployeeTeamCommissionScheme();
    },
    /**  新增  */
    showAddDialog() {
      let that = this;
      that.addRuleForm = {
        Active: true,
        Name: "", //模糊搜索
        PerformanceSchemeID: "", //业绩取值方案编号
        Calculation: "10", //计算方式
        Commission: [], //条件设置集合
        CommissionEmployee: [],
        Employee: [],
      };
      that.isAdd = true;
      that.dialogVisible = true;
    },
    /**  编辑  */
    showEditDialog(row) {
      let that = this;
      that.isAdd = false;
      that.addRuleForm = Object.assign(that.addRuleForm, row);
      that.channelEmployeeTeamCommissionScheme_commission(row.ID);
      that.channelEmployeeTeamCommissionScheme_commissionEmployee(row.ID);
      that.channelEmployeeTeamCommissionScheme_employee(row.ID);
      that.dialogVisible = true;
    },

    /**  删除业绩方案  */
    removeEntityClick() {
      // let that = this;
    },
    /**  保存业绩方案  */
    saveTeamCommissionSchemeCreate() {
      let that = this;
      that.$refs.addRuleFormRef.validate((valid) => {
        if (valid) {
          if (that.isAdd) {
            that.channelEmployeeTeamCommissionScheme_create();
          } else {
            that.channelEmployeeTeamCommissionScheme_update();
          }
        }
      });
    },
    /**   新增提成方案 */
    addSchemeClick() {
      let that = this;
      that.isAddCommission = true;
      that.commissionFormData = {
        BeginPerformance: "",
        EndPerformance: "",
        Rate: "",
        Fixed: "",
      };
      that.commissionDialogVisible = true;
    },
    /** 编辑   */
    editCommissionClick(row, index) {
      let that = this;
      that.isAddCommission = false;
      that.commissionEditIndex = index;
      that.commissionFormData = Object.assign(that.commissionFormData, row);
      that.commissionDialogVisible = true;
    },
    /**  删除业绩方案  */
    removeCommissionClick(index) {
      let that = this;
      that.addRuleForm.Commission.splice(index, 1);
    },
    /**    */
    commissionClose() {
      let that = this;
      that.$refs.commissionFormRef.resetFields();
    },
    /**  比例提成输入  */
    setCriteriaRate(val) {
      let that = this;
      if (Number(val) > 100) {
        that.commissionFormData.Rate = 100;
      }
    },
    /**   添加员工   */
    addEmployee(isCommission) {
      this.EmployeePaginations.page = 1;
      this.searchEmployeeData = {
        Name: "",
        JobID: "",
        State: true,
      };
      this.getEntityCommissionSchemeAllEmployee();
      if (this.isAdd) {
        this.addCommissionOrEmplyee(isCommission);
      } else {
        this.addCommissionOrEmplyee(isCommission);
      }
    },
    /**  删除员工  */
    handleDeleteApplyEmployee(index, isTeam) {
      if (isTeam) {
        this.commissionEmployee.splice(index, 1);
      } else {
        this.teamEmployee.splice(index, 1);
      }
    },
    /**  关闭员工选择弹窗  */
    emplyoeeBeforeClose() {
      this.$refs.emplyoeeList.clearSelection();
    },
    /**  获取员工所属门店名称组合  */
    getEntityNames(entitys) {
      let names = entitys.map((i) => {
        if (i.IsPrimaryEntity) {
          return i.EntityName + "[主]";
        } else {
          return i.EntityName;
        }
      });
      return names.join();
    },
    /**    */
    filterEmplyoee(emps, key) {
      let temp = emps.filter((i) => !this[key].Name || i.Name.toLowerCase().includes(this[key].Name.toLowerCase()) || i.ID == this[key].Name);
      temp = temp.filter((i) => !this[key].JobID || i.JobID == this[key].JobID);
      temp = temp.filter((i) => {
        if (this[key].State.length == 0) {
          return i;
        } else {
          return i.State == this[key].State;
        }
      });

      return temp;
    },
    /**  员工列表搜索  */
    handleEmployeeSearch() {
      this.EmployeePaginations.page = 1;
      this.getEntityCommissionSchemeAllEmployee();
    },
    /**  员工列表分页修改  */
    handleEmployeeCurrentChange(page) {
      this.EmployeePaginations.page = page;
      this.getEntityCommissionSchemeAllEmployee();
    },

    /**  添加提成员工或团队员工  */
    addCommissionOrEmplyee(isCommission) {
      this.isCommission = isCommission;
      this.commissionEmployeeDialogVisible = true;
      if (isCommission) {
        this.$nextTick(() => {
          this.commissionEmployee.forEach((i) => {
            this.$refs.emplyoeeList.toggleRowSelection(i);
          });
        });
      } else {
        this.teamEmployee.forEach((i) => {
          this.$refs.emplyoeeList.toggleRowSelection(i);
        });
      }
    },

    /**   选择员工 */
    handleSelectionChange(selection) {
      this.selectionEmp = selection;
    },
    /**    */
    EmployeeRowClick(row) {
      let that = this;
      that.$refs.emplyoeeList.toggleRowSelection(row);
    },
    /**   保存选中员工 */
    handleSaveEmployee() {
      if (this.isCommission) {
        /**  提成员工  */
        this.commissionEmployee = this.selectionEmp.map((i) => {
          i.BelongEntity = i.Entity;
          return i;
        });
      } else {
        /**  团队员工  */
        this.teamEmployee = this.selectionEmp.map((i) => {
          i.BelongEntity = i.Entity;
          return i;
        });
      }
      this.commissionEmployeeDialogVisible = false;
    },
    /**  保存员工提成方案  */
    handleSaveTeamCommissionScheme() {
      let that = this;
      this.$refs.formData.validate((valid) => {
        if (valid) {
          if (that.isAdd) {
            that.teamCommissionScheme_create();
          } else {
            this.teamCommissionScheme_update();
          }
        }
      });
    },

    /**   保存提成方案 */
    handleSaveCommissin() {
      let that = this;
      that.$refs.commissionFormRef.validate((valid) => {
        if (valid) {
          if (that.commissionFormData.BeginPerformance - that.commissionFormData.EndPerformance > 0) {
            that.$message.error({
              message: "截止业绩数额不能小于开始业绩数额",
              duration: 2000,
            });
            return;
          }
          // 判断是否有相同的提成方案
          let commission = JSON.parse(JSON.stringify(that.addRuleForm.Commission));
          if (!that.isAddCommission) {
            let index = that.commissionEditIndex;
            commission.splice(index, 1);
          }
          let num = commission.every((item) => {
            const num1 = Number(item.BeginPerformance);
            const num2 = Number(item.EndPerformance);
            const num3 = Number(that.commissionFormData.BeginPerformance);
            const num4 = Number(that.commissionFormData.EndPerformance);
            if (num3 >= num2) return true;
            if (num4 <= num1) return true;
            return false;
          });
          if (!num) {
            that.$message.error({
              message: "条件设置存在重复数额",
              duration: 2000,
            });
            return;
          }

          if (that.isAddCommission) {
            that.addRuleForm.Commission.push({ ...that.commissionFormData });
          } else {
            let index = that.commissionEditIndex;
            that.addRuleForm.Commission.splice(index, 1, { ...that.commissionFormData });
          }
          that.commissionDialogVisible = false;
        }
      });
    },

    /**••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••*/
    /**    */
    async channelEmployeeTeamCommissionScheme() {
      let that = this;
      try {
        let params = {
          PageNum: that.paginations.page,
          Name: that.searchData.Name, //模糊搜索
          PerformanceSchemeID: that.searchData.PerformanceSchemeID, //业绩取值方案编号
          Active: that.searchData.Active, //有效性
        };
        let res = await API.channelEmployeeTeamCommissionScheme(params);
        if (res.StateCode == 200) {
          that.tableData = res.List;
          that.paginations.total = res.Total;
        } else {
          that.$message.error(res.Message);
        }
      } catch (error) {
        that.$message.error(error);
      }
    },
    /**    */
    async channelEmployeeTeamCommissionScheme_create() {
      let that = this;
      that.confirmLoading = true;
      try {
        let params = {
          Name: that.addRuleForm.Name, //模糊搜索
          Active: true,
          PerformanceSchemeID: that.addRuleForm.PerformanceSchemeID, //业绩取值方案编号
          Calculation: that.addRuleForm.Calculation, //计算方式
          Commission: that.addRuleForm.Commission.map((i) => {
            return {
              BeginPerformance: i.BeginPerformance, //开始数额
              EndPerformance: i.EndPerformance, //结束数额
              Rate: i.Rate, //比例提成
              Fixed: i.Fixed, //固定提成
            };
          }), //条件设置集合
          CommissionEmployee: that.commissionEmployee.map((i) => i.EmployeeID),
          Employee: that.teamEmployee.map((i) => i.EmployeeID),
        };
        let res = await API.channelEmployeeTeamCommissionScheme_create(params);
        if (res.StateCode == 200) {
          that.dialogVisible = false;
          that.$message.success("操作成功");
          that.channelEmployeeTeamCommissionScheme();
        } else {
          that.$message.error(res.Message);
        }
        that.confirmLoading = false;
      } catch (error) {
        that.confirmLoading = false;
        that.$message.error(error);
      }
    },
    /**    */
    async channelEmployeeTeamCommissionScheme_commission(ID) {
      let that = this;
      try {
        let params = {
          ID: ID, //个人业绩提成编号
        };
        let res = await API.channelEmployeeTeamCommissionScheme_commission(params);
        if (res.StateCode == 200) {
          that.addRuleForm.Commission = res.Data;
        } else {
          that.$message.error(res.Message);
        }
      } catch (error) {
        that.$message.error(error);
      }
    },
    /**    */
    async channelEmployeeTeamCommissionScheme_commissionEmployee(ID) {
      let that = this;
      try {
        let params = {
          ID: ID, //个人业绩提成编号
        };
        let res = await API.channelEmployeeTeamCommissionScheme_commissionEmployee(params);
        if (res.StateCode == 200) {
          that.commissionEmployee = res.Data;
        } else {
          that.$message.error(res.Message);
        }
      } catch (error) {
        that.$message.error(error);
      }
    },
    /**    */
    async channelEmployeeTeamCommissionScheme_employee(ID) {
      let that = this;
      try {
        let params = {
          ID: ID, //个人业绩提成编号
        };
        let res = await API.channelEmployeeTeamCommissionScheme_employee(params);
        if (res.StateCode == 200) {
          that.teamEmployee = res.Data;
        } else {
          that.$message.error(res.Message);
        }
      } catch (error) {
        that.$message.error(error);
      }
    },
    /**    */
    async channelEmployeeTeamCommissionScheme_update() {
      let that = this;
      that.confirmLoading = true;
      try {
        let params = {
          ID: that.addRuleForm.ID,
          Name: that.addRuleForm.Name, //模糊搜索
          Active: that.addRuleForm.Active,
          PerformanceSchemeID: that.addRuleForm.PerformanceSchemeID, //业绩取值方案编号
          Calculation: that.addRuleForm.Calculation, //计算方式
          Commission: that.addRuleForm.Commission.map((i) => {
            return {
              BeginPerformance: i.BeginPerformance, //开始数额
              EndPerformance: i.EndPerformance, //结束数额
              Rate: i.Rate, //比例提成
              Fixed: i.Fixed, //固定提成
            };
          }), //条件设置集合
          CommissionEmployee: that.commissionEmployee.map((i) => i.EmployeeID),
          Employee: that.teamEmployee.map((i) => i.EmployeeID),
        };
        let res = await API.channelEmployeeTeamCommissionScheme_update(params);
        if (res.StateCode == 200) {
          that.dialogVisible = false;
          that.$message.success("操作成功");
          that.channelEmployeeTeamCommissionScheme();
        } else {
          that.$message.error(res.Message);
        }
        that.confirmLoading = false;
      } catch (error) {
        that.confirmLoading = false;
        that.$message.error(error);
      }
    },

    /**    */
    async channelPerformanceScheme_valid() {
      let that = this;
      try {
        let params = {};
        let res = await API.channelPerformanceScheme_valid(params);
        if (res.StateCode == 200) {
          that.performanceSchemeAllList = res.Data;
        } else {
          that.$message.error(res.Message);
        }
      } catch (error) {
        that.$message.error(error);
      }
    },
    /**    */
    async getEntityCommissionSchemeAllEmployee() {
      let that = this;
      let params = {
        PageNum: that.EmployeePaginations.page,
        Name: that.searchEmployeeData.Name,
        JobID: that.searchEmployeeData.JobID,
        State: that.searchEmployeeData.State,
      };
      let res = await API.getEntityCommissionSchemeAllEmployee(params);
      if (res.StateCode == 200) {
        this.emplyoeeList = res.List;
        this.EmployeePaginations.total = res.Total;
      } else {
        that.$message.error(res.Message);
      }
    },

    //获取全部职务列表
    getJobTypeAll: function () {
      var that = this;
      var params = {
        JobTypeName: that.JobTypeName,
      };
      APIJob.getJobJobtypeAll(params).then((res) => {
        if (res.StateCode == 200) {
          that.jobTypeData = res.Data;
        } else {
          that.$message.error({
            message: res.Message,
            duration: 2000,
          });
        }
      });
    },
  },
  /** 创建实例之前执行函数   */
  beforeCreate() {},
  /**  实例创建完成之后  */
  created() {},
  /**  在挂载开始之前被调用  */
  beforeMount() {},
  /**  实例被挂载后调用  */
  mounted() {
    this.channelEmployeeTeamCommissionScheme();
    this.getEntityCommissionSchemeAllEmployee();
    this.channelPerformanceScheme_valid();
    this.getJobTypeAll();
  },
  /**  数据更新 之前 调用  */
  beforeUpdate() {},
  /** 数据更新 完成 调用   */
  updated() {},
  /**  实例销毁之前调用  */
  beforeDestroy() {},
  /**  实例销毁后调用  */
  destroyed() {},
};
</script>

<style lang="scss">
.ChannelEmployeeTeamCommissionScheme {
  .el-scrollbar_height {
    height: 55vh;
    .el-scrollbar__wrap {
      overflow-x: hidden;
    }
  }
}
</style>
