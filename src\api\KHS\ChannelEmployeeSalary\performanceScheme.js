/**
 * Created by preference on 2022/09/01
 *  zmx
 */

import * as API from "@/api/index";
export default {
  /**  业绩取值方案查询 */
  channelPerformanceScheme_all: (params) => {
    return API.POST("api/channelEmployeePerformanceScheme/all", params);
  },
  /**  业绩取值方案查询-不加分页 */
  channelPerformanceScheme_valid: (params) => {
    return API.POST("api/channelEmployeePerformanceScheme/valid", params);
  },
  /** 业绩取值方案添加  */
  channelPerformanceScheme_create: (params) => {
    return API.POST("api/channelEmployeePerformanceScheme/create", params);
  },
  /** 业绩取值方案修改  */
  channelEmployeePerformanceScheme_update: (params) => {
    return API.POST("api/channelEmployeePerformanceScheme/update", params);
  },
  /** 业绩取值方案详情 */
  channelPerformanceScheme_detail: (params) => {
    return API.POST("api/channelEmployeePerformanceScheme/detail", params);
  },
  /** 业绩取值方案产品详情 */
  channelPerformanceSchemeProduct_all: (params) => {
    return API.POST("api/channelEmployeePerformanceSchemeProduct/all", params);
  },
  /** 业绩取值方案产品保存  */
  channelPerformanceSchemeProduct_create: (params) => {
    return API.POST("api/channelEmployeePerformanceSchemeProduct/create", params);
  },
  /**  业绩取值方案项目详情 */
  channelPerformanceSchemeProject_all: (params) => {
    return API.POST("api/channelEmployeePerformanceSchemeProject/all", params);
  },
  /** 业绩取值方案项目保存  */
  channelPerformanceSchemeProject_create: (params) => {
    return API.POST("api/channelEmployeePerformanceSchemeProject/create", params);
  },
  /**  业绩取值方案通用次卡详情 */
  channelPerformanceSchemeGeneralCard_all: (params) => {
    return API.POST("api/channelEmployeePerformanceSchemeGeneralCard/all", params);
  },
  /** 业绩取值方案通用次卡保存  */
  channelPerformanceSchemeGeneralCard_create: (params) => {
    return API.POST("api/channelEmployeePerformanceSchemeGeneralCard/create", params);
  },

  /**  业绩取值方案时效卡详情 */
  channelPerformanceSchemeTimeCard_all: (params) => {
    return API.POST("api/channelEmployeePerformanceSchemeTimeCard/all", params);
  },
  /** 业绩取值方案时效卡详情保存  */
  channelPerformanceSchemeTimeCard_create: (params) => {
    return API.POST("api/channelEmployeePerformanceSchemeTimeCard/create", params);
  },

  /**  业绩取值方案储值卡详情 */
  channelPerformanceSchemeSavingCard_all: (params) => {
    return API.POST("api/channelEmployeePerformanceSchemeSavingCard/all", params);
  },
  /** 业绩取值方案储值卡保存  */
  channelPerformanceSchemeSavingCard_create: (params) => {
    return API.POST("api/channelEmployeePerformanceSchemeSavingCard/create", params);
  },

  /**  业绩取值方案消耗产品详情 */
  channelPerformanceSchemeTreatProduct_all: (params) => {
    return API.POST("api/channelEmployeePerformanceSchemeTreatProduct/all", params);
  },
  /** 业绩取值方案消耗产品保存  */
  channelPerformanceSchemeTreatProduct_create: (params) => {
    return API.POST("api/channelEmployeePerformanceSchemeTreatProduct/create", params);
  },

  /**  业绩取值方案消耗项目详情 */
  channelPerformanceSchemeTreatProject_all: (params) => {
    return API.POST("api/channelEmployeePerformanceSchemeTreatProject/all", params);
  },
  /** 业绩取值方案消耗项目保存  */
  channelPerformanceSchemeTreatProject_create: (params) => {
    return API.POST("api/channelEmployeePerformanceSchemeTreatProject/create", params);
  },

  /**  业绩取值方案消耗通用此卡详情 */
  channelPerformanceSchemeTreatGeneralCard_all: (params) => {
    return API.POST("api/channelEmployeePerformanceSchemeTreatGeneralCard/all", params);
  },
  /** 业绩取值方案消耗通用此卡保存  */
  channelPerformanceSchemeTreatGeneralCard_create: (params) => {
    return API.POST(
      "api/channelEmployeePerformanceSchemeTreatGeneralCard/create",
      params
    );
  },

  /**  业绩取值方案消耗时效卡详情 */
  channelPerformanceSchemeTreatTimeCard_all: (params) => {
    return API.POST("api/channelEmployeePerformanceSchemeTreatTimeCard/all", params);
  },
  /** 业绩取值方案消耗通用此卡保存  */
  channelPerformanceSchemeTreatTimeCard_create: (params) => {
    return API.POST("api/channelEmployeePerformanceSchemeTreatTimeCard/create", params);
  },

  /**  业绩取值方案消耗储值卡详情 */
  channelEmployeePerformanceSchemeTreatSavingCard_all: (params) => {
    return API.POST("api/channelEmployeePerformanceSchemeTreatSavingCard/all", params);
  },
  /** 业绩取值方案消耗储值卡保存  */
  channelEmployeePerformanceSchemeTreatSavingCard_create: (params) => {
    return API.POST(
      "api/channelEmployeePerformanceSchemeTreatSavingCard/create",
      params
    );
  },
};
