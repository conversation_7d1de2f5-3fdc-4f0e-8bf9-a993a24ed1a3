#!/bin/bash

# Exit immediately if a command exits with a non-zero status.
set -e

# --- Configuration ---
# Get the directory where the script is located
SCRIPT_DIR=$(cd -- "$(dirname -- "${BASH_SOURCE[0]}")" &> /dev/null && pwd)

# Local project paths (relative to the script's location)
VUE_PROJECT_PATH="${SCRIPT_DIR}"

# Remote server details
REMOTE_USER="root"
REMOTE_HOST="**************"
REMOTE_SSH_TARGET="${REMOTE_USER}@${REMOTE_HOST}"

# Remote application paths
REMOTE_FRONTEND_PATH="/var/www/erp.37mei.com/html"

# --- Deployment Steps ---

echo "🚀 Starting deployment to erp.37mei.com..."

# 1. Build the frontend application
echo "
[1/3] Building frontend application..."
cd "$VUE_PROJECT_PATH"
npm run build:prod
echo "✅ Frontend build complete."

# 2. Prepare directories on the server
echo "
[2/3] Connecting to server and preparing directories..."
ssh "$REMOTE_SSH_TARGET" "mkdir -p $REMOTE_FRONTEND_PATH"
echo "✅ Remote directories are ready."

# 3. Deploy frontend files
echo "
[3/3] Deploying frontend files..."
rsync -avz --delete "$VUE_PROJECT_PATH/dist/" "${REMOTE_SSH_TARGET}:${REMOTE_FRONTEND_PATH}/"
ssh "$REMOTE_SSH_TARGET" "chown -R nginx:nginx ${REMOTE_FRONTEND_PATH}"
echo "✅ Frontend deployed and permissions updated successfully."

echo "
🎉 Deployment finished successfully!"