<template>
  <div class="Seckill" style="backaground-color: #f8f8f8">
    <div v-if="ContentProperty && ContentProperty.length > 0">
      <div
        v-for="(item, index) in ContentProperty"
        :key="index"
        class="groupon-item"
      >
        <div class="image-main">
          <el-image
            class="grouponImage"
            :src="item.GoodsImageUrl"
            fit="cover"
          ></el-image>

          <div
            v-if="ConfigProperty && ConfigProperty.some((val) => val == 'Time')"
            class="downTime"
          >
            <countDown :time="getDownTimes(item)">
              <template v-slot="{ days, hours, minutes, seconds }">
                {{ getDownTitle(item) }}:{{ days }}天{{ hours }}:{{
                  minutes
                }}:{{ seconds }}
              </template>
            </countDown>
          </div>
        </div>

        <div class="info-container">
          <div class="info-header">
            <el-tag
              v-if="
                ConfigProperty && ConfigProperty.some((val) => val == 'Name')
              "
              size="small"
              >{{ grouponGoodsTypeFormatter(item.GoodsType) }}
            </el-tag>
            <span
              v-if="ConfigProperty.some((val) => val == 'Name')"
              class="title"
              >{{ item.Name }}</span
            >
          </div>
          <div
            v-if="
              ConfigProperty && ConfigProperty.some((val) => val == 'Price')
            "
            class="groupon-label-wrap"
          >
            <div class="price-info">
              <p class="valid-price">
                <span class="font_12">￥</span
                >{{ item.SeckillPrice | NumFormat }}
              </p>
              <p class="origin-price">¥{{ item.Price }}</p>
            </div>
            <el-tag size="small" effect="plain" type="danger">去购买</el-tag>
          </div>
        </div>
      </div>
    </div>
    <div v-else style="min-height: 20px">
      <div class="groupon-item">
        <div class="image-main">
          <div
            v-if="ConfigProperty.some((val) => val == 'Time')"
            class="downTime"
          >
            <count-down :time="2 * 24 * 60 * 60 * 1000">
              <template v-slot="{ days, hours, minutes, seconds }">
                距结束仅剩:{{ days }}天{{ hours }}:{{ minutes }}:{{ seconds }}
              </template>
            </count-down>
          </div>
        </div>

        <div class="info-container">
          <div
            v-if="ConfigProperty.some((val) => val == 'Name')"
            class="info-header"
          >
            <el-tag size="small">项目</el-tag>
            <span class="title">此处显示秒杀活动名称</span>
          </div>

          <div
            v-if="ConfigProperty.some((val) => val == 'Price')"
            class="groupon-label-wrap"
          >
            <div class="price-info">
              <p class="valid-price"><span class="font_12">￥</span>999</p>
              <p class="origin-price">¥1000</p>
            </div>
            <el-tag size="small" effect="plain" type="danger">去购买</el-tag>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import countDown from "@/components/countdown/CountDown.vue";

export default {
  name: "Seckill_HomeConf",
  props: {
    ContentProperty: {
      type: Array,
      default: () => {
        return [];
      },
    },
    ConfigProperty: {
      type: Array,
      default: () => {
        return ["Time", "Name", "Price"];
      },
    },
  },
  /**  引入的组件  */
  components: {
    countDown,
  },
  /**  Vue 实例的数据对象**/
  data() {
    return {};
  },
  /**计算属性  */
  computed: {},
  /**  方法集合  */
  methods: {
    /**  拼团商品类型   */
    grouponGoodsTypeFormatter(GoodsType) {
      switch (GoodsType) {
        case "10":
          return "产品";
        case "20":
          return "项目";
        case "30":
          return "通用次卡";
        case "40":
          return "时效卡";
        default:
          return "";
      }
    },
    /**  获取倒计时 时间戳  */
    getDownTimes(item) {
      let curTimes = new Date().getTime();
      let beginTimes = new Date(item.BeginDateTime).getTime();
      let endTimes = new Date(item.EndDateTime).getTime();
      /**  当前时间 大于开始时间  */
      if (curTimes > beginTimes) {
        let temp = endTimes - curTimes;
        return temp > 0 ? temp : 0;
      }
      /**  当前时间小于 开始时间  */
      if (curTimes < beginTimes) {
        return beginTimes - curTimes;
      }
      return 0;
    },
    /**  获取 倒计时 标题  */
    getDownTitle(item) {
      let curTimes = new Date().getTime();
      let beginTimes = new Date(item.BeginDateTime).getTime();
      let endTimes = new Date(item.EndDateTime).getTime();
      /**  当前时间大于开始时间 且小于结束时间  */
      if (curTimes > beginTimes && curTimes < endTimes) {
        return "距结束";
      }
      /**  当前时间 小于开始时间  */
      if (curTimes < beginTimes) {
        return "距开始";
      }
      return "活动已结束";
    },
  },
  /** 监听数据变化   */
  watch: {},
  /** 创建实例之前执行函数   */
  beforeCreate() {},
  /**  实例创建完成之后  */
  created() {},
  /**  在挂载开始之前被调用  */
  beforeMount() {},
  /**  实例被挂载后调用  */
  mounted() {
  },
  /**  数据更新 之前 调用  */
  beforeUpdate() {},
  /** 数据更新 完成 调用   */
  updated() {},
  /**  实例销毁之前调用  */
  beforeDestroy() {},
  /**  实例销毁后调用  */
  destroyed() {},
};
</script>

<style lang="scss">
.Seckill {
  width: 100%;
  .groupon-item {
    display: flex;
    background: #ffffff;
    margin-top: 10px;
    margin-left: 10px;
    margin-right: 10px;
    border-radius: 8px;
    overflow: hidden;
    .image-main {
      background: #dbf5ff url("../../../../assets/img/goodsEmpty.png") no-repeat
        center;
      background-size: 63px 47px;
      width: 154px;
      height: 154px;
      position: relative;

      .downTime {
        padding: 0px 8px;
        line-height: 30px;
        position: absolute;
        bottom: 0;
        right: 0;
        left: 0;
        font-size: 12px;
        color: #ffffff;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 1;
        text-overflow: clip;
        background: -webkit-linear-gradient(
          to right,
          var(--zl-color-orange-primary),
          var(--zl-color-orange-primary-soft)
        );
        background: -moz-linear-gradient(
          to right,
          var(--zl-color-orange-primary),
          var(--zl-color-orange-primary-soft)
        );
        background: linear-gradient(
          to right,
          var(--zl-color-orange-primary),
          var(--zl-color-orange-primary-soft)
        );
      }
      .grouponImage {
        width: 100%;
        height: 100%;
      }
    }
    .info-container {
      flex: 1;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      width: 100%;
      -webkit-flex-direction: column;
      -ms-flex-direction: column;
      flex-direction: column;
      -webkit-justify-content: space-between;
      -ms-flex-pack: justify;
      justify-content: space-between;
      padding: 12px;
      .info-header {
        font-size: 14px;
        .title {
          margin-left: 5px;
          line-height: 20px;
          vertical-align: middle;
          font-weight: 600;
          color: rgb(50, 50, 51);

          overflow: hidden;
          box-orient: vertical;
          -webkit-line-clamp: 2;
        }
      }
      .groupon-label-wrap {
        margin-top: 8px;
        color: #f44;
        position: relative;
        display: -webkit-flex;
        display: -ms-flexbox;
        display: flex;
        -webkit-align-items: center;
        -ms-flex-align: center;
        align-items: center;
        -webkit-justify-content: space-between;
        -ms-flex-pack: justify;
        justify-content: space-between;
        .price-info {
          flex: 1;
          .valid-price {
            // color_red font_24
            color: red;
            font-size: 14px;
          }
          .origin-price {
            font-size: 12px;
            color: #6d6d6d;
            line-height: 12px;
            text-decoration: line-through;
          }
        }
      }
    }
  }
}
</style>
