/**
 * Created by wsf on 2022/01/05.
 * 员工项目销售业绩方案 api
 */

 import * as API from '@/api/index'

 export default {
    // 获取员工项目销售业绩方案列表
    getProjectPerformanceScheme: params => {
        return API.POST('api/saleProjectPerformanceScheme/list', params)
    }, 
    // 创建员工项目销售业绩方案
    createProjectPerformanceScheme: params => {
        return API.POST('api/saleProjectPerformanceScheme/create', params)
    },
    // 删除员工项目销售业绩方案
    deleteProjectPerformanceScheme: params => {
        return API.POST('api/saleProjectPerformanceScheme/delete', params)
    },
    // 获取所有项目经手人业绩
    getProjectSchemeHandlerPerformance: params => {
        return API.POST('api/saleProjectSchemeHandlerPerformance/all', params)
    },
    // 获取所有套餐卡项目经手人业绩
    getPackageCardProjectSchemeHandlerPerformance: params => {
        return API.POST('api/saleProjectSchemeHandlerPerformance/packageCard', params)
    },
    // 所有项目经手人业绩保存
    updateProjectSchemeHandlerPerformance: params => {
         return API.POST('api/saleProjectSchemeHandlerPerformance/update', params)
    },
    // 获取项目分类业绩
    getProjectCategoryPerformance: params => {
        return API.POST('api/saleProjectCategoryPerformance/all', params)
    },
    // 项目分类业绩保存
    updateProjectCategoryPerformance: params => {
        return API.POST('api/saleProjectCategoryPerformance/update', params)
    },
    // 获取分类项目经手人业绩 
    getProjectCategoryHandlerPerformance: params => {
        return API.POST('api/saleProjectCategoryHandlerPerformance/all', params)
    },
    // 获取分类套餐卡项目经手人业绩 
    getPackageCardProjectCategoryHandlerPerformance: params => {
        return API.POST('api/saleProjectCategoryHandlerPerformance/packageCard', params)
    },
    // 项目经手人业绩保存 
    updateProjectCategoryHandlerPerformance: params => {
        return API.POST('api/saleProjectCategoryHandlerPerformance/update', params)
    },
    // 获取项目业绩
    getProjectPerformance: params => {
        return API.POST('api/saleProjectPerformance/all', params)
    },
    // 项目业绩保存
    updateProjectPerformance: params => {
        return API.POST('api/saleProjectPerformance/update', params)
    },
    // 获取项目经手人业绩
    getProjectHandlerPerformance: params => {
        return API.POST('api/saleProjectHandlerPerformance/all', params)
    },
    // 获取套餐卡项目经手人业绩 
    getPackageCardProejctHandlerPerformance: params => {
        return API.POST('api/saleProjectHandlerPerformance/packageCard', params)
    },
    // 项目经手人业绩保存 
    updateProjectHandlerPerformance: params => {
        return API.POST('api/saleProjectHandlerPerformance/update', params)
    }
 }