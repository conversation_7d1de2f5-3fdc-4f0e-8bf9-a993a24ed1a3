<template>
  <div class="customVideoPlayer">
    <videoPlayer class="video-player vjs-custom-skin" ref="videoPlayer" :playsinline="true" :options="playerOptions"> </videoPlayer>
  </div>
</template>

<script>
import { videoPlayer } from "vue-video-player";
import "video.js/dist/video-js.css";
export default {
  name: "customVideoPlayer",

  props: {
    src: {
      type: String,
    },
    poster: {
      type: String,
    },
  },
  /** 监听数据变化   */
  watch: {
    src: {
      immediate: true,
      handler(val) {
        if (val) {
          this.playerOptions.sources[0].src = val;
        }
      },
    },
  },
  /**  引入的组件  */
  components: {
    videoPlayer,
  },
  /**  Vue 实例的数据对象**/
  data() {
    return {
      playerOptions: {
        playbackRates: [0.7, 1.0, 1.5, 2.0], //播放速度
        autoplay: false, // 如果true,浏览器准备好时开始回放。
        muted: false, // 默认情况下将会消除任何音频。
        loop: false, // 导致视频一结束就重新开始。
        preload: "auto", // 建议浏览器在<video>加载元素后是否应该开始下载视频数据。auto浏览器选择最佳行为,立即开始加载视频（如果浏览器支持）
        language: "zh-CN",
        aspectRatio: "16:9", // 将播放器置于流畅模式，并在计算播放器的动态大小时使用该值。值应该代表一个比例 - 用冒号分隔的两个数字（例如"16:9"或"4:3"）
        fluid: true, // 当true时，Video.js player将拥有流体大小。换句话说，它将按比例缩放以适应其容器。
        sources: [
          {
            type: "video/mp4", // 这里的种类支持很多种：基本视频格式、直播、流媒体等，具体可以参看git网址项目
            src: this.src, // url地址
          },
        ],
        hls: true,
        poster: this.poster, // 你的封面地址
        // width: document.documentElement.clientWidth, // 播放器宽度
        width: "280px",
        notSupportedMessage: "视频暂无法播放，请稍后再试", // 允许覆盖Video.js无法播放媒体源时显示的默认信息。
        controlBar: {
          timeDivider: true, //时间分割线
          durationDisplay: true, //总时间
          remainingTimeDisplay: true, //剩余播放时间
          progressControl: true, // 进度条
          fullscreenToggle: true, // 全屏按钮
        },
      },
    };
  },
  /**计算属性  */
  computed: {},
  /**  方法集合  */
  methods: {},
  /** 创建实例之前执行函数   */
  beforeCreate() {},
  /**  实例创建完成之后  */
  created() {},
  /**  在挂载开始之前被调用  */
  beforeMount() {},
  /**  实例被挂载后调用  */
  mounted() {},
  /**  数据更新 之前 调用  */
  beforeUpdate() {},
  /** 数据更新 完成 调用   */
  updated() {},
  /**  实例销毁之前调用  */
  beforeDestroy() {},
  /**  实例销毁后调用  */
  destroyed() {},
};
</script>

<style lang="scss">
.customVideoPlayer {
  width: 280px;
  height: 159px;
  .video-player {
    .video-js {
      .vjs-big-play-button {
        height: 1.7em;
        width: 1.7em;
        border-radius: 50%;
        left: 50%;
        top: 50%;
        transform: translate(-50%,-50%);
        -webkit-transform: translate(-50%,-50%);
      }
    }
  }
}
</style>
