/**
 * Created by Jo<PERSON><PERSON> on 2022/01/06. Performance
 * 员工业绩 时效卡销售业绩
 */
import * as API from '@/api/index'

export default {

    // 获取时效卡销售_时效卡销售业绩方案列表
    getSaleTimeCardPerformanceScheme: params => {
        return API.POST('api/saleTimeCardPerformanceScheme/list', params)
    },

    // 新增时效卡销售_组织单位业绩方案
    createSaleTimeCardPerformanceScheme: params => {
        return API.POST('api/saleTimeCardPerformanceScheme/create', params)
    },

    // 删除时效卡销售_组织单位业绩方案
    deleteSaleTimeCardPerformanceScheme: params => {
        return API.POST('api/saleTimeCardPerformanceScheme/delete', params)
    },

    // 获取时效卡销售_分类业绩方案 即编辑
    getSaleTimeCardCategoryPerformance: params => {
        return API.POST('api/saleTimeCardCategoryPerformance/all', params)
    },


    // 获取 所有时效卡经手人/职务业绩-方案(时效卡)
    getSaleTimeCardSchemeHandlerPerformance: params => {
        return API.POST('api/saleTimeCardSchemeHandlerPerformance/all', params)
    },

    // 获取 所有时效卡经手人/职务业绩-方案(套餐卡-时效卡)
    getPackageSaleTimeCardSchemeHandlerPerformance: params => {
        return API.POST('api/saleTimeCardSchemeHandlerPerformance/packageCard', params)
    },

    // 保存 所有时效卡经手人/职务业绩方案
    updateSaleTimeCardSchemeHandlerPerformance: params => {
        return API.POST('api/saleTimeCardSchemeHandlerPerformance/update', params)
    },

    // 获取 分类经手人/职务业绩方案(时效卡)
    getSaleTimeCardCategoryHandlerPerformance: params => {
        return API.POST('api/saleTimeCardCategoryHandlerPerformance/all', params)
    },

    // 获取 分类经手人/职务业绩方案(套餐卡-时效卡)
    getPackageSaleTimeCardCategoryHandlerPerformance: params => {
        return API.POST('api/saleTimeCardCategoryHandlerPerformance/packageCard', params)
    },

    // 保存 分类经手人/职务业绩方案
    updateSaleTimeCardCategoryHandlerPerformance: params => {
        return API.POST('api/saleTimeCardCategoryHandlerPerformance/update', params)
    },

    // 获取 时效卡业绩方案 即时效卡业绩 列表
    getSaleTimeCardPerformance: params => {
        return API.POST('api/saleTimeCardPerformance/all', params)
    },

    // 获取 时效卡业绩下 经手人业绩方案(时效卡)
    getSaleTimeCardHanderPerformance: params => {
        return API.POST('api/saleTimeCardHandlerPerformance/all', params)
    },

    // 获取 时效卡业绩下 经手人业绩方案(套餐卡-时效卡)
    getPackageSaleTimeCardHandlerPerformance: params => {
        return API.POST('api/saleTimeCardHandlerPerformance/packageCard', params)
    },

    // 保存 时效卡业绩下 经手人业绩方案
    updateSaleTimeCardHanderPerformance: params => {
        return API.POST('api/saleTimeCardHandlerPerformance/update', params)
    },

    // 保存 时效卡业绩
    updateSaleTimeCardPerformance: params => {
        return API.POST('api/saleTimeCardPerformance/update', params)
    },

    // 保存 编辑
    updateSaleTimeCardCategoryPerformance: params => {
        return API.POST('api/saleTimeCardCategoryPerformance/update', params)
    },

}
