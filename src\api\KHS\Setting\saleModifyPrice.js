/**
 * Created by preference on 2024/04/19
 *  zmx
 */

import * as API from "@/api/index";
export default {
  /**  查询员工职位 */
  jobtype_all: (params) => {
    return API.POST("api/jobtype/all", params);
  },
  /**  查询门店 */
  entity_list: (params) => {
    return API.POST("api/entity/list", params);
  },
  /** 获取员工职位折扣列表  */
  employeeJobSaleDiscount_list: (params) => {
    return API.POST("api/employeeJobSaleDiscount/list", params);
  },
  /** 创建员工职位折扣  */
  employeeJobSaleDiscount_create: (params) => {
    return API.POST("api/employeeJobSaleDiscount/create", params);
  },
  /**  更新员工职位折扣 */
  employeeJobSaleDiscount_update: (params) => {
    return API.POST("api/employeeJobSaleDiscount/update", params);
  },
  /** 删除员工职位折扣  */
  employeeJobSaleDiscount_delete: (params) => {
    return API.POST("api/employeeJobSaleDiscount/delete", params);
  },
};
