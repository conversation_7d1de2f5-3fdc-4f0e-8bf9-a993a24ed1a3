/**
 * Created by wsf on 2022/01/06.
 * 员工业绩 通用次卡销售业绩设置 api
 */
 import * as API from '@/api/index'

 export default {
   // 获取员工通用次卡销售业绩方案列表
   getGeneralCardPerformanceScheme: params => {
       return API.POST('api/saleGeneralCardPerformanceScheme/list', params)
   },
   // 员工通用次卡销售业绩方案保存
   createGeneralCardPerformanceScheme: params => {
      return API.POST('api/saleGeneralCardPerformanceScheme/create', params)
   },
   // 员工通用次卡销售业绩方案删除
   deleteGeneralCardPerformanceScheme: params => {
      return API.POST('api/saleGeneralCardPerformanceScheme/delete', params)
   },
   // 获取通用次卡分类业绩 
   getGeneralCardCategoryPerformance: params => {
       return API.POST('api/saleGeneralCardCategoryPerformance/all', params)
   },
   // 通用次卡分类业绩保存 
   updateGeneralCardCategoryPerformance: params => {
       return API.POST('api/saleGeneralCardCategoryPerformance/update', params)
   },
   // 获取所有通用次卡经手人业绩
   getGeneralCardSchemeHandlerPerformance: params => {
       return API.POST('api/saleGeneralCardSchemeHandlerPerformance/all', params)
   },
   // 获取所有套餐卡通用次卡经手人业绩
   getPackageCardGeneralCardSchemeHandlerPerformance: params => {
       return API.POST('api/saleGeneralCardSchemeHandlerPerformance/packageCard', params)
   },
   // 所有通用次卡经手人业绩保存
   updateGeneralCardSchemeHandlerPerformance: params => {
       return API.POST('api/saleGeneralCardSchemeHandlerPerformance/update', params)
   },
   // 获取分类通用次卡经手人业绩
   getGeneralCardCategoryHandlerPerformance: params => {
       return API.POST('api/saleGeneralCardCategoryHandlerPerformance/all', params)
   },
   // 获取分类套餐卡通用次卡经手人业绩 
   getPackageCardGeneralCardCategoryHandlerPerformance: params => {
       return API.POST('api/saleGeneralCardCategoryHandlerPerformance/packageCard', params)
   },
   // 分类通用次卡经手人业绩保存
   updateGeneralCardCategoryHandlerPerformance: params => {
       return API.POST('api/saleGeneralCardCategoryHandlerPerformance/update', params)
   },
   // 获取通用次卡业绩
   getGeneralCardPerformance: params => {
       return API.POST('api/saleGeneralCardPerformance/all', params)
   },
   // 保存通用次卡业绩
   updateGeneralCardPerformance: params => {
       return API.POST('api/saleGeneralCardPerformance/update', params)
   },
   // 获取通用次卡下的经手人业绩 
   getGeneralCardHandlerPerformance: params => {
       return API.POST('api/saleGeneralCardHandlerPerformance/all', params)
   },
   // 获取通用次卡下的套餐卡通用次卡经手人业绩 
   getPackageCardGeneralCardHandlerPerformance: params => {
       return API.POST('api/saleGeneralCardHandlerPerformance/packageCard', params)
   },
   // 通用次卡下的经手人业绩保存 
   updateGeneralCardHandlerPerformance: params => {
       return API.POST('api/saleGeneralCardHandlerPerformance/update', params)
   }
 }