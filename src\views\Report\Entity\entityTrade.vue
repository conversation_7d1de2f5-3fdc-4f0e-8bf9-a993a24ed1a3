<template>
  <div class="entityTrade content_body" v-loading="loading">
    <!-- 搜索 -->
    <div class="nav_header">
      <el-form :inline="true" size="small" @submit.native.prevent>
        <el-form-item v-if="EntityList.length > 1" label="门店">
          <el-select v-model="searchData.EntityID" clearable filterable placeholder="请选择门店" :default-first-option="true" @change="handleSearch">
            <el-option v-for="item in EntityList" :key="item.ID" :label="item.EntityName" :value="item.ID"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="时间筛选">
          <el-date-picker v-model="searchData.QueryDate" unlink-panels type="daterange" range-separator="至" value-format="yyyy-MM-dd" start-placeholder="开始日期" end-placeholder="结束日期" @change="handleSearch" :picker-options="pickerOptions"></el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" v-prevent-click @click="handleSearch">搜索</el-button>
        </el-form-item>
        <el-form-item>
          <el-button v-if="isExport" type="primary" size="small" v-prevent-click :loading="downloadLoading" @click="downloadSalePayExcel">导出</el-button>
        </el-form-item>
      </el-form>
    </div>
    <!-- 表格 -->
    <el-table :data="tableData" size="small" show-summary :summary-method="getSummary">
      <el-table-column label="门店" prop="EntityName" fixed width="150"></el-table-column>
      <el-table-column label="实际收款金额" align="center">
        <template slot="header">
          实际收款金额
          <el-popover placement="top-start" trigger="hover">
            <p>1.订单完成时间在统计时间内，现金类收款的金额总和；</p>
            <p>2.订单类型包含：销售订单、充值订单、补欠款单；</p>
            <p>3.现金类包含：现金、微信、支付宝等自定义付款方式</p>
            <el-button type="text" style="color: #dcdfe6; padding: 0" icon="el-icon-info" slot="reference"></el-button>
          </el-popover>
        </template>
        <el-table-column :width="PayMethod.length > 1 ? '' : 120" v-for="item in PayMethod" :key="item.ID" :label="item.Type" prop="Amount" align="right">
          <template slot-scope="scope">{{ getAmount(item.ID, scope.row.PayMethod) | toFixed | NumFormat }}</template>
        </el-table-column>
      </el-table-column>
      <el-table-column label="销售金额" align="center">
        <el-table-column label="实收金额" align="right" prop="SalePayAmount">
          <template slot-scope="scope">{{ scope.row.SalePayAmount | toFixed | NumFormat }}</template>
        </el-table-column>
        <el-table-column label="卡抵扣金额" align="right" width="85" prop="SaleSavingCardDeductionAmount">
          <template slot-scope="scope">{{ scope.row.SaleSavingCardDeductionAmount | toFixed | NumFormat }}</template>
        </el-table-column>
        <el-table-column label="赠送卡抵扣金额" align="right" width="105" prop="SaleLargessSavingCardDeductionAmount">
          <template slot-scope="scope">{{ scope.row.SaleLargessSavingCardDeductionAmount | toFixed | NumFormat }}</template>
        </el-table-column>
        <el-table-column label="赠送金额" align="right" prop="SaleLargessAmount">
          <template slot-scope="scope">{{ scope.row.SaleLargessAmount | toFixed | NumFormat }}</template>
        </el-table-column>
      </el-table-column>

      <el-table-column label="补欠款信息" align="center">
        <el-table-column label="补欠款金额" align="right" width="85" prop="ArrearPayAmount">
          <template slot-scope="scope">{{ scope.row.ArrearPayAmount | toFixed | NumFormat }}</template>
        </el-table-column>

        <el-table-column label="卡抵扣金额" align="right" width="85" prop="ArrearSavingCardDeductionAmount">
          <template slot-scope="scope">{{ scope.row.ArrearSavingCardDeductionAmount | toFixed | NumFormat }}</template>
        </el-table-column>

        <el-table-column label="赠送卡抵扣金额" align="right" width="85" prop="ArrearLargessSavingCardDeductionAmount">
          <template slot-scope="scope">{{ scope.row.ArrearLargessSavingCardDeductionAmount | toFixed | NumFormat }}</template>
        </el-table-column>
      </el-table-column>
      <el-table-column label="退款金额" align="center">
        <el-table-column label="退款金额" align="right" prop="RefundPayAmount">
          <template slot-scope="scope">
            <span v-if="scope.row.RefundPayAmount > 0" class="color_red">-{{ scope.row.RefundPayAmount | toFixed | NumFormat }}</span>
            <span v-else>{{ scope.row.RefundPayAmount | toFixed | NumFormat }}</span>
          </template>
        </el-table-column>
        <el-table-column label="退回卡金额" align="right" width="85" prop="RefundSavingCardDeductionAmount">
          <template slot-scope="scope">
            <span v-if="scope.row.RefundSavingCardDeductionAmount > 0" class="color_red">-{{ scope.row.RefundSavingCardDeductionAmount | toFixed | NumFormat }}</span>
            <span v-else>{{ scope.row.RefundSavingCardDeductionAmount | toFixed | NumFormat }}</span>
          </template>
        </el-table-column>
        <el-table-column label="退回赠送卡金额" align="right" width="105" prop="RefundLargessSavingCardDeductionAmount">
          <template slot-scope="scope">
            <span v-if="scope.row.RefundLargessSavingCardDeductionAmount > 0" class="color_red">-{{ scope.row.RefundLargessSavingCardDeductionAmount | toFixed | NumFormat }}</span>
            <span v-else>{{ scope.row.RefundLargessSavingCardDeductionAmount | toFixed | NumFormat }}</span>
          </template>
        </el-table-column>
        <el-table-column label="退回赠送金额" align="right" width="95" prop="RefundLargessAmount">
          <template slot-scope="scope">
            <span v-if="scope.row.RefundLargessAmount > 0" class="color_red">-{{ scope.row.RefundLargessAmount | toFixed | NumFormat }}</span>
            <span v-else>{{ scope.row.RefundLargessAmount | toFixed | NumFormat }}</span>
          </template>
        </el-table-column>
      </el-table-column>
      <el-table-column label="消耗金额" align="center">
        <el-table-column label="现金金额" align="right" prop="TreatPayAmount">
          <template slot-scope="scope">{{ scope.row.TreatPayAmount | toFixed | NumFormat }}</template>
        </el-table-column>
        <el-table-column label="卡抵扣金额" align="right" width="85" prop="TreatCardDeductionAmount">
          <template slot-scope="scope">{{ scope.row.TreatCardDeductionAmount | toFixed | NumFormat }}</template>
        </el-table-column>
        <el-table-column label="赠送卡抵扣" align="right" width="85" prop="TreatLargesCardDeductionAmount">
          <template slot-scope="scope">{{ scope.row.TreatLargesCardDeductionAmount | toFixed | NumFormat }}</template>
        </el-table-column>
        <el-table-column label="赠送金额" align="right" prop="TreatLargessAmount">
          <template slot-scope="scope">{{ scope.row.TreatLargessAmount | toFixed | NumFormat }}</template>
        </el-table-column>
      </el-table-column>
      <el-table-column label="退消耗金额" align="center">
        <el-table-column label="退回金额" align="right" prop="RefundTreatPayAmount">
          <template slot-scope="scope">
            <span v-if="scope.row.RefundTreatPayAmount > 0" class="color_red">-{{ scope.row.RefundTreatPayAmount | toFixed | NumFormat }}</span>
            <span v-else>{{ scope.row.RefundTreatPayAmount | toFixed | NumFormat }}</span>
          </template>
        </el-table-column>
        <el-table-column label="退卡扣金额" align="right" width="85" prop="RefundTreatCardDeductionAmount">
          <template slot-scope="scope">
            <span v-if="scope.row.RefundTreatCardDeductionAmount > 0" class="color_red">-{{ scope.row.RefundTreatCardDeductionAmount | toFixed | NumFormat }}</span>
            <span v-else>{{ scope.row.RefundTreatCardDeductionAmount | toFixed | NumFormat }}</span>
          </template>
        </el-table-column>
        <el-table-column label="退赠卡扣金额" align="right" width="95" prop="RefundTreatLargesCardDeductionAmount">
          <template slot-scope="scope">
            <span v-if="scope.row.RefundTreatLargesCardDeductionAmount > 0" class="color_red">-{{ scope.row.RefundTreatLargesCardDeductionAmount | toFixed | NumFormat }}</span>
            <span v-else>{{ scope.row.RefundTreatLargesCardDeductionAmount | toFixed | NumFormat }}</span>
          </template>
        </el-table-column>
        <el-table-column label="赠送金额" align="right" prop="RefundTreatLargessAmount">
          <template slot-scope="scope">
            <span v-if="scope.row.RefundTreatLargessAmount > 0" class="color_red">-{{ scope.row.RefundTreatLargessAmount | toFixed | NumFormat }}</span>
            <span v-else>{{ scope.row.RefundTreatLargessAmount | toFixed | NumFormat }}</span>
          </template>
        </el-table-column>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <div class="pad_15 text_right">
      <el-pagination background v-if="paginations.total > 0" @current-change="handlePageChange" :current-page.sync="paginations.page" :page-size="paginations.page_size" :layout="paginations.layout" :total="paginations.total"></el-pagination>
    </div>
  </div>
</template>

<script>
import API from "@/api/Report/Entity/entityTrade";
const dayjs = require("dayjs");
const isoWeek = require("dayjs/plugin/isoWeek");
dayjs.extend(isoWeek);

var Enumerable = require("linq");
export default {
  name: "ReportEntityTrade",

  beforeRouteEnter(to, from, next) {
    next((vm) => {
      vm.isExport = vm.$permission.permission(to.meta.Permission, "Report-Entity-EntityTrade-Export");
    });
  },
  props: {},
  /** 监听数据变化   */
  watch: {},
  /**  引入的组件  */
  components: {},
  /**  Vue 实例的数据对象**/
  data() {
    return {
      isExport: false,
      loading: false,
      downloadLoading: false,
      EntityList: [], //门店数据
      tableData: [], // 表格数据
      SumOutputForm: {}, // 合计数据
      PayMethod: [], // 实际收款金额
      searchData: {
        EntityID: null,
        QueryDate: [this.$formatDate(new Date(), "YYYY-MM-DD"), this.$formatDate(new Date(), "YYYY-MM-DD")],
      },
      paginations: {
        page: 1, // 当前位于哪页
        total: 0, // 总数
        page_size: 10, // 1页显示多少条
        layout: "total, prev, pager, next,jumper", // 翻页属性
      },
      pickerOptions: {
        shortcuts: [
          {
            text: "今天",
            onClick: (picker) => {
              let end = new Date();
              let start = new Date();
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "本周",
            onClick: (picker) => {
              let end = new Date();
              let weekDay = dayjs(end).isoWeekday();
              let start = dayjs(end)
                .subtract(weekDay - 1, "day")
                .toDate();
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "本月",
            onClick: (picker) => {
              let end = new Date();
              let start = dayjs(dayjs(end).format("YYYY-MM") + "01").toDate();
              picker.$emit("pick", [start, end]);
            },
          },
        ],
      },
    };
  },
  /**计算属性  */
  computed: {},
  /**  方法集合  */
  methods: {
    /* 搜索 */
    handleSearch() {
      let that = this;
      that.paginations.page = 1;
      that.getEntityTradingList();
    },
    /* 分页 */
    handlePageChange(page) {
      let that = this;
      that.paginations.page = page;
      that.getEntityTradingList();
    },
    /* 导出 */
    downloadSalePayExcel() {
      let that = this;
      if (that.searchData.QueryDate != null) {
        if (dayjs(that.searchData.QueryDate[0]).add(366, "day").valueOf() < dayjs(that.searchData.QueryDate[1]).valueOf()) {
          that.$message.error("时间筛选范围不能超366天");
          return;
        }
        let params = {
          EntityID: that.searchData.EntityID,
          StartDate: that.searchData.QueryDate[0],
          EndDate: that.searchData.QueryDate[1],
        };
        that.downloadLoading = true;
        API.entityTradingExcel(params)
          .then((res) => {
            this.$message.success({
              message: "正在导出",
              duration: "4000",
            });
            const link = document.createElement("a");
            let blob = new Blob([res], { type: "application/octet-stream" });
            link.style.display = "none";
            link.href = URL.createObjectURL(blob);
            link.download = "门店交易报表.xlsx"; //下载的文件名
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
          })
          .finally(function () {
            that.downloadLoading = false;
          });
      } else {
        this.$message.error({
          message: "请选择筛选时间",
          duration: 2000,
        });
      }
    },
    /* 获取表格数据 */
    getEntityTradingList() {
      let that = this;
      if (that.searchData.QueryDate != null) {
        if (dayjs(that.searchData.QueryDate[0]).add(366, "day").valueOf() < dayjs(that.searchData.QueryDate[1]).valueOf()) {
          that.$message.error("时间筛选范围不能超366天");
          return;
        }
        that.loading = true;
        let params = {
          EntityID: that.searchData.EntityID,
          StartDate: that.searchData.QueryDate[0],
          EndDate: that.searchData.QueryDate[1],
          PageNum: that.paginations.page,
        };
        API.getEntityTradingList(params)
          .then((res) => {
            if (res.StateCode == 200) {
              that.tableData = res.Data.Detail.List;
              that.paginations.page_size = res.Data.Detail.PageSize;
              that.paginations.total = res.Data.Detail.Total;
              that.SumOutputForm = res.Data.SumOutputForm;
              that.PayMethod = res.Data.SumOutputForm.PayMethod;
            } else {
              this.$message.error({
                message: res.Message,
                duration: 2000,
              });
            }
          })
          .finally(function () {
            that.loading = false;
          });
      } else {
        that.pickerMinDate = null;
        this.$message.error({
          message: "请选择筛选时间",
          duration: 2000,
        });
      }
    },
    /* 获取实际收款金额 */
    getAmount(item, PayMethod) {
      var pay = Enumerable.from(PayMethod)
        .where((v) => {
          return v.ID == item;
        })
        .toArray();
      if (pay.length > 0) {
        return pay[0].Amount;
      } else {
        return 0;
      }
    },
    /* 合计 */
    getSummary({ columns }) {
      let sums = [];
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = <span class="font_weight_600">{this.SumOutputForm ? this.SumOutputForm.EntityName : "合计"}</span>;
          return;
        }
        var filter_NumFormat = this.$options.filters["NumFormat"];
        switch (column.property) {
          case "Amount":
            sums[index] = <span class="font_weight_600">{filter_NumFormat(this.SumOutputForm.PayMethod ? this.SumOutputForm.PayMethod[index - 1].Amount : 0)}</span>;
            break;
          case "SalePayAmount":
            sums[index] = <span class="font_weight_600">{filter_NumFormat(this.SumOutputForm ? this.SumOutputForm.SalePayAmount : 0)}</span>;
            break;
          case "SaleSavingCardDeductionAmount":
            sums[index] = <span class="font_weight_600">{filter_NumFormat(this.SumOutputForm ? this.SumOutputForm.SaleSavingCardDeductionAmount : 0)}</span>;
            break;
          case "SaleLargessSavingCardDeductionAmount":
            sums[index] = <span class="font_weight_600">{filter_NumFormat(this.SumOutputForm ? this.SumOutputForm.SaleLargessSavingCardDeductionAmount : 0)}</span>;
            break;
          case "SaleLargessAmount":
            sums[index] = <span class="font_weight_600">{filter_NumFormat(this.SumOutputForm ? this.SumOutputForm.SaleLargessAmount : 0)}</span>;
            break;
          case "ArrearPayAmount":
            sums[index] = <span class="font_weight_600">{filter_NumFormat(this.SumOutputForm ? this.SumOutputForm.ArrearPayAmount : 0)}</span>;
            break;
          case "RefundPayAmount":
            if (this.SumOutputForm.RefundPayAmount) {
              sums[index] = <span class="font_weight_600 color_red">-{filter_NumFormat(this.SumOutputForm ? this.SumOutputForm.RefundPayAmount : 0)}</span>;
            } else {
              sums[index] = <span class="font_weight_600 ">{filter_NumFormat(this.SumOutputForm ? this.SumOutputForm.RefundPayAmount : 0)}</span>;
            }
            break;
          case "RefundSavingCardDeductionAmount":
            if (this.SumOutputForm.RefundSavingCardDeductionAmount > 0) {
              sums[index] = <span class="font_weight_600 color_red">-{filter_NumFormat(this.SumOutputForm ? this.SumOutputForm.RefundSavingCardDeductionAmount : 0)}</span>;
            } else {
              sums[index] = <span class="font_weight_600 ">{filter_NumFormat(this.SumOutputForm ? this.SumOutputForm.RefundSavingCardDeductionAmount : 0)}</span>;
            }
            break;
          case "RefundLargessSavingCardDeductionAmount":
            if (this.SumOutputForm.RefundLargessSavingCardDeductionAmount > 0) {
              sums[index] = <span class="font_weight_600 color_red">-{filter_NumFormat(this.SumOutputForm ? this.SumOutputForm.RefundLargessSavingCardDeductionAmount : 0)}</span>;
            } else {
              sums[index] = <span class="font_weight_600 ">{filter_NumFormat(this.SumOutputForm ? this.SumOutputForm.RefundLargessSavingCardDeductionAmount : 0)}</span>;
            }
            break;
          case "RefundLargessAmount":
            if (this.SumOutputForm.RefundLargessAmount > 0) {
              sums[index] = <span class="font_weight_600 color_red">-{filter_NumFormat(this.SumOutputForm ? this.SumOutputForm.RefundLargessAmount : 0)}</span>;
            } else {
              sums[index] = <span class="font_weight_600 ">{filter_NumFormat(this.SumOutputForm ? this.SumOutputForm.RefundLargessAmount : 0)}</span>;
            }
            break;
          case "TreatPayAmount":
            sums[index] = <span class="font_weight_600">{filter_NumFormat(this.SumOutputForm ? this.SumOutputForm.TreatPayAmount : 0)}</span>;
            break;
          case "TreatCardDeductionAmount":
            sums[index] = <span class="font_weight_600">{filter_NumFormat(this.SumOutputForm ? this.SumOutputForm.TreatCardDeductionAmount : 0)}</span>;
            break;
          case "TreatLargesCardDeductionAmount":
            sums[index] = <span class="font_weight_600">{filter_NumFormat(this.SumOutputForm ? this.SumOutputForm.TreatLargesCardDeductionAmount : 0)}</span>;
            break;
          case "TreatLargessAmount":
            sums[index] = <span class="font_weight_600">{filter_NumFormat(this.SumOutputForm ? this.SumOutputForm.TreatLargessAmount : 0)}</span>;
            break;
          case "RefundTreatPayAmount":
            if (this.SumOutputForm.RefundTreatPayAmount > 0) {
              sums[index] = <span class="font_weight_600 color_red">-{filter_NumFormat(this.SumOutputForm ? this.SumOutputForm.RefundTreatPayAmount : 0)}</span>;
            } else {
              sums[index] = <span class="font_weight_600">{filter_NumFormat(this.SumOutputForm ? this.SumOutputForm.RefundTreatPayAmount : 0)}</span>;
            }
            break;
          case "RefundTreatCardDeductionAmount":
            if (this.SumOutputForm.RefundTreatCardDeductionAmount) {
              sums[index] = <span class="font_weight_600 color_red">-{filter_NumFormat(this.SumOutputForm ? this.SumOutputForm.RefundTreatCardDeductionAmount : 0)}</span>;
            } else {
              sums[index] = <span class="font_weight_600">{filter_NumFormat(this.SumOutputForm ? this.SumOutputForm.RefundTreatCardDeductionAmount : 0)}</span>;
            }
            break;
          case "RefundTreatLargesCardDeductionAmount":
            if (this.SumOutputForm.RefundTreatLargesCardDeductionAmount > 0) {
              sums[index] = <span class="font_weight_600 color_red">-{filter_NumFormat(this.SumOutputForm ? this.SumOutputForm.RefundTreatLargesCardDeductionAmount : 0)}</span>;
            } else {
              sums[index] = <span class="font_weight_600">{filter_NumFormat(this.SumOutputForm ? this.SumOutputForm.RefundTreatLargesCardDeductionAmount : 0)}</span>;
            }
            break;
          case "RefundTreatLargessAmount":
            if (this.SumOutputForm.RefundTreatLargessAmount > 0) {
              sums[index] = <span class="font_weight_600 color_red">-{filter_NumFormat(this.SumOutputForm ? this.SumOutputForm.RefundTreatLargessAmount : 0)}</span>;
            } else {
              sums[index] = <span class="font_weight_600">{filter_NumFormat(this.SumOutputForm ? this.SumOutputForm.RefundTreatLargessAmount : 0)}</span>;
            }
            break;

          case "ArrearSavingCardDeductionAmount":
            if (this.SumOutputForm.ArrearSavingCardDeductionAmount > 0) {
              sums[index] = <span class="font_weight_600 color_red">-{filter_NumFormat(this.SumOutputForm ? this.SumOutputForm.ArrearSavingCardDeductionAmount : 0)}</span>;
            } else {
              sums[index] = <span class="font_weight_600">{filter_NumFormat(this.SumOutputForm ? this.SumOutputForm.ArrearSavingCardDeductionAmount : 0)}</span>;
            }
            break;
          case "ArrearLargessSavingCardDeductionAmount":
            if (this.SumOutputForm.ArrearLargessSavingCardDeductionAmount > 0) {
              sums[index] = <span class="font_weight_600 color_red">-{filter_NumFormat(this.SumOutputForm ? this.SumOutputForm.ArrearLargessSavingCardDeductionAmount : 0)}</span>;
            } else {
              sums[index] = <span class="font_weight_600">{filter_NumFormat(this.SumOutputForm ? this.SumOutputForm.ArrearLargessSavingCardDeductionAmount : 0)}</span>;
            }
            break;
        }
      });
      return sums;
    },
    /* 获取门店 */
    async getStoreList() {
      var that = this;
      let res = await API.getStoreList();
      if (res.StateCode == 200) {
        that.EntityList = res.Data;
      } else {
        that.$message.error({
          message: res.Message,
          duration: 2000,
        });
      }
    },
  },
  /** 创建实例之前执行函数   */
  beforeCreate() {},
  /**  实例创建完成之后  */
  created() {},
  /**  在挂载开始之前被调用  */
  beforeMount() {},
  /**  实例被挂载后调用  */
  mounted() {
    let that = this;
    this.isExport = this.$permission.permission(this.$route.meta.Permission, "Report-Entity-EntityTrade-Export");
    that.getStoreList();
    that.getEntityTradingList();
  },
  /**  数据更新 之前 调用  */
  beforeUpdate() {},
  /** 数据更新 完成 调用   */
  updated() {},
  /**  实例销毁之前调用  */
  beforeDestroy() {},
  /**  实例销毁后调用  */
  destroyed() {},
};
</script>

<style lang="scss">
.entityTrade {
}
</style>
