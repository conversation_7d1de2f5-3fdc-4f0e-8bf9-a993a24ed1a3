/**
 * Created by preference on 2023/11/29
 *  zmx
 */

import * as API from "@/api/index";
export default {
  /**  查询默认负责员工 */
  clueDistribution_employeeConfigAll: (params) => {
    return API.POST("api/clueDistribution/employeeConfigAll", params);
  },
  /** 新增默认负责员工  */
  clueDistribution_addEmployeeConfig: (params) => {
    return API.POST("api/clueDistribution/addEmployeeConfig", params);
  },
  /** 查询区域负责员工  */
  clueDistribution_list: (params) => {
    return API.POST("api/clueDistribution/list", params);
  },
  /** 查询区域详情  */
  clueDistribution_detail: (params) => {
    return API.POST("api/clueDistribution/detail", params);
  },
  /** 区域负责人新增  */
  clueDistribution_add: (params) => {
    return API.POST("api/clueDistribution/add", params);
  },
  /** 区域负责人更新  */
  clueDistribution_update: (params) => {
    return API.POST("api/clueDistribution/update", params);
  },
  /** 员工查询  */
  employee_listAll: (params) => {
    return API.POST("api/employee/listAll", params);
  },
};