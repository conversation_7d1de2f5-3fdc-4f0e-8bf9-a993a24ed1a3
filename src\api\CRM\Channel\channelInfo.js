/**
 * Created by wsf on 2022/01/17
 *  渠道配置 api
 */
import * as API from "@/api/index";

export default {
  /* 获取渠道配置列表 */
  getChannelList: (params) => {
    return API.POST("api/channel/list", params);
  },
  /* 新增渠道配置 */
  creatChannel: (params) => {
    return API.POST("api/channel/create", params);
  },
  /* 更新渠道配置 */
  updateChannel: (params) => {
    return API.POST("api/channel/update", params);
  },
  /* 渠道 */
  getAllchannel: (params) => {
    return API.POST("api/channel/all", params);
  },
  /* 渠道 非树状列表 */
  getChannelInfoList: (params) => {
    return API.POST("api/channel/info", params);
  },
  /* 渠道 非树状列表 */
  channel_customerInfo: (params) => {
    return API.POST("api/channel/customerInfo", params);
  },
  /* 批量更新 */
  updateParentChannel: (params) => {
    return API.POST("api/channel/updateParent", params);
  },
  /* 获取左侧树形列表 */
  getTreeList: (params) => {
    return API.POST("api/channel/treeList", params);
  },
  /* 左侧树形列表移动 */
  moveChannel: (params) => {
    return API.POST("api/channel/move", params);
  },
  /* 业务代表 */
  geTemployeeAll: (params) => {
    return API.POST("api/channel/employeeAll", params);
  },

  /* 渠道详情 */
  getChannelDetail: (params) => {
    return API.POST("api/channel/detail", params);
  },
  // 余量导出
  channel_excel: (params) => {
    return API.exportExcel("api/channel/excel", params);
  },
  /* 开发人员转移 */
  channel_transferDeveloper: (params) => {
    return API.POST("api/channel/transferDeveloper", params);
  },
  /* 市场咨询转移 */
  channel_transferConsultant: (params) => {
    return API.POST("api/channel/transferConsultant", params);
  },
};
