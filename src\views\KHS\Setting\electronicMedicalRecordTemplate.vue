<template>
  <div class="electronicMedicalRecordTemplate content_body">
    <el-container style="height: 100%">
      <el-aside width="280px" style="border-right: 1px solid #ddd; padding: 10px">
        <el-row>
          <el-col :span="18">
            <el-input v-model="searchCateName" size="small" placeholder="请输入分类名称搜索"></el-input>
          </el-col>
          <el-col :span="6" class="text_right">
            <el-button @click="addCategoryClick" type="primary" size="small" v-prevent-click>新增</el-button>
          </el-col>
        </el-row>
        <el-tree
          class="padtp_10 hidden_node__expand-icon"
          draggable
          node-key="code"
          :data="category_list.filter((item) => !searchCateName || item.Name.toLowerCase().includes(searchCateName.toLowerCase()))"
          :auto-expand-parent="true"
          @node-click="handleNodeClick"
          @node-drop="nodeDropComplete"
          :allow-drop="allowDrop"
        >
          <span slot-scope="{ data }" class="custom-tree-node">
            <span class="font_14" :class="data.ID == select_category.ID ? 'customTreeColor' : ''">{{ data.Name }} </span>
            <span class="custom-tree-node-action">
              <el-button type="text" size="mini" icon="el-icon-plus" @click.stop="addTemplateClick(data)">添加</el-button>
              <el-button type="text" size="mini" icon="el-icon-delete" @click.stop="removeCategoryClick(data)">删除</el-button>
            </span>
          </span>
        </el-tree>
      </el-aside>

      <el-container>
        <el-main style="padding: 0px">
          <div>
            <el-table size="small" ref="multipleTable" :data="template_list" tooltip-effect="light">
              <el-table-column prop="Name" label="模板名称"></el-table-column>

              <el-table-column label="移动" min-width="180px">
                <template slot-scope="scope">
                  <el-button size="small" type="primary" circle icon="el-icon-upload2" @click="upOneClick(scope.row, scope.$index)" v-prevent-click :disabled="scope.$index == 0"></el-button>
                  <el-button size="small" type="primary" circle icon="el-icon-top" @click="upClick(scope.row, scope.$index)" v-prevent-click :disabled="scope.$index == 0"></el-button>
                  <el-button size="small" type="primary" circle icon="el-icon-bottom" @click="downClick(scope.row, scope.$index)" v-prevent-click :disabled="scope.$index == template_list.length - 1"> </el-button>
                  <el-button size="small" type="primary" circle icon="el-icon-download" @click="downOneClick(scope.row, scope.$index)" v-prevent-click :disabled="scope.$index == template_list.length - 1"></el-button>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="145px">
                <template slot-scope="scope">
                  <el-button type="primary" size="small" @click="showEdit(scope.row)" v-prevent-click>编辑</el-button>
                  <el-button type="danger" size="small" @click="removeTemplateClick(scope.row)" v-prevent-click>删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-main>
      </el-container>
    </el-container>
    <!--新增分类弹窗-->
    <el-dialog title="新增分类" :visible.sync="catDialogVisible" width="30%">
      <div>
        <el-form :model="addCatRuleForm" :rules="addCatRules" ref="addCatRuleForm" label-width="120px" size="small" @submit.native.prevent>
          <el-form-item label="标签名称" prop="Name">
            <el-input size="small" width="60%" v-model="addCatRuleForm.Name"></el-input>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="catDialogVisible = false" v-prevent-click>取 消</el-button>
        <el-button type="primary" size="small" :loading="modalLoading" v-prevent-click @click="saveCategoryClick">保存</el-button>
      </div>
    </el-dialog>

    <!-- 新增 -->
    <el-dialog v-if="dialogVisible" :visible.sync="dialogVisible" :title="isAdd ? '新增病例模板' : '编辑病例模板'" :close-on-click-modal="false" width="55%">
      <el-form ref="addRuleForm" size="small" :model="addRuleForm" :rules="rules">
        <el-row>
          <!-- <el-col :span="8">
						<el-form-item size="small">
							<el-button size="small" icon="el-icon-upload">导入HTML模板</el-button>
						</el-form-item>
					</el-col> -->

          <el-col :span="8">
            <el-form-item size="small" label="模板名称" label-width="100px" prop="Name">
              <el-input v-model="addRuleForm.Name" size="small" placeholder="请输入模板名称"> </el-input>
            </el-form-item>
          </el-col>

          <el-col :offset="8" :span="8" class="text_right">
            <el-form-item size="small">
              <!-- <el-button @click="getDefaultTemplateClick" size="small">还原系统默认模板</el-button> -->
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item size="small" prop="Template">
          <editor ref="editorRef" v-model="addRuleForm.Template" :resizable-toolbar="true" style="height: 560px">
            <editor-tool v-for="item in editorTools" :key="item" :name="item"></editor-tool>
          </editor>
        </el-form-item>
      </el-form>

      <div slot="footer">
        <el-button @click="dialogVisible = false" size="small" v-prevent-click>取 消</el-button>
        <el-button type="primary" @click="saveTemplateClick" :loading="addLoading" size="small" v-prevent-click>保存</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import "@progress/kendo-ui";
import "@progress/kendo-theme-default/dist/all.css";
import "@progress/kendo-ui/js/messages/kendo.messages.zh-CN.js";
import { Editor, EditorTool } from "@progress/kendo-editor-vue-wrapper";
import API from "@/api/KHS/Setting/electronicMedicalRecordTemplate.js";

export default {
  name: "ElectronicMedicalRecordTemplate",
  props: {},
  /**  引入的组件  */
  components: {
    editor: Editor,
    "editor-tool": EditorTool,
  },
  /**  Vue 实例的数据对象**/
  data() {
    return {
      catDialogVisible: false,
      modalLoading: false,
      dialogVisible: false,
      addLoading: false,
      isAdd: false,

      searchCateName: "",
      category_list: [],
      select_category: { ID: "", Name: "" },
      template_list: [],
      addCatRuleForm: { Name: "" },
      addCatRules: { Name: [{ required: true, message: "请输入分类名称", trigger: "change" }] },

      templateTypeData: [
        {
          label: "库存相关",
          code: "stock",
          children: [
            {
              label: "入库单",
              code: "instock",
            },
            {
              label: "出库单",
              code: "outstock",
            },
            {
              label: "调拨单",
              code: "stockmove",
            },
            {
              label: "盘点单",
              code: "stockcheck",
            },
            {
              label: "采购入库单",
              code: "purchasestorage",
            },

            {
              label: "采购退货单",
              code: "purchaserefund",
            },
            {
              label: "门店要货单",
              code: "entityapplystock",
            },

            {
              label: "门店退货单",
              code: "entityrefundstock",
            },
          ],
        },
      ],

      defaultProps: {
        children: "children",
        label: "label",
      },
      entityDefaultExpandedKeys: ["stock"],

      editorTools: [
        "bold",
        "italic",
        "underline",
        "strikethrough",
        "justifyLeft",
        "justifyCenter",
        "justifyRight",
        "justifyFull",
        "insertUnorderedList",
        "insertOrderedList",
        "indent",
        "outdent",
        "createLink",
        "unlink",
        "insertImage",
        "insertFile",
        "fontSize",
        "tableWizard",
        "createTable",
        "addRowAbove",
        "addRowBelow",
        "addColumnLeft",
        "addColumnRight",
        "deleteRow",
        "deleteColumn",
        "formatting",
        "cleanFormatting",

        "viewHtml",
        "print",
        "pdf",
        "copyFormat",
        "applyFormat",
      ],

      addRuleForm: {
        Name: "",
        Template: "",
      },
      rules: {
        Name: [
          {
            required: true,
            message: "请输入模板名称",
            trigger: ["change", "blur"],
          },
        ],
        Template: [
          {
            required: true,
            message: "请编辑模板内容",
            trigger: ["change", "blur"],
          },
        ],
      },
    };
  },
  /**计算属性  */
  computed: {},
  /**  方法集合  */
  methods: {
    /** **************** 分类相关操作 **************** */
    /**  新增分类  */
    addCategoryClick() {
      this.addCatRuleForm.Name = "";
      this.catDialogVisible = true;
    },
    /**  保存分类  */
    saveCategoryClick() {
      let that = this;
      that.$refs.addCatRuleForm.validate((valid) => {
        if (valid) {
          that.electronicMedicalRecordCategory_create();
        }
      });
    },
    /**  点击节点  */
    handleNodeClick(node) {
      let that = this;
      that.select_category = node;
      that.electronicMedicalRecordTemplate_list();
    },
    /**  节点拖拽完成  */
    nodeDropComplete(draggingNode, dropNode) {
      this.electronicMedicalRecordCategory_move(draggingNode.data.ID, dropNode.data.ID);
    },
    /**  删除  */
    removeCategoryClick(data) {
      this.$confirm("此次操作将永久删除模板分类，是否继续？", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.electronicMedicalRecordCategory_delete(data.ID);
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    /**  将拖拽节点节点放置在 目标节点前    */
    allowDrop(draggingNode, dropNode, type) {
      return type !== "inner";
    },

    /** **************** 模板相关操作 **************** */
    /**  添加打印模板  */
    addTemplateClick(node) {
      let that = this;
      that.isAdd = true;
      that.select_category = node;
      that.addRuleForm = {
        ElectronicMedicalRecordCategoryID: node.ID,
        Name: "",
        Template: "",
      };
      that.dialogVisible = true;
    },

    /**   保存打印模板 */
    saveTemplateClick() {
      let that = this;
      that.$refs.addRuleForm.validate((valid) => {
        if (valid) {
          if (this.isAdd) {
            that.electronicMedicalRecordTemplate_create();
          } else {
            that.electronicMedicalRecordTemplate_update();
          }
        }
      });
    },
    /**   删除 */
    removeTemplateClick(row) {
      let that = this;
      that
        .$confirm("此次操作将永久删除病例模板，是否继续？", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
        .then(() => {
          that.electronicMedicalRecordTemplate_delete(row.ID);
        })
        .catch(() => {
          that.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    /**  编辑  */
    showEdit(row) {
      let that = this;
      that.isAdd = false;
      that.addRuleForm = {
        ID: row.ID,
        Name: row.Name,
        Template: row.Template,
      };
      that.dialogVisible = true;
    },

    // 移动首部
    upOneClick: function (row) {
      var that = this;
      that.electronicMedicalRecordTemplate_move(row.ID, "");
    },
    // 移动尾部
    downOneClick: function (row, index) {
      var that = this;
      var tabIndex = that.template_list.length;
      var beforeId = "";
      if (index < tabIndex - 1) {
        beforeId = that.template_list[tabIndex - 1].ID;
      }
      that.electronicMedicalRecordTemplate_move(row.ID, beforeId);
    },
    // 向上
    upClick: function (row, index) {
      var that = this;
      var beforeId = "";
      if (index > 1) {
        beforeId = that.template_list[index - 2].ID;
      }
      that.electronicMedicalRecordTemplate_move(row.ID, beforeId);
    },
    // 向下
    downClick: function (row, index) {
      var that = this;
      var beforeId = "";
      if (index + 1 != that.template_list.length) {
        beforeId = that.template_list[index + 1].ID;
      }
      that.electronicMedicalRecordTemplate_move(row.ID, beforeId);
    },

    /**  ***************************  */
    /**   病历模板分类查询 */
    async electronicMedicalRecordCategory_all() {
      let that = this;
      let params = {};
      let res = await API.electronicMedicalRecordCategory_all(params);
      if (res.StateCode == 200) {
        this.category_list = res.Data;
        if (this.category_list.length > 0) {
          that.select_category = this.category_list[0];
          this.electronicMedicalRecordTemplate_list();
        }
      } else {
        that.$message.error(res.Message);
      }
    },
    /**   病历模板分类创建 */
    async electronicMedicalRecordCategory_create() {
      let that = this;
      let params = {
        Name: that.addCatRuleForm.Name,
      };
      let res = await API.electronicMedicalRecordCategory_create(params);
      if (res.StateCode == 200) {
        that.electronicMedicalRecordCategory_all();
        that.$message.success("添加成功");
        that.catDialogVisible = false;
      } else {
        that.$message.error(res.Message);
      }
    },
    /**  病历模板分类删除  */
    async electronicMedicalRecordCategory_delete(ID) {
      let that = this;
      let params = { ID: ID };
      let res = await API.electronicMedicalRecordCategory_delete(params);
      if (res.StateCode == 200) {
        that.electronicMedicalRecordCategory_all();
        that.$message.success("操作成功");
      } else {
        that.$message.error(res.Message);
      }
    },
    /**  病历分类排序  */
    async electronicMedicalRecordCategory_move(MoveID, BeforeID) {
      let that = this;
      let params = {
        MoveID: MoveID,
        BeforeID: BeforeID,
      };
      let res = await API.electronicMedicalRecordCategory_move(params);
      if (res.StateCode == 200) {
        this.electronicMedicalRecordCategory_all();
        this.$message.success("操作成功");
      } else {
        that.$message.error(res.Message);
      }
    },
    /**  病历模板查询  */
    async electronicMedicalRecordTemplate_list() {
      let that = this;
      let params = { ElectronicMedicalRecordCategoryID: that.select_category.ID };
      let res = await API.electronicMedicalRecordTemplate_list(params);
      if (res.StateCode == 200) {
        that.template_list = res.Data;
      } else {
        that.$message.error(res.Message);
      }
    },
    /**  病历模板创建  */
    async electronicMedicalRecordTemplate_create() {
      let that = this;
      let params = Object.assign({}, that.addRuleForm);
      let res = await API.electronicMedicalRecordTemplate_create(params);
      if (res.StateCode == 200) {
        that.electronicMedicalRecordTemplate_list();
        that.$message.success("添加成功");
        that.dialogVisible = false;
      } else {
        that.$message.error(res.Message);
      }
    },
    /**  病历模板编辑  */
    async electronicMedicalRecordTemplate_update() {
      let that = this;
      let params = Object.assign({}, that.addRuleForm);
      let res = await API.electronicMedicalRecordTemplate_update(params);
      if (res.StateCode == 200) {
        that.electronicMedicalRecordTemplate_list();
        that.$message.success("更新成功");
        that.dialogVisible = false;
      } else {
        that.$message.error(res.Message);
      }
    },
    /**  病历模板删除  */
    async electronicMedicalRecordTemplate_delete(ID) {
      let that = this;
      let params = { ID: ID };
      let res = await API.electronicMedicalRecordTemplate_delete(params);
      if (res.StateCode == 200) {
        that.electronicMedicalRecordTemplate_list();
        that.$message.success("操作成功");
      } else {
        that.$message.error(res.Message);
      }
    },
    /**  病历模板排序  */
    async electronicMedicalRecordTemplate_move(MoveID, BeforeID) {
      let that = this;
      let params = { MoveID: MoveID, BeforeID: BeforeID };
      let res = await API.electronicMedicalRecordTemplate_move(params);
      if (res.StateCode == 200) {
        that.$message.success("操作成功");
        that.electronicMedicalRecordTemplate_list();
      } else {
        that.$message.error(res.Message);
      }
    },
  },
  /** 监听数据变化   */
  watch: {},
  /** 创建实例之前执行函数   */
  beforeCreate() {},
  /**  实例创建完成之后  */
  created() {
    this.electronicMedicalRecordCategory_all();
  },
  /**  在挂载开始之前被调用  */
  beforeMount() {},
  /**  实例被挂载后调用  */
  mounted() {},
  /**  数据更新 之前 调用  */
  beforeUpdate() {},
  /** 数据更新 完成 调用   */
  updated() {},
  /**  实例销毁之前调用  */
  beforeDestroy() {},
  /**  实例销毁后调用  */
  destroyed() {},
};
</script>

<style lang="scss">
.electronicMedicalRecordTemplate {
  padding: 0px;
  height: 100%;
  .el-header {
    padding: 0 0px;
    background-color: #fff;
  }
  .customTreeColor {
    color: var(--zl-color-orange-primary);
  }

  .custom-tree-node {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 14px;
    padding-right: 8px;
    .custom-tree-node-action {
      display: none;
    }
  }
  .custom-tree-node:hover {
    .custom-tree-node-action {
      display: inline-block;
    }
  }

  .hidden_node__expand-icon {
    .el-tree-node__expand-icon {
      display: none;
    }
  }
}
</style>
