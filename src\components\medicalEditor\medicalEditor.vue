<template>
  <div class="medicalEditorContent">
    <div id="zl-medical-medicalEditor"></div>
  </div>
</template>

<script>
import API_entry from "@/api/KHS/MedicalRecord/template.js";
export default {
  name: "medicalEditor-component",
  //    初始化完成编辑器的回调
  props: {
    /* 客户字段信息 */
    fieldData: {
      type: Object,
      default: () => {
        return {
          ctm_name: "", // 姓名
          ctm_sex: "", // 性别
          ctm_age: "" /* 年龄 */,
          ctm_mobile: "" /* 联系电话 */,
          ctm_id_code: "" /* 客户身份证 */,
          S_AddressPCCS: "" /*  客户住址*/,
          ctm_wktype: "" /* 职业 */,
          ctm_code: "" /*客户卡号  */,
          HospitalName: "" /* 医院名称 */,
          O_MedicRecordVisitDepartmentId: "" /* 就诊科室 */,
        };
      },
    },
    /* 文档数据对象 */
    docData: {
      type: Object,
      default: () => {
        return {};
      },
    },
    /* 打印纸张信息 */
    paperOption: {
      type: Object,
      default: () => {
        return {
          size: "A4",
          marginX: 10,
          marginTop: 20,
          marginBottom: 0,
          headerContent: "",
          showHeader: false,
        };
      },
    },
    // 文档内容
    docContent: {
      type: String,
      default: "",
    },
    // 是否显示工具条
    hideToolbar: {
      type: Boolean,
      default: false,
    },
    // 是否可编辑
    editable: {
      type: Boolean,
      default: true,
    },
    // 是否可编辑
    showHeader: {
      type: Boolean,
      default: true,
    },
  },
  /** 监听数据变化   */
  watch: {
    /* 纸张信息 */
    paperOption: {
      handler(val) {
        if (val) {
          this.medicalEditor.setPaper(val);
        }
      },
    },
    /* 文档内容 */
    docContent: {
      handler(val) {
        if (this.isInitComplete && val && this.medicalEditor) {
          this.medicalEditor.setDocContent(decodeURIComponent(val));
        }
      },
    },
    /* 是否显示工具条 */
    hideToolbar: {
      handler(val) {
        if (this.medicalEditor) {
          this.medicalEditor.hideToolbar(val);
        }
      },
      immediate: true,
    },
    editable: {
      handler(val) {
        if (this.medicalEditor) {
          this.medicalEditor.setEditable(val);
        }
      },
      immediate: true,
    },
    showHeader: {
      handler(val) {
        if (this.medicalEditor) {
          this.medicalEditor.showHeader(val);
        }
      },
      immediate: true,
    },
    docData: {
      handler(val) {
        Object.assign(this.editDocData, val);
      },
    },
    fieldData: {
      handler(val) {
        if (!Object.keys(val).length) {
          // this.fieldData_ = {};
          Object.assign(this.fieldData_, {});
        } else {
          Object.assign(this.fieldData_, val);
        }
      },
    },
  },
  /**  引入的组件  */
  components: {},
  /**  Vue 实例的数据对象**/
  // public/EditorContent
  data() {
    return {
      medicalEditor: null,
      isInitComplete: false, //编辑器是否加载完成 加载完成后才能进行后续的操作
      jsLinks: ["/EditorContent/chunk-vendors-editor.js", "/EditorContent/chunk-editor.js", "/EditorContent/medicalEditor.js", "/EditorContent/pdf.min.js"],
      cssLinks: ["/EditorContent/medicalEditor.css"],
      entryCategoryList: [],
      editorPaperSetting: {},
      editDocData: {},
      fieldData_: {
        ctm_name: "", // 姓名
        ctm_sex: "", // 性别
        ctm_age: "" /* 年龄 */,
        ctm_mobile: "" /* 联系电话 */,
        ctm_id_code: "" /* 客户身份证 */,
        S_AddressPCCS: "" /*  客户住址*/,
        ctm_wktype: "" /* 职业 */,
        ctm_code: "" /*客户卡号  */,
        HospitalName: "" /* 医院名称 */,
        O_MedicRecordVisitDepartmentId: "" /* 就诊科室 */,
      },
    };
  },
  /**计算属性  */
  computed: {},
  /**  方法集合  */
  methods: {
    /* ref 调用方法 */
    /**  获取纸张信息  */
    getPaper() {
      let that = this;
      let Paper = that.medicalEditor.getPaper();
      return Paper;
    },
    /**  获取文档内容  */
    getDocContent() {
      let that = this;
      let docContent = that.medicalEditor.getDocContent();
      let contetn = encodeURIComponent(docContent);
      return contetn;
    },
    /**  设置编辑器文档内容  */
    setDocContent(content = "") {
      let that = this;
      that.medicalEditor.setDocContent(decodeURIComponent(content));
    },
    /**   重置文档内容 */
    resetDocContent() {
      let that = this;
      that.medicalEditor.resetDocContent();
    },
    /**  清除 文档数据内容   */
    clearData() {
      let that = this;
      that.medicalEditor.clearData();
    },
    /**    */
    print() {
      let that = this;
      console.log("打印");
      that.medicalEditor.print();
    },

    /**    */
    getDocData() {
      let that = this;
      if (that.editDocData) {
        return JSON.stringify(that.editDocData);
      }
      return "";
    },

    /* •••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••• */

    /**    */
    initLoadSrc() {
      let that = this;
      let promiseJsLinks = that.jsLinks.map((link) => () => that.loadScript(link));
      let promiseCssLinks = that.cssLinks.map((link) => {
        return that.loadCss(link);
      });
      Promise.all([
        // 依次加载依赖的 JS 文件，JS 执行是有顺序要求的，比如 ueditor.all.js 就要晚于 ueditor.config.js 执行
        // 动态创建 script 是先加载完的先执行，所以不可以一次性创建所有资源的引入脚本
        that.asyncSeries(promiseJsLinks),

        ...promiseCssLinks,
      ])
        .then(() => {
          console.log("病历编辑器资源加载成功！");
          that.initHMUEEdit();
        })
        .catch((fail) => {
          console.log("🚀 ~ initLoadSrc 病历编辑器加载失败 ~ fail:", fail);
        });
    },

    asyncSeries(funs) {
      return funs.reduce((promise, fun) => promise.then(fun), Promise.resolve());
    },
    // 动态创建 script 标签来加载 JS 脚本，保证同一个脚本只被加载一次
    loadScript(link) {
      let that = this;
      return new Promise((resolve, reject) => {
        // 如果这个资源从未被请求过，就手动创建脚本去加载
        if (that.$store.state.editor.editorLoadResource[link] == false || !that.$store.state.editor.editorLoadResource[link]) {
          that.$store.commit("setEditorLoadResource", {
            key: link,
            value: true,
          });
          const script = document.createElement("script");
          script.src = link;
          script.onload = (res) => {
            resolve(res);
          };
          script.onerror = (fail) => {
            reject(fail);
          };
          document.getElementsByTagName("body")[0].appendChild(script);
        } else {
          resolve();
        }
      });
    },
    // 动态创建 link 标签来加载 CSS 文件
    loadCss(link) {
      let that = this;
      return new Promise((resolve, reject) => {
        if (that.$store.state.editor.editorLoadResource[link] == false || !that.$store.state.editor.editorLoadResource[link]) {
          that.$store.commit("setEditorLoadResource", {
            key: link,
            value: true,
          });

          const css = document.createElement("link");
          css.type = "text/css";
          css.rel = "stylesheet";
          css.href = link;
          css.onload = () => {
            resolve();
          };
          css.onerror = (fail) => {
            reject(fail);
          };
          document.getElementsByTagName("head")[0].appendChild(css);
        } else {
          resolve();
        }
      });
    },
    /* •••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••• */
    /**  初始化打印   */
    initHMUEEdit() {
      let that = this;
      // 启动住院功能，添加一些住院的信息
      let HospitalizationEnableState1 = false;
      /*
        编辑器上下文，编辑器外部关联环境的一下参数 纸张页眉页脚设置
        size: "A4", // 纸张尺寸，目前只有两个值：A4 和 A5
        marginX: 10, //打印时，左右两边的边距，单位：毫米
        marginTop: 20, //打印时，上边的边距，单位：毫米
        marginBottom: 0, //打印时，下边的边距，单位：毫米
        headerContent: "", //页面的html内容
        showHeader: false, //是否展示页眉
       */
      that.editorPaperSetting = {
        size: "A4",
        marginX: 10,
        marginTop: 20,
        marginBottom: 0,
        headerContent: "",
        showHeader: false,
      };
      /*
       编辑器上下文，编辑器外部关联环境的一下参数
        paperSetting: editorPaperSetting,   // 设置打印参数
        fieldData: {}, //页眉字段值，用来自动填充字段数据的，像是下面这样的：{user: {age: 28, name: "孙悟空"}, doctor: {name:"华佗在世"}}
        fieldDict: [], //页眉字段的词典，用来做设计时，选择字段类型，像是下面这样的：[{label: "医生 · 主治医生", value: "doctor.name", icon: "hm-icon-doctor"}]
       */
      let docContext = {
        paperSetting: that.editorPaperSetting,
        fieldData: {},
        fieldDict: [],
      };
      //文档数据，就是要保存到数据的 key-value 对象
      // let docData = {};
      //字段值，用来自动填充字段数据的，像是下面这样的：{user: {age: 28, name: "孙悟空"}, doctor: {name:"华佗在世"}}
      // let fieldData = {};
      // 字段的词典，用来做设计时，选择字段类型，像是下面这样的：[{label: "医生 · 主治医生", value: "doctor.name", icon: "hm-icon-doctor"}]
      let fieldDict = [
        { label: "客户姓名", value: "ctm_name" },
        { label: "客户性别", value: "ctm_sex" },
        { label: "客户年龄", value: "ctm_age" },
        { label: "客户电话", value: "ctm_mobile" },
        { label: "客户身份证", value: "ctm_id_code" },
        { label: "客户住址", value: "S_AddressPCCS" },
        { label: "客户职业", value: "ctm_wktype" },
        { label: "客户卡号", value: "ctm_code" },
        { label: "医院名称", value: "HospitalName" },
        { label: "病历编号", value: "O_MedicRecordCode" },
        { label: "治疗日期", value: "O_TreatmentDate" },
        { label: "就诊科室", value: "O_MedicRecordVisitDepartmentId" },
        { label: "主治医生", value: "O_MedicRecordVisitDoctorId" },
        { label: "出生日期", value: "ctm_datebirth" },
      ];

      // if (HospitalizationEnableState1) {
      //   let ospitalizaTtionAll = [
      //     { label: "住院号", value: "O_InpatientNumber" },
      //     { label: "医疗机构", value: "H_OrganizeId" },
      //     { label: "入院时间", value: "hr_admissiontime" },
      //     { label: "入院科室", value: "O_AdmissionDepartment" },
      //     { label: "住院医生", value: "O_ResidentName" },
      //     { label: "住院护士", value: "O_ResidentNurseName" },
      //     { label: "医生助理", value: "hr_docassistant_name" },
      //     { label: "门/急诊医生", value: "hr_clinicdoc_name" },
      //     { label: "麻醉医生", value: "hr_anesthesiologist_name" },
      //     { label: "病区", value: "ha_name" },
      //     { label: "床号", value: "bedshowname" },
      //     { label: "床位", value: "hb_name" },
      //     { label: "出院时间", value: "hr_dischargetime" },
      //     { label: "出院科室", value: "hr_dischargedept_name" },
      //     { label: "实际住院天数", value: "hr_day" },
      //     { label: "门/急诊诊断", value: "hr_clinicdiagnosis_name" },
      //     { label: "门/急诊诊断-疾病编码", value: "hr_clinicdiagnosis_code" },
      //     { label: "入院诊断", value: "hr_inpatientdiagnosis_name" },
      //     { label: "入院诊断-疾病编码", value: "hr_inpatientdiagnosis_code" },
      //   ];
      //   ospitalizaTtionAll.forEach((i) => {
      //     fieldDict.push(i);
      //   });
      // }

      // 初始化词条字典 词条字典，用来在设计模式下，选择词条类型，像是下面这样的：[{"value": "kqzhk", "label": "口腔综合科"}]
      let wordDict = [{ label: "<空>", value: "" }];
      Array.prototype.push.apply(wordDict, that.entryCategoryList);
      that.medicalEditor = window.createEditor(document.querySelector("#zl-medical-medicalEditor"), docContext, that.editDocData, that.fieldData_, fieldDict, wordDict);

      that.medicalEditor.hideToolbar(that.hideToolbar);
      const base = process.env.VUE_APP_API_URL; // 请求地址
      // 处理界面token
      const accessuser = JSON.parse(localStorage.getItem("access-user"));
      let zl_header_token = "";
      if (accessuser) {
        zl_header_token = "Basic " + accessuser.AuthToken;
      }
      /**
       * 编辑器内核是在 iframe 里初始化的，iframe 加载需要一定时间，编辑器初始化需要一点时间
       * 编辑器初始化完毕后，会触发 "initComplete" 事件
       */
      that.medicalEditor
        .$on("initComplete", (editorWindow) => {
          // 编辑器初始化完成
          editorWindow.zl_base_url = base;
          editorWindow.zl_header_token = zl_header_token;
          //
          if (that.docContent && that.docContent != "" && that.medicalEditor) {
            that.medicalEditor.setDocContent(decodeURIComponent(that.docContent));
          }
          that.medicalEditor.setEditable(that.editable);
          that.medicalEditor.showHeader(that.showHeader);
          that.isInitComplete = true;
          that.$emit("initEditorComplete");
        })
        .$on("transaction", (html) => {
          // localStorage.setItem("editorTemp", that.medicalEditor.getDocContent());
        })
        .$on("paperchange", (setting) => {
          localStorage.setItem("editorHeader", JSON.stringify(setting));
        })
        .$on("sign", (id) => {
          /* 签字 */
          browser(".png, .jpg", false, (files) => {
            let f = files[0];
            if (!f) return;
            if (f.size > 1024 * 1024) {
              //1M 大小
              // this.tip({
              //   type: "warning",
              //   msg: "qing'jiang 1 M 的图片文件",
              // });
              that.$message.error("图片大小不能超过 1M");
            }
            //获取文件
            var reader = new FileReader();
            //读取完成
            reader.onload = (e) => {
              that.docData[id] = reader.result;
            };
            reader.readAsDataURL(f);
          });
        });

      /**
       *	@param accept
       *	accept="image/png" or accept=".png" — 只接受 png 图片.
       *	accept="image/png, image/jpeg" or accept=".png, .jpg, .jpeg" — PNG/JPEG 文件.
       *	accept="image/*" — 接受任何图片文件类型.
       *	accept=".doc,.docx,.xml,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document" — 接受任何 MS Doc 文件类型.
       *	@param isMultiple 是否多选
       *	@param onchange 回调函数
       */
      function browser(accept, isMultiple, onchange) {
        let input = document.createElement("input");
        input.type = "file";

        if (accept) {
          input.accept = accept;
        }
        if (isMultiple === true) {
          input.multiple = true;
        }
        input.style.display = "none";
        input.onchange = () => {
          onchange(input.files);
          document.body.removeChild(input);
        };
        document.body.appendChild(input);
        input.click();
      }
    },
    /**  获取词条分类  */
    async medicalRecord_categoryAll() {
      let that = this;
      try {
        let params = {};
        let res = await API_entry.medicalRecord_categoryAll(params);
        if (res.StateCode == 200) {
          that.entryCategoryList = res.Data.map((i) => {
            return {
              value: i.CategoryID,
              label: i.CategoryName,
            };
          });
        }
      } catch (error) {}
    },
  },
  /** 创建实例之前执行函数   */
  beforeCreate() {},
  /**  实例创建完成之后  */
  created() {},
  /**  在挂载开始之前被调用  */
  beforeMount() {},
  /**  实例被挂载后调用  */
  mounted() {
    let that = this;
    that.medicalRecord_categoryAll().then(() => {
      that.initLoadSrc();
    });
  },
  /**  数据更新 之前 调用  */
  beforeUpdate() {},
  /** 数据更新 完成 调用   */
  updated() {},
  /**  实例销毁之前调用  */
  beforeDestroy() {},
  /**  实例销毁后调用  */
  destroyed() {},
};
</script>

<style lang="scss">
.medicalEditorContent {
  height: 100%;
  box-sizing: border-box;
  #zl-medical-medicalEditor {
    height: 100%;
  }
}
</style>
