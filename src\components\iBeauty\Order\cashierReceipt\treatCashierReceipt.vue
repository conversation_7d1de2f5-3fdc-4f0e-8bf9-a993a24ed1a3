<template>
  <div class="zl_custom_component_treatCashierReceipt" >
    <el-dialog :visible.sync="dialogVisible_" width="300px" @close="onClose" append-to-body>
      <span slot="title" class="font_14 color_333">打印小票</span>
      <div v-if="treatInfo && entityName">
        <el-row>
          <el-col :span="24">
            <el-scrollbar class="el-scrollbar_height" style="height: 500px">
              <div class="marrt_10">
                <div class="dis_flex">
                  <span class="flex_box text_center font_16" style="line-height: 32px">{{ entityName }}</span>
                </div>
                <el-divider><span class="font_12 color_gray">消耗信息</span></el-divider>
                <div class="dis_flex">
                  <span class="flex_box font_12 color_gray text-left line_height_24">订单编号</span>
                  <span class="font_12 text_right line_height_24" style="flex: 3">{{ treatInfo.ID }}</span>
                </div>
                <div class="dis_flex">
                  <span class="flex_box font_12 color_gray text-left line_height_24">下单时间</span>
                  <span class="font_12 text_right line_height_24" style="flex: 3">{{ treatInfo.BillDate | dateFormat("YYYY-MM-DD HH:mm") }}</span>
                </div>
                <div class="dis_flex">
                  <span class="flex_box font_12 color_gray text-left line_height_24">会员姓名</span>
                  <span class="flex_box font_12 text_right line_height_24">{{ cashierReceipt.NameEncrypt ? formatName(treatInfo.Name) : treatInfo.Name }}</span>
                </div>
                <div class="dis_flex">
                  <span class="flex_box font_12 color_gray text-left line_height_24">会员手机号</span>
                  <span class="flex_box font_12 text_right line_height_24">{{ cashierReceipt.MobileEncrypt ? formatPhone(treatInfo.PhoneNumber) : treatInfo.PhoneNumber }}</span>
                </div>
                <div class="dis_flex">
                  <span class="flex_box font_12 color_gray text-left line_height_24">开单人</span>
                  <span class="flex_box font_12 text_right line_height_24">{{ treatInfo.EmployeeName }}</span>
                </div>
                <div v-if="cashierReceipt.EntityAddress" class="dis_flex">
                  <span class="flex_box6 color_gray text-left line_height_24">地址：</span>
                  <span class="flex_box font_12 text_right line_height_24">{{ treatInfo.AddressDetail }}</span>
                </div>
                <el-divider><span class="font_12 color_gray">消耗明细</span></el-divider>

                <div v-for="(item, index) in treatInfo.Project" :key="index + 'Project' + item.ProjectID">
                  <div class="dis_flex">
                    <span class="font_12 color_gray text-left line_height_24" style="flex: 2">
                      {{ index + 1 }} {{ item.ProjectName }}
                      <span class="font_12" size="mini" v-if="item.IsLargess">【赠】</span>
                    </span>
                    <span v-if="cashierReceipt.TreatGoodsOriginPrice" class="font_12 text_right line_height_24" style="flex: 1">￥{{ item.Price | toFixed | NumFormat }}</span>
                  </div>

                  <div class="dis_flex">
                    <span class="font_12 color_gray text-left line_height_24 marlt_10" style="flex: 2">数量</span>
                    <span class="font_12 text_right line_height_24" style="flex: 1">{{ item.Quantity }}</span>
                  </div>
                  <div v-if="cashierReceipt.TreatPromotions && item.CardPreferentialAmount > 0  && cashierReceipt.TreatAmount" class="dis_flex">
                    <span class="font_12 color_gray text-left line_height_24 marlt_10" style="flex: 2">卡优惠</span>
                    <span class="font_12 text_right line_height_24" v-if="item.CardPreferentialAmount < 0">¥ {{ Math.abs(item.CardPreferentialAmount) | toFixed | NumFormat }}</span>
                    <span class="font_12 text_right line_height_24" v-if="item.CardPreferentialAmount > 0">¥ -{{ Math.abs(item.CardPreferentialAmount) | toFixed | NumFormat }}</span>
                  </div>
                  <div class="dis_flex" v-if="cashierReceipt.TreatAmount">
                    <span class="font_12 color_gray text-left line_height_24 marlt_10" style="flex: 2">小计</span>
                    <span class="font_12 text_right line_height_24" style="flex: 1">￥ {{ item.TotalAmount | toFixed | NumFormat }}</span>
                  </div>
                </div>

                  <div v-for="(item, index) in treatInfo.SavingCard" :key="index + 'SavingCard' + item.SavingCardID">
                    <div v-for="(Project, pIndex) in item.Project" :key="pIndex + item.SavingCardID + Project.ProjectID">
                      <div class="dis_flex">
                        <span class="font_12 color_gray text-left line_height_24" style="flex: 2">
                          {{ +treatInfo.Project.length + getCurrentCardLength(treatInfo.SavingCard, index, pIndex) }}
                          {{ Project.ProjectName }}
                          <span class="font_12" size="mini" v-if="Project.IsLargess">【赠】</span>
                        </span>
                        <span v-if="cashierReceipt.TreatGoodsOriginPrice" class="font_12 text_right line_height_24" style="flex: 1">￥{{ Project.Price | toFixed | NumFormat }}</span>
                      </div>

                      <div class="dis_flex">
                        <span class="font_12 color_gray text-left line_height_24 marlt_10" style="flex: 2">数量</span>
                        <span class="font_12 text_right line_height_24" style="flex: 1">{{ Project.Quantity }}</span>
                      </div>
                      <div v-if="cashierReceipt.TreatPromotions && Project.CardPreferentialAmount > 0  && cashierReceipt.TreatAmount" class="dis_flex">
                        <span class="font_12 color_gray text-left line_height_24 marlt_10" style="flex: 2">卡优惠</span>
                        <span class="font_12 text_right line_height_24" v-if="Project.CardPreferentialAmount < 0">¥ {{ Math.abs(Project.CardPreferentialAmount) | toFixed | NumFormat }}</span>
                        <span class="font_12 text_right line_height_24" v-if="Project.CardPreferentialAmount > 0">¥ -{{ Math.abs(Project.CardPreferentialAmount) | toFixed | NumFormat }}</span>
                      </div>
                      
                      <div v-if="cashierReceipt.TreatPromotions && Project.MemberPreferentialAmount > 0  && cashierReceipt.TreatAmount" class="dis_flex">
                        <span class="font_12 color_gray text-left line_height_24 marlt_10" style="flex: 2">会员优惠</span>
                        <span class="font_12 text_right line_height_24" v-if="Project.MemberPreferentialAmount < 0">¥ {{ Math.abs(Project.MemberPreferentialAmount) | toFixed | NumFormat }}</span>
                        <span class="font_12 text_right line_height_24" v-if="Project.MemberPreferentialAmount > 0">¥ -{{ Math.abs(Project.MemberPreferentialAmount) | toFixed | NumFormat }}</span>
                      </div>

                      <div v-if="cashierReceipt.TreatPromotions && Project.PricePreferentialAmount > 0  && cashierReceipt.TreatAmount" class="dis_flex">
                        <span class="font_12 color_gray text-left line_height_24 marlt_10" style="flex: 2">手动改价</span>
                        <span class="font_12 text_right line_height_24" style="flex: 1">¥ {{ Project.PricePreferentialAmount | toFixed | NumFormat }}</span>
                      </div>
                      <div class="dis_flex" v-if="cashierReceipt.TreatAmount">
                        <span class="font_12 color_gray text-left line_height_24 marlt_10" style="flex: 2">小计</span>
                        <span class="font_12 text_right line_height_24" style="flex: 1">￥ {{ Project.TotalAmount | toFixed | NumFormat }}</span>
                      </div>
                    </div>
                  </div>

                  <div  v-for="(item, index) in treatInfo.TimeCard" :key="index + 'TimeCard' + item.TimeCardID">
                    <div v-for="(Project, pIndex) in item.Project" :key="pIndex + '' + item.TimeCardID + '' + Project.ProjectID">
                      <div class="dis_flex">
                        <span class="font_12 color_gray text-left line_height_24" style="flex: 2">
                          {{ treatInfo.Project.length + getCardTotalLength(treatInfo.SavingCard) + getCurrentCardLength(treatInfo.TimeCard, index, pIndex) }}
                          {{ Project.ProjectName }}
                          <span class="font_12" size="mini" v-if="Project.IsLargess">【赠】</span>
                        </span>
                        <span v-if="cashierReceipt.TreatGoodsOriginPrice" class="font_12 text_right line_height_24" style="flex: 1">￥{{ Project.Price | toFixed | NumFormat }}</span>
                      </div>

                      <div class="dis_flex">
                        <span class="font_12 color_gray text-left line_height_24 marlt_10" style="flex: 2">数量</span>
                        <span class="font_12 text_right line_height_24" style="flex: 1">{{ Project.Quantity }}</span>
                      </div>
                      <div v-if="cashierReceipt.TreatPromotions && Project.CardPreferentialAmount != 0  && cashierReceipt.TreatAmount" class="dis_flex">
                        <span class="font_12 color_gray text-left line_height_24 marlt_10" style="flex: 2">卡优惠</span>
                        <span class="font_12 text_right line_height_24" v-if="Project.CardPreferentialAmount < 0">¥ {{ Math.abs(Project.CardPreferentialAmount) | toFixed | NumFormat }}</span>
                        <span class="font_12 text_right line_height_24" v-if="Project.CardPreferentialAmount > 0">¥ -{{ Math.abs(Project.CardPreferentialAmount) | toFixed | NumFormat }}</span>
                      </div>
                      <div class="dis_flex" v-if="cashierReceipt.TreatAmount">
                        <span class="font_12 color_gray text-left line_height_24 marlt_10" style="flex: 2">小计</span>
                        <span class="font_12 text_right line_height_24" style="flex: 1">￥ {{ Project.TotalAmount | toFixed | NumFormat }}</span>
                      </div>
                    </div>
                  </div>

                  <div v-for="(item, index) in treatInfo.GeneralCard" :key="index + 'GeneralCard' + item.GeneralCardID">
                    <div v-for="(Project, pIndex) in item.Project" :key="pIndex + item.SavingCardID + Project.ProjectID">
                      <div class="dis_flex">
                        <span class="font_12 color_gray text-left line_height_24" style="flex: 2">
                          {{ treatInfo.Project.length + getCardTotalLength(treatInfo.SavingCard) + getCardTotalLength(treatInfo.TimeCard) + getCurrentCardLength(treatInfo.GeneralCard, index, pIndex) }}
                          {{ Project.ProjectName }}
                          <span class="font_12" size="mini" v-if="Project.IsLargess">【赠】</span>
                        </span>
                        <span v-if="cashierReceipt.TreatGoodsOriginPrice" class="font_12 text_right line_height_24" style="flex: 1">￥{{ Project.Price | toFixed | NumFormat }}</span>
                      </div>

                      <div class="dis_flex">
                        <span class="font_12 color_gray text-left line_height_24 marlt_10" style="flex: 2">数量</span>
                        <span class="font_12 text_right line_height_24" style="flex: 1">{{ Project.Quantity }}</span>
                      </div>
                      <div v-if="cashierReceipt.TreatPromotions && item.CardPreferentialAmount > 0  && cashierReceipt.TreatAmount" class="dis_flex">
                        <span class="font_12 color_gray text-left line_height_24 marlt_10" style="flex: 2">卡优惠</span>
                        <!-- <span class="font_12 text_right line_height_24" style="flex:1;">¥ {{Project.CardPreferentialAmount | NumFormat}}</span> -->

                        <span class="font_12 text_right line_height_24" v-if="Project.CardPreferentialAmount < 0">¥ {{ Math.abs(Project.CardPreferentialAmount) | toFixed | NumFormat }}</span>
                        <span class="font_12 text_right line_height_24" v-if="Project.CardPreferentialAmount > 0">¥ -{{ Math.abs(Project.CardPreferentialAmount) | toFixed | NumFormat }}</span>
                      </div>
                      <div class="dis_flex" v-if="cashierReceipt.TreatAmount">
                        <span class="font_12 color_gray text-left line_height_24 marlt_10" style="flex: 2">小计</span>
                        <span class="font_12 text_right line_height_24" style="flex: 1">￥ {{ Project.TotalAmount | toFixed | NumFormat }}</span>
                      </div>
                    </div>
                  </div>

                  <div v-for="(item, index) in treatInfo.Product" :key="index + 'Product' + item.ProductID">
                    <div class="dis_flex">
                      <span class="font_12 color_gray text-left line_height_24" style="flex: 2">
                        {{ index + 1 + treatInfo.Project.length + getCardTotalLength(treatInfo.SavingCard) + getCardTotalLength(treatInfo.TimeCard) + getCardTotalLength(treatInfo.GeneralCard) }}
                        {{ item.ProductName }}
                        <span class="font_12" size="mini" v-if="item.IsLargess">【赠】</span>
                      </span>
                      <span v-if="cashierReceipt.TreatGoodsOriginPrice" class="font_12 text_right line_height_24" style="flex: 1">￥{{ item.Price | toFixed | NumFormat }}</span>
                    </div>

                    <div class="dis_flex">
                      <span class="font_12 color_gray text-left line_height_24 marlt_10" style="flex: 2">数量</span>
                      <span class="font_12 text_right line_height_24" style="flex: 1">{{ item.Quantity }}</span>
                    </div>
                    <div v-if="cashierReceipt.TreatPromotions && item.CardPreferentialAmount > 0  && cashierReceipt.TreatAmount" class="dis_flex">
                      <span class="font_12 color_gray text-left line_height_24 marlt_10" style="flex: 2">卡优惠</span>
                      <!-- <span class="font_12 text_right line_height_24" style="flex:1;">¥ {{item.CardPreferentialAmount | NumFormat}}</span> -->

                      <span class="font_12 text_right line_height_24" v-if="item.CardPreferentialAmount < 0">¥ {{ Math.abs(item.CardPreferentialAmount) | toFixed | NumFormat }}</span>
                      <span class="font_12 text_right line_height_24" v-if="item.CardPreferentialAmount > 0">¥ -{{ Math.abs(item.CardPreferentialAmount) | toFixed | NumFormat }}</span>
                    </div>
                    <div class="dis_flex" v-if="cashierReceipt.TreatAmount">
                      <span class="font_12 color_gray text-left line_height_24 marlt_10" style="flex: 2">小计</span>
                      <span class="font_12 text_right line_height_24" style="flex: 1">￥ {{ item.TotalAmount | toFixed | NumFormat }}</span>
                    </div>
                  </div>
                  <div v-for="(item, index) in treatInfo.PackageCard" :key="index + 'PackageCard' + item.PackageCardID">
                    <!-- 套餐卡项目 -->
                    <div v-for="(packItem, Pindex) in item.Project" :key="Pindex + 'PProjectID' + packItem.ProjectID">
                      <div class="dis_flex">
                        <span class="font_12 color_gray text-left line_height_24" style="flex: 2">
                          {{ Pindex + 1 + treatInfo.Project.length + getCardTotalLength(treatInfo.SavingCard) + getCardTotalLength(treatInfo.TimeCard) + getCardTotalLength(treatInfo.GeneralCard) + treatInfo.Product.length }}
                          {{ packItem.ProjectName }}
                        </span>
                        <span v-if="cashierReceipt.TreatGoodsOriginPrice" class="font_12 text_right line_height_24" style="flex: 1">￥{{ packItem.Price | toFixed | NumFormat }}</span>
                      </div>

                      <div class="dis_flex">
                        <span class="font_12 color_gray text-left line_height_24 marlt_10" style="flex: 2">数量</span>
                        <span class="font_12 text_right line_height_24" style="flex: 1">{{ packItem.Quantity }}</span>
                      </div>
                      <div v-if="cashierReceipt.TreatPromotions && packItem.CardPreferentialAmount > 0  && cashierReceipt.TreatAmount" class="dis_flex">
                        <span class="font_12 color_gray text-left line_height_24 marlt_10" style="flex: 2">卡优惠</span>
                        <span class="font_12 text_right line_height_24" style="flex: 1">¥ {{ packItem.CardPreferentialAmount | toFixed | NumFormat }}</span>
                      </div>
                      <div class="dis_flex" v-if="cashierReceipt.TreatAmount">
                        <span class="font_12 color_gray text-left line_height_24 marlt_10" style="flex: 2">小计</span>
                        <span class="font_12 text_right line_height_24" style="flex: 1">￥ {{ packItem.TotalAmount | toFixed | NumFormat }}</span>
                      </div>
                    </div>
                    <!-- 套餐卡储值卡 -->
                    <div v-for="(packItem, Pindex) in item.SavingCard" :key="Pindex + 'PProjectID' + packItem.SavingCardID">
                      <div v-for="(Project, projectIndex) in packItem.Project" :key="projectIndex + item.PackageCardAccountID + packItem.SavingCardID + Project.ProjectID">
                        <div class="dis_flex">
                          <span class="font_12 color_gray text-left line_height_24" style="flex: 2">
                            {{ +treatInfo.Project.length + getCardTotalLength(treatInfo.SavingCard) + getCardTotalLength(treatInfo.TimeCard) + getCardTotalLength(treatInfo.GeneralCard) + treatInfo.Product.length + item.Project.length + getCurrentCardLength(item.SavingCard, Pindex, projectIndex) }}
                            {{ Project.ProjectName }}
                            <span class="font_12" size="mini" v-if="Project.IsLargess">【赠】</span>
                          </span>
                          <span v-if="cashierReceipt.TreatGoodsOriginPrice" class="font_12 text_right line_height_24" style="flex: 1">￥{{ Project.Price | toFixed | NumFormat }}</span>
                        </div>

                        <div class="dis_flex">
                          <span class="font_12 color_gray text-left line_height_24 marlt_10" style="flex: 2">数量</span>
                          <span class="font_12 text_right line_height_24" style="flex: 1">{{ Project.Quantity }}</span>
                        </div>
                        <div v-if="cashierReceipt.TreatPromotions && Project.CardPreferentialAmount > 0  && cashierReceipt.TreatAmount" class="dis_flex">
                          <span class="font_12 color_gray text-left line_height_24 marlt_10" style="flex: 2">卡优惠</span>
                          <!-- <span class="font_12 text_right line_height_24" style="flex:1;">¥ {{Project.CardPreferentialAmount | NumFormat}}</span> -->
                          <span class="font_12 text_right line_height_24" v-if="Project.CardPreferentialAmount < 0">¥ {{ Math.abs(Project.CardPreferentialAmount) | toFixed | NumFormat }}</span>
                          <span class="font_12 text_right line_height_24" v-if="Project.CardPreferentialAmount > 0">¥ -{{ Math.abs(Project.CardPreferentialAmount) | toFixed | NumFormat }}</span>
                        </div>

                      
                      <div v-if="cashierReceipt.TreatPromotions && Project.MemberPreferentialAmount > 0  && cashierReceipt.TreatAmount" class="dis_flex">
                        <span class="font_12 color_gray text-left line_height_24 marlt_10" style="flex: 2">会员优惠</span>
                        <span class="font_12 text_right line_height_24" v-if="Project.MemberPreferentialAmount < 0">¥ {{ Math.abs(Project.MemberPreferentialAmount) | toFixed | NumFormat }}</span>
                        <span class="font_12 text_right line_height_24" v-if="Project.MemberPreferentialAmount > 0">¥ -{{ Math.abs(Project.MemberPreferentialAmount) | toFixed | NumFormat }}</span>
                      </div>

                        <div v-if="cashierReceipt.TreatPromotions && Project.PricePreferentialAmount > 0 && cashierReceipt.TreatAmount" class="dis_flex">
                          <span class="font_12 color_gray text-left line_height_24 marlt_10" style="flex: 2">手动改价</span>
                          <!-- <span class="font_12 text_right line_height_24" style="flex:1;">¥ {{Project.PricePreferentialAmount | NumFormat}}</span> -->
                          <span class="font_12 text_right line_height_24" v-if="Project.PricePreferentialAmount < 0">¥ {{ Math.abs(Project.PricePreferentialAmount) | toFixed | NumFormat }}</span>
                          <span class="font_12 text_right line_height_24" v-if="Project.PricePreferentialAmount > 0">¥ -{{ Math.abs(Project.PricePreferentialAmount) | toFixed | NumFormat }}</span>
                        </div>

                        <div class="dis_flex" v-if="cashierReceipt.TreatAmount">
                          <span class="font_12 color_gray text-left line_height_24 marlt_10" style="flex: 2">小计</span>
                          <span class="font_12 text_right line_height_24" style="flex: 1">￥ {{ Project.TotalAmount | toFixed | NumFormat }}</span>
                        </div>
                      </div>
                    </div>
                    <!-- 套餐卡中 时效卡 -->
                    <div v-for="(packItem, Pindex) in item.TimeCard" :key="Pindex + 'PProjectID' + packItem.TimeCardID">
                      <div v-for="(Project, ProjectIndex) in packItem.Project" :key="ProjectIndex + item.PackageCardID + packItem.TimeCardID + Project.ProjectID">
                        <div class="dis_flex">
                          <span class="font_12 color_gray text-left line_height_24" style="flex: 2">
                            {{
                              +treatInfo.Project.length +
                              getCardTotalLength(treatInfo.SavingCard) +
                              getCardTotalLength(treatInfo.TimeCard) +
                              getCardTotalLength(treatInfo.GeneralCard) +
                              treatInfo.Product.length +
                              item.Project.length +
                              getCardTotalLength(item.SavingCard) +
                              getCurrentCardLength(item.TimeCard, Pindex, projectIndex)
                            }}
                            {{ Project.ProjectName }}
                            <span class="font_12" size="mini" v-if="Project.IsLargess">【赠】</span>
                          </span>
                          <span v-if="cashierReceipt.TreatGoodsOriginPrice" class="font_12 text_right line_height_24" style="flex: 1">￥{{ Project.Price | toFixed | NumFormat }}</span>
                        </div>

                        <div class="dis_flex">
                          <span class="font_12 color_gray text-left line_height_24 marlt_10" style="flex: 2">数量</span>
                          <span class="font_12 text_right line_height_24" style="flex: 1">{{ Project.Quantity }}</span>
                        </div>
                        <div v-if="cashierReceipt.TreatPromotions && Project.CardPreferentialAmount > 0  && cashierReceipt.TreatAmount" class="dis_flex">
                          <span class="font_12 color_gray text-left line_height_24 marlt_10" style="flex: 2">卡优惠</span>
                          <span class="font_12 text_right line_height_24" style="flex: 1">¥ {{ Project.CardPreferentialAmount | toFixed | NumFormat }}</span>
                        </div>
                        <div class="dis_flex" v-if="cashierReceipt.TreatAmount">
                          <span class="font_12 color_gray text-left line_height_24 marlt_10" style="flex: 2">小计</span>
                          <span class="font_12 text_right line_height_24" style="flex: 1">￥ {{ Project.TotalAmount | toFixed | NumFormat }}</span>
                        </div>
                      </div>
                    </div>

                    <!-- 套餐卡中 通用次卡 -->
                    <div v-for="(packItem, Pindex) in item.GeneralCard" :key="Pindex + 'PProjectID' + packItem.GeneralCardID">
                      <div v-for="(Project, pIndex) in packItem.Project" :key="pIndex + item.PackageCardID + packItem.GeneralCardID + Project.ProjectID">
                        <div class="dis_flex">
                          <span class="font_12 color_gray text-left line_height_24" style="flex: 2">
                            {{
                              +treatInfo.Project.length +
                              getCardTotalLength(treatInfo.SavingCard) +
                              getCardTotalLength(treatInfo.TimeCard) +
                              getCardTotalLength(treatInfo.GeneralCard) +
                              treatInfo.Product.length +
                              item.Project.length +
                              getCardTotalLength(item.SavingCard) +
                              getCardTotalLength(item.TimeCard) +
                              getCurrentCardLength(item.GeneralCard, Pindex, projectIndex)
                            }}
                            {{ Project.ProjectName }}
                            <span class="font_12" size="mini" v-if="Project.IsLargess">【赠】</span>
                          </span>
                          <span v-if="cashierReceipt.TreatGoodsOriginPrice" class="font_12 text_right line_height_24" style="flex: 1">￥{{ Project.Price | toFixed | NumFormat }}</span>
                        </div>

                        <div class="dis_flex">
                          <span class="font_12 color_gray text-left line_height_24 marlt_10" style="flex: 2">数量</span>
                          <span class="font_12 text_right line_height_24" style="flex: 1">{{ Project.Quantity }}</span>
                        </div>
                        <div v-if="cashierReceipt.TreatPromotions && Project.CardPreferentialAmount > 0  && cashierReceipt.TreatAmount" class="dis_flex">
                          <span class="font_12 color_gray text-left line_height_24 marlt_10" style="flex: 2">卡优惠</span>
                          <span class="font_12 text_right line_height_24" style="flex: 1">¥ {{ Project.CardPreferentialAmount | toFixed | NumFormat }}</span>
                        </div>
                        <div class="dis_flex" v-if="cashierReceipt.TreatAmount">
                          <span class="font_12 color_gray text-left line_height_24 marlt_10" style="flex: 2">小计</span>
                          <span class="font_12 text_right line_height_24" style="flex: 1">￥ {{ Project.TotalAmount | toFixed | NumFormat }}</span>
                        </div>
                      </div>
                    </div>

                    <!-- 套餐卡产品 -->
                    <div v-for="(packItem, Pindex) in item.Product" :key="Pindex + 'PProductID' + packItem.ProductID">
                      <div class="dis_flex">
                        <span class="font_12 color_gray text-left line_height_24" style="flex: 2">
                          {{
                            +treatInfo.Project.length +
                            getCardTotalLength(treatInfo.SavingCard) +
                            getCardTotalLength(treatInfo.TimeCard) +
                            getCardTotalLength(treatInfo.GeneralCard) +
                            treatInfo.Product.length +
                            item.Project.length +
                            getCardTotalLength(item.SavingCard) +
                            getCardTotalLength(item.TimeCard) +
                            getCardTotalLength(item.GeneralCard) +
                            Pindex +
                            1
                          }}
                          {{ packItem.ProductName }}
                        </span>
                        <span v-if="cashierReceipt.TreatGoodsOriginPrice" class="font_12 text_right line_height_24" style="flex: 1">￥{{ packItem.Price | toFixed | NumFormat }}</span>
                      </div>

                      <div class="dis_flex">
                        <span class="font_12 color_gray text-left line_height_24 marlt_10" style="flex: 2">数量</span>
                        <span class="font_12 text_right line_height_24" style="flex: 1">{{ packItem.Quantity }}</span>
                      </div>
                      <div v-if="cashierReceipt.TreatPromotions && packItem.CardPreferentialAmount > 0  && cashierReceipt.TreatAmount" class="dis_flex">
                        <span class="font_12 color_gray text-left line_height_24 marlt_10" style="flex: 2">卡优惠</span>
                        <span class="font_12 text_right line_height_24" style="flex: 1">¥ {{ packItem.CardPreferentialAmount | toFixed | NumFormat }}</span>
                      </div>
                      <div class="dis_flex" v-if="cashierReceipt.TreatAmount">
                        <span class="font_12 color_gray text-left line_height_24 marlt_10" style="flex: 2">小计</span>
                        <span class="font_12 text_right line_height_24" style="flex: 1">￥ {{ packItem.TotalAmount | toFixed | NumFormat }}</span>
                      </div>
                    </div>
                  </div>
                <el-divider v-if="cashierReceipt.TreatAmount" class="sell-el-divider"></el-divider>
                <div class="dis_flex" v-if="cashierReceipt.TreatAmount">
                  <span class="flex_box font_12 color_gray text-left line_height_24">合计</span>
                  <span class="flex_box font_12 text_right line_height_24">￥{{ treatInfo.Amount | toFixed | NumFormat }}</span>
                </div>
                <div v-if="treatInfo.SaleBillPay && treatInfo.SaleBillPay.length > 0 && cashierReceipt.TreatAmount" class="dis_flex font_12">
                  <span class="flex_box6 color_gray text-left line_height_24">付款：</span>
                  <div class="flex_box">
                    <div class="dis_flex" v-for="pay in treatInfo.SaleBillPay" :key="pay.ID + 'pay'">
                      <span class="flex_box color_gray line_height_24">{{ pay.Name }}</span>
                      <span class="flex_box text_right line_height_24">¥ {{ pay.Amount | toFixed | NumFormat }}</span>
                    </div>
                  </div>
                </div>
                <!--  -->
                <div v-if="treatInfo.SaleBillPaySavingCardDeduction && treatInfo.SaleBillPaySavingCardDeduction.length > 0 && cashierReceipt.TreatAmount" class="dis_flex font_12">
                  <span class="flex_box6 color_gray text-left line_height_24">卡抵扣：</span>
                  <div class="flex_box">
                    <div class="dis_flex flex_box" v-for="cardPay in treatInfo.SaleBillPaySavingCardDeduction" :key="cardPay.ID + 'cardPay'">
                      <span class="flex_box color_gray line_height_24">{{ cardPay.Name }}</span>
                      <span class="flex_box text_right line_height_24">¥ {{ cardPay.TotalAmount | toFixed | NumFormat }}</span>
                    </div>
                  </div>
                </div>
                <div class="dis_flex" v-if="treatInfo.PricePreferentialAmount != 0 && cashierReceipt.SalePromotions && cashierReceipt.TreatAmount">
                  <span class="flex_box font_12 color_gray text-left line_height_24">手动改价</span>
                  <span class="flex_box font_12 text_right line_height_24" v-if="treatInfo.PricePreferentialAmount > 0">-￥{{ treatInfo.PricePreferentialAmount | toFixed | NumFormat }}</span>
                  <span class="flex_box font_12 text_right line_height_24" v-else>+￥{{ mathAbsData(treatInfo.PricePreferentialAmount) | toFixed | NumFormat }}</span>
                </div>
                <div class="dis_flex" v-if="treatInfo.CardPreferentialAmount > 0 && cashierReceipt.SalePromotions && cashierReceipt.TreatAmount">
                  <span class="flex_box font_12 color_gray text-left line_height_24">卡优惠</span>
                  <span class="flex_box font_12 text_right line_height_24">-￥{{ treatInfo.CardPreferentialAmount | toFixed | NumFormat }}</span>
                </div>

                <div class="dis_flex" v-if="treatInfo.MemberPreferentialAmount > 0 && cashierReceipt.SalePromotions && cashierReceipt.TreatAmount">
                  <span class="flex_box font_12 color_gray text-left line_height_24">会员优惠</span>
                  <span class="flex_box font_12 text_right line_height_24">-￥{{ treatInfo.MemberPreferentialAmount | toFixed | NumFormat }}</span>
                </div>
                <el-divider class="sell-el-divider"></el-divider>
                <div class="dis_flex flex_dir_column font_14 font_weight_600 flex_y_center color_999 padbm_10">
                  <span>{{ cashierReceipt.WriteTextFirst }}</span>
                  <span>{{ cashierReceipt.WriteTextSecond }}</span>
                </div>

                <div class="dis_flex font_12">
                  <span class="flex_box6 color_gray text-left line_height_24">签字：</span>
                </div>
              </div>
            </el-scrollbar>
          </el-col>
        </el-row>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible_ = false" size="small" :disabled="modalLoading">取 消</el-button>
        <el-button type="primary" @click="printTreatBillContent" :loading="modalLoading" v-prevent-click size="small">打印</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import printReceipt from "@/components/js/print.js";
import cashierAPI from "@/api/iBeauty/Order/cashierReceipt";
var socket;
export default {
  name: "zl_custom_component_treatCashierReceipt",

  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    treatInfo: {
      type: Object,
      default: () => {
        return {};
      },
    },
    cashierReceipt: {
      type: Object,
      default: () => {
        return {
          NameEncrypt: "",
        };
      },
    },
    entityName: {
      type: String,
      default: "",
    },
  },
  /** 监听数据变化   */
  watch: {
    visible: {
      deep: true,
      immediate: true,
      handler(val) {
        console.log("🚀 ~ handler ~ val:", val)
        if (val) {
          this.dialogVisible_ = val;
        }
      },
    },
  },
  /**  引入的组件  */
  components: {},
  /**  Vue 实例的数据对象**/
  data() {
    return {
      dialogVisible_: false,
      modalLoading: false,
    };
  },
  /**计算属性  */
  computed: {},
  /**  方法集合  */
  methods: {
    /**    */
    printTreatBillContent() {
      let params = {
        ID: this.treatInfo.ID,
      };
      cashierAPI
        .printTreatBillContent(params)
        .then((res) => {
          if (res.StateCode == 200) {
            for (let index = 0; index < res.Data.copies; index++) {
              printReceipt.doActionPrint(res.Data.printDocuments, (request) => {
                socket.send(JSON.stringify(request));
              });
            }
          }
        })
        .finally(() => {});
    },
    /**    */
    onClose() {
      this.$emit("update:visible", false);
    },
    // 姓名隐藏
    formatName(name) {
      return printReceipt.hiddenName(name);
    },
    // 手机号隐藏
    formatPhone(phone) {
      return printReceipt.hiddenPhone(phone);
    },
    /**  卡项中项目总长度  */
    getCardTotalLength(Cards) {
      let number = 0;
      for (let i = 0; i < Cards.length; i++) {
        const element = Cards[i];
        number += element.Project.length;
      }
      return number;
    },
    // 获取 当前卡项的序号
    getCurrentCardLength(savingCards, index, chileIndex) {
      let number = chileIndex + 1;
      for (let i = 0; i < savingCards.length; i++) {
        const element = savingCards[i];
        if (i < index) {
          number += element.Project.length;
        }
      }
      return number;
    },
  },
  /** 创建实例之前执行函数   */
  beforeCreate() {},
  /**  实例创建完成之后  */
  created() {},
  /**  在挂载开始之前被调用  */
  beforeMount() {},
  /**  实例被挂载后调用  */
  mounted() {
    socket = printReceipt.getSocket((res) => {
      if (res.status == "success") {
        this.$message.success({
          message: "打印成功",
          duration: 2000,
        });
        this.dialogVisible_ = false;
        // that.cashierReceiptDialogVisible = false;
      }
    });
  },
  /**  数据更新 之前 调用  */
  beforeUpdate() {},
  /** 数据更新 完成 调用   */
  updated() {},
  /**  实例销毁之前调用  */
  beforeDestroy() {},
  /**  实例销毁后调用  */
  destroyed() {},
};
</script>

<style lang="scss">
.zl_custom_component_treatCashierReceipt {
  .el-scrollbar_height {
    height: 100%;
    .el-scrollbar__wrap {
      overflow-x: hidden;
    }
  }
}
</style>
