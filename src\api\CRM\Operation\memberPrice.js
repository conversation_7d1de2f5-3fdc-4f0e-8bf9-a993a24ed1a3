/**
 * Created by preference on 2024/04/16
 *  zmx
 */

import * as API from "@/api/index";
export default {
  /**  查询客户等级 */
  customerLevel_all: (params) => {
    return API.POST("api/customerLevel/all", params);
  },
  /**  查询客户等级折扣配置 */
  customerPriceProjectConfig_customerLevelDiscountAll: (params) => {
    return API.POST("api/customerPriceProjectConfig/customerLevelDiscountAll", params);
  },
  /** 创建客户等级折扣配置  */
  customerPriceProjectConfig_createCustomerLevelDiscountConfig: (params) => {
    return API.POST("api/customerPriceProjectConfig/createCustomerLevelDiscountConfig", params);
  },
  /** 查询项目分类  */
  customerPriceProjectConfig_category: (params) => {
    return API.POST("api/customerPriceProjectConfig/category", params);
  },
  /** 查询项目分类  */
  customerPriceProjectConfig_selectedCategory: (params) => {
    return API.POST("api/customerPriceProjectConfig/selectedCategory", params);
  },
  /**  查询客户等级项目分类折扣配置 */
  customerPriceProjectConfig_customerLevelCategoryDiscountAll: (params) => {
    return API.POST("api/customerPriceProjectConfig/customerLevelCategoryDiscountAll", params);
  },
  /** 创建客户等级项目分类折扣配置  */
  customerPriceProjectConfig_createCustomerLevelCategoryDiscountConfig: (params) => {
    return API.POST("api/customerPriceProjectConfig/createCustomerLevelCategoryDiscountConfig", params);
  },
  /**  更新客户等级项目分类折扣配置 */
  customerPriceProjectConfig_updateCustomerLevelCategoryDiscountConfig: (params) => {
    return API.POST("api/customerPriceProjectConfig/updateCustomerLevelCategoryDiscountConfig", params);
  },
  /** 创建客户等级项目分类折扣配置  */
  customerPriceProjectConfig_deleteCustomerLevelCategoryDiscountConfig: (params) => {
    return API.POST("api/customerPriceProjectConfig/deleteCustomerLevelCategoryDiscountConfig", params);
  },
  /**  批量更新客户等级项目分类折扣配置 */
  customerPriceProjectConfig_batchUpdateCustomerLevelCategoryDiscountConfig: (params) => {
    return API.POST("api/customerPriceProjectConfig/batchUpdateCustomerLevelCategoryDiscountConfig", params);
  },
  /** 创建客户等级项目分类折扣配置  */
  customerPriceProjectConfig_batchDeleteCustomerLevelCategoryDiscountConfig: (params) => {
    return API.POST("api/customerPriceProjectConfig/batchDeleteCustomerLevelCategoryDiscountConfig", params);
  },
  /**  查询项目 */
  customerPriceProjectConfig_project: (params) => {
    return API.POST("api/customerPriceProjectConfig/project", params);
  },
  /**  查询已选项目 */
  customerPriceProjectConfig_selectedProject: (params) => {
    return API.POST("api/customerPriceProjectConfig/selectedProject", params);
  },
  /** 查询客户等级项目折扣配置  */
  customerPriceProjectConfig_customerLevelProjectDiscountPriceAll: (params) => {
    return API.POST("api/customerPriceProjectConfig/customerLevelProjectDiscountPriceAll", params);
  },
  /** 创建客户等级项目折扣配置  */
  customerPriceProjectConfig_createCustomerLevelProjectDiscountPriceConfig: (params) => {
    return API.POST("api/customerPriceProjectConfig/createCustomerLevelProjectDiscountPriceConfig", params);
  },
  /** 查询客户等级项目折扣配置  */
  customerPriceProjectConfig_updateCustomerLevelProjectDiscountConfig: (params) => {
    return API.POST("api/customerPriceProjectConfig/updateCustomerLevelProjectDiscountConfig", params);
  },
  /** 创建客户等级项目折扣配置  */
  customerPriceProjectConfig_deleteCustomerLevelProjectDiscountConfig: (params) => {
    return API.POST("api/customerPriceProjectConfig/deleteCustomerLevelProjectDiscountConfig", params);
  },
  /** 批量更新客户等级项目折扣配置  */
  customerPriceProjectConfig_batchUpdateCustomerLevelProjectDiscountConfig: (params) => {
    return API.POST("api/customerPriceProjectConfig/batchUpdateCustomerLevelProjectDiscountConfig", params);
  },
  /** 批量删除客户等级项目折扣配置  */
  customerPriceProjectConfig_batchDeleteCustomerLevelProjectDiscountConfig: (params) => {
    return API.POST("api/customerPriceProjectConfig/batchDeleteCustomerLevelProjectDiscountConfig", params);
  },
  /* ************************************************* */
  /* 时效卡 */
  /**  查询客户等级折扣配置 */
  customerPriceTimeCardConfig_customerLevelDiscountAll: (params) => {
    return API.POST("api/customerPriceTimeCardConfig/customerLevelDiscountAll", params);
  },
  /** 创建客户等级折扣配置  */
  customerPriceTimeCardConfig_createCustomerLevelDiscountConfig: (params) => {
    return API.POST("api/customerPriceTimeCardConfig/createCustomerLevelDiscountConfig", params);
  },
  /** 查询时效卡分类  */
  customerPriceTimeCardConfig_category: (params) => {
    return API.POST("api/customerPriceTimeCardConfig/category", params);
  },
  /** 查询已选时效卡分类  */
  customerPriceTimeCardConfig_selectedCategory: (params) => {
    return API.POST("api/customerPriceTimeCardConfig/selectedCategory", params);
  },
  /** 查询客户等级时效卡分类折扣配置  */
  customerPriceTimeCardConfig_customerLevelCategoryDiscountAll: (params) => {
    return API.POST("api/customerPriceTimeCardConfig/customerLevelCategoryDiscountAll", params);
  },
  /** 创建客户等级时效卡分类折扣配置  */
  customerPriceTimeCardConfig_createCustomerLevelCategoryDiscountConfig: (params) => {
    return API.POST("api/customerPriceTimeCardConfig/createCustomerLevelCategoryDiscountConfig", params);
  },
  /** 查询客户等级时效卡分类折扣配置  */
  customerPriceTimeCardConfig_updateCustomerLevelCategoryDiscountConfig: (params) => {
    return API.POST("api/customerPriceTimeCardConfig/updateCustomerLevelCategoryDiscountConfig", params);
  },
  /** 创建客户等级时效卡分类折扣配置  */
  customerPriceTimeCardConfig_deleteCustomerLevelCategoryDiscountConfig: (params) => {
    return API.POST("api/customerPriceTimeCardConfig/deleteCustomerLevelCategoryDiscountConfig", params);
  },
  /** 批量更新客户等级时效卡分类折扣配置  */
  customerPriceTimeCardConfig_batchUpdateCustomerLevelCategoryDiscountConfig: (params) => {
    return API.POST("api/customerPriceTimeCardConfig/batchUpdateCustomerLevelCategoryDiscountConfig", params);
  },
  /** 创建客户等级时效卡分类折扣配置  */
  customerPriceTimeCardConfig_batchDeleteCustomerLevelCategoryDiscountConfig: (params) => {
    return API.POST("api/customerPriceTimeCardConfig/batchDeleteCustomerLevelCategoryDiscountConfig", params);
  },
  /** 查询时效卡  */
  customerPriceTimeCardConfig_timeCard: (params) => {
    return API.POST("api/customerPriceTimeCardConfig/timeCard", params);
  },
  /** 查询时效卡  */
  customerPriceTimeCardConfig_selectedTimeCard: (params) => {
    return API.POST("api/customerPriceTimeCardConfig/selectedTimeCard", params);
  },
  /** 查询客户等级时效卡折扣配置  */
  customerPriceTimeCardConfig_customerLevelTimeCardDiscountPriceAll: (params) => {
    return API.POST("api/customerPriceTimeCardConfig/customerLevelTimeCardDiscountPriceAll", params);
  },
  /** 创建客户等级时效卡折扣配置  */
  customerPriceTimeCardConfig_createCustomerLevelTimeCardDiscountPriceConfig: (params) => {
    return API.POST("api/customerPriceTimeCardConfig/createCustomerLevelTimeCardDiscountPriceConfig", params);
  },

  /** 查询客户等级时效卡折扣配置  */
  customerPriceTimeCardConfig_updateCustomerLevelTimeCardDiscountConfig: (params) => {
    return API.POST("api/customerPriceTimeCardConfig/updateCustomerLevelTimeCardDiscountConfig", params);
  },
  /** 创建客户等级时效卡折扣配置  */
  customerPriceTimeCardConfig_deleteCustomerLevelTimeCardDiscountConfig: (params) => {
    return API.POST("api/customerPriceTimeCardConfig/deleteCustomerLevelTimeCardDiscountConfig", params);
  },

  /** 批量更新客户等级时效卡折扣配置  */
  customerPriceTimeCardConfig_batchUpdateCustomerLevelTimeCardDiscountConfig: (params) => {
    return API.POST("api/customerPriceTimeCardConfig/batchUpdateCustomerLevelTimeCardDiscountConfig", params);
  },
  /** 批量删除客户等级时效卡折扣配置  */
  customerPriceTimeCardConfig_batchDeleteCustomerLevelTimeCardDiscountConfig: (params) => {
    return API.POST("api/customerPriceTimeCardConfig/batchDeleteCustomerLevelTimeCardDiscountConfig", params);
  },
  /* ************************************************* */
  /* 通用次卡 */
  /**  查询客户等级折扣配置 */
  customerPriceGeneralCardConfig_customerLevelDiscountAll: (params) => {
    return API.POST("api/customerPriceGeneralCardConfig/customerLevelDiscountAll", params);
  },
  /** 创建客户等级折扣配置  */
  customerPriceGeneralCardConfig_createCustomerLevelDiscountConfig: (params) => {
    return API.POST("api/customerPriceGeneralCardConfig/createCustomerLevelDiscountConfig", params);
  },
  /** 查询通用次卡分类  */
  customerPriceGeneralCardConfig_category: (params) => {
    return API.POST("api/customerPriceGeneralCardConfig/category", params);
  },
  /** 查询已选通用次卡分类  */
  customerPriceGeneralCardConfig_selectedCategory: (params) => {
    return API.POST("api/customerPriceGeneralCardConfig/selectedCategory", params);
  },
  /** 查询客户等级通用次卡分类折扣配置  */
  customerPriceGeneralCardConfig_customerLevelCategoryDiscountAll: (params) => {
    return API.POST("api/customerPriceGeneralCardConfig/customerLevelCategoryDiscountAll", params);
  },
  /** 创建客户等级通用次卡分类折扣配置  */
  customerPriceGeneralCardConfig_createCustomerLevelCategoryDiscountConfig: (params) => {
    return API.POST("api/customerPriceGeneralCardConfig/createCustomerLevelCategoryDiscountConfig", params);
  },
  /** 查询客户等级通用次卡分类折扣配置  */
  customerPriceGeneralCardConfig_updateCustomerLevelCategoryDiscountConfig: (params) => {
    return API.POST("api/customerPriceGeneralCardConfig/updateCustomerLevelCategoryDiscountConfig", params);
  },
  /** 创建客户等级通用次卡分类折扣配置  */
  customerPriceGeneralCardConfig_deleteCustomerLevelCategoryDiscountConfig: (params) => {
    return API.POST("api/customerPriceGeneralCardConfig/deleteCustomerLevelCategoryDiscountConfig", params);
  },
  /** 批量更新客户等级通用次卡分类折扣配置  */
  customerPriceGeneralCardConfig_batchUpdateCustomerLevelCategoryDiscountConfig: (params) => {
    return API.POST("api/customerPriceGeneralCardConfig/batchUpdateCustomerLevelCategoryDiscountConfig", params);
  },
  /** 批量删除客户等级通用次卡分类折扣配置  */
  customerPriceGeneralCardConfig_batchDeleteCustomerLevelCategoryDiscountConfig: (params) => {
    return API.POST("api/customerPriceGeneralCardConfig/batchDeleteCustomerLevelCategoryDiscountConfig", params);
  },
  /** 查询通用次卡  */
  customerPriceGeneralCardConfig_generalCard: (params) => {
    return API.POST("api/customerPriceGeneralCardConfig/generalCard", params);
  },
  /** 查询通用次卡  */
  customerPriceGeneralCardConfig_selectedGeneralCard: (params) => {
    return API.POST("api/customerPriceGeneralCardConfig/selectedGeneralCard", params);
  },
  /**  查询客户等级通用次卡折扣配置 */
  customerPriceGeneralCardConfig_customerLevelGeneralCardDiscountPriceAll: (params) => {
    return API.POST("api/customerPriceGeneralCardConfig/customerLevelGeneralCardDiscountPriceAll", params);
  },
  /** 创建客户等级通用次卡折扣配置  */
  customerPriceGeneralCardConfig_createCustomerLevelGeneralCardDiscountPriceConfig: (params) => {
    return API.POST("api/customerPriceGeneralCardConfig/createCustomerLevelGeneralCardDiscountPriceConfig", params);
  },
  /**  查询客户等级通用次卡折扣配置 */
  customerPriceGeneralCardConfig_updateCustomerLevelGeneralCardDiscountConfig: (params) => {
    return API.POST("api/customerPriceGeneralCardConfig/updateCustomerLevelGeneralCardDiscountConfig", params);
  },
  /** 创建客户等级通用次卡折扣配置  */
  customerPriceGeneralCardConfig_deleteCustomerLevelGeneralCardDiscountConfig: (params) => {
    return API.POST("api/customerPriceGeneralCardConfig/deleteCustomerLevelGeneralCardDiscountConfig", params);
  },
  /**  批量更新客户等级通用次卡折扣配置 */
  customerPriceGeneralCardConfig_batchUpdateCustomerLevelGeneralCardDiscountConfig: (params) => {
    return API.POST("api/customerPriceGeneralCardConfig/batchUpdateCustomerLevelGeneralCardDiscountConfig", params);
  },
  /** 创建客户等级通用次卡折扣配置  */
  customerPriceGeneralCardConfig_batchDeleteCustomerLevelGeneralCardDiscountConfig: (params) => {
    return API.POST("api/customerPriceGeneralCardConfig/batchDeleteCustomerLevelGeneralCardDiscountConfig", params);
  },
  /* ************************************************* */

  /**  查询客户等级折扣配置 */
  customerPricePackageCardConfig_customerLevelDiscountAll: (params) => {
    return API.POST("api/customerPricePackageCardConfig/customerLevelDiscountAll", params);
  },
  /** 创建客户等级折扣配置  */
  customerPricePackageCardConfig_createCustomerLevelDiscountConfig: (params) => {
    return API.POST("api/customerPricePackageCardConfig/createCustomerLevelDiscountConfig", params);
  },
  /** 查询套餐卡分类  */
  customerPricePackageCardConfig_category: (params) => {
    return API.POST("api/customerPricePackageCardConfig/category", params);
  },
  /** 查询套餐卡分类  */
  customerPricePackageCardConfig_selectedCategory: (params) => {
    return API.POST("api/customerPricePackageCardConfig/selectedCategory", params);
  },
  /** 查询客户等级套餐卡分类折扣配置  */
  customerPricePackageCardConfig_customerLevelCategoryDiscountAll: (params) => {
    return API.POST("api/customerPricePackageCardConfig/customerLevelCategoryDiscountAll", params);
  },
  /**  创建客户等级套餐卡分类折扣配置 */
  customerPricePackageCardConfig_createCustomerLevelCategoryDiscountConfig: (params) => {
    return API.POST("api/customerPricePackageCardConfig/createCustomerLevelCategoryDiscountConfig", params);
  },
  /** 查询客户等级套餐卡分类折扣配置  */
  customerPricePackageCardConfig_updateCustomerLevelCategoryDiscountConfig: (params) => {
    return API.POST("api/customerPricePackageCardConfig/updateCustomerLevelCategoryDiscountConfig", params);
  },
  /**  创建客户等级套餐卡分类折扣配置 */
  customerPricePackageCardConfig_deleteCustomerLevelCategoryDiscountConfig: (params) => {
    return API.POST("api/customerPricePackageCardConfig/deleteCustomerLevelCategoryDiscountConfig", params);
  },
  /** 批量更新客户等级套餐卡分类折扣配置  */
  customerPricePackageCardConfig_batchUpdateCustomerLevelCategoryDiscountConfig: (params) => {
    return API.POST("api/customerPricePackageCardConfig/batchUpdateCustomerLevelCategoryDiscountConfig", params);
  },
  /**  创建客户等级套餐卡分类折扣配置 */
  customerPricePackageCardConfig_batchDeleteCustomerLevelCategoryDiscountConfig: (params) => {
    return API.POST("api/customerPricePackageCardConfig/batchDeleteCustomerLevelCategoryDiscountConfig", params);
  },
  /**  查询套餐卡 */
  customerPricePackageCardConfig_packageCard: (params) => {
    return API.POST("api/customerPricePackageCardConfig/packageCard", params);
  },
  /**  查询套餐卡 */
  customerPricePackageCardConfig_selectedPackageCard: (params) => {
    return API.POST("api/customerPricePackageCardConfig/selectedPackageCard", params);
  },
  /** 查询客户等级套餐卡折扣配置  */
  customerPricePackageCardConfig_customerLevelPackageCardDiscountPriceAll: (params) => {
    return API.POST("api/customerPricePackageCardConfig/customerLevelPackageCardDiscountPriceAll", params);
  },
  /** 创建客户等级套餐卡折扣配置  */
  customerPricePackageCardConfig_createCustomerLevelPackageCardDiscountPriceConfig: (params) => {
    return API.POST("api/customerPricePackageCardConfig/createCustomerLevelPackageCardDiscountPriceConfig", params);
  },
  /** 查询客户等级套餐卡折扣配置  */
  customerPricePackageCardConfig_updateCustomerLevelPackageCardDiscountConfig: (params) => {
    return API.POST("api/customerPricePackageCardConfig/updateCustomerLevelPackageCardDiscountConfig", params);
  },
  /** 创建客户等级套餐卡折扣配置  */
  customerPricePackageCardConfig_deleteCustomerLevelPackageCardDiscountConfig: (params) => {
    return API.POST("api/customerPricePackageCardConfig/deleteCustomerLevelPackageCardDiscountConfig", params);
  },
  /** 批量更新客户等级套餐卡折扣配置  */
  customerPricePackageCardConfig_batchUpdateCustomerLevelPackageCardDiscountConfig: (params) => {
    return API.POST("api/customerPricePackageCardConfig/batchUpdateCustomerLevelPackageCardDiscountConfig", params);
  },
  /** 批量删除客户等级套餐卡折扣配置  */
  customerPricePackageCardConfig_batchDeleteCustomerLevelPackageCardDiscountConfig: (params) => {
    return API.POST("api/customerPricePackageCardConfig/batchDeleteCustomerLevelPackageCardDiscountConfig", params);
  },
  /* ************************************************* */
  /**  查询客户等级折扣配置 */
  customerPriceProductConfig_customerLevelDiscountAll: (params) => {
    return API.POST("api/customerPriceProductConfig/customerLevelDiscountAll", params);
  },
  /** 创建客户等级折扣配置  */
  customerPriceProductConfig_createCustomerLevelDiscountConfig: (params) => {
    return API.POST("api/customerPriceProductConfig/createCustomerLevelDiscountConfig", params);
  },
  /**  查询产品分类 */
  customerPriceProductConfig_category: (params) => {
    return API.POST("api/customerPriceProductConfig/category", params);
  },
  /**  查询已选产品分类 */
  customerPriceProductConfig_selectedCategory: (params) => {
    return API.POST("api/customerPriceProductConfig/selectedCategory", params);
  },
  /** 查询客户等级产品分类折扣配置  */
  customerPriceProductConfig_customerLevelCategoryDiscountAll: (params) => {
    return API.POST("api/customerPriceProductConfig/customerLevelCategoryDiscountAll", params);
  },
  /** 创建客户等级产品分类折扣配置  */
  customerPriceProductConfig_createCustomerLevelCategoryDiscountConfig: (params) => {
    return API.POST("api/customerPriceProductConfig/createCustomerLevelCategoryDiscountConfig", params);
  },
  /** 查询客户等级产品分类折扣配置  */
  customerPriceProductConfig_updateCustomerLevelCategoryDiscountConfig: (params) => {
    return API.POST("api/customerPriceProductConfig/updateCustomerLevelCategoryDiscountConfig", params);
  },
  /** 创建客户等级产品分类折扣配置  */
  customerPriceProductConfig_deleteCustomerLevelCategoryDiscountConfig: (params) => {
    return API.POST("api/customerPriceProductConfig/deleteCustomerLevelCategoryDiscountConfig", params);
  },
  /** 批量更新客户等级产品分类折扣配置  */
  customerPriceProductConfig_batchUpdateCustomerLevelCategoryDiscountConfig: (params) => {
    return API.POST("api/customerPriceProductConfig/batchUpdateCustomerLevelCategoryDiscountConfig", params);
  },
  /** 批量删除客户等级产品分类折扣配置  */
  customerPriceProductConfig_batchDeleteCustomerLevelCategoryDiscountConfig: (params) => {
    return API.POST("api/customerPriceProductConfig/batchDeleteCustomerLevelCategoryDiscountConfig", params);
  },
  /**  查询产品 */
  customerPriceProductConfig_product: (params) => {
    return API.POST("api/customerPriceProductConfig/product", params);
  },
  /**  查询产品 */
  customerPriceProductConfig_selectedProduct: (params) => {
    return API.POST("api/customerPriceProductConfig/selectedProduct", params);
  },
  /** 查询客户等级产品折扣配置  */
  customerPriceProductConfig_customerLevelProductDiscountPriceAll: (params) => {
    return API.POST("api/customerPriceProductConfig/customerLevelProductDiscountPriceAll", params);
  },
  /** 创建客户等级产品折扣配置  */
  customerPriceProductConfig_createCustomerLevelProductDiscountPriceConfig: (params) => {
    return API.POST("api/customerPriceProductConfig/createCustomerLevelProductDiscountPriceConfig", params);
  },
  /** 查询客户等级产品折扣配置  */
  customerPriceProductConfig_updateCustomerLevelProductDiscountConfig: (params) => {
    return API.POST("api/customerPriceProductConfig/updateCustomerLevelProductDiscountConfig", params);
  },
  /** 创建客户等级产品折扣配置  */
  customerPriceProductConfig_deleteCustomerLevelProductDiscountConfig: (params) => {
    return API.POST("api/customerPriceProductConfig/deleteCustomerLevelProductDiscountConfig", params);
  },
  /** 批量更新客户等级产品折扣配置  */
  customerPriceProductConfig_batchUpdateCustomerLevelProductDiscountConfig: (params) => {
    return API.POST("api/customerPriceProductConfig/batchUpdateCustomerLevelProductDiscountConfig", params);
  },
  /** 批量删除客户等级产品折扣配置  */
  customerPriceProductConfig_batchDeleteCustomerLevelProductDiscountConfig: (params) => {
    return API.POST("api/customerPriceProductConfig/batchDeleteCustomerLevelProductDiscountConfig", params);
  },
};
