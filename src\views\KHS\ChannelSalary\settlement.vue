<template>
  <div class="ChannelSalarySettlement content_body">
    <!-- 头部搜索 -->
    <div class="nav_header">
      <el-row>
        <el-col :span="21">
          <el-form :inline="true" size="small" @submit.native.prevent @keyup.enter.native="handleSearch">
            <el-form-item label="结算周期" size="small">
              <el-select v-model="searchData.SalarySettlementIntervalID" placeholder="选择结算周期" clearable filterable
                :default-first-option="true" size="small" popper-class="monthSel" @change="handleSearch">
                <el-option v-for="item in settlementPeriodList" :key="item.ID" :label="item.SettlementMonth"
                  :value="item.ID" style="height: 48px">
                  <div style="height: 25px; line-height: 30px">{{ item.SettlementMonth }}</div>
                  <div style="height: 18px; line-height: 18px; font-size: 12px" class="color_999">{{ item.StartDate }} -
                    {{ item.EndDate }}</div>
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="渠道名称" size="small">
              <el-input v-model="searchData.Name" placeholder="输入渠道名称搜索" clearable @clear="handleSearch"></el-input>
            </el-form-item>
            <el-form-item label="上级渠道" size="small">
              <treeselect v-model="searchData.ParentID" :options="allChannelList" :normalizer="normalizer"
                clearValueText :max-height="200" noResultsText="无匹配数据" placeholder="选择上级渠道" @input="handleSearch" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" size="small" @click="handleSearch">搜 索</el-button>
            </el-form-item>
          </el-form>
        </el-col>
        <el-col :span="1" class="text_right">
          <el-button type="primary" size="small" v-prevent-click @click="downloadExcel" :loading="downloadLoading">导出
          </el-button>
        </el-col>
        <el-col :span="2" class="text_right">
          <el-button type="primary" size="small" v-prevent-click @click="settlementClick">业绩提成结算 </el-button>
        </el-col>
      </el-row>
    </div>
    <!-- 表格 -->
    <el-table size="small" :data="tableData" v-loading="loading" tooltip-effect="light">
      <el-table-column prop="SettlementMonth" label="结算周期">
        <template slot-scope="scope">
          <div>{{scope.row.SettlementMonth}}</div>
          <div style="height: 18px; line-height: 18px; font-size: 12px" class="color_999">{{ scope.row.StartDate }} -
            {{ scope.row.EndDate }}</div>
        </template>
      </el-table-column>
      <el-table-column prop="ChannelName" label="渠道名称"></el-table-column>
      <el-table-column prop="ChannelTypeName" label="渠道类型"></el-table-column>
      <el-table-column prop="ParentName" label="上级渠道"></el-table-column>
      <el-table-column prop="ChannelPerformanceCommission" label="提成">
        <template slot-scope="scope"> ￥{{ scope.row.ChannelPerformanceCommission | toFixed | NumFormat }} </template>
      </el-table-column>
      <el-table-column label="操作" width="100">
        <template slot-scope="scope">
          <el-button type="primary" size="small" v-prevent-click @click="settlementDetailsClick(scope.row)">结算明细
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <div class="pad_15 text_right">
      <el-pagination background v-if="paginations.total > 0" @current-change="handleCurrentChange"
        :current-page.sync="paginations.page" :page-size="paginations.page_size" :layout="paginations.layout"
        :total="paginations.total"></el-pagination>
    </div>
    <!-- 业绩提成结算 -->
    <el-dialog title="业绩提成结算" :visible.sync="settlementPerformanceDialogVisible" width="590px">
      <el-form size="small" :model="settlementPerformanceData" ref="settlementPerformanceRef"
        :rules="settlementPerformanceRules" label-width="100px" class="demo-ruleForm">
        <el-form-item label="结算月份" prop="SalarySettlementIntervalID">
          <el-col :span="13">
            <el-select v-model="settlementPerformanceData.SalarySettlementIntervalID" placeholder="请选择结算月份" filterable
              clearable>
              <el-option v-for="item in settlementPeriodList" :key="item.ID" :label="item.SettlementMonth"
                :value="item.ID"> </el-option>
            </el-select>
          </el-col>
          <el-col :span="11">
            <el-button type="primary" size="small" v-prevent-click @click="settlementPeriodClick"> 结算周期设置</el-button>
          </el-col>
        </el-form-item>
        <el-form-item label="结算周期" prop="QueryDate">
          <el-date-picker v-model="settlementPerformanceData.QueryDate" :disabled="true" type="daterange"
            range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" value-format="yyyy-MM-dd">
          </el-date-picker>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button size="small" @click="settlementPerformanceDialogVisible = false" v-prevent-click>取 消</el-button>
        <el-button size="small" type="primary" @click="saveSettlementPerformance" v-prevent-click
          :loading="saveLoading">结 算 </el-button>
      </span>
    </el-dialog>
    <!-- 结算周期设置 -->
    <el-dialog title="结算周期设置" :visible.sync="settlementPeriodDialogVisible" width="550px">
      <el-form size="small" :model="settlementPeriodData" ref="settlementPeriodRef" :rules="settlementPeriodRules"
        label-width="100px" class="demo-ruleForm">
        <el-form-item label="结算月份" prop="SettlementMonth">
          <el-input v-model="settlementPeriodData.SettlementMonth"></el-input>
        </el-form-item>
        <el-form-item label="结算周期" prop="QueryDate">
          <el-date-picker v-model="settlementPeriodData.QueryDate" type="daterange" range-separator="至"
            start-placeholder="开始日期" end-placeholder="结束日期" value-format="yyyy-MM-dd">
          </el-date-picker>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button size="small" @click="settlementPeriodDialogVisible = false" v-prevent-click>取 消</el-button>
        <el-button size="small" type="primary" @click="saveSettlementPeriod" v-prevent-click
          :loading="settlementSaveLoading">保 存</el-button>
      </span>
    </el-dialog>
    <!-- 渠道业绩提成结算明细 -->
    <el-dialog title="渠道业绩提成结算明细" :visible.sync="settlementDetailsDialogVisible" width="1100px" height="350">
      <el-table size="small" ooltip-effect="light" :data="commissionDetailsData">
        <el-table-column prop="CommissionName" label="提成名称"></el-table-column>
        <el-table-column prop="PerformanceName" label="业绩取值名称"></el-table-column>
        <el-table-column prop="Commission" label="提成">
          <template slot-scope="scope"> ￥{{ scope.row.Commission | toFixed | NumFormat }} </template>
        </el-table-column>
        <el-table-column prop="Performance" label="业绩">
          <template slot-scope="scope"> ￥{{ scope.row.Performance | toFixed | NumFormat }} </template>
        </el-table-column>
      </el-table>
      <span slot="footer" class="dialog-footer">
        <el-button size="small" @click="settlementDetailsDialogVisible = false">取 消</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import API from "@/api/KHS/ChannelSalary/settlement";
import channelInfoAPI from "@/api/CRM/Channel/channelInfo";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";

export default {
  name: "ChannelSalarySettlement",

  props: {},
  /** 监听数据变化   */
  watch: {
    "settlementPerformanceData.SalarySettlementIntervalID"(ID) {
      if (ID) {
        const item = this.settlementPeriodList.find((item) => item.ID == ID);
        if (item) {
          this.settlementPerformanceData.QueryDate = [item.StartDate, item.EndDate];
        }
      }
    },
    deep: true,
  },
  /**  引入的组件  */
  components: { Treeselect },
  /**  Vue 实例的数据对象**/
  data() {
    return {
      loading: false,
      settlementSaveLoading: false,
      saveLoading: false,
      downloadLoading: false,
      settlementDetailsDialogVisible: false, // 结算明细
      settlementPeriodDialogVisible: false, // 设置结算周期
      settlementPerformanceDialogVisible: false, // 业绩提成结算
      searchData: {
        ParentID: null,
        SalarySettlementIntervalID: "",
        Name: "",
      }, // 搜索数据
      allChannelList: [], // 渠道信息
      settlementPeriodList: [], // 结算周期
      commissionDetailsData: [], // 结算明细数据
      settlementPeriodData: {
        SettlementMonth: "",
        QueryDate: [],
      }, // 设置结算周期
      settlementPerformanceData: {
        SalarySettlementIntervalID: "",
        QueryDate: [],
      }, // 业绩提成结算数据
      tableData: [{ data: "测试数据" }], // 表格数据
      paginations: {
        page: 1, // 当前位于哪页
        total: 0, // 总数
        page_size: 10, // 1页显示多少条
        layout: "total, prev, pager, next,jumper", // 翻页属性
      }, // 分页属性
      settlementPerformanceRules: {
        SalarySettlementIntervalID: [{ required: true, message: "请选择结算月份", trigger: "change" }],
      }, // 业绩提成结算验证
      settlementPeriodRules: {
        SettlementMonth: [{ required: true, message: "请输入结算月份", trigger: "blur" }],
        QueryDate: [{ required: true, message: "请输入结算周期", trigger: "change" }],
      }, // 结算周期设置验证
    };
  },
  /**计算属性  */
  computed: {},
  /**  方法集合  */
  methods: {
    /* 搜索 */
    handleSearch() {
      let that = this;
      that.paginations.page = 1;
      that.getAllchannelSalarySheet();
    },
    /* 分页 */
    handleCurrentChange(page) {
      let that = this;
      that.paginations.page = page;
      that.getAllchannelSalarySheet();
    },
    /* 结算列表数据 */
    getAllchannelSalarySheet() {
      let that = this;
      that.loading = true;
      let params = {
        PageNum: that.paginations.page,
        Name: that.searchData.Name, //模糊搜索
        ChannelSalarySettlementIntervalID: that.searchData.SalarySettlementIntervalID, //结算周期ID
        ParentID: that.searchData.ParentID, //上级单位
      };
      API.getAllchannelSalarySheet(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.tableData = res.List;
            that.paginations.total = res.Total;
            that.paginations.page_size = res.PageSize;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(() => {
          that.loading = false;
        });
    },
    /* 业绩提成结算 */
    settlementClick() {
      let that = this;
      that.settlementPerformanceDialogVisible = true;
      that.settlementPerformanceData = {
        SalarySettlementIntervalID: "",
        QueryDate: [],
      };
      if (this.$refs.settlementPerformanceRef) {
        this.$refs.settlementPerformanceRef.resetFields();
      }
    },
    /* 业绩提成结算保存 */
    saveSettlementPerformance() {
      let that = this;
      that.$refs.settlementPerformanceRef.validate((valid) => {
        if (valid) {
          that.saveLoading = true;
          let params = {
            ChannelSalarySettlementIntervalID: that.settlementPerformanceData.SalarySettlementIntervalID,
          };
          API.settlement(params)
            .then((res) => {
              if (res.StateCode == 200) {
                that.$message.success({
                  message: "结算成功",
                  duration: 2000,
                });
                that.settlementPerformanceDialogVisible = false;
                that.handleSearch();
              } else {
                that.$message.error({
                  message: res.Message,
                  duration: 2000,
                });
              }
            })
            .finally(() => {
              that.saveLoading = false;
            });
        }
      });
    },
    /* 结算周期设置 */
    settlementPeriodClick() {
      let that = this;
      that.settlementPeriodDialogVisible = true;
      that.settlementPeriodData = {
        SettlementMonth: "",
        QueryDate: [],
      };
      if (this.$refs.settlementPeriodRef) {
        this.$refs.settlementPeriodRef.resetFields();
      }
    },
    /* 结算周期设置保存 */
    saveSettlementPeriod() {
      let that = this;
      that.$refs.settlementPeriodRef.validate((valid) => {
        if (valid) {
          that.settlementSaveLoading = true;
          let params = {
            SettlementMonth: that.settlementPeriodData.SettlementMonth, //结算月份
            StartDate: that.settlementPeriodData.QueryDate[0], //开始时间
            EndDate: that.settlementPeriodData.QueryDate[1], //结束时间
          };
          API.createSettlementInterval(params)
            .then((res) => {
              if (res.StateCode == 200) {
                that.$message.success({
                  message: "结算周期设置成功",
                  duration: 2000,
                });
                that.settlementPeriodDialogVisible = false;
                that.getAllSettlementInterval();
              } else {
                that.$message.error({
                  message: res.Message,
                  duration: 2000,
                });
              }
            })
            .finally(() => {
              that.settlementSaveLoading = false;
            });
        }
      });
    },
    /* 结算明细 */
    settlementDetailsClick(row) {
      let that = this;
      that.getChannelSalarySheetDetail(row);
    },
    /* 获取结算明细 */
    getChannelSalarySheetDetail(row) {
      let that = this;
      let params = {
        ChannelSalarySettlementIntervalID: row.ChannelSalarySettlementIntervalID,
        ChannelID: row.ChannelID,
      };
      API.getChannelSalarySheetDetail(params).then((res) => {
        if (res.StateCode == 200) {
          that.settlementDetailsDialogVisible = true;
          that.commissionDetailsData = res.Data;
        }
      });
    },
    /* 业绩数据导出 */
    downloadExcel() {
      let that = this;
      that.downloadLoading = true;
      let params = {
        Name: that.searchData.Name, //模糊搜索
        ChannelSalarySettlementIntervalID: that.searchData.SalarySettlementIntervalID, //结算周期ID
        ParentID: that.searchData.ParentID, //上级单位
      };
      API.exportChannelSalarySheetExcek(params)
        .then((res) => {
          this.$message.success({
            message: "正在导出",
            duration: "4000",
          });
          const link = document.createElement("a");
          let blob = new Blob([res], { type: "application/octet-stream" });
          link.style.display = "none";
          link.href = URL.createObjectURL(blob);
          link.download = "渠道业绩提成结算.xlsx"; //文件名
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
        })
        .finally(() => {
          that.downloadLoading = false;
        });
    },
    /* 结算周期查询 */
    getAllSettlementInterval() {
      let that = this;
      let params = {};
      API.getAllSettlementInterval(params).then((res) => {
        if (res.StateCode == 200) {
          that.settlementPeriodList = res.Data;
        }
      });
    },
    /*  获取渠道 顶部筛选条件 */
    getAllchannel() {
      let that = this;
      let params = {};
      channelInfoAPI.getAllchannel(params).then((res) => {
        if (res.StateCode == 200) {
          that.allChannelList = res.Data;
        }
      });
    },
    /* 树形结构数据转换 */
    normalizer(node) {
      return {
        id: node.ID,
        label: node.Name,
        children: node.Child,
      };
    },
  },
  /** 创建实例之前执行函数   */
  beforeCreate() { },
  /**  实例创建完成之后  */
  created() { },
  /**  在挂载开始之前被调用  */
  beforeMount() { },
  /**  实例被挂载后调用  */
  mounted() {
    let that = this;
    that.getAllchannel();
    that.getAllSettlementInterval();
    that.getAllchannelSalarySheet();
  },
  /**  数据更新 之前 调用  */
  beforeUpdate() { },
  /** 数据更新 完成 调用   */
  updated() { },
  /**  实例销毁之前调用  */
  beforeDestroy() { },
  /**  实例销毁后调用  */
  destroyed() { },
};
</script>

<style lang="scss">
.ChannelSalarySettlement {
  .vue-treeselect__control {
    width: 250px;
    margin-right: 10px;
    height: 32px;
  }

  .vue-treeselect__menu-container {
    width: 250px;
  }
}
</style>
