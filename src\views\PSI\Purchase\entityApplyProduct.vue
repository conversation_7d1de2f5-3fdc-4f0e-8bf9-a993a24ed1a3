<template>
  <div class="content_body EntityApplyProduct" v-loading="loading">
    <div class="nav_header">
      <el-row>
        <el-col :span="22">
          <el-form :inline="true" size="small" :model="searchForm" @keyup.enter.native="handleSearchEntityApplyProductClick">
            <el-form-item label="单据号">
              <el-input v-model="searchForm.ID" placeholder="输入单据号搜索" clearable @clear="handleSearchEntityApplyProductClick" @keyup.enter.native="handleSearchEntityApplyProductClick"> </el-input>
            </el-form-item>
            <el-form-item v-if="purchaseStorage.length > 1" label="仓库/门店">
              <el-select v-model="searchForm.EntityID" :default-first-option="true" @change="handleSearchEntityApplyProductClick" @clear="handleSearchEntityApplyProductClick" clearable filterable placeholder="请选择仓库">
                <el-option v-for="item in purchaseStorage" :key="item.ID" :label="item.EntityName" :value="item.ID"> </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="产品名称">
              <el-input v-model="searchForm.Name" placeholder="输入产品名称、别名搜索" clearable @clear="handleSearchEntityApplyProductClick" @keyup.enter.native="handleSearchEntityApplyProductClick"> </el-input>
            </el-form-item>
            <el-form-item label="制单日期">
              <el-date-picker v-model="searchForm.DateTime" unlink-panels type="daterange" range-separator="至" value-format="yyyy-MM-dd" start-placeholder="开始日期" end-placeholder="结束日期" :picker-options="pickerOptions" clearable @change="searchDateChange"></el-date-picker>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleSearchEntityApplyProductClick" v-prevent-click>搜索</el-button>
            </el-form-item>
          </el-form>
        </el-col>
        <el-col :span="2" class="text_right">
          <el-button v-if="isAdd" type="primary" @click="addEntityApplyProductClick" size="small" v-prevent-click>新增 </el-button>
        </el-col>
      </el-row>
    </div>

    <!-- 单据状态，05:草稿、10：待审核、20：待配送、30：待入库、40：已驳回、50：已完成、60：已关闭 -->
    <el-tabs v-model="searchForm.BillStatus" type="border-card" @tab-click="tabsHandleClick">
      <el-tab-pane label="全部" name="0"></el-tab-pane>
      <el-tab-pane name="05">
        <span slot="label"
          >草稿
          <el-badge v-if="StatusNumberInfo && StatusNumberInfo.BillStatus05 != 0" is-dot />
        </span>
      </el-tab-pane>
      <el-tab-pane name="10">
        <span slot="label"
          >待审核
          <el-badge v-if="StatusNumberInfo && StatusNumberInfo.BillStatus10 != 0" is-dot />
        </span>
      </el-tab-pane>
      <el-tab-pane name="15">
        <span slot="label"
          >待付款
          <el-badge v-if="StatusNumberInfo && StatusNumberInfo.BillStatus15 != 0" is-dot />
        </span>
      </el-tab-pane>
      <el-tab-pane name="20">
        <span slot="label"
          >待配送
          <el-badge v-if="StatusNumberInfo && StatusNumberInfo.BillStatus20 != 0" is-dot />
        </span>
      </el-tab-pane>
      <el-tab-pane name="30">
        <span slot="label"
          >待入库
          <el-badge v-if="StatusNumberInfo && StatusNumberInfo.BillStatus30 != 0" is-dot />
        </span>
      </el-tab-pane>
      <el-tab-pane name="40">
        <span slot="label"
          >已驳回
          <el-badge v-if="StatusNumberInfo && StatusNumberInfo.BillStatus40 != 0" is-dot />
        </span>
      </el-tab-pane>
      <el-tab-pane label="已完成" name="50">
        <!-- <span slot="label">已完成<el-badge style="height: 30px;" v-model="StatusNumberInfo.BillStatus50" /><span v-if="StatusNumberInfo  && StatusNumberInfo.BillStatus50 != 0" class="color_red">({{StatusNumberInfo.BillStatus50}})</span> </span> -->
      </el-tab-pane>
      <el-tab-pane label="已关闭" name="60">
        <!-- <span slot="label">已关闭<span v-if="StatusNumberInfo  && StatusNumberInfo.BillStatus60 != 0" class="color_red">({{StatusNumberInfo.BillStatus60}})</span> </span> -->
      </el-tab-pane>
    </el-tabs>

    <el-table size="small" class="martp_10" :data="inventoryApplyList">
      <el-table-column prop="ID" label="单据号"></el-table-column>
      <el-table-column prop="InboundEntityName" label="申请仓库/门店"></el-table-column>
      <el-table-column prop="BillStatus" label="单据状态" :formatter="ApplyOrderBillStatusFormatter"></el-table-column>
      <el-table-column prop="TotalAmount" label="单据总额（元）">
        <template slot-scope="scope">￥{{ scope.row.TotalAmount | toFixed | NumFormat }}</template>
      </el-table-column>
      <el-table-column prop="CreatedOn" label="制单时间">
        <template slot-scope="scope">
          {{ scope.row.CreatedOn | dateFormat("YYYY-MM-DD HH:mm") }}
        </template>
      </el-table-column>
      <el-table-column prop="CreatedByName" label="申请人"></el-table-column>
      <el-table-column prop="Remark" label="备注信息" show-overflow-tooltip></el-table-column>
      <el-table-column label="操作" width="240">
        <template slot-scope="scope">
          <!-- 主动关闭 -->
          <el-button v-if="(scope.row.BillStatus == '10' && isClose) || (scope.row.BillStatus == '05' && isClose && isClose)" type="danger" size="small" @click="closeEntityApplyProductClick(scope.row)" v-prevent-click>关 闭</el-button>

          <!-- 审核驳回 编辑 -->
          <el-button v-if="(isEdit && scope.row.BillStatus == '40') || (isEdit && scope.row.BillStatus == '05')" size="small" type="primary" v-prevent-click @click="updateApplyProductInfo(scope.row)">编 辑</el-button>
          <!-- 审核驳回关闭 -->
          <el-button v-if="scope.row.BillStatus == '40' && isTurnClose" type="danger" size="small" @click="turnCloseEntityApplyProductClick(scope.row)" v-prevent-click>关 闭</el-button>

          <el-button v-if="scope.row.BillStatus == '10' && isCheck" class="martp_5" type="primary" size="small" @click="approvalEntityApplyProductDetail(scope.row)" v-prevent-click>审 批</el-button>
          <el-button v-if="scope.row.BillStatus == '20' && isDelivery && scope.row.IsHavePermission" type="primary" size="small" @click="outboundEntityApplyProductClick(scope.row)" v-prevent-click>配 送</el-button>
          <el-button v-if="scope.row.BillStatus == '30' && isStorage && scope.row.IsHavePermission" type="primary" size="small" @click="inboundEntityApplyProductClick(scope.row)" v-prevent-click>入 库</el-button>

          <!-- 确认收款 -->
          <el-button v-if="scope.row.BillStatus == '15' && isPaymentConfirm && scope.row.IsHavePermission" type="primary" size="small" @click="confirmPaymentClick(scope.row)" v-prevent-click>确认付款</el-button>
          <el-button v-if="scope.row.BillStatus != '20' || scope.row.BillStatus != '30' || scope.row.BillStatus != '50'" type="primary" size="small" @click="checkEntityApplyProductDetail(scope.row)" v-prevent-click>详 情 </el-button>
        </template>
      </el-table-column>
    </el-table>

    <div class="pad_15 text_right">
      <el-pagination background v-if="paginations.total > 0" @current-change="EntityApplyProductListHandleCurrentChange" :current-page.sync="paginations.page" :page-size="paginations.page_size" :layout="paginations.layout" :total="paginations.total"></el-pagination>
    </div>

    <!-- 新建门店要货  编辑申请 -->
    <el-dialog custom-class="entityApplyProductDialogClass" :title="isAddOrEdit ? '新建申请' : '编辑申请'" :visible.sync="dialogVisible" width="1200px" @close="closeAddApplyProduct" :close-on-click-modal="false">
      <div class="tip marbm_10" style="margin-top: 0">要货信息</div>
      <el-form v-if="dialogVisible" class="entityApplyProductInfoFrom" :inline="true" :inline-message="true" label-width="auto" size="small" :model="entityApplyProduct" :rules="entityApplyProductRules" ref="entityApplyProductRef">
        <el-row>
          <el-col :span="12">
            <el-form-item label="申请仓库/门店：" prop="EntityID">
              <el-select size="small" value-key="ID" v-model="entityApplyProduct.EntityName" filterable placeholder="请选择仓库/门店" @change="handleSelectProductEntity" class="zl-custom-select-width">
                <el-option value-key="ID" v-for="item in purchaseStorage" :key="item.ID" :label="item.EntityName" :value="item"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="备注信息：">
              <el-input type="textarea" style="width: 300px" :autosize="{ minRows: 1, maxRows: 3 }" v-model="entityApplyProduct.Remark" placeholder="请输入备注信息" size="small"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <!-- 余额 -->
        <el-row v-if="entityApplyProduct.TotalBalance">
          <el-col :span="12">
            <el-form-item label="账户余额：">¥ {{ entityApplyProduct.Balance | toFixed | NumFormat }}</el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="赠送余额：">¥ {{ entityApplyProduct.LargessBalance | toFixed | NumFormat }}</el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="entityApplyProduct.EntityID">
          <el-col :span="24">
            <el-form-item label="门店地址：">{{ entityApplyProduct.AddressDetail }}</el-form-item>
          </el-col>
          <el-col :span="24" v-if="BillStatus == '40'">
            <el-form-item label="驳回原因：">{{ entityApplyProduct.RejectReason }}</el-form-item>
          </el-col>
        </el-row>

        <div class="tip marbm_10 martp_10">产品明细</div>
        <el-row>
          <el-col :span="8">
            <el-button type="primary" size="small" @click="addProducts">添加产品</el-button>
            <el-button type="danger" size="small" @click="removeMultipleProduct" :disabled="removeDisabled">删除产品 </el-button>
            <el-button type="primary" size="small" @click="addProductsImportProduct">批量导入</el-button>
          </el-col>
        </el-row>

        <!-- max-height="400px" -->
        <el-table empty-text="暂无产品" size="small" class="martp_15" :data="entityApplyProduct.Product" @selection-change="handleChangeSelectProduct" show-summary :summary-method="entityApplyProductDetailedSummary">
          <el-table-column type="selection" width="55"></el-table-column>
          <el-table-column type="index" width="50" label="序号"></el-table-column>
          <el-table-column label="产品名称" prop="ProductName" width="200px">
            <template slot-scope="scope">
              {{ scope.row.ProductName }}
              <el-button icon="el-icon-edit-outline" type="text" style="padding: unset; font-size: 15px" @click="editRemarkClick(scope.row, scope.$index, 'apply')"></el-button>
              <div v-if="scope.row.Remark" class="font_12 color_999" style="line-height: 100%; margin-top: 5px">备注： {{ scope.row.Remark }}</div>
            </template>
          </el-table-column>
          <el-table-column prop="Specification" label="产品规格">
            <template slot-scope="scope">{{ scope.row.Specification }}</template>
          </el-table-column>
          <el-table-column prop="Quantity" label="可用库存">
            <template slot-scope="scope">
              {{ scope.row.Quantity }}
              <span class="marlt_5">{{ scope.row.miniUnitName }}</span>
            </template>
          </el-table-column>
          <el-table-column label="要货单位">
            <template slot-scope="scope">
              <el-form-item :show-message="false" :prop="'Product.' + scope.$index + '.CurrentUnit'" :rules="entityApplyProductRules.CurrentUnit">
                <el-select value-key="UnitID" v-model="scope.row.CurrentUnit" size="small" filterable placeholder="请选择单位" @change="(val) => handleSelectProductUnit(val, scope.row)">
                  <el-option v-for="unitItem in scope.row.Unit" :key="unitItem.UnitID" :label="unitItem.UnitName" :value="unitItem"></el-option>
                </el-select>
              </el-form-item>
            </template>
          </el-table-column>
          <el-table-column label="要货单位数量">
            <template slot-scope="scope">
              <el-form-item v-if="entityApplyProduct.Product.length > 0" :show-message="false" :prop="'Product.' + scope.$index + '.ApplyQuantity'" :rules="entityApplyProductRules.ApplyQuantity">
                <el-input v-model="scope.row.ApplyQuantity" size="small" placeholder="要货数量" @input="changeApplyQuantity(scope.row)" min="0" type="number"></el-input>
              </el-form-item>
            </template>
          </el-table-column>
          <el-table-column label="最小包装单位数量" prop="ApplyMiniUnitQuantity">
            <template slot-scope="scope">
              {{ scope.row.ApplyMiniUnitQuantity }}
              <span v-if="scope.row.ApplyMiniUnitQuantity">{{ scope.row.MiniUnit.UnitName }}</span>
            </template>
          </el-table-column>

          <el-table-column prop="DeliveryPrice" label="配销价格（元）">
            <template slot-scope="scope">{{ scope.row.DeliveryPrice | toFixed | NumFormat }}</template>
          </el-table-column>
          <el-table-column prop="DeliveryTotalPrice" label="配销价小计（元）">
            <template slot-scope="scope">{{ scope.row.DeliveryTotalPrice | toFixed | NumFormat }}</template>
          </el-table-column>
        </el-table>
      </el-form>

      <div slot="footer">
        <el-button @click="dialogVisible = false" size="small" v-prevent-click>取 消</el-button>
        <el-button type="primary" @click="saveDraftClick" :loading="saveDraftLoading" size="small" v-prevent-click>保存草稿 </el-button>
        <el-button type="primary" @click="saveEntityApplyProductClick" :loading="modalLoading" size="small" v-prevent-click>提交申请</el-button>
      </div>
    </el-dialog>

    <!-- 门店要详情  -->
    <el-dialog custom-class="entityApplyProductDialogClass" title="要货详情" :visible.sync="applyDetaildialogVisible" width="1100px">
      <div v-if="applyDetailInfo" class="tip">
        基本信息 -
        <span class="font_weight_600">{{ ApplyOrderBillStatusFormatter(applyDetailInfo) }}（{{ applyDetailInfo.ID }}）</span>
      </div>
      <el-form v-if="applyDetailInfo" class="entityApplyProductInfoFrom" label-width="110px" size="small" :model="applyDetailInfo">
        <el-row>
          <el-col :span="8">
            <el-form-item label="申请仓库/门店：">{{ applyDetailInfo.InboundEntityName }} </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="申请人：">{{ applyDetailInfo.CreatedByName }}</el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="申请时间：">{{ applyDetailInfo.CreatedOn | dateFormat("YYYY-MM-DD HH:mm") }}</el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="applyDetailInfo.BillStatus == '15' || applyDetailInfo.BillStatus == '20' || applyDetailInfo.BillStatus == '30' || applyDetailInfo.BillStatus == '40' || applyDetailInfo.BillStatus == '50'">
          <el-col :span="24">
            <el-form-item label="要货地址：">{{ applyDetailInfo.AddressDetail }}</el-form-item>
          </el-col>
        </el-row>
        <!-- 审批信息 -->
        <template v-if="applyDetailInfo.ApprovedByName">
          <el-row>
            <el-col :span="8" v-if="applyDetailInfo.OutboundEntityID">
              <el-form-item label="发货仓库：">
                {{ applyDetailInfo.OutboundEntityName }}
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="审批人：">
                {{ applyDetailInfo.ApprovedByName }}
              </el-form-item>
            </el-col>

            <el-col :span="8">
              <el-form-item label="审批时间：">
                {{ applyDetailInfo.ApprovedOn | dateFormat("YYYY-MM-DD HH:mm") }}
              </el-form-item>
            </el-col>

            <el-col :span="24">
              <el-form-item label="审批备注：">
                {{ applyDetailInfo.ApprovalRemark }}
              </el-form-item>
            </el-col>

            <el-col :span="24" v-if="applyDetailInfo.BillStatus == '40'">
              <el-form-item label="驳回原因：">
                {{ applyDetailInfo.RejectReason }}
              </el-form-item>
            </el-col>
          </el-row>
        </template>
        <el-row>
          <el-col v-if="applyDetailInfo.BillStatus == '60' || applyDetailInfo.BillStatus == '10'" :span="24">
            <el-form-item label="要货地址：">{{ applyDetailInfo.AddressDetail }}</el-form-item>
          </el-col>

          <el-col :span="24">
            <el-form-item label="备注信息：">
              <div>{{ applyDetailInfo.Remark }}</div>
            </el-form-item>
          </el-col>
          <el-col v-if="applyDetailInfo.OutboundBillID && isViewDeliveryBill" :span="8">
            <el-form-item label="配送出库单号：">
              {{ applyDetailInfo.OutboundBillID }}
              <el-button type="text" @click="checkOutboundBillInfo">查看</el-button>
            </el-form-item>
          </el-col>
          <el-col :span="8" v-if="applyDetailInfo.InboundBillID && isViewStorageyBill">
            <el-form-item label="要货入库单号：">
              {{ applyDetailInfo.InboundBillID }}
              <el-button type="text" @click="checkInbounBillInfo">查看</el-button>
            </el-form-item>
          </el-col>
        </el-row>

        <div class="tip">产品明细</div>
        <el-table size="small" :data="applyDetailInfo.Detail" :show-summary="applyDetailInfo.BillStatus != '40' && applyDetailInfo.BillStatus != '60'" :summary-method="approvalDetailsummary">
          <el-table-column type="index" width="50" label="序号"></el-table-column>
          <el-table-column label="产品" prop="ProductName">
            <template slot-scope="scope">
              <div>
                {{ scope.row.ProductName }}<span v-if="scope.row.Alias" class="color_gray font_12"> ({{ scope.row.Alias }})</span>
              </div>
              <div v-if="scope.row.Specification" class="color_gray font_12">规格：{{ scope.row.Specification }}</div>
              <div v-if="scope.row.Remark" class="color_gray font_12">备注：{{ scope.row.Remark }}</div>
            </template>
          </el-table-column>

          <el-table-column prop="ApplyQuantity" label="要货数量">
            <template slot-scope="scope">
              <div>{{ scope.row.ApplyQuantity }} {{ scope.row.UnitName }}</div>
              <div class="color_gray font_12">
                最小包装数量：{{ scope.row.ApplyMinimumUnitQuantity }}
                {{ scope.row.MinimumUnitName }}
              </div>
            </template>
          </el-table-column>

          <el-table-column v-if="applyDetailInfo.BillStatus == '20' || applyDetailInfo.BillStatus == '50' || applyDetailInfo.BillStatus == '15'" prop="OutboundQuantity" label="预配送数量">
            <template slot-scope="scope">
              <div>{{ scope.row.ApproveQuantity }}{{ scope.row.UnitName }}</div>
              <div class="color_gray font_12">
                最小包装数量：{{ scope.row.ApproveMinimumUnitQuantity }}
                {{ scope.row.MinimumUnitName }}
              </div>
            </template>
          </el-table-column>

          <el-table-column v-if="applyDetailInfo.BillStatus == '30' || applyDetailInfo.BillStatus == '50'" prop="OutboundQuantity" label="实发数量">
            <template slot-scope="scope">
              <div>{{ scope.row.OutboundQuantity }}{{ scope.row.UnitName }}</div>
              <div class="color_gray font_12">
                最小包装数量：{{ scope.row.OutboundMinimumUnitQuantity }}
                {{ scope.row.MinimumUnitName }}
              </div>
            </template>
          </el-table-column>

          <el-table-column prop="InboundQuantity" v-if="applyDetailInfo.BillStatus == '50'" label="实收数量">
            <template slot-scope="scope">
              <div>{{ scope.row.InboundQuantity }} {{ scope.row.UnitName }}</div>
              <div class="color_gray font_12">
                最小包装数量：{{ scope.row.InboundMinimumUnitQuantity }}
                {{ scope.row.MinimumUnitName }}
              </div>
            </template>
          </el-table-column>

          <el-table-column prop="ApprovedPrice" label="单价" v-if="applyDetailInfo.BillStatus == '20' || applyDetailInfo.BillStatus == '30' || applyDetailInfo.BillStatus == '50'">
            <template slot="header" slot-scope="scope">
              {{ formatApprovedPriceTitle(scope.row, true) }}
            </template>
            <template slot-scope="scope">
              {{ scope.row.ApprovedPrice | toFixed | NumFormat }}
            </template>
          </el-table-column>

          <el-table-column prop="TotalAmount" :formatter="formatterTotalAmount" label="小计(元)" v-if="applyDetailInfo.BillStatus == '20' || applyDetailInfo.BillStatus == '30' || applyDetailInfo.BillStatus == '50'">
            <template slot-scope="scope">
              {{ formatterTotalAmount(scope.row) | toFixed | NumFormat }}
            </template>
          </el-table-column>

          <el-table-column v-if="applyDetailInfo.BillStatus == '15' || applyDetailInfo.BillStatus == '10'" prop="ApprovedPrice" label="配销价格(元)">
            <template slot-scope="scope">
              {{ scope.row.ApprovedPrice | toFixed | NumFormat }}
            </template>
          </el-table-column>

          <el-table-column v-if="applyDetailInfo.BillStatus == '15' || applyDetailInfo.BillStatus == '10'" prop="ApprovedTotalAmount" label="配销价格小计(元)">
            <template slot-scope="scope">
              {{ scope.row.ApprovedTotalAmount | toFixed | NumFormat }}
            </template>
          </el-table-column>
        </el-table>
      </el-form>
      <div slot="footer">
        <el-button v-show="templateTypeList && templateTypeList.length == 0" type="primary" @click="printInfoTips" size="small" v-prevent-click>打印</el-button>
        <el-button v-show="templateTypeList && templateTypeList.length == 1" type="primary" v-print="'printContent'" @click="printInfo" size="small" v-prevent-click>打印</el-button>
        <el-button v-show="templateTypeList && templateTypeList.length > 1" type="primary" @click="printInfoSelectTemplate" size="small" v-prevent-click>打印</el-button>
      </div>
    </el-dialog>

    <!-- 门店要货审批  -->
    <el-dialog custom-class="entityApplyProductDialogClass" title="要货审批" :visible.sync="approvalDetaildialogVisible" width="1100px" :close-on-click-modal="false">
      <div class="tip" style="margin-top: 0">基本信息</div>
      <!-- :inline="true"   -->
      <el-form v-if="applyDetailInfo" class="entityApplyProductInfoFrom" label-width="110px" size="small" :model="applyDetailInfo" :rules="approveEntityRules" ref="approvalDetailRef">
        <el-row>
          <el-col :span="8">
            <el-form-item label="申请仓库/门店：" prop="OutboundEntityName">
              {{ applyDetailInfo.InboundEntityName }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="要货状态：" prop="BillStatus">
              {{ ApplyOrderBillStatusFormatter(applyDetailInfo) }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="申请人："> {{ applyDetailInfo.CreatedByName }}</el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="申请日期：">
              {{ applyDetailInfo.CreatedOn | dateFormat("YYYY-MM-DD HH:mm") }}
            </el-form-item>
          </el-col>

          <el-col :span="24">
            <el-form-item label="要货地址：">{{ applyDetailInfo.AddressDetail }}</el-form-item>
          </el-col>

          <el-col :span="24">
            <el-form-item label="备注信息：">
              <div>{{ applyDetailInfo.Remark }}</div>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="账户余额：">
              {{ applyDetailInfo.Balance }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="赠送余额：">{{ applyDetailInfo.LargessBalance }}</el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="审批备注：">
              <el-input type="textarea" :rows="1" placeholder="请输入审批备注" v-model="ApprovalRemark"> </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="发货仓库：" prop="EntityID" :inline-message="true">
              <el-select size="small" value-key="ID" v-model="applyDetailInfo.EntityItem" filterable placeholder="请选择发货仓库" @change="deliveryHandleSelectProductEntity">
                <el-option value-key="ID" v-for="item in OutboundEntitys" :key="item.ID" :label="item.EntityName" :value="item"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="结算方式:" prop="SettlementWay" :inline-message="true">
              <el-select size="small" value-key="ID" v-model="applyDetailInfo.SettlementWay" placeholder="请选择结算方式">
                <el-option label="先货后款" value="10"></el-option>
                <el-option label="先款后货" value="20"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row v-if="applyDetailInfo.EntityID">
          <el-col :span="24">
            <el-form-item label="发货地址：">{{ applyDetailInfo.InboundAddressDetail }}</el-form-item>
          </el-col>
        </el-row>

        <div class="tip martp_10">产品明细</div>

        <el-table size="small" class="martp_15" :data="applyDetailInfo.Detail" show-summary :summary-method="approvalsummary">
          <el-table-column type="index" width="50"></el-table-column>
          <el-table-column label="产品名称" prop="ProductName">
            <template slot-scope="scope">
              <div>
                {{ scope.row.ProductName }}<span v-if="scope.row.Alias" class="color_gray font_12"> ({{ scope.row.Alias }})</span>

                <el-button icon="el-icon-edit-outline" type="text" style="padding: unset; font-size: 15px" @click="editRemarkClick(scope.row, scope.$index, 'approve')"></el-button>
              </div>
              <div v-if="scope.row.Specification" class="color_gray font_12">规格：{{ scope.row.Specification }}</div>
              <div v-if="scope.row.Remark" class="color_gray font_12">备注: {{ scope.row.Remark }}</div>
            </template>
          </el-table-column>
          <el-table-column prop="StockQuantity" label="实物库存">
            <template slot-scope="scope">{{ scope.row.StockQuantity }} {{ scope.row.MinimumUnitName }}</template>
          </el-table-column>
          <el-table-column prop="ApplyQuantity" label="要货数量">
            <template slot-scope="scope">
              <div>{{ scope.row.ApplyQuantity }} {{ scope.row.UnitName }}</div>
              <div class="color_gray font_12">
                最小包装数量：{{ scope.row.ApplyMinimumUnitQuantity }}
                {{ scope.row.MinimumUnitName }}
              </div>
            </template>
          </el-table-column>

          <el-table-column prop="ApproveQuantity" label="预配数量">
            <template slot="header">
              预配数量
              <el-popover placement="top-start" width="200" trigger="hover">
                <p>预计可以给仓库/门店配送的数量，仓库实际发货以此为依据进行发货</p>
                <el-button type="text" style="color: #dcdfe6; padding: 0px" icon="el-icon-info" slot="reference"> </el-button>
              </el-popover>
            </template>
            <template slot-scope="scope">
              <el-form-item label-width="0px" :show-message="false" :prop="'Detail.' + scope.$index + '.ApproveQuantity'" :rules="approveEntityRules.ApproveQuantity">
                <el-input v-if="applyDetailInfo.EntityID" v-model="scope.row.ApproveQuantity" class="input_type" size="small" @input="changeApprovalQuantity(scope.row)" validate-event v-enter-number2 v-enterInt min="0" type="number">
                  <template slot="append">{{ scope.row.UnitName }}</template>
                </el-input>
              </el-form-item>
            </template>
          </el-table-column>

          <el-table-column v-if="applyDetailInfo.EntityID" label="预配最小包装数量">
            <template slot-scope="scope">{{ scope.row.MinimumUnitAmount * scope.row.ApproveQuantity }} {{ scope.row.MinimumUnitName }}</template>
          </el-table-column>
          <el-table-column prop="ApprovedPrice" label="配送单价(元)">
            <template slot-scope="scope">
              <el-form-item label-width="0px" :show-message="false" :prop="'Detail.' + scope.$index + '.ApprovedPrice'" :rules="approveEntityRules.ApprovedPrice">
                <el-input v-if="applyDetailInfo.EntityID" v-model="scope.row.ApprovedPrice" class="input_type" size="small" @input="changeApprovalPrice(scope.row)" validate-event v-input-fixed="2" min="0" type="number">
                  <template slot="append">元</template>
                </el-input>
              </el-form-item>
            </template>
          </el-table-column>
          <el-table-column prop="ApprovedTotalAmount" label="小计(元)">
            <template slot-scope="scope">
              {{ scope.row.ApprovedTotalAmount | toFixed | NumFormat }}
            </template>
          </el-table-column>
        </el-table>
      </el-form>

      <div slot="footer">
        <el-button @click="approvalDetaildialogVisible = false" size="small" v-prevent-click>取 消</el-button>
        <el-button type="danger" @click="approvalEntityApplyProductClick(false)" size="small" v-prevent-click plain>审核驳回 </el-button>
        <el-button type="primary" @click="approvalEntityApplyProductClick(true)" :loading="approvedPaasLoading" size="small" v-prevent-click>审核通过</el-button>
      </div>
    </el-dialog>

    <!-- 确认付款  -->
    <el-dialog custom-class="entityApplyProductDialogClass" title="确认付款" :visible.sync="confirmPaymentDialogVisible" width="1100px" :close-on-click-modal="false">
      <div class="tip" style="margin-top: 0">基本信息</div>
      <!-- size="small" -->
      <el-form v-if="applyDetailInfo" size="small" class="PaymententityApplyProductInfoFrom" label-width="110px" :model="applyDetailInfo" :rules="approveEntityRules" ref="PaymentWayapprovalDetailRef">
        <el-row>
          <el-col :span="8">
            <el-form-item label="申请仓库/门店：" prop="OutboundEntityName">
              {{ applyDetailInfo.InboundEntityName }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="要货状态：" prop="BillStatus">
              {{ ApplyOrderBillStatusFormatter(applyDetailInfo) }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="申请人："> {{ applyDetailInfo.CreatedByName }}</el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="8">
            <el-form-item label="申请日期：">
              {{ applyDetailInfo.CreatedOn | dateFormat("YYYY-MM-DD HH:mm") }}
            </el-form-item>
          </el-col>

          <el-col :span="24">
            <el-form-item label="要货地址：">
              {{ applyDetailInfo.AddressDetail }}
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="审批备注：">{{ applyDetailInfo.ApprovalRemark }}</el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="备注信息：">
              <div>{{ applyDetailInfo.Remark }}</div>
            </el-form-item>
          </el-col>

          <el-col :span="8">
            <el-form-item label="发货仓库：">{{ applyDetailInfo.OutboundEntityName }}</el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="结算方式：">{{ settlementWayTitleFormat(applyDetailInfo.SettlementWay) }}</el-form-item>
          </el-col>

          <el-col :span="8">
            <el-form-item label="付款方式：" prop="PaymentWay">
              <el-select size="small" v-model="applyDetailInfo.PaymentWay" @change="changePaymentWayClick" placeholder="请选择">
                <el-option label="离线转账" value="10"></el-option>
                <el-option label="余额支付" value="20"></el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="8">
            <el-form-item label="剩余本金：">{{ applyDetailInfo.Balance | toFixed | NumFormat }}</el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="剩余赠金：">{{ applyDetailInfo.LargessBalance | toFixed | NumFormat }}</el-form-item>
          </el-col>

          <el-col :span="8" v-show="applyDetailInfo.PaymentWay == 20">
            <el-form-item label="支付本金：" prop="Balance_payment">
              <el-input v-model="applyDetailInfo.Balance_payment" @input="changPaymentBalance" placeholder="请输入付款本金金额" type="number" class="custom_input" size="small"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8" v-show="applyDetailInfo.PaymentWay == 20">
            <el-form-item label="支付赠金：" prop="LargessBalance_payment">
              <el-input v-model="applyDetailInfo.LargessBalance_payment" @input="changePaymentLargessBalance" placeholder="请输入付款赠金金额" type="number" class="custom_input" size="small"></el-input>
            </el-form-item>
          </el-col>

          <el-col :span="8" v-show="applyDetailInfo.PaymentWay == 10">
            <el-form-item label="回单号码：" prop="ReceiptNumber">
              <el-input v-model="applyDetailInfo.ReceiptNumber" placeholder="请输入回单号码" size="small"></el-input>
            </el-form-item>
          </el-col>

          <el-col :span="8" v-show="applyDetailInfo.PaymentWay == 10">
            <el-form-item label="付款户名：" prop="PaymentAccountName">
              <el-input v-model="applyDetailInfo.PaymentAccountName" placeholder="请输入付款户名" size="small"></el-input>
            </el-form-item>
          </el-col>
          <!-- v-show="applyDetailInfo.PaymentWay == 10" -->
          <el-col :span="8">
            <el-form-item label="付款备注：" prop="Remark">
              <el-input v-model="applyDetailInfo.RemarkPayment" placeholder="请输入付款备注信息" size="small"></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <div class="tip martp_10">产品明细</div>

        <el-table size="small" :data="applyDetailInfo.Detail" :show-summary="true" :summary-method="getSummaries">
          <el-table-column type="index" width="50"></el-table-column>
          <el-table-column label="产品名称" prop="ProductName">
            <template slot-scope="scope">
              <div>
                {{ scope.row.ProductName }}<span v-if="scope.row.Alias" class="color_gray font_12"> ({{ scope.row.Alias }})</span>
              </div>
              <div v-if="scope.row.Specification" class="color_gray font_12">规格：{{ scope.row.Specification }}</div>
              <div v-if="scope.row.Remark" class="color_gray font_12">备注{{ scope.row.Remark }}</div>
            </template>
          </el-table-column>
          <el-table-column prop="StockQuantity" label="实物库存">
            <template slot-scope="scope">{{ scope.row.StockQuantity }} {{ scope.row.MinimumUnitName }}</template>
          </el-table-column>
          <el-table-column prop="ApplyQuantity" label="要货数量">
            <template slot-scope="scope">
              <div>{{ scope.row.ApplyQuantity }} {{ scope.row.UnitName }}</div>
              <div class="color_gray font_12">
                最小包装数量：{{ scope.row.ApplyMinimumUnitQuantity }}
                {{ scope.row.MinimumUnitName }}
              </div>
            </template>
          </el-table-column>

          <el-table-column prop="ApproveQuantity" label="预配数量">
            <template slot="header">
              预配数量
              <el-popover placement="top-start" width="200" trigger="hover">
                <p>预计可以给仓库/门店配送的数量，仓库实际发货以此为依据进行发货</p>
                <el-button type="text" style="color: #dcdfe6; padding: 0px" icon="el-icon-info" slot="reference"> </el-button>
              </el-popover>
            </template>
          </el-table-column>
          <el-table-column prop="ApprovedPrice" label="配送单价(元)"></el-table-column>
          <el-table-column prop="ApprovedTotalAmount" label="小计(元)">
            <template slot-scope="scope">
              {{ scope.row.ApprovedTotalAmount | toFixed | NumFormat }}
            </template>
          </el-table-column>
          <el-table-column v-if="applyDetailInfo.EntityID" label="预配最小包装数量">
            <template slot-scope="scope">{{ scope.row.MinimumUnitAmount * scope.row.ApproveQuantity }} {{ scope.row.MinimumUnitName }}</template>
          </el-table-column>
        </el-table>
      </el-form>

      <div slot="footer">
        <el-button @click="confirmPaymentDialogVisible = false" size="small" v-prevent-click>取 消</el-button>
        <el-button type="danger" size="small" @click="rejectClick" v-prevent-click plain>驳 回</el-button>
        <el-button type="primary" @click="saveConfirmPaymentClick(true)" :loading="approvedPaasLoading" size="small" v-prevent-click>确认付款</el-button>
      </div>
    </el-dialog>

    <!-- 驳回审核 -->
    <el-dialog width="600px" :visible.sync="finalRejectionDialogVisible" :title="BillStatus == '15' ? '驳回原因' : '审核驳回'" @close="rejectClose">
      <el-input type="textarea" :rows="4" v-model="finalRejection" placeholder="请输入备注内容"></el-input>
      <span slot="footer" class="dialog-footer">
        <el-button @click="finalRejectionDialogVisible = false" v-prevent-click size="small">取消</el-button>
        <el-button type="danger" @click="finalRejectApprovedClick" :loading="approvedRefuseLoading" v-prevent-click size="small">{{ BillStatus == "15" ? "驳 回" : "驳回审核" }}</el-button>
      </span>
    </el-dialog>

    <!-- 门店要货 配送  -->
    <el-dialog custom-class="entityApplyProductDialogClass" title="配送出库" :visible.sync="outboundDetaildialogVisible" width="1100px" :close-on-click-modal="false">
      <div class="tip">基本信息</div>
      <el-form v-if="applyDetailInfo" :inline="true" :inline-message="true" class="entityApplyProductInfoFrom" label-width="110px" size="small" :model="applyDetailInfo" :rules="approveEntityRules" ref="outboundDetailRef">
        <el-row>
          <el-col :span="8">
            <el-form-item label="申请仓库/门店：" prop="InboundEntityName">
              {{ applyDetailInfo.InboundEntityName }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="申请人："> {{ applyDetailInfo.CreatedByName }}</el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="申请日期：">
              {{ applyDetailInfo.CreatedOn | dateFormat("YYYY-MM-DD HH:mm") }}
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="8">
            <el-form-item label="发货仓库：" prop="OutboundEntityName">
              {{ applyDetailInfo.OutboundEntityName }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="审批人：" prop="ApprovedByName">
              {{ applyDetailInfo.ApprovedByName }}
            </el-form-item>
          </el-col>

          <el-col :span="8">
            <el-form-item label="审批日期：" prop="InDate">
              {{ applyDetailInfo.ApprovedOn | dateFormat("YYYY-MM-DD HH:mm") }}
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="配送信息：" prop="outRemark" class="padbm_10 padtp_5">
              <el-input type="textarea" style="width: 280px" :autosize="{ minRows: 1, maxRows: 3 }" v-model="applyDetailInfo.outRemark" placeholder="请输入配送信息" size="small"></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <div class="tip">产品明细</div>
        <el-table size="small" :data="applyDetailInfo.Detail">
          <el-table-column type="index" width="50"></el-table-column>
          <el-table-column label="产品名称" prop="ProductName">
            <template slot-scope="scope">
              <div>
                {{ scope.row.ProductName }}<span v-if="scope.row.Alias" class="color_gray font_12"> ({{ scope.row.Alias }})</span>
              </div>
              <div v-if="scope.row.Specification" class="color_gray font_12">规格：{{ scope.row.Specification }}</div>
              <div v-if="scope.row.Remark" class="color_gray font_12">备注{{ scope.row.Remark }}</div>
            </template>
          </el-table-column>
          <el-table-column prop="StockQuantity" label="可用库存">
            <template slot-scope="scope">
              {{ scope.row.StockQuantity }}
              <span>{{ scope.row.MinimumUnitName }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="ApplyQuantity" label="要货数量">
            <template slot-scope="scope">
              <div>{{ scope.row.ApplyQuantity }} {{ scope.row.UnitName }}</div>
              <div class="color_gray font_12">
                最小包装数量：{{ scope.row.ApplyMinimumUnitQuantity }}
                {{ scope.row.MinimumUnitName }}
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="ApproveQuantity" label="预配数量">
            <template slot="header">
              预配数量
              <el-popover placement="top-start" width="200" trigger="hover">
                <p>预计可以给仓库/门店配送的数量，仓库实际发货以此为依据进行发货</p>
                <el-button type="text" style="color: #dcdfe6; padding: 0px" icon="el-icon-info" slot="reference"> </el-button>
              </el-popover>
            </template>
            <template slot-scope="scope">
              <div>{{ scope.row.ApproveQuantity }} {{ scope.row.UnitName }}</div>
              <div class="color_gray font_12">
                最小包装数量：{{ scope.row.ApproveMinimumUnitQuantity }}
                {{ scope.row.MinimumUnitName }}
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="OutboundQuantity" label="实发数量">
            <template slot-scope="scope">
              <el-form-item :show-message="false" :prop="'Detail.' + scope.$index + '.OutboundQuantity'" :rules="approveEntityRules.OutboundQuantity">
                <el-input v-model="scope.row.OutboundQuantity" size="small" class="input_type" placeholder="请输入实发数量" @input="changeOutboundQuantity(scope.row)" validate-event v-enter-number2 v-enterInt min="0" type="number" :disabled="scope.row.OutboundIsLock">
                  <template slot="append">{{ scope.row.UnitName }}</template>
                </el-input>
              </el-form-item>
            </template>
          </el-table-column>
          <el-table-column label="实发最小包装数量">
            <template slot-scope="scope">
              <div v-if="!scope.row.OutboundIsLock">
                {{ parseFloat(scope.row.MinimumUnitAmount || 0) * parseFloat(scope.row.OutboundQuantity || 0) }}
                {{ scope.row.MinimumUnitName }}
              </div>
              <el-tag v-else size="mini" type="warning">{{ "盘点锁定" }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="stockoutQuantity" label="缺货数量">
            <template slot="header">
              缺货数量
              <el-popover placement="top-start" width="200" trigger="hover">
                <p>缺货数量 = 预配数量 - 实发数量，结果最小为0</p>
                <el-button type="text" style="color: #dcdfe6; padding: 0px" icon="el-icon-info" slot="reference"> </el-button>
              </el-popover>
            </template>
            <template slot-scope="scope">
              <div>{{ scope.row.stockoutQuantity || 0 }} {{ scope.row.UnitName }}</div>
              <div class="color_gray font_12">
                最小包装数量：{{ parseFloat(scope.row.stockoutQuantity || 0) * parseFloat(scope.row.MinimumUnitAmount || 0) }}
                {{ scope.row.MinimumUnitName }}
              </div>
            </template>
          </el-table-column>
        </el-table>
      </el-form>
      <div slot="footer">
        <el-button @click="outboundDetaildialogVisible = false" size="small" v-prevent-click>取 消</el-button>
        <el-button type="primary" @click="saveOutboundEntityApplyProductClick" :loading="outboundLoading" size="small" v-prevent-click>配送出库</el-button>
      </div>
    </el-dialog>

    <!-- 门店要货 入库  -->
    <el-dialog custom-class="entityApplyProductDialogClass" title="要货入库" :visible.sync="inboundDetaildialogVisible" width="1100px" :close-on-click-modal="false">
      <div class="tip">基本信息</div>
      <el-form v-if="applyDetailInfo" :inline="true" :inline-message="true" class="entityApplyProductInfoFrom" label-width="110px" size="small" :model="applyDetailInfo" :rules="approveEntityRules" ref="InboundDetailRef">
        <!-- 申请信息 -->
        <el-row>
          <el-col :span="8">
            <el-form-item label="申请仓库/门店：" prop="InboundEntityName">
              {{ applyDetailInfo.InboundEntityName }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="申请人："> {{ applyDetailInfo.CreatedByName }}</el-form-item>
          </el-col>

          <el-col :span="8">
            <el-form-item label="入库备注信息：">
              <el-input type="textarea" :autosize="{ minRows: 1, maxRows: 3 }" v-model="applyDetailInfo.inRemark" placeholder="请输入备注信息" size="small"></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <div class="tip martp_10">产品明细</div>
        <el-table class="martp_10" size="small" :data="applyDetailInfo.Detail" show-summary :summary-method="applyDetailsummary">
          <el-table-column type="index" width="50"></el-table-column>
          <el-table-column label="产品名称" prop="ProductName">
            <template slot-scope="scope">
              <div>
                {{ scope.row.ProductName }}<span v-if="scope.row.Alias" class="color_gray font_12"> ({{ scope.row.Alias }})</span>
              </div>
              <div v-if="scope.row.Specification" class="color_gray font_12">规格：{{ scope.row.Specification }}</div>
              <div v-if="scope.row.Remark" class="color_gray font_12">备注{{ scope.row.Remark }}</div>
            </template>
          </el-table-column>
          <el-table-column prop="ApplyQuantity" label="要货数量">
            <template slot-scope="scope">
              <div>{{ scope.row.ApplyQuantity }} {{ scope.row.UnitName }}</div>
              <div class="color_gray font_12">
                最小包装数量：{{ scope.row.ApplyMinimumUnitQuantity }}
                {{ scope.row.MinimumUnitName }}
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="OutboundQuantity" label="实发数量">
            <template slot-scope="scope">
              <div>{{ scope.row.OutboundQuantity }} {{ scope.row.UnitName }}</div>
              <div class="color_gray font_12">
                最小包装数量：{{ scope.row.OutboundQuantity * scope.row.MinimumUnitAmount }}
                {{ scope.row.MinimumUnitName }}
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="InboundQuantity" label="实收数量">
            <template slot-scope="scope">
              <el-form-item :show-message="false" :prop="'Detail.' + scope.$index + '.InboundQuantity'" :rules="approveEntityRules.InboundQuantity">
                <el-input v-model="scope.row.InboundQuantity" class="input_type" size="small" placeholder="请输入入库数量" @input="changeInboundQuantity(scope.row)" validate-event v-enter-number2 v-enterInt min="0" type="number" :disabled="scope.row.InboundIsLock">
                  <template slot="append">{{ scope.row.UnitName }}</template>
                </el-input>
              </el-form-item>
            </template>
          </el-table-column>

          <el-table-column prop="stockoutQuantity" label="实收最小包装数量">
            <template slot-scope="scope">
              <div v-if="!scope.row.InboundIsLock">
                {{ parseFloat(scope.row.MinimumUnitAmount || 0) * parseFloat(scope.row.InboundQuantity || 0) }}
                {{ scope.row.MinimumUnitName }}
              </div>
              <el-tag v-else size="mini" type="warning">{{ "盘点锁定" }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="ApprovedPrice" label="配送单价(元)">
            <template slot-scope="scope">
              {{ scope.row.ApprovedPrice | toFixed | NumFormat }}
            </template>
          </el-table-column>
          <el-table-column label="小计(元)">
            <template slot-scope="scope">
              {{ (scope.row.ApprovedPrice * scope.row.InboundQuantity) | toFixed | NumFormat }}
            </template>
          </el-table-column>
        </el-table>
      </el-form>

      <div slot="footer">
        <el-button @click="inboundDetaildialogVisible = false" size="small" v-prevent-click>取 消</el-button>
        <el-button type="primary" @click="saveInboundEntityApplyProductClick" :loading="outboundLoading" size="small" v-prevent-click>确认入库</el-button>
      </div>
    </el-dialog>

    <!-- 入库 详情 -->
    <el-dialog custom-class="entityApplyProductDialogClass" title="要货入库详情" :visible.sync="InboundInfoDialogVisible" @close="closeInboundInfoDialog" width="1000px">
      <div class="tip">基本信息</div>
      <el-form class="entityApplyProductInfoFrom" :inline="true" :inline-message="true" label-width="100px" size="small" :model="InboundInfo">
        <el-row>
          <el-col :span="8">
            <el-form-item label="入库仓库：">{{ InboundInfo.EntityName }}</el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="操作人：">{{ InboundInfo.CreatedByName }}</el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="入库时间：">{{ InboundInfo.InDate | dateFormat("YYYY-MM-DD HH:mm") }}</el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form-item label="备注信息：">{{ InboundInfo.Remark }}</el-form-item>
          </el-col>
        </el-row>

        <div class="tip">产品明细</div>
        <el-table size="small" :data="InboundInfo.Detail">
          <el-table-column type="index" width="50"></el-table-column>
          <el-table-column label="产品" prop="ProductName">
            <template slot-scope="scope">
              <div>
                {{ scope.row.ProductName }}<span v-if="scope.row.Alias" class="color_gray font_12"> ({{ scope.row.Alias }})</span>
              </div>
              <div v-if="scope.row.Specification" class="color_gray font_12">规格：{{ scope.row.Specification }}</div>
              <div v-if="scope.row.Remark" class="color_gray font_12">备注{{ scope.row.Remark }}</div>
            </template>
          </el-table-column>
          <el-table-column label="入库数量" prop="Quantity">
            <template slot-scope="scope">
              <div>{{ scope.row.Quantity || 0 }} {{ scope.row.UnitName }}</div>
              <div class="color_gray font_12">
                最小包装数量：{{ scope.row.MinimumUnitQuantity || 0 }}
                {{ scope.row.MinimumUnitName }}
              </div>
            </template>
          </el-table-column>
        </el-table>
      </el-form>
      <div slot="footer">
        <el-button v-show="templateTypeList && templateTypeList.length == 0" type="primary" @click="printInfoTips" size="small" v-prevent-click>打印</el-button>
        <el-button v-show="templateTypeList && templateTypeList.length == 1" type="primary" v-print="'printContent'" @click="printInfo" size="small" v-prevent-click>打印</el-button>
        <el-button v-show="templateTypeList && templateTypeList.length > 1" type="primary" @click="printInfoSelectTemplate" size="small" v-prevent-click>打印</el-button>
      </div>
    </el-dialog>

    <!-- 详情  配送出库详情-->
    <el-dialog custom-class="entityApplyProductDialogClass" title="配送出库详情" :visible.sync="OutboundInfoDialogVisible" @close="closeOutboundInfoDialog" width="1000px">
      <div class="tip">基本信息</div>
      <el-form class="entityApplyProductInfoFrom" :inline="true" :inline-message="true" label-width="100px" size="small" :model="OutboundInfo">
        <el-row>
          <el-col :span="8">
            <el-form-item label="出库仓库：">{{ OutboundInfo.EntityName }}</el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="操作人：">{{ OutboundInfo.CreatedByName }}</el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="出库时间：" prop="OutDate">{{ OutboundInfo.OutDate | dateFormat("YYYY-MM-DD HH:mm") }} </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form-item label="配送信息：">{{ OutboundInfo.Remark }} <el-button type="text" @click="outboundInfoRemarkClick">修改</el-button></el-form-item>
          </el-col>
        </el-row>
        <div class="tip">产品明细</div>
        <el-table size="small" :data="OutboundInfo.Detail">
          <el-table-column type="index" width="50"></el-table-column>
          <el-table-column label="产品" prop="ProductName">
            <template slot-scope="scope">
              <div>
                {{ scope.row.ProductName }}<span v-if="scope.row.Alias" class="color_gray font_12"> ({{ scope.row.Alias }})</span>
              </div>
              <div v-if="scope.row.Specification" class="color_gray font_12">规格：{{ scope.row.Specification }}</div>
              <div v-if="scope.row.Remark" class="color_gray font_12">备注{{ scope.row.Remark }}</div>
            </template>
          </el-table-column>
          <el-table-column prop="Quantity" label="出库数量">
            <template slot-scope="scope">
              <div>{{ scope.row.Quantity || 0 }} {{ scope.row.UnitName }}</div>
              <div class="color_gray font_12">
                最小包装数量：{{ scope.row.MinimumUnitQuantity || 0 }}
                {{ scope.row.MinimumUnitName }}
              </div>
            </template>
          </el-table-column>
        </el-table>
      </el-form>
      <div slot="footer">
        <el-button v-show="templateTypeList && templateTypeList.length == 0" type="primary" @click="printInfoTips" size="small" v-prevent-click>打印</el-button>
        <el-button v-show="templateTypeList && templateTypeList.length == 1" type="primary" v-print="'printContent'" @click="printInfo" size="small" v-prevent-click>打印</el-button>
        <el-button v-show="templateTypeList && templateTypeList.length > 1" type="primary" @click="printInfoSelectTemplate" size="small" v-prevent-click>打印</el-button>
      </div>
    </el-dialog>
    <!-- 打印 -->
    <el-dialog title="选择打印模板" :visible.sync="printTemplateVisible" width="400px">
      <el-select size="small" v-model="printTemplateID" @change="changePrintTemplate">
        <el-option v-for="item in templateTypeList" :key="item.ID" :label="item.Name" :value="item.ID"></el-option>
      </el-select>
      <div slot="footer">
        <el-button @click="printTemplateVisible = false" size="small" v-prevent-click>取消</el-button>
        <el-button v-print="'printContent'" type="primary" @click="confirmPrintTemplate" size="small" v-prevent-click>打印 </el-button>
      </div>
    </el-dialog>
    <div style="display: none">
      <component id="printContent" :is="printComponentName"></component>
    </div>
    <!-- 选择商品弹窗 -->
    <el-dialog title="选择商品" :visible.sync="showProductVisible" :close-on-click-modal="false">
      <el-row>
        <el-col :span="8" class="pad_10_0">
          <el-input size="small" v-model="ProductName" clearable @clear="handleSearchProductClick" @keyup.enter.native="handleSearchProductClick" placeholder="输入商品名称搜索"></el-input>
        </el-col>
        <el-col :span="2" class="pad_10_0 marlt_10">
          <el-button @click="handleSearchProductClick" size="small" type="primary" v-prevent-click>搜索</el-button>
        </el-col>
      </el-row>
      <div>
        <el-table size="small" :data="ProductList" max-height="480px" :row-key="(row) => row.ID" @selection-change="getSelectProduct" ref="productTable">
          <el-table-column type="selection" width="55" :selectable="checkboxSelect" :reserve-selection="true"> </el-table-column>
          <el-table-column prop="ProductName" label="商品名称">
            <template slot-scope="scope">
              {{ scope.row.ProductName }}
              <el-tag v-if="scope.row.IsLock" type="warning" size="small">库存盘点中</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="Specification" label="产品规格"></el-table-column>
          <el-table-column prop="PCategoryName" label="品牌名称"></el-table-column>
          <el-table-column prop="Quantity" label="可用库存"></el-table-column>
          <el-table-column prop="Price" label="价格">
            <template slot-scope="scope">
              {{ scope.row.Price | toFixed | NumFormat }}
            </template>
          </el-table-column>
          <el-table-column prop="DeliveryPrice" label="配销价格(元)">
            <template slot-scope="scope">
              {{ scope.row.DeliveryPrice | toFixed | NumFormat }}
            </template>
          </el-table-column>
          <el-table-column label="最小包装单位">
            <template slot-scope="scope">
              <span v-for="(item, index) in scope.row.Unit" :key="index">
                <span v-if="item.UnitName">{{ item.Amount }}/{{ item.UnitName }}</span>
              </span>
            </template>
          </el-table-column>
        </el-table>
        <div class="pad_15 text_right">
          <el-pagination background v-if="ProductPaginations.total > 0" @current-change="ProductChange" :current-page.sync="ProductPaginations.page" :page-size="ProductPaginations.page_size" :layout="ProductPaginations.layout" :total="ProductPaginations.total"></el-pagination>
        </div>
      </div>

      <span slot="footer" class="dialog-footer">
        <el-button @click="showProductVisible = false" v-prevent-click size="small">取 消</el-button>
        <el-button type="primary" size="small" @click="submitFormApplicableDuty" v-prevent-click>确 定</el-button>
      </span>
    </el-dialog>
    <!-- 商品明细备注 -->
    <el-dialog :visible.sync="projectRemarkDialogVisible" title="备注信息" width="500px" :close-on-click-modal="false">
      <el-input type="textarea" :autosize="{ minRows: 2, maxRows: 4 }" maxlength="100" show-word-limit placeholder="请输入备注信息" v-model="projectRemark"> </el-input>

      <div slot="footer" class="dialog-footer">
        <el-button @click="projectRemarkDialogVisible = false" size="small" :disabled="RemarkLoading">取 消</el-button>
        <el-button type="primary" @click="saveRemarkClick" :loading="RemarkLoading" v-prevent-click size="small">保 存 </el-button>
      </div>
    </el-dialog>
    <!-- 编辑配送出库详情配送信息 -->
    <el-dialog :visible.sync="outboundInfoRemarkDialogVisible" title="配送信息" width="500px" :close-on-click-modal="false">
      <el-input type="textarea" :autosize="{ minRows: 2, maxRows: 4 }" maxlength="100" show-word-limit placeholder="请输入配送信息" v-model="outboundInfoRemark"> </el-input>

      <div slot="footer" class="dialog-footer">
        <el-button @click="outboundInfoRemarkDialogVisible = false" size="small" :disabled="RemarkLoading">取 消</el-button>
        <el-button type="primary" @click="saveoutboundInfoRemarkClick" :loading="outboundInfoRemarkLoading" v-prevent-click size="small">保 存 </el-button>
      </div>
    </el-dialog>

    <el-dialog :visible.sync="showImportProduct" title="批量导入" width="500px" :close-on-click-modal="false" @close="closeImportProduct">
      <el-upload v-if="importLoading == '1'" class="upload-file-xlsx" accept=".xls,.xlsx" :before-upload="beforeImportProduct" action="#" multiple>
        <i class="el-icon-upload font_24"></i>
        <br />
        <span>添加文件</span>
        <span v-if="importFile">1/1</span>
        <span v-else>0/1</span>
        <br />
        <div>
          仅支持xlsx格式文件，最大不超过1M。<br />格式可参考模板
          <el-link @click.stop.native href="https://mfl-saas-data.oss-cn-shanghai.aliyuncs.com/import/apply-import-template.xlsx" target="_blank" type="primary">下载模板</el-link>
        </div>
      </el-upload>

      <div v-if="importLoading == '2'" class="dis_flex flex_x_center flex_y_center">
        <i class="el-icon-loading" style="font-size: 30px"></i>
        <span>数据导入中...</span>
        <span>数据批量导入中，请耐心等待</span>
      </div>

      <div v-if="importLoading == '3'" class="dis_flex flex_dir_column flex_x_center flex_y_center">
        <div class="marbm_10">
          <i class="el-icon-warning color_green" style="font-size: 40px"></i>
        </div>
        <div>
          <span>商品导入完成</span>
          <span>其中成功导入 </span>
          <span class="color_green">{{ importFileData.SuccessCount }} </span>
          <span>条数据，未导入 </span>
          <span class="color_red">{{ importFileData.FaildCount }}</span>
          <span>条数据 </span>
          <span v-if="importFileData.ErrorLine">在第 {{  importFileData.ErrorLine }} 行</span>
        </div>
      </div>

      <div slot="footer">
        <el-button @click="showImportProduct = false" size="small" v-prevent-click>取 消</el-button>
        <el-button v-if="importLoading == '1'" type="primary" @click="saveaddProductsImportProduct" :loading="importLoading == '2'" size="small" v-prevent-click :disabled="!importFile">一键导入 </el-button>
        <el-button type="primary" size="small" v-if="importLoading == '3'" @click="confirmImportProductClick">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import permission from "@/components/js/permission.js";
import APIStorage from "@/api/PSI/Purchase/storage";
import APIPSIApplyProduct from "@/api/PSI/Purchase/entityApplyProduct";
import APIInbound from "@/api/PSI/Inventory/inventoryProductInbound";
import APIOutbound from "@/api/PSI/Inventory/inventoryProductOutbound";
import dateUtil from "@/components/js/date";

var Enumerable = require("linq");
import print from "vue-print-nb";
import Vue from "vue";

export default {
  name: "PurchaseEntityApplyProduct",
  directives: {
    print,
  },
  /**  Vue 实例的数据对象**/
  data() {
    return {
      importLoading: "1",
      showImportProduct: false,
      outboundInfoRemarkLoading: false,
      outboundInfoRemarkDialogVisible: false,
      outboundInfoRemark: "",
      tmpSelectProductList: [],
      ProductName: "",
      showProductVisible: false,
      isSaveDraft: false,
      BillStatus: null,
      ApprovalRemark: "",
      printComponentName: "",
      printContent: "",
      printTemplateVisible: false,
      projectRemarkDialogVisible: false,
      saveDraftLoading: false,
      RemarkLoading: false,
      projectRemark: "",
      remarkItemIndex: "",
      remarktype: "",
      printTemplateID: "",
      templateTypeList: [],
      TemplateType: "entityapplystock",
      clickdelite: true,
      clickout: true,

      /**  权限功能按钮   */
      isAdd: false, // 新建要货
      isCheck: false, // 审核单据
      isClose: false, // 关闭待审核单据
      isTurnClose: false, // 关闭已驳回单据单据
      isDelivery: false, // 配送出库
      isStorage: false, // 要货入库
      isPaymentConfirm: false, // 确认收款
      isEdit: false,

      isAddOrEdit: false, // 是否新增

      // 一下两个功能权限暂时不写 【功能模块没有写】
      isViewDeliveryBill: false, // 查看配送出库单
      isViewStorageyBill: false, // 查看要货入库单

      loading: false,
      approvedLoading: false,
      outboundLoading: false,
      modalLoading: false,

      approvedRefuseLoading: false, //审批驳货
      approvedPaasLoading: false, // 审批通过

      dialogVisible: false,
      applyDetaildialogVisible: false, //要货详情
      approvalDetaildialogVisible: false, // 审批
      finalRejectionDialogVisible: false,
      outboundDetaildialogVisible: false, // 审批出库
      inboundDetaildialogVisible: false, //入库

      selectProductDialogVisible: false,

      InboundInfoDialogVisible: false, //入库详情
      OutboundInfoDialogVisible: false, // 出库
      productLoading: false,

      removeDisabled: true, //选中产品删除按钮是否禁用
      multipleProducts: [], // 已选中的将要删除的产品

      confirmPaymentDialogVisible: false,
      confirmPaymentInfo: "", // 确认付款信息
      // 列表筛选条件
      searchForm: {
        ID: "",
        Name: "", //产品名称
        DateTime: "",
        BillStatus: "0", //单据状态，05：草稿、 10：待审核、15、已审核、20：待配送、30：待入库、40：已驳回、50：已完成、60：已取消
        EntityID: "",
      },
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now() ? true : false;
        },
      },
      searchProductName: "",
      //创建要货 验证规则
      entityApplyProduct: {
        InDate: dateUtil.formatDate.format(new Date(), "YYYY-MM-DD hh:mm"), //入库时间
        EntityID: "", //仓库ID
        EntityName: "",
        LargessBalance: "",
        TotalBalance: "",
        Remark: "", // 备注
        Product: [], // 产品列表
      },
      entityApplyProductRules: {
        EntityID: [
          {
            required: true,
            message: "请选择仓库/门店",
            trigger: ["blur", "change"],
          },
        ],
        ApplyQuantity: [{ required: true, trigger: ["blur", "change"] }],
        ProductID: [{ required: true, trigger: ["blur", "change"] }],
        CurrentUnit: [{ required: true, trigger: ["blur", "change"] }],
      }, //产品验证规则

      // 要货列表
      inventoryApplyList: [],
      paginations: {
        page: 1, // 当前位于哪页
        total: 0, // 总数
        page_size: 10, // 1页显示多少条
        layout: "total, prev, pager, next, jumper", // 翻页属性
      },
      purchaseStorage: [], //仓库列表
      OutboundEntitys: [], // 审批要货仓库 排除入库仓
      // 要货产品信息
      ProductList: [],
      ProductListTotal: 0,
      productPageNum: 1,
      ProductPaginations: {
        page: 1, // 当前位于哪页
        total: 0, // 总数
        page_size: 10, // 1页显示多少条
        layout: "total, prev, pager, next, jumper", // 翻页属性
      }, //需要给分页组件传的信息
      applyDetailInfo: "",
      approveEntityRules: "",
      finalRejection: "", //驳回原因

      StatusNumberInfo: "", // 个状态数量
      InboundInfo: "",
      OutboundInfo: "",
      editProductList: [],
      editProduct: [], //编辑时商品明细
      importFile: null,
      importFileData: "",
    };
  },
  /**  路由  */
  beforeRouteEnter(to, from, next) {
    next((vm) => {
      /**  新建要货  */
      vm.isAdd = permission.permission(to.meta.Permission, "PSI-SupplyChain-EntityApplyProduct-Add");
      /**  审核单据  */
      vm.isCheck = permission.permission(to.meta.Permission, "PSI-SupplyChain-EntityApplyProduct-Check");
      /** 关闭待审核单据 */
      vm.isClose = permission.permission(to.meta.Permission, "PSI-SupplyChain-EntityApplyProduct-Close");
      /** 关闭已驳回单据 */
      vm.isTurnClose = permission.permission(to.meta.Permission, "PSI-SupplyChain-EntityApplyProduct-RejectClose");
      /**  配送出库 */
      vm.isDelivery = permission.permission(to.meta.Permission, "PSI-SupplyChain-EntityApplyProduct-Delivery");
      /**  要货入库 */
      vm.isStorage = permission.permission(to.meta.Permission, "PSI-SupplyChain-EntityApplyProduct-Storage");
      /**  查看配送出库单 */
      vm.isViewDeliveryBill = permission.permission(to.meta.Permission, "PSI-SupplyChain-EntityApplyProduct-ViewDeliveryBill");
      /**  查看要货入库单  */
      vm.isViewStorageyBill = permission.permission(to.meta.Permission, "PSI-SupplyChain-EntityApplyProduct-ViewStorageyBill");
      /** 确认付款审核单据 */
      vm.isPaymentConfirm = permission.permission(to.meta.Permission, "PSI-SupplyChain-EntityApplyProduct-PaymentConfirm");
      vm.isEdit = permission.permission(to.meta.Permission, "PSI-SupplyChain-EntityApplyProduct-Edit");
    });
  },
  /**  方法集合  */
  methods: {
    /**  确认导入的数据  */
    confirmImportProductClick() {
      let that = this;

      that.entityApplyProduct.Product = that.importFileData.ProductList.map((val) => {
        val.MiniUnit = {
          Amount: val.MinimumUnitAmount,
          UnitID: val.MinimumUnitID,
          UnitName: val.MinimumUnitName,
        };
        val.CurrentUnit = val.Unit.filter((i) => i.UnitID == val.UnitID)[0];
        val.DeliveryPrice = val.DeliveryPrice ? val.DeliveryPrice : val.Price;
        val.DeliveryTotalPrice = val.DeliveryPrice ? parseFloat(val.DeliveryPrice * val.ApplyMinimumUnitQuantity || 0) : parseFloat(val.Price * val.ApplyMinimumUnitQuantity || 0);
        val.IsLock = val.InboundIsLock;
        val.ID = val.ProductID;
        val.Quantity = parseInt(val.StockAmount || 0);
        val.ApplyMiniUnitQuantity = val.ApplyMinimumUnitQuantity;
        return val;
      }); // 产品列表
      that.showImportProduct = false;
    },

    /**  导入文件 前  */
    closeImportProduct() {
      let that = this;
      // that.importFileData = "";
      that.importLoading = "1";
      that.importFile = null;
    },
    /**  导入文件 前  */
    beforeImportProduct(file) {
      let that = this;
      that.importFile = file;
      return false;
    },
    /**  确定导入  */
    saveaddProductsImportProduct() {
      let that = this;
      that.inventoryApply_importProduct(that.importFile);
    },
    /**  批量导入弹窗显示  */
    addProductsImportProduct() {
      let that = this;
      that.$refs["entityApplyProductRef"].validateField("EntityID", (valid) => {
        if (!valid) {
          that.showImportProduct = true;
          // that.ProductName = "";
          // that.get_stock_list_entityProductListNetwork();
          // that.showProductVisible = true;
          // that.tmpSelectProductList = [];
          // that.$nextTick(() => {
          //   this.$refs.productTable.clearSelection();
          // });
        }
      });
    },
    /**  修改配送出库详情  */
    outboundInfoRemarkClick() {
      let that = this;
      that.outboundInfoRemarkDialogVisible = true;
      that.outboundInfoRemark = that.OutboundInfo.Remark;
    },
    /**    */
    saveoutboundInfoRemarkClick() {
      let that = this;
      that.outboundInfoRemarkLoading = true;
      that.inventoryProductOutbound_updateRemark();
    },
    /**    */
    async inventoryProductOutbound_updateRemark() {
      let that = this;
      try {
        let params = {
          ID: that.OutboundInfo.ID, //单号
          Remark: that.outboundInfoRemark, //备注
        };
        let res = await APIPSIApplyProduct.inventoryProductOutbound_updateRemark(params);
        if (res.StateCode == 200) {
          that.OutboundInfo.Remark = that.outboundInfoRemark;
          that.outboundInfoRemarkDialogVisible = false;
          that.$message.success("编辑成功");
        } else {
          that.$message.error(res.Message);
        }
        that.outboundInfoRemarkLoading = false;
      } catch (error) {
        that.outboundInfoRemarkLoading = false;
        that.$message.error(error);
      }
    },
    /**  保存备注  */
    saveRemarkClick() {
      let that = this;
      if (that.remarktype == "apply") {
        that.entityApplyProduct.Product[that.remarkItemIndex].Remark = that.projectRemark;
      }
      if (that.remarktype == "approve") {
        that.applyDetailInfo.Detail[that.remarkItemIndex].Remark = that.projectRemark;
      }

      that.projectRemarkDialogVisible = false;
      that.projectRemark = "";
    },
    /**  修改产品明细备注  */
    editRemarkClick(item, index, type) {
      let that = this;
      that.projectRemarkDialogVisible = true;
      that.remarkItemIndex = index;
      that.projectRemark = item.Remark;
      that.remarktype = type;
    },
    /**  修改编辑状态下 要货产品明细   */
    editAndProductList(originalArray) {
      if (this.isAddOrEdit) {
        return originalArray;
      } else {
        let tempUnion = Enumerable.from(originalArray)
          .union(this.editProduct, (i) => i.ID)
          .toArray();
        return tempUnion;
      }
    },
    /**  编辑 要货  */
    updateApplyProductInfo(row) {
      this.isAddOrEdit = row.BillStatus == "05" ? true : false;
      this.BillStatus = row.BillStatus;
      this.loading = true;
      this.editProduct = [];

      this.inventoryApply_dataDetail(row.ID);
    },
    /**  申请合计   */
    entityApplyProductDetailedSummary({ columns }) {
      const sums = [];
      columns.forEach((column, index) => {
        if (index == 0) {
          sums[index] = "总计";
        } else if (index == columns.length - 1) {
          let total = parseFloat(
            this.entityApplyProduct.Product &&
              this.entityApplyProduct.Product.reduce((perVal, nexVal) => {
                return perVal + parseFloat(nexVal.DeliveryTotalPrice || 0);
              }, 0)
          ).toFixed(2);
          let filterNumFormat = this.$options.filters["NumFormat"];
          let toFixed = this.$options.filters["toFixed"];

          sums[index] = <span class="font_weight_600">{filterNumFormat(toFixed(total))}</span>;
        } else if (index == columns.length - 4) {
          let applyQuantityTotal = parseInt(
            this.entityApplyProduct.Product &&
              this.entityApplyProduct.Product.reduce((perVal, nexVal) => {
                return perVal + parseInt(nexVal.ApplyQuantity || 0);
              }, 0)
          );
          sums[index] = <span class="font_weight_600">{applyQuantityTotal}</span>;
        } else {
          sums[index] = "";
        }
      });
      return sums;
    },
    /** 审批合计   */
    approvalsummary({ columns }) {
      const sums = [];
      columns.forEach((column, index) => {
        if (index == 0) {
          sums[index] = "总计";
        } else if (index == columns.length - 1) {
          let tempk = "ApprovedTotalAmount";
          if (this.applyDetailInfo.BillStatus == "30") {
            tempk = "OutboundTotalAmount";
          } else if (this.applyDetailInfo.BillStatus == "50") {
            tempk = "InboundTotalAmount";
          } else {
            tempk = "ApprovedTotalAmount";
          }

          let total = parseFloat(
            this.applyDetailInfo.Detail &&
              this.applyDetailInfo.Detail.reduce((perVal, nexVal) => {
                return perVal + parseFloat(nexVal[tempk] || 0);
              }, 0)
          ).toFixed(2);
          let filterNumFormat = this.$options.filters["NumFormat"];
          let toFixed = this.$options.filters["toFixed"];

          sums[index] = <span class="font_weight_600">{filterNumFormat(toFixed(total))}</span>;
        } else {
          sums[index] = "";
        }
      });
      return sums;
    },
    /** 详情合计   */
    approvalDetailsummary({ columns }) {
      const sums = [];
      columns.forEach((column, index) => {
        if (index == 0) {
          sums[index] = "总计";
        } else if (index == columns.length - 1) {
          let tempk = "ApprovedTotalAmount";
          if (this.applyDetailInfo.BillStatus == "30") {
            tempk = "OutboundTotalAmount";
          } else if (this.applyDetailInfo.BillStatus == "50") {
            tempk = "InboundTotalAmount";
          } else {
            tempk = "ApprovedTotalAmount";
          }

          let total = parseFloat(
            this.applyDetailInfo.Detail &&
              this.applyDetailInfo.Detail.reduce((perVal, nexVal) => {
                return perVal + parseFloat(nexVal[tempk] || 0);
              }, 0)
          ).toFixed(2);
          let filterNumFormat = this.$options.filters["NumFormat"];
          let toFixed = this.$options.filters["toFixed"];

          sums[index] = <span class="font_weight_600">{filterNumFormat(toFixed(total))}</span>;
        } else {
          sums[index] = "";
        }
      });
      return sums;
    },
    /**  入库合计  */
    applyDetailsummary({ columns }) {
      const sums = [];
      columns.forEach((column, index) => {
        if (index == 0) {
          sums[index] = "总计";
        } else if (index == columns.length - 1) {
          let total = parseFloat(
            this.applyDetailInfo.Detail &&
              this.applyDetailInfo.Detail.reduce((perVal, nexVal) => {
                return perVal + parseFloat(nexVal.OutboundTotalAmount || 0);
              }, 0)
          ).toFixed(2);
          let filterNumFormat = this.$options.filters["NumFormat"];
          let toFixed = this.$options.filters["toFixed"];

          sums[index] = <span class="font_weight_600">{filterNumFormat(toFixed(total))}</span>;
        } else {
          sums[index] = "";
        }
      });
      return sums;
    },
    /**  格式化明细商品名称 中间以... 代替  */
    formatProductName(name) {
      if (!name) return "";
      if (name.length > 20) {
        let frontStr = name.substr(0, 8);
        let afterStr = name.substr(name.length - 10, name.length);
        return frontStr + " ... " + afterStr;
      }
      return name;
    },
    /**  获取产品是否显示提示  */
    getShowProductNameTooltip(name) {
      if (!name || name == "") {
        return true;
      }
      if (name.length > 20) {
        return false;
      }
      return true;
    },
    /** 确定选择打印模板，并打印   */
    confirmPrintTemplate() {
      let that = this;
      that.printTemplateVisible = false;

      that.$nextTick(() => {
        /* that.clickdelite?that.applyDetailInfo:that.clickout?that.OutboundInfo:that.InboundInfo */
        that.createPrintComponent(that.clickdelite ? that.applyDetailInfo : that.clickout ? that.OutboundInfo : that.InboundInfo, that.printContent);
      });
    },
    /** 修改打印模板   */
    changePrintTemplate(val) {
      let that = this;
      let tempItem = that.templateTypeList.filter((item) => item.ID == val);
      that.printContent = tempItem[0].Template;
    },
    // 创建打印组件
    createPrintComponent(info, printContent) {
      let tempInfo = info; //传入打印数据源
      let templateStr = printContent; //传入打印模板
      var timestamp = new Date().valueOf();
      var componentName = "print" + timestamp;

      //创建组件
      Vue.component(componentName, {
        data: function () {
          return {
            info: tempInfo, //传入打印数据源
          };
        },
        template: "<div>" + templateStr + "</div>", //打印模板
      });
      this.printComponentName = componentName; //显示打印组件
    },

    /**  打印  */
    /**    */
    printInfoTips() {
      let that = this;
      that.$message.error("暂无打印模板，请添加打印模板");
    },
    printInfoSelectTemplate() {
      let that = this;
      that.printTemplateID = "";
      that.printTemplateVisible = true;
    },
    printInfo() {
      let that = this;
      let tempPrintTemplate = that.templateTypeList.length == 1 ? that.templateTypeList[0].Template : "";
      /* that.clickdelite?that.applyDetailInfo:that.clickout?that.OutboundInfo:that.InboundInfo */
      that.createPrintComponent(that.clickdelite ? that.applyDetailInfo : that.clickout ? that.OutboundInfo : that.InboundInfo, tempPrintTemplate);
    },
    /**    */
    settlementWayTitleFormat(type) {
      switch (type) {
        case "10":
          return "先货后款";
        case "20":
          return "先款后货";
        default:
          return "";
      }
    },
    closeOutboundInfoDialog() {
      let that = this;
      that.clickdelite = true;
      that.TemplateType = "entityapplystock";
      that.getPrintTemplate_list();
    },
    closeInboundInfoDialog() {
      let that = this;
      that.clickdelite = true;
      that.TemplateType = "entityapplystock";
      that.getPrintTemplate_list();
    },
    /**  点击搜索  */
    handleSearchEntityApplyProductClick() {
      let that = this;
      // that.searchForm.BillStatus = "0";
      that.paginations.page = 1;
      that.getInventoryApplyListNetwork();
    },
    /**  时间修改  */
    searchDateChange() {
      let that = this;
      that.handleSearchEntityApplyProductClick();
    },

    /** 要货列表 分页切换  */
    EntityApplyProductListHandleCurrentChange(page) {
      let that = this;
      that.paginations.paginations = page;
      that.getInventoryApplyListNetwork();
    },
    /**  订单状态数据格式化  */
    ApplyOrderBillStatusFormatter(row) {
      // 05：草稿、 10：待审核、20：待配送、30：待入库、40：已驳回、50：已完成、60：已取消
      if (!row) {
        return "";
      }
      switch (row.BillStatus) {
        case "05":
          return "草稿";
        case "10":
          return "待审核";
        case "15":
          return "待付款";
        case "20":
          return "待配送";
        case "30":
          return "待入库";
        case "40":
          return "已驳回";
        case "50":
          return "已完成";
        case "60":
          return "已取消";
      }
    },
    /**  点击tabs 切换  */
    tabsHandleClick() {
      var that = this;
      that.paginations.page = 1;
      that.getInventoryApplyListNetwork();
    },

    /**  添加要货申请  */
    addEntityApplyProductClick() {
      var that = this;

      // that.$refs["entityApplyProductRef"].resetFields()
      that.entityApplyProduct = {
        InDate: dateUtil.formatDate.format(new Date(), "YYYY-MM-DD hh:mm"), //入库时间
        EntityID: "", //仓库ID
        EntityName: "",
        Remark: "", // 备注
        LargessBalance: "",
        TotalBalance: "",
        Product: [], // 产品列表
      };
      if (that.purchaseStorage.length == 1) {
        that.entityApplyProduct.EntityID = that.purchaseStorage[0].ID;
        that.entityApplyProduct.EntityName = that.purchaseStorage[0].EntityName;
      }
      this.isAddOrEdit = true;
      that.dialogVisible = true;
    },
    /** 关闭新增弹窗的回调   */
    closeAddApplyProduct() {
      let that = this;
      that.$refs["entityApplyProductRef"].clearValidate();
      if (that.$refs.multipleTable) {
        that.$refs.multipleTable.clearSelection();
      }
    },
    /**  选择 要货仓库  */
    handleSelectProductEntity(row) {
      let that = this;
      that.entityApplyProduct.EntityID = row.ID;
      that.entityApplyProduct.LargessBalance = row.LargessBalance;
      that.entityApplyProduct.TotalBalance = row.TotalBalance;
      that.entityApplyProduct.Balance = row.Balance;
      that.entityApplyProduct.AddressDetail = row.AddressDetail;

      that.entityApplyProduct.Product = [];
      that.tmpSelectProductList = [];
    },
    /**  要货添加产品 -- 新增 */
    addProducts() {
      let that = this;
      that.ProductPaginations.page = 1;
      that.$refs["entityApplyProductRef"].validateField("EntityID", (valid) => {
        if (!valid) {
          that.ProductName = "";
          that.get_stock_list_entityProductListNetwork();
          that.showProductVisible = true;
          that.tmpSelectProductList = [];
          that.$nextTick(() => {
            this.$refs.productTable.clearSelection();
          });
        }
      });
    },
    /* 产品搜索 */
    handleSearchProductClick() {
      let that = this;
      that.ProductPaginations.page = 1;
      that.get_stock_list_entityProductListNetwork();
    },
    /**   选择单位 */
    handleSelectProductUnit(val, row) {
      row.ApplyQuantity = "";
      row.ApplyMiniUnitQuantity = "";
    },

    /**  修改要货数量  */
    changeApplyQuantity(row) {
      row.ApplyQuantity = Math.floor(row.ApplyQuantity);
      row.ApplyMiniUnitQuantity = parseFloat(row.ApplyQuantity) * parseFloat(row.CurrentUnit.Amount);
      row.DeliveryTotalPrice = parseFloat(parseFloat(row.ApplyMiniUnitQuantity) * parseFloat(row.DeliveryPrice) || 0).toFixed(2);
    },
    /**  产品 --- 列表切换分页 */
    ProductChange(page) {
      let that = this;
      that.ProductPaginations.page = page;
      that.get_stock_list_entityProductListNetwork();
    },

    /**  批量删除  */
    removeMultipleProduct() {
      var that = this;
      if (that.multipleProducts.length > 0) {
        for (var i = 0; i < that.entityApplyProduct.Product.length; i++) {
          that.multipleProducts.forEach(function (item) {
            if (that.entityApplyProduct.Product[i] == item) {
              that.entityApplyProduct.Product.splice(i, 1);
              i--;
            }
          });
        }
      }
    },

    /** 选中 产品 执行删除   */
    handleChangeSelectProduct(selection) {
      this.multipleProducts = selection;
      if (this.multipleProducts.length > 0) {
        this.removeDisabled = false;
      } else {
        this.removeDisabled = true;
      }
    },
    /** 保存要货申请信息   */
    saveEntityApplyProductClick() {
      let that = this;
      if (that.entityApplyProduct.Product.length == 0) {
        that.$message.error({
          message: "请选择要货产品",
          duration: 2000,
        });
        return;
      }
      that.$refs["entityApplyProductRef"].validate((valid) => {
        if (valid) {
          if (that.isAddOrEdit) {
            that.setInventoryApplyCreateNetwork();
          } else {
            that.inventoryApply_update();
          }
        }
      });
    },
    /* 保存要货草稿 */
    saveDraftClick() {
      let that = this;
      if (that.entityApplyProduct.Product.length == 0) {
        that.$message.error({
          message: "请选择要货产品",
          duration: 2000,
        });
        return;
      }
      that.$refs["entityApplyProductRef"].validate((valid) => {
        if (valid) {
          that.saveDraft();
        }
      });
    },
    /**  关闭要货   */
    closeEntityApplyProductClick(row) {
      var that = this;
      that
        .$confirm("关闭要货后将无法继续要货, 确定要关闭吗?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
        .then(() => {
          that.setInventoryProductCancelNetwork(row.ID);
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消操作",
          });
        });
    },
    /**  审核驳回关闭  */
    turnCloseEntityApplyProductClick(row) {
      var that = this;
      that
        .$confirm("确定要关闭吗?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
        .then(() => {
          that.setinventoryProductCancelRejectApplylNetwork(row.ID);
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消操作",
          });
        });
    },
    /**  查看要货详情  */
    checkEntityApplyProductDetail(row) {
      let that = this;

      that.getinventoryApplyInfoNetwork(row.ID, 0);
      that.clickdelite = true;
    },

    /**  单价 标题动态修改  */
    formatApprovedPriceTitle(row, isPrice) {
      var that = this;
      switch (that.applyDetailInfo.BillStatus) {
        case "20":
          return isPrice ? "审核单价" : "小计(元)";
        case "30":
          return isPrice ? "配送单价" : "小计(元)";
        case "50":
          return isPrice ? "配送单价" : "小计(元)";
        default:
          return "";
      }
    },
    /**  要货金额统计  */
    formatterTotalAmount(row) {
      let that = this;
      let TotalAmount = "";
      switch (that.applyDetailInfo.BillStatus) {
        case "20":
          TotalAmount = row.ApprovedTotalAmount;
          break;

        case "30":
          TotalAmount = row.OutboundTotalAmount;
          break;

        case "40":
        case "50":
          TotalAmount = row.InboundTotalAmount;
          break;

        default:
          TotalAmount = "";
          break;
      }
      // NumFormat
      return TotalAmount;
    },
    /** 要货 审批 详情  */
    approvalEntityApplyProductDetail(row) {
      let that = this;
      that.ApprovalRemark = "";
      that.getinventoryApplyInfoNetwork(row.ID, 1);
    },
    /**  审批 发货仓库选择  */
    deliveryHandleSelectProductEntity(row) {
      let that = this;
      let ProductIDs = Enumerable.from(that.applyDetailInfo.Detail)
        .select((i) => i.ProductID)
        .toArray();
      that.applyDetailInfo.EntityID = row.ID;
      that.applyDetailInfo.InboundAddressDetail = row.AddressDetail;
      that.getinventoryProductStockNetwork(ProductIDs, that.applyDetailInfo.EntityID);
    },
    /**  修改审批通过数量  */
    changeApprovalQuantity(row) {
      let that = this;
      row.ApproveQuantity = Math.floor(row.ApproveQuantity);
      // 下标
      let indexOf = that.applyDetailInfo.Detail.indexOf(row);
      // 排除 当前项的  已审批数量
      let totalQuantity = Enumerable.from(that.applyDetailInfo.Detail)
        .where((i, index) => {
          return i.ProductID == row.ProductID && index != indexOf;
        })
        .sum((i) => Number(i.ApproveQuantity) * Number(i.MinimumUnitAmount));
      // 剩余的最小单位数量
      let balanceMiniQuantity = parseFloat(row.StockQuantity) - parseFloat(totalQuantity);
      // 转换大单位数量
      let balanceQuantity = Math.floor(balanceMiniQuantity / row.MinimumUnitAmount);
      // 临时值  申请数量是否大于当前库存数量  单位按照申请单位计算
      let tempQuantity = row.ApplyQuantity > balanceQuantity ? balanceQuantity : row.ApplyQuantity;
      if (tempQuantity <= 0) {
        tempQuantity = 0;
      }

      if (row.ApproveQuantity > tempQuantity) {
        row.ApproveQuantity = tempQuantity;
        that.$message.error({
          message: "预配数量超出实物库存数量",
          duration: 2000,
        });
      }

      if (row.ApprovedPrice) {
        row.ApprovedTotalAmount = parseFloat(parseFloat(row.ApprovedPrice) * parseFloat(row.ApproveQuantity)).toFixed(2);
      }
    },
    /**  修改审核 配送单价  */
    changeApprovalPrice(row) {
      if (row.ApprovedPrice && row.ApproveQuantity) {
        row.ApprovedTotalAmount = parseFloat(parseFloat(row.ApprovedPrice) * parseFloat(row.ApproveQuantity)).toFixed(2);
      }
    },

    /**  审核通过 驳回  */
    approvalEntityApplyProductClick(isPass) {
      let that = this;
      if (isPass) {
        // 通过
        that.$refs["approvalDetailRef"].validate((valid) => {
          if (valid) {
            let ApprovedTotalAmount = Enumerable.from(that.applyDetailInfo.Detail).sum((i) => Number(i.ApprovedTotalAmount));

            let InventoryApplyDetail = Enumerable.from(that.applyDetailInfo.Detail)
              .select((i) => ({
                ID: i.ID,
                ApproveQuantity: i.ApproveQuantity,
                ApprovedPrice: i.ApprovedPrice,
                ApprovedTotalAmount: parseFloat(i.ApprovedTotalAmount || 0).toFixed(2),
                ApproveMinimumUnitQuantity: parseFloat(i.ApproveQuantity || 0) * parseFloat(i.MinimumUnitAmount || 0), // 最小单位数量
                Remark: i.Remark,
              }))
              .toArray();

            var approvedParams = {
              ID: that.applyDetailInfo.ID,
              BillStatus: that.applyDetailInfo.SettlementWay == "10" ? "20" : "15",
              ApprovedTotalAmount: parseFloat(ApprovedTotalAmount || 0).toFixed(2),
              OutboundEntityID: that.applyDetailInfo.EntityID,
              InventoryApplyDetail: InventoryApplyDetail,
              SettlementWay: that.applyDetailInfo.SettlementWay,
              ApprovalRemark: that.ApprovalRemark,
            };
            that.approvedPaasLoading = true;
            that.setinventoryApplyApprovedNetwork(approvedParams);
          }
        });
      } else {
        // 驳回
        that.finalRejection = "";
        that.finalRejectionDialogVisible = true;
      }
    },

    /**  确认付款  */
    confirmPaymentClick(row) {
      let that = this;
      that.getinventoryApplyInfoNetwork(row.ID, 4);
    },
    /**  修改支付方式  */
    changePaymentWayClick(val) {
      let that = this;
      if (val == 10) {
        that.$set(that.approveEntityRules, "PaymentWay", [
          {
            required: true,
            message: "请选择付款方式",
            trigger: ["blur", "change"],
          },
        ]);
        that.$set(that.approveEntityRules, "ReceiptNumber", [
          {
            required: true,
            message: "请输入回单号码",
            trigger: ["blur", "change"],
          },
        ]);
        that.$set(that.approveEntityRules, "PaymentAccountName", [
          {
            required: true,
            message: "请输入付款户名",
            trigger: ["blur", "change"],
          },
        ]);

        delete that.approveEntityRules.Balance_payment;
      }
      if (val == 20) {
        let balanceTotal = parseFloat(parseFloat(that.applyDetailInfo.Balance || 0) + parseFloat(that.applyDetailInfo.LargessBalance || 0)).toFixed(2);
        if (parseFloat(that.applyDetailInfo.PaymentTotalAmout) > parseFloat(balanceTotal)) {
          that.$message.error("账户余额不足！");
          return;
        }
        that.$set(that.approveEntityRules, "Balance_payment", [
          {
            required: true,
            message: "请输入付款本金金额",
            trigger: ["blur", "change"],
          },
        ]);

        delete that.approveEntityRules.PaymentWay;
        delete that.approveEntityRules.ReceiptNumber;
        delete that.approveEntityRules.PaymentAccountName;
      }
      // that.approveEntityRules.
    },
    /**  修改支付本金  */
    changPaymentBalance(val) {
      let that = this;
      if (val > that.applyDetailInfo.Balance) {
        that.applyDetailInfo.Balance_payment = that.applyDetailInfo.Balance;
      }
    },
    /**  修改支付赠金  */
    changePaymentLargessBalance(val) {
      let that = this;
      if (val > that.applyDetailInfo.LargessBalance) {
        that.applyDetailInfo.LargessBalance_payment = that.applyDetailInfo.LargessBalance;
      }
    },
    /**  保存确认付款  */
    saveConfirmPaymentClick() {
      let that = this;
      let balanceTotal = parseFloat(parseFloat(that.applyDetailInfo.Balance || 0) + parseFloat(that.applyDetailInfo.LargessBalance || 0)).toFixed(2);
      if (that.applyDetailInfo.PaymentWay == "20" && parseFloat(that.applyDetailInfo.PaymentTotalAmout) > parseFloat(balanceTotal)) {
        that.$message.error("账户余额不足！");
        return;
      }
      that.$refs.PaymentWayapprovalDetailRef.validate((valid) => {
        if (valid) {
          if (that.applyDetailInfo.PaymentWay == "20" && parseFloat(that.applyDetailInfo.PaymentTotalAmout).toFixed(2) != parseFloat(parseFloat(that.applyDetailInfo.Balance_payment || 0) + parseFloat(that.applyDetailInfo.LargessBalance_payment || 0)).toFixed(2)) {
            that.$message.error("付款金额不正确");
            return;
          }

          // else {
          that.inventoryApply_pay();
          // }
        }
      });
    },
    // 合计
    getSummaries(param) {
      let that = this;
      const { columns } = param;
      const sums = [];
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = "合计";
          return;
        }
        if (index == columns.length - 1) {
          let temp = parseFloat(that.applyDetailInfo.PaymentTotalAmout).toFixed(2);
          var filter_NumFormat = this.$options.filters["NumFormat"];
          let toFixed = this.$options.filters["toFixed"];
          sums[index] = <span class="font_weight_600">¥{filter_NumFormat(toFixed(temp))}</span>;
        } else {
          sums[index] = "";
        }
      });

      return sums;
    },

    /**  审核驳回  */
    finalRejectApprovedClick() {
      let that = this;
      if (that.BillStatus == "15") {
        let params = {
          BillStatus: "40",
          ID: that.applyDetailInfo.ID,
          RejectReason: that.finalRejection,
        };
        that.approvedRefuseLoading = true;
        that.rejectInventoryApply(params);
      } else {
        let approvedParams = {
          ID: that.applyDetailInfo.ID,
          BillStatus: "40",
          RejectReason: that.finalRejection,
          ApprovedTotalAmount: "",
          OutboundEntityID: "",
          InventoryApplyDetail: [],
          ApprovalRemark: that.ApprovalRemark,
        };
        that.approvedRefuseLoading = true;
        that.setinventoryApplyApprovedNetwork(approvedParams);
      }
    },

    /**  配送 详情  */
    outboundEntityApplyProductClick(row) {
      let that = this;
      that.getinventoryApplyInfoNetwork(row.ID, 2);
    },

    /** 配送修改数量  */
    changeOutboundQuantity(row) {
      let that = this;
      row.OutboundQuantity = Math.floor(row.OutboundQuantity);
      let tempQuantity = row.StockQuantity > row.ApproveQuantity ? row.ApproveQuantity : row.StockQuantity > 0 ? row.StockQuantity : 0;
      if (row.OutboundQuantity > tempQuantity) {
        row.OutboundQuantity = tempQuantity;
        row.stockoutQuantity = tempQuantity - row.OutboundQuantity;
        that.$message.error({
          message: "实发数量不能超过预配数量和可用库存数量",
          duration: 2000,
        });
        return;
      }
      row.stockoutQuantity = row.ApproveQuantity - row.OutboundQuantity;
      row.OutboundTotalAmount = parseFloat(row.OutboundQuantity) * parseFloat(row.ApprovedPrice);
    },
    /** 创建配送出库   */
    saveOutboundEntityApplyProductClick() {
      let that = this;
      let isLock = Enumerable.from(that.applyDetailInfo.Detail).contains(true, (val) => {
        return val.OutboundIsLock;
      });
      if (isLock) {
        that.$message.error({
          message: "存在盘点锁定的产品",
          duration: 2000,
        });
        return;
      }

      that.$refs["outboundDetailRef"].validate((valid) => {
        if (valid) {
          let InventoryApplyDetail = Enumerable.from(that.applyDetailInfo.Detail)
            .select((i) => ({
              ID: i.ID,
              OutboundQuantity: i.OutboundQuantity,
              OutboundPrice: i.OutboundPrice,
              OutboundTotalAmount: i.OutboundTotalAmount,
              OutboundMinimumUnitQuantity: parseFloat(i.OutboundQuantity) * parseFloat(i.MinimumUnitAmount),
            }))
            .toArray();

          let OutboundTotalAmount = Enumerable.from(that.applyDetailInfo.Detail).sum((i) => Number(i.OutboundTotalAmount));

          let params = {
            ID: that.applyDetailInfo.ID,
            OutboundTotalAmount: OutboundTotalAmount,
            InventoryApplyDetail: InventoryApplyDetail,
            Remark: that.applyDetailInfo.outRemark,
          };

          that.setinventoryApplyOutboundNetwork(params);
        }
      });
    },

    /**  入库  */
    inboundEntityApplyProductClick(row) {
      let that = this;
      that.getinventoryApplyInfoNetwork(row.ID, 3);
    },
    /**  修改入库数量  */
    changeInboundQuantity(row) {
      let that = this;
      row.InboundQuantity = Math.floor(row.InboundQuantity);

      if (row.InboundQuantity > row.OutboundQuantity) {
        row.InboundQuantity = row.OutboundQuantity;
        // row.stockoutQuantity = row.ApproveQuantity - row.OutboundQuantity
        that.$message.error({
          message: "实收数量不能超过实发数量",
          duration: 2000,
        });
        return;
      }
      // row.stockoutQuantity = row.ApproveQuantity - row.OutboundQuantity
      row.InboundTotalAmount = parseFloat(row.InboundQuantity) * parseFloat(row.InboundPrice);
    },
    /**  保存入库  */
    saveInboundEntityApplyProductClick() {
      let that = this;
      let isLock = Enumerable.from(that.applyDetailInfo.Detail).contains(true, (val) => {
        return val.InboundIsLock;
      });
      if (isLock) {
        that.$message.error({
          message: "存在盘点锁定的产品",
          duration: 2000,
        });
        return;
      }
      that.$refs["InboundDetailRef"].validate((valid) => {
        if (valid) {
          let InventoryApplyDetail = Enumerable.from(that.applyDetailInfo.Detail)
            .select((i) => ({
              ID: i.ID,
              InboundQuantity: i.InboundQuantity,
              InboundPrice: i.InboundPrice,
              InboundTotalAmount: i.InboundTotalAmount,
              InboundMinimumUnitQuantity: parseFloat(i.InboundQuantity) * parseFloat(i.MinimumUnitAmount),
            }))
            .toArray();

          let InboundTotalAmount = Enumerable.from(that.applyDetailInfo.Detail).sum((i) => Number(i.InboundTotalAmount));

          let params = {
            ID: that.applyDetailInfo.ID,
            InboundTotalAmount: InboundTotalAmount,
            InventoryApplyDetail: InventoryApplyDetail,
            Remark: that.applyDetailInfo.inRemark,
          };

          that.setinventoryApplyInboundNetwork(params);
        }
      });
    },

    /** 查看出库明细   */
    checkOutboundBillInfo() {
      let that = this;
      let parmas = {
        ID: that.applyDetailInfo.OutboundBillID,
        InventoryType: "配送出库",
      };
      that.get_info_ProductInventoryOutbound_netWork(parmas);
      that.clickout = true;
      that.clickdelite = false;
      that.TemplateType = "outstock";
      that.getPrintTemplate_list();
    },
    /**  查看入库明细  */
    checkInbounBillInfo() {
      let that = this;
      let parmas = {
        ID: that.applyDetailInfo.InboundBillID,
        InventoryType: "要货入库",
      };
      that.get_info_inventoryProductInbound_netWork(parmas);
      that.clickout = false;
      that.clickdelite = false;
      that.TemplateType = "instock";
      that.getPrintTemplate_list();
      // that.fileType.typeName = 'that.InboundInfo'
      that.clickout = false;
      that.clickdelite = false;
    },

    /**  ==========================================================================================  */

    /**  6.1.门店要货申请列表  */
    getInventoryApplyListNetwork: function () {
      var that = this;
      that.loading = true;
      var params = {
        PageNum: that.paginations.page,
        ID: that.searchForm.ID,
        ProductName: that.searchForm.Name,
        EntityID: that.searchForm.EntityID,
        StartDate: that.searchForm.DateTime == null ? "" : that.searchForm.DateTime[0],
        EndDate: that.searchForm.DateTime == null ? "" : that.searchForm.DateTime[1],
        BillStatus: that.searchForm.BillStatus == "0" ? "" : that.searchForm.BillStatus,
      };

      APIPSIApplyProduct.inventoryApplyList(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.inventoryApplyList = res.List;
            that.paginations.page_size = res.PageSize;
            that.paginations.total = res.Total;
            that.getInventoryProductBillStatusNumberNetwork();
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },
    /** 查询 产品 库存列表 列表  */
    get_stock_list_entityProductListNetwork: function () {
      var that = this;
      var params = {
        PageNum: that.ProductPaginations.page,
        ProductName: that.ProductName,
        EntityID: that.entityApplyProduct.EntityID,
      };
      APIPSIApplyProduct.getEntityProductDeliveryPrice(params)
        .then((res) => {
          if (res.StateCode == 200) {
            let tmp = res.List.map((i) => {
              // 最小包装单位
              i.MiniUnit = i.Unit.filter((u) => {
                return u.IsMinimumUnit;
              })[0];
              let DefaultUnit = i.Unit.filter((u) => {
                return u.IsDefautSendReceive;
              });
              // 默认包装单位
              if (DefaultUnit && DefaultUnit.length > 0) {
                i.DefaultUnit = DefaultUnit[0];
                i.CurrentUnit = i.DefaultUnit ? i.DefaultUnit : i.MiniUnit;
              }
              i.DeliveryPrice = i.DeliveryPrice ? i.DeliveryPrice : i.Price;
              i.ApplyQuantity = "";
              i.DeliveryTotalPrice = "";
              i.ProductID = i.ID;
              i.Remark = "";
              i.ApplyMiniUnitQuantity = "";

              return i;
            });
            that.ProductPaginations.total = res.Total;
            that.ProductList = tmp;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {});
    },

    /** 查询 产品 库存列表 列表  */
    async getProductList_list(searchProductName, InventoryApplyID) {
      var that = this;
      var params = {
        PageNum: that.productPageNum,
        ProductName: searchProductName,
        EntityID: that.entityApplyProduct.EntityID,
        InventoryApplyID: InventoryApplyID,
      };
      let res = await APIPSIApplyProduct.getProductList_list(params);

      if (res.StateCode == 200) {
        that.editProductList = res.Data;
      } else {
        that.$message.error({
          message: res.Message,
          duration: 2000,
        });
      }
      this.dialogVisible = true;
    },

    /**  4.4.仓库列表  */
    getStorageEntityNetwork: function () {
      var that = this;
      var params = {};
      APIStorage.getpurchaseStorageEntity(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.purchaseStorage = res.Data;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {});
    },
    /**  6.3.门店要货单申请  */
    setInventoryApplyCreateNetwork: function () {
      let that = this;
      let Products = Enumerable.from(that.entityApplyProduct.Product)
        .select((i) => ({
          ProductID: i.ProductID,
          UnitID: i.CurrentUnit.UnitID,
          ApplyQuantity: i.ApplyQuantity,
          MinimumUnitID: i.MiniUnit.UnitID,
          ApplyMinimumUnitQuantity: parseFloat(i.ApplyQuantity || 0) * parseFloat(i.CurrentUnit.Amount || 0),
          ApprovedPrice: i.DeliveryPrice ? i.DeliveryPrice : "0",
          ApprovedTotalAmount: i.DeliveryTotalPrice,
          Remark: i.Remark,
        }))
        .toArray();

      let Amount = Products.reduce((perVal, nextVal) => {
        return perVal + parseFloat(nextVal.ApprovedTotalAmount);
      }, 0);

      var params = {
        InboundEntityID: that.entityApplyProduct.EntityID,
        Remark: that.entityApplyProduct.Remark,
        InventoryApplyDetail: Products,
        Amount: parseFloat(Amount).toFixed(2),
        BillStatus: "10",
      };
      if (that.entityApplyProduct.ID) {
        params.ID = that.entityApplyProduct.ID;
      }
      this.modalLoading = true;
      APIPSIApplyProduct.inventoryApplyCreate(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.$message.success({
              message: "申请成功",
              duration: 2000,
            });
            that.dialogVisible = false;
            that.handleSearchEntityApplyProductClick();
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.modalLoading = false;
        });
    },

    /**  确认付款  */
    async inventoryApply_pay() {
      let that = this;
      let params = {
        ID: that.applyDetailInfo.ID, //订单号
        BillStatus: "20", //订单状态(10：待审核、20：待配送、30：待入库、40：已驳回、50：已完成、60：已取消、15：预付款)
        PaymentWay: that.applyDetailInfo.PaymentWay, //付款方式（10：离线转账，20：余额支付）
        ReceiptNumber: that.applyDetailInfo.ReceiptNumber, //回单号码（离线转账填写）
        PaymentAccountName: that.applyDetailInfo.PaymentAccountName, //付款户名（离线转账填写）
        Balance: that.applyDetailInfo.Balance_payment, //本金
        LargessBalance: that.applyDetailInfo.LargessBalance_payment, //赠额
        Remark: that.applyDetailInfo.Remark ? that.applyDetailInfo.Remark : that.applyDetailInfo.RemarkPayment, //备注
      };
      let res = await APIPSIApplyProduct.inventoryApply_pay(params);
      if (res.StateCode == 200) {
        that.confirmPaymentDialogVisible = false;
        that.$message.success({
          message: "操作成功",
          duration: 2000,
        });
        that.handleSearchEntityApplyProductClick();
      } else {
        that.$message.error(res.Message);
      }
    },
    /**  6.2.门店要货单详情 */
    getinventoryApplyInfoNetwork: function (ID, type) {
      var that = this;
      var params = {
        ID: ID,
      };
      APIPSIApplyProduct.inventoryApplyInfo(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.applyDetailInfo = res.Data;
            that.approveEntityRules = {};
            if (type != "0") {
              if (that.applyDetailInfo.BillStatus == "10") {
                that.approveEntityRules = {
                  EntityID: [
                    {
                      required: true,
                      message: "请选择发货仓库",
                      trigger: ["blur", "change"],
                    },
                  ],
                  SettlementWay: [
                    {
                      required: true,
                      message: "请选择结算方式",
                      trigger: ["blur", "change"],
                    },
                  ],
                  ApproveQuantity: [{ required: true, trigger: ["blur", "change"] }],
                  ApprovedPrice: [{ required: true, trigger: ["blur", "change"] }],
                };
              }
              if (that.applyDetailInfo.BillStatus == "20") {
                that.approveEntityRules = {
                  OutboundQuantity: [{ required: true, trigger: ["blur", "change"] }],
                  outRemark: [
                    {
                      required: true,
                      message: "请输入配送信息",
                      trigger: ["blur", "change"],
                    },
                  ],
                };
              }
              if (that.applyDetailInfo.BillStatus == "30") {
                that.approveEntityRules = {
                  InboundQuantity: [{ required: true, trigger: ["blur", "change"] }],
                };
              }
            }
            switch (type) {
              case 0: // 查看详情
                that.applyDetaildialogVisible = true;
                break;
              case 1: // 审批详情
                that.OutboundEntitys = Enumerable.from(that.purchaseStorage)
                  .where((i) => i.ID != that.applyDetailInfo.InboundEntityID)
                  .toArray();
                that.applyDetailInfo.EntityID = "";
                that.applyDetailInfo.EntityName = "";
                that.approvalDetaildialogVisible = true;
                that.applyDetailInfo.InDate = dateUtil.formatDate.format(new Date(), "YYYY-MM-DD hh:mm:ss");
                break;

              case 2: // 发货详情
                that.outboundDetaildialogVisible = true;
                Enumerable.from(that.applyDetailInfo.Detail).forEach((i) => {
                  if (!i.OutboundIsLock) {
                    i.OutboundQuantity = i.StockQuantity > 0 && i.StockQuantity > i.ApproveQuantity ? i.ApproveQuantity : i.StockQuantity > 0 ? i.StockQuantity : 0;
                    i.OutboundPrice = i.ApprovedPrice;
                    i.OutboundTotalAmount = parseFloat(i.OutboundQuantity) * parseFloat(i.OutboundPrice);
                  }
                });
                break;

              case 3: // 入库详情
                that.inboundDetaildialogVisible = true;
                Enumerable.from(that.applyDetailInfo.Detail).forEach((i) => {
                  if (!i.InboundIsLock) {
                    i.InboundQuantity = i.OutboundQuantity;
                    i.InboundPrice = i.OutboundPrice;
                    i.InboundTotalAmount = parseFloat(i.InboundQuantity) * parseFloat(i.InboundPrice);
                  }
                });

                break;
              case 4: // 付款
                that.confirmPaymentDialogVisible = true;
                that.applyDetailInfo.PaymentTotalAmout = parseFloat(
                  that.applyDetailInfo.Detail.reduce((perVal, curVal) => {
                    return perVal + parseFloat(curVal.ApprovedTotalAmount);
                  }, 0)
                ).toFixed(2);
                that.$set(that.applyDetailInfo, "PaymentWay", "10");
                that.$set(that.applyDetailInfo, "Balance_payment", "");
                that.$set(that.applyDetailInfo, "LargessBalance_payment", "");

                that.$set(that.applyDetailInfo, "ReceiptNumber", "");
                that.$set(that.applyDetailInfo, "PaymentAccountName", "");
                that.$set(that.applyDetailInfo, "RemarkPayment", "");
                that.approveEntityRules.PaymentWay = [
                  {
                    required: true,
                    message: "请选择付款方式",
                    trigger: ["blur", "change"],
                  },
                ];
                that.approveEntityRules.ReceiptNumber = [
                  {
                    required: true,
                    message: "请输入回单号码",
                    trigger: ["blur", "change"],
                  },
                ];
                that.approveEntityRules.PaymentAccountName = [
                  {
                    required: true,
                    message: "请输入付款户名",
                    trigger: ["blur", "change"],
                  },
                ];

                break;
            }
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {});
    },
    /**  6.4.门店要货单审批*/
    setinventoryApplyApprovedNetwork: function (params) {
      var that = this;
      APIPSIApplyProduct.inventoryApplyApproved(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.$message.success({
              message: "审核成功",
              duration: 2000,
            });
            that.handleSearchEntityApplyProductClick();
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.approvedPaasLoading = false;
          that.approvedRefuseLoading = false;
          that.approvalDetaildialogVisible = false;
          that.finalRejectionDialogVisible = false;
        });
    },
    /**  6.5.门店要货单配送出库 */
    setinventoryApplyOutboundNetwork: function (params) {
      var that = this;
      this.outboundLoading = true;
      APIPSIApplyProduct.inventoryProductOutbound(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.$message.success({
              message: "审核成功",
              duration: 2000,
            });
            that.outboundDetaildialogVisible = false;
            that.handleSearchEntityApplyProductClick();
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.finalRejectionDialogVisible = false;
          that.outboundLoading = false;
        });
    },

    /**  6.6.门店要货单入库  */
    setinventoryApplyInboundNetwork: function (params) {
      var that = this;
      that.outboundLoading = true;
      APIPSIApplyProduct.inventoryProductInbound(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.$message.success({
              message: "入库成功",
              duration: 2000,
            });
            that.inboundDetaildialogVisible = false;
            that.handleSearchEntityApplyProductClick();
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.outboundLoading = false;
        });
    },

    /**  6.7.门店要货单取消 */
    setInventoryProductCancelNetwork: function (ID) {
      var that = this;
      that.approvedLoading = true;
      var params = {
        ID: ID,
      };
      APIPSIApplyProduct.inventoryProductCancel(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.$message({
              message: "取消成功",
              duration: 2000,
            });
            that.handleSearchEntityApplyProductClick();
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {});
    },
    /**  6.7.门店要货单取消 */
    setinventoryProductCancelRejectApplylNetwork: function (ID) {
      var that = this;
      that.approvedLoading = true;
      var params = {
        ID: ID,
      };
      APIPSIApplyProduct.inventoryProductCancelRejectApply(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.$message({
              message: "取消成功",
              duration: 2000,
            });
            that.handleSearchEntityApplyProductClick();
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {});
    },
    /** 6.8.门店要货单状态统计 */
    getInventoryProductBillStatusNumberNetwork: function () {
      var that = this;
      that.approvedLoading = true;
      var params = {
        ID: that.searchForm.ID,
        ProductName: that.searchForm.Name,
        EntityID: that.searchForm.EntityID,
        StartDate: that.searchForm.DateTime == null ? "" : that.searchForm.DateTime[0],
        EndDate: that.searchForm.DateTime == null ? "" : that.searchForm.DateTime[1],
        // BillStatus:
        //   that.searchForm.BillStatus == "0" ? "" : that.searchForm.BillStatus,
      };

      APIPSIApplyProduct.inventoryProductBillStatusNumber(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.StatusNumberInfo = res.Data;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {});
    },

    /** 门店要货单审批 产品实时库存  */
    getinventoryProductStockNetwork: function (ProductIDs, EntityID) {
      var that = this;
      var params = {
        ProductID: ProductIDs,
        EntityID: EntityID,
      };
      APIPSIApplyProduct.inventoryProductStock(params)
        .then((res) => {
          if (res.StateCode == 200) {
            let Products = res.Data;
            that.applyDetailInfo.Detail = Enumerable.from(that.applyDetailInfo.Detail)
              .select((val) => {
                val.ApproveQuantity = "";
                return val;
              })
              .toArray();
            Enumerable.from(that.applyDetailInfo.Detail).forEach((i) => {
              i.ApproveQuantity = "";
              let tempProduct = Enumerable.from(Products).singleOrDefault((j) => {
                return j.ProductID == i.ProductID;
              }, -1);

              if (tempProduct != -1) {
                i.StockQuantity = tempProduct.Quantity; // 可用库存  最小单位数量
                i.miniUnitName = tempProduct.UnitName; // 最小单位名称
                i.miniUnitID = tempProduct.UnitID; // 最小单位 ID

                let totalQuantity = Enumerable.from(that.applyDetailInfo.Detail)
                  .where((val) => {
                    return val.ProductID == i.ProductID;
                  })
                  .sum((val) => Number(val.ApproveQuantity || 0) * Number(val.MinimumUnitAmount));

                // 最小单位剩余数量
                tempProduct.balanceQuantity = parseFloat(tempProduct.Quantity) - parseFloat(totalQuantity || 0);
                // 当前大单位的数量
                let tempApproveQuantity = Math.floor(parseFloat(tempProduct.balanceQuantity) / parseFloat(i.MinimumUnitAmount));
                if (tempApproveQuantity <= 0) {
                  tempApproveQuantity = 0;
                }

                i.ApproveQuantity = i.ApplyQuantity > tempApproveQuantity ? tempApproveQuantity : i.ApplyQuantity;

                // 计算价格
                if (tempProduct.DeliveryPrice) {
                  i.ApprovedPrice = tempProduct.DeliveryPrice;
                }
                if (i.ApprovedPrice) {
                  i.ApprovedTotalAmount = parseFloat(i.ApproveQuantity) * parseFloat(i.MinimumUnitAmount) * parseFloat(i.ApprovedPrice);
                }
              }
            });
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {});
    },

    /**   10.2.产品入库详情    */
    get_info_inventoryProductInbound_netWork: function (params) {
      var that = this;
      that.loading = true;
      APIInbound.get_info_inventoryProductInbound(params)
        .then((res) => {
          if (res.StateCode == 200) {
            if (!res.Data) {
              that.$message.error("暂未获取到数据");
            } else {
              that.InboundInfo = res.Data;
              that.InboundInfoDialogVisible = true;
            }
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },

    /**  8.2.产品出库详情   */
    get_info_ProductInventoryOutbound_netWork: function (params) {
      var that = this;
      that.loading = true;
      APIOutbound.getProductInventoryOutbound_info(params)
        .then((res) => {
          if (res.StateCode == 200) {
            if (!res.Data) {
              that.$message.error("暂未获取到数据");
            } else {
              that.OutboundInfoDialogVisible = true;
              that.OutboundInfo = res.Data;
            }
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },

    /**    */
    async getProductDispatchPrice(ProductID, EntityID) {
      let that = this;
      let params = {
        ProductID: ProductID, //产品编号
        EntityID: EntityID, //门店编号
      };
      let res = await APIPSIApplyProduct.getProductDispatchPrice(params);
      if (res.StateCode == 200) {
        return res.Data;
      } else {
        that.$message.error(res.Message);
      }
    },
    /** 获取模板列表   */
    async getPrintTemplate_list() {
      let that = this;
      let params = { TemplateType: that.TemplateType };
      let res = await APIPSIApplyProduct.getPrintTemplate_list(params);
      if (res.StateCode == 200) {
        that.templateTypeList = res.Data;
      } else {
        that.$message.error(res.Message);
      }
    },
    /**   门店要货驳回后编辑   */
    async inventoryApply_update() {
      let that = this;
      let Products = Enumerable.from(that.entityApplyProduct.Product)
        .select((i) => ({
          ProductID: i.ProductID,
          UnitID: i.CurrentUnit.UnitID,
          ApplyQuantity: i.ApplyQuantity,
          MinimumUnitID: i.MiniUnit.UnitID,
          ApplyMinimumUnitQuantity: Number(i.ApplyQuantity) * Number(i.CurrentUnit.Amount),
          ApprovedPrice: i.DeliveryPrice ? i.DeliveryPrice : "0",
          ApprovedTotalAmount: i.DeliveryTotalPrice,
          Remark: i.Remark,
        }))
        .toArray();

      let Amount = Products.reduce((perVal, nextVal) => {
        return perVal + parseFloat(nextVal.ApprovedTotalAmount);
      }, 0);
      var params = {
        ID: that.entityApplyProduct.ID,
        InboundEntityID: that.entityApplyProduct.EntityID,
        Remark: that.entityApplyProduct.Remark,
        InventoryApplyDetail: Products,
        ApprovedTotalAmount: parseFloat(Amount || 0).toFixed(2),
      };
      this.modalLoading = true;
      let res = await APIPSIApplyProduct.inventoryApply_update(params);
      if (res.StateCode == 200) {
        that.$message.success({
          message: "申请成功",
          duration: 2000,
        });
        that.dialogVisible = false;
        that.handleSearchEntityApplyProductClick();
      } else {
        that.$message.error(res.Message);
      }
      this.modalLoading = false;
    },
    /* 保存草稿 */
    async saveDraft() {
      let that = this;
      let Products = Enumerable.from(that.entityApplyProduct.Product)
        .select((i) => ({
          ProductID: i.ProductID,
          UnitID: i.CurrentUnit.UnitID,
          ApplyQuantity: i.ApplyQuantity,
          MinimumUnitID: i.MiniUnit.UnitID,
          ApplyMinimumUnitQuantity: Number(i.ApplyQuantity) * Number(i.CurrentUnit.Amount),
          ApprovedPrice: i.DeliveryPrice ? i.DeliveryPrice : "0",
          ApprovedTotalAmount: i.DeliveryTotalPrice,
          Remark: i.Remark,
        }))
        .toArray();

      let Amount = Products.reduce((perVal, nextVal) => {
        return perVal + parseFloat(nextVal.ApprovedTotalAmount);
      }, 0);
      var params = {
        ID: that.entityApplyProduct.ID,
        InboundEntityID: that.entityApplyProduct.EntityID,
        Remark: that.entityApplyProduct.Remark,
        InventoryApplyDetail: Products,
        Amount: parseFloat(Amount || 0).toFixed(2),
        BillStatus: "05",
      };
      this.saveDraftLoading = true;
      let res = await APIPSIApplyProduct.inventoryApplyCreate(params);
      if (res.StateCode == 200) {
        that.$message.success({
          message: "要货草稿保存成功",
          duration: 2000,
        });
        that.dialogVisible = false;
        that.handleSearchEntityApplyProductClick();
      } else {
        that.$message.error(res.Message);
      }
      this.saveDraftLoading = false;
    },
    /**  编辑详情请求   */
    async inventoryApply_dataDetail(ID) {
      let that = this;
      let params = { ID: ID };
      let res = await APIPSIApplyProduct.inventoryApply_dataDetail(params);
      if (res.StateCode == 200) {
        let temp = res.Data;
        this.editProduct = temp.Detail.map((i) => {
          i.MiniUnit = {
            Amount: i.MinimumUnitAmount,
            UnitID: i.MinimumUnitID,
            UnitName: i.MinimumUnitName,
          };
          i.CurrentUnit = i.Unit.filter((val) => val.UnitID == i.UnitID)[0];
          i.DeliveryPrice = i.DeliveryPrice ? i.DeliveryPrice : i.Price;
          i.DeliveryTotalPrice = "";
          i.Quantity = parseInt(i.StockAmount || 0);
          i.IsLock = i.InboundIsLock;
          return i;
        });

        this.entityApplyProduct = {
          ID: temp.ID,
          InDate: temp.CreatedOn, //入库时间
          EntityID: temp.InboundEntityID, //仓库ID
          EntityName: temp.InboundEntityName,
          TotalBalance: temp.Balance + temp.LargessBalance,
          Balance: temp.Balance,
          LargessBalance: temp.LargessBalance,
          AddressDetail: temp.AddressDetail,
          Remark: temp.Remark, // 备注
          RejectReason: temp.RejectReason,
          Product: temp.Detail.map((val) => {
            val.MiniUnit = {
              Amount: val.MinimumUnitAmount,
              UnitID: val.MinimumUnitID,
              UnitName: val.MinimumUnitName,
            };
            val.CurrentUnit = val.Unit.filter((i) => i.UnitID == val.UnitID)[0];

            val.DeliveryPrice = val.DeliveryPrice ? val.DeliveryPrice : val.ApprovedPrice;
            val.DeliveryTotalPrice = val.DeliveryPrice ? parseFloat(val.DeliveryPrice * val.ApplyQuantity || 0) : parseFloat(val.Price * val.ApplyQuantity || 0);
            val.IsLock = val.InboundIsLock;
            val.ID = val.ProductID;
            val.Quantity = parseInt(val.StockAmount || 0);
            val.ApplyMiniUnitQuantity = val.ApplyMinimumUnitQuantity;
            return val;
          }), // 产品列表
        };
        this.loading = false;
        this.dialogVisible = true;
      } else {
        that.$message.error(res.Message);
      }
    },
    /* 确认付款环节驳回 */
    rejectClick() {
      let that = this;
      that.BillStatus = that.applyDetailInfo.BillStatus;
      that.finalRejectionDialogVisible = true;
    },
    rejectInventoryApply: function (params) {
      let that = this;
      APIPSIApplyProduct.rejectInventoryApply(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.$message.success({
              message: "驳回成功",
              duration: 2000,
            });
            that.handleSearchEntityApplyProductClick();
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.approvedRefuseLoading = false;
          that.finalRejectionDialogVisible = false;
          that.confirmPaymentDialogVisible = false;
        });
    },
    rejectClose() {
      let that = this;
      that.BillStatus = null;
      that.finalRejection = "";
    },
    /* 选择商品 */
    getSelectProduct(selection) {
      this.tmpSelectProductList = selection;
    },
    /* 确认商品选择 */
    submitFormApplicableDuty() {
      let that = this;
      that.entityApplyProduct.Product.push(...that.tmpSelectProductList);
      that.showProductVisible = false;
    },
    /* 商品选择选择框是否禁用 */
    checkboxSelect(row) {
      return !row.IsLock;
    },
    /**    */
    async inventoryApply_importProduct(file) {
      let that = this;
      try {
        let params = {
          file: file,
          entityID: that.entityApplyProduct.EntityID,
        };
        that.importLoading = "2";
        let res = await APIPSIApplyProduct.inventoryApply_importProduct(params);
        if (res.StateCode == 200) {
          that.importFileData = res.Data;

          that.importLoading = "3";
          // that.showImportProduct = false;
        } else {
          that.$message.error(res.Message);
          that.importLoading = "1";
        }
      } catch (error) {
        that.$message.error(error);
        that.importLoading = "1";
      }
    },

    /**  提成结算导出  */
    async inventoryApply_template() {
      let that = this;
      that.downloadLoading = true;
      try {
        let params = {};
        let res = await APIPSIApplyProduct.inventoryApply_template(params);
        this.$message.success({
          message: "正在下载",
          duration: "4000",
        });
        const link = document.createElement("a");
        let blob = new Blob([res], { type: "application/octet-stream" });
        link.style.display = "none";
        link.href = URL.createObjectURL(blob);
        link.download = "产品导入模板.xlsx"; //下载的文件名
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        that.downloadLoading = false;
      } catch (error) {
        that.$message.error(error);
        that.downloadLoading = false;
      }
    },
  },
  /**  实例被挂载后调用  */
  mounted() {
    var that = this;
    /**  新建要货  */
    that.isAdd = permission.permission(that.$route.meta.Permission, "PSI-SupplyChain-EntityApplyProduct-Add");
    /**  审核单据  */
    that.isCheck = permission.permission(that.$route.meta.Permission, "PSI-SupplyChain-EntityApplyProduct-Check");
    /** 关闭待审核单据 */
    that.isClose = permission.permission(that.$route.meta.Permission, "PSI-SupplyChain-EntityApplyProduct-Close");
    /** 关闭已驳回单据 */
    that.isTurnClose = permission.permission(that.$route.meta.Permission, "PSI-SupplyChain-EntityApplyProduct-RejectClose");
    /**  配送出库 */
    that.isDelivery = permission.permission(that.$route.meta.Permission, "PSI-SupplyChain-EntityApplyProduct-Delivery");
    /**  要货入库 */
    that.isStorage = permission.permission(that.$route.meta.Permission, "PSI-SupplyChain-EntityApplyProduct-Storage");
    /**  查看配送出库单 */
    that.isViewDeliveryBill = permission.permission(that.$route.meta.Permission, "PSI-SupplyChain-EntityApplyProduct-ViewDeliveryBill");
    /**  查看要货入库单  */
    that.isViewStorageyBill = permission.permission(that.$route.meta.Permission, "PSI-SupplyChain-EntityApplyProduct-ViewStorageyBill");
    that.isPaymentConfirm = permission.permission(that.$route.meta.Permission, "PSI-SupplyChain-EntityApplyProduct-PaymentConfirm");
    that.isEdit = permission.permission(that.$route.meta.Permission, "PSI-SupplyChain-EntityApplyProduct-Edit");
    // var date = new Date(),
    //   y = date.getFullYear(),
    //   m = date.getMonth();
    // that.searchForm.DateTime = [
    //   dateUtil.formatDate.format(new Date(y, m, 1), "YYYY-MM-DD"),
    //   dateUtil.formatDate.format(new Date(), "YYYY-MM-DD"),
    // ];
    that.handleSearchEntityApplyProductClick();
    that.getStorageEntityNetwork();
    // that.get_stock_list_entityProductListNetwork();
    that.getPrintTemplate_list();
  },
};
</script>

<style lang="scss">
.EntityApplyProduct {
  .entityApplyProductDialogClass {
    .entityApplyProductInfoFrom {
      .el-form-item__label {
        font-size: 13px !important;
      }

      .el-form-item__content {
        font-size: 13px !important;
        line-height: 33px;
      }

      .el-form-item {
        margin-bottom: 0px;
      }

      .el-input__inner {
        padding-right: 0;
      }
    }

    .productFormInforClass {
      .el-form-item {
        margin-bottom: 0px;
      }
    }

    .PaymententityApplyProductInfoFrom {
      .el-form-item__label {
        font-size: 13px !important;
      }

      .el-form-item__content {
        font-size: 13px !important;
        line-height: 33px;
      }

      .el-form-item {
        margin-bottom: 6px;
      }

      .el-input__inner {
        padding-right: 0;
      }
    }
  }

  .IsLockProduct_list_back {
    background-color: #edf2fc;
    cursor: not-allowed;
  }

  .IsLockProduct_list_back:hover > td {
    background-color: #edf2fc !important;
  }

  .input_type {
    .el-input-group__append {
      padding: 0 10px;
    }
  }

  .el-tabs--border-card {
    // border: 0px,0px,0px,0px !important;
    border-bottom: 0px;
    box-shadow: 0 0px 0px 0 rgba(0, 0, 0, 0), 0 0 0px 0 rgba(0, 0, 0, 0);

    .el-tabs__content {
      padding: 0px !important;
    }
  }

  .custom_input {
    .el-input__inner {
      padding: 0 0 0 10px;
    }
  }

  .zl-custom-select-width {
    width: 210px;
  }

  .upload-file-xlsx {
    border: 1px solid #dddddd;
    border-radius: 3px;
    padding: 6px;
    text-align: center;
  }
}

.EntityApplyProduct_custom_popper_class {
  .el-select-dropdown__item {
    line-height: normal;
    height: auto;
  }
}

@media print {
  html,
  body {
    height: inherit;
  }
}
</style>
