/**
 * Created by JoJ<PERSON> on 2022/01/11.
 * 门店业绩-产品消耗门店业绩
 */
import * as API from '@/api/index'

export default {
  // 获取门店产品消耗业绩方案列表
  getProductEntityPerformanceScheme: params => {
    return API.POST('api/treatProductEntityPerformanceScheme/list', params)
  },

  // 创建门店产品消耗业绩方案
  createProductEntityPerformanceScheme: params => {
    return API.POST('api/treatProductEntityPerformanceScheme/create', params)
  },

  // 删除门店产品消耗业绩方案
  deleteProductEntityPerformanceScheme: params => {
    return API.POST('api/treatProductEntityPerformanceScheme/delete', params)
  },

  // 编辑的点击
  getProductCategoryEntityPerformance: params => {
    return API.POST('api/treatProductCategoryEntityPerformance/all', params)
  },

  // 编辑的保存
  updateProductCategoryEntityPerformance: params => {
    return API.POST('api/treatProductCategoryEntityPerformance/update', params)
  },

  // 产品业绩的点击
  getProductEntityPerformance: params => {
    return API.POST('api/treatProductEntityPerformance/all', params)
  },

  // 产品业绩的保存
  updateProductEntityPerformance: params => {
    return API.POST('api/treatProductEntityPerformance/update', params)
  },

}
