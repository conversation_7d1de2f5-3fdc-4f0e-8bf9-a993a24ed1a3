# 预约取消接口后端校验需求文档

## 问题描述

当前系统存在预约数据同步问题：
- **线索列表页面**（客服人员使用）
- **预约看板页面**（门店人员使用）

两个页面可以同时操作同一个预约的取消功能，导致：
1. 同一预约被重复取消
2. 产生多条取消记录
3. 数据不一致

## 涉及的取消预约接口

### 1. 主要取消接口

#### 接口1：预约状态更新接口（预约看板使用）
- **接口路径**: `POST /api/appointmentBill/updateStatus`
- **调用位置**: `src/views/iBeauty/Appointment/appointmentView.vue`
- **请求参数**:
```json
{
  "ID": "预约ID",
  "Status": 30  // 30表示已取消
}
```

#### 接口2：预约取消接口（线索跟进使用）
- **接口路径**: `POST /api/appointmentBill/cancel`
- **调用位置**: 
  - `src/views/iBeauty/Workbench/clueFollowUp.vue`
  - `src/views/iBeauty/Workbench/Component/workbenchAppointmentRecord.vue`
- **请求参数**:
```json
{
  "ID": "预约ID",
  "Status": 30  // 30表示已取消
}
```

### 2. 前端调用代码分析

#### 预约看板取消逻辑
```javascript
// 文件: src/views/iBeauty/Appointment/appointmentView.vue
// 方法: cancelAppiontmentClick(item)
cancelAppiontmentClick(item) {
  var that = this;
  that.$confirm('确定要取消预约吗?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(() => {
    // 调用状态更新接口
    that.appointmentBill_updateStatus(item.ID, '30');
  });
}

// 实际API调用
appointmentBill_updateStatus: (params) => {
  return API.POST("api/appointmentBill/updateStatus", params);
}
```

#### 线索跟进取消逻辑
```javascript
// 文件: src/views/iBeauty/Workbench/clueFollowUp.vue
// 方法: cancelAppointment(appointmentID, row)
cancelAppointment(appointmentID, row) {
  let that = this;
  let params = {
    ID: appointmentID,
    Status: 30 // 30表示已取消
  };
  that.saveLoading = true;
  // 调用取消接口
  API.appointmentBillCancel(params)
    .then((res) => {
      if (res.StateCode == 200) {
        that.$message.success('预约已取消');
        row.AppointmentStatus = 30;
      }
    });
}

// 实际API调用
appointmentBillCancel: (params) => {
  return API.POST("api/appointmentBill/cancel", params);
}
```

## 后端校验需求

### 1. 核心校验逻辑

#### 状态检查校验
```sql
-- 在执行取消操作前，检查当前预约状态
SELECT Status FROM AppointmentBill WHERE ID = @AppointmentID;

-- 校验规则：
-- 1. 如果 Status = 30（已取消），返回错误："该预约已被取消，无需重复操作"
-- 2. 如果 Status = 20（已到店），返回错误："该预约已确认到店，无法取消"
-- 3. 只有 Status = 10（未到店）时，才允许取消操作
```

#### 并发控制校验
```sql
-- 使用乐观锁或悲观锁防止并发修改
-- 方案1：乐观锁（推荐）
UPDATE AppointmentBill 
SET Status = 30, 
    CancelTime = GETDATE(),
    CancelBy = @OperatorID,
    UpdateTime = GETDATE()
WHERE ID = @AppointmentID 
  AND Status = 10  -- 只有未到店状态才能取消
  AND UpdateTime = @OriginalUpdateTime; -- 乐观锁检查

-- 如果影响行数为0，说明状态已被其他操作修改
```

### 2. 接口响应规范

#### 成功响应
```json
{
  "StateCode": 200,
  "Message": "预约取消成功",
  "Data": {
    "AppointmentID": "预约ID",
    "OriginalStatus": 10,
    "NewStatus": 30,
    "CancelTime": "2025-01-15 14:30:00",
    "CancelBy": "操作员ID"
  }
}
```

#### 失败响应
```json
{
  "StateCode": 400,
  "Message": "该预约已被取消，无需重复操作",
  "Data": {
    "AppointmentID": "预约ID",
    "CurrentStatus": 30,
    "StatusName": "已取消",
    "LastCancelTime": "2025-01-15 14:25:00",
    "LastCancelBy": "其他操作员ID"
  }
}
```

### 3. 具体实现建议

#### 接口1：`/api/appointmentBill/updateStatus` 增强
```csharp
[HttpPost]
public async Task<ApiResponse> UpdateStatus([FromBody] UpdateStatusRequest request)
{
    try
    {
        // 1. 参数验证
        if (string.IsNullOrEmpty(request.ID))
            return ApiResponse.Error("预约ID不能为空");
            
        // 2. 获取当前预约信息
        var appointment = await _appointmentService.GetByIdAsync(request.ID);
        if (appointment == null)
            return ApiResponse.Error("预约不存在");
            
        // 3. 状态校验
        if (request.Status == 30) // 取消操作
        {
            if (appointment.Status == 30)
                return ApiResponse.Error("该预约已被取消，无需重复操作");
                
            if (appointment.Status == 20)
                return ApiResponse.Error("该预约已确认到店，无法取消");
        }
        
        // 4. 执行更新（使用乐观锁）
        var result = await _appointmentService.UpdateStatusAsync(
            request.ID, 
            request.Status, 
            appointment.UpdateTime, // 乐观锁
            GetCurrentUserId()
        );
        
        if (!result.Success)
            return ApiResponse.Error(result.Message);
            
        return ApiResponse.Success("操作成功", result.Data);
    }
    catch (Exception ex)
    {
        _logger.LogError(ex, "更新预约状态失败");
        return ApiResponse.Error("系统错误，请稍后重试");
    }
}
```

#### 接口2：`/api/appointmentBill/cancel` 增强
```csharp
[HttpPost]
public async Task<ApiResponse> Cancel([FromBody] CancelRequest request)
{
    try
    {
        // 1. 参数验证
        if (string.IsNullOrEmpty(request.ID))
            return ApiResponse.Error("预约ID不能为空");
            
        // 2. 获取当前预约信息
        var appointment = await _appointmentService.GetByIdAsync(request.ID);
        if (appointment == null)
            return ApiResponse.Error("预约不存在");
            
        // 3. 状态校验
        if (appointment.Status == 30)
        {
            return ApiResponse.Error(new
            {
                Message = "该预约已被取消，无需重复操作",
                CurrentStatus = appointment.Status,
                LastCancelTime = appointment.CancelTime,
                LastCancelBy = appointment.CancelBy
            });
        }
        
        if (appointment.Status == 20)
            return ApiResponse.Error("该预约已确认到店，无法取消");
            
        // 4. 执行取消操作
        var result = await _appointmentService.CancelAsync(
            request.ID,
            GetCurrentUserId(),
            request.CancelReason
        );
        
        return result.Success ? 
            ApiResponse.Success("预约取消成功", result.Data) : 
            ApiResponse.Error(result.Message);
    }
    catch (Exception ex)
    {
        _logger.LogError(ex, "取消预约失败");
        return ApiResponse.Error("系统错误，请稍后重试");
    }
}
```

### 4. 数据库表结构建议

#### 预约表增强字段
```sql
ALTER TABLE AppointmentBill ADD COLUMN
(
    CancelTime DATETIME NULL,           -- 取消时间
    CancelBy NVARCHAR(50) NULL,        -- 取消操作员
    CancelReason NVARCHAR(500) NULL,   -- 取消原因
    UpdateTime DATETIME NOT NULL DEFAULT GETDATE(), -- 更新时间（乐观锁）
    OperationSource NVARCHAR(50) NULL  -- 操作来源（appointment_board/lead_follow）
);

-- 添加索引
CREATE INDEX IX_AppointmentBill_Status_UpdateTime ON AppointmentBill(Status, UpdateTime);
```

#### 操作日志表
```sql
CREATE TABLE AppointmentOperationLog
(
    ID BIGINT IDENTITY(1,1) PRIMARY KEY,
    AppointmentID NVARCHAR(50) NOT NULL,
    OperationType NVARCHAR(20) NOT NULL, -- CREATE/UPDATE/CANCEL/CONFIRM
    OriginalStatus INT NULL,
    NewStatus INT NULL,
    OperatorID NVARCHAR(50) NOT NULL,
    OperatorName NVARCHAR(100) NOT NULL,
    OperationSource NVARCHAR(50) NULL,   -- 操作来源
    OperationTime DATETIME NOT NULL DEFAULT GETDATE(),
    Remark NVARCHAR(500) NULL
);
```

## 测试验证

### 1. 并发测试场景
1. **同时取消测试**：两个用户同时点击取消同一预约
2. **状态变更测试**：一个用户取消预约，另一个用户同时确认到店
3. **重复操作测试**：预约已取消后，再次尝试取消

### 2. 预期结果
1. 只有第一个操作成功，后续操作返回友好错误提示
2. 不会产生重复的取消记录
3. 数据状态保持一致

## 实施优先级

### 立即实施（高优先级）
1. ✅ 状态检查校验
2. ✅ 错误信息优化
3. ✅ 操作日志记录

### 后续优化（中优先级）
1. 🔄 乐观锁机制
2. 🔄 前端实时同步
3. 🔄 操作权限控制

---

**请后端开发人员根据此文档实施相应的校验逻辑，确保预约取消操作的数据一致性。**
