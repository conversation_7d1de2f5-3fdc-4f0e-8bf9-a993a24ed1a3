
import * as API from '@/api/index'

export default {
    // 获取类型列表
    getCustomerFileCategory: params => {
        return API.POST('api/customerFileCategory/all',params)
    },
    // 新增类型
    customerFileCategory: params => {
        return API.POST('api/customerFileCategory/create',params)
    },
    // 更新类型
    updateCustomerFileCategory: params => {
        return API.POST('api/customerFileCategory/update',params)
    },
    // 移动类型
    moveCustomerFileCategory: params => {
        return API.POST('api/customerFileCategory/move',params)
    },
}