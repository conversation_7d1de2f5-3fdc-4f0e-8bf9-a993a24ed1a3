<template>
  <div class="content_body_nopadding AppointmentView position_relative" v-loading="loading" ref="appointmentRef">
    <!-- 预约看板 状态 及 时间修改  -->
    <div class="appointmentBar">
      <el-form :inline="true" size="small">
        <el-form-item v-if="handleIndex == 0">
          <el-select v-model="appointment_servicerID" placeholder="请选择" size="small" @change="changeServicer">
            <el-option v-for="servicer in servicerList" :key="servicer.ServicerID" :label="servicer.ServicerName" :value="servicer.ServicerID"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item v-if="handleIndex == 0">
          <i class="el-icon-arrow-left color_333 pad_10" @click="upClick"></i>
          <el-date-picker v-model="currentDate" placeholder="选择日期" size="small" value-format="yyyy-MM-dd" @change="getAppointmentBillAll"> </el-date-picker>
          <i class="el-icon-arrow-right color_333 pad_10" @click="downClick"></i>
        </el-form-item>
        <el-form-item v-if="handleIndex == 0">
          <el-popover placement="bottom" width="200px" trigger="click">
            <span>列宽 <el-slider v-model="itemSize.width" size="small" :min="150" :max="300" show-tooltip @change="changeItemSizeWidth"></el-slider></span>
            <span>行高 <el-slider v-model="itemSize.height" size="small" :min="40" :max="150" show-tooltip @change="changeItemSizeHeight"></el-slider></span>
            <el-button slot="reference" class="el-icon-zoom-in" size="small"></el-button>
          </el-popover>
        </el-form-item>
        <el-form-item v-if="handleIndex == 0">
          <span class="squareBlock_orange marrt_10"></span>
          <span class="font_13 color_333">未到店</span>

          <div class="squareBlock_gray marrt_10 marlt_10"></div>
          <span class="font_13 color_333">已到店</span>

          <div class="squareBlock_white marrt_10 marlt_10"></div>
          <span class="font_13 color_333">空闲</span>

          <div class="squareBlock_rest marrt_10 marlt_10"></div>
          <span class="font_13 color_333">休息</span>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" size="small" @click="addAppointment(0)">添加预约 </el-button>
        </el-form-item>
      </el-form>
    </div>

    <el-tabs v-model="handleIndex" type="border-card">
      <el-tab-pane label="预约看板" name="0">
        <appointment-panel
          style="width: 100%"
          :style="{ height: panelHeight + 'px' }"
          :itemSize="itemSize"
          :panelTimeAxis="panelTimeAxis"
          :employeeList="viewEmployeeList"
          :currentDate="currentDate"
          :AppointmentServicerIsRequired="appointmentConfigInfo.AppointmentServicerIsRequired"
          @addAppiontment="addAppiontmentClick"
          @cancel="cancelAppiontmentClick"
          @edit="editAppiontmentClick"
          @confrim="confrimAppiontmentClick"
          @autoRefresh="handleAutoRefresh"
        ></appointment-panel>
      </el-tab-pane>
      <el-tab-pane label="预约明细" name="1">
        <div class="nav_header">
          <el-row>
            <el-col :span="24">
              <el-form :inline="true" size="small" @keyup.enter.native="handleSearch">
                <el-row>
                  <el-form-item label="客户">
                    <el-input v-model="searchValue" placeholder="输入客户名称、手机号搜索" clearable @clear="handleSearch"> </el-input>
                  </el-form-item>
                  <el-form-item label="预约时间">
                    <el-date-picker
                      v-model="SearchData"
                      type="daterange"
                      range-separator="至"
                      value-format="yyyy-MM-dd"
                      start-placeholder="开始日期"
                      end-placeholder="结束日期"
                      @change="handleSearch"
                    ></el-date-picker>
                  </el-form-item>
                  <el-form-item label="状态">
                    <el-select placeholder="请选择预约状态" clearable v-model="statusValue" @change="handleSearch">
                      <el-option label="未到店" value="10"></el-option>
                      <el-option label="已到店" value="20"></el-option>
                      <el-option label="已取消" value="30"></el-option>
                    </el-select>
                  </el-form-item>
                  <el-form-item label="预约来源">
                    <el-select placeholder="请选择预约来源" clearable v-model="Channel" @change="handleSearch">
                      <el-option label="PC" value="PC"></el-option>
                      <el-option label="小程序" value="Miniprogram"></el-option>
                      <el-option label="商城" value="MicroMall"></el-option>
                    </el-select>
                  </el-form-item>

                  <el-form-item label="创建时间">
                    <el-date-picker
                      v-model="CreatedOnDate"
                      type="daterange"
                      range-separator="至"
                      value-format="yyyy-MM-dd"
                      start-placeholder="开始日期"
                      end-placeholder="结束日期"
                      @change="handleSearch"
                    ></el-date-picker>
                  </el-form-item>
                </el-row>
                <el-row>
                  <el-form-item label="预约角色">
                    <el-select placeholder="请选择" clearable v-model="ServicerID" @change="handleSearch">
                      <el-option
                        v-for="item in servicerList"
                        :key="'servicer' + item.ServicerID"
                        :label="item.ServicerName"
                        :value="item.ServicerID"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                  <el-form-item label="预约员工">
                    <el-select placeholder="请选择" clearable filterable v-model="ServicerEmployeeID" @change="handleSearch">
                      <el-option
                        v-for="item in searchEmployeeAllList"
                        :key="'searchEmployeeAllList_' + item.ID"
                        :label="item.Name"
                        :value="item.ID"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                  <el-form-item label="会员等级">
                    <el-select v-model="searchCustomerLevelID" placeholder="请选择会员等级" filterable size="small" clearable @change="handleSearch">
                      <el-option v-for="item in customerLevel" :key="item.ID" :label="item.Name" :value="item.ID"> </el-option>
                    </el-select>
                  </el-form-item>

                  
                  <el-form-item label="渠道">
                    <el-input v-model="searchCustomerChannel" placeholder="输入渠道名称" clearable @clear="handleSearch"> </el-input>
                  </el-form-item>
                  <el-form-item>
                    <el-button type="primary" @click="handleSearch" v-prevent-click>搜索</el-button>
                    <el-button type="primary" @click="excelappointmentBill" v-prevent-click :loading="excelIncomeLoading">导出</el-button>
                  </el-form-item>
                </el-row>
              </el-form>
            </el-col>
          </el-row>
        </div>

        <div class="table-container">
          <el-table :data="tableData" size="small" style="width: 100%">
            <el-table-column prop="CustomerName" label="客户姓名" min-width="120" show-overflow-tooltip></el-table-column>
            <el-table-column prop="PhoneNumber" label="客户手机号" min-width="130" show-overflow-tooltip>
              <template slot-scope="scope">
                {{ scope.row.PhoneNumber | hidephone }}
              </template>
            </el-table-column>
            <el-table-column prop="LevelName" label="会员等级" min-width="100" show-overflow-tooltip></el-table-column>
            <el-table-column prop="ChannelName" label="渠道" min-width="100" show-overflow-tooltip></el-table-column>
            <el-table-column v-if="appointmentTypeList.length > 0" prop="AppointmentTypeName" label="预约类型" min-width="100" show-overflow-tooltip> </el-table-column>
            <el-table-column prop="Servicer" label="接待人" min-width="150" show-overflow-tooltip>
              <template slot-scope="scope">
                <el-popover placement="top-start" width="200" trigger="hover">
                  <el-descriptions :column="1" size="small" border>
                    <el-descriptions-item v-for="item in scope.row.Servicer" :key="'Servicer' + item.ServicerID" :label="item.ServicerName">{{
                      servicerNames(item.Employee)
                    }}</el-descriptions-item>
                  </el-descriptions>
                  <div slot="reference" style="text-overflow: ellipsis; white-space: nowrap; overflow: hidden">
                    <span v-if="scope.row.Servicer.length > 0">
                      <span v-if="scope.row.Servicer[0].Employee">{{ scope.row.Servicer[0].ServicerName }}：</span>
                      <span>{{ servicerNames(scope.row.Servicer[0].Employee) }}</span>
                    </span>
                  </div>
                </el-popover>
              </template>
            </el-table-column>
            <el-table-column prop="Status" label="预约状态" min-width="100" show-overflow-tooltip>
              <template slot-scope="scope">
                {{ scope.row.Status == 10 ? '未到店' : scope.row.Status == 20 ? '已到店' : '已取消' }}
              </template>
            </el-table-column>
            <el-table-column prop="AppointmentDate" label="预约时间" min-width="150" show-overflow-tooltip></el-table-column>
            <el-table-column prop="Period" label="预约时长（分钟）" min-width="130" show-overflow-tooltip></el-table-column>
            <el-table-column prop="ArrivalDate" label="到店时间" min-width="150" show-overflow-tooltip></el-table-column>
            <el-table-column prop="CreatedBy" label="创建人" min-width="100" show-overflow-tooltip></el-table-column>
            <el-table-column prop="CreatedOn" label="创建时间" min-width="150" show-overflow-tooltip></el-table-column>
            <el-table-column prop="Channel" label="预约来源" min-width="100" show-overflow-tooltip></el-table-column>
            <el-table-column label="操作" width="80" fixed="right">
              <template slot-scope="scope">
                <el-button type="primary" size="small" @click="editAppiontmentClick(scope.row)">编辑</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div class="text_right pad_15">
          <el-pagination
            background
            v-if="paginations.total > 0"
            @current-change="handleCurrentChange"
            :current-page.sync="paginations.page"
            :page-size="paginations.page_size"
            :layout="paginations.layout"
            :total="paginations.total"
          ></el-pagination>
        </div>
        <!-- </div> -->
      </el-tab-pane>
    </el-tabs>

    <!--新增 修改-->
    <el-dialog :visible.sync="appointmentDialogShow" v-if="appointmentDialogShow" width="960px">
      <span slot="title" class="font_18">{{ !isAdd ? '编辑预约' : '添加预约' }}</span>
      <div v-if="isOperatingRecord && appointmentRecordList && appointmentRecordList.length">
        <el-tabs v-model="recordActive">
          <el-tab-pane label="预约信息" name="Info">
            <!-- 预约顾客信息  -->
            <el-row>
              <el-col :span="15">
                <el-autocomplete
                  popper-class="customer-autocomplete"
                  prefix-icon="el-icon-user-solid"
                  v-model="customerName"
                  style="width: 100%"
                  placeholder="请输入客户名称、手机号、编号查找，无匹配按回车新增"
                  :fetch-suggestions="saleCustomerData"
                  @select="handleCustomerSelect"
                  :disabled="CustomerID != null"
                  :trigger-on-focus="false"
                  :hide-loading="true"
                  :highlight-first-item="true"
                  :select-when-unmatched="true"
                  size="small"
                >
                  <template slot="append">
                    <el-button icon="el-icon-delete" @click="removeCustomer" :disabled="!isAdd"></el-button>
                  </template>
                  <template slot-scope="{ item }">
                    <div class="name">
                      {{ item.Name }}
                      <el-tag size="mini" v-if="item.CustomerLevelName">{{ item.CustomerLevelName }}</el-tag>
                    </div>
                    <div class="info">手机号：{{ item.PhoneNumber | hidephone }}</div>
                    <div class="info" v-if="item.Code">客户编号：{{ item.Code }}</div>
                    <div class="info" v-if="item.EntityName">所属组织：{{ item.EntityName }}</div>
                    <div class="info" v-if="item.ChannelName">渠道信息：{{ item.ChannelName }}</div>
                  </template>
                </el-autocomplete>
              </el-col>
              <el-col :span="8" :offset="1" class="back_f8 dis_flex flex_y_center radius5 line_height_38 pad_0_10">
                <el-col :span="12" class="back_f8">预约项目</el-col>
                <el-col :span="12" class="text_right">
                  <el-button
                    type="text"
                    size="small"
                    @click="addAppointmentProject"
                    :disabled="(currentItme.Status == '30' || currentItme.Status == '20') && handleIndex == '1'"
                    >添 加
                  </el-button>
                </el-col>
              </el-col>
            </el-row>

            <el-row class="martp_10">
              <el-col :span="15">
                <div style="height: 420px" class="back_f8 radius5">
                  <el-scrollbar class="el-scrollbar_height" style="height: 100%">
                    <el-row class="padtp_15">
                      <el-form :model="AppointmentInfoRuleForm" :rules="AppointmentInfoRules" ref="AppointmentInfoRuleForm" label-width="120px" size="small">
                        <el-form-item v-if="appointmentTypeList.length > 0" label="预约类型">
                          <el-col :span="20">
                            <el-select v-model="AppointmentInfoRuleForm.AppointmentTypeID" placeholder="请选择预约类型" clearable>
                              <el-option
                                :label="item.Name"
                                :value="item.ID"
                                v-for="item in appointmentTypeList"
                                :key="'appointmentType' + item.ID"
                                :disabled="(currentItme.Status == '30' || currentItme.Status == '20') && handleIndex == '1'"
                              >
                              </el-option>
                            </el-select>
                          </el-col>
                        </el-form-item>
                        <el-form-item label="预约时间" style="margin-bottom: 0px" required>
                          <el-col :span="12">
                            <el-form-item prop="AppointmentDate">
                              <el-date-picker
                                v-model="AppointmentInfoRuleForm.AppointmentDate"
                                value-format="yyyy-MM-dd"
                                placeholder="选择日期时间"
                                size="small"
                                @change="changeHandleAppointmentDate"
                              >
                              </el-date-picker>
                            </el-form-item>
                          </el-col>

                          <el-col :span="12">
                            <el-form-item prop="Time">
                              <el-time-select
                                v-model="AppointmentInfoRuleForm.Time"
                                :picker-options="{
                                  start: appointmentConfigInfo.StartTime,
                                  step: '00:' + appointmentConfigInfo.Period,
                                  end: appointmentConfigInfo.EndTime,
                                }"
                                placeholder="选择时间"
                              >
                              </el-time-select>
                            </el-form-item>
                          </el-col>
                        </el-form-item>
                        <el-form-item label="预约时长" prop="Period">
                          <el-col :span="20">
                            <el-select
                              v-model="AppointmentInfoRuleForm.Period"
                              placeholder="请选择预约时长"
                              :disabled="(currentItme.Status == '30' || currentItme.Status == '20') && handleIndex == '1'"
                              clearable
                            >
                              <el-option :label="item.time" :value="item.value" v-for="(item, index) in timeArr" :key="index"> </el-option>
                            </el-select>
                          </el-col>
                        </el-form-item>
                        <!-- <el-form-item
                      prop="Servicer"
                      required
                      :rules="[{ message: '请选择预约服务人员', trigger: ['blur', 'change'] }]"
                      label-width="0px"
                      class="custom-form-servicer-error"
                    > -->
                        <el-form-item v-for="item in addServicerEmployeeList" :key="'addServicerID' + item.ServicerID" :label="item.ServicerName">
                          <el-col :span="20">
                            <el-select v-model="item.SelectEmployeeID" placeholder="请选择员工" clearable @change="changeAppointmentServicer">
                              <el-option
                                :label="item.EmployeeName"
                                :value="item.EmployeeID"
                                v-for="item in item.Employee"
                                :key="'addEmployee' + item.EmployeeID"
                                :disabled="(currentItme.Status == '30' || currentItme.Status == '20') && handleIndex == '1'"
                              >
                              </el-option>
                            </el-select>
                          </el-col>
                        </el-form-item>
                        <!-- </el-form-item> -->
                        <el-form-item label="商家备注" prop="Remark">
                          <el-col :span="20">
                            <el-input
                              type="textarea"
                              :rows="4"
                              v-model="AppointmentInfoRuleForm.Remark"
                              placeholder="商家备注不超过200个字"
                              :disabled="(currentItme.Status == '30' || currentItme.Status == '20') && handleIndex == '1'"
                            >
                            </el-input>
                          </el-col>
                        </el-form-item>
                      </el-form>
                    </el-row>
                  </el-scrollbar>
                </div>
              </el-col>
              <el-col :span="8" :offset="1" class="right_item">
                <div style="height: 420px" class="back_f8 radius5">
                  <el-scrollbar class="el-scrollbar_height" style="height: 100%">
                    <el-tag v-for="(item, index) in appointmentProjectList" :key="index" type="primary" class="marlt_5 martp_5">{{
                      item.Name || item.ProjectName
                    }}</el-tag>
                  </el-scrollbar>
                </div>
              </el-col>
            </el-row>
          </el-tab-pane>
          <el-tab-pane label="修改记录" name="record">
            <el-table :data="appointmentRecordList" size="small">
              <el-table-column prop="CreatedByName" label="修改人"></el-table-column>
              <el-table-column prop="CreatedOn" label="修改日期"></el-table-column>
              <el-table-column prop="ActionType" label="修改后状态" :formatter="actionTypeFormatter"></el-table-column>
            </el-table>
          </el-tab-pane>
        </el-tabs>
      </div>
      <div v-else>
        <!-- 预约顾客信息  -->
        <el-row>
          <el-col :span="15">
            <el-autocomplete
              popper-class="customer-autocomplete"
              prefix-icon="el-icon-user-solid"
              v-model="customerName"
              style="width: 100%"
              placeholder="请输入客户名称、手机号、编号查找，无匹配按回车新增"
              :fetch-suggestions="saleCustomerData"
              @select="handleCustomerSelect"
              :disabled="CustomerID != null"
              :trigger-on-focus="false"
              :hide-loading="true"
              :highlight-first-item="true"
              :select-when-unmatched="true"
              size="small"
            >
              <template slot="append">
                <el-button icon="el-icon-delete" @click="removeCustomer" :disabled="!isAdd"></el-button>
              </template>
              <template slot-scope="{ item }">
                <div class="name">
                  {{ item.Name }}
                  <el-tag size="mini" v-if="item.CustomerLevelName">{{ item.CustomerLevelName }}</el-tag>
                </div>
                <div class="info">手机号：{{ item.PhoneNumber | hidephone }}</div>
                <div class="info" v-if="item.Code">客户编号：{{ item.Code }}</div>
                <div class="info" v-if="item.EntityName">所属组织：{{ item.EntityName }}</div>
                <div class="info" v-if="item.ChannelName">渠道信息：{{ item.ChannelName }}</div>
              </template>
            </el-autocomplete>
          </el-col>
          <el-col :span="8" :offset="1" class="back_f8 dis_flex flex_y_center radius5 line_height_38 pad_0_10">
            <el-col :span="12" class="back_f8">预约项目</el-col>
            <el-col :span="12" class="text_right">
              <el-button
                type="text"
                size="small"
                @click="addAppointmentProject"
                :disabled="(currentItme.Status == '30' || currentItme.Status == '20') && handleIndex == '1'"
                >添 加
              </el-button>
            </el-col>
          </el-col>
        </el-row>

        <el-row class="martp_10">
          <el-col :span="15">
            <div style="height: 420px" class="back_f8 radius5">
              <el-scrollbar class="el-scrollbar_height" style="height: 100%">
                <el-row class="padtp_15">
                  <el-form :model="AppointmentInfoRuleForm" :rules="AppointmentInfoRules" ref="AppointmentInfoRuleForm" label-width="120px" size="small">
                    <el-form-item v-if="appointmentTypeList.length > 0" label="预约类型">
                      <el-col :span="20">
                        <el-select v-model="AppointmentInfoRuleForm.AppointmentTypeID" placeholder="请选择预约类型" clearable>
                          <el-option
                            :label="item.Name"
                            :value="item.ID"
                            v-for="item in appointmentTypeList"
                            :key="'appointmentType' + item.ID"
                            :disabled="(currentItme.Status == '30' || currentItme.Status == '20') && handleIndex == '1'"
                          >
                          </el-option>
                        </el-select>
                      </el-col>
                    </el-form-item>
                    <el-form-item label="预约时间" style="margin-bottom: 0px" required>
                      <el-col :span="12">
                        <el-form-item prop="AppointmentDate">
                          <el-date-picker
                            v-model="AppointmentInfoRuleForm.AppointmentDate"
                            value-format="yyyy-MM-dd"
                            placeholder="选择日期时间"
                            size="small"
                            @change="handleAppointmentDateChange"
                          >
                          </el-date-picker>
                        </el-form-item>
                      </el-col>

                      <el-col :span="12">
                        <el-form-item prop="Time">
                          <el-time-select
                            v-model="AppointmentInfoRuleForm.Time"
                            :picker-options="{
                              start: appointmentConfigInfo.StartTime,
                              step: '00:' + appointmentConfigInfo.Period,
                              end: appointmentConfigInfo.endTimeApp_,
                            }"
                            placeholder="选择时间"
                          >
                          </el-time-select>
                        </el-form-item>
                      </el-col>
                    </el-form-item>
                    <el-form-item label="预约时长" prop="Period">
                      <el-col :span="20">
                        <el-select
                          v-model="AppointmentInfoRuleForm.Period"
                          placeholder="请选择预约时长"
                          :disabled="(currentItme.Status == '30' || currentItme.Status == '20') && handleIndex == '1'"
                          clearable
                        >
                          <el-option :label="item.time" :value="item.value" v-for="(item, index) in timeArr" :key="index"> </el-option>
                        </el-select>
                      </el-col>
                    </el-form-item>
                    <!-- <el-form-item
                      prop="Servicer"
                      required
                      :rules="[{ message: '请选择预约服务人员', trigger: ['blur', 'change'] }]"
                      label-width="0px"
                      class="custom-form-servicer-error"
                    > -->
                    <el-form-item v-for="item in addServicerEmployeeList" :key="'addServicerID' + item.ServicerID" :label="item.ServicerName">
                      <el-col :span="20">
                        <el-select v-model="item.SelectEmployeeID" placeholder="请选择员工" clearable @change="changeAppointmentServicer">
                          <el-option
                            :label="item.EmployeeName"
                            :value="item.EmployeeID"
                            v-for="item in item.Employee"
                            :key="'addEmployee' + item.EmployeeID"
                            :disabled="(currentItme.Status == '30' || currentItme.Status == '20') && handleIndex == '1'"
                          >
                          </el-option>
                        </el-select>
                      </el-col>
                    </el-form-item>
                    <!-- </el-form-item> -->
                    <el-form-item label="商家备注" prop="Remark">
                      <el-col :span="20">
                        <el-input
                          type="textarea"
                          :rows="4"
                          v-model="AppointmentInfoRuleForm.Remark"
                          placeholder="商家备注不超过200个字"
                          :disabled="(currentItme.Status == '30' || currentItme.Status == '20') && handleIndex == '1'"
                        >
                        </el-input>
                      </el-col>
                    </el-form-item>
                  </el-form>
                </el-row>
              </el-scrollbar>
            </div>
          </el-col>
          <el-col :span="8" :offset="1" class="right_item">
            <div style="height: 420px" class="back_f8 radius5">
              <el-scrollbar class="el-scrollbar_height" style="height: 100%">
                <el-tag v-for="(item, index) in appointmentProjectList" :key="index" type="primary" class="marlt_5 martp_5">{{
                  item.Name || item.ProjectName
                }}</el-tag>
                <!-- <div class="dis_flex flex_x_between padlt_10 padtp_5 flex_y_center" v-for="(item, index) in appointmentProjectList"
                  :key="index">
                  <span>{{ item.Name || item.ProjectName }}</span>
                </div> -->
              </el-scrollbar>
            </div>
          </el-col>
        </el-row>
      </div>

      <span slot="footer" class="dialog-footer" v-if="!isAdd">
        <el-button
          type="success"
          @click="saveAppointment('editLoading')"
          size="small"
          :disabled="(AppointmentInfoRuleForm.Status == '30' || AppointmentInfoRuleForm.Status == '20') && handleIndex == '1'"
          v-prevent-click
          :loading="editLoading"
          >保存</el-button
        >
        <el-button
          type="primary"
          @click="saveUpdateAppointment('confirmLoading')"
          size="small"
          :disabled="(AppointmentInfoRuleForm.Status == '30' || AppointmentInfoRuleForm.Status == '20') && handleIndex == '1'"
          v-prevent-click
          :loading="confirmLoading"
          >确认到店</el-button
        >
        <el-button
          type="danger"
          @click="clickPutOrder"
          size="small"
          :disabled="(AppointmentInfoRuleForm.Status == '30' || AppointmentInfoRuleForm.Status == '20') && handleIndex == '1'"
          v-prevent-click
          >开单</el-button
        >
      </span>
      <span slot="footer" class="dialog-footer" v-else>
        <el-button @click="appointmentDialogShow = false" v-prevent-click size="small">取消</el-button>
        <el-button type="primary" @click="saveAppointment" v-prevent-click size="small" :loading="saveLoading">保存 </el-button>
      </span>
    </el-dialog>
    <!--选择项目-->
    <el-dialog :visible.sync="selectProjectDialogState" title="选择预约项目" width="900px">
      <template>
        <el-row>
          <el-col :span="8">
            <el-input placeholder="输入项目名称进行搜索" v-model="filterText" size="small" clearable></el-input>
            <el-scrollbar class="el-scrollbar_height martp_5">
              <el-tree
                class="filter-tree"
                :data="projectList"
                show-checkbox
                node-key="PID"
                ref="treeRef"
                accordion
                highlight-current
                :props="defaultProps"
                :default-checked-keys="defaultCheckedKeysApplyApp"
                :filter-node-method="filterNode"
                @check="selectApplicableItems"
              >
                <span slot-scope="{ data }">
                  <span>{{ data.Name }}</span>
                  <el-tag plain class="marlt_5" size="mini" v-if="!data.IsProject">分类</el-tag>
                </span>
              </el-tree>
            </el-scrollbar>
          </el-col>
          <el-col :span="15" :offset="1" class="border_left">
            <el-table
              size="small"
              :data="selectedTableData.filter((data) => !filterText || data.Name.toLowerCase().includes(filterText.toLowerCase()))"
              max-height="500px"
            >
              <el-table-column prop="Name" label="项目名称"></el-table-column>
              <el-table-column label="操作" width="80px">
                <template slot-scope="scope">
                  <el-button type="danger" size="small" @click="deleteSelectRow(scope.row, scope.$index)">删除 </el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-col>
        </el-row>
      </template>
      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="selectProjectDialogState = false" v-prevent-click>取 消</el-button>
        <el-button type="primary" size="small" @click="confirmProjectSelect" v-prevent-click>确 认</el-button>
      </div>
    </el-dialog>

    <!--新增 客户-->
    <add-customer
      title="新增客户"
      :visible.sync="isAddCustom"
      :customerPhoneNumber="addCustomerPhoneNumber"
      @addCustomerSuccess="addCustomerSuccess"
    ></add-customer>
    <!--预约提醒-->
    <el-dialog :visible.sync="customerAppointmentDialogVisible" title="提示" width="500px">
      <div>本客户今日已预约，是否继续添加预约</div>

      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="customerAppointmentDialogVisible = false" v-prevent-click>取 消</el-button>
        <el-button type="primary" size="small" @click="customerAppointmentAllDialogVisible = true" v-prevent-click>查看预约记录</el-button>
        <el-button type="primary" size="small" @click="confirmCustomerAppointment" v-prevent-click>确 认</el-button>
      </div>
    </el-dialog>
    <!--客户预约记录-->
    <el-dialog :visible.sync="customerAppointmentAllDialogVisible" title="预约记录" width="1200px">
      <el-table size="small" :data="customerAppointmentAll">
        <el-table-column prop="CustomerName" label="客户姓名"></el-table-column>
        <el-table-column prop="PhoneNumber" label="客户手机号">
          <template slot-scope="scope">
            {{ scope.row.PhoneNumber | hidephone }}
          </template>
        </el-table-column>
        <el-table-column prop="LevelName" label="会员等级"></el-table-column>
        <el-table-column prop="ChannelName" label="渠道"></el-table-column>
        <el-table-column v-if="appointmentTypeList.length > 0" prop="AppointmentTypeName" label="预约类型"> </el-table-column>
        <el-table-column prop="Servicer" label="接待人" width="150px">
          <template slot-scope="scope">
            <el-popover placement="top-start" width="200" trigger="hover">
              <el-descriptions :column="1" size="small" border>
                <el-descriptions-item v-for="item in scope.row.Servicer" :key="'Servicer' + item.ServicerID" :label="item.ServicerName">{{
                  servicerNames(item.Employee)
                }}</el-descriptions-item>
              </el-descriptions>
              <div slot="reference" style="text-overflow: ellipsis; white-space: nowrap; overflow: hidden">
                <span v-if="scope.row.Servicer.length > 0">
                  <span v-if="scope.row.Servicer[0].Employee">{{ scope.row.Servicer[0].ServicerName }}：</span>
                  <span>{{ servicerNames(scope.row.Servicer[0].Employee) }}</span>
                </span>
              </div>
            </el-popover>
          </template>
        </el-table-column>
        <el-table-column prop="Status" label="预约状态">
          <template slot-scope="scope">
            {{ scope.row.Status == 10 ? '未到店' : scope.row.Status == 20 ? '已到店' : '已取消' }}
          </template>
        </el-table-column>
        <el-table-column prop="AppointmentDate" label="预约时间"></el-table-column>
        <el-table-column prop="Period" label="预约时长（分钟）"></el-table-column>
        <el-table-column prop="ArrivalDate" label="到店时间"></el-table-column>
        <el-table-column prop="CreatedBy" label="创建人"></el-table-column>
        <el-table-column prop="CreatedOn" label="创建时间"></el-table-column>
        <el-table-column prop="Channel" label="预约来源"></el-table-column>
      </el-table>

      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="customerAppointmentAllDialogVisible = false" v-prevent-click>取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import API from '@/api/iBeauty/Appointment/appointmentView';
import APIConfig from '@/api/iBeauty/Appointment/appointmentConfig';
import APICustomer from '@/api/iBeauty/Order/saleGoods';
import validate from '@/components/js/validate';
import permission from '@/components/js/permission';
import appointmentTypeAPI from '@/api/iBeauty/Appointment/appointmentType.js';

import APICustomerLevel from '@/api/CRM/Customer/customerLevel';
const dayjs = require('dayjs');
import 'dayjs/locale/zh-cn'; // 导入本地化语言
var isSameOrBefore = require('dayjs/plugin/isSameOrBefore');
dayjs.extend(isSameOrBefore);
dayjs.locale('zh-cn');
import appointmentPanel from '@/views/iBeauty/Appointment/Component/appointmentPanel.vue';
import addCustomer from '@/views/CRM/Customer/Components/CustomerDetail/addCustomer.vue';

var Enumerable = require('linq');

export default {
  name: 'AppointmentView',
  components: {
    appointmentPanel,
    addCustomer,
  },

  data() {
    return {
      searchCustomerChannel:"",
      searchCustomerLevelID: '',
      customerAppointmentAllDialogVisible: false,
      customerAppointmentDialogVisible: false,
      customerAppointmentAll: [],
      customerAppointmentNumber: 0,
      isOperatingRecord: false,
      recordActive: 'Info',
      isAddCustom: false,
      addCustomerPhoneNumber: '',
      saveLoading: false,
      confirmLoading: false,
      editLoading: false,
      panelHeight: 700,
      detailHeight: 700,
      itemSize: {
        width: 150,
        height: 40,
      },

      panelTimeAxis: [],
      servicerList: [], //预约看板-服务人员列表
      appointment_servicerID: '',
      viewEmployeeList: [],
      addServicerEmployeeList: [],
      appointmentTypeList: [], // 预约类型列表
      appointmentConfigInfo: {
        AppointmentServicerID: null,
        EndTime: '',
        Period: 15,
        StartTime: '',
        AppointmentServicerIsRequired: false,
      }, // 预约配置信息
      isAdd: '', // 预约详情弹框标题标识符
      CreatedBy: '', //创建人
      ServicerID: '', //预约角色ID
      ServicerEmployeeID: '', //预约角色员工ID
      Channel: '', //来源    店务：PC    小程序： Miniprogram  商城： MicroMall
      CreatedOnDate: [],
      servicerEmployeeAllList: [],
      searchEmployeeAllList: [],

      excelIncomeLoading: false,
      resizeRatio: 1, //界面缩放比率
      loading: false, // 加载状态
      modalLoading: false, // 新增顾客加载状态
      handleIndex: '0', // 当前tab
      searchValue: '', // 搜索值
      SearchData: [dayjs().format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')], // 预约时间
      statusValue: '10', // 状态
      AppointmentBillID: '',
      CustomerID: null, // 顾客ID
      appointmentDialogShow: false, // 预约弹框展示状态
      selectProjectDialogState: false, // 选择项目弹框展示状态
      projectClassification: '', // 项目分类
      projectSearch: '', // 查找项目
      appointmentProjectList: [], // 预约项目列表
      projectList: [], // 项目列表数据
      currentProject: [], // 当前所选中的项目
      timeArr: [], // 预约时长数据
      employeeList: [], // 可预约员工列表
      empList: [], // 员工和顾客预约信息
      tableData: [], // 预约列表
      customerName: '', // 新增预约搜索
      filterText: '',
      defaultCheckedKeysApplyApp: [], // 默认选中的适用项目节点
      selectedTableData: [],
      appointDetails: {
        showDetails: false,
      }, // 顾客预约详情
      dataTime: [], // 时间
      Period: '', // 预约间隔
      periodArr: [],
      currentDate: dayjs().format('YYYY-MM-DD'), // 当前时间
      scrollWidth: 150,
      interval: 0,
      dividerWidth: '', // 分割线宽度
      StartTime: '', // 开始时间
      EndTime: '', // 结束时间
      StartTimeData: '', // 开始时间(选择时间)
      EndTimeData: '', // 结束时间(选择时间)
      cancelState: false, // 取消预约状态
      IsMustEmployee: true, // 添加预约时是否必选员工
      openBillState: true, // 是否可开单
      currentItme: {}, // 当前编辑数据
      customerSource: [], //会员来源
      employee: [], //营销顾问
      customerLevel: [], //顾客等级
      isContinueShow: false, // 是否继续显示 预约详情
      continueIndex: 0,
      defaultProps: {
        children: 'Child',
        label: 'Name',
      },
      AppointmentInfoRuleForm: {
        ID: '', // 预约ID
        CustomerID: '', // 顾客ID
        AppointmentTypeID: '', // 预约类型
        EmployeeID: '', // 接待人ID
        AppointmentDate: dayjs().format('YYYY-MM-DD'), // 预约日期
        Time: '', // 预约时间
        Type: '', // 接待人状态
        Status: '', // 审批状态
        Period: '', // 预约时长
        Remark: '', // 备注
        Servicer: [],
      },
      AppointmentInfoRules: {
        EmployeeID: [{ required: true, message: '请选择接待人', trigger: 'change' }],
        AppointmentDate: [{ required: true, message: '请选择预约日期', trigger: 'change' }],
        Time: [{ required: true, message: '请选择预约时间', trigger: 'change' }],
        Type: [{ required: true, message: '请选择接待人状态', trigger: 'change' }],
      },
      customer: {
        Name: '',
        PhoneNumber: '',
        Gender: '2',
        CustomerSourceID: null,
        EmployeeID: '',
        CustomerLevelID: '',
        Code: '',
      },
      customerRules: {
        Name: [{ required: true, message: '请输入客户名称', trigger: 'blur' }],
        PhoneNumber: [
          {
            required: true,
            validator: validate.validPhoneNumber,
            trigger: 'blur',
          },
        ],
        Gender: [{ required: true, message: '请选择客户性别', trigger: 'change' }],
        CustomerSourceID: [{ required: true, message: '请选择客户来源', trigger: 'change' }],
        EmployeeID: [{ required: true, message: '请选择顾问', trigger: 'change' }],
        CustomerLevelID: [{ required: true, message: '请选择客户等级', trigger: 'change' }],
        Code: [{ required: true, message: '请输入客户编号', trigger: 'blur' }],
      },
      expireTimeOption: {
        disabledDate(date) {
          //disabledDate 文档上：设置禁用状态，参数为当前日期，要求返回 Boolean
          return date.getTime() < Date.now() - 24 * 60 * 60 * 1000;
        },
      },
      paginations: {
        page: 1, // 当前位于哪页
        total: 5, // 总数
        page_size: 10, // 1页显示多少条
        layout: 'total, prev, pager, next,jumper', // 翻页属性
      },

      position: {
        x: undefined,
        y: undefined,
      },
      appointmentRecordList: [],
    };
  },
  computed: {
    /**    */
    positionStyle() {
      var that = this;
      return `top:${that.position.y}px;left:${that.position.x}px;`;
    },
  },
  methods: {
    /**  修改预约时间  */
    changeHandleAppointmentDate() {
      let that = this;
      if (that.AppointmentInfoRuleForm.AppointmentDate) {
        that.appointmentBill_ervicerEmployee();
      }
    },
    /**    */
    handleAppointmentDateChange() {
      let that = this;

      if (that.AppointmentInfoRuleForm.AppointmentDate) {
        that.appointmentBill_ervicerEmployee();
      }
      that.appointmentBill_getCustomerAppointmentNumber();
    },
    /**    */
    confirmCustomerAppointment() {
      let that = this;
      that.createAppointment();
    },
    /**    */
    actionTypeFormatter(row) {
      let actionType = {
        10: '未到店',
        20: '已到店',
        30: '已取消',
      };
      return actionType[row.ActionType];
    },
    /**  保存并确认到店  */
    saveUpdateAppointment(type) {
      let that = this;
      that.AppointmentInfoRuleForm.Status = '20';

      if (type == 'confirmLoading' && !dayjs(that.AppointmentInfoRuleForm.AppointmentDate).isSameOrBefore(dayjs(), 'day')) {
        that.$message.error('只可以确认今天或之前的预约单');
        return;
      }
      that.appointmentBillUpdate(type);
    },
    /**    */
    servicerNames(employees) {
      return employees.map((i) => i.EmployeeName).join(',');
    },

    /**  获取实际的列宽度  */
    getActualColumnWidth(employeeCount) {
      let that = this;
      // 如果没有传入员工数量，使用当前的viewEmployeeList长度
      if (!employeeCount) {
        employeeCount = that.viewEmployeeList.length;
      }

      // 尝试获取实际的DOM元素宽度
      try {
        const panelContainer = document.querySelector('.panel-gridcontent');
        if (panelContainer && employeeCount > 0) {
          const containerWidth = panelContainer.clientWidth;
          if (containerWidth > 0) {
            return Math.floor(containerWidth / employeeCount);
          }
        }
      } catch (error) {
        // 静默处理错误，使用默认值
      }
      // 如果无法获取DOM宽度或者宽度为0，使用默认值
      return that.itemSize.width || 150;
    },

    /**    */
    changeItemSizeWidth() {
      let that = this;
      that.formatEmployeeData(that.viewEmployeeList);
    },
    /**    */
    changeItemSizeHeight() {
      let that = this;
      that.formatEmployeeData(that.viewEmployeeList);
    },
    /**  修改预约角色  */
    changeServicer() {
      let that = this;
      that.appointmentBillAll(); // 预约信息
    },
    /**
     * @description:
     * @param {*} start 预约配置开始时间
     * @param {*} end 预约配置结束时间
     * @param {*} period 预约配置间隔
     * @return {*} 返回 时间集合
     */
    getPanelTimes(start, end, period) {
      let startTime = dayjs(start).valueOf();
      let endTime = dayjs(end).valueOf();
      let periodTime = period * 60 * 1000;
      let times = [];
      for (let i = startTime; i < endTime; i += periodTime) {
        times.push(dayjs(i).format('HH:mm'));
      }
      return times;
    },
    // 切换tabs
    handleClick() {
      var that = this;
      if (that.handleIndex == '0') {
        that.appointmentBillAll();
      } else {
        that.setDetailHeight();
        that.getAppointmentBillList();
      }
    },
    // 搜索
    handleSearch() {
      var that = this;
      that.paginations.page = 1;
      that.getAppointmentBillList();
    },

    // 添加预约
    addAppointment() {
      var that = this;
      that.appointmentBill_ervicerEmployee().then(() => {
        that.appointmentRecordList = [];
        that.currentItme = {};
        that.isAdd = true;
        that.AppointmentInfoRuleForm = {
          AppointmentTypeID: '', // 预约类型
          AppointmentDate: that.currentDate,
          Time: '',
          Period: that.timeArr[0].value,
          Remark: '',
          Status: '',
          Servicer: [],
        };
        that.appointmentProjectList = [];
        that.customerName = '';
        that.CustomerID = null;
        let employeeInfo = JSON.parse(localStorage.getItem('access-user'));
        that.addServicerEmployeeList.forEach((i) => {
          let empItem = i.Employee.find((j) => j.EmployeeID == employeeInfo.EmployeeID);
          if (empItem == undefined) {
            that.$set(i, 'SelectEmployeeID', null);
          } else {
            that.$set(i, 'SelectEmployeeID', empItem.EmployeeID);
          }
        });
        that.appointmentDialogShow = true;
      });
    },

    /**    */
    timeIsRest(item, axis) {
      let that = this;
      let curDate = that.currentDate + ' ' + axis.time;
      if (item.StartTime && item.EndTime) {
        let startDeta = item.ScheduleDate + ' ' + item.StartTime;
        let endDeta = item.ScheduleDate + ' ' + item.EndTime;
        let isBetween = dayjs(curDate).isBetween(startDeta, endDeta, 'minute');
        if (isBetween) {
          return false;
        } else {
          return true;
        }
      }
      return false;
    },
    /**  看板添加预约  */
    addAppiontmentClick({ column, row }) {
      let that = this;
      that.appointmentBill_ervicerEmployee().then(() => {
        that.appointmentRecordList = [];
        if (column.IsRest || that.timeIsRest(column, row)) {
          if (!that.appointmentConfigInfo.CanChooseRestEmployee) {
            that.$message.error('当前员工休息中！暂不可以添加预约');
            return;
          }
        }

        that.currentItme = {};
        that.isAdd = true;

        that.AppointmentInfoRuleForm = {
          AppointmentTypeID: '', // 预约类型
          AppointmentDate: that.currentDate,
          Time: row.time,
          Period: that.timeArr[0].value,
          Remark: '',
          Status: '',
          Servicer: [],
        };

        that.appointmentProjectList = [];
        that.customerName = '';
        that.CustomerID = null;

        that.addServicerEmployeeList.forEach((i) => {
          if (that.appointment_servicerID == i.ServicerID) {
            that.$set(i, 'SelectEmployeeID', column.EmployeeID);
          } else {
            that.$set(i, 'SelectEmployeeID', null);
          }
        });

        that.appointmentDialogShow = true;
      });
    },
    // 适用项目弹框搜索事件
    filterNode(value, data) {
      if (!value) return true;
      return data.Name.indexOf(value) !== -1;
    },

    // 选择顾客
    handleCustomerSelect(item) {
      var filter_hidephone = this.$options.filters['hidephone'];
      if (item.ID != undefined) {
        this.CustomerID = item.ID;
        this.customerFullName = item.Name;
        this.customerPhoneNumber = item.PhoneNumber;
        if (item.PhoneNumber) {
          this.customerName = item.Name + '【' + filter_hidephone(item.PhoneNumber) + '】';
        } else {
          this.customerName = item.Name;
        }
        this.appointmentBill_getCustomerAppointmentNumber();
      } else {
        if (/^1[3456789]\d{9}$/.test(this.customerName)) {
          this.addCustomerPhoneNumber = this.customerName;
        }
        this.addNewCustomer();
      }
    },
    // 新增顾客
    addNewCustomer() {
      var that = this;
      that.isAddCustom = true;
    },
    /**   新增客户成功 */
    addCustomerSuccess(info) {
      var filter_hidephone = this.$options.filters['hidephone'];
      let that = this;
      that.CustomerID = info.ID;
      that.customerFullName = info.Name;
      that.customerPhoneNumber = info.PhoneNumber;

      if (info.PhoneNumber) {
        this.customerName = info.Name + '【' + filter_hidephone(info.PhoneNumber) + '】';
      } else {
        this.customerName = info.Name;
      }
    },
    removeCustomer() {
      this.CustomerID = null;
      this.customerFullName = '';
      this.customerPhoneNumber = '';
      this.customerName = '';
      // this.customerChange();
    },
    // 添加预约项目
    addAppointmentProject() {
      var that = this;
      that.selectedTableData = Object.assign([], that.appointmentProjectList);
      that.defaultCheckedKeysApplyApp = [];

      that.selectProjectDialogState = true;
      that.$nextTick(() => {
        var defaultCheckedKeys = Enumerable.from(that.appointmentProjectList)
          .select((val) => '1' + val.ID)
          .toArray();
        that.$refs.treeRef.setCheckedKeys(defaultCheckedKeys);
      });
    },
    // 选择适用项目事件
    selectApplicableItems(item, list) {
      var that = this;
      that.selectedTableData = Enumerable.from(list.checkedNodes)
        .where(function (i) {
          return i.IsProject;
        })
        .select((item) => ({
          ID: item.ID,
          Name: item.Name,
          PID: item.PID,
          ParentID: item.ParentID,
          Price: item.Price,
          ProjectCategoryName: item.ProjectCategoryName,
          TreatTime: item.TreatTime,
        }))
        .toArray();
    },
    // 选择项目确认
    confirmProjectSelect() {
      var that = this;
      var selectedTableData = Object.assign([], that.selectedTableData);
      if (that.selectedTableData.length == 0) {
        that.$message.error('请选择项目');
        return false;
      } else {
        var totalPeriod = 0;
        that.appointmentProjectList = Object.assign([], selectedTableData);
        that.selectProjectDialogState = false;
        that.appointmentProjectList.forEach((val) => {
          totalPeriod += val.TreatTime;
        });
        for (let i = 0; i <= that.timeArr.length - 1; i++) {
          if (that.timeArr[i].value >= totalPeriod) {
            that.AppointmentInfoRuleForm.Period = that.timeArr[i].value;
            break;
          }
        }
      }
    },
    // 删除所选中的适用项目
    deleteSelectRow(row, index) {
      var that = this;
      that.selectedTableData.splice(index, 1);
      that.$nextTick(() => {
        var defaultCheckedKeys = Enumerable.from(that.selectedTableData)
          .select((val) => val.PID)
          .toArray();
        that.$refs.treeRef.setCheckedKeys(defaultCheckedKeys);
      });
    },

    /** 获取 预约时长  */
    getDateAppointmentIntervalList(period) {
      let that = this;
      let startTimestamp = dayjs(dayjs(that.currentDate).format('YYYY-MM-DD') + ' ' + '00:' + period).valueOf();
      let endTimestamp = dayjs(dayjs(that.currentDate).format('YYYY-MM-DD') + ' ' + '08:00').valueOf();
      let periodTimestamp = Number(period) * 60 * 1000; // 间隔的毫秒
      let interValue = period;

      for (let index = startTimestamp; index <= endTimestamp; index += periodTimestamp) {
        if (interValue < 60) {
          that.timeArr.push({
            time: dayjs(index).format('mm分钟'), // 时间轴显示字符串
            value: interValue,
          });
        } else {
          that.timeArr.push({
            time: dayjs(index).format('H小时mm分钟'), // 时间轴显示字符串
            value: interValue,
          });
        }
        interValue += period;
      }
    },
    /**  修改预约服务角色人员  */
    changeAppointmentServicer() {
      // let that = this;
      // that.AppointmentInfoRuleForm.Servicer = that.addServicerEmployeeList
      //   .filter((i) => i.SelectEmployeeID)
      //   .map((val) => {
      //     return {
      //       ServicerID: val.ServicerID,
      //       EmployeeID: val.SelectEmployeeID,
      //     };
      //   }); //预约角色集合
    },

    // 保存预约
    saveAppointment(type) {
      var that = this;
      if (!that.CustomerID) {
        that.$message.error('请选择客户');
        return;
      }
      if (that.appointmentConfigInfo.AppointmentServicerIsRequired && that.addServicerEmployeeList.every((i) => !i.SelectEmployeeID)) {
        that.$message.error('请选择预约接待人');
        return;
      }

      if (that.appointmentDialogShow) {
        that.$refs['AppointmentInfoRuleForm'].validate((valid) => {
          if (valid) {
            if (!that.isAdd) {
              that.appointmentBillUpdate(type);
            } else {
              if (that.customerAppointmentNumber > 0) {
                that.customerAppointmentDialogVisible = true;
              } else {
                that.createAppointment();
              }
            }
          }
        });
      } else {
        if (!that.isAdd) {
          that.appointmentBillUpdate(type);
        } else {
          if (that.customerAppointmentNumber > 0) {
            that.customerAppointmentDialogVisible = true;
          } else {
            that.createAppointment();
          }
        }
      }
    },
    // 递归
    setRecursion(data) {
      var that = this;
      for (let i = 0; i <= data.length - 1; i++) {
        if (data[i].IsProject) {
          data[i].PID = '1' + data[i].ID;
        } else {
          data[i].PID = '0' + data[i].ID;
        }
        if (data[i].Child) {
          that.setRecursion(data[i].Child);
        }
      }
    },

    // 前一天
    upClick: function () {
      let that = this;
      that.currentDate = dayjs(that.currentDate).subtract(1, 'day').format('YYYY-MM-DD');
      that.AppointmentInfoRuleForm.AppointmentDate = that.currentDate;
      that.appointmentBillAll();
    },
    // 后一天
    downClick: function () {
      var that = this;
      that.currentDate = dayjs(that.currentDate).add(1, 'day').format('YYYY-MM-DD');
      that.AppointmentInfoRuleForm.AppointmentDate = that.currentDate;
      that.appointmentBillAll();
    },
    // 日期改变
    getAppointmentBillAll() {
      var that = this;
      that.AppointmentInfoRuleForm.AppointmentDate = that.currentDate;
      that.appointmentBillAll();
    },

    // 处理自动刷新事件
    handleAutoRefresh() {
      var that = this;
      // 只有在预约看板页面时才自动刷新
      if (that.handleIndex === '0') {
        console.log('预约看板自动刷新中...');
        that.appointmentBillAll();
      }
    },
    // 分页
    handleCurrentChange(page) {
      var that = this;
      that.paginations.page = page;
      that.getAppointmentBillList();
    },
    // 修改预约状态
    updateStatus(data, type) {
      var that = this;
      var item = data;
      that.AppointmentInfoRuleForm.ID = item.ID;
      that.AppointmentInfoRuleForm.CustomerID = item.CustomerID;
      that.AppointmentInfoRuleForm.EmployeeID = item.EmployeeID;
      let appointmentDates = item.AppointmentDate.split(' ');
      that.AppointmentInfoRuleForm.AppointmentDate = appointmentDates[0];
      that.AppointmentInfoRuleForm.AppointmentTime = appointmentDates[1];
      that.AppointmentInfoRuleForm.Type = item.Type;
      that.AppointmentInfoRuleForm.Period = item.Period;
      that.AppointmentInfoRuleForm.Remark = item.Remark;

      that.customerName = item.CustomerName;
      that.appointmentProjectList = item.Project;
      that.CustomerID = item.CustomerID;

      that.isAdd = false;
      if (type == 0) {
        this.$confirm('确定要取消预约吗？?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })
          .then(() => {
            that.AppointmentInfoRuleForm.Status = 30;
            that.cancelState = true;
            that.saveAppointment();
          })
          .catch(() => {
            this.$message({
              type: 'info',
              message: '已取消操作',
            });
          });
      } else {
        this.$confirm('确认已完成?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })
          .then(() => {
            that.AppointmentInfoRuleForm.Status = 20;
            that.cancelState = false;
            that.saveAppointment();
          })
          .catch(() => {
            this.$message({
              type: 'info',
              message: '已取消操作',
            });
          });
      }
    },
    // 取消预约
    cancelAppointment() {
      var that = this;
      this.$confirm('确定要取消预约吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          that.appointmentBill_updateStatus(that.AppointmentInfoRuleForm.ID, '30').then(() => {
            that.appointmentDialogShow = false;
          });
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消操作',
          });
        });
    },
    // 取消预约
    cancelAppiontmentClick(item) {
      var that = this;
      that
        .$confirm('确定要取消预约吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })
        .then(() => {
          that.appointmentBill_updateStatus(item.ID, '30');
        })
        .catch(() => {
          that.$message({
            type: 'info',
            message: '已取消操作',
          });
        });
    },
    /**  编辑预约  */
    editAppiontmentClick(item) {
      let that = this;
      that.appointmentBill_ervicerEmployee().then(() => {
        that.getAppointmentBillInfo(item.ID);
        that.isAdd = false;
      });
    },
    /**   确认到店 */
    confrimAppiontmentClick(item) {
      let that = this;
      if (!dayjs(item.AppointmentDate).isSameOrBefore(dayjs(), 'day')) {
        that.$message.error('只可以确认今天或之前的预约单');
        return;
      }

      that
        .$confirm('确定顾客已到店了吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })
        .then(() => {
          that.appointmentBill_updateStatus(item.ID, '20');
        })
        .catch(() => {
          that.$message({
            type: 'info',
            message: '已取消操作',
          });
        });
    },

    // 开单按钮点击事件
    clickPutOrder() {
      var that = this;
      that.appointmentDialogShow = false;
      that.$router.push({
        path: '/Order/Bill',
        name: 'Bill',
        params: { customerID: that.CustomerID },
      });
    },
    /**  添加修改预约是修改预约时间  */
    changeAppointmentDate() {
      let that = this;
      that.appointmentBill_ervicerEmployee();
    },

    /**••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••*/
    // 创建预约
    createAppointment() {
      var that = this;
      let params = {
        AppointmentDate: that.AppointmentInfoRuleForm.AppointmentDate + ' ' + that.AppointmentInfoRuleForm.Time, //预约时间
        CustomerID: that.CustomerID, //顾客iD
        Period: that.AppointmentInfoRuleForm.Period, //时长
        AppointmentTypeID: that.AppointmentInfoRuleForm.AppointmentTypeID, //预约类型
        Remark: that.AppointmentInfoRuleForm.Remark, //备注
        Servicer: that.addServicerEmployeeList
          .filter((i) => i.SelectEmployeeID)
          .map((val) => {
            return {
              ServicerID: val.ServicerID,
              EmployeeID: val.SelectEmployeeID,
            };
          }), //预约角色集合

        Project: that.appointmentProjectList.map((val) => {
          return {
            ProjectID: val.ID,
          };
        }), //项目集合
      };
      that.saveLoading = true;
      API.appointmentBillCreate(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.$message.success({
              message: '成功创建预约',
              duration: 2000,
            });
            that.appointmentDialogShow = false;
            that.customerAppointmentDialogVisible = false;
            if (that.handleIndex == '0') {
              that.appointmentBillAll();
            } else {
              that.getAppointmentBillList();
            }
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.saveLoading = false;
        });
    },
    // 修改预约
    appointmentBillUpdate(type) {
      let that = this;
      let params = Object.assign({}, that.AppointmentInfoRuleForm);
      params.CustomerID = that.CustomerID; //顾客iD
      params.AppointmentDate = dayjs(that.AppointmentInfoRuleForm.AppointmentDate).format('YYYY-MM-DD') + ' ' + that.AppointmentInfoRuleForm.Time; //预约时间
      params.Servicer = that.addServicerEmployeeList
        .filter((i) => i.SelectEmployeeID)
        .map((val) => {
          return {
            ServicerID: val.ServicerID,
            EmployeeID: val.SelectEmployeeID,
          };
        }); //预约角色集合
      params.Project = that.appointmentProjectList.map((i) => {
        return {
          ProjectID: i.ID,
        };
      });
      that[type] = true;
      API.appointmentBillUpdate(params)
        .then((res) => {
          if (res.StateCode == 200) {
            if (that.cancelState) {
              this.$message({
                type: 'success',
                message: '取消成功',
              });
              that.cancelState = false;
            } else {
              this.$message({
                type: 'success',
                message: '修改成功',
              });
            }
            that.appointmentDialogShow = false;
            if (that.handleIndex == '0') {
              that.appointmentBillAll();
            } else {
              that.getAppointmentBillList();
            }
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that[type] = false;
        });
    },
    // 获取预约列表
    getAppointmentBillList() {
      var that = this;
      that.loading = true;
      var params = {
        PageNum: that.paginations.page,
        Name: that.searchValue,
        AppointmentBillID: that.AppointmentBillID,
        StartDate: that.SearchData != null ? that.SearchData[0] : '',
        EndDate: that.SearchData != null ? that.SearchData[1] : '',
        Status: that.statusValue,

        CreatedBy: that.CreatedBy, //创建人
        CreatedOnStartDate: that.CreatedOnDate && that.CreatedOnDate[0] ? that.CreatedOnDate[0] : '', //创建开始时间
        CreatedOnEndDate: that.CreatedOnDate && that.CreatedOnDate[1] ? that.CreatedOnDate[1] : '', //创建结束时间
        ServicerID: that.ServicerID, //预约角色ID
        ServicerEmployeeID: that.ServicerEmployeeID, //预约角色员工ID
        Channel: that.Channel, //来源    店务：PC    小程序： Miniprogram  商城： MicroMall
        CustomerLevelID: that.searchCustomerLevelID,
        CustomerChannel:that.searchCustomerChannel
      };
      API.appointmentBillList(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.tableData = res.List;
            that.paginations.total = res.Total;
            that.paginations.page_size = res.PageSize;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },
    // 获取预约详情
    getAppointmentBillInfo(ID) {
      var that = this;
      that.loading = true;
      var params = {
        ID: ID,
      };
      API.appointmentBillInfo(params)
        .then((res) => {
          if (res.StateCode == 200) {
            let item = res.Data;
            that.AppointmentInfoRuleForm.Status = item.Status;
            that.AppointmentInfoRuleForm.ID = item.ID;
            that.CustomerID = item.CustomerID;
            that.customerName = item.CustomerName;
            let filter_hidephone = that.$options.filters['hidephone'];
            if (item.PhoneNumber) {
              that.customerName = item.CustomerName + '【' + filter_hidephone(item.PhoneNumber) + '】';
            } else {
              this.customerName = item.CustomerName;
            }
            that.AppointmentInfoRuleForm.AppointmentDate = dayjs(item.AppointmentDate).format('YYYY-MM-DD');
            that.AppointmentInfoRuleForm.Time = dayjs(item.AppointmentDate).format('HH:mm');
            that.AppointmentInfoRuleForm.AppointmentTypeID = item.AppointmentTypeID;
            that.AppointmentInfoRuleForm.Period = item.Period;
            that.AppointmentInfoRuleForm.Remark = item.Remark;
            that.appointmentProjectList = item.Project.map((i) => {
              return {
                Name: i.ProjectName,
                ID: i.ProjectID,
              };
            });
            that.addServicerEmployeeList.forEach((i) => {
              that.$set(i, 'SelectEmployeeID', null);
              item.Servicer.forEach((j) => {
                if (j.ServicerID == i.ServicerID) {
                  that.$set(i, 'SelectEmployeeID', j.Employee[0].EmployeeID);
                }
              });
            });
            that.appointmentDialogShow = true;
            if (that.isOperatingRecord) {
              that.appointmentBill_process(ID);
            }
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
          that.loading = false;
        })
        .finally(function () {
          that.loading = false;
        });
    },
    /**  修改预约状态  */
    async appointmentBill_updateStatus(ID, Status) {
      let that = this;
      try {
        let params = {
          ID: ID, //订单号
          Status: Status, //状态
        };
        let res = await API.appointmentBill_updateStatus(params);
        if (res.StateCode == 200) {
          that.$message.success('操作成功');
          that.appointmentBillAll();
        } else {
          that.$message.error(res.Message);
        }
      } catch (error) {
        that.$message.error(error);
      }
    },

    /**   导出明细 */
    async excelappointmentBill() {
      let that = this;
      if (that.SearchData && that.SearchData[0]) {
        that.excelIncomeLoading = true;
        var params = {
          PageNum: that.paginations.page,
          Name: that.searchValue,
          AppointmentBillID: that.AppointmentBillID,
          StartDate: that.SearchData != null ? that.SearchData[0] : '',
          EndDate: that.SearchData != null ? that.SearchData[1] : '',
          Status: that.statusValue,
          CreatedBy: that.CreatedBy, //创建人
          CreatedOnStartDate: that.CreatedOnDate && that.CreatedOnDate[0] ? that.CreatedOnDate[0] : '', //创建开始时间
          CreatedOnEndDate: that.CreatedOnDate && that.CreatedOnDate[1] ? that.CreatedOnDate[1] : '', //创建结束时间
          ServicerID: that.ServicerID, //预约角色ID
          ServicerEmployeeID: that.ServicerEmployeeID, //预约角色员工ID
          Channel: that.Channel, //来源    店务：PC    小程序： Miniprogram  商城： MicroMall
          CustomerLevelID: that.searchCustomerLevelID,
          CustomerChannel:that.searchCustomerChannel
        };
        let res = await API.excelappointmentBill(params);
        that.$message.success({
          message: '正在导出',
          duration: '4000',
        });
        const link = document.createElement('a');
        let blob = new Blob([res], { type: 'application/octet-stream' });
        link.style.display = 'none';
        link.href = URL.createObjectURL(blob);
        link.download = '预约明细.xlsx'; //下载的文件名
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        this.excelIncomeLoading = false;
      } else {
        this.$message.error('请选择预约时间');
      }
    },

    /* 获取预约配置 */
    getAppointmentConfig() {
      var that = this;
      that.loading = true;
      var params = {};
      APIConfig.appointmentConfig(params)
        .then((res) => {
          if (res.StateCode == 200) {
            let dayStr = dayjs().format('YYYY-MM-DD');
            that.appointmentConfigInfo = res.Data;
            that.appointmentConfigInfo.endTimeApp_ = dayjs(dayStr + ' ' + res.Data.EndTime)
              .subtract(that.appointmentConfigInfo.Period, 'minute')
              .format('HH:mm');
            that.panelTimeAxis = that.getPanelTimes(
              dayStr + ' ' + that.appointmentConfigInfo.StartTime,
              dayStr + ' ' + that.appointmentConfigInfo.EndTime,
              that.appointmentConfigInfo.Period
            );
            that.getDateAppointmentIntervalList(that.appointmentConfigInfo.Period);
            that.appointmentBillAll(); // 预约信息
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },
    // 获取预约信息
    appointmentBillAll() {
      let that = this;
      that.loading = true;
      let params = {
        AppointmentDate: that.currentDate,
        ServicerID: that.appointment_servicerID,
      };
      API.appointmentBillAll(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.formatEmployeeData(res.Data);
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },
    /**    */
    formatEmployeeData(data) {
      let that = this;
      let curMinutes = dayjs().format('HH:mm');
      let curTime = dayjs(that.currentDate + ' ' + curMinutes).valueOf();
      that.viewEmployeeList = data.map((i) => {
        /* 设置 列日期 禁选状态 、 添加预约状态 */
        i.timeAxis = that.panelTimeAxis.map((j) => {
          let tmpTime = dayjs(dayjs().format('YYYY-MM-DD') + ' ' + j + ':00').valueOf();
          let disabled = false;
          if (dayjs().isSame(dayjs(curTime), 'day')) {
            disabled = curTime >= tmpTime;
          } else {
            if (dayjs().isBefore(dayjs(curTime))) {
              disabled = dayjs(curTime).isBefore(dayjs(tmpTime));
            } else {
              //当前日期之前
              disabled = dayjs(tmpTime).isAfter(dayjs(curTime));
            }
          }
          return {
            time: j,
            showState: false,
            disabled: disabled,
          };
        });

        /* 排序  */
        i.Appointment = i.Appointment.sort((a, b) => {
          let a_time = dayjs(a.AppointmentDate).valueOf();
          let b_time = dayjs(b.AppointmentDate).valueOf();
          if (a_time > b_time) {
            return 1;
          }
          if (a_time < b_time) {
            return -1;
          }
          return 0;
        });

        /* 设置 定位 坐标 */
        i.Appointment.map((j, index, array) => {
          let findIndex = that.panelTimeAxis.findIndex((f) => {
            let ss = dayjs(j.AppointmentDate).format('HH:mm');
            return f == ss;
          });

          if (findIndex == -1) {
            let findIndex_ =
              that.panelTimeAxis.findIndex((f) => {
                let ss = dayjs(j.AppointmentDate).subtract(15, 'minute').format('HH:mm');
                return f == ss;
              }) + 0.5;

            j.top = Math.abs(findIndex_ * that.itemSize.height) + 1;
          } else {
            j.top = Math.abs(findIndex * that.itemSize.height) + 1;
          }

          /* 高度已最低的间隔 15 分钟计算  that.appointmentConfigInfo.Period */
          let ratio = 15 / that.appointmentConfigInfo.Period;
          j.height = (j.Period / 15) * that.itemSize.height * ratio - 3;

          /* 开始时间戳 */
          let curStartTime = dayjs(j.AppointmentDate).valueOf();
          /* 结束时间戳 */
          // let curEndTime = dayjs(j.AppointmentDate).valueOf() + j.Period * 60 * 1000;

          // 动态计算每列的实际宽度，传入当前数据的员工数量
          let actualColumnWidth = that.getActualColumnWidth(data.length);

          j.left = 1;
          j.width = actualColumnWidth - 3;
          /* 当前开始 时间 大于 之前预约信息 的 结束时间 */
          let before = array
            .filter((t, bIndex) => {
              /* 开始时间戳 */
              // let tStartTime = dayjs(t.AppointmentDate).valueOf();
              /* 结束时间戳 */
              let tEndTime = dayjs(t.AppointmentDate).valueOf() + t.Period * 60 * 1000;
              return index >= bIndex && curStartTime < tEndTime;
            })
            .map((t, tIndex, tArray) => {
              let tmpLeft = (actualColumnWidth / tArray.length) * tIndex;
              let tmpWidth = actualColumnWidth / tArray.length - 2;
              if (t.width > tmpWidth) {
                t.left = tmpLeft;
                t.width = tmpWidth;
              }
              return t;
            });
          before.map((t, tIndex) => {
            if (tIndex == 0 && t.left != 0) {
              t.left == 0;
            }
            return t;
          });

          return j;
        });

        return i;
      });
    },

    /**  重新计算预约事件的宽度  */
    recalculateAppointmentWidths() {
      let that = this;
      let actualColumnWidth = that.getActualColumnWidth();

      // 如果获取到的宽度和默认宽度不同，重新计算所有预约事件的宽度
      if (actualColumnWidth !== that.itemSize.width) {
        that.viewEmployeeList.forEach((employee) => {
          if (employee.Appointment && employee.Appointment.length > 0) {
            employee.Appointment.forEach((appointment, index, array) => {
              let curStartTime = dayjs(appointment.AppointmentDate).valueOf();

              appointment.left = 1;
              appointment.width = actualColumnWidth - 3;

              let before = array
                .filter((t, bIndex) => {
                  let tEndTime = dayjs(t.AppointmentDate).valueOf() + t.Period * 60 * 1000;
                  return index >= bIndex && curStartTime < tEndTime;
                })
                .map((t, tIndex, tArray) => {
                  let tmpLeft = (actualColumnWidth / tArray.length) * tIndex;
                  let tmpWidth = actualColumnWidth / tArray.length - 2;
                  if (t.width > tmpWidth) {
                    t.left = tmpLeft;
                    t.width = tmpWidth;
                  }
                  return t;
                });

              before.map((t, tIndex) => {
                if (tIndex == 0 && t.left != 0) {
                  t.left = 0;
                }
                return t;
              });
            });
          }
        });
      }
    },
    /**  预约看板-服务人员  */
    async appointmentBill_servicer() {
      let that = this;
      try {
        let params = {};
        let res = await API.appointmentBill_servicer(params);
        if (res.StateCode == 200) {
          that.servicerList = res.Data;
          if (that.servicerList.length > 0) {
            that.appointment_servicerID = that.servicerList[0].ServicerID;
            that.getAppointmentConfig();
          }
        } else {
          that.$message.error(res.Message);
        }
      } catch (error) {
        that.$message.error(error);
      }
    },
    /** 服务人员员工 -新增修改时用  */
    async appointmentBill_ervicerEmployee() {
      let that = this;
      try {
        let params = {
          AppointmentDate: dayjs(that.AppointmentInfoRuleForm.AppointmentDate).format('YYYY-MM-DD'),
        };
        let res = await API.appointmentBill_ervicerEmployee(params);
        if (res.StateCode == 200) {
          that.servicerEmployeeAllList = res.Data.reduce((per, cur) => {
            let tmp = cur.Employee.filter((i) => {
              return !per.some((j) => j.EmployeeID == i.EmployeeID);
            });
            return [...per, ...tmp];
          }, []);
          that.addServicerEmployeeList = res.Data;
        } else {
          that.$message.error(res.Message);
        }
      } catch (error) {
        that.$message.error(error);
      }
    },
    /**  获取预约类型  */
    async appointmentType_all() {
      let that = this;
      try {
        let params = {
          Name: '', //搜索名称
          Active: true, //有效性
        };
        let res = await appointmentTypeAPI.appointmentType_all(params);
        if (res.StateCode == 200) {
          that.appointmentTypeList = res.Data;
        } else {
          that.$message.error(res.Message);
        }
      } catch (error) {
        that.$message.error(error);
      }
    },
    // 获取项目列表
    getProjectList() {
      var that = this;
      that.loading = true;
      var params = {
        Name: '',
      };
      API.getProjectList(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.setRecursion(res.Data);
            that.projectList = Enumerable.from(res.Data)
              .where((i) => {
                if (!i.IsProject) {
                  i.Child = Enumerable.from(i.Child)
                    .where((i) => {
                      return !i.IsProject && i.Child.length > 0;
                    })
                    .toArray();
                }
                return !i.IsProject && i.Child.length > 0;
              })
              .toArray();
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },
    // 顾客数据
    saleCustomerData(queryString, cb) {
      var that = this;
      that.loading = true;
      var params = {
        Name: queryString ? queryString : '',
      };
      APICustomer.getSaleCustomer(params)
        .then((res) => {
          if (res.StateCode == 200) {
            cb(res.Data);
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },

    getWindowInfo() {
      let that = this;
      if (that.$refs.appointmentRef) {
        that.$nextTick(() => {
          that.panelHeight = that.$refs.appointmentRef.offsetHeight - 30 - 45; //100
          // 窗口大小改变时重新计算预约事件宽度
          if (that.handleIndex === '0' && that.viewEmployeeList.length > 0) {
            that.recalculateAppointmentWidths();
          }
        });
      }
    },
    /**  设置明细table 高度   */
    setDetailHeight() {
      let that = this;
      if (that.$refs.detailHeader) {
        that.$nextTick(() => {
          let headerHeight = that.$refs.detailHeader.offsetHeight;
          let paginationHeight = that.$refs.detailpagination.offsetHeight;
          that.detailHeight = that.panelHeight - headerHeight - paginationHeight;
        });
      }
    },
    /**    */
    async appointmentBill_process(AppointmentBillID) {
      let that = this;
      try {
        let params = {
          AppointmentBillID: AppointmentBillID,
        };
        let res = await API.appointmentBill_process(params);
        if (res.StateCode == 200) {
          that.appointmentRecordList = res.Data;
        } else {
          that.$message.error(res.Message);
        }
      } catch (error) {
        that.$message.error(error);
      }
    },
    /**    */
    async employee_all() {
      let that = this;
      try {
        let params = {};
        let res = await API.employee_all(params);
        if (res.StateCode == 200) {
          that.searchEmployeeAllList = res.Data;
        } else {
          that.$message.error(res.Message);
        }
      } catch (error) {
        that.$message.error(error);
      }
    },
    /**    */
    appointmentBill_getCustomerAppointmentNumber() {
      let that = this;
      if (!that.CustomerID) {
        return;
      }
      let params = {
        CustomerID: that.CustomerID,
        AppointmentDate: that.AppointmentInfoRuleForm.AppointmentDate,
      };
      API.appointmentBill_getCustomerAppointmentNumber(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.customerAppointmentNumber = res.Data.CustomerAppointmentNumber;
            if (that.customerAppointmentNumber > 0) {
              that.appointmentBill_getCustomerAppointmentAll();
            }
          } else {
            that.$message.error(res.Message);
          }
        })
        .catch((fail) => {
          that.$message.error(fail);
        });
    },
    /**    */
    appointmentBill_getCustomerAppointmentAll() {
      let that = this;
      if (!that.CustomerID) {
        return;
      }
      let params = {
        CustomerID: that.CustomerID,
        AppointmentDate: that.AppointmentInfoRuleForm.AppointmentDate,
      };
      API.appointmentBill_getCustomerAppointmentAll(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.customerAppointmentAll = res.Data;
          } else {
            that.$message.error(res.Message);
          }
        })
        .catch((fail) => {
          that.$message.error(fail);
        });
    },
    /* 顾客等级 */
    CustomerLevelData() {
      var that = this;
      var params = {
        Name: '',
        Active: true,
      };
      APICustomerLevel.getCustomerLevel(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.customerLevel = res.Data;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },
  },

  watch: {
    filterText(val) {
      var that = this;
      that.$refs.treeRef.filter(val);
    },
  },
  beforeRouteEnter(to, from, next) {
    next((vm) => {
      vm.openBillState = permission.routerPermission('/Order/Bill');
      if (from.fullPath == '/iBeautyCustomer/Customer') {
        if (to.params.CustomerID == undefined || to.params.CustomerID == null || to.params.CustomerID == '') {
          return;
        } else {
          vm.isAdd = true;
          vm.AppointmentInfoRuleForm.AppointmentDate = vm.currentDate;
          vm.customerName = to.params.customerName;
          vm.CustomerID = to.params.CustomerID;
          vm.AppointmentInfoRuleForm.EmployeeID = '';
          vm.AppointmentInfoRuleForm.AppointmentTime = '';
          vm.AppointmentInfoRuleForm.Type = '10';
          vm.AppointmentInfoRuleForm.Status = '';
          vm.AppointmentInfoRuleForm.Period = vm.timeArr[0].value;
          vm.AppointmentInfoRuleForm.Remark = '';
          vm.appointmentProjectList = [];
          vm.appointmentDialogShow = true;
        }
      }

      vm.isOperatingRecord = permission.permission(to.meta.Permission, 'iBeauty-Appointment-AppointmentView-OperatingRecord');
    });
  },
  mounted() {
    var that = this;
    that.isOperatingRecord = permission.permission(that.$route.meta.Permission, 'iBeauty-Appointment-AppointmentView-OperatingRecord');
    window.addEventListener('resize', that.getWindowInfo);
    that.panelHeight = that.$refs.appointmentRef.offsetHeight - 30 - 45; //100

    // var time = new Date();
    // that.currentDate = date.formatDate.format(new Date(time), "YYYY-MM-DD");

    that.appointmentBill_servicer(); // 顶部状态栏服务角色列表
    that.appointmentBill_ervicerEmployee(); // 新增预约是服务列表
    that.appointmentType_all();
    that.getAppointmentBillList(); // 预约列表
    that.getProjectList(); // 项目列表
    that.employee_all();
    that.CustomerLevelData();
  },

  beforeDestroy() {
    // 移除窗口大小改变事件监听器
    window.removeEventListener('resize', this.getWindowInfo);
  },
};
</script>

<style lang="scss">
.AppointmentView {
  max-height: calc(100% - 30px);

  .appointmentBar {
    position: absolute !important;
    // top: 6px;
    right: -10px;
    // width: 900px;
    z-index: 1;
    height: 39px;
    justify-content: center;
    align-items: center;
    display: flex;

    .el-form-item {
      margin-bottom: 0px;

      .el-form-item__content {
        height: 33px;
        justify-content: center;
        align-items: center;
        display: flex;
      }
    }
  }

  .appointmentDesClass {
    right: 100px;
    top: 10px;
    z-index: 1;
    width: 500px;
  }

  .addAppointmentBtn {
    right: 15px;
    top: 10px;
    z-index: 1;
  }

  .right_item {
    background: #f8f8f8;
    // overflow-y: auto;
  }

  .project_item {
    height: 100px;
    /*height: 70%;*/
    overflow-y: scroll;
  }

  .checked_group {
    width: 100%;
  }

  .normal_right {
    border-right: 1px solid #c0c4cc;
    border-bottom: 1px solid #c0c4cc;
    border-top: 1px solid #c0c4cc;
    font-size: 14px;
    padding: 5px 20px;
    color: #333;
    height: 40px;
    line-height: 40px;
  }

  .normal_left {
    border-left: 1px solid #c0c4cc;
    border-bottom: 1px solid #c0c4cc;
    border-top: 1px solid #c0c4cc;
    font-size: 14px;
    padding: 5px 20px;
    color: #333;
    height: 40px;
    line-height: 40px;
  }

  .squareBlock_orange {
    width: 14px;
    height: 14px;
    background: var(--zl-color-orange-primary);
  }

  .squareBlock_gray {
    width: 14px;
    height: 14px;
    background: var(--zl-appointment-success-border);
  }

  .squareBlock_white {
    width: 14px;
    height: 14px;
    background: white;
    border: 1px solid #d3d3d3;
  }

  .squareBlock_rest {
    width: 14px;
    height: 14px;
    color: #c8c9cc;
    cursor: not-allowed;
    background: repeating-linear-gradient(45deg, #d3d3d3, #d3d3d3 2px, #e5e5e5 0, #e5e5e5 5px);
    background-repeat: repeat-y;
    border: 1px solid #ebedf0;
  }

  .squareBlock_purple {
    width: 20px;
    height: 20px;
    background: #cc99cc;
  }

  .squareBlock_green {
    width: 20px;
    height: 20px;
    background: #91c47e;
  }

  .arrow_box {
    width: 30px;
    height: 30px;
  }

  .tdd {
    display: table-cell;
    vertical-align: middle;
    height: 100%;
    z-index: 10;
    position: relative;
  }

  .line_height_38 {
    line-height: 35px;
  }

  .el-scrollbar_height {
    height: 60vh;

    .el-scrollbar__wrap {
      overflow-x: hidden;
    }
  }
}

.customer-autocomplete {
  li {
    line-height: normal;
    padding: 7px;

    .name {
      text-overflow: ellipsis;
      overflow: hidden;
    }

    .info {
      font-size: 12px;
      color: #b4b4b4;
    }

    .highlighted .info {
      color: #ddd;
    }
  }
}

:root {
  --zl-appointment-success: #e9f8ee;
  --zl-appointment-success-border: #45c57b;
  --zl-appointment-other: #fff7f3;
}

.custom_channelPopperClass {
  .el-select-dropdown__item {
    line-height: normal;
    height: auto;
  }
}

.el-tabs--border-card {
  border: 0px !important;
  box-shadow: 0 0px 0px 0 rgba(0, 0, 0, 0), 0 0 0px 0 rgba(0, 0, 0, 0);
}

.custom-form-servicer-error {
  .el-form-item__error {
    margin-left: 120px;
  }
}
</style>
