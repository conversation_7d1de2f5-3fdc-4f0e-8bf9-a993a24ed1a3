<template>
  <div class="workbenchMedicalRecord" v-loading="loading">
    <el-row>
      <el-col :offset="16" :span="8" class="text_right padrt_10">
        <el-button v-if="CustomerCases.Add" @click="addMedicalRecordFormClick" v-prevent-click size="small" type="primary">新增</el-button>
        <el-button v-if="CustomerCases.Update" @click="editMedicalRecordFormClick" v-prevent-click size="small" type="primary">编辑</el-button>
        <el-button v-if="CustomerCases.Delete" @click="removeMedicalRecordFormClick" v-prevent-click size="small" type="primary">删除</el-button>
      </el-col>
    </el-row>
    <el-table :data="medicalRecord_list" @row-click="medicalRecordRowClick" highlight-current-row size="small" height="250px" class="martp_5" ref="medicalRef">
      <el-table-column label="病历记录名称" prop="Name" width="150"></el-table-column>
      <el-table-column label="顾客名称" prop="CustomerName"></el-table-column>
      <el-table-column label="性别" prop="Gender" :formatter="medicalFormatterValue"></el-table-column>

      <el-table-column label="年龄" prop="Age"></el-table-column>
      <el-table-column label="病历状态" prop="MedicalRecordStatus" :formatter="medicalFormatterValue"></el-table-column>
      <el-table-column label="病历编号" prop="MedicalRecordCode" width="150"></el-table-column>
      <el-table-column label="病历号" prop="MedicalRecordNumber"></el-table-column>
      <el-table-column label="医疗证号" prop="MedicCertificateNumber" width="130"></el-table-column>

      <el-table-column label="医院名称" prop="EntityName" width="150"></el-table-column>
      <el-table-column label="科室" prop="DepartmentName"></el-table-column>
      <el-table-column label="主治医生" prop="AttendingDoctorName"></el-table-column>
      <el-table-column label="就诊时间" prop="TreatmentDate" width="130"></el-table-column>
      <el-table-column label="费别" prop="CostCategory" :formatter="medicalFormatterValue"></el-table-column>
      <el-table-column label="备注" prop="Remark" width="150" show-overflow-tooltip></el-table-column>
      <el-table-column label="创建人" prop="CreatedByName"></el-table-column>
      <el-table-column label="创建时间" prop="CreatedOn" width="130"></el-table-column>
    </el-table>
    <div class="pad_0_10 text_right">
      <el-pagination background v-if="paginations.total > 0" @current-change="handleCurrentChange" :current-page.sync="paginations.page" :page-size="paginations.page_size" :layout="paginations.layout" :total="paginations.total"></el-pagination>
    </div>
    <el-tabs v-model="medicalActiveName" class="medicalRecordTabsClass" @tab-click="medicalActiveChange">
      <el-tab-pane label="病历详情" name="detail">
        <el-row class="font_13">
          <el-col :span="8" style="line-height: 36px">{{ currentItemDetail.Name }}</el-col>
          <el-col :span="16" :offset="currentItemDetail.Name ? 0 : 8" class="text_right padrt_10">
            <el-button v-if="CustomerCases.SelectStencil" @click="selectMedicalRecordTemplateClick(1)" v-prevent-click size="small" type="primary">选择模板</el-button>
            <el-button v-if="CustomerCases.SaveStencil" @click="saveMedicalRecordTemplateClick" v-prevent-click :loading="detailSaveLoading" size="small" type="primary" :disabled="medicalDisable">保存</el-button>
            <el-button v-if="CustomerCases.SelectStencil" @click="printMedicalRecordTemplateClick" v-prevent-click size="small" type="primary">打印</el-button>
            <el-button v-if="CustomerCases.DeleteStencil" @click="clearMedicalRecordTemplateClick" v-prevent-click size="small" type="primary">清空</el-button>
          </el-col>
        </el-row>
        <el-row class="font_13 medicalRecordContent">
          <el-col :span="6" class="border pad_10 medicalRecordContent-left">
            <el-scrollbar class="menuSelectedClass-scrollbar">
              <el-tree :data="currentItemDetail.RecordList" node-key="ID" highlight-current ref="recodeListRef">
                <div @click="clickMedicalRecordTemplateListItem(node, data)" slot-scope="{ node, data }" class="dis_flex flex_x_between padrt_10 flex_y_center" style="width: 100%">
                  <div style="width: 200px" class="dis_flex flex_y_center">
                    <i v-if="!data.isChild" class="el-icon-folder-opened"></i>
                    <i v-else class="el-icon-document-remove"></i>
                    <div class="clamp1 marlt_5">{{ data.MedicalRecordTemplateName }}</div>
                  </div>
                  <div>
                    <i v-if="CustomerCases.Delete" @click.stop.prevent="removeMedicalRecordTemplateClick(data)" v-prevent-click class="el-icon-delete-solid font_16"></i>
                  </div>
                </div>
              </el-tree>
            </el-scrollbar>
          </el-col>
          <el-col :span="18" class="medicalRecordContent-right">
            <div id="medicalRecordEditor"></div>
          </el-col>
        </el-row>
      </el-tab-pane>
      <el-tab-pane label="同意书" name="agreement">
        <el-row class="font_13">
          <el-col :span="8" style="line-height: 36px">{{ currentItemDetail.Name }}</el-col>
          <el-col :span="16" :offset="currentItemDetail.Name ? 0 : 8" class="text_right padrt_10">
            <el-button v-if="CustomerCases.SelectStencil" @click="selectMedicalRecordTemplateClick(2)" v-prevent-click size="small" type="primary">选择模板</el-button>
            <el-button v-if="CustomerCases.SaveStencil" @click="saveMedicalRecordTemplateClick" v-prevent-click :loading="detailSaveLoading" size="small" type="primary" :disabled="agreementDisable">保存</el-button>
            <el-button @click="printMedicalRecordTemplateClick" v-prevent-click size="small" type="primary">打印</el-button>
            <el-button v-if="CustomerCases.DeleteStencil" @click="clearMedicalRecordTemplateClick" v-prevent-click size="small" type="primary">清空</el-button>
          </el-col>
        </el-row>
        <el-row class="font_13 medicalRecordContent">
          <el-col :span="6" class="border pad_10 medicalRecordContent-left">
            <el-scrollbar class="menuSelectedClass-scrollbar">
              <el-tree :data="currentItemDetail.ConsentList" node-key="ID" highlight-current ref="medicalRecordContentRef">
                <div @click="clickMedicalRecordTemplateListItem(node, data)" slot-scope="{ node, data }" class="dis_flex flex_x_between padrt_10 flex_y_center" style="width: 100%">
                  <div style="width: 200px" class="dis_flex flex_y_center">
                    <i v-if="!data.isChild" class="el-icon-folder-opened"></i>
                    <i v-else class="el-icon-document-remove"></i>
                    <div class="clamp1 marlt_5">{{ data.MedicalRecordTemplateName }}</div>
                  </div>
                  <div>
                    <i v-if="CustomerCases.Delete" @click.stop.prevent="removeMedicalRecordTemplateClick(data)" v-prevent-click class="el-icon-delete-solid font_16"></i>
                  </div>
                </div>
              </el-tree>
            </el-scrollbar>
          </el-col>
          <el-col :span="18" class="medicalRecordContent-right">
            <div id="medicalRecordConsentEditor"></div>
          </el-col>
        </el-row>
      </el-tab-pane>
    </el-tabs>

    <el-dialog :title="isAdd ? '新增病历' : '编辑病历'" :visible.sync="showMedicalRecordForm" append-to-body width="1000px" inline>
      <el-form :model="ruleForm" :rules="rules" size="small" label-width="100px" ref="ruleFormRef">
        <el-row>
          <el-col :span="8">
            <el-form-item label="病历状态" prop="MedicalRecordStatus">
              <el-select v-model="ruleForm.MedicalRecordStatus" @change="changeMedicalRecordStatus" placeholder="请选择病历状态">
                <el-option label="初诊病历" value="FST"></el-option>
                <el-option label="复诊病历" value="FID"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="病历号" prop="MedicalRecordNumber">
              <el-input v-model="ruleForm.MedicalRecordNumber" :disabled="true" placeholder="请输入病历号">
                <el-button @click="customerMedicalRecord_getMedicalRecordNumber" v-prevent-click v-if="!ruleForm.MedicalRecordNumber" slot="append">取</el-button>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="病历编号" prop="MedicalRecordCode">
              <el-input v-model="ruleForm.MedicalRecordCode" :disabled="true" placeholder="请输入病历编号">
                <!-- <template slot="append">取</template> -->
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="病历名称" prop="Name">
              <el-input v-model="ruleForm.Name" placeholder="请输入病历名称"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="主治医生" prop="AttendingDoctor">
              <el-select v-model="ruleForm.AttendingDoctor" filterable placeholder="请选择主治医生">
                <el-option v-for="item in doctorList" :label="item.Name" :value="item.ID" :key="item.ID"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="就诊科室" prop="DepartmentID">
              <el-select v-model="ruleForm.DepartmentID" filterable placeholder="请选择就诊科室">
                <el-option v-for="item in department" :label="item.Name" :value="item.ID" :key="item.ID"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="医疗证号" prop="MedicCertificateNumber">
              <el-input v-model="ruleForm.MedicCertificateNumber" placeholder="请输入医疗证号"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="费别" prop="CostCategory">
              <el-select v-model="ruleForm.CostCategory" placeholder="请选择费别">
                <el-option label="医保" value="YB"></el-option>
                <el-option label="非医保" value="FYB"></el-option>
                <el-option label="自费" value="ZF"></el-option>
                <el-option label="住院" value="STA"></el-option>
                <el-option label="门诊" value="MEN"></el-option>
                <el-option label="手术室" value="DOC"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="治疗日期" prop="TreatmentDate">
              <el-date-picker v-model="ruleForm.TreatmentDate" type="date" placeholder="治疗日期" value-format="yyyy-MM-dd"> </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="16">
            <el-form-item label="备注" prop="Remark" placeholder="">
              <el-input v-model="ruleForm.Remark" type="textarea"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <span slot="footer" class="dialog-footer">
        <el-button @click="showMedicalRecordForm = false" size="small" v-prevent-click>取 消</el-button>
        <el-button type="primary" size="small" @click="saveMedicalRecordFormClick" v-prevent-click :loading="modalLoading">保 存</el-button>
      </span>
    </el-dialog>

    <el-dialog title="选择病历模板" :visible.sync="showSelectMedicalRecordTemplate" append-to-body width="1500px" class="selectMedicakRecordClass">
      <el-row class="padtp_5">
        <el-col :span="6">
          <div class="padbm_5">
            <el-select v-model="recordTemplateTypeID" size="small" @change="changeTemplateType">
              <el-option v-for="item in medicalRecord_type_list" :key="item.ID" :label="item.Name" :value="item.ID"></el-option>
            </el-select>
          </div>
          <div class="left-menu">
            <el-scrollbar class="left-scrollbar-height">
              <!-- :default-expanded-keys="defaultExpandedKeys" -->
              <el-tree :data="medicalRecord_template_list" node-key="ID" show-checkbox check-strictly highlight-current @check-change="changeCheckClick" ref="templateRef">
                <div @click="selectTemplateMenuClick(node, data)" slot-scope="{ node, data }" class="dis_flex flex_x_between padrt_10 flex_y_center" style="width: 100%">
                  <div class="dis_flex flex_y_center">
                    <i class="el-icon-document-remove"></i>
                    <div class="clamp1 marlt_5">{{ data.Name }}</div>
                  </div>
                </div>
              </el-tree>
            </el-scrollbar>
          </div>
          <div class="menuSelectedClass">
            <el-scrollbar class="menuSelectedClass-scrollbar">
              <el-tree :data="selectTemplates" node-key="ID" highlight-current>
                <div @click="selectTemplateMenuClick(node, data)" slot-scope="{ node, data }" class="dis_flex flex_x_between padrt_10 flex_y_center" style="width: 100%">
                  <div class="dis_flex flex_y_center">
                    <i v-if="!data.isChild" class="el-icon-folder-opened"></i>
                    <i v-else class="el-icon-document-remove"></i>
                    <div class="clamp1 marlt_5">{{ data.Name }}</div>
                  </div>
                  <div>
                    <i @click="removeSelectTemplatesItem(data)" v-prevent-click class="el-icon-delete-solid font_16"></i>
                  </div>
                </div>
              </el-tree>
            </el-scrollbar>
          </div>
        </el-col>
        <el-col :span="18" class="edit-wrapper">
          <div id="selectMedicalRecordEditor"></div>
        </el-col>
      </el-row>

      <span slot="footer" class="dialog-footer">
        <el-button @click="showSelectMedicalRecordTemplate = false" size="small" v-prevent-click>取 消</el-button>
        <el-button type="primary" size="small" @click="saveMedicalRecordTempalteClick" v-prevent-click :loading="modalLoading">保 存</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import API from "@/api/iBeauty/Workbench/workbenchMedicalRecord.js";
export default {
  name: "workbenchMedicalRecord",
  props: {
    CustomerID: {
      type: Number,
      require: true,
    },
    CustomerName: {
      type: String,
      require: true,
    },
    CustomerCases: {
      Type: Boolean,
      default() {
        return {
          Add: false,
          Update: false,
          Delete: false,
          SelectStencil: false,
          SaveStencil: false,
          DeleteStencil: false,
        };
      },
    },
  },
  /** 监听数据变化   */
  watch: {},
  /**  引入的组件  */
  components: {},
  /**  Vue 实例的数据对象**/
  data() {
    return {
      medicalDisable: false,
      agreementDisable: false,
      loading: false,
      detailSaveLoading: false,
      initEditer: true,
      showSelectMedicalRecordTemplate: false,
      medicalActiveName: "detail",
      modalLoading: false,
      showMedicalRecordForm: false,
      ruleForm: {
        Name: "", //病历名称
        MedicalRecordStatus: "", //病历状态（FST：初诊病历、FID：复诊病历）
        MedicalRecordCode: "", //病历编号:
        MedicalRecordNumber: "", //病历号
        MedicCertificateNumber: "", //医疗证号
        DepartmentID: "", //科室
        AttendingDoctor: "", //主治医生
        CostCategory: "ZF", //费别（YB：医保、FYB：非医保、ZF：自费、STA：住院、MEN：门诊、DOC：手术室）
        TreatmentDate: "", //就诊时间
        Remark: "", //备注
      },
      rules: {
        MedicalRecordStatus: [{ required: true, message: "请选择病历状态", trigger: ["blur", "change"] }],
        MedicalRecordNumber: [{ required: true, message: "请输入病历号", trigger: ["blur", "change"] }],
        MedicalRecordCode: [{ required: true, message: "请输入病历编号", trigger: ["blur", "change"] }],
        Name: [{ required: true, message: "请输入病历名称", trigger: ["blur", "change"] }],
        AttendingDoctor: [{ required: true, message: "请选择主治医生", trigger: ["blur", "change"] }],
        DepartmentID: [{ required: true, message: "请选择就诊科室", trigger: ["blur", "change"] }],
      },

      paginations: {
        page: 1, // 当前位于哪页
        total: 0, // 总数
        page_size: 10, // 1页显示多少条
        layout: "total, prev, pager, next,jumper", // 翻页属性
      },
      doctorList: [],
      department: [],
      isAdd: false,
      medicalRecord_list: [],
      FormatterValue: {
        Gender: {
          0: "未知",
          1: "男",
          2: "女",
        },
        MedicalRecordStatus: {
          FST: "初诊病历",
          FID: "复诊病历",
        },
        CostCategory: {
          YB: "医保",
          FYB: "非医保",
          ZF: "自费",
          STA: "住院",
          MEN: "门诊",
          DOC: "手术室",
        },
      },
      currentItem: "",
      currentItemDetail: "",
      medicalRecordEditor: "",
      docDataConsent: {},
      consentDocDataConsent: {},
      templateType: "",
      recordTemplateTypeID: "",
      medicalRecord_type_list: [],
      medicalRecord_template_list: [],
      currentNodeKey: "",
      // defaultExpandedKeys: [],
      selectMedicalRecordEditor: "",
      selectTemplates: [],
      medicalRecordConsentEditor: "",
      currentTemplateItem: "FST",

      editorPaperSetting: {
        size: "A4", // 纸张尺寸，目前只有两个值：A4 和 A5
        marginX: 10, //打印时，左右两边的边距，单位：毫米
        marginTop: 20, //打印时，上边的边距，单位：毫米
        marginBottom: 0, //打印时，下边的边距，单位：毫米
        headerContent: "", //页面的html内容
        showHeader: false, //是否展示页眉
      },
      fieldDataConsent: {
        ctm_name: "",
        ctm_sex: "" /* 性别 */,
        ctm_age: "" /* 年龄 */,
        ctm_mz: "" /* 客户民族 */,
        ctm_mobile: "" /* 联系电话 */,
        ctm_id_code: "" /* 客户身份证 */,
        S_AddressPCCS: "" /*  客户住址*/,
        ctm_wktype: "" /* 职业 */,
        ctm_code: "" /*客户卡号  */,
        HospitalName: "" /* 医院名称 */,
        HospitalizationEnableState1: "" /* '(门诊)床号 */,
        O_MedicRecordFileNumber: "" /* 病历号 */,
        O_MedicRecordCode: "" /*  病历编号 */,
        O_CostCategory: "" /* 费别 */,
        O_TreatmentDate: "" /* 治疗日期 */,
        O_MedicRecordVisitDepartmentId: "" /* 就诊科室 */,
        O_MedicRecordVisitDoctorId: "" /* 就诊医生 */,
        O_MedicCertificateNumber: "" /* 医疗证号 */,
        O_CtfStatus: "" /* 病历状态 */,
        O_Title: "" /* 病历名称 */,
        ctm_datebirth: "" /* 出生日期 */,
        H_Exp3: "" /* 国籍 */,
      },
      medicalRecordStatus: "",
    };
  },
  /**计算属性  */
  computed: {},
  /**  方法集合  */
  methods: {
    /**    */
    medicalActiveChange() {
      let that = this;
      if (that.medicalActiveName == "detail" && that.currentItemDetail.RecordList.length > 0) {
        that.medicalDisable = true;
        let item = that.currentItemDetail.RecordList[0];
        that.medicalRecordEditor.resetDocContent("");
        that.customerMedicalRecord_getMedicalRecordDetail(item.ID).then((data) => {
          that.currentTemplateItem = data;
          that.$nextTick(() => {
            let DocContent = decodeURIComponent(data.MedicalRecordContent);

            that.medicalRecordEditor.setDocContent(DocContent);
            Object.assign(that.docDataConsent, {});
            Object.assign(that.fieldDataConsent, {});

            /* 编辑器页眉和纸张内容设置 */
            let decodes = data.TemplatePrintJSON ? JSON.parse(data.TemplatePrintJSON) : "";
            if (decodes) {
              decodes.headerContent = decodes.headerContent ? decodeURIComponent(decodes.headerContent) : "";
              that.medicalRecordEditor.setPaper(decodes);
              that.medicalRecordEditor.setHeaderContent(decodes.headerContent);
              that.editorPaperSetting = decodes;
            }

            let MedicalRecordData = decodeURIComponent(data.MedicalRecordDate) ? JSON.parse(decodeURIComponent(data.MedicalRecordDate)) : "";
            Object.assign(that.docDataConsent, MedicalRecordData);

            let tempData = {
              ctm_name: that.currentItemDetail.CustomerName,
              ctm_sex: that.FormatterValue["Gender"][that.currentItemDetail.Gender] /* 性别 */,
              ctm_age: that.currentItemDetail.Age /* 年龄 */,
              ctm_mz: "" /* 客户民族 */,
              ctm_mobile: that.currentItemDetail.PhoneNumber /* 联系电话 */,
              ctm_id_code: that.currentItemDetail.IdentityCard /* 客户身份证 */,
              S_AddressPCCS: that.currentItemDetail.Address /*  客户住址*/,
              ctm_wktype: that.currentItemDetail.Job /* 职业 */,
              ctm_code: that.currentItemDetail.Code /*客户卡号  */,
              HospitalName: that.currentItemDetail.EntityName /* 医院名称 */,
              HospitalizationEnableState1: "" /* '(门诊)床号 */,
              O_MedicRecordFileNumber: that.currentItemDetail.MedicalRecordNumber /* 病历号 */,
              O_MedicRecordCode: that.currentItemDetail.MedicalRecordCode /*  病历编号 */,
              O_CostCategory: that.FormatterValue["CostCategory"][that.currentItemDetail.CostCategory] /* 费别 */,
              O_TreatmentDate: that.currentItemDetail.TreatmentDate /* 治疗日期 */,
              O_MedicRecordVisitDepartmentId: that.currentItemDetail.DepartmentName /* 就诊科室 */,
              O_MedicRecordVisitDoctorId: that.currentItemDetail.AttendingDoctorName ? that.currentItemDetail.AttendingDoctorName : that.currentItemDetail.AttendingDoctorName /* 就诊医生 */,
              O_MedicCertificateNumber: that.currentItemDetail.MedicCertificateNumber ? that.currentItemDetail.MedicCertificateNumber : that.currentItemDetail.EntityID /* 医疗证号 */,
              O_CtfStatus: that.FormatterValue["MedicalRecordStatus"][that.currentItemDetail.MedicalRecordStatus] /* 病历状态 */,
              O_Title: that.currentItemDetail.Name /* 病历名称 */,
              ctm_datebirth: "" /* 出生日期 */,
              H_Exp3: "" /* 国籍 */,
            };

            Object.assign(that.fieldDataConsent, tempData); //设置字段值
            that.$refs.recodeListRef.setCurrentKey(data);
            that.medicalDisable = false;
            that.loading = false;
          });
        });
      } else if (that.medicalActiveName == "agreement" && that.currentItemDetail.ConsentList.length > 0) {
        that.agreementDisable = true;
        let item = that.currentItemDetail.ConsentList[0];
        that.medicalRecordConsentEditor.resetDocContent("");
        that.customerMedicalRecord_getMedicalRecordDetail(item.ID).then((data) => {
          that.currentTemplateConsentItem = data;
          that.$nextTick(() => {
            let DocContent = decodeURIComponent(data.MedicalRecordContent);

            that.medicalRecordConsentEditor.setDocContent(DocContent);
            Object.assign(that.consentDocDataConsent, {});
            Object.assign(that.fieldDataConsent, {});

            /* 编辑器页眉和纸张内容设置 */
            let decodes = data.TemplatePrintJSON ? JSON.parse(data.TemplatePrintJSON) : "";
            if (decodes) {
              decodes.headerContent = decodes.headerContent ? decodeURIComponent(decodes.headerContent) : "";
              that.medicalRecordConsentEditor.setPaper(decodes);
              that.medicalRecordConsentEditor.setHeaderContent(decodes.headerContent);
              that.editorPaperSetting = decodes;
            }

            let MedicalRecordData = decodeURIComponent(data.MedicalRecordDate) ? JSON.parse(decodeURIComponent(data.MedicalRecordDate)) : "";
            Object.assign(that.consentDocDataConsent, MedicalRecordData);

            let tempData = {
              ctm_name: that.currentItemDetail.CustomerName,
              ctm_sex: that.FormatterValue["Gender"][that.currentItemDetail.Gender] /* 性别 */,
              ctm_age: that.currentItemDetail.Age /* 年龄 */,
              ctm_mz: "" /* 客户民族 */,
              ctm_mobile: that.currentItemDetail.PhoneNumber /* 联系电话 */,
              ctm_id_code: that.currentItemDetail.IdentityCard /* 客户身份证 */,
              S_AddressPCCS: that.currentItemDetail.Address /*  客户住址*/,
              ctm_wktype: that.currentItemDetail.Job /* 职业 */,
              ctm_code: that.currentItemDetail.Code /*客户卡号  */,
              HospitalName: that.currentItemDetail.EntityName /* 医院名称 */,
              HospitalizationEnableState1: "" /* '(门诊)床号 */,
              O_MedicRecordFileNumber: that.currentItemDetail.MedicalRecordNumber /* 病历号 */,
              O_MedicRecordCode: that.currentItemDetail.MedicalRecordCode /*  病历编号 */,
              O_CostCategory: that.FormatterValue["CostCategory"][that.currentItemDetail.CostCategory] /* 费别 */,
              O_TreatmentDate: that.currentItemDetail.TreatmentDate /* 治疗日期 */,
              O_MedicRecordVisitDepartmentId: that.currentItemDetail.DepartmentName /* 就诊科室 */,
              O_MedicRecordVisitDoctorId: that.currentItemDetail.AttendingDoctorName ? that.currentItemDetail.AttendingDoctorName : that.currentItemDetail.AttendingDoctorName /* 就诊医生 */,
              O_MedicCertificateNumber: that.currentItemDetail.MedicCertificateNumber ? that.currentItemDetail.MedicCertificateNumber : that.currentItemDetail.EntityID /* 医疗证号 */,
              O_CtfStatus: that.FormatterValue["MedicalRecordStatus"][that.currentItemDetail.MedicalRecordStatus] /* 病历状态 */,
              O_Title: that.currentItemDetail.Name /* 病历名称 */,
              ctm_datebirth: "" /* 出生日期 */,
              H_Exp3: "" /* 国籍 */,
            };

            Object.assign(that.fieldDataConsent, tempData); //设置字段值
            that.$refs.medicalRecordContentRef.setCurrentKey(data);
            that.agreementDisable = false;
            that.loading = false;
          });
        });
      } else {
        that.medicalRecordEditor.resetDocContent("");
        that.medicalRecordConsentEditor.resetDocContent("");
        that.currentTemplateItem = "";
        that.currentTemplateConsentItem = "";
        that.medicalDisabled = true;
        that.contentSaveDisabled = true;
        that.agreementDisable = true;
        that.medicalDisable = true;
      }
    },
    /**    */
    changeMedicalRecordStatus() {
      let that = this;
      let empInfo = JSON.parse(localStorage.getItem("access-user"));
      if (that.ruleForm.MedicalRecordStatus == "FST") {
        that.ruleForm.Name = `${that.CustomerName}初诊病历(${empInfo.EmployeeName}医生)`; //病历名称
      }
      if (that.ruleForm.MedicalRecordStatus == "FID") {
        that.ruleForm.Name = `${that.CustomerName}复诊病历(${empInfo.EmployeeName}医生)`; //病历名称
      }
    },
    /**  保存病历模板  */
    saveMedicalRecordTempalteClick() {
      let that = this;
      if (that.medicalActiveName == "detail") {
        let samePart = that.querySamePart(that.currentItemDetail.RecordList);
        if (samePart.length > 0) {
          let lastItem = samePart[0];
          that
            .$confirm(`病历单已有 ${lastItem.Name} 该模板的病历，是否新增该模板病历`, {
              confirmButtonText: "确定",
              cancelButtonText: "取消",
              type: "warning",
            })
            .then(() => {
              that.customerMedicalRecord_createMedicalTemplate();
            })
            .catch(() => {
              this.$message({
                type: "info",
                message: "已取消操作",
              });
            });
          return;
        } else {
          that.customerMedicalRecord_createMedicalTemplate();
        }
      } else {
        // that.currentItemDetail.ConsentList
        let samePart = that.querySamePart(that.currentItemDetail.ConsentList);
        if (samePart.length > 0) {
          let lastItem = samePart[0];
          that
            .$confirm(`同意书已有 ${lastItem.Name} 该模板的同意书，是否新增该模板同意书`, {
              confirmButtonText: "确定",
              cancelButtonText: "取消",
              type: "warning",
            })
            .then(() => {
              that.customerMedicalRecord_createMedicalTemplate();
            })
            .catch(() => {
              this.$message({
                type: "info",
                message: "已取消操作",
              });
            });
          return;
        } else {
          that.customerMedicalRecord_createMedicalTemplate();
        }
      }
    },
    /**  查询是否存在相同的 病历模板   */
    querySamePart(array) {
      let that = this;
      let samePart = that.selectTemplates.filter((i) => {
        return array.some((j) => j.MedicalRecordTemplateID == i.ID);
      });
      return samePart;
    },
    /**    */
    removeSelectTemplatesItem(data) {
      let that = this;
      let findIndex = that.selectTemplates.findIndex((i) => i.ID == data.ID);
      if (findIndex != -1) {
        let item = that.selectTemplates[findIndex];
        that.selectTemplates.splice(findIndex, 1);
        that.$refs.templateRef.setChecked(item.ID, false);
      }
    },
    /**    */
    changeCheckClick(item, nodeCheck) {
      let that = this;
      if (nodeCheck) {
        let findIndex = that.selectTemplates.findIndex((i) => i.ID == item.ID);
        if (findIndex == -1) {
          that.selectTemplates.push(item);
        }
      } else {
        let findIndex = that.selectTemplates.findIndex((i) => i.ID == item.ID);
        if (findIndex != -1) {
          that.selectTemplates.splice(findIndex, 1);
        }
      }
    },
    /**    */
    selectTemplateMenuClick(node, data) {
      let that = this;
      that.medicalRecord_getTemplate(data.ID);
    },
    /**  修改模板类型  */
    changeTemplateType() {
      let that = this;
      let item = that.medicalRecord_type_list.find((i) => i.ID == that.recordTemplateTypeID);
      that.medicalRecord_template_list = item.Template;
      that.$nextTick(() => {
        that.selectTemplates.forEach((i) => {
          that.medicalRecord_template_list.forEach((j) => {
            if (i.ID == j.ID) {
              that.$refs.templateRef.setChecked(j.ID, true);
            }
          });
        });
      });
    },
    isShowTreeTitle(data) {
      let that = this;
      return data.ID == that.lasetItemID;
    },
    /**  清空病历内容  */
    clearMedicalRecordTemplateClick() {
      let that = this;
      if (that.medicalActiveName == "detail") {
        that.medicalRecordEditor.clearData();
      } else {
        that.medicalRecordConsentEditor.clearData();
      }
    },
    /**  打印病历  */
    printMedicalRecordTemplateClick() {
      let that = this;
      if (that.medicalActiveName == "detail") {
        that.medicalRecordEditor.print();
      } else {
        that.medicalRecordConsentEditor.print();
      }
    },
    /**  保存病历  */
    saveMedicalRecordTemplateClick() {
      let that = this;
      if (that.medicalActiveName == "detail") {
        if (!that.currentTemplateItem.ID) {
          that.$message.error("请选择保存的病历");
          return;
        }
        let params = {
          ID: that.currentTemplateItem.ID, //病历记录详情ID
          MedicalRecordContent: encodeURIComponent(that.medicalRecordEditor.getDocContent()), //模板
          MedicalRecordDate: encodeURIComponent(JSON.stringify(that.docDataConsent)), //模板数据
          TemplatePrintJSON: JSON.stringify(that.editorPaperSetting), //打印格式
        };
        that.customerMedicalRecord_updateMedicalTemplate(params);
      } else {
        if (!that.currentTemplateItem.ID) {
          that.$message.error("请选择保的同意书");
          return;
        }
        let params = {
          ID: that.currentTemplateItem.ID, //病历记录详情ID
          MedicalRecordContent: encodeURIComponent(that.medicalRecordConsentEditor.getDocContent()), //模板
          MedicalRecordDate: encodeURIComponent(JSON.stringify(that.consentDocDataConsent)), //模板数据
          TemplatePrintJSON: JSON.stringify(that.editorPaperSetting), //打印格式
        };
        that.customerMedicalRecord_updateMedicalTemplate(params);
      }
    },
    /**  选择病历模板  */
    selectMedicalRecordTemplateClick(type) {
      let that = this;
      that.templateType = type;
      that.selectTemplates = [];
      that.medicalRecord_template(type);
      that.showSelectMedicalRecordTemplate = true;
      if (!that.selectMedicalRecordEditor) {
        that.$nextTick(() => {
          that.initselectMedicalRecordEditor();
        });
      }
    },
    /**  点击病历模板  */
    clickMedicalRecordTemplateListItem(node, item) {
      let that = this;
      if (that.medicalActiveName == "detail") {
        that.medicalRecordEditor.resetDocContent("");
        that.medicalRecordEditor.destroyData();
        that.medicalDisable = true;
      } else {
        that.medicalRecordConsentEditor.resetDocContent("");
        that.medicalRecordConsentEditor.destroyData();
        that.agreementDisable = true;
      }
      that.currentTemplateItem = item;
      that.customerMedicalRecord_getMedicalRecordDetail(item.ID).then((data) => {
        if (that.medicalActiveName == "detail") {
          let MedicalRecordData = data.MedicalRecordDate && decodeURIComponent(data.MedicalRecordDate) ? JSON.parse(decodeURIComponent(data.MedicalRecordDate)) : "";
          let DocContent = decodeURIComponent(data.MedicalRecordContent);
          that.medicalRecordEditor.resetDocContent(DocContent);
          Object.assign(that.docDataConsent, MedicalRecordData);
          that.medicalDisable = false;
        } else {
          let MedicalRecordData = data.MedicalRecordDate && decodeURIComponent(data.MedicalRecordDate) ? JSON.parse(decodeURIComponent(data.MedicalRecordDate)) : "";
          let DocContent = decodeURIComponent(data.MedicalRecordContent);
          that.medicalRecordConsentEditor.resetDocContent(DocContent);
          Object.assign(that.consentDocDataConsent, MedicalRecordData);
          that.agreementDisable = false;
        }
        that.loading = false;
      });
    },
    /**  删除病历模板  */
    removeMedicalRecordTemplateClick(item) {
      let that = this;
      if (that.medicalActiveName == "detail") {
        that.medicalRecordEditor.resetDocContent();
      } else {
        that.medicalRecordConsentEditor.resetDocContent();
      }
      that.customerMedicalRecord_deleteTemplate(item.ID);
    },
    /**  点击数据  */
    medicalRecordRowClick(row) {
      let that = this;
      that.currentItem = row;

      if (that.medicalActiveName == "detail" && that.medicalRecordEditor) {
        that.medicalRecordEditor.resetDocContent("");
        that.medicalRecordEditor.destroyData();
      }
      if (that.medicalActiveName == "agreement" && that.medicalRecordConsentEditor) {
        that.medicalRecordConsentEditor.resetDocContent("");
        that.medicalRecordConsentEditor.destroyData();
      }
      that.customerMedicalRecord_detail();
    },
    /**  table 值格式化 */
    medicalFormatterValue(row, column) {
      let that = this;
      return that.FormatterValue[column.property][row[column.property]];
    },
    /**  删除病历  */
    removeMedicalRecordFormClick() {
      let that = this;
      that
        .$confirm("是否要删除病历？", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
        .then(() => {
          that.customerMedicalRecord_delete();
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消操作",
          });
        });
    },
    /**  编辑病历  */
    editMedicalRecordFormClick() {
      let that = this;
      that.ruleForm = {
        ID: that.currentItem.ID, //病历记录ID
        Name: that.currentItem.Name, //病历名称
        MedicalRecordStatus: that.currentItem.MedicalRecordStatus, //病历状态（FST：初诊病历、FID：复诊病历）
        MedicalRecordCode: that.currentItem.MedicalRecordCode, //病历编号:
        MedicalRecordNumber: that.currentItem.MedicalRecordNumber, //病历号
        MedicCertificateNumber: that.currentItem.MedicCertificateNumber, //医疗证号
        DepartmentID: that.currentItemDetail.DepartmentID, //科室
        AttendingDoctor: that.currentItemDetail.AttendingDoctor, //主治医生
        CostCategory: that.currentItem.CostCategory, //费别（YB：医保、FYB：非医保、ZF：自费、STA：住院、MEN：门诊、DOC：手术室）
        TreatmentDate: that.currentItem.TreatmentDate, //就诊时间
        Remark: that.currentItem.Remark, //备注
      };
      that.isAdd = false;
      that.showMedicalRecordForm = true;
    },
    /**  新增病历  */
    saveMedicalRecordFormClick() {
      let that = this;
      that.$refs.ruleFormRef.validate((valid) => {
        if (valid) {
          if (that.isAdd) {
            that.customerMedicalRecord_create();
          } else {
            that.customerMedicalRecord_update();
          }
        }
      });
    },
    /**  新增病历  */
    async addMedicalRecordFormClick() {
      let that = this;
      that.isAdd = true;
      let MedicalRecordNumber = await that.customerMedicalRecord_getMedicalRecordCode();
      let empInfo = JSON.parse(localStorage.getItem("access-user"));
      let isDoctor = that.doctorList.some((i) => i.ID == empInfo.EmployeeID);
      that.ruleForm = {
        Name: `${that.CustomerName}${that.medicalRecordStatus == "FST" ? "初诊" : "复诊"}病历(${empInfo.EmployeeName}医生)`, //病历名称
        MedicalRecordStatus: that.medicalRecordStatus, //病历状态（FST：初诊病历、FID：复诊病历）
        MedicalRecordCode: MedicalRecordNumber, //病历编号:
        MedicalRecordNumber: that.currentItemDetail.MedicalRecordNumber, //病历号
        MedicCertificateNumber: "", //医疗证号
        DepartmentID: empInfo.DepartmentID, //科室
        AttendingDoctor: isDoctor ? empInfo.EmployeeID : "", //主治医生
        CostCategory: "ZF", //费别（YB：医保、FYB：非医保、ZF：自费、STA：住院、MEN：门诊、DOC：手术室）
        TreatmentDate: that.$dayjs().format("YYYY-MM-DD"), //就诊时间
        Remark: "", //备注
      };
      that.showMedicalRecordForm = true;
    },
    /**  修改分页  */
    handleCurrentChange(page) {
      let that = this;
      that.paginations.page = page;
      that.customerMedicalRecord_list();
    },

    /* ••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••• */
    /**  客户病历列表  */
    async customerMedicalRecord_list() {
      let that = this;
      try {
        that.loading = true;
        let params = {
          PageNum: that.paginations.page,
          CustomerID: that.CustomerID,
        };
        let res = await API.customerMedicalRecord_list(params);
        if (res.StateCode == 200) {
          that.medicalRecord_list = res.List;
          if (that.medicalRecord_list.length > 0) {
            that.currentItem = that.medicalRecord_list[0];
            that.$refs.medicalRef.setCurrentRow(that.currentItem);
            that.customerMedicalRecord_detail();
          } else {
            that.currentItemDetail = "";
            if (that.medicalRecordEditor) {
              that.medicalRecordEditor.resetDocContent("<div></div>");
            }
          }
          that.paginations.total = res.Total;

          that.customerMedicalRecord_medicalRecordStatus();
        } else {
          that.$message.error(res.Message);
        }

        that.loading = false;
      } catch (error) {
        that.loading = false;
        // that.$message.error(error);
      }
    },
    /**  医生列表  */
    async customerMedicalRecord_doctor() {
      let that = this;
      try {
        let params = {};
        let res = await API.customerMedicalRecord_doctor(params);
        if (res.StateCode == 200) {
          that.doctorList = res.Data;
        } else {
          that.$message.error(res.Message);
        }
      } catch (error) {
        // that.$message.error(error);
      }
    },
    /**  创建病历  */
    async customerMedicalRecord_create() {
      let that = this;
      try {
        that.modalLoading = true;
        let params = {
          Name: that.ruleForm.Name, //病历名称
          CustomerID: that.CustomerID, //顾客编号
          MedicalRecordStatus: that.ruleForm.MedicalRecordStatus, //病历状态（FST：初诊病历、FID：复诊病历）
          MedicalRecordCode: that.ruleForm.MedicalRecordCode, //病历编号:
          MedicalRecordNumber: that.ruleForm.MedicalRecordNumber, //病历号
          MedicCertificateNumber: that.ruleForm.MedicCertificateNumber, //医疗证号
          DepartmentID: that.ruleForm.DepartmentID, //科室
          AttendingDoctor: that.ruleForm.AttendingDoctor, //主治医生
          CostCategory: that.ruleForm.CostCategory, //费别（YB：医保、FYB：非医保、ZF：自费、STA：住院、MEN：门诊、DOC：手术室）
          TreatmentDate: that.ruleForm.TreatmentDate, //就诊时间
          Remark: that.ruleForm.Remark, //备注
        };
        let res = await API.customerMedicalRecord_create(params);
        if (res.StateCode == 200) {
          that.$message.success("操作成功");
          that.showMedicalRecordForm = false;
          that.customerMedicalRecord_list();

          if (that.medicalActiveName == "detail" && that.medicalRecordEditor) {
            that.medicalRecordEditor.resetDocContent("");
            that.medicalRecordEditor.destroyData();
            that.medicalDisabled = false;
          }

          if (that.medicalActiveName == "agreement" && that.medicalRecordConsentEditor) {
            that.medicalRecordConsentEditor.resetDocContent("");
            that.medicalRecordConsentEditor.destroyData();
            that.contentSaveDisabled = false;
          }
        } else {
          that.$message.error(res.Message);
        }
        that.modalLoading = false;
      } catch (error) {
        that.$message.error(error);
        that.modalLoading = false;
      }
    },
    /**  病历详情  */
    async customerMedicalRecord_detail() {
      let that = this;
      try {
        that.loading = true;
        let params = { ID: that.currentItem.ID };
        let res = await API.customerMedicalRecord_detail(params);
        if (res.StateCode == 200) {
          that.currentItemDetail = res.Data;
          if (!that.initEditer && that.currentItemDetail && that.currentItemDetail.RecordList.length > 0) {
            /* 设置病历 模板内容 */
            let item = that.currentItemDetail.RecordList[0];
            that.customerMedicalRecord_getMedicalRecordDetail(item.ID).then((data) => {
              that.currentTemplateItem = data;
              that.$nextTick(() => {
                let DocContent = decodeURIComponent(data.MedicalRecordContent);

                that.medicalRecordEditor.setDocContent(DocContent);
                Object.assign(that.docDataConsent, {});
                Object.assign(that.fieldDataConsent, {});

                /* 编辑器页眉和纸张内容设置 */
                let decodes = data.TemplatePrintJSON ? JSON.parse(data.TemplatePrintJSON) : "";
                if (decodes) {
                  decodes.headerContent = decodes.headerContent ? decodeURIComponent(decodes.headerContent) : "";
                  that.medicalRecordEditor.setPaper(decodes);
                  that.medicalRecordEditor.setHeaderContent(decodes.headerContent);
                  that.editorPaperSetting = decodes;
                }

                let MedicalRecordData = decodeURIComponent(data.MedicalRecordDate) ? JSON.parse(decodeURIComponent(data.MedicalRecordDate)) : "";
                Object.assign(that.docDataConsent, MedicalRecordData);

                let tempData = {
                  ctm_name: that.currentItemDetail.CustomerName,
                  ctm_sex: that.FormatterValue["Gender"][that.currentItemDetail.Gender] /* 性别 */,
                  ctm_age: that.currentItemDetail.Age /* 年龄 */,
                  ctm_mz: "" /* 客户民族 */,
                  ctm_mobile: that.currentItemDetail.PhoneNumber /* 联系电话 */,
                  ctm_id_code: that.currentItemDetail.IdentityCard /* 客户身份证 */,
                  S_AddressPCCS: that.currentItemDetail.Address /*  客户住址*/,
                  ctm_wktype: that.currentItemDetail.Job /* 职业 */,
                  ctm_code: that.currentItemDetail.Code /*客户卡号  */,
                  HospitalName: that.currentItemDetail.EntityName /* 医院名称 */,
                  HospitalizationEnableState1: "" /* '(门诊)床号 */,
                  O_MedicRecordFileNumber: that.currentItemDetail.MedicalRecordNumber /* 病历号 */,
                  O_MedicRecordCode: that.currentItemDetail.MedicalRecordCode /*  病历编号 */,
                  O_CostCategory: that.FormatterValue["CostCategory"][that.currentItemDetail.CostCategory] /* 费别 */,
                  O_TreatmentDate: that.currentItemDetail.TreatmentDate /* 治疗日期 */,
                  O_MedicRecordVisitDepartmentId: that.currentItemDetail.DepartmentName /* 就诊科室 */,
                  O_MedicRecordVisitDoctorId: that.currentItemDetail.AttendingDoctorName ? that.currentItemDetail.AttendingDoctorName : that.currentItemDetail.AttendingDoctorName /* 就诊医生 */,
                  O_MedicCertificateNumber: that.currentItemDetail.MedicCertificateNumber ? that.currentItemDetail.MedicCertificateNumber : that.currentItemDetail.EntityID /* 医疗证号 */,
                  O_CtfStatus: that.FormatterValue["MedicalRecordStatus"][that.currentItemDetail.MedicalRecordStatus] /* 病历状态 */,
                  O_Title: that.currentItemDetail.Name /* 病历名称 */,
                  ctm_datebirth: that.currentItemDetail.Birthday /* 出生日期 */,
                  H_Exp3: "" /* 国籍 */,
                };

                Object.assign(that.fieldDataConsent, tempData); //设置字段值
                that.$refs.recodeListRef.setCurrentKey(data);
                that.loading = false;
              });
            });
          }
        } else {
          that.loading = false;
          that.$message.error(res.Message);
        }
      } catch (error) {
        that.loading = false;
        that.$message.error(error);
      }
    },
    /**  修改病历  */
    async customerMedicalRecord_update() {
      let that = this;
      try {
        let params = {
          ID: that.ruleForm.ID,
          Name: that.ruleForm.Name, //病历名称
          CustomerID: that.CustomerID, //顾客编号
          MedicalRecordStatus: that.ruleForm.MedicalRecordStatus, //病历状态（FST：初诊病历、FID：复诊病历）
          MedicalRecordCode: that.ruleForm.MedicalRecordCode, //病历编号:
          MedicalRecordNumber: that.ruleForm.MedicalRecordNumber, //病历号
          MedicCertificateNumber: that.ruleForm.MedicCertificateNumber, //医疗证号
          DepartmentID: that.ruleForm.DepartmentID, //科室
          AttendingDoctor: that.ruleForm.AttendingDoctor, //主治医生
          CostCategory: that.ruleForm.CostCategory, //费别（YB：医保、FYB：非医保、ZF：自费、STA：住院、MEN：门诊、DOC：手术室）
          TreatmentDate: that.ruleForm.TreatmentDate, //就诊时间
          Remark: that.ruleForm.Remark, //备注
        };
        let res = await API.customerMedicalRecord_update(params);
        if (res.StateCode == 200) {
          that.$message.success("操作成功");
          that.showMedicalRecordForm = false;
          that.customerMedicalRecord_list();
        } else {
          that.$message.error(res.Message);
        }
      } catch (error) {
        // that.$message.error(error);
      }
    },
    /**   删除病历 */
    async customerMedicalRecord_delete() {
      let that = this;
      try {
        let params = {
          ID: that.currentItem.ID,
        };
        let res = await API.customerMedicalRecord_delete(params);
        if (res.StateCode == 200) {
          that.$message.success("操作成功");
          that.customerMedicalRecord_list();
        } else {
          that.$message.error(res.Message);
        }
      } catch (error) {
        // that.$message.error(error);
      }
    },
    /**  病历列表（病例详情或者同意书 选择新增模板的时候使用）  */
    async customerMedicalRecord_medicalRecordList() {
      let that = this;
      try {
        let params = {};
        let res = await API.customerMedicalRecord_medicalRecordList(params);
        if (res.StateCode == 200) {
          // console.log(res);
        } else {
          that.$message.error(res.Message);
        }
      } catch (error) {
        // that.$message.error(error);
      }
    },
    /**  获取科室列表  */
    async department_all() {
      let that = this;
      try {
        var params = {
          Name: "",
          Active: true,
        };
        let res = await API.department_all(params);
        if (res.StateCode == 200) {
          that.department = res.Data;
        } else {
          that.$message.error(res.Message);
        }
      } catch (error) {
        // that.$message.error(error);
      }
    },
    /**  模板列表  1:整形病历 ；  2：整形之情同意书  3 不良事件上报 */
    async medicalRecord_template() {
      let that = this;
      try {
        let params = {
          TypeID: that.templateType,
        };
        let res = await API.medicalRecord_template(params);
        if (res.StateCode == 200) {
          that.medicalRecord_type_list = res.Data;
          if (res.Data.length > 0) {
            let item = res.Data[0];
            that.recordTemplateTypeID = item.ID;
            that.medicalRecord_template_list = item.Template;
          }
        } else {
          that.$message.error(res.Message);
        }
      } catch (error) {
        // that.$message.error(error);
      }
    },
    /**  新建病历模板  */
    async customerMedicalRecord_createMedicalTemplate() {
      let that = this;
      if (!that.selectTemplates || !that.selectTemplates.length) {
        that.$message.error("请选择模板");
        return;
      }
      try {
        let params = {
          CustomerMedicalRecordID: that.currentItemDetail.ID, //病历记录ID
          Template: that.selectTemplates.map((i) => {
            return {
              MedicalRecordTemplateID: i.ID,
            };
          }),
          MedicalRecordTypeID: that.templateType, //病历类型id
        };
        let res = await API.customerMedicalRecord_createMedicalTemplate(params);
        if (res.StateCode == 200) {
          that.showSelectMedicalRecordTemplate = false;
          that.selectTemplates = [];
          that.$message.success("操作成功");
          that.customerMedicalRecord_detail();
        } else {
          that.$message.error(res.Message);
        }
      } catch (error) {
        // that.$message.error(error);
      }
    },
    /**  修改病历模板  */
    async customerMedicalRecord_updateMedicalTemplate(params) {
      let that = this;
      try {
        that.detailSaveLoading = true;
        let res = await API.customerMedicalRecord_updateMedicalTemplate(params);
        if (res.StateCode == 200) {
          that.$message.success("操作成功");
        } else {
          that.$message.error(res.Message);
        }
        that.detailSaveLoading = false;
      } catch (error) {
        that.detailSaveLoading = false;
        that.$message.error(error);
      }
    },

    /**  病历信息  */
    async medicalRecord_entryInfo() {
      let that = this;
      try {
        let params = {};
        let res = await API.medicalRecord_entryInfo(params);
        if (res.StateCode == 200) {
          return res.Data;
        } else {
          that.$message.error(res.Message);
        }
      } catch (error) {
        // that.$message.error(error);
      }
    },
    /**  模板详情  */
    async medicalRecord_getTemplate(TemplateID) {
      let that = this;
      try {
        let params = {
          TemplateID: TemplateID,
        };
        let res = await API.medicalRecord_getTemplate(params);
        if (res.StateCode == 200) {
          that.templateDetail = res.Data;
          let editorTemplate = decodeURIComponent(that.templateDetail.TemplateContent);
          that.selectMedicalRecordEditor.setDocContent(editorTemplate);
        } else {
          that.$message.error(res.Message);
        }
      } catch (error) {
        // that.$message.error(error);
      }
    },
    /**  删除病历模板  */
    async customerMedicalRecord_deleteTemplate(ID) {
      let that = this;
      try {
        let params = {
          ID: ID,
        };
        let res = await API.customerMedicalRecord_deleteTemplate(params);
        if (res.StateCode == 200) {
          that.$message.success("操作成功");
          that.customerMedicalRecord_detail();
        } else {
          that.$message.error(res.Message);
        }
      } catch (error) {
        // that.$message.error(error);
      }
    },
    /**  获取病历号  */
    async customerMedicalRecord_getMedicalRecordNumber() {
      let that = this;
      try {
        let params = {};
        let res = await API.customerMedicalRecord_getMedicalRecordNumber(params);
        if (res.StateCode == 200) {
          that.ruleForm.MedicalRecordNumber = res.Message;
        } else {
          that.$message.error(res.Message);
        }
      } catch (error) {
        // that.$message.error(error);
      }
    },
    /**   获取病历编号 */
    async customerMedicalRecord_getMedicalRecordCode() {
      let that = this;
      try {
        let params = {};
        let res = await API.customerMedicalRecord_getMedicalRecordCode(params);
        if (res.StateCode == 200) {
          return res.Message;
        } else {
          that.$message.error(res.Message);
        }
      } catch (error) {
        // that.$message.error(error);
      }
    },
    /**  获取病历模板信息、内容  */
    async customerMedicalRecord_getMedicalRecordDetail(ID) {
      let that = this;
      try {
        that.loading = true;
        let params = { ID: ID };
        let res = await API.customerMedicalRecord_getMedicalRecordDetail(params);
        if (res.StateCode == 200) {
          return res.Data;
        } else {
          that.loading = false;
          that.$message.error(res.Message);
        }
        that.loading = false;
      } catch (error) {
        that.loading = false;
        that.$message.error(error);
      }
    },

    /**  初始化打印   */
    initHMUEEdit() {
      let that = this;
      let docContext = {
        paperSetting: that.editorPaperSetting,
        fieldData: {}, //页眉字段值，用来自动填充字段数据的
        fieldDict: [], //页眉字段的词典，用来做设计
      };
      //文档数据，就是要保存到数据的 key-value 对象
      that.docDataConsent = {};
      //字段值，用来自动填充字段数据的，像是下面这样的：{user: {age: 28, name: "孙悟空"}, doctor: {name:"华佗在世"}}
      // that.fieldDataConsent = {};
      // 字段的词典，用来做设计时，选择字段类型，像是下面这样的：[{label: "医生 · 主治医生", value: "doctor.name", icon: "hm-icon-doctor"}]
      let fieldDict = [];
      // 词条字典，用来在设计模式下，选择词条类型，像是下面这样的：[{"value": "kqzhk", "label": "口腔综合科"}]
      let wordDict = [];
      return new Promise((resolve) => {
        if (!that.medicalRecordEditor) {
          that.medicalRecordEditor = window.createEditor(document.querySelector("#medicalRecordEditor"), docContext, that.docDataConsent, that.fieldDataConsent, fieldDict, wordDict);

          that.medicalRecordConsentEditor = window.createEditor(document.querySelector("#medicalRecordConsentEditor"), docContext, that.consentDocDataConsent, that.fieldDataConsent, fieldDict, wordDict);
        }

        that.medicalRecordEditor.hideToolbar(true);
        that.medicalRecordEditor
          .$on("initComplete", () => {
            // 初始化词条字典
            that.medicalRecord_entryInfo().then((data) => {
              wordDict.length = 0;
              data = data.map((i) => {
                return {
                  value: i.ID,
                  label: i.Name,
                  children: i.Child.map((j) => {
                    return {
                      value: j.ID,
                      label: j.Name,
                    };
                  }),
                };
              });
              data.unshift({ label: "<空>", value: "" });
              Array.prototype.push.apply(wordDict, data);
            });

            if (that.currentItemDetail && that.currentItemDetail.RecordList.length > 0) {
              /* 设置病历 模板内容 */
              let item = that.currentItemDetail.RecordList[0];
              that.customerMedicalRecord_getMedicalRecordDetail(item.ID).then((data) => {
                that.currentTemplateItem = data;
                that.$nextTick(() => {
                  let DocContent = decodeURIComponent(data.MedicalRecordContent);
                  that.medicalRecordEditor.setDocContent(DocContent);
                  Object.assign(that.docDataConsent, {});
                  Object.assign(that.fieldDataConsent, {});
                  /* 编辑器页眉和纸张内容设置 */
                  let decodes = data.TemplatePrintJSON ? JSON.parse(data.TemplatePrintJSON) : "";
                  if (decodes) {
                    decodes.headerContent = decodes.headerContent ? decodeURIComponent(decodes.headerContent) : "";
                    that.medicalRecordEditor.setPaper(decodes);
                    that.medicalRecordEditor.setHeaderContent(decodes.headerContent);
                    that.editorPaperSetting = decodes;
                  }

                  let MedicalRecordData = decodeURIComponent(data.MedicalRecordDate) ? JSON.parse(decodeURIComponent(data.MedicalRecordDate)) : "";
                  Object.assign(that.docDataConsent, MedicalRecordData);

                  let tempData = {
                    ctm_name: that.currentItemDetail.CustomerName,
                    ctm_sex: that.FormatterValue["Gender"][that.currentItemDetail.Gender] /* 性别 */,
                    ctm_age: that.currentItemDetail.Age /* 年龄 */,
                    ctm_mz: "" /* 客户民族 */,
                    ctm_mobile: that.currentItemDetail.PhoneNumber /* 联系电话 */,
                    ctm_id_code: that.currentItemDetail.IdentityCard /* 客户身份证 */,
                    S_AddressPCCS: that.currentItemDetail.Address /*  客户住址*/,
                    ctm_wktype: that.currentItemDetail.Job /* 职业 */,
                    ctm_code: that.currentItemDetail.Code /*客户卡号  */,
                    HospitalName: that.currentItemDetail.EntityName /* 医院名称 */,
                    HospitalizationEnableState1: "" /* '(门诊)床号 */,
                    O_MedicRecordFileNumber: that.currentItemDetail.MedicalRecordNumber /* 病历号 */,
                    O_MedicRecordCode: that.currentItemDetail.MedicalRecordCode /*  病历编号 */,
                    O_CostCategory: that.FormatterValue["CostCategory"][that.currentItemDetail.CostCategory] /* 费别 */,
                    O_TreatmentDate: that.currentItemDetail.TreatmentDate /* 治疗日期 */,
                    O_MedicRecordVisitDepartmentId: that.currentItemDetail.DepartmentName /* 就诊科室 */,
                    O_MedicRecordVisitDoctorId: that.currentItemDetail.AttendingDoctorName ? that.currentItemDetail.AttendingDoctorName : that.currentItemDetail.AttendingDoctorName /* 就诊医生 */,
                    O_MedicCertificateNumber: that.currentItemDetail.MedicCertificateNumber ? that.currentItemDetail.MedicCertificateNumber : that.currentItemDetail.EntityID /* 医疗证号 */,
                    O_CtfStatus: that.FormatterValue["MedicalRecordStatus"][that.currentItemDetail.MedicalRecordStatus] /* 病历状态 */,
                    O_Title: that.currentItemDetail.Name /* 病历名称 */,
                    ctm_datebirth: that.currentItemDetail.Birthday /* 出生日期 */,
                    H_Exp3: "" /* 国籍 */,
                  };
                  Object.assign(that.fieldDataConsent, tempData); //设置字段值
                  that.$refs.recodeListRef.setCurrentKey(data);
                  that.loading = false;
                });
              });
            }
          })
          .$on("contentWindowOnload", (contentWindow) => {
            const base = process.env.VUE_APP_API_URL; // 请求地址
            // 处理界面token
            const accessuser = JSON.parse(localStorage.getItem("access-user"));
            let zl_header_token = "";
            if (accessuser) {
              zl_header_token = "Basic " + accessuser.AuthToken;
            }
            /* 新增的iframe 加载完成后的回调方法 */
            contentWindow.zl_base_url = base;
            contentWindow.zl_header_token = zl_header_token;
            that.medicalRecordEditor.setEditable(false);
            that.medicalRecordEditor.showHeader(false);
            resolve();
            that.initEditer = false;
          });

        that.medicalRecordConsentEditor.hideToolbar(true);
        that.medicalRecordConsentEditor
          .$on("initComplete", () => {
            // 初始化词条字典
            that.medicalRecord_entryInfo().then((data) => {
              wordDict.length = 0;
              data = data.map((i) => {
                return {
                  value: i.ID,
                  label: i.Name,
                  children: i.Child.map((j) => {
                    return {
                      value: j.ID,
                      label: j.Name,
                    };
                  }),
                };
              });
              data.unshift({ label: "<空>", value: "" });
              Array.prototype.push.apply(wordDict, data);
            });
          })
          .$on("contentWindowOnload", (contentWindow) => {
            const base = process.env.VUE_APP_API_URL; // 请求地址
            // 处理界面token
            const accessuser = JSON.parse(localStorage.getItem("access-user"));
            let zl_header_token = "";
            if (accessuser) {
              zl_header_token = "Basic " + accessuser.AuthToken;
            }
            /* 新增的iframe 加载完成后的回调方法 */
            contentWindow.zl_base_url = base;
            contentWindow.zl_header_token = zl_header_token;
            that.medicalRecordConsentEditor.setEditable(false);
            that.medicalRecordConsentEditor.showHeader(false);
            that.initEditer = false;
          });
      });
    },

    /* 初始化选择模板 */
    initselectMedicalRecordEditor() {
      let that = this;
      let editorPaperSetting = {
        size: "A4", // 纸张尺寸，目前只有两个值：A4 和 A5
        marginX: 10, //打印时，左右两边的边距，单位：毫米
        marginTop: 20, //打印时，上边的边距，单位：毫米
        marginBottom: 0, //打印时，下边的边距，单位：毫米
        headerContent: "", //页面的html内容
        showHeader: false, //是否展示页眉
      };
      let docContext = {
        paperSetting: editorPaperSetting,
        fieldData: {}, //页眉字段值，用来自动填充字段数据的
        fieldDict: [], //页眉字段的词典，用来做设计
      };
      //文档数据，就是要保存到数据的 key-value 对象
      let docDataConsent = {};
      //字段值，用来自动填充字段数据的，像是下面这样的：{user: {age: 28, name: "孙悟空"}, doctor: {name:"华佗在世"}}
      let fieldDataConsent = {};
      // 字段的词典，用来做设计时，选择字段类型，像是下面这样的：[{label: "医生 · 主治医生", value: "doctor.name", icon: "hm-icon-doctor"}]
      let fieldDict = [];
      // 词条字典，用来在设计模式下，选择词条类型，像是下面这样的：[{"value": "kqzhk", "label": "口腔综合科"}]
      let wordDict = [];
      that.selectMedicalRecordEditor = window.createEditor(document.querySelector("#selectMedicalRecordEditor"), docContext, docDataConsent, fieldDataConsent, fieldDict, wordDict);

      that.selectMedicalRecordEditor.hideToolbar(true);
      that.selectMedicalRecordEditor
        .$on("initComplete", () => {
          // 初始化词条字典
          that.medicalRecord_entryInfo().then((data) => {
            wordDict.length = 0;
            data = data.map((i) => {
              return {
                value: i.ID,
                label: i.Name,
                children: i.Child.map((j) => {
                  return {
                    value: j.ID,
                    label: j.Name,
                  };
                }),
              };
            });
            data.unshift({ label: "<空>", value: "" });
            Array.prototype.push.apply(wordDict, data);
          });
        })
        .$on("contentWindowOnload", () => {
          that.selectMedicalRecordEditor.setEditable(true);
          that.selectMedicalRecordEditor.showHeader(false);
          that.selectMedicalRecordEditor.setReadonly(true);
        });
    },
    /* 初始化 历史病历记录 */
    initWorkbenchMedicakRecordData() {
      let that = this;

      localStorage.removeItem("editorTemp");
      that.customerMedicalRecord_list().then(() => {
        that.customerMedicalRecord_doctor();
        that.department_all();

        that.initHMUEEdit().then(() => {});
      });
      // that.customerMedicalRecord_medicalRecordStatus();
    },
    /**  查询顾客的病历状态  */
    async customerMedicalRecord_medicalRecordStatus() {
      let that = this;
      try {
        let params = {
          CustomerID: that.CustomerID, //顾客编号
        };
        let res = await API.customerMedicalRecord_medicalRecordStatus(params);
        if (res.StateCode == 200) {
          that.medicalRecordStatus = res.Message;
        } else {
          that.$message.error(res.Message);
        }
      } catch (error) {
        // that.$message.error(error);
      }
    },
  },
  /** 创建实例之前执行函数   */
  beforeCreate() {},
  /**  实例创建完成之后  */
  created() {},
  /**  在挂载开始之前被调用  */
  beforeMount() {},
  /**  实例被挂载后调用  */
  mounted() {},
  /**  数据更新 之前 调用  */
  beforeUpdate() {},
  /** 数据更新 完成 调用   */
  updated() {},
  /**  实例销毁之前调用  */
  beforeDestroy() {},
  /**  实例销毁后调用  */
  destroyed() {},
};
</script>

<style lang="scss">
.workbenchMedicalRecord {
  .medicalRecordContent {
    height: calc(100vh - 500px);
  }
}
.medicalRecordTabsClass {
  .el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content {
    // 设置颜色
    background-color: var(--zl-color-orange-primary-header); // 透明度为0.2的skyblue，作者比较喜欢的颜色
    color: var(--zl-color-orange-primary); // 节点的字体颜色
    font-weight: bold; // 字体加粗
  }
  .medicalRecordContent-left {
    // width: 400px;
    height: 100%;
  }
  .medicalRecordContent-right {
    height: 100%;
  }
}

.selectMedicakRecordClass {
  .left-menu {
    height: 300px;
    width: 400px;
    border: 1px solid #eeeeee;
    .left-scrollbar-height {
      height: 100%;
      overflow-x: hidden;
    }
  }
  .menuSelectedClass {
    margin-top: 10px;
    height: 250px;
    width: 400px;
    border: 1px solid #eeeeee;
    .menuSelectedClass-scrollbar {
      height: 100%;
      overflow-x: hidden;
    }
  }
  .el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content {
    // 设置颜色
    background-color: var(--zl-color-orange-primary-header); // 透明度为0.2的skyblue，作者比较喜欢的颜色
    color: var(--zl-color-orange-primary); // 节点的字体颜色
    font-weight: bold; // 字体加粗
  }
  .edit-wrapper {
    height: 600px;
  }
}
</style>
