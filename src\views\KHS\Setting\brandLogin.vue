<template>
  <div class="settingBrandLogin content_body" v-loading="loading">
    <el-row>
      <el-col :span="24">
        <el-scrollbar></el-scrollbar>
        <el-form size="small" label-width="130px" :model="ruleForm" :rules="rules" ref="ruleForm">
          <el-form-item label="默认登录:">
            <el-link :underline="false" type="primary" :href="loginPath">{{ loginPath }}</el-link>
          </el-form-item>
          <el-form-item label="主题色调:" prop="FrontColor">
            <div class="dis_flex flex_y_center">
              <div
                v-for="color in predefineColors"
                :key="color"
                :style="setSelectFrontColor(color)"
                style="height: 26px; width: 26px; border-radius: 26px; margin-right: 8px; box-sizing: border-box"
                class="dis_flex flex_y_center flex_x_center"
                @click="selectFrontColor(color)"
              >
                <div
                  :style="{ 'background-color': color }"
                  style="height: 18px; width: 18px; border-radius: 18px"
                ></div>
              </div>

              <div
                style="height: 16px; width: 1px; background-color: #aaaaaa; margin-right: 16px; margin-left: 8px"
              ></div>
              <span class="color_666 font_13 marrt_5">自定义</span>
              <el-color-picker
                style="border-radius: 20px"
                v-model="ruleForm.FrontColor"
                show-alpha
                :predefine="predefineColors"
              ></el-color-picker>
            </div>
          </el-form-item>
          <el-form-item label="店铺logo:" prop="BrandLogoIcon">
            <el-upload
              class="brandLoginImage_upload_icon"
              action="#"
              :show-file-list="false"
              :before-upload="($event) => ImageBeforeUpload($event, 'BrandLogoIcon')"
              multiple
            >
              <div v-if="ruleForm.BrandLogoIcon" class="image">
                <el-image
                  style="widht: 100%; height: 100%"
                  :src="ruleForm.BrandLogoIcon"
                  fit="scale-down"
                ></el-image>
                <span class="el-upload-list__item-actions">
                  <span @click.stop="uploadImageRemove('BrandLogoIcon')">
                    <i class="el-icon-delete"></i>
                  </span>
                </span>
              </div>
              <i v-else class="el-icon-plus brandLoginImage_upload-icon"></i>
            </el-upload>
            <div class="color_999 font_13 dis_flex flex_dir_column" style="line-height: 20px">
              <span>登录页Logo，建议尺寸(宽x高): 65x60; 大小不超过200KB</span>
              <span></span>
            </div>
          </el-form-item>
          <el-form-item label="菜单展开logo:" prop="BrandLogo">
            <el-upload
              class="brandLoginImage_upload"
              action="#"
              :show-file-list="false"
              :before-upload="($event) => ImageBeforeUpload($event, 'BrandLogo')"
              multiple
            >
              <div v-if="ruleForm.BrandLogo" class="image">
                <el-image
                  style="widht: 100%; height: 100%"
                  :src="ruleForm.BrandLogo"
                  fit="scale-down"
                ></el-image>
                <span class="el-upload-list__item-actions">
                  <span @click.stop="uploadImageRemove('BrandLogo')">
                    <i class="el-icon-delete"></i>
                  </span>
                </span>
              </div>
              <i v-else class="el-icon-plus brandLoginImage_upload-icon"></i>
            </el-upload>
            <div class="color_999 font_13 dis_flex flex_dir_column" style="line-height: 20px">
              <span>菜单展开Logo，建议尺寸(宽x高): 210x60; 大小不超过200KB</span>
            </div>
          </el-form-item>

          <el-form-item label="品牌图片:" prop="BrandImage">
            <el-upload
              class="brandBrandImage_upload"
              action="#"
              :show-file-list="false"
              :before-upload="($event) => ImageBeforeUpload($event, 'BrandImage')"
              multiple
            >
              <div v-if="ruleForm.BrandImage" class="image">
                <el-image
                  style="widht: 100%; height: 100%"
                  :src="ruleForm.BrandImage"
                  fit="scale-down"
                ></el-image>
                <span class="el-upload-list__item-actions">
                  <span @click.stop="uploadImageRemove('BrandImage')">
                    <i class="el-icon-delete"></i>
                  </span>
                </span>
              </div>
              <i v-else class="el-icon-plus brandBrandImage_upload-icon"></i>
            </el-upload>
            <div class="color_999 font_13 dis_flex flex_dir_column" style="line-height: 20px">
              <span>品牌图片，建议尺寸(宽x高): 1290x1080; 大小不超过200KB</span>
            </div>
          </el-form-item>
          <el-form-item>
            <el-button size="small" type="primary" v-loading="modeloading" @click="submitClick">保存</el-button>
          </el-form-item>
        </el-form>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import API from "@/api/KHS/Setting/brandLogin";
import utils from "@/components/js/utils.js";

export default {
  name: "settingBrandLogin",
  props: {},
  /** 监听数据变化   */
  watch: {},
  /**  引入的组件  */
  components: {},
  /**  Vue 实例的数据对象**/
  data() {
    return {
      color: "",
      modeloading: false,
      loading: false,
      ruleForm: {
        BrandImage: "",
        BrandLogo: "",
        BrandLogoIcon: "",
        FrontColor: "",
        BackgroundColor: "#f7f8f8"
      },
      rules: {
        BrandImage: [
          {
            required: true,
            message: "请选择品牌图片",
            trigger: ["blur", "change"]
          }
        ],
        BrandLogo: [
          {
            required: true,
            message: "请选择菜单展开logo",
            trigger: ["blur", "change"]
          }
        ],
        BrandLogoIcon: [
          {
            required: true,
            message: "请选择品牌logo",
            trigger: ["blur", "change"]
          }
        ],
        FrontColor: [
          {
            required: true,
            message: "请选择主题颜色",
            trigger: ["blur", "change"]
          }
        ]
      },
      loginPath: "",
      predefineColors: ["#ff8646", "#ff4500", "#ffd700", "#90ee90", "#00ced1", "#1e90ff", "#c71585", "#c7158577"]
    };
  },
  /**计算属性  */
  computed: {},
  /**  方法集合  */
  methods: {
    /**    */
    selectFrontColor(color) {
      let that = this;
      that.ruleForm.FrontColor = color;
    },
    /**    */
    setSelectFrontColor(color) {
      let that = this;
      if (color == that.ruleForm.FrontColor) {
        return "border: 1px solid " + color;
      }
      return "";
    },
    /**  获取图片尺寸  */
    asyncImgSizeCheck(base64, width, height) {
      let that = this;
      return new Promise(function(resolve) {
        let img = document.createElement("img");
        img.src = base64;
        img.onload = function() {
          if (img && img.width == width && img.height == height) {
            resolve(true);
          } else {
            that.$message.error("请上传（宽*高）" + width + "*" + height + "尺寸的图片");
            // reject({check:true});
          }
        };
      });
    },

    /* 图片上传 */
    ImageBeforeUpload(file, type) {
      let that = this;
      let imageSize = file.size / 1024 < 200;
      if (!imageSize) {
        this.$message.error("上传图片不能超过 200kb!");
        return false;
      }

      let widht = 0;
      let height = 0;
      switch (type) {
        case "BrandLogo":
          widht = 210;
          height = 60;
          break;
        case "BrandLogoIcon":
          widht = 65;
          height = 60;
          break;
        case "BrandImage":
          widht = 1290;
          height = 1080;
          break;
      }

      let reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = async function(evt) {
        let base64 = evt.target.result;
        let isImgSize = await that.asyncImgSizeCheck(base64, widht, height);
        if (isImgSize) {
          that.$nextTick(function() {
            that.ruleForm[type] = base64;
          });
        }
      };
      return false;
    },

    /* 登录页配图删除 */
    uploadImageRemove(type) {
      this.ruleForm[type] = "";
    },

    /* 品牌logo保存 */
    submitClick() {
      let that = this;
      that.$refs.ruleForm.validate(valid => {
        if (valid) {
          that.createBrandLogo();
        }
      });
    },
    /** 保存请求   */
    createBrandLogo() {
      let that = this;
      that.modeloading = true;
      let params = Object.assign({}, that.ruleForm);
      API.createBrandLogo(params)
        .then(res => {
          if (res.StateCode == 200) {
            that.$message.success({
              message: "品牌logo保存成功",
              duration: 2000
            });
            this.$bus.$emit(this.$bus.ChangeLogoImage);
            that.getALLBrandLogo();
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000
            });
          }
        })
        .finally(function() {
          that.modeloading = false;
        });
    },
    /*品牌log查询 */
    getALLBrandLogo() {
      let that = this;
      that.loading = true;
      let params = {};
      API.getALLBrandLogo(params)
        .then(res => {
          if (res.StateCode == 200) {
            utils.getCanvasBase64(res.Data.BrandLogo).then(function(base64) {
              res.Data.BrandLogo = base64;
            });
            utils.getCanvasBase64(res.Data.BrandLogoIcon).then(function(base64) {
              res.Data.BrandLogoIcon = base64;
            });
            utils.getCanvasBase64(res.Data.BrandImage).then(function(base64) {
              res.Data.BrandImage = base64;
            });

            that.ruleForm = res.Data;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000
            });
          }
        })
        .finally(function() {
          that.loading = false;
        });
    }
  },
  /** 创建实例之前执行函数   */
  beforeCreate() {},
  /**  实例创建完成之后  */
  created() {
    this.loginPath = "https://" + window.location.host + "/Brand/" + localStorage.getItem("EnterpriseCode");
  },
  /**  在挂载开始之前被调用  */
  beforeMount() {},
  /**  实例被挂载后调用  */
  mounted() {
    let that = this;
    that.getALLBrandLogo();
  },
  /**  数据更新 之前 调用  */
  beforeUpdate() {},
  /** 数据更新 完成 调用   */
  updated() {},
  /**  实例销毁之前调用  */
  beforeDestroy() {},
  /**  实例销毁后调用  */
  destroyed() {}
};
</script>

<style lang="scss">
.settingBrandLogin {
  .preview-login_information {
    padding: 30px;
    height: 360px;
    width: 260px;
    border-radius: 8px;
    background-color: #ffffff;
    box-shadow: 10px 10px 10px #f5f0ee;

    display: flex;
    flex-direction: column;
    justify-content: center;
    .login_logo {
      text-align: center;
    }

    .el-input__inner {
      border: none !important;
      border-bottom: 1px solid #eeeeee !important;
      padding: 15px;
    }
    .forget_password {
      color: #666;
    }
    .btn-miniprogram {
      color: rgb(157, 165, 179);
      background-color: rgb(248, 249, 252);
      border: none;
    }
  }
  .brandLogoIcon_upload {
    .el-upload--picture-card {
      width: 65px;
      height: 60px;
      font-size: 16px !important;
    }
    .el-upload {
      width: 100px;
      height: 100px;
      line-height: 100px;
      font-size: 16px;
    }
    .el-upload-list--picture-card .el-upload-list__item {
      border: none;
      width: 65px;
      height: 60px;
      line-height: 60px;
      font-size: 16px;
    }
  }
  .brandLoginImage_upload {
    .el-upload {
      border: 1px dashed #d9d9d9;
      border-radius: 6px;
      cursor: pointer;
      position: relative;
      overflow: hidden;
      width: 210px;
      height: 60px;
    }
    .el-upload:hover {
      border-color: var(--zl-color-orange-primary);
    }

    .brandLoginImage_upload-icon {
      font-size: 28px;
      color: #8c939d;
      text-align: center;
      width: 210px;
      height: 60px;
      line-height: 60px;
    }
    .image {
      display: block;
      width: 210px;
      height: 60px;
    }
  }

  .brandLoginImage_upload_icon {
    .el-upload {
      border: 1px dashed #d9d9d9;
      border-radius: 6px;
      cursor: pointer;
      position: relative;
      overflow: hidden;
      width: 65px;
      height: 60px;
    }
    .el-upload:hover {
      border-color: var(--zl-color-orange-primary);
    }

    .brandLoginImage_upload-icon {
      font-size: 28px;
      color: #8c939d;
      text-align: center;
      width: 65px;
      height: 60px;
      line-height: 60px;
    }
    .image {
      display: block;
      width: 65px;
      height: 60px;
    }
  }

  .brandBrandImage_upload {
    .el-upload {
      border: 1px dashed #d9d9d9;
      border-radius: 6px;
      cursor: pointer;
      position: relative;
      overflow: hidden;
      width: 258px;
      height: 216px;
    }
    .el-upload:hover {
      border-color: var(--zl-color-orange-primary);
    }

    .brandBrandImage_upload-icon {
      font-size: 28px;
      color: #8c939d;
      text-align: center;
      width: 258px;
      height: 216px;
      line-height: 216px;
    }
    .image {
      display: block;
      width: 258px;
      height: 216px;
    }
  }

  .el-upload-list__item-actions:hover {
    opacity: 1;
  }
  .el-upload-list__item-actions {
    position: absolute;
    width: 100%;
    height: 100%;
    line-height: 100%;
    left: 0;
    top: 0;
    cursor: default;
    color: #fff;
    opacity: 0;
    font-size: 20px;
    background-color: rgba(0, 0, 0, 0.5);
    transition: opacity 0.3s;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}
</style>
