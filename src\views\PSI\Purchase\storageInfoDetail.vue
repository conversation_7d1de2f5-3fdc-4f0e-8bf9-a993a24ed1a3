<template>
  <div class="content_body StorageInfoDetail">
    <el-card>
      <div slot="header" class="clearfix">
        <span>采购入库详情 - {{ storageInfo.ID }}</span>
        <el-button style="float: right; padding: 3px 0" type="text" @click="goBack">返回列表</el-button>
      </div>

      <!-- 基本信息展示 -->
      <div class="info-section">
        <h3>基本信息</h3>
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="info-item">
              <label>入库日期：</label>
              <span>{{ storageInfo.InDate }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>供应商：</label>
              <span>{{ storageInfo.SupplierName }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>仓库/门店：</label>
              <span>{{ storageInfo.EntityName }}</span>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="info-item">
              <label>制单人：</label>
              <span>{{ storageInfo.EmployeeName }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>制单时间：</label>
              <span>{{ storageInfo.CreatedOn }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>单据状态：</label>
              <el-tag :type="getStatusTagType(storageInfo.BillStatus)">{{ storageInfo.BillStatusName }}</el-tag>
            </div>
          </el-col>
        </el-row>
        <el-row v-if="storageInfo.ConfirmedBy" :gutter="20">
          <el-col :span="8">
            <div class="info-item">
              <label>确认人：</label>
              <span>{{ storageInfo.ConfirmedByName }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>确认时间：</label>
              <span>{{ storageInfo.ConfirmedOn }}</span>
            </div>
          </el-col>
        </el-row>
        <el-row v-if="storageInfo.Remark">
          <el-col :span="24">
            <div class="info-item">
              <label>备注：</label>
              <span>{{ storageInfo.Remark }}</span>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 产品明细表格 -->
      <div class="product-section">
        <h3>产品明细</h3>
        <el-table :data="productList" border v-loading="loading">
          <el-table-column prop="ProductName" label="产品名称" min-width="120" />
          <el-table-column prop="Specification" label="规格" width="100" />
          <el-table-column prop="UnitName" label="单位" width="80" />
          <el-table-column prop="UnitPrice" label="单价" width="100">
            <template slot-scope="scope">
              ¥{{ scope.row.UnitPrice }}
            </template>
          </el-table-column>
          <el-table-column prop="Quantity" label="计划数量" width="100" />
          <el-table-column prop="ActualQuantity" label="实际入库数量" width="120">
            <template slot-scope="scope">
              <span :class="{ 'text-success': scope.row.IsCompleted, 'text-warning': !scope.row.IsCompleted }">
                {{ scope.row.ActualQuantity || 0 }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="RemainingQuantity" label="剩余数量" width="100">
            <template slot-scope="scope">
              <span :class="{ 'text-success': scope.row.RemainingQuantity === 0, 'text-warning': scope.row.RemainingQuantity > 0 }">
                {{ scope.row.RemainingQuantity || 0 }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="Amount" label="计划金额" width="120">
            <template slot-scope="scope">
              ¥{{ scope.row.Amount }}
            </template>
          </el-table-column>
          <el-table-column prop="ActualAmount" label="实际入库金额" width="120">
            <template slot-scope="scope">
              ¥{{ scope.row.ActualAmount || 0 }}
            </template>
          </el-table-column>
          <el-table-column label="完成状态" width="100">
            <template slot-scope="scope">
              <el-tag :type="scope.row.IsCompleted ? 'success' : 'warning'">
                {{ scope.row.IsCompleted ? '已完成' : '未完成' }}
              </el-tag>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 操作按钮 -->
      <div class="button-section">
        <el-button @click="goBack">返回</el-button>
        <el-button v-if="canContinueConfirm" type="primary" @click="continueConfirm">继续确认</el-button>
      </div>
    </el-card>
  </div>
</template>

<script>
import APIStorage from "@/api/PSI/Purchase/storage";

export default {
  name: 'StorageInfoDetail',
  data() {
    return {
      loading: false,
      storageInfo: {},
      productList: []
    }
  },
  computed: {
    storageId() {
      return this.$route.query.id;
    },
    canContinueConfirm() {
      return this.storageInfo.BillStatus === '15'; // 部分确认状态可以继续确认
    }
  },
  methods: {
    // 获取入库单详情
    async getStorageInfo() {
      if (!this.storageId) {
        this.$message.error('缺少入库单号参数');
        this.goBack();
        return;
      }

      this.loading = true;
      try {
        const response = await APIStorage.getpurchaseStorageInfo({ ID: this.storageId });
        if (response.StateCode === 200) {
          this.storageInfo = response.Data;
          this.productList = response.Data.Detail || [];
          
          // 如果后端还没有返回状态相关字段，设置默认值
          if (!this.storageInfo.BillStatus) {
            this.storageInfo.BillStatus = '20'; // 默认已确认
            this.storageInfo.BillStatusName = '已确认';
          }
        } else {
          this.$message.error(response.Message || '获取入库单详情失败');
          this.goBack();
        }
      } catch (error) {
        console.error('获取入库单详情失败:', error);
        this.$message.error('获取入库单详情失败，请稍后重试');
        this.goBack();
      } finally {
        this.loading = false;
      }
    },

    // 获取状态标签类型
    getStatusTagType(status) {
      const statusMap = {
        '10': 'warning',  // 待确认 - 橙色
        '15': 'primary',  // 部分确认 - 蓝色
        '20': 'success'   // 已确认 - 绿色
      };
      return statusMap[status] || 'success';
    },

    // 继续确认
    continueConfirm() {
      // 跳转到确认页面
      this.$router.push({
        path: '/Purchase/StorageConfirmDetail',
        query: { id: this.storageId }
      });
    },

    // 返回列表
    goBack() {
      this.$router.go(-1);
    }
  },

  mounted() {
    this.getStorageInfo();
  }
}
</script>

<style lang="scss" scoped>
.StorageInfoDetail {
  .info-section, .product-section {
    margin-bottom: 20px;
    
    h3 {
      margin-bottom: 15px;
      color: #303133;
      font-size: 16px;
      font-weight: 600;
      border-bottom: 2px solid #409EFF;
      padding-bottom: 8px;
    }
  }

  .info-item {
    margin-bottom: 10px;
    
    label {
      font-weight: 600;
      color: #606266;
      margin-right: 8px;
    }
    
    span {
      color: #303133;
    }
  }

  .button-section {
    text-align: center;
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #EBEEF5;
    
    .el-button {
      margin: 0 10px;
      min-width: 100px;
    }
  }

  .text-success {
    color: #67C23A;
    font-weight: 600;
  }

  .text-warning {
    color: #E6A23C;
    font-weight: 600;
  }
}
</style>
