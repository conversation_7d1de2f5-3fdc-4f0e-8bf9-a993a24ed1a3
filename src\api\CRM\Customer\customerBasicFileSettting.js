/**
 * Created by preference on 2021/11/25
 *  zmx 
 */

import * as API from '@/api/index'
export default {
  /**  基础档单列表 */
  customerBasicFile_all: params => {
    return API.POST('api/customerBasicFile/all', params)
  },
  /**  基础档单添加 */
  customerBasicFile_create: params => {
    return API.POST('api/customerBasicFile/create', params)
  },
  /**  基础档单修改 */
  customerBasicFile_update: params => {
    return API.POST('api/customerBasicFile/update', params)
  },
  /**  基础档移动 */
  customerBasicFile_move: params => {
    return API.POST('api/customerBasicFile/move', params)
  },
  /**  基础档删除 */
  customerBasicFile_delete: params => {
    return API.POST('api/customerBasicFile/delete', params)
  },
}