<template>
  <div class="BindOpenid" :style="{ 'background-image': `url(${require('@/assets/img/bindOpenID-res.png')})` }">
    <el-button
      v-if="!state && !openID"
      @click="getAuthorizUrl"
      class="custom-unsetFontWeight"
      type="primary"
      style="width: calc(100% - 40px); position: absolute; bottom: 50px; left: 20px; right: 20px"
      round
      >绑定手机号</el-button
    >
    <!-- 输入手机号 -->
    <transition name="slide-fade">
      <div v-if="!showPhoneCode && openID">
        <div class="color_333" style="font-size: 24px; margin-top: 20%">请您输入手机号</div>
        <div class="font_12 color_999">为了方便取得联系，请输入您的常用手机号码</div>
        <el-form @submit.native.prevent>
          <el-form-item>
            <el-input v-model="phoneNumber" class="csutom_phone_input" placeholder="请输入手机号" style="margin-top: 50px"></el-input>
          </el-form-item>
          <el-form-item>
            <el-button @click="onsendPhoneCode" :disabled="setDisabled" class="custom-unsetFontWeight" type="primary" style="width: 100%; margin-top: 20px">
              下一步
            </el-button>
          </el-form-item>
        </el-form>
      </div>
    </transition>
    <!-- 输入验证码 -->
    <transition name="slide-fade-code">
      <div v-if="showPhoneCode && openID" class="check-code">
        <p style="font-size: 26px; color: #333333; margin-bottom: 5px">请输入验证码</p>
        <p class="check-code_title">验证码已发送到您的手机</p>
        <p class="check-code_title">{{ phoneNumber }}</p>
        <div class="dis_flex flex_x_between" style="margin-top: 30px; font-size: 13px; color: #aaaaaa">
          <div>6位数字验证码</div>
          <div v-if="!showDowncount" class="color_main">{{ count }}</div>
        </div>

        <div class="check-code_content">
          <input
            v-for="(c, index) in aCheckCodeInput"
            :key="index"
            type="number"
            v-model="aCheckCodeInput[index]"
            ref="input"
            :class="{ 'g-code-input_color': checkCodeInputComputed[index] !== '' }"
            @input="
              (e) => {
                onInput(e.target.value, index);
              }
            "
            @keydown.delete="
              (e) => {
                onKeydown(e.target.value, index);
              }
            "
            @focus="onFocus"
          />
        </div>
      </div>
    </transition>
  </div>
</template>

<script>
import API from "@/api/Marketing/App/BindOpenid.js";
export default {
  name: "BindOpenid",
  props: {},
  /** 监听数据变化   */
  watch: {
    cIndex() {
      this.resetCaret();
    },
    lastCode(val) {
      if (val) {
        this.$refs.input[this.ctSize - 1].blur();
        this.phoneCode = this.aCheckCodeInput.join("");
        this.setUserBindNetwork();
      }
    },
  },
  /**  引入的组件  */
  components: {},
  /**  Vue 实例的数据对象**/
  data() {
    return {
      aCheckCodeInput: ["", "", "", "", "", ""], // 存储输入验证码内容
      aCheckCodePasteResult: [], // 粘贴的验证码
      showDowncount: true,
      count: "",
      timer: null,
      state: null,
      phoneCode: null,
      showPhoneCode: false,
      phoneNumber: "",
      EnterpriseCode: null,
      openID: null,
      numList: [],
    };
  },
  /**计算属性  */
  computed: {
    // 验证码计算属性
    checkCodeInputComputed() {
      if (this.aCheckCodePasteResult.length === 6) {
        console.log(this.aCheckCodePasteResult);
        return this.aCheckCodePasteResult;
      } else if (this.aCheckCodeInput && Array.isArray(this.aCheckCodeInput) && this.aCheckCodeInput.length === 6) {
        return this.aCheckCodeInput;
      } else if (/^\d{6}$/.test(this.aCheckCodeInput.toString())) {
        return this.aCheckCodeInput.toString().split("");
      } else {
        return new Array(6);
      }
    },
    /**  设置按钮禁选与否  */
    setDisabled() {
      if (!this.phoneNumber) {
        return true;
      }
      if (!/^1[3456789]\d{9}$/.test(this.phoneNumber) && !/^[5689]\d{7}$/.test(this.phoneNumber)) {
        return true;
      }
      return false;
    },
    ctSize() {
      return this.aCheckCodeInput.length;
    },
    cIndex() {
      let i = this.aCheckCodeInput.findIndex((item) => item === "");
      i = (i + this.ctSize) % this.ctSize;
      return i;
    },
    lastCode() {
      return this.aCheckCodeInput[this.ctSize - 1];
    },
  },
  /**  方法集合  */
  methods: {
    /**  发送验证码  */
    onsendPhoneCode() {
      let that = this;
      if (!this.phoneNumber) {
        this.$message.error("请输入手机号码");
        return;
      }
      if (!/^1[3456789]\d{9}$/.test(this.phoneNumber)) {
        this.$message.error("请输入正确的手机号码");
        return;
      }
      that.sendMicroMallVerificationCode();
    },
    getDownCountCode() {
      let that = this;
      const TIME_COUNT = 60;
      if (!that.timer) {
        that.count = TIME_COUNT;
        that.showDowncount = false;
        that.timer = setInterval(() => {
          if (that.count > 0 && that.count <= TIME_COUNT) {
            that.count -= 1;
          } else {
            that.showDowncount = true;
            clearInterval(that.timer);
            that.timer = null;
          }
        }, 1000);
      }
    },
    getQueryString(name) {
      var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
      var r = window.location.search.substr(1).match(reg);
      if (r != null) return unescape(r[2]);
      return null;
    },
    /**    */
    async getAuthorizUrl() {
      let that = this;
      let tmpWindow = window;
      let EnterpriseCode = this.getQueryString("EnterpriseCode");
      let params = {
        EnterpriseCode: EnterpriseCode,
      };
      let res = await API.getAuthorizUrl(params);
      if (res.StateCode == 200) {
        tmpWindow.location = res.Data.AuthorizationUrl;
      } else {
        that.$message.error(res.Message);
      }
    },
    /**    */
    async getUseropenid() {
      let that = this;
      that.EnterpriseCode = that.getQueryString("state");
      let Code = that.getQueryString("code");
      let params = {
        EnterpriseCode: that.EnterpriseCode,
        Code: Code,
      };
      let res = await API.getUseropenid(params);
      if (res.StateCode == 200) {
        that.openID = res.Data.openId;
      } else {
        that.$message.error(res.Message);
      }
    },
    /**  发送验证码  */
    async sendMicroMallVerificationCode() {
      let that = this;
      that.EnterpriseCode = this.getQueryString("state");
      let params = {
        EnterpriseCode: that.EnterpriseCode, //商户好
        PhoneNumber: that.phoneNumber, //手机号
      };
      let res = await API.sendMicroMallVerificationCode(params);
      if (res.StateCode == 200) {
        that.showPhoneCode = true;
        that.getDownCountCode();
      } else {
        that.$message.error(res.Message);
      }
    },
    /**  绑定账号  */
    async setUserBindNetwork() {
      let that = this;
      let params = {
        EnterpriseCode: that.EnterpriseCode, //商户好
        PhoneNumber: that.phoneNumber, //手机号
        OpenID: that.openID, //openid
        VerificationCode: that.phoneCode, //验证码
      };
      let res = await API.setUserBindNetwork(params);
      if (res.StateCode == 200) {
        this.$router.push({
          name: "BindOpenidSucceed",
          params: {
            phoneNumber: this.phoneNumber,
          },
        });
      } else {
        let isFollow = res.Message.indexOf("43004");
        if (isFollow != -1) {
          that.$message.error("请先关注公众号然后在进行绑定操作");
        }
        else{
          that.$message.error(res.Message);
        }
      }
    },

    onInput(val, index) {
      val = val.replace(/\s/g, "");
      if (index == this.ctSize - 1) {
        this.aCheckCodeInput[this.ctSize - 1] = val[0]; // 最后一个码，只允许输入一个字符。
      } else if (val.length > 1) {
        let i = index;
        for (i = index; i < this.ctSize && i - index < val.length; i++) {
          this.aCheckCodeInput[i] = val[i];
        }
        this.resetCaret();
      }
    },
    // 重置光标位置。
    resetCaret() {
      this.$refs.input[this.ctSize - 1].focus();
    },
    onFocus() {
      // 监听 focus 事件，将光标重定位到“第一个空白符的位置”。
      let index = this.aCheckCodeInput.findIndex((item) => item === "");
      index = (index + this.ctSize) % this.ctSize;
      this.$refs.input[index].focus();
    },
    onKeydown(val, index) {
      if (val === "") {
        // 删除上一个input里的值，并对其focus。
        if (index > 0) {
          this.aCheckCodeInput[index - 1] = "";
          this.$refs.input[index - 1].focus();
        }
      }
    },

    reset() {
      // 重置。一般是验证码错误时触发。
      this.ct = this.ct.map(() => "");
      this.resetCaret();
    },
  },
  /** 创建实例之前执行函数   */
  beforeCreate() {},
  /**  实例创建完成之后  */
  created() {
    this.state = this.getQueryString("state");
    if (this.state == null) {
      this.getAuthorizUrl();
    } else {
      this.getUseropenid();
    }
  },
  /**  在挂载开始之前被调用  */
  beforeMount() {},
  /**  实例被挂载后调用  */
  mounted() {
    this.resetCaret();
  },
  /**  数据更新 之前 调用  */
  beforeUpdate() {},
  /** 数据更新 完成 调用   */
  updated() {},
  /**  实例销毁之前调用  */
  beforeDestroy() {
    this.state = null;
    this.openID = null;
    this.EnterpriseCode = null;
  },
  /**  实例销毁后调用  */
  destroyed() {},
};
</script>

<style lang="scss">
.BindOpenid {
  height: 100%;
  background-repeat: no-repeat;
  background-position: center;
  background-size: cover;
  padding: 20px;
  position: relative;

  .csutom_phone_input {
    .el-input__inner {
      border: unset;
      border-bottom: 1px solid #dcdfe6;
      border-radius: unset;
      background-color: unset;
    }
    .el-input-group__append {
      background-color: unset;
      border: unset;
      border-radius: unset;
      border-bottom: 1px solid #dcdfe6;
    }
  }

  .custom-unsetFontWeight {
    font-weight: unset;
  }

  .check-code {
    width: 100%;
    padding: 4px 0 8px 0;
  }

  .check-code .check-code_title {
    font-size: 14px;
    color: #666;
  }

  .check-code .check-code_content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    padding: 28px 0 28px 0;
    margin: 0 auto;
  }

  .check-code .check-code_content input {
    width: calc((100vw - 96px) / 6);
    height: calc((100vw - 96px) / 6);
    font-size: 34px;
    text-align: center;
    border: none;
    outline: none;
    border: solid 1px #dcdcdc;
    border-radius: 4px;
    -moz-appearance: textfield;
  }

  .check-code .check-code_content input.g-code-input_color {
    border-color: #dcdcdc;
  }

  .check-code .check-code_content input::-webkit-outer-spin-button,
  .check-code .check-code_content input::-webkit-inner-spin-button {
    appearance: none;
    margin: 0;
  }

  .slide-fade-enter {
    opacity: 0;
    transform: translate(100%, 0);
  }
  .slide-fade-enter-active {
    z-index: 1;
  }
  .slide-fade-leave-active {
    // transition: all 0.8s cubic-bezier(1, 0.5, 0.8, 1);
    z-index: 0;
  }
  .slide-fade-code-active {
    opacity: 0;
    transform: translate(100%, 0);
    z-index: 2;
  }
}
</style>
