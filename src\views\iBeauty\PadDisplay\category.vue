<template>
  <div class="Groupon content_body PadDisplayCategory" v-loading="loading">
    <!-- 搜索 -->
    <div class="nav_header">
      <el-row>
        <el-col :span="20">
          <el-form :inline="true" size="small" class="demo-form-inline" @keyup.enter.native="handleSearch">
            <el-form-item label="分类名称">
              <el-input v-model="name" @clear="handleSearch" clearable placeholder="输入分类名称搜索"></el-input>
            </el-form-item>
            <el-form-item label="有效性">
              <el-select @change="handleSearch" v-model="active" @clear="handleSearch" placeholder="请选择" clearable>
                <el-option label="有效" :value="true"></el-option>
                <el-option label="无效" :value="false"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleSearch">搜索</el-button>
            </el-form-item>
          </el-form>
        </el-col>
        <el-col :span="4" class="text_right">
          <el-button type="primary" size="small" @click="showDialog('add')">新增</el-button>
        </el-col>
      </el-row>
    </div>
    <!-- 表格 -->
    <div>
      <el-table size="small" :data="CategoryList" row-key="ID" :tree-props="{ children: 'Child' }" v-loading="CategoryListLoading">
        <el-table-column prop="Name" label="分类名称"></el-table-column>
        <el-table-column label="移动" min-width="180px">
          <template slot-scope="scope">
            <el-button
              size="small"
              type="primary"
              circle
              icon="el-icon-upload2"
              :disabled="scope.$index == 0"
              @click="upOneClick(scope.row, scope.$index)"
            ></el-button>
            <el-button
              size="small"
              type="primary"
              circle
              :disabled="scope.$index == 0"
              icon="el-icon-top"
              @click="upClick(scope.row, scope.$index)"
            ></el-button>
            <el-button
              size="small"
              type="primary"
              circle
              :disabled="scope.$index == CategoryList - 1"
              icon="el-icon-bottom"
              @click="downClick(scope.row, scope.$index)"
            ></el-button>
            <el-button
              size="small"
              type="primary"
              circle
              :disabled="scope.$index == CategoryList - 1"
              icon="el-icon-download"
              @click="downOneClick(scope.row, scope.$index)"
            ></el-button>
          </template>
        </el-table-column>
        <el-table-column label="类别图片">
          <template slot-scope="scope">
            <el-image style="width: 35px; height: 35px" v-if="scope.row.ImageURL" :src="scope.row.ImageURL" fit="cover"></el-image>
          </template>
        </el-table-column>
        <el-table-column prop="Active" label="有效性" :formatter="formatStatus"></el-table-column>
        <el-table-column label="操作" width="180px">
          <template slot-scope="scope">
            <el-button v-if="!scope.row.ParentID" type="primary" size="small" @click="showDialog('edit', scope.row)">编辑 </el-button>
            <el-button v-else type="primary" size="small" @click="showDialog('editSon', scope.row)">编辑</el-button>
            <el-button v-if="!scope.row.ParentID" type="primary" size="small" @click="showDialog('son', scope.row)"> 新增子分类</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <!-- 添加分类的弹窗 -->
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="1000px" @close="closeDialog">
      <el-scrollbar class="el_scrollbar_height">
        <el-form size="small" :model="Category" ref="Category" label-width="100px" class="demo-ruleForm">
          <el-form-item v-show="dialogVisibleType == 'son' || dialogVisibleType == 'editSon'" label="上级分类">
            <el-input v-model="Category.ParName" disabled autocomplete="off"></el-input>
          </el-form-item>
          <el-form-item label="分类名称" prop="Name" :rules="[{ required: true, message: '分类名称不能为空' }]">
            <el-input v-model="Category.Name" autocomplete="off"></el-input>
          </el-form-item>
          <el-form-item label="有效性" prop="Active" :rules="[{ required: true, message: '请选择有效性' }]">
            <el-radio-group v-model="Category.Active">
              <el-radio :label="true">是</el-radio>
              <el-radio :label="false">否</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="分类图片" prop="ImageURL" :rules="[{ required: true, message: '图片不能为空' }]">
            <el-upload
              action="#"
              :limit="1"
              list-type="picture-card"
              :file-list="Category.ImageURL"
              :before-upload="($event) => beforeAvatarUpload($event, true)"
              :class="customUploadClass()"
              :on-exceed="onUploadExceed"
              multiple
            >
              <i slot="default" class="el-icon-camera-solid" style="font-size: 40px; color: #999"></i>
              <div slot="file" slot-scope="{ file }">
                <el-image
                  class="el-upload-list__item-thumbnail"
                  :id="file.uid"
                  :src="file.url"
                  :preview-src-list="showFileList_1"
                  :z-index="9999"
                  fit="cover"
                ></el-image>
                <span class="el-upload-list__item-actions">
                  <span class="el-upload-list__item-preview" @click="DialogPreview(file)">
                    <i class="el-icon-zoom-in"></i>
                  </span>

                  <span class="el-upload-list__item-delete" @click="handleRemove()">
                    <i class="el-icon-delete"></i>
                  </span>
                </span>
              </div>
            </el-upload>
            <span class="font_12 color_999" v-if="dialogVisibleType != 'son' && dialogVisibleType != 'editSon'">建议尺寸比1:1</span>
            <span class="font_12 color_999" v-else>建议尺寸比16:9</span>
          </el-form-item>

          <el-form-item label="分类详情:" v-if="dialogVisibleType != 'son' && dialogVisibleType != 'editSon'">
            <!-- 富文本编辑器 -->
            <quill-editor ref="myTextEditor" v-model="Category.Memo" :options="editorOption" style="width: 90%; height: 400px"></quill-editor>
          </el-form-item>
        </el-form>
      </el-scrollbar>
      <span slot="footer" class="dialog-footer">
        <el-button size="small" @click="closeDialog" v-prevent-click>取 消</el-button>
        <el-button size="small" type="primary" @click="addCategory" :loading="isAddLoading" v-prevent-click>保 存</el-button>
      </span>
    </el-dialog>
    <!-- 图片上传组件辅助-->
    <el-upload class="goodsCategory-image-uploader" action="#" accept="image/*" :show-file-list="false" :before-upload="goodsCategoryBeforeUpload" multiple> </el-upload>
  </div>
</template>

<script>
import API from "@/api/iBeauty/PadDisplay/category";
import utils from "@/components/js/utils.js";

// 引入富文本编辑样式以及组件
import "quill/dist/quill.core.css";
import "quill/dist/quill.snow.css";
import "quill/dist/quill.bubble.css";
import { quillEditor } from "vue-quill-editor";
export default {
  name: "PadDisplayCategory",
  created() {
    var that = this;
    // 富文本编辑器的配置
    var toolbarOptions = [
      ["bold", "italic", "underline", "strike"], // 加粗 斜体 下划线 删除线 -----['bold', 'italic', 'underline', 'strike']
      ["blockquote", "code-block"], // 引用  代码块-----['blockquote', 'code-block']
      [{ header: 1 }, { header: 2 }], // 1、2 级标题-----[{ header: 1 }, { header: 2 }]
      [{ list: "ordered" }, { list: "bullet" }], // 有序、无序列表-----[{ list: 'ordered' }, { list: 'bullet' }]
      [{ script: "sub" }, { script: "super" }], // 上标/下标-----[{ script: 'sub' }, { script: 'super' }]
      [{ indent: "-1" }, { indent: "+1" }], // 缩进-----[{ indent: '-1' }, { indent: '+1' }]
      [{ direction: "rtl" }], // 文本方向-----[{'direction': 'rtl'}]
      [{ size: ["small", false, "large", "huge"] }], // 字体大小-----[{ size: ['small', false, 'large', 'huge'] }]
      [{ header: [1, 2, 3, 4, 5, 6, false] }], // 标题-----[{ header: [1, 2, 3, 4, 5, 6, false] }]
      [{ color: [] }, { background: [] }], // 字体颜色、字体背景颜色-----[{ color: [] }, { background: [] }]
      [{ font: [] }], // 字体种类-----[{ font: [] }]
      [{ align: [] }], // 对齐方式-----[{ align: [] }]
      ["clean"], // 清除文本格式-----['clean']
      ["image"],
    ]; // 链接、图片、视频-----['link', 'image', 'video']

    that.editorOption = {
      modules: {
        //工具栏定义的
        toolbar: {
          container: toolbarOptions,
          handlers: {
            image: function (val) {
              if (val) {
                // 通过input的type=file唤醒选择弹框，选择之后自定义上传路径
                document.querySelector(".goodsCategory-image-uploader input").click();
              } else {
                this.quill.format("image", false);
              }
            },
          },
        },
      },
      //主题
      theme: "snow",
      placeholder: "请输入正文",
    };
  },
  components: {
    quillEditor,
  },
  data() {
    return {
      loading: false,
      isAddLoading: false,
      CategoryListLoading: false,
      name: "",
      active: null,
      CategoryList: [], //分类列表
      Category: {
        ParName: "",
        Name: "",
        ParentID: 0,
        Active: true,
        ImageURL: [],
        Memo: "",
      },
      showFileList_1: null, //分类主图预览
      moveIndex: "", //移动类型
      moveID: "", //移动ID
      // 添加分类的弹窗是否开启
      activeIndex: "first",
      dialogVisible: false,
      dialogVisibleType: "",
      dialogTitle: "",
    };
  },

  mounted() {
    var that = this;
    that.handleSearch();
  },

  methods: {
    /**  详情上传图片  */
    goodsCategoryBeforeUpload(file) {
      let that = this;
      // let size = file.size / 1024 < 200;
      // if (!size) {
      //   this.$message.error("上传图片大小不能超过 200kb!");
      //   return false;
      // }
      utils.getImageBase64(file).then((base64) => {
        this.upload_addAttachment(base64).then((AttachmentURL) => {
          let quill = that.$refs.myTextEditor.quill;
          let len = quill.getSelection().index;
          quill.insertEmbed(len, "image", AttachmentURL);
          quill.setSelection(len + 1);
        });
      });

      return false;
    },
    /** 修改图片上传样式   */
    customUploadClass() {
      let hidden = "";
      if (this.Category.ImageURL.length > 0) {
        hidden = " hiddenPicturCard";
      }
      if (this.dialogVisibleType != "son" && this.dialogVisibleType != "editSon") {
        return "categoryImg" + hidden;
      } else {
        return "childCategoryImg" + hidden;
      }
    },
    // //状态显示转换
    formatStatus: function (row) {
      return row.Active ? "有效" : "无效";
    },
    // 上传之前
    beforeAvatarUpload(file) {
      let that = this;
      // const isLt2M = file.size / 1024 < 200;
      // if (!isLt2M) {
      //   that.$message.error("上传图片大小不能超过 200kb!");
      //   return false;
      // }
      utils.getImageBase64(file).then((base64) => {
        that.upload_addAttachment(base64).then((AttachmentURL) => {
          that.Category.ImageURL = [{ name: "", url: AttachmentURL }];
          that.showFileList_1 = [base64];
        });
      });
      // let reader = new FileReader();
      // reader.readAsDataURL(file);
      // reader.onload = function (evt) {
      //   let base64 = evt.target.result;
      //   that.$nextTick(function () {
      //     that.Category.ImageURL = [{ name: "", url: base64 }];
      //     that.showFileList_1 = [base64];
      //   });
      // };
      return false;
    },
    /**  超出数量上传回调  */
    onUploadExceed() {
      this.$message.error("分类主图只能上传一张图片！");
    },
    // 查看大图
    DialogPreview(file) {
      document.getElementById(file.uid).click();
    },
    // 删除图片
    handleRemove() {
      let that = this;
      that.Category.ImageURL = [];
      that.showFileList_1 = [];
    },
    // 保存
    handleSave() {
      const that = this;
      let param = {
        Name: that.Category.Name,
        ParentID: that.Category.ParentID,
        Active: that.Category.Active,
        ImageURL: that.Category.ImageURL.length ? that.Category.ImageURL[0].url : null,
        Memo: that.Category.Memo ? that.Category.Memo : "",
      };
      that.isAddLoading = true;
      if (that.dialogVisibleType == "add" || that.dialogVisibleType == "son") {
        API.Create(param)
          .then((res) => {
            if (res.StateCode == 200) {
              that.handleSearch();
              that.$message.success({
                message: "添加成功",
                duration: 2000,
              });
            } else {
              that.$message.error({
                message: res.Message,
                duration: 2000,
              });
            }
          })
          .finally(() => {
            that.isAddLoading = false;
            that.dialogVisible = false;
          });
      } else if (that.dialogVisibleType == "edit" || that.dialogVisibleType == "editSon") {
        param.ID = that.Category.ID;
        API.Update(param)
          .then((res) => {
            if (res.StateCode == 200) {
              that.handleSearch();
              that.$message.success({
                message: "修改成功",
                duration: 2000,
              });
            } else {
              that.$message.error({
                message: res.Message,
                duration: 2000,
              });
            }
          })
          .finally(() => {
            that.isAddLoading = false;
            that.dialogVisible = false;
          });
      }
    },
    // 点击确定
    addCategory() {
      const that = this;
      this.$refs.Category.validate((valid) => {
        if (valid) {
          that.handleSave();
        }
      });
    },

    //搜索
    handleSearch() {
      this.getCategoryList();
    },
    // 获取分类列表
    getCategoryList() {
      const that = this;
      const params = {
        Name: that.name,
        Active: that.active,
      };
      that.CategoryListLoading = true;
      API.CategoryAll(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.CategoryList = res.Data;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(() => {
          that.CategoryListLoading = false;
        });
    },
    // 获取详情的简介
    CategoryMemo(ID) {
      const that = this;
      const params = {
        ID,
      };
      API.Memo(params).then((res) => {
        if (res.StateCode == 200) {
          that.Category.Memo = res.Message;
        } else {
          that.$message.error({
            message: res.Message,
            duration: 2000,
          });
        }
      });
    },
    showDialog(type, row) {
      const that = this;
      let ParName;
      that.dialogVisible = true;
      that.dialogVisibleType = type;
      this.$refs.Category ? this.$refs.Category.resetFields() : "";
      switch (type) {
        case "add":
          that.dialogTitle = "添加分类";
          that.Category = {
            Name: "",
            ParentID: 0,
            Active: true,
            ImageURL: [],
            Memo: "",
          };
          break;
        case "edit":
          that.dialogTitle = "编辑分类";
          if (row.ImageURL) {
            that.CategoryMemo(row.ID);
            that.showFileList_1 = [row.ImageURL];
            that.Category = {
              ID: row.ID,
              Name: row.Name,
              ParentID: 0,
              Active: row.Active,
              ImageURL: [{ name: "", url: row.ImageURL }],
              Memo: row.Memo,
            };
          } else {
            that.showFileList_1 = [];
            that.Category = {
              ID: row.ID,
              Name: row.Name,
              ParentID: 0,
              Active: row.Active,
              ImageURL: [],
              Memo: row.Memo,
            };
          }

          break;
        case "editSon":
          that.dialogTitle = "编辑子分类";

          that.CategoryList.forEach((item) => {
            if (item.ID == row.ParentID) {
              ParName = item.Name;
            }
          });
          if (row.ImageURL) {
            that.showFileList_1 = [row.ImageURL];
            that.Category = {
              ID: row.ID,
              ParName: ParName ? ParName : "",
              Name: row.Name,
              ParentID: row.ParentID,
              Active: row.Active,
              ImageURL: [{ url: row.ImageURL }],
              Memo: row.Memo ? row.Memo : "",
            };
          } else {
            that.showFileList_1 = [];
            that.Category = {
              ID: row.ID,
              ParName: ParName ? ParName : "",
              Name: row.Name,
              ParentID: row.ParentID,
              Active: row.Active,
              ImageURL: [],
              Memo: row.Memo ? row.Memo : "",
            };
          }

          break;
        case "son":
          that.dialogTitle = "添加子分类";
          that.Category = {
            ParName: row.Name,
            Name: "",
            ParentID: row.ID,
            Active: true,
            ImageURL: [],
            Memo: "",
          };
          break;
      }
    },
    closeDialog() {
      this.$refs.Category ? this.$refs.Category.resetFields() : "";
      this.Category.ImageURL = [];
      this.dialogVisible = false;
    },

    // 移动首部
    upOneClick: function (row) {
      var that = this;
      that.moveIndex = 1;
      that.moveID = row.ID;
      that.setRecursion(that.CategoryList);
    },
    // 移动尾部
    downOneClick: function (row) {
      var that = this;
      that.moveIndex = 2;
      that.moveID = row.ID;
      that.setRecursion(that.CategoryList);
    },
    // 向上
    upClick: function (row) {
      var that = this;
      that.moveIndex = 3;
      that.moveID = row.ID;
      that.setRecursion(that.CategoryList);
    },
    // 向下
    downClick: function (row) {
      var that = this;
      that.moveIndex = 4;
      that.moveID = row.ID;
      that.setRecursion(that.CategoryList);
    },
    // 递归
    setRecursion(data) {
      var that = this;
      var index = data.length;
      var moveId = "",
        beforeId = "",
        destParentID = 0;
      for (let i = 0; i < index; i++) {
        if (data[i].ID == that.moveID) {
          if (that.moveIndex == 1) {
            moveId = data[i].ID;
            beforeId = "";
            destParentID = data[i].ParentID;
          } else if (that.moveIndex == 2) {
            moveId = data[i].ID;
            beforeId = data[index - 1].ID;
            destParentID = data[i].ParentID;
          } else if (that.moveIndex == 3) {
            moveId = data[i].ID;
            destParentID = data[i].ParentID;
            if (i == 0 || i == 1) {
              beforeId = "";
            } else {
              beforeId = data[i - 2].ID;
            }
          } else {
            moveId = data[i].ID;
            destParentID = data[i].ParentID;
            if (i == index - 1) {
              beforeId = data[i == 0 ? 0 : i - 1].ID;
            } else {
              beforeId = data[i + 1].ID;
            }
          }
          that.moveCategory(moveId, beforeId, destParentID);
          return false;
        }
        if (data[i].Child) {
          that.setRecursion(data[i].Child);
        }
      }
    },
    moveCategory(moveId, beforeId, destParentID) {
      var that = this;
      that.loading = true;
      var params = {
        MoveID: moveId,
        BeforeID: beforeId,
        DestParentID: destParentID,
      };
      API.Move(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.$message.success({
              message: "移动成功",
              duration: 2000,
            });
            that.handleSearch();
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },
    /**    */
    async upload_addAttachment(base64) {
      let that = this;
      let params = { AttachmentURL: base64 };
      that.logding = true;
      let res = await API.upload_addAttachment(params);
      if (res.StateCode == 200) {
        return res.Data.AttachmentURL;
      } else {
        that.$message.error(res.Message);
      }
      that.logding = false;
    },
  },
};
</script>

<style lang="scss" >
.PadDisplayCategory {
  // 移动列中禁用按钮的样式
  .el-button--primary.is-disabled,
  .el-button--primary.is-disabled:active,
  .el-button--primary.is-disabled:focus,
  .el-button--primary.is-disabled:hover {
    color: #fff !important;
    background-color: #81D8D0 !important;
    border-color: #81D8D0 !important;
  }

  .el_scrollbar_height {
    height: 55vh;
    .el-scrollbar__wrap {
      overflow-x: hidden !important;
    }
    #container {
      width: 100%;
      height: 55vh;
    }
    .categoryImg {
      .el-upload-list--picture-card .el-upload-list__item {
        width: 100px;
        height: 100px;
      }
      .el-upload.el-upload--picture-card {
        width: 100px !important;
        height: 100px !important;
        line-height: 115px;
      }
    }
    .childCategoryImg {
      .el-upload-list--picture-card .el-upload-list__item {
        width: 160px;
        height: 90px;
      }
      .el-upload.el-upload--picture-card {
        width: 160px !important;
        height: 90px !important;
        line-height: 105px;
      }
    }
    .hiddenPicturCard {
      .el-upload--picture-card {
        display: none;
      }
    }
    .ql-container {
      img {
        width: 100%;
      }
    }
  }
}
</style>