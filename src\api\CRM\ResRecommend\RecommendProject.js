import * as API from '../../index';
export default {
    //项目列表
    skinTreatConfig:params =>{
        return API.POST('api/skinTreatConfig/all',params)
    },
    //创建检测项目
    create:params =>{
        return API.POST('api/skinTreatConfig/create',params)
    },
    // 编辑检测项目
    update:params =>{
        return API.POST('api/skinTreatConfig/update',params)
    },
    skinCategory: params => {
        return API.POST("api/skinCategory/all", params)
    },
    //仪器编号
    skinInstrument: params => {
        return API.POST("api/skinInstrument/list", params)
    },
    // 标准状态
    skinCondition:params =>{
        return API.POST("api/skinCondition/all",params)
    },
}