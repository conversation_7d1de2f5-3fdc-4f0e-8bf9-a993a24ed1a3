<template>
  <div class="customerFollowUpRecord">
    <el-scrollbar class="custom-scrollbar_hidden-x">
      <el-row>
        <el-col :offset="2" :span="6">
          <el-button type="primary" size="small" @click="addFollowUp">新增</el-button>
        </el-col>
      </el-row>
      <el-row class="martp_15">
        <el-col :offset="2" :span="22">
          <el-timeline class="csutom-timeline-conten">
            <el-timeline-item v-for="yearItem in activities" :key="'year-' + yearItem.Year" class="position_relative" color="var(--zl-color-orange-primary)" size="large">
              <!-- 年份 -->
              <div class="position_absolute" style="top: 0; left: -75px">
                <span @click="expandTimelineClick(yearItem)" class="bold">
                  <span>{{ yearItem.Year }}</span>
                  <i :class="[yearItem.isYearExpand ? 'el-icon-caret-bottom' : 'el-icon-caret-right', 'color_999', 'marlt_5']"></i>
                </span>
              </div>
              <el-timeline v-show="yearItem.isYearExpand" class="custom-moth-timeline">
                <el-timeline-item v-for="mothItem in yearItem.Child" :key="'moth-' + mothItem.Month" color="var(--zl-color-orange-primary)">
                  <!-- 月 -->
                  <span class="bold" @click="showDetailsClick(mothItem)"
                    >{{ mothItem.Month }}月
                    <i :class="[mothItem.isMothExpand ? 'el-icon-caret-bottom' : 'el-icon-caret-right', 'color_999']"></i>
                  </span>
                  <el-timeline v-show="mothItem.isMothExpand" class="custom-day-timeline">
                    <el-timeline-item v-for="(dayItem, index) in mothItem.Log" :key="getID(dayItem, index)" color="var(--zl-color-orange-primary)">
                      <el-row type="flex" justify="space-between">
                        <el-col :span="3">
                          <p class="bold">
                            {{ dayItem.PlannedOn | formatDateStr("date") }}
                          </p>
                          <span class="font_12 color_999">{{ dayItem.PlannedOn | formatDateStr("time") }}</span>
                        </el-col>
                        <el-col :span="21">
                          <p class="color_333">
                            <span>{{ dayItem.EmployeeName }}</span>
                            <span v-show="dayItem.JobName">[{{ dayItem.JobName }}]</span>
                            <el-tag :type="getTag(dayItem.Type)" class="marlt_10" size="small">{{ getType(dayItem.Type) }}</el-tag>
                            <span style="margin-left: 10px" v-if="!dayItem.Status && dayItem.Type == 10" class="color_main">待跟进</span>
                            <span style="margin-left: 10px" v-if="!dayItem.Status && dayItem.Type == 30" class="color_main">待回访</span>
                          </p>
                          <span class="color_999 font_13">{{ dayItem.EntityName }}</span>
                        </el-col>
                      </el-row>
                      <el-row>
                        <el-card class="martp_5" shadow="always" v-if="dayItem.Type == 10 || dayItem.Type == 20">
                          <el-row v-if="dayItem.AssignName != dayItem.EmployeeName" style="line-height: 1.5">
                            <el-col :span="3" style="color: #909399">指派人员：</el-col>
                            <el-col :span="21" style="color: #606266">{{ dayItem.AssignName }}</el-col>
                          </el-row>
                          <el-row v-if="dayItem.Remark" style="line-height: 1.5">
                            <el-col :span="3" style="color: #909399">任务备注：</el-col>
                            <el-col :span="21" style="color: #606266">{{ dayItem.Remark }}</el-col>
                          </el-row>
                          <el-row v-if="dayItem.MethodName" style="line-height: 1.5">
                            <el-col :span="3" style="color: #909399">{{ getType(dayItem.Type) }}方式：</el-col>
                            <el-col :span="21" style="color: #606266">
                              <span style="margin-right: 10px">{{ dayItem.MethodName }}</span>
                              <span v-if="dayItem.Status">【{{ dayItem.Status }}】</span>
                            </el-col>
                          </el-row>
                          <el-row v-if="dayItem.FollowUpOn" style="line-height: 1.5">
                            <el-col :span="3" style="color: #909399">{{ getType(dayItem.Type) }}时间：</el-col>
                            <el-col :span="21" style="color: #606266">{{ dayItem.FollowUpOn | dateFormat("YYYY-MM-DD HH:mm") }}</el-col>
                          </el-row>
                          <el-row v-if="dayItem.Content" style="line-height: 1.5">
                            <el-col :span="3" style="color: #909399; white-space: pre">{{ getType(dayItem.Type) }}记录：</el-col>
                            <el-col :span="21" style="color: #606266; white-space: pre-wrap">{{ dayItem.Content }}</el-col>
                          </el-row>
                          <el-row class="martp_5" v-if="dayItem.Attachment && dayItem.Attachment.length > 0">
                            <el-col :offset="3" :span="21">
                              <!-- :preview-src-list="dayItem.Attachment.map((val) => val.AttachmentURL)"  -->
                              <el-image class="imgShowBox" v-for="(img, imgIndex) in dayItem.Attachment" :key="imgIndex" :src="img.AttachmentURL + '?x-oss-process=image/resize,h_100,m_lfit'" @click="handlePreviewImage(dayItem.Attachment,img.AttachmentURL)"  fit="cover"></el-image>
                            </el-col>
                          </el-row>
                        </el-card>
                        <el-card class="martp_5" shadow="always" v-if="dayItem.Type == 30">
                          <el-col :span="24" class="martp_5">
                            <el-row v-if="dayItem.CallbackCycle" style="line-height: 1.5">
                              <el-col :span="3" style="color: #909399">回访天数：</el-col>
                              <el-col :span="21" style="color: #606266">{{ dayItem.CallbackCycle }}天</el-col>
                            </el-row>
                            <el-row v-if="dayItem.ProjectName" style="line-height: 1.5">
                              <el-col :span="3" style="color: #909399">回访项目：</el-col>
                              <el-col :span="21" style="color: #606266"
                                ><el-tag size="mini">{{ dayItem.ProjectName }}</el-tag></el-col
                              >
                            </el-row>
                            <el-row v-if="dayItem.Remark" style="line-height: 1.5">
                              <el-col :span="3" style="color: #909399; white-space: pre">回访内容：</el-col>
                              <el-col :span="21" style="color: #606266">{{ dayItem.Remark }}</el-col>
                            </el-row>

                            <el-row v-if="dayItem.Content" style="line-height: 1.5">
                              <el-col :span="3" style="color: #909399; white-space: pre">回访记录：</el-col>
                              <el-col :span="21" style="color: #606266">{{ dayItem.Content }}</el-col>
                            </el-row>
                            <!-- :preview-src-list="dayItem.Attachment.map((val) => val.AttachmentURL)"  -->
                            <el-row class="martp_5">
                              <el-col :offset="3" :span="21">
                                <el-image class="imgShowBox" v-for="(img, imgIndex) in dayItem.Attachment" :key="imgIndex" :src="img.AttachmentURL + '?x-oss-process=image/resize,h_100,m_lfit'" @click="handlePreviewImage(dayItem.Attachment,img.AttachmentURL)"  fit="cover"></el-image>
                              </el-col>
                            </el-row>
                          </el-col>
                        </el-card>

                        <el-card class="martp_5" shadow="always" v-if="dayItem.Type == 40">
                          <el-col :span="24" class="martp_5">
                            <el-row v-if="dayItem.Content" style="line-height: 1.5">
                              <el-col :span="3" style="color: #909399">跟进内容：</el-col>
                              <el-col :span="21" style="color: #606266">
                                {{ dayItem.Content }}
                              </el-col>
                            </el-row>
                            <!-- :preview-src-list="dayItem.Attachment.map((val) => val.AttachmentURL)"  -->
                            <el-row class="martp_5">
                              <el-col :offset="3" :span="21">
                                <el-image class="imgShowBox" v-for="(img, imgIndex) in dayItem.Attachment" :key="imgIndex" :src="img.AttachmentURL + '?x-oss-process=image/resize,h_100,m_lfit'" @click="handlePreviewImage(dayItem.Attachment,img.AttachmentURL)"  fit="cover"></el-image>
                              </el-col>
                            </el-row>
                          </el-col>
                        </el-card>
                      </el-row>
                    </el-timeline-item>
                  </el-timeline>
                </el-timeline-item>
              </el-timeline>
            </el-timeline-item>
          </el-timeline>
        </el-col>
      </el-row>
    </el-scrollbar>
    <!-- 新建、处理弹出框 -->
    <el-dialog :title="isAdd ? '新建跟进' : '处理跟进'" append-to-body :visible.sync="dialogVisible" width="800px" custom-class="custom-dialog" @close="closeAddFollowUpDialog">
      <el-scrollbar class="el_scrollbar_height_followup" style="height: 65vh">
        <div class="information" style="background-color: #f7f8fa; padding: 8px 8px 8px 8px; margin-bottom: 5px">
          <el-row type="flex" align="" :class="AssignName != EmployeeName || PlannedRemark ? 'border-bottom' : ''" style="padding-bottom: 5px">
            <el-col :span="2">
              <el-avatar :size="50" :src="circleUrl"></el-avatar>
            </el-col>
            <el-col :span="22">
              <el-row type="flex" justify="space-between">
                <el-col :span="24">
                  <strong class="marrt_5 font_18">{{ customerDetail.Name }}</strong>
                  <el-image v-if="customerDetail.Gender == 2" style="width: 8px; height: 12px" :src="require('@/assets//img//gender-female.png')"></el-image>
                  <el-image v-if="customerDetail.Gender == 1" style="width: 8px; height: 12px" :src="require('@/assets/img/gender-male.png')"></el-image>
                </el-col>
              </el-row>
              <el-col justify="space-between">
                <el-col :span="8" class="color_999 martp_10"
                  >手机号：<span class="color_333">{{ customerDetail.PhoneNumber }}</span></el-col
                >
                <el-col :span="8" class="color_999 martp_10"
                  >客户编号：<span class="color_333">{{ customerDetail.Code }}</span></el-col
                >
                <el-col :span="8" class="color_999 martp_10"
                  >注册时间：<span class="color_333">{{ customerDetail.CreatedOn }}</span></el-col
                >
                <el-col :span="8" class="color_999 martp_10"
                  >介绍人：<span class="color_333">{{ customerDetail.IntroducerName }}</span></el-col
                >
                <el-col :span="8" class="color_999 martp_10"
                  >渠道来源：<span class="color_333">{{ customerDetail.ChannelName }}</span></el-col
                >
                <el-col :span="8" class="color_999 martp_10"
                  >信息来源：<span class="color_333">{{ customerDetail.CustomerSourceName }}</span></el-col
                >
              </el-col>
            </el-col>
          </el-row>
          <el-form size="small" v-if="!isAdd">
            <el-row>
              <el-col :span="24">
                <el-form-item v-if="AssignName != EmployeeName" style="margin-bottom: 0">
                  <el-col :span="3">指派人：</el-col>
                  <el-col :span="21">{{ AssignName }}</el-col>
                </el-form-item>
              </el-col>
              <el-col :span="24" v-if="PlannedRemark">
                <el-form-item style="margin-bottom: 0">
                  <el-col :span="3">任务备注：</el-col>
                  <el-col :span="21"> {{ PlannedRemark }}</el-col>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>
        <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="110px" size="small">
          <el-row>
            <el-col :span="24">
              <el-form-item label="跟进方式" prop="FollowUpMethodID">
                <el-radio-group v-model="ruleForm.FollowUpMethodID">
                  <el-radio v-for="item in tableDataMethod" :key="item.ID" :label="item.ID">{{ item.Name }}</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="跟进状态" prop="FollowUpStatusID">
                <el-radio-group v-model="ruleForm.FollowUpStatusID">
                  <el-radio v-for="item in tableDataStatus" :key="item.ID" :label="item.ID">{{ item.Name }}</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="跟进记录" prop="FollowUpContent">
                <el-input type="textarea" :rows="5" v-model="ruleForm.FollowUpContent"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="">
                <el-upload :limit="9" class="avatar-uploader" list-type="picture-card" action="#" :file-list="ruleForm.Attachment" :before-upload="commodityMainbeforeUpload" :on-remove="commodityMainRemove" accept="image/*" multiple>
                  <i class="el-icon-plus avatar-uploader-icon"></i>
                  <div slot="file" slot-scope="{ file }" style="height: 100px; widht: 100px">
                    <el-image :id="file.uid" :src="file.AttachmentURL" :preview-src-list="preview_src_list" :z-index="2000" fit="cover" style="height: 100%; width: 100%"></el-image>
                    <span class="el-upload-list__item-actions">
                      <span class="el-upload-list__item-preview" @click="DialogPreview(file)">
                        <i class="el-icon-zoom-in"></i>
                      </span>
                      <span class="el-upload-list__item-preview" @click="commodityMainRemove(file)">
                        <i class="el-icon-delete"></i>
                      </span>
                    </span>
                  </div>
                </el-upload>
              </el-form-item>
            </el-col>
            <el-col :span="24" v-if="isAdd">
              <el-form-item
                label="下次跟进"
                prop="IsNextFollowUp"
                :rules="[
                  {
                    required: false,
                    message: '请选择下次是否跟进',
                    trigger: 'change',
                  },
                ]"
              >
                <el-radio-group v-model="ruleForm.IsNextFollowUp">
                  <el-radio :label="true">是</el-radio>
                  <el-radio :label="false">否</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="24" v-if="isAdd && ruleForm.IsNextFollowUp">
              <el-form-item
                label="下次跟进时间"
                prop="PlannedOn"
                :rules="[
                  {
                    required: ruleForm.IsNextFollowUp,
                    message: '请选择下次跟进时间',
                    trigger: 'change',
                  },
                ]"
              >
                <el-date-picker v-model="ruleForm.PlannedOn" type="datetime" value-format="yyyy-MM-dd HH:mm:ss" :default-time="nextDateTime" placeholder="请选择下次跟进时间"> </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="24" v-if="isAdd && ruleForm.IsNextFollowUp">
              <el-form-item label="备注">
                <el-input type="textarea" :rows="3" v-model="ruleForm.PlannedRemark"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </el-scrollbar>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false" size="small">取 消</el-button>
        <el-button type="primary" size="small" @click="submitFollowUp" v-prevent-click :loading="modalLoading">保 存</el-button>
      </span>
    </el-dialog>
    
    <el-image-viewer v-if="showViewer" :initialIndex="initialIndex" :on-close="closeViewer" :url-list="previewImageList" :z-index="3015"/>
  </div>
</template>

<script>
import APIFollowUp from "@/api/KHS/Setting/followUpConfig.js";
import APIUpload from "@/api/Common/uploadAttachment.js";
import cusAPI from "@/api/CRM/Customer/customer";
import FollowUpAPI from "@/api/iBeauty/Workbench/followUp";
import utils from "@/components/js/utils.js";
import ElImageViewer from "element-ui/packages/image/src/image-viewer";
import Enumerable from "linq";
var dayjs = require("dayjs");

export default {
  name: "customerfollowUpRecord",
  props: {
    customerID: {
      require: true,
    },
  },
  /**   过滤器  */
  filters: {
    formatDateStr(date, type) {
      if (!date) return "";
      // let times = date.split(" ");
      // let dateStr = times[0];
      // dateStr.substr(dateStr.indexOf("-"));

      if (type == "date") {
        // return dateStr.substr(dateStr.indexOf("-") + 1);
        return dayjs(date).format("MM-DD");
      }

      if (type == "time") {
        return dayjs(date).format("HH:mm");
        // return times[1];
      }
      return date;
    },
  },
  /**  引入的组件  */
  components: {
    ElImageViewer,
  },
  /**  Vue 实例的数据对象**/
  data() {
    return {
      addAppointmentVisible: false,
      modalLoading: false,
      isExpandTimeline: true,
      isShowDetails: true,
      dialogVisible: false, //新增、修改弹出框
      assignDialogVisible: false, // 指派弹出框
      isAdd: false, //新增、修改事件判断
      tableDataMethod: [], // 跟进方式
      tableDataStatus: [], // 跟进状态
      followUpEntityData: [], // 跟进部门
      customerDetail: {}, // 顾客信息
      preview_src_list: [],
      AssignName: "", // 指派人
      EmployeeName: "",
      PlannedRemark: "", // 任务备注
      circleUrl: "https://cube.elemecdn.com/3/7c/********************************.png", //默认头像
      ruleForm: {
        FollowUpMethodID: "", // 跟进方式
        FollowUpStatusID: "", // 跟进状态
        FollowUpContent: "", // 跟进记录
        PlannedOn: "", // 计划跟进时间
        IsNextFollowUp: true, // 下次是否跟进
        PlannedRemark: "", // 计划跟进备注
        Attachment: [],
      },
      FollowUpID: "",
      rules: {
        FollowUpMethodID: [{ required: true, message: "请选择跟进方式", trigger: "change" }],
        FollowUpStatusID: [{ required: true, message: "请选择跟进状态", trigger: "change" }],
        FollowUpContent: [{ required: true, message: "请填写跟进记录", trigger: "blur" }],
      },
      activities: [],
      nextDateTime: "",
      IsServicer: true,
      employeeID: "", //当前登录员工ID
      showViewer:false,
      initialIndex:0 ,
      previewImageList:[],
    };
  },
  /**计算属性  */
  computed: {},
  /**  方法集合  */
  methods: {
    
    /**    */
    closeViewer(){
      let that = this;
      that.showViewer = false;
    },
    /**    */
    handlePreviewImage(urls,url){
      let that = this;
      that.showViewer = true;
      that.initialIndex = urls.findIndex((i) => i.AttachmentURL == url);
      that.previewImageList = urls.map(i=>i.AttachmentURL);
    
    },
    /**    */
    clearRecordData() {
      this.activities = [];
    },
    /* 年份点击事件(是否展开时间轴) */
    expandTimelineClick(year) {
      year.isYearExpand = !year.isYearExpand;
    },
    /* 月份点击事件(是否展示具体日期) */
    showDetailsClick(moth) {
      moth.isMothExpand = !moth.isMothExpand;
    },
    /* 点击修改 */
    modifyClick(activityitem) {
      let that = this;
      that.dialogVisible = true;
      that.isAdd = false;
      that.PlannedRemark = "";
      that.AssignName = "";
      that.EmployeeName = "";
      that.getCustomerDetail();
      that.PlannedRemark = activityitem.PlannedRemark;
      that.AssignName = activityitem.AssignName;
      that.EmployeeName = activityitem.EmployeeName;
      that.FollowUpID = activityitem.FollowUpID; //跟进记录ID
      that.ruleForm.FollowUpMethodID = activityitem.FollowUpMethodID; //跟进方式
      that.ruleForm.FollowUpStatusID = activityitem.FollowUpStatusID; //跟进状态
      that.ruleForm.FollowUpContent = activityitem.FollowUpContent; //内容
      that.ruleForm.Attachment = JSON.parse(JSON.stringify(activityitem.Attachment)); //图片
    },
    /* 点击新建 */
    addFollowUp() {
      let that = this;
      that.dialogVisible = true;
      that.isAdd = true;
      that.PlannedRemark = "";
      that.AssignName = "";
      that.EmployeeName = "";
      this.nextDateTime = this.$formatDate(new Date(), "hh:mm:ss");
      that.ruleForm = {
        FollowUpMethodID: "", // 跟进方式
        FollowUpStatusID: "", // 跟进状态
        FollowUpContent: "", // 跟进记录
        PlannedOn: "", // 计划跟进时间
        IsNextFollowUp: true, // 下次是否跟进
        PlannedRemark: "", // 计划跟进备注
        Attachment: [],
      };
      if (this.$refs.ruleForm) {
        this.$refs["ruleForm"].resetFields();
      }
      that.dialogVisible = true;
      that.getCustomerDetail();
    },

    /**  跟进弹窗关闭  */
    closeAddFollowUpDialog() {
      this.ruleForm.Attachment = [];
    },
    /* 点击删除 */
    deleteClick(ID) {
      let that = this;
      that
        .$confirm("此操作将永久删除该条数据, 是否继续?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
        .then(() => {
          FollowUpAPI.deleteFollowUp({ ID }).then((res) => {
            if (res.StateCode == 200) {
              this.$message.success("删除成功");
              that.getCustomerFollowUp();
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    /* 保存 */
    async submitFollowUp() {
      let that = this;
      if (that.isAdd) {
        await that.createFollowUp();
      } else {
        if (that.ruleForm.Attachment.length == 0) {
          that.updateFollowUp();
        } else {
          for (let index = 0; index < that.ruleForm.Attachment.length - 1; index++) {
            that.ruleForm.Attachment[index].AttachmentType = 10;
            that.ruleForm.Attachment[index].AttachmentURL = await utils.getCanvasBase64(that.ruleForm.Attachment[index].AttachmentURL);
          }
          that.updateFollowUp();
        }
      }
    },
    /* 新建保存 */
    createFollowUp() {
      let that = this;
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          that.modalLoading = true;
          let params = that.ruleForm;
          params.CustomerID = that.customerID;
          params.PlannedOn = this.ruleForm.PlannedOn ? this.ruleForm.PlannedOn : "";
          FollowUpAPI.createFollowUp(params)
            .then((res) => {
              if (res.StateCode === 200) {
                that.$message.success({
                  message: "新建成功",
                  duration: 2000,
                });
                that.dialogVisible = false;
                that.getCustomerFollowUp();
              } else {
                that.$message.error({
                  message: res.Message,
                  duration: 2000,
                });
              }
            })
            .finally(function () {
              that.modalLoading = false;
            });
        }
      });
    },
    /* 修改保存 */
    updateFollowUp() {
      let that = this;
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          that.modalLoading = true;
          let params = {
            ID: that.FollowUpID, //跟进记录ID
            FollowUpMethodID: that.ruleForm.FollowUpMethodID, //跟进方式
            FollowUpStatusID: that.ruleForm.FollowUpStatusID, //跟进状态
            FollowUpContent: that.ruleForm.FollowUpContent, //内容
            Attachment: that.ruleForm.Attachment, //图片
          };
          FollowUpAPI.updateFollowUp(params)
            .then((res) => {
              if (res.StateCode === 200) {
                that.$message.success({
                  message: "跟进更新成功",
                  duration: 2000,
                });
                that.dialogVisible = false;
                that.getCustomerFollowUp();
              } else {
                that.$message.error({
                  message: res.Message,
                  duration: 2000,
                });
              }
            })
            .finally(function () {
              that.modalLoading = false;
            });
        }
      });
    },
    /* 上传图片 */
    commodityMainbeforeUpload(file) {
      let that = this;
      utils.getImageBase64(file).then((base64) => {
        this.addAttachment(base64).then((AttachmentURL) => {
          that.$nextTick(() => {
            that.ruleForm.Attachment.push({
              AttachmentType: "10",
              AttachmentURL: AttachmentURL,
            });
          });
        });
      });

      // const isSize3M = file.size / 1024 < 200;
      // if (!isSize3M) {
      //   that.$message.error("上传图片大小不能超过 200kb!");
      //   return false;
      // }

      // let reader = new FileReader();
      // reader.readAsDataURL(file);
      // reader.onload = function (evt) {
      //   let base64 = evt.target.result;
      //   that.$nextTick(() => {
      //     that.ruleForm.Attachment.push({
      //       AttachmentType: 10,
      //       AttachmentURL: base64,
      //     });
      //   });
      // };
      return false;
    },
    /* 查看大图 */
    DialogPreview(file) {
      document.getElementById(file.uid).click();
    },
    /* 删除图片 */
    commodityMainRemove(file) {
      if (file && file.status !== "success") return;
      let that = this;
      let index = that.ruleForm.Attachment.findIndex((item) => item.AttachmentURL == file.AttachmentURL);
      that.ruleForm.Attachment.splice(index, 1);
    },
    /* 获取跟进方式列表 */
    getFollowUpMethod() {
      var that = this;
      var params = {
        Name: "",
        Active: true,
      };
      APIFollowUp.getFollowUpMethod(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.tableDataMethod = res.Data;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },
    /* 获取跟进类型列表 */
    getFollowUpStatus() {
      var that = this;
      var params = {
        Name: "",
        Active: true,
      };
      APIFollowUp.getFollowUpStatus(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.tableDataStatus = res.Data;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },
    /* 获取顾客信息 */
    getCustomerDetail() {
      const that = this;
      cusAPI.getCustomerDetail({ CustomerID: that.customerID }).then((res) => {
        if (res.StateCode == 200) {
          that.customerDetail = res.Data;
        } else {
          that.$message.error({
            message: res.Message,
            duration: 2000,
          });
        }
      });
    },
    /* 获取顾客跟进列表 */
    getCustomerFollowUp() {
      let that = this;
      let params = {
        CustomerID: that.customerID,
      };
      FollowUpAPI.getCustomerFollowUp(params).then((res) => {
        if (res.StateCode == 200) {
          res.Data.forEach((item) => {
            item.isYearExpand = true;
            item.Child.forEach((child) => {
              child.isMothExpand = true;
            });
          });
          that.activities = res.Data;
        }
      });
    },
    /* 获取tag标签名 */
    getType(type) {
      if (type == 10) {
        return "跟进";
      }
      if (type == 20) {
        return "接诊";
      }
      if (type == 30) {
        return "回访";
      }
      if (type == 40) {
        return "营销云";
      }
    },
    /* 获取tag标签颜色 */
    getTag(type) {
      if (type == 10) {
        return "";
      }
      if (type == 20) {
        return "success";
      }
      if (type == 30) {
        return "danger";
      }
      if (type == 40) {
        return "warning";
      }
    },
    getID(dayItem, index) {
      if (dayItem.FollowUpID) {
        return index + "day-" + dayItem.FollowUpID;
      }
      if (dayItem.CallbackRecordID) {
        return index + "day-" + dayItem.CallbackRecordID;
      }
    },

    /** 图片上传   */
    async addAttachment(base64) {
      let that = this;
      let params = { AttachmentURL: base64 };
      let res = await APIUpload.addAttachment(params);
      if (res.StateCode == 200) {
        return res.Data.AttachmentURL;
      } else {
        that.$message.error(res.Message);
      }
    },
  },
  /** 监听数据变化   */
  watch: {
    "ruleForm.Attachment": {
      deep: true,
      immediate: true,
      handler(val) {
        this.preview_src_list = [];
        this.preview_src_list = val.map((i) => i.AttachmentURL);
      },
    },
  },
  /** 创建实例之前执行函数   */
  beforeCreate() {},
  /**  实例创建完成之后  */
  created() {
    let that = this;
    that.preview_src_list = Enumerable.from(that.ruleForm.Attachment)
      .select((val) => val.AttachmentURL)
      .toArray();
  },
  /**  在挂载开始之前被调用  */
  beforeMount() {},
  /**  实例被挂载后调用  */
  mounted() {
    let that = this;
    if (localStorage.getItem('access-user')) {
      that.employeeID = JSON.parse(localStorage.getItem('access-user')).EmployeeID;
    }
    that.getFollowUpStatus();
    that.getFollowUpMethod();
  },
  /**  数据更新 之前 调用  */
  beforeUpdate() {},
  /** 数据更新 完成 调用   */
  updated() {},
  /**  实例销毁之前调用  */
  beforeDestroy() {},
  /**  实例销毁后调用  */
  destroyed() {},
};
</script>

<style lang="scss">
.customerFollowUpRecord {
  height: 100%;
  .custom-scrollbar_hidden-x {
    .el-scrollbar__wrap {
      overflow-x: hidden;
    }
    height: 40vh;
  }
  .custom-scrollbar-class {
    .el-scrollbar__wrap {
      overflow-x: auto !important;
      height: calc(100% + 20px); //多出来的20px是横向滚动条默认的样式
      .el-scrollbar__view {
        white-space: nowrap;
        display: inline-block;
      }
    }
  }
  // .el_scrollbar_height_followup {
  //   height: 65vh;
  //   .el-scrollbar__wrap{
  //     overflow-x:scroll !important;
  //   }
  // }
  .border-bottom {
    border-bottom: 1px solid #cfcfcf;
  }
  .csutom-timeline-conten {
    .el-timeline-item__wrapper {
      padding-top: 48px;
      padding-left: 0px;
      .custom-moth-timeline {
        .el-timeline-item__wrapper {
          padding-top: 0px;
          padding-left: 20px;
          .custom-day-timeline {
            margin-top: 20px;
            margin-left: -20px;
            font-size: 13px !important;
            width: 55%;
            .el-timeline-item__tail {
              display: none !important;
            }
            .el-timeline-item__node--normal {
              width: 10px;
              height: 10px;
            }
            .el-card-form .el-form-item {
              margin-bottom: 5px;
            }
            .el-form-item__label {
              line-height: 18px;
            }
            .el-form-item__content {
              line-height: 18px;
            }
          }
        }
      }
    }
    .el-timeline-item__tail {
      display: block !important;
    }
  }
  .bold {
    color: #333;
    font-weight: bold;
  }
  .button-box {
    margin-bottom: 10px;
    align-items: right;
  }
  .information {
    background-color: #f7f8fa;
    padding: 8px 8px 8px 8px;
    margin-bottom: 5px;
  }
  .imgShowBox {
    width: 110px;
    height: 110px;
    line-height: 110px;
    margin-right: 10px;
    border-radius: 5px;
  }
  .el-card__header {
    padding: 14px 18px;
  }
  .el-card__body {
    padding: 5px 5px 5px 5px !important;
  }
  .el-card-content {
    border: 2px solid #ccc;
    padding: 15px 0;
  }
  .otherStaff {
    border: 1px solid #ebeef5;
    padding: 16px 12px;
  }

  .serviceTypeClass {
    .selectServiceEmp {
      border: solid 2px var(--zl-color-orange-primary);
      box-sizing: border-box;
    }
  }
  .empItem {
    margin-top: 10px;
    margin-left: 10px;
    margin-right: 10px;
  }
  .customer-detail {
    background-color: #ffffff;
    padding: 15px;
    height: 100%;
    box-sizing: border-box;
  }
  .is-always-shadow {
    background-color: #f7f8fa;
  }
  .custom-el-select {
    li {
      line-height: normal;
      height: auto;
    }
  }
  .el-upload--picture-card {
    width: 100px;
    height: 100px;
    font-size: 16px !important;
  }
  .el-upload {
    width: 100px;
    height: 100px;
    line-height: 100px;
    font-size: 16px;
  }
  .el-upload-list--picture-card .el-upload-list__item {
    width: 100px;
    height: 100px;
    line-height: 100px;
    font-size: 16px;
  }
  .over-flow {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}
</style>
