<template>
  <div class="content_body" v-loading="loading">
    <div class="nav_header">
      <el-form :inline="true" size="small" :model="searchSalePerformanceCommissionDetailData" @submit.native.prevent>
        <el-row>
          <!-- <el-form-item v-if="storeEntityList.length > 1" label="开单门店">
            <el-select v-model="searchSalePerformanceCommissionDetailData.EntityID" clearable filterable
              placeholder="请选择门店" :default-first-option="true" @change="handleSalePerformanceCommissionSearch">
              <el-option v-for="item in storeEntityList" :key="item.ID" :label="item.EntityName"
                :value="item.ID"></el-option>
            </el-select>
          </el-form-item> -->
          <el-form-item label="时间筛选">
            <el-date-picker v-model="searchSalePerformanceCommissionDetailData.QueryDate"
              :picker-options="pickerOptions" unlink-panels type="daterange" range-separator="至"
              value-format="yyyy-MM-dd" start-placeholder="开始日期" end-placeholder="结束日期"
              @change="handleSalePerformanceCommissionSearch"></el-date-picker>
          </el-form-item>
          <el-form-item label="商品分类">
            <el-cascader v-model="searchSalePerformanceCommissionDetailData.CategoryID" :options="categoryList"
              :props="cascaderProps" @change="handleSalePerformanceCommissionSearch" clearable></el-cascader>
          </el-form-item>

          <el-form-item label="商品名称">
            <el-input v-model="searchSalePerformanceCommissionDetailData.GoodsName" clearable
              @keyup.enter.native="handleSalePerformanceCommissionSearch" @clear="handleSalePerformanceCommissionSearch"
              placeholder="请输入商品名称"></el-input>
          </el-form-item>
          <el-form-item label="单据编号">
            <el-input v-model="searchSalePerformanceCommissionDetailData.BillID" clearable
              @keyup.enter.native="handleSalePerformanceCommissionSearch" @clear="handleSalePerformanceCommissionSearch"
              placeholder="请输入单据编号"></el-input>
          </el-form-item>
        </el-row>

        <el-form-item label="客户姓名">
          <el-input v-model="searchSalePerformanceCommissionDetailData.CustomerName" clearable
            @keyup.enter.native="handleSalePerformanceCommissionSearch" @clear="handleSalePerformanceCommissionSearch"
            placeholder="请输入客户姓名"></el-input>
        </el-form-item>
        <el-form-item label="客户等级">
          <el-select v-model="searchSalePerformanceCommissionDetailData.CustomerLevelID" placeholder="请选择客户等级"
            @clear="handleSalePerformanceCommissionSearch" clearable @change="handleSalePerformanceCommissionSearch">
            <el-option v-for="item in customerLevelList" :label="item.Name" :value="item.ID" :key="item.ID"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="员工姓名">
          <el-input v-model="searchSalePerformanceCommissionDetailData.EmployeeName" clearable
            @keyup.enter.native="handleSalePerformanceCommissionSearch" @clear="handleSalePerformanceCommissionSearch"
            placeholder="请输入员工姓名"></el-input>
        </el-form-item>
        
        <el-form-item v-if="storeEntityList.length > 1" label="员工归属门店">
            <el-select v-model="searchSalePerformanceCommissionDetailData.EntityID" clearable filterable
              placeholder="请选择门店" :default-first-option="true" @change="handleSalePerformanceCommissionSearch">
              <el-option v-for="item in storeEntityList" :key="item.ID" :label="item.EntityName"
                :value="item.ID"></el-option>
            </el-select>
          </el-form-item>
          
        <el-form-item label="员工职务">
          <el-select v-model="searchSalePerformanceCommissionDetailData.JobID" filterable placeholder="选择员工职务"
            @change="handleSalePerformanceCommissionSearch" clearable>
            <el-option v-for="item in jobTypeList" :key="item.ID" :label="item.JobName" :value="item.ID"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" v-prevent-click @click="handleSalePerformanceCommissionSearch">搜索</el-button>
        </el-form-item>
        <el-form-item>
          <el-button v-if="salePerformanceCommissionDetailExport" type="primary" size="small" :loading="downloadLoading"
            @click="downloadExcel">导出</el-button>
        </el-form-item>
      </el-form>
    </div>
    <el-table size="small" show-summary :summary-method="getSalePerformanceCommissionDetailListSummaries"
      :data="salePerformanceCommissionDetailList">
      <el-table-column prop="SaleBillID" label="订单编号"></el-table-column>
      <el-table-column prop="BillDate" label="下单日期">
        <template slot-scope="scope">{{ scope.row.BillDate | dateFormat('YYYY-MM-DD HH:mm') }}</template>
      </el-table-column>
      <el-table-column prop="EntityName" label="下单门店"></el-table-column>
      <el-table-column label="客户信息">
        <el-table-column prop="CustomerName" label="客户姓名"></el-table-column>
        <el-table-column label="客户等级" prop="CustomerLevelName"></el-table-column>
        <el-table-column prop="CustomerSourceName" label="客户来源"></el-table-column>
        <el-table-column prop="CustomerEntityName" label="所属门店"></el-table-column>
        <el-table-column prop="ChannelName" label="归属渠道"></el-table-column>
      </el-table-column>
      <el-table-column label="员工信息">
        <el-table-column prop="EmployeeName" label="员工姓名"></el-table-column>
        <el-table-column prop="EmployeeID" label="员工编号"></el-table-column>
        <el-table-column prop="JobName" label="员工职务"></el-table-column>
        <el-table-column prop="SaleHandlerName" label="经手人名称" width="100"></el-table-column>
        <el-table-column prop="EmployeeEntityName" label="所属门店"></el-table-column>
      </el-table-column>
      <el-table-column label="商品信息">
        <el-table-column prop="GoodName" label="商品名称"></el-table-column>
        <el-table-column prop="CategoryName" label="商品分类"></el-table-column>
        <el-table-column prop="GoodsTypeName" label="商品类型"></el-table-column>
        <el-table-column prop="Price" label="单价">
          <template slot-scope="scope">
            {{ scope.row.Price | toFixed | NumFormat }}
          </template>
        </el-table-column>

        <el-table-column prop="Quantity" label="数量"></el-table-column>
        <el-table-column prop="PreferentialAmount" label="优惠金额">
          <template slot-scope="scope">
            <span v-if="scope.row.PreferentialAmount < 0" class="color_red">{{ scope.row.PreferentialAmount | toFixed |
              NumFormat }}</span>
            <span v-else-if="scope.row.PreferentialAmount > 0" class="color_green">+{{ scope.row.PreferentialAmount |
              toFixed | NumFormat }}</span>
            <span v-else>0.00</span>
          </template>
        </el-table-column>
        <el-table-column prop="TotalAmount" label="合计金额">
          <template slot-scope="scope">
            {{ scope.row.TotalAmount | toFixed | NumFormat }}
          </template>
        </el-table-column>
        <el-table-column prop="ArrearAmount" label="欠款金额 ">
          <template slot-scope="scope">
            {{ scope.row.ArrearAmount | toFixed | NumFormat }}
          </template>
        </el-table-column>
        <el-table-column prop="PayAmount" label="现金">
          <template slot-scope="scope">
            {{ scope.row.PayAmount | toFixed | NumFormat }}
          </template>
        </el-table-column>
        <el-table-column prop="SavingCardDeductionAmount" label="卡扣金额">
          <template slot-scope="scope">
            {{ scope.row.SavingCardDeductionAmount | toFixed | NumFormat }}
          </template>
        </el-table-column>
        <el-table-column prop="LargessSavingCardDeductionAmount" label="赠卡扣金额">
          <template slot-scope="scope">
            {{ scope.row.LargessSavingCardDeductionAmount | toFixed | NumFormat }}
          </template>
        </el-table-column>
        <el-table-column prop="Scale" label="经手人比例">
          <template slot-scope="scope">
            <span v-if="scope.row.Scale > 0">{{ (scope.row.Scale * 100) | toFixed | NumFormat }}%</span>
            <span v-else>{{ scope.row.Scale }}</span>
          </template>
        </el-table-column>
      </el-table-column>
      <el-table-column label="业绩提成">
        <el-table-column align="right" prop="PayPerformance" label="现金业绩">
          <template align="right" slot-scope="scope">
            <div v-if="scope.row.PayPerformance < 0" class="color_red">{{ scope.row.PayPerformance | toFixed | NumFormat
              }}</div>
            <div v-else-if="scope.row.PayPerformance > 0" class="color_green">+{{ scope.row.PayPerformance | toFixed |
              NumFormat }}</div>
            <div v-else>0.00</div>
          </template>
        </el-table-column>
        <el-table-column align="right" prop="PayCommission" label="现金提成">
          <template align="right" slot-scope="scope">
            <div v-if="scope.row.PayCommission < 0" class="color_red">{{ scope.row.PayCommission | toFixed | NumFormat
              }}</div>
            <div v-else-if="scope.row.PayCommission > 0" class="color_green">+{{ scope.row.PayCommission | toFixed |
              NumFormat }}</div>
            <div v-else>0.00</div>
          </template>
        </el-table-column>
        <el-table-column align="right" prop="SavingCardPerformance" label="卡扣业绩">
          <template align="right" slot-scope="scope">
            <div v-if="scope.row.SavingCardPerformance < 0" class="color_red">{{ scope.row.SavingCardPerformance |
              toFixed | NumFormat }}</div>
            <div v-else-if="scope.row.SavingCardPerformance > 0" class="color_green">+{{ scope.row.SavingCardPerformance
              | toFixed | NumFormat }}</div>
            <div v-else>0.00</div>
          </template>
        </el-table-column>
        <el-table-column align="right" prop="SavingCardCommission" label="卡扣提成">
          <template align="right" slot-scope="scope">
            <div v-if="scope.row.SavingCardCommission < 0" class="color_red">{{ scope.row.SavingCardCommission | toFixed
              | NumFormat }}</div>
            <div v-else-if="scope.row.SavingCardCommission > 0" class="color_green">+{{ scope.row.SavingCardCommission |
              toFixed | NumFormat }}</div>
            <div v-else>0.00</div>
          </template>
        </el-table-column>
        <el-table-column align="right" prop="SavingCardLargessPerformance" label="赠卡扣业绩">
          <template align="right" slot-scope="scope">
            <div v-if="scope.row.SavingCardLargessPerformance < 0" class="color_red">{{
              scope.row.SavingCardLargessPerformance | toFixed | NumFormat }}</div>
            <div v-else-if="scope.row.SavingCardLargessPerformance > 0" class="color_green">
              +{{ scope.row.SavingCardLargessPerformance | toFixed | NumFormat }}
            </div>
            <div v-else>0.00</div>
          </template>
        </el-table-column>
        <el-table-column align="right" prop="SavingCardLargessCardCommission" label="赠卡扣提成">
          <template align="right" slot-scope="scope">
            <div v-if="scope.row.SavingCardLargessCardCommission < 0" class="color_red">
              {{ scope.row.SavingCardLargessCardCommission | toFixed | NumFormat }}
            </div>
            <div v-else-if="scope.row.SavingCardLargessCardCommission > 0" class="color_green">
              +{{ scope.row.SavingCardLargessCardCommission | toFixed | NumFormat }}
            </div>
            <div v-else>0.00</div>
          </template>
        </el-table-column>
        <el-table-column align="right" prop="SpecialBenefitCommission" label="无业绩奖励">
          <template align="right" slot-scope="scope">
            <div v-if="scope.row.SpecialBenefitCommission < 0" class="color_red">{{ scope.row.SpecialBenefitCommission |
              toFixed | NumFormat }}</div>
            <div v-else-if="scope.row.SpecialBenefitCommission > 0" class="color_green">+{{
              scope.row.SpecialBenefitCommission | toFixed | NumFormat }}</div>
            <div v-else>0.00</div>
          </template>
        </el-table-column>
      </el-table-column>
    </el-table>
    <div class="pad_15 text_right">
      <el-pagination background v-if="salePerformanceCommissionDetailPaginations.total > 0"
        @current-change="handleSalePerformanceCommissionPageChange"
        :current-page.sync="salePerformanceCommissionDetailPaginations.page"
        :page-size="salePerformanceCommissionDetailPaginations.page_size"
        :layout="salePerformanceCommissionDetailPaginations.layout"
        :total="salePerformanceCommissionDetailPaginations.total"></el-pagination>
    </div>
  </div>
</template>
<script>
import EntityAPI from '@/api/Report/Common/entity';
import API from '@/api/Report/Employee/salePerformanceCommissionDetailStatement';
import permission from '@/components/js/permission.js';
import APIJob from '@/api/KHS/Entity/jobtype';
import APICategory from '@/api/Report/Goods/SaleStatistics.js';
const dayjs = require('dayjs');
const isoWeek = require('dayjs/plugin/isoWeek');
dayjs.extend(isoWeek);

export default {
  name: 'EmployeeSalePerformanceCommissionDetail',

  beforeRouteEnter (to, from, next) {
    next((vm) => {
      vm.salePerformanceCommissionDetailExport = permission.permission(to.meta.Permission, 'Report-Employee-SalePerformanceCommissionDetail-Export');
    });
  },
  data () {
    return {
      loading: false,
      downloadLoading: false,
      storeEntityList: [], //门店列表
      jobTypeList: [], //职务列表
      searchSalePerformanceCommissionDetailData: {
        EntityID: null,
        QueryDate: [new Date(), new Date()],
        GoodsTypeName: '',
        EmployeeName: '',
        JobID: '',
        CustomerName: '',
        CategoryID: '',
        GoodsName: '',
        CustomerLevelID: '',
      },
      salePerformanceCommissionDetailList: [],
      salePerformanceCommissionDetailSum: {},
      //需要给分页组件传的信息
      salePerformanceCommissionDetailPaginations: {
        page: 1, // 当前位于哪页
        total: 0, // 总数
        page_size: 10, // 1页显示多少条
        layout: 'total, prev, pager, next,jumper', // 翻页属性
      },
      salePerformanceCommissionDetailExport: false,
      pickerOptions: {
        shortcuts: [
          {
            text: '今天',
            onClick: (picker) => {
              let end = new Date();
              let start = new Date();
              picker.$emit('pick', [start, end]);
            },
          },
          {
            text: '本周',
            onClick: (picker) => {
              let end = new Date();
              let weekDay = dayjs(end).isoWeekday();
              let start = dayjs(end)
                .subtract(weekDay - 1, 'day')
                .toDate();
              picker.$emit('pick', [start, end]);
            },
          },
          {
            text: '本月',
            onClick: (picker) => {
              let end = new Date();
              let start = dayjs(dayjs(end).format('YYYY-MM') + '01').toDate();
              picker.$emit('pick', [start, end]);
            },
          },
        ],
      },
      categoryList: [],
      cascaderProps: {
        checkStrictly: true,
        label: 'Name',
        value: 'ID',
        children: 'Child',
      },
      customerLevelList: [],
    };
  },
  methods: {
    //获得当前用户下的权限门店
    getstoreEntityList () {
      var that = this;
      that.loading = true;
      EntityAPI.getStoreEntityList()
        .then((res) => {
          if (res.StateCode == 200) {
            that.storeEntityList = res.Data;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },

    handleSalePerformanceCommissionSearch () {
      var that = this;
      that.salePerformanceCommissionDetailPaginations.page = 1;
      that.salePerformanceCommissionDetail();
    },
    handleSalePerformanceCommissionPageChange (page) {
      this.salePerformanceCommissionDetailPaginations.page = page;
      this.salePerformanceCommissionDetail();
    },
    // 销售搜索
    salePerformanceCommissionDetail () {
      var that = this;
      if (that.searchSalePerformanceCommissionDetailData.QueryDate != null) {
        if (
          dayjs(that.searchSalePerformanceCommissionDetailData.QueryDate[0]).add(366, 'day').valueOf() <
          dayjs(that.searchSalePerformanceCommissionDetailData.QueryDate[1]).valueOf()
        ) {
          that.$message.error('时间筛选范围不能超366天');
          return;
        }
        let CategoryID = '';
        if (that.searchSalePerformanceCommissionDetailData.CategoryID.length == 2) {
          CategoryID = that.searchSalePerformanceCommissionDetailData.CategoryID[1];
        }

        if (that.searchSalePerformanceCommissionDetailData.CategoryID.length == 3) {
          CategoryID = that.searchSalePerformanceCommissionDetailData.CategoryID[2];
        }
        var params = {
          EntityID: that.searchSalePerformanceCommissionDetailData.EntityID,
          StartDate: that.searchSalePerformanceCommissionDetailData.QueryDate[0],
          EndDate: that.searchSalePerformanceCommissionDetailData.QueryDate[1],
          EmployeeName: that.searchSalePerformanceCommissionDetailData.EmployeeName.trim(),
          PageNum: that.salePerformanceCommissionDetailPaginations.page,
          JobID: that.searchSalePerformanceCommissionDetailData.JobID,
          CustomerName: that.searchSalePerformanceCommissionDetailData.CustomerName,
          BillID: that.searchSalePerformanceCommissionDetailData.BillID,
          GoodsTypeName: that.searchSalePerformanceCommissionDetailData.CategoryID.length
            ? that.getGoodsTypeID(that.searchSalePerformanceCommissionDetailData.CategoryID[0])
            : '',
          CategoryID: CategoryID,
          GoodsName: that.searchSalePerformanceCommissionDetailData.GoodsName,
          CustomerLevelID: that.searchSalePerformanceCommissionDetailData.CustomerLevelID,
        };
        that.loading = true;
        API.getEmployeeSalePerformanceCommissionDetail(params)
          .then((res) => {
            if (res.StateCode == 200) {
              that.salePerformanceCommissionDetailSum = res.Data.employeeSalePerformanceCommissionSumStatementForm;
              that.salePerformanceCommissionDetailList = res.Data.employeeSalePerformanceCommissionDetailStatementForms.List;
              that.salePerformanceCommissionDetailPaginations.total = res.Data.employeeSalePerformanceCommissionDetailStatementForms.Total;
              that.salePerformanceCommissionDetailPaginations.page_size = res.Data.employeeSalePerformanceCommissionDetailStatementForms.PageSize;
            } else {
              that.$message.error({
                message: res.Message,
                duration: 2000,
              });
            }
          })
          .finally(function () {
            that.loading = false;
          });
      }
    },

    /**    */
    getGoodsTypeID (type) {
      switch (type) {
        case 10:
          return '产品';
        case 20:
          return '项目';
        case 30:
          return '通用次卡';
        case 40:
          return '时效卡';
        case 50:
          return '储值卡';
        case 60:
          return '套餐卡';
      }
    },
    getSalePerformanceCommissionDetailListSummaries ({ columns }) {
      const sums = [];
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = <span class="font_weight_600">合计</span>;
          return;
        }
        var filter_NumFormat = this.$options.filters['NumFormat'];

        switch (column.property) {
          case 'TotalAmount':
            sums[index] = (
              <span class="font_weight_600">
                {filter_NumFormat(this.salePerformanceCommissionDetailSum ? this.salePerformanceCommissionDetailSum.TotalAmount : 0)}
              </span>
            );
            break;
          case 'ArrearAmount':
            sums[index] = (
              <span class="font_weight_600">
                {filter_NumFormat(this.salePerformanceCommissionDetailSum ? this.salePerformanceCommissionDetailSum.ArrearAmount : 0)}
              </span>
            );
            break;
          case 'PayAmount':
            sums[index] = (
              <span class="font_weight_600">
                {filter_NumFormat(this.salePerformanceCommissionDetailSum ? this.salePerformanceCommissionDetailSum.PayAmount : 0)}
              </span>
            );
            break;
          case 'SavingCardDeductionAmount':
            sums[index] = (
              <span class="font_weight_600">
                {filter_NumFormat(this.salePerformanceCommissionDetailSum ? this.salePerformanceCommissionDetailSum.SavingCardDeductionAmount : 0)}
              </span>
            );
            break;
          case 'LargessSavingCardDeductionAmount':
            sums[index] = (
              <span class="font_weight_600">
                {filter_NumFormat(this.salePerformanceCommissionDetailSum ? this.salePerformanceCommissionDetailSum.LargessSavingCardDeductionAmount : 0)}
              </span>
            );
            break;
          case 'PayPerformance':
            sums[index] = (
              <span class="font_weight_600">
                {filter_NumFormat(this.salePerformanceCommissionDetailSum ? this.salePerformanceCommissionDetailSum.PayPerformance : 0)}
              </span>
            );
            break;
          case 'PayCommission':
            sums[index] = (
              <span class="font_weight_600">
                {filter_NumFormat(this.salePerformanceCommissionDetailSum ? this.salePerformanceCommissionDetailSum.PayCommission : 0)}
              </span>
            );
            break;
          case 'SavingCardPerformance':
            sums[index] = (
              <span class="font_weight_600">
                {filter_NumFormat(this.salePerformanceCommissionDetailSum ? this.salePerformanceCommissionDetailSum.SavingCardPerformance : 0)}
              </span>
            );
            break;
          case 'SavingCardCommission':
            sums[index] = (
              <span class="font_weight_600">
                {filter_NumFormat(this.salePerformanceCommissionDetailSum ? this.salePerformanceCommissionDetailSum.SavingCardCommission : 0)}
              </span>
            );
            break;
          case 'SavingCardLargessPerformance':
            sums[index] = (
              <span class="font_weight_600">
                {filter_NumFormat(this.salePerformanceCommissionDetailSum ? this.salePerformanceCommissionDetailSum.SavingCardLargessPerformance : 0)}
              </span>
            );
            break;
          case 'SavingCardLargessCardCommission':
            sums[index] = (
              <span class="font_weight_600">
                {filter_NumFormat(this.salePerformanceCommissionDetailSum ? this.salePerformanceCommissionDetailSum.SavingCardLargessCardCommission : 0)}
              </span>
            );
            break;
          case 'SpecialBenefitCommission':
            sums[index] = (
              <span class="font_weight_600">
                {filter_NumFormat(this.salePerformanceCommissionDetailSum ? this.salePerformanceCommissionDetailSum.SpecialBenefitCommission : 0)}
              </span>
            );
            break;
        }
      });
      return sums;
    },

    /** 数据导出 */
    downloadExcel () {
      var that = this;
      if (that.searchSalePerformanceCommissionDetailData.QueryDate != null) {
        if (
          dayjs(that.searchSalePerformanceCommissionDetailData.QueryDate[0]).add(366, 'day').valueOf() <
          dayjs(that.searchSalePerformanceCommissionDetailData.QueryDate[1]).valueOf()
        ) {
          that.$message.error('时间筛选范围不能超366天');
          return;
        }

        let CategoryID = '';
        if (that.searchSalePerformanceCommissionDetailData.CategoryID.length == 2) {
          CategoryID = that.searchSalePerformanceCommissionDetailData.CategoryID[1];
        }

        if (that.searchSalePerformanceCommissionDetailData.CategoryID.length == 3) {
          CategoryID = that.searchSalePerformanceCommissionDetailData.CategoryID[2];
        }
        let params = {
          EntityID: that.searchSalePerformanceCommissionDetailData.EntityID,
          StartDate: that.searchSalePerformanceCommissionDetailData.QueryDate[0],
          EndDate: that.searchSalePerformanceCommissionDetailData.QueryDate[1],
          EmployeeName: that.searchSalePerformanceCommissionDetailData.EmployeeName.trim(),
          JobID: that.searchSalePerformanceCommissionDetailData.JobID,
          CustomerName: that.searchSalePerformanceCommissionDetailData.CustomerName,
          BillID: that.searchSalePerformanceCommissionDetailData.BillID,
          GoodsTypeName: that.searchSalePerformanceCommissionDetailData.CategoryID.length
            ? that.getGoodsTypeID(that.searchSalePerformanceCommissionDetailData.CategoryID[0])
            : '',
          CategoryID: CategoryID,
          GoodsName: that.searchSalePerformanceCommissionDetailData.GoodsName,
          CustomerLevelID: that.searchSalePerformanceCommissionDetailData.CustomerLevelID,
        };
        that.downloadLoading = true;
        API.exportEmployeeSalePerformanceCommissionDetailStatement(params)
          .then((res) => {
            this.$message.success({
              message: '正在导出',
              duration: '4000',
            });
            const link = document.createElement('a');
            let blob = new Blob([res], { type: 'application/octet-stream' });
            link.style.display = 'none';
            link.href = URL.createObjectURL(blob);
            link.download = '员工销售业绩.xlsx'; //下载的文件名
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
          })
          .finally(function () {
            that.downloadLoading = false;
          });
      }
    },
    // 职务ID
    async getJobID () {
      var that = this;
      var params = {
        JobTypeName: '',
      };
      let res = await APIJob.getJobJobtypeAll(params);
      if (res.StateCode == 200) {
        that.jobTypeList = res.Data;
      } else {
        that.$message.error({
          message: res.Message,
          duration: 2000,
        });
      }
    },

    /**  分类  */
    async entitySaleGoodsDetailStatement_category () {
      let that = this;
      let res = await APICategory.entitySaleGoodsDetailStatement_category();
      if (res.StateCode == 200) {
        that.categoryList = res.Data;
      } else {
        that.$message.error(res.Message);
      }
    },
    /**  获取客户等级  */
    getCustomerLevel_all () {
      let that = this;
      let params = {
        Name: '',
        Active: true, //有效性
      };
      API.customerLevel_all(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.customerLevelList = res.Data;
          } else {
            that.$message.error(res.Message);
          }
        })
        .catch((fail) => {
          that.$message.error(fail);
        });
    },
  },
  mounted () {
    var that = this;
    that.salePerformanceCommissionDetailExport = permission.permission(that.$route.meta.Permission, 'Report-Employee-SalePerformanceCommissionDetail-Export');
    that.getstoreEntityList();
    that.handleSalePerformanceCommissionSearch();
    that.getJobID();
    that.entitySaleGoodsDetailStatement_category();
    that.getCustomerLevel_all();
  },
};
</script>
