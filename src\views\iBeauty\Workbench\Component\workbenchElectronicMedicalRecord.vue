<template>
  <!-- 电子病历 -->
  <div class="electronicMedicalRecord" v-loading="loading">
    <!-- 列表 -->
    <div class="position_relative">
      <el-table size="mini" :data="medicalRecord_list" height="30vh">
        <el-table-column label="病历编号" prop="ID"></el-table-column>
        <el-table-column label="客户名称" prop="CustomerName"></el-table-column>
        <el-table-column label="性别" prop="Gender">
          <template slot-scope="scope">
            <span v-if="scope.row.Gender == 1">男</span>
            <span v-if="scope.row.Gender == 2">女</span>
            <span v-if="scope.row.Gender == 0">未知</span>
          </template>
        </el-table-column>
        <el-table-column label="年龄" prop="Age"></el-table-column>
        <el-table-column label="门店名称" prop="EntityName"></el-table-column>
        <el-table-column label="科室" prop="DepartmentName"></el-table-column>
        <el-table-column label="就诊日期" prop="TreatmentDate"></el-table-column>
        <el-table-column label="主治医生" prop="DoctorName"></el-table-column>
        <el-table-column label="备注信息" prop="Remark"></el-table-column>
        <el-table-column label="创建人" prop="CreatedBy"></el-table-column>
        <el-table-column label="创建日期" prop="CreatedOn">
          <template slot-scope="scope">
            {{ scope.row.CreatedOn | dateFormat("YYYY-MM-DD HH:mm") }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="210">
          <template slot-scope="scope">
            <el-button type="primary" size="small" @click="editMedicalRecordClick(scope.row)" v-prevent-click>编辑</el-button>
            <el-button type="primary" size="small" @click="checkMedicalRecordClick(scope.row)" v-prevent-click>查看</el-button>
            <el-button type="danger" size="small" @click="deleteMedicalRecordClick(scope.row)" v-prevent-click>删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-button class="add_btn" type="primary" size="small" @click="addCustomerElectronicMedicalRecord" v-prevent-click>新增病历</el-button>
    </div>
    <div class="pad_15 text_right">
      <el-pagination background v-if="paginations.total > 0" @current-change="handleCurrentChange" :current-page.sync="paginations.page" :page-size="paginations.page_size" :layout="paginations.layout" :total="paginations.total"></el-pagination>
    </div>
    <!--  -->
    <!-- 新增病历 -->
    <el-dialog :title="isAdd ? '新增病历' : '编辑病历'" :visible.sync="dialogVisible" append-to-body width="600px" :close-on-click-modal="false" custom-class="custom-electronicMedicalRecord-dialog">
      <el-form :model="addRuleForm" :rules="addRules" size="small" ref="addRuleForm" label-width="100px">
        <el-form-item prop="DepartmentID" label="科室">
          <el-select v-model="addRuleForm.DepartmentID" placeholder="请选择科室" size="small" clearable>
            <el-option v-for="item in departmentList" :label="item.Name" :value="item.ID" :key="item.ID"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item prop="EmployeeID" label="主治医生">
          <el-select v-model="addRuleForm.EmployeeID" placeholder="请选择主治医生" size="small" clearable>
            <el-option v-for="item in doctorList" :label="item.Name" :value="item.ID" :key="item.ID"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item prop="TreatmentDate" label="就诊日期">
          <el-date-picker v-model="addRuleForm.TreatmentDate" placeholder="请选择就诊日期" value-format="yyyy-MM-dd" clearable></el-date-picker>
        </el-form-item>
        <el-form-item prop="Remark" label="备注信息">
          <el-input type="textarea" v-model="addRuleForm.Remark" size="small"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button size="small" @click="dialogVisible = false">取 消</el-button>
        <el-button size="small" type="primary" @click="saveCustomerMedicalRecord" v-prevent-click :loading="saveModeloading">保 存</el-button>
      </span>
    </el-dialog>

    <!-- 病历模板 -->
    <el-dialog :title="'病历信息：' + medicalRecordDetail.ID" :visible.sync="dialogVisible_medical" append-to-body width="1150px" :close-on-click-modal="false" custom-class="edit_medical_class" top="5vh" @close="editDialogBeforeClose">
      <div class="edit_content" v-loading="edit_loading">
        <div class="pad_10_0">
          <el-form size="small" class="custom_medical_form" label-width="115px">
            <el-row>
              <el-col :span="6">
                <el-form-item label="门店名称：">{{ medicalRecordDetail.EntityName }}</el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="科室名称：">{{ medicalRecordDetail.DepartmentName }}</el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="就诊日期：">{{ medicalRecordDetail.TreatmentDate }}</el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="主治医生：">{{ medicalRecordDetail.DoctorName }}</el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="客户名称：">{{ medicalRecordDetail.CustomerName }}</el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="客户性别：">
                  <span v-if="medicalRecordDetail.Gender == 1">男</span>
                  <span v-if="medicalRecordDetail.Gender == 2">女</span>
                  <span v-if="medicalRecordDetail.Gender == 0">未知</span>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="客户年龄：">{{ medicalRecordDetail.Age }}</el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="备注信息：">{{ medicalRecordDetail.Remark }}</el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>
        <div class="pad_0_5 tab_category_content">
          <el-tabs v-model="categoryActiveName" @tab-click="handleCategoryClick">
            <el-tab-pane v-for="item in categoryList" :label="item.Name" :name="item.ID + ''" :key="'category-' + item.ID"></el-tab-pane>
          </el-tabs>
        </div>
        <el-container>
          <el-aside>
            <el-scrollbar class="category_content">
              <div @click="selectCustomerTemplateItemClick(item)" v-for="(item, index) in medicalRecordDetail.DetailList" :key="item.ID" class="edit_template_item" :class="item.isSelect ? 'edit_template_item_select' : ''">
                <div>{{ item.MedicalRecordTemplateName }}</div>
                <i @click.stop="delectSelectTemplateItemClick(item, index)" class="el-icon-delete"></i>
              </div>
            </el-scrollbar>
          </el-aside>
          <el-main>
            <medicalEditor :docData="medicalRecordDate()" :fieldData="fieldData" :docContent="templateDetail_edit && templateDetail_edit.MedicalRecordContent" :hideToolbar="true" :editable="false" :showHeader="false" ref="medicalRecordEditorContentRef"></medicalEditor>
          </el-main>
        </el-container>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button size="small" @click="dialogVisible_medical = false">取 消</el-button>
        <el-button size="small" type="primary" @click="printMedicalRecordTemplate" v-prevent-click>打 印</el-button>
        <el-button size="small" type="primary" @click="addMedicalRecordTemplate" v-prevent-click>选择模板</el-button>
        <el-button size="small" type="primary" @click="saveCustomerMedicalRecordTemplate" v-prevent-click :loading="saveTemplateLoading" :disabled="!templateDetail_edit">保 存</el-button>
      </span>
    </el-dialog>

    <!-- 选择模板 -->
    <el-dialog title="选择病历模板" :visible.sync="dialogVisible_select_template" append-to-body width="1150px" :close-on-click-modal="false" custom-class="select_medical_class" top="8vh">
      <el-container>
        <el-aside>
          <div class="border_bottom">
            <el-select v-model="catalogModel" size="small" @change="changeMedicalRecordCatalog" style="width: 100%">
              <el-option v-for="item in catalogList" :key="item.ID" :label="item.Name" :value="item.ID"></el-option>
            </el-select>
          </div>
          <el-scrollbar class="all_catalog_item_list">
            <div @click="selectCatalogItemClick(item)" v-for="item in templateList" :key="item.ID" class="catalog_item" :class="item.isSelect ? 'catalog_item_select' : ''">
              <el-checkbox v-model="item.isCheck" @change="(env) => addSelectCatalogItemClick(env, item)"></el-checkbox>
              <span class="marlt_10">{{ item.Name }}</span>
            </div>
          </el-scrollbar>
          <div class="pad_10 border_top border_bottom font_14 text-bold">已选模板</div>
          <el-scrollbar class="selection_catalog_item">
            <div @click="selectionCatalogItemClick(item)" v-for="(item, index) in selectTemplateList" :key="item.ID" class="catalog_item dis_flex flex_x_between flex_y_center" :class="item.isSelection ? 'catalog_item_select' : ''">
              <div class="flex_box">{{ item.Name }}</div>
              <i @click="delectSelectCatalogItemClick(item, index)" class="el-icon-delete"></i>
            </div>
          </el-scrollbar>
        </el-aside>
        <el-main>
          <medicalEditor :fieldData="{}" :docContent="templateDetail && templateDetail.TemplateContent" :hideToolbar="true" :editable="false" :showHeader="false" ref="selectTemplatMedicalRecordEditorRef"> </medicalEditor>
          <!-- @initEditorComplete="selectInitEditorComplete"  -->
        </el-main>
      </el-container>

      <span slot="footer" class="dialog-footer">
        <el-button size="small" @click="dialogVisible_select_template = false">取 消</el-button>
        <el-button size="small" type="primary" @click="saveMedicalRecordTemoaltClick" v-prevent-click :loading="templateLoading" :disabled="selectTemplateList && selectTemplateList.length == 0">确 认</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import API from "@/api/CRM/Customer/electronicMedicalRecord.js";
import medicalEditor from "@/components/medicalEditor/medicalEditor.vue";
export default {
  name: "electronicMedicalRecord",
  directives: {},
  props: {
    CustomerID: Number,
  },
  /**  引入的组件  */
  components: {
    medicalEditor,
  },
  /**  Vue 实例的数据对象**/
  data() {
    return {
      edit_loading: false,
      loading: false,
      saveTemplateLoading: false,
      templateLoading: false,
      dialogVisible_select_template: false,
      dialogVisible_medical: false,
      saveModeloading: false,
      dialogVisible: false,
      isAdd: false,
      medicalRecord_list: [],
      paginations: {
        page: 1, // 当前位于哪页
        total: 0, // 总数
        page_size: 10, // 1页显示多少条
        layout: "total, prev, pager, next,jumper", // 翻页属性
      },
      departmentList: [],
      doctorList: [],
      addRuleForm: {
        CustomerID: "", //顾客编号
        DepartmentID: "", //科室编号
        EmployeeID: "",
        TreatmentDate: "", //就诊日期
        Remark: "", //备注
      },
      addRules: {
        DepartmentID: [{ required: true, message: "请选择科室", trigger: "change" }],
        EmployeeID: [{ required: true, message: "请选择主治医生", trigger: "change" }],
        TreatmentDate: [{ required: true, message: "请选择就诊日期", trigger: "change" }],
      },
      medicalRecordDetail: "",
      categoryList: [],
      categoryActiveName: "",
      catalogList: [],
      catalogModel: "",
      templateList: [],
      templateDetail: "",
      templateDetail_edit: "",
      selectTemplateList: [],
      selectMedicalItem: "",
      fieldData: {
        ctm_name: "", // 姓名
        ctm_sex: "", // 性别
        ctm_age: "" /* 年龄 */,
        ctm_mobile: "" /* 联系电话 */,
        ctm_id_code: "" /* 客户身份证 */,
        S_AddressPCCS: "" /*  客户住址*/,
        ctm_wktype: "" /* 职业 */,
        ctm_code: "" /*客户卡号  */,
        HospitalName: "" /* 医院名称 */,
        O_MedicRecordVisitDepartmentId: "" /* 就诊科室 */,
        O_MedicRecordVisitDoctorId: "" /* 主治医生 */,
        ctm_datebirth: "" /* 出生日期 */,
      },
    };
  },
  /**计算属性  */
  computed: {},
  /**  方法集合  */
  methods: {
    /**    */
    editDialogBeforeClose() {
      let that = this;
      that.templateDetail_edit = "";
      that.$refs.medicalRecordEditorContentRef.resetDocContent();
    },
    /**  打印病历  */
    printMedicalRecordTemplate() {
      let that = this;
      that.$refs.medicalRecordEditorContentRef.print();
    },
    /**  删除病历模板  */
    delectSelectTemplateItemClick(item, index) {
      let that = this;
      this.$confirm("确定要删除该病历吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          that.$refs.medicalRecordEditorContentRef.resetDocContent();
          that.customerMedicalRecord_deleteMedicalTemplate(item.ID, index);
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    /**  保存病历模板数据  */
    saveCustomerMedicalRecordTemplate() {
      let that = this;
      that.customerMedicalRecord_updateMedicalTemplate();
    },
    /**    */
    medicalRecordDate() {
      let that = this;
      if (that.templateDetail_edit && that.templateDetail_edit.MedicalRecordDate != "" && that.templateDetail_edit.MedicalRecordDate) {
        return JSON.parse(that.templateDetail_edit.MedicalRecordDate);
      }
      return {};
    },
    /**  查看病历模板  */
    selectCustomerTemplateItemClick(item) {
      let that = this;
      if (item.ID == that.templateDetail_edit.ID && item.MedicalRecordCategoryID == that.templateDetail_edit.MedicalRecordCategoryID) {
        return;
      }
      that.$refs.medicalRecordEditorContentRef.clearData();
      that.$refs.medicalRecordEditorContentRef.resetDocContent();
      that.medicalRecordDetail.DetailList.forEach((i) => {
        i.isSelect = false;
      });
      item.isSelect = true;
      that.templateDetail_edit = item;
    },
    /**  确定选择病历模板  */
    saveMedicalRecordTemoaltClick() {
      let that = this;
      that.customerMedicalRecord_createMedicalTemplate();
    },
    /**    */
    selectionCatalogItemClick(item) {
      let that = this;
      that.selectTemplateList.forEach((i) => {
        i.isSelection = false;
      });
      item.isSelection = true;
      that.customerMedicalRecord_medicalRecordTemplateDetail(item.ID);
    },
    /**    */
    delectSelectCatalogItemClick(item, index) {
      let that = this;
      this.$confirm("确定要删除该模板吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          that.selectTemplateList.splice(index, 1);
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    /**   添加到预选内容区 */
    addSelectCatalogItemClick(env, item) {
      let that = this;
      if (env) {
        that.selectTemplateList.push(item);
      } else {
        let findIndex = that.selectTemplateList.findIndex((i) => i.ID == item.ID);
        if (findIndex != -1) {
          that.selectTemplateList.splice(findIndex, 1);
        }
      }
    },
    /** 选择预览模板内容   */
    selectCatalogItemClick(item) {
      let that = this;
      that.$refs.selectTemplatMedicalRecordEditorRef.clearData();
      that.templateList.forEach((i) => {
        i.isSelect = false;
      });
      item.isSelect = true;
      that.customerMedicalRecord_medicalRecordTemplateDetail(item.ID);
    },
    /** 切换病历分类  */
    handleCategoryClick() {
      let that = this;
      that.templateDetail_edit = null;
      that.$refs.medicalRecordEditorContentRef.resetDocContent();
      that.customerMedicalRecord_detail();
    },
    /**   切换病历目录 */
    changeMedicalRecordCatalog(val) {
      let that = this;
      let item = that.catalogList.find((i) => i.ID == val);
      if (item) {
        that.templateList = item.Template;
        that.templateList[0].isSelect = true;
        let tempItem = that.templateList[0];

        that.customerMedicalRecord_medicalRecordTemplateDetail(tempItem.ID);
      }
    },
    /**  选择模板  */
    addMedicalRecordTemplate() {
      let that = this;
      that
        .customerMedicalRecord_medicalRecordTemplate(that.categoryActiveName)
        .then(() => {
          that.dialogVisible_select_template = true;
          that.selectTemplateList = [];
          if (that.catalogList && that.catalogList.length > 0) {
            let item = that.catalogList[0];
            that.catalogModel = item.ID;
            that.templateList = item.Template;
            that.templateList.forEach((i) => {
              i.isCheck = false;
            });
            if (that.templateList && that.templateList.length > 0) {
              that.templateList[0].isSelect = true;
              let tempItem = that.templateList[0];
              that.customerMedicalRecord_medicalRecordTemplateDetail(tempItem.ID);
            }
          }
        })
        .catch(() => {});
    },
    /**  查看病历模板  */
    checkMedicalRecordClick(row) {
      let that = this;
      that.dialogVisible_medical = true;
      that.selectMedicalItem = row;
      that.categoryActiveName = that.categoryList[0].ID + "";
      that.customerMedicalRecord_detail();
    },

    /**  删除病历  */
    deleteMedicalRecordClick(row) {
      let that = this;
      that
        .$confirm("是否要删除病历?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
        .then(() => {
          that.customerMedicalRecord_delete(row.ID);
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    /**  编辑病历  */
    editMedicalRecordClick(row) {
      let that = this;
      that.isAdd = false;
      that.dialogVisible = true;
      that.addRuleForm = {
        ID: row.ID, //顾客编号
        DepartmentID: row.DepartmentID, //科室编号
        TreatmentDate: row.TreatmentDate, //就诊日期
        EmployeeID: row.DoctorID,
        Remark: row.Remark, //备注
      };
    },
    /**   添加病历 */
    addCustomerElectronicMedicalRecord() {
      let that = this;
      that.isAdd = true;
      that.dialogVisible = true;
      that.addRuleForm = {
        CustomerID: "", //顾客编号
        DepartmentID: "", //科室编号
        TreatmentDate: "", //就诊日期
        EmployeeID: "",
        Remark: "", //备注
      };
    },
    /**  保存 创建/编辑 病历  */
    saveCustomerMedicalRecord() {
      let that = this;
      that.$refs.addRuleForm.validate((valid) => {
        if (valid) {
          if (that.isAdd) {
            that.customerMedicalRecord_create();
          } else {
            that.customerMedicalRecord_update();
          }
        }
      });
    },
    /**  病历列表修改分页  */
    handleCurrentChange(page) {
      let that = this;
      that.paginations.page = page;
      that.customerMedicalRecord_list();
    },

    /**  清除数据  */
    claerElectronicMedicalRecordData() {
      this.medicalRecord_list = [];
      this.selectMedicalItem = "";
      this.employee_list = [];
      this.template_list = [];
    },
    /**  组件内部数据获取  */
    getElectronicMedicalRecordData() {
      let that = this;
      that.customerMedicalRecord_list();
      that.customerMedicalRecord_department();
      that.customerMedicalRecord_doctor();
      that.medicalRecordCategory_list();
    },
    /**    */
    formaterGender(type) {
      switch (type) {
        case "1":
          return "男";
        case "2":
          return "女";
        case "0":
          return "未知";
      }
    },
    /* **************************** */
    /**  顾客病历查询  */
    async customerMedicalRecord_list() {
      let that = this;
      let params = { CustomerID: that.CustomerID, PageNum: that.paginations.page };
      if (that.CustomerID) {
        let res = await API.customerMedicalRecord_list(params);
        if (res.StateCode == 200) {
          that.medicalRecord_list = res.List;
          that.paginations.total = res.Total;
          that.paginations.page_size = res.PageSize;
        } else {
          that.$message.error(res.Message);
        }
      }
    },
    /**  查询医生  */
    customerMedicalRecord_doctor() {
      let that = this;
      let params = {};
      API.customerMedicalRecord_doctor(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.doctorList = res.Data;
          } else {
            that.$message.error(res.Message);
          }
        })
        .catch((fail) => {
          that.$message.error(fail);
        });
    },
    /**  查询科室  */
    customerMedicalRecord_department() {
      let that = this;
      let params = {};
      API.customerMedicalRecord_department(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.departmentList = res.Data;
          } else {
            that.$message.error(res.Message);
          }
        })
        .catch((fail) => {
          that.$message.error(fail);
        });
    },
    /**    */
    customerMedicalRecord_create() {
      let that = this;
      let params = {
        CustomerID: that.CustomerID, //顾客编号
        DepartmentID: that.addRuleForm.DepartmentID, //科室编号
        TreatmentDate: that.addRuleForm.TreatmentDate, //就诊日期
        EmployeeID: that.addRuleForm.EmployeeID, //就诊日期
        Remark: that.addRuleForm.Remark, //备注
      };
      API.customerMedicalRecord_create(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.$message.success("操作成功");
            that.customerMedicalRecord_list();
            that.dialogVisible = false;
            if (that.isAdd) {
              that.dialogVisible_medical = true;
              that.selectMedicalItem = res.Data;
              that.categoryActiveName = that.categoryList[0].ID + "";
              that.customerMedicalRecord_detail();
            }
          } else {
            that.$message.error(res.Message);
          }
        })
        .catch((fail) => {
          that.$message.error(fail);
        });
    },
    /** 修改病历   */
    customerMedicalRecord_update() {
      let that = this;
      let params = {
        ID: that.addRuleForm.ID, //顾客编号
        DepartmentID: that.addRuleForm.DepartmentID, //科室编号
        TreatmentDate: that.addRuleForm.TreatmentDate, //就诊日期
        EmployeeID: that.addRuleForm.EmployeeID, //就诊日期
        Remark: that.addRuleForm.Remark, //备注
      };
      API.customerMedicalRecord_update(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.$message.success("操作成功");
            that.customerMedicalRecord_list();
            that.dialogVisible = false;
          } else {
            that.$message.error(res.Message);
          }
        })
        .catch((fail) => {
          that.$message.error(fail);
        });
    },
    /**  删除病历  */
    customerMedicalRecord_delete(ID) {
      let that = this;
      let params = { ID: ID };
      API.customerMedicalRecord_delete(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.$message.success("操作成功");
            that.customerMedicalRecord_list();
          } else {
            that.$message.error(res.Message);
          }
        })
        .catch((fail) => {
          that.$message.error(fail);
        });
    },
    /**  获取病历明细  */
    customerMedicalRecord_detail() {
      let that = this;
      let params = {
        ID: that.selectMedicalItem.ID,
        CategoryID: that.categoryActiveName,
      };
      that.edit_loading = true;
      API.customerMedicalRecord_detail(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.medicalRecordDetail = res.Data;
            if (res.Data.DetailList && res.Data.DetailList.length > 0) {
              let item = null;
              if (that.templateDetail_edit) {
                let item_ = res.Data.DetailList.find((i) => i.ID == that.templateDetail_edit.ID);
                if (item_) {
                  item = item_;
                  item.isSelect = true;
                } else {
                  item = res.Data.DetailList[0];
                  item.isSelect = true;
                }
              } else {
                item = res.Data.DetailList[0];
                item.isSelect = true;
              }

              that.templateDetail_edit = item;
              that.fieldData = {
                ctm_name: that.medicalRecordDetail.CustomerName, // 姓名
                ctm_sex: that.formaterGender(that.medicalRecordDetail.Gender), // 性别
                ctm_age: that.medicalRecordDetail.Age /* 年龄 */,
                ctm_mobile: that.medicalRecordDetail.PhoneNumber /* 联系电话 */,
                ctm_id_code: that.medicalRecordDetail.IdentityCard /* 客户身份证 */,
                S_AddressPCCS: that.medicalRecordDetail.Address /*  客户住址*/,
                ctm_wktype: that.medicalRecordDetail.Job /* 职业 */,
                ctm_code: that.medicalRecordDetail.Code /*客户卡号  */,
                HospitalName: that.medicalRecordDetail.EntityName /* 医院名称 */,
                O_MedicRecordCode: that.medicalRecordDetail.ID /*  病历编号 */,
                O_TreatmentDate: that.medicalRecordDetail.TreatmentDate /* 治疗日期 */,
                O_MedicRecordVisitDepartmentId: that.medicalRecordDetail.DepartmentName /* 就诊科室 */,
                O_MedicRecordVisitDoctorId: that.medicalRecordDetail.DoctorName /* 主治医生 */,
                ctm_datebirth: that.medicalRecordDetail.Birthday /* 出生日期 */,
              };
            }
          } else {
            that.$message.error(res.Message);
          }
          that.edit_loading = false;
        })
        .catch((fail) => {
          that.edit_loading = false;
          that.$message.error(fail);
        });
    },
    /**  请求分类 */
    medicalRecordCategory_list() {
      let that = this;
      that.loading = true;
      let params = {
        Name: "", //名称
        Active: true, //有效性
      };
      API.customerMedicalRecord_medicalRecordCategory(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.categoryList = res.Data;
            if (that.categoryList.length > 0) {
              that.categoryActiveName = that.categoryList[0].ID + "";
              // that.medicalRecord_list();
            }
          } else {
            that.$message.error(res.Message);
          }
          that.loading = false;
        })
        .catch((fail) => {
          that.loading = false;
          that.$message.error(fail);
        });
    },
    /**  请求病历分类下目录  */
    async customerMedicalRecord_medicalRecordTemplate(CategoryID) {
      let that = this;
      try {
        let params = {
          CategoryID: CategoryID,
        };
        let res = await API.customerMedicalRecord_medicalRecordTemplate(params);
        if (res.StateCode == 200) {
          that.catalogList = res.Data;
          return res;
        } else {
          that.$message.error(res.Message);
        }
      } catch (error) {
        console.log("🚀 ~ customerMedicalRecord_medicalRecordTemplate ~ error:", error);
      }
    },
    /**  请求模板详情  */
    customerMedicalRecord_medicalRecordTemplateDetail(ID) {
      let that = this;
      let params = { ID: ID };
      API.customerMedicalRecord_medicalRecordTemplateDetail(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.templateDetail = res.Data;
          } else {
            that.$message.error(res.Message);
          }
        })
        .catch((fail) => {
          that.$message.error(fail);
        });
    },
    /**   创建病历模板 */
    customerMedicalRecord_createMedicalTemplate() {
      let that = this;
      that.templateLoading = true;
      let params = {
        CustomerMedicalRecordID: that.medicalRecordDetail.ID, //病例编号
        Template: that.selectTemplateList.map((i) => {
          return {
            MedicalRecordTemplateID: i.ID,
          };
        }),
      };
      API.customerMedicalRecord_createMedicalTemplate(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.$message.success("操作成功");
            that.dialogVisible_select_template = false;
            that.templateLoading = false;
            that.customerMedicalRecord_detail();
          } else {
            that.templateLoading = false;
            that.$message.error(res.Message);
          }
        })
        .catch((fail) => {
          that.templateLoading = false;
          that.$message.error(fail);
        });
    },
    /**  更新病历模板  */
    customerMedicalRecord_updateMedicalTemplate() {
      let that = this;
      let params = {
        ID: that.templateDetail_edit.ID, //病例编号
        MedicalRecordContent: that.$refs.medicalRecordEditorContentRef.getDocContent(), //病历内容
        MedicalRecordDate: that.$refs.medicalRecordEditorContentRef.getDocData(), //病历数据
        TemplatePrintJSON: JSON.stringify(that.$refs.medicalRecordEditorContentRef.getPaper()), //病历打印格式
      };
      that.saveTemplateLoading = true;
      API.customerMedicalRecord_updateMedicalTemplate(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.saveTemplateLoading = false;
            that.$message.success("保存成功");
            that.customerMedicalRecord_detail();
          } else {
            that.saveTemplateLoading = false;
            that.$message.error(res.Message);
          }
        })
        .catch((fail) => {
          that.saveTemplateLoading = false;
          that.$message.error(fail);
        });
    },
    /**  删除病历模板  */
    customerMedicalRecord_deleteMedicalTemplate(ID, index) {
      let that = this;
      let params = { ID: ID };
      API.customerMedicalRecord_deleteMedicalTemplate(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.$message.success("操作成功");
            that.medicalRecordDetail.DetailList.splice(index, 1);
            if (that.medicalRecordDetail.DetailList.length == 0) {
              that.templateDetail_edit = "";
            }
          } else {
            that.$message.error(res.Message);
          }
        })
        .catch((fail) => {
          that.$message.error(fail);
        });
    },
  },
  /** 监听数据变化   */
  watch: {},
  /** 创建实例之前执行函数   */
  beforeCreate() {},
  /**  实例创建完成之后  */
  created() {
    // this.customerMedicalRecord_list();
    // this.employee_all();
    // this.customerElectronicMedicalRecord_getTemplate();
  },
  /**  在挂载开始之前被调用  */
  beforeMount() {},
  /**  实例被挂载后调用  */
  mounted() {},
  /**  数据更新 之前 调用  */
  beforeUpdate() {},
  /** 数据更新 完成 调用   */
  updated() {},
  /**  实例销毁之前调用  */
  beforeDestroy() {},
  /**  实例销毁后调用  */
  destroyed() {},
};
</script>

<style lang="scss">
.electronicMedicalRecord {
  height: 40vh;

  .custom-el-form {
    .el-form-item {
      margin-bottom: unset;
    }
  }

  .add_btn {
    position: absolute;
    right: 5px;
    top: 2px;
  }
}

.edit_medical_class {
  .el-dialog__header {
    border-bottom: 1px solid #eeeeee;
  }

  .el-dialog__body {
    height: calc(90vh - 118px);
    padding: unset !important;

    .edit_content {
      height: 100%;

      .custom_medical_form {
        .el-form-item {
          margin-bottom: 0px;

          .el-form-item__label {
            font-size: 14px;
          }

          .el-form-item__content {
            font-size: 14px;
          }
        }
      }

      .el-container {
        height: calc(100% - 124px);

        .el-aside {
          height: 100%;
          display: flex;
          flex-direction: column;
          border-right: 1px solid #eeeeee;

          .category_content {
            height: 100%;

            .el-scrollbar__wrap {
              overflow-x: hidden;
            }

            .edit_template_item {
              display: flex;
              justify-content: space-between;
              align-items: center;
              padding: 8px;
              cursor: pointer;
            }

            .edit_template_item:hover {
              background-color: var(--zl-color-orange-primary-header);
            }

            .edit_template_item_select {
              background-color: var(--zl-color-orange-primary-header);
            }
          }
        }

        .el-main {
          padding: unset;
          display: flex;
          flex-direction: column;
        }
      }

      .tab_category_content {
        .el-tabs__header {
          margin: unset !important;
        }

        .el-tabs__content {
          display: none;
        }
      }
    }
  }

  .el-dialog__footer {
    border-top: 1px solid #eeeeee;
  }
}

.select_medical_class {
  .el-dialog__body {
    height: calc(100vh - 16vh - 130px);
    padding: unset !important;
    border-top: 1px solid #eeeeee;
    display: flex;
    flex-direction: column;

    .custom_medical_form {
      .el-form-item {
        margin-bottom: 0px;

        .el-form-item__label {
          font-size: 14px;
        }

        .el-form-item__content {
          font-size: 14px;
        }
      }
    }

    .el-container {
      height: 100%;

      .el-aside {
        height: calc(100%);
        // margin: 10px;
        border-right: 1px solid #eeeeee;
        // box-shadow: 0 0 5px 2px rgba(64, 60, 67, 0.16);

        .all_catalog_item_list {
          height: 55%;

          .el-scrollbar__wrap {
            overflow-x: hidden;
          }

          .catalog_item {
            padding: 8px;
            cursor: pointer;
          }

          .catalog_item:hover {
            background-color: var(--zl-color-orange-primary-header);
          }

          .catalog_item_select {
            background-color: var(--zl-color-orange-primary-header);
          }
        }

        .selection_catalog_item {
          height: calc(45% - 86px);

          .el-scrollbar__wrap {
            overflow-x: hidden;
          }

          .catalog_item {
            padding: 8px;
            cursor: pointer;
          }

          .catalog_item:hover {
            background-color: var(--zl-color-orange-primary-header);
          }

          .catalog_item_select {
            background-color: var(--zl-color-orange-primary-header);
          }
        }
      }

      .el-main {
        padding: unset;
        display: flex;
        flex-direction: column;
      }
    }
  }

  .el-dialog__footer {
    // display: none;
    border-top: 1px solid #eeeeee;
  }
}
</style>
