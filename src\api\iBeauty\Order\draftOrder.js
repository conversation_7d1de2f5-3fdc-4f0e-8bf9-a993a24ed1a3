/**
 * Created by preference on 2021/08/16
 *  zmx 
 */

import * as API from '@/api/index'
export default {
  /** 消耗挂单创建  */
  treatBillPendingOrder_create: params => {
    return API.POST('api/treatBill/createPendingOrder', params)
  },
  /** 消耗挂单列表  */
  treatBillPendingOrder_list: params => {
    return API.POST('api/treatBill/list', params)
  },
  /**  消耗挂单删除 */
  treatBillPendingOrder_delete: params => {
    return API.POST('api/treatBill/deletePendingOrder', params)
  },
  // 消耗订单详情
  pendingOrderInfo: params => {
    return API.POST('api/treatBill/pendingOrderInfo', params)
  },

  /**  销售挂单 列表 */
  saleBillPendingOrder: params => {
    return API.POST('api/saleBill/pendingList', params)
  },

  /**  销售挂单创建 */
  createBillPendingOrder: params => {
    return API.POST('api/saleBill/createPendingOrder', params)
  },

  /**  销售挂单 删除 */
  deleteBillPendingOrder: params => {
    return API.POST('api/saleBill/deletePendingOrder', params)
  },

  //销售取单 获取订单详情
  pendingOrderInfo_saleBill:params =>{
    return API.POST('api/saleBill/pendingOrderInfo',params)
  },
}