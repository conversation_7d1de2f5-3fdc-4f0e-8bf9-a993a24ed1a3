/**
 * Created by preference on 2023/02/01
 *  zmx
 */

import * as API from '@/api/index';
export default {
  /**   */
  customerStatistics_monthlySaleStatistics: (params) => {
    return API.POST('api/customerStatistics/monthlySaleStatistics', params);
  },

  // 动销动耗导出
  customerStatistics_monthlySaleStatisticsExcel: (params) => {
    return API.exportExcel('api/customerStatistics/monthlySaleStatisticsExcel', params);
  },
  /**   */
  customerStatistics_yearSaleStatistics: (params) => {
    return API.POST('api/customerStatistics/yearSaleStatistics', params);
  },

  // 动销动耗导出
  customerStatistics_yearSaleStatisticsExcel: (params) => {
    return API.exportExcel('api/customerStatistics/yearSaleStatisticsExcel', params);
  },

  // 动销动耗导出
  customerStatistics_monthlySaleStatisticsExcelDisPlayPhone: (params) => {
    return API.exportExcel('api/customerStatistics/monthlySaleStatisticsExcelDisPlayPhone', params);
  },

  // 动销动耗导出
  customerStatistics_yearSaleStatisticsExcelDisPlayPhone: (params) => {
    return API.exportExcel('api/customerStatistics/yearSaleStatisticsExcelDisPlayPhone', params);
  },
};
