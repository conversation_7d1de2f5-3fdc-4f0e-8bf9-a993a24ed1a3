<template>
  <div style="height: calc(100% - 63px)">
    <el-row class="sale_content">
      <el-col :span="9" class="project_left">
        <div class="pad_15">
          <el-input
            placeholder="请输入商品名称、别名关键字，按回车搜索"
            prefix-icon="el-icon-search"
            v-model="goodsName"
            clearable
            size="small"
            @keyup.enter.native="searchGoodsClick"
            @clear="clearClick"
          >
            <el-select v-model="typeIndex" slot="prepend" style="width: 100px">
              <el-option label="全部" value="0"></el-option>
              <el-option label="项目" value="1"></el-option>
              <el-option label="储值卡" value="2"></el-option>
              <el-option label="时效卡" value="3"></el-option>
              <el-option label="通用次卡" value="4"></el-option>
              <el-option label="套餐卡" value="5"></el-option>
              <el-option label="产品" value="6"></el-option>
            </el-select>
          </el-input>
        </div>
        <el-tabs v-model="tabPane" @tab-click="handleClick">
          <el-tab-pane label="全部" name="0">
            <el-container>
              <el-main>
                <el-row class="category_project">
                  <el-col :span="6" class="border_right text_center category">
                    <el-scrollbar class="el-scrollbar_height">
                      <div
                        v-for="(item, index) in allCategory"
                        :key="item.GoodsType"
                        :class="[allCategoryIndex == index ? 'category_select' : '', 'pad_10_0', 'border_bottom', 'cursor_pointer']"
                        @click="goodsCategoryChange(item, index)"
                      >
                        {{ item.GoodsTypeName }}
                      </div>
                    </el-scrollbar>
                  </el-col>
                  <el-col :span="18" class="project">
                    <div class="producct_list" v-loading="allLoading">
                      <el-scrollbar class="el-scrollbar_height">
                        <div v-for="product in goodsAll" :key="product.ID" class="border_bottom pad_5_10 cursor_pointer" @click="goodsChange(product)">
                          <div>
                            <span>{{ product.Name }}</span>
                            <span v-if="product.Alias">({{ product.Alias }})</span>
                            <span v-if="allCategoryItem.GoodsType == '30'" class="font_12 color_gray">× {{ product.Amount }}次</span>
                          </div>
                          <div>
                            <span class="color_red">¥ {{ product.Price | toFixed | NumFormat }}</span>
                            <span class="marlt_10 font_12 color_gray" v-if="product.LargessPrice"
                              >赠送：¥ {{ product.LargessPrice | toFixed | NumFormat }}</span
                            >
                            <span class="font_12 color_gray" style="float: right">{{ product.ValidDayName }}</span>
                          </div>
                        </div>
                      </el-scrollbar>
                    </div>
                  </el-col>
                </el-row>
              </el-main>
              <el-footer class="border_top">
                <el-row type="flex" align="middle">
                  <el-col :span="24" class="text_right">
                    <el-pagination
                      background
                      v-if="allPaginations.total > 0"
                      @current-change="handleAllGoodsCurrentChange"
                      :current-page.sync="allPaginations.page"
                      :page-size="allPaginations.page_size"
                      :layout="allPaginations.layout"
                      :total="allPaginations.total"
                      :pager-count="5"
                    ></el-pagination>
                  </el-col>
                </el-row>
              </el-footer>
            </el-container>
          </el-tab-pane>
          <el-tab-pane label="项目" v-if="IsExistProject" name="1">
            <el-container>
              <el-main>
                <el-row class="category_project">
                  <el-col :span="5" class="border_right text_center category">
                    <el-scrollbar class="el-scrollbar_height">
                      <div
                        v-for="(item, index) in projectCategory"
                        :key="index"
                        :class="[projectCategoryIndex == index ? 'category_select' : '', 'pad_10_0', 'border_bottom', 'cursor_pointer']"
                        @click="projectCategoryChange(item, index)"
                      >
                        {{ item.ParentName }}
                      </div>
                    </el-scrollbar>
                  </el-col>
                  <el-col :span="5" class="border_right text_center category">
                    <el-scrollbar class="el-scrollbar_height">
                      <div
                        v-for="(item, index) in projectSecondCategory"
                        :key="index"
                        :class="[projectSecondCategoryIndex == index ? 'category_select' : '', 'pad_10_0', 'border_bottom', 'cursor_pointer']"
                        @click="projectSecondCategoryChange(item, index)"
                      >
                        {{ item.CategoryName }}
                      </div>
                    </el-scrollbar>
                  </el-col>
                  <el-col :span="14" class="project">
                    <div class="project_list" v-loading="projectLoading">
                      <el-scrollbar class="el-scrollbar_height el_scrollbar_project">
                        <div v-for="project in projectList" class="border_bottom pad_5_10 cursor_pointer" :key="project.ID" @click="projectChange(project)">
                          <div>
                            <span>{{ project.Name }}</span>
                            <span v-if="project.Alias">({{ project.Alias }})</span>
                          </div>
                          <div class="color_red">¥ {{ project.Price | toFixed | NumFormat }}</div>
                        </div>
                      </el-scrollbar>
                    </div>
                  </el-col>
                </el-row>
              </el-main>
              <el-footer class="border_top">
                <el-row type="flex" align="middle">
                  <el-col :span="24" class="text_right">
                    <el-pagination
                      background
                      v-if="projectPaginations.total > 0"
                      @current-change="handleProjectCurrentChange"
                      :current-page.sync="projectPaginations.page"
                      :page-size="projectPaginations.page_size"
                      :layout="projectPaginations.layout"
                      :total="projectPaginations.total"
                      :pager-count="5"
                    ></el-pagination>
                  </el-col>
                </el-row>
              </el-footer>
            </el-container>
          </el-tab-pane>
          <el-tab-pane label="储值卡" v-if="IsExistSavingCard" name="2">
            <el-container>
              <el-main>
                <el-row class="category_project">
                  <el-col :span="6" class="border_right text_center category">
                    <el-scrollbar class="el-scrollbar_height">
                      <div
                        v-for="(item, index) in savingCardCategory"
                        :key="index"
                        :class="[savingCategoryIndex == index ? 'category_select' : '', 'pad_10_0', 'border_bottom', 'cursor_pointer']"
                        @click="savingCategoryChange(item, index)"
                      >
                        {{ item.CategoryName }}
                      </div>
                    </el-scrollbar>
                  </el-col>
                  <el-col :span="18" class="project">
                    <div class="producct_list" v-loading="savingLoading">
                      <el-scrollbar class="el-scrollbar_height">
                        <div v-for="saving in savingCardList" :key="saving.ID" class="border_bottom pad_5_10 cursor_pointer" @click="savingCardChange(saving)">
                          <div>
                            <span>{{ saving.Name }}</span>
                            <span v-if="saving.Alias">({{ saving.Alias }})</span>
                          </div>
                          <div>
                            <span class="color_red">¥ {{ saving.Price | toFixed | NumFormat }}</span>
                            <span class="marlt_10 font_12 color_gray" v-if="saving.LargessPrice">赠送：¥ {{ saving.LargessPrice }}</span>
                            <span class="font_12 color_gray" style="float: right">{{ saving.ValidDayName }}</span>
                          </div>
                        </div>
                      </el-scrollbar>
                    </div>
                  </el-col>
                </el-row>
              </el-main>
              <el-footer class="border_top">
                <el-row type="flex" align="middle">
                  <el-col :span="24" class="text_right">
                    <el-pagination
                      background
                      v-if="savingPaginations.total > 0"
                      @current-change="handleSavingCurrentChange"
                      :current-page.sync="savingPaginations.page"
                      :page-size="savingPaginations.page_size"
                      :layout="savingPaginations.layout"
                      :total="savingPaginations.total"
                      :pager-count="5"
                    ></el-pagination>
                  </el-col>
                </el-row>
              </el-footer>
            </el-container>
            <!--  -->
          </el-tab-pane>
          <el-tab-pane label="时效卡" v-if="IsExistTimeCard" name="3">
            <el-container>
              <el-main>
                <el-row class="category_project">
                  <el-col :span="6" class="border_right text_center category">
                    <el-scrollbar class="el-scrollbar_height">
                      <div
                        v-for="(item, index) in timeCardCategory"
                        :key="index"
                        :class="[timeCategoryIndex == index ? 'category_select' : '', 'pad_10_0', 'border_bottom', 'cursor_pointer']"
                        @click="timeCategoryChange(item, index)"
                      >
                        {{ item.CategoryName }}
                      </div>
                    </el-scrollbar>
                  </el-col>
                  <el-col :span="18" class="project">
                    <div class="producct_list" v-loading="timeCardLoading">
                      <el-scrollbar class="el-scrollbar_height">
                        <div v-for="time in timeCardList" :key="time.ID" class="border_bottom pad_5_10 cursor_pointer" @click="timeCardChange(time)">
                          <div>
                            <span>{{ time.Name }}</span>
                            <span v-if="time.Alias">({{ time.Alias }})</span>
                          </div>
                          <div class="color_red">
                            <span>¥ {{ time.Price | toFixed | NumFormat }}</span>
                            <span class="font_12 color_gray" style="float: right">{{ time.ValidDayName }}</span>
                          </div>
                        </div>
                      </el-scrollbar>
                    </div>
                  </el-col>
                </el-row>
              </el-main>
              <el-footer class="border_top">
                <el-row type="flex" align="middle">
                  <el-col :span="24" class="text_right">
                    <el-pagination
                      background
                      v-if="timeCardPaginations.total > 0"
                      @current-change="handleTimeCurrentChange"
                      :current-page.sync="timeCardPaginations.page"
                      :page-size="timeCardPaginations.page_size"
                      :layout="timeCardPaginations.layout"
                      :total="timeCardPaginations.total"
                      :pager-count="5"
                    ></el-pagination>
                  </el-col>
                </el-row>
              </el-footer>
            </el-container>
          </el-tab-pane>
          <el-tab-pane label="通用次卡" v-if="IsExistGeneralCard" name="4">
            <el-container>
              <el-main>
                <el-row class="category_project">
                  <el-col :span="6" class="border_right text_center category">
                    <el-scrollbar class="el-scrollbar_height">
                      <div
                        v-for="(item, index) in generalCardCategory"
                        :key="index"
                        :class="[generalCategoryIndex == index ? 'category_select' : '', 'pad_10_0', 'border_bottom', 'cursor_pointer']"
                        @click="generalCategoryChange(item, index)"
                      >
                        {{ item.CategoryName }}
                      </div>
                    </el-scrollbar>
                  </el-col>
                  <el-col :span="18" class="project">
                    <div class="producct_list" v-loading="generalCarLoading">
                      <el-scrollbar class="el-scrollbar_height">
                        <div
                          v-for="general in generalCardList"
                          :key="general.ID"
                          class="border_bottom pad_5_10 cursor_pointer"
                          @click="generalCardChange(general)"
                        >
                          <div>
                            <span>{{ general.Name }}</span>
                            <span v-if="general.Alias">({{ general.Alias }})</span>
                            <span class="font_12 color_gray">× {{ general.Amount }}次</span>
                          </div>
                          <div class="color_red">
                            <span>¥ {{ general.Price | toFixed | NumFormat }}</span>
                            <span class="font_12 color_gray" style="float: right">{{ general.ValidDayName }}</span>
                          </div>
                        </div>
                      </el-scrollbar>
                    </div>
                  </el-col>
                </el-row>
              </el-main>
              <el-footer class="border_top">
                <el-row type="flex" align="middle">
                  <el-col :span="24" class="text_right">
                    <el-pagination
                      background
                      v-if="generalCarPaginations.total > 0"
                      @current-change="handleGeneralCarCurrentChange"
                      :current-page.sync="generalCarPaginations.page"
                      :page-size="generalCarPaginations.page_size"
                      :layout="generalCarPaginations.layout"
                      :total="generalCarPaginations.total"
                      :pager-count="5"
                    ></el-pagination>
                  </el-col>
                </el-row>
              </el-footer>
            </el-container>
          </el-tab-pane>
          <el-tab-pane label="套餐卡" v-if="IsExistPackageCard" name="5">
            <el-container>
              <el-main>
                <el-row class="category_project">
                  <el-col :span="6" class="border_right text_center category">
                    <el-scrollbar class="el-scrollbar_height">
                      <div
                        v-for="(item, index) in packageCardCategory"
                        :key="index"
                        :class="[packageCategoryIndex == index ? 'category_select' : '', 'pad_10_0', 'border_bottom', 'cursor_pointer']"
                        @click="packageCategoryChange(item, index)"
                      >
                        {{ item.CategoryName }}
                      </div>
                    </el-scrollbar>
                  </el-col>
                  <el-col :span="18" class="project">
                    <div class="producct_list" v-loading="packageCardLoading">
                      <el-scrollbar class="el-scrollbar_height">
                        <div v-for="pack in packageCardList" :key="pack.ID" class="border_bottom pad_5_10 cursor_pointer" @click="packageCardChange(pack)">
                          <div>
                            <span>{{ pack.Name }}</span>
                            <span v-if="pack.Alias">({{ pack.Alias }})</span>
                          </div>
                          <div class="color_red">
                            <span>¥ {{ pack.Price | toFixed | NumFormat }}</span>
                            <span class="font_12 color_gray" style="float: right">{{ pack.ValidDayName }}</span>
                          </div>
                        </div>
                      </el-scrollbar>
                    </div>
                  </el-col>
                </el-row>
              </el-main>
              <el-footer class="border_top">
                <el-row type="flex" align="middle">
                  <el-col :span="24" class="text_right">
                    <el-pagination
                      background
                      v-if="packageCardPaginations.total > 0"
                      @current-change="handlePackageCardCurrentChange"
                      :current-page.sync="packageCardPaginations.page"
                      :page-size="packageCardPaginations.page_size"
                      :layout="packageCardPaginations.layout"
                      :total="packageCardPaginations.total"
                      :pager-count="5"
                    ></el-pagination>
                  </el-col>
                </el-row>
              </el-footer>
            </el-container>
          </el-tab-pane>
          <el-tab-pane label="产品" v-if="IsExistProduct" name="6">
            <el-container>
              <el-main>
                <el-row class="category_project">
                  <el-col :span="5" class="border_right text_center category">
                    <el-scrollbar class="el-scrollbar_height">
                      <div
                        v-for="(item, index) in productCategory"
                        :key="index"
                        :class="[productCategoryIndex == index ? 'category_select' : '', 'pad_10_0', 'border_bottom', 'cursor_pointer']"
                        @click="productCategoryChange(item, index)"
                      >
                        {{ item.ParentName }}
                      </div>
                    </el-scrollbar>
                  </el-col>
                  <el-col :span="5" class="border_right text_center category">
                    <el-scrollbar class="el-scrollbar_height">
                      <div
                        v-for="(item, index) in productSecondCategory"
                        :key="index"
                        :class="[productSecondCategoryIndex == index ? 'category_select' : '', 'pad_10_0', 'border_bottom', 'cursor_pointer']"
                        @click="productSecondCategoryChange(item, index)"
                      >
                        {{ item.CategoryName }}
                      </div>
                    </el-scrollbar>
                  </el-col>
                  <el-col :span="14" class="project">
                    <div class="producct_list" v-loading="productLoading">
                      <el-scrollbar class="el-scrollbar_height">
                        <div v-for="product in productList" class="border_bottom pad_5_10 cursor_pointer" :key="product.ID" @click="productChange(product)">
                          <div>
                            <span>{{ product.Name }}</span>
                            <span v-if="product.Alias">({{ product.Alias }})</span>
                          </div>
                          <div class="color_red">¥ {{ product.Price | toFixed | NumFormat }}</div>
                        </div>
                      </el-scrollbar>
                    </div>
                  </el-col>
                </el-row>
              </el-main>
              <el-footer class="border_top">
                <el-row type="flex" align="middle">
                  <el-col :span="24" class="text_right">
                    <el-pagination
                      background
                      v-if="productPaginations.total > 0"
                      @current-change="handleProductCurrentChange"
                      :current-page.sync="productPaginations.page"
                      :page-size="productPaginations.page_size"
                      :layout="productPaginations.layout"
                      :total="productPaginations.total"
                      :pager-count="5"
                    ></el-pagination>
                  </el-col>
                </el-row>
              </el-footer>
            </el-container>
          </el-tab-pane>
        </el-tabs>
      </el-col>
      <el-col :span="15" class="project_right position_relative">
        <el-container style="height: 100%">
          <el-main>
            <el-scrollbar class="el-scrollbar_height">
              <!--项目-->
              <div v-if="selectProject.length > 0">
                <el-row class="row_header border_bottom">
                  <el-col :span="24">
                    <el-col :span="6">项目</el-col>
                    <el-col :span="5">数量</el-col>
                    <el-col :span="5">金额</el-col>
                    <el-col :span="5">欠款</el-col>
                    <el-col :span="2">赠送</el-col>
                    <el-col :span="1"></el-col>
                  </el-col>
                </el-row>
                <el-row class="border_bottom" v-for="(item, index) in selectProject" :key="index">
                  <el-col :span="24" class="pad_10 border_bottom">
                    <el-col :span="24">
                      <el-col :span="6">
                        <div>
                          <span>{{ item.Name }}</span>
                          <span v-if="item.Alias">({{ item.Alias }})</span>
                          <span class="marlt_5 color_primary cursor_pointer">
                            <el-button icon="el-icon-edit-outline" type="text" style="padding: unset" @click="editProjectRemarkClick(item)"></el-button>
                          </span>
                        </div>
                      </el-col>
                      <el-col :span="5">
                        <el-input-number
                          :min="1"
                          :max="999"
                          size="mini"
                          style="width: 100px"
                          v-model="item.number"
                          @change="numberChange(item)"
                        ></el-input-number>
                      </el-col>
                      <el-col :span="5">
                        <span class="marrt_10">{{ parseFloat(item.TotalAmount) | toFixed | NumFormat }}</span>
                        <el-button
                          type="primary"
                          icon="el-icon-edit"
                          circle
                          size="mini"
                          @click="savingCardDeductionClick(1, selectProject, item, index)"
                          v-if="!item.IsLargess"
                        ></el-button>
                      </el-col>
                      <el-col :span="5">
                        <!--  -->
                        <el-input
                          v-model="item.ArrearAmount"
                          :min="0"
                          v-enter-number2
                          placeholder
                          size="mini"
                          v-input-fixed="2"
                          style="width: 70px"
                          @input="arrearChange(item)"
                          :disabled="item.IsLargess"
                        ></el-input>
                      </el-col>
                      <el-col :span="2">
                        <el-checkbox v-show="item.IsAllowLargess" v-model="item.IsLargess" @change="largessChange(item)"></el-checkbox>
                      </el-col>
                      <el-col :span="1" :offset="item.IsAllowLargess ? 0 : 2" class="text_right">
                        <el-button type="danger" icon="el-icon-delete" circle size="mini" @click="removeClick(1, index, item)"></el-button>
                      </el-col>
                    </el-col>
                    <el-col :span="24" style="margin-top: 4px">
                      <el-col :span="4">
                        <div class="color_red">¥ {{ item.Price | toFixed | NumFormat }}</div>
                      </el-col>
                      <el-col :span="20">
                        <div class="text_right">
                          <span class="font_12">支付金额：{{ (item.IsLargess ? 0 : item.PayAmount) | toFixed | NumFormat }}</span>
                          <span class="color_gray font_12 marlt_15" v-if="item.discountPrice != 0">
                            手动改价：
                            <span class="color_red" v-if="item.discountPrice > 0">-{{ item.discountPrice | toFixed | NumFormat }}</span>
                            <span class="color_green" v-else>+{{ mathAbsData(item.discountPrice) | toFixed | NumFormat }}</span>
                          </span>
                          <span class="color_gray font_12 marlt_15" v-if="item.CardDiscountPrice > 0">
                            卡优惠：
                            <span class="color_red">-{{ parseFloat(item.CardDiscountPrice) | toFixed | NumFormat }}</span>
                          </span>
                          <span class="color_gray font_12 marlt_15" v-if="item.CardDeductionAmount > 0">
                            卡抵扣：
                            <span class="color_red">-{{ parseFloat(item.CardDeductionAmount) | toFixed | NumFormat }}</span>
                          </span>
                          <span class="color_gray font_12 marlt_15" v-if="item.MemberPreferentialAmountTotal > 0">
                            会员优惠：
                            <span class="color_red">-{{ parseFloat(item.MemberPreferentialAmountTotal) | toFixed | NumFormat }}</span>
                          </span>
                        </div>
                      </el-col>
                    </el-col>
                  </el-col>

                  <el-col v-if="item.Remark" :span="24" class="pad_10 border_bottom font_12 color_333">备注信息: {{ item.Remark }} </el-col>

                  <el-col v-if="item.handleTypeList.length" :span="24" class="pad_10 padbm_0 border_bottom">
                    <el-row
                      class="cursor_pointer"
                      v-for="(handler, pIndex) in item.handleTypeList"
                      :key="pIndex"
                      @click.native="employeeHandleClick(1, selectProject, item, index)"
                      type="flex"
                      align="top"
                    >
                      <el-col :span="4">
                        <el-form :inline="true" size="mini" label-position="left">
                          <el-form-item style="margin-bottom: 10px" :label="`${handler.Name}：`"></el-form-item>
                        </el-form>
                      </el-col>
                      <el-col :span="20">
                        <el-form :inline="true" size="mini">
                          <el-form-item
                            style="margin-bottom: 10px"
                            v-for="(employee, handleIndex) in handler.Employee"
                            :key="handleIndex"
                            :label="`${employee.EmployeeName}`"
                          >
                            <el-input
                              class="employee_num"
                              v-model="employee.Discount"
                              size="mini"
                              :min="0"
                              :max="100"
                              type="number"
                              v-on:click.native.stop
                              v-input-fixed
                              @input="handlerPercentChange(handler.Employee, employee)"
                            >
                              <template slot="append">%</template>
                            </el-input>
                            <i
                              class="el-icon-error marlt_5 font_16"
                              style="color: #909399; vertical-align: middle"
                              v-on:click.stop="removeHandleClick(handler, handleIndex)"
                            ></i>
                          </el-form-item>
                        </el-form>
                      </el-col>
                    </el-row>
                  </el-col>
                </el-row>
              </div>
              <!--储值卡-->
              <div v-if="selectSavingCard.length > 0">
                <el-row class="row_header border_bottom">
                  <el-col :span="24">
                    <el-col :span="6">储值卡</el-col>
                    <el-col :span="5">数量</el-col>
                    <el-col :span="8">金额/赠额</el-col>
                    <el-col :span="4">欠款</el-col>
                    <el-col :span="1"></el-col>
                  </el-col>
                </el-row>
                <el-row class="border_bottom" v-for="(item, index) in selectSavingCard" :key="index">
                  <el-col :span="24" class="pad_10 border_bottom">
                    <el-col :span="24">
                      <el-col :span="6">
                        <div>
                          <span>{{ item.Name }}</span>
                          <span v-if="item.Alias">({{ item.Alias }})</span>
                          <span class="marlt_5 color_primary cursor_pointer">
                            <el-button icon="el-icon-edit-outline" type="text" style="padding: unset" @click="editProjectRemarkClick(item)"></el-button>
                          </span>
                        </div>
                      </el-col>
                      <el-col :span="5">
                        <el-input-number
                          :min="1"
                          size="mini"
                          :max="999"
                          style="width: 100px"
                          v-model="item.number"
                          @change="numberChange(item)"
                        ></el-input-number>
                      </el-col>
                      <el-col :span="8">
                        <el-row>
                          <el-col :span="7">
                            <el-input
                              v-model="item.Amount"
                              placeholder="金额"
                              :min="0"
                              class="savingCardAmount"
                              v-input-fixed="2"
                              size="mini"
                              @input="savingAmountChange(item)"
                              :disabled="!item.IsModifyPrice"
                            ></el-input>
                          </el-col>
                          <el-col :span="7">
                            <el-input
                              v-model="item.LargessPrice"
                              placeholder="赠送金额"
                              :min="0"
                              class="savingCardLargessAmount"
                              v-input-fixed="2"
                              size="mini"
                              :disabled="!item.IsModifyLargessPrice"
                            ></el-input>
                          </el-col>
                        </el-row>
                      </el-col>
                      <el-col :span="4">
                        <el-row>
                          <el-col :span="18">
                            <el-input v-model="item.ArrearAmount" placeholder :min="0" v-input-fixed="2" size="mini" @input="arrearChange(item)"></el-input>
                          </el-col>
                        </el-row>
                      </el-col>
                      <el-col :span="1" class="text_right">
                        <el-button type="danger" icon="el-icon-delete" circle size="mini" @click="removeClick(2, index, item)"></el-button>
                      </el-col>
                    </el-col>
                    <el-col :span="24" style="margin-top: 4px">
                      <el-col :span="8">
                        <div>
                          <span class="color_red">¥ {{ item.Price | toFixed | NumFormat }}</span>
                          <span class="marlt_10 font_12 color_gray" v-if="item.largessPrice">赠送：¥ {{ item.largessPrice }}</span>
                        </div>
                      </el-col>
                      <el-col :span="16">
                        <div class="text_right font_12">支付金额：{{ item.PayAmount | toFixed | NumFormat }}</div>
                      </el-col>
                    </el-col>
                    <el-col :span="24" v-if="item.SavingCardRechargeRules.length > 0" class="dis_flex flex_dir_row flex_wrap">
                      <!-- never always -->
                      <el-card
                        shadow="never"
                        v-for="(RecharPrice, index) in item.SavingCardRechargeRules"
                        :key="index"
                        :body-style="{
                          padding: '0px',
                          padding: '10px',
                          'min-width': '100px',
                        }"
                        class="marrt_10 martp_10"
                        :class="RechargeRulesClass(item, index)"
                        @click.native="selectRechargeRules(item, RecharPrice, index)"
                      >
                        <div class="font_15 font_weight_600">
                          {{ RecharPrice.Price | toFixed | NumFormat }}
                        </div>
                        <div class="font_13 color_666">赠额：{{ RecharPrice.LargessPrice | toFixed | NumFormat }}</div>
                      </el-card>
                    </el-col>
                  </el-col>

                  <el-col v-if="item.Remark" :span="24" class="pad_10 border_bottom font_12 color_333">备注信息: {{ item.Remark }} </el-col>

                  <el-col v-if="item.handleTypeList.length" :span="24" class="pad_10 padbm_0 border_bottom">
                    <el-row
                      class="cursor_pointer"
                      v-for="(handler, pIndex) in item.handleTypeList"
                      :key="pIndex"
                      @click.native="employeeHandleClick(2, selectSavingCard, item, index)"
                    >
                      <el-col :span="4">
                        <el-form :inline="true" size="mini" label-position="left">
                          <el-form-item style="margin-bottom: 10px" :label="`${handler.Name}：`"></el-form-item>
                        </el-form>
                      </el-col>
                      <el-col :span="20">
                        <el-form :inline="true" size="mini">
                          <el-form-item
                            style="margin-bottom: 10px"
                            v-for="(employee, handleIndex) in handler.Employee"
                            :key="handleIndex"
                            :label="`${employee.EmployeeName}`"
                          >
                            <el-input
                              class="employee_num"
                              v-model="employee.Discount"
                              size="mini"
                              :min="0"
                              :max="100"
                              type="number"
                              v-on:click.native.stop
                              v-input-fixed
                              @input="handlerPercentChange(handler.Employee, employee)"
                            >
                              <template slot="append">%</template>
                            </el-input>
                            <i
                              class="el-icon-error marlt_5 font_16"
                              style="color: #909399; vertical-align: middle"
                              v-on:click.stop="removeHandleClick(handler, handleIndex)"
                            ></i>
                          </el-form-item>
                        </el-form>
                      </el-col>
                    </el-row>
                  </el-col>
                </el-row>
              </div>
              <!--时效卡-->
              <div v-if="selectTimeCard.length > 0">
                <el-row class="row_header border_bottom">
                  <el-col :span="24">
                    <el-col :span="6">时效卡</el-col>
                    <el-col :span="5">数量</el-col>
                    <el-col :span="5">金额</el-col>
                    <el-col :span="5">欠款</el-col>
                    <el-col :span="2">赠送</el-col>
                    <el-col :span="1"></el-col>
                  </el-col>
                </el-row>
                <el-row class="border_bottom" v-for="(item, index) in selectTimeCard" :key="index">
                  <el-col :span="24" class="pad_10 border_bottom">
                    <el-col :span="24">
                      <el-col :span="6">
                        <div>
                          <span>{{ item.Name }}</span>
                          <span v-if="item.Alias">({{ item.Alias }})</span>
                          <span class="marlt_5 color_primary cursor_pointer">
                            <el-button icon="el-icon-edit-outline" type="text" style="padding: unset" @click="editProjectRemarkClick(item)"></el-button>
                          </span>
                        </div>
                      </el-col>
                      <el-col :span="5">
                        <el-input-number
                          :min="1"
                          :max="999"
                          size="mini"
                          style="width: 100px"
                          v-model="item.number"
                          @change="numberChange(item)"
                        ></el-input-number>
                      </el-col>
                      <el-col :span="5">
                        <span class="marrt_15">{{ parseFloat(item.TotalAmount) | toFixed | NumFormat }}</span>
                        <el-button
                          type="primary"
                          icon="el-icon-edit"
                          circle
                          size="mini"
                          @click="savingCardDeductionClick(3, selectTimeCard, item, index)"
                          v-if="!item.IsLargess"
                        ></el-button>
                      </el-col>
                      <el-col :span="5">
                        <el-input
                          v-model="item.ArrearAmount"
                          :min="0"
                          placeholder
                          size="mini"
                          v-input-fixed="2"
                          style="width: 70px"
                          @input="arrearChange(item)"
                          :disabled="item.IsLargess"
                        ></el-input>
                      </el-col>
                      <el-col :span="2" v-show="item.IsAllowLargess">
                        <el-checkbox v-model="item.IsLargess" @change="largessChange(item)"></el-checkbox>
                      </el-col>
                      <el-col :span="1" :offset="item.IsAllowLargess ? 0 : 2" class="text_right">
                        <el-button type="danger" icon="el-icon-delete" circle size="mini" @click="removeClick(3, index, item)"></el-button>
                      </el-col>
                    </el-col>
                    <el-col :span="24" style="margin-top: 4px">
                      <el-col :span="4">
                        <div class="color_red">¥ {{ item.Price | toFixed | NumFormat }}</div>
                      </el-col>
                      <el-col :span="20">
                        <div class="text_right font_12">
                          <!-- <span>支付金额：{{item.PayAmount | NumFormat}}</span> -->
                          <span class="font_12">支付金额：{{ (item.IsLargess ? 0 : item.PayAmount) | toFixed | NumFormat }}</span>
                          <span class="color_gray font_12 marlt_15" v-if="item.discountPrice != 0">
                            手动改价：
                            <span class="color_red" v-if="item.discountPrice > 0">-{{ item.discountPrice | toFixed | NumFormat }}</span>
                            <span class="color_green" v-else>+{{ mathAbsData(item.discountPrice) | toFixed | NumFormat }}</span>
                          </span>
                          <span class="color_gray font_12 marlt_15" v-if="item.CardDiscountPrice > 0">
                            卡优惠：
                            <span class="color_red">- {{ item.CardDiscountPrice | toFixed | NumFormat }}</span>
                          </span>
                          <span class="color_gray font_12 marlt_15" v-if="item.CardDeductionAmount > 0">
                            卡抵扣：
                            <span class="color_red">- {{ item.CardDeductionAmount | toFixed | NumFormat }}</span>
                          </span>
                          <span class="color_gray font_12 marlt_15" v-if="item.MemberPreferentialAmountTotal > 0">
                            会员优惠：
                            <span class="color_red">-{{ parseFloat(item.MemberPreferentialAmountTotal) | toFixed | NumFormat }}</span>
                          </span>
                        </div>
                      </el-col>
                    </el-col>
                  </el-col>

                  <el-col v-if="item.Remark" :span="24" class="pad_10 border_bottom font_12 color_333">备注信息: {{ item.Remark }} </el-col>

                  <el-col v-if="item.handleTypeList.length" :span="24" class="pad_10 padbm_0 border_bottom">
                    <el-row
                      class="cursor_pointer"
                      v-for="(handler, pIndex) in item.handleTypeList"
                      :key="pIndex"
                      @click.native="employeeHandleClick(3, selectTimeCard, item, index)"
                    >
                      <el-col :span="4">
                        <el-form :inline="true" size="mini" label-position="left">
                          <el-form-item style="margin-bottom: 10px" :label="`${handler.Name}：`"></el-form-item>
                        </el-form>
                      </el-col>
                      <el-col :span="20">
                        <el-form :inline="true" size="mini">
                          <el-form-item
                            style="margin-bottom: 10px"
                            v-for="(employee, handleIndex) in handler.Employee"
                            :key="handleIndex"
                            :label="`${employee.EmployeeName}`"
                          >
                            <el-input
                              class="employee_num"
                              v-model="employee.Discount"
                              size="mini"
                              :min="0"
                              :max="100"
                              type="number"
                              v-on:click.native.stop
                              v-input-fixed
                              @input="handlerPercentChange(handler.Employee, employee)"
                            >
                              <template slot="append">%</template>
                            </el-input>
                            <i
                              class="el-icon-error marlt_5 font_16"
                              style="color: #909399; vertical-align: middle"
                              v-on:click.stop="removeHandleClick(handler, handleIndex)"
                            ></i>
                          </el-form-item>
                        </el-form>
                      </el-col>
                    </el-row>
                  </el-col>
                </el-row>
              </div>
              <!--通用次卡-->
              <div v-if="selectGeneralCard.length > 0">
                <el-row class="row_header border_bottom">
                  <el-col :span="24">
                    <el-col :span="6">通用次卡</el-col>
                    <el-col :span="5">数量</el-col>
                    <el-col :span="5">金额</el-col>
                    <el-col :span="5">欠款</el-col>
                    <el-col :span="2">赠送</el-col>
                    <el-col :span="1"></el-col>
                  </el-col>
                </el-row>
                <el-row class="border_bottom" v-for="(item, index) in selectGeneralCard" :key="index">
                  <el-col :span="24" class="pad_10 border_bottom">
                    <el-col :span="24">
                      <el-col :span="6">
                        <div>
                          <span>{{ item.Name }}</span>
                          <span v-if="item.Alias">({{ item.Alias }})</span>
                          <span class="font_12 color_gray">× {{ item.Times }}次</span>
                          <span class="marlt_5 color_primary cursor_pointer">
                            <el-button icon="el-icon-edit-outline" type="text" style="padding: unset" @click="editProjectRemarkClick(item)"></el-button>
                          </span>
                        </div>
                      </el-col>
                      <el-col :span="5">
                        <el-input-number
                          :min="1"
                          :max="999"
                          size="mini"
                          style="width: 100px"
                          v-model="item.number"
                          @change="numberChange(item)"
                        ></el-input-number>
                      </el-col>
                      <el-col :span="5">
                        <span class="marrt_15">{{ parseFloat(item.TotalAmount) | toFixed | NumFormat }}</span>
                        <el-button
                          type="primary"
                          icon="el-icon-edit"
                          circle
                          size="mini"
                          @click="savingCardDeductionClick(4, selectGeneralCard, item, index)"
                          v-if="!item.IsLargess"
                        ></el-button>
                      </el-col>
                      <el-col :span="5">
                        <el-input
                          v-model="item.ArrearAmount"
                          :min="0"
                          placeholder
                          size="mini"
                          v-input-fixed="2"
                          style="width: 70px"
                          @input="arrearChange(item)"
                          :disabled="item.IsLargess"
                        ></el-input>
                      </el-col>
                      <el-col :span="2" v-show="item.IsAllowLargess">
                        <el-checkbox v-model="item.IsLargess" @change="largessChange(item)"></el-checkbox>
                      </el-col>
                      <el-col :span="1" :offset="item.IsAllowLargess ? 0 : 2" class="text_right">
                        <el-button type="danger" icon="el-icon-delete" circle size="mini" @click="removeClick(4, index, item)"></el-button>
                      </el-col>
                    </el-col>
                    <el-col :span="24" style="margin-top: 4px">
                      <el-col :span="4">
                        <div class="color_red">¥ {{ item.Price | toFixed | NumFormat }}</div>
                      </el-col>
                      <el-col :span="20">
                        <div class="text_right font_12">
                          <!-- <span>支付金额： {{item.PayAmount | NumFormat}}</span> -->
                          <span class="font_12">支付金额：{{ (item.IsLargess ? 0 : item.PayAmount) | toFixed | NumFormat }}</span>
                          <span class="color_gray font_12 marlt_15" v-if="item.discountPrice != 0">
                            手动改价：
                            <span class="color_red" v-if="item.discountPrice > 0">-{{ item.discountPrice | toFixed | NumFormat }}</span>
                            <span class="color_green" v-else>+{{ mathAbsData(item.discountPrice) | toFixed | NumFormat }}</span>
                          </span>
                          <span class="color_gray font_12 marlt_15" v-if="item.CardDiscountPrice">
                            卡优惠：
                            <span class="color_red">- {{ item.CardDiscountPrice | toFixed | NumFormat }}</span>
                          </span>
                          <span class="color_gray font_12 marlt_15" v-if="item.CardDeductionAmount">
                            卡抵扣：
                            <span class="color_red">- {{ item.CardDeductionAmount | toFixed | NumFormat }}</span>
                          </span>
                          <span class="color_gray font_12 marlt_15" v-if="item.MemberPreferentialAmountTotal > 0">
                            会员优惠：
                            <span class="color_red">-{{ parseFloat(item.MemberPreferentialAmountTotal) | toFixed | NumFormat }}</span>
                          </span>
                        </div>
                      </el-col>
                    </el-col>
                  </el-col>

                  <el-col v-if="item.Remark" :span="24" class="pad_10 border_bottom font_12 color_333">备注信息: {{ item.Remark }} </el-col>

                  <el-col v-if="item.handleTypeList.length" :span="24" class="pad_10 padbm_0 border_bottom">
                    <el-row
                      class="cursor_pointer"
                      v-for="(handler, pIndex) in item.handleTypeList"
                      :key="pIndex"
                      @click.native="employeeHandleClick(4, selectGeneralCard, item, index)"
                    >
                      <el-col :span="4">
                        <el-form :inline="true" size="mini" label-position="left">
                          <el-form-item style="margin-bottom: 10px" :label="`${handler.Name}：`"></el-form-item>
                        </el-form>
                      </el-col>
                      <el-col :span="20">
                        <el-form :inline="true" size="mini">
                          <el-form-item
                            style="margin-bottom: 10px"
                            v-for="(employee, handleIndex) in handler.Employee"
                            :key="handleIndex"
                            :label="`${employee.EmployeeName}`"
                          >
                            <el-input
                              class="employee_num"
                              v-model="employee.Discount"
                              size="mini"
                              :min="0"
                              :max="100"
                              type="number"
                              v-on:click.native.stop
                              v-input-fixed
                              @input="handlerPercentChange(handler.Employee, employee)"
                            >
                              <template slot="append">%</template>
                            </el-input>
                            <i
                              class="el-icon-error marlt_5 font_16"
                              style="color: #909399; vertical-align: middle"
                              v-on:click.stop="removeHandleClick(handler, handleIndex)"
                            ></i>
                          </el-form-item>
                        </el-form>
                      </el-col>
                    </el-row>
                  </el-col>
                </el-row>
              </div>
              <!--产品-->
              <div v-if="selectProduct.length > 0">
                <el-row class="row_header border_bottom">
                  <el-col :span="24">
                    <el-col :span="6">产品</el-col>
                    <el-col :span="5">数量</el-col>
                    <el-col :span="5">金额</el-col>
                    <el-col :span="5">欠款</el-col>
                    <el-col :span="2">赠送</el-col>
                    <el-col :span="1"></el-col>
                  </el-col>
                </el-row>
                <el-row class="border_bottom" v-for="(item, index) in selectProduct" :key="index">
                  <el-col :span="24" class="pad_10 border_bottom">
                    <el-col :span="24">
                      <el-col :span="6">
                        <div>
                          <span>{{ item.Name }}</span>
                          <span v-if="item.Alias">({{ item.Alias }})</span>
                          <span class="marlt_5 color_primary cursor_pointer">
                            <el-button icon="el-icon-edit-outline" type="text" style="padding: unset" @click="editProjectRemarkClick(item)"></el-button>
                          </span>
                        </div>
                      </el-col>
                      <el-col :span="5">
                        <el-input-number
                          :min="1"
                          :max="999"
                          size="mini"
                          style="width: 100px"
                          v-model="item.number"
                          @change="numberChange(item)"
                        ></el-input-number>
                      </el-col>
                      <el-col :span="5">
                        <span class="marrt_15">{{ parseFloat(item.TotalAmount) | toFixed | NumFormat }}</span>
                        <el-button
                          type="primary"
                          icon="el-icon-edit"
                          circle
                          size="mini"
                          @click="savingCardDeductionClick(6, selectProduct, item, index)"
                          v-if="!item.IsLargess"
                        ></el-button>
                      </el-col>
                      <el-col :span="5">
                        <el-input
                          v-model="item.ArrearAmount"
                          :min="0"
                          placeholder
                          size="mini"
                          v-input-fixed="2"
                          style="width: 70px"
                          @input="arrearChange(item)"
                          :disabled="item.IsLargess"
                        ></el-input>
                      </el-col>
                      <el-col :span="2" v-show="item.IsAllowLargess">
                        <el-checkbox v-model="item.IsLargess" @change="largessChange(item)"></el-checkbox>
                      </el-col>
                      <el-col :span="1" :offset="item.IsAllowLargess ? 0 : 2" class="text_right">
                        <el-button type="danger" icon="el-icon-delete" circle size="mini" @click="removeClick(6, index, item)"></el-button>
                      </el-col>
                    </el-col>
                    <el-col :span="24" style="margin-top: 4px">
                      <el-col :span="4">
                        <div class="color_red">¥ {{ item.Price | toFixed | NumFormat }}</div>
                      </el-col>
                      <el-col :span="20">
                        <div class="text_right font_12">
                          <!-- <span>支付金额：{{item.PayAmount | NumFormat}}</span> -->
                          <span class="font_12">支付金额：{{ (item.IsLargess ? 0 : item.PayAmount) | toFixed | NumFormat }}</span>
                          <span class="color_gray font_12 marlt_15" v-if="item.discountPrice != 0">
                            手动改价：
                            <span class="color_red" v-if="item.discountPrice > 0">-{{ item.discountPrice | toFixed | NumFormat }}</span>
                            <span class="color_green" v-else>+{{ mathAbsData(item.discountPrice) | toFixed | NumFormat }}</span>
                          </span>
                          <span class="color_gray font_12 marlt_15" v-if="item.CardDiscountPrice > 0">
                            储值卡优惠：
                            <span class="color_red">- {{ item.CardDiscountPrice | toFixed | NumFormat }}</span>
                          </span>
                          <span class="color_gray font_12 marlt_15" v-if="item.CardDeductionAmount > 0">
                            储值卡抵扣：
                            <span class="color_red">- {{ item.CardDeductionAmount | toFixed | NumFormat }}</span>
                          </span>
                          <span class="color_gray font_12 marlt_15" v-if="item.MemberPreferentialAmountTotal > 0">
                            会员优惠：
                            <span class="color_red">-{{ parseFloat(item.MemberPreferentialAmountTotal) | toFixed | NumFormat }}</span>
                          </span>
                        </div>
                      </el-col>
                    </el-col>
                  </el-col>

                  <el-col v-if="item.Remark" :span="24" class="pad_10 border_bottom font_12 color_333">备注信息: {{ item.Remark }} </el-col>

                  <el-col v-if="item.handleTypeList.length" :span="24" class="pad_10 padbm_0 border_bottom">
                    <el-row
                      class="cursor_pointer"
                      v-for="(handler, pIndex) in item.handleTypeList"
                      :key="pIndex"
                      @click.native="employeeHandleClick(6, selectProduct, item, index)"
                    >
                      <el-col :span="4">
                        <el-form :inline="true" size="mini" label-position="left">
                          <el-form-item style="margin-bottom: 10px" :label="`${handler.Name}：`"></el-form-item>
                        </el-form>
                      </el-col>
                      <el-col :span="20">
                        <el-form :inline="true" size="mini">
                          <el-form-item
                            style="margin-bottom: 10px"
                            v-for="(employee, handleIndex) in handler.Employee"
                            :key="handleIndex"
                            :label="`${employee.EmployeeName}`"
                          >
                            <el-input
                              class="employee_num"
                              v-model="employee.Discount"
                              size="mini"
                              :min="0"
                              :max="100"
                              type="number"
                              v-on:click.native.stop
                              v-input-fixed
                              @input="handlerPercentChange(handler.Employee, employee)"
                            >
                              <template slot="append">%</template>
                            </el-input>
                            <i
                              class="el-icon-error marlt_5 font_16"
                              style="color: #909399; vertical-align: middle"
                              v-on:click.stop="removeHandleClick(handler, handleIndex)"
                            ></i>
                          </el-form-item>
                        </el-form>
                      </el-col>
                    </el-row>
                  </el-col>
                </el-row>
              </div>
              <!--套餐卡-->
              <div v-if="selectPackageCard.length > 0">
                <el-row class="row_header border_bottom">
                  <el-col :span="24">
                    <el-col :span="6">套餐卡</el-col>
                    <el-col :span="5">数量</el-col>
                    <el-col :span="5">金额</el-col>
                    <el-col :span="5">欠款</el-col>
                    <el-col :span="2">赠送</el-col>
                    <el-col :span="1"></el-col>
                  </el-col>
                </el-row>
                <el-row class="border_bottom" v-for="(item, index) in selectPackageCard" :key="index">
                  <el-col :span="24" class="pad_10 border_bottom">
                    <el-col :span="24">
                      <el-col :span="6">
                        <div>
                          <span>{{ item.Name }}</span>
                          <span v-if="item.Alias">({{ item.Alias }})</span>
                          <span class="marlt_5 color_primary cursor_pointer">
                            <el-button type="text" @click="packDetailClick(item)" style="padding: unset"><i class="el-icon-view el-icon--right"></i></el-button>
                          </span>
                          <span class="marlt_5 color_primary cursor_pointer">
                            <el-button icon="el-icon-edit-outline" type="text" style="padding: unset" @click="editProjectRemarkClick(item)"></el-button>
                          </span>
                        </div>
                      </el-col>
                      <el-col :span="5">
                        <el-input-number
                          :min="1"
                          :max="999"
                          size="mini"
                          style="width: 100px"
                          v-model="item.number"
                          @change="numberChange(item)"
                        ></el-input-number>
                      </el-col>
                      <el-col :span="5">
                        <span class="marrt_15">{{ parseFloat(item.TotalAmount) | toFixed | NumFormat }}</span>
                        <el-button
                          type="primary"
                          icon="el-icon-edit"
                          circle
                          size="mini"
                          @click="savingCardDeductionClick(5, selectPackageCard, item, index)"
                          v-if="!item.IsLargess"
                        ></el-button>
                      </el-col>
                      <el-col :span="5">
                        <div>{{ item.ArrearAmount }}</div>
                      </el-col>
                      <el-col :span="2" v-show="item.IsAllowLargess">
                        <el-checkbox v-model="item.IsLargess" @change="largessChange(item)"></el-checkbox>
                      </el-col>
                      <el-col :span="1" :offset="item.IsAllowLargess ? 0 : 2" class="text_right">
                        <el-button type="danger" icon="el-icon-delete" circle size="mini" @click="removeClick(5, index, item)"></el-button>
                      </el-col>
                    </el-col>
                    <el-col :span="24" style="margin-top: 4px">
                      <el-col :span="4">
                        <div>
                          <span class="color_red">¥ {{ item.Price | toFixed | NumFormat }}</span>
                        </div>
                      </el-col>
                      <el-col :span="20">
                        <div class="text_right">
                          <span class="font_12">支付金额：{{ (item.IsLargess ? 0 : item.PayAmount) | toFixed | NumFormat }}</span>
                          <span class="color_gray font_12 marlt_15" v-if="item.discountPrice != 0">
                            手动改价：
                            <span class="color_red" v-if="item.discountPrice > 0">-{{ item.discountPrice | toFixed | NumFormat }}</span>
                            <span class="color_green" v-else>+{{ mathAbsData(item.discountPrice) | toFixed | NumFormat }}</span>
                          </span>
                          <span class="color_gray font_12 marlt_15" v-if="item.CardDiscountPrice > 0">
                            卡优惠：
                            <span class="color_red">- {{ item.CardDiscountPrice | toFixed | NumFormat }}</span>
                          </span>
                          <span class="color_gray font_12 marlt_15" v-if="item.CardDeductionAmount > 0">
                            卡抵扣：
                            <span class="color_red">- {{ item.CardDeductionAmount | toFixed | NumFormat }}</span>
                          </span>
                          <span class="color_gray font_12 marlt_15" v-if="item.MemberPreferentialAmountTotal > 0">
                            会员优惠：
                            <span class="color_red">-{{ parseFloat(item.MemberPreferentialAmountTotal) | toFixed | NumFormat }}</span>
                          </span>
                        </div>
                      </el-col>
                    </el-col>
                  </el-col>
                  <el-col :span="24" v-if="item.isPackDetail">
                    <el-col :span="24" class="border_bottom pad_10 font_12" v-for="(noLargess, noLargessIndex) in item.noLargess" :key="`1-${noLargess.ID}`">
                      <el-col :span="24">
                        <el-col :span="7">
                          <span>{{ noLargess.Name }} </span>
                          <span v-if="noLargess.GeneralCardAmount" class="color_gray marlt_10">单卡：{{ noLargess.GeneralCardAmount }}次</span>
                          <span class="marlt_10">× {{ noLargess.Amount }}</span>
                          <el-tag class="marlt_5" size="mini">{{ noLargess.cardType }}</el-tag>
                          <span class="marlt_10 color_primary cursor_pointer">
                            <el-button icon="el-icon-edit-outline" type="text" style="padding: unset" @click="editProjectRemarkClick(noLargess)"></el-button>
                          </span>
                        </el-col>
                        <el-col :span="4">× {{ noLargess.number }}</el-col>
                        <el-col :span="5">
                          {{ noLargess.totalAmount | toFixed | NumFormat }}
                          <el-button
                            v-if="noLargess.isCardType != 2 && !item.IsLargess"
                            class="custom-packageDetail-changebtn"
                            type="primary"
                            icon="el-icon-edit"
                            circle
                            size="mini"
                            @click="changePackageDetailItemAmount(item, noLargess, index, noLargessIndex)"
                          ></el-button>
                        </el-col>
                        <el-col :span="3">
                          <el-input
                            v-model="noLargess.ArrearAmount"
                            placeholder="请输入欠款金额"
                            size="mini"
                            :min="0"
                            v-input-fixed="2"
                            @input="packageArrearChange(item, noLargess)"
                            :disabled="item.IsLargess"
                          ></el-input>
                        </el-col>
                      </el-col>
                      <el-col :span="24" style="margin-top: 4px">
                        <el-col :span="12" class="color_red"> ¥ {{ noLargess.TotalPrice | toFixed | NumFormat }} </el-col>
                        <el-col :span="12">
                          <div class="text_right">
                            <span class="font_12">支付金额：{{ noLargess.PayAmount | toFixed | NumFormat }}</span>
                            <span
                              class="color_gray font_12 marlt_15"
                              v-if="noLargess.discountPrice != 0 && noLargess.discountPrice && noLargess.isCardType != 2"
                            >
                              手动改价：
                              <span class="color_red" v-if="noLargess.discountPrice > 0">-{{ noLargess.discountPrice | toFixed | NumFormat }}</span>
                              <span class="color_green" v-else>+{{ mathAbsData(noLargess.discountPrice) | toFixed | NumFormat }}</span>
                            </span>
                            <span class="color_gray font_12 marlt_15" v-if="noLargess.cardDiscountPrice > 0 && noLargess.isCardType != 2">
                              卡优惠：
                              <span class="color_red">-{{ parseFloat(noLargess.cardDiscountPrice) | toFixed | NumFormat }}</span>
                            </span>
                            <span class="color_gray font_12 marlt_15" v-if="noLargess.cardDeductionAmount > 0 && noLargess.isCardType != 2">
                              卡抵扣：
                              <span class="color_red">-{{ parseFloat(noLargess.cardDeductionAmount) | toFixed | NumFormat }}</span>
                            </span>
                            <span class="color_gray font_12 marlt_15" v-if="noLargess.MemberPreferentialAmountTotal > 0">
                              会员优惠：
                              <span class="color_red">-{{ parseFloat(noLargess.MemberPreferentialAmountTotal) | toFixed | NumFormat }}</span>
                            </span>
                          </div>
                        </el-col>
                      </el-col>
                      <el-col :span="24" v-if="noLargess.Remark" class="martp_10 font_12 color_333">备注信息: {{ noLargess.Remark }} </el-col>
                    </el-col>
                    <!-- 赠送 -->
                    <el-col :span="24" class="border_bottom pad_10 font_12" v-for="largess in item.largess" :key="`2-${largess.ID}`">
                      <el-col :span="24">
                        <el-col :span="7">
                          <div>
                            <el-tag class="marrt_5" size="mini" type="danger">赠</el-tag>
                            <span>{{ largess.Name }}</span>
                            <span v-if="largess.GeneralCardAmount" class="color_gray marlt_5">单卡：{{ largess.GeneralCardAmount }}次</span>
                            <span class="marlt_5"> × {{ largess.Amount }}</span>
                            <el-tag class="marlt_5" size="mini">{{ largess.cardType }}</el-tag>
                            <span class="marlt_10 color_primary cursor_pointer">
                              <el-button icon="el-icon-edit-outline" type="text" style="padding: unset" @click="editProjectRemarkClick(largess)"></el-button>
                            </span>
                          </div>
                          <div class="color_red martp_5">¥{{ largess.TotalPrice | toFixed | NumFormat }}</div>
                        </el-col>
                        <el-col :span="17">× {{ largess.number }}</el-col>
                      </el-col>

                      <el-col v-if="largess.Remark" :span="24" class="martp_10 font_12 color_333">备注信息: {{ largess.Remark }} </el-col>
                    </el-col>
                  </el-col>

                  <el-col v-if="item.Remark" :span="24" class="pad_10 border_bottom font_12 color_333">备注信息: {{ item.Remark }} </el-col>

                  <el-col v-if="item.handleTypeList.length" :span="24" class="pad_10 padbm_0 border_bottom">
                    <el-row
                      class="cursor_pointer"
                      v-for="(handler, pIndex) in item.handleTypeList"
                      :key="pIndex"
                      @click.native="employeeHandleClick(5, selectPackageCard, item, index)"
                    >
                      <el-col :span="4">
                        <el-form :inline="true" size="mini" label-position="left">
                          <el-form-item style="margin-bottom: 10px" :label="`${handler.Name}：`"></el-form-item>
                        </el-form>
                      </el-col>
                      <el-col :span="20">
                        <el-form :inline="true" size="mini">
                          <el-form-item
                            style="margin-bottom: 10px"
                            v-for="(employee, handleIndex) in handler.Employee"
                            :key="handleIndex"
                            :label="`${employee.EmployeeName}`"
                          >
                            <el-input
                              class="employee_num"
                              v-model="employee.Discount"
                              size="mini"
                              :min="0"
                              :max="100"
                              type="number"
                              v-on:click.native.stop
                              v-input-fixed
                              @input="handlerPercentChange(handler.Employee, employee)"
                            >
                              <template slot="append">%</template>
                            </el-input>
                            <i
                              class="el-icon-error marlt_5 font_16"
                              style="color: #909399; vertical-align: middle"
                              v-on:click.stop="removeHandleClick(handler, handleIndex)"
                            ></i>
                          </el-form-item>
                        </el-form>
                      </el-col>
                    </el-row>
                  </el-col>
                </el-row>
              </div>
            </el-scrollbar>
          </el-main>

          <transition>
            <div v-show="showRemark" class="orderInfoRemark" @click="showRemark = false">
              <div @click.stop class="infoRemarContent">
                <el-row @click.native="hiddenInfoRemarkClick" style="height: 40px" class="dis_flex flex_y_center">
                  <el-col :span="12">订单信息</el-col>
                  <el-col :span="12" class="text_right">
                    <el-button @click="hiddenInfoRemarkClick" type="text">收起</el-button>
                    <i class="el-icon-arrow-down color_main font_16 text-bold"></i>
                  </el-col>
                </el-row>
                <div class="back_f7f8fa" style="padding: 20px 16px 2px 16px">
                  <el-form label-width="80px" size="small">
                    <el-form-item
                      v-show="
                        (selectProject && selectProject.length > 0) ||
                        (selectProduct && selectProduct.length > 0) ||
                        (selectSavingCard && selectSavingCard.length > 0) ||
                        (selectTimeCard && selectTimeCard.length > 0) ||
                        (selectGeneralCard && selectGeneralCard.length > 0) ||
                        (selectPackageCard && selectPackageCard.length > 0)
                      "
                      label="批量添加："
                    >
                      <el-button @click="showSelectAllHandlerClick" icon="el-icon-plus">经手人</el-button>
                    </el-form-item>
                    <el-form-item label="订单备注：">
                      <el-input type="textarea" :rows="3" placeholder="请输入备注信息" v-model="Remark"></el-input>
                    </el-form-item>
                  </el-form>
                </div>
              </div>
            </div>
          </transition>
          <el-row @click.native="showRemarkClick" style="height: 40px" class="dis_flex flex_y_center pad_0_10 back_f8 font_13">
            <el-col :span="12" class="color_666">订单信息<span class="font_12 color_999">(已折叠)</span></el-col>
            <el-col :span="12" class="text_right">
              <el-button type="text">展开</el-button>
              <i class="el-icon-arrow-up color_main font_16 text-bold"></i>
            </el-col>
          </el-row>
          <el-footer class="border_top">
            <el-row type="flex" align="middle">
              <el-col :span="20">
                <span class="font_14 color_maroon marrt_15">待收款金额：¥ {{ PayAmount | toFixed | NumFormat }}</span>
                <span class="marlt_5 color_primary cursor_pointer">
                  <el-button @click="changOrderAmountClick" icon="el-icon-edit" type="text" style="padding: unset"></el-button>
                </span>
              </el-col>

              <el-col :span="2" class="text_right">
                <el-button
                  v-if="SellPermission.isSalePending || SellPermission.isSaleBilling_SalePending"
                  size="small"
                  type="primary"
                  @click="createPendingOrderClick"
                >
                  <span v-if="SellPermission.isSalePending">挂单</span>
                  <span v-if="SellPermission.isSaleBilling_SalePending">提交</span>
                </el-button>
              </el-col>
              <el-col :span="2" class="text_right">
                <el-button v-if="SellPermission.isSaleSettle || SellPermission.isSaleBilling_SaleSettle" type="primary" size="small" @click="billClick"
                  >收款</el-button
                >
              </el-col>
            </el-row>
          </el-footer>
        </el-container>
      </el-col>
    </el-row>

    <!--经手人-->
    <el-dialog title="经手人" :visible.sync="dialogVisible" width="800px" append-to-body>
      <div>
        <el-row class="padbm_10">
          <el-col :span="8">
            <el-input placeholder="请输入员工编号、姓名" prefix-icon="el-icon-search" v-model="handlerName" size="small" clearable></el-input>
          </el-col>
        </el-row>
        <el-tabs v-model="tabHandle">
          <el-tab-pane :label="handler.Name" :name="`${index}`" v-for="(handler, index) in handlerList" :key="index">
            <el-row style="max-height: 300px; overflow-y: auto">
              <el-col
                :span="12"
                v-for="item in handler.Employee.filter(
                  (item) =>
                    !handlerName ||
                    item.EmployeeName.toLowerCase().includes(handlerName.toLowerCase()) ||
                    item.EmployeeID.toLowerCase().includes(handlerName.toLowerCase())
                )"
                :key="item.EmployeeID"
                class="marbm_10 dis_flex flex_y_center"
              >
                <el-checkbox v-model="item.Checked" @change="handlerCheckedChange(handler.Employee, item)">
                  <span class="marrt_10">{{ item.EmployeeName }} [{{ item.EmployeeID }}]</span>
                </el-checkbox>
                <el-input
                  placeholder
                  v-model="item.Discount"
                  style="width: 120px"
                  type="number"
                  size="mini"
                  min="0"
                  max="100"
                  v-input-fixed
                  @input="handlerPercentChange(handler.Employee, item, 'dialog')"
                >
                  <template slot="append">%</template>
                </el-input>
              </el-col>
            </el-row>
          </el-tab-pane>
        </el-tabs>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false" size="small">取 消</el-button>
        <el-button type="primary" size="small" @click="submitHandleClick" v-prevent-click>确 定</el-button>
      </div>
    </el-dialog>
    <!--结账-->
    <el-dialog
      title="收银台"
      :visible.sync="dialogBill"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="false"
      width="900px"
      append-to-body
    >
      <div>
        <el-row>
          <el-col :span="8">
            <el-scrollbar class="el-scrollbar_height" style="height: 500px">
              <div class="marrt_10">
                <div class="dis_flex">
                  <span class="flex_box text_center font_16" style="line-height: 32px">{{ entityName }}</span>
                </div>
                <el-divider>
                  <span class="font_12 color_gray">订单信息</span>
                </el-divider>
                <div class="dis_flex">
                  <span class="flex_box font_12 color_gray text-left line_height_24">下单时间</span>
                  <span class="font_12 text_right line_height_24" style="flex: 3">{{ getBillDate() | dateFormat('YYYY-MM-DDHH: mm') }}</span>
                </div>
                <div class="dis_flex">
                  <span class="flex_box font_12 color_gray text-left line_height_24">会员姓名</span>
                  <span class="flex_box font_12 text_right line_height_24">{{ customerFullName }}</span>
                </div>
                <div class="dis_flex">
                  <span class="flex_box font_12 color_gray text-left line_height_24">会员手机号</span>
                  <span class="flex_box font_12 text_right line_height_24">{{ customerPhoneNumber | hidephone }}</span>
                </div>
                <div class="dis_flex">
                  <span class="flex_box font_12 color_gray text-left line_height_24">开单人</span>
                  <span class="flex_box font_12 text_right line_height_24">{{ userName }}</span>
                </div>
                <el-divider>
                  <span class="font_12 color_gray">消费明细</span>
                </el-divider>
                <template>
                  <div v-for="(item, index) in selectProject" :key="index + item.type + item.ID">
                    <div class="dis_flex">
                      <span class="font_12 color_gray text-left line_height_24" style="flex: 2">
                        {{ index + 1 }} {{ item.Name }}
                        <span class="font_12" size="mini" v-if="item.IsLargess">(赠)</span>
                      </span>
                      <span class="font_12 text_right line_height_24" style="flex: 1">￥{{ item.Price | toFixed | NumFormat }}</span>
                    </div>
                    <div class="dis_flex">
                      <span class="font_12 color_gray text-left line_height_24 marlt_10" style="flex: 2">数量</span>
                      <span class="font_12 text_right line_height_24" style="flex: 1">{{ item.number }}</span>
                    </div>
                    <div class="dis_flex" v-if="item.discountPrice != 0">
                      <span class="font_12 color_gray text-left line_height_24 marlt_10" style="flex: 2">手动改价</span>
                      <span class="font_12 text_right line_height_24" style="flex: 1" v-if="item.discountPrice > 0"
                        >-￥{{ item.discountPrice | toFixed | NumFormat }}</span
                      >
                      <span class="font_12 text_right line_height_24" style="flex: 1" v-else
                        >+￥{{ mathAbsData(item.discountPrice) | toFixed | NumFormat }}</span
                      >
                    </div>
                    <div class="dis_flex" v-if="item.CardDiscountPrice != 0">
                      <span class="font_12 color_gray text-left line_height_24 marlt_10" style="flex: 2">卡优惠</span>
                      <span class="font_12 text_right line_height_24" style="flex: 1">-￥{{ item.CardDiscountPrice | toFixed | NumFormat }}</span>
                    </div>
                    <div class="dis_flex">
                      <span class="font_12 color_gray text-left line_height_24 marlt_10" style="flex: 2">小计</span>
                      <span class="font_12 text_right line_height_24" style="flex: 1" v-if="item.IsLargess">￥0.00</span>
                      <span class="font_12 text_right line_height_24" style="flex: 1" v-else>￥{{ item.TotalAmount | toFixed | NumFormat }}</span>
                    </div>
                  </div>
                </template>
                <template>
                  <div v-for="(item, index) in selectSavingCard" :key="index + item.type + item.ID">
                    <div class="dis_flex">
                      <span class="font_12 color_gray text-left line_height_24" style="flex: 2">{{ index + 1 + selectProject.length }} {{ item.Name }}</span>
                      <span class="font_12 text_right line_height_24" style="flex: 1">￥{{ (item.Amount / item.number).toFixed(2) | NumFormat }}</span>
                    </div>
                    <div class="dis_flex" v-if="item.LargessPrice > 0">
                      <span class="font_12 color_gray text-left line_height_24 marlt_10" style="flex: 2">充值赠送</span>
                      <span class="font_12 text_right line_height_24" style="flex: 1">￥{{ (item.LargessPrice / item.number).toFixed(2) | NumFormat }}</span>
                    </div>
                    <div class="dis_flex">
                      <span class="font_12 color_gray text-left line_height_24 marlt_10" style="flex: 2">数量</span>
                      <span class="font_12 text_right line_height_24" style="flex: 1">{{ item.number }}</span>
                    </div>
                    <div class="dis_flex">
                      <span class="font_12 color_gray text-left line_height_24 marlt_10" style="flex: 2">小计</span>
                      <span class="font_12 text_right line_height_24" style="flex: 1">￥{{ item.Amount | toFixed | NumFormat }}</span>
                    </div>
                  </div>
                </template>
                <template>
                  <div v-for="(item, index) in selectTimeCard" :key="index + item.type + item.ID">
                    <div class="dis_flex">
                      <span class="font_12 color_gray text-left line_height_24" style="flex: 2">
                        {{ index + 1 + selectProject.length + selectSavingCard.length }}
                        {{ item.Name }}
                        <span class="font_12" size="mini" v-if="item.IsLargess">(赠)</span>
                      </span>
                      <span class="font_12 text_right line_height_24" style="flex: 1">￥{{ item.Price | toFixed | NumFormat }}</span>
                    </div>
                    <div class="dis_flex">
                      <span class="font_12 color_gray text-left line_height_24 marlt_10" style="flex: 2">数量</span>
                      <span class="font_12 text_right line_height_24" style="flex: 1">{{ item.number }}</span>
                    </div>
                    <div class="dis_flex" v-if="item.discountPrice != 0">
                      <span class="font_12 color_gray text-left line_height_24 marlt_10" style="flex: 2">手动改价</span>
                      <span class="font_12 text_right line_height_24" style="flex: 1" v-if="item.discountPrice > 0"
                        >-￥{{ item.discountPrice | toFixed | NumFormat }}</span
                      >
                      <span class="font_12 text_right line_height_24" style="flex: 1" v-else
                        >+￥{{ mathAbsData(item.discountPrice) | toFixed | NumFormat }}</span
                      >
                    </div>
                    <div class="dis_flex" v-if="item.CardDiscountPrice != 0">
                      <span class="font_12 color_gray text-left line_height_24 marlt_10" style="flex: 2">卡优惠</span>
                      <span class="font_12 text_right line_height_24" style="flex: 1">-￥{{ item.CardDiscountPrice | toFixed | NumFormat }}</span>
                    </div>
                    <div class="dis_flex">
                      <span class="font_12 color_gray text-left line_height_24 marlt_10" style="flex: 2">小计</span>
                      <span class="font_12 text_right line_height_24" style="flex: 1" v-if="item.IsLargess">￥0.00</span>
                      <span class="font_12 text_right line_height_24" style="flex: 1" v-else>￥{{ item.TotalAmount | toFixed | NumFormat }}</span>
                    </div>
                  </div>
                </template>
                <template>
                  <div v-for="(item, index) in selectGeneralCard" :key="index + item.type + '_' + item.ID">
                    <div class="dis_flex">
                      <span class="font_12 color_gray text-left line_height_24" style="flex: 2">
                        {{ index + 1 + selectProject.length + selectSavingCard.length + selectTimeCard.length }}
                        {{ item.Name }}
                        <span class="font_12" size="mini" v-if="item.IsLargess">(赠)</span>
                      </span>
                      <span class="font_12 text_right line_height_24" style="flex: 1">￥{{ item.Price | toFixed | NumFormat }}</span>
                    </div>
                    <div class="dis_flex">
                      <span class="font_12 color_gray text-left line_height_24 marlt_10" style="flex: 2">数量</span>
                      <span class="font_12 text_right line_height_24" style="flex: 1">{{ item.number }}</span>
                    </div>
                    <div class="dis_flex" v-if="item.discountPrice != 0">
                      <span class="font_12 color_gray text-left line_height_24 marlt_10" style="flex: 2">手动改价</span>
                      <span class="font_12 text_right line_height_24" style="flex: 1" v-if="item.discountPrice > 0"
                        >-￥{{ item.discountPrice | toFixed | NumFormat }}</span
                      >
                      <span class="font_12 text_right line_height_24" style="flex: 1" v-else
                        >+￥{{ mathAbsData(item.discountPrice) | toFixed | NumFormat }}</span
                      >
                    </div>
                    <div class="dis_flex" v-if="item.CardDiscountPrice != 0">
                      <span class="font_12 color_gray text-left line_height_24 marlt_10" style="flex: 2">卡优惠</span>
                      <span class="font_12 text_right line_height_24" style="flex: 1">-￥{{ item.CardDiscountPrice | toFixed | NumFormat }}</span>
                    </div>
                    <div class="dis_flex">
                      <span class="font_12 color_gray text-left line_height_24 marlt_10" style="flex: 2">小计</span>
                      <span class="font_12 text_right line_height_24" style="flex: 1" v-if="item.IsLargess">￥0.00</span>
                      <span class="font_12 text_right line_height_24" style="flex: 1" v-else>￥{{ item.TotalAmount | toFixed | NumFormat }}</span>
                    </div>
                  </div>
                </template>
                <template>
                  <div v-for="(item, index) in selectProduct" :key="index + item.type + item.ID">
                    <div class="dis_flex">
                      <span class="font_12 color_gray text-left line_height_24" style="flex: 2">
                        {{ index + 1 + selectProject.length + selectSavingCard.length + selectTimeCard.length + selectGeneralCard.length }}
                        {{ item.Name }}
                        <span class="font_12" size="mini" v-if="item.IsLargess">(赠)</span>
                      </span>
                      <span class="font_12 text_right line_height_24" style="flex: 1">￥{{ item.Price | toFixed | NumFormat }}</span>
                    </div>
                    <div class="dis_flex">
                      <span class="font_12 color_gray text-left line_height_24 marlt_10" style="flex: 2">数量</span>
                      <span class="font_12 text_right line_height_24" style="flex: 1">{{ item.number }}</span>
                    </div>
                    <div class="dis_flex" v-if="item.discountPrice != 0">
                      <span class="font_12 color_gray text-left line_height_24 marlt_10" style="flex: 2">手动改价</span>
                      <span class="font_12 text_right line_height_24" style="flex: 1" v-if="item.discountPrice > 0"
                        >-￥{{ item.discountPrice | toFixed | NumFormat }}</span
                      >
                      <span class="font_12 text_right line_height_24" style="flex: 1" v-else
                        >+￥{{ mathAbsData(item.discountPrice) | toFixed | NumFormat }}</span
                      >
                    </div>
                    <div class="dis_flex" v-if="item.CardDiscountPrice != 0">
                      <span class="font_12 color_gray text-left line_height_24 marlt_10" style="flex: 2">卡优惠</span>
                      <span class="font_12 text_right line_height_24" style="flex: 1">-￥{{ item.CardDiscountPrice | toFixed | NumFormat }}</span>
                    </div>
                    <div class="dis_flex">
                      <span class="font_12 color_gray text-left line_height_24 marlt_10" style="flex: 2">小计</span>
                      <span class="font_12 text_right line_height_24" style="flex: 1" v-if="item.IsLargess">￥0.00</span>
                      <span class="font_12 text_right line_height_24" style="flex: 1" v-else>￥{{ item.TotalAmount | toFixed | NumFormat }}</span>
                    </div>
                  </div>
                </template>
                <template>
                  <div v-for="(item, index) in selectPackageCard" :key="index + item.type + item.ID">
                    <div class="dis_flex">
                      <span class="font_12 color_gray text-left line_height_24" style="flex: 2">
                        {{ index + 1 + selectProject.length + selectSavingCard.length + selectTimeCard.length + selectGeneralCard.length }}
                        {{ item.Name }}
                        <span class="font_12" size="mini" v-if="item.IsLargess">(赠)</span>
                      </span>
                      <span class="font_12 text_right line_height_24" style="flex: 1">￥{{ item.Price | toFixed | NumFormat }}</span>
                    </div>
                    <div class="dis_flex">
                      <span class="font_12 color_gray text-left line_height_24 marlt_10" style="flex: 2">数量</span>
                      <span class="font_12 text_right line_height_24" style="flex: 1">{{ item.number }}</span>
                    </div>
                    <div class="dis_flex" v-if="item.discountPrice != 0">
                      <span class="font_12 color_gray text-left line_height_24 marlt_10" style="flex: 2">手动改价</span>
                      <span class="font_12 text_right line_height_24" style="flex: 1" v-if="item.discountPrice > 0"
                        >-￥{{ item.discountPrice | toFixed | NumFormat }}</span
                      >
                      <span class="font_12 text_right line_height_24" style="flex: 1" v-else
                        >+￥{{ mathAbsData(item.discountPrice) | toFixed | NumFormat }}</span
                      >
                    </div>
                    <div class="dis_flex" v-if="item.CardDiscountPrice != 0">
                      <span class="font_12 color_gray text-left line_height_24 marlt_10" style="flex: 2">卡优惠</span>
                      <span class="font_12 text_right line_height_24" style="flex: 1">-￥{{ item.CardDiscountPrice | toFixed | NumFormat }}</span>
                    </div>
                    <div class="dis_flex">
                      <span class="font_12 color_gray text-left line_height_24 marlt_10" style="flex: 2">小计</span>
                      <span class="font_12 text_right line_height_24" style="flex: 1" v-if="item.IsLargess">￥0.00</span>
                      <span class="font_12 text_right line_height_24" style="flex: 1" v-else>￥{{ item.TotalAmount | toFixed | NumFormat }}</span>
                    </div>
                  </div>
                </template>
                <el-divider class="sell-el-divider"></el-divider>
                <div class="dis_flex">
                  <span class="flex_box font_12 color_gray text-left line_height_24">合计</span>
                  <span class="flex_box font_12 text_right line_height_24"
                    >￥{{
                      (parseFloat(Amount) + parseFloat(PricePreferentialAmount) + parseFloat(CardPreferentialAmount)).toFixed(2) | toFixed | NumFormat
                    }}</span
                  >
                </div>
                <div class="dis_flex" v-if="PricePreferentialAmount != 0">
                  <span class="flex_box font_12 color_gray text-left line_height_24">手动改价</span>
                  <span class="flex_box font_12 text_right line_height_24" v-if="PricePreferentialAmount > 0"
                    >-￥{{ PricePreferentialAmount | toFixed | NumFormat }}</span
                  >
                  <span class="flex_box font_12 text_right line_height_24" v-else>+￥{{ mathAbsData(PricePreferentialAmount) | toFixed | NumFormat }}</span>
                </div>
                <div class="dis_flex" v-if="CardPreferentialAmount > 0">
                  <span class="flex_box font_12 color_gray text-left line_height_24">卡优惠</span>
                  <span class="flex_box font_12 text_right line_height_24">-￥{{ CardPreferentialAmount | toFixed | NumFormat }}</span>
                </div>
                <div class="dis_flex">
                  <span class="flex_box font_12 color_gray text-left line_height_24">订单金额</span>
                  <span class="flex_box font_12 text_right line_height_24">￥{{ Amount | toFixed | NumFormat }}</span>
                </div>
              </div>
            </el-scrollbar>
          </el-col>
          <el-col :span="16">
            <el-row type="flex" align="middle" class="dialog_bill_detail">
              <el-col :span="24">
                <div class="marbm_10">
                  <span class="font_20">待收款：</span>
                  <span class="font_20">¥{{ PayAmount | toFixed | NumFormat }}</span>
                </div>
                <div>
                  <span>订单金额：¥{{ Amount | toFixed | NumFormat }}</span>
                  <span v-if="ArrearAmount > 0" class="color_gray font_12 marlt_10">
                    欠款：
                    <span class="color_red">-¥{{ ArrearAmount | toFixed | NumFormat }}</span>
                  </span>
                  <span class="color_gray font_12 marlt_10" v-if="(parseFloat(CardDeductionAmount) + parseFloat(cardDeductionAmount)).toFixed(2) > 0">
                    <span>
                      卡抵扣：
                      <span class="color_red">-¥{{ (parseFloat(CardDeductionAmount) + parseFloat(cardDeductionAmount)).toFixed(2) | NumFormat }}</span>
                    </span>
                  </span>
                  <span v-if="PayCashAmount > 0" class="color_gray font_12 marlt_10">
                    付款：
                    <span class="color_red">-¥{{ PayCashAmount | toFixed | NumFormat }}</span>
                  </span>
                </div>
              </el-col>
            </el-row>
            <el-scrollbar class="el-scrollbar_height" style="height: 415px">
              <div class="tip" style="margin-top: 10px; margin-bottom: 0px" v-if="(savingCardAllGoods.length > 0) & (savingCardPrice > 0)">
                <i class="el-icon-warning-outline"></i>
                储值卡支付金额为￥{{ savingCardPrice | toFixed | NumFormat }}，且不能卡抵扣支付。
              </div>
              <el-table :data="savingCardAllGoods" class="saving_discount martp_10" v-if="savingCardAllGoods.length > 0" :show-header="false" size="small">
                <el-table-column type="selection" width="55">
                  <template slot-scope="scope">
                    <el-checkbox v-model="scope.row.checked" @change="savingCheckedAllChange(scope.row)"></el-checkbox>
                  </template>
                </el-table-column>
                <el-table-column label="商品信息">
                  <template slot-scope="scope">
                    <div>
                      {{ scope.row.SavingCardName }}
                      <el-tag v-if="scope.row.IsLargess" size="mini" type="danger" class="marlt_5">赠</el-tag>
                    </div>
                    <div>
                      <div>可用金额：¥ {{ scope.row.TotalPrice | toFixed | NumFormat }}</div>
                      <div class="font_12 color_999">
                        本金：¥ {{ scope.row.Balance | toFixed | NumFormat }} 赠额：¥
                        {{ scope.row.LargessBalance | toFixed | NumFormat }}
                      </div>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column label="卡扣金额">
                  <template slot-scope="scope">
                    <el-input
                      size="small"
                      v-model="scope.row.TotalAmount"
                      v-input-fixed="2"
                      placeholder="请输入抵扣金额"
                      @input="savingPriceAllChange(scope.row)"
                      :disabled="!scope.row.checked"
                    ></el-input>
                  </template>
                </el-table-column>
              </el-table>
              <el-table :data="payList" size="small" class="padtp_15" :show-header="false">
                <el-table-column prop="payName" label="选择收款方式">
                  <template slot-scope="scope">
                    <el-select
                      v-model="scope.row.PayMethodID"
                      placeholder="选择收款方式"
                      size="small"
                      clearable
                      filterable
                      @change="payMethodChange(scope.row)"
                    >
                      <el-option v-for="item in payTypeList" :key="item.ID" :label="item.Name" :value="item.ID"></el-option>
                    </el-select>
                  </template>
                </el-table-column>
                <el-table-column prop="price" label="支付金额">
                  <template slot-scope="scope">
                    <el-input
                      v-model="scope.row.Amount"
                      size="small"
                      v-input-fixed="2"
                      placeholder="请输入收款金额"
                      :disabled="scope.row.PayMethodID == ''"
                      @input="payPriceChange(scope.row)"
                    ></el-input>
                  </template>
                </el-table-column>
                <el-table-column prop="address" label="操作" width="100">
                  <template slot-scope="scope">
                    <el-button
                      type="danger"
                      icon="el-icon-close"
                      circle
                      size="mini"
                      @click="removePayClick(scope.$index)"
                      v-if="scope.$index + 1 != 1"
                    ></el-button>
                    <el-button type="primary" icon="el-icon-plus" circle size="mini" @click="addPayclick" v-if="scope.$index + 1 == payList.length"></el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-scrollbar>
          </el-col>
        </el-row>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="onCancelSubmitPay" size="small" :disabled="modalLoading">取 消</el-button>
        <el-button type="primary" @click="submitPayClick" :loading="modalLoading" v-prevent-click size="small">确定收款</el-button>
      </div>
    </el-dialog>

    <!--储值卡抵扣(部分商品)-->
    <el-dialog title="商品价格调整/储值卡抵扣" :visible.sync="dialogDeduction" width="700px" append-to-body>
      <el-row class="border pad_10 marbm_10 radius5" style="height: 65px">
        <el-col :span="12" class="line_height_23">
          {{ selectGood.Name }}
          <span v-if="selectGood.Alias">({{ selectGood.Alias }})</span>
        </el-col>
        <el-col :span="6" class="color_gray font_13 line_height_23">¥ {{ selectGood.Price | toFixed | NumFormat }} × {{ selectGood.number }}</el-col>
        <el-col :span="6" class="line_height_23">
          <div>
            ¥
            {{ (selectGood.Price * selectGood.number - selectGood.DeductionProjectAmount - selectGood.MemberPreferentialAmountTotal).toFixed(2) | NumFormat }}
          </div>
          <div class="color_gray font_12 line_height_23" v-show="selectGood.DeductionProjectAmount != 0 || selectGood.MemberPreferentialAmountTotal != 0">
            合计优惠：
            <span class="color_red" v-if="selectGood.DeductionProjectAmount > 0 || selectGood.MemberPreferentialAmountTotal > 0"
              >-{{ getSaveCardDiscountAmount(selectGood.DeductionProjectAmount, selectGood.MemberPreferentialAmountTotal) | toFixed | NumFormat }}</span
            >
            <span class="color_green" v-else>+{{ mathAbsData(selectGood.DeductionProjectAmount) | toFixed | NumFormat }}</span>
          </div>
        </el-col>
      </el-row>
      <el-row v-if="selectGood.isShowSelectMemberAmout" class="pad_10_0" type="flex" align="middle">
        <el-col :span="7">
          <el-select v-model="selectGood.isUseMemberDiscount" @change="changeSelectMemberDiscountAmout(selectGood)" placeholder="请选择" size="mini">
            <el-option label="不使用会员折扣" :value="false"></el-option>
            <el-option label="使用会员折扣" :value="true"></el-option>
          </el-select>
        </el-col>
        <el-col :span="12" :offset="1">
          <span class="color_gray font_12" v-if="selectGood.MemberPreferentialAmountTotal > 0 && selectGood.isUseMemberDiscount">
            会员优惠：
            <span class="color_red">
              <span v-if="selectGood.MemberPriceType == 1">折扣：（{{ (selectGood.MemberDiscountPrice * 10) | toFixed }}折）</span>
              <span v-if="selectGood.MemberPriceType == 2">折扣价：（{{ selectGood.MemberDiscountPrice }}）</span>
              <span>-{{ parseFloat(selectGood.MemberPreferentialAmountTotal) | toFixed | NumFormat }}</span>
            </span>
          </span>
        </el-col>
      </el-row>
      <template v-if="showModifyPrices && selectGood.IsModifyPrice && !selectGood.isUseMemberDiscount">
        <el-row class="border pad_10 marbm_10 martp_10 radius5" type="flex" align="middle" v-if="selectGood.isModify">
          <el-col :span="18">
            <span>手动改价:</span>
            <span class="mar_0_15">¥ {{ selectGood.Amount | toFixed | NumFormat }}</span>
            <el-button type="text" @click="selectGood.isModify = false" size="mini">改价</el-button>
          </el-col>
          <el-col :span="6" class="color_gray font_12" v-if="selectGood.discountPrice != 0">
            <span>手动改价：</span>
            <span class="color_red" v-if="selectGood.discountPrice > 0">-{{ selectGood.discountPrice | toFixed | NumFormat }}</span>
            <span class="color_green" v-else>+{{ mathAbsData(selectGood.discountPrice) | toFixed | NumFormat }}</span>
          </el-col>
        </el-row>
        <el-row class="border pad_10 marbm_10 martp_10 radius5" type="flex" align="middle" v-else>
          <el-col :span="4">
            <span>手动改价</span>
          </el-col>
          <el-col :span="14">
            <el-row type="flex" align="middle">
              <el-col :span="10">
                <el-input size="mini" v-model="selectGood.Amount" type="number" v-input-fixed @input="amountChange(selectGood)">
                  <template slot="prepend">¥</template>
                </el-input>
              </el-col>
              <el-col :span="2" class="text_center">
                <i class="el-icon-sort"></i>
              </el-col>
              <el-col :span="10">
                <el-input size="mini" v-model="selectGood.discount" type="number" v-input-fixed @input="discountChange(selectGood)">
                  <template slot="append">%</template>
                </el-input>
              </el-col>
            </el-row>
          </el-col>
          <el-col :span="6" class="text_right">
            <el-button type="primary" size="mini" @click="modifyChange(selectGood)">确认</el-button>
          </el-col>
        </el-row>
      </template>

      <el-table class="saving_discount radius5" :data="savingCardAll" size="small" v-if="savingCardAll.length > 0" :show-header="false" max-height="270px">
        <el-table-column type="selection" width="55">
          <template slot-scope="scope">
            <el-checkbox v-model="scope.row.checked" @change="savingCardCheckedChange(selectGood, scope.row)"></el-checkbox>
          </template>
        </el-table-column>
        <el-table-column label="储值卡">
          <template slot-scope="scope">
            <div class="font_14">
              {{ scope.row.SavingCardName }}
              <el-tag v-if="scope.row.IsLargess" size="mini" type="danger" class="marrt_5">赠</el-tag>
              <span class="color_gray font_12" v-if="scope.row.PriceType == 1">{{ scope.row.DiscountPrice | toFixed }}折</span>
              <span class="color_gray font_12" v-else>¥ {{ scope.row.DiscountPrice | toFixed | NumFormat }}</span>
            </div>
            <div class="font_12">可用金额：¥ {{ scope.row.TotalPrice | toFixed | NumFormat }}</div>
          </template>
        </el-table-column>
        <el-table-column label="卡扣金额">
          <template slot-scope="scope">
            <el-input
              :disabled="!scope.row.checked"
              size="small"
              type="number"
              v-model="scope.row.TotalAmount"
              v-input-fixed
              placeholder="请输入抵扣金额"
              @input="savingCardPriceChange(selectGood, scope.row)"
            ></el-input>
            <div v-if="scope.row.cardDiscountPrice > 0" class="color_red">卡优惠：-{{ scope.row.cardDiscountPrice | toFixed | NumFormat }}</div>
          </template>
        </el-table-column>
      </el-table>
      <div slot="footer" class="dialog-footer">
        <el-row type="flex" align="middle">
          <el-col :span="18" class="text_left font_14">
            <div>
              <span>
                <span>支付金额：</span>
                <span class="color_red">¥ {{ selectGood.PayAmount | toFixed | NumFormat }}</span>
              </span>
              <span class="font_12 color_gray" v-if="selectGood.CardDeductionAmount > 0">
                （
                <span>卡抵扣：</span>
                <span>-¥ {{ selectGood.CardDeductionAmount | toFixed | NumFormat }}</span
                >）
              </span>
            </div>
          </el-col>
          <el-col :span="6">
            <el-button @click="dialogDeduction = false" size="small">取消</el-button>
            <el-button type="primary" @click="submitSavingCard" size="small" v-prevent-click>确认</el-button>
          </el-col>
        </el-row>
      </div>
    </el-dialog>

    <!--储值卡抵扣(套餐卡)-->
    <el-dialog title="套餐价格调整/储值卡抵扣" :visible.sync="dialogDeductionPackage" width="700px" append-to-body>
      <template slot="title">
        <div>
          套餐价格调整/储值卡抵扣
          <span class="color_red font_13">（注：调整价格不能低于储值卡金额）</span>
        </div>
      </template>
      <el-row class="border pad_10 marbm_10 radius5" style="height: 65px">
        <el-col :span="12" class="line_height_23">
          <div>
            {{ selectGood.Name }}
            <span v-if="selectGood.Alias">({{ selectGood.Alias }})</span>
          </div>
          <div class="color_gray font_12" v-if="selectGood.Price - selectGood.DeductPrice > 0">
            储值卡金额：{{ ((selectGood.Price - selectGood.DeductPrice) * selectGood.number) | toFixed | NumFormat }}
          </div>
        </el-col>
        <el-col :span="6" class="color_gray font_13 line_height_23">¥ {{ selectGood.Price | toFixed | NumFormat }} × {{ selectGood.number }}</el-col>
        <el-col :span="6" class="line_height_23">
          <div>¥ {{ selectGood.Amount | toFixed | NumFormat }}</div>
          <div class="color_gray font_12 line_height_23" v-show="selectGood.DeductionProjectAmount != 0 || selectGood.MemberPreferentialAmountTotal != 0">
            合计优惠：
            <span class="color_red" v-if="selectGood.DeductionProjectAmount > 0 || selectGood.MemberPreferentialAmountTotal > 0"
              >-{{ getSaveCardDiscountAmount(selectGood.DeductionProjectAmount, selectGood.MemberPreferentialAmountTotal) | toFixed | NumFormat }}</span
            >
            <span class="color_green" v-else>+{{ mathAbsData(selectGood.DeductionProjectAmount) | toFixed | NumFormat }}</span>
          </div>
        </el-col>
      </el-row>

      <el-row v-if="selectGood.isShowSelectMemberAmout" class="margin_top_10 marbm_10" type="flex" align="middle">
        <el-col :span="7">
          <el-select v-model="selectGood.isUseMemberDiscount" @change="changePackageCardSelectMemberDiscountAmout(selectGood)" placeholder="请选择" size="mini">
            <el-option label="不使用会员折扣" :value="false"></el-option>
            <el-option label="使用会员折扣" :value="true"></el-option>
          </el-select>
        </el-col>
        <el-col :span="12" :offset="1">
          <span class="color_gray font_12" v-if="selectGood.MemberPreferentialAmountTotal > 0 && selectGood.isUseMemberDiscount">
            会员优惠：
            <span class="color_red">
              <span v-if="selectGood.PriceType == 1">折扣：（{{ (selectGood.DiscountPrice * 10) | toFixed }}折）</span>
              <span v-if="selectGood.PriceType == 2">折扣价：（{{ selectGood.DiscountPrice }}）</span>
              <span>-{{ parseFloat(selectGood.MemberPreferentialAmountTotal) | toFixed | NumFormat }}</span>
            </span>
          </span>
        </el-col>
      </el-row>
      <template v-if="showModifyPrices && selectGood.IsModifyPrice && !selectGood.isUseMemberDiscount">
        <el-row class="border pad_10 marbm_10 martp_10 radius5" align="middle" v-if="selectGood.isModify">
          <el-col :span="18">
            <span>手动改价:</span>
            <span class="mar_0_15">¥ {{ selectGood.modifyAmount | toFixed | NumFormat }}</span>
            <el-button type="text" @click="selectGood.isModify = false" size="mini">改价</el-button>
          </el-col>
          <el-col :span="6" class="color_gray font_12" v-if="selectGood.discountPrice != 0">
            <span>手动改价：</span>
            <span class="color_red" v-if="selectGood.discountPrice > 0">-{{ selectGood.discountPrice | toFixed | NumFormat }}</span>
            <span class="color_green" v-else>+{{ mathAbsData(selectGood.discountPrice) | toFixed | NumFormat }}</span>
          </el-col>

          <el-col class="font_12 color_gray martp_5" :span="24">
            <span>套餐卡金额：{{ (selectGood.Price * selectGood.number) | toFixed | NumFormat }}</span>
            <span class="marlt_10">储值卡金额：{{ ((selectGood.Price - selectGood.DeductPrice) * selectGood.number) | toFixed | NumFormat }}</span>
            <span class="marlt_10">可手动改价金额：{{ (selectGood.DeductPrice * selectGood.number) | toFixed | NumFormat }}</span>
          </el-col>
        </el-row>
        <el-row class="border pad_10 marbm_10 martp_10 radius5" align="middle" v-else>
          <el-col :span="4">
            <span>手动改价</span>
          </el-col>
          <el-col :span="14">
            <el-row type="flex" align="middle">
              <el-col :span="10">
                <el-input
                  size="mini"
                  v-model="selectGood.modifyAmount"
                  type="number"
                  v-input-fixed
                  @input="packageAmountChange(selectGood)"
                  @blur="packageModifyChange(selectGood)"
                >
                  <template slot="prepend">¥</template>
                </el-input>
              </el-col>
              <el-col :span="2" class="text_center">
                <i class="el-icon-sort"></i>
              </el-col>
              <el-col :span="10">
                <el-input
                  size="mini"
                  v-model="selectGood.discount"
                  type="number"
                  v-input-fixed
                  @input="discountChange(selectGood)"
                  @blur="packageModifyChange(selectGood)"
                >
                  <template slot="append">%</template>
                </el-input>
              </el-col>
            </el-row>
          </el-col>
          <el-col :span="6" class="text_right">
            <el-button type="primary" size="mini" @click="packageModifyChange(selectGood)">确认</el-button>
          </el-col>

          <el-col class="font_12 color_gray martp_5" :span="24">
            <span>套餐卡金额：{{ (selectGood.Price * selectGood.number) | toFixed | NumFormat }}</span>
            <span class="marlt_10">储值卡金额：{{ ((selectGood.Price - selectGood.DeductPrice) * selectGood.number) | toFixed | NumFormat }}</span>
            <span class="marlt_10">可手动改价金额：{{ (selectGood.DeductPrice * selectGood.number) | toFixed | NumFormat }}</span>
          </el-col>
        </el-row>
      </template>
      <el-table class="saving_discount radius5" :data="savingCardAll" size="small" v-if="savingCardAll.length > 0" :show-header="false" max-height="270px">
        <el-table-column type="selection" width="55">
          <template slot-scope="scope">
            <el-checkbox v-model="scope.row.checked" @change="packageSavingCardCheckedChange(selectGood, scope.row)"></el-checkbox>
          </template>
        </el-table-column>
        <el-table-column label="储值卡">
          <template slot-scope="scope">
            <div class="font_14">
              {{ scope.row.SavingCardName }}
              <el-tag v-if="scope.row.IsLargess" size="mini" type="danger" class="marrt_5">赠</el-tag>
              <span class="color_gray font_12" v-if="scope.row.PriceType == 1">{{ scope.row.DiscountPrice }}折</span>
              <span class="color_gray font_12" v-else>¥ {{ scope.row.DiscountPrice }}</span>
            </div>
            <div class="font_12">可用金额：¥ {{ scope.row.TotalPrice | toFixed | NumFormat }}</div>
          </template>
        </el-table-column>
        <el-table-column label="卡扣金额">
          <template slot-scope="scope">
            <el-input
              :disabled="!scope.row.checked"
              size="small"
              type="number"
              v-model="scope.row.TotalAmount"
              v-input-fixed
              placeholder="请输入抵扣金额"
              @input="packageSavingCardPriceChange(selectGood, scope.row)"
            ></el-input>
            <div v-if="scope.row.cardDiscountPrice > 0" class="color_red">卡优惠：-{{ scope.row.cardDiscountPrice }}</div>
          </template>
        </el-table-column>
      </el-table>
      <div slot="footer" class="dialog-footer">
        <el-row type="flex" align="middle">
          <el-col :span="18" class="text_left font_14">
            <div>
              <span>
                <span>支付金额：</span>
                <span class="color_red">¥ {{ selectGood.PayAmount | toFixed | NumFormat }}</span>
              </span>
              <span class="font_12 color_gray" v-if="selectGood.CardDeductionAmount > 0">
                （
                <span>卡抵扣：</span>
                <span>-¥ {{ selectGood.CardDeductionAmount | toFixed | NumFormat }}</span
                >）
              </span>
            </div>
          </el-col>
          <el-col :span="6">
            <el-button @click="dialogDeductionPackage = false" size="small">取消</el-button>
            <el-button type="primary" @click="submitSavingCardPackage" v-prevent-click size="small">确认</el-button>
          </el-col>
        </el-row>
      </div>
    </el-dialog>

    <!--套餐卡 明细改价-->
    <el-dialog title="套餐明细价格调整" :visible.sync="changePackageDetailItemDialog" width="700px" append-to-body>
      <div class="border marbm_10 radius5">
        <el-row class="pad_10" style="height: 40px; background-color: #fff7f3">
          <el-col :span="12" class="line_height_23">
            <span>{{ selectGood.Name }}</span>
            <span v-if="selectGood.Alias">({{ selectGood.Alias }})</span>
          </el-col>
          <el-col :span="6" class="color_gray font_13 line_height_23">¥ {{ selectGood.Price | toFixed | NumFormat }} × {{ selectGood.number }}</el-col>
          <el-col :span="6" class="line_height_23">
            <div>¥ {{ selectGood.Amount | toFixed | NumFormat }}</div>
          </el-col>
        </el-row>

        <el-row class="pad_10">
          <el-col :span="24">
            <el-col :span="12">
              <span>{{ selectPackageDetailItem.Name }} </span>
              <span v-if="selectPackageDetailItem.GeneralCardAmount" class="color_gray marlt_10">单卡：{{ selectPackageDetailItem.GeneralCardAmount }}次</span>
              <span class="marlt_10">× {{ selectPackageDetailItem.Amount }}</span>
              <el-tag class="marlt_5" size="mini">{{ selectPackageDetailItem.cardType }}</el-tag>
            </el-col>

            <el-col :span="6" class="color_gray font_13"
              >¥ {{ selectPackageDetailItem.TotalPrice | toFixed | NumFormat }} × {{ selectPackageDetailItem.number }}</el-col
            >
            <el-col :span="6">
              <div>¥ {{ selectPackageDetailItem.totalAmount | toFixed | NumFormat }}</div>
              <div
                class="color_gray font_12 line_height_23"
                v-show="selectPackageDetailItem.DeductionProjectAmount != 0 || selectPackageDetailItem.MemberPreferentialAmountTotal != 0"
              >
                合计优惠：
                <span class="color_red" v-if="selectPackageDetailItem.DeductionProjectAmount > 0 || selectPackageDetailItem.MemberPreferentialAmountTotal > 0"
                  >-{{
                    getSaveCardDiscountAmount(selectPackageDetailItem.DeductionProjectAmount, selectPackageDetailItem.MemberPreferentialAmountTotal)
                      | toFixed
                      | NumFormat
                  }}</span
                >
                <span class="color_green" v-else>+{{ mathAbsData(selectPackageDetailItem.DeductionProjectAmount) | toFixed | NumFormat }}</span>
              </div>
            </el-col>
          </el-col>
        </el-row>
      </div>
      <el-row
        v-if="showModifyPrices && selectGood.IsModifyPrice && !selectGood.isUseMemberDiscount"
        class="border pad_10 marbm_10 martp_10 radius5"
        type="flex"
        align="middle"
      >
        <el-col :span="4">
          <span>手动改价</span>
        </el-col>
        <el-col :span="14">
          <el-row type="flex" align="middle">
            <el-col :span="10">
              <el-input size="mini" v-model="selectPackageDetailItem.totalAmount" type="number" v-input-fixed @input="packageDetailAmountChange()">
                <template slot="prepend">¥</template>
              </el-input>
            </el-col>
            <el-col :span="2" class="text_center">
              <i class="el-icon-sort"></i>
            </el-col>
            <el-col :span="10">
              <el-input size="mini" v-model="selectPackageDetailItem.discount" type="number" v-input-fixed @input="packageDetailDiscountChange()">
                <template slot="append">%</template>
              </el-input>
            </el-col>
          </el-row>
        </el-col>
      </el-row>

      <div slot="footer" class="dialog-footer">
        <el-row type="flex" align="middle">
          <el-col :span="18" class="text_left font_14">
            <div>
              <span>
                <span>支付金额：</span>
                <span class="color_red">¥ {{ selectGood.PayAmount | toFixed | NumFormat }}</span>
              </span>
            </div>
          </el-col>
          <el-col :span="6">
            <el-button @click="changePackageDetailItemDialog = false" size="small">取消</el-button>
            <el-button type="primary" @click="submitPackageCardChanePrice" size="small" v-prevent-click>确认</el-button>
          </el-col>
        </el-row>
      </div>
    </el-dialog>

    <!--结账成功-->
    <el-dialog :visible.sync="dialogPay" width="450px" @close="continueSaleBillClick" append-to-body>
      <div class="text_center pad_15">
        <i class="el-icon-document" style="font-size: 80px; color: #999"></i>
        <div class="pad_15 color_primary font_weight_600 font_18">订单已结账成功</div>
      </div>
      <el-row class="pad_15 border_bottom">
        <el-col :span="12">销售订单号：</el-col>
        <el-col :span="12" class="text_right">{{ orderNumber }}</el-col>
      </el-row>
      <el-row class="pad_15 border_bottom">
        <el-col :span="12">订单金额：</el-col>
        <el-col :span="12" class="color_red text_right">¥{{ orderAmount }}</el-col>
      </el-row>
      <el-row class="pad_15 border_bottom">
        <el-col :span="5">订单备注：</el-col>
        <el-col :span="19">{{ Remark }}</el-col>
      </el-row>

      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="continueSaleBillClick" size="small">继续开单</el-button>
        <el-button type="primary" @click="printNoteOfSmallDenomination" :loading="printLoading" v-prevent-click size="small">打印小票</el-button>
        <el-button type="primary" @click="confrimPrintClick" v-prevent-click size="small">打印单据</el-button>
      </div>
    </el-dialog>

    <el-dialog :visible.sync="projectRemarkDialogVisible" title="备注信息" width="500px" append-to-body>
      <el-input type="textarea" :autosize="{ minRows: 2, maxRows: 4 }" maxlength="100" show-word-limit placeholder="请输入备注信息" v-model="projectRemark">
      </el-input>

      <div slot="footer" class="dialog-footer">
        <el-button @click="projectRemarkDialogVisible = false" size="small" :disabled="RemarkLoading">取 消</el-button>
        <el-button type="primary" @click="saveRemarkClick" :loading="RemarkLoading" v-prevent-click size="small">保 存</el-button>
      </div>
    </el-dialog>

    <el-dialog :visible.sync="wholeOrderChangeAmountVisible" title="单据修改价格" width="700px" append-to-body>
      <el-row class="border pad_10 marbm_10 radius5" style="height: 55px" type="flex" align="middle">
        <el-col :span="8">待收款金额：¥ {{ PayAmount | toFixed | NumFormat }}</el-col>
        <el-col :span="8" class="font_14 color_maroon">可修改金额：¥ {{ originalAllowedAmount | toFixed | NumFormat }}</el-col>
        <!-- <el-col :span="8">不可修改金额：</el-col> -->
      </el-row>

      <el-row class="border pad_10 marbm_10 radius5" type="flex" align="middle">
        <el-col :span="4">
          <span>手动改价</span>
        </el-col>
        <el-col :span="20">
          <el-row type="flex" align="middle">
            <el-col :span="10">
              <el-input size="mini" v-model="allowedAmount" type="number" v-input-fixed @input="wholeOrderAmountChangeInput">
                <template slot="prepend">¥</template>
              </el-input>
            </el-col>
            <el-col :span="2" class="text_center">
              <i class="el-icon-sort"></i>
            </el-col>
            <el-col :span="10">
              <el-input size="mini" v-model="allowedAmountDiscount" type="number" v-input-fixed @input="wholeOrderdiscountChangeInput">
                <template slot="append">%</template>
              </el-input>
            </el-col>
          </el-row>
        </el-col>
      </el-row>

      <div slot="footer" class="dialog-footer">
        <el-button @click="wholeOrderChangeAmountVisible = false" size="small" :disabled="RemarkLoading">取 消</el-button>
        <el-button type="primary" @click="confirmWholeOrderChangeAmountClick" v-prevent-click size="small">保 存</el-button>
      </div>
    </el-dialog>

    <!--经手人-->
    <el-dialog title="经手人" :visible.sync="dialogVisibleAllHandler" width="800px" append-to-body>
      <div>
        <el-row class="padbm_10">
          <el-col :span="8">
            <el-input placeholder="请输入员工编号、姓名" prefix-icon="el-icon-search" v-model="handlerAllName" size="small" clearable></el-input>
          </el-col>
        </el-row>
        <el-tabs v-model="tabAllHandle">
          <el-tab-pane :label="handler.Name" :name="`${index}`" v-for="(handler, index) in getAllHandlerList()" :key="index">
            <el-row style="max-height: 300px; overflow-y: auto">
              <el-col
                :span="12"
                v-for="item in handler.Employee.filter(
                  (item) =>
                    !handlerAllName ||
                    item.EmployeeName.toLowerCase().includes(handlerAllName.toLowerCase()) ||
                    item.EmployeeID.toLowerCase().includes(handlerAllName.toLowerCase())
                )"
                :key="item.EmployeeID"
                class="marbm_10 dis_flex flex_y_center"
              >
                <el-checkbox v-model="item.Checked" @change="handlerCheckedChange(handler.Employee, item)">
                  <span class="marrt_10">{{ item.EmployeeName }} [{{ item.EmployeeID }}]</span>
                </el-checkbox>
                <el-input
                  placeholder
                  v-model="item.Discount"
                  style="width: 120px"
                  type="number"
                  size="mini"
                  v-input-fixed
                  min="0"
                  max="100"
                  @input="handlerPercentChange(handler.Employee, item, 'dialog')"
                >
                  <template slot="append">%</template>
                </el-input>
              </el-col>
            </el-row>
          </el-tab-pane>
        </el-tabs>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisibleAllHandler = false" size="small">取 消</el-button>
        <el-button type="primary" size="small" @click="confirmAllHandlerClick" v-prevent-click>确 定</el-button>
      </div>
    </el-dialog>

    <!-- 整单修改价格 -->
    <el-dialog :visible.sync="orderChangeAmountVisible" v-if="orderChangeAmountVisible" title="修改金额" width="700px" append-to-body @close="closeOrderChangeAmountDialog">
      <el-row class="border pad_10 marbm_10 radius5" align="middle">
        <el-col :span="4">
          <span>手动改价</span>
        </el-col>
        <el-col :span="20">
          <el-row type="flex" align="middle">
            <el-col :span="11">
              <el-input size="mini" v-model="changOrderAmount" type="number" v-input-fixed @input="orderAmountChangeClick">
                <template slot="prepend">¥</template>
              </el-input>
            </el-col>
            <el-col :span="2" class="text_center">
              <i class="el-icon-sort"></i>
            </el-col>
            <el-col :span="11">
              <el-input size="mini" v-model="changOrderDiscount" type="number" v-input-fixed @input="orderDiscountChange">
                <template slot="append">%</template>
              </el-input>
            </el-col>
          </el-row>
        </el-col>
        <el-col :span="24" class="martp_10 font_12 color_999">
          <span>订单金额：</span>
          <span class="color_red">¥ {{ Amount | toFixed | NumFormat }}</span>

          <span class="marlt_15">储值卡金额：</span>
          <span class="color_red">¥ {{ savingPayAmount | toFixed | NumFormat }}</span>

          <span class="marlt_15">储值卡抵扣金额：</span>
          <span class="color_red">¥ {{ (parseFloat(CardDeductionAmount) + parseFloat(CardPreferentialAmount)) | toFixed | NumFormat }}</span>

          <span class="marlt_15">可修改金额：</span>
          <span class="color_red">¥ {{ modifyPayAmount | toFixed | NumFormat }}</span>
        </el-col>
      </el-row>
      <div slot="footer" class="dialog-footer">
        <el-row type="flex" align="middle">
          <el-col :span="18" class="text_left font_14">
            <div>
              <span>待收款金额：</span>
              <span class="color_red">¥ {{ (Number(changOrderAmount) + Number(savingPayAmount)) | toFixed | NumFormat }}</span>
            </div>
          </el-col>
          <el-col :span="6">
            <el-button @click="orderChangeAmountVisibleClick" size="small">取消</el-button>
            <el-button type="primary" @click="confirmOrderChangeAmountClick" size="small" v-prevent-click>确认</el-button>
          </el-col>
        </el-row>
      </div>
    </el-dialog>

    <el-button v-show="false" ref="printButton" v-print="'sellPrintContent'">打印</el-button>
    <!-- 打印 -->
    <el-dialog title="选择打印模板" :visible.sync="printTemplateVisible" width="400px" append-to-body>
      <el-select size="small" v-model="printTemplateID">
        <el-option v-for="item in templateTypeList" :key="item.ID" :label="item.Name" :value="item.ID"></el-option>
      </el-select>
      <div slot="footer">
        <el-button @click="printTemplateVisible = false" size="small" v-prevent-click>取消</el-button>
        <el-button type="primary" @click="confirmSelectPrintTemplate" size="small" v-prevent-click>打印 </el-button>
      </div>
    </el-dialog>
    <div style="display: none">
      <div id="sellPrintContent">
        <component :is="printComponentName"></component>
      </div>
    </div>

    <saleCashierReceipt
      v-if="cashierReceiptDialogVisible"
      :visible.sync="cashierReceiptDialogVisible"
      :saleOrderDetail="saleOrderDetail"
      :entityName="entityName"
      :cashierReceipt="cashierReceipt"
    ></saleCashierReceipt>
  </div>
</template>

<script>
import API from '@/api/iBeauty/Order/saleGoods';
import draftAPI from '@/api/iBeauty/Order/draftOrder';
import date from '@/components/js/date';
import cashierAPI from '@/api/iBeauty/Order/cashierReceipt';
import orderAPI from '@/api/iBeauty/Order/saleBill';
// import printReceipt from "@/components/js/print";
import printComponent from '@/views/iBeauty/Order/components/zl-print.js';
import print from 'vue-print-nb';

var Enumerable = require('linq');

const customerDiscountAPIMap = {
  project: 'saleGoods_projectCustomerDiscount',
  product: 'saleGoods_productCustomerDiscount',
  timeCard: 'saleGoods_timeCardCustomerDiscount',
  generalCard: 'saleGoods_generalCardCustomerDiscount',
  packageCard: 'saleGoods_packageCardCustomerDiscount',
};
const saleGoodsTypeMap = {
  project: 'ProjectID',
  product: 'ProductID',
  timeCard: 'TimeCardID',
  generalCard: 'GeneralCardID',
  packageCard: 'PackageCardID',
};
export default {
  directives: {
    print,
  },
  components: {
    saleCashierReceipt: () => import('@/components/iBeauty/Order/cashierReceipt/saleCashierReceipt.vue'),
  },
  props: {
    billDate: String,
    isSupplement: Boolean,
    customerID: [Number, String],
    customerFullName: String,
    customerPhoneNumber: String,
    SellPermission: Object,
  },
  data() {
    return {
      templateTypeList: [],
      printComponentName: '',
      printTemplateVisible: false,
      printTemplateID: null,
      CardDiscountPrice:"",

      modifyPayAmount: 0,
      totalLength: 0,
      changOrderDiscount: 100,
      changOrderAmount: 0,
      savingPayAmount: 0,
      packageSavingPayAmount: 0,
      orderChangeAmountVisible: false,
      isLockOrderAmount: false,

      dialogVisibleAllHandler: false,
      showRemark: false,
      noLargessIndex: 0,
      selectPackageIndex: 0,
      selectPackageDetailItem: '', //将要修改价格的套餐卡明细品项
      changePackageDetailItemDialog: false,
      RemarkLoading: false,
      projectRemarkDialogVisible: false,
      showModifyPrices: false,
      cashierReceiptDialogVisible: false, // 小票打印
      loading: false,
      printLoading: false,
      modalLoading: false,
      dialogVisible: false,
      dialogBill: false,
      dialogDeduction: false,
      dialogDeductionPackage: false,
      dialogPay: false,
      goodsName: '',
      typeIndex: '0',
      categorySubIndex: '0',
      tabPane: '1',
      orderNumber: '',
      orderAmount: 0,
      BillID: '',
      Amount: 0, //订单金额
      PayAmount: 0, //待支付金额（待收款）
      PayCashAmount: 0, //现金支付金额
      payTotalPrice: 0,
      ArrearAmount: 0, //欠款金额
      cardDeductionAmount: 0,
      CardDeductionAmount: 0, // 储值卡抵扣金额
      PricePreferentialAmount: 0, //手动改价优惠金额
      CardPreferentialAmount: 0, //卡优惠金额
      savingCardPrice: 0, //储值卡金额
      Remark: '', //备注
      type: 1,
      handlerName: '',
      collapseName: [],
      goodsAll: [],
      goodsIndex: '',

      /**  全部商品  */
      allLoading: false,
      allCategory: [], //项目
      allCategoryIndex: 0,
      allCategoryItem: null,
      goodsList: [], //全部商品
      allPaginations: {
        page: 1, // 当前位于哪页
        total: 0, // 总数
        page_size: 10, // 1页显示多少条
        layout: 'total, prev, pager, next', // 翻页属性
      },

      /**  项目  */
      projectLoading: false,
      projectCategory: [], //项目
      projectCategoryIndex: 0,
      projectSecondCategory: [], //项目二级分类
      projectSecondCategoryIndex: 0,
      projectSecondItem: null,
      projectList: [], //项目
      projectPaginations: {
        page: 1, // 当前位于哪页
        total: 0, // 总数
        page_size: 10, // 1页显示多少条
        layout: 'total, prev, pager, next', // 翻页属性
      },
      /**  产品  */
      productLoading: false,
      productCategory: [], //产品
      productCategoryIndex: 0,
      productSecondCategory: [], //项目二级分类
      productSecondCategoryIndex: 0,
      productSecondItem: null,
      productList: [], //产品
      productPaginations: {
        page: 1, // 当前位于哪页
        total: 0, // 总数
        page_size: 10, // 1页显示多少条
        layout: 'total, prev, pager, next', // 翻页属性
      },
      /**  储值卡  */
      savingLoading: false,
      savingCardCategory: [], //储值卡
      savingCategoryIndex: 0,
      savingCardCategoryItem: null,
      savingCardList: [], //储值卡
      savingPaginations: {
        page: 1, // 当前位于哪页
        total: 0, // 总数
        page_size: 10, // 1页显示多少条
        layout: 'total, prev, pager, next', // 翻页属性
      },
      /**  时效卡  */
      timeCardLoading: false,
      timeCardCategory: [], //时效卡
      timeCategoryIndex: 0,
      timeCategoryItem: null,
      timeCardList: [], //时效卡
      timeCardPaginations: {
        page: 1, // 当前位于哪页
        total: 0, // 总数
        page_size: 10, // 1页显示多少条
        layout: 'total, prev, pager, next', // 翻页属性
      },
      /**通用次卡  */
      generalCarLoading: false,
      generalCardCategory: [], //通用次卡
      generalCategoryIndex: 0,
      generalCategoryItem: null,
      generalCardList: [], //通用次卡
      generalCarPaginations: {
        page: 1, // 当前位于哪页
        total: 0, // 总数
        page_size: 10, // 1页显示多少条
        layout: 'total, prev, pager, next', // 翻页属性
      },
      /**套餐卡  */
      packageCardLoading: false,
      packageCardCategory: [], //套餐卡
      packageCategoryIndex: 0,
      packageCategoryItem: null,
      packageCardList: [], //套餐卡
      packageCardPaginations: {
        page: 1, // 当前位于哪页
        total: 0, // 总数
        page_size: 10, // 1页显示多少条
        layout: 'total, prev, pager, next', // 翻页属性
      },

      selectProject: [],
      selectProduct: [],
      selectGeneralCard: [],
      selectTimeCard: [],
      selectSavingCard: [],
      selectPackageCard: [],
      selectGoods: '',
      selectGood: '',
      handlerList: [], //公共经手人列表
      productHandlerList: [], //产品经手人列表
      projectHandlerList: [], //项目经手人列表
      savingCardHandlerList: [], //储值卡经手人列表
      timeCardHandlerList: [], //时效卡经手人列表
      generalCardHandlerList: [], //通用次卡经手人列表
      packageCardHandlerList: [], //套餐卡经手人列表
      tabHandle: '0',
      savingCardAllGoods: [], // 通用储值卡
      savingCardSomeGoods: [], // 非通用储值卡
      savingCardAll: [],
      payTypeList: [],
      payList: [{ PayMethodID: '', Amount: '', price: 0 }],
      SavingCardDeduction: [],
      name: '',
      IsExistProduct: false,
      IsExistProject: false,
      IsExistGeneralCard: false,
      IsExistSavingCard: false,
      IsExistTimeCard: false,
      IsExistPackageCard: false,
      userName: '',
      entityName: '',
      saleOrderDetail: null,
      cashierReceipt: {
        NameEncrypt: '',
      }, // 小票配置信息
      projectRemark: '',
      currentRemarkItem: '',
      saleTakeOrderData: '',

      /* 订单修改价格 */
      wholeOrderChangeAmountVisible: false,
      originalAllowedAmount: 0,
      allowedAmount: 0,
      allowedAmountDiscount: 100,
      allowedAmountItem: [],
      notAllowedAmount: 0,
      notAllowedAmountItem: [],
      tabAllHandlePosition: 'saleHandler',
      tabAllHandle: '0',
      saleAllHandlerList: [],
      treatAllHandlerList: [],
      handlerAllName: '',

      goodsType: 'project',
      employeeDiscount: '',
      memberPreferentialAmount: 0,
      customerDiscount: '',
      originalTotalAmount: 0, // 原价 订单总金额
    };
  },

  methods: {
    closeOrderChangeAmountDialog(){
      let that = this;
      that.payAmountData();
    },
        /**    */
        orderChangeAmountVisibleClick(){
      let that = this;
      that.orderChangeAmountVisible = false;
      that.payAmountData();
    },
    getSaveCardDiscountAmount(a, b) {
      return parseFloat(a || 0) + parseFloat(b || 0);
    },
    // 套餐卡选择会员优惠金额
    changePackageCardSelectMemberDiscountAmout(row) {
      console.log('🚀 ~ changePackageCardSelectMemberDiscountAmout ~ row:', row);
      let that = this;
      that.savingCardAll.forEach(function (item) {
        item.checked = false;
        item.TotalAmount = '';
        that.savingCardCheckedChange(that.selectGood, item);
      });
      let savingCardTotalAmount = (row.Price - row.DeductPrice) * row.number;
      if (row.isUseMemberDiscount) {
        let Amount = row.Price * row.number;
        if (row.MemberPriceType == 1) {
          Amount = parseFloat(row.DeductPrice * row.number) * parseFloat(row.MemberDiscountPrice) + savingCardTotalAmount;
        }
        if (row.MemberPriceType == 2) {
          Amount = row.number * row.MemberDiscountPrice;
        }
        row.Amount = Amount;
        row.PayAmount = Amount;
        row.TotalAmount = Amount;
        row.totalPrice = Amount;
        row.discountPrice = 0;
        row.discount = 100;
        row.MemberPreferentialAmountTotal = parseFloat(row.MemberPreferentialAmount * row.number).toFixed(2);
        row.noLargess.forEach(function (i) {
          i.number = row.number;
          let memberDiscount = { DiscountPrice: i.MemberDiscountPrice, PriceType: i.MemberPriceType };
          let child_amonunt = that.getPackageDetailMemberAmount(i.TotalPrice, memberDiscount);
          if (i.cardType != '储值卡') {
            i.totalAmount = child_amonunt * i.number;
            i.TotalAmount = child_amonunt * i.number;
            i.PayAmount = child_amonunt * i.number;
            i.MemberPreferentialAmountTotal = i.MemberPreferentialAmount * i.number;
          }
        });
      } else {
        let Amount = row.Price * row.number;
        row.Amount = Amount;
        row.PayAmount = Amount;
        row.TotalAmount = Amount;
        row.totalPrice = Amount;
        row.discountPrice = 0;
        row.discount = 100;
        row.MemberPreferentialAmountTotal = 0;

        row.noLargess.forEach(function (i) {
          i.number = row.number;
          let child_amonunt = i.TotalPrice;
          if (i.cardType != '储值卡') {
            i.totalAmount = child_amonunt * i.number;
            i.TotalAmount = child_amonunt * i.number;
            i.PayAmount = child_amonunt * i.number;
            i.MemberPreferentialAmountTotal = 0;
          }
        });
      }
    },
    /**  修改是否使用会员折扣  */
    changeSelectMemberDiscountAmout(row) {
      let that = this;
      that.savingCardAll.forEach(function (item) {
        item.checked = false;
        item.TotalAmount = '';
        that.savingCardCheckedChange(that.selectGood, item);
      });
      if (row.isUseMemberDiscount) {
        let Amount = row.Price;
        if (row.MemberPriceType == 1) {
          Amount = row.Price * row.MemberDiscountPrice;
        }
        if (row.MemberPriceType == 2) {
          Amount = row.MemberDiscountPrice;
        }

        row.Amount = Amount * row.number;
        row.PayAmount = Amount * row.number;
        row.TotalAmount = Amount * row.number;
        row.totalPrice = Amount * row.number;
        row.discountPrice = 0;
        row.discount = 100;
        row.MemberPreferentialAmountTotal = row.MemberPreferentialAmount * row.number;
      } else {
        let Amount = row.Price * row.number;
        row.Amount = Amount;
        row.PayAmount = Amount;
        row.TotalAmount = Amount;
        row.totalPrice = Amount;
        row.discountPrice = 0;
        row.discount = 100;
        row.MemberPreferentialAmountTotal = 0;
        row.DeductionProjectAmount = 0;
      }
    },
    /**  切换  */
    handleClick() {
      const goodsType = {
        1: 'project',
        3: 'timeCard',
        4: 'generalCard',
        5: 'packageCard',
        6: 'product',
      };
      this.goodsType = goodsType[this.tabPane];
    },
    /**  确定打印   */
    confirmSelectPrintTemplate() {
      let that = this;
      that.printTemplateVisible = false;
      that.$nextTick(() => {
        let temp = that.templateTypeList.find((i) => {
          return i.ID == that.printTemplateID;
        });
        that.printComponentName = printComponent.getPrintComponent(that.saleOrderDetail, temp.Template);
        let buttonElement = that.$refs.printButton.$el;
        let clickEvent = new MouseEvent('click');
        buttonElement.dispatchEvent(clickEvent);
      });
    },
    /**  确认打印  */
    confrimPrintClick() {
      let that = this;
      if (that.templateTypeList.length == 1) {
        let temp = that.templateTypeList[0].Template;
        that.$nextTick(() => {
          that.printComponentName = printComponent.getPrintComponent(that.saleOrderDetail, temp);
          let buttonElement = that.$refs.printButton.$el;
          let clickEvent = new MouseEvent('click');
          buttonElement.dispatchEvent(clickEvent);
        });
      } else {
        if (!that.templateTypeList || !that.templateTypeList.length) {
          that.$message.error('暂无打印模板，请添加打印模板');
          return;
        }
        that.printTemplateID = that.templateTypeList[0].ID;
        that.printTemplateVisible = true;
      }
    },
    // 打印小票
    printNoteOfSmallDenomination() {
      let that = this;
      that.getReceiptConfig();
      that.cashierReceiptDialogVisible = true;
    },
    /*  */
    onCancelSubmitPay() {
      let that = this;
      that.dialogBill = false;
      that.savingCardAllGoods.forEach((i) => {
        i.checked = false;
      });
      that.savingDeductionPriceAll();
      that.payAmountData();
    },
    /**  修改折扣  */
    orderDiscountChange() {
      let that = this;
      that.changOrderAmount = ((Number(that.changOrderDiscount) * (Number(that.originalTotalAmount || 0) - Number(that.CardDeductionAmount || 0) - Number(that.CardDiscountPrice || 0) - Number(that.ArrearAmount || 0)) ) / 100).toFixed(2);
    },
    /**  修改金额  */
    orderAmountChangeClick() {
      let that = this;
      that.changOrderDiscount = ((that.changOrderAmount /  (Number(that.originalTotalAmount || 0) - Number(that.CardDeductionAmount || 0) - Number(that.CardDiscountPrice || 0) - Number(that.ArrearAmount || 0)) ) * 100).toFixed(2);
    },
    /* 确定修改价格 */
    confirmOrderChangeAmountClick() {
      let that = this;
      if (that.employeeDiscount * 100 > that.changOrderDiscount) {
        that.$message.error('改价金额不能小于员工最低改价金额');
        that.changOrderAmount = parseFloat(that.modifyPayAmount * that.employeeDiscount).toFixed(2);
        that.changOrderDiscount = parseFloat(that.employeeDiscount * 100).toFixed(2);
        return;
      }
      let currentLength = 1;
      let totalAllocated = 0;// 已分配金额
      let changOrderAmount = that.changOrderAmount; // 待分配总金额

      that.selectProject.forEach((i) => {
        if (!i.IsLargess && i.IsModifyPrice) {
          let PayAmount = 0;
          if (currentLength == that.totalLength) {
            PayAmount = (Number(changOrderAmount) - Number(totalAllocated)).toFixed(2);
          }else{
            PayAmount = ((Number(i.Price) * Number(i.number) - Number(i.CardDeductionAmount) - Number(i.CardDiscountPrice)  - Number(i.ArrearAmount)) *
            Number(changOrderAmount) / 
             (Number(that.originalTotalAmount) - Number(that.CardDeductionAmount) - Number(that.CardDiscountPrice) - Number(that.ArrearAmount)) ).toFixed(2);  
          }
          totalAllocated += Number(PayAmount);
          currentLength += 1;
          i.PayAmount = Number(PayAmount).toFixed(2);
          i.Amount = Number(i.PayAmount) + Number(i.CardDeductionAmount) + Number(i.CardDiscountPrice) + Number(i.ArrearAmount);
          i.TotalAmount = Number(i.PayAmount) + Number(i.CardDeductionAmount)  + Number(i.ArrearAmount);
          i.discount = that.changOrderDiscount;
          i.DeductionProjectAmount = (Number(i.Price) * Number(i.number) -
            Number(i.CardDeductionAmount) - Number(i.CardDiscountPrice) - Number(i.PayAmount) - Number(i.ArrearAmount)).toFixed(2);
            
          i.discountPrice = ((Number(i.Price) * Number(i.number) -
            Number(i.CardDeductionAmount) - Number(i.CardDiscountPrice) - Number(PayAmount) - Number(i.ArrearAmount)).toFixed(2));
            
          i.MemberPreferentialAmountTotal = 0;
        }
      });
      that.selectTimeCard.forEach((i) => {
        if (!i.IsLargess && i.IsModifyPrice) {
          let PayAmount = 0;
          if (currentLength == that.totalLength) {
            PayAmount = (Number(changOrderAmount) - Number(totalAllocated)).toFixed(2);
          }else{
            PayAmount = ((Number(i.Price) * Number(i.number) - Number(i.CardDeductionAmount) - Number(i.CardDiscountPrice)  - Number(i.ArrearAmount)) *
            Number(changOrderAmount) / 
             (Number(that.originalTotalAmount) - Number(that.CardDeductionAmount) - Number(that.CardDiscountPrice) - Number(that.ArrearAmount)) ).toFixed(2);  
          }
          totalAllocated += Number(PayAmount);
          currentLength += 1;
          
          i.PayAmount =  Number(PayAmount).toFixed(2);
          i.Amount = Number(i.PayAmount) + Number(i.CardDeductionAmount) + Number(i.CardDiscountPrice) + Number(i.ArrearAmount);
          i.TotalAmount = parseFloat(i.PayAmount) + parseFloat(i.CardDeductionAmount)   + Number(i.ArrearAmount);
          i.discount = that.changOrderDiscount;
          
          i.DeductionProjectAmount = (Number(i.Price) * Number(i.number) -
            Number(i.CardDeductionAmount) - Number(i.CardDiscountPrice) - Number(i.PayAmount) - Number(i.ArrearAmount)).toFixed(2);
            
          i.discountPrice = ((Number(i.Price) * Number(i.number) -
            Number(i.CardDeductionAmount) - Number(i.CardDiscountPrice) - Number(PayAmount) - Number(i.ArrearAmount)).toFixed(2));
          
          i.MemberPreferentialAmountTotal = 0;
        }
      });
      that.selectGeneralCard.forEach((i) => {
        if (!i.IsLargess && i.IsModifyPrice) {
          let PayAmount = 0;
          if (currentLength == that.totalLength) {
            PayAmount = (Number(changOrderAmount) - Number(totalAllocated)).toFixed(2);
          }else{
            PayAmount = ((Number(i.Price) * Number(i.number) - Number(i.CardDeductionAmount) - Number(i.CardDiscountPrice)  - Number(i.ArrearAmount)) *
            Number(changOrderAmount) / 
             (Number(that.originalTotalAmount) - Number(that.CardDeductionAmount) - Number(that.CardDiscountPrice) - Number(that.ArrearAmount)) ).toFixed(2);  
          }
          totalAllocated += Number(PayAmount);
          currentLength += 1;
          
          i.PayAmount =  Number(PayAmount).toFixed(2);
          i.Amount = Number(i.PayAmount) + Number(i.CardDeductionAmount) + Number(i.CardDiscountPrice) + Number(i.ArrearAmount);
          i.TotalAmount = parseFloat(i.PayAmount) + parseFloat(i.CardDeductionAmount)   + Number(i.ArrearAmount);
          i.discount = that.changOrderDiscount;
          i.DeductionProjectAmount = (Number(i.Price) * Number(i.number) -
            Number(i.CardDeductionAmount) - Number(i.CardDiscountPrice) - Number(i.PayAmount) - Number(i.ArrearAmount)).toFixed(2);
          i.discountPrice = ((Number(i.Price) * Number(i.number) -
            Number(i.CardDeductionAmount) - Number(i.CardDiscountPrice) - Number(PayAmount) - Number(i.ArrearAmount)).toFixed(2));
          i.MemberPreferentialAmountTotal = 0;
        }
      });
      that.selectProduct.forEach((i) => {
        if (!i.IsLargess && i.IsModifyPrice) {
          let PayAmount = 0;
          if (currentLength == that.totalLength) {
            PayAmount = (Number(changOrderAmount) - Number(totalAllocated)).toFixed(2);
          }else{
            PayAmount = ((Number(i.Price) * Number(i.number) - Number(i.CardDeductionAmount) - Number(i.CardDiscountPrice)  - Number(i.ArrearAmount)) *
            Number(changOrderAmount) / 
             (Number(that.originalTotalAmount) - Number(that.CardDeductionAmount) - Number(that.CardDiscountPrice) - Number(that.ArrearAmount)) ).toFixed(2);  
          }
          totalAllocated += Number(PayAmount);
          currentLength += 1;
          
          i.PayAmount = Number(PayAmount).toFixed(2);
          i.Amount = Number(i.PayAmount) + Number(i.CardDeductionAmount) + Number(i.CardDiscountPrice) + Number(i.ArrearAmount);
          i.TotalAmount = parseFloat(i.PayAmount) + parseFloat(i.CardDeductionAmount)  + Number(i.ArrearAmount);
          i.discount = that.changOrderDiscount;
          i.DeductionProjectAmount = (Number(i.Price) * Number(i.number) -
            Number(i.CardDeductionAmount) - Number(i.CardDiscountPrice) - Number(i.PayAmount) - Number(i.ArrearAmount)).toFixed(2);
          i.discountPrice = ((Number(i.Price) * Number(i.number) -
            Number(i.CardDeductionAmount) - Number(i.CardDiscountPrice) - Number(PayAmount) - Number(i.ArrearAmount)).toFixed(2));
          i.MemberPreferentialAmountTotal = 0;
        }
      });
      that.selectPackageCard.forEach(function (i) {
        // 储值卡金额
        let savingCardAmount = i.noLargess
          .filter((i) => i.cardType == '储值卡')
          .reduce((per, cur) => {
            return per + cur.PayAmount ;
          }, 0);

        if (!i.IsLargess && i.IsModifyPrice) {

          let PayAmount = 0;
          
          if (currentLength == that.totalLength) {
            PayAmount = (Number(changOrderAmount) - Number(totalAllocated)).toFixed(2);
          }else{
            PayAmount = ((Number(i.DeductPrice) * Number(i.number) - Number(i.CardDeductionAmount) - Number(i.CardDiscountPrice)  - Number(i.ArrearAmount)) *
            Number(changOrderAmount) / 
             (Number(that.originalTotalAmount) - Number(that.CardDeductionAmount) - Number(that.CardDiscountPrice) - Number(that.ArrearAmount)) ).toFixed(2);  
          }
          console.log("🚀 ~ PayAmount:", PayAmount)
            
          totalAllocated += Number(PayAmount);
          currentLength += 1;
          i.modifyAmount = PayAmount;
          i.PayAmount = parseFloat(parseFloat(i.modifyAmount) + parseFloat(savingCardAmount)).toFixed(2);
          i.discount = parseFloat((i.modifyAmount / (i.DeductPrice * i.number)) * 100).toFixed(2);
          i.DeductionProjectAmount = (Number(i.DeductPrice) * Number(i.number) -
            Number(i.CardDeductionAmount) - Number(i.CardDiscountPrice) - Number(i.PayAmount) - Number(i.ArrearAmount)).toFixed(2);
          i.discountPrice = ((Number(i.DeductPrice) * Number(i.number) -
            Number(i.CardDeductionAmount) - Number(i.CardDiscountPrice) - Number(PayAmount) - Number(i.ArrearAmount)).toFixed(2));
          i.Amount = Number(i.PayAmount) + Number(i.CardDeductionAmount) + Number(i.CardDiscountPrice) + Number(i.ArrearAmount);
          i.TotalAmount = (Number(i.PayAmount) + Number(i.CardDeductionAmount)).toFixed(2)   + Number(i.ArrearAmount);
          i.MemberPreferentialAmountTotal = 0;

          let detailTotalAllocated = PayAmount;
          i.noLargess
            .filter((j) => j.cardType != '储值卡')
            .forEach((j, index, arr) => {
              if (index == arr.length - 1) {
                j.PayAmount = detailTotalAllocated;
                j.TotalAmount = (Number(detailTotalAllocated) + Number(j.cardDeductionAmount)).toFixed(2);
                j.totalAmount = (Number(detailTotalAllocated) + Number(j.cardDeductionAmount)).toFixed(2);
                j.DeductionProjectAmount = (Number(j.TotalPrice)  * Number(j.number) - Number(j.cardDeductionAmount) - Number(j.cardDiscountPrice) - Number(detailTotalAllocated)).toFixed(2);
                j.discount = i.discount;
                j.discountPrice = (Number(j.TotalPrice) * Number(j.number) - Number(j.cardDeductionAmount) - Number(j.cardDiscountPrice) - Number(detailTotalAllocated)).toFixed(2);
              } else {
                let oldAmount = (Number(j.TotalPrice) * Number(j.number) - Number(j.cardDeductionAmount) - Number(j.cardDiscountPrice));
                let detaipPayAmount = (( oldAmount *  Number(PayAmount)) / (Number(PayAmount) + Number(i.discountPrice)) ).toFixed(2); 
                j.PayAmount = detaipPayAmount;
                j.TotalAmount = (Number(detaipPayAmount) + Number(j.cardDeductionAmount)).toFixed(2);
                j.totalAmount = (Number(detaipPayAmount) + Number(j.cardDeductionAmount)).toFixed(2);
                j.DeductionProjectAmount = (Number(j.TotalPrice) * Number(j.number) - Number(j.cardDeductionAmount) - Number(detaipPayAmount)).toFixed(2);
                j.discount = i.discount;
                j.discountPrice = (Number(j.TotalPrice) * Number(j.number)  - Number(j.cardDeductionAmount) - Number(j.cardDiscountPrice) - Number(detaipPayAmount)).toFixed(2);
                detailTotalAllocated = (Number(detailTotalAllocated) - Number(detaipPayAmount)).toFixed(2);
              }
            });
        }
      });

      that.payAmountData();
      that.orderChangeAmountVisible = false;
    },
    /* 修改整单折扣 */
    changOrderAmountClick() {
      let that = this;
      if (that.originalTotalAmount == 0) {
        that.$message.error('没有可以修改金额的商品');
        return;
      }

      if (
        that.selectProject.length == 0 &&
        that.selectProduct.length == 0 &&
        that.selectTimeCard.length == 0 &&
        that.selectGeneralCard.length == 0 &&
        that.selectPackageCard.length == 0
      ) {
        that.$message.error('请选择商品');
        return;
      }
      
      
      let isModifyPrice = false;
      let isUseMemberDiscount = false;
      that.selectProject.forEach((i) => {
        if (i.IsModifyPrice  && !i.IsLargess) {
          isModifyPrice = true;
        }
        if (i.isUseMemberDiscount  && !i.IsLargess) {
          isUseMemberDiscount = true;
        }
      });
      that.selectProduct.forEach((i) => {
        if (i.IsModifyPrice  && !i.IsLargess) {
          isModifyPrice = true;
        }
        if (i.isUseMemberDiscount  && !i.IsLargess) {
          isUseMemberDiscount = true;
        }
      });
      that.selectTimeCard.forEach((i) => {
        if (i.IsModifyPrice  && !i.IsLargess) {
          isModifyPrice = true;
        }
        if (i.isUseMemberDiscount  && !i.IsLargess) {
          isUseMemberDiscount = true;
        }
      });
      that.selectGeneralCard.forEach((i) => {
        if (i.IsModifyPrice  && !i.IsLargess) {
          isModifyPrice = true;
        }
        if (i.isUseMemberDiscount  && !i.IsLargess) {
          isUseMemberDiscount = true;
        }
      });
      that.selectPackageCard.forEach((i) => {
        if (i.IsModifyPrice && !i.IsLargess) {
          isModifyPrice = true;
        }
        if (i.isUseMemberDiscount  && !i.IsLargess) {
          isUseMemberDiscount = true;
        }
      });

      if (!isModifyPrice ) {
        that.$message.error('订单中不包含可以改价商品，不能整单改价');
        return;
      }
      if (isUseMemberDiscount) {
        that.$message.error('订单中包含会员优惠商品，不能整单改价');
        return;
      }
      that.isLockOrderAmount = true;
      

      that.changOrderDiscount = parseFloat((parseFloat(that.changOrderAmount) /  (Number(that.originalTotalAmount || 0) - Number(that.CardDeductionAmount || 0) - Number(that.CardDiscountPrice || 0) - Number(that.ArrearAmount || 0)) ) * 100).toFixed(2);
      that.orderChangeAmountVisible = true;
    },

    /**  确认全部修改经手人  */
    confirmAllHandlerClick() {
      let that = this;
      /* 项目 */
      that.selectProject.forEach((j) => {
        j.handleTypeList.forEach((n) => {
          that.saleAllHandlerList
            .filter((i) => i.Name == n.Name)
            .forEach((e) => {
              n.Employee = e.Employee.filter((em) => em.Checked).map((emp) => {
                emp.ID = `${n.ID}-${emp.EmployeeID}`;
                emp.SaleHandlerID = n.ID;
                return Object.assign({}, emp);
              });
            });
        });
      });

      /* 储值卡 */
      that.selectSavingCard.forEach((j) => {
        j.handleTypeList.forEach((n) => {
          that.saleAllHandlerList
            .filter((i) => i.Name == n.Name)
            .forEach((e) => {
              n.Employee = e.Employee.filter((em) => em.Checked).map((emp) => {
                emp.ID = `${n.ID}-${emp.EmployeeID}`;
                emp.SaleHandlerID = n.ID;
                return Object.assign({}, emp);
              });
            });
        });
      });

      /* 时效卡 */
      that.selectTimeCard.forEach((j) => {
        j.handleTypeList.forEach((n) => {
          that.saleAllHandlerList
            .filter((i) => i.Name == n.Name)
            .forEach((e) => {
              n.Employee = e.Employee.filter((em) => em.Checked).map((emp) => {
                emp.ID = `${n.ID}-${emp.EmployeeID}`;
                emp.SaleHandlerID = n.ID;
                return Object.assign({}, emp);
              });
            });
        });
      });

      /* 通用次卡 */
      that.selectGeneralCard.forEach((j) => {
        j.handleTypeList.forEach((n) => {
          that.saleAllHandlerList
            .filter((i) => i.Name == n.Name)
            .forEach((e) => {
              n.Employee = e.Employee.filter((em) => em.Checked).map((emp) => {
                emp.ID = `${n.ID}-${emp.EmployeeID}`;
                emp.SaleHandlerID = n.ID;
                return Object.assign({}, emp);
              });
            });
        });
      });

      /* 套餐卡 */
      that.selectPackageCard.forEach((j) => {
        j.handleTypeList.forEach((n) => {
          that.saleAllHandlerList
            .filter((i) => i.Name == n.Name)
            .forEach((e) => {
              n.Employee = e.Employee.filter((em) => em.Checked).map((emp) => {
                emp.ID = `${n.ID}-${emp.EmployeeID}`;
                emp.SaleHandlerID = n.ID;
                return Object.assign({}, emp);
              });
            });
        });
      });

      /* 产品 */
      that.selectProduct.forEach((j) => {
        j.handleTypeList.forEach((n) => {
          that.saleAllHandlerList
            .filter((i) => i.Name == n.Name)
            .forEach((e) => {
              n.Employee = e.Employee.filter((em) => em.Checked).map((emp) => {
                emp.ID = `${n.ID}-${emp.EmployeeID}`;
                emp.SaleHandlerID = n.ID;
                return Object.assign({}, emp);
              });
            });
        });
      });

      that.dialogVisibleAllHandler = false;
      that.showRemark = false;
      that.saleAllHandlerList = [];
      that.treatAllHandlerList = [];
    },

    /** 获取经手人   */
    getAllHandlerList() {
      var that = this;
      if (that.tabAllHandlePosition == 'saleHandler') {
        return that.saleAllHandlerList;
      } else {
        return that.treatAllHandlerList;
      }
    },

    /**  修改经手人类型 销售、消耗  */
    hangeAllHandleType() {
      var that = this;
      that.tabAllHandle = '0';
    },
    /**    */
    showSelectAllHandlerClick() {
      let that = this;

      let GoodTypes = [];

      if (that.selectProduct && that.selectProduct.length > 0) {
        GoodTypes.push('10');
      }
      if (that.selectProject && that.selectProject.length > 0) {
        GoodTypes.push('20');
      }
      if (that.selectGeneralCard && that.selectGeneralCard.length > 0) {
        GoodTypes.push('30');
      }
      if (that.selectTimeCard && that.selectTimeCard.length > 0) {
        GoodTypes.push('40');
      }
      if (that.selectSavingCard && that.selectSavingCard.length > 0) {
        GoodTypes.push('50');
      }
      if (that.selectPackageCard && that.selectPackageCard.length > 0) {
        GoodTypes.push('60');
      }

      that.saleHandler_allHandler(GoodTypes);
      that.dialogVisibleAllHandler = true;
    },
    /**    */
    hiddenInfoRemarkClick() {
      let that = this;
      that.showRemark = false;
    },
    /**    */
    showRemarkClick() {
      let that = this;
      that.showRemark = true;
    },

    /**  确认修改价格  */
    confirmWholeOrderChangeAmountClick() {
      let that = this;
      that.allowedAmountItem.forEach((i) => {
        i.PayAmount = parseFloat((i.PayAmount * that.allowedAmountDiscount) / 100).toFixed(2);
        i.Amount = parseFloat((i.PayAmount * that.allowedAmountDiscount) / 100).toFixed(2);
        i.TotalAmount = parseFloat((i.PayAmount * that.allowedAmountDiscount) / 100).toFixed(2);
      });
      that.wholeOrderChangeAmountVisible = false;
      that.payAmountData();
    },
    /**  修改总价  */
    wholeOrderAmountChangeInput() {
      let that = this;
      that.allowedAmountDiscount = parseFloat((that.allowedAmount / that.originalAllowedAmount) * 100).toFixed(2);
    },
    /**  修改总价折扣  */
    wholeOrderdiscountChangeInput() {
      let that = this;
      that.allowedAmount = parseFloat((that.originalAllowedAmount * that.allowedAmountDiscount) / 100).toFixed(2);
    },
    /**  整单改价  */
    wholeOrderChangeAmount() {
      let that = this;
      that.allowedAmountDiscount = 100;
      that.allowedAmountItem = [];
      that.notAllowedAmountItem = [];
      /* 项目 */
      that.selectProject.forEach((i) => {
        if ((that.SellPermission.ModifyPrices_SaleProject || that.SellPermission.isSaleBilling_ModifyPrices_SaleProject) && i.IsModifyPrice && !i.IsLargess) {
          that.allowedAmountItem.push(i);
        } else {
          that.notAllowedAmountItem.push(i);
        }
      });

      /* 产品 */
      that.selectProduct.forEach((i) => {
        if ((that.SellPermission.ModifyPrices_SaleProduct || that.SellPermission.isSaleBilling_ModifyPrices_SaleProduct) && i.IsModifyPrice && !i.IsLargess) {
          that.allowedAmountItem.push(i);
        } else {
          that.notAllowedAmountItem.push(i);
        }
      });
      /* 时效卡 */
      that.selectTimeCard.forEach((i) => {
        if ((that.SellPermission.ModifyPrices_SaleTimeCard || that.SellPermission.isSaleBilling_ModifyPrices_SaleProject) && i.IsModifyPrice && !i.IsLargess) {
          that.allowedAmountItem.push(i);
        } else {
          that.notAllowedAmountItem.push(i);
        }
      });

      /* 通用次卡 */
      that.selectGeneralCard.forEach((i) => {
        if (
          (that.SellPermission.ModifyPrices_SaleGeneralCard || that.SellPermission.isSaleBilling_ModifyPrices_SaleGeneralCard) &&
          i.IsModifyPrice &&
          !i.IsLargess
        ) {
          that.allowedAmountItem.push(i);
        } else {
          that.notAllowedAmountItem.push(i);
        }
      });

      that.allowedAmount = that.allowedAmountItem.reduce((per, cur) => {
        return (per += parseFloat(cur.PayAmount));
      }, 0);
      that.originalAllowedAmount = that.allowedAmount;
      that.notAllowedAmount = that.notAllowedAmountItem.reduce((per, cur) => {
        return (per += parseFloat(cur.PayAmount));
      }, 0);

      that.wholeOrderChangeAmountVisible = true;
    },
    /**    */
    continueSaleBillClick() {
      let that = this;
      that.Remark = '';
      that.dialogPay = false;
      that.$emit('continueSaleBill');
    },
    /**  修改套餐卡明细 品项金额  */
    changePackageDetailItemAmount(item, noLargess, index, noLargessIndex) {
      this.showModifyPrices = true;
      this.noLargessIndex = noLargessIndex;
      item.ArrearAmount = 0;
      noLargess.ArrearAmount = 0;
      this.selectPackageIndex = index;
      this.selectGood = Object.assign({}, item);
      this.selectPackageDetailItem = Object.assign({}, item.noLargess[noLargessIndex]);
      this.changePackageDetailItemDialog = true;
    },
    /**  套餐卡明细修改金额  */
    packageDetailAmountChange() {
      let row = this.selectPackageDetailItem;
      console.log('🚀 ~ packageDetailAmountChange ~ row:', row);
      //计算折扣
      row.discount = parseInt((parseFloat(row.totalAmount) / (parseFloat(row.TotalPrice) * parseFloat(row.number))) * 100);
      //计算优惠
      row.discountPrice = (row.TotalPrice * row.number - row.totalAmount).toFixed(2);
      row.DeductionProjectAmount = row.discountPrice;
      // this.changeSelectPackageInfo(row);
    },
    /**  修改套餐卡明细折扣  */
    packageDetailDiscountChange() {
      let row = this.selectPackageDetailItem;
      //计算金额
      row.totalAmount = ((row.TotalPrice * row.number * row.discount) / 100).toFixed(2);
      //计算优惠
      row.discountPrice = (row.TotalPrice * row.number - row.totalAmount).toFixed(2);
      row.DeductionProjectAmount = row.discountPrice;
      // this.changeSelectPackageInfo(row);
    },
    /**   修改套餐卡金额信息 */
    changeSelectPackageInfo(row) {
      row.DeductionProjectAmount = row.discountPrice;
      row.PayAmount = parseFloat(row.totalAmount) - parseFloat(row.ArrearAmount || 0);
      row.TotalAmount = row.totalAmount;

      this.selectGood.noLargess[this.noLargessIndex] = row;

      let noLargesstotalAmount = this.selectGood.noLargess
        .reduce((perVal, curVal) => {
          return parseFloat(perVal) + parseFloat(curVal.totalAmount || 0);
        }, 0)
        .toFixed(2);

      this.selectGood.Amount = noLargesstotalAmount;
      this.selectGood.PayAmount = noLargesstotalAmount;
    },
    /**  保存修改套餐卡明细价格  */
    submitPackageCardChanePrice() {
      let that = this;
      if (parseFloat(that.selectPackageDetailItem.discount) < parseFloat(that.employeeDiscount * 100)) {
        this.$message({
          message: `员工最低折扣为${that.employeeDiscount * 10} 折，改价不能低于员工最低折扣。`,
          type: 'error',
        });

        // let row = that.selectPackageDetailItem;
        // //计算金额
        // row.totalAmount = (row.TotalPrice * row.number  * that.employeeDiscount).toFixed(2);
        // row.discount = that.employeeDiscount * 100;
        // //计算优惠
        // row.discountPrice = (row.TotalPrice * row.number - row.totalAmount).toFixed(2);
        return;
      }
      let row = that.selectPackageDetailItem;
      row.PayAmount = row.totalAmount;
      row.TotalAmount = row.totalAmount;
      row.totalPrice = row.totalAmount;

      that.selectGood.noLargess.splice(that.noLargessIndex, 1, that.selectPackageDetailItem);
      let PayAmount = 0;
      let discountPrice = 0;

      that.selectGood.noLargess.forEach((item) => {
        PayAmount += parseFloat(item.PayAmount);
        discountPrice = parseFloat(parseFloat(item.discountPrice || 0) + parseFloat(discountPrice)).toFixed(2);
      });
      that.selectGood.PayAmount = PayAmount;
      that.selectGood.TotalAmount = PayAmount;
      that.selectGood.discountPrice = discountPrice;

      that.selectPackageCard.splice(that.selectPackageIndex, 1, that.selectGood);
      that.payAmountData();
      that.changePackageDetailItemDialog = false;
    },
    /**  修改套餐卡详情是否使用会员折扣 */
    // changePackageCardDetaileItemSelectMemberDiscountAmout () {
    //   let row = this.selectPackageDetailItem;
    //   if (row.isUseMemberDiscount) {
    //     let Amount = row.Price * row.number;
    //     if (row.MemberPriceType == 1) {
    //       Amount = row.TotalPrice * row.number * row.MemberDiscountPrice;
    //     }
    //     if (row.MemberPriceType == 2) {
    //       Amount = row.number * row.MemberDiscountPrice;
    //     }
    //     row.Amount = Amount;
    //     row.PayAmount = Amount;
    //     row.TotalAmount = Amount;
    //     row.totalPrice = Amount;
    //     row.discountPrice = 0;
    //     row.DeductionProjectAmount = row.totalPrice * row.number - row.discountPrice;
    //     row.discount = 100;
    //     // row.MemberPreferentialAmount = parseFloat(row.TotalPrice * row.number - Amount).toFixed(2);
    //     row.MemberPreferentialAmountTotal = row.MemberPreferentialAmount * row.number;
    //   } else {
    //     let Amount = row.TotalPrice * row.number;
    //     row.totalAmount = Amount;
    //     row.TotalAmount = Amount;
    //     row.PayAmount = Amount;
    //     row.Amount = Amount;
    //     row.totalPrice = Amount;
    //     row.discountPrice = 0;
    //     row.discount = 100;
    //     row.MemberPreferentialAmountTotal = 0;
    //   }
    // },

    /**  保存备注  */
    saveRemarkClick() {
      let that = this;
      that.currentRemarkItem.Remark = that.projectRemark;
      that.projectRemarkDialogVisible = false;
      that.projectRemark = '';
      that.currentRemarkItem = '';
    },
    /**  编辑项目 备注  */
    editProjectRemarkClick(item) {
      let that = this;
      that.projectRemarkDialogVisible = true;
      that.currentRemarkItem = item;
      that.projectRemark = that.currentRemarkItem.Remark;
    },

    mathAbsData: function (item) {
      return Math.abs(item);
    },
    /**  清除因时间改变选择的卡项   */
    clearDateCardItem() {
      let that = this;
      that.selectGeneralCard = [];
      that.selectTimeCard = [];
      that.selectSavingCard = [];
      that.selectPackageCard = [];
      that.payAmountData();
    },

    /**  清除因时间改变选择的卡项   */
    clearAllDateCardItem() {
      let that = this;
      that.selectProject = [];
      that.selectProduct = [];
      that.selectGeneralCard = [];
      that.selectTimeCard = [];
      that.selectSavingCard = [];
      that.selectPackageCard = [];
      that.payAmountData();
    },

    //获取开单时间
    getBillDate: function () {
      var that = this;
      return that.isSupplement ? that.billDate : date.formatDate.format(new Date(), 'YYYY-MM-DD hh:mm:ss');
    },

    /**  全部分页切换  */
    handleAllGoodsCurrentChange(page) {
      let that = this;
      that.allPaginations.page = page;
      that.getSaleGoodsGoodsTypeGoods();
    },
    /* 全部 分类切换 */
    goodsCategoryChange: function (item, index) {
      var that = this;
      if (that.allCategoryIndex == index) {
        return;
      }
      const goodsType = {
        20: 'project',
        40: 'timeCard',
        30: 'generalCard',
        60: 'packageCard',
        10: 'product',
      };
      this.goodsType = goodsType[item.GoodsType];
      that.allCategoryItem = item;
      that.allCategoryIndex = index;
      that.allPaginations.page = 1;
      that.goodsAll = [];
      that.getSaleGoodsGoodsTypeGoods();
    },
    /**    */
    async getSaleGoodsGoodsType() {
      let that = this;
      let params = {
        Name: that.goodsName,
        BillDate: that.getBillDate(),
      };
      let res = await API.getSaleGoodsGoodsType(params);
      if (res.StateCode == 200) {
        that.allCategory = res.Data;
        that.allCategory.forEach((i) => {
          switch (i.GoodsType) {
            case '10' /* 产品 */:
              that.IsExistProduct = true;
              break;
            case '20' /* 项目 */:
              that.IsExistProject = true;
              break;
            case '30' /* 通用此卡 */:
              that.IsExistGeneralCard = true;
              break;
            case '40' /* 时效卡 */:
              that.IsExistTimeCard = true;
              break;
            case '50' /* 储值卡 */:
              that.IsExistSavingCard = true;
              break;
            case '60' /* 类型ID */:
              that.IsExistPackageCard = true;
              break;
          }
        });

        if (that.allCategory && that.allCategory.length) {
          that.allCategoryIndex = 0;
          that.allCategoryItem = that.allCategory[that.allCategoryIndex];
          that.getSaleGoodsGoodsTypeGoods();
        } else {
          that.goodsAll = [];
        }
      } else {
        that.$message.error(res.Message);
      }
    },
    /**  获取分类下的全部商品  */
    async getSaleGoodsGoodsTypeGoods() {
      let that = this;
      let params = {
        PageNum: that.allPaginations.page,
        GoodsType: that.allCategoryItem.GoodsType, //类型ID
        Name: that.typeIndex == '0' ? that.goodsName : '',
        BillDate: that.getBillDate(), //时间
      };
      that.allLoading = true;
      let res = await API.getSaleGoodsGoodsTypeGoods(params);
      if (res.StateCode == 200) {
        that.goodsAll = res.List;
        // that.goodsList = res.List;
        that.allPaginations.total = res.Total;
        that.allPaginations.page_size = res.PageSize;
      } else {
        that.$message.error(res.Message);
      }
      that.allLoading = false;
    },

    /**  项目 TODO ******************************  */
    // 项目一级分类
    projectCategoryChange: function (item, index) {
      var that = this;
      /* 已经选中的防止重复点击 */
      if (that.projectCategoryIndex == index) {
        return;
      }
      that.projectCategoryIndex = index;
      that.projectSecondCategoryIndex = 0;
      that.projectSecondCategory = item.Child;
      that.projectSecondItem = that.projectSecondCategory[that.projectSecondCategoryIndex];
      that.projectPaginations.page = 1;
      that.getSaleGoodsProjectByCategoryy();
    },
    /**   项目二级分类点击 */
    projectSecondCategoryChange(item, index) {
      let that = this;
      /* 已经选中的防止重复点击 */
      if (that.projectSecondCategoryIndex == index) {
        return;
      }
      that.projectSecondCategoryIndex = index;
      that.projectSecondItem = item;
      that.projectPaginations.page = 1;
      that.getSaleGoodsProjectByCategoryy();
    },
    /**  修改项目分页  */
    handleProjectCurrentChange(page) {
      let that = this;
      that.projectPaginations.page = page;
      that.getSaleGoodsProjectByCategoryy();
    },
    /**
     * @description: TODO 项目分类
     * @return {*}
     */
    async getSaleGoodsProjectCategory() {
      let that = this;
      let params = {
        Name: that.goodsName,
      };
      let res = await API.getSaleGoodsProjectCategory(params);
      if (res.StateCode == 200) {
        that.projectCategory = res.Data;
        if (that.projectCategory && that.projectCategory.length) {
          that.projectCategoryIndex = 0;
          let firstItem = that.projectCategory[that.projectCategoryIndex];
          if (firstItem.Child && firstItem.Child.length) {
            that.projectSecondCategory = firstItem.Child;
            that.projectSecondCategoryIndex = 0;
            that.projectSecondItem = firstItem.Child[that.projectSecondCategoryIndex];

            that.getSaleGoodsProjectByCategoryy();
          }
        } else {
          that.projectSecondCategory = [];
          that.projectList = [];
        }
      } else {
        that.$message.error(res.Message);
      }
    },
    /**
     * @description: 分类下的项目
     * @return {*}
     */
    async getSaleGoodsProjectByCategoryy() {
      let that = this;
      let params = {
        PageNum: that.projectPaginations.page,
        Name: that.typeIndex == '1' ? that.goodsName : '',
        CategoryID: that.projectSecondItem.CategoryID, //分类ID
      };
      that.projectLoading = true;
      let res = await API.getSaleGoodsProjectByCategoryy(params);
      if (res.StateCode == 200) {
        that.projectList = res.List;
        that.projectPaginations.total = res.Total;
        that.projectPaginations.page_size = res.PageSize;
      } else {
        that.$message.error(res.Message);
      }
      that.projectLoading = false;
    },
    /**  产品 TODO ******************************  */
    // 产品一级分类
    productCategoryChange: function (item, index) {
      var that = this;
      /* 已经选中的防止重复点击 */
      if (that.productCategoryIndex == index) {
        return;
      }
      that.productCategoryIndex = index;
      that.productSecondCategoryIndex = 0;
      that.productSecondCategory = item.Child;
      that.productSecondItem = that.productSecondCategory[that.productSecondCategoryIndex];
      that.productPaginations.page = 1;
      that.getSaleGoodsProductByCategory();
    },
    /**  产品二级分类  */
    productSecondCategoryChange(item, index) {
      let that = this;
      /* 已经选中的防止重复点击 */
      if (that.productSecondCategoryIndex == index) {
        return;
      }
      that.productSecondCategoryIndex = index;
      that.productSecondItem = item;
      that.productPaginations.page = 1;
      that.getSaleGoodsProductByCategory();
    },
    /**  修改项目分页  */
    handleProductCurrentChange(page) {
      let that = this;
      that.productPaginations.page = page;
      that.getSaleGoodsProductByCategory();
    },
    /**
     * @description: TODO 产品分类
     * @return {*}
     */
    async getSaleGoodsProductCategory() {
      let that = this;
      let params = { Name: that.goodsName };
      let res = await API.getSaleGoodsProductCategory(params);
      if (res.StateCode == 200) {
        that.productCategory = res.Data;
        if (that.productCategory && that.productCategory.length) {
          that.productCategoryIndex = 0;
          let firstItem = that.productCategory[that.productCategoryIndex];
          if (firstItem.Child && firstItem.Child.length) {
            that.productSecondCategory = firstItem.Child;
            that.productSecondCategoryIndex = 0;
            that.productSecondItem = firstItem.Child[that.productSecondCategoryIndex];
            that.getSaleGoodsProductByCategory();
          }
        } else {
          that.productSecondCategory = [];
          that.productList = [];
        }
      } else {
        that.$message.error(res.Message);
      }
    },
    /**
     * @description: 产品列表
     * @return {*}
     */
    async getSaleGoodsProductByCategory() {
      let that = this;
      let params = {
        PageNum: that.productPaginations.page,
        Name: that.typeIndex == '6' ? that.goodsName : '',
        CategoryID: that.productSecondItem.CategoryID, //分类ID
      };
      that.productLoading = true;
      let res = await API.getSaleGoodsProductByCategory(params);
      if (res.StateCode == 200) {
        that.productList = res.List;
        that.productPaginations.total = res.Total;
        that.productPaginations.page_size = res.PageSize;
      } else {
        that.$message.error(res.Message);
      }
      that.productLoading = false;
    },

    /**  储值卡 TODO ******************************  */
    /**  储值卡修改分页  */
    handleSavingCurrentChange(page) {
      let that = this;
      that.savingPaginations.page = page;
      that.getSaleGoodsSavingCardByCategory();
    },
    //储值卡一级分类
    savingCategoryChange: function (item, index) {
      var that = this;
      /* 已经选中的防止重复点击 */
      if (that.savingCategoryIndex == index) {
        return;
      }
      that.savingCategoryIndex = index;
      that.savingCardCategoryItem = that.savingCardCategory[that.savingCategoryIndex];
      that.savingPaginations.page = 1;
      that.getSaleGoodsSavingCardByCategory();
    },
    /**  请求储值卡分类  */
    async getSaleGoodsSavingCardCategoryd() {
      let that = this;
      let params = {
        Name: that.goodsName,
        BillDate: that.getBillDate(),
      };
      let res = await API.getSaleGoodsSavingCardCategoryd(params);
      if (res.StateCode == 200) {
        that.savingCardCategory = res.Data;
        if (that.savingCardCategory && that.savingCardCategory.length) {
          that.savingCategoryIndex = 0;
          that.savingCardCategoryItem = that.savingCardCategory[that.savingCategoryIndex];
          that.getSaleGoodsSavingCardByCategory();
        } else {
          that.savingCardList = [];
        }
      } else {
        that.$message.error(res.Message);
      }
    },
    /**  请求储值卡分类  */
    async getSaleGoodsSavingCardByCategory() {
      let that = this;
      let params = {
        PageNum: that.savingPaginations.page,
        Name: that.typeIndex == '2' ? that.goodsName : '',
        BillDate: that.getBillDate(), //时间
        CategoryID: that.savingCardCategoryItem.CategoryID, //分类ID
      };
      that.savingLoading = true;
      let res = await API.getSaleGoodsSavingCardByCategory(params);
      if (res.StateCode == 200) {
        that.savingCardList = res.List;
        that.savingPaginations.total = res.Total;
        that.savingPaginations.page_size = res.PageSize;
        that.savingLoading = false;
      } else {
        that.savingLoading = false;
        that.$message.error(res.Message);
      }
      that.savingLoading = false;
    },

    /**  时效卡 TODO ******************************  */
    /**  时效卡修改分页  */
    handleTimeCurrentChange(page) {
      let that = this;
      that.timeCardPaginations.page = page;
      that.getSaleGoodsTimeCardByCategory();
    },
    // 时效卡一级分类
    timeCategoryChange(item, index) {
      var that = this;
      /* 已经选中的防止重复点击 */
      if (that.timeCategoryIndex == index) {
        return;
      }
      that.timeCategoryIndex = index;
      that.timeCategoryItem = that.timeCardCategory[that.timeCategoryIndex];
      that.timeCardPaginations.page = 1;
      that.getSaleGoodsTimeCardByCategory();
    },
    /**  请求时效卡分类  */
    async getSaleGoodsTimeCardCategory() {
      let that = this;
      let params = {
        Name: that.goodsName,
        BillDate: that.getBillDate(),
      };
      let res = await API.getSaleGoodsTimeCardCategory(params);
      if (res.StateCode == 200) {
        that.timeCardCategory = res.Data;
        if (that.timeCardCategory && that.timeCardCategory.length) {
          that.timeCategoryIndex = 0;
          that.timeCategoryItem = that.timeCardCategory[that.timeCategoryIndex];
          that.getSaleGoodsTimeCardByCategory();
        } else {
          that.timeCardList = [];
        }
      } else {
        that.$message.error(res.Message);
      }
    },
    /**  分类 时效卡  */
    async getSaleGoodsTimeCardByCategory() {
      let that = this;
      let params = {
        PageNum: that.timeCardPaginations.page,
        Name: that.typeIndex == '3' ? that.goodsName : '',
        BillDate: that.getBillDate(), //时间
        CategoryID: that.timeCategoryItem.CategoryID, //分类ID
      };
      that.timeCardLoading = true;
      let res = await API.getSaleGoodsTimeCardByCategory(params);
      if (res.StateCode == 200) {
        that.timeCardList = res.List;
        that.timeCardPaginations.total = res.Total;
        that.timeCardPaginations.page_size = res.PageSize;
        that.timeCardLoading = false;
      } else {
        that.timeCardLoading = false;
        that.$message.error(res.Message);
      }
      that.timeCardLoading = false;
    },

    /**  通用次卡 TODO ******************************  */
    /**  通用次卡修改分页  */
    handleGeneralCarCurrentChange(page) {
      let that = this;
      that.generalCarPaginations.page = page;
      that.getSaleGoodsGeneralCardByCategory();
    },
    // 通用次卡一级分类
    generalCategoryChange: function (item, index) {
      var that = this;
      /* 已经选中的防止重复点击 */
      if (that.generalCategoryIndex == index) {
        return;
      }
      that.generalCategoryIndex = index;
      that.generalCategoryItem = that.generalCardCategory[that.generalCategoryIndex];
      that.generalCarPaginations.page = 1;
      that.getSaleGoodsGeneralCardByCategory();
    },
    /**  请求通用次卡卡分类  */
    async getSaleGoodsGeneralCardCategory() {
      let that = this;
      let params = {
        Name: that.goodsName,
        BillDate: that.getBillDate(),
      };
      let res = await API.getSaleGoodsGeneralCardCategory(params);
      if (res.StateCode == 200) {
        that.generalCardCategory = res.Data;
        if (that.generalCardCategory && that.generalCardCategory.length) {
          that.generalCategoryIndex = 0;
          that.generalCategoryItem = that.generalCardCategory[that.generalCategoryIndex];
          that.getSaleGoodsGeneralCardByCategory();
        } else {
          that.generalCardList = [];
        }
      } else {
        that.$message.error(res.Message);
      }
    },
    /**  分类 通用次卡  */
    async getSaleGoodsGeneralCardByCategory() {
      let that = this;
      let params = {
        PageNum: that.generalCarPaginations.page,
        Name: that.typeIndex == '4' ? that.goodsName : '',
        BillDate: that.getBillDate(), //时间
        CategoryID: that.generalCategoryItem.CategoryID, //分类ID
      };
      that.generalCarLoading = true;
      let res = await API.getSaleGoodsGeneralCardByCategory(params);
      if (res.StateCode == 200) {
        that.generalCardList = res.List;
        that.generalCarPaginations.total = res.Total;
        that.generalCarPaginations.page_size = res.PageSize;
        that.generalCarLoading = false;
      } else {
        that.generalCarLoading = false;
        that.$message.error(res.Message);
      }
      that.generalCarLoading = false;
    },
    /**  套餐卡 TODO ******************************  */
    /* 套餐卡分页修改 */
    handlePackageCardCurrentChange(page) {
      let that = this;
      that.packageCardPaginations.page = page;
      that.getSaleGoodsPackageCardByCategory();
    },
    // 套餐卡一级分类
    packageCategoryChange: function (item, index) {
      var that = this;
      /* 已经选中的防止重复点击 */
      if (that.packageCategoryIndex == index) {
        return;
      }
      that.packageCategoryIndex = index;
      that.packageCategoryItem = that.packageCardCategory[that.packageCategoryIndex];
      that.packageCardPaginations.page = 1;
      that.getSaleGoodsPackageCardByCategory();
    },
    /**  请求通套餐卡分类  */
    async getSaleGoodsPackageCardCategory() {
      let that = this;
      let params = {
        Name: that.goodsName,
        BillDate: that.getBillDate(),
      };
      let res = await API.getSaleGoodsPackageCardCategory(params);
      if (res.StateCode == 200) {
        that.packageCardCategory = res.Data;
        if (that.packageCardCategory && that.packageCardCategory.length) {
          that.packageCategoryIndex = 0;
          that.packageCategoryItem = that.packageCardCategory[that.packageCategoryIndex];
          that.getSaleGoodsPackageCardByCategory();
        }
      } else {
        that.$message.error(res.Message);
      }
    },
    /**  分类 套餐卡 */
    async getSaleGoodsPackageCardByCategory() {
      let that = this;
      let params = {
        PageNum: that.packageCardPaginations.page,
        Name: that.typeIndex == '5' ? that.goodsName : '',
        BillDate: that.getBillDate(), //时间
        CategoryID: that.packageCategoryItem.CategoryID, //分类ID
      };
      that.packageCardLoading = true;
      let res = await API.getSaleGoodsPackageCardByCategory(params);
      if (res.StateCode == 200) {
        that.packageCardList = res.List;
        that.packageCardPaginations.total = res.Total;
        that.packageCardLoading = false;
      } else {
        that.packageCardLoading = false;
        that.$message.error(res.Message);
      }
      that.packageCardLoading = false;
    },

    // 项目经手人
    projectHandlerData: function () {
      var that = this;
      that.loading = true;
      API.getProjectHandler()
        .then((res) => {
          if (res.StateCode == 200) {
            that.projectHandlerList = res.Data;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },
    // 产品经手人
    productHandlerData: function () {
      var that = this;
      that.loading = true;
      API.getProductHandler()
        .then((res) => {
          if (res.StateCode == 200) {
            that.productHandlerList = res.Data;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },
    // 通用次卡经手人
    generalHandlerData: function () {
      var that = this;
      that.loading = true;
      API.getGeneralCardHandler()
        .then((res) => {
          if (res.StateCode == 200) {
            that.generalCardHandlerList = res.Data;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },
    // 时效卡经手人
    timeCardHandlerData: function () {
      var that = this;
      that.loading = true;
      API.getTimeCardHandler()
        .then((res) => {
          if (res.StateCode == 200) {
            that.timeCardHandlerList = res.Data;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },
    // 储值卡经手人
    savingCardHandlerData: function () {
      var that = this;
      that.loading = true;
      API.getSavingCardHandler()
        .then((res) => {
          if (res.StateCode == 200) {
            that.savingCardHandlerList = res.Data;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },
    // 套餐卡经手人
    packageCardHandlerData: function () {
      var that = this;
      that.loading = true;
      API.getPackageCardHandler()
        .then((res) => {
          if (res.StateCode == 200) {
            that.packageCardHandlerList = res.Data;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },
    // 支付方式
    salePayMethodData: function () {
      var that = this;
      that.loading = true;
      API.getSalePayMethod()
        .then((res) => {
          if (res.StateCode == 200) {
            that.payTypeList = res.Data;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },
    // 会员通用储值卡
    savingCardAllGoodsData: function () {
      var that = this;
      that.loading = true;
      var params = {
        CustomerID: that.customerID,
      };
      API.getSavingCardAllGoods(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.savingCardAllGoods = res.Data;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },
    // 会员非通用储值卡（指定商品抵扣卡）
    savingCardSomeGoodsData: function () {
      var that = this;
      that.loading = true;
      var params = {
        CustomerID: that.customerID,
      };
      API.getSavingCardSomeGoods(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.savingCardSomeGoods = res.Data;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },
    // 可抵扣产品的储值卡列表
    savingCardProductData: function (item) {
      var that = this;
      if (that.customerID == '' || that.customerID == null) {
        return false;
      }
      that.loading = true;
      var params = {
        CustomerID: that.customerID,
        ProductID: item.ID,
      };
      API.getSavingCardProduct(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.savingCardDeductionData(res.Data, item);
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },
    // 可抵扣项目的储值卡列表
    savingCardProjecctData: function (item) {
      var that = this;
      that.loading = true;
      if (that.customerID == '' || that.customerID == null) {
        return false;
      }
      var params = {
        CustomerID: that.customerID,
        ProjectID: item.ID,
      };
      API.getSavingCardProject(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.savingCardDeductionData(res.Data, item);
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },
    // 可抵扣通用次卡的储值卡列表
    savingCardGeneralCardData: function (item) {
      var that = this;
      that.loading = true;
      if (that.customerID == '' || that.customerID == null) {
        return false;
      }
      var params = {
        CustomerID: that.customerID,
        GeneralCardID: item.ID,
      };
      API.getSavingCardGeneralCard(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.savingCardDeductionData(res.Data, item);
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },
    // 可抵扣时效卡的储值卡列表
    savingCardTimeCardData: function (item) {
      var that = this;
      that.loading = true;
      if (that.customerID == '' || that.customerID == null) {
        return false;
      }
      var params = {
        CustomerID: that.customerID,
        TimeCardID: item.ID,
      };
      API.getSavingCardTimeCard(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.savingCardDeductionData(res.Data, item);
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },
    // 可抵扣套餐卡的储值卡列表
    savingCardPackageCardData: function (item) {
      var that = this;
      that.loading = true;
      if (that.customerID == '' || that.customerID == null) {
        return false;
      }
      var params = {
        CustomerID: that.customerID,
        PackageCardID: item.ID,
      };
      API.getSavingCardPackageCard(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.savingCardDeductionData(res.Data, item);
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },
    savingCardDeductionData: function (data, item) {
      var that = this;
      var savingCardAll = [];
      data.forEach(function (discount) {
        if (discount.PriceType == 1) {
          discount.DiscountPrice = discount.DiscountPrice * 10;
        }
      });
      that.savingCardSomeGoods.forEach(function (some) {
        data.forEach(function (project) {
          if (some.ID == project.ID) {
            savingCardAll.push({
              IsLargess: project.IsLargess,
              checked: false,
              AccountID: some.AccountID,
              Balance: some.Balance,
              ID: some.ID,
              LargessBalance: some.LargessBalance,
              SavingCardName: some.SavingCardName,
              TotalBalance: some.TotalBalance,
              TotalPrice: some.TotalBalance,
              Type: some.Type,
              DiscountPrice: project.DiscountPrice,
              PriceType: project.PriceType,
              TotalAmount: '',
              PreferentialAmount: 0,
              cardDiscountPrice: 0,
              cardDeductionAmount: 0,
            });
          }
        });
      });
      savingCardAll.forEach(function (saving) {
        item.savingCardDeduction.forEach(function (deduction) {
          if (saving.ID == deduction.ID) {
            saving.checked = true;
            saving.TotalAmount = deduction.TotalAmount || 0;
            saving.TotalBalance = (parseFloat(saving.TotalBalance) + parseFloat(deduction.TotalAmount)).toFixed(2);
            saving.TotalPrice = (saving.TotalBalance - saving.TotalAmount).toFixed(2);
            saving.PreferentialAmount = deduction.PreferentialAmount;
            saving.cardDiscountPrice = deduction.cardDiscountPrice;
            saving.cardDeductionAmount = deduction.cardDeductionAmount;
          }
        });
      });
      that.savingCardAll = Enumerable.from(savingCardAll)
        .where((i) => {
          return i.TotalBalance > 0;
        })
        .toArray();
    },
    // 搜索商品
    searchGoodsClick: function () {
      var that = this;
      that.tabPane = that.typeIndex;
      switch (that.typeIndex) {
        case '0':
          that.getSaleGoodsGoodsType();
          break;
        case '1':
          that.getSaleGoodsProjectCategory();
          break;
        case '2':
          that.getSaleGoodsSavingCardCategoryd();
          break;
        case '3':
          that.getSaleGoodsTimeCardCategory();
          break;
        case '4':
          that.getSaleGoodsGeneralCardCategory();
          break;
        case '5':
          that.getSaleGoodsPackageCardCategory();
          break;
        case '6':
          that.getSaleGoodsProductCategory();
          break;
        default:
          that.getSaleGoodsGoodsType();
      }
    },
    // 清空
    clearClick: function () {
      var that = this;
      that.searchGoodsClick();
    },

    // 锚点
    navChange: function (index, selector) {
      var that = this;
      var anchor = this.$el.querySelector(selector);
      that.$el.querySelector('.el_scrollbar_project').scrollTop = anchor.offsetTop;
    },
    // 商品选择
    goodsChange: function (row) {
      var that = this;
      switch (that.allCategoryItem.GoodsType) {
        case '20':
          that.projectChange(row);
          break;
        case '50':
          that.savingCardChange(row);
          break;
        case '40':
          that.timeCardChange(row);
          break;
        case '30':
          that.generalCardChange(row);
          break;
        case '60':
          that.packageCardChange(row);
          break;
        case '10':
          that.productChange(row);
          break;
      }
    },
    // 项目选择
    projectChange: function (row) {
      if (this.customerID) {
        this.saleGoods_CustomerDiscount(row.ID).then((res) => {
          this.projectChangePush(row, res);
        });
      } else {
        this.projectChangePush(row);
      }
    },
    projectChangePush(row, memberDiscount) {
      let that = this;
      let isUseMemberDiscount = false;
      let isShowSelectMemberAmout = false;
      let MemberPreferentialAmount = '';
      let MemberDiscountPrice = '';
      let MemberPriceType = '';
      let Price = row.Price;
      if (memberDiscount) {
        isUseMemberDiscount = true;
        MemberDiscountPrice = memberDiscount.DiscountPrice;
        MemberPriceType = memberDiscount.PriceType;
        isShowSelectMemberAmout = true;
        if (memberDiscount.PriceType == 1) {
          Price = parseFloat(row.Price) * parseFloat(memberDiscount.DiscountPrice);
          MemberPreferentialAmount = parseFloat(row.Price) - Price;
        }
        if (memberDiscount.PriceType == 2) {
          Price = parseFloat(memberDiscount.DiscountPrice);
          MemberPreferentialAmount = parseFloat(row.Price) - Price;
        }
      }
      var data = {
        type: 1,
        Alias: row.Alias,
        ID: row.ID,
        IsAllowLargess: row.IsAllowLargess,
        IsLargess: false,
        Name: row.Name,
        Price: row.Price,
        totalPrice: Price,
        number: 1,
        discount: 100,
        Amount: Price,
        PayAmount: Price,
        TotalAmount: Price,
        ArrearAmount: 0,
        isModify: true,
        DeductionProjectAmount: 0,
        discountPrice: 0,
        CardDeductionAmount: 0,
        CardDiscountPrice: 0,
        savingCardDeduction: [],
        handleTypeList: [],
        Remark: '',
        IsModifyPrice: row.IsModifyPrice,
        MemberPreferentialAmount, // 会员优惠金额
        MemberPreferentialAmountTotal: MemberPreferentialAmount,
        isUseMemberDiscount,
        isShowSelectMemberAmout,
        MemberDiscountPrice,
        MemberPriceType,
      };

      that.projectHandlerList.forEach(function (item) {
        data.handleTypeList.push({
          ID: item.ID,
          Name: item.Name,
          Employee: [],
        });
      });

      that.selectProject.push(data);
      that.payAmountData();
    },
    // 储值卡选择
    savingCardChange: function (row) {
      var that = this;
      let recharItem = row.SavingCardRechargeRules.length > 0 ? row.SavingCardRechargeRules[0] : null;
      var data = {
        type: 2,
        Alias: row.Alias,
        ID: row.ID,
        IsAllowLargess: row.IsAllowLargess,
        IsLargess: false,
        Name: row.Name,
        Price: recharItem ? recharItem.Price : row.Price,
        LargessPrice: recharItem ? recharItem.LargessPrice : row.LargessPrice,
        largessPrice: recharItem ? recharItem.LargessPrice : row.LargessPrice,
        number: 1,
        Amount: recharItem ? recharItem.Price : row.Price,
        totalPrice: recharItem ? recharItem.Price : row.Price,
        PayAmount: recharItem ? recharItem.Price : row.Price,
        ArrearAmount: 0,
        LargessAmount: 0,
        handleTypeList: [],
        SavingCardRechargeRules: row.SavingCardRechargeRules, //阶梯价格
        RechargeRulesSelectIndex: 0,
        IsModifyLargessPrice: row.IsModifyLargessPrice, //是否可修改赠金
        IsModifyPrice: row.IsModifyPrice, // 是否可修改本金
        Remark: '',
      };
      that.savingCardHandlerList.forEach(function (item) {
        data.handleTypeList.push({
          ID: item.ID,
          Name: item.Name,
          Employee: [],
        });
      });
      that.selectSavingCard.push(data);
      that.payAmountData();
    },
    // 时效卡选择
    timeCardChange: function (row) {
      if (this.customerID) {
        this.saleGoods_CustomerDiscount(row.ID).then((res) => {
          this.timeCardChangePush(row, res);
        });
      } else {
        this.timeCardChangePush(row);
      }
    },
    timeCardChangePush(row, memberDiscount) {
      var that = this;
      let isUseMemberDiscount = false;
      let isShowSelectMemberAmout = false;
      let MemberPreferentialAmount = '';
      let MemberDiscountPrice = '';
      let MemberPriceType = '';
      let Price = row.Price;
      if (memberDiscount) {
        isUseMemberDiscount = true;
        MemberDiscountPrice = memberDiscount.DiscountPrice;
        MemberPriceType = memberDiscount.PriceType;
        isShowSelectMemberAmout = true;
        if (memberDiscount.PriceType == 1) {
          Price = parseFloat(row.Price) * parseFloat(memberDiscount.DiscountPrice);
          MemberPreferentialAmount = parseFloat(row.Price) - Price;
        }
        if (memberDiscount.PriceType == 2) {
          Price = parseFloat(memberDiscount.DiscountPrice);
          MemberPreferentialAmount = parseFloat(row.Price) - Price;
        }
      }
      var data = {
        type: 3,
        Alias: row.Alias,
        ID: row.ID,
        IsAllowLargess: row.IsAllowLargess,
        IsLargess: false,
        Name: row.Name,
        Price: row.Price,
        totalPrice: Price,
        number: 1,
        discount: 100,
        Amount: Price,
        PayAmount: Price,
        TotalAmount: Price,
        ArrearAmount: 0,
        isModify: true,
        DeductionProjectAmount: 0,
        discountPrice: 0,
        CardDeductionAmount: 0,
        CardDiscountPrice: 0,
        savingCardDeduction: [],
        handleTypeList: [],
        Remark: '',
        IsModifyPrice: row.IsModifyPrice,

        MemberPreferentialAmount, // 会员优惠金额
        MemberPreferentialAmountTotal: MemberPreferentialAmount,
        isUseMemberDiscount,
        isShowSelectMemberAmout,
        MemberDiscountPrice,
        MemberPriceType,
      };
      that.timeCardHandlerList.forEach(function (item) {
        data.handleTypeList.push({
          ID: item.ID,
          Name: item.Name,
          Employee: [],
        });
      });
      that.selectTimeCard.push(data);
      that.payAmountData();
    },
    // 通用次卡选择
    generalCardChange: function (row) {
      if (this.customerID) {
        this.saleGoods_CustomerDiscount(row.ID).then((res) => {
          this.generalCardChangePush(row, res);
        });
      } else {
        this.generalCardChangePush(row);
      }
    },
    generalCardChangePush(row, memberDiscount) {
      var that = this;
      let isUseMemberDiscount = false;
      let isShowSelectMemberAmout = false;
      let MemberPreferentialAmount = '';
      let MemberDiscountPrice = '';
      let MemberPriceType = '';
      let Price = row.Price;
      if (memberDiscount) {
        isUseMemberDiscount = true;
        MemberDiscountPrice = memberDiscount.DiscountPrice;
        MemberPriceType = memberDiscount.PriceType;
        isShowSelectMemberAmout = true;
        if (memberDiscount.PriceType == 1) {
          Price = parseFloat(row.Price) * parseFloat(memberDiscount.DiscountPrice);
          MemberPreferentialAmount = parseFloat(row.Price) - Price;
        }
        if (memberDiscount.PriceType == 2) {
          Price = parseFloat(memberDiscount.DiscountPrice);
          MemberPreferentialAmount = parseFloat(row.Price) - Price;
        }
      }

      var data = {
        type: 4,
        Alias: row.Alias,
        ID: row.ID,
        IsAllowLargess: row.IsAllowLargess,
        IsLargess: false,
        Name: row.Name,
        Price: row.Price,
        Times: row.Amount,
        totalPrice: Price,
        number: 1,
        discount: 100,
        Amount: Price,
        PayAmount: Price,
        TotalAmount: Price,
        ArrearAmount: 0,
        isModify: true,
        DeductionProjectAmount: 0,
        discountPrice: 0,
        CardDeductionAmount: 0,
        CardDiscountPrice: 0,
        savingCardDeduction: [],
        handleTypeList: [],
        Remark: '',
        IsModifyPrice: row.IsModifyPrice,
        MemberPreferentialAmount, // 会员优惠金额
        MemberPreferentialAmountTotal: MemberPreferentialAmount, // 会员优惠金额
        isUseMemberDiscount,
        isShowSelectMemberAmout,
        MemberDiscountPrice,
        MemberPriceType,
      };
      that.generalCardHandlerList.forEach(function (item) {
        data.handleTypeList.push({
          ID: item.ID,
          Name: item.Name,
          Employee: [],
        });
      });

      that.selectGeneralCard.push(data);
      that.payAmountData();
    },
    packageCardChange(row) {
      if (this.customerID) {
        this.saleGoods_CustomerDiscount(row.ID).then((res) => {
          this.packageCardChangePush(row, res);
        });
      } else {
        this.packageCardChangePush(row);
      }
    },
    // 套餐卡选择
    packageCardChangePush(row, memberDiscount) {
      var that = this;
      // 计算套餐卡会员优惠金额
      let isUseMemberDiscount = false;
      let isShowSelectMemberAmout = false;
      let MemberPreferentialAmount = '';
      let MemberDiscountPrice = '';
      let MemberPriceType = '';
      let Price = row.DeductPrice;
      if (memberDiscount) {
        MemberDiscountPrice = memberDiscount.DiscountPrice;
        MemberPriceType = memberDiscount.PriceType;
        isUseMemberDiscount = true;
        isShowSelectMemberAmout = true;
        if (memberDiscount.PriceType == 1) {
          Price = parseFloat(row.DeductPrice) * parseFloat(memberDiscount.DiscountPrice) + (row.Price - row.DeductPrice);
          MemberPreferentialAmount = parseFloat(row.Price) - Price;
        }
        if (memberDiscount.PriceType == 2) {
          Price = parseFloat(memberDiscount.DiscountPrice) + (row.Price - row.DeductPrice);
          MemberPreferentialAmount = parseFloat(row.Price) - Price;
        }
      } else {
        Price = row.Price;
      }
      var largess = [];
      var noLargess = [];
      var largessProduct = Enumerable.from(row.PackageCardLargessGoods.Product)
        .select((val) => ({
          ID: val.ID,
          Name: val.Name,
          Alias: val.Alias,
          Amount: val.Amount,
          Price: val.Price,
          TotalPrice: val.TotalPrice,
          cardType: '产品',
          number: 1,
          isCardType: 6,
          Remark: '',
        }))
        .toArray();
      var largessProject = Enumerable.from(row.PackageCardLargessGoods.Project)
        .select((val) => ({
          ID: val.ID,
          Name: val.Name,
          Alias: val.Alias,
          Amount: val.Amount,
          Price: val.Price,
          TotalPrice: val.TotalPrice,
          cardType: '项目',
          number: 1,
          isCardType: 1,
          Remark: '',
        }))
        .toArray();
      var largessGeneralCard = Enumerable.from(row.PackageCardLargessGoods.GeneralCard)
        .select((val) => ({
          ID: val.ID,
          Name: val.Name,
          Alias: val.Alias,
          Amount: val.Amount,
          Price: val.Price,
          TotalPrice: val.TotalPrice,
          GeneralCardAmount: val.GeneralCardAmount,
          cardType: '通用次卡',
          number: 1,
          isCardType: 4,
          Remark: '',
        }))
        .toArray();
      var largessTimeCard = Enumerable.from(row.PackageCardLargessGoods.TimeCard)
        .select((val) => ({
          ID: val.ID,
          Name: val.Name,
          Alias: val.Alias,
          Amount: val.Amount,
          Price: val.Price,
          TotalPrice: val.TotalPrice,
          cardType: '时效卡',
          number: 1,
          isCardType: 3,
          Remark: '',
        }))
        .toArray();
      var largessSavingCard = Enumerable.from(row.PackageCardLargessGoods.SavingCard)
        .select((val) => ({
          ID: val.ID,
          Name: val.Name,
          Alias: val.Alias,
          Amount: 1,
          Price: val.Price,
          TotalPrice: val.TotalPrice,
          cardType: '储值卡',
          number: 1,
          isCardType: 2,
          Remark: '',
        }))
        .toArray();

      var product = Enumerable.from(row.PackageCardGoods.Product)
        .select((val) => {
          let child_amonunt = that.getPackageDetailMemberAmount(val.TotalPrice, memberDiscount);
          return {
            ID: val.ID,
            Name: val.Name,
            Alias: val.Alias,
            Amount: val.Amount,
            Price: val.Price,
            TotalPrice: val.TotalPrice,
            OriginalPrice: val.OriginalPrice,
            ArrearAmount: '',
            cardType: '产品',
            number: 1,
            totalAmount: child_amonunt,
            TotalAmount: child_amonunt,
            PayAmount: child_amonunt,
            discountPrice: 0,
            cardDeductionAmount: 0,
            cardDiscountPrice: 0,
            isCardType: 6,
            Remark: '',
            isModify: false,
            discount: 100,
            DeductionProjectAmount: 0,

            isUseMemberDiscount: isUseMemberDiscount,
            isShowSelectMemberAmout: isShowSelectMemberAmout,
            MemberDiscountPrice: MemberDiscountPrice,
            MemberPriceType: MemberPriceType,
            MemberPreferentialAmount: val.TotalPrice - child_amonunt,
            MemberPreferentialAmountTotal: val.TotalPrice - child_amonunt,
          };
        })
        .toArray();
      var project = Enumerable.from(row.PackageCardGoods.Project)
        .select((val) => {
          let child_amonunt = that.getPackageDetailMemberAmount(val.TotalPrice, memberDiscount);
          return {
            ID: val.ID,
            Name: val.Name,
            Alias: val.Alias,
            Amount: val.Amount,
            Price: val.Price,
            TotalPrice: val.TotalPrice,
            OriginalPrice: val.OriginalPrice,
            ArrearAmount: '',
            cardType: '项目',
            number: 1,
            totalAmount: child_amonunt,
            TotalAmount: child_amonunt,
            PayAmount: child_amonunt,
            discountPrice: 0,
            cardDeductionAmount: 0,
            cardDiscountPrice: 0,
            isCardType: 1,
            Remark: '',
            isModify: false,
            discount: 100,
            DeductionProjectAmount: 0,

            isUseMemberDiscount: isUseMemberDiscount,
            isShowSelectMemberAmout: isShowSelectMemberAmout,
            MemberDiscountPrice: MemberDiscountPrice,
            MemberPriceType: MemberPriceType,
            MemberPreferentialAmount: val.TotalPrice - child_amonunt,
            MemberPreferentialAmountTotal: val.TotalPrice - child_amonunt,
          };
        })
        .toArray();
      var generalCard = Enumerable.from(row.PackageCardGoods.GeneralCard)
        .select((val) => {
          let child_amonunt = that.getPackageDetailMemberAmount(val.TotalPrice, memberDiscount);
          return {
            ID: val.ID,
            Name: val.Name,
            Alias: val.Alias,
            Amount: val.Amount,
            Price: val.Price,
            TotalPrice: val.TotalPrice,
            OriginalPrice: val.OriginalPrice,
            ArrearAmount: '',
            cardType: '通用次卡',
            number: 1,
            totalAmount: child_amonunt,
            TotalAmount: child_amonunt,
            PayAmount: child_amonunt,
            discountPrice: 0,
            cardDeductionAmount: 0,
            cardDiscountPrice: 0,
            isCardType: 4,
            Remark: '',
            GeneralCardAmount: val.GeneralCardAmount,
            isModify: false,
            discount: 100,
            DeductionProjectAmount: 0,

            isUseMemberDiscount: isUseMemberDiscount,
            isShowSelectMemberAmout: isShowSelectMemberAmout,
            MemberDiscountPrice: MemberDiscountPrice,
            MemberPriceType: MemberPriceType,
            MemberPreferentialAmount: val.TotalPrice - child_amonunt,
            MemberPreferentialAmountTotal: val.TotalPrice - child_amonunt,
          };
        })
        .toArray();
      var timeCard = Enumerable.from(row.PackageCardGoods.TimeCard)
        .select((val) => {
          let child_amonunt = that.getPackageDetailMemberAmount(val.TotalPrice, memberDiscount);
          return {
            ID: val.ID,
            Name: val.Name,
            Alias: val.Alias,
            Amount: val.Amount,
            Price: val.Price,
            TotalPrice: val.TotalPrice,
            OriginalPrice: val.OriginalPrice,
            ArrearAmount: '',
            cardType: '时效卡',
            number: 1,
            totalAmount: child_amonunt,
            TotalAmount: child_amonunt,
            PayAmount: child_amonunt,
            discountPrice: 0,
            cardDeductionAmount: 0,
            cardDiscountPrice: 0,
            isCardType: 3,
            Remark: '',
            isModify: false,
            discount: 100,
            DeductionProjectAmount: 0,
            isUseMemberDiscount: isUseMemberDiscount,
            isShowSelectMemberAmout: isShowSelectMemberAmout,
            MemberDiscountPrice: MemberDiscountPrice,
            MemberPriceType: MemberPriceType,
            MemberPreferentialAmount: val.TotalPrice - child_amonunt,
            MemberPreferentialAmountTotal: val.TotalPrice - child_amonunt,
          };
        })
        .toArray();
      var savingCard = Enumerable.from(row.PackageCardGoods.SavingCard)
        .select((val) => ({
          ID: val.ID,
          Name: val.Name,
          Alias: val.Alias,
          Amount: val.Amount,
          Price: val.Price,
          TotalPrice: val.TotalPrice,
          OriginalPrice: val.OriginalPrice,
          ArrearAmount: '',
          cardType: '储值卡',
          number: 1,
          totalAmount: val.TotalPrice,
          PayAmount: val.TotalPrice,
          isCardType: 2,
          Remark: '',
          isModify: false,
          discount: 100,
          DeductionProjectAmount: 0,
        }))
        .toArray();

      largess = largess.concat(largessProduct, largessProject, largessGeneralCard, largessTimeCard, largessSavingCard);
      noLargess = noLargess.concat(product, project, generalCard, timeCard, savingCard);
      var data = {
        type: 5,
        Alias: row.Alias,
        ID: row.ID,
        IsAllowLargess: row.IsAllowLargess,
        IsLargess: false,
        Name: row.Name,
        Price: row.Price,
        totalPrice: Price,
        DeductPrice: row.DeductPrice,
        number: 1,
        discount: 100,
        Amount: Price,
        PayAmount: Price,
        TotalAmount: Price,
        ArrearAmount: 0,
        isModify: true,
        DeductionProjectAmount: 0,
        discountPrice: 0,
        CardDeductionAmount: 0,
        CardDiscountPrice: 0,
        largess: largess,
        noLargess: noLargess,
        isPackDetail: false,
        savingCardDeduction: [],
        handleTypeList: [],
        Remark: '',
        IsModifyPrice: row.IsModifyPrice,
        MemberPreferentialAmount, // 会员优惠金额
        MemberPreferentialAmountTotal: MemberPreferentialAmount, // 会员优惠金额
        isUseMemberDiscount,
        isShowSelectMemberAmout,
        MemberDiscountPrice,
        MemberPriceType,
        modifyAmount: row.DeductPrice,
      };
      // this.packageCardNoLargessMemberDiscountPrice(data, memberDiscount);
      that.packageCardHandlerList.forEach(function (item) {
        data.handleTypeList.push({
          ID: item.ID,
          Name: item.Name,
          Employee: [],
        });
      });

      that.selectPackageCard.push(data);
      that.payAmountData();
    },
    // 获取套餐卡明细会员优惠金额
    getPackageDetailMemberAmount(Price, memberDiscount) {
      let Amount = Price;
      if (memberDiscount) {
        if (memberDiscount.PriceType == 1) {
          Amount = parseFloat(memberDiscount.DiscountPrice) * parseFloat(Amount);
        }
        if (memberDiscount.PriceType == 2) {
          Amount = parseFloat(memberDiscount.DiscountPrice);
        }
      }
      return Amount;
    },
    //  套餐卡明细设置会员优惠
    // packageCardNoLargessMemberDiscountPrice (row, memberDiscount) {
    //   let isUseMemberDiscount = false;
    //   let isShowSelectMemberAmout = false;
    //   let MemberDiscountPrice = '';
    //   let MemberPriceType = '';
    //   if (memberDiscount) {
    //     isUseMemberDiscount = true;
    //     MemberDiscountPrice = memberDiscount.DiscountPrice;
    //     MemberPriceType = memberDiscount.PriceType;
    //     isShowSelectMemberAmout = true;
    //     let noLargess = row.noLargess.filter((i) => {
    //       return i.isCardType != 2;
    //     });
    //     let totalAmount = 0;
    //     row.noLargess.forEach((item, index) => {
    //       if (item.isCardType != 2) {
    //         if (index < noLargess.length - 1) {
    //           let childPrice = (((row.Amount - (row.Price - row.DeductPrice)) * item.TotalPrice) / row.DeductPrice).toFixed(2);

    //           let childMemberPreferentialAmount = parseFloat(item.TotalPrice * row.number) - childPrice;

    //           item.totalAmount = childPrice;
    //           item.MemberPreferentialAmount = childMemberPreferentialAmount;

    //           //累计总支付金额
    //           totalAmount = (parseFloat(totalAmount) + parseFloat(item.totalAmount)).toFixed(2);
    //           //改价支付金额=总支付金额
    //           item.PayAmount = item.totalAmount;
    //           //手动优惠金额 = 总金额-支付金额

    //           item.discountPrice = 0;
    //           item.cardDiscountPrice = 0; //卡优惠金额
    //           item.cardDeductionAmount = 0; //卡抵扣金额
    //           item.TotalAmount = item.totalAmount; //总支付金额【卡抵扣时使用】= 总支付金额
    //           item.discount = row.discount;

    //           item.isUseMemberDiscount = isUseMemberDiscount;
    //           item.isShowSelectMemberAmout = isShowSelectMemberAmout;
    //           item.MemberDiscountPrice = MemberDiscountPrice;
    //           item.MemberPriceType = MemberPriceType;
    //         } else {
    //           //总支付金额=改价金额-已分配的支付金额-储值卡金额
    //           item.totalAmount = parseFloat(row.Amount - totalAmount - (row.Price - row.DeductPrice)).toFixed(2);
    //           item.MemberPreferentialAmount = parseFloat(item.TotalPrice) - item.totalAmount;
    //           //改价支付金额=总支付金额
    //           item.PayAmount = item.totalAmount;
    //           //手动优惠金额 = 总金额-支付金额
    //           item.discountPrice = 0;
    //           item.cardDiscountPrice = 0; //卡优惠金额
    //           item.cardDeductionAmount = 0; //卡抵扣金额
    //           item.TotalAmount = item.totalAmount; //总支付金额【卡抵扣时使用】= 总支付金额
    //           item.discount = row.discount;

    //           item.isUseMemberDiscount = isUseMemberDiscount;
    //           item.isShowSelectMemberAmout = isShowSelectMemberAmout;
    //           item.MemberDiscountPrice = MemberDiscountPrice;
    //           item.MemberPriceType = MemberPriceType;
    //         }
    //       } else {
    //         item.number = row.number;
    //         item.totalAmount = (row.number * item.TotalPrice).toFixed(2);
    //         item.TotalAmount = item.totalAmount;
    //         item.cardDeductionAmount = 0;
    //         item.cardDiscountPrice = 0;
    //         item.discountPrice = 0;
    //         item.discount = 100;
    //         item.ArrearAmount = '';
    //         item.PayAmount = (row.number * item.TotalPrice).toFixed(2);
    //       }
    //     });
    //   }
    // },
    productChange(row) {
      if (this.customerID) {
        this.saleGoods_CustomerDiscount(row.ID).then((res) => {
          this.productChangePush(row, res);
        });
      } else {
        this.productChangePush(row);
      }
    },
    // 产品选择
    productChangePush: function (row, memberDiscount) {
      var that = this;

      let isUseMemberDiscount = false;
      let isShowSelectMemberAmout = false;
      let MemberPreferentialAmount = '';
      let MemberDiscountPrice = '';
      let MemberPriceType = '';
      let Price = row.Price;
      if (memberDiscount) {
        isUseMemberDiscount = true;
        MemberDiscountPrice = memberDiscount.DiscountPrice;
        MemberPriceType = memberDiscount.PriceType;
        isShowSelectMemberAmout = true;
        if (memberDiscount.PriceType == 1) {
          Price = parseFloat(row.Price) * parseFloat(memberDiscount.DiscountPrice);
          MemberPreferentialAmount = parseFloat(row.Price - Price).toFixed(2);
        }
        if (memberDiscount.PriceType == 2) {
          Price = parseFloat(memberDiscount.DiscountPrice);
          MemberPreferentialAmount = parseFloat(row.Price - Price).toFixed(2);
        }
      }
      var data = {
        type: 6,
        Alias: row.Alias,
        ID: row.ID,
        IsAllowLargess: row.IsAllowLargess,
        IsLargess: false,
        Name: row.Name,
        Price: row.Price,
        totalPrice: Price,
        number: 1,
        discount: 100,
        Amount: Price,
        PayAmount: Price,
        TotalAmount: Price,
        ArrearAmount: 0,
        isModify: true,
        DeductionProjectAmount: 0,
        discountPrice: 0,
        CardDeductionAmount: 0,
        CardDiscountPrice: 0,
        savingCardDeduction: [],
        handleTypeList: [],
        Remark: '',
        IsModifyPrice: row.IsModifyPrice,
        MemberPreferentialAmount, // 会员优惠金额
        MemberPreferentialAmountTotal: MemberPreferentialAmount, // 会员优惠金额
        isUseMemberDiscount,
        isShowSelectMemberAmout,
        MemberDiscountPrice,
        MemberPriceType,
      };
      that.productHandlerList.forEach(function (item) {
        data.handleTypeList.push({
          ID: item.ID,
          Name: item.Name,
          Employee: [],
        });
      });

      that.selectProduct.push(data);
      that.payAmountData();
    },
    // 经手人
    employeeHandleClick: function (type, row, item, index) {
      var that = this;
      var emplayee = [];
      that.tabHandle = '0';
      switch (type) {
        case 1:
          that.handlerList = that.projectHandlerList;
          break;
        case 2:
          that.handlerList = that.savingCardHandlerList;
          break;
        case 3:
          that.handlerList = that.timeCardHandlerList;
          break;
        case 4:
          that.handlerList = that.generalCardHandlerList;
          break;
        case 5:
          that.handlerList = that.packageCardHandlerList;
          break;
        case 6:
          that.handlerList = that.productHandlerList;
          break;
      }
      item.handleTypeList.forEach(function (hand) {
        hand.Employee.forEach(function (emp) {
          emplayee.push({ ID: emp.ID, Discount: emp.Discount });
        });
      });
      that.handlerList.forEach(function (handler) {
        handler.Employee.forEach(function (emp) {
          emp.Checked = false;
          emp.Discount = '';
          emplayee.forEach(function (i) {
            if (emp.ID == i.ID) {
              emp.Checked = true;
              emp.Discount = i.Discount;
            }
          });
        });
      });
      that.type = type;
      that.selectGoods = row;
      that.goodsIndex = index;
      that.dialogVisible = true;
    },
    // 经手人确认选择
    submitHandleClick: function () {
      var that = this;
      var goodsHandlerList = JSON.parse(JSON.stringify(that.handlerList));
      if (
        goodsHandlerList.some((item) => {
          return (
            item.Employee.reduce((pre, pri) => {
              return pre + Number(pri.Discount);
            }, 0) > 100
          );
        })
      ) {
        that.$message.error('比例总和不能超过100%');
        return;
      }
      goodsHandlerList.forEach(function (item) {
        item.Employee = Enumerable.from(item.Employee)
          .where(function (i) {
            return i.Checked;
          })
          .toArray();
      });

      that.selectGoods[that.goodsIndex].handleTypeList = goodsHandlerList;
      switch (that.type) {
        case 1:
          that.selectProject = that.selectGoods;
          break;
        case 2:
          that.selectSavingCard = that.selectGoods;
          break;
        case 3:
          that.selectTimeCard = that.selectGoods;
          break;
        case 4:
          that.selectGeneralCard = that.selectGoods;
          break;
        case 5:
          that.selectPackageCard = that.selectGoods;
          break;
        case 6:
          that.selectProduct = that.selectGoods;
          break;
      }
      that.dialogVisible = false;
    },
    // 删除经手人
    removeHandleClick: function (item, index) {
      item.Employee.splice(index, 1);
    },
    // 删除项目
    removeClick: function (type, index, item) {
      var that = this;
      switch (type) {
        case 1:
          that.selectProject.splice(index, 1);
          break;
        case 2:
          that.selectSavingCard.splice(index, 1);
          break;
        case 3:
          that.selectTimeCard.splice(index, 1);
          break;
        case 4:
          that.selectGeneralCard.splice(index, 1);
          break;
        case 5:
          that.selectPackageCard.splice(index, 1);
          break;
        case 6:
          that.selectProduct.splice(index, 1);
          break;
      }
      if (item.savingCardDeduction != undefined) {
        that.deductionReset(item);
      }
      that.payAmountData();
    },
    // 经手人选择
    handlerCheckedChange: function (row, item) {
      let checkedArr = row.filter((i) => {
        return i.Checked;
      });
      row.forEach((val) => {
        if (val.Checked) {
          val.Discount = Math.floor(100 / checkedArr.length);
          if (val.EmployeeID == checkedArr[checkedArr.length - 1].EmployeeID) {
            val.Discount = Math.ceil(100 / checkedArr.length);
          }
        }
      });
      var discount = 0;
      var employee = Enumerable.from(row)
        .where(function (i) {
          return i.Checked;
        })
        .toArray();
      employee.forEach(function (emp) {
        var Discount = emp.Discount;
        if (Discount == '') {
          Discount = 0;
        }
        discount = parseFloat(discount) + parseFloat(Discount);
      });
      if (!item.Checked) {
        item.Discount = '';
      } else {
        if (item.Discount == '') {
          if (discount > 100) {
            item.Discount = 0;
          } else {
            item.Discount = 100 - discount;
          }
        }
      }
    },
    // 百分比
    handlerPercentChange: function (row, item, type) {
      var that = this;
      var discount = 0;
      if (item.Discount != '') {
        item.Discount = parseFloat(item.Discount);
      }
      if (type !== 'dialog') {
        if (item.Discount > 100) {
          item.Discount = 100;
        }
      }
      var employee = Enumerable.from(row)
        .where(function (i) {
          return i.Checked;
        })
        .toArray();
      employee.forEach(function (emp) {
        var Discount = emp.Discount;
        if (Discount == '') {
          Discount = 0;
        }
        discount = parseFloat(discount) + parseFloat(Discount);
      });
      if (type !== 'dialog') {
        if (parseFloat(discount) > 100) {
          item.Discount = 100 - (discount - item.Discount);
          that.$message.error('比例总和不能超过100%');
        }
      }
    },
    // 储值卡抵扣(部分商品)
    savingCardDeductionClick: function (type, row, item, index) {
      var that = this;
      item.PayAmount = parseFloat(item.PayAmount) + (parseFloat(item.ArrearAmount) || 0);
      item.ArrearAmount = 0;
      that.payAmountData();
      that.savingCardAll = [];
      switch (type) {
        case 1:
          that.showModifyPrices = that.SellPermission.ModifyPrices_SaleProject || that.SellPermission.isSaleBilling_ModifyPrices_SaleProject;
          that.savingCardProjecctData(item);
          break;
        case 3:
          that.showModifyPrices = that.SellPermission.ModifyPrices_SaleTimeCard || that.SellPermission.isSaleBilling_ModifyPrices_SaleProject;
          that.savingCardTimeCardData(item);
          break;
        case 4:
          that.showModifyPrices = that.SellPermission.ModifyPrices_SaleGeneralCard || that.SellPermission.isSaleBilling_ModifyPrices_SaleGeneralCard;
          that.savingCardGeneralCardData(item);
          break;
        case 5:
          that.showModifyPrices = that.SellPermission.ModifyPrices_SalePackageCard || that.SellPermission.isSaleBilling_ModifyPrices_SalePackageCard;
          that.savingCardPackageCardData(item);
          break;
        case 6:
          that.showModifyPrices = that.SellPermission.ModifyPrices_SaleProduct || that.SellPermission.isSaleBilling_ModifyPrices_SaleProduct;
          that.savingCardProductData(item);
          break;
      }
      that.type = type;
      that.selectGoods = row;
      that.selectGood = Object.assign({}, item);
      that.goodsIndex = index;
      item.ArrearAmount = 0;
      if (type == 5) {
        item.noLargess.forEach(function (i) {
          if (i.isCardType != 2) {
            i.ArrearAmount = '';
            i.PayAmount = (i.totalAmount - i.cardDeductionAmount).toFixed(2);
          } else {
            i.ArrearAmount = '';
          }
        });
        that.dialogDeductionPackage = true;
      } else {
        that.dialogDeduction = true;
      }
    },
    // 折扣
    discountChange: function (row) {
      var that = this;
      //清除选中的储值卡
      that.savingCardAll.forEach(function (item) {
        item.checked = false;
        item.TotalAmount = '';
        that.savingCardCheckedChange(row, item);
      });
      if (row.type == 5) {
        //计算金额
        row.modifyAmount = ((row.DeductPrice * row.number * row.discount) / 100).toFixed(2);
        //计算优惠
        row.discountPrice = (row.DeductPrice * row.number - row.modifyAmount).toFixed(2);
      } else {
        //计算金额
        row.Amount = ((row.Price * row.number * row.discount) / 100).toFixed(2);
        //计算优惠
        row.discountPrice = (row.totalPrice - row.Amount).toFixed(2);
      }
      that.payPriceData(row);
    },
    // 折后金额
    amountChange: function (row) {
      var that = this;
      //清除选中的储值卡
      that.savingCardAll.forEach(function (item) {
        item.checked = false;
        item.TotalAmount = '';
        that.savingCardCheckedChange(row, item);
      });
      //计算折扣
      row.discount = parseInt((row.Amount / row.Price / row.number) * 100);
      //计算优惠
      row.discountPrice = (row.totalPrice - row.Amount).toFixed(2);
      that.payPriceData(row);
    },
    // 改价
    modifyChange: function (row) {
      var that = this;
      row.isModify = !row.isModify;
      //清除选中的储值卡
      that.savingCardAll.forEach(function (item) {
        item.checked = false;
        item.TotalAmount = '';
        that.savingCardCheckedChange(that.selectGood, item);
      });
      if (row.Amount == '') {
        row.Amount = 0;
      }
      if (this.employeeDiscount && row.discount < that.employeeDiscount * 100) {
        that.$message.error('折扣不能小于员工最低折扣');
        row.discount = that.employeeDiscount * 100;
        //计算金额
        row.Amount = ((row.Price * row.number * row.discount) / 100).toFixed(2);
      }

      row.discountPrice = (row.totalPrice - row.Amount).toFixed(2);
      that.payPriceData(row);
    },
    // 储值卡抵扣选择
    savingCardCheckedChange: function (row, item) {
      var that = this;
      var amount;
      if (item.checked) {
        if (item.PriceType == 1) {
          var payAmount = (
            row.totalPrice -
            row.DeductionProjectAmount -
            row.CardDeductionAmount -
            row.ArrearAmount +
            parseFloat(item.cardDiscountPrice) +
            parseFloat(item.cardDeductionAmount)
          ).toFixed(2);
          amount = ((item.DiscountPrice / 10) * payAmount).toFixed(2);
          if (parseFloat(amount) > parseFloat(item.TotalBalance)) {
            item.TotalAmount = item.TotalBalance;
          } else {
            item.TotalAmount = amount;
          }
          item.PreferentialAmount = (item.TotalAmount / (item.DiscountPrice / 10)).toFixed(2);
        } else {
          if (item.DiscountPrice == 0) {
            amount = parseFloat(row.PayAmount).toFixed(2);
          }
          else {
            amount = ((row.PayAmount * item.DiscountPrice) / row.Price).toFixed(2);  
          }
          
          if (parseFloat(amount) > parseFloat(item.TotalBalance)) {
            item.TotalAmount = item.TotalBalance;
          } else {
            
            if (item.DiscountPrice == 0) {
              item.TotalAmount = 0;
             }
            else {
              item.TotalAmount = ((row.PayAmount / (row.Price / item.DiscountPrice)) || 0).toFixed(2);  
            }
          }
          if  (item.DiscountPrice == 0) {
            item.PreferentialAmount = row.PayAmount;
          } else {
            item.PreferentialAmount = ((row.Price / item.DiscountPrice) * item.TotalAmount).toFixed(2);  
          }
          
        }
        item.cardDeductionAmount = item.TotalAmount;
        if (item.PreferentialAmount == 0) {
          item.cardDiscountPrice = parseFloat(item.PreferentialAmount ).toFixed(2);
        }
        else {
          item.cardDiscountPrice = (item.PreferentialAmount - item.TotalAmount).toFixed(2);  
        }
        

        item.TotalPrice = (item.TotalBalance - item.TotalAmount).toFixed(2);
      } else {
        item.TotalAmount = '';
        item.cardDeductionAmount = 0;
        item.cardDiscountPrice = 0;
        item.PreferentialAmount = 0;
        item.TotalPrice = item.TotalBalance;
      }
      that.savingCardDeductionPrice(row);
      if (parseFloat(amount) < parseFloat(item.TotalBalance) && parseFloat(row.PayAmount) < 0.1) {
        item.cardDiscountPrice = (parseFloat(item.cardDiscountPrice) + parseFloat(row.PayAmount)).toFixed(2);
        row.PayAmount = 0;
        that.savingCardDeductionPrice(row);
      }
    },
    // 储值卡抵扣金额变化
    savingCardPriceChange: function (row, item) {
      var that = this;
      var amount;
      if (item.PriceType == 1) {
        let payAmount = (
          row.totalPrice -
          row.DeductionProjectAmount -
          row.CardDeductionAmount -
          row.ArrearAmount +
          parseFloat(item.cardDiscountPrice) +
          parseFloat(item.cardDeductionAmount)
        ).toFixed(2);
        amount = ((item.DiscountPrice / 10) * payAmount).toFixed(2);
        if (parseFloat(amount) > parseFloat(item.TotalBalance)) {
          if (parseFloat(item.TotalAmount) > parseFloat(item.TotalBalance)) {
            item.TotalAmount = item.TotalBalance;
            that.$message.error('卡扣金额不能大于' + item.TotalBalance + '元');
          }
        } else {
          if (parseFloat(item.TotalAmount) > parseFloat(amount)) {
            item.TotalAmount = amount;
            that.$message.error('卡扣金额不能大于' + amount + '元');
          }
        }
        item.PreferentialAmount = (item.TotalAmount / (item.DiscountPrice / 10)).toFixed(2);
      } else {
        var payAmount = (
          row.totalPrice -
          row.DeductionProjectAmount -
          row.CardDeductionAmount -
          row.ArrearAmount +
          parseFloat(item.cardDiscountPrice) +
          parseFloat(item.cardDeductionAmount)
        ).toFixed(2);
        amount = ((payAmount * item.DiscountPrice) / row.Price).toFixed(2);
        if (parseFloat(amount) > parseFloat(item.TotalBalance)) {
          if (parseFloat(item.TotalAmount) > parseFloat(item.TotalBalance)) {
            item.TotalAmount = item.TotalBalance;
            that.$message.error('卡扣金额不能大于' + item.TotalBalance + '元');
          }
        } else {
          if (parseFloat(item.TotalAmount) > parseFloat(amount)) {
            item.TotalAmount = amount;
            that.$message.error('卡扣金额不能大于' + amount + '元');
          }
        }
        item.PreferentialAmount = ((row.Price / item.DiscountPrice) * item.TotalAmount).toFixed(2);
      }
      item.cardDiscountPrice = (item.PreferentialAmount - item.TotalAmount).toFixed(2);
      item.TotalPrice = (item.TotalBalance - item.TotalAmount).toFixed(2);
      item.cardDeductionAmount = item.TotalAmount;
      that.savingCardDeductionPrice(row);
      if (parseFloat(amount) < parseFloat(item.TotalBalance) && parseFloat(row.PayAmount) < 0.1) {
        item.cardDiscountPrice = (parseFloat(item.cardDiscountPrice) + parseFloat(row.PayAmount)).toFixed(2);
        row.PayAmount = 0;
        that.savingCardDeductionPrice(row);
      }
    },
    // 套餐卡储值卡抵扣选择
    packageSavingCardCheckedChange: function (row, item) {
      var that = this;
      if (item.checked) {
        if (item.PriceType == 1) {
          let payAmount = (
            row.totalPrice -
            row.DeductionProjectAmount -
            row.CardDeductionAmount -
            (row.Price - row.DeductPrice) * row.number -
            row.ArrearAmount +
            parseFloat(item.cardDiscountPrice) +
            parseFloat(item.cardDeductionAmount)
          ).toFixed(2);
          let amount = ((item.DiscountPrice / 10) * payAmount).toFixed(2);
          if (parseFloat(amount) > parseFloat(item.TotalBalance)) {
            item.TotalAmount = item.TotalBalance;
          } else {
            item.TotalAmount = amount;
          }
          item.PreferentialAmount = (item.TotalAmount / (item.DiscountPrice / 10)).toFixed(2);
        } else {
          var payAmount = (
            row.totalPrice -
            row.DeductionProjectAmount -
            row.CardDeductionAmount -
            row.ArrearAmount -
            (row.Price - row.DeductPrice) * row.number
          ).toFixed(2);
          console.log('🚀 ~ else payAmount:', payAmount);
          if (payAmount / (row.DeductPrice / item.DiscountPrice) > parseFloat(item.TotalBalance)) {
            item.TotalAmount = item.TotalBalance;
          } else {
            item.TotalAmount = (payAmount / (row.DeductPrice / item.DiscountPrice)).toFixed(2);
          }

          item.PreferentialAmount = ((row.DeductPrice / item.DiscountPrice) * item.TotalAmount).toFixed(2);
        }
        item.cardDeductionAmount = item.TotalAmount;
        item.cardDiscountPrice = (item.PreferentialAmount - item.TotalAmount).toFixed(2);
        item.TotalPrice = (item.TotalBalance - item.TotalAmount).toFixed(2);
      } else {
        item.TotalAmount = '';
        item.cardDeductionAmount = 0;
        item.cardDiscountPrice = 0;
        item.PreferentialAmount = 0;
        item.TotalPrice = item.TotalBalance;
      }
      that.savingCardDeductionPrice(row);
    },
    // 储值卡抵扣确认
    submitSavingCard: function () {
      let that = this;
      //清除选中的储值卡
      // that.savingCardAll.forEach(function (item) {
      //   item.checked = false;
      //   item.TotalAmount = '';
      //   that.savingCardCheckedChange(that.selectGood, item);
      // });

      if (that.employeeDiscount && that.selectGood && that.selectGood.discount < that.employeeDiscount * 100) {
        that.$message.error('折扣不能小于员工最低折扣');
        that.selectGood.discount = that.employeeDiscount * 100;
        //计算金额
        that.selectGood.Amount = ((that.selectGood.Price * that.selectGood.number * that.selectGood.discount) / 100).toFixed(2);
        that.selectGood.discountPrice = (that.selectGood.totalPrice - that.selectGood.Amount).toFixed(2);
        that.payPriceData(that.selectGood);
      }

      that.selectGoods[that.goodsIndex] = that.selectGood;

      var savingCardAll = that.savingCardAll;
      that.savingCardSomeGoods.forEach(function (item) {
        that.savingCardAll.forEach(function (sav) {
          if (item.ID == sav.ID) {
            item.TotalBalance = sav.TotalPrice;
          }
        });
      });
      savingCardAll = Enumerable.from(savingCardAll)
        .where(function (i) {
          return i.checked;
        })
        .toArray();

      console.log('🚀 ~ savingCardAll:', savingCardAll);
      that.selectGoods[that.goodsIndex].savingCardDeduction = savingCardAll;
      switch (that.type) {
        case 1:
          that.selectProject = that.selectGoods;
          break;
        case 2:
          that.selectSavingCard = that.selectGoods;
          break;
        case 3:
          that.selectTimeCard = that.selectGoods;
          break;
        case 4:
          that.selectGeneralCard = that.selectGoods;
          break;
        case 5:
          that.selectPackageCard = that.selectGoods;
          break;
        case 6:
          that.selectProduct = that.selectGoods;
          break;
      }
      that.dialogDeduction = false;
      that.payAmountData();
    },

    // 套餐卡折后金额
    packageAmountChange: function (row) {
      var that = this;
      //清除选中的储值卡
      that.savingCardAll.forEach(function (item) {
        item.checked = false;
        item.TotalAmount = '';
        that.savingCardCheckedChange(row, item);
      });
      if (row.type == 5) {
        //计算折扣
        if (row.modifyAmount == 0 || row.modifyAmount == '') {
          row.discount = 0;
        } else {
          row.discount = parseInt((parseFloat(row.modifyAmount || 0) / (row.DeductPrice * row.number)) * 100);
        }
        //计算优惠
        row.discountPrice = (row.DeductPrice * row.number - row.modifyAmount).toFixed(2);
      } else {
        //计算折扣
        row.discount = parseInt((row.Amount / row.Price / row.number) * 100);
        //计算优惠
        row.discountPrice = (row.totalPrice - row.Amount).toFixed(2);
      }
      that.payPriceData(row);
    },
    // 套餐卡改价
    packageModifyChange: function (row) {
      var that = this;
      if (row.DeductPrice == 0) {
        row.Amount = (row.totalPrice - row.DeductPrice * row.number).toFixed(2);
        row.discount = parseInt((row.Amount / row.totalPrice) * 100);
        row.modifyAmount = row.DeductPrice;
        row.PayAmount = row.Price * row.number;
        row.DeductionProjectAmount = row.DeductionProjectAmount - row.discountPrice;
        row.discountPrice = 0;
        that.$message.error('此套餐卡暂不可修改价格');
        return;
      }
      //判断调整价格不能低于储值卡金额
      if (row.Amount < row.totalPrice - row.DeductPrice * row.number) {
        row.Amount = (row.totalPrice - row.DeductPrice * row.number).toFixed(2);
        row.discount = parseInt((row.Amount / row.totalPrice) * 100);
        that.$message.error('调整价格不能低于储值卡金额');
      }

      if (row.discount < that.employeeDiscount * 100) {
        that.$message.error('折扣不能小于员工最低折扣');
        row.discount = that.employeeDiscount * 100;
        //计算金额
        row.modifyAmount = ((row.DeductPrice * row.number * row.discount) / 100).toFixed(2);
        row.Amount = parseFloat((row.totalPrice - row.DeductPrice) * row.number + parseFloat(row.modifyAmount)).toFixed(2);
      }
      row.Amount = parseFloat((row.Price - row.DeductPrice) * row.number + parseFloat(row.modifyAmount)).toFixed(2);
      //手动调整价格，配个到套餐卡明细
      let detailPayAmount = row.modifyAmount;
      var noLargess = Enumerable.from(row.noLargess)
        .where(function (i) {
          return i.isCardType != 2;
        })
        .toArray();
      row.noLargess = JSON.parse(JSON.stringify(row.noLargess));
      row.noLargess.forEach(function (item, i) {
        if (item.isCardType != 2) {
          if (i < noLargess.length - 1) {
            item.totalAmount = (item.TotalPrice * item.number * row.discount) / 100;
            //改价支付金额=总支付金额
            item.PayAmount = item.totalAmount;
            item.discount = row.discount;
            //手动优惠金额 = 总金额-支付金额
            item.discountPrice = (item.TotalPrice * row.number - item.totalAmount).toFixed(2);
            item.TotalAmount = item.totalAmount; //总支付金额【卡抵扣时使用】= 总支付金额
            item.MemberPreferentialAmount = 0;
            item.cardDiscountPrice = 0; //卡优惠金额
            item.cardDeductionAmount = 0; //卡抵扣金额
            detailPayAmount = detailPayAmount - item.totalAmount;
          } else {
            //总支付金额=改价金额-已分配的支付金额-储值卡金额
            item.totalAmount = detailPayAmount;
            //改价支付金额=总支付金额
            item.PayAmount = item.totalAmount;
            //手动优惠金额 = 总金额-支付金额
            item.discountPrice = (item.TotalPrice * row.number - item.totalAmount).toFixed(2);
            item.discount = row.discount;
            item.TotalAmount = item.totalAmount; //总支付金额【卡抵扣时使用】= 总支付金额
            item.cardDiscountPrice = 0; //卡优惠金额
            item.cardDeductionAmount = 0; //卡抵扣金额
            item.MemberPreferentialAmount = 0;
          }
        } else {
          item.totalAmount = item.TotalPrice * row.number;
        }
      });

      row.isModify = !row.isModify;
      if (row.Amount == '') {
        row.Amount = 0;
      }
      // row.discountPrice = (row.totalPrice - row.Amount).toFixed(2);
      row.discountPrice = (row.DeductPrice * row.number - row.modifyAmount).toFixed(2);
      that.payPriceData(row);
    },
    // 套餐卡储值卡抵扣金额变化
    packageSavingCardPriceChange: function (row, item) {
      var that = this;
      if (item.PriceType == 1) {
        let payAmount = (
          row.totalPrice -
          row.DeductionProjectAmount -
          row.CardDeductionAmount -
          row.ArrearAmount -
          (row.Price - row.DeductPrice) * row.number +
          parseFloat(item.cardDiscountPrice) +
          parseFloat(item.cardDeductionAmount)
        ).toFixed(2);
        let amount = ((item.DiscountPrice / 10) * payAmount).toFixed(2);
        if (parseFloat(amount) > parseFloat(item.TotalBalance)) {
          if (parseFloat(item.TotalAmount) > parseFloat(item.TotalBalance)) {
            item.TotalAmount = item.TotalBalance;
            that.$message.error('卡扣金额不能大于' + item.TotalBalance + '元');
          }
        } else {
          if (parseFloat(item.TotalAmount) > parseFloat(amount)) {
            item.TotalAmount = amount;
            that.$message.error('卡扣金额不能大于' + amount + '元');
          }
        }
        item.PreferentialAmount = (item.TotalAmount / (item.DiscountPrice / 10)).toFixed(2);
      } else {
        var payAmount = (
          row.totalPrice -
          row.DeductionProjectAmount -
          row.CardDeductionAmount -
          row.ArrearAmount -
          (row.Price - row.DeductPrice) * row.number +
          parseFloat(item.cardDiscountPrice) +
          parseFloat(item.cardDeductionAmount)
        ).toFixed(2);
        var amount = (payAmount / (row.DeductPrice / item.DiscountPrice)).toFixed(2);
        if (payAmount / (row.DeductPrice / item.DiscountPrice) > parseFloat(item.TotalBalance)) {
          if (parseFloat(item.TotalAmount) > parseFloat(item.TotalBalance)) {
            item.TotalAmount = item.TotalBalance;
            that.$message.error('卡扣金额不能大于' + item.TotalBalance + '元');
          }
        } else {
          if (parseFloat(item.TotalAmount) > parseFloat(amount)) {
            item.TotalAmount = payAmount / (row.DeductPrice / item.DiscountPrice);
            that.$message.error('卡扣金额不能大于' + amount + '元');
          }
        }
        item.PreferentialAmount = ((row.DeductPrice / item.DiscountPrice) * item.TotalAmount).toFixed(2);
      }
      item.cardDiscountPrice = (item.PreferentialAmount - item.TotalAmount).toFixed(2);
      item.TotalPrice = (item.TotalBalance - item.TotalAmount).toFixed(2);
      item.cardDeductionAmount = item.TotalAmount;
      that.savingCardDeductionPrice(row);
    },
    // 储值卡抵扣总金额
    savingCardDeductionPrice: function (row) {
      var that = this;
      var CardDeductionAmount = 0;
      var cardDiscountPrice = 0;
      var savingCardAll = Enumerable.from(that.savingCardAll)
        .where(function (i) {
          return i.checked;
        })
        .toArray();
      savingCardAll.forEach(function (item) {
        var cardDeductionAmount = parseFloat(item.cardDeductionAmount) || 0;
        if (cardDeductionAmount == '') {
          cardDeductionAmount = 0;
        }
        CardDeductionAmount = (parseFloat(CardDeductionAmount) + cardDeductionAmount).toFixed(2);
        cardDiscountPrice = (parseFloat(cardDiscountPrice) + parseFloat(item.cardDiscountPrice)).toFixed(2);
      });
      row.CardDeductionAmount = CardDeductionAmount;
      row.CardDiscountPrice = cardDiscountPrice;
      that.payPriceData(row);
      if (row.type == 5) {
        that.packageSavingCardDeductionAmount(row);
      }
    },
    // 套餐卡抵扣明细金额变化
    packageSavingCardDeductionAmount: function (row) {
      var amount = 0;
      var cardDeductionAmount = 0;
      var cardDiscountPrice = 0;
      var noLargess = Enumerable.from(row.noLargess)
        .where(function (i) {
          if (i.isCardType != 2) {
            amount = (parseFloat(amount) + parseFloat(i.TotalAmount)).toFixed(2);
          }
          return i.isCardType != 2;
        })
        .toArray();
      row.noLargess = JSON.parse(JSON.stringify(row.noLargess));
      row.noLargess.forEach(function (item, i) {
        if (item.isCardType != 2) {
          if (parseFloat(amount) != 0) {
            if (i < noLargess.length - 1) {
              //卡抵扣金额 =（ 商品支付金额*卡扣金额）/非储值卡商品总支付金额
              item.cardDeductionAmount = ((item.TotalAmount * row.CardDeductionAmount) / amount).toFixed(2);
              //累计卡抵扣金额= 累计卡抵扣金额+卡抵扣金额
              cardDeductionAmount = (parseFloat(cardDeductionAmount) + parseFloat(item.cardDeductionAmount)).toFixed(2);
              //卡优惠金额 = （ 商品支付金额*卡优惠金额）/非储值卡商品总支付金额
              item.cardDiscountPrice = ((item.TotalAmount * row.CardDiscountPrice) / amount).toFixed(2);
              //累计卡优惠金额= 累计卡优惠金额+卡优惠金额
              cardDiscountPrice = (parseFloat(cardDiscountPrice) + parseFloat(item.cardDiscountPrice)).toFixed(2);
              //卡支付金额 = 手动改价后的支付金额-卡优惠金额
              item.totalAmount = (item.TotalAmount - item.cardDiscountPrice).toFixed(2);
              //支付金额= 手动改价后的支付金额-卡扣金额
              item.PayAmount = (item.totalAmount - item.cardDeductionAmount).toFixed(2);
            } else {
              item.cardDeductionAmount = (row.CardDeductionAmount - cardDeductionAmount).toFixed(2);
              item.cardDiscountPrice = (row.CardDiscountPrice - cardDiscountPrice).toFixed(2);
              //卡支付金额 = 手动改价后的支付金额-卡优惠金额
              item.totalAmount = (item.TotalAmount - item.cardDiscountPrice).toFixed(2);
              //支付金额= 手动改价后的支付金额-卡扣金额
              item.PayAmount = (item.totalAmount - item.cardDeductionAmount).toFixed(2);
            }
          } else {
            item.totalAmount = 0;
          }
        } else {
          item.totalAmount = item.TotalPrice * item.number;
        }
      });
    },
    // 套餐卡明细欠款
    packageArrearChange: function (row, item) {
      var that = this;
      var ArrearAmount = 0;
      if (item.isCardType == 2) {
        if (parseFloat(item.ArrearAmount) > parseFloat(item.totalAmount)) {
          item.ArrearAmount = item.totalAmount;
          that.$message.error('欠款金额不能大于支付金额');
        }
        item.PayAmount = (item.totalAmount - item.ArrearAmount).toFixed(2);
      } else {
        if (parseFloat(item.ArrearAmount) > parseFloat((item.totalAmount - item.cardDeductionAmount).toFixed(2))) {
          item.ArrearAmount = (item.totalAmount - item.cardDeductionAmount).toFixed(2);
          that.$message.error('欠款金额不能大于支付金额');
        }
        item.PayAmount = (item.totalAmount - item.cardDeductionAmount - item.ArrearAmount).toFixed(2);
      }

      row.noLargess.forEach(function (largess) {
        var arrearAmount = parseFloat(largess.ArrearAmount) || 0;
        ArrearAmount = (parseFloat(ArrearAmount) + arrearAmount).toFixed(2);
      });
      row.ArrearAmount = ArrearAmount;
      that.payPriceData(row);
      that.payAmountData();
    },

    // 储值卡抵扣确认(套餐卡)
    submitSavingCardPackage: function () {
      var that = this;
      that.selectGoods[that.goodsIndex] = that.selectGood;
      var savingCardAll = that.savingCardAll;
      that.savingCardSomeGoods.forEach(function (item) {
        that.savingCardAll.forEach(function (sav) {
          if (item.ID == sav.ID) {
            item.TotalBalance = sav.TotalPrice;
          }
        });
      });
      savingCardAll = Enumerable.from(savingCardAll)
        .where(function (i) {
          return i.checked;
        })
        .toArray();
      that.selectGoods[that.goodsIndex].savingCardDeduction = savingCardAll;

      that.selectPackageCard = that.selectGoods;

      that.dialogDeductionPackage = false;
      that.payAmountData();
    },
    // 储值卡抵扣选择(通用)
    savingCheckedAllChange: function (item) {
      var that = this;
      var PayAmount = 0;
      PayAmount = (parseFloat(that.PayAmount) - parseFloat(that.savingCardPrice) + parseFloat(that.PayCashAmount)).toFixed(2);
      if (item.checked) {
        if (parseFloat(item.TotalBalance) < parseFloat(PayAmount)) {
          item.TotalAmount = item.TotalBalance;
        } else {
          item.TotalAmount = PayAmount;
        }
        item.TotalPrice = (item.TotalBalance - item.TotalAmount).toFixed(2);
      } else {
        item.TotalAmount = '';
        item.TotalPrice = item.TotalBalance;
      }
      item.cardDeductionAmount = item.TotalAmount;
      that.savingDeductionPriceAll();
    },
    // 储值卡抵扣金额变化（通用）
    savingPriceAllChange: function (item) {
      var that = this;
      var PayAmount = 0;
      PayAmount = (
        parseFloat(that.PayAmount) -
        parseFloat(that.savingCardPrice) +
        parseFloat(item.cardDeductionAmount) +
        parseFloat(that.PayCashAmount)
      ).toFixed(2);
      if (parseFloat(item.TotalBalance) < parseFloat(PayAmount)) {
        if (parseFloat(item.TotalAmount) > parseFloat(item.TotalBalance)) {
          item.TotalAmount = item.TotalBalance;
          that.$message.error('卡扣金额不能大于' + item.TotalBalance + '元');
        }
      } else {
        if (parseFloat(item.TotalAmount) > parseFloat(PayAmount)) {
          item.TotalAmount = PayAmount;
          that.$message.error('卡扣金额不能大于' + PayAmount + '元');
        }
      }
      item.cardDeductionAmount = item.TotalAmount;
      item.TotalPrice = item.TotalBalance - item.TotalAmount;
      that.savingDeductionPriceAll();
    },
    // 储值卡抵扣金额(通用)
    savingDeductionPriceAll: function () {
      var that = this;

      var cardDeductionAmount = 0;
      var savingCardAllGoods = Enumerable.from(that.savingCardAllGoods)
        .where(function (i) {
          return i.checked;
        })
        .toArray();
      savingCardAllGoods.forEach(function (item) {
        cardDeductionAmount = (parseFloat(cardDeductionAmount) + (parseFloat(item.TotalAmount) || 0)).toFixed(2);
      });
      that.cardDeductionAmount = cardDeductionAmount;
      that.payAmountData();
    },
    // 数量
    numberChange: function (row) {
      var that = this;
      row.discount = 100;
      row.discountPrice = 0;
      row.ArrearAmount = 0;
      // 储值卡
      if (row.type == 2) {
        row.LargessPrice = (row.largessPrice * row.number).toFixed(2);
        row.Amount = (row.Price * row.number).toFixed(2);
        row.totalPrice = row.Amount;
        row.PayAmount = row.Amount;
      } else if (row.type == 5) {
        //套餐卡
        that.deductionReset(row); //重置卡抵扣
        row.savingCardDeduction = [];
        if (row.isShowSelectMemberAmout && row.isUseMemberDiscount && !row.IsLargess) {
          row.savingCardDeduction = [];
          if (row.isUseMemberDiscount) {
            row.MemberPreferentialAmountTotal = row.MemberPreferentialAmount * row.number;
            let Amount = (row.Price * row.number - row.MemberPreferentialAmountTotal).toFixed(2);
            row.Amount = Amount;
            row.totalPrice = Amount;
          } else {
            row.MemberPreferentialAmountTotal = 0;
            let Amount = (row.Price * row.number).toFixed(2);
            row.Amount = Amount;
            row.totalPrice = Amount;
          }
          row.modifyAmount = row.DeductPrice * row.number;
        } else {
          row.Amount = ((row.Price * row.number * row.discount) / 100).toFixed(2);
          row.totalPrice = ((row.Price * row.number * row.discount) / 100).toFixed(2);
          row.modifyAmount = row.DeductPrice * row.number;
        }

        row.noLargess.forEach(function (i) {
          i.number = row.number;
          let memberDiscount = { DiscountPrice: i.MemberDiscountPrice, PriceType: i.MemberPriceType };
          let child_amonunt = i.TotalPrice;
          if (row.isUseMemberDiscount) {
            child_amonunt = that.getPackageDetailMemberAmount(i.TotalPrice, memberDiscount);
          }

          if (i.cardType != '储值卡' && !row.IsLargess) {
            i.totalAmount = child_amonunt * i.number;
            i.TotalAmount = child_amonunt * i.number;
            i.PayAmount = child_amonunt * i.number;
            i.cardDeductionAmount = 0; // 重置卡抵扣
            i.cardDiscountPrice = 0; // 重置卡优惠
            i.discount = 100;
            i.discountPrice = 0;
            if (row.isUseMemberDiscount) {
              i.MemberPreferentialAmountTotal = i.MemberPreferentialAmount * i.number;
            } else {
              i.MemberPreferentialAmountTotal = 0;
            }
          } else if (i.cardType == '储值卡') {
            i.totalAmount = i.Price * i.Amount * i.number;
            i.TotalAmount = i.Price * i.Amount * i.number;
            i.PayAmount = i.Price * i.Amount * i.number;
            i.MemberPreferentialAmountTotal = 0;
          } else if (row.IsLargess) {
            i.totalAmount = i.TotalPrice * i.number;
            i.TotalAmount = i.TotalPrice * i.number;
            i.PayAmount = i.TotalPrice * i.number;
            i.MemberPreferentialAmountTotal = 0;
          }
        });
        row.largess.forEach(function (i) {
          i.number = row.number;
        });

        that.payPriceData(row);
      } else {
        that.deductionReset(row);
        row.savingCardDeduction = [];
        if (row.isShowSelectMemberAmout && row.isUseMemberDiscount && !row.IsLargess) {
          if (row.MemberPriceType == 1) {
            let Amount = (row.Price * row.number * row.MemberDiscountPrice).toFixed(2);
            row.Amount = Amount;
            row.totalPrice = Amount;
            row.MemberPreferentialAmountTotal = row.MemberPreferentialAmount * row.number;
          }
          if (row.MemberPriceType == 2) {
            let Amount = (row.MemberDiscountPrice * row.number).toFixed(2);
            row.Amount = Amount;
            row.totalPrice = Amount;
            row.MemberPreferentialAmountTotal = row.MemberPreferentialAmount * row.number;
          }
        } else {
          row.Amount = ((row.Price * row.number * row.discount) / 100).toFixed(2);
          row.totalPrice = ((row.Price * row.number * row.discount) / 100).toFixed(2);
        }
        that.payPriceData(row);
      }

      // if (row.type == 5) {
      //   // that.packageCardNoLargessMemberDiscountPrice(row, { DiscountPrice: row.MemberDiscountPrice, PriceType: row.MemberPriceType });
      //   row.largess.forEach(function (item) {

      //   });
      // }
      that.payAmountData();
    },
    // 赠送
    largessChange: function (row) {
      var that = this;
      if (row.IsLargess) {
        row.discount = 100;
        row.discountPrice = 0;
        that.deductionReset(row);
        row.savingCardDeduction = [];
        row.Amount = ((row.Price * row.number * row.discount) / 100).toFixed(2);
        row.TotalAmount = row.Amount;
        row.ArrearAmount = 0;
        row.PayAmount = 0;
        row.DeductionProjectAmount = 0;
        row.discountPrice = 0;
        row.CardDeductionAmount = 0;
        row.CardDiscountPrice = 0;
        row.totalPrice = row.Amount;
        row.MemberPreferentialAmountTotal = 0;

        if (row.type == 2) {
          row.LargessPrice = (row.largessPrice * row.number).toFixed(2);
        }
        if (row.type == 5) {
          row.noLargess.forEach(function (item) {
            item.number = row.number;
            item.totalAmount = (row.number * item.TotalPrice).toFixed(2);
            item.cardDeductionAmount = 0;
            item.cardDiscountPrice = 0;
            item.ArrearAmount = '';
            item.PayAmount = 0;
            item.MemberPreferentialAmountTotal = 0;
          });
        }
      } else {
        if (row.isShowSelectMemberAmout && row.isUseMemberDiscount) {
          if (row.MemberPriceType == 1) {
            let Amount = (row.Price * row.number * row.MemberDiscountPrice).toFixed(2);
            if (row.type == 5) {
              Amount = parseFloat(row.DeductPrice * row.number * row.MemberDiscountPrice) + parseFloat(row.Price - row.DeductPrice) * row.number;
            }
            row.Amount = Amount;
            row.totalPrice = Amount;
            row.MemberPreferentialAmountTotal = row.MemberPreferentialAmount * row.number;
            row.PayAmount = Amount;
            row.TotalAmount = Amount;
            row.discountPrice = 0;
            row.CardDeductionAmount = 0;
            row.CardDiscountPrice = 0;
          }
          if (row.MemberPriceType == 2) {
            let Amount = (row.MemberDiscountPrice * row.number).toFixed(2);
            if (row.type == 5) {
              if (row.DeductPrice > row.MemberDiscountPrice) {
                Amount = parseFloat(row.number * row.MemberDiscountPrice);
              } else {
                Amount = parseFloat(row.DeductPrice * row.number * row.MemberDiscountPrice) + parseFloat(row.Price - row.DeductPrice) * row.number;
              }
            }
            console.log('🚀 ~ Amount:', Amount);
            row.Amount = Amount;

            row.totalPrice = Amount;
            row.MemberPreferentialAmountTotal = row.MemberPreferentialAmount * row.number;
            row.PayAmount = Amount;
            row.TotalAmount = Amount;
            row.discountPrice = 0;
            row.CardDeductionAmount = 0;
            row.CardDiscountPrice = 0;
          }
        } else {
          let Amount = ((row.Price * row.number * row.discount) / 100).toFixed(2);
          row.Amount = Amount;
          row.totalPrice = Amount;
          row.PayAmount = Amount;
          row.discountPrice = 0;
          row.CardDeductionAmount = 0;
          row.CardDiscountPrice = 0;
        }
        if (row.type == 5) {
          row.noLargess.forEach(function (item) {
            item.number = row.number;
            if (item.cardType != '储值卡') {
              if (row.MemberPriceType == 1) {
                item.totalAmount = (row.number * item.TotalPrice * row.MemberDiscountPrice).toFixed(2);
              }
              if (row.MemberPriceType == 2) {
                item.totalAmount = (row.number * row.MemberDiscountPrice).toFixed(2);
              }
            } else {
              item.totalAmount = (row.number * item.TotalPrice).toFixed(2);
            }
            item.cardDeductionAmount = 0;
            item.cardDiscountPrice = 0;
            item.ArrearAmount = '';
            item.PayAmount = item.totalAmount;
            item.MemberPreferentialAmountTotal = item.MemberPreferentialAmount * item.number;
          });
        }
      }

      that.payAmountData();
    },
    // 储值卡金额变化
    savingAmountChange: function (row) {
      var that = this;
      row.ArrearAmount = 0;

      row.PayAmount = (row.Amount - row.ArrearAmount).toFixed(2);
      that.payAmountData();
    },
    // 欠款
    arrearChange: function (item) {
      var that = this;
      if (item.type == 2) {
        if (parseFloat(item.ArrearAmount) > parseFloat(item.Amount)) {
          that.$message.error('欠款金额不能大于支付金额');
          item.ArrearAmount = item.Amount;
        }
      } else {
        if (parseFloat(item.ArrearAmount) > parseFloat(item.Amount) - parseFloat(item.CardDiscountPrice) - parseFloat(item.CardDeductionAmount)) {
          that.$message.error('欠款金额不能大于支付金额');
          item.ArrearAmount = parseFloat(item.Amount) - parseFloat(item.CardDiscountPrice) - parseFloat(item.CardDeductionAmount);
        }
      }

      that.payPriceData(item);
      that.payAmountData();
    },
    payPriceData(item) {
      if (item.type == 2) {
        item.PayAmount = (item.Amount - (item.ArrearAmount || 0)).toFixed(2);
      } else {
        item.DeductionProjectAmount = (parseFloat(item.discountPrice) + parseFloat(item.CardDiscountPrice)).toFixed(2);
        if (!item.IsLargess) {
          item.PayAmount = (item.totalPrice - item.DeductionProjectAmount - item.CardDeductionAmount - item.ArrearAmount).toFixed(2);
        } else {
          item.PayAmount = 0;
        }
        if (item.type == 2) {
          item.TotalAmount = (item.Amount - item.CardDiscountPrice - item.discountPrice).toFixed(2);
        } else {
          item.TotalAmount = (item.Amount - item.CardDiscountPrice).toFixed(2);
        }
      }
    },
    // 卡抵扣重置
    deductionReset: function (row) {
      var that = this;
      row.CardDeductionAmount = 0;
      row.CardDiscountPrice = 0;
      that.savingCardSomeGoods.forEach(function (item) {
        row.savingCardDeduction.forEach(function (sav) {
          if (item.ID == sav.ID) {
            item.TotalBalance = (parseFloat(item.TotalBalance) + parseFloat(sav.TotalAmount)).toFixed(2);
          }
        });
      });
    },
    // 删除会员卡抵扣重置
    deductionAllReset: function () {
      var that = this;
      that.selectProject.forEach(function (item) {
        item.CardDeductionAmount = 0;
        item.CardDiscountPrice = 0;
        item.DeductionProjectAmount = 0;
        item.discountPrice = 0;
        item.discount = 100;
        item.Amount = (item.number * item.Price).toFixed(2);
        item.totalPrice = item.Amount;
        item.TotalAmount = item.Amount;
        item.ArrearAmount = 0;
        item.PayAmount = item.Amount;
        item.IsLargess = false;
        item.savingCardDeduction = [];
      });
      that.selectTimeCard.forEach(function (item) {
        item.CardDeductionAmount = 0;
        item.CardDiscountPrice = 0;
        item.DeductionProjectAmount = 0;
        item.discountPrice = 0;
        item.discount = 100;
        item.Amount = (item.number * item.Price).toFixed(2);
        item.totalPrice = item.Amount;
        item.TotalAmount = item.Amount;
        item.ArrearAmount = 0;
        item.PayAmount = item.Amount;
        item.IsLargess = false;
        item.savingCardDeduction = [];
      });
      that.selectGeneralCard.forEach(function (item) {
        item.CardDeductionAmount = 0;
        item.CardDiscountPrice = 0;
        item.DeductionProjectAmount = 0;
        item.discountPrice = 0;
        item.discount = 100;
        item.Amount = (item.number * item.Price).toFixed(2);
        item.totalPrice = item.Amount;
        item.TotalAmount = item.Amount;
        item.ArrearAmount = 0;
        item.PayAmount = item.Amount;
        item.IsLargess = false;
        item.savingCardDeduction = [];
      });
      that.selectPackageCard.forEach(function (item) {
        item.CardDeductionAmount = 0;
        item.CardDiscountPrice = 0;
        item.DeductionProjectAmount = 0;
        item.discountPrice = 0;
        item.discount = 100;
        item.Amount = (item.number * item.Price).toFixed(2);
        item.totalPrice = item.Amount;
        item.TotalAmount = item.Amount;
        item.ArrearAmount = 0;
        item.PayAmount = item.Amount;
        item.IsLargess = false;
        item.savingCardDeduction = [];
        item.noLargess.forEach(function (noLargess) {
          noLargess.cardDeductionAmount = 0;
          noLargess.cardDiscountPrice = 0;
          noLargess.discountPrice = 0;
          noLargess.TotalAmount = (noLargess.TotalPrice * noLargess.number).toFixed(2);
          noLargess.ArrearAmount = '';
          noLargess.totalAmount = noLargess.TotalAmount;
          noLargess.PayAmount = noLargess.TotalAmount;
        });
      });
      that.selectProduct.forEach(function (item) {
        item.CardDeductionAmount = 0;
        item.CardDiscountPrice = 0;
        item.DeductionProjectAmount = 0;
        item.discountPrice = 0;
        item.discount = 100;
        item.Amount = (item.number * item.Price).toFixed(2);
        item.totalPrice = item.Amount;
        item.TotalAmount = item.Amount;
        item.ArrearAmount = 0;
        item.PayAmount = item.Amount;
        item.IsLargess = false;
        item.savingCardDeduction = [];
      });
      that.selectSavingCard.forEach(function (item) {
        item.Amount = (item.number * item.Price).toFixed(2);
        item.LargessPrice = item.largessPrice;
        item.IsLargess = false;
      });
    },

    // 计算总金额
    payAmountData() {
      var that = this;
      let originalTotalAmount = 0;
      var payAmount = 0;
      var TotalAmount = 0; //订单金额
      var ArrearAmount = 0; //欠款金额
      var PricePreferentialAmount = 0;
      var CardPreferentialAmount = 0;
      var CardDeductionAmount = 0;
      var modifyPayAmount = 0;
      let CardDiscountPrice = 0;
      that.modifyPayAmount = 0;
      that.totalLength = 0;
      that.savingPayAmount = 0;
      that.packageSavingPayAmount = 0;
      let memberPreferentialAmount = 0;
      let changOrderAmount = 0;

      that.selectProject.forEach(function (item) {
        if (!item.IsLargess) {
          payAmount = (parseFloat(payAmount) + parseFloat(item.PayAmount)).toFixed(2);

          ArrearAmount = (parseFloat(ArrearAmount) + parseFloat(item.ArrearAmount || 0)).toFixed(2);
          TotalAmount = (parseFloat(TotalAmount) + parseFloat(item.TotalAmount)).toFixed(2);
          PricePreferentialAmount = (parseFloat(PricePreferentialAmount) + parseFloat(item.discountPrice)).toFixed(2);
          CardPreferentialAmount = (parseFloat(CardPreferentialAmount) + parseFloat(item.CardDiscountPrice)).toFixed(2);
          CardDeductionAmount = (parseFloat(CardDeductionAmount) + parseFloat(item.CardDeductionAmount)).toFixed(2);
          memberPreferentialAmount += parseFloat(item.MemberPreferentialAmountTotal || 0);
          if (item.IsModifyPrice) {
            modifyPayAmount = (
              parseFloat(modifyPayAmount) +
              parseFloat(item.PayAmount || 0) +
              parseFloat(item.MemberPreferentialAmountTotal || 0) +
              parseFloat(item.discountPrice || 0)
            ).toFixed(2);
            changOrderAmount += Number(item.PayAmount || 0);
            that.totalLength += 1;
            originalTotalAmount += parseFloat(item.Price * item.number || 0);
            CardDiscountPrice += Number(item.CardDiscountPrice || 0);
          }
        }
      });
      that.selectSavingCard.forEach(function (item) {
        payAmount = (parseFloat(payAmount || 0) + parseFloat(item.PayAmount || 0)).toFixed(2);
        ArrearAmount = (parseFloat(ArrearAmount) + parseFloat(item.ArrearAmount || 0)).toFixed(2);
        TotalAmount = (parseFloat(TotalAmount || 0) + parseFloat(item.Amount || 0)).toFixed(2);
        that.savingPayAmount = Number(that.savingPayAmount || 0) + Number(item.PayAmount || 0);
      });
      that.selectTimeCard.forEach(function (item) {
        if (!item.IsLargess) {
          payAmount = (parseFloat(payAmount) + parseFloat(item.PayAmount)).toFixed(2);
          ArrearAmount = (parseFloat(ArrearAmount) + parseFloat(item.ArrearAmount || 0)).toFixed(2);
          TotalAmount = (parseFloat(TotalAmount) + parseFloat(item.TotalAmount)).toFixed(2);
          PricePreferentialAmount = (parseFloat(PricePreferentialAmount) + parseFloat(item.discountPrice)).toFixed(2);
          CardPreferentialAmount = (parseFloat(CardPreferentialAmount) + parseFloat(item.CardDiscountPrice)).toFixed(2);
          CardDeductionAmount = (parseFloat(CardDeductionAmount) + parseFloat(item.CardDeductionAmount)).toFixed(2);
          memberPreferentialAmount += parseFloat(item.MemberPreferentialAmountTotal || 0);
          if (item.IsModifyPrice) {
            modifyPayAmount = (
              parseFloat(modifyPayAmount) +
              parseFloat(item.PayAmount || 0) +
              parseFloat(item.MemberPreferentialAmountTotal || 0) +
              parseFloat(item.discountPrice || 0)
            ).toFixed(2);
            changOrderAmount += Number(item.PayAmount || 0);
            that.totalLength += 1;
            originalTotalAmount += parseFloat(item.Price * item.number || 0);
            CardDiscountPrice += Number(item.CardDiscountPrice || 0);
          }
        }
      });
      that.selectGeneralCard.forEach(function (item) {
        if (!item.IsLargess) {
          payAmount = (parseFloat(payAmount) + parseFloat(item.PayAmount)).toFixed(2);
          ArrearAmount = (parseFloat(ArrearAmount) + parseFloat(item.ArrearAmount || 0)).toFixed(2);
          TotalAmount = (parseFloat(TotalAmount) + parseFloat(item.TotalAmount)).toFixed(2);
          PricePreferentialAmount = (parseFloat(PricePreferentialAmount) + parseFloat(item.discountPrice)).toFixed(2);
          CardPreferentialAmount = (parseFloat(CardPreferentialAmount) + parseFloat(item.CardDiscountPrice)).toFixed(2);
          CardDeductionAmount = (parseFloat(CardDeductionAmount) + parseFloat(item.CardDeductionAmount)).toFixed(2);
          memberPreferentialAmount += parseFloat(item.MemberPreferentialAmountTotal || 0);
          if (item.IsModifyPrice) {
            modifyPayAmount = (
              parseFloat(modifyPayAmount) +
              parseFloat(item.PayAmount || 0) +
              parseFloat(item.MemberPreferentialAmountTotal || 0) +
              parseFloat(item.discountPrice || 0)
            ).toFixed(2);
            changOrderAmount += Number(item.PayAmount || 0);
            that.totalLength += 1;
            originalTotalAmount += parseFloat(item.Price * item.number || 0);
            CardDiscountPrice += Number(item.CardDiscountPrice || 0);
          }
        }
      });
      that.selectPackageCard.forEach(function (item) {
        if (!item.IsLargess) {
          payAmount = (parseFloat(payAmount) + parseFloat(item.PayAmount)).toFixed(2);
          ArrearAmount = (parseFloat(ArrearAmount) + parseFloat(item.ArrearAmount || 0)).toFixed(2);
          TotalAmount = (parseFloat(TotalAmount) + parseFloat(item.TotalAmount)).toFixed(2);
          PricePreferentialAmount = (parseFloat(PricePreferentialAmount) + parseFloat(item.discountPrice)).toFixed(2);
          CardPreferentialAmount = (parseFloat(CardPreferentialAmount) + parseFloat(item.CardDiscountPrice)).toFixed(2);
          CardDeductionAmount = (parseFloat(CardDeductionAmount) + parseFloat(item.CardDeductionAmount)).toFixed(2);
          memberPreferentialAmount += parseFloat(item.MemberPreferentialAmountTotal || 0);

          
          let cardAmount = item.noLargess
            .filter((i) => i.cardType == '储值卡')
            .reduce((per, cur) => {
              return per + cur.PayAmount;
            }, 0);
          that.packageSavingPayAmount += cardAmount;
          if (item.IsModifyPrice) {
            modifyPayAmount = (
              parseFloat(modifyPayAmount) +
              parseFloat(item.PayAmount || 0) +
              parseFloat(item.MemberPreferentialAmountTotal || 0) -
              cardAmount +
              parseFloat(item.discountPrice || 0)
            ).toFixed(2);
            changOrderAmount += Number(item.PayAmount || 0) - Number(cardAmount);
            that.totalLength += 1;
            originalTotalAmount += parseFloat(item.DeductPrice * item.number || 0);
            CardDiscountPrice += Number(item.CardDiscountPrice || 0);
          }
        }
      });
      that.selectProduct.forEach(function (item) {
        if (!item.IsLargess) {
          payAmount = (parseFloat(payAmount) + parseFloat(item.PayAmount)).toFixed(2);
          ArrearAmount = (parseFloat(ArrearAmount) + parseFloat(item.ArrearAmount || 0)).toFixed(2);
          TotalAmount = (parseFloat(TotalAmount) + parseFloat(item.TotalAmount)).toFixed(2);
          PricePreferentialAmount = (parseFloat(PricePreferentialAmount) + parseFloat(item.discountPrice)).toFixed(2);
          CardPreferentialAmount = (parseFloat(CardPreferentialAmount) + parseFloat(item.CardDiscountPrice)).toFixed(2);
          CardDeductionAmount = (parseFloat(CardDeductionAmount) + parseFloat(item.CardDeductionAmount)).toFixed(2);
          memberPreferentialAmount += parseFloat(item.MemberPreferentialAmountTotal || 0);
          if (item.IsModifyPrice) {
            modifyPayAmount = (
              parseFloat(modifyPayAmount) +
              parseFloat(item.PayAmount || 0) +
              parseFloat(item.MemberPreferentialAmountTotal || 0) +
              parseFloat(item.discountPrice || 0)
            ).toFixed(2);
            changOrderAmount += Number(item.PayAmount || 0);
            that.totalLength += 1;
            originalTotalAmount += parseFloat(item.Price  * item.number || 0);
            CardDiscountPrice += Number(item.CardDiscountPrice || 0);
          }
        }
      });
      that.originalTotalAmount = originalTotalAmount;
      
      that.savingPayAmount = (Number(that.savingPayAmount) + Number(that.packageSavingPayAmount)).toFixed(2);
      that.Amount = TotalAmount;
      that.PayAmount = (payAmount - that.cardDeductionAmount).toFixed(2);
      that.modifyPayAmount = parseFloat(modifyPayAmount - that.cardDeductionAmount).toFixed(2);
      that.changOrderAmount = Number(changOrderAmount || 0).toFixed(2);
      // console.log("🚀 ~ payAmountData ~ that.originalTotalAmount:", that.originalTotalAmount)
      that.oldPayAmount = that.modifyPayAmount;

      that.ArrearAmount = ArrearAmount;
      that.PricePreferentialAmount = PricePreferentialAmount;
      that.CardPreferentialAmount = CardPreferentialAmount;
      that.CardDeductionAmount = CardDeductionAmount;
      that.CardDiscountPrice = CardDiscountPrice;
      that.payList = [{ PayMethodID: '', Amount: '', price: 0 }];
      that.PayCashAmount = 0;
      that.payTotalPrice = that.PayAmount;
      //计算购买储值卡金额，用于结账储值卡不可抵扣储值卡
      that.savingCardPrice = 0;
      if (that.savingCardAllGoods.length) {
        that.selectSavingCard.forEach(function (item) {
          that.savingCardPrice = (parseFloat(that.savingCardPrice) + parseFloat(item.PayAmount)).toFixed(2);
        });
        that.selectPackageCard.forEach(function (item) {
          item.noLargess.forEach(function (noLargess) {
            if (noLargess.isCardType == 2) {
              that.savingCardPrice = (parseFloat(that.savingCardPrice) + parseFloat(noLargess.PayAmount)).toFixed(2);
            }
          });
        });
      }
      that.memberPreferentialAmount = parseFloat(memberPreferentialAmount || 0).toFixed(2);
      that.changOrderDiscount = 100;
    },

    /*  */
    reviewHandledScale() {
      const that = this;
      const isProjectHandleScale = that.selectProject.some((i) => {
        return (
          i.handleTypeList &&
          i.handleTypeList.some((h) => {
            return h.Employee.some((e) => {
              return !e.Discount;
            });
          })
        );
      });
      const isProductHandleScale = that.selectProduct.some((i) => {
        return (
          i.handleTypeList &&
          i.handleTypeList.some((h) => {
            return h.Employee.some((e) => {
              return !e.Discount;
            });
          })
        );
      });
      const isSavingCardHandleScale = that.selectSavingCard.some((i) => {
        return (
          i.handleTypeList &&
          i.handleTypeList.some((h) => {
            return h.Employee.some((e) => {
              return !e.Discount;
            });
          })
        );
      });
      const isTimeCardHandleScale = that.selectTimeCard.some((i) => {
        return (
          i.handleTypeList &&
          i.handleTypeList.some((h) => {
            return h.Employee.some((e) => {
              return !e.Discount;
            });
          })
        );
      });
      const isGeneralCardHandleScale = that.selectGeneralCard.some((i) => {
        return (
          i.handleTypeList &&
          i.handleTypeList.some((h) => {
            return h.Employee.some((e) => {
              return !e.Discount;
            });
          })
        );
      });
      const isPackageCardHandleScale = that.selectPackageCard.some((i) => {
        return (
          i.handleTypeList &&
          i.handleTypeList.some((h) => {
            return h.Employee.some((e) => {
              return !e.Discount;
            });
          })
        );
      });
      return (
        isProductHandleScale || isProjectHandleScale || isSavingCardHandleScale || isTimeCardHandleScale || isGeneralCardHandleScale || isPackageCardHandleScale
      );
    },
    // 结账
    billClick: function () {
      var that = this;
      if (that.customerID == null) {
        that.$message.error('请填写客户信息');
        return;
      }
      if (
        (that.selectProject.length <= 0) &
        (that.selectSavingCard.length <= 0) &
        (that.selectTimeCard.length <= 0) &
        (that.selectGeneralCard.length <= 0) &
        (that.selectPackageCard.length <= 0) &
        (that.selectProduct.length <= 0)
      ) {
        that.$message.error('请选择商品');
        return;
      }
      if (that.getBillDate() == null) {
        that.$message.error('请输入补录日期');
        return;
      }

      let isProjectHanlder = that.selectProject.some((i) => {
        return !i.handleTypeList || !i.handleTypeList.some((j) => j.Employee && j.Employee.length);
      });
      let isSavingCardHanlder = that.selectSavingCard.some((i) => {
        return !i.handleTypeList || !i.handleTypeList.some((j) => j.Employee && j.Employee.length);
      });
      let isTimeCardHanlder = that.selectTimeCard.some((i) => {
        return !i.handleTypeList || !i.handleTypeList.some((j) => j.Employee && j.Employee.length);
      });
      let isGeneralCardHanlder = that.selectGeneralCard.some((i) => {
        return !i.handleTypeList || !i.handleTypeList.some((j) => j.Employee && j.Employee.length);
      });
      let isPackageCardHanlder = that.selectPackageCard.some((i) => {
        return !i.handleTypeList || !i.handleTypeList.some((j) => j.Employee && j.Employee.length);
      });
      let isProductHanlder = that.selectProduct.some((i) => {
        return !i.handleTypeList || !i.handleTypeList.some((j) => j.Employee && j.Employee.length);
      });
      if (isProjectHanlder || isSavingCardHanlder || isTimeCardHanlder || isGeneralCardHanlder || isPackageCardHanlder || isProductHanlder) {
        that
          .$confirm('存在未添加经手人的商品，是否继续收款', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          })
          .then(() => {
            that.billClick_method();
          })
          .catch(() => {
            that.$message({
              type: 'info',
              message: '已取消操作',
            });
          });
      } else {
        that.billClick_method();
      }
    },
    /**    */
    billClick_method() {
      let that = this;
      that.dialogBill = true;
      that.savingCardAllGoods = Enumerable.from(that.savingCardAllGoods)
        .select((val) => ({
          checked: false,
          AccountID: val.AccountID,
          Balance: val.Balance,
          ID: val.ID,
          LargessBalance: val.LargessBalance,
          SavingCardName: val.SavingCardName,
          TotalBalance: val.TotalBalance,
          TotalPrice: val.TotalBalance,
          Type: val.Type,
          TotalAmount: '',
          IsLargess: val.IsLargess,
        }))
        .toArray();
      that.savingDeductionPriceAll();
      that.payAmountData();
    },
    // 支付方式支付金额变化
    payPriceChange: function (item) {
      var that = this;
      var payAmount = (parseFloat(that.PayAmount) + parseFloat(item.price)).toFixed(2);
      if (parseFloat(item.Amount) > parseFloat(payAmount)) {
        item.Amount = payAmount;
      }
      item.price = item.Amount;
      that.paymentAmountData();
    },
    payMethodChange: function (item) {
      var that = this;
      if (item.PayMethodID == '') {
        item.Amount = '';
      } else {
        if (item.Amount == '') {
          item.Amount = parseFloat(that.PayAmount).toFixed(2);
        }
      }
      that.payPriceChange(item);
    },
    // 支付方式金额总计
    paymentAmountData: function () {
      var that = this;
      var amount = 0;
      that.payList.forEach(function (item) {
        amount = (parseFloat(amount) + (parseFloat(item.price) || 0)).toFixed(2);
      });
      that.PayCashAmount = amount;
      that.PayAmount = (that.payTotalPrice - amount).toFixed(2);
    },
    // 删除支付
    removePayClick: function (index) {
      var that = this;
      that.payList.splice(index, 1);
      that.paymentAmountData();
    },
    // 添加支付
    addPayclick: function () {
      var that = this;
      that.payList.push({ PayMethodID: '', Amount: '', price: 0 });
    },

    // 确认结帐
    submitPayClick: function () {
      var that = this;
      that.modalLoading = true;
      if (that.PayAmount != 0) {
        that.$message.error({
          message: '请填写收款金额。',
          duration: 2000,
        });
        that.modalLoading = false;
        return;
      }

      var payList = Enumerable.from(that.payList)
        .where(function (i) {
          return i.PayMethodID != '' && i.Amount != '';
        })
        .select((val) => ({
          PayMethodID: val.PayMethodID,
          Amount: val.Amount,
        }))
        .toArray();

      that.SavingCardDeduction = Enumerable.from(that.savingCardAllGoods)
        .where(function (i) {
          return i.checked && parseFloat(i.TotalAmount) != 0 && i.TotalAmount != '';
        })
        .select((val) => ({
          Type: val.Type,
          SavingCardAccountID: val.AccountID,
          DeductionAmount: val.TotalAmount,
          ID: val.ID,
        }))
        .toArray();

      let params = Object.assign(
        {
          PayMethod: payList,
          SavingCardDeduction: that.SavingCardDeduction,
        },
        that.getSaleBillParams()
      );

      API.createSaleBill(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.selectProject = [];
            that.selectProduct = [];
            that.selectGeneralCard = [];
            that.selectTimeCard = [];
            that.selectSavingCard = [];
            that.selectPackageCard = [];
            that.orderAmount = that.Amount;
            that.Amount = 0; //订单金额
            that.PayAmount = 0; //现金支付金额
            that.payTotalPrice = 0; //待支付总金额
            that.ArrearAmount = 0; //欠款金额
            that.cardDeductionAmount = 0;
            that.CardDeductionAmount = 0; // 储值卡抵扣金额
            that.PricePreferentialAmount = 0; //手动改价优惠金额
            that.CardPreferentialAmount = 0; //卡优惠金额
            that.savingCardAllGoodsData();
            that.savingCardSomeGoodsData();
            that.orderNumber = res.Message;
            that.dialogBill = false;
            that.dialogPay = true;
            that.BillID = '';
            that.getOrderDetail();
            // that.$message.success({ message: "收款完成", duration: 3000 });
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.modalLoading = false;
        });
    },
    /**  获取 结账参数  */
    getSaleBillParams() {
      let that = this;

      that.selectProject.forEach(function (item) {
        item.ProjectSaleHandler = [];
        item.projectSavingCardDeduction = [];
        item.handleTypeList.forEach(function (handler) {
          handler.Employee.forEach(function (employee) {
            item.ProjectSaleHandler.push({
              SaleHandlerID: employee.SaleHandlerID,
              EmployeeID: employee.EmployeeID,
              Scale: employee.Discount,
            });
          });
        });
        item.projectSavingCardDeduction = Enumerable.from(item.savingCardDeduction)
          .select((val) => ({
            SavingCardAccountID: val.AccountID,
            DeductionAmount: val.TotalAmount,
            PreferentialAmount: val.cardDiscountPrice,
            Type: val.Type,
            ID: val.ID,
          }))
          .toArray();
      });
      that.selectProduct.forEach(function (item) {
        item.ProductSaleHandler = [];
        item.productSavingCardDeduction = [];
        item.handleTypeList.forEach(function (handler) {
          handler.Employee.forEach(function (employee) {
            item.ProductSaleHandler.push({
              SaleHandlerID: employee.SaleHandlerID,
              EmployeeID: employee.EmployeeID,
              Scale: employee.Discount,
            });
          });
        });
        item.productSavingCardDeduction = Enumerable.from(item.savingCardDeduction)
          .select((val) => ({
            SavingCardAccountID: val.AccountID,
            DeductionAmount: val.TotalAmount,
            PreferentialAmount: val.cardDiscountPrice,
            Type: val.Type,
            ID: val.ID,
          }))
          .toArray();
      });
      that.selectGeneralCard.forEach(function (item) {
        item.GeneralCardSaleHandler = [];
        item.generalSavingCardDeduction = [];
        item.handleTypeList.forEach(function (handler) {
          handler.Employee.forEach(function (employee) {
            item.GeneralCardSaleHandler.push({
              SaleHandlerID: employee.SaleHandlerID,
              EmployeeID: employee.EmployeeID,
              Scale: employee.Discount,
            });
          });
        });
        item.generalSavingCardDeduction = Enumerable.from(item.savingCardDeduction)
          .select((val) => ({
            SavingCardAccountID: val.AccountID,
            DeductionAmount: val.TotalAmount,
            PreferentialAmount: val.cardDiscountPrice,
            Type: val.Type,
            ID: val.ID,
          }))
          .toArray();
      });
      that.selectTimeCard.forEach(function (item) {
        item.TimeCardSaleHandler = [];
        item.timeCardSavingCardDeduction = [];
        item.handleTypeList.forEach(function (handler) {
          handler.Employee.forEach(function (employee) {
            item.TimeCardSaleHandler.push({
              SaleHandlerID: employee.SaleHandlerID,
              EmployeeID: employee.EmployeeID,
              Scale: employee.Discount,
            });
          });
        });
        item.timeCardSavingCardDeduction = Enumerable.from(item.savingCardDeduction)
          .select((val) => ({
            SavingCardAccountID: val.AccountID,
            DeductionAmount: val.TotalAmount,
            PreferentialAmount: val.cardDiscountPrice,
            Type: val.Type,
            ID: val.ID,
          }))
          .toArray();
      });
      that.selectSavingCard.forEach(function (item) {
        item.SavingCardSaleHandler = [];
        item.handleTypeList.forEach(function (handler) {
          handler.Employee.forEach(function (employee) {
            item.SavingCardSaleHandler.push({
              SaleHandlerID: employee.SaleHandlerID,
              EmployeeID: employee.EmployeeID,
              Scale: employee.Discount,
            });
          });
        });
      });
      that.selectPackageCard.forEach(function (item) {
        item.PackageCardSaleHandler = [];
        item.packageSavingCardDeduction = [];
        item.packageCardGoods = {};
        item.packageCardLargessGoods = {};
        var Project = [];
        var Product = [];
        var GeneralCard = [];
        var TimeCard = [];
        var SavingCard = [];
        var ProjectLargess = [];
        var ProductLargess = [];
        var GeneralCardLargess = [];
        var TimeCardLargess = [];
        var SavingCardLargess = [];
        item.handleTypeList.forEach(function (handler) {
          handler.Employee.forEach(function (employee) {
            item.PackageCardSaleHandler.push({
              SaleHandlerID: employee.SaleHandlerID,
              EmployeeID: employee.EmployeeID,
              Scale: employee.Discount,
            });
          });
        });
        item.packageSavingCardDeduction = Enumerable.from(item.savingCardDeduction)
          .select((val) => ({
            SavingCardAccountID: val.AccountID,
            DeductionAmount: val.TotalAmount,
            PreferentialAmount: val.cardDiscountPrice,
            Type: val.Type,
            ID: val.ID,
          }))
          .toArray();
        Project = Enumerable.from(item.noLargess)
          .where(function (i) {
            return i.isCardType == 1;
          })
          .select((val) => ({
            ID: val.ID,
            Quantity: val.Amount,
            PackagePrice: parseFloat(val.Price).toFixed(2),
            PackageTotalPrice: parseFloat(val.TotalPrice).toFixed(2),
            PackageQuantity: val.number,
            ArrearAmount: val.ArrearAmount == '' ? 0 : parseFloat(val.ArrearAmount).toFixed(2),
            TotalAmount: parseFloat(val.totalAmount).toFixed(2),
            PayAmount: parseFloat(val.PayAmount).toFixed(2),
            PricePreferentialAmount: parseFloat(val.discountPrice).toFixed(2),
            CardPreferentialAmount: parseFloat(val.cardDiscountPrice).toFixed(2),
            Remark: val.Remark,
            MemberPreferentialAmount: parseFloat(val.MemberPreferentialAmount || 0).toFixed(2),
          }))
          .toArray();
        Product = Enumerable.from(item.noLargess)
          .where(function (i) {
            return i.isCardType == 6;
          })
          .select((val) => ({
            ID: val.ID,
            Quantity: val.Amount,
            PackagePrice: parseFloat(val.Price).toFixed(2),
            PackageTotalPrice: parseFloat(val.TotalPrice).toFixed(2),
            PackageQuantity: val.number,
            ArrearAmount: val.ArrearAmount == '' ? 0 : parseFloat(val.ArrearAmount).toFixed(2),
            TotalAmount: parseFloat(val.totalAmount).toFixed(2),
            PayAmount: parseFloat(val.PayAmount).toFixed(2),
            PricePreferentialAmount: parseFloat(val.discountPrice).toFixed(2),
            CardPreferentialAmount: parseFloat(val.cardDiscountPrice).toFixed(2),
            Remark: val.Remark,
            MemberPreferentialAmount: parseFloat(val.MemberPreferentialAmount || 0).toFixed(2),
          }))
          .toArray();
        GeneralCard = Enumerable.from(item.noLargess)
          .where(function (i) {
            return i.isCardType == 4;
          })
          .select((val) => ({
            ID: val.ID,
            Quantity: val.Amount,
            PackagePrice: parseFloat(val.Price).toFixed(2),
            PackageTotalPrice: parseFloat(val.TotalPrice).toFixed(2),
            PackageQuantity: val.number,
            ArrearAmount: val.ArrearAmount == '' ? 0 : parseFloat(val.ArrearAmount).toFixed(2),
            TotalAmount: parseFloat(val.totalAmount).toFixed(2),
            PayAmount: parseFloat(val.PayAmount).toFixed(2),
            PricePreferentialAmount: parseFloat(val.discountPrice).toFixed(2),
            CardPreferentialAmount: parseFloat(val.cardDiscountPrice).toFixed(2),
            Remark: val.Remark,
            MemberPreferentialAmount: parseFloat(val.MemberPreferentialAmount || 0).toFixed(2),
          }))
          .toArray();
        TimeCard = Enumerable.from(item.noLargess)
          .where(function (i) {
            return i.isCardType == 3;
          })
          .select((val) => ({
            ID: val.ID,
            Quantity: val.Amount,
            PackagePrice: parseFloat(val.Price).toFixed(2),
            PackageTotalPrice: parseFloat(val.TotalPrice).toFixed(2),
            PackageQuantity: val.number,
            ArrearAmount: val.ArrearAmount == '' ? 0 : parseFloat(val.ArrearAmount).toFixed(2),
            TotalAmount: parseFloat(val.totalAmount).toFixed(2),
            PayAmount: parseFloat(val.PayAmount).toFixed(2),
            PricePreferentialAmount: parseFloat(val.discountPrice).toFixed(2),
            CardPreferentialAmount: parseFloat(val.cardDiscountPrice).toFixed(2),
            Remark: val.Remark,
            MemberPreferentialAmount: parseFloat(val.MemberPreferentialAmount || 0).toFixed(2),
          }))
          .toArray();
        SavingCard = Enumerable.from(item.noLargess)
          .where(function (i) {
            return i.isCardType == 2;
          })
          .select((val) => ({
            ID: val.ID,
            Quantity: val.Amount,
            Amount: parseFloat(val.Price).toFixed(2),
            TotalPrice: parseFloat(val.TotalPrice).toFixed(2),
            ArrearAmount: val.ArrearAmount == '' ? 0 : parseFloat(val.ArrearAmount).toFixed(2),
            PackageQuantity: val.number,
            TotalAmount: parseFloat(val.totalAmount).toFixed(2),
            Remark: val.Remark,
          }))
          .toArray();

        ProjectLargess = Enumerable.from(item.largess)
          .where(function (i) {
            return i.isCardType == 1;
          })
          .select((val) => ({
            ID: val.ID,
            Quantity: val.Amount,
            PackagePrice: parseFloat(val.Price).toFixed(2),
            PackageTotalPrice: parseFloat(val.TotalPrice).toFixed(2),
            PackageQuantity: val.number,
            Remark: val.Remark,
          }))
          .toArray();
        ProductLargess = Enumerable.from(item.largess)
          .where(function (i) {
            return i.isCardType == 6;
          })
          .select((val) => ({
            ID: val.ID,
            Quantity: val.Amount,
            PackagePrice: parseFloat(val.Price).toFixed(2),
            PackageTotalPrice: parseFloat(val.TotalPrice).toFixed(2),
            PackageQuantity: val.number,
            Remark: val.Remark,
          }))
          .toArray();
        GeneralCardLargess = Enumerable.from(item.largess)
          .where(function (i) {
            return i.isCardType == 4;
          })
          .select((val) => ({
            ID: val.ID,
            Quantity: val.Amount,
            PackagePrice: parseFloat(val.Price).toFixed(2),
            PackageTotalPrice: parseFloat(val.TotalPrice).toFixed(2),
            PackageQuantity: val.number,
            Remark: val.Remark,
          }))
          .toArray();
        TimeCardLargess = Enumerable.from(item.largess)
          .where(function (i) {
            return i.isCardType == 3;
          })
          .select((val) => ({
            ID: val.ID,
            Quantity: val.Amount,
            PackagePrice: parseFloat(val.Price).toFixed(2),
            PackageTotalPrice: parseFloat(val.TotalPrice).toFixed(2),
            PackageQuantity: val.number,
            Remark: val.Remark,
          }))
          .toArray();
        SavingCardLargess = Enumerable.from(item.largess)
          .where(function (i) {
            return i.isCardType == 2;
          })
          .select((val) => ({
            ID: val.ID,
            Quantity: val.Amount,
            // 原数据
            // PackagePrice: val.Price,
            // PackageTotalPrice: val.TotalPrice,
            // 修改后数据
            PackagePrice: parseFloat(val.TotalPrice).toFixed(2),
            PackageTotalPrice: parseFloat(val.Price).toFixed(2),
            PackageQuantity: val.number,
            Remark: val.Remark,
          }))
          .toArray();
        item.packageCardGoods.Project = Project;
        item.packageCardGoods.Product = Product;
        item.packageCardGoods.GeneralCard = GeneralCard;
        item.packageCardGoods.TimeCard = TimeCard;
        item.packageCardGoods.SavingCard = SavingCard;
        item.packageCardLargessGoods.Project = ProjectLargess;
        item.packageCardLargessGoods.Product = ProductLargess;
        item.packageCardLargessGoods.GeneralCard = GeneralCardLargess;
        item.packageCardLargessGoods.TimeCard = TimeCardLargess;
        item.packageCardLargessGoods.SavingCard = SavingCardLargess;
      });

      var Project = Enumerable.from(that.selectProject)
        .select((val) => ({
          ProjectID: val.ID,
          Quantity: val.number,
          Price: parseFloat(val.Price).toFixed(2),
          TotalAmount: parseFloat(val.TotalAmount).toFixed(2),
          PayAmount: parseFloat(val.PayAmount).toFixed(2),
          ArrearAmount: parseFloat(val.ArrearAmount || 0).toFixed(2),
          SavingCardDeductionAmount: parseFloat(val.CardDeductionAmount).toFixed(2),
          PricePreferentialAmount: parseFloat(val.discountPrice).toFixed(2),
          CardPreferentialAmount: parseFloat(val.CardDiscountPrice).toFixed(2),
          IsLargess: val.IsLargess,
          ProjectSaleHandler: val.ProjectSaleHandler,
          SavingCardDeduction: val.projectSavingCardDeduction,
          Remark: val.Remark,
          MemberPreferentialAmount: parseFloat(val.MemberPreferentialAmountTotal || 0).toFixed(2),
        }))
        .toArray();
      var Product = Enumerable.from(that.selectProduct)
        .select((val) => ({
          ProductID: val.ID,
          Quantity: val.number,
          Price: parseFloat(val.Price).toFixed(2),
          TotalAmount: parseFloat(val.TotalAmount).toFixed(2),
          PayAmount: parseFloat(val.PayAmount).toFixed(2),
          ArrearAmount: parseFloat(val.ArrearAmount || 0).toFixed(2),
          SavingCardDeductionAmount: parseFloat(val.CardDeductionAmount).toFixed(2),
          PricePreferentialAmount: parseFloat(val.discountPrice).toFixed(2),
          CardPreferentialAmount: parseFloat(val.CardDiscountPrice).toFixed(2),
          IsLargess: val.IsLargess,
          ProductSaleHandler: val.ProductSaleHandler,
          SavingCardDeduction: val.productSavingCardDeduction,
          Remark: val.Remark,
          MemberPreferentialAmount: parseFloat(val.MemberPreferentialAmountTotal || 0).toFixed(2),
        }))
        .toArray();
      var GeneralCard = Enumerable.from(that.selectGeneralCard)
        .select((val) => ({
          GeneralCardID: val.ID,
          Quantity: val.number,
          Price: parseFloat(val.Price).toFixed(2),
          TotalAmount: parseFloat(val.TotalAmount).toFixed(2),
          PayAmount: parseFloat(val.PayAmount).toFixed(2),
          ArrearAmount: parseFloat(val.ArrearAmount || 0).toFixed(2),
          SavingCardDeductionAmount: parseFloat(val.CardDeductionAmount).toFixed(2),
          PricePreferentialAmount: parseFloat(val.discountPrice).toFixed(2),
          CardPreferentialAmount: parseFloat(val.CardDiscountPrice).toFixed(2),
          IsLargess: val.IsLargess,
          GeneralCardSaleHandler: val.GeneralCardSaleHandler,
          SavingCardDeduction: val.generalSavingCardDeduction,
          Remark: val.Remark,
          MemberPreferentialAmount: parseFloat(val.MemberPreferentialAmountTotal || 0).toFixed(2),
        }))
        .toArray();
      var TimeCard = Enumerable.from(that.selectTimeCard)
        .select((val) => ({
          TimeCardID: val.ID,
          Quantity: val.number,
          Price: parseFloat(val.Price).toFixed(2),
          TotalAmount: parseFloat(val.TotalAmount).toFixed(2),
          PayAmount: parseFloat(val.PayAmount).toFixed(2),
          ArrearAmount: parseFloat(val.ArrearAmount || 0).toFixed(2),
          SavingCardDeductionAmount: parseFloat(val.CardDeductionAmount).toFixed(2),
          PricePreferentialAmount: parseFloat(val.discountPrice).toFixed(2),
          CardPreferentialAmount: parseFloat(val.CardDiscountPrice).toFixed(2),
          IsLargess: val.IsLargess,
          TimeCardSaleHandler: val.TimeCardSaleHandler,
          SavingCardDeduction: val.timeCardSavingCardDeduction,
          Remark: val.Remark,
          MemberPreferentialAmount: parseFloat(val.MemberPreferentialAmountTotal || 0).toFixed(2),
        }))
        .toArray();
      var SavingCard = Enumerable.from(that.selectSavingCard)
        .select((val) => ({
          SavingCardID: val.ID,
          Quantity: val.number,
          Price: parseFloat(val.Price).toFixed(2),
          TotalAmount: parseFloat(val.Amount || 0).toFixed(2),
          PayAmount: parseFloat(val.PayAmount || 0).toFixed(2),
          ArrearAmount: parseFloat(val.ArrearAmount || 0).toFixed(2),
          LargessAmount: parseFloat(val.LargessPrice || 0).toFixed(2),
          LargessPrice: parseFloat(val.largessPrice || 0).toFixed(2),
          SavingCardSaleHandler: val.SavingCardSaleHandler,
          Remark: val.Remark,
        }))
        .toArray();
      var PackageCard = Enumerable.from(that.selectPackageCard)
        .select((val) => ({
          PackageCardID: val.ID,
          Quantity: val.number,
          Price: parseFloat(val.Price).toFixed(2),
          TotalAmount: parseFloat(val.TotalAmount).toFixed(2),
          PayAmount: parseFloat(val.PayAmount).toFixed(2),
          ArrearAmount: parseFloat(val.ArrearAmount || 0).toFixed(2),
          SavingCardDeductionAmount: parseFloat(val.CardDeductionAmount).toFixed(2),
          PricePreferentialAmount: parseFloat(val.discountPrice).toFixed(2),
          CardPreferentialAmount: parseFloat(val.CardDiscountPrice).toFixed(2),
          IsLargess: val.IsLargess,
          PackageCardSaleHandler: val.PackageCardSaleHandler,
          SavingCardDeduction: val.packageSavingCardDeduction,
          PackageCardGoods: val.packageCardGoods,
          PackageCardLargessGoods: val.packageCardLargessGoods,
          Remark: val.Remark,
          MemberPreferentialAmount: parseFloat(val.MemberPreferentialAmountTotal || 0).toFixed(2),
        }))
        .toArray();

      var CardDeductionAmount = (parseFloat(that.CardDeductionAmount) + parseFloat(that.cardDeductionAmount)).toFixed(2);

      var params = {
        BillID: that.BillID ? that.BillID : '',
        CustomerID: that.customerID,
        BillDate: that.getBillDate(),
        Amount: parseFloat(that.Amount || 0).toFixed(2),
        PayAmount: parseFloat(that.PayCashAmount || 0).toFixed(2),
        ArrearAmount: parseFloat(that.ArrearAmount || 0).toFixed(2),
        PricePreferentialAmount: parseFloat(that.PricePreferentialAmount || 0).toFixed(2),
        CardPreferentialAmount: parseFloat(that.CardPreferentialAmount || 0).toFixed(2),
        Remark: that.Remark,
        Project: Project,
        Product: Product,
        GeneralCard: GeneralCard,
        TimeCard: TimeCard,
        SavingCard: SavingCard,
        PackageCard: PackageCard,
        CardDeductionAmount: CardDeductionAmount,
        MemberPreferentialAmount: parseFloat(that.memberPreferentialAmount || 0).toFixed(2),
      };

      return params;
    },
    // 套餐明细
    packDetailClick: function (row) {
      row.isPackDetail = !row.isPackDetail;
    },
    /** 阶梯价格选中项 样式   */
    RechargeRulesClass(item, index) {
      // let mar = index == 0?"":""
      let style = item.RechargeRulesSelectIndex == index ? 'selectRules' : '';
      return ' ' + style;
    },
    /**  选择 阶梯价格规则  */
    selectRechargeRules(item, RechargeItem, index) {
      item.RechargeRulesSelectIndex = index;

      item.number = 1;
      item.Price = RechargeItem.Price;
      item.LargessPrice = RechargeItem.LargessPrice;
      item.largessPrice = RechargeItem.LargessPrice;
      item.Amount = RechargeItem.Price;
      item.totalPrice = RechargeItem.Price;
      item.PayAmount = RechargeItem.Price;
    },
    /**  打印小票  */
    printOrderReceipt() {
      var that = this;
      that.getOrderDetail();
    },
    /** 获取订单详情 */
    getOrderDetail() {
      var that = this;
      var params = {
        SaleBillID: that.orderNumber,
      };
      orderAPI
        .getOrderDetail(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.saleOrderDetail = res.Data;
            that.getPrintTemplate_list();
          }
        })
        .finally(function () {
          that.printLoading = false;
        });
    },
    /**  获取小票配置信息  */
    getReceiptConfig() {
      var that = this;
      cashierAPI
        .getReceiptConfigBill()
        .then((res) => {
          if (res.StateCode == 200) {
            that.cashierReceipt = res.Data;
          }
        })
        .finally(() => {});
    },

    /**  结账成功关闭弹窗  */
    closeSucceedDialog() {
      this.Remark = '';
    },
    /**  创建 挂单  */
    createPendingOrderClick() {
      let that = this;
      if (
        (that.selectProject.length <= 0) &
        (that.selectSavingCard.length <= 0) &
        (that.selectTimeCard.length <= 0) &
        (that.selectGeneralCard.length <= 0) &
        (that.selectPackageCard.length <= 0) &
        (that.selectProduct.length <= 0)
      ) {
        that.$message.error('请选择商品');
        return;
      }
      if (that.getBillDate() == null) {
        that.$message.error('请输入补录日期');
        return;
      }
      if (that.customerID == null) {
        that.$message.error('请填写客户信息');
        return;
      }

      that
        .$confirm('是否确定挂单信息？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })
        .then(() => {
          that.createBillPendingOrder();
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消挂单',
          });
        });
    },
    /**  创建销售挂单  */
    async createBillPendingOrder() {
      let that = this;
      let content = {
        selectProject: that.selectProject,
        selectProduct: that.selectProduct,
        selectSavingCard: that.selectSavingCard,
        selectTimeCard: that.selectTimeCard,
        selectGeneralCard: that.selectGeneralCard,
        selectPackageCard: that.selectPackageCard,
      };
      let params = Object.assign({ Content: JSON.stringify(content) }, that.getSaleBillParams());
      let res = await draftAPI.createBillPendingOrder(params);
      if (res.StateCode == 200) {
        that.selectProject = [];
        that.selectProduct = [];
        that.selectGeneralCard = [];
        that.selectTimeCard = [];
        that.selectSavingCard = [];
        that.selectPackageCard = [];
        that.orderAmount = that.Amount;
        that.Amount = 0; //订单金额
        that.PayAmount = 0; //现金支付金额
        that.payTotalPrice = 0; //待支付总金额
        that.ArrearAmount = 0; //欠款金额
        that.cardDeductionAmount = 0;
        that.CardDeductionAmount = 0; // 储值卡抵扣金额
        that.PricePreferentialAmount = 0; //手动改价优惠金额
        that.CardPreferentialAmount = 0; //卡优惠金额
        that.Remark = '';
        that.BillID = '';
        that.savingCardAllGoodsData();
        that.savingCardSomeGoodsData();
        // that.orderNumber = res.Message;

        that.$message.success('挂单成功');
      } else {
        that.$message.error(res.Message);
      }
    },

    /**    */
    getSaleTakeOrderHandle(SaleTypeHandlers, SaleBillHandler) {
      let tempHandle = SaleBillHandler.map((item) => {
        let selItem = SaleBillHandler.find((selItem) => selItem.TreatHandlerID == item.ID, {});
        let tempEmployees = [];
        if (selItem != undefined) {
          tempEmployees = selItem.Employee.map((emp) => {
            // 取出 经手人信息
            let tempEmp = item.Employee.find((selEmp) => selEmp.EmployeeID == emp.EmployeeID);
            // 有
            if (tempEmp != undefined) {
              return {
                Checked: true,
                Discount: emp.Scale,
                EmployeeID: tempEmp.EmployeeID,
                EmployeeName: tempEmp.EmployeeName,
                ID: tempEmp.ID,
                JobTypeName: tempEmp.JobTypeName,
                TreatHandlerID: tempEmp.TreatHandlerID,
              };
            } else {
              // 无
              return {
                EmployeeID: emp.EmployeeID,
                EmployeeName: emp.EmployeeName,
                Scale: emp.Scale,
              };
            }
          });
        }

        let temp = {
          Name: item.Name,
          ID: item.ID,
          Employee: tempEmployees,
        };
        return temp;
      });

      return tempHandle;
    },
    /**  取单参数  */
    saleTakeOrder(data) {
      let that = this;

      // that.saleTakeOrderData = data;
      that.$nextTick(() => {
        that.BillID = data.ID;
        let selectItem = JSON.parse(data.Content);
        that.Remark = data.Remark;
        /**  项目  */
        that.selectProject = Array.from(selectItem.selectProject).map((val) => {
          return val;
        });
        /**  产品  */
        that.selectProduct = Array.from(selectItem.selectProduct).map((val) => val);
        /**  储值卡  */
        that.selectSavingCard = Array.from(selectItem.selectSavingCard).map((val) => val);
        /**  时效卡  */
        that.selectTimeCard = Array.from(selectItem.selectTimeCard).map((val) => val);
        /**  通用次卡  */
        that.selectGeneralCard = Array.from(selectItem.selectGeneralCard).map((val) => val);
        /**  套餐卡  */
        that.selectPackageCard = Array.from(selectItem.selectPackageCard).map((val) => val);
        that.payAmountData();
      });
    },

    /** 销售经手人   */
    async saleHandler_allHandler(GoodTypes) {
      let that = this;
      try {
        let params = {
          GoodTypes: GoodTypes,
        };
        let res = await API.saleHandler_allHandler(params);
        if (res.StateCode == 200) {
          that.saleAllHandlerList = res.Data.map((i) => {
            return {
              Name: i.Name,
              Employee: i.Employee.map((j) => {
                j.Checked = false;
                return Object.assign({}, j);
              }),
            };
          });
        } else {
          that.$message.error(res.Message);
        }
      } catch (error) {
        //that.$message.error(error);
      }
    },

    /** 获取模板列表   */
    async getPrintTemplate_list() {
      let that = this;
      let params = { TemplateType: 'salebill' };
      let res = await orderAPI.getPrintTemplate_list(params);
      if (res.StateCode == 200) {
        that.templateTypeList = res.Data;
      } else {
        that.$message.error(res.Message);
      }
      return res;
    },
    /** 获取会员折扣   */
    async saleGoods_CustomerDiscount(ID) {
      let params = {
        CustomerID: this.customerID,
      };
      params[saleGoodsTypeMap[this.goodsType]] = ID;
      let res = await API[customerDiscountAPIMap[this.goodsType]](params);
      if (res.StateCode == 200) {
        this.customerDiscount = res.Data;
        return res.Data;
      } else {
        this.$message.error(res.Message);
      }
    },
    /**  获取员工最低折扣权限  */
    saleBill_employeeDiscount() {
      let that = this;
      let params = {};
      API.saleBill_employeeDiscount(params)
        .then((res) => {
          if (res.StateCode == 200) {
            this.employeeDiscount = res.Data ? res.Data.Discount : null;
          } else {
            that.$message.error(res.Message);
          }
        })
        .catch((fail) => {
          that.$message.error(fail);
        });
    },
  },
  mounted() {
    var that = this;

    if (localStorage.getItem('access-user')) {
      that.userName = JSON.parse(localStorage.getItem('access-user')).EmployeeName;
      that.entityName = JSON.parse(localStorage.getItem('access-user')).EntityName;
    }

    that.saleBill_employeeDiscount();
    that.projectHandlerData();
    that.productHandlerData();
    that.generalHandlerData();
    that.timeCardHandlerData();
    that.savingCardHandlerData();
    that.packageCardHandlerData();
    that.salePayMethodData();

    /* 获取全部商品分类 */
    that.getSaleGoodsGoodsType();
    /* 获取项目分类 */
    that.getSaleGoodsProjectCategory();
    /**  储值卡分类   */
    that.getSaleGoodsSavingCardCategoryd();
    /* 时效卡 */
    that.getSaleGoodsTimeCardCategory();
    /* 通用次卡 */
    that.getSaleGoodsGeneralCardCategory();
    /* 套餐卡 */
    that.getSaleGoodsPackageCardCategory();
    /* 产品 */
    that.getSaleGoodsProductCategory();

    if (that.customerID != null) {
      that.savingCardAllGoodsData();
      that.savingCardSomeGoodsData();
    }
  },

  beforeDestroy() {},
};
</script>

<style lang="scss">
.sale_content {
  font-size: 13px;
  height: 100%;

  .project_left {
    border-right: 1px solid #eee;
    height: 100%;
    color: #303133;

    .el-tabs__content {
      div {
        line-height: 23px;
      }
    }

    .el-tabs {
      height: calc(100% - 62px);

      .el-tabs__header {
        margin: 0;

        .el-tabs__nav-scroll {
          // padding-left: 15px;
          margin-left: 15px;
        }
      }

      .el-tabs__content {
        height: calc(100% - 40px);

        .el-tab-pane {
          height: 100%;

          .category_project {
            height: 100%;

            .category {
              height: 100%;

              .category_select {
                color: #ff8646;
              }
            }

            .project {
              height: 100%;
              overflow: auto;

              .el-collapse {
                .el-collapse-item__header {
                  background-color: #f5f7fa !important;
                  padding: 0 10px;
                }

                .el-collapse-item__content {
                  padding: 0;
                }
              }

              .category_sub_list {
                overflow-x: auto;

                .category_sub_select {
                  color: #ff8646;
                }
              }

              .project_list {
                // height: calc(100% - 53px);
                height: 100%;
              }

              .producct_list {
                // height: calc(100% - 53px);
                height: 100%;
              }
            }
          }
        }
      }
    }

    .el-main {
      padding: 0px;
    }

    .el-footer {
      height: initial !important;
      padding: 10px;
    }
  }

  .project_right {
    height: 100%;

    .el-main {
      padding: 0;

      .row_header {
        background-color: #fff7f3;
        padding: 10px;
      }

      .employee_num {
        width: 90px;

        .el-input-group__append {
          padding: 0 10px;
        }
      }

      .el-form-item__label {
        font-size: 13px !important;
      }
    }

    .el-footer {
      height: initial !important;
      padding: 10px;
    }
  }
}

.orderInfoRemark {
  position: absolute;
  background-color: rgba($color: #333, $alpha: 0.3);
  top: 0;
  right: 0;
  width: 100%;
  height: calc(100% - 54px);
  z-index: 1;

  .infoRemarContent {
    padding: 0px 10px 10px;
    background-color: #fff;
    position: absolute;
    bottom: 0;
    right: 0;
    left: 0;
    // width: 100%;
    font-size: 13px;
    color: #666;

    .el-form {
      .el-form-item {
        .el-form-item__label {
          font-size: 13px;
        }
      }
    }
  }

  .v-enter-active,
  .v-leave-active {
    transition: all 0.2s ease;
  }

  .v-enter,
  .v-leave-to {
    transform: opacity 0.5s;
    opacity: 0;
  }
}

.el-icon-sort {
  color: #666;
  font-size: 14px;
  transform: rotate(90deg);
}

.dialog_bill_detail {
  background-color: #fff7f3;
  padding: 15px;
  border-radius: 5px;
}

.el-scrollbar_height {
  height: 100%;

  .el-scrollbar__wrap {
    overflow-x: hidden;
  }
}

.el-scrollbar_x_width {
  .el-scrollbar__wrap {
    .el-scrollbar__view {
      white-space: nowrap;

      .el-menu-item {
        display: inline-block;
      }

      .is-active {
        a {
          color: #ff8646;
        }
      }
    }
  }
}

.el-input__inner {
  padding: 0 0 0 10px;
}

.saving_discount {
  .el-table__row {
    background-color: #f5f7fa;
  }
}

.savingCardAmount {
  .el-input__inner {
    border-top-right-radius: 0px;
    border-bottom-right-radius: 0px;
  }
}

.savingCardLargessAmount {
  .el-input__inner {
    border-top-left-radius: 0px;
    border-bottom-left-radius: 0px;
  }
}

.el-divider {
  margin-top: 15px;
  margin-bottom: 15px;
}

.sell-el-divider {
  margin-top: 5px;
  margin-bottom: 5px;
}

.selectRules {
  border: 1px solid #50bfff;
  box-shadow: 0 2px 6px 0 #50bfff !important;
}

.custom-packageDetail-changebtn {
  padding: 4px !important;
}
</style>
