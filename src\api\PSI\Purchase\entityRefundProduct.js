/**
 * Created by preference on 2021/12/08
 *  zmx
 */

import * as API from "@/api/index";
export default {
  /**  7.1.门店出货申请列表 */
  inventoryRefundList: (params) => {
    return API.POST("api/entityRefundApply/list", params);
  },
  /**  7.2.门店出货单详情 */
  inventoryRefundInfo: (params) => {
    return API.POST("api/entityRefundApply/info", params);
  },
  /** 7.3.门店出货单状态统计  */
  inventoryProductBillStatusNumber: (params) => {
    return API.POST("api/entityRefundApply/billStatusNumber", params);
  },
  /**  7.4.门店出货单 新增 */
  inventoryRefundCreate: (params) => {
    return API.POST("api/entityRefundApply/create", params);
  },
  /**  7.5.门店出货单 审核通过 */
  inventoryRefundApproved: (params) => {
    return API.POST("api/entityRefundApply/approved", params);
  },

  /** 7.6.门店 退货单取消  */
  inventoryRefundProductCancel: (params) => {
    return API.POST("api/entityRefundApply/cancel", params);
  },

  /** 7.7.门店 待退货 点击退货  */
  inventoryProductOutbound: (params) => {
    return API.POST("api/entityRefundApply/outbound", params);
  },

  /** 7.8.门店 待入库 点击入库  */
  inventoryProductInbound: (params) => {
    return API.POST("api/entityRefundApply/inbound", params);
  },
  /** 7.9 确认收款  */
  inventoryRefundProductStock: (params) => {
    return API.POST("api/entityRefundApply/pay", params);
  },
  /** 8.0 驳回里的关闭  */
  inventoryRefundProductStockBack: (params) => {
    return API.POST("api/entityRefundApply/cancelRejectApply", params);
  },
  /**  模板列表 */
  getPrintTemplate_list: (params) => {
    return API.POST("api/template/list", params);
  },
  /**  驳回的编辑 的保存 */
  entityRefundApply: (params) => {
    return API.POST("api/entityRefundApply/update", params);
  },
};
