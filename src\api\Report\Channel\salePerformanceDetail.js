import * as API from "@/api/index";

export default {
  // 获取门店
  getAllEntityApi: (params) => {
    return API.POST("api/entity/allEntity", params);
  },
  // 获取列表
  getdataListSaleApi: (params) => {
    return API.POST("api/channelSalePerformanceDetailStatement/list", params);
  },
  // 导出
  exportDataDetailSaleApi: (params) => {
    return API.exportExcel(
      "api/channelSalePerformanceDetailStatement/excel",
      params
    );
  },
};
