/**
 * Created by wsf on 2022/01/12.
 * 服务人员配置 api
 */
import  * as API from '@/api/index.js'

export default {
    // 获取服务人员列表
    getServiceList: params => {
        return API.POST('api/servicer/list', params)
    },
    // 新增服务人员
    createService: params => {
        return API.POST('api/servicer/create', params)
    },
    // 更新服务人员
    updateService: params => {
        return API.POST('api/servicer/update', params)
    },
    // 移动
    moveServicer: params => {
        return API.POST('api/servicer/move', params)
    }
}