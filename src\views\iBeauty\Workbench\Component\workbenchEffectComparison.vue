<template>
  <div v-loading.fullscreen="loading" body class="workbench_customer_effectComparison">
    <div style="display: flex; height: 100%">
      <div class="left">
        <el-row>
          <el-col :span="18" :gutter="10">
            <el-form size="small" label-width="40px">
              <el-form-item label="项目">
                <el-input v-model="projectName" placeholder="输入项目名称搜索" clearable></el-input>
              </el-form-item>
            </el-form>
          </el-col>
          <el-col :span="4" :offset="2" class="text_right padrt_10">
            <el-button type="primary" size="small" @click="showAddDialog" v-prevent-click>新增</el-button>
          </el-col>
        </el-row>
        <el-table size="small" :data="photoCompareData.filter((data) => !projectName || data.ProjectName.toLowerCase().includes(projectName.toLowerCase()))" tooltip-effect="light" :show-header="false" @cell-click="projectClick" highlight-current-row>
          <el-table-column prop="ProjectName"></el-table-column>
        </el-table>
      </div>
      <div class="right ulpoad" v-show="showRight">
        <el-form label-width="55px">
          <el-form-item class="text_right">
            <el-button type="primary" size="small" @click="showContrastDialog" v-prevent-click>对比</el-button>
          </el-form-item>
          <el-form-item label="术前">
            <el-upload class="avatar-uploader" list-type="picture-card" action="#" :file-list="contrast.preoperativeImageList" :before-upload="preoperativeUpload" multiple>
              <i class="el-icon-plus avatar-uploader-icon"></i>
              <div slot="file" slot-scope="{ file }" style="height: 100px; width: 100px">
                <el-image class="el-upload-list__item-thumbnail" :id="file.ID" :src="file.AttachmentURL + '?x-oss-process=image/resize,h_100,m_lfit'" :preview-src-list="preoperative_img_src" :z-index="2000" fit="cover"></el-image>
                <div class="CreatedOn">
                  {{ file.CreatedOn.slice(0, 10) }}
                </div>
                <span class="el-upload-list__item-actions" style="width: 100px; height: 100px">
                  <span class="el-upload-list__item-preview" @click="preoperativePreview(file)">
                    <i class="el-icon-zoom-in"></i>
                  </span>
                  <span class="el-upload-list__item-preview" @click="preoperativeRemove(file)">
                    <i class="el-icon-delete"></i>
                  </span>
                </span>
              </div>
            </el-upload>
          </el-form-item>
          <el-form-item label="术后">
            <el-upload class="avatar-uploader" list-type="picture-card" action="#" :file-list="contrast.operationImageList" :before-upload="operationUpload" multiple>
              <i class="el-icon-plus avatar-uploader-icon"></i>

              <div slot="file" slot-scope="{ file }" style="height: 100px; width: 100px">
                <el-image class="el-upload-list__item-thumbnail" :id="file.ID" :src="file.AttachmentURL + '?x-oss-process=image/resize,h_100,m_lfit'" :preview-src-list="operation_img_src" :z-index="2000" fit="cover"></el-image>
                <div class="CreatedOn">
                  {{ file.CreatedOn.slice(0, 10) }}
                </div>
                <span class="el-upload-list__item-actions" style="width: 100px; height: 100px">
                  <span class="el-upload-list__item-preview" @click="operationPreview(file)">
                    <i class="el-icon-zoom-in"></i>
                  </span>
                  <span class="el-upload-list__item-preview" @click="operationRemove(file)">
                    <i class="el-icon-delete"></i>
                  </span>
                </span>
              </div>
            </el-upload>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <!-- 新增对比照片弹出层 -->
    <el-dialog title="新增对比照片" :visible.sync="addDialog" width="35%" custom-class="dialog-add ulpoad" append-to-body @close="addDialogClose">
      <el-form label-width="60px" size="small" :model="ruleForm" :rules="rules" ref="ruleForm">
        <el-form-item label="项目" prop="ProjectID">
          <!-- v-loadmore="loadMoreProiect" -->
          <el-select v-model="ruleForm.ProjectID" filterable placeholder="请选择项目" clearable remote :remote-method="project_list" @focus="(val) => project_list('', val)">
            <el-option v-for="item in projectData" :key="item.ProjectID" :label="item.ProjectName" :value="item.ProjectID"></el-option>
          </el-select>
          <el-radio-group v-model="radio" style="margin-left: 20px" @change="radioGroupChange">
            <el-radio-button :label="10">所有项目</el-radio-button>
            <el-radio-button :label="20">购买项目</el-radio-button>
            <el-radio-button :label="30">已消耗项目</el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="术前" prop="preoperativeImageList">
          <el-upload class="avatar-uploader" list-type="picture-card" action="#" :file-list="ruleForm.preoperativeImageList" :before-upload="preoperativeCommodityMainbeforeUpload" :on-remove="preoperativeCommodityMainRemove" multiple>
            <i class="el-icon-plus avatar-uploader-icon"></i>

            <div slot="file" slot-scope="{ file }" style="height: 100px; width: 100px">
              <el-image class="el-upload-list__item-thumbnail" :id="file.uid" :src="file.AttachmentURL" :preview-src-list="preoperative_src_list" :z-index="2000" fit="cover"></el-image>
              <div class="CreatedOn">
                {{ file.CreatedOn }}
              </div>
              <span class="el-upload-list__item-actions" style="width: 100px; height: 100px">
                <span class="el-upload-list__item-preview" @click="preoperativeDialogPreview(file)">
                  <i class="el-icon-zoom-in"></i>
                </span>
                <span class="el-upload-list__item-preview" @click="preoperativeCommodityMainRemove(file)">
                  <i class="el-icon-delete"></i>
                </span>
              </span>
            </div>
          </el-upload>
        </el-form-item>
        <el-form-item label="术后" prop="operationImageList">
          <el-upload class="avatar-uploader" list-type="picture-card" action="#" :file-list="ruleForm.operationImageList" :before-upload="operationCommodityMainbeforeUpload" :on-remove="operationCommodityMainRemove" multiple>
            <i class="el-icon-plus avatar-uploader-icon"></i>

            <div slot="file" slot-scope="{ file }" style="height: 100px; widht: 100px">
              <el-image class="el-upload-list__item-thumbnail" :id="file.uid" :src="file.AttachmentURL" :preview-src-list="operation_src_list" :z-index="2000" fit="cover"></el-image>
              <div class="CreatedOn">
                {{ file.CreatedOn }}
              </div>
              <span class="el-upload-list__item-actions" style="width: 100px; height: 100px">
                <span class="el-upload-list__item-preview" @click="operationDialogPreview(file)">
                  <i class="el-icon-zoom-in"></i>
                </span>
                <span class="el-upload-list__item-preview" @click="operationCommodityMainRemove(file)">
                  <i class="el-icon-delete"></i>
                </span>
              </span>
            </div>
          </el-upload>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button size="small" @click="addDialog = false">取 消</el-button>
        <el-button size="small" type="primary" @click="saveComparisonPhotos" :loading="modalLoading">保 存</el-button>
      </span>
    </el-dialog>
    <!-- 对比照片弹出层 -->
    <el-dialog title="照片对比" :visible.sync="contrastDialog" width="50%" custom-class="dialog-contrast" append-to-body>
      <div style="display: flex">
        <div class="show_left">
          <div class="name_box">
            项目: <span>{{ contrast.ProjectName }}</span>
          </div>
          <el-scrollbar style="height: 650px">
            <div class="preoperative_box">
              <span>术前</span>
              <div class="show_box">
                <el-row>
                  <draggable v-model="contrast.preoperativeImageList" chosenClass="chosen" v-bind="{ group: { name: 'falgs', pull: 'clone', put: false } }">
                    <el-col v-for="img in contrast.preoperativeImageList" :key="img.ID" style="width: 100px; margin: 10px; display: inline-block">
                      <img class="img_box" :src="img.AttachmentURL" />
                      <div style="width: 100px; height: 20px; line-height: 20px; text-align: center; color: #fff; background: rgba(0, 0, 0, 0.5); position: relative; bottom: 22px; border-radius: 0 0 5px 5px">
                        {{ img.CreatedOn.slice(0, 10) }}
                      </div>
                    </el-col>
                  </draggable>
                </el-row>
              </div>
            </div>
            <div class="operation_box">
              <span>术后</span>
              <div class="show_box">
                <el-row>
                  <draggable v-model="contrast.operationImageList" chosenClass="chosen" v-bind="{ group: { name: 'falgs', pull: 'clone', put: false } }">
                    <el-col v-for="img in contrast.operationImageList" :key="img.ID" style="width: 100px; margin: 10px; display: inline-block">
                      <img class="img_box" :src="img.AttachmentURL" />
                      <div style="width: 100px; height: 20px; line-height: 20px; text-align: center; color: #fff; background: rgba(0, 0, 0, 0.5); position: relative; bottom: 22px; border-radius: 0 0 5px 5px">
                        {{ img.CreatedOn.slice(0, 10) }}
                      </div>
                    </el-col>
                  </draggable>
                </el-row>
              </div>
            </div>
          </el-scrollbar>
        </div>
        <div class="show_right">
          <el-radio-group v-model="spec" style="margin-left: 20px" @change="specChange">
            <el-radio-button :label="10">1x1</el-radio-button>
            <el-radio-button :label="20">1x2</el-radio-button>
            <el-radio-button :label="30">2x2</el-radio-button>
          </el-radio-group>
          <div style="margin-left: 20px; margin-top: 10px">
            <div v-if="spec == 10" style="width: 100%; height: 300px">
              <div style="width: 50%; height: 100%; border: 1px dashed #999; border-radius: 20px; text-align: center">
                <draggable chosenClass="chosen" v-model="contrast.contrastPhotos.img11" @add="addCollection($event, 'img11')" v-bind="{ group: { name: 'falgs', pull: 'clone', put: true } }" style="width: 100%; height: 100%; border-radius: 20px; overflow: hidden">
                  <div style="width: 100%; height: 100%; border-radius: 20px; position: relative" @mousewheel.prevent="rollImg($event, 'img11')" class="box11">
                    <img v-for="(img, index) in contrast.contrastPhotos.img11" :key="index" :src="img.AttachmentURL" style="width: 100%; height: 100%; border-radius: 20px; position: absolute; cursor: move; top: 0; left: 0" ref="imgDiv11" class="img11" @mousedown="move($event, 'img11')" />
                  </div>
                </draggable>
              </div>
            </div>
            <div v-if="spec == 20" style="width: 100%; height: 300px; display: flex">
              <div style="width: 50%; height: 100%; border: 1px dashed #999; border-radius: 20px; text-align: center; margin-right: 20px">
                <draggable chosenClass="chosen" v-model="contrast.contrastPhotos.img21" @add="addCollection($event, 'img21')" v-bind="{ group: { name: 'falgs', pull: 'clone', put: true } }" style="width: 100%; height: 100%; border-radius: 20px; overflow: hidden">
                  <div style="width: 100%; height: 100%; border-radius: 20px; position: relative" @mousewheel.prevent="rollImg($event, 'img21')" class="box21">
                    <img v-for="(img, index) in contrast.contrastPhotos.img21" :key="index" :src="img.AttachmentURL" style="width: 100%; height: 100%; border-radius: 20px; position: absolute; cursor: move; top: 0; left: 0" ref="imgDiv21" class="img21" @mousedown="move($event, 'img21')" />
                  </div>
                </draggable>
              </div>
              <div style="width: 50%; height: 100%; border: 1px dashed #999; border-radius: 20px; text-align: center">
                <draggable chosenClass="chosen" v-model="contrast.contrastPhotos.img22" @add="addCollection($event, 'img22')" v-bind="{ group: { name: 'falgs', pull: 'clone', put: true } }" style="width: 100%; height: 100%; border-radius: 20px; overflow: hidden">
                  <div style="width: 100%; height: 100%; border-radius: 20px; position: relative" @mousewheel.prevent="rollImg($event, 'img22')" class="box22">
                    <img v-for="(img, index) in contrast.contrastPhotos.img22" :key="index" :src="img.AttachmentURL" style="width: 100%; height: 100%; border-radius: 20px; position: absolute; cursor: move; top: 0; left: 0" ref="imgDiv22" class="img22" @mousedown="move($event, 'img22')" />
                  </div>
                </draggable>
              </div>
            </div>
            <div v-if="spec == 30" style="width: 100%; height: 300px">
              <div style="width: 100%; height: 300px; display: flex">
                <div style="width: 50%; height: 100%; border: 1px dashed #999; border-radius: 20px; text-align: center; margin-right: 20px">
                  <draggable chosenClass="chosen" v-model="contrast.contrastPhotos.img41" @add="addCollection($event, 'img41')" v-bind="{ group: { name: 'falgs', pull: 'clone', put: true } }" style="width: 100%; height: 100%; border-radius: 20px; overflow: hidden">
                    <div style="width: 100%; height: 100%; border-radius: 20px; position: relative" @mousewheel.prevent="rollImg($event, 'img41')" class="box41">
                      <img v-for="(img, index) in contrast.contrastPhotos.img41" :key="index" :src="img.AttachmentURL" style="width: 100%; height: 100%; border-radius: 20px; position: absolute; cursor: move; top: 0; left: 0" ref="imgDiv41" class="img41" @mousedown="move($event, 'img41')" />
                    </div>
                  </draggable>
                </div>
                <div style="width: 50%; height: 100%; border: 1px dashed #999; border-radius: 20px; text-align: center">
                  <draggable chosenClass="chosen" v-model="contrast.contrastPhotos.img42" @add="addCollection($event, 'img42')" v-bind="{ group: { name: 'falgs', pull: 'clone', put: true } }" style="width: 100%; height: 100%; border-radius: 20px; overflow: hidden">
                    <div style="width: 100%; height: 100%; border-radius: 20px; position: relative" @mousewheel.prevent="rollImg($event, 'img42')" class="box42">
                      <img v-for="(img, index) in contrast.contrastPhotos.img42" :key="index" :src="img.AttachmentURL" style="width: 100%; height: 100%; border-radius: 20px; position: absolute; cursor: move; top: 0; left: 0" ref="imgDiv42" class="img42" @mousedown="move($event, 'img42')" />
                    </div>
                  </draggable>
                </div>
              </div>
              <div style="width: 100%; height: 300px; display: flex; margin-top: 20px">
                <div style="width: 50%; height: 100%; border: 1px dashed #999; border-radius: 20px; text-align: center; margin-right: 20px">
                  <draggable chosenClass="chosen" v-model="contrast.contrastPhotos.img43" @add="addCollection($event, 'img43')" v-bind="{ group: { name: 'falgs', pull: 'clone', put: true } }" style="width: 100%; height: 100%; border-radius: 20px; overflow: hidden">
                    <div style="width: 100%; height: 100%; border-radius: 20px; position: relative" @mousewheel.prevent="rollImg($event, 'img43')" class="box43">
                      <img v-for="(img, index) in contrast.contrastPhotos.img43" :key="index" :src="img.AttachmentURL" style="width: 100%; height: 100%; border-radius: 20px; position: absolute; cursor: move; top: 0; left: 0" ref="imgDiv43" class="img43" @mousedown="move($event, 'img43')" />
                    </div>
                  </draggable>
                </div>
                <div style="width: 50%; height: 100%; border: 1px dashed #999; border-radius: 20px; text-align: center">
                  <draggable chosenClass="chosen" v-model="contrast.contrastPhotos.img44" @add="addCollection($event, 'img44')" v-bind="{ group: { name: 'falgs', pull: 'clone', put: true } }" style="width: 100%; height: 100%; border-radius: 20px; overflow: hidden">
                    <div style="width: 100%; height: 100%; border-radius: 20px; position: relative" @mousewheel.prevent="rollImg($event, 'img44')" class="box44">
                      <img v-for="(img, index) in contrast.contrastPhotos.img44" :key="index" :src="img.AttachmentURL" style="width: 100%; height: 100%; border-radius: 20px; position: absolute; cursor: move; top: 0; left: 0" ref="imgDiv44" class="img44" @mousedown="move($event, 'img44')" />
                    </div>
                  </draggable>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>
    <el-image-viewer v-if="showViewer" :initialIndex="initialIndex" :on-close="closeViewer" :url-list="previewImageList" :z-index="3005"/>
  </div>
</template>

<script>
import API from "@/api/CRM/Customer/effectComparison.js";
import draggable from "vuedraggable";
import date from "@/components/js/date";
import ElImageViewer from "element-ui/packages/image/src/image-viewer";
export default {
  name: "workbench_customer_effectComparison",
  props: {
    CustomerID: Number,
  },
  /** 监听数据变化   */
  watch: {
    "ruleForm.preoperativeImageList": {
      deep: true,
      immediate: true,
      handler(val) {
        this.preoperative_src_list = [];
        this.preoperative_src_list = val.map((i) => i.AttachmentURL);
      },
    },
    "ruleForm.operationImageList": {
      deep: true,
      immediate: true,
      handler(val) {
        this.operation_src_list = [];
        this.operation_src_list = val.map((i) => i.AttachmentURL);
      },
    },
    "contrast.preoperativeImageList": {
      deep: true,
      immediate: true,
      handler(val) {
        this.preoperative_img_src = [];
        this.preoperative_img_src = val.map((i) => i.AttachmentURL);
      },
    },
    "contrast.operationImageList": {
      deep: true,
      immediate: true,
      handler(val) {
        this.operation_img_src = [];
        this.operation_img_src = val.map((i) => i.AttachmentURL);
      },
    },
  },
  /**  引入的组件  */
  components: {
    draggable,
    ElImageViewer
  },
  /**  Vue 实例的数据对象**/
  data() {
    return {
      loading: false,
      zoomD: 100,
      multiples: 1,
      addDialog: false,
      contrastDialog: false,
      modalLoading: false,
      projectName: "", // 搜索项目
      ComparisonID: "", // 对比ID
      radio: "10",
      spec: "20",
      isAgain: false,
      showRight: false,
      ProiectListTotal: "",
      PageNum: 1,
      photoCompareData: [], // 对比照片列表
      contrastmageList: [],
      img: "",
      ruleForm: {
        ProjectID: "",
        preoperativeImageList: [], // 新增对比照片术前照片列表
        operationImageList: [], // 新增对比照片术后照片列表
      },
      projectData: [], // 新增对比照的项目列表
      preoperative_src_list: [],
      operation_src_list: [],
      preoperative_img_src: [],
      operation_img_src: [],
      contrast: {
        ProjectName: "", // 项目名称
        preoperativeImageList: [], // 对比照片术前照片
        operationImageList: [], // 对比照片术后照片
        contrastPhotos: {
          img11: [],
          img21: [],
          img22: [],
          img41: [],
          img42: [],
          img43: [],
          img44: [],
        },
      },
      rules: {
        ProjectID: [
          {
            required: true,
            message: "请选择项目",
            trigger: ["blur", "change"],
          },
        ],
      },
      showViewer:false,
      initialIndex:0 ,
      previewImageList:[],
    };
  },
  /**计算属性  */
  computed: {},
  /**  方法集合  */
  methods: {
    /**    */
    closeViewer(){
      let that = this;
      that.showViewer = false;
    },
    /* 清除数据 */
    clearData() {
      let that = this;
      that.showRight = false;
      that.contrast = {
        ProjectName: "", // 项目名称
        preoperativeImageList: [], // 对比照片术前照片
        operationImageList: [], // 对比照片术后照片
        contrastPhotos: {
          img11: [],
          img21: [],
          img22: [],
          img41: [],
          img42: [],
          img43: [],
          img44: [],
        },
      };
    },
    /* 新增对比照片 */
    showAddDialog() {
      let that = this;
      that.addDialog = true;
      that.radio = "10";
      if (this.$refs.ruleForm) {
        this.$refs["ruleForm"].resetFields();
      }
      that.project_list();
    },
    /* 点击项目 */
    projectClick(row) {
      let that = this;
      that.contrast.preoperativeImageList = [];
      that.contrast.operationImageList = [];
      that.contrast.ProjectName = "";
      that.contrast.ProjectName = row.ProjectName;
      that.ComparisonID = row.ID;
      that.showRight = true;
      that.isAgain = false;
      row.Attachment.forEach((item) => {
        if (item.PhotoType == 10) {
          that.contrast.preoperativeImageList.push(item);
        } else if (item.PhotoType == 20) {
          that.contrast.operationImageList.push(item);
        }
      });
    },
    /* 所有、购买、已消耗切换 */
    radioGroupChange() {
      let that = this;
      that.project_list();
    },
    /* 保存对比照片 */
    saveComparisonPhotos() {
      let that = this;
      that.$refs.ruleForm.validate((valid) => {
        if (valid) {
          if (that.ruleForm.preoperativeImageList.length == 0 && that.ruleForm.operationImageList.length == 0) {
            this.$message({
              type: "info",
              message: "请上传术前或术后任意一张照片",
            });
          } else {
            that.modalLoading = true;
            let Attachment = [];
            Attachment.push(...that.ruleForm.preoperativeImageList, ...that.ruleForm.operationImageList);
            let params = {
              CustomerID: that.CustomerID,
              ProjectID: that.ruleForm.ProjectID,
              Attachment,
            };
            API.createPhotoCompare(params)
              .then((res) => {
                if (res.StateCode === 200) {
                  that.$message.success({
                    message: "新增成功",
                    duration: 2000,
                  });
                  that.addDialog = false;
                  (that.ruleForm.preoperativeImageList = []), (that.ruleForm.operationImageList = []), (that.ruleForm.ProjectID = ""), (that.preoperative_src_list = []), (that.operation_src_list = []), (that.radio = "10"), that.photoCompare();
                } else {
                  that.$message.error({
                    message: res.Message,
                    duration: 2000,
                  });
                }
              })
              .finally(function () {
                that.modalLoading = false;
              });
          }
        }
      });
    },
    /* 新增对比照片术前图片上传 */
    preoperativeCommodityMainbeforeUpload(file) {
      let that = this;
      let reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = function (evt) {
        let base64 = evt.target.result;
        let params = {
          AttachmentURL: base64,
        };
        that.loading = true;
        API.addAttachment(params).then((res) => {
          that.loading = false;
          if (res.StateCode === 200) {
            that.ruleForm.preoperativeImageList.push({
              PhotoType: 10,
              AttachmentURL: res.Data.AttachmentURL,
              CreatedOn: date.formatDate.format(new Date(), "YYYY-MM-DD"),
            });
          }
        });
      };
      return false;
    },
    /* 新增对比照片术前图片查看大图 */
    preoperativeDialogPreview(file) {
      document.getElementById(file.uid).click();
    },
    /* 新增对比照片术前图片删除 */
    preoperativeCommodityMainRemove(file) {
      let that = this;
      if (file && file.status !== "success") return;
      let index = that.ruleForm.preoperativeImageList.findIndex((item) => item.AttachmentURL == file.AttachmentURL);
      that.ruleForm.preoperativeImageList.splice(index, 1);
    },
    /* 新增对比照片术后图片上传 */
    operationCommodityMainbeforeUpload(file) {
      let that = this;
      let reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = function (evt) {
        let base64 = evt.target.result;
        let params = {
          AttachmentURL: base64,
        };
        that.loading = true;
        API.addAttachment(params).then((res) => {
          that.loading = false;
          if (res.StateCode === 200) {
            that.ruleForm.operationImageList.push({
              PhotoType: 20,
              AttachmentURL: res.Data.AttachmentURL,
              CreatedOn: date.formatDate.format(new Date(), "YYYY-MM-DD"),
            });
          }
        });
      };
      return false;
    },
    /* 新增对比照片术后图片查看大图 */
    operationDialogPreview(file) {
      document.getElementById(file.uid).click();
    },
    /* 新增对比照片术后图片删除 */
    operationCommodityMainRemove(file) {
      let that = this;
      if (file && file.status !== "success") return;
      let index = that.ruleForm.operationImageList.findIndex((item) => item.AttachmentURL == file.AttachmentURL);
      that.ruleForm.operationImageList.splice(index, 1);
    },
    /* 对比列表追加术前照片 */
    preoperativeUpload(file) {
      let that = this;
      that.isAgain = true;
      let reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = function (evt) {
        let base64 = evt.target.result;
        let params = {
          AttachmentURL: base64,
        };
        API.addAttachment(params).then((res) => {
          if (res.StateCode === 200) {
            let param = {
              PhotoCompareID: that.ComparisonID, //照片对比ID
              PhotoType: 10, //照片类型（10：术前、20：术后）
              AttachmentURL: res.Data.AttachmentURL, //图片
            };
            API.addToPhoto(param).then((res) => {
              if (res.StateCode === 200) {
                that.$message.success({
                  message: "照片追加成功",
                  duration: 2000,
                });
                that.photoCompare();
              }
            });
          }
        });
      };
      return false;
    },
    /* 对比列表删除术前照片 */
    preoperativeRemove(file) {
      let that = this;
      that.isAgain = false;
      this.$confirm("此操作将永久删除该照片, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          let params = {
            ID: file.ID,
          };
          API.deletePhoto(params).then((res) => {
            if (res.StateCode === 200) {
              this.$message({
                type: "success",
                message: "删除成功!",
              });
              let removeIndex = that.contrast.preoperativeImageList.findIndex((val) => {
                return val.ID == file.ID;
              });
              that.contrast.preoperativeImageList.splice(removeIndex, 1);
              that.photoCompare();
            } else {
              that.$message.error({
                message: res.Message,
                duration: 2000,
              });
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    /* 对比列表术前照片查看大图 */
    preoperativePreview(file) {
      let that = this;
      that.showViewer = true;
      that.initialIndex = that.contrast.preoperativeImageList.findIndex((i) => i.AttachmentURL == file.AttachmentURL);
      that.previewImageList = that.contrast.preoperativeImageList.map(i=>i.AttachmentURL);
    },
    /* 对比列表追加术后照片 */
    operationUpload(file) {
      let that = this;
      that.isAgain = true;
      let reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = function (evt) {
        let base64 = evt.target.result;
        let params = {
          AttachmentURL: base64,
        };
        API.addAttachment(params).then((res) => {
          if (res.StateCode === 200) {
            let param = {
              PhotoCompareID: that.ComparisonID, //照片对比ID
              PhotoType: 20, //照片类型（10：术前、20：术后）
              AttachmentURL: res.Data.AttachmentURL, //图片
            };
            API.addToPhoto(param).then((re) => {
              if (re.StateCode === 200) {
                that.$message.success({
                  message: "照片追加成功",
                  duration: 2000,
                });
                that.photoCompare();
              }
            });
          }
        });
      };
      return false;
    },
    /* 对比列表删除术后照片 */
    operationRemove(file) {
      let that = this;
      that.isAgain = false;
      this.$confirm("此操作将永久删除该照片, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          let params = {
            ID: file.ID,
          };
          API.deletePhoto(params).then((res) => {
            if (res.StateCode === 200) {
              this.$message({
                type: "success",
                message: "删除成功!",
              });
              let removeIndex = that.contrast.operationImageList.findIndex((val) => {
                return val.ID == file.ID;
              });
              that.contrast.operationImageList.splice(removeIndex, 1);
              that.photoCompare();
            } else {
              that.$message.error({
                message: res.Message,
                duration: 2000,
              });
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    /* 对比列表术后照片查看大图 */
    operationPreview(file) {
      let that = this;
      // document.getElementById(file.ID).click();
      that.showViewer = true;
      that.initialIndex = that.contrast.operationImageList.findIndex((i) => i.AttachmentURL == file.AttachmentURL);
      that.previewImageList = that.contrast.operationImageList.map(i=>i.AttachmentURL);
    },
    /* 获取项目 */
    project_list(query) {
      let that = this;
      let params = {
        CustomerID: that.CustomerID, //顾客id
        Type: that.radio, //10  :全部   20：销售项目   30：消耗项目
        Name: query,
        PageNum: that.PageNum,
      };
      API.project_list(params).then((res) => {
        if (res.StateCode === 200) {
          that.projectData = res.List;
          that.ProiectListTotal = res.Total;
        } else {
          that.$message.error({
            message: res.Message,
            duration: 2000,
          });
        }
      });
    },
    /* 加载更多项目 */
    loadMoreProiect() {
      let that = this;
      if (that.ProiectListTotal > that.projectData.length) {
        that.productPageNum++;
        that.project_list();
      }
    },
    /* 获取对比照片列表 */
    photoCompare() {
      let that = this;
      that.photoCompareData = [];
      let params = {
        CustomerID: that.CustomerID, //顾客id
        Name: "",
      };
      API.photoCompare(params).then((res) => {
        if (res.StateCode === 200) {
          that.photoCompareData = res.Data;
          if (that.isAgain) {
            res.Data.forEach((val) => {
              if (that.ComparisonID == val.ID) {
                val.Attachment.forEach((item) => {
                  if (item.PhotoType == 10) {
                    if (that.contrast.preoperativeImageList.length) {
                      let index = that.contrast.preoperativeImageList.findIndex((val) => {
                        return val.ID == item.ID;
                      });
                      if (index == -1) {
                        that.contrast.preoperativeImageList.push(item);
                      }
                    } else {
                      that.contrast.preoperativeImageList.push(item);
                    }
                  } else if (item.PhotoType == 20) {
                    if (that.contrast.operationImageList.length) {
                      let index = that.contrast.operationImageList.findIndex((val) => {
                        return val.ID == item.ID;
                      });
                      if (index == -1) {
                        that.contrast.operationImageList.push(item);
                      }
                    } else {
                      that.contrast.operationImageList.push(item);
                    }
                  }
                });
              }
            });
          }
        } else {
          that.$message.error({
            message: res.Message,
            duration: 2000,
          });
        }
      });
    },
    /* 对比弹出层 */
    showContrastDialog() {
      let that = this;
      that.contrastDialog = true;
      that.spec = "20";
      that.contrast.contrastPhotos = {
        img11: [],
        img21: [],
        img22: [],
        img41: [],
        img42: [],
        img43: [],
        img44: [],
      };
    },
    /* 1x1 1x2 2x2布局切换 */
    specChange() {
      let that = this;
      that.multiples = 1;
      that.contrast.contrastPhotos = {
        img11: [],
        img21: [],
        img22: [],
        img41: [],
        img42: [],
        img43: [],
        img44: [],
      };
    },
    /* 图片拖拽完成 */
    addCollection($event, e) {
      let that = this;
      that.multiples = 1;
      switch (e) {
        case "img11":
          if (that.contrast.contrastPhotos.img11.length > 1) {
            that.contrast.contrastPhotos.img11 = that.contrast.contrastPhotos.img11.splice($event.newIndex, 1);
          }
          break;
        case "img21":
          if (that.contrast.contrastPhotos.img21.length > 1) {
            that.contrast.contrastPhotos.img21 = that.contrast.contrastPhotos.img21.splice($event.newIndex, 1);
          }
          break;
        case "img22":
          if (that.contrast.contrastPhotos.img22.length > 1) {
            that.contrast.contrastPhotos.img22 = that.contrast.contrastPhotos.img22.splice($event.newIndex, 1);
          }
          break;
        case "img41":
          if (that.contrast.contrastPhotos.img41.length > 1) {
            that.contrast.contrastPhotos.img41 = that.contrast.contrastPhotos.img41.splice($event.newIndex, 1);
          }
          break;
        case "img42":
          if (that.contrast.contrastPhotos.img42.length > 1) {
            that.contrast.contrastPhotos.img42 = that.contrast.contrastPhotos.img42.splice($event.newIndex, 1);
          }
          break;
        case "img43":
          if (that.contrast.contrastPhotos.img43.length > 1) {
            that.contrast.contrastPhotos.img43 = that.contrast.contrastPhotos.img43.splice($event.newIndex, 1);
          }
          break;
        case "img44":
          if (that.contrast.contrastPhotos.img44.length > 1) {
            that.contrast.contrastPhotos.img44 = that.contrast.contrastPhotos.img44.splice($event.newIndex, 1);
          }
          break;
      }
    },
    /* 移动图层 */
    move(e, type) {
      let ty = type;
      let img;
      let box;
      e.preventDefault();
      // 获取元素
      switch (ty) {
        case "img11":
          img = document.querySelector(".img11");
          box = document.querySelector(".box11");
          break;
        case "img21":
          img = document.querySelector(".img21");
          box = document.querySelector(".box21");
          break;
        case "img22":
          img = document.querySelector(".img22");
          box = document.querySelector(".box22");
          break;
        case "img41":
          img = document.querySelector(".img41");
          box = document.querySelector(".box41");
          break;
        case "img42":
          img = document.querySelector(".img42");
          box = document.querySelector(".box42");
          break;
        case "img43":
          img = document.querySelector(".img43");
          box = document.querySelector(".box43");
          break;
        case "img44":
          img = document.querySelector(".img44");
          box = document.querySelector(".box44");
          break;
      }
      let x = e.pageX - img.offsetLeft;
      let y = e.pageY - img.offsetTop;
      // 添加鼠标移动事件
      box.addEventListener("mousemove", moved);
      function moved(e) {
        img.style.left = e.pageX - x + "px";
        img.style.top = e.pageY - y + "px";
      }
      // 添加鼠标抬起事件，鼠标抬起，将事件移除
      img.addEventListener("mouseup", function () {
        box.removeEventListener("mousemove", moved);
      });
      // 鼠标离开父级元素，把事件移除
      box.addEventListener("mouseout", function () {
        box.removeEventListener("mousemove", moved);
      });
    },
    /* 缩放图片 */
    rollImg(event, type) {
      /* 获取当前页面的缩放比 若未设置zoom缩放比，则为默认100，原图大小 */
      if (this.zoomD <= 50) {
        this.zoomD = 50;
      }
      var zoom = this.zoomD + event.wheelDelta / 12;
      /*根据修改transform 改变图片缩放大小 */
      switch (type) {
        case "img11":
          if (this.$refs.imgDiv11 && this.$refs.imgDiv11.length != 0) {
            this.$refs.imgDiv11[0].style.transform = "scale(" + zoom / 100 + ")";
          }
          break;
        case "img21":
          if (this.$refs.imgDiv21 && this.$refs.imgDiv21.length != 0) {
            this.$refs.imgDiv21[0].style.transform = "scale(" + zoom / 100 + ")";
          }
          break;
        case "img22":
          if (this.$refs.imgDiv22 && this.$refs.imgDiv22.length != 0) {
            this.$refs.imgDiv22[0].style.transform = "scale(" + zoom / 100 + ")";
          }
          break;
        case "img41":
          if (this.$refs.imgDiv41 && this.$refs.imgDiv41.length != 0) {
            this.$refs.imgDiv41[0].style.transform = "scale(" + zoom / 100 + ")";
          }
          break;
        case "img42":
          if (this.$refs.imgDiv42 && this.$refs.imgDiv42.length != 0) {
            this.$refs.imgDiv42[0].style.transform = "scale(" + zoom / 100 + ")";
          }
          break;
        case "img43":
          if (this.$refs.imgDiv43 && this.$refs.imgDiv43.length != 0) {
            this.$refs.imgDiv43[0].style.transform = "scale(" + zoom / 100 + ")";
          }
          break;
        case "img44":
          if (this.$refs.imgDiv44 && this.$refs.imgDiv44.length != 0) {
            this.$refs.imgDiv44[0].style.transform = "scale(" + zoom / 100 + ")";
          }
          break;
      }
      this.zoomD = zoom;
    },
    /* 新增弹出层关闭 */
    addDialogClose() {
      let that = this;
      that.ruleForm.preoperativeImageList = [];
      that.ruleForm.operationImageList = [];
    },
  },
  /** 创建实例之前执行函数   */
  beforeCreate() {},
  /**  实例创建完成之后  */
  created() {},
  /**  在挂载开始之前被调用  */
  beforeMount() {},
  /**  实例被挂载后调用  */
  mounted() {},
  /**  数据更新 之前 调用  */
  beforeUpdate() {},
  /** 数据更新 完成 调用   */
  updated() {},
  /**  实例销毁之前调用  */
  beforeDestroy() {},
  /**  实例销毁后调用  */
  destroyed() {},
};
</script>

<style lang="scss">
.workbench_customer_effectComparison {
  height: calc(100vh - 110px);
  padding: 5px;
  .left {
    height: 100%;
    width: 40%;
    border-right: 1px solid #ccc;
  }
  .right {
    height: 100%;
    width: 60%;
    .el-form-item {
      margin-bottom: 10px !important;
    }
  }
  .el-table td,
  .el-table th.is-leaf {
    border-bottom: none !important;
  }
  .el-table__body-wrapper,
  .is-scrolling-none {
    height: 100% !important;
  }
}
.dialog-add {
  min-width: 600px;
}
.dialog-contrast {
  min-width: 1250px;
  min-height: 750px;
}
.ulpoad {
  .el-upload--picture-card {
    width: 100px;
    height: 100px;
    font-size: 16px !important;
  }
  .el-upload {
    width: 100px;
    height: 100px;
    line-height: 100px;
    font-size: 16px;
  }
  .el-upload-list--picture-card .el-upload-list__item {
    width: 100px;
    height: 100px;
    line-height: 100px;
    font-size: 16px;
  }
}
.show_left {
  width: 40%;
  .name_box {
    color: #000;
    background-color: rgb(245, 247, 250);
    padding: 10px;
  }
  .preoperative_box,
  .operation_box {
    color: #000;
    margin-top: 10px;
    .show_box {
      width: 100%;
      margin-top: 10px;
      border: 1px solid rgb(245, 247, 250);
      .img_box {
        width: 100px;
        height: 100px;
        border-radius: 5px;
      }
    }
  }
  .el-scrollbar {
    .el-scrollbar__wrap {
      overflow-x: hidden;
    }
    .el-scrollbar__bar {
      opacity: 0;
    }
    .el-scrollbar__thumb {
      display: none;
    }
  }
}
.show_right {
  width: 60%;
}
.CreatedOn {
  width: 100px;
  height: 20px;
  line-height: 20px;
  text-align: center;
  color: #fff;
  background: rgba(0, 0, 0, 0.5);
  position: absolute;
  bottom: 0px;
}
</style>
