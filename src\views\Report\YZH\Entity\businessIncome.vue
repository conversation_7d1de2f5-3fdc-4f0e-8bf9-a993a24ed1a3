<template>
  <div class="businessIncome content_body" v-loading="loading">
    <el-tabs v-model="activeName" type="border-card" @tab-click="handleClick">
      <!-- 营业收入明细表1 -->
      <el-tab-pane label="营业收入明细表" name="first">
        <div class="ScheduleOne">
          <!-- 搜索 -->
          <div class="nav_header" style="padding:15px 0 0 0">
            <el-form :inline="true" size="small" :model="ysOneSearchFrom" @submit.native.prevent>
              <el-form-item label="门店">
                <el-select v-model="ysOneSearchFrom.EntityID" clearable filterable placeholder="请选择门店" :default-first-option="true" @change="ScheduleOnehandleSearch" @clear="ScheduleOnehandleSearch">
                  <el-option v-for="item in storeEntityList" :key="item.ID" :label="item.EntityName" :value="item.ID"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="日期筛选">
                <el-date-picker v-model="ysOneSearchFrom.QueryDate" unlink-panels type="daterange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" @change="ScheduleOnehandleSearch" @clear="ScheduleOnehandleSearch" value-format="yyyy-MM-dd">
                </el-date-picker>
              </el-form-item>
              <el-form-item>
                <el-button type="primary" v-prevent-click @click="ScheduleOnehandleSearch">搜索</el-button>
              </el-form-item>
              <el-form-item>
                <el-button v-if="ScheduleOneExport" type="primary" size="small" :loading="downloadLoading" @click="downloadScheduleOne">导出</el-button>
              </el-form-item>
            </el-form>
          </div>
          <!-- 表格 -->
          <el-table :data="ScheduleOneTableData" style="width: 100%" show-summary :summary-method="getentityIncomeSumStatementForm" v-loading="entityIncomeStatementLoading">
            <el-table-column prop="BillDate" label="日期" width="120"></el-table-column>
            <el-table-column prop="EntityName" label="门店"></el-table-column>
            <el-table-column label="美容" align="center">
              <el-table-column prop="BeautyRecharge" label="充值">
                <template slot-scope="scope">
                  {{scope.row.BeautyRecharge | NumFormat}}
                </template>
              </el-table-column>
              <el-table-column prop="BeautyProduct" label="产品">
                <template slot-scope="scope">
                  {{scope.row.BeautyProduct | NumFormat}}
                </template>
              </el-table-column>
              <el-table-column prop="BeautyProject" label="项目">
                <template slot-scope="scope">
                  {{scope.row.BeautyProject | NumFormat}}
                </template>
              </el-table-column>
            </el-table-column>
            <el-table-column prop="BeautyTotal" label="美容现金业绩">
              <template slot-scope="scope">
                {{scope.row.BeautyTotal | NumFormat}}
              </template>
            </el-table-column>
            <el-table-column label="美发" align="center">
              <el-table-column prop="HairRecharge" label="充值">
                <template slot-scope="scope">
                  {{scope.row.HairRecharge | NumFormat}}
                </template>
              </el-table-column>
              <el-table-column prop="HairProduct" label="产品">
                <template slot-scope="scope">
                  {{scope.row.HairProduct | NumFormat}}
                </template>
              </el-table-column>
              <el-table-column prop="HairProject" label="项目">
                <template slot-scope="scope">
                  {{scope.row.HairProject | NumFormat}}
                </template>
              </el-table-column>
            </el-table-column>
            <el-table-column prop="HairTotal" label="美发现金业绩">
              <template slot-scope="scope">
                {{scope.row.HairTotal | NumFormat}}
              </template>
            </el-table-column>
            <el-table-column label="美容划卡">
              <el-table-column prop="BeautyTreatProduct" label="产品">
                <template slot-scope="scope">
                  {{scope.row.BeautyTreatProduct | NumFormat}}
                </template>
              </el-table-column>
              <el-table-column prop="BeautyTreatProject" label="项目">
                <template slot-scope="scope">
                  {{scope.row.BeautyTreatProject | NumFormat}}
                </template>
              </el-table-column>
            </el-table-column>
            <el-table-column prop="BeautyTreatTotal" label="美容划卡合计">
              <template slot-scope="scope">
                {{scope.row.BeautyTreatTotal | NumFormat}}
              </template>
            </el-table-column>
            <el-table-column label="美发划卡">
              <el-table-column prop="HairTreatProduct" label="产品">
                <template slot-scope="scope">
                  {{scope.row.HairTreatProduct | NumFormat}}
                </template>
              </el-table-column>
              <el-table-column prop="HairTreatProject" label="项目">
                <template slot-scope="scope">
                  {{scope.row.HairTreatProject | NumFormat}}
                </template>
              </el-table-column>
            </el-table-column>
            <el-table-column prop="HairTreatTotal" label="美发划卡合计">
              <template slot-scope="scope">
                {{scope.row.HairTreatTotal | NumFormat}}
              </template>
            </el-table-column>
            <el-table-column prop="BeautySavingCardRate" label="美容0.1">
              <template slot-scope="scope">
                {{scope.row.BeautySavingCardRate | NumFormat}}
              </template>
            </el-table-column>
            <el-table-column prop="HaidSavingCardRate" label="美发0.4">
              <template slot-scope="scope">
                {{scope.row.HaidSavingCardRate | NumFormat}}
              </template>
            </el-table-column>
            <el-table-column prop="BeautySavingCard" label="美容赠送金额">
              <template slot-scope="scope">
                {{scope.row.BeautySavingCard | NumFormat}}
              </template>
            </el-table-column>
            <el-table-column prop="HaidSavingCard" label="美发赠送金额">
              <template slot-scope="scope">
                {{scope.row.HaidSavingCard | NumFormat}}
              </template>
            </el-table-column>
            <el-table-column prop="BeautyTotalMoney" label="美容卡消耗金额合计">
              <template slot-scope="scope">
                {{scope.row.BeautyTotalMoney | NumFormat}}
              </template>
            </el-table-column>
            <el-table-column prop="HairTotalMoney" label="美发卡消耗金额合计">
              <template slot-scope="scope">
                {{scope.row.HairTotalMoney | NumFormat}}
              </template>
            </el-table-column>
          </el-table>
          <!-- 分页 -->
          <div class="pad_15 text_right">
            <el-pagination background v-if="ScheduleOnePaginations.total > 0" @current-change="handleScheduleOnePageChange" :current-page.sync="ScheduleOnePaginations.page" :page-size="ScheduleOnePaginations.page_size" :layout="ScheduleOnePaginations.layout" :total="ScheduleOnePaginations.total"></el-pagination>
          </div>
        </div>
      </el-tab-pane>
      <!-- 营业收入明细表2 -->
      <el-tab-pane label="营业收入表" name="second">
        <div class="ScheduleTwo">
          <!-- 搜索 -->
          <div class="nav_header" style="padding:15px 0 0 0">
            <el-form :inline="true" size="small" :model="ysTwoSearchFrom" @submit.native.prevent>
              <el-form-item label="门店">
                <el-select v-model="EntityID" clearable filterable placeholder="请选择门店(可多选)" :default-first-option="true" @change="ScheduleTwohandleSearch" @clear="ScheduleTwohandleSearch" multiple collapse-tags>
                  <el-option v-for="item in storeEntityList" :key="item.ID" :label="item.EntityName" :value="item.ID"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="日期筛选">
                <el-date-picker v-model="ysTwoSearchFrom.QueryDate" unlink-panels type="daterange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" @change="ScheduleTwohandleSearch" @clear="ScheduleTwohandleSearch" value-format="yyyy-MM-dd">
                </el-date-picker>
              </el-form-item>
              <el-form-item>
                <el-button type="primary" v-prevent-click @click="ScheduleTwohandleSearch">搜索</el-button>
              </el-form-item>
              <el-form-item>
                <el-button v-if="ScheduleTwoExport" type="primary" size="small" :loading="downloadLoading" @click="downloadScheduleTwo">导出</el-button>
              </el-form-item>
            </el-form>
          </div>
          <!-- 表格 -->
          <el-table :data="ScheduleTwoTableData" style="width: 100%" show-summary :summary-method="getentityIncomeSumStatementFormTwo" v-loading="entityIncomeStatementLoading">
            <el-table-column prop="EntityName" label="门店"></el-table-column>
            <el-table-column label="美容" align="center">
              <el-table-column prop="BeautyRecharge" label="充值">
                <template slot-scope="scope">
                  {{scope.row.BeautyRecharge | NumFormat}}
                </template>
              </el-table-column>
              <el-table-column prop="BeautyProduct" label="产品">
                <template slot-scope="scope">
                  {{scope.row.BeautyProduct | NumFormat}}
                </template>
              </el-table-column>
              <el-table-column prop="BeautyProject" label="项目">
                <template slot-scope="scope">
                  {{scope.row.BeautyProject | NumFormat}}
                </template>
              </el-table-column>
              <el-table-column prop="BeautyTotal" label="美容现金业绩">
                <template slot-scope="scope">
                  {{scope.row.BeautyTotal | NumFormat}}
                </template>
              </el-table-column>
            </el-table-column>
            <el-table-column label="美发" align="center">
              <el-table-column prop="HairRecharge" label="充值">
                <template slot-scope="scope">
                  {{scope.row.HairRecharge | NumFormat}}
                </template>
              </el-table-column>
              <el-table-column prop="HairProduct" label="产品">
                <template slot-scope="scope">
                  {{scope.row.HairProduct | NumFormat}}
                </template>
              </el-table-column>
              <el-table-column prop="HairProject" label="项目">
                <template slot-scope="scope">
                  {{scope.row.HairProject | NumFormat}}
                </template>
              </el-table-column>
              <el-table-column prop="HairTotal" label="美发现金业绩">
                <template slot-scope="scope">
                  {{scope.row.HairTotal | NumFormat}}
                </template>
              </el-table-column>
            </el-table-column>
            <el-table-column label="美容划卡">
              <el-table-column prop="BeautyTreatProduct" label="产品">
                <template slot-scope="scope">
                  {{scope.row.BeautyTreatProduct | NumFormat}}
                </template>
              </el-table-column>
              <el-table-column prop="BeautyTreatProject" label="项目">
                <template slot-scope="scope">
                  {{scope.row.BeautyTreatProject | NumFormat}}
                </template>
              </el-table-column>
            </el-table-column>
            <el-table-column prop="BeautyTreatTotal" label="美容划卡合计">
              <template slot-scope="scope">
                {{scope.row.BeautyTreatTotal | NumFormat}}
              </template>
            </el-table-column>
            <el-table-column label="美发划卡">
              <el-table-column prop="HairTreatProduct" label="产品">
                <template slot-scope="scope">
                  {{scope.row.HairTreatProduct | NumFormat}}
                </template>
              </el-table-column>
              <el-table-column prop="HairTreatProject" label="项目">
                <template slot-scope="scope">
                  {{scope.row.HairTreatProject | NumFormat}}
                </template>
              </el-table-column>
            </el-table-column>
            <el-table-column prop="HairTreatTotal" label="美发划卡合计">
              <template slot-scope="scope">
                {{scope.row.HairTreatTotal | NumFormat}}
              </template>
            </el-table-column>
            <el-table-column prop="BeautySavingCardRate" label="美容0.1">
              <template slot-scope="scope">
                {{scope.row.BeautySavingCardRate | NumFormat}}
              </template>
            </el-table-column>
            <el-table-column prop="HaidSavingCardRate" label="美发0.4">
              <template slot-scope="scope">
                {{scope.row.HaidSavingCardRate | NumFormat}}
              </template>
            </el-table-column>
            <el-table-column prop="BeautySavingCard" label="美容赠送金额">
              <template slot-scope="scope">
                {{scope.row.BeautySavingCard | NumFormat}}
              </template>
            </el-table-column>
            <el-table-column prop="HaidSavingCard" label="美发赠送金额">
              <template slot-scope="scope">
                {{scope.row.HaidSavingCard | NumFormat}}
              </template>
            </el-table-column>
            <el-table-column prop="BeautyTotalMoney" label="美容卡消耗金额合计">
              <template slot-scope="scope">
                {{scope.row.BeautyTotalMoney | NumFormat}}
              </template>
            </el-table-column>
            <el-table-column prop="HairTotalMoney" label="美发卡消耗金额合计">
              <template slot-scope="scope">
                {{scope.row.HairTotalMoney | NumFormat}}
              </template>
            </el-table-column>
          </el-table>
          <!-- 分页 -->
          <div class="pad_15 text_right">
            <el-pagination background v-if="ScheduleTwoPaginations.total > 0" @current-change="handleScheduleTwoPageChange" :current-page.sync="ScheduleTwoPaginations.page" :page-size="ScheduleTwoPaginations.page_size" :layout="ScheduleTwoPaginations.layout" :total="ScheduleTwoPaginations.total"></el-pagination>
          </div>
        </div>
      </el-tab-pane>
      <el-tab-pane label="营业收入表汇总" name="third">
        <div class="Summary">
          <!-- 搜索 -->
          <div class="nav_header" style="padding:15px 0 0 0">
            <el-form :inline="true" size="small" :model="SummarySearchFrom" @submit.native.prevent>
              <el-form-item label="门店">
                <el-select v-model="SummarySearchFrom.EntityID" clearable filterable placeholder="请选择门店" :default-first-option="true" @change="SummaryhandleSearch" @clear="SummaryhandleSearch">
                  <el-option v-for="item in storeEntityList" :key="item.ID" :label="item.EntityName" :value="item.ID"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="日期筛选">
                <el-date-picker v-model="SummarySearchFrom.QueryDate" unlink-panels type="daterange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" @change="SummaryhandleSearch" @clear="SummaryhandleSearch" value-format="yyyy-MM-dd">
                </el-date-picker>
              </el-form-item>
              <el-form-item>
                <el-button type="primary" v-prevent-click @click="SummaryhandleSearch">搜索</el-button>
              </el-form-item>
              <el-form-item>
                <el-button v-if="SummaryExport" type="primary" size="small" :loading="downloadLoading" @click="downloadSummary">导出</el-button>
              </el-form-item>
            </el-form>
          </div>
          <!-- 表格 -->
          <el-table :data="SummaryTableData" style="width: 100%" show-summary :summary-method="getincomeSumStatementForm" v-loading="entityIncomeStatementLoading">
            <el-table-column prop="EntityName" label="门店"></el-table-column>
            <el-table-column prop="BeautyTotal" label="美容现金业绩">
              <template slot-scope="scope">
                {{scope.row.BeautyTotal | NumFormat}}
              </template>
            </el-table-column>
            <el-table-column prop="BeautyTreatTotal" label="美容总划卡">
              <template slot-scope="scope">
                {{scope.row.BeautyTreatTotal | NumFormat}}
              </template>
            </el-table-column>
            <el-table-column prop="BeautyTotalMoney" label="美容总卡金">
              <template slot-scope="scope">
                {{scope.row.BeautyTotalMoney | NumFormat}}
              </template>
            </el-table-column>
            <el-table-column prop="HairTotal" label="美发现金业绩">
              <template slot-scope="scope">
                {{scope.row.HairTotal | NumFormat}}
              </template>
            </el-table-column>
            <el-table-column prop="HairTreatTotal" label="美发总划卡">
              <template slot-scope="scope">
                {{scope.row.HairTreatTotal | NumFormat}}
              </template>
            </el-table-column>
            <el-table-column prop="HairTotalMoney" label="美发总卡金">
              <template slot-scope="scope">
                {{scope.row.HairTotalMoney | NumFormat}}
              </template>
            </el-table-column>
            <el-table-column prop="TotalPerformance" label="业绩合计">
              <template slot-scope="scope">
                {{scope.row.TotalPerformance | NumFormat}}
              </template>
            </el-table-column>
            <el-table-column prop="ToShopRate" label="到店率"></el-table-column>
            <el-table-column prop="AgainBuyRate" label="复购率"></el-table-column>
            <el-table-column prop="ConversionRate" label="转化率"></el-table-column>
          </el-table>
          <!-- 分页 -->
          <div class="pad_15 text_right">
            <el-pagination background v-if="SummaryPaginations.total > 0" @current-change="handleSummaryPageChange" :current-page.sync="SummaryPaginations.page" :page-size="SummaryPaginations.page_size" :layout="SummaryPaginations.layout" :total="SummaryPaginations.total"></el-pagination>
          </div>
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>

import EntityAPI from "@/api/Report/Common/entity";
import API from '@/api/Report/YZH/Entity/businessIncome'


export default {
  name: 'ReportYZHEntityBusinessIncome',
  props: {},
  /**  引入的组件  */
  components: {

  },
  /**  Vue 实例的数据对象**/
  data() {
    return {
      loading:false,
      entityIncomeStatementLoading: false,
      activeName: 'first', //选项卡默认显示
      downloadLoading: false,
      ScheduleOneExport: true,//是否导出
      ScheduleTwoExport: true,
      SummaryExport: true,
      storeEntityList: [], //门店列表
      ScheduleOneTableData: [], //明细表1表格数据
      entityIncomeSumStatementForm: {}, //明细表1合计
      entityIncomeSumStatementFormTwo: {}, //明细表2合计
      ScheduleTwoTableData: [], //明细表2表格数据
      SummaryTableData: [], //汇总表表格数据
      incomeSumStatementForm: {}, //汇总表合计
      EntityID: [],
      ysOneSearchFrom: {
        EntityID: "",
        QueryDate: [this.$formatDate(new Date(),"YYYY-MM-DD"), this.$formatDate(new Date(),"YYYY-MM-DD")],
      },
      ScheduleOnePaginations: {
        page: 1, // 当前位于哪页
        total: 0, // 总数
        page_size: 0, // 1页显示多少条
        layout: "total, prev, pager, next,jumper", // 翻页属性
      },
      ysTwoSearchFrom: {
        QueryDate: [this.$formatDate(new Date(),"YYYY-MM-DD"), this.$formatDate(new Date(),"YYYY-MM-DD")],
      },
      ScheduleTwoPaginations: {
        page: 1, // 当前位于哪页
        total: 0, // 总数
        page_size: 0, // 1页显示多少条
        layout: "total, prev, pager, next,jumper", // 翻页属性
      },
      SummarySearchFrom: {
        EntityID: "",
        QueryDate: [this.$formatDate(new Date(),"YYYY-MM-DD"), this.$formatDate(new Date(),"YYYY-MM-DD")],
      },
      SummaryPaginations: {
        page: 1, // 当前位于哪页
        total: 0, // 总数
        page_size: 0, // 1页显示多少条
        layout: "total, prev, pager, next,jumper", // 翻页属性
      },
    }
  },
  /**计算属性  */
  computed: {
  },
  /**  方法集合  */
  methods: {
    /* 选项卡点击事件 */
    handleClick() { },
    /* 明细表1搜索 */
    ScheduleOnehandleSearch() {
      let that = this
      that.ScheduleOnePaginations.page = 1
      that.entityIncomeStatementOne()
    },
    /* 明细表1分页 */
    handleScheduleOnePageChange(page) {
      let that = this
      that.ScheduleOnePaginations.page = page
      that.entityIncomeStatementOne()
    },
    /* 明细表1导出 */
    downloadScheduleOne() {
      let that = this
      that.downloadLoading = true;
      const param = that.ysOneSearchFrom;
      if (param.QueryDate != null) {
        const params = {
          EntityID: param.EntityID,
          StartTime: param.QueryDate[0],
          EndTime: param.QueryDate[1],
        };
        API.dateDetailExcel(params)
          .then((res) => {
            this.$message.success({
              message: "正在导出",
              duration: "4000",
            });
            const link = document.createElement("a");
            let blob = new Blob([res], { type: "application/octet-stream" });
            link.style.display = "none";
            link.href = URL.createObjectURL(blob);
            link.download = "营业收入明细表.xlsx"; //下载的文件名
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
          })
          .finally(() => {
            that.downloadLoading = false;
          });
      }
    },
    /* 明细表2搜索 */
    ScheduleTwohandleSearch() {
      let that = this
      that.ScheduleTwoPaginations.page = 1
      that.entityIncomeStatementTwo()
    },
    /* 明细表2分页 */
    handleScheduleTwoPageChange(page) {
      let that = this
      that.ScheduleTwoPaginations.page = page
      that.entityIncomeStatementTwo()
    },
    /* 明细表2导出 */
    downloadScheduleTwo() {
      let that = this
      that.downloadLoading = true;
      const param = that.ysTwoSearchFrom;
      if (param.QueryDate != null) {
        const params = {
          EntityID: that.EntityID,
          StartTime: param.QueryDate[0],
          EndTime: param.QueryDate[1],
        };
        API.entityDetailExcel(params)
          .then((res) => {
            this.$message.success({
              message: "正在导出",
              duration: "4000",
            });
            const link = document.createElement("a");
            let blob = new Blob([res], { type: "application/octet-stream" });
            link.style.display = "none";
            link.href = URL.createObjectURL(blob);
            link.download = "营业收入表.xlsx"; //下载的文件名
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
          })
          .finally(() => {
            that.downloadLoading = false;
          });
      }
    },
    /* 汇总表搜索 */
    SummaryhandleSearch() {
      let that = this
      that.SummaryPaginations.page = 1
      that.entityIncomeStatement_list()
    },
    /* 汇总表分页 */
    handleSummaryPageChange(page) {
      let that = this
      that.SummaryPaginations.page = page
      that.entityIncomeStatement_list()
    },
    /* 汇总表导出 */
    downloadSummary() {
      let that = this
      that.downloadLoading = true;
      const param = that.SummarySearchFrom
      if (param.QueryDate != null) {
        const params = {
          Entity: param.EntityID,
          StartTime: param.QueryDate[0],
          EndTime: param.QueryDate[1],
        };
        API.entityDetail(params)
          .then((res) => {
            this.$message.success({
              message: "正在导出",
              duration: "4000",
            });
            const link = document.createElement("a");
            let blob = new Blob([res], { type: "application/octet-stream" });
            link.style.display = "none";
            link.href = URL.createObjectURL(blob);
            link.download = "营业收入汇总表.xlsx"; //下载的文件名
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
          })
          .finally(() => {
            that.downloadLoading = false;
          });
      }
    },
    // 明细表1数据请求
    entityIncomeStatementOne() {
      let that = this
      that.entityIncomeStatementLoading = true
      let params = {
        PageNum: that.ScheduleOnePaginations.page, //分页
        EntityID: that.ysOneSearchFrom.EntityID, //门店
       /*  StartTime: "2021-09-01", //开始时间
        EndTime: "2021-09-11",//结束时间 */
        StartTime: that.ysOneSearchFrom.QueryDate === null ? "" : that.ysOneSearchFrom.QueryDate[0],
        EndTime: that.ysOneSearchFrom.QueryDate === null ? "" : that.ysOneSearchFrom.QueryDate[1],
      }
      API.entityIncomeStatementOne(params).then((res) => {
        if (res.StateCode == 200) {
          that.ScheduleOneTableData = res.Data.detail.List
          that.ScheduleOnePaginations.total = res.Data.detail.Total
          that.ScheduleOnePaginations.page_size = res.Data.detail.PageSize
          that.entityIncomeSumStatementForm = res.Data.entityIncomeSumStatementForm
        }
      }).finally(function () {
        that.entityIncomeStatementLoading = false;
      });
    },
    /* 明细表2数据请求 */
    entityIncomeStatementTwo() {
      let that = this
      that.entityIncomeStatementLoading = true
      let params = {
        PageNum: that.ScheduleTwoPaginations.page, //分页
        EntityID: that.EntityID, //门店
        /* StartTime: "2021-09-01", //开始时间
        EndTime: "2021-09-11",//结束时间 */
        StartTime: that.ysTwoSearchFrom.QueryDate === null ? "" : that.ysTwoSearchFrom.QueryDate[0],
        EndTime: that.ysTwoSearchFrom.QueryDate === null ? "" : that.ysTwoSearchFrom.QueryDate[1],
      }
      API.entityIncomeStatementTwo(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.ScheduleTwoTableData = res.Data.detail.List
            that.ScheduleTwoPaginations.total = res.Data.detail.Total
            that.ScheduleTwoPaginations.page_size = res.Data.detail.PageSize
            that.entityIncomeSumStatementFormTwo = res.Data.entityIncomeSumStatementForm
          }
        }).finally(function () {
          that.entityIncomeStatementLoading = false;
        });
    },
    /* 汇总表数据请求 */
    entityIncomeStatement_list() {
      let that = this
      that.entityIncomeStatementLoading = true
      let params = {
        PageNum: that.SummaryPaginations.page, //分页
        EntityID: that.SummarySearchFrom.EntityID, //门店
        /* StartTime: "2021-09-01", //开始时间
        EndTime: "2021-09-11",//结束时间 */
        StartTime: that.SummarySearchFrom.QueryDate === null ? "" : that.SummarySearchFrom.QueryDate[0],
        EndTime: that.SummarySearchFrom.QueryDate === null ? "" : that.SummarySearchFrom.QueryDate[1],
      }
      API.entityIncomeStatement_list(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.SummaryTableData = res.Data.detail.List
            that.SummaryPaginations.total = res.Data.detail.Total
            that.SummaryPaginations.page_size = res.Data.detail.PageSize
            that.incomeSumStatementForm = res.Data.incomeSumStatementForm
          }
        }).finally(function () {
          that.entityIncomeStatementLoading = false;
        });
    },
    //获得当前用户下的权限门店
    getstoreEntityList() {
      var that = this;
      that.loading = true;
      EntityAPI.getStoreEntityList()
        .then((res) => {
          if (res.StateCode == 200) {
            that.storeEntityList = res.Data;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },
    /* 明细表1合计 */
    getentityIncomeSumStatementForm(param) {
      const { columns } = param;
      const sums = [];
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = <span class="font_weight_600">合计</span>;
          return;
        }
        var filter_NumFormat = this.$options.filters["NumFormat"];
        switch (column.property) {
          case "BeautyRecharge":
            sums[index] = (
              <span class="font_weight_600">
                {filter_NumFormat(
                  this.entityIncomeSumStatementForm?this.entityIncomeSumStatementForm.BeautyRecharge:""
                )}
              </span>
            );
            break;
          case "BeautyProduct":
            sums[index] = (
              <span class="font_weight_600">
                {filter_NumFormat(
                  this.entityIncomeSumStatementForm?this.entityIncomeSumStatementForm.BeautyProduct:""
                )}
              </span>
            );
            break;
          case "BeautyProject":
            sums[index] = (
              <span class="font_weight_600">
                {filter_NumFormat(
                  this.entityIncomeSumStatementForm?this.entityIncomeSumStatementForm.BeautyProject:""
                )}
              </span>
            );
            break;
          case "BeautyTotal":
            sums[index] = (
              <span class="font_weight_600">
                {filter_NumFormat(
                  this.entityIncomeSumStatementForm?this.entityIncomeSumStatementForm.BeautyTotal:""
                )}
              </span>
            );
            break;
          case "HairRecharge":
            sums[index] = (
              <span class="font_weight_600">
                {filter_NumFormat(
                  this.entityIncomeSumStatementForm?this.entityIncomeSumStatementForm.HairRecharge:""
                )}
              </span>
            );
            break;
          case "HairProduct":
            sums[index] = (
              <span class="font_weight_600">
                {filter_NumFormat(
                  this.entityIncomeSumStatementForm?this.entityIncomeSumStatementForm.HairProduct:""
                )}
              </span>
            );
            break;
          case "HairProject":
            sums[index] = (
              <span class="font_weight_600">
                {filter_NumFormat(
                  this.entityIncomeSumStatementForm?this.entityIncomeSumStatementForm.HairProject:""
                )}
              </span>
            );
            break;
          case "HairTotal":
            sums[index] = (
              <span class="font_weight_600">
                {filter_NumFormat(
                  this.entityIncomeSumStatementForm?this.entityIncomeSumStatementForm.HairTotal:""
                )}
              </span>
            );
            break;
          case "BeautyTreatProduct":
            sums[index] = (
              <span class="font_weight_600">
                {filter_NumFormat(
                  this.entityIncomeSumStatementForm?this.entityIncomeSumStatementForm.BeautyTreatProduct:""
                )}
              </span>
            );
            break;
          case "BeautyTreatProject":
            sums[index] = (
              <span class="font_weight_600">
                {filter_NumFormat(
                  this.entityIncomeSumStatementForm?this.entityIncomeSumStatementForm.BeautyTreatProject:""
                )}
              </span>
            );
            break;
          case "BeautyTreatTotal":
            sums[index] = (
              <span class="font_weight_600">
                {filter_NumFormat(
                  this.entityIncomeSumStatementForm?this.entityIncomeSumStatementForm.BeautyTreatTotal:""
                )}
              </span>
            );
            break;
          case "HairTreatProduct":
            sums[index] = (
              <span class="font_weight_600">
                {filter_NumFormat(
                  this.entityIncomeSumStatementForm?this.entityIncomeSumStatementForm.HairTreatProduct:""
                )}
              </span>
            );
            break;
          case "HairTreatProject":
            sums[index] = (
              <span class="font_weight_600">
                {filter_NumFormat(
                  this.entityIncomeSumStatementForm?this.entityIncomeSumStatementForm.HairTreatProject:""
                )}
              </span>
            );
            break;
          case "HairTreatTotal":
            sums[index] = (
              <span class="font_weight_600">
                {filter_NumFormat(
                  this.entityIncomeSumStatementForm?this.entityIncomeSumStatementForm.HairTreatTotal:""
                )}
              </span>
            );
            break;
          case "BeautySavingCardRate":
            sums[index] = (
              <span class="font_weight_600">
                {filter_NumFormat(
                  this.entityIncomeSumStatementForm?this.entityIncomeSumStatementForm.BeautySavingCardRate:""
                )}
              </span>
            );
            break;
          case "HaidSavingCardRate":
            sums[index] = (
              <span class="font_weight_600">
                {filter_NumFormat(
                  this.entityIncomeSumStatementForm?this.entityIncomeSumStatementForm.HaidSavingCardRate:""
                )}
              </span>
            );
            break;
          case "BeautySavingCard":
            sums[index] = (
              <span class="font_weight_600">
                {filter_NumFormat(
                  this.entityIncomeSumStatementForm?this.entityIncomeSumStatementForm.BeautySavingCard:""
                )}
              </span>
            );
            break;
          case "HaidSavingCard":
            sums[index] = (
              <span class="font_weight_600">
                {filter_NumFormat(
                  this.entityIncomeSumStatementForm?this.entityIncomeSumStatementForm.HaidSavingCard:""
                )}
              </span>
            );
            break;
          case "BeautyTotalMoney":
            sums[index] = (
              <span class="font_weight_600">
                {filter_NumFormat(
                  this.entityIncomeSumStatementForm?this.entityIncomeSumStatementForm.BeautyTotalMoney:""
                )}
              </span>
            );
            break;
          case "HairTotalMoney":
            sums[index] = (
              <span class="font_weight_600">
                {filter_NumFormat(
                  this.entityIncomeSumStatementForm?this.entityIncomeSumStatementForm.HairTotalMoney:""
                )}
              </span>
            );
            break;
        }
      });
      return sums;
    },
    /* 明细表2合计 */
    getentityIncomeSumStatementFormTwo(param) {
      const { columns } = param;
      const sums = [];
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = <span class="font_weight_600">合计</span>;
          return;
        }
        var filter_NumFormat = this.$options.filters["NumFormat"];
        switch (column.property) {
            case "BeautyRecharge":
            sums[index] = (
              <span class="font_weight_600">
                {filter_NumFormat(
                  this.entityIncomeSumStatementFormTwo?this.entityIncomeSumStatementFormTwo.BeautyRecharge:""
                )}
              </span>
            );
            break;
            case "BeautyProduct":
            sums[index] = (
              <span class="font_weight_600">
                {filter_NumFormat(
                  this.entityIncomeSumStatementFormTwo?this.entityIncomeSumStatementFormTwo.BeautyProduct:""
                )}
              </span>
            );
            break;
            case "BeautyProject":
            sums[index] = (
              <span class="font_weight_600">
                {filter_NumFormat(
                  this.entityIncomeSumStatementFormTwo?this.entityIncomeSumStatementFormTwo.BeautyProject:""
                )}
              </span>
            );
            break;
            case "BeautyTotal":
            sums[index] = (
              <span class="font_weight_600">
                {filter_NumFormat(
                  this.entityIncomeSumStatementFormTwo?this.entityIncomeSumStatementFormTwo.BeautyTotal:""
                )}
              </span>
            );
            break;
            case "HairRecharge":
            sums[index] = (
              <span class="font_weight_600">
                {filter_NumFormat(
                  this.entityIncomeSumStatementFormTwo?this.entityIncomeSumStatementFormTwo.HairRecharge:""
                )}
              </span>
            );
            break;
            case "HairProduct":
            sums[index] = (
              <span class="font_weight_600">
                {filter_NumFormat(
                  this.entityIncomeSumStatementFormTwo?this.entityIncomeSumStatementFormTwo.HairProduct:""
                )}
              </span>
            );
            break;
            case "HairProject":
            sums[index] = (
              <span class="font_weight_600">
                {filter_NumFormat(
                  this.entityIncomeSumStatementFormTwo?this.entityIncomeSumStatementFormTwo.HairProject:""
                )}
              </span>
            );
            break;
            case "HairTotal":
            sums[index] = (
              <span class="font_weight_600">
                {filter_NumFormat(
                  this.entityIncomeSumStatementFormTwo?this.entityIncomeSumStatementFormTwo.HairTotal:""
                )}
              </span>
            );
            break;
            case "BeautyTreatProduct":
            sums[index] = (
              <span class="font_weight_600">
                {filter_NumFormat(
                  this.entityIncomeSumStatementFormTwo?this.entityIncomeSumStatementFormTwo.BeautyTreatProduct:""
                )}
              </span>
            );
            break;
            case "BeautyTreatProject":
            sums[index] = (
              <span class="font_weight_600">
                {filter_NumFormat(
                  this.entityIncomeSumStatementFormTwo?this.entityIncomeSumStatementFormTwo.BeautyTreatProject:""
                )}
              </span>
            );
            break;

            case "BeautyTreatTotal":
            sums[index] = (
              <span class="font_weight_600">
                {filter_NumFormat(
                  this.entityIncomeSumStatementFormTwo?this.entityIncomeSumStatementFormTwo.BeautyTreatTotal:""
                )}
              </span>
            );
            break;
            case "HairTreatProduct":
            sums[index] = (
              <span class="font_weight_600">
                {filter_NumFormat(
                  this.entityIncomeSumStatementFormTwo?this.entityIncomeSumStatementFormTwo.HairTreatProduct:""
                )}
              </span>
            );
            break;
            case "HairTreatProject":
            sums[index] = (
              <span class="font_weight_600">
                {filter_NumFormat(
                  this.entityIncomeSumStatementFormTwo?this.entityIncomeSumStatementFormTwo.HairTreatProject:""
                )}
              </span>
            );
            break;
            case "HairTreatTotal":
            sums[index] = (
              <span class="font_weight_600">
                {filter_NumFormat(
                  this.entityIncomeSumStatementFormTwo?this.entityIncomeSumStatementFormTwo.HairTreatTotal:""
                )}
              </span>
            );
            break;
            case "BeautySavingCardRate":
            sums[index] = (
              <span class="font_weight_600">
                {filter_NumFormat(
                  this.entityIncomeSumStatementFormTwo?this.entityIncomeSumStatementFormTwo.BeautySavingCardRate:""
                )}
              </span>
            );
            break;
            case "HaidSavingCardRate":
            sums[index] = (
              <span class="font_weight_600">
                {filter_NumFormat(
                  this.entityIncomeSumStatementFormTwo?this.entityIncomeSumStatementFormTwo.HaidSavingCardRate:""
                )}
              </span>
            );
            break;
            case "BeautySavingCard":
            sums[index] = (
              <span class="font_weight_600">
                {filter_NumFormat(
                  this.entityIncomeSumStatementFormTwo?this.entityIncomeSumStatementFormTwo.BeautySavingCard:""
                )}
              </span>
            );
            break;
            case "HaidSavingCard":
            sums[index] = (
              <span class="font_weight_600">
                {filter_NumFormat(
                  this.entityIncomeSumStatementFormTwo?this.entityIncomeSumStatementFormTwo.HaidSavingCard:""
                )}
              </span>
            );
            break;
            case "BeautyTotalMoney":
            sums[index] = (
              <span class="font_weight_600">
                {filter_NumFormat(
                  this.entityIncomeSumStatementFormTwo?this.entityIncomeSumStatementFormTwo.BeautyTotalMoney:""
                )}
              </span>
            );
            break;
            case "HairTotalMoney":
            sums[index] = (
              <span class="font_weight_600">
                {filter_NumFormat(
                  this.entityIncomeSumStatementFormTwo?this.entityIncomeSumStatementFormTwo.HairTotalMoney:""
                )}
              </span>
            );
            break;
        }

      });
      return sums
    },
    /* 汇总表合计 */
    getincomeSumStatementForm(param) {
      const { columns } = param;
      const sums = [];
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = <span class="font_weight_600">合计</span>;
          return;
        }
        var filter_NumFormat = this.$options.filters["NumFormat"];
        switch (column.property) {
          case "BeautyTotal":
            sums[index] = (
              <span class="font_weight_600">
                {filter_NumFormat(
                  this.incomeSumStatementForm?this.incomeSumStatementForm.BeautyTotal:""
                )}
              </span>
            );
            break;
          case "BeautyTreatTotal":
            sums[index] = (
              <span class="font_weight_600">
                {filter_NumFormat(
                  this.incomeSumStatementForm?this.incomeSumStatementForm.BeautyTreatTotal:""
                )}
              </span>
            );
            break;
          case "BeautyTotalMoney":
            sums[index] = (
              <span class="font_weight_600">
                {filter_NumFormat(
                  this.incomeSumStatementForm?this.incomeSumStatementForm.BeautyTotalMoney:""
                )}
              </span>
            );
            break;
          case "HairTotal":
            sums[index] = (
              <span class="font_weight_600">
                {filter_NumFormat(
                  this.incomeSumStatementForm?this.incomeSumStatementForm.HairTotal:""
                )}
              </span>
            );
            break;
          case "HairTreatTotal":
            sums[index] = (
              <span class="font_weight_600">
                {filter_NumFormat(
                  this.incomeSumStatementForm?this.incomeSumStatementForm.HairTreatTotal:""
                )}
              </span>
            );
            break;
          case "HairTotalMoney":
            sums[index] = (
              <span class="font_weight_600">
                {filter_NumFormat(
                  this.incomeSumStatementForm?this.incomeSumStatementForm.HairTotalMoney:""
                )}
              </span>
            );
            break;
          case "TotalPerformance":
            sums[index] = (
              <span class="font_weight_600">
                {filter_NumFormat(
                  this.incomeSumStatementForm?this.incomeSumStatementForm.TotalPerformance:""
                )}
              </span>
            );
            break;
        }
      });
      return sums
    }
  },
  /** 监听数据变化   */
  watch: {},
  /** 创建实例之前执行函数   */
  beforeCreate() { },
  /**  实例创建完成之后  */
  created() { },
  /**  在挂载开始之前被调用  */
  beforeMount() { },
  /**  实例被挂载后调用  */
  mounted() {
    let that = this
    that.getstoreEntityList()
    that.entityIncomeStatementOne()
    that.entityIncomeStatementTwo()
    that.entityIncomeStatement_list()
  },
  /**  数据更新 之前 调用  */
  beforeUpdate() { },
  /** 数据更新 完成 调用   */
  updated() { },
  /**  实例销毁之前调用  */
  beforeDestroy() { },
  /**  实例销毁后调用  */
  destroyed() { },
}
</script>

<style lang="scss">
.businessIncome {
}
</style>
