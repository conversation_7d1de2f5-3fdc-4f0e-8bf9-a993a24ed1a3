<template>
  <div class="Seckill content_body" :loading="loading">
    <!-- 搜索框 -->
    <div class="nav_header">
      <el-row>
        <el-col :span="20">
          <el-form :inline="true" size="small" class="demo-form-inline" @keyup.enter.native="handleSearch">
            <el-form-item label="活动名称">
              <el-input v-model="searchName" @clear="handleSearch" clearable placeholder="输入线上名称搜索"></el-input>
            </el-form-item>
            <el-form-item label="活动状态">
              <el-select @change="handleSearch" v-model="searchStatus" placeholder="请选择" clearable>
                <el-option label="未开始" value="10"></el-option>
                <el-option label="进行中" value="20"></el-option>
                <el-option label="已结束" value="30"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleSearch">搜索</el-button>
            </el-form-item>
          </el-form>
        </el-col>
        <el-col :span="4" class="text_right">
          <el-button type="primary" size="small" @click="addHandleSeckillClick">新增</el-button>
        </el-col>
      </el-row>
    </div>
    <!-- 表格 -->
    <div>
      <el-table size="small" ref="multipleTable" :data="tableData">
        <el-table-column prop="Name" label="活动名称"></el-table-column>
        <el-table-column prop="BeginDateTime" label="开始时间">
          <template slot-scope="scope">
            {{ scope.row.BeginDateTime | dateFormat("YYYY-MM-DD HH:mm") }}
          </template>
        </el-table-column>
        <el-table-column prop="EndDateTime" label="结束时间">
          <template slot-scope="scope">
            {{ scope.row.EndDateTime | dateFormat("YYYY-MM-DD HH:mm") }}
          </template>
        </el-table-column>
        <el-table-column prop="Status" label="活动状态" :formatter="sckillStatueFormatter"></el-table-column>
        <el-table-column label="操作" width="80px">
          <template slot-scope="scope">
            <el-button :disabled="scope.row.Status == 30" type="primary" size="small" @click="handleEditSeckill(scope.row)" v-prevent-click>编辑</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <!-- 分页 -->
    <div class="pad_10 dis_flex flex_x_end" v-if="paginations.total > 0">
      <el-pagination
        background
        :current-page.sync="paginations.page"
        :layout="paginations.layout"
        :total="paginations.total"
        @current-change="currentChange"
      ></el-pagination>
    </div>

    <!-- 新增秒杀活动弹出层 -->
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="1100px" custom-class="addSeckillClass">
      <el-row :gutter="20">
        <el-col :span="7">
          <el-carousel :interval="4000" height="525px" indicator-position="outside" style="margin-top: 14px">
            <el-carousel-item v-for="item in ImageName" :key="item">
              <el-image :src="getImgUrl(item)"></el-image>
            </el-carousel-item>
          </el-carousel>
        </el-col>

        <el-col :span="17">
          <el-form :model="addRuleForm" :rules="rules" ref="addRuleForm" label-width="100px" size="small">
            <el-tabs v-model="activeName">
              <el-tab-pane label="基础信息" name="0">
                <el-scrollbar class="add_el_scrollbar_height">
                  <el-form-item label="活动名称" prop="Name">
                    <el-input v-model="addRuleForm.Name" size="small" placeholder="作为标题在页面上展示，长度2到20个字"></el-input>
                  </el-form-item>

                  <el-form-item prop="BeginDateTime" format="yyyy-MM-dd HH:mm" label="活动时间">
                    <el-col :span="8" style="padding-left: 0px">
                      <el-form-item prop="BeginDateTime" style="margin-bottom: 0">
                        <el-date-picker
                          :disabled="addRuleForm.Status != 10 && !isAdd"
                          size="small"
                          type="datetime"
                          placeholder="开始时间"
                          :picker-options="BeginDateTimeDisabled"
                          v-model="addRuleForm.BeginDateTime"
                          @change="beginDataChange"
                          default-time="08:00:00"
                        ></el-date-picker>
                      </el-form-item>
                    </el-col>
                    <el-col :span="1">
                      <span class="color_666"> - </span>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item prop="EndDateTime" style="margin-bottom: 0">
                        <el-date-picker
                          size="small"
                          format="yyyy-MM-dd HH:mm"
                          type="datetime"
                          placeholder="结束时间"
                          :picker-options="EndDateTimeDisabled"
                          v-model="addRuleForm.EndDateTime"
                          @change="endDataChange"
                          default-time="17:00:00"
                        ></el-date-picker>
                      </el-form-item>
                    </el-col>
                  </el-form-item>

                  <el-form-item label="活动限购" prop="Quota">
                    <el-col :span="7">
                      <el-radio-group v-model="addRuleForm.isQuota">
                        <el-radio :label="false">不限</el-radio>
                        <el-radio :label="true">限购</el-radio>
                      </el-radio-group>
                    </el-col>
                    <el-col :span="5" v-if="addRuleForm.isQuota">
                      <el-input size="small" min="1" placeholder="请输入" v-model="addRuleForm.Quota"></el-input>
                    </el-col>
                  </el-form-item>

                  <el-form-item label="活动商品" prop="GoodsID">
                    <el-dropdown v-if="addRuleForm.GoodsList.length == 0">
                      <span class="el-dropdown-link"> 选择商品<i class="el-icon-arrow-down el-icon--right"></i> </span>
                      <el-dropdown-menu slot="dropdown">
                        <el-dropdown-item @click.native="selectGoodsClick('20')">项目</el-dropdown-item>
                        <el-dropdown-item @click.native="selectGoodsClick('10')">产品</el-dropdown-item>
                        <el-dropdown-item @click.native="selectGoodsClick('30')">通用次卡</el-dropdown-item>
                        <el-dropdown-item @click.native="selectGoodsClick('40')">时效卡</el-dropdown-item>
                      </el-dropdown-menu>
                    </el-dropdown>

                    <el-table
                      v-if="addRuleForm.GoodsList.length > 0"
                      size="small"
                      :data="addRuleForm.GoodsList"
                      tooltip-effect="light"
                      class="customTableClass"
                    >
                      <el-table-column prop="Name" label="商品名称"></el-table-column>
                      <el-table-column prop="Price" label="商品价格">
                        <template slot-scope="scope">
                          {{ scope.row.Price | toFixed | NumFormat }}
                        </template>
                      </el-table-column>
                      <el-table-column prop="ActivityStock">
                        <div slot="header">
                          <span>活动库存</span>
                          <el-popover content="客户支付成功即扣减购买所需库存，建议库存数量设置多一些" placement="top-start" width="300" trigger="hover">
                            <i slot="reference" class="el-icon-info" style="color: #dcdfe6"></i>
                          </el-popover>
                        </div>
                        <template slot-scope="scope">
                          <el-form-item :prop="'GoodsList.' + scope.$index + '.ActivityStock'" :rules="rules.ActivityStock">
                            <el-input
                              v-model="addRuleForm.GoodsList[scope.$index].ActivityStock"
                              size="small"
                              type="number"
                              v-input-fixed="0"
                              style="width: 70%"
                            ></el-input>
                          </el-form-item>
                        </template>
                      </el-table-column>
                      <el-table-column prop="SeckillPrice" label="活动价格【元】">
                        <template slot-scope="scope">
                          <el-form-item :prop="'GoodsList.' + scope.$index + '.SeckillPrice'" :rules="rules.SeckillPrice">
                            <el-input
                              :disabled="addRuleForm.Status != 10 && !isAdd"
                              v-model="addRuleForm.GoodsList[scope.$index].SeckillPrice"
                              size="small"
                              type="number"
                              v-input-fixed
                              min="0"
                              style="width: 70%"
                            >
                              <!-- <template slot="append">元</template> -->
                            </el-input>
                          </el-form-item>
                        </template>
                      </el-table-column>

                      <el-table-column label="操作" width="80px">
                        <template slot-scope="scope">
                          <el-button type="primary" size="small" @click="handleRemoveSeckillClick(scope.row)" v-prevent-click>删 除</el-button>
                        </template>
                      </el-table-column>
                    </el-table>
                  </el-form-item>

                  <el-form-item label="订单取消" prop="PayTimeOutMinuter" class="customPayTime">
                    买家
                    <el-input
                      type="number"
                      v-input-fixed="0"
                      size="small"
                      min="1"
                      placeholder="请输入"
                      style="width: 100px"
                      v-model="addRuleForm.PayTimeOutMinuter"
                    ></el-input>
                    分钟 未支付订单，订单取消
                  </el-form-item>
                </el-scrollbar>
              </el-tab-pane>

              <el-tab-pane label="活动门店" name="1">
                <div class="message el-message--info marbm_10">
                  <i class="el-message__icon el-icon-info"></i>
                  <p class="el-message__content">适用于同级所有节点，则只需勾选父节点。比如：适用于所有节点，只需勾选“顶级/第一个”节点。</p>
                </div>

                <el-scrollbar class="add_el_scrollbar_height">
                  <el-tree
                    ref="grouponEntity"
                    :expand-on-click-node="false"
                    :check-on-click-node="true"
                    :check-strictly="true"
                    :data="scopeData"
                    show-checkbox
                    node-key="ID"
                    :default-checked-keys="defaultCheckedKeys"
                    :default-expanded-keys="defaultExpandedKeys"
                    :props="defaultProps"
                  ></el-tree>
                </el-scrollbar>
              </el-tab-pane>
            </el-tabs>
          </el-form>
        </el-col>
      </el-row>
      <span slot="footer" class="dialog-footer">
        <el-button size="small" @click="dialogVisible = false" v-prevent-click>取 消</el-button>
        <el-button size="small" type="primary" @click="saveSeckillInfoClick" v-prevent-click>保 存</el-button>
      </span>
    </el-dialog>

    <!-- 新增选择指定商品弹出层 -->
    <el-dialog title="选择秒杀商品" :visible.sync="selShopDialogVisible" width="900">
      <el-row>
        <el-col :span="22">
          <el-form
            :inline="true"
            size="small"
            label-width="auto"
            class="demo-form-inline"
            @keyup.enter.native="selectGoodsSearchHandleClick"
            @submit.native.prevent
          >
            <el-form-item label="商品名称">
              <el-input v-model="goodsSearchName" @clear="selectGoodsSearchHandleClick" clearable placeholder="输入商品线上名称搜索"></el-input>
            </el-form-item>
          </el-form>
        </el-col>
        <el-col :span="2">
          <el-button size="small" type="primary" @click="selectGoodsSearchHandleClick">搜索</el-button>
        </el-col>
      </el-row>
      <el-scrollbar class="el_scrollbar_height goods">
        <el-table size="small" ref="multipleTable" :data="goodsList" tooltip-effect="light" height="60vh">
          <el-table-column prop="Name" label="名称"></el-table-column>
          <el-table-column prop="Price" label="售价（元）">
            <template slot-scope="scope">
              {{ scope.row.Price | toFixed | NumFormat }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="80px">
            <template slot-scope="scope">
              <el-button type="primary" size="small" @click="selectGoodsClickItem(scope.row)" v-prevent-click>选择</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-scrollbar>
      <div class="pad_10 dis_flex flex_x_end" v-if="goodspaginations.total > 0">
        <el-pagination
          background
          :current-page.sync="goodspaginations.page"
          :layout="goodspaginations.layout"
          :total="goodspaginations.total"
          @current-change="goodscurrentChange"
        ></el-pagination>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import API from "@/api/Marketing/MarketingPlay/Seckill";

export default {
  name: "Seckill",
  /**  引入的组件  */
  components: {},
  /**  Vue 实例的数据对象**/
  data() {
    return {
      loading: false,
      dialogVisible: false,
      selShopDialogVisible: false,
      isAdd: false,
      dialogTitle: "",
      searchName: "",
      searchStatus: "",
      activeName: "0",
      tableData: [], //表格数据
      paginations: {
        total: "",
        layout: " prev, pager, next, jumper, ->, total, slot",
        page: 1,
      }, //秒杀分页
      ImageName: ["seckill_1", "seckill_2", "seckill_3"],
      addRuleForm: {
        Name: "", //活动名称模糊搜索
        BeginDateTime: "", //开始时间
        EndDateTime: "", //结束时间
        Quota: 1, //活动限购（0表示不限）
        isQuota: false,
        GoodsType: "", //商品类型（10：产品、20：项目、30：通用次卡、40：时效卡）
        SeckillPrice: "", //秒杀价
        GoodsID: "", //商品ID
        ActivityStock: "", //活动库存
        PayTimeOutMinuter: "", //支付超时
        SeckillEntity: [], //适应门店
        GoodsList: [],
      },
      BeginDateTimeDisabled: {},
      EndDateTimeDisabled: {},
      rules: {
        Name: [
          {
            required: true,
            message: "请输入活动名称",
            trigger: ["change", "blur"],
          },
        ],
        BeginDateTime: [
          {
            required: true,
            message: "请选择活动开始时间",
            trigger: ["change", "blur"],
          },
        ], //活动时间

        EndDateTime: [
          {
            required: true,
            message: "请选择活动结束时间",
            trigger: ["change", "blur"],
          },
        ], //活动时间
        Quota: [
          {
            required: true,
            message: "请选择活动限制",
            trigger: ["change", "blur"],
          },
        ], //活动限购
        GoodsID: [
          {
            required: true,
            message: "请选择活动商品",
            trigger: ["change", "blur"],
          },
        ], //商品ID
        ActivityStock: [
          {
            required: true,
            message: "请输入活动库存",
            trigger: ["change", "blur"],
          },
        ], //活动库存

        SeckillPrice: [
          {
            required: true,
            message: "请输入活动库存",
            trigger: ["change", "blur"],
          },
        ], //活动价格
      },
      scopeData: [], // 销售范围数据
      defaultCheckedKeys: [],
      defaultExpandedKeys: [1],
      defaultProps: {
        children: "Child",
        label: "EntityName",
      }, // 销售范围选择配置项
      entityList: [],
      goodsType: "",
      goodsSearchName: "",
      goodsList: [],
      goodspaginations: {
        page: 1, // 当前位于哪页
        total: 0, // 总数
        page_size: 10, // 1页显示多少条
        layout: "total, prev, pager, next,jumper", // 翻页属性
      },
    };
  },
  /**计算属性  */
  computed: {},
  /**  方法集合  */
  methods: {
    /**  搜索  */
    handleSearch() {
      let that = this;
      that.paginations.page = 1;
      that.seckill_all();
    },
    /** 修改分页   */
    currentChange(page) {
      let that = this;
      that.paginations.page = page;
      that.seckill_all();
    },
    /**  状态格式化  */
    sckillStatueFormatter(row) {
      switch (row.Status) {
        case "10":
          return "未开始";
        case "20":
          return "进行中";
        case "30":
          return "已结束";
        default:
          return "";
      }
    },
    /**  编辑秒杀  */
    handleEditSeckill(row) {
      let that = this;
      that.seckill_info(row.ID);
    },
    /**  新增秒杀  */
    addHandleSeckillClick() {
      let that = this;
      that.dialogTitle = "新增秒杀活动";
      that.addRuleForm = {
        Name: "", //活动名称模糊搜索
        BeginDateTime: "", //开始时间
        EndDateTime: "", //结束时间
        Quota: 1, //活动限购（0表示不限）
        isQuota: false,
        GoodsType: "", //商品类型（10：产品、20：项目、30：通用次卡、40：时效卡）
        SeckillPrice: "", //秒杀价
        GoodsID: "", //商品ID
        ActivityStock: "", //活动库存
        PayTimeOutMinuter: "", //支付超时
        SeckillEntity: [], //适应门店
        GoodsList: [],
      };
      that.activeName = "0";
      that.scopeData = [];
      that.defaultCheckedKeys = [];
      that.defaultExpandedKeys = [1];
      Object.assign(that.scopeData, that.entityList);

      that.isAdd = true;
      that.dialogVisible = true;
    },

    /** 起始时间改变的时候，将此之前的时间禁用  **/

    beginDataChange(times) {
      if (times) {
        this.EndDateTimeDisabled = {
          disabledDate(time) {
            let endTime = time.setHours(17);
            return endTime < times.getTime();
          },
        };
      }
    },
    /**  终止时间改变的时候，将此之后的时间禁用 **/
    endDataChange(times) {
      if (times) {
        this.BeginDateTimeDisabled = {
          disabledDate(time) {
            let beginTime = time.setHours(8);
            return beginTime > times.getTime();
          },
        };
      }
    },

    /**  选择商品弹窗  */
    selectGoodsClick(type) {
      let that = this;
      that.goodsList = [];
      that.goodspaginations.page = 1;
      that.goodsType = type;
      switch (type) {
        case "20":
          that.getProjectList();
          break;

        case "10":
          that.getProductList();
          break;

        case "30":
          that.getGeneralCardList();
          break;

        case "40":
          that.getTimeCardList();
          break;
      }
    },
    /**  搜索商品  */
    selectGoodsSearchHandleClick() {
      let that = this;
      that.selectGoodsClick(that.goodsType);
    },
    /**  商品修改分页  */
    goodscurrentChange(page) {
      let that = this;
      that.goodspaginations.page = page;
      switch (that.goodsType) {
        case "20":
          that.getProjectList();
          break;

        case "10":
          that.getProductList();
          break;

        case "30":
          that.getGeneralCardList();
          break;

        case "40":
          that.getTimeCardList();
          break;
      }
    },

    /**  选择商品  */
    selectGoodsClickItem(row) {
      let that = this;
      that.addRuleForm.GoodsList.push({
        Name: row.Name,
        Price: row.Price,
        ActivityStock: "",
        SeckillPrice: "",
        ID: row.ID,
      });
      that.addRuleForm.GoodsType = that.goodsType;
      that.addRuleForm.GoodsID = row.ID;
      that.selShopDialogVisible = false;
    },
    /**  删除 选择的商品  */
    handleRemoveSeckillClick(row) {
      let that = this;
      that.addRuleForm.GoodsList = Array.from(that.addRuleForm.GoodsList).filter((val) => val.ID != row.ID);
      that.addRuleForm;
    },

    /**  保存新建秒杀  */
    saveSeckillInfoClick() {
      let that = this;
      that.$refs.addRuleForm.validate((valid, errorMsg) => {
        if (valid) {
          if (that.isAdd) {
            that.seckill_create();
          } else {
            that.seckill_update();
          }
        } else {
          let errorItem = Object.keys(errorMsg);
          let msg = errorMsg[errorItem[0]][0].message;
          that.$message.error(msg ? msg : "请补全信息");
        }
      });
    },

    /**  *************************  */
    getImgUrl(img) {
      return require("@/assets/img/store/" + img + ".png");
    },
    /**  秒杀列表  */
    async seckill_all() {
      let that = this;
      let params = {
        PageNum: that.paginations.page, //分页
        Name: that.searchName, //活动名称模糊搜索
        Status: that.searchStatus, //10 未开始  20 进行中 30  已结束
      };
      let res = await API.seckill_all(params);
      if (res.StateCode == 200) {
        that.tableData = res.List;
        that.paginations.total = res.Total;
      } else {
        that.$message.error(res.Message);
      }
    },

    /**  新增秒杀  */
    async seckill_create() {
      let that = this;
      let params = Object.assign({}, that.addRuleForm);

      params.Quota = params.isQuota ? params.Quota : 0;
      delete params.isQuota;
      params.ActivityStock = params.GoodsList && params.GoodsList.length > 0 ? params.GoodsList[0].ActivityStock : "0";
      params.SeckillPrice = params.GoodsList && params.GoodsList.length > 0 ? params.GoodsList[0].SeckillPrice : "0";
      delete params.GoodsList;
      params.SeckillEntity = that.$refs.grouponEntity.getCheckedKeys();
      let res = await API.seckill_create(params);
      if (res.StateCode == 200) {
        that.$message.success("创建成功");
        that.dialogVisible = false;
        that.handleSearch();
      } else {
        that.$message.error(res.Message);
      }
    },

    /**  更新秒杀  */
    async seckill_update() {
      let that = this;
      let params = Object.assign({}, that.addRuleForm);
      params.ActivityStock = params.GoodsList && params.GoodsList.length > 0 ? params.GoodsList[0].ActivityStock : "0";
      params.SeckillPrice = params.GoodsList && params.GoodsList.length > 0 ? params.GoodsList[0].SeckillPrice : "0";
      delete params.GoodsList;
      params.SeckillEntity = that.$refs.grouponEntity.getCheckedKeys();
      params.Quota = params.isQuota ? params.Quota : 0;
      delete params.isQuota;
      let res = await API.seckill_update(params);
      if (res.StateCode == 200) {
        that.$message.success("更新成功");
        that.dialogVisible = false;
        that.handleSearch();
      } else {
        that.$message.error(res.Message);
      }
    },
    /**  秒杀详情  */
    async seckill_info(ID) {
      let that = this;
      let params = { ID: ID };
      let res = await API.seckill_info(params);
      if (res.StateCode == 200) {
        let temp = res.Data;
        let isQuota = temp.Quota == 0 ? false : true;
        that.$set(temp, "isQuota", isQuota);

        temp.BeginDateTime = new Date(temp.BeginDateTime);
        temp.EndDateTime = new Date(temp.EndDateTime);
        temp.GoodsList = [];
        temp.GoodsList.push({
          Name: temp.GoodsName,
          Price: temp.Price,
          ActivityStock: temp.ActivityStock,
          SeckillPrice: temp.SeckillPrice,
          ID: temp.GoodsID,
        });
        that.addRuleForm = temp;
        that.activeName = "0";
        that.scopeData = [];
        that.EndDateTimeDisabled = {
          disabledDate(time) {
            let endTime = time.setHours(17);
            return endTime < temp.BeginDateTime.getTime();
          },
        };
        that.defaultCheckedKeys = [];
        that.defaultExpandedKeys = [1];
        Object.assign(that.scopeData, that.entityList);
        that.seckill_entity(ID);
        that.dialogTitle = "编辑秒杀活动信息";
        that.isAdd = false;
        that.dialogVisible = true;
      } else {
        that.$message.error(res.Message);
      }
    },
    /** 适用门店   */
    async seckill_entity(ID) {
      let that = this;
      let params = { ID: ID };
      let res = await API.seckill_entity(params);
      if (res.StateCode == 200) {
        that.defaultCheckedKeys = res.Data;
      } else {
        that.$message.error(res.Message);
      }
    },

    // 新增时获取权限范围
    async getEntityList() {
      var that = this;
      var params = {
        SearchKey: "",
        Active: "",
      };
      let res = await API.getEntityList(params);
      if (res.StateCode == 200) {
        that.entityList = res.Data;
      } else {
        that.$message.error({
          message: res.Message,
          duration: 2000,
        });
      }
    },

    /**  项目列表  */
    async getProjectList() {
      let that = this;
      let params = {
        IsAllowSell: true,
        Name: that.goodsSearchName,
        PageNum: that.goodspaginations.page,
        ProjectBrandID: "",
        ProjectCategoryID: "",
      };
      let res = await API.getProjectList(params);
      if (res.StateCode == 200) {
        that.goodsList = res.List;
        that.goodspaginations.total = res.Total;
        that.selShopDialogVisible = true;
      } else {
        that.$message.error(res.Message);
      }
    },

    /**  产品列表  */
    async getProductList() {
      let that = this;
      let params = {
        IsAllowSell: true,
        Name: that.goodsSearchName,
        PageNum: that.goodspaginations.page,
        PCategoryID: "",
        ProductBrandID: "",
      };
      let res = await API.getProductList(params);
      if (res.StateCode == 200) {
        that.goodsList = res.List;
        that.goodspaginations.total = res.Total;
        that.selShopDialogVisible = true;
      } else {
        that.$message.error(res.Message);
      }
    },

    /**  通用次卡列表  */
    async getGeneralCardList() {
      let that = this;
      let params = {
        IsAllowSell: true,
        Name: that.goodsSearchName,
        PageNum: that.goodspaginations.page,
        GeneralCardCategoryID: "",
      };
      let res = await API.getGeneralCardList(params);
      if (res.StateCode == 200) {
        that.goodsList = res.List;
        that.goodspaginations.total = res.Total;
        that.selShopDialogVisible = true;
      } else {
        that.$message.error(res.Message);
      }
    },

    /**  时效卡列表  */
    async getTimeCardList() {
      let that = this;
      let params = {
        IsAllowSell: true,
        Name: that.goodsSearchName,
        PageNum: that.goodspaginations.page,
        TimeCardCategoryID: "",
      };
      let res = await API.getTimeCardList(params);
      if (res.StateCode == 200) {
        that.goodsList = res.List;
        that.goodspaginations.total = res.Total;
        that.selShopDialogVisible = true;
      } else {
        that.$message.error(res.Message);
      }
    },
  },
  /** 监听数据变化   */
  watch: {},
  /** 创建实例之前执行函数   */
  beforeCreate() {},
  /**  实例创建完成之后  */
  created() {
    let that = this;
    that.seckill_all();
    that.getEntityList();
  },
  /**  在挂载开始之前被调用  */
  beforeMount() {},
  /**  实例被挂载后调用  */
  mounted() {},
  /**  数据更新 之前 调用  */
  beforeUpdate() {},
  /** 数据更新 完成 调用   */
  updated() {},
  /**  实例销毁之前调用  */
  beforeDestroy() {},
  /**  实例销毁后调用  */
  destroyed() {},
};
</script>

<style lang="scss">
.Seckill {
  .el_scrollbar_height.new {
    #container {
      height: 75vh;
    }
  }
  .el_scrollbar_height.goods {
    height: 60vh;
    #container {
      width: 600px;
      height: 75vh;
    }
  }
  .el-icon-arrow-down {
    font-size: 13px;
  }
  .addSeckillClass {
    .add_el_scrollbar_height {
      height: 60vh;
      .el-scrollbar__wrap {
        overflow-x: hidden;
      }
    }
    .el-dropdown-link {
      cursor: pointer;
      color: var(--zl-color-orange-primary);
    }
    .customPayTime {
      .el-input {
        .el-input__inner {
          padding: 0px 0px 0px 15px !important;
        }
      }
    }
    .customTableClass {
      .el-input {
        .el-input__inner {
          padding: 0px 0px 0px 15px !important;
        }
      }
    }
  }
}
</style>
