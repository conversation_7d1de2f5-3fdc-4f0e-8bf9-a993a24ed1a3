/**
 * Created by preference on 2022/09/02
 *  zmx
 */

import * as API from "@/api/index";
export default {
  /**   */
  channelEmployeeCommissionScheme_all: (params) => {
    return API.POST("api/channelEmployeeCommissionScheme/all", params);
  },
  /**   */
  channelEmployeeCommissionScheme_create: (params) => {
    return API.POST("api/channelEmployeeCommissionScheme/create", params);
  },
  /**   */
  channelEmployeeCommissionScheme_commission: (params) => {
    return API.POST("api/channelEmployeeCommissionScheme/commission", params);
  },
  /**   */
  channelEmployeeCommissionScheme_range: (params) => {
    return API.POST("api/channelEmployeeCommissionScheme/jobType", params);
  },
  /**   */
  channelEmployeeCommissionScheme_update: (params) => {
    return API.POST("api/channelEmployeeCommissionScheme/update", params);
  },
  /**  业绩取值方案查询-不加分页 */
  channelPerformanceScheme_valid: (params) => {
    return API.POST("api/channelEmployeePerformanceScheme/valid", params);
  },
  // 获取职务列表
  getJobTypeAll: (params) => {
    return API.POST("api/jobtype/all", params);
  },
};
