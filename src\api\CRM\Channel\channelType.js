import * as API from '@/api/index'

export default {
  getChannelTypeList:params =>{
    return API.POST('api/channelType/list',params)
  },
  createChannelType:params =>{
    return API.POST('api/channelType/create',params)
  },
  updateChannelType: params => {
    return API.POST('api/channelType/update',params)
  },
  moveChannelType: params => {
    return API.POST('api/channelType/move', params)
  }

}
