<template>
  <div class="BindOpenidSucceed" style="padding: 20px" :style="{ 'background-image': `url(${require('@/assets/img/bindOpenID-bg.png')})` }">
    <p style="font-size: 24px; margin-top: 50px">尾号{{ mantissapn }} <el-tag size="mini">已绑定</el-tag></p>
    <div class="color_999 padtp_10 padbm_10 font_14">当前绑定手机号：<span class="color_main">{{phoneNumber}}</span></div>
    <div class="pad_15 radius5 martp_10" style="background-color: #fdeeeb">
      <p class="color_333">在商家消费后，可通过微信公众号接受消息</p>
      <!-- <div class="color_999 font_13">点击下方按钮进入商城</div> -->
    </div>
  </div>
</template>

<script>
export default {
  name: "BindOpenidSucceed",
  props: {},
  /** 监听数据变化   */
  watch: {},
  /**  引入的组件  */
  components: {},
  /**  Vue 实例的数据对象**/
  data() {
    return {
      mantissapn: null,
      phoneNumber: null,
    };
  },
  /**计算属性  */
  computed: {},
  /**  方法集合  */
  methods: {},
  /** 创建实例之前执行函数   */
  beforeCreate() {},
  /**  实例创建完成之后  */
  created() {},
  /**  在挂载开始之前被调用  */
  beforeMount() {},
  /**  实例被挂载后调用  */
  mounted() {
    var reg = /^(\d{3})\d{4}(\d{4})$/;
    let pn = this.$route.params.phoneNumber;
    this.mantissapn = pn.substring(pn.length - 4, pn.length);
    this.phoneNumber = pn.replace(reg, "$1****$2");
  },
  /**  数据更新 之前 调用  */
  beforeUpdate() {},
  /** 数据更新 完成 调用   */
  updated() {},
  /**  实例销毁之前调用  */
  beforeDestroy() {},
  /**  实例销毁后调用  */
  destroyed() {},
};
</script>

<style lang="scss">
.BindOpenidSucceed {
  height: 100%;
  background-repeat: no-repeat;
  background-position: center;
  background-size: cover;
}
</style>
