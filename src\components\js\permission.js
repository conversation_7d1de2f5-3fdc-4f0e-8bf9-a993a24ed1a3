import route from "../../router";

var Enumerable = require("linq");

export default {
	permission: function(permission, code) {
		var isPermission = Enumerable.from(permission)
			.where(function(i) {
				return i.PermissionCode == code;
			})
			.select((val) => val.IsPermission)
			.toArray();

		return isPermission[0] || false;
	},
	routerPermission: function(path) {
		return route.matcher.match(path).name != "Home";
	},
	getCustomerDetailPermission: function(params, code) {
		let curRoute = params.currentRoute;
		if (curRoute.path !== "/Customer/Customer") {
			let curRoutes = params.getRoutes();
			curRoute = curRoutes.filter((val) => val.path === "/Customer/Customer")[0] || null;
		}
		return curRoute ? this.permission(curRoute.meta.Permission, code) : false;
	},
};
