/**
 * Created by preference on 2022/01/25
 *  zmx
 */

import * as API from '@/api/index';
export default {
  /**  接诊列表 */
  diagnosis_list: (params) => {
    return API.POST('api/diagnosis/list', params);
  },
  /**  接诊列表 */
  diagnosis_all: (params) => {
    return API.POST('api/diagnosis/all', params);
  },
  /**  接诊  */
  diagnosis_diagnosis: (params) => {
    return API.POST('api/diagnosis/diagnosis', params);
  },
  /**  转诊 */
  diagnosis_transferDiagnosis: (params) => {
    return API.POST('api/diagnosis/transferDiagnosis', params);
  },
  /**  顾客接诊列表 */
  customer_diagnosisRecord: (params) => {
    return API.POST('api/customer/diagnosisRecord', params);
  },
  /** 获取服务人员列表   */
  getServiceList: (params) => {
    return API.POST('api/servicer/diagnosisServicer', params);
  },

  /** 获取其他服务人员列表   */
  employee_search: (params) => {
    return API.POST('api/employee/search', params);
  },
  /** 获取用户的服务人员类型列表   */
  customerDetailServicer: (params) => {
    return API.POST('api/customer/customerDetail', params);
  },
  // 导出 隐藏手机号
  diagnosis_excelNoDisPlayPhone: (params) => {
    return API.exportExcel('api/diagnosis/excelNoDisPlayPhone', params);
  },
  // 导出 显示手机号
  diagnosis_excelDisPlayPhone: (params) => {
    return API.exportExcel('api/diagnosis/excelDisPlayPhone', params);
  },
    /* 查询会员等级 */
    customerLevel_all: (params) => {
      return API.POST('api/customerLevel/all', params);
    },
};
