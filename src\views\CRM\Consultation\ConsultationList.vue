<template>
  <div class="consultation-list" v-loading="loading">
    <!-- 搜索栏 -->
    <div class="nav_header">
      <el-row>
        <el-col :span="22">
          <el-form :inline="true" size="small" @keyup.enter.native="handleSearch">
            <el-form-item label="客户姓名">
              <el-input v-model="searchForm.name" placeholder="请输入客户姓名" clearable @clear="handleSearch"></el-input>
            </el-form-item>
            <el-form-item label="手机号">
              <el-input v-model="searchForm.phoneNumber" placeholder="请输入手机号" clearable @clear="handleSearch"></el-input>
            </el-form-item>
            <el-form-item label="性别">
              <el-select v-model="searchForm.gender" placeholder="请选择性别" clearable @change="handleSearch">
                <el-option label="全部" value=""></el-option>
                <el-option label="男" value="1"></el-option>
                <el-option label="女" value="2"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="提交日期">
              <el-date-picker
                v-model="searchForm.dateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                value-format="yyyy-MM-dd"
                clearable
                @change="handleSearch"
              ></el-date-picker>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleSearch" v-prevent-click>搜索</el-button>
            </el-form-item>
          </el-form>
        </el-col>
      </el-row>
    </div>

    <!-- 表格 -->
    <div>
      <el-table size="small" :data="tableData" tooltip-effect="light">
        <el-table-column prop="name" label="客户姓名" width="120"></el-table-column>
        <el-table-column prop="phoneNumber" label="手机号" width="130">
          <template slot-scope="scope">
            {{ scope.row.phoneNumber | hidephone }}
          </template>
        </el-table-column>
        <el-table-column prop="gender" label="性别" width="80" :formatter="formatGender"></el-table-column>
        <el-table-column prop="birthday" label="生日" width="120"></el-table-column>
        <el-table-column prop="isMarried" label="婚姻状况" width="100" :formatter="formatMaritalStatus"></el-table-column>
        <el-table-column prop="address" label="地址" min-width="200"></el-table-column>
        <el-table-column prop="stainsDuration" label="色斑时长" width="120"></el-table-column>
        <el-table-column prop="demand" label="改善诉求" min-width="150">
          <template slot-scope="scope">
            <el-tag v-for="(item, index) in scope.row.demand" :key="index" size="mini" class="tag-item">
              {{ item }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createdOn" label="提交时间" width="160">
          <template slot-scope="scope">
            {{ scope.row.createdOn | dateFormat("YYYY-MM-DD HH:mm") }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="145" fixed="right">
          <template slot-scope="scope">
            <el-button type="primary" size="mini" @click="viewDetail(scope.row)" v-prevent-click>详情</el-button>
            <el-button type="danger" size="mini" @click="deleteItem(scope.row)" v-prevent-click>删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pad_15 text_right">
        <el-pagination
          background
          v-if="paginations.total > 0"
          @current-change="handleCurrentChange"
          :current-page.sync="paginations.page"
          :page-size="paginations.page_size"
          :layout="paginations.layout"
          :total="paginations.total"
        ></el-pagination>
      </div>
    </div>

    <!-- 详情弹窗 -->
    <el-dialog title="面诊单详情" :visible.sync="detailVisible" width="80%" :close-on-click-modal="false">
      <div v-if="currentDetail" class="detail-content">
        <!-- 个人信息 -->
        <div class="detail-section">
          <h3 class="detail-title">个人信息</h3>
          <el-row :gutter="20">
            <el-col :span="6">
              <div class="detail-item">
                <label>姓名：</label>
                <span>{{ currentDetail.name }}</span>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="detail-item">
                <label>生日：</label>
                <span>{{ currentDetail.birthday }}</span>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="detail-item">
                <label>性别：</label>
                <span>{{ formatGender(currentDetail) }}</span>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="detail-item">
                <label>电话：</label>
                <span>{{ currentDetail.phoneNumber }}</span>
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="6">
              <div class="detail-item">
                <label>婚姻状况：</label>
                <span>{{ formatMaritalStatus(currentDetail) }}</span>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="detail-item" v-if="currentDetail.isMarried">
                <label>子女数量：</label>
                <span>{{ formatChildrenCount(currentDetail) }}</span>
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <div class="detail-item">
                <label>省市区：</label>
                <span>{{ formatProvinceCityArea(currentDetail) }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="detail-item">
                <label>详细地址：</label>
                <span>{{ currentDetail.address }}</span>
              </div>
            </el-col>
          </el-row>
        </div>

        <!-- 祛斑经历 -->
        <div class="detail-section">
          <h3 class="detail-title">祛斑经历</h3>
          <div class="tag-container">
            <el-tag v-for="(item, index) in currentDetail.spotRemoval" :key="index" class="tag-item">
              {{ item }}
            </el-tag>
            <span v-if="!currentDetail.spotRemoval || currentDetail.spotRemoval.length === 0" class="no-data">无</span>
          </div>
        </div>

        <!-- 色斑出现时长 -->
        <div class="detail-section">
          <h3 class="detail-title">色斑出现时长</h3>
          <div class="detail-item">
            <span>{{ currentDetail.stainsDuration || '未填写' }}</span>
          </div>
        </div>

        <!-- 美容经历 -->
        <div class="detail-section">
          <h3 class="detail-title">美容经历</h3>
          <div class="tag-container">
            <el-tag v-for="(item, index) in currentDetail.cosmetic" :key="index" class="tag-item" type="success">
              {{ item }}
            </el-tag>
            <span v-if="!currentDetail.cosmetic || currentDetail.cosmetic.length === 0" class="no-data">无</span>
          </div>
        </div>

        <!-- 家居护肤品 -->
        <div class="detail-section">
          <h3 class="detail-title">家居护肤品</h3>
          <div class="tag-container">
            <el-tag v-for="(item, index) in currentDetail.skinCare" :key="index" class="tag-item" type="warning">
              {{ item }}
            </el-tag>
            <span v-if="!currentDetail.skinCare || currentDetail.skinCare.length === 0" class="no-data">无</span>
          </div>
          <div v-if="currentDetail.brand" class="detail-item" style="margin-top: 10px;">
            <label>使用品牌：</label>
            <span>{{ currentDetail.brand }}</span>
          </div>
        </div>

        <!-- 改善诉求 -->
        <div class="detail-section">
          <h3 class="detail-title">改善诉求</h3>
          <div class="tag-container">
            <el-tag v-for="(item, index) in currentDetail.demand" :key="index" class="tag-item" type="info">
              {{ item }}
            </el-tag>
            <span v-if="!currentDetail.demand || currentDetail.demand.length === 0" class="no-data">无</span>
          </div>
        </div>
      </div>
      <div slot="footer">
        <el-button @click="detailVisible = false">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import API from "@/api/CRM/Consultation/consultation";
import { CodeToText } from "element-china-area-data";

export default {
  name: "ConsultationList",
  data() {
    return {
      loading: false,
      exportLoading: false,
      detailVisible: false,
      currentDetail: null,
      searchForm: {
        name: "",
        phoneNumber: "",
        gender: "",
        dateRange: []
      },
      tableData: [],
      paginations: {
        page: 1,
        total: 0,
        page_size: 10,
        layout: "total, prev, pager, next, jumper"
      }
    };
  },
  methods: {
    // 搜索
    handleSearch() {
      this.paginations.page = 1;
      this.getList();
    },

    // 获取列表数据
    getList() {
      this.loading = true;
      const params = {
        name: this.searchForm.name,
        phoneNumber: this.searchForm.phoneNumber,
        gender: this.searchForm.gender,
        startTime: this.searchForm.dateRange && this.searchForm.dateRange.length ? this.searchForm.dateRange[0] : "",
        endTime: this.searchForm.dateRange && this.searchForm.dateRange.length > 1 ? this.searchForm.dateRange[1] : "",
        pageNum: this.paginations.page,
        pageSize: this.paginations.page_size
      };

      API.query(params)
        .then((res) => {
          if (res.StateCode === 200) {
            this.tableData = res.List || [];
            this.paginations.total = res.Total || 0;
          } else {
            this.$message.error(res.Message || "获取数据失败");
          }
        })
        .catch((error) => {
          console.error("获取列表失败:", error);
          this.$message.error("获取数据失败，请检查网络连接");
        })
        .finally(() => {
          this.loading = false;
        });
    },

    // 分页
    handleCurrentChange(page) {
      this.paginations.page = page;
      this.getList();
    },

    // 查看详情
    viewDetail(row) {
      this.currentDetail = row;
      this.detailVisible = true;
    },

    // 删除
    deleteItem(row) {
      this.$confirm("确定要删除这条咨询服务单吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        API.delete({ ids: [row.id] })
          .then((res) => {
            if (res.StateCode === 200) {
              this.$message.success("删除成功");
              this.getList();
            } else {
              this.$message.error(res.Message || "删除失败");
            }
          })
          .catch((error) => {
            console.error("删除失败:", error);
            this.$message.error("删除失败，请检查网络连接");
          });
      });
    },

    // 格式化性别
    formatGender(row) {
      switch (row.gender) {
        case "1":
          return "男";
        case "2":
          return "女";
        default:
          return "未知";
      }
    },

    // 格式化婚姻状况
    formatMaritalStatus(row) {
      if (row.isMarried === null || row.isMarried === undefined) return '未填写';
      return row.isMarried ? '已婚' : '未婚';
    },
    
    // 格式化省市区显示
    formatProvinceCityArea(row) {
      const provinceName = row.provinceCode ? CodeToText[row.provinceCode] : '';
      const cityName = row.cityCode ? CodeToText[row.cityCode] : '';
      const areaName = row.areaCode ? CodeToText[row.areaCode] : '';
      
      const names = [provinceName, cityName, areaName].filter(item => item);
      return names.length > 0 ? names.join(' / ') : '';
    },
    
    // 格式化子女数量
    formatChildrenCount(row) {
      if (row.isMarried) {
        switch (row.childrenCount) {
          case 1:
            return '一孩';
          case 2:
            return '二孩';
          case 3:
            return '三孩及以上';
          default:
            return '';
        }
      }
      return '';
    }
  },
  mounted() {
    this.getList();
  }
};
</script>

<style lang="scss" scoped>
.consultation-list {
  .nav_header {
    background: #fff;
    padding: 20px;
    margin-bottom: 20px;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .tag-item {
    margin-right: 8px;
    margin-bottom: 4px;
  }

  .detail-content {
    .detail-section {
      margin-bottom: 30px;
      
      .detail-title {
        font-size: 16px;
        color: #333;
        margin-bottom: 15px;
        padding-bottom: 8px;
        border-bottom: 1px solid #eee;
      }
      
      .detail-item {
        margin-bottom: 10px;
        
        label {
          font-weight: 500;
          color: #666;
          margin-right: 8px;
        }
        
        span {
          color: #333;
        }
      }
      
      .tag-container {
        .tag-item {
          margin-right: 8px;
          margin-bottom: 8px;
        }
        
        .no-data {
          color: #999;
          font-style: italic;
        }
      }
    }
  }
}
</style>
