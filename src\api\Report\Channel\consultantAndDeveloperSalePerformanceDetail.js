/**
 * Created by preference on 2022/08/11
 *  zmx
 */

import * as API from '@/api/index';
export default {
  /* 销售明细 */
  channelSaleDetailStatement_list: (params) => {
    return API.POST('api/channelSaleDetailStatement/list', params);
  },
  // 销售明细导出
  channelSaleDetailStatement_excel: (params) => {
    return API.exportExcel('api/channelSaleDetailStatement/excel', params);
  },

  /* 补欠款明细 */
  channelSaleArrearDetailStatement_list: (params) => {
    return API.POST('api/channelSaleArrearDetailStatement/list', params);
  },
  // 补欠款明细导出
  channelSaleArrearDetailStatement_excel: (params) => {
    return API.exportExcel('api/channelSaleArrearDetailStatement/excel', params);
  },

  /* 退款明细 */
  channelSaleRefundDetailStatement_list: (params) => {
    return API.POST('api/channelSaleRefundDetailStatement/list', params);
  },
  // 退款明细导出
  channelSaleRefundDetailStatement_excel: (params) => {
    return API.exportExcel('api/channelSaleRefundDetailStatement/excel', params);
  },
  // 营业报表-消耗统计
  allEntity: (params) => {
    return API.POST('api/entity/allEntity', params);
  },
};
