<template>
  <div class="appointmentType content_body">
    <div class="nav_header">
      <el-row>
        <el-col :span="20">
          <el-form :inline="true" size="small" @keyup.enter.native="appointmentType_all">
            <el-form-item label="预约类型名称">
              <el-input v-model="searchForm.Name" @clear="appointmentType_all" placeholder="输入预约类型名称搜索" clearable></el-input>
            </el-form-item>
            <el-form-item label="有效性">
              <el-select @change="appointmentType_all" @clear="appointmentType_all" v-model="searchForm.Active" placeholder="请选择" clearable>
                <el-option label="有效" :value="true"></el-option>
                <el-option label="无效" :value="false"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="appointmentType_all" v-prevent-click>搜索</el-button>
            </el-form-item>
          </el-form>
        </el-col>
        <el-col :span="4" class="text_right">
          <el-button type="primary" size="small" @click="addPojectBrandClick">新增</el-button>
        </el-col>
      </el-row>
    </div>

      <el-table size="small" :data="tableData" v-loading="loading">
        <el-table-column prop="Name" label="预约类型名称"></el-table-column>
        <el-table-column label="移动" min-width="180px">
          <template slot-scope="scope">
            <el-button
              size="small"
              type="primary"
              circle
              icon="el-icon-upload2"
              @click="upOneClick(scope.row, scope.$index)"
              v-prevent-click
              :disabled="scope.$index == 0"
            ></el-button>
            <el-button
              size="small"
              type="primary"
              circle
              icon="el-icon-top"
              @click="upClick(scope.row, scope.$index)"
              v-prevent-click
              :disabled="scope.$index == 0"
            ></el-button>
            <el-button
              size="small"
              type="primary"
              circle
              icon="el-icon-bottom"
              @click="downClick(scope.row, scope.$index)"
              v-prevent-click
              :disabled="scope.$index == tableData.length - 1"
            ></el-button>
            <el-button
              size="small"
              type="primary"
              circle
              icon="el-icon-download"
              @click="downOneClick(scope.row, scope.$index)"
              v-prevent-click
              :disabled="scope.$index == tableData.length - 1"
            ></el-button>
          </template>
        </el-table-column>
        <el-table-column prop="Active" label="有效性" :formatter="formatStatus"></el-table-column>
        <el-table-column label="操作" width="80">
          <template slot-scope="scope">
            <el-button type="primary" size="small" @click="showEditDialog(scope.row)">编辑</el-button>
          </template>
        </el-table-column>
      </el-table>

    <!--弹窗-->
    <el-dialog :title="isAdd ? '新增预约类型' : '编辑预约类型'" :visible.sync="dialogVisible" width="550px">
      <div>
        <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="120px" size="small" @submit.native.prevent>
          <el-form-item label="预约类型名称" prop="Name">
            <el-input clearable v-model="ruleForm.Name"></el-input>
          </el-form-item>
          <el-form-item label="是否有效" v-if="!isAdd">
            <el-radio-group v-model="ruleForm.Active">
              <el-radio :label="true">是</el-radio>
              <el-radio :label="false">否</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="dialogVisible = false" v-prevent-click>取 消</el-button>
        <el-button type="primary" size="small" @click="addSubmit" :loading="modalLoading" v-prevent-click>保 存</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import API from "@/api/iBeauty/Appointment/appointmentType.js";
export default {
  name: "appointmentType",
  props: {},
  /** 监听数据变化   */
  watch: {},
  /**  引入的组件  */
  components: {},
  /**  Vue 实例的数据对象**/
  data() {
    return {
      loading: false,
      dialogVisible: false,
      modalLoading: false,
      tableData: [],
      searchForm: {
        Name: "", //搜索名称
        Active: true, //有效性
      },
      isAdd: "",
      ruleForm: {
        Name: "",
        Active: true,
      },
      rules: {
        Name: [{ required: true, message: "请输入预约类型名称", trigger: "blur" }],
        Active: [{ required: true, message: "请选择有效性", trigger: "change" }],
      },
    };
  },
  /**计算属性  */
  computed: {},
  /**  方法集合  */
  methods: {
    // //状态显示转换
    formatStatus: function (row) {
      return row.Active ? "有效" : "无效";
    },
    /**  新增预约类型弹窗  */
    addPojectBrandClick() {
      var that = this;
      that.dialogVisible = true;
      that.isAdd = true;
      that.ruleForm = {
        Name: "",
      };
    },
    // 编辑
    showEditDialog: function (row) {
      var that = this;
      that.dialogVisible = true;
      that.isAdd = false;
      that.ruleForm = Object.assign({}, row);
    },
    /**  保存 预约类型信息    */
    addSubmit() {
      let that = this;
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          that.modalLoading = true;
          if (that.isAdd) {
            that.appointmentType_create();
          } else {
            that.appointmentType_update();
          }
        }
      });
    },

    // 移动首部
    upOneClick: function (row) {
      var that = this;
      that.appointmentType_move(row.ID, "");
    },
    // 移动尾部
    downOneClick: function (row, index) {
      var that = this;
      var tabIndex = that.tableData.length;
      var beforeId = "";
      if (index < tabIndex - 1) {
        beforeId = that.tableData[tabIndex - 1].ID;
      }
      that.appointmentType_move(row.ID, beforeId);
    },
    // 向上
    upClick: function (row, index) {
      var that = this;
      var beforeId = "";
      if (index > 1) {
        beforeId = that.tableData[index - 2].ID;
      }
      that.appointmentType_move(row.ID, beforeId);
    },
    // 向下
    downClick: function (row, index) {
      var that = this;
      var beforeId = "";
      if (index + 1 != that.tableData.length) {
        beforeId = that.tableData[index + 1].ID;
      }
      that.appointmentType_move(row.ID, beforeId);
    },
    /**••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••*/
    /**    */
    async appointmentType_all() {
      let that = this;
      try {
        that.loading = true;
        let params = {
          Name: that.searchForm.Name, //搜索名称
          Active: that.searchForm.Active, //有效性
        };
        let res = await API.appointmentType_all(params);
        if (res.StateCode == 200) {
          that.tableData = res.Data;
        } else {
          that.$message.error(res.Message);
        }
        that.loading = false;
      } catch (error) {
        that.loading = false;
        that.$message.error(error);
      }
    },

    /**    */
    async appointmentType_create() {
      let that = this;
      try {
        let params = {
          Name: that.ruleForm.Name,
        };
        let res = await API.appointmentType_create(params);
        if (res.StateCode == 200) {
          that.$message.success("操作成功");
          that.dialogVisible = false;
          that.appointmentType_all();
        } else {
          that.$message.error(res.Message);
        }
        that.modalLoading = false;
      } catch (error) {
        that.modalLoading = false;
        that.$message.error(error);
      }
    },

    /**    */
    async appointmentType_update() {
      let that = this;
      try {
        let params = {
          ID: that.ruleForm.ID, //预约类型ID
          Name: that.ruleForm.Name, //预约类型名称
          Active: that.ruleForm.Active, //有效性
        };
        let res = await API.appointmentType_update(params);
        if (res.StateCode == 200) {
          that.$message.success("操作成功");
          that.dialogVisible = false;
          that.appointmentType_all();
        } else {
          that.$message.error(res.Message);
        }
        that.modalLoading = false;
      } catch (error) {
        that.modalLoading = false;
        that.$message.error(error);
      }
    },

    /**    */
    async appointmentType_move(MoveID, BeforeID) {
      let that = this;
      try {
        let params = {
          MoveID: MoveID,
          BeforeID: BeforeID,
        };
        let res = await API.appointmentType_move(params);
        if (res.StateCode == 200) {
          that.$message.success("操作成功");
          that.appointmentType_all();
        } else {
          that.$message.error(res.Message);
        }
      } catch (error) {
        that.$message.error(error);
      }
    },
  },
  /** 创建实例之前执行函数   */
  beforeCreate() {},
  /**  实例创建完成之后  */
  created() {},
  /**  在挂载开始之前被调用  */
  beforeMount() {},
  /**  实例被挂载后调用  */
  mounted() {
    this.appointmentType_all();
  },
  /**  数据更新 之前 调用  */
  beforeUpdate() {},
  /** 数据更新 完成 调用   */
  updated() {},
  /**  实例销毁之前调用  */
  beforeDestroy() {},
  /**  实例销毁后调用  */
  destroyed() {},
};
</script>

<style lang="scss">
.appointmentType {
}
</style>
