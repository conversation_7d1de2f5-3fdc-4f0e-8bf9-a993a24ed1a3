<template>
  <div>
    <!-- 性别 -->
    <el-card class="box-card" shadow="never">
      <div class="dis_flex flex_x_between flex_y_center marbm_20">
        <span>{{title}}</span>
        <i class="el-icon-close" @click="handlerClose"></i>
      </div>
      <div>
        <span class="marrt_15">{{contentTitle}}</span>
        <el-radio-group v-model="contentValues_"  @change="handlerChange">
          <el-radio :label="1">男</el-radio>
          <el-radio :label="2">女</el-radio>
          <el-radio :label="0">未知</el-radio>
        </el-radio-group>
      </div>
    </el-card>
  </div>
</template>

<script>
export default {
  name: "followRadio",
  components: {},
  props: {
    title: {
      type: String,
      default: "",
    },
    subTitle: {
      type: String,
      default: null,
    },
    contentTitle: {
      type: String,
      default: null,
    },
    Code:{
      type: String,
      default: null,
    },
    contentValues: {
      type: Number,
      default:1,
    },
  },
  data() {
    return {
      contentValues_: 1,
    };
  },
  computed: {},
  watch: {
    contentValues:  {
      handler(val) {
        this.contentValues_ = val;
      },
      immediate: true,
    },
  },
  created() {},
  mounted() {},
  methods: {
    handlerChange() {
      this.$emit("handlerChildChange", this.contentValues_);
    },
    handlerClose() {
      this.$emit("handlerChildClone", this.Code);
    },
  },
};
</script>

<style scoped lang="less">
