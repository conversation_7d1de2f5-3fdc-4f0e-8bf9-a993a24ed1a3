<template>
  <div class="followUpRecord">
    <el-scrollbar style="height: 100%" class="custom-scrollbar_hidden-x">
      <div v-if="customerID" class="button-box">
        <div v-if="!isCallBack">
          <el-button size="small" type="primary" @click="handleFollowUpClick">新建跟进</el-button>

          <!-- 预约相关按钮 - 暂时注释，避免与外层列表功能冲突 -->
          <!--
          <el-button
            v-if="!hasAppointment"
            size="small"
            type="primary"
            plain
            @click="addAppointmentClick"
          >
            预约
          </el-button>

          <el-dropdown
            v-if="hasAppointment && (appointmentStatus === 10 || appointmentStatus === '10')"
            @command="handleAppointmentCommand"
            trigger="click"
            size="small"
          >
            <el-button size="small" type="success" plain>
              预约管理<i class="el-icon-arrow-down el-icon--right"></i>
            </el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item :command="{action: 'edit'}">
                <i class="el-icon-edit"></i> 修改预约
              </el-dropdown-item>
              <el-dropdown-item :command="{action: 'cancel'}">
                <i class="el-icon-close"></i> 取消预约
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>

          <el-button
            v-if="hasAppointment && (appointmentStatus === 20 || appointmentStatus === '20')"
            size="small"
            type="primary"
            plain
            @click="addAppointmentClick"
          >
            继续预约
          </el-button>

          <el-button
            v-if="hasAppointment && (appointmentStatus === 30 || appointmentStatus === '30')"
            size="small"
            type="primary"
            plain
            @click="addAppointmentClick"
          >
            重新预约
          </el-button>
          -->

          <el-button size="small" type="primary" @click="assignClick">指派跟进任务</el-button>
        </div>
      </div>
      <el-row class="custom-header">
        <el-col :offset="3" :sapn="24">
          <el-checkbox-group v-model="typeList" @change="changeTypeListClick">
            <el-checkbox label="10">跟进</el-checkbox>
            <el-checkbox label="20">接诊</el-checkbox>
            <el-checkbox label="30">回访</el-checkbox>
          </el-checkbox-group>
        </el-col>
      </el-row>
      <el-row class="martp_15">
        <el-col :offset="3" :span="21">
          <el-timeline class="csutom-timeline-conten">
            <el-timeline-item v-for="yearItem in activities" :key="'year-' + yearItem.Year" class="position_relative" color="var(--zl-color-orange-primary)" size="large">
              <!-- 年份 -->
              <div class="position_absolute" style="top: 0; left: -75px">
                <span @click="expandTimelineClick(yearItem)" class="bold">
                  <span>{{ yearItem.Year }}</span>
                  <i :class="[yearItem.isYearExpand ? 'el-icon-caret-bottom' : 'el-icon-caret-right', 'color_999', 'marlt_5']"></i>
                </span>
              </div>
              <el-timeline v-show="yearItem.isYearExpand" class="custom-moth-timeline">
                <el-timeline-item v-for="mothItem in yearItem.Child" :key="'moth-' + mothItem.Month" color="var(--zl-color-orange-primary)">
                  <!-- 月 -->
                  <span class="bold" @click="showDetailsClick(mothItem)"
                    >{{ mothItem.Month }}月
                    <i :class="[mothItem.isMothExpand ? 'el-icon-caret-bottom' : 'el-icon-caret-right', 'color_999']"></i>
                  </span>

                  <el-timeline v-show="mothItem.isMothExpand" class="custom-day-timeline">
                    <el-timeline-item v-for="(dayItem, index) in mothItem.Log" :key="getID(dayItem, index)" color="var(--zl-color-orange-primary)">
                      <el-row type="flex" justify="space-between">
                        <el-col :span="3">
                          <p class="bold">
                            {{ dayItem.PlannedOn | formatDateStr("date") }}
                          </p>
                          <span class="font_12 color_999">{{ dayItem.PlannedOn | formatDateStr("time") }}</span>
                        </el-col>
                        <el-col :span="18">
                          <p class="color_333">
                            <span>{{ dayItem.EmployeeName }}</span>
                            <span v-show="dayItem.JobName">[{{ dayItem.JobName }}]</span>
                            <span v-show="dayItem.EntityName && layoutType === 'inline'" class="color_999 font_13" style="margin-left: 5px;">{{ dayItem.EntityName }}</span>
                            <el-tag :type="getTag(dayItem.Type)" class="marlt_10" size="small">{{ getType(dayItem.Type) }}</el-tag>
                            <span style="margin-left: 10px" v-if="!dayItem.Status && dayItem.Type == 10" class="color_main">待跟进</span>
                            <span style="margin-left: 10px" v-if="!dayItem.Status && dayItem.Type == 30" class="color_main">待回访</span>
                          </p>
                          <span v-show="dayItem.EntityName && layoutType === 'default'" class="color_999 font_13">{{ dayItem.EntityName }}</span>
                        </el-col>
                        <el-col :span="3">
                          <el-button v-if="(dayItem.Status && dayItem.Type == 10 && dayItem.EmployeeID == employeeID) || (dayItem.Status && dayItem.Type == 30 && dayItem.EmployeeID == employeeID)" size="small" type="primary" @click="modifyClick(dayItem)">修改</el-button>

                          <el-button v-if="dayItem.Type == 20 && dayItem.EmployeeID == employeeID" size="small" type="primary" @click="modifyConsultClick(dayItem)">修改</el-button>

                          <el-button v-if="(!dayItem.Status && dayItem.Type == 10 && dayItem.EmployeeID == employeeID) || (!dayItem.Status && dayItem.Type == 30 && dayItem.EmployeeID == employeeID)" size="small" type="danger" @click="deleteClick(dayItem)">删除</el-button>
                        </el-col>
                      </el-row>
                      <el-row>
                        <el-card class="martp_5" shadow="always" v-if="dayItem.Type == 10 || dayItem.Type == 20">
                          <el-row v-if="dayItem.AssignName != dayItem.EmployeeName" style="line-height: 1.5">
                            <el-col :span="3" style="color: #909399">指派人员：</el-col>
                            <el-col :span="21" style="color: #606266">{{ dayItem.AssignName }}</el-col>
                          </el-row>
                          <el-row v-if="dayItem.Remark" style="line-height: 1.5">
                            <el-col :span="3" style="color: #909399">任务备注：</el-col>
                            <el-col :span="21" style="color: #606266">{{ dayItem.Remark }}</el-col>
                          </el-row>
                          <!-- 跟进方式：在跟进工作台中显示，在线索列表和客服预约表中隐藏 -->
                          <el-row v-if="dayItem.MethodName && layoutType === 'default'" style="line-height: 1.5">
                            <el-col :span="3" style="color: #909399">{{ getType(dayItem.Type) }}方式：</el-col>
                            <el-col :span="21" style="color: #606266">
                              <span style="margin-right: 10px">{{ dayItem.MethodName }}</span>
                              <span v-if="dayItem.Status">【{{ dayItem.Status }}】</span>
                            </el-col>
                          </el-row>
                          <!-- 跟进时间：在跟进工作台中显示，在线索列表和客服预约表中隐藏 -->
                          <el-row v-if="dayItem.FollowUpOn && layoutType === 'default'" style="line-height: 1.5">
                            <el-col :span="3" style="color: #909399">{{ getType(dayItem.Type) }}时间：</el-col>
                            <el-col :span="21" style="color: #606266">{{ dayItem.FollowUpOn | dateFormat("YYYY-MM-DD HH:mm") }}</el-col>
                          </el-row>
                          <el-row v-if="dayItem.Content" style="line-height: 1.5">
                            <el-col :span="3" style="color: #909399; white-space: pre">{{ getType(dayItem.Type) }}记录：</el-col>
                            <el-col :span="21" style="color: #606266; white-space: pre-wrap">{{ dayItem.Content }}</el-col>
                          </el-row>
                          <el-row class="martp_5" v-if="dayItem.Attachment && dayItem.Attachment.length > 0">
                            <el-col :offset="3" :span="21">
                              <el-image class="imgShowBox" v-for="(img, imgIndex) in dayItem.Attachment" :key="imgIndex" :src="img.AttachmentURL + '?x-oss-process=image/resize,h_100,m_lfit'" @click="handlePreviewImage(dayItem.Attachment,img.AttachmentURL)"  fit="cover"></el-image>
                              <!-- :preview-src-list="dayItem.Attachment.map((val) => val.AttachmentURL)"  -->
                            </el-col>
                          </el-row>
                        </el-card>
                        <el-card class="martp_5" shadow="always" v-if="dayItem.Type == 30">
                          <el-col :span="24" class="martp_5">
                            <!-- v-for="(callBackItem, index) in dayItem['CallbackRecord']" :key="index" -->
                            <el-row v-if="dayItem.CallbackCycle" style="line-height: 1.5">
                              <el-col :span="3" style="color: #909399">回访天数：</el-col>
                              <el-col :span="21" style="color: #606266">{{ dayItem.CallbackCycle }}天</el-col>
                            </el-row>
                            <el-row v-if="dayItem.ProjectName" style="line-height: 1.5">
                              <el-col :span="3" style="color: #909399">回访项目：</el-col>
                              <el-col :span="21" style="color: #606266"
                                ><el-tag size="mini">{{ dayItem.ProjectName }}</el-tag></el-col
                              >
                            </el-row>
                            <el-row v-if="dayItem.Remark" style="line-height: 1.5">
                              <el-col :span="3" style="color: #909399; white-space: pre">回访内容：</el-col>
                              <el-col :span="21" style="color: #606266">{{ dayItem.Remark }}</el-col>
                            </el-row>

                            <el-row v-if="dayItem.Content" style="line-height: 1.5">
                              <el-col :span="3" style="color: #909399; white-space: pre">回访记录：</el-col>
                              <el-col :span="21" style="color: #606266">{{ dayItem.Content }}</el-col>
                            </el-row>

                            <el-row class="martp_5">
                              <el-col :offset="3" :span="21">
                                <el-image class="imgShowBox" v-for="(img, imgIndex) in dayItem.Attachment" :key="imgIndex" :src="img.AttachmentURL + '?x-oss-process=image/resize,h_100,m_lfit'"  @click="handlePreviewImage(dayItem.Attachment,img.AttachmentURL)"  fit="cover"></el-image>

                                <!-- :preview-src-list="dayItem.Attachment.map((val) => val.AttachmentURL)" -->
                              </el-col>
                            </el-row>
                          </el-col>
                        </el-card>

                        <el-card class="martp_5" shadow="always" v-if="dayItem.Type == 40">
                          <el-col :span="24" class="martp_5">
                            <el-row v-if="dayItem.Content" style="line-height: 1.5">
                              <el-col :span="3" style="color: #909399">跟进内容：</el-col>
                              <el-col :span="21" style="color: #606266">
                                {{ dayItem.Content }}
                              </el-col>
                            </el-row>
                            <!-- :preview-src-list="dayItem.Attachment.map((val) => val.AttachmentURL)"  -->
                            <el-row class="martp_5">
                              <el-col :offset="3" :span="21">
                                <el-image class="imgShowBox" v-for="(img, imgIndex) in dayItem.Attachment" :key="imgIndex" :src="img.AttachmentURL + '?x-oss-process=image/resize,h_100,m_lfit'" @click="handlePreviewImage(dayItem.Attachment,img.AttachmentURL)"  fit="cover"></el-image>
                              </el-col>
                            </el-row>
                          </el-col>
                        </el-card>
                      </el-row>
                    </el-timeline-item>
                  </el-timeline>
                </el-timeline-item>
              </el-timeline>
            </el-timeline-item>
          </el-timeline>
        </el-col>
      </el-row>
    </el-scrollbar>
    <!-- 新建、处理弹出框 -->
    <el-dialog :title="isAdd ? '新建跟进' : '处理跟进'" :visible.sync="dialogVisible" width="800px" custom-class="custom-dialog" @close="closeAddFollowUpDialog" append-to-body>
      <el-scrollbar class="el_scrollbar_height_followup">
        <div class="information">
          <el-row type="flex" align="" :class="AssignName != EmployeeName || PlannedRemark ? 'border-bottom' : ''" style="padding-bottom: 5px">
            <el-col :span="2">
              <el-avatar :size="50" :src="circleUrl"></el-avatar>
            </el-col>
            <el-col :span="22">
              <el-row type="flex" justify="space-between">
                <el-col :span="24">
                  <strong class="marrt_5 font_18">{{ customerDetail.Name }}</strong>
                  <el-image v-if="customerDetail.Gender == 2" style="width: 8px; height: 12px" :src="require('@/assets//img//gender-female.png')"></el-image>
                  <el-image v-if="customerDetail.Gender == 1" style="width: 8px; height: 12px" :src="require('@/assets/img/gender-male.png')"></el-image>
                </el-col>
              </el-row>
              <el-col justify="space-between">
                <el-col :span="8" class="color_999 martp_10"
                  >手机号：<span class="color_333">{{ customerDetail.PhoneNumber }}</span></el-col
                >
                <el-col :span="8" class="color_999 martp_10"
                  >客户编号：<span class="color_333">{{ customerDetail.Code }}</span></el-col
                >
                <el-col :span="8" class="color_999 martp_10"
                  >注册时间：<span class="color_333">{{ customerDetail.CreatedOn }}</span></el-col
                >
                <el-col :span="8" class="color_999 martp_10"
                  >介绍人：<span class="color_333">{{ customerDetail.IntroducerName }}</span></el-col
                >
                <el-col :span="8" class="color_999 martp_10"
                  >渠道来源：<span class="color_333">{{ customerDetail.ChannelName }}</span></el-col
                >
                <el-col :span="8" class="color_999 martp_10"
                  >信息来源：<span class="color_333">{{ customerDetail.CustomerSourceName }}</span></el-col
                >
              </el-col>
            </el-col>
          </el-row>
          <el-form size="small" v-if="!isAdd">
            <el-row>
              <el-col :span="24">
                <el-form-item v-if="AssignName != EmployeeName" style="margin-bottom: 0">
                  <el-col :span="3">指派人：</el-col>
                  <el-col :span="21">{{ AssignName }}</el-col>
                </el-form-item>
              </el-col>
              <el-col :span="24" v-if="PlannedRemark">
                <el-form-item style="margin-bottom: 0">
                  <el-col :span="3">任务备注：</el-col>
                  <el-col :span="21"> {{ PlannedRemark }}</el-col>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>
        <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="110px" size="small">
          <el-row>
            <el-col :span="24">
              <el-form-item label="跟进方式" prop="FollowUpMethodID">
                <el-radio-group v-model="ruleForm.FollowUpMethodID">
                  <el-radio v-for="item in tableDataMethod" :key="item.ID" :label="item.ID">{{ item.Name }}</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="跟进状态" prop="FollowUpStatusID">
                <el-radio-group v-model="ruleForm.FollowUpStatusID">
                  <el-radio v-for="item in tableDataStatus" :key="item.ID" :label="item.ID">{{ item.Name }}</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="跟进记录" prop="FollowUpContent">
                <el-input type="textarea" :rows="5" v-model="ruleForm.FollowUpContent"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="">
                <el-upload :limit="9" class="avatar-uploader" list-type="picture-card" action="#" :file-list="ruleForm.Attachment" :before-upload="commodityMainbeforeUpload" :on-remove="commodityMainRemove" accept="image/*" multiple>
                  <i class="el-icon-plus avatar-uploader-icon"></i>
                  <div slot="file" slot-scope="{ file }" style="height: 100px; widht: 100px">
                    <el-image :id="file.uid" :src="file.AttachmentURL" :preview-src-list="preview_src_list" :z-index="2000" fit="cover" style="height: 100%; width: 100%"></el-image>
                    <span class="el-upload-list__item-actions">
                      <span class="el-upload-list__item-preview" @click="DialogPreview(file)">
                        <i class="el-icon-zoom-in"></i>
                      </span>
                      <span class="el-upload-list__item-preview" @click="commodityMainRemove(file)">
                        <i class="el-icon-delete"></i>
                      </span>
                    </span>
                  </div>
                </el-upload>
              </el-form-item>
            </el-col>
            <el-col :span="24" v-if="isAdd">
              <el-form-item
                label="下次跟进"
                prop="IsNextFollowUp"
                :rules="[
                  {
                    required: false,
                    message: '请选择下次是否跟进',
                    trigger: 'change',
                  },
                ]"
              >
                <el-radio-group v-model="ruleForm.IsNextFollowUp">
                  <el-radio :label="true">是</el-radio>
                  <el-radio :label="false">否</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="24" v-if="isAdd && ruleForm.IsNextFollowUp">
              <el-form-item
                label="下次跟进时间"
                prop="PlannedOn"
                :rules="[
                  {
                    required: ruleForm.IsNextFollowUp,
                    message: '请选择下次跟进时间',
                    trigger: 'change',
                  },
                ]"
              >
                <el-date-picker v-model="ruleForm.PlannedOn" type="datetime" value-format="yyyy-MM-dd HH:mm:ss" :default-time="nextDateTime" placeholder="请选择下次跟进时间"> </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="24" v-if="isAdd && ruleForm.IsNextFollowUp">
              <el-form-item label="备注">
                <el-input type="textarea" :rows="3" v-model="ruleForm.PlannedRemark"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </el-scrollbar>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false" size="small" v-prevent-click>取 消</el-button>
        <el-button type="primary" size="small" @click="submitFollowUp" v-prevent-click :loading="modalLoading">保 存</el-button>
      </span>
    </el-dialog>
    <!-- 指派弹出框 -->
    <el-dialog title="指派跟进" :visible.sync="assignDialogVisible" width="800px" custom-class="custom-dialog" append-to-body>
      <el-scrollbar class="el_scrollbar_height_followup">
        <div class="information">
          <el-row type="flex" align="" style="padding-bottom: 5px">
            <el-col :span="2">
              <el-avatar :size="50" :src="circleUrl"></el-avatar>
            </el-col>
            <el-col :span="22">
              <el-row type="flex" justify="space-between">
                <el-col :span="24">
                  <strong class="marrt_5 font_18">{{ customerDetail.Name }}</strong>
                  <el-image v-if="customerDetail.Gender == 2" style="width: 8px; height: 12px" :src="require('@/assets//img//gender-female.png')"></el-image>
                  <el-image v-if="customerDetail.Gender == 1" style="width: 8px; height: 12px" :src="require('@/assets/img/gender-male.png')"></el-image>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="8" class="color_999 over-flow martp_5" v-for="item in customerDetail.ServicerEmployee" :key="item.ID">
                  {{ item.Name }}：
                  <el-tooltip class="item" effect="light" placement="top" :content="getServicerEmpNames(item.ServicerEmpList)">
                    <span class="color_333">
                      {{ getServicerEmpNames(item.ServicerEmpList) }}
                    </span>
                  </el-tooltip>
                </el-col>
              </el-row>
            </el-col>
          </el-row>
        </div>
        <el-tabs v-model="activeName" @tab-click="handleClick">
          <el-tab-pane label="服务人员" name="first">
            <el-radio-group style="margin-top: 10px" v-model="servicerRuleForm.Status">
              <el-radio :label="10">不替换不追加</el-radio>
              <el-radio :label="20">替换服务人员</el-radio>
              <el-radio :label="30">追加服务人员</el-radio>
            </el-radio-group>
            <div class="martp_10 border_top border_right">
              <el-scrollbar class="custom-scrollbar-class">
                <div class="dis_flex">
                  <div class="border_bottom border_left" style="width: 210px" v-for="item in servicerData" :key="item.ServicerID">
                    <div class="dis_flex flex_dir_column border_bottom pad_5">
                      <strong class="color_333 font_12">{{ item.ServicerName }}</strong>
                      <el-input v-model="item.servicerKey" placeholder="请输入员工编号、姓名" prefix-icon="el-icon-search" size="mini" class="martp_5" clearable> </el-input>
                    </div>
                    <el-scrollbar style="height: 240px; padding-bottom: 30px" class="serviceTypeClass">
                      <div style="width: 210px; cursor: pointer">
                        <div
                          @click="selectServiceClick(emp.EmployeeID, item.ServicerID)"
                          v-for="emp in item.Detail.filter((val) => !item.servicerKey || val.EmployeeID.toLowerCase().includes(item.servicerKey.toLowerCase()) || val.EmployeeName.toLowerCase().includes(item.servicerKey.toLowerCase()))"
                          :key="'emp' + emp.EmployeeID"
                          class="empItem border pad_5 font_13 position_relative border_box"
                          :class="setSelectServiceClass(item, emp) && IsServicer ? 'selectServiceEmp' : ''"
                        >
                          <div class="overflow_hidden">
                            <strong class="color_333">{{ emp.EmployeeName }}</strong>
                            <!-- <span class="font_12 color_orange marlt_10" v-if="emp.IsBelong">所属</span> -->
                          </div>
                          <div v-if="emp.IsBelong" class="color_main position_absolute" style="top: 5px; right: 5px">所属</div>
                          <div class="overflow_hidden martp_5">
                            <span class="color_666"> [{{ emp.JobName }}] </span>
                          </div>
                          <el-image v-if="setSelectServiceClass(item, emp) && IsServicer" :src="require('@/assets/img/select-servicer.png')" style="height: 20px; width: 20px; bottom: 0; right: 0; position: absolute"></el-image>
                        </div>
                      </div>
                    </el-scrollbar>
                  </div>
                </div>
              </el-scrollbar>
            </div>
            <el-form size="small" style="margin-top: 15px" :model="servicerRuleForm" :rules="servicerRules" ref="servicerRuleForm" label-width="85px">
              <el-form-item label="跟进日期" prop="PlannedOn">
                <el-date-picker type="datetime" :default-time="nextDateTime" placeholder="请选择跟进日期" v-model="servicerRuleForm.PlannedOn" value-format="yyyy-MM-dd HH:mm"> </el-date-picker>
              </el-form-item>
              <el-form-item label="指派备注">
                <el-input type="textarea" :rows="3" v-model="servicerRuleForm.PlannedRemark" placeholder="请输入备注内容"></el-input>
              </el-form-item>
            </el-form>
          </el-tab-pane>
          <el-tab-pane label="其他人员" name="second">
            <el-form size="small" style="margin-top: 10px" :model="searchRuleForm" :rules="searchRules" ref="searchRuleForm" label-width="85px">
              <el-form-item label="跟进人员">
                <el-select :popper-append-to-body="false" popper-class="custom-el-select" v-model="searchValue" @change="searchChange" filterable remote :remote-method="searchEmpRemote" placeholder="请选择其他人员">
                  <el-option v-for="item in searchData" :key="item.ID" :label="item.Name" :value="item.ID">
                    <div class="dis_flex flex_dir_column pad_5_0">
                      <div style="line-height: 25px">
                        <span style="float: left">{{ item.Name }}</span>
                        <span style="float: right; color: #8492a6; font-size: 13px">{{ item.ID }}</span>
                      </div>
                      <div style="line-height: 20px; color: #8492a6">
                        <span style="float: left">{{ item.JobName }}</span>
                        <span style="float: right; font-size: 13px" class="marlt_5">{{ item.JobName }}</span>
                      </div>
                    </div>
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="跟进日期" prop="PlannedOn">
                <el-date-picker type="datetime" :default-time="nextDateTime" placeholder="请选择跟进日期" v-model="searchRuleForm.PlannedOn" format="yyyy-MM-dd HH:mm:ss"> </el-date-picker>
              </el-form-item>
              <el-form-item label="指派备注">
                <el-input type="textarea" :rows="3" placeholder="请输入备注内容" v-model="searchRuleForm.PlannedRemark"></el-input>
              </el-form-item>
            </el-form>
          </el-tab-pane>
        </el-tabs>
      </el-scrollbar>
      <span slot="footer" class="dialog-footer">
        <el-button @click="assignDialogVisible = false" v-prevent-click size="small">取 消</el-button>
        <el-button type="primary" size="small" :loading="modalLoading" v-prevent-click @click="submitAssign">确认指派</el-button>
      </span>
    </el-dialog>

    <!-- 回访弹出层 -->
    <el-dialog title="修改回访任务" :visible.sync="callBackdialogVisible" width="800px" custom-class="custom-dialog" @close="closeCallBackDialog" append-to-body>
      <el-scrollbar class="el_scrollbar_height_callback">
        <div class="information">
          <el-row type="flex" align="">
            <el-col :span="2">
              <el-avatar :size="50" :src="circleUrl"></el-avatar>
            </el-col>
            <el-col :span="22">
              <el-row type="flex" justify="space-between">
                <el-col :span="24">
                  <strong class="marrt_5 font_18">{{ customerDetail.Name }}</strong>
                  <el-image v-if="customerDetail.Gender == 2" style="width: 8px; height: 12px" :src="require('@/assets//img//gender-female.png')"></el-image>
                  <el-image v-if="customerDetail.Gender == 1" style="width: 8px; height: 12px" :src="require('@/assets/img/gender-male.png')"></el-image>
                </el-col>
              </el-row>
              <el-col justify="space-between">
                <el-col :span="8" class="color_999 martp_10"
                  >手机号：<span class="color_333">{{ customerDetail.PhoneNumber }}</span></el-col
                >
                <el-col :span="8" class="color_999 martp_10"
                  >客户编号：<span class="color_333">{{ customerDetail.Code }}</span></el-col
                >
                <el-col :span="8" class="color_999 martp_10"
                  >注册时间：<span class="color_333">{{ customerDetail.CreatedOn }}</span></el-col
                >
                <el-col :span="8" class="color_999 martp_10"
                  >介绍人：<span class="color_333">{{ customerDetail.IntroducerName }}</span></el-col
                >
                <el-col :span="8" class="color_999 martp_10"
                  >渠道来源：<span class="color_333">{{ customerDetail.ChannelName }}</span></el-col
                >
                <el-col :span="8" class="color_999 martp_10"
                  >信息来源：<span class="color_333">{{ customerDetail.CustomerSourceName }}</span></el-col
                >
              </el-col>
            </el-col>
          </el-row>
        </div>
        <el-form :model="callBackRuleForm" :rules="callBackRules" ref="callBackRuleForm" label-width="110px" size="small">
          <el-form-item label="回访方式" prop="CallbackMethodID">
            <el-radio-group v-model="callBackRuleForm.CallbackMethodID">
              <div style="padding-top: 8px">
                <el-radio style="padding-bottom: 8px" v-for="item in callbackMethod" :key="item.ID" :label="item.ID">{{ item.Name }}</el-radio>
              </div>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="回访状态" prop="CallbackStatusID">
            <el-radio-group v-model="callBackRuleForm.CallbackStatusID">
              <div style="padding-top: 8px">
                <el-radio style="padding-bottom: 8px" v-for="item in callBackStatus" :key="item.ID" :label="item.ID">{{ item.Name }}</el-radio>
              </div>
            </el-radio-group>
          </el-form-item>
          <div style="border-top: 1px solid #cfcfcf" v-for="(item, index) in callBackRuleForm.Content" :key="item.CallbackRecordProjectID">
            <el-row style="margin-left: 42px; padding: 5px 0">
              <el-col :span="24" class="martp_5">
                <el-col :span="3" class="color_999">回访天数：</el-col>
                <el-col :span="21">{{ item.CallbackCycle }}天</el-col>
              </el-col>
              <el-col :span="24" class="martp_5">
                <el-col :span="3" class="color_999">回访项目：</el-col>
                <el-col :span="21">
                  <el-tag size="mini">{{ item.ProjectName }}</el-tag></el-col
                >
              </el-col>
              <el-col :span="24" class="martp_5">
                <el-col :span="3" class="color_999">回访内容：</el-col>
                <el-col :span="21"> {{ item.CallbackRemark }}</el-col>
              </el-col>
            </el-row>
            <el-form-item label="回访记录" :prop="'Content.' + index + '.CallbackContent'" :rules="callBackRules.ContentInput">
              <el-input rows="4" type="textarea" v-model="item.CallbackContent"></el-input>
            </el-form-item>
            <el-form-item label="">
              <el-upload :limit="9" class="avatar-uploader" list-type="picture-card" action="#" :file-list="item.Attachment" :before-upload="(file) => callBackcommodityMainbeforeUpload(file, index)" :on-remove="(file) => callBackcommodityMainRemove(file, index)" accept="image/*" multiple>
                <i class="el-icon-plus avatar-uploader-icon"></i>
                <div slot="file" slot-scope="{ file }" style="height: 100px; width: 100px">
                  <el-image :id="file.uid" :src="file.AttachmentURL" :preview-src-list="item.Attachment.map((val) => val.AttachmentURL)" :z-index="2000" fit="cover" style="height: 100px; width: 100px"></el-image>
                  <span class="el-upload-list__item-actions">
                    <span class="el-upload-list__item-preview" @click="callBackDialogPreview(file, index)">
                      <i class="el-icon-zoom-in"></i>
                    </span>
                    <span class="el-upload-list__item-preview" @click="callBackcommodityMainRemove(file, index)">
                      <i class="el-icon-delete"></i>
                    </span>
                  </span>
                </div>
              </el-upload>
            </el-form-item>
          </div>
        </el-form>
      </el-scrollbar>
      <span slot="footer" class="dialog-footer">
        <el-button @click="callBackdialogVisible = false" v-prevent-click size="small">取 消</el-button>
        <el-button :loading="modalLoading" type="primary" v-prevent-click size="small" @click="submitCallBack">保 存</el-button>
      </span>
    </el-dialog>

    <!-- 更新接诊 -->
    <el-dialog title="修改接诊" :visible.sync="updateDiagnosisVisible" width="980px" :close-on-click-modal="false" append-to-body>
      <el-scrollbar class="el_scrollbar_height_followup">
        <!-- 顾客信息 -->
        <!-- <el-row type="flex" align="" class="pad_10 radius5" style="background-color: #f4f6fe" :style="ruleForm.Guidance ? 'border-bottom: 1px solid #E4E4E4;' : ''">
          <el-col :span="2">
            <!==  ==>
            <el-avatar :size="50" :src="ruleForm.Avatar ? ruleForm.Avatar : 'https://cube.elemecdn.com/3/7c/********************************.png'"></el-avatar>
          </el-col>
          <el-col :span="22">
            <el-row type="flex" justify="space-between">
              <el-col :span="24">
                <strong class="marrt_5 font_18">{{ ruleForm.CustomerName }}</strong>
                <el-image v-if="ruleForm.Gender == 1" style="width: 8px; height: 12px" :src="require('@/assets/img/gender-male.png')"></el-image>
                <el-image v-if="ruleForm.Gender == 2" style="width: 8px; height: 12px" :src="require('@/assets/img//gender-female.png')"></el-image>
              </el-col>
            </el-row>
            <el-col justify="space-between">
              <el-col :span="8" class="color_999 martp_10"
                >手机号：<span class="color_333">{{ ruleForm.PhoneNumber | hidephone }}</span></el-col
              >
              <el-col :span="8" class="color_999 martp_10"
                >会员编号：<span class="color_333">{{ ruleForm.Code }}</span></el-col
              >
              <el-col :span="8" class="color_999 martp_10"
                >注册时间：<span class="color_333">{{ ruleForm.CustomerCreatedOn }}</span></el-col
              >
              <el-col :span="8" class="color_999 martp_10"
                >介绍人：<span class="color_333">{{ ruleForm.CustomerName }}</span></el-col
              >
              <el-col :span="8" class="color_999 martp_10"
                >渠道来源：<span class="color_333">{{ ruleForm.CustomerName }}</span></el-col
              >
              <el-col :span="8" class="color_999 martp_10"
                >信息来源：<span class="color_333">{{ ruleForm.CustomerSourceName }}</span></el-col
              >
            </el-col>
          </el-col>
        </el-row> -->
        <!-- 导诊备注 -->
        <!-- <el-row v-if="ruleForm.Guidance" class="pad_10 radius5" style="background-color: #f4f6fe">
          <el-col :span="2">导诊备注:</el-col>
          <el-col :span="22">{{ ruleForm.Guidance }}</el-col>
        </el-row> -->
        <el-form :model="updateConsultItem" size="small" class="martp_15" ref="diagnosisForm" label-width="110px">
          <el-form-item
            label="咨询记录"
            prop="Content"
            :rules="[
              {
                required: true,
                message: '请输入咨询记录',
                trigger: ['blur', 'change'],
              },
            ]"
          >
            <el-input v-model="updateConsultItem.Content" type="textarea" :rows="5"></el-input>
          </el-form-item>
          <el-form-item>
            <el-upload :limit="9" class="avatar-uploader" list-type="picture-card" action="#" accept="image/*" :file-list="updateConsultItem.Attachment" :before-upload="consultBeforeUpload" multiple>
              <i class="el-icon-plus avatar-uploader-icon"></i>
              <div slot="file" slot-scope="{ file }" style="height: 100px; widht: 100px">
                <el-image style="width: 100%" :id="file.uid + '-consult'" :src="file.AttachmentURL" :preview-src-list="previewImgSrcs" :z-index="2000" fit="cover"></el-image>
                <span class="el-upload-list__item-actions">
                  <span class="el-upload-list__item-preview" @click="checkPreview(file)">
                    <i class="el-icon-zoom-in"></i>
                  </span>
                  <span class="el-upload-list__item-preview" @click="removeConsultImage(file)">
                    <i class="el-icon-delete"></i>
                  </span>
                </span>
              </div>
            </el-upload>
          </el-form-item>
          <el-form-item label="咨询时间：" prop="PlannedOn"> {{ updateConsultItem.FollowUpOn }}</el-form-item>
          <el-form-item label="备注：">{{ updateConsultItem.Remark }} </el-form-item>
        </el-form>
      </el-scrollbar>
      <div slot="footer">
        <el-button @click="updateDiagnosisVisible = false" size="small" v-prevent-click>取 消</el-button>
        <el-button type="primary" @click="saveDiagnosisClick" :loading="saveDiagnosisLoading" size="small" v-prevent-click>保存</el-button>
      </div>
    </el-dialog>

    <el-image-viewer v-if="showViewer" :initialIndex="initialIndex" :on-close="closeViewer" :url-list="previewImageList" :z-index="2100"/>
  </div>
</template>

<script>
import APIFollowUp from "@/api/KHS/Setting/followUpConfig.js";
import APIConfig from "@/api/iBeauty/Appointment/appointmentConfig";
import cusAPI from "@/api/CRM/Customer/customer";
import FollowUpAPI from "@/api/iBeauty/Workbench/followUp";
import APICallback from "@/api/KHS/Setting/callbackConfig";
import CallbackAPI from "@/api/iBeauty/Workbench/callback";
import APIUpload from "@/api/Common/uploadAttachment.js";
import utils from "@/components/js/utils.js";
import ElImageViewer from "element-ui/packages/image/src/image-viewer";
import Enumerable from "linq";
import date from "@/components/js/date";
var dayjs = require("dayjs");

export default {
  name: "followUpRecord",
  props: {
    customerID: {
      require: true,
    },
    customerName: {
      type: String,
      default: '',
    },
    isCallBack: {
      type: Boolean,
    },
    // 布局类型：'default' 门店单独一行，'inline' 门店在职位后面
    layoutType: {
      type: String,
      default: 'default',
    },
    // 预约相关状态
    hasAppointment: {
      type: Boolean,
      default: false,
    },
    appointmentStatus: {
      type: [Number, String],
      default: null,
    },
    appointmentID: {
      type: String,
      default: null,
    },
    leadStatus: {
      type: [Number, String],
      default: 0,
    },
    leadID: {
      type: [String, Number],
      default: null,
    },
  },
  /**   过滤器  */
  filters: {
    formatDateStr(date, type) {
      if (!date) return "";
      // let times = date.split(" ");
      // let dateStr = times[0];
      // dateStr.substr(dateStr.indexOf("-"));

      if (type == "date") {
        // return dateStr.substr(dateStr.indexOf("-") + 1);
        return dayjs(date).format("MM-DD");
      }

      if (type == "time") {
        // return times[1];
        return dayjs(date).format("HH:mm");
      }
      return date;
    },
  },
  /**  引入的组件  */
  components: {
    ElImageViewer,},
  /**  Vue 实例的数据对象**/
  data() {
    return {
      
      typeList: ["10", "50", "20", "30"],
      previewImgSrcs: [],
      saveDiagnosisLoading: false,
      updateDiagnosisVisible: false,
      callBackdialogVisible: false,

      modalLoading: false,
      isExpandTimeline: true,
      isShowDetails: true,
      dialogVisible: false, //新增、修改弹出框
      assignDialogVisible: false, // 指派弹出框
      isAdd: false, //新增、修改事件判断
      tableDataMethod: [], // 跟进方式
      tableDataStatus: [], // 跟进状态
      followUpEntityData: [], // 跟进部门
      customerDetail: {}, // 顾客信息
      preview_src_list: [],
      callbackMethod: [], // 回访方式
      callBackStatus: [], // 回访状态
      AssignName: "", // 指派人
      EmployeeName: "",
      PlannedRemark: "", // 任务备注
      circleUrl: "https://cube.elemecdn.com/3/7c/********************************.png", //默认头像
      ruleForm: {
        FollowUpMethodID: "", // 跟进方式
        FollowUpStatusID: "", // 跟进状态
        FollowUpContent: "", // 跟进记录
        PlannedOn: "", // 计划跟进时间
        IsNextFollowUp: true, // 下次是否跟进
        PlannedRemark: "", // 计划跟进备注
        Attachment: [],
      },
      RecordID: "",
      CallBack: "",
      rules: {
        FollowUpMethodID: [{ required: true, message: "请选择跟进方式", trigger: "change" }],
        FollowUpStatusID: [{ required: true, message: "请选择跟进状态", trigger: "change" }],
        FollowUpContent: [{ required: true, message: "请填写跟进记录", trigger: "blur" }],
      },
      callBackRuleForm: {
        CallbackMethodID: "", // 回访方式
        CallbackStatusID: "", // 回访状态
        Content: [],
      },
      callBackRules: {
        CallbackMethodID: [{ required: true, message: "请选择回访方式", trigger: "change" }],
        CallbackStatusID: [{ required: true, message: "请选择回访状态", trigger: "change" }],
        ContentInput: [{ required: true, trigger: "blur", validator: this.ContentValidate }],
      },
      activities: [],
      nextDateTime: "",
      IsServicer: true,
      activeName: "first", // 跟进指派
      servicerKey: "", // 指派-服务人员搜索
      searchValue: "", // 指派-其他人员搜索
      servicerData: [], // 指派-服务人员
      searchData: [], // 指派-其他人员
      servicerRuleForm: {
        FollowUpBy: "", //跟进人
        Status: 10, //10 ：不替换不追加   20 ：替换  30：追加
        ServicerID: "", //服务人员ID
        PlannedOn: "",
        PlannedRemark: "",
      }, // 指派-服务人员


      servicerRules: {
        PlannedOn: [{ required: true, message: "请填写跟进时间", trigger: "change" }],
      },
      searchRuleForm: {
        PlannedOn: "",
        PlannedRemark: "",
      }, // 指派-其他人员
      searchRules: {
        PlannedOn: [{ required: true, message: "请填写跟进时间", trigger: "change" }],
      },

      cascaderProps: {
        checkStrictly: true,
        label: "Name",
        value: "ID",
        children: "Detail",
      },
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() < new Date() - 3600 * 1000 * 24;
        },
      },
      employeeID: "", // 当前登录员工ID

      updateConsultItem: {
        Attachment: [],
      },
      rawDataActivities:[],
      showViewer:false,
      initialIndex:0 ,
      previewImageList:[],
    };
  },
  /**计算属性  */
  computed: {},
  /**  方法集合  */
  methods: {  
    /**    */
    closeViewer(){
      let that = this;
      that.showViewer = false;
    },
    /**    */
    handlePreviewImage(urls,url){
      let that = this;
      that.showViewer = true;
      that.initialIndex = urls.findIndex((i) => i.AttachmentURL == url);
      that.previewImageList = urls.map(i=>i.AttachmentURL);
    
    },
    /**  修改删选条件  */
    changeTypeListClick(val) {
      let that = this;
      let rawTemp = JSON.parse(JSON.stringify(that.rawDataActivities));
      that.activities = rawTemp.filter((cur) => {
        if (cur.Child) {
          let temp = cur.Child.filter((cCur) => {
            let tempLog = cCur.Log.filter((i) => {
              return val.some((j) => {
                return j == i.Type;
              });
            });
            cCur.Log = tempLog;
            return tempLog && tempLog.length > 0;
          });
          cur.Child = temp;
          return temp && temp.length > 0;
        }
      });
    },
    /**    */
    saveDiagnosisClick() {
      let that = this;
      this.$refs.diagnosisForm.validate((valid) => {
        if (valid) {
          that.diagnosis_update();
        }
      });
    },
    // /**    */
    // modifyDiagnosisClick(dataItem){
    //   let that = this;
    //   that.updateDiagnosisVisible = true;
    //   that.updateConsultItem = JSON.parse(JSON.stringify(dataItem));

    // },
    /**   保存更新 */
    saveConsultClick() {
      let that = this;
      this.$refs.diagnosisForm.validate((valid) => {
        if (valid) {
          that.consult_update();
        }
      });
    },
    /**   图片上传前 回调   */
    consultBeforeUpload(file) {
      let that = this;
      // const isSize3M = file.size / 1024 < 1000;
      // if (!isSize3M) {
      //   that.$message.error("上传图片大小不能超过 1000kb!");
      //   return false;
      // }
      utils.getImageBase64(file).then((base64) => {
        this.addAttachment(base64).then((AttachmentURL) => {
          that.$nextTick(() => {
            that.updateConsultItem.Attachment.push({
              AttachmentType: "10",
              AttachmentURL: AttachmentURL,
            });
          });
        });
      });
      return false;
    },
    /**  预览图片  */
    checkPreview(file) {
      document.getElementById(file.uid + "-consult").click();
    },
    /**  删除图片  */
    removeConsultImage(file) {
      let that = this;
      if (file && file.status !== "success") return;
      let index = that.updateConsultItem.Attachment.findIndex((item) => item.AttachmentURL == file.AttachmentURL);
      that.updateConsultItem.Attachment.splice(index, 1);
    },
    /**   修改咨询信息 */
    modifyConsultClick(dataItem) {
      let that = this;
      that.updateDiagnosisVisible = true;
      that.updateConsultItem = JSON.parse(JSON.stringify(dataItem));
    },
    ContentValidate(rule, value, callback) {
      if (value == "") {
        callback(new Error("请输入回访记录"));
      } else {
        callback();
      }
    },
    /**  添加预约  */
    addAppointmentClick() {
      // 通过事件通知父组件打开预约弹框，使用统一的预约功能
      this.$emit('openAppointment', {
        CustomerID: this.customerID,
        CustomerName: this.customerName || '',
        LeadID: null,
        action: 'create' // 新建预约
      });
    },

    // 处理预约管理下拉菜单命令
    handleAppointmentCommand(command) {
      if (command.action === 'edit') {
        // 修改预约
        this.$emit('openAppointment', {
          CustomerID: this.customerID,
          CustomerName: this.customerName || '',
          LeadID: null,
          AppointmentID: this.appointmentID,
          action: 'edit' // 修改预约
        });
      } else if (command.action === 'cancel') {
        // 取消预约
        this.$emit('cancelAppointment', {
          CustomerID: this.customerID,
          AppointmentID: this.appointmentID
        });
      }
    },

    // 处理跟进点击事件 - 使用外层统一的跟进功能
    handleFollowUpClick() {
      // 通过事件通知父组件打开跟进弹框，使用统一的跟进功能
      this.$emit('openFollowUp', {
        CustomerID: this.customerID,
        CustomerName: this.customerName || '',
        LeadID: this.leadID || null, // 线索ID，在跟进记录中可能没有
        action: 'followUp' // 跟进操作
      });
    },
    /* 年份点击事件(是否展开时间轴) */
    expandTimelineClick(year) {
      year.isYearExpand = !year.isYearExpand;
    },
    /* 月份点击事件(是否展示具体日期) */
    showDetailsClick(moth) {
      moth.isMothExpand = !moth.isMothExpand;
    },
    /* 点击修改 */
    modifyClick(activityitem) {
      let that = this;
      that.RecordID = "";
      switch (activityitem.Type) {
        case "10":
          that.RecordID = activityitem.FollowUpID;
          break;
        case "20":
          that.RecordID = activityitem.DiansosisID;
          break;
        case "30":
          that.RecordID = activityitem.CallbackID;
          break;
        case "40":
          that.RecordID = activityitem.YingXioaYunFollowUpID;
          break;
      }
      that.getCustomerDetail();
      if (activityitem.Type == 30) {
        that.callBackdialogVisible = true;
        that.detailCallBack();
      } else {
        that.dialogVisible = true;
        that.isAdd = false;
        that.PlannedRemark = "";
        that.AssignName = "";
        that.EmployeeName = "";
        that.PlannedRemark = activityitem.Remark;
        that.AssignName = activityitem.AssignName;
        that.EmployeeName = activityitem.EmployeeName;
        that.ruleForm.FollowUpMethodID = activityitem.MethodID; //跟进方式
        that.ruleForm.FollowUpStatusID = activityitem.StatusID; //跟进状态
        that.ruleForm.FollowUpContent = activityitem.Content; //内容
        that.ruleForm.Attachment = JSON.parse(JSON.stringify(activityitem.Attachment)); //图片
      }
    },
    /* 点击新建 */
    addFollowUp() {
      let that = this;
      that.dialogVisible = true;
      that.isAdd = true;
      that.PlannedRemark = "";
      that.AssignName = "";
      that.EmployeeName = "";
      this.nextDateTime = this.$formatDate(new Date(), "hh:mm:ss");
      that.ruleForm = {
        FollowUpMethodID: "", // 跟进方式
        FollowUpStatusID: "", // 跟进状态
        FollowUpContent: "", // 跟进记录
        PlannedOn: "", // 计划跟进时间
        IsNextFollowUp: true, // 下次是否跟进
        PlannedRemark: "", // 计划跟进备注
        Attachment: [],
      };
      if (this.$refs.ruleForm) {
        this.$refs["ruleForm"].resetFields();
      }
      that.dialogVisible = true;
      that.getCustomerDetail();
    },

    /**  跟进弹窗关闭  */
    closeAddFollowUpDialog() {
      this.ruleForm.Attachment = [];
    },
    /* 回访弹出层关闭 */
    closeCallBackDialog() {
      let that = this;
      that.callBackRuleForm = {
        CallbackMethodID: "", // 回访方式
        CallbackStatusID: "", // 回访状态
        Content: [],
      };
    },
    /* 点击删除 */
    deleteClick(dayItem) {
      let that = this;
      that
        .$confirm("此操作将永久删除该条数据, 是否继续?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
        .then(() => {
          let ID = dayItem.CallbackID;
          switch (dayItem.Type) {
            case "10":
              ID = dayItem.FollowUpID;
              break;
            case "20":
              ID = dayItem.DiansosisID;
              break;
            case "30":
              ID = dayItem.CallbackID;
              break;
            case "40":
              ID = dayItem.YingXioaYunFollowUpID;
              break;
          }
          if (dayItem.Type == 30) {
            let params = { CallbackRecordID: ID };
            CallbackAPI.deleteCallBack(params).then((res) => {
              if (res.StateCode == 200) {
                this.$message.success("删除成功");
                that.getCustomerFollowUp();
                that.$bus.$emit(that.$bus.RefreshCallBackList);
              } else {
                that.$message.error({
                  message: res.Message,
                  duration: 2000,
                });
              }
            });
          } else {
            let params = { ID: ID };
            FollowUpAPI.deleteFollowUp(params).then((res) => {
              if (res.StateCode == 200) {
                this.$message.success("删除成功");
                that.getCustomerFollowUp();
                that.$bus.$emit(that.$bus.RefreshFollowUpList);
              } else {
                that.$message.error({
                  message: res.Message,
                  duration: 2000,
                });
              }
            });
          }
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    /* 保存 */
    async submitFollowUp() {
      let that = this;
      if (that.isAdd) {
        await that.createFollowUp();
      } else {
        if (that.ruleForm.Attachment.length == 0) {
          that.updateFollowUp();
        } else {
          // for (let index = 0; index < that.ruleForm.Attachment.length; index++) {
          //   that.ruleForm.Attachment[index].AttachmentType = 10;
          //   that.ruleForm.Attachment[index].AttachmentURL = await utils.getCanvasBase64(that.ruleForm.Attachment[index].AttachmentURL);
          // }
          that.updateFollowUp();
        }
      }
    },
    /* 新建保存 */
    createFollowUp() {
      let that = this;
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          that.modalLoading = true;
          let params = that.ruleForm;
          params.CustomerID = that.customerID;
          params.PlannedOn = this.ruleForm.PlannedOn ? this.ruleForm.PlannedOn : "";
          FollowUpAPI.createFollowUp(params)
            .then((res) => {
              if (res.StateCode === 200) {
                that.$message.success({
                  message: "新建成功",
                  duration: 2000,
                });
                that.dialogVisible = false;
                that.getCustomerFollowUp();
                that.$bus.$emit(that.$bus.RefreshFollowUpList);
              } else {
                that.$message.error({
                  message: res.Message,
                  duration: 2000,
                });
              }
            })
            .finally(function () {
              that.modalLoading = false;
            });
        }
      });
    },
    /* 修改保存 */
    updateFollowUp() {
      let that = this;
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          that.modalLoading = true;
          let params = {
            ID: that.RecordID, //跟进记录ID
            FollowUpMethodID: that.ruleForm.FollowUpMethodID, //跟进方式
            FollowUpStatusID: that.ruleForm.FollowUpStatusID, //跟进状态
            FollowUpContent: that.ruleForm.FollowUpContent, //内容
            Attachment: that.ruleForm.Attachment, //图片
          };
          FollowUpAPI.updateFollowUp(params)
            .then((res) => {
              if (res.StateCode === 200) {
                that.$message.success({
                  message: "跟进更新成功",
                  duration: 2000,
                });
                that.dialogVisible = false;
                that.getCustomerFollowUp();
                that.$bus.$emit(that.$bus.RefreshFollowUpList);
              } else {
                that.$message.error({
                  message: res.Message,
                  duration: 2000,
                });
              }
            })
            .finally(function () {
              that.modalLoading = false;
            });
        }
      });
    },
    /* 上传图片 */
    commodityMainbeforeUpload(file) {
      let that = this;

      // const isSize200kb = file.size / 1024 < 200;
      // if (!isSize200kb) {
      //   that.$message.error("上传图片大小不能超过 200kb!");
      //   return false;
      // }
      utils.getImageBase64(file).then((base64) => {
        this.addAttachment(base64).then((AttachmentURL) => {
          that.$nextTick(() => {
            that.ruleForm.Attachment.push({
              AttachmentType: 10,
              AttachmentURL: AttachmentURL,
            });
          });
        });
      });
      return false;
    },
    /* 查看大图 */
    DialogPreview(file) {
      document.getElementById(file.uid).click();
    },
    /* 删除图片 */
    commodityMainRemove(file) {
      if (file && file.status !== "success") return;
      let that = this;
      let index = that.ruleForm.Attachment.findIndex((item) => item.AttachmentURL == file.AttachmentURL);
      that.ruleForm.Attachment.splice(index, 1);
    },
    /* 获取跟进方式列表 */
    getFollowUpMethod() {
      var that = this;
      var params = {
        Name: "",
        Active: true,
      };
      APIFollowUp.getFollowUpMethod(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.tableDataMethod = res.Data;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },
    /* 获取跟进类型列表 */
    getFollowUpStatus() {
      var that = this;
      var params = {
        Name: "",
        Active: true,
      };
      APIFollowUp.getFollowUpStatus(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.tableDataStatus = res.Data;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },
    /* 获取顾客信息 */
    getCustomerDetail() {
      const that = this;
      cusAPI.getCustomerDetail({ CustomerID: that.customerID }).then((res) => {
        if (res.StateCode == 200) {
          that.customerDetail = res.Data;
        } else {
          that.$message.error({
            message: res.Message,
            duration: 2000,
          });
        }
      });
    },
    /* 获取顾客跟进列表 */
    getCustomerFollowUp() {
      let that = this;
      let params = {
        CustomerID: that.customerID,
      };
      FollowUpAPI.getCustomerFollowUp(params).then((res) => {
        if (res.StateCode == 200) {
          res.Data.forEach((item) => {
            item.isYearExpand = true;
            item.Child.forEach((child) => {
              child.isMothExpand = true;
            });
          });
          that.activities = res.Data;
          that.rawDataActivities = res.Data && JSON.parse(JSON.stringify(res.Data));
        }
      });
    },
    /* 点击指派 */
    assignClick() {
      let that = this;
      that.assignDialogVisible = true;
      that.activeName = "first";
      this.nextDateTime = this.$formatDate(new Date(), "hh:mm");
      if (this.$refs.servicerRuleForm) {
        this.$refs["servicerRuleForm"].resetFields();
      }
      if (this.$refs.searchRuleForm) {
        this.$refs["searchRuleForm"].resetFields();
      }
      (that.servicerRuleForm = {
        FollowUpBy: "", //跟进人
        Status: 10, //10 ：不替换不追加   20 ：替换  30：追加
        ServicerID: "", //服务人员ID
        PlannedOn: this.$formatDate(new Date(), "YYYY-MM-DD hh:mm"),
        PlannedRemark: "",
      }),
        (that.searchValue = ""),
        (that.searchRuleForm = {
          PlannedOn: "",
          PlannedRemark: "",
        });
      that.getCustomerDetail();
      that.getServicer();
    },
    /* 获取指派-服务人员 */
    getServicer() {
      let that = this;
      let params = { CustomerID: that.customerID };
      FollowUpAPI.getServicer(params).then((res) => {
        if (res.StateCode == 200) {
          that.servicerData = res.Data.map((val) => {
            val.servicerKey = "";
            return val;
          });
        } else {
          that.$message.error({
            message: res.Message,
            duration: 2000,
          });
        }
      });
    },
    /**  搜索其他人员  */
    searchEmpRemote(query) {
      this.getSearch(query);
    },
    /* 搜索其他人员改变 */
    searchChange() {
      let that = this;
      that.searchValue;
      that.IsServicer = false;
    },
    /* 选中样式 */
    setSelectServiceClass(item, emp) {
      if (this.servicerRuleForm.ServicerID == item.ServicerID && this.servicerRuleForm.FollowUpBy == emp.EmployeeID) {
        return true;
      }
      return false;
    },
    /* 获取指派-其他人员 */
    getSearch(SearchKey) {
      let that = this;
      let params = {
        SearchKey: SearchKey,
      };
      FollowUpAPI.getSearch(params).then((res) => {
        if (res.StateCode == 200) {
          that.searchData = res.Data;
        } else {
          that.$message.error({
            message: res.Message,
            duration: 2000,
          });
        }
      });
    },
    /* 人员点击事件  */
    selectServiceClick(emp, item) {
      let that = this;
      that.IsServicer = true;
      that.servicerRuleForm.FollowUpBy = emp;
      that.servicerRuleForm.ServicerID = item;
      that.searchValue = null;
    },
    /* 保存指派 */
    submitAssign() {
      let that = this;
      if (that.activeName == "first") {
        if (that.servicerRuleForm.FollowUpBy) {
          this.$refs.servicerRuleForm.validate((valid) => {
            if (valid) {
              that.modalLoading = true;
              let params = {
                CustomerID: that.customerID, //顾客ID
                FollowUpBy: that.servicerRuleForm.FollowUpBy, //跟进人
                PlannedOn: that.servicerRuleForm.PlannedOn, //计划跟进时间
                PlannedRemark: that.servicerRuleForm.PlannedRemark, //计划跟进备注
                Status: that.servicerRuleForm.Status, //10 ：不替换不追加   20 ：替换  30：追加
                ServicerID: that.servicerRuleForm.ServicerID, //服务人员ID
              };
              FollowUpAPI.assignFollowUp(params)
                .then((res) => {
                  if (res.StateCode == 200) {
                    that.assignDialogVisible = false;
                    that.$message.success("确认指派成功");
                    that.$bus.$emit(that.$bus.RefreshFollowUpList);
                    that.getCustomerFollowUp();
                  } else {
                    that.$message.error({
                      message: res.Message,
                      duration: 2000,
                    });
                  }
                })
                .finally(function () {
                  that.modalLoading = false;
                });
            }
          });
        } else {
          that.$message.error({
            message: "请指派服务人员",
            duration: 2000,
          });
        }
      } else {
        if (that.searchValue) {
          this.$refs.searchRuleForm.validate((valid) => {
            if (valid) {
              that.modalLoading = true;
              let params = {
                CustomerID: that.customerID, //顾客ID
                FollowUpBy: that.searchValue, //跟进人
                PlannedOn: that.searchRuleForm.PlannedOn, //计划跟进时间
                PlannedRemark: that.searchRuleForm.PlannedRemark, //计划跟进备注
                Status: "", //10 ：不替换不追加   20 ：替换  30：追加
                ServicerID: "", //服务人员ID
              };
              FollowUpAPI.assignFollowUp(params)
                .then((res) => {
                  if (res.StateCode == 200) {
                    that.assignDialogVisible = false;
                    that.$message.success("确认指派成功");
                    that.$bus.$emit(that.$bus.RefreshFollowUpList);
                    that.getCustomerFollowUp();
                  } else {
                    that.$message.error({
                      message: res.Message,
                      duration: 2000,
                    });
                  }
                })
                .finally(function () {
                  that.modalLoading = false;
                });
            }
          });
        } else {
          that.$message.error({
            message: "请指派其他人员",
            duration: 2000,
          });
        }
      }
    },
    /* 服务人员处理  */
    getServicerEmpNames(ServicerEmpList) {
      if (!ServicerEmpList) {
        return "";
      }
      return ServicerEmpList.map((val) => (val ? val.Name : "")).join(", ");
    },
    /* tabs切换 */
    handleClick(tab) {
      let that = this;
      if (tab.paneName == "first") {
        that.servicerRuleForm.PlannedOn = this.$formatDate(new Date(), "YYYY-MM-DD hh:mm");
      } else if (tab.paneName == "second") {
        that.searchRuleForm.PlannedOn = this.$formatDate(new Date(), "YYYY-MM-DD hh:mm");
      }
    },




    /* 获取回访方式 */
    getAllCallbackMethod() {
      let that = this;
      let params = {
        Name: "",
        Active: true,
      };
      APICallback.getAllCallbackMethod(params).then((res) => {
        if (res.StateCode == 200) {
          that.callbackMethod = res.Data;
        }
      });
    },
    /* 获取回访状态 */
    getAllCallBackStatus() {
      let that = this;
      let params = {
        Name: "",
        Active: true,
      };
      APICallback.getAllCallBackStatus(params).then((res) => {
        if (res.StateCode == 200) {
          that.callBackStatus = res.Data;
        }
      });
    },
    /* 回访详情 */
    detailCallBack() {
      let that = this;
      let params = {
        CallbackRecordID: that.RecordID,
      };
      CallbackAPI.detailCallBack(params).then((res) => {
        if (res.StateCode == 200) {
          that.callBackRuleForm.Content = res.Data.Content;
          that.callBackRuleForm.CallbackMethodID = res.Data.CallbackMethodID;
          that.callBackRuleForm.CallbackStatusID = res.Data.CallbackStatusID;
        }
      });
    },
    /* 上传图片 */
    callBackcommodityMainbeforeUpload(file, index) {
      let that = this;
      that.imgIndex = index;
      // const isSize200kb = file.size / 1024 < 200;
      // if (!isSize200kb) {
      //   that.$message.error("上传图片大小不能超过 200kb!");
      //   return false;
      // }
      utils.getImageBase64(file).then((base64) => {
        this.addAttachment(base64).then((AttachmentURL) => {
          that.$nextTick(() => {
            that.callBackRuleForm.Content[index].Attachment.push({
              AttachmentType: 10,
              AttachmentURL: AttachmentURL,
            });
          });
        });
      });
      // let reader = new FileReader();
      // reader.readAsDataURL(file);
      // reader.onload = function (evt) {
      //   let base64 = evt.target.result;
      //   that.$nextTick(() => {
      //     that.callBackRuleForm.Content[index].Attachment.push({
      //       AttachmentType: 10,
      //       AttachmentURL: base64,
      //     });
      //   });
      // };
      return false;
    },

    /* 查看大图 */
    callBackDialogPreview(file, index) {
      let that = this;
      that.imgIndex = index;
      document.getElementById(file.uid).click();
    },
    /* 删除图片 */
    callBackcommodityMainRemove(file, index) {
      if (file && file.status !== "success") return;
      let that = this;
      that.imgIndex = index;
      let Index = that.callBackRuleForm.Content[index].Attachment.findIndex((item) => item.AttachmentURL == file.AttachmentURL);
      that.callBackRuleForm.Content[index].Attachment.splice(Index, 1);
    },
    /* 获取tag标签名 */
    getType(type) {
      if (type == 10) {
        return "跟进";
      }
      if (type == 20) {
        return "接诊";
      }
      if (type == 30) {
        return "回访";
      }
      if (type == 40) {
        return "营销云";
      }
    },
    /* 获取tag标签颜色 */
    getTag(type) {
      if (type == 10) {
        return "";
      }
      if (type == 20) {
        return "success";
      }
      if (type == 30) {
        return "danger";
      }
      if (type == 30) {
        return "warning";
      }
    },
    getID(dayItem, index) {
      if (dayItem.FollowUpID) {
        return index + "day-" + dayItem.FollowUpID;
      }
      if (dayItem.CallbackRecordID) {
        return index + "day-" + dayItem.CallbackRecordID;
      }
    },
    /* 回访修改保存 */
    submitCallBack() {
      let that = this;
      this.$refs.callBackRuleForm.validate(async (valid) => {
        if (valid) {
          that.modalLoading = true;
          let params = {
            CallbackRecordID: that.RecordID,
            CallbackMethodID: that.callBackRuleForm.CallbackMethodID,
            CallbackStatusID: that.callBackRuleForm.CallbackStatusID,
            Content: that.callBackRuleForm.Content,
          };
          for (let index = 0; index < params.Content.length; index++) {
            for (let ind = 0; ind < params.Content[index].Attachment.length; ind++) {
              params.Content[index].Attachment[ind].AttachmentType = 10;
            }
          }
          CallbackAPI.updateCallBack(params)
            .then((res) => {
              if (res.StateCode == 200) {
                this.$message.success({
                  message: "回访修改成功",
                  duration: 2000,
                });
                that.callBackdialogVisible = false;
                that.getCustomerFollowUp();
              } else {
                this.$message.error({
                  message: res.Message,
                  duration: 2000,
                });
              }
            })
            .finally(function () {
              that.modalLoading = false;
            });
        }
      });
    },
    /* 获取循环 */
    getLoopObject(type) {
      if (type == 10) {
        return "dayItem.FollowUpRecord";
      }
      if (type == 20) {
        return "dayItem.DiagnosisRecord";
      }
      if (type == 30) {
        return "dayItem.CallbackRecord";
      }
    },
    /** 图片上传   */
    async addAttachment(base64) {
      let that = this;
      let params = { AttachmentURL: base64 };
      let res = await APIUpload.addAttachment(params);
      if (res.StateCode == 200) {
        return res.Data.AttachmentURL;
      } else {
        that.$message.error(res.Message);
      }
    },
    /**  接诊内容修改  */
    async diagnosis_update() {
      let that = this;
      try {
        let params = {
          DiagnosisID: that.updateConsultItem.DiansosisID, //接诊编号
          DiagnosisContent: that.updateConsultItem.Content, //修改内容
          Attachment: that.updateConsultItem.Attachment.map((i) => {
            return {
              AttachmentURL: i.AttachmentURL,
              AttachmentType: i.AttachmentType,
            };
          }), //图片
        };
        let res = await FollowUpAPI.diagnosis_update(params);
        if (res.StateCode == 200) {
          that.$message.success("操作成功");
          that.updateDiagnosisVisible = false;
          that.getCustomerFollowUp();
        } else {
          that.$message.error(res.Message);
        }
      } catch (error) {
        //that.$message.error(error);
      }
    },
  },

  /** 监听数据变化   */
  watch: {
    "ruleForm.Attachment": {
      deep: true,
      immediate: true,
      handler(val) {
        this.preview_src_list = [];
        this.preview_src_list = val.map((i) => i.AttachmentURL);
      },
    },
    "updateConsultItem.Attachment": {
      immediate: true,
      handler(val) {
        if (val) {
          this.previewImgSrcs = val.map((val) => val.AttachmentURL);
        }
      },
    },
  },
  /** 创建实例之前执行函数   */
  beforeCreate() {},
  /**  实例创建完成之后  */
  created() {
    let that = this;
    that.preview_src_list = Enumerable.from(that.ruleForm.Attachment)
      .select((val) => val.AttachmentURL)
      .toArray();
  },
  /**  在挂载开始之前被调用  */
  beforeMount() {},
  /**  实例被挂载后调用  */
  mounted() {
    let that = this;
    if (localStorage.getItem("access-user")) {
      that.employeeID = JSON.parse(localStorage.getItem("access-user")).EmployeeID;
    }
    that.getAllCallbackMethod();
    that.getAllCallBackStatus();
    that.getFollowUpStatus();
    that.getFollowUpMethod();
  },
  /**  数据更新 之前 调用  */
  beforeUpdate() {},
  /** 数据更新 完成 调用   */
  updated() {},
  /**  实例销毁之前调用  */
  beforeDestroy() {},
  /**  实例销毁后调用  */
  destroyed() {},
};
</script>

<style lang="scss">
.followUpRecord {
  height: 100%;
  .custom-scrollbar_hidden-x {
    .el-scrollbar__wrap {
      overflow-x: hidden;
    }
  }
  .custom-scrollbar-class {
    .el-scrollbar__wrap {
      overflow-x: auto !important;
      height: calc(100% + 20px); //多出来的20px是横向滚动条默认的样式
      .el-scrollbar__view {
        white-space: nowrap;
        display: inline-block;
      }
    }
  }
  .el_scrollbar_height_followup {
    height: 65vh;
    .el-scrollbar__wrap {
      overflow-x: scroll !important;
    }
  }
  .border-bottom {
    border-bottom: 1px solid #cfcfcf;
  }
  .csutom-timeline-conten {
    .el-timeline-item__wrapper {
      padding-top: 48px;
      padding-left: 0px;
      .custom-moth-timeline {
        .el-timeline-item__wrapper {
          padding-top: 0px;
          padding-left: 20px;
          .custom-day-timeline {
            margin-top: 20px;
            margin-left: -20px;

            font-size: 13px !important;
            .el-timeline-item__tail {
              display: none !important;
            }
            .el-timeline-item__node--normal {
              width: 10px;
              height: 10px;
            }
            .el-card-form .el-form-item {
              margin-bottom: 5px;
            }
            .el-form-item__label {
              line-height: 18px;
            }
            .el-form-item__content {
              line-height: 18px;
            }
          }
        }
      }
    }
    .el-timeline-item__tail {
      display: block !important;
    }
  }
  .bold {
    color: #333;
    font-weight: bold;
  }
  .button-box {
    margin-bottom: 10px;
  }
  .information {
    background-color: #f7f8fa;
    padding: 8px 8px 8px 8px;
    margin-bottom: 5px;
  }
  .imgShowBox {
    width: 106px;
    height: 106px;
    line-height: 106px;
    margin-right: 10px;
    border-radius: 5px;
  }
  .el-card__header {
    padding: 14px 18px;
  }
  .el-card__body {
    padding: 5px 5px 5px 5px !important;
  }
  .el-card-content {
    border: 2px solid #ccc;
    padding: 15px 0;
  }
  .otherStaff {
    border: 1px solid #ebeef5;
    padding: 16px 12px;
  }

  .serviceTypeClass {
    .selectServiceEmp {
      border: solid 2px var(--zl-color-orange-primary);
      box-sizing: border-box;
    }
  }
  .empItem {
    margin-top: 10px;
    margin-left: 10px;
    margin-right: 10px;
  }
  .customer-detail {
    background-color: #ffffff;
    padding: 15px;
    height: 100%;
    box-sizing: border-box;
  }
  .is-always-shadow {
    background-color: #f7f8fa;
  }
  .custom-el-select {
    li {
      line-height: normal;
      height: auto;
    }
  }
  .el-upload--picture-card {
    width: 100px;
    height: 100px;
    font-size: 16px !important;
  }
  .el-upload {
    width: 100px;
    height: 100px;
    line-height: 100px;
    font-size: 16px;
  }
  .el-upload-list--picture-card .el-upload-list__item {
    width: 100px;
    height: 100px;
    line-height: 100px;
    font-size: 16px;
  }
  .over-flow {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}
</style>
