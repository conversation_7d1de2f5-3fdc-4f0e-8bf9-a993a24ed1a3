<template>
  <div class="content_body WXMiniProgram" v-loading="loading">
    <!-- <el-card shadow="never" style="border: none"> -->
    <!-- 未授权小程序信息 -->
    <div v-if="!Authorizer" class="dis_flex flex_dir_row">
      <el-card class="position_relative" shadow="hover" style="height: 260px; width: 260px"
        :body-style="card_body_style()">
        <el-image class="martp_20" style="width: 50px; height: 50px" :src="require('@/assets/img/MiniProgramlog.png')">
        </el-image>
        <p class="font_12 color_666 martp_20">已拥有小程序账号</p>
        <p class="font_12 color_666 martp_10 text_center">点击下方按钮<br />在新窗口中进行授权操作<br /></p>

        <el-button class="position_absolute" type="primary" size="small"
          style="width: 120px; margin-top: 20px; bottom: 20px" @click="bindingClick">
          立即授权
        </el-button>
      </el-card>

      <el-card class="position_relative marlt_20" shadow="hover" style="height: 260px; width: 260px"
        :body-style="card_body_style()">
        <el-image class="martp_20" style="width: 50px; height: 50px"
          :src="require('@/assets/img/MiniProgramlog_dis.png')"></el-image>
        <p class="font_12 color_666 martp_20">还没有小程序账号</p>
        <p class="font_12 color_666 martp_10 pad_0_15">
          点击下方按钮前往微信公众平台注册（目前仅支持「企业」类型的主体）；<br />注册成功后，再进行授权操作即可
        </p>
        <el-button class="position_absolute" size="small" style="width: 120px; margin-top: 20px; bottom: 20px"
          @click="miniProgramRegister">
          小程序官方注册
        </el-button>
      </el-card>
    </div>

    <!-- 已授权小程序信息 -->
    <div v-else>
      <el-form :model="payInfo" class="PayInfoClass" :rules="rules" ref="ruleForm" label-width="135px" size="mini">
        <div class="tip martp_10 color_666 font_14">小程序发布信息</div>
        <el-form-item label="开通进度：">
          <div style="background-color: #f8f8f8; width: 30%" class="pad_5_10 radius5">
            <div class="font_14 color_666">完成所有准备，提交审核并发布小程序</div>
            <div class="font_12 color_666">提交微信审核（最长14个工作日），核审通过后立即发布版本</div>
            <div :style="reviewStateStyle()" class="font_15">{{ reviewState }}</div>
            <span class="font_12 color_red" style="line-height: 15px; padding: unset" v-html="reason"></span>
          </div>
        </el-form-item>
        <el-form-item v-if="showButton">
          <el-button size="small" type="primary" @click="submitForReviewClick">{{ stateTitle() }}</el-button>
        </el-form-item>

        <div class="tip martp_10 color_666 font_14">微信支付信息</div>
        <el-form-item prop="MchId" label="微信商户号：">
          <el-input v-model="payInfo.MchId" placeholder="请输入商户号" size="small" style="width: 20%"></el-input>
          <el-link class="marlt_10 font_14" type="primary" :underline="false"
            href="https://kf.qq.com/faq/200729EZ7fEj200729aumYR7.html" target="_blank">查看指引</el-link>
        </el-form-item>

        <el-form-item prop="MchKey" label="微信商户API密钥：">
          <el-input v-model="payInfo.MchKey" placeholder="请输入商户API密钥" size="small" style="width: 20%"></el-input>

          <el-link class="marlt_10 font_14" type="primary" :underline="false"
            href="https://kf.qq.com/faq/180830UVRZR7180830Ij6ZZz.html" target="_blank">查看指引</el-link>
        </el-form-item>

        <el-form-item prop="File" label="微信商户API证书：">
          <div class="dis_flex">
            <el-upload style="width: 20%" class="upload-demo" ref="upload" action="#" accept="application/x-pkcs12"
              :before-upload="before_upload_p12" multiple>
              <div class="dis_flex">
                <div slot="tip" class="color_999 font_12" style="margin-right: 20px">请选择.p12文件上传</div>
                <el-button slot="trigger" size="small" type="text" plain>选取文件</el-button>
              </div>
              <div v-if="payInfo.File" class="text_left">
                {{ payInfo.File.name ? payInfo.File.name : "" }}
                <i class="el-icon-close marlt_10 pad_10" @click.stop="removeFileClick"></i>
              </div>
            </el-upload>
            <el-link class="marlt_10 font_14" type="primary" :underline="false"
              href="https://kf.qq.com/faq/161222NneAJf161222U7fARv.html" target="_blank">查看指引</el-link>
          </div>
        </el-form-item>

        <el-form-item prop="PayMethod" label="微商城支付方式：">
          <el-select v-model="payInfo.PayMethod" filterable placeholder="请选择" size="small" clearable>
            <el-option v-for="item in payTypeList" :key="item.ID" :label="item.Name" :value="item.ID"> </el-option>
          </el-select>
        </el-form-item>

        <el-form-item>
          <el-button size="small" type="primary" @click="savePayInfo">保 存</el-button>
        </el-form-item>
      </el-form>

      <el-form :model="miniProgramInfo" class="WeixinInfo" label-width="135px" size="mini">
        <div class="tip martp_10 color_666 font_14">小程序基本信息</div>

        <el-form-item label="小程序名称：">
          {{ miniProgramInfo.authorizerInfo.nickName }}
          <el-button class="marlt_10" size="small" type="text" @click="bindingClick">重新授权</el-button>
          <el-button class="marlt_10" size="small" type="text" @click="MiniprogramRemoveBind">解除授权</el-button>
        </el-form-item>
        <el-form-item label="小程序头像：">
          <el-image :src="miniProgramInfo.authorizerInfo.headImg" style="width: 60px; height: 60px"
            :preview-src-list="[miniProgramInfo.authorizerInfo.headImg]"></el-image>
        </el-form-item>
        <el-form-item label="介绍：">{{ miniProgramInfo.authorizerInfo.signature }}</el-form-item>
        <el-form-item label="主体信息：">{{ miniProgramInfo.authorizerInfo.principalName }}</el-form-item>
        <el-form-item label="微信认证：">{{ getWXverify_type_info(miniProgramInfo.authorizerInfo.verifyTypeInfo) }}
        </el-form-item>
        <el-form-item label="原始ID：">{{ miniProgramInfo.authorizerInfo.userName }}</el-form-item>
        <el-form-item label="APPID：">{{ miniProgramInfo.authorizationInfo.authorizerAppid }}</el-form-item>
        <el-form-item label="注册来源ID：">微信公众平台</el-form-item>
        <el-form-item label="二维码：">
          <el-image :src="getQrcodeUrl(miniProgramInfo.authorizerInfo.qrcodeUrl)" style="width: 140px; height: 140px"
            :preview-src-list="[getQrcodeUrl(miniProgramInfo.authorizerInfo.qrcodeUrl)]"></el-image>
        </el-form-item>
      </el-form>
    </div>
    <!-- </el-card> -->

    <el-dialog title="提示" :visible.sync="dialogVisible" width="30%">
      <span>请在新窗口中完成小程序账号授权</span>

      <span slot="footer" class="dialog-footer">
        <el-button @click="authorizationError">授权失败，重试</el-button>
        <el-button type="primary" @click="authorizationSucceed">已成功设置</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import API from "@/api/Marketing/App/WeiXinOpen";
import utils from "@/components/js/utils.js";

export default {
  name: "WXMiniProgram",
  /**  引入的组件  */
  components: {},
  /**  Vue 实例的数据对象**/
  data() {
    return {
      loading: false,
      dialogVisible: false,
      miniProgramInfo: null,
      Authorizer: false, //获取小程序授权成功与否
      payTypeList: [],
      payType: "",
      payInfo: {
        MchId: "",
        MchKey: "",
        File: "",
        PayMethod: "",
      },
      rules: {
        MchId: [{ required: true, message: "请输入微信商户号", trigger: "blur" }],
        MchKey: [{ required: true, message: "是输入微信商户密钥", trigger: "blur" }],
        File: [{ required: true, message: "请上传微信商户API证书", trigger: ["blur", "change"] }],
        PayMethod: [{ required: true, message: "请选择支付方式", trigger: ["blur", "change"] }],
      },
      reviewState: "提交审核",
      showButton: false,
      IsPublish: false,
      reason: "",
    };
  },
  /**  方法集合  */
  methods: {
    getQrcodeUrl(src) {
      return utils.imgProxy(src);
    },
    /**    */
    getWXverify_type_info(verify_type_info) {
      switch (verify_type_info) {
        case -1:
          return "未认证";

        case 0:
          return "微信认证";

        case 1:
          return "新浪微博认证";

        case 2:
          return "腾讯微博认证";

        case 3:
          return "已资质认证通过但还未通过名称认证";

        case 4:
          return "已资质认证通过、还未通过名称认证，但通过了新浪微博认证";

        case 5:
          return "已资质认证通过、还未通过名称认证，但通过了腾讯微博认证";

        default:
          break;
      }
    },
    /**  跳转连接   */
    // getHrefURL(){
    //   let EnterpriseCode = localStorage.getItem("EnterpriseCode");
    //   let hrefURL = API.miniprogramComponent()+'?EnterpriseCode=' + EnterpriseCode;
    //   return hrefURL
    // },
    /**  立即授权小程序  */
    bindingClick() {
      let that = this;

      that.bindingClickMiniprogramAuthorizer();
    },
    /**  授权失败  */
    authorizationError() {
      let that = this;
      that.bindingClickMiniprogramAuthorizer();
    },
    /**  授权成功  */
    authorizationSucceed() {
      let that = this;
      that.dialogVisible = false;
      that.getMiniprogramAuthorizerInfo();
      that.salePayMethodData();
      that.getMiniprogramPayMethod();
      that.miniprogram_getLatestAuditStatus();
    },

    /**  小程序注册  */
    miniProgramRegister() {
      window.open("https://mp.weixin.qq.com/");
    },

    /**    */
    card_body_style() {
      return {
        display: "flex",
        padding: "0px",
        "flex-direction": "column",
        "align-items": "center",
      };
    },

    /**  跳转连接   */
    bindingClickMiniprogramAuthorizer() {
      let that = this;
      API.miniprogramComponent().then((res) => {
        if (res.StateCode == 200) {
          let URL = res.Data.URL;
          window.open(URL);
          that.dialogVisible = true;
        } else {
          that.$message.error({
            message: "操作失败",
            duration: 2000,
          });
        }
      });
    },

    /**  解除小程序授权  */
    MiniprogramRemoveBind() {
      var that = this;
      that
        .$confirm("解除授权微信号，会造成当前店铺的重要信息丢失（包括图文素材、自动回复、自定义菜单等），请谨慎操作", "提示", {
          confirmButtonText: "解除授权",
          cancelButtonText: "取消",
          type: "warning",
        })
        .then(() => {
          API.miniprogram_cancelauth()
            .then((res) => {
              if (res.StateCode == 200) {
                that.$message.error({
                  message: "已解除授权",
                  duration: 2000,
                });
                that.miniProgramInfo = null;
                that.Authorizer = false;
              } else {
                that.$message.error({
                  message: res.Message,
                  duration: 2000,
                });
              }
            })
            .finally(function () {
              that.dialogVisible = false;
            });
        })
        .catch(() => {
          that.$message({
            type: "info",
            message: "已取消操作",
          });
        });
    },
    /**  获取授权信息  */
    getMiniprogramAuthorizerInfo() {
      var that = this;
      API.getMiniprogramAuthorizerInfo()
        .then((res) => {
          if (res.StateCode == 200) {
            that.miniProgramInfo = res.Data.wxOpenAuthorizerInfoResult;
            that.Authorizer = true;
            that.IsPublish = res.Data.IsPublish;
          } else {
            if (res.StateCode == 1100) {
              that.Authorizer = false;
            } else {
              that.$message.error({
                message: res.Message,
                duration: 2000,
              });
            }
          }
        })
        .finally(function () { });
    },
    /**   删除上传的文件 */
    removeFileClick() {
      let that = this;
      that.payInfo.File = "";
    },

    /**    */
    reviewStateStyle() {
      let that = this;
      switch (that.reviewState) {
        case "审核成功": {
          that.showButton = true;
          return "color:#018dff";
        }
        case "审核被拒绝": {
          that.showButton = true;
          return "color:#FD6B6F";
        }
        case "审核中": {
          that.showButton = false;
          return "color:#ECA24A";
        }
        case "已撤回": {
          that.showButton = true;
          return "color:#999999";
        }
        default: {
          if (that.IsPublish) {
            that.showButton = false;
          } else {
            that.showButton = true;
          }
          return "color:#999999";
        }
      }
    },

    /**  获取 发布 审核 按钮标题  */
    stateTitle() {
      let that = this;
      // if (!that.IsPublish && that.reviewState != "审核中") {
      //   that.reviewState = "提交审核";
      // }

      if (that.IsPublish) return "重新提交";
      switch (that.reviewState) {
        case "审核成功":
          return "发布";
        case "审核被拒绝":
          return "重新提交";
        case "审核中":
          return "";
        case "已撤回":
          return "重新提交";
        case "未提交审核":
          return "提交审核";
        default:
          return "";
      }
    },

    /**  上传P12 文件*/
    before_upload_p12(file) {
      let that = this;
      if (file.type != "application/x-pkcs12") {
        that.$message.error("请选择.p12文件");
      }
      // Array.from(that.payInfo).File.push(file);
      that.payInfo.File = file;
      that.$message.success("上传成功");
      // that.$refs.ruleForm.validateField("File");
      return false;
    },

    /**  保存支付信息  */
    savePayInfo() {
      let that = this;
      that.$refs.ruleForm.validate((valid) => {
        if (valid) {
          that.miniprogram_PayMethod();
        }
      });
    },

    /**  提交版本审核  */
    submitForReviewClick() {
      let that = this;
      if (that.IsPublish) {
        that.miniprogram_submitAudit();
      } else {
        switch (that.reviewState) {
          case "审核成功":
            that.miniprogram_release();
            break;
          case "审核被拒绝":
          case "提交审核":
          case "已撤回":
            that.miniprogram_submitAudit();
            break;
          case "审核中":
            break;
          default:
            that.miniprogram_submitAudit();
            break;
        }
      }
    },

    /**  提交小程序审核  */
    async miniprogram_submitAudit() {
      let that = this;
      let params = {};
      that.loading = true;
      let res = await API.miniprogram_submitAudit(params);
      if (res.StateCode == 200) {
        that.$message.success("提交成功");
        that.miniprogram_getLatestAuditStatus();
      } else {
        that.$message.error(res.Message);
      }
      that.loading = false;
    },
    /**  获取最后一次审核 状态  */
    async miniprogram_getLatestAuditStatus() {
      let that = this;
      let params = {};
      let res = await API.miniprogram_getLatestAuditStatus(params);

      if (res.StateCode == 200) {
        switch (res.Data.status) {
          case 0:
            {
              that.reviewState = "审核成功";
            }
            break;
          case 1:
            {
              that.reviewState = "审核被拒绝";
            }
            break;
          case 2:
            {
              that.reviewState = "审核中";
            }
            break;
          case 3:
            {
              that.reviewState = "已撤回";
            }
            break;
        }
        // if (!that.IsPublish && res.Data.status != 2) {
        //   that.reviewState = "提交审核"
        // }
        that.reason = res.Data.reason;
      } else if (res.StateCode == 1000) {
        that.reviewState = "未提交审核";
      } else if (res.StateCode == 1100) {
        that.$message.error(res.Message);
      }
    },

    /**  发布的接口  */
    async miniprogram_release() {
      let that = this;
      let params = {};
      that.loading = true;
      let res = await API.miniprogram_release(params);
      if (res.StateCode == 200) {
        that.$message.success("发布成功");
        that.miniprogram_getLatestAuditStatus();
        that.getMiniprogramAuthorizerInfo();
      } else {
        that.$message.error(res.Message);
      }
      that.loading = false;
    },

    /**  补充支付信息 */
    async miniprogram_PayMethod() {
      let that = this;
      let params = {
        File: that.payInfo.File,
        MchId: that.payInfo.MchId,
        MchKey: that.payInfo.MchKey,
        PayMethod: that.payInfo.PayMethod,
      };
      let res = await API.miniprogram_PayMethod(params);
      if (res.StateCode == 200) {
        that.$message.success("保存成功");
      } else {
        that.$message.error(res.Message);
      }
    },

    // 支付方式
    async salePayMethodData() {
      var that = this;
      that.loading = true;
      let params = {
        Active: true,
        Name: "",
      };
      let res = await API.getSalePayMethod(params);
      if (res.StateCode == 200) {
        that.payTypeList = res.Data;
      } else {
        that.$message.error({
          message: res.Message,
          duration: 2000,
        });
      }
      that.loading = false;
    },

    // 获取支付信息
    async getMiniprogramPayMethod() {
      var that = this;
      let res = await API.getMiniprogramPayMethod();
      if (res.StateCode == 200) {
        let temp = res.Data ? res.Data : that.payInfo;
        temp.File = "";
        that.payInfo = temp;
        if (that.payInfo.MchId && that.payInfo.MchKey && that.payInfo.PayMethod && that.payInfo.KeyPath) {
          that.showButton = true;
        }
      } else {
        that.$message.error({
          message: res.Message,
          duration: 2000,
        });
      }
    },
  },
  /**  实例被挂载后调用  */
  mounted() {
    this.getMiniprogramAuthorizerInfo();
    this.salePayMethodData();
    this.getMiniprogramPayMethod();
    this.miniprogram_getLatestAuditStatus();
  },
};
</script>

<style  lang="scss">
.WXMiniProgram {
  border: none;

  .WeixinInfo {
    .el-form-item__label {
      font-size: 13px !important;
    }

    .el-f
.WXMiniProgram {
  border: none;

  .WeixinInfo {
    .el-form-item__label {
      font-size: 13px !important;
    }

    .el-form-item__content {
      font-size: 13px !important;
    }

    .el-form-item {
      margin-bottom: 0px;
    }
  }

  .PayInfoClass {
    .el-form-item__label {
      font-size: 13px !important;
    }

    .el-form-item__content {
      font-size: 13px !important;
    }

    .el-form-item {
      margin-bottom: 22px;
    }
  }
}
