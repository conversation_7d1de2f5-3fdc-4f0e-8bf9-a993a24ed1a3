<template>
  <div class="Department content_body">
    <div class="nav_header">
      <el-row>
        <el-col :span="20">
          <el-form :inline="true" size="small" @keyup.enter.native="handleSearch">
            <el-form-item label="名称">
              <el-input v-model="name" @clear="handleSearch" placeholder="输入名称搜索" clearable></el-input>
            </el-form-item>
            <el-form-item label="有效性">
              <el-select @change="handleSearch" @clear="handleSearch" v-model="active" placeholder="请选择" clearable>
                <el-option label="有效" :value="true"></el-option>
                <el-option label="无效" :value="false"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleSearch" v-prevent-click>搜索</el-button>
            </el-form-item>
          </el-form>
        </el-col>
        <el-col :span="4" class="text_right">
          <el-button type="primary" size="small" @click="showAddDialog">新增</el-button>
        </el-col>
      </el-row>
    </div>
    <div class="paymethodlist">
      <el-table size="small" :data="tableData">
        <el-table-column prop="Name" label="名称"></el-table-column>
        <el-table-column label="移动" min-width="180px">
          <template slot-scope="scope">
            <el-button size="small" type="primary" circle icon="el-icon-upload2" @click="upOneClick(scope.row, scope.$index)" v-prevent-click :disabled="scope.$index == 0"></el-button>
            <el-button size="small" type="primary" circle icon="el-icon-top" @click="upClick(scope.row, scope.$index)" v-prevent-click :disabled="scope.$index == 0"></el-button>
            <el-button size="small" type="primary" circle icon="el-icon-bottom" @click="downClick(scope.row, scope.$index)" v-prevent-click :disabled="scope.$index == tableData.length - 1"> </el-button>
            <el-button size="small" type="primary" circle icon="el-icon-download" @click="downOneClick(scope.row, scope.$index)" v-prevent-click :disabled="scope.$index == tableData.length - 1"></el-button>
          </template>
        </el-table-column>
        <el-table-column prop="Active" label="有效性" :formatter="formatStatus"></el-table-column>
        <el-table-column label="操作" width="80">
          <template slot-scope="scope">
            <el-button type="primary" size="small" @click="editDepartementClick(scope.row)">编辑</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!--弹窗-->
    <el-dialog :title="isAdd ? '新增科室' : '编辑科室'" :visible.sync="dialogVisible" @close="close" width="700px">
      <el-tabs v-model="activeName">
        <el-tab-pane label="科室管理" name="first">
          <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="100px" size="small" @submit.native.prevent>
            <el-form-item label="科室管理" prop="Name">
              <el-input clearable v-model="ruleForm.Name"></el-input>
            </el-form-item>
            <el-form-item label="是否有效" v-if="!isAdd">
              <el-radio-group v-model="ruleForm.Active">
                <el-radio :label="true">有效</el-radio>
                <el-radio :label="false">无效</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-form>
        </el-tab-pane>
        <el-tab-pane label="适用医院" name="second">
          <div class="message el-message--info marbm_10">
            <i class="el-message__icon el-icon-info"></i>
            <p class="el-message__content">适用于同级所有节点，则只需勾选父节点。比如：适用于所有节点，只需勾选“顶级/第一个”节点。</p>
          </div>
          <el-scrollbar class="el-scrollbar_height">
            <el-tree ref="treeSale" :expand-on-click-node="false" :check-on-click-node="true" check-strictly :default-expanded-keys="defaultExpandedKeysPay" :default-checked-keys="defaultCheckedKeysPay" :data="salesScopeData" show-checkbox node-key="ID" :props="defaultProps"></el-tree>
          </el-scrollbar>
        </el-tab-pane>
      </el-tabs>

      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="close" v-prevent-click>取 消</el-button>
        <el-button type="primary" size="small" @click="addSubmit" :loading="modalLoading" v-prevent-click>保 存 </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import API from "@/api/KHS/Entity/department.js";
import EntityListAPI from "@/api/iBeauty/Goods/project";
export default {
  name: "Department",

  props: {},
  /** 监听数据变化   */
  watch: {},
  /**  引入的组件  */
  components: {},
  /**  Vue 实例的数据对象**/
  data() {
    return {
      loading: false,
      modalLoading: false,
      dialogVisible: false,
      isAdd: true,
      active: true,
      activeName: "first",
      name: "",
      tableData: [],
      entityList: [], //医院列表
      defaultExpandedKeysPay: [1],
      defaultCheckedKeysPay: [],
      defaultProps: {
        children: "Child",
        label: "EntityName",
      }, // 适用范围选择配置项
      salesScopeData: [],
      ruleForm: {
        Name: "",
        Active: true,
      },
      rules: {
        Name: [{ required: true, message: "请输入名称", trigger: "blur" }],
        Active: [{ required: true, message: "请选择有效性", trigger: "change" }],
      },
    };
  },
  /**计算属性  */
  computed: {},
  methods: {
    // //状态显示转换
    formatStatus(row) {
      return row.Active ? "有效" : "无效";
    },
    // 数据显示
    handleSearch() {
      let that = this;
      that.loading = true;
      var params = {
        Name: that.name,
        Active: that.active,
      };
      API.department_all(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.tableData = res.Data;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },

    // 移动首部
    upOneClick(row) {
      var that = this;
      that.movePaymetData(row.ID, "");
    },
    // 移动尾部
    downOneClick(row, index) {
      var that = this;
      var tabIndex = that.tableData.length;
      var beforeId = "";
      if (index < tabIndex - 1) {
        beforeId = that.tableData[tabIndex - 1].ID;
      }
      that.movePaymetData(row.ID, beforeId);
    },
    // 向上
    upClick(row, index) {
      var that = this;
      var beforeId = "";
      if (index > 1) {
        beforeId = that.tableData[index - 2].ID;
      }
      that.movePaymetData(row.ID, beforeId);
    },
    // 向下
    downClick(row, index) {
      var that = this;
      var beforeId = "";
      if (index + 1 != that.tableData.length) {
        beforeId = that.tableData[index + 1].ID;
      }
      that.movePaymetData(row.ID, beforeId);
    },
    // 移动顺序
    movePaymetData(moveId, beforeId) {
      var that = this;
      that.loading = true;
      var params = {
        MoveID: moveId,
        BeforeID: beforeId,
      };
      API.department_move(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.$message.success({
              message: "移动成功",
              duration: 2000,
            });
            that.handleSearch();
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },
    // 新增
    showAddDialog() {
      var that = this;
      that.dialogVisible = true;
      that.isAdd = true;
      that.ruleForm = {
        Name: "",
        Entity: [],
      };
      that.defaultExpandedKeysPay = [1];
      that.defaultCheckedKeysPay = [];
      Object.assign(that.salesScopeData, that.entityList);
    },
    // 编辑
    editDepartementClick(row) {
      var that = this;
      that.dialogVisible = true;
      that.isAdd = false;
      if (that.$refs.ruleForm) {
        that.$refs.ruleForm.resetFields();
      }
      that.ruleForm = Object.assign({}, row);
      Object.assign(that.salesScopeData, that.entityList);

      that.defaultExpandedKeysPay = row.Entity;
      that.defaultCheckedKeysPay = row.Entity;
    },
    //模态窗数据
    addSubmit() {
      let that = this;
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          that.modalLoading = true;
          let para = Object.assign({}, that.ruleForm);
          para.Entity = that.$refs.treeSale.getCheckedKeys();
          if (that.isAdd) {
            API.department_create(para)
              .then(function (res) {
                if (res.StateCode === 200) {
                  that.$message.success({
                    showClose: true,
                    message: "新增成功",
                    duration: 2000,
                  });
                  that.handleSearch();
                  that.$refs["ruleForm"].resetFields();
                } else {
                  that.$message.error({
                    showClose: true,
                    message: res.Message,
                    duration: 2000,
                  });
                }
              })
              .finally(function () {
                that.dialogVisible = false;
                that.modalLoading = false;
              });
          } else {
            var params = {
              ID: para.ID,
              Name: para.Name,
              Active: para.Active,
              Entity: para.Entity,
            };
            API.department_update(params)
              .then(function (res) {
                if (res.StateCode === 200) {
                  that.$message.success({
                    showClose: true,
                    message: "编辑成功",
                    duration: 2000,
                  });
                  that.$refs["ruleForm"].resetFields();
                  that.handleSearch();
                } else {
                  that.$message.error({
                    showClose: true,
                    message: res.Message,
                    duration: 2000,
                  });
                }
              })
              .finally(function () {
                that.dialogVisible = false;
                that.modalLoading = false;
              });
          }
        }
      });
    },
    close() {
      this.dialogVisible = false;
      this.activeName = "first";
      this.defaultExpandedKeysPay = [1];
      this.defaultCheckedKeysPay = [];
      this.$refs.treeSale.setCheckedKeys([]);
    },
    // 新增时获取权限范围
    getEntityList() {
      var that = this;
      var params = {
        SearchKey: "",
        Active: "",
      };
      EntityListAPI.getEntityList(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.entityList = res.Data;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {});
    },
  },
  /** 创建实例之前执行函数   */
  beforeCreate() {},
  /**  实例创建完成之后  */
  created() {},
  /**  在挂载开始之前被调用  */
  beforeMount() {},
  /**  实例被挂载后调用  */
  mounted() {
    var that = this;
    that.handleSearch();
    that.getEntityList();
  },
  /**  数据更新 之前 调用  */
  beforeUpdate() {},
  /** 数据更新 完成 调用   */
  updated() {},
  /**  实例销毁之前调用  */
  beforeDestroy() {},
  /**  实例销毁后调用  */
  destroyed() {},
};
</script>

<style lang="scss">
.Department {
  height: calc(100% - 30px);
  .paymethodlist {
    height: calc(100% - 51px);
    overflow-y: auto;
  }
  .el-scrollbar_height {
    height: 50vh;
    .el-scrollbar__wrap {
      overflow-x: hidden;
    }
  }
}
</style>
