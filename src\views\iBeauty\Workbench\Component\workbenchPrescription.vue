<template>
  <div class="workbenchPrescription" :loading="loading">
    <div class="nav_header" style="background-color: #ffffff">
      <el-row>
        <el-form :inline="true" size="small" v-model="searchForm">
          <el-col :span="20">
            <el-form-item label="单据号">
              <el-input v-model="searchForm.ID" size="small" @clear="handleSearch" clearable placeholder="输入处方单据号"></el-input>
            </el-form-item>
            <el-form-item label="处方状态">
              <el-select v-model="searchForm.BillStatus" @change="handleSearch" clearable>
                <el-option label="新处方" value="NEW"></el-option>
                <el-option label="未收费" value="PAY"></el-option>
                <el-option label="取消" value="CAN"></el-option>
                <el-option label="收款未发药" value="YES"></el-option>
                <el-option label="已发药" value="OUT"></el-option>
                <el-option label="作废" value="RTD"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button size="small" type="primary" @click="handleSearch" v-prevent-click>搜索</el-button>
            </el-form-item>
          </el-col>
          <el-col :span="4" class="text_right">
            <el-form-item>
              <el-button v-if="CustomerCases.PrescriptionAdd" size="small" type="primary" @click="addPrescriptionClick" v-prevent-click>新增</el-button>
            </el-form-item>
          </el-col>
        </el-form>
      </el-row>
    </div>
    <el-table :data="prescriptionBill_list" size="small">
      <el-table-column label="操作" width="80" fixed>
        <template slot-scope="scope">
          <el-button @click="editPrescriptClick(scope.row)" v-prevent-click size="small" type="primary">查看</el-button>
        </template>
      </el-table-column>
      <el-table-column label="处方单据号" prop="ID" width="150"></el-table-column>
      <el-table-column label="处方状态" prop="BillStatus" :formatter="tableFormatterValue"></el-table-column>
      <el-table-column label="医院名称" prop="EntityName" width="150"></el-table-column>
      <el-table-column label="仓库名称" prop="WarehouseName" width="150"></el-table-column>
      <el-table-column label="顾客名称" prop="CustomerName"></el-table-column>
      <el-table-column label="医生名称" prop="EmployeeName"></el-table-column>
      <el-table-column label="费别" prop="CostCategory" :formatter="tableFormatterValue"></el-table-column>
      <el-table-column label="类型" prop="PrescriptionType" :formatter="tableFormatterValue"></el-table-column>
      <el-table-column label="诊断" prop="Diagnostic" width="200" show-overflow-tooltip></el-table-column>
      <el-table-column label="医嘱" prop="MedicalAdvice" width="200" show-overflow-tooltip></el-table-column>
      <el-table-column label="单据时间" prop="BillDate" width="150">
        <template slot-scope="scope">
          {{ scope.row.BillDate | dateFormat("YYYY-MM-DD") }}
        </template>
      </el-table-column>
      <el-table-column label="关联的销售订单" prop="SaleBillID" width="150"></el-table-column>
      <el-table-column label="关联的消耗订单" prop="TreatBillID" width="150"></el-table-column>
    </el-table>
    <div class="pad_10 text_right">
      <el-pagination background v-if="paginations.total > 0" @current-change="handleCurrentChange" :current-page.sync="paginations.page" :page-size="paginations.page_size" :layout="paginations.layout" :total="paginations.total"></el-pagination>
    </div>
    <el-dialog :title="isAdd ? '新增处方' : '编辑处方'" :visible.sync="showAddPrescriptionVisible" append-to-body width="1200px" class="workbench-addPrescription-dialog">
      <el-form :model="addRuleForm" :rules="rules" size="small" label-width="100px" ref="addRuleFormRef">
        <el-row>
          <el-col :span="8">
            <el-form-item label="科室" prop="DepartmentID">
              <el-select v-model="addRuleForm.DepartmentID" filterable :disabled="disabledEdit()">
                <el-option v-for="item in department_list" :key="item.ID" :label="item.Name" :value="item.ID"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="医生" prop="EmployeeID">
              <el-select v-model="addRuleForm.EmployeeID" filterable :disabled="disabledEdit()">
                <el-option v-for="item in doctor_list" :key="item.ID" :label="item.Name" :value="item.ID"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="费别" prop="CostCategory">
              <el-select v-model="addRuleForm.CostCategory" filterable :disabled="disabledEdit()">
                <el-option label="医保" value="YB"></el-option>
                <el-option label="非医保" value="FYB"></el-option>
                <el-option label="自费" value="ZF"></el-option>
                <el-option label="住院" value="STA"></el-option>
                <el-option label="门诊" value="MEN"></el-option>
                <el-option label="手术室" value="DOC"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="病历" prop="CustomerMedicalRecordID">
              <el-autocomplete
                v-model="customerMedicalRecordName"
                :fetch-suggestions="customerMedicalRecord_search"
                filterable
                :disabled="disabledEdit()"
                @select="handleMedicalRecordSelect"
                :hide-loading="true"
                :trigger-on-focus="true"
                highlight-first-item
                placeholder="请输入病历名称搜索"
                style="width: 100%"
              >
                <template slot="append">
                  <el-button icon="el-icon-delete" @click="removeMedicalRecord" :disabled="!isAdd"></el-button>
                </template>
                <template slot-scope="{ item }">
                  <div class="name">
                    {{ item.Name }}
                    <el-tag size="mini" v-if="item.CustomerLevelName">{{ item.CustomerLevelName }} </el-tag>
                  </div>
                  <div class="info">手机号：{{ item.PhoneNumber | hidephone }}</div>
                  <span class="info" v-if="item.Code">客户编号：{{ item.Code }}</span>
                  <span class="info" v-if="item.EntityName">所属组织：{{ item.EntityName }}</span>
                  <div class="info" v-if="item.ChannelDeveloperList && item.ChannelDeveloperList.length > 0">
                    开发人员：
                    <el-tooltip placement="top">
                      <div slot="content">{{ getChannelNames(item.ChannelDeveloperList) }}</div>
                      <span>{{ getChannelNames(item.ChannelDeveloperList) }}</span>
                    </el-tooltip>
                  </div>
                  <div class="info" v-if="item.ChannelConsultantList && item.ChannelConsultantList.length > 0">
                    市场咨询：
                    <el-tooltip placement="top">
                      <div slot="content">{{ getChannelNames(item.ChannelConsultantList) }}</div>
                      <span>{{ getChannelNames(item.ChannelConsultantList) }}</span>
                    </el-tooltip>
                  </div>
                </template>
              </el-autocomplete>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="类型" prop="PrescriptionType">
              <el-select v-model="addRuleForm.PrescriptionType" filterable :disabled="disabledEdit()">
                <el-option label="麻醉" value="A"></el-option>
                <el-option label="精一" value="B"></el-option>
                <el-option label="精二" value="C"></el-option>
                <el-option label="普通" value="D"></el-option>
                <el-option label="毒" value="E"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="营业日期" prop="BillDate">
              <el-date-picker v-model="addRuleForm.BillDate" type="date" placeholder="治疗日期" value-format="yyyy-MM-dd" :disabled="disabledEdit()"> </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="仓库" prop="WarehouseID">
              <el-select v-model="addRuleForm.WarehouseID" filterable :disabled="disabledEdit()">
                <el-option v-for="item in warehouseByEntity" :key="item.ID" :label="item.Name" :value="item.ID"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="诊断" prop="Diagnostic">
              <el-input type="textarea" v-model="addRuleForm.Diagnostic" :disabled="disabledEdit()"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="医嘱" prop="MedicalAdvice">
              <el-input type="textarea" v-model="addRuleForm.MedicalAdvice" :disabled="disabledEdit()"></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item v-if="!disabledEdit()">
          <el-button @click="addPrescriptionWarehouseClick" v-prevent-click type="primary" size="small">新增药品</el-button>
        </el-form-item>

        <el-table :data="addRuleForm.Detail" size="mini" height="400">
          <el-table-column label="名称" prop="ProductName" width="180">
            <template slot-scope="scope">
              <div>{{ scope.row.ProductName }}</div>
              <div class="font_12 color_999">{{ scope.row.Alias }}</div>
            </template>
          </el-table-column>
          <el-table-column label="单剂量" prop="OneDose" width="80px">
            <template slot-scope="scope">
              <el-input v-model="scope.row.OneDose" @change="changeDoseInput(scope.row, 'OneDose')" type="number" size="mini" v-input-fixed="0" class="table_custom_input" :min="1" :disabled="disabledEdit()"></el-input>
            </template>
          </el-table-column>
          <el-table-column label="每日次数" prop="DailyCount">
            <template slot-scope="scope">
              <el-input v-model="scope.row.DailyCount" @change="changeDoseInput(scope.row, 'DailyCount')" size="mini" v-input-fixed="0" class="table_custom_input" type="number" :min="1" :disabled="disabledEdit()"></el-input>
            </template>
          </el-table-column>
          <el-table-column label="开药天数" prop="PrescribingNumber">
            <template slot-scope="scope">
              <el-input v-model="scope.row.PrescribingNumber" @change="changeDoseInput(scope.row, 'PrescribingNumber')" size="mini" v-input-fixed="0" class="table_custom_input" type="number" :min="1" :disabled="disabledEdit()"></el-input>
            </template>
          </el-table-column>
          <el-table-column label="数量" prop="Quantity">
            <template slot-scope="scope">
              <el-input v-model="scope.row.Quantity" @change="changeQuantity(scope.row)" size="mini" v-input-fixed="0" type="number" class="table_custom_input" :min="scope.row.TotalDose" :disabled="disabledEdit()"></el-input>
            </template>
          </el-table-column>
          <el-table-column label="总计量" prop="TotalDose" width="80">
            <template slot-scope="scope">
              <span>{{ scope.row.TotalDose }}</span>
              <span class="marlt_5">{{ scope.row.UnitName }}</span>
            </template>
          </el-table-column>
          <el-table-column label="价格" prop="Price"></el-table-column>
          <el-table-column label="折扣" prop="Discount" width="100">
            <template slot-scope="scope">
              <el-input v-model="scope.row.Discount" @change="changeDiscount(scope.row)" size="mini" v-input-fixed="0" class="table_custom_input" :disabled="disabledEdit()">
                <template slot="append">%</template>
              </el-input>
            </template>
          </el-table-column>
          <el-table-column label="应付金额" prop="TotalAmount"> </el-table-column>
          <el-table-column label="规格" prop="Specification"></el-table-column>

          <el-table-column label="批次/有效期" prop="StockBatchID" width="150">
            <template slot-scope="scope">
              <el-form-item v-if="scope.row.IsBatchManagement" :show-message="false" :prop="'Detail.' + scope.$index + '.StockBatchID'" :rules="rules.StockBatchID" class="workench-table_from_item_class" label-width="0">
                <el-select value-key="ID" v-model="scope.row.StockBatchID" size="mini" filterable placeholder="请选择批次" :default-first-option="true" popper-class="batch-popper-class" :disabled="disabledEdit()">
                  <!-- @change="(val) => changeBathcNumber(val, scope.row)" -->
                  <el-option v-for="item in scope.row.Batch" :key="item.ID" :label="item.label" :value="item.ID">
                    <div>{{ item.BatchNumber }}</div>
                    <div class="color_999 font_12 selected">{{ item.ValidDate }}</div>
                  </el-option>
                </el-select>
              </el-form-item>
            </template>
          </el-table-column>

          <el-table-column label="单次剂量单位" prop="DoseUnit" width="150px">
            <template slot-scope="scope">
              <el-form-item :show-message="false" :prop="'Detail.' + scope.$index + '.DoseUnit'" :rules="rules.DoseUnit" class="workench-table_from_item_class" label-width="0">
                <el-select v-model="scope.row.DoseUnit" size="mini" :disabled="disabledEdit()">
                  <el-option v-for="item in OneDoses" :key="item.ID" :label="item.Name" :value="item.ID"></el-option>
                </el-select>
              </el-form-item>
            </template>
          </el-table-column>
          <el-table-column label="单次用量" prop="OnceDose" width="100">
            <template slot-scope="scope">
              <el-form-item :show-message="false" :prop="'Detail.' + scope.$index + '.OnceDose'" :rules="rules.OnceDose" class="workench-table_from_item_class" label-width="0">
                <el-input v-model="scope.row.OnceDose" size="mini" v-input-fixed="2" class="table_custom_input" :disabled="disabledEdit()"></el-input>
              </el-form-item>
            </template>
          </el-table-column>
          <el-table-column label="用法" prop="Frequency" width="150">
            <template slot-scope="scope">
              <el-form-item :show-message="false" :prop="'Detail.' + scope.$index + '.Frequency'" :rules="rules.Frequency" class="workench-table_from_item_class" label-width="0">
                <el-select v-model="scope.row.Frequency" filterable size="mini" :disabled="disabledEdit()">
                  <el-option v-for="item in frequencys" :key="item.ID" :label="item.Name" :value="item.ID"></el-option>
                </el-select>
              </el-form-item>
            </template>
          </el-table-column>
          <el-table-column label="途径" prop="DrugUse" width="150">
            <template slot-scope="scope">
              <el-form-item :show-message="false" :prop="'Detail.' + scope.$index + '.DrugUse'" :rules="rules.DrugUse" class="workench-table_from_item_class" label-width="0">
                <el-select v-model="scope.row.DrugUse" filterable size="mini" :disabled="disabledEdit()">
                  <el-option v-for="item in drugUses" :key="item.ID" :label="item.Name" :value="item.ID"></el-option>
                </el-select>
              </el-form-item>
            </template>
          </el-table-column>
          <el-table-column label="备注(用法)" prop="Remark" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.Remark" size="mini" class="table_custom_input" :disabled="disabledEdit()"></el-input>
            </template>
          </el-table-column>
          <el-table-column label="皮试" prop="IsSkinTest">
            <template slot-scope="scope">
              <el-checkbox v-model="scope.row.IsSkinTest" :disabled="disabledEdit()"></el-checkbox>
            </template>
          </el-table-column>
        </el-table>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="showAddPrescriptionVisible = false" size="small" v-prevent-click>取 消</el-button>
        <el-button v-if="isAdd || (addRuleForm.BillStatus == 'NEW' && CustomerCases.PrescriptionUpdate)" type="primary" size="small" @click="saveAddPrescriptionClick" v-prevent-click :loading="modalLoading">保存处方</el-button>

        <el-button v-if="(addRuleForm.BillStatus == 'NEW' && CustomerCases.PrescriptionUpdate) || isAdd" type="primary" size="small" @click="submitAddPrescriptionClick" v-prevent-click :loading="submitLoading">提交处方</el-button>

        <el-button v-if="!isAdd && (addRuleForm.BillStatus == 'NEW' || addRuleForm.BillStatus == 'PAY' || addRuleForm.BillStatus == 'YES') && CustomerCases.PrescriptionUpdate" type="danger" size="small" @click="cancelAddPrescriptionClick" v-prevent-click :loading="cancelLoading">取消处方</el-button>

        <el-button v-if="!isAdd && addRuleForm.BillStatus == 'OUT' && CustomerCases.PrescriptionUpdate" type="danger" size="small" @click="cancellationAddPrescriptionClick" v-prevent-click :loading="cancellationLoading">作废处方</el-button>

        <el-button v-show="templateTypeList && templateTypeList.length == 0 && !isAdd" type="primary" @click="printInfoTips" size="small" v-prevent-click>打印</el-button>
        <el-button v-show="templateTypeList && templateTypeList.length == 1 && !isAdd" type="primary" v-print="'printContent'" @click="printInfo" size="small" v-prevent-click>打印</el-button>
        <el-button v-show="templateTypeList && templateTypeList.length > 1" type="primary" @click="printInfoSelectTemplate" size="small" v-prevent-click>打印</el-button>
      </span>
    </el-dialog>
    <!-- 选择商品 -->
    <el-dialog title="选择药品" :visible.sync="showPrescriptionWarehouse" append-to-body width="1100px" @close="closeProductListDialog">
      <el-row>
        <el-col :span="24">
          <el-form size="small" inline>
            <el-form-item label="药品名称">
              <el-input v-model="searchProductName" @change="handleSearchProduct" placeholder="请输入药品名称搜索" clearable></el-input>
            </el-form-item>
            <el-form-item>
              <el-button @click="handleSearchProduct" v-prevent-click type="primary" size="small">搜索</el-button>
            </el-form-item>
          </el-form>
        </el-col>
      </el-row>

      <el-table :data="product_list" size="small" :row-style="productrowStyle" ref="productRef" @row-click="productRowClick" @select="productSelectCheckbox" :header-cell-class-name="headerCellClass" height="50vh">
        <el-table-column type="selection" :selectable="productSelectable" width="55"> </el-table-column>
        <el-table-column label="药品名称" prop="ProductName">
          <template slot-scope="scope">
            <div>{{ scope.row.ProductName }}</div>
            <div class="font_12 color_999">{{ scope.row.Alias }}</div>
            <el-tag v-if="scope.row.IsLock" color="#eca24a" size="mini" type="warning" effect="dark">盘点锁定</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="价格" prop="Price"></el-table-column>
        <el-table-column label="规格" prop="Specification"></el-table-column>
        <el-table-column label="包装单位" prop="Unit" :formatter="unitFormatter"></el-table-column>
      </el-table>
      <div class="pad_10 text_right">
        <el-pagination background v-if="warehousePaginations.total > 0" @current-change="warehouseHandleCurrentChange" :current-page.sync="warehousePaginations.page" :page-size="warehousePaginations.page_size" :layout="warehousePaginations.layout" :total="warehousePaginations.total"></el-pagination>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="showPrescriptionWarehouse = false" size="small" v-prevent-click>取 消</el-button>
        <el-button type="primary" size="small" @click="saveAddPrescriptionProductClick" v-prevent-click :loading="modalLoading">保 存</el-button>
      </span>
    </el-dialog>
    <!-- 打印 -->
    <el-dialog title="选择打印模板" :visible.sync="printTemplateVisible" width="400px">
      <el-select size="small" v-model="printTemplateID" @change="changePrintTemplate">
        <el-option v-for="item in templateTypeList" :key="item.ID" :label="item.Name" :value="item.ID"></el-option>
      </el-select>
      <div slot="footer">
        <el-button @click="printTemplateVisible = false" size="small" v-prevent-click>取消</el-button>
        <el-button v-print="'printContent'" type="primary" @click="confirmPrintTemplate" size="small" v-prevent-click>打印</el-button>
      </div>
    </el-dialog>
    <div style="display: none">
      <component id="printContent" :is="printComponentName"></component>
    </div>
  </div>
</template>

<script>
import API from "@/api/iBeauty/Workbench/workbenchPrescription.js";
import printAPI from "@/api/PSI/Inventory/inventoryCheck.js";
import print from "vue-print-nb";
import Vue from "vue";
export default {
  name: "workbenchPrescription",
  directives: {
    print,
  },
  props: {
    CustomerID: {
      type: Number,
      require: true,
    },
    CustomerCases: {
      Type: Boolean,
      default() {
        return {
          PrescriptionAdd: false,
          PrescriptionUpdate: false,
        };
      },
    },
  },
  /** 监听数据变化   */
  watch: {},
  /**  引入的组件  */
  components: {},
  /**  Vue 实例的数据对象**/
  data() {
    return {
      printComponentName: "",
      printContent: "",
      printTemplateVisible: false,
      printTemplateID: "",
      templateTypeListAll: [],
      templateTypeList: [],

      loading: false,
      modalLoading: false,
      submitLoading: false,
      cancelLoading: false,
      cancellationLoading: false,
      showPrescriptionWarehouse: false,
      showAddPrescriptionVisible: false,
      paginations: {
        page: 1, // 当前位于哪页
        total: 0, // 总数
        page_size: 10, // 1页显示多少条
        layout: "total, prev, pager, next,jumper", // 翻页属性
      },
      warehousePaginations: {
        page: 1, // 当前位于哪页
        total: 0, // 总数
        page_size: 10, // 1页显示多少条
        layout: "total, prev, pager, next,jumper", // 翻页属性
      },
      searchForm: {
        ID: "",
        BillStatus: "",
      },
      prescriptionBill_list: [],
      addRuleForm: {
        CustomerID: "", //顾客编号
        BillDate: "", //订单时间
        WarehouseID: "", //仓库编号
        DepartmentID: "", //科室
        EmployeeID: "", //医生编号
        CostCategory: "ZF", //费别（YB：医保、FYB：非医保、ZF：自费、STA：住院、MEN：门诊、DOC：手术室）
        PrescriptionType: "", //类型(A：麻醉、E：毒、B：精一、C：精二、D：普通)
        Diagnostic: "", //诊断
        MedicalAdvice: "", //医嘱
        Detail: [],
      },

      rules: {
        BillDate: [{ required: true, message: "请选择日期", trigger: ["blur", "change"] }],
        WarehouseID: [{ required: true, message: "请选择仓库", trigger: ["blur", "change"] }],
        DepartmentID: [{ required: true, message: "请选择科室", trigger: ["blur", "change"] }],
        EmployeeID: [{ required: true, message: "请选择医生", trigger: ["blur", "change"] }],
        CostCategory: [{ required: true, message: "请选择费别", trigger: ["blur", "change"] }],
        PrescriptionType: [{ required: true, message: "请选择类型", trigger: ["blur", "change"] }],
        Detail: [{ required: true, message: "请选择处方药品", trigger: ["blur", "change"] }],
        CustomerMedicalRecordID: [{ required: true, message: "请选择病历", trigger: ["blur", "change"] }],
        StockBatchID: [{ required: true, trigger: ["blur", "change"] }],
        DoseUnit: [{ required: true, message: "请选择单次剂量单位", trigger: ["blur", "change"] }],
        OnceDose: [{ required: true, trigger: ["blur", "change"] }],
        Frequency: [{ required: true, trigger: ["blur", "change"] }],
        DrugUse: [{ required: true, trigger: ["blur", "change"] }],
      },
      warehouseByEntity: [],
      department_list: [],
      doctor_list: [],
      searchProductName: "",
      product_list: [],
      selectProduct: [],
      // 途径数组
      drugUses: [
        { ID: 40, Name: "口服" },
        { ID: 1, Name: "外用" },
        { ID: 2, Name: "滴眼" },
        { ID: 3, Name: "静脉注射" },
        { ID: 4, Name: "静脉滴注" },
        { ID: 5, Name: "肌肉注射" },
        { ID: 6, Name: "皮下注射" },
        { ID: 7, Name: "皮内注射" },
        { ID: 8, Name: "腔内注射" },
        { ID: 9, Name: "椎管注射" },
        { ID: 10, Name: "动脉注射" },
        { ID: 11, Name: "心内注射" },
        { ID: 12, Name: "球内注射" },
        { ID: 13, Name: "皮下埋植" },
        { ID: 14, Name: "口腔喷雾" },
        { ID: 15, Name: "口腔吸入" },
        { ID: 16, Name: "口腔黏膜给药" },
        { ID: 17, Name: "滴鼻" },
        { ID: 18, Name: "鼻腔喷雾" },
        { ID: 19, Name: "鼻饲" },
        { ID: 20, Name: "鼻腔吸入" },
        { ID: 21, Name: "滴耳" },
        { ID: 22, Name: "眼科外用" },
        { ID: 23, Name: "涂于眼睑内" },
        { ID: 24, Name: "含漱" },
        { ID: 25, Name: "含服" },
        { ID: 26, Name: "舌下含服" },
        { ID: 27, Name: "直肠给药" },
        { ID: 28, Name: "直肠塞入" },
        { ID: 29, Name: "肛门涂抹" },
        { ID: 30, Name: "阴道给药" },
        { ID: 31, Name: "阴道塞入" },
        { ID: 32, Name: "阴道擦洗" },
        { ID: 33, Name: "滴于眼睑内" },
        { ID: 34, Name: "雾化吸入" },
        { ID: 35, Name: "肌内注射" },
        { ID: 36, Name: "穴位封闭" },
        { ID: 37, Name: "软组织封闭" },
        { ID: 38, Name: "饭前口服" },
        { ID: 39, Name: "饭后口服" },
      ],
      // 用法用量
      frequencys: [
        { ID: 20, Name: "每天1次" },
        { ID: 1, Name: "每天2次" },
        { ID: 2, Name: "每天3次" },
        { ID: 3, Name: "每天4次" },
        { ID: 4, Name: "隔日1次" },
        { ID: 5, Name: "每周1次" },
        { ID: 6, Name: "必要时" },
        { ID: 7, Name: "隔周1次" },
        { ID: 8, Name: "每晚1次" },
        { ID: 9, Name: "立即" },
        { ID: 10, Name: "每6小时1次" },
        { ID: 11, Name: "每8小时1次" },
        { ID: 12, Name: "每12小时1次" },
        { ID: 13, Name: "每4小时1次" },
        { ID: 14, Name: "每天5次" },
        { ID: 15, Name: "每周2次" },
        { ID: 16, Name: "每周3次" },
        { ID: 17, Name: "每月1次" },
        { ID: 18, Name: "每小时1次" },
        { ID: 19, Name: "每3天1次" },
      ],
      FormatterValue: {
        BillStatus: { NEW: "新处方", PAY: "未收费", CAN: "取消", YES: "收款未发药", OUT: "已发药", RTD: "作废" },
        CostCategory: { YB: "医保", FYB: "非医保", ZF: "自费", STA: "住院", MEN: "门诊", DOC: "手术室" },
        PrescriptionType: { A: "麻醉", E: "毒", B: "精一", C: "精二", D: "普通" },
      },
      isAdd: true,
      medicalRecord_list: [],
      customerMedicalRecordName: "",
      OneDoses: [
        {
          ID: 1,
          Name: "mg",
        },
        {
          ID: 2,
          Name: "g",
        },
        {
          ID: 3,
          Name: "ml",
        },
        {
          ID: 4,
          Name: "支",
        },
        {
          ID: 5,
          Name: "片",
        },
        {
          ID: 6,
          Name: "粒",
        },
        {
          ID: 7,
          Name: "丸",
        },
        {
          ID: 8,
          Name: "袋",
        },
        {
          ID: 9,
          Name: "瓶",
        },
        {
          ID: 10,
          Name: "贴",
        },
        {
          ID: 11,
          Name: "枚",
        },
        {
          ID: 12,
          Name: "包",
        },
        {
          ID: 13,
          Name: "盒",
        },
        {
          ID: 14,
          Name: "吸",
        },
        {
          ID: 15,
          Name: "揿",
        },
        {
          ID: 16,
          Name: "滴",
        },
        {
          ID: 17,
          Name: "只",
        },
        {
          ID: 18,
          Name: "dag",
        },
        {
          ID: 19,
          Name: "U",
        },
        {
          ID: 20,
          Name: "IU",
        },
        {
          ID: 21,
          Name: "单位",
        },
        {
          ID: 22,
          Name: "万单位",
        },
        {
          ID: 23,
          Name: "μg",
        },
        {
          ID: 24,
          Name: "个",
        },
        {
          ID: 25,
          Name: "次",
        },
        {
          ID: 26,
          Name: "IIU",
        },
        {
          ID: 27,
          Name: "cm",
        },
      ],
    };
  },
  /**计算属性  */
  computed: {},
  /**  方法集合  */
  methods: {
    /**    */
    getChannelNames(items) {
      if (!items) {
        return "";
      }
      return items.reduce((per, cur, index) => {
        if (items.length - 1 == index) {
          return per + cur.EmployeeName;
        } else {
          return per + cur.EmployeeName + ",";
        }
      }, "");
    },
    /**    */
    removeMedicalRecord() {
      let that = this;
      that.customerMedicalRecordName = "";
      that.addRuleForm.CustomerMedicalRecordID = "";
    },
    /**    */
    handleMedicalRecordSelect(row) {
      let that = this;
      that.customerMedicalRecordName = row.Name;
      that.addRuleForm.CustomerMedicalRecordID = row.ID;
    },
    /** 确定选择打印模板，并打印   */
    confirmPrintTemplate() {
      let that = this;
      that.printTemplateVisible = false;
      that.$nextTick(() => {
        that.createPrintComponent(that.addRuleForm, that.printContent);
      });
    },
    /** 修改打印模板   */
    changePrintTemplate(val) {
      let that = this;
      let tempItem = that.templateTypeList.filter((item) => item.ID == val);
      that.printContent = tempItem[0].Template;
    },
    // 创建打印组件
    createPrintComponent(info, printContent) {
      let tempInfo = info; //传入打印数据源
      let templateStr = printContent; //传入打印模板
      var timestamp = new Date().valueOf();
      var componentName = "print" + timestamp;

      //创建组件
      Vue.component(componentName, {
        data() {
          return {
            info: tempInfo, //传入打印数据源
          };
        },
        template: "<div>" + templateStr + "</div>", //打印模板
      });
      this.printComponentName = componentName; //显示打印组件
    },

    /**  打印  */
    /**    */
    printInfoTips() {
      let that = this;
      that.$message.error("暂无打印模板，请添加打印模板");
    },
    printInfoSelectTemplate() {
      let that = this;
      that.printTemplateID = "";
      that.printTemplateVisible = true;
    },
    printInfo() {
      let that = this;
      let tempPrintTemplate = that.templateTypeList.length == 1 ? that.templateTypeList[0].Template : "";
      that.createPrintComponent(that.addRuleForm, tempPrintTemplate);
    },

    /**    */
    disabledEdit() {
      let that = this;
      if (!that.isAdd) {
        if (that.addRuleForm.BillStatus == "CAN" || that.addRuleForm.BillStatus == "YES" || that.addRuleForm.BillStatus == "OUT" || that.addRuleForm.BillStatus == "RTD" || that.addRuleForm.BillStatus == "PAY") {
          return true;
        }
        return false;
      }
      return false;
    },
    /**  商品列表修改分页  */
    warehouseHandleCurrentChange(page) {
      let that = this;
      that.warehousePaginations.page = page;
      that.stock_entityMedicineList();
    },
    /**  作废处方  */
    cancellationAddPrescriptionClick() {
      let that = this;
      that
        .$confirm("是否要作废处方？", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
        .then(() => {
          that.prescriptionBill_cancellation(that.addRuleForm.ID);
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消操作",
          });
        });
    },
    /** 取消   */
    cancelAddPrescriptionClick() {
      let that = this;
      that
        .$confirm("是否要取消处方？", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
        .then(() => {
          that.prescriptionBill_cancel(that.addRuleForm.ID);
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消操作",
          });
        });
    },
    /**  提交处方  */
    submitAddPrescriptionClick() {
      let that = this;
      if (!that.addRuleForm.Detail || !that.addRuleForm.Detail.length) {
        that.$message.error("请选择药品");
        return;
      }
      that.$refs.addRuleFormRef.validate((valid) => {
        if (valid) {
          that.prescriptionBill_submit();
        }
      });
    },
    /**  编辑处方  */
    editPrescriptClick(row) {
      let that = this;
      that.isAdd = false;
      that.prescriptionBill_detail(row.ID).then((data) => {
        data.Detail = data.Detail.map((i) => {
          i.DrugUseName = i.DrugUse ? that.drugUses.find((j) => j.ID == i.DrugUse).Name : "";
          i.FrequencyName = i.Frequency ? that.frequencys.find((j) => j.ID == i.Frequency).Name : "";
          // DoseUnit  单次剂量单位
          i.OnceDoseName = i.DoseUnit ? that.OneDoses.find((j) => j.ID == i.DoseUnit).Name : "";
          return Object.assign({}, i);
        });
        Object.assign(that.addRuleForm, data);
        that.selectProduct = that.addRuleForm.Detail.map((i) => {
          i.ID = i.ProductID;
          return i;
        });
        that.templateTypeList = that.templateTypeListAll.filter((j) => {
          return j.Name.includes(that.FormatterValue.PrescriptionType[data.PrescriptionType]);
        });
        that.showAddPrescriptionVisible = true;
      });
    },
    /**   格式化表格数据 */
    tableFormatterValue(row, column) {
      let that = this;
      return that.FormatterValue[column.property][row[column.property]];
    },
    /**   修改折扣 */
    changeDiscount(row) {
      if (row.Discount > 100) {
        row.Discount = 100;
      }
      row.TotalAmount = parseFloat((parseFloat(row.Price) * parseInt(row.TotalDose) * parseFloat(row.Discount)) / 100).toFixed(2);
    },
    /**    */
    changeQuantity(row) {
      if (row.Quantity > row.StockQuantity) {
        row.TotalDose = row.StockQuantity;
        row.Quantity = row.StockQuantity;
      }
      if (row.Quantity < row.TotalDose) {
        row.Quantity = row.TotalDose;
      } else {
        row.TotalDose = row.Quantity;
        row.TotalAmount = (parseFloat(row.Price) * parseInt(row.TotalDose) * parseFloat(row.Discount)) / 100;
      }
    },
    /**    */
    changeDoseInput(row, type) {
      let that = this;
      if (row.OneDose == 0) {
        row.OneDose = 1;
      }
      if (row.DailyCount == 0) {
        row.DailyCount = 1;
      }
      if (row.PrescribingNumber == 0) {
        row.PrescribingNumber = 1;
      }
      row.Quantity = parseInt(row.OneDose) * parseInt(row.DailyCount) * parseInt(row.PrescribingNumber);
      if (row.Quantity > row.StockQuantity) {
        that.$message.error("药品库存不足");
        if (type == "OneDose") {
          row.OneDose = parseInt(parseInt(row.StockQuantity) / parseInt(row.DailyCount) / parseInt(row.PrescribingNumber));
        }
        if (type == "DailyCount") {
          row.DailyCount = parseInt(parseInt(row.StockQuantity) / parseInt(row.OneDose) / parseInt(row.PrescribingNumber));
        }
        if (type == "PrescribingNumber") {
          row.PrescribingNumber = parseInt(parseInt(row.StockQuantity) / parseInt(row.DailyCount) / parseInt(row.OneDose));
        }
        row.Quantity = row.StockQuantity;
      }

      row.TotalDose = row.Quantity;
      row.TotalAmount = (parseFloat(row.Price) * parseInt(row.TotalDose) * parseFloat(row.Discount)) / 100;
    },
    /**    */
    closeProductListDialog() {
      let that = this;
      that.selectProduct = [];
    },
    /**    */
    headerCellClass({ columnIndex }) {
      if (columnIndex === 0) {
        return "disableSelection";
      }
    },
    /**  点击 商品Checkbox  */
    productSelectCheckbox(selection, row) {
      if (row.IsLock) return;
      let that = this;
      that.selectProduct = selection;
    },
    /**    */
    productRowClick(row) {
      let that = this;
      if (row.IsLock) return;
      let findIndex = that.selectProduct.findIndex((i) => i.ID == row.ID);
      if (findIndex != -1) {
        that.$refs.productRef.toggleRowSelection(row, false);
        that.selectProduct.splice(findIndex, 1);
      } else {
        that.$refs.productRef.toggleRowSelection(row, true);
        that.selectProduct.push(row);
      }
    },
    /**    */
    productrowStyle({ row }) {
      if (row.IsLock) {
        return {
          background: "#f5f7fa !important",
        };
      }
    },
    /**  保存已选择的药品  */
    saveAddPrescriptionProductClick() {
      let that = this;
      that.showPrescriptionWarehouse = false;
      let temp = that.selectProduct.map((i) => {
        let defautSendReceiveItem = i.Unit.find((j) => j.IsDefautSendReceive);
        return {
          ProductID: i.ID, //产品编号
          ProductName: i.ProductName,
          Alias: i.Alias,
          Price: i.Price, //价格
          Specification: i.Specification,
          Unit: i.Unit,
          OneDose: i.OneDose ? i.OneDose : 1, //单剂量
          DailyCount: i.DailyCount ? i.DailyCount : 1, //每日次数
          PrescribingNumber: i.PrescribingNumber ? i.PrescribingNumber : 1, //开药天数
          Quantity: i.Discount ? i.Quantity : 1, //数量
          TotalDose: i.TotalDose ? i.TotalDose : "1", //总计量
          Discount: i.Discount ? i.Discount : 100, //折扣
          TotalAmount: i.TotalAmount ? i.TotalAmount : i.Price, //总金额
          UnitID: defautSendReceiveItem.UnitID, //单位ID
          UnitName: defautSendReceiveItem.UnitName, //单位名称
          OnceDose: i.OnceDose ? i.OnceDose : "1", //单次用量
          DrugUse: i.DrugUse ? i.DrugUse : "", //途径
          Frequency: i.Frequency ? i.Frequency : "", //服(用)法
          Remark: i.Remark ? i.Remark : "", //备注
          StockQuantity: i.StockQuantity ? i.StockQuantity : "",
          IsSkinTest: i.IsSkinTest ? i.IsSkinTest : false,
          IsBatchManagement: i.IsBatchManagement,
          Batch: i.Batch.map((j) => {
            j.label = j.BatchNumber + " / " + j.ValidDate;
            return j;
          }),
        };
      });
      that.addRuleForm.Detail = [...that.addRuleForm.Detail, ...temp];
      that.selectProduct = [];
    },
    /**    */
    productSelectable(row) {
      return !row.IsLock;
    },
    /**    */
    unitFormatter(row) {
      let unitItem = row.Unit.find((i) => i.IsDefautSendReceive);
      return unitItem.UnitName;
    },
    /**  搜索商品、药品  */
    handleSearchProduct() {
      let that = this;
      that.warehousePaginations.page = 1;
      that.stock_entityMedicineList();
    },
    /**  添加商品、药品  */
    addPrescriptionWarehouseClick() {
      let that = this;
      that.searchProductName = "";
      that.warehousePaginations.page = 1;
      that.selectProduct = [];
      that.$refs.addRuleFormRef.validateField("WarehouseID", (valid) => {
        if (valid !== "请选择仓库") {
          that.stock_entityMedicineList().then(() => {
            that.showPrescriptionWarehouse = true;
            // that.$nextTick(() => {
            //   that.addRuleForm.Detail.forEach((i) => {
            //     that.product_list.forEach((j) => {
            //       if (i.ProductID == j.ID) {
            //         that.$refs.productRef.toggleRowSelection(j, true);
            //         that.selectProduct.push(i);
            //       }
            //     });
            //   });
            // });
          });
        }
      });
    },
    /**  保存新建处方  */
    saveAddPrescriptionClick() {
      let that = this;
      if (!that.addRuleForm.Detail || !that.addRuleForm.Detail.length) {
        that.$message.error("请选择药品");
        return;
      }
      that.$refs.addRuleFormRef.validate((valid) => {
        if (valid) {
          that.prescriptionBill_create();
        }
      });
    },
    /**    */
    addPrescriptionClick() {
      let that = this;
      that.isAdd = true;
      that.customerMedicalRecordName = "";
      let empInfo = JSON.parse(localStorage.getItem("access-user"));
      let WarehouseID = that.warehouseByEntity.length > 0 ? that.warehouseByEntity[0].ID : "";
      that.addRuleForm = {
        ID: "",
        CustomerID: that.CustomerID, //顾客编号
        BillDate: that.$dayjs().format("YYYY-MM-DD"), //订单时间
        WarehouseID: WarehouseID, //仓库编号
        DepartmentID: empInfo.DepartmentID, //科室
        EmployeeID: that.doctor_list.findIndex((i) => i.ID == empInfo.EmployeeID) == -1 ? "" : empInfo.EmployeeID, //医生编号
        CostCategory: "ZF", //费别（YB：医保、FYB：非医保、ZF：自费、STA：住院、MEN：门诊、DOC：手术室）
        PrescriptionType: "", //类型(A：麻醉、E：毒、B：精一、C：精二、D：普通)
        Diagnostic: "", //诊断
        MedicalAdvice: "", //医嘱
        CustomerMedicalRecordID: "",
        Detail: [],
      };
      that.showAddPrescriptionVisible = true;
    },
    /**    */
    handleSearch() {
      let that = this;
      that.paginations.page = 1;
      that.customer_prescriptionBill();
    },
    /**    */
    handleCurrentChange(page) {
      let that = this;
      that.paginations.page = page;
      that.customer_prescriptionBill();
    },
    /* ••••••••••••••••••••••••••••••••••••••••••••••••••••• */
    /**  处方列表  */
    async customer_prescriptionBill() {
      let that = this;
      try {
        let params = {
          PageNum: that.paginations.page,
          ID: that.searchForm.ID, //处方单号
          CustomerID: that.CustomerID, //顾客编号
          BillStatus: that.searchForm.BillStatus,
        };
        let res = await API.customer_prescriptionBill(params);
        if (res.StateCode == 200) {
          that.prescriptionBill_list = res.List;
          that.paginations.total = res.Total;
        } else {
          that.$message.error(res.Message);
        }
      } catch (error) {
        // that.$message.error(error);
      }
    },
    /**  处方详情  */
    async prescriptionBill_detail(ID) {
      let that = this;
      try {
        let params = { ID: ID };
        let res = await API.prescriptionBill_detail(params);
        if (res.StateCode == 200) {
          for (const item of res.Data.Detail) {
            for (const batchItem of item.Batch) {
              batchItem.label = batchItem.BatchNumber + " / " + batchItem.ValidDate;
            }
          }
          return res.Data;
        } else {
          that.$message.error(res.Message);
        }
      } catch (error) {
        // that.$message.error(error);
      }
    },
    /**  科室列表  */
    async prescriptionBill_department() {
      let that = this;
      try {
        let params = {
          Active: true,
        };
        let res = await API.prescriptionBill_department(params);
        if (res.StateCode == 200) {
          that.department_list = res.Data;
        } else {
          that.$message.error(res.Message);
        }
      } catch (error) {
        // that.$message.error(error);
      }
    },
    /** 仓库列表   */
    async entity_warehouseByEntity() {
      let that = this;
      try {
        let params = {};
        let res = await API.entity_warehouseByEntity(params);
        if (res.StateCode == 200) {
          that.warehouseByEntity = res.Data;
        } else {
          that.$message.error(res.Message);
        }
      } catch (error) {
        // that.$message.error(error);
      }
    },
    /**  药品列表  */
    async stock_entityMedicineList() {
      let that = this;
      try {
        let params = {
          PageNum: that.warehousePaginations.page,
          ProductName: that.searchProductName,
          WarehouseID: that.addRuleForm.WarehouseID,
        };
        let res = await API.stock_entityMedicineList(params);
        if (res.StateCode == 200) {
          that.product_list = res.List;
          that.warehousePaginations.total = res.Total;
        } else {
          that.$message.error(res.Message);
        }
      } catch (error) {
        // that.$message.error(error);
      }
    },
    /**  创建处方  */
    async prescriptionBill_create() {
      let that = this;
      try {
        that.modalLoading = true;
        let params = {
          ID: that.addRuleForm.ID,
          CustomerID: that.CustomerID, //顾客编号
          BillDate: that.addRuleForm.BillDate, //订单时间
          WarehouseID: that.addRuleForm.WarehouseID, //仓库编号
          DepartmentID: that.addRuleForm.DepartmentID, //科室
          EmployeeID: that.addRuleForm.EmployeeID, //医生编号
          CostCategory: that.addRuleForm.CostCategory, //费别（YB：医保、FYB：非医保、ZF：自费、STA：住院、MEN：门诊、DOC：手术室）
          PrescriptionType: that.addRuleForm.PrescriptionType, //类型(A：麻醉、E：毒、B：精一、C：精二、D：普通)
          Diagnostic: that.addRuleForm.Diagnostic, //诊断
          MedicalAdvice: that.addRuleForm.MedicalAdvice, //医嘱
          CustomerMedicalRecordID: that.addRuleForm.CustomerMedicalRecordID,
          Detail: that.addRuleForm.Detail.map((i) => {
            return {
              OneDose: i.OneDose, //单剂量
              DailyCount: i.DailyCount, //每日次数
              PrescribingNumber: i.PrescribingNumber, //开药天数
              Quantity: i.Quantity, //数量
              TotalDose: i.TotalDose, //总计量
              ProductID: i.ProductID, //药品编号
              Price: i.Price, //价格
              Discount: i.Discount, //折扣
              TotalAmount: i.TotalAmount || 0, //应付金额
              UnitID: i.UnitID, //单位
              OnceDose: i.OnceDose, //单位用量
              DrugUse: i.DrugUse, //途径
              Frequency: i.Frequency, //用法
              Remark: i.Remark, //备注
              IsSkinTest: i.IsSkinTest,
              StockBatchID: i.StockBatchID,
              DoseUnit: i.DoseUnit,
            };
          }),
        };
        let res = await API.prescriptionBill_create(params);
        if (res.StateCode == 200) {
          that.$message.success("操作成功");
          that.customer_prescriptionBill();
          that.showAddPrescriptionVisible = false;
        } else {
          that.$message.error(res.Message);
        }
        that.modalLoading = false;
      } catch (error) {
        that.modalLoading = false;
        that.$message.error(error);
      }
    },
    /**  处方提交  */
    async prescriptionBill_submit() {
      let that = this;
      try {
        that.submitLoading = true;
        let params = {
          ID: that.isAdd ? "" : that.addRuleForm.ID,
          CustomerID: that.CustomerID, //顾客编号
          BillDate: that.addRuleForm.BillDate, //订单时间
          WarehouseID: that.addRuleForm.WarehouseID, //仓库编号
          DepartmentID: that.addRuleForm.DepartmentID, //科室
          EmployeeID: that.addRuleForm.EmployeeID, //医生编号
          CostCategory: that.addRuleForm.CostCategory, //费别（YB：医保、FYB：非医保、ZF：自费、STA：住院、MEN：门诊、DOC：手术室）
          PrescriptionType: that.addRuleForm.PrescriptionType, //类型(A：麻醉、E：毒、B：精一、C：精二、D：普通)
          Diagnostic: that.addRuleForm.Diagnostic, //诊断
          MedicalAdvice: that.addRuleForm.MedicalAdvice, //医嘱
          CustomerMedicalRecordID: that.addRuleForm.CustomerMedicalRecordID,
          Detail: that.addRuleForm.Detail.map((i) => {
            return {
              OneDose: i.OneDose, //单剂量
              DailyCount: i.DailyCount, //每日次数
              PrescribingNumber: i.PrescribingNumber, //开药天数
              Quantity: i.Quantity, //数量
              TotalDose: i.TotalDose, //总计量
              ProductID: i.ProductID, //药品编号
              Price: i.Price, //价格
              Discount: i.Discount, //折扣
              TotalAmount: i.TotalAmount || 0, //应付金额
              UnitID: i.UnitID, //单位
              OnceDose: i.OnceDose, //单位用量
              DrugUse: i.DrugUse, //途径
              Frequency: i.Frequency, //用法
              Remark: i.Remark, //备注
              IsSkinTest: i.IsSkinTest,
              StockBatchID: i.StockBatchID,
              DoseUnit: i.DoseUnit,
            };
          }),
        };
        let res = await API.prescriptionBill_submit(params);
        if (res.StateCode == 200) {
          that.$message.success("操作成功");
          that.customer_prescriptionBill();
          that.showAddPrescriptionVisible = false;
        } else {
          that.$message.error(res.Message);
        }
        that.submitLoading = false;
      } catch (error) {
        that.submitLoading = false;
        that.$message.error(error);
      }
    },
    /**  处方收款未发药  */
    async prescriptionBill_pay() {
      let that = this;
      try {
        let params = {};
        let res = await API.prescriptionBill_pay(params);
        if (res.StateCode == 200) {
          // console.log(res);
        } else {
          that.$message.error(res.Message);
        }
      } catch (error) {
        // that.$message.error(error);
      }
    },
    /**  处方取消  */
    async prescriptionBill_cancel(ID) {
      let that = this;
      try {
        that.cancelLoading = true;
        let params = { ID: ID };
        let res = await API.prescriptionBill_cancel(params);
        if (res.StateCode == 200) {
          that.$message.success("操作成功");
          that.customer_prescriptionBill();
          that.showAddPrescriptionVisible = false;
        } else {
          that.$message.error(res.Message);
        }
        that.cancelLoading = false;
      } catch (error) {
        that.cancelLoading = false;
        that.$message.error(error);
      }
    },
    /**    */
    async prescriptionBill_cancellation(ID) {
      let that = this;
      try {
        that.cancellationLoading = true;
        let params = { ID };
        let res = await API.prescriptionBill_cancellation(params);
        if (res.StateCode == 200) {
          that.$message.success("操作成功");
          that.customer_prescriptionBill();
          that.showAddPrescriptionVisible = false;
        } else {
          that.$message.error(res.Message);
        }
        that.cancellationLoading = false;
      } catch (error) {
        that.cancellationLoading = false;
        that.$message.error(error);
      }
    },
    /**  处方收款发药  */
    async prescriptionBill_dispenseMedicine() {
      let that = this;
      try {
        let params = {};
        let res = await API.prescriptionBill_dispenseMedicine(params);
        if (res.StateCode == 200) {
          // console.log(res);
        } else {
          that.$message.error(res.Message);
        }
      } catch (error) {
        // that.$message.error(error);
      }
    },
    /**  医生列表  */
    async customerMedicalRecord_doctor() {
      let that = this;
      try {
        let params = {};
        let res = await API.customerMedicalRecord_doctor(params);
        if (res.StateCode == 200) {
          that.doctor_list = res.Data;
        } else {
          that.$message.error(res.Message);
        }
      } catch (error) {
        // that.$message.error(error);
      }
    },
    /** 获取模板列表   */
    async getPrintTemplate_list() {
      let that = this;
      let params = { TemplateType: "prescription" };
      let res = await printAPI.getPrintTemplate_list(params);
      if (res.StateCode == 200) {
        that.templateTypeListAll = res.Data;
      } else {
        that.$message.error(res.Message);
      }
    },
    /**  病历列表  */
    async customerMedicalRecord_search(Name, cb) {
      let that = this;

      try {
        let params = {
          CustomerID: that.CustomerID,
          Name: Name,
        };
        let res = await API.customerMedicalRecord_search(params);
        if (res.StateCode == 200) {
          that.medicalRecord_list = res.Data;
          cb(that.medicalRecord_list);
        } else {
          that.$message.error(res.Message);
        }
      } catch (error) {
        // that.$message.error(error);
      }
    },
    /**    */
    initWorkbenchPrescriptionData() {
      let that = this;
      that.customer_prescriptionBill();
      that.prescriptionBill_department();
      that.entity_warehouseByEntity();
      that.customerMedicalRecord_doctor();
      that.getPrintTemplate_list();
    },
  },
  /** 创建实例之前执行函数   */
  beforeCreate() {},
  /**  实例创建完成之后  */
  created() {},
  /**  在挂载开始之前被调用  */
  beforeMount() {},
  /**  实例被挂载后调用  */
  mounted() {},
  /**  数据更新 之前 调用  */
  beforeUpdate() {},
  /** 数据更新 完成 调用   */
  updated() {},
  /**  实例销毁之前调用  */
  beforeDestroy() {},
  /**  实例销毁后调用  */
  destroyed() {},
};
</script>

<style lang="scss">
.workbenchPrescription {
}
.table_custom_input {
  .el-input__inner {
    padding: 0 5px !important;
  }
  .el-input-group__append {
    padding: 0 8px;
  }
}
.disableSelection {
  .cell {
    display: none !important;
  }
}

.batch-popper-class {
  .el-select-dropdown__item {
    line-height: unset;
    height: 40px;
  }
}
.workbench-addPrescription-dialog {
  .workench-table_from_item_class {
    font-size: 13px !important;
    margin-bottom: 0px;
  }
}
@media print {
  html,
  body {
    height: inherit;
  }
}
</style>
