<template>
  <div class="EmployeeSalePerformanceCommissionStatistics content_body" v-loading="loading">
    <div class="nav_header">
      <el-form :inline="true" size="small" :model="searchData" @submit.native.prevent>
        <el-form-item label="时间筛选">
          <el-date-picker v-model="searchData.QueryDate" :picker-options="pickerOptions" unlink-panels type="daterange" range-separator="至" value-format="yyyy-MM-dd" start-placeholder="开始日期" end-placeholder="结束日期" @change="handleSalePerformanceCommissionSearch"></el-date-picker>
        </el-form-item>
        <el-form-item label="员工姓名">
          <el-input v-model="searchData.EmployeeName" clearable @keyup.enter.native="handleSalePerformanceCommissionSearch" @clear="handleSalePerformanceCommissionSearch" placeholder="请输入员工姓名"></el-input>
        </el-form-item>
        <el-form-item v-if="storeEntityList.length > 1" label="所属门店">
          <el-select v-model="searchData.EntityID" clearable filterable placeholder="请选择门店" :default-first-option="true" @change="handleSalePerformanceCommissionSearch">
            <el-option v-for="item in storeEntityList" :key="item.ID" :label="item.EntityName" :value="item.ID"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="员工职务">
          <el-select v-model="searchData.JobID" filterable placeholder="选择员工职务" @change="handleSalePerformanceCommissionSearch" clearable>
            <el-option v-for="item in jobTypeList" :key="item.ID" :label="item.JobName" :value="item.ID"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" v-prevent-click @click="handleSalePerformanceCommissionSearch">搜索</el-button>
        </el-form-item>
        <el-form-item>
          <el-button v-if="isExport" type="primary" size="small" :loading="downloadLoading" @click="downloadExcel">导出</el-button>
        </el-form-item>
      </el-form>
    </div>
    <el-table size="small" show-summary :summary-method="gettableDataSummaries" :data="tableData">
      <el-table-column prop="EmployeeName" label="员工姓名" width="80" fixed></el-table-column>
      <el-table-column prop="EmployeeID" label="员工编号" fixed></el-table-column>
      <el-table-column prop="JobName" label="职务" width="110" fixed></el-table-column>
      <el-table-column prop="EntityName" label="所属组织" width="170" fixed></el-table-column>
      <el-table-column label="销售信息" align="center">
        <el-table-column prop="SalePayPerformance" label="实收业绩" align="right" width="100">
          <template slot-scope="scope">
            {{ scope.row.SalePayPerformance | toFixed | NumFormat }}
          </template>
        </el-table-column>
        <el-table-column prop="SalePayCommission" label="实收提成" align="right" width="100">
          <template slot-scope="scope">
            {{ scope.row.SalePayCommission | toFixed | NumFormat }}
          </template>
        </el-table-column>
        <el-table-column prop="SaleSavingCardPerformance" label="卡扣业绩" align="right" width="100">
          <template slot-scope="scope">
            {{ scope.row.SaleSavingCardPerformance | toFixed | NumFormat }}
          </template>
        </el-table-column>
        <el-table-column prop="SaleSavingCardCommission" label="卡扣提成" align="right" width="100">
          <template slot-scope="scope">
            {{ scope.row.SaleSavingCardCommission | toFixed | NumFormat }}
          </template>
        </el-table-column>
        <el-table-column prop="SaleSavingCardLargessPerformance" label="赠卡扣业绩" align="right" width="100">
          <template slot-scope="scope">
            {{ scope.row.SaleSavingCardLargessPerformance | toFixed | NumFormat }}
          </template>
        </el-table-column>
        <el-table-column prop="SaleSavingCardLargessCommission" label="赠卡扣提成" align="right" width="100">
          <template slot-scope="scope">
            {{ scope.row.SaleSavingCardLargessCommission | toFixed | NumFormat }}
          </template>
        </el-table-column>
        <el-table-column prop="SaleSpecialBenefitCommission" label="无业绩奖励" align="right" width="100">
          <template slot-scope="scope">
            {{ scope.row.SaleSpecialBenefitCommission | toFixed | NumFormat }}
          </template>
        </el-table-column>
      </el-table-column>
      <el-table-column label="补欠款信息" align="center">
        <el-table-column prop="ArrearPayPerformance" label="实收业绩" align="right" width="100">
          <template slot-scope="scope">
            {{ scope.row.ArrearPayPerformance | toFixed | NumFormat }}
          </template>
        </el-table-column>
        <el-table-column prop="ArrearPayCommission" label="实收提成" align="right" width="100">
          <template slot-scope="scope">
            {{ scope.row.ArrearPayCommission | toFixed | NumFormat }}
          </template>
        </el-table-column>
        <el-table-column prop="ArrearSavingCardPerformance" label="卡扣业绩" align="right" width="100">
          <template slot-scope="scope">
            {{ scope.row.ArrearSavingCardPerformance | toFixed | NumFormat }}
          </template>
        </el-table-column>

        <el-table-column prop="ArrearSavingCardCommission" label="卡扣提成" align="right" width="100">
          <template slot-scope="scope">
            {{ scope.row.ArrearSavingCardCommission | toFixed | NumFormat }}
          </template>
        </el-table-column>

        <el-table-column prop="ArrearSavingCardLargessPerformance" label="赠卡扣业绩" align="right" width="100">
          <template slot-scope="scope">
            {{ scope.row.ArrearSavingCardLargessPerformance | toFixed | NumFormat }}
          </template>
        </el-table-column>

        <el-table-column prop="ArrearSavingCardLargessCommission" label="赠卡扣提成" align="right" width="100">
          <template slot-scope="scope">
            {{ scope.row.ArrearSavingCardLargessCommission | toFixed | NumFormat }}
          </template>
        </el-table-column>
        <el-table-column prop="ArrearSpecialBenefitCommission" label="无业绩奖励" align="right" width="100">
          <template slot-scope="scope">
            {{ scope.row.ArrearSpecialBenefitCommission | toFixed | NumFormat }}
          </template>
        </el-table-column>
      </el-table-column>
      <el-table-column label="退款信息" align="center">
        <el-table-column prop="RefundSalePayPerformance" label="退实收业绩" align="right" width="100">
          <template slot-scope="scope">
            {{ scope.row.RefundSalePayPerformance | toFixed | NumFormat }}
          </template>
        </el-table-column>
        <el-table-column prop="RefundSalePayCommission" label="退实收提成" align="right" width="100">
          <template slot-scope="scope">
            {{ scope.row.RefundSalePayCommission | toFixed | NumFormat }}
          </template>
        </el-table-column>
        <el-table-column prop="RefundSaleSavingCardPerformance" label="退卡扣业绩" align="right" width="100">
          <template slot-scope="scope">
            {{ scope.row.RefundSaleSavingCardPerformance | toFixed | NumFormat }}
          </template>
        </el-table-column>
        <el-table-column prop="RefundSaleSavingCardCommission" label="退卡扣提成" align="right" width="100">
          <template slot-scope="scope">
            {{ scope.row.RefundSaleSavingCardCommission | toFixed | NumFormat }}
          </template>
        </el-table-column>
        <el-table-column prop="RefundSaleSavingCardLargessPerformance" label="退赠卡扣业绩" align="right" width="100">
          <template slot-scope="scope">
            {{ scope.row.RefundSaleSavingCardLargessPerformance | toFixed | NumFormat }}
          </template>
        </el-table-column>
        <el-table-column prop="RefundSaleSavingCardLargessCommission" label="退赠卡扣提成" align="right" width="100">
          <template slot-scope="scope">
            {{ scope.row.RefundSaleSavingCardLargessCommission | toFixed | NumFormat }}
          </template>
        </el-table-column>
        <el-table-column prop="RefundSaleSpecialBenefitCommission" label="退无业绩奖励" align="right" width="100">
          <template slot-scope="scope">
            {{ scope.row.RefundSaleSpecialBenefitCommission | toFixed | NumFormat }}
          </template>
        </el-table-column>
      </el-table-column>
      <el-table-column label="合计" align="center">
        <el-table-column prop="TotalPayPerformance" label="实收业绩" align="right" width="100">
          <template slot-scope="scope">
            <div v-if="scope.row.TotalPayPerformance < 0" class="color_red">{{ scope.row.TotalPayPerformance | toFixed | NumFormat }}</div>
            <div v-else-if="scope.row.TotalPayPerformance > 0" class="color_green">+{{ scope.row.TotalPayPerformance | toFixed | NumFormat }}</div>
            <div v-else>0.00</div>
          </template>
        </el-table-column>
        <el-table-column prop="TotalPayCommission" label="实收提成" align="right" width="100">
          <template slot-scope="scope">
            <div v-if="scope.row.TotalPayCommission < 0" class="color_red">{{ scope.row.TotalPayCommission | toFixed | NumFormat }}</div>
            <div v-else-if="scope.row.TotalPayCommission > 0" class="color_green">+{{ scope.row.TotalPayCommission | toFixed | NumFormat }}</div>
            <div v-else>0.00</div>
          </template>
        </el-table-column>
        <el-table-column prop="TotalSavingCardPerformance" label="卡扣业绩" align="right" width="100">
          <template slot-scope="scope">
            <div v-if="scope.row.TotalSavingCardPerformance < 0" class="color_red">{{ scope.row.TotalSavingCardPerformance | toFixed | NumFormat }}</div>
            <div v-else-if="scope.row.TotalSavingCardPerformance > 0" class="color_green">+{{ scope.row.TotalSavingCardPerformance | toFixed | NumFormat }}</div>
            <div v-else>0.00</div>
          </template>
        </el-table-column>
        <el-table-column prop="TotalSavingCardCommission" label="卡扣提成" align="right" width="100">
          <template slot-scope="scope">
            <div v-if="scope.row.TotalSavingCardCommission < 0" class="color_red">{{ scope.row.TotalSavingCardCommission | toFixed | NumFormat }}</div>
            <div v-else-if="scope.row.TotalSavingCardCommission > 0" class="color_green">+{{ scope.row.TotalSavingCardCommission | toFixed | NumFormat }}</div>
            <div v-else>0.00</div>
          </template>
        </el-table-column>
        <el-table-column prop="TotalSavingCardLargessPerformance" label="赠卡扣业绩" align="right" width="100">
          <template slot-scope="scope">
            <div v-if="scope.row.TotalSavingCardLargessPerformance < 0" class="color_red">
              {{ scope.row.TotalSavingCardLargessPerformance | toFixed | NumFormat }}
            </div>
            <div v-else-if="scope.row.TotalSavingCardLargessPerformance > 0" class="color_green">+{{ scope.row.TotalSavingCardLargessPerformance | toFixed | NumFormat }}</div>
            <div v-else>0.00</div>
          </template>
        </el-table-column>
        <el-table-column prop="TotalSavingCardLargessCommission" label="赠卡扣提成" align="right" width="100">
          <template slot-scope="scope">
            <div v-if="scope.row.TotalSavingCardLargessCommission < 0" class="color_red">
              {{ scope.row.TotalSavingCardLargessCommission | toFixed | NumFormat }}
            </div>
            <div v-else-if="scope.row.TotalSavingCardLargessCommission > 0" class="color_green">+{{ scope.row.TotalSavingCardLargessCommission | toFixed | NumFormat }}</div>
            <div v-else>0.00</div>
          </template>
        </el-table-column>
        <el-table-column prop="TotalSpecialBenefitCommission" label="无业绩奖励" align="right" width="100">
          <template slot-scope="scope">
            <div v-if="scope.row.TotalSpecialBenefitCommission < 0" class="color_red">{{ scope.row.TotalSpecialBenefitCommission | toFixed | NumFormat }}</div>
            <div v-else-if="scope.row.TotalSpecialBenefitCommission > 0" class="color_green">+{{ scope.row.TotalSpecialBenefitCommission | toFixed | NumFormat }}</div>
            <div v-else>0.00</div>
          </template>
        </el-table-column>
      </el-table-column>
    </el-table>
    <div class="pad_15 text_right">
      <el-pagination
        background
        v-if="tableDataPaginations.total > 0"
        @current-change="handleSalePerformanceCommissionPageChange"
        :current-page.sync="tableDataPaginations.page"
        :page-size="tableDataPaginations.page_size"
        :layout="tableDataPaginations.layout"
        :total="tableDataPaginations.total"
      ></el-pagination>
    </div>
  </div>
</template>

<script>
import API from "@/api/Report/Employee/salePerformanceCommissionStatistics.js";
import EntityAPI from "@/api/Report/Common/entity";
import APIJob from "@/api/KHS/Entity/jobtype";
const dayjs = require("dayjs");
const isoWeek = require("dayjs/plugin/isoWeek");
dayjs.extend(isoWeek);
export default {
  name: "EmployeeSalePerformanceCommissionStatistics",
  beforeRouteEnter(to, from, next) {
    next((vm) => {
      vm.isExport = vm.$permission.permission(to.meta.Permission, "Report-Employee-SalePerformanceCommissionStatistics-Export");
    });
  },
  props: {},
  /** 监听数据变化   */
  watch: {},
  /**  引入的组件  */
  components: {},
  /**  Vue 实例的数据对象**/
  data() {
    return {
      isExport: false,
      loading: false,
      downloadLoading: false,
      storeEntityList: [], //门店列表
      jobTypeList: [], //职务列表
      searchData: {
        QueryDate: [new Date(), new Date()],
        GoodsTypeName: "",
        EmployeeName: "",
        JobID: "",
        CustomerName: "",
        CategoryID: "",
        GoodsName: "",
      },
      tableData: [],
      tableDataSum: {},
      //需要给分页组件传的信息
      tableDataPaginations: {
        page: 1, // 当前位于哪页
        total: 0, // 总数
        page_size: 10, // 1页显示多少条
        layout: "total, prev, pager, next,jumper", // 翻页属性
      },
      pickerOptions: {
        shortcuts: [
          {
            text: "今天",
            onClick: (picker) => {
              let end = new Date();
              let start = new Date();
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "本周",
            onClick: (picker) => {
              let end = new Date();
              let weekDay = dayjs(end).isoWeekday();
              let start = dayjs(end)
                .subtract(weekDay - 1, "day")
                .toDate();
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "本月",
            onClick: (picker) => {
              let end = new Date();
              let start = dayjs(dayjs(end).format("YYYY-MM") + "01").toDate();
              picker.$emit("pick", [start, end]);
            },
          },
        ],
      },
      categoryList: [],
      cascaderProps: {
        checkStrictly: true,
        label: "Name",
        value: "ID",
        children: "Child",
      },
    };
  },
  /**计算属性  */
  computed: {},
  /**  方法集合  */
  methods: {
    handleSalePerformanceCommissionSearch() {
      var that = this;
      that.tableDataPaginations.page = 1;
      that.salePerformanceCommissionDetail();
    },
    handleSalePerformanceCommissionPageChange(page) {
      this.tableDataPaginations.page = page;
      this.salePerformanceCommissionDetail();
    },
    // 销售搜索
    salePerformanceCommissionDetail() {
      var that = this;
      if (that.searchData.QueryDate != null) {
        if (dayjs(that.searchData.QueryDate[0]).add(366, "day").valueOf() < dayjs(that.searchData.QueryDate[1]).valueOf()) {
          that.$message.error("时间筛选范围不能超366天");
          return;
        }
        var params = {
          EntityID: that.searchData.EntityID,
          StartDate: that.searchData.QueryDate[0],
          EndDate: that.searchData.QueryDate[1],
          EmployeeName: that.searchData.EmployeeName.trim(),
          PageNum: that.tableDataPaginations.page,
          JobID: that.searchData.JobID,
        };
        that.loading = true;
        API.employeeSalePerformanceCommissionDetailStatement_statistics(params)
          .then((res) => {
            if (res.StateCode == 200) {
              that.tableDataSum = res.Data.employeeSalePerformanceCommissionSumStatementForm;
              that.tableData = res.Data.employeeSalePerformanceCommissionDetailStatementForms.List;
              that.tableDataPaginations.total = res.Data.employeeSalePerformanceCommissionDetailStatementForms.Total;
              that.tableDataPaginations.page_size = res.Data.employeeSalePerformanceCommissionDetailStatementForms.PageSize;
            } else {
              that.$message.error({
                message: res.Message,
                duration: 2000,
              });
            }
          })
          .finally(function () {
            that.loading = false;
          });
      }
    },

    gettableDataSummaries({ columns }) {
      const sums = [];
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = <span class="font_weight_600">合计</span>;
          return;
        }
        var filter_NumFormat = this.$options.filters["NumFormat"];
        var filter_toFixed = this.$options.filters["toFixed"];
        switch (column.property) {
          case "SalePayPerformance":
            sums[index] = <span class="font_weight_600">{filter_NumFormat(this.tableDataSum ? this.tableDataSum.SalePayPerformance : 0)}</span>;
            break;
          case "SalePayCommission":
            sums[index] = <span class="font_weight_600">{filter_NumFormat(this.tableDataSum ? this.tableDataSum.SalePayCommission : 0)}</span>;
            break;
          case "SaleSavingCardPerformance":
            sums[index] = <span class="font_weight_600">{filter_NumFormat(this.tableDataSum ? this.tableDataSum.SaleSavingCardPerformance : 0)}</span>;
            break;
          case "SaleSavingCardCommission":
            sums[index] = <span class="font_weight_600">{filter_NumFormat(this.tableDataSum ? this.tableDataSum.SaleSavingCardCommission : 0)}</span>;
            break;
          case "SaleSavingCardLargessPerformance":
            sums[index] = <span class="font_weight_600">{filter_NumFormat(this.tableDataSum ? this.tableDataSum.SaleSavingCardLargessPerformance : 0)}</span>;
            break;
          case "SaleSavingCardLargessCommission":
            sums[index] = <span class="font_weight_600">{filter_NumFormat(this.tableDataSum ? this.tableDataSum.SaleSavingCardLargessCommission : 0)}</span>;
            break;
          case "SaleSpecialBenefitCommission":
            sums[index] = <span class="font_weight_600">{filter_NumFormat(this.tableDataSum ? this.tableDataSum.SaleSpecialBenefitCommission : 0)}</span>;
            break;
          case "ArrearPayPerformance":
            sums[index] = <span class="font_weight_600">{filter_NumFormat(this.tableDataSum ? this.tableDataSum.ArrearPayPerformance : 0)}</span>;
            break;
          case "ArrearPayCommission":
            sums[index] = <span class="font_weight_600">{filter_NumFormat(this.tableDataSum ? this.tableDataSum.ArrearPayCommission : 0)}</span>;
            break;

            case "ArrearSavingCardPerformance":
            sums[index] = <span class="font_weight_600">{filter_NumFormat(this.tableDataSum ? this.tableDataSum.ArrearSavingCardPerformance : 0)}</span>;
            break;
          case "ArrearSavingCardCommission":
            sums[index] = <span class="font_weight_600">{filter_NumFormat(this.tableDataSum ? this.tableDataSum.ArrearSavingCardCommission : 0)}</span>;
            break;

            case "ArrearSavingCardLargessPerformance":
            sums[index] = <span class="font_weight_600">{filter_NumFormat(this.tableDataSum ? this.tableDataSum.ArrearSavingCardLargessPerformance : 0)}</span>;
            break;
          case "ArrearSavingCardLargessCommission":
            sums[index] = <span class="font_weight_600">{filter_NumFormat(this.tableDataSum ? this.tableDataSum.ArrearSavingCardLargessCommission : 0)}</span>;
            break;


          case "ArrearSpecialBenefitCommission":
            sums[index] = <span class="font_weight_600">{filter_NumFormat(this.tableDataSum ? this.tableDataSum.ArrearSpecialBenefitCommission : 0)}</span>;
            break;



          case "RefundSalePayPerformance":
            sums[index] = <span class="font_weight_600">{filter_NumFormat(this.tableDataSum ? this.tableDataSum.RefundSalePayPerformance : 0)}</span>;
            break;
          case "RefundSalePayCommission":
            sums[index] = <span class="font_weight_600">{filter_NumFormat(this.tableDataSum ? this.tableDataSum.RefundSalePayCommission : 0)}</span>;
            break;
          case "RefundSaleSavingCardPerformance":
            sums[index] = <span class="font_weight_600">{filter_NumFormat(this.tableDataSum ? this.tableDataSum.RefundSaleSavingCardPerformance : 0)}</span>;
            break;
          case "RefundSaleSavingCardCommission":
            sums[index] = <span class="font_weight_600">{filter_NumFormat(this.tableDataSum ? this.tableDataSum.RefundSaleSavingCardCommission : 0)}</span>;
            break;
          case "RefundSaleSavingCardLargessPerformance":
            sums[index] = <span class="font_weight_600">{filter_NumFormat(this.tableDataSum ? this.tableDataSum.RefundSaleSavingCardLargessPerformance : 0)}</span>;
            break;
          case "RefundSaleSavingCardLargessCommission":
            sums[index] = <span class="font_weight_600">{filter_NumFormat(this.tableDataSum ? this.tableDataSum.RefundSaleSavingCardLargessCommission : 0)}</span>;
            break;
          case "RefundSaleSpecialBenefitCommission":
            sums[index] = <span class="font_weight_600">{filter_NumFormat(this.tableDataSum ? this.tableDataSum.RefundSaleSpecialBenefitCommission : 0)}</span>;
            break;
          case "TotalPayPerformance":
            if (this.tableDataSum && this.tableDataSum.TotalPayPerformance < 0) {
              sums[index] = <div class="color_red font_weight_600">{filter_NumFormat(filter_toFixed(this.tableDataSum.TotalPayPerformance))}</div>;
            } else if (this.tableDataSum && this.tableDataSum.TotalPayPerformance > 0) {
              sums[index] = <div class="color_green font_weight_600">+{filter_NumFormat(filter_toFixed(this.tableDataSum.TotalPayPerformance))}</div>;
            } else {
              sums[index] = <div class="font_weight_600">0.00</div>;
            }
            break;
          case "TotalPayCommission":
            if (this.tableDataSum && this.tableDataSum.TotalPayCommission < 0) {
              sums[index] = <div class="color_red font_weight_600">{filter_NumFormat(filter_toFixed(this.tableDataSum.TotalPayCommission))}</div>;
            } else if (this.tableDataSum && this.tableDataSum.TotalPayCommission > 0) {
              sums[index] = <div class="color_green font_weight_600">+{filter_NumFormat(filter_toFixed(this.tableDataSum.TotalPayCommission))}</div>;
            } else {
              sums[index] = <div class="font_weight_600">0.00</div>;
            }
            break;
          case "TotalSavingCardPerformance":
            if (this.tableDataSum && this.tableDataSum.TotalSavingCardPerformance < 0) {
              sums[index] = <div class="color_red font_weight_600">{filter_NumFormat(filter_toFixed(this.tableDataSum.TotalSavingCardPerformance))}</div>;
            } else if (this.tableDataSum && this.tableDataSum.TotalSavingCardPerformance > 0) {
              sums[index] = <div class="color_green font_weight_600">+{filter_NumFormat(filter_toFixed(this.tableDataSum.TotalSavingCardPerformance))}</div>;
            } else {
              sums[index] = <div class="font_weight_600">0.00</div>;
            }
            break;
          case "TotalSavingCardCommission":
            if (this.tableDataSum && this.tableDataSum.TotalSavingCardCommission < 0) {
              sums[index] = <div class="color_red font_weight_600">{filter_NumFormat(filter_toFixed(this.tableDataSum.TotalSavingCardCommission))}</div>;
            } else if (this.tableDataSum && this.tableDataSum.TotalSavingCardCommission > 0) {
              sums[index] = <div class="color_green font_weight_600">+{filter_NumFormat(filter_toFixed(this.tableDataSum.TotalSavingCardCommission))}</div>;
            } else {
              sums[index] = <div class="font_weight_600">0.00</div>;
            }
            break;
          case "TotalSavingCardLargessPerformance":
            if (this.tableDataSum && this.tableDataSum.TotalSavingCardLargessPerformance < 0) {
              sums[index] = <div class="color_red font_weight_600">{filter_NumFormat(filter_toFixed(this.tableDataSum.TotalSavingCardLargessPerformance))}</div>;
            } else if (this.tableDataSum && this.tableDataSum.TotalSavingCardLargessPerformance > 0) {
              sums[index] = <div class="color_green font_weight_600">+{filter_NumFormat(filter_toFixed(this.tableDataSum.TotalSavingCardLargessPerformance))}</div>;
            } else {
              sums[index] = <div class="font_weight_600">0.00</div>;
            }
            break;
          case "TotalSavingCardLargessCommission":
            if (this.tableDataSum && this.tableDataSum.TotalSavingCardLargessCommission < 0) {
              sums[index] = <div class="color_red font_weight_600">{filter_toFixed(filter_NumFormat(this.tableDataSum.TotalSavingCardLargessCommission))}</div>;
            } else if (this.tableDataSum && this.tableDataSum.TotalSavingCardLargessCommission > 0) {
              sums[index] = <div class="color_green font_weight_600">+{filter_toFixed(filter_NumFormat(this.tableDataSum.TotalSavingCardLargessCommission))}</div>;
            } else {
              sums[index] = <div class="font_weight_600">0.00</div>;
            }
            break;
          case "TotalSpecialBenefitCommission":
            if (this.tableDataSum && this.tableDataSum.TotalSpecialBenefitCommission < 0) {
              sums[index] = <div class="color_red font_weight_600">{filter_toFixed(filter_NumFormat(this.tableDataSum.TotalSpecialBenefitCommission))}</div>;
            } else if (this.tableDataSum && this.tableDataSum.TotalSpecialBenefitCommission > 0) {
              sums[index] = <div class="color_green font_weight_600">+{filter_toFixed(filter_NumFormat(this.tableDataSum.TotalSpecialBenefitCommission))}</div>;
            } else {
              sums[index] = <div class="font_weight_600">0.00</div>;
            }
            break;


        }
      });
      return sums;
    },

    /** 数据导出 */
    downloadExcel() {
      var that = this;
      if (that.searchData.QueryDate != null) {
        if (dayjs(that.searchData.QueryDate[0]).add(366, "day").valueOf() < dayjs(that.searchData.QueryDate[1]).valueOf()) {
          that.$message.error("时间筛选范围不能超366天");
          return;
        }

        let params = {
          EntityID: that.searchData.EntityID,
          StartDate: that.searchData.QueryDate[0],
          EndDate: that.searchData.QueryDate[1],
          EmployeeName: that.searchData.EmployeeName.trim(),
          PageNum: that.tableDataPaginations.page,
          JobID: that.searchData.JobID,
        };
        that.downloadLoading = true;
        API.employeeSalePerformanceCommissionDetailStatement_statisticsExcel(params)
          .then((res) => {
            this.$message.success({
              message: "正在导出",
              duration: "4000",
            });
            const link = document.createElement("a");
            let blob = new Blob([res], { type: "application/octet-stream" });
            link.style.display = "none";
            link.href = URL.createObjectURL(blob);
            link.download = "员工销售业绩统计.xlsx"; //下载的文件名
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
          })
          .finally(function () {
            that.downloadLoading = false;
          });
      }
    },
    // 职务ID
    async getJobID() {
      var that = this;
      var params = {
        JobTypeName: "",
      };
      let res = await APIJob.getJobJobtypeAll(params);
      if (res.StateCode == 200) {
        that.jobTypeList = res.Data;
      } else {
        that.$message.error({
          message: res.Message,
          duration: 2000,
        });
      }
    },

    //获得当前用户下的权限门店
    getstoreEntityList() {
      var that = this;
      that.loading = true;
      EntityAPI.getStoreEntityList()
        .then((res) => {
          if (res.StateCode == 200) {
            that.storeEntityList = res.Data;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },
  },
  /** 创建实例之前执行函数   */
  beforeCreate() {},
  /**  实例创建完成之后  */
  created() {},
  /**  在挂载开始之前被调用  */
  beforeMount() {},
  /**  实例被挂载后调用  */
  mounted() {
    this.isExport = this.$permission.permission(this.$route.meta.Permission, "Report-Employee-SalePerformanceCommissionStatistics-Export");
    this.getJobID();
    this.getstoreEntityList();
    this.handleSalePerformanceCommissionSearch();
  },
  /**  数据更新 之前 调用  */
  beforeUpdate() {},
  /** 数据更新 完成 调用   */
  updated() {},
  /**  实例销毁之前调用  */
  beforeDestroy() {},
  /**  实例销毁后调用  */
  destroyed() {},
};
</script>

<style lang="scss">
.EmployeeSalePerformanceCommissionStatistics {
}
</style>
