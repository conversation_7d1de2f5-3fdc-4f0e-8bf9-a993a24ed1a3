/* 颜色变量 */

:root {
  --zl-color-orange-primary: #019EAF;
  --zl-color-orange-primary-soft: #81D8D0;
  --zl-color-orange-primary-end: #81D8D0;
  --zl-color-orange-primary-header:#fff7f3;
  --zl-color-subheader:#f5f7fa;
}

/* 行为相关颜色 */
$zl-color-primary: #4861fc;
$zl-color-assist: #f4f6ff;
$zl-color-success: #4cd964;
$zl-color-warning: #81D8D0;
$zl-color-error: #e12f18;

/* 文字基本颜色 */
$zl-text-color: #000000; /*基本色*/
$zl-text-color-inverse: #fff; /*反色*/
$zl-text-color-grey: #bbbbbb; /*辅助灰色，如加载更多的提示信息*/
/* 背景颜色 */
$zl-bg-color: #ffffff;
$zl-bg-color-grey: #f8f8f8;
$zl-bg-color-hover: #f1f1f1; /*点击状态颜色*/
$zl-bg-color-mask: rgba(0, 0, 0, 0.4); /*遮罩颜色*/

/* 边框颜色 */
$zl-border-color: #eeeeee;

/* 尺寸变量 */

/* 文字尺寸 */
$zl-font-size-16: 16px;
$zl-font-size-14: 14px;
$zl-font-size-12: 12px;

/* 图片尺寸 */
$zl-img-size-sm: 40px;
$zl-img-size-base: 52px;
$zl-img-size-lg: 80px;

/* Border Radius */
$zl-border-radius-sm: 4px;
$zl-border-radius-base: 6px;
$zl-border-radius-lg: 12px;
$zl-border-radius-circle: 50%;

/* 水平间距 */
$zl-spacing-row-sm: 10px;
$zl-spacing-row-base: 20px;
$zl-spacing-row-lg: 30px;

/* 垂直间距 */
$zl-spacing-col-sm: 8px;
$zl-spacing-col-base: 16px;
$zl-spacing-col-lg: 24px;

/* 透明度 */
$zl-opacity-disabled: 0.3; /* 组件禁用态的透明度*/
