<template>
  <!--    门店业绩-产品消耗门店业绩-->
  <div class="treatProductEntityPerformanceScheme content_body">
    <div class="nav_header">
      <el-row>
        <el-col :span="20">
          <el-form :inline="true" size="small" @submit.native.prevent>
            <el-form-item label="组织单位">
              <el-input
                v-model="name"
                placeholder="输入组织单位名称搜索"
                clearable
                @clear="handleSearch"
                @keyup.enter.native="handleSearch"
              ></el-input>
            </el-form-item>
            <el-form-item>
              <el-button
                type="primary"
                size="small"
                @click="handleSearch"
                v-prevent-click
              >搜索</el-button
              >
            </el-form-item>
          </el-form>
        </el-col>
        <el-col :span="4" class="text_right">
          <el-button
            type="primary"
            size="small"
            @click="showAddDialog"
            v-prevent-click
          >新增</el-button
          >
        </el-col>
      </el-row>
    </div>

    <div>
      <el-table size="small" :data="tableData">
        <el-table-column prop="EntityName" label="组织单位"></el-table-column>
        <el-table-column label="操作" width="145px">
          <template slot-scope="scope">
            <el-button
              type="primary"
              size="small"
              @click="showEditDialog(scope.row)"
              v-prevent-click
            >编辑</el-button
            >
            <el-button
              type="danger"
              size="small"
              @click="removeEntityClick(scope.row)"
              v-prevent-click
              v-if="isDelete"
            >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <div class="page pad_10">
        <div class="text_right" v-if="paginations.total > 0">
          <el-pagination
            background
            @current-change="handleCurrentChange"
            :current-page.sync="paginations.page"
            :page-size="paginations.page_size"
            :layout="paginations.layout"
            :total="paginations.total"
          ></el-pagination>
        </div>
      </div>
    </div>

    <!--新增弹窗-->
    <el-dialog
      title="新增产品消耗门店业绩方案"
      :visible.sync="dialogVisible"
      width="30%"
      custom-class="custom-dialog-add"
    >
      <div>
        <el-form
          :model="ruleForm"
          :rules="rules"
          ref="ruleForm"
          label-width="120px"
          size="small"
        >
          <el-form-item label="适用组织" prop="EntityID">
            <span slot="label">
              适用组织
              <el-popover placement="top-start" width="200" trigger="hover">
                <p>适用于同级所有节点，则只需选择父节点。</p>
                <p>比如：适用于所有节点，只需选择“顶级/第一个”节点。</p>
                <!--                <p>-->
                <!--                  如需要设置经手人/职务业绩，请选择已经配置经手人的组织单位。-->
                <!--                </p>-->
                <el-button
                  type="text"
                  style="color: #dcdfe6"
                  icon="el-icon-info"
                  slot="reference"
                ></el-button>
              </el-popover>
            </span>
            <treeselect
              v-model="ruleForm.EntityID"
              :options="entityList"
              :normalizer="normalizer"
              clearValueText
              noResultsText="无匹配数据"
              placeholder="选择适用组织"
            />
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="dialogVisible = false" v-prevent-click
        >取 消</el-button
        >
        <el-button
          type="primary"
          size="small"
          :loading="modalLoading"
          v-prevent-click
          @click="submitProductPerformanceScheme"
        >保存</el-button
        >
      </div>
    </el-dialog>

    <!--编辑弹窗-->
    <el-dialog :visible.sync="dialogEdit" custom-class="custom-dialog-edit" width="60%">
      <div slot="title">{{ entityName }} - 产品分类消耗门店业绩方案</div>
      <el-table
        :data="productEntityPerformance"
        size="small"
        max-height="500px"
        :row-class-name="tableRowClassName"
        row-key="id"
        :tree-props="{ children: 'Child', hasChildren: 'hasChild' }"
      >
        <el-table-column
          prop="CategoryName"
          label="产品分类"
          min-width="100px"
          fixed
        ></el-table-column>

        <el-table-column prop="PayRate" label="现金比例" min-width="105px">
          <template slot-scope="scope">
            <el-input
              type="number"
              size="mini"
              v-input-fixed="2"
              v-model="scope.row.PayRate"
              class="input_type"
              @input="royaltyRateChange(0, scope.row)"
            >
              <template slot="append">%</template>
            </el-input>
          </template>
        </el-table-column>
        <el-table-column
          prop="CardRate"
          label="卡抵扣比例"
          min-width="105px"
        >
          <template slot-scope="scope">
            <el-input
              type="number"
              size="mini"
              v-input-fixed="2"
              v-model="scope.row.CardRate"
              class="input_type"
              @input="royaltyRateChange(1, scope.row)"
            >
              <template slot="append">%</template>
            </el-input>
          </template>
        </el-table-column>
        <el-table-column
          prop="CardLargessRate"
          label="赠送卡抵扣比例"
          min-width="105px"
        >
          <template slot-scope="scope">
            <el-input
              type="number"
              size="mini"
              v-input-fixed="2"
              v-model="scope.row.CardLargessRate"
              class="input_type"
              @input="royaltyRateChange(2, scope.row)"
            >
              <template slot="append">%</template>
            </el-input>
          </template>
        </el-table-column>

        <el-table-column prop="LargessRate" label="赠送比例" min-width="105px">
          <template slot-scope="scope">
            <el-input type="number" v-model="scope.row.LargessRate" size="mini" v-input-fixed="2" class="input_type" @input="royaltyRateChange(3, scope.row)">
              <template slot="append">%</template>
            </el-input>
          </template>
        </el-table-column>


        <el-table-column label="操作" width="115px">
          <template slot-scope="scope">

            <el-button
              type="primary"
              size="mini"
              @click="productPerformanceClick(scope.row)"
              v-if="!scope.row.isProductPerformance && !scope.row.isEntity"
            >产品业绩</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="dialogEdit = false" v-prevent-click
        >取 消</el-button
        >
        <el-button
          type="primary"
          size="small"
          :loading="modalLoading"
          v-prevent-click
          @click="submitProductEntityPerformance"
        >保 存</el-button
        >
      </div>
    </el-dialog>

    <!--产品弹窗-->
    <el-dialog :visible.sync="dialogProduct"  custom-class="custom-dialog-edit_product">
      <div slot="title">
        {{ entityName }} - {{ categoryName }} - 产品消耗业绩方案
      </div>
      <div>
        <el-form :inline="true" size="small" @submit.native.prevent>
          <el-form-item>
            <el-input
              v-model="searchProductName"
              placeholder="输入产品名称搜索"
              prefix-icon="el-icon-search"
              clearable
            ></el-input>
          </el-form-item>
        </el-form>
        <el-table
          :data="
            productPerformance.filter(
              (data) =>
                !searchProductName ||
                data.GoodName.toLowerCase().includes(
                  searchProductName.toLowerCase()
                )
            )
          "
          max-height="500px"
          size="small"
        >

          <el-table-column
            prop="GoodName"
            label="产品名称"
            min-width="150px"
            fixed
          ></el-table-column>
          <el-table-column prop="PayRate" label="现金比例" min-width="105px">
            <template slot-scope="scope">
              <el-input
                type="number"
                v-model="scope.row.PayRate"
                v-input-fixed="2"
                class="input_type"
                @input="royaltyRateChange(0, scope.row)"
                size="mini"
              >
                <template slot="append">%</template>
              </el-input>
            </template>
          </el-table-column>
          <el-table-column
            prop="CardRate"
            label="卡抵扣比例"
            min-width="105px"
          >
            <template slot-scope="scope">
              <el-input
                type="number"
                v-model="scope.row.CardRate"
                v-input-fixed="2"
                class="input_type"
                @input="royaltyRateChange(1, scope.row)"
                size="mini"
              >
                <template slot="append">%</template>
              </el-input>
            </template>
          </el-table-column>
          <el-table-column
            prop="CardLargessRate"
            label="赠送卡扣比例"
            min-width="105px"
          >
            <template slot-scope="scope">
              <el-input
                type="number"
                v-model="scope.row.CardLargessRate"
                v-input-fixed="2"
                class="input_type"
                @input="royaltyRateChange(2, scope.row)"
                size="mini"
              >
                <template slot="append">%</template>
              </el-input>
            </template>
          </el-table-column>
          <el-table-column prop="LargessRate" label="赠送比例" min-width="105px">
            <template slot-scope="scope">
              <el-input type="number" v-model="scope.row.LargessRate" size="mini" v-input-fixed="2" class="input_type" @input="royaltyRateChange(3, scope.row)">
                <template slot="append">%</template>
              </el-input>
            </template>
          </el-table-column>

        </el-table>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="dialogProduct = false" v-prevent-click
        >取 消</el-button
        >
        <el-button
          type="primary"
          size="small"
          :loading="modalLoading"
          v-prevent-click
          @click="submitProductEntityCategoryPerformance"
        >保存</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import APIEntity from "@/api/KHS/Entity/entity";
  import API from "@/api/iBeauty/EntityPerformance/treatProductEntityPerformanceScheme";
  var Enumerable = require("linq");
  import Treeselect from "@riophae/vue-treeselect";
  import "@riophae/vue-treeselect/dist/vue-treeselect.css";
export default {

 name: 'treatProductEntityPerformanceScheme',

beforeRouteEnter(to, from, next) {
 next((vm) => {
     vm.isDelete = vm.$permission.permission(
      to.meta.Permission,
      "KHS-EntityPerformance-TreatProductEntityPerformanceScheme-Delete"
    );
  });
},
  /**  引入的组件  */
  components: {
    Treeselect
  },
  /**  Vue 实例的数据对象**/
  data() {
    return {
      loading: false,
      modalLoading: false,
      dialogVisible: false,
      dialogEdit: false,
      dialogProduct: false,
      isDelete: false,
      name: "",
      entityList: [], //选择组织
      tableData: [], //组织单位列表
      searchProductName: "", //产品业绩弹框搜索框
      entityID: "",
      categoryID: "",
      entityName: "", //门店名称
      categoryName: "",
      productName: "",
      productEntityPerformance: [],
      productPerformance: [],
      ruleForm: {
        EntityID: null,
      },
      rules: {
        EntityID: [
          { required: true, message: "请选择组织", trigger: "change" },
        ],
      },
      //需要给分页组件传的信息
      paginations: {
        page: 1, // 当前位于哪页
        total: 0, // 总数
        page_size: 10, // 1页显示多少条
        layout: "total, prev, pager, next,jumper", // 翻页属性
      },
    };
  },
   /**计算属性  */
  computed: {
  },
  /**  方法集合  */
  methods: {
    normalizer(node) {
      return {
        id: node.ID,
        label: node.EntityName,
        children: node.Child,
      };
    },
    tableRowClassName({  rowIndex }) {
      if (rowIndex === 0) {
        return "info-row";
      }
      return "";
    },
    // 所属单位
    entityData: function () {
      var that = this;
      APIEntity.getEntityAll()
        .then((res) => {
          if (res.StateCode == 200) {
            that.entityList = res.Data;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },
    // 数据显示
    handleSearch: function () {
      let that = this;
      that.paginations.page = 1;
      that.search();
    },
    // 数据显示
    search: function () {
      let that = this;
      that.loading = true;
      var params = {
        Name: that.name,
        PageNum: that.paginations.page,
      };
      API.getProductEntityPerformanceScheme(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.tableData = res.List;
            that.paginations.total = res.Total;
            that.paginations.page_size = res.PageSize;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },
    // 上下分页
    handleCurrentChange(page) {
      var that = this;
      that.paginations.page = page;
      that.search();
    },
    // 编辑
    showEditDialog: function (row) {
      var that = this;
      that.entityName = row.EntityName;
      that.entityID = row.EntityID;
      that.getProductCategoryEntityPerformance();
    },
    // 编辑 获取产品消耗门店业绩
    getProductCategoryEntityPerformance: function () {
      var that = this;
      that.loading = true;
      var params = {
        EntityID: that.entityID,
      };
      API.getProductCategoryEntityPerformance(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.dialogEdit = true;
            var data = {
              id: res.Data.EntityID + "Product" + res.Data.EntityName,
              CategoryID: "0" + res.Data.EntityID,
              CategoryName: "所有产品",
              PayRate: res.Data.PayRate, //现金比例业绩
              CardRate: res.Data.CardRate, //卡本金比例业绩
              CardLargessRate: res.Data.CardLargessRate, //赠卡比例业绩
              LargessRate: res.Data.LargessRate,
              isEntity: true,
              isProductPerformance: false,
            };

            res.Data.Category.forEach((item) => {
              item.Child = Enumerable.from(item.Child)
                .select((val) => ({
                  id: val.CategoryName + val.CategoryID + "Product",
                  CategoryID: val.CategoryID,
                  CategoryName: val.CategoryName,
                  LargessRate: val.LargessRate,
                  PayRate: val.PayRate,
                  CardRate: val.CardRate,
                  CardLargessRate: val.CardLargessRate,
                }))
                .toArray();
            });

            var Category = Enumerable.from(res.Data.Category)
              .select((val) => ({
                id: val.CategoryName + val.CategoryID + "Product",
                CategoryID: val.CategoryID,
                CategoryName: val.CategoryName,
                LargessRate: val.LargessRate,
                PayRate: val.PayRate, //现金比例业绩
                CardRate: val.CardRate, //卡本金比例业绩
                CardLargessRate: val.CardLargessRate, //赠卡比例业绩
                isEntity: false,
                isProductPerformance: true,
                Child: val.Child,
              }))
              .toArray();
            that.productEntityPerformance = Object.assign([], Category);
            that.productEntityPerformance.unshift(data);
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },
    // 保存 编辑
    submitProductEntityPerformance: function () {
      var that = this;
      var params = {
        EntityID: that.productEntityPerformance[0].CategoryID,
        PayRate: that.productEntityPerformance[0].PayRate, //现金比例业绩
        CardRate: that.productEntityPerformance[0].CardRate, //卡本金比例业绩
        CardLargessRate: that.productEntityPerformance[0].CardLargessRate, //赠卡比例业绩
        LargessRate: that.productEntityPerformance[0].LargessRate,
      };
      let  productEntityPerformance = JSON.parse(JSON.stringify(that.productEntityPerformance))
      productEntityPerformance.forEach((item) => {
        item.Child = Enumerable.from(item.Child)
          .where(function(i) {
            return (
              (i.PayRate !== "" && i.PayRate !== null) ||
              (i.CardRate !== "" && i.CardRate !== null) ||
              (i.CardLargessRate !== "" && i.CardLargessRate !== null) ||
              (i.LargessRate !== "" && i.LargessRate !== null)
            );
          })
          .select((val) => ({
            CategoryID: val.CategoryID,
            PayRate: val.PayRate, //现金比例业绩
            CardRate: val.CardRate, //卡本金比例业绩
            CardLargessRate: val.CardLargessRate, //赠卡比例业绩
            LargessRate: val.LargessRate, //赠送比例
          }))
          .toArray();
      });
      productEntityPerformance = Enumerable.from(productEntityPerformance)
        .where(function(i) {
          return (
            (i.PayRate !== "" && i.PayRate !== null) ||
            (i.CardRate !== "" && i.CardRate !== null) ||
            (i.CardLargessRate !== "" && i.CardLargessRate !== null) ||
            (i.LargessRate !== "" && i.LargessRate !== null) ||
            i.Child.length > 0
          );
        })
        .toArray();

      that.modalLoading = true;
      var Category = Enumerable.from(productEntityPerformance)
        .where(function(i) {
          return !i.isEntity;
        })
        .select((val) => ({
          CategoryID: val.CategoryID,
          PayRate: val.PayRate, //现金比例业绩
          CardRate: val.CardRate, //卡本金比例业绩
          CardLargessRate: val.CardLargessRate, //赠卡比例业绩
          LargessRate: val.LargessRate, //赠送比例
          Child: val.Child,
        }))
        .toArray();
      params.Category = Category;
      API.updateProductCategoryEntityPerformance(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.$message.success("业绩设置成功");
            that.dialogEdit = false;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function() {
          that.modalLoading = false;
        });
    },
    // 产品业绩的点击
    productPerformanceClick: function (row) {
      var that = this;
      that.categoryID = row.CategoryID;
      that.categoryName = row.CategoryName;
      that.getProductEntityPerformance();

    },
    // 获取产品业绩列表
    getProductEntityPerformance: function () {
      var that = this;
      // that.loading = true;
      var params = {
        EntityID: that.entityID,
        CategoryID: that.categoryID,
      };
      API.getProductEntityPerformance(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.dialogProduct = true;
            that.productPerformance = res.Data;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.loading = false;
        });
    },
    // 保存 产品业绩
    submitProductEntityCategoryPerformance: function () {
      var that = this;
      var productPerformance = Enumerable.from(that.productPerformance)
        .where(function (i) {
          return (
            (i.PayRate !== "" && i.PayRate !== null) ||
            (i.LargessRate !== "" && i.LargessRate !== null) ||
            (i.CardLargessRate !== "" && i.CardLargessRate !== null) ||
            (i.CardRate !== "" && i.CardRate !== null)
          );
        }).select((val)=>({
          GoodID:val.GoodID,
          PayRate:val.PayRate,
          CardRate:val.CardRate,
          CardLargessRate:val.CardLargessRate,
          LargessRate:val.LargessRate
        }))
        .toArray();
      that.modalLoading = true;
      var params = {
        EntityID: that.entityID,
        Good: productPerformance,
        CategoryID: that.categoryID,
      };
      API.updateProductEntityPerformance(params)
        .then((res) => {
          if (res.StateCode == 200) {
            that.$message.success("业绩设置成功");
            that.dialogProduct = false;
          } else {
            that.$message.error({
              message: res.Message,
              duration: 2000,
            });
          }
        })
        .finally(function () {
          that.modalLoading = false;
        });
    },
    // 新增 组织单位
    showAddDialog: function () {
      var that = this;
      that.ruleForm = {
        EntityID: null,
      };
      that.dialogVisible = true;
    },
    // 创建 组织单位
    submitProductPerformanceScheme: function () {
      var that = this;
      that.$refs.ruleForm.validate((valid) => {
        if (valid) {
          that.modalLoading = true;
          let para = Object.assign({}, that.ruleForm);
          API.createProductEntityPerformanceScheme(para)
            .then(function (res) {
              if (res.StateCode === 200) {
                that.$message.success({
                  message: "新增成功",
                  duration: 2000,
                });
                that.search();
                that.$refs["ruleForm"].resetFields();
                that.dialogVisible = false;
              } else {
                that.$message.error({
                  message: res.Message,
                  duration: 2000,
                });
              }
            })
            .finally(function () {
              that.modalLoading = false;
            });
        }
      });
    },
    // 删除 组织单位
    removeEntityClick: function (row) {
      var that = this;
      that
        .$confirm("此操作将永久删除该记录, 是否继续?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
        .then(() => {
          that.loading = false;
          var params = {
            EntityID: row.EntityID,
          };
          API.deleteProductEntityPerformanceScheme(params)
            .then((res) => {
              if (res.StateCode == 200) {
                that.$message.success("删除成功");
                that.search();
              } else {
                that.$message.error({
                  message: res.Message,
                  duration: 2000,
                });
              }
            })
            .finally(function () {
              that.loading = false;
            });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    // 提成比例
    royaltyRateChange: function (index, row) {
      switch (index) {
        case 0:
          row.PayRate = row.PayRate > 100 ? 100 : row.PayRate;
          break;
        case 1:
          row.CardRate = row.CardRate > 100 ? 100 : row.CardRate;
          break;
        case 2:
          row.CardLargessRate = row.CardLargessRate > 100 ? 100 : row.CardLargessRate;
          break;
        case 3:
          row.LargessRate = row.LargessRate > 100 ? 100 : row.LargessRate;
          break;
      }
    },
  },
  /** 监听数据变化   */
  watch: {},
  /** 创建实例之前执行函数   */
  beforeCreate() {},
  /**  实例创建完成之后  */
  created() {},
  /**  在挂载开始之前被调用  */
  beforeMount() {},
  /**  实例被挂载后调用  */
  mounted() {
    this.isDelete = this.$permission.permission(
      this.$route.meta.Permission,
       "KHS-EntityPerformance-TreatProductEntityPerformanceScheme-Delete"
    );
    this.handleSearch();
    this.entityData();
  },
  /**  数据更新 之前 调用  */
  beforeUpdate() {},
  /** 数据更新 完成 调用   */
  updated() {},
  /**  实例销毁之前调用  */
  beforeDestroy() {},
  /**  实例销毁后调用  */
  destroyed() {},
}
</script>

<style lang="scss">

.treatProductEntityPerformanceScheme{
  .input_type {
    .el-input-group__append {
      padding: 0 10px;
    }
  }
  .el-input__inner {
    padding-right: 0;
  }

  .el-table .info-row {
    background: #c0c4cc;
  }
  .custom-dialog-add{
    min-width: 500px;
  }
  .custom-dialog-edit{
    min-width: 950px;
  }
  .custom-dialog-edit_product{
    min-width: 850px;
  }
 }
</style>
