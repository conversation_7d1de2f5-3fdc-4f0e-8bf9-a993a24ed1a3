<template>
  <div class="customerlevel content_body">
    <el-row class="levelTip font_14 color_333" v-show="levelStatus">
      <el-col :span="24" class="flex_y_center dis_flex">
        <i class="el-icon-warning marrt_10 color_main font_24"></i>
        <div class="marrt_10 color_666">你已经手动修改了等级规则，新会员已生效，老会员未实时生效，如需对所有会员一起生效，请</div>
        <el-button type="text" @click="CustormerUploadClick">更新会员等级</el-button>
      </el-col>
    </el-row>
    <ul class="dis_flex flex_wrap martp_10">
      <li v-for="item in customerleveList" :key="item.ID">
        <el-card :body-style="{ padding: '0px' }" class="marrt_20 marbm_20" :style="cardSize">
          <div slot="header" class="dis_flex flex_y_center flex_x_center color_333" style="font-size: 28px; height: 40px">
            {{ item.Name }}</div>
          <div style="height: 140px; margin-top: 50px">
            <div class="dis_flex flex_y_center flex_x_center color_666 font_14">需要成长值{{ item.LevelValue }}</div>

            <div class="dis_flex flex_x_between flex_y_center pad_20 font_13" style="margin-top: 20px">
              <div class="color_999">会员数</div>
              <div class="color_999">{{ item.CustomerCount }}</div>
            </div>
            <div class="dis_flex flex_x_around flex_y_center border_top" style="height: 50px">
              <el-button type="text" @click="customerLeveEdit(item)">编辑</el-button>
              <el-button type="text" @click="deleteCustomerLevel(item)">删除</el-button>
            </div>
          </div>
        </el-card>
      </li>
      <li>
        <el-card :body-style="{ padding: '0px' }" :style="cardSize">
          <div class="dis_flex flex_x_center flex_y_center cursor_pointer flex_dir_column padbm_20"
            style="height: 220px; padding-top: 36px" @click="customerLeveAdd">
            <i class="el-icon-plus font_24 color_main"></i>
            <el-button type="text">添加会员等级</el-button>
          </div>
        </el-card>
      </li>
    </ul>
    <!-- 更新会员等级 -->
    <el-dialog title="提示" :visible.sync="customerleveDialog" width="500px">
      <span>更新会员等级会对所有的会员等级进行更新，需要一定时间，并且更新过程中无法编辑等级，建议完成所有规则调整后，再进行更新。</span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="customerleveDialog = false" size="small">取 消</el-button>
        <el-button type="primary" @click="uploadCustomerClick" size="small">确 定</el-button>
      </span>
    </el-dialog>
    <!-- 添加--编辑--会员 -->
    <el-dialog :title="title" :visible.sync="customerleveDialogAdd" width="500px">
      <el-form :model="customerAddModel" :rules="rules" size="small" ref="ruleForm">
        <el-form-item label-width="120px" label="等级名称：" prop="gradeName">
          <el-input v-model="customerAddModel.gradeName" placeholder="请输入内容"></el-input>
        </el-form-item>
        <el-form-item label-width="120px" label="需要成长值：" prop="needGrowthValue">
          <!-- @change="changeLevelValue" -->
          <el-input v-model="customerAddModel.needGrowthValue" placeholder="请输入" type="number" :min="0" v-input-fixed="0"
            class="custom-input-number"></el-input>
        </el-form-item>
        <el-form-item label-width="120px" label="保级成长值：">
          <!-- @change="changeReduceLevelValue" -->
          <el-input v-model="customerAddModel.relegationGrowthValue" placeholder="请输入" type="number" v-input-fixed="0"
            class="custom-input-number"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="customerleveDialogAdd = false" size="small">取消</el-button>
        <el-button type="primary" @click="createCustomerLeve" size="small">保存</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import API from "@/api/CRM/CustomerLevel/customerlevel";
export default {
  name: "CustomerLevel",
  components: {},
  props: {},
  data() {
    return {
      levelStatus: false,
      cardSize: {
        height: "280px",
        width: "200px",
      },
      title: "", // 添加-编辑
      customerleveDialog: false, // 更新会员等级弹框
      customerleveDialogAdd: false, // 添加编辑弹框
      customerleveList: [], // 列表
      customerAddModel: {
        gradeName: "", // 等级名称
        needGrowthValue: "", // 成长值
        relegationGrowthValue: "", // 保级成长值
        ID: "",
      },
      rules: {
        gradeName: [{ required: true, message: "请输入等级名称", trigger: "blur" }],
        needGrowthValue: [{ required: true, message: "请输入需要成长值", trigger: "blur" }],
      },
    };
  },
  computed: {},
  watch: {},
  created() { },
  mounted() {
    this.getCustomerLevelList();
    this.getStatusCustomerLevel();
  },
  methods: {
    // 点击显示新增会员
    customerLeveAdd() {
      this.title = "添加会员等级";
      this.customerAddModel = {
        gradeName: "", // 等级名称
        needGrowthValue: "", // 成长值
        relegationGrowthValue: "", // 保级成长值
      };
      this.customerleveDialogAdd = true;
    },
    // 点击显示编辑会员
    customerLeveEdit(item) {
      this.title = "编辑会员等级";
      this.customerAddModel.gradeName = item.Name;
      this.customerAddModel.needGrowthValue = item.LevelValue;
      this.customerAddModel.relegationGrowthValue = item.ReduceLevelValue;
      this.customerAddModel.ID = item.ID;
      this.customerleveDialogAdd = true;
    },
    // 确认删除
    deleteCustomerLevel(item) {
      this.$confirm(
        "删除会员等级会更新该等级的会员，期间会员无法使用等级权限(视会员数量差异在1-10分钟之间)。建议请提前公告且在闲时操作，以免造成不必要客诉。",
        "提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      )
        .then(() => {
          let params = {
            ID: item.ID,
          };
          API.deleteCustomerLevel(params).then((res) => {
            if (res.StateCode == 200) {
              this.$message.success("删除成功");
              this.getCustomerLevelList();
              this.getStatusCustomerLevel();
            } else {
              this.$message.error({
                message: res.Message,
                duration: 2000,
              });
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    // 保存新增
    createCustomerLeve() {
      let that = this;

      that.$refs.ruleForm.validate((valid) => {
        if (!valid) return;
        if (parseFloat(that.customerAddModel.relegationGrowthValue) > parseFloat(that.customerAddModel.needGrowthValue)) {
          that.$message.error("保级成长值不能大于所需的成长值");
          return;
        }

        if (that.title == "添加会员等级") {
          if (that.customerleveList.some((i) => i.LevelValue == that.customerAddModel.needGrowthValue)) {
            that.$message.error("存在成长值相同的等级，请修改当前成长值");
            return;
          }
          if (that.customerleveList.some((i) => i.Name == that.customerAddModel.gradeName)) {
            that
              .$confirm("存在相同名称的等级，是否继续创建", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
              })
              .then(() => {
                that.createCustomerLeveNet();
              })
              .catch(() => {
                that.$message({ type: "info", message: "取消操作" });
              });
          } else {
            that.createCustomerLeveNet();
          }
        } else {
          if (that.customerleveList.filter((i) => i.ID != that.customerAddModel.ID).some((i) => i.LevelValue == that.customerAddModel.needGrowthValue)) {
            that.$message.error("存在成长值相同的等级，请修改当前成长值");
            return;
          }

          if (that.customerleveList.filter((i) => i.ID != that.customerAddModel.ID).some((i) => i.Name == that.customerAddModel.gradeName)) {
            that
              .$confirm("存在相同名称的等级，是否继续创建", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
              })
              .then(() => {
                that.updateCustomerLevel();
              })
              .catch(() => {
                that.$message({ type: "info", message: "取消操作" });
              });
          } else {
            that.updateCustomerLevel();
          }
        }
      });
    },

    /**  新增  */
    async createCustomerLeveNet() {
      let that = this;

      let params = {
        Name: that.customerAddModel.gradeName,
        LevelValue: that.customerAddModel.needGrowthValue,
        ReduceLevelValue: that.customerAddModel.relegationGrowthValue,
      };
      let res = await API.createCustomerLeve(params);
      if (res.StateCode == 200) {
        that.$message.success("添加成功");
        that.getCustomerLevelList();
        that.getStatusCustomerLevel();
        that.customerleveDialogAdd = false;
      } else {
        that.$message.error({
          message: res.Message,
          duration: 2000,
        });
      }
    },
    /**  编辑  */
    async updateCustomerLevel() {
      let that = this;
      let params = {
        ID: that.customerAddModel.ID,
        Name: that.customerAddModel.gradeName,
        LevelValue: that.customerAddModel.needGrowthValue,
        ReduceLevelValue: that.customerAddModel.relegationGrowthValue,
      };
      let res = await API.updateCustomerLevel(params);
      if (res.StateCode == 200) {
        that.$message.success("编辑成功");
        that.getCustomerLevelList();
        that.getStatusCustomerLevel();
        that.customerleveDialogAdd = false;
      } else {
        that.$message.error({
          message: res.Message,
          duration: 2000,
        });
      }
    },
    // 获取等级列表
    async getCustomerLevelList() {
      let that = this;
      let params = {
        Name: "",
        Active: true,
      };
      let res = await API.getCustomerLevelList(params);
      if (res.StateCode == 200) {
        that.customerleveList = res.Data;
      } else {
        that.$message.error({
          message: res.Message,
          duration: 2000,
        });
      }
    },
    // 获取提示栏状态
    async getStatusCustomerLevel() {
      let params = {
        Name: "",
      };
      let res = await API.statusCustomerLevel(params);
      if (res.StateCode == 200) {
        this.levelStatus = res.Data;
      } else {
        this.$message.error(res.Message);
      }
    },
    // 点击显示更新会员等级
    CustormerUploadClick() {
      this.customerleveDialog = true;
    },
    // 更新会员等级
    async uploadCustomerClick() {
      let params = {
        Name: "",
      };
      let res = await API.updateAllCustomerLevel(params);
      if (res.StateCode == 200) {
        this.$message.success("更新成功");
        this.customerleveDialog = false;
        this.getStatusCustomerLevel();
      } else {
        this.$message.error(res.Message);
      }
    },
  },
};
</script>

<style  lang="scss">
.customerlevel {
  .levelTip {
    height: 50px;
    line-height: 50px;
    border-radius: 5px;
    border: 1px solid var(--zl-color-orange-primary);
    background: #fbf1eb;
    padding-left: 20px;
  }

  .el-scrollbar_height {
    .el-scrollbar__wrap {
      overflow-x: hidden !important;
    }
  }
}
</style>
