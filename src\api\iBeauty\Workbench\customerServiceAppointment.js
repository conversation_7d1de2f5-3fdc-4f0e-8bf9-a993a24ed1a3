import * as API from '@/api/index'

export default {
  // 获取客服预约表列表
  getCustomerServiceAppointmentList: params => {
    return API.POST('api/followUp/customerServiceAppointmentList', params)
  },

  // 获取预约人员列表（与线索跟进的跟进人员使用同一个接口）
  getAppointmentByList: params => {
    return API.POST('api/followUp/followUpEmployee', params)
  },

  // 获取门店列表（与线索跟进使用同一个接口）
  getEntityList: params => {
    return API.POST('api/entity/allEntity', params)
  }
}
